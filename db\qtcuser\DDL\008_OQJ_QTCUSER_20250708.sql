
call qtc_pack_commonutils_proc_add_table_column('qtc_conf_factor_output_ref' ,'current_previous_is' , 'varchar(3)', '当年/往年标识：C-当年；P-往年；N-不区分') ;

update qtc_conf_factor_output_ref set current_previous_is = 'N' where current_previous_is is null  ;

update qtc_conf_factor_output_ref set current_previous_is = 'C'
where value_ref in ('M3_OI_02','M3_OJ_02','M3_OK_02','M3_OL_02', 'M4_OI_02','M4_OJ_02','M4_OK_02','M4_OL_02' )  ;

update qtc_conf_factor_output_ref set current_previous_is = 'P'
where value_ref in ('M3_OI_03','M3_OJ_03','M3_OK_03','M3_OL_03', 'M4_OI_03','M4_OJ_03','M4_OK_03','M4_OL_03' )  ;

commit ;