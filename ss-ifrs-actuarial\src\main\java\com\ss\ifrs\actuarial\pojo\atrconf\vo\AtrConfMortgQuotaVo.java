/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2023-02-02 14:32:29
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ss.platform.core.annotation.ExcelVaild;
import com.ss.library.excel.ExcelValidEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-02 14:32:29<br/>
 * Description: Mortgage假设配置<br/>
 * Table Name: ATR_CONF_MORTG_QUOTA<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "Mortgage假设配置")
public class AtrConfMortgQuotaVo implements Serializable {
    /**
     * Database column: ATR_CONF_MORTG_QUOTA.MORTG_QUOTA_ID
     * Database remarks: mortg Quota Id|主键
     */
    @ApiModelProperty(value = "mortg Quota Id|主键", required = true)
    private Long mortgQuotaId;

    @NotNull(message = "The Center Id can't be null|业务单位不能为空|業務單位不能為空")
    private Long entityId;

    @ApiModelProperty(value = "year_month|评估期", required = true)
    @NotBlank(message = "The year month can't be null|评估期不能为空|評估期不能為空")
    private String yearMonth;

    /**
     * Database column: ATR_CONF_MORTG_QUOTA.POLICY_NO
     * Database remarks: policy No|保单号
     */
    @ApiModelProperty(value = "policy No|保单号", required = false)
    @ExcelProperty(index = 0)
    @ExcelVaild(regexType = ExcelValidEnum.NOTNULL)
    private String policyNo;

    /**
     * Database column: ATR_CONF_MORTG_QUOTA.INSURED_NAME
     * Database remarks: insured Name|被保险人
     */
    @ApiModelProperty(value = "insured Name|被保险人", required = false)
    @ExcelProperty(index = 1)
    private String insuredName;

    /**
     * Database column: ATR_CONF_MORTG_QUOTA.UW_YEAR
     * Database remarks: uw Year|核保年份
     */
    @ApiModelProperty(value = "uw Year|核保年份", required = false)
    @ExcelProperty(index = 2)
    private String uwYear;

    /**
     * Database column: ATR_CONF_MORTG_QUOTA.DELINQUENCY_RATE
     * Database remarks: delinquency Rate|拖欠比率
     */
    @ApiModelProperty(value = "delinquency Rate|拖欠比率", required = false)
    @ExcelProperty(index = 3)
    @ExcelVaild(regexType = ExcelValidEnum.NOTNULL)
    private BigDecimal delinquencyRate;

    /**
     * Database column: ATR_CONF_MORTG_QUOTA.DEFAULT_RATE
     * Database remarks: default Rate|默认比率
     */
    @ApiModelProperty(value = "default Rate|不良贷款比例", required = false)
    @ExcelProperty(index = 4)
    @ExcelVaild(regexType = ExcelValidEnum.NOTNULL)
    private BigDecimal defaultRate;

    /**
     * Database column: ATR_CONF_MORTG_QUOTA.VAL_DATE_LOAN_OS
     * Database remarks: val date loan os|DSI共享配款部分
     */
    @ApiModelProperty(value = "val date loan os|借出贷款", required = false)
    @ExcelProperty(index = 5)
    @ExcelVaild(regexType = ExcelValidEnum.NOTNULL)
    private BigDecimal valDateLoanOs;

    /**
     * Database column: ATR_CONF_MORTG_QUOTA.SERIAL_NO
     * Database remarks: serialNo|版本号
     */
    @ApiModelProperty(value = "serialNo|版本号", required = true)
    private Long serialNo;

    /**
     * Database column: ATR_CONF_MORTG_QUOTA.VALID_IS
     * Database remarks: validIs|是否有效
     */
    @ApiModelProperty(value = "validIs|是否有效", required = true)
    private String validIs;

    /**
     * Database column: ATR_CONF_MORTG_QUOTA.AUDIT_STATE
     * Database remarks: audit_State|审核状态
     */
    @ApiModelProperty(value = "audit_State|审核状态", required = false)
    private String auditState;

    /**
     * Database column: ATR_CONF_MORTG_QUOTA.CHECKED_MSG
     * Database remarks: checked_msg|审核意见
     */
    @ApiModelProperty(value = "checked_msg|审核意见", required = false)
    private String checkedMsg;

    /**
     * Database column: ATR_CONF_MORTG_QUOTA.CHECKED_ID
     * Database remarks: checked_id|审核人
     */
    @ApiModelProperty(value = "checked_id|审核人", required = false)
    private Long checkedId;

    /**
     * Database column: ATR_CONF_MORTG_QUOTA.CHECKED_TIME
     * Database remarks: checked_time|审核时间
     */
    @ApiModelProperty(value = "checked_time|审核时间", required = false)
    private Date checkedTime;

    /**
     * Database column: ATR_CONF_MORTG_QUOTA.CREATOR_ID
     * Database remarks: creator_id|创建人
     */
    @ApiModelProperty(value = "creator_id|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: ATR_CONF_MORTG_QUOTA.CREATE_TIME
     * Database remarks: create_time|创建时间
     */
    @ApiModelProperty(value = "create_time|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: ATR_CONF_MORTG_QUOTA.UPDATOR_ID
     * Database remarks: updator_id|最后修改人
     */
    @ApiModelProperty(value = "updator_id|最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: ATR_CONF_MORTG_QUOTA.UPDATE_TIME
     * Database remarks: update_time|最后修改时间
     */
    @ApiModelProperty(value = "update_time|最后修改时间", required = false)
    private Date updateTime;

    /* 以下为业务数据传输字段 */
    private String entityCode;

    private String entityEName;

    private String entityCName;

    private String entityLName;

    private String creatorName;

    private String updatorName;

    private String templateFileName;

    List<AtrConfMortgQuotaPeriodVo> confMortgQuotaPeriodVoList = new ArrayList<>();

    private String targetRouter;

    private Long Line;

    private String inValidType;

    private static final long serialVersionUID = 1L;

    public Long getMortgQuotaId() {
        return mortgQuotaId;
    }

    public void setMortgQuotaId(Long mortgQuotaId) {
        this.mortgQuotaId = mortgQuotaId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getInsuredName() {
        return insuredName;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }

    public String getUwYear() {
        return uwYear;
    }

    public void setUwYear(String uwYear) {
        this.uwYear = uwYear;
    }

    public BigDecimal getDelinquencyRate() {
        return delinquencyRate;
    }

    public void setDelinquencyRate(BigDecimal delinquencyRate) {
        this.delinquencyRate = delinquencyRate;
    }

    public BigDecimal getDefaultRate() {
        return defaultRate;
    }

    public void setDefaultRate(BigDecimal defaultRate) {
        this.defaultRate = defaultRate;
    }

    public BigDecimal getValDateLoanOs() {
        return valDateLoanOs;
    }

    public void setValDateLoanOs(BigDecimal valDateLoanOs) {
        this.valDateLoanOs = valDateLoanOs;
    }

    public Long getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Long serialNo) {
        this.serialNo = serialNo;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    public String getCheckedMsg() {
        return checkedMsg;
    }

    public void setCheckedMsg(String checkedMsg) {
        this.checkedMsg = checkedMsg;
    }

    public Long getCheckedId() {
        return checkedId;
    }

    public void setCheckedId(Long checkedId) {
        this.checkedId = checkedId;
    }

    public Date getCheckedTime() {
        return checkedTime;
    }

    public void setCheckedTime(Date checkedTime) {
        this.checkedTime = checkedTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getEntityCode() {
        return entityCode;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public String getEntityEName() {
        return entityEName;
    }

    public void setEntityEName(String entityEName) {
        this.entityEName = entityEName;
    }

    public String getEntityCName() {
        return entityCName;
    }

    public void setEntityCName(String entityCName) {
        this.entityCName = entityCName;
    }

    public String getEntityLName() {
        return entityLName;
    }

    public void setEntityLName(String entityLName) {
        this.entityLName = entityLName;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }

    public String getTemplateFileName() {
        return templateFileName;
    }

    public void setTemplateFileName(String templateFileName) {
        this.templateFileName = templateFileName;
    }

    public List<AtrConfMortgQuotaPeriodVo> getConfMortgQuotaPeriodVoList() {
        return confMortgQuotaPeriodVoList;
    }

    public void setConfMortgQuotaPeriodVoList(List<AtrConfMortgQuotaPeriodVo> confMortgQuotaPeriodVoList) {
        this.confMortgQuotaPeriodVoList = confMortgQuotaPeriodVoList;
    }

    public String getTargetRouter() {
        return targetRouter;
    }

    public void setTargetRouter(String targetRouter) {
        this.targetRouter = targetRouter;
    }

    public Long getLine() {
        return Line;
    }

    public void setLine(Long line) {
        Line = line;
    }

    public String getInValidType() {
        return inValidType;
    }

    public void setInValidType(String inValidType) {
        this.inValidType = inValidType;
    }
}