/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-12-04 15:35:00
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao;

import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcAction;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcActionQueryVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcClaimVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcICPQueryResultVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcICPQueryVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcParamVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcSettledDevAdjustmentVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcSettledDevQueryVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcSettledDevResultVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcStepAccDataVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcStepAccDevDataVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcStepDevDataVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcStepQueryVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcStepVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcTVDiscountInfoVO;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcTVDiscountVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrInflationRatioRangeVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrInflationRatioVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-12-04 15:35:00<br/>
 * Description: ibnr计算-action Dao类<br/>
 * Related Table Name: atr_buss_ibnrcalc_action<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrBussIbnrcalcActionDao extends IDao<AtrBussIbnrcalcAction, Long> {

    String createActionNo(@Param("ran") double ran);

    void calcStep1(@Param("actionNo") String actionNo);
    void calcStep2(@Param("actionNo") String actionNo);
    void calcStep3(@Param("actionNo") String actionNo);
    void calcStep4(@Param("actionNo") String actionNo);
    void calcStep3Manual(AtrBussIbnrcalcParamVo vo);
    void calcStep4Manual(String actionNo,Integer modifyType,String methodType,Long icpId);

    Page<AtrBussIbnrcalcAction> searchPage(AtrBussIbnrcalcActionQueryVo vo, Pageable pageParam);

    Page<AtrBussIbnrcalcICPQueryResultVo> searchICPPage(AtrBussIbnrcalcICPQueryVo vo, Pageable pageParam);

    List<AtrBussIbnrcalcStepVo> findStepBase(AtrBussIbnrcalcStepQueryVo vo);

    List<String> findAccidentNodes(@Param("actionNo") String actionNo);

    List<Long> findDevNos(@Param("actionNo") String actionNo);

    List<AtrBussIbnrcalcStepAccDevDataVo> findAccDevData(AtrBussIbnrcalcStepQueryVo vo);

    List<AtrBussIbnrcalcStepDevDataVo> findDevData(AtrBussIbnrcalcStepQueryVo vo);

    List<AtrBussIbnrcalcStepAccDataVo> findAccData(AtrBussIbnrcalcStepQueryVo vo);

    AtrInflationRatioRangeVo queryBussInfRatioRange(@Param("actionNo") String actionNo);

    List<AtrInflationRatioVo> queryAllBussInfRatio(@Param("actionNo") String actionNo);


    void delete(@Param("actionNo") String actionNo);

    List<AtrBussIbnrcalcSettledDevResultVo> querySettledDevData(AtrBussIbnrcalcSettledDevQueryVo vo);

    void saveSettledDevAdjustment(List<AtrBussIbnrcalcSettledDevAdjustmentVo> vos);

    /**
     * 检查是否需要同步 IBNR 到导入表
     * @return 0-不需要， 1-需要
     */
    int checkNeedToSyncImport(Map<String, Object> paramMap);

    void revokeIbnrImportConfirmation(Map<String, Object> paramMap);

    void syncIbnrImportMain(Map<String, Object> paramMap);

    void syncIbnrImportDetail(Map<String, Object> paramMap);
    AtrBussIbnrcalcClaimVo queryIbnrcalcClaimInfo(String actionNo, Long icpId);
    List<AtrBussIbnrcalcTVDiscountVo> findAtrBussIbnrcalcTVDiscount(String actionNo,Long icpId);
    AtrBussIbnrcalcTVDiscountInfoVO queryIbnrcalcDiscountInfo(String actionNo, Long icpId);
    List<AtrBussIbnrcalcTVDiscountVo> queryIbnrcalcInterestRatio(String actionNo, Long icpId);
    void updateIbnrcalcInterestRatio(String actionNo, Long icpId, Integer devNo,String methodType, BigDecimal newValue);

    void autoJob(Map<String, Object> paramMap);

}