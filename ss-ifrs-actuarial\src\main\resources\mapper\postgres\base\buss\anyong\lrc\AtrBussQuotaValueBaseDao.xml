<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-03-08 16:40:45 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussQuotaValueDao">
  <!-- 本文件由GIS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussQuotaValue">
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="TASK_CODE" property="taskCode" jdbcType="VARCHAR" />
    <result column="entity_id" property="entityId" jdbcType="DECIMAL" />
    <result column="QUOTA_CODE" property="quotaCode" jdbcType="VARCHAR" />
    <result column="MODEL_DEF_ID" property="modelDefId" jdbcType="DECIMAL" />
    <result column="DIMENSION" property="dimension" jdbcType="VARCHAR" />
    <result column="DIMENSION_VALUE" property="dimensionValue" jdbcType="VARCHAR" />
    <result column="DEV_NO" property="devNo" jdbcType="DECIMAL" />
    <result column="QUOTA_VALUE" property="quotaValue" jdbcType="DECIMAL" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    ID, TASK_CODE, entity_id, QUOTA_CODE, MODEL_DEF_ID, DIMENSION, DIMENSION_VALUE, DEV_NO,
    QUOTA_VALUE
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="id != null ">
          and ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="taskCode != null and taskCode != ''">
          and TASK_CODE = #{taskCode,jdbcType=VARCHAR}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="quotaCode != null and quotaCode != ''">
          and QUOTA_CODE = #{quotaCode,jdbcType=VARCHAR}
      </if>
      <if test="modelDefId != null ">
          and MODEL_DEF_ID = #{modelDefId,jdbcType=DECIMAL}
      </if>
      <if test="dimension != null and dimension != ''">
          and DIMENSION = #{dimension,jdbcType=VARCHAR}
      </if>
      <if test="dimensionValue != null and dimensionValue != ''">
          and DIMENSION_VALUE = #{dimensionValue,jdbcType=VARCHAR}
      </if>
      <if test="devNo != null ">
          and DEV_NO = #{devNo,jdbcType=DECIMAL}
      </if>
      <if test="quotaValue != null ">
          and QUOTA_VALUE = #{quotaValue,jdbcType=DECIMAL}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.id != null ">
          and ID = #{condition.id,jdbcType=DECIMAL}
      </if>
      <if test="condition.taskCode != null and condition.taskCode != ''">
          and TASK_CODE = #{condition.taskCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.entityId != null ">
          and entity_id = #{condition.entityId,jdbcType=DECIMAL}
      </if>
      <if test="condition.quotaCode != null and condition.quotaCode != ''">
          and QUOTA_CODE = #{condition.quotaCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.modelDefId != null ">
          and MODEL_DEF_ID = #{condition.modelDefId,jdbcType=DECIMAL}
      </if>
      <if test="condition.dimension != null and condition.dimension != ''">
          and DIMENSION = #{condition.dimension,jdbcType=VARCHAR}
      </if>
      <if test="condition.dimensionValue != null and condition.dimensionValue != ''">
          and DIMENSION_VALUE = #{condition.dimensionValue,jdbcType=VARCHAR}
      </if>
      <if test="condition.devNo != null ">
          and DEV_NO = #{condition.devNo,jdbcType=DECIMAL}
      </if>
      <if test="condition.quotaValue != null ">
          and QUOTA_VALUE = #{condition.quotaValue,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="id != null ">
          and ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="taskCode != null and taskCode != ''">
          and TASK_CODE = #{taskCode,jdbcType=VARCHAR}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="quotaCode != null and quotaCode != ''">
          and QUOTA_CODE = #{quotaCode,jdbcType=VARCHAR}
      </if>
      <if test="modelDefId != null ">
          and MODEL_DEF_ID = #{modelDefId,jdbcType=DECIMAL}
      </if>
      <if test="dimension != null and dimension != ''">
          and DIMENSION = #{dimension,jdbcType=VARCHAR}
      </if>
      <if test="dimensionValue != null and dimensionValue != ''">
          and DIMENSION_VALUE = #{dimensionValue,jdbcType=VARCHAR}
      </if>
      <if test="devNo != null ">
          and DEV_NO = #{devNo,jdbcType=DECIMAL}
      </if>
      <if test="quotaValue != null ">
          and QUOTA_VALUE = #{quotaValue,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_DD_QUOTA_VALUE
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_DD_QUOTA_VALUE
    where ID in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_DD_QUOTA_VALUE
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussQuotaValue">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_DD_QUOTA_VALUE
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_DD_QUOTA_VALUE
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="ID" keyProperty="id" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussQuotaValue">
    insert into ATR_BUSS_DD_QUOTA_VALUE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="taskCode != null">
        TASK_CODE,
      </if>
      <if test="entityId != null">
        entity_id,
      </if>
      <if test="quotaCode != null">
        QUOTA_CODE,
      </if>
      <if test="modelDefId != null">
        MODEL_DEF_ID,
      </if>
      <if test="dimension != null">
        DIMENSION,
      </if>
      <if test="dimensionValue != null">
        DIMENSION_VALUE,
      </if>
      <if test="devNo != null">
        DEV_NO,
      </if>
      <if test="quotaValue != null">
        QUOTA_VALUE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="taskCode != null">
        #{taskCode,jdbcType=VARCHAR},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=DECIMAL},
      </if>
      <if test="quotaCode != null">
        #{quotaCode,jdbcType=VARCHAR},
      </if>
      <if test="modelDefId != null">
        #{modelDefId,jdbcType=DECIMAL},
      </if>
      <if test="dimension != null">
        #{dimension,jdbcType=VARCHAR},
      </if>
      <if test="dimensionValue != null">
        #{dimensionValue,jdbcType=VARCHAR},
      </if>
      <if test="devNo != null">
        #{devNo,jdbcType=DECIMAL},
      </if>
      <if test="quotaValue != null">
        #{quotaValue,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all 
    <foreach collection="list" item="item" index="index">
      into ATR_BUSS_DD_QUOTA_VALUE values 
       (#{item.id,jdbcType=DECIMAL}, #{item.taskCode,jdbcType=VARCHAR}, 
        #{item.entityId,jdbcType=DECIMAL}, #{item.quotaCode,jdbcType=VARCHAR}, #{item.modelDefId,jdbcType=DECIMAL},
        #{item.dimension,jdbcType=VARCHAR}, #{item.dimensionValue,jdbcType=VARCHAR}, #{item.devNo,jdbcType=DECIMAL}, 
        #{item.quotaValue,jdbcType=DECIMAL})
    </foreach>
    select 1 
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussQuotaValue">
    update ATR_BUSS_DD_QUOTA_VALUE
    <set>
      <if test="taskCode != null">
        TASK_CODE = #{taskCode,jdbcType=VARCHAR},
      </if>
      <if test="entityId != null">
        entity_id = #{entityId,jdbcType=DECIMAL},
      </if>
      <if test="quotaCode != null">
        QUOTA_CODE = #{quotaCode,jdbcType=VARCHAR},
      </if>
      <if test="modelDefId != null">
        MODEL_DEF_ID = #{modelDefId,jdbcType=DECIMAL},
      </if>
      <if test="dimension != null">
        DIMENSION = #{dimension,jdbcType=VARCHAR},
      </if>
      <if test="dimensionValue != null">
        DIMENSION_VALUE = #{dimensionValue,jdbcType=VARCHAR},
      </if>
      <if test="devNo != null">
        DEV_NO = #{devNo,jdbcType=DECIMAL},
      </if>
      <if test="quotaValue != null">
        QUOTA_VALUE = #{quotaValue,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussQuotaValue">
    update ATR_BUSS_DD_QUOTA_VALUE
    <set>
      <if test="record.taskCode != null">
        TASK_CODE = #{record.taskCode,jdbcType=VARCHAR},
      </if>
      <if test="record.entityId != null">
        entity_id = #{record.entityId,jdbcType=DECIMAL},
      </if>
      <if test="record.quotaCode != null">
        QUOTA_CODE = #{record.quotaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.modelDefId != null">
        MODEL_DEF_ID = #{record.modelDefId,jdbcType=DECIMAL},
      </if>
      <if test="record.dimension != null">
        DIMENSION = #{record.dimension,jdbcType=VARCHAR},
      </if>
      <if test="record.dimensionValue != null">
        DIMENSION_VALUE = #{record.dimensionValue,jdbcType=VARCHAR},
      </if>
      <if test="record.devNo != null">
        DEV_NO = #{record.devNo,jdbcType=DECIMAL},
      </if>
      <if test="record.quotaValue != null">
        QUOTA_VALUE = #{record.quotaValue,jdbcType=DECIMAL},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from ATR_BUSS_DD_QUOTA_VALUE
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from ATR_BUSS_DD_QUOTA_VALUE
    where ID in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from ATR_BUSS_DD_QUOTA_VALUE
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussQuotaValue">
    select count(1) from ATR_BUSS_DD_QUOTA_VALUE
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>