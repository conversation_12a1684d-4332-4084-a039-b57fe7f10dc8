package com.ss.ifrs.actuarial.api;

import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.*;
import com.ss.ifrs.actuarial.service.AtrBussReserveDacService;
import com.ss.ifrs.actuarial.service.AtrBussReserveEarnedService;
import com.ss.ifrs.actuarial.service.AtrBussReserveOsService;
import com.ss.ifrs.actuarial.service.AtrBussReserveUprService;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.platform.util.CheckParamsUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.*;

@RestController
@RequestMapping("/reserve")
@Api(value = "准备金API")
public class AtrBussReserveApi extends BaseApi {

    @Autowired
    AtrBussReserveUprService atrBussReserveUprService;

    @Autowired
    AtrBussReserveDacService atrBussReserveDacService;

    @Autowired
    AtrBussReserveOsService atrBussReserveOsService;

    @Autowired
    AtrBussReserveEarnedService atrBussReserveEarnedService;

    @ApiOperation(value = "upr准备金提取")
    @RequestMapping(value = "/upr/extract", method = RequestMethod.POST)
    public BaseResponse<Object> reserveUprExtract(HttpServletRequest request, @RequestBody @Validated
             AtrBussReserveUprVo atrBussReserveUprVo, BindingResult br) {
        if (br.hasErrors()) {
            // 该状态码为“其它格式转换错误”
            StringBuffer errorMessages = new StringBuffer();
            for (int i = 0; i < br.getErrorCount(); i++) {
                errorMessages.append(br.getAllErrors().get(i).getDefaultMessage() + ";");
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.FORMAT_TRANSFORM_ERROR,new CheckParamsUtil().getMessages(errorMessages.toString()));
        }
        Long userId = this.loginUserId(request);
        atrBussReserveUprService.reserveUprExtract(atrBussReserveUprVo, userId);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
    }

    @ApiOperation(value = "upr准备金主表查询")
    @RequestMapping(value = "/upr/enquiry", method = RequestMethod.POST)
    public BaseResponse<Object> reserveUprEnquiry(@RequestBody AtrBussReserveUprVo atrBussReserveUprVo, int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrBussReserveUprVo> atrBussReserveUprVoPage = atrBussReserveUprService.searchPage(atrBussReserveUprVo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("atrReserveUprVoList", atrBussReserveUprVoPage);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "校验UPR是否已确认版本")
    @RequestMapping(value = "/upr/checkIsConfirm", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> checkIsConfirm(@RequestBody AtrBussReserveUprVo atrBussReserveUprVo, HttpServletRequest request) {
        Boolean result = atrBussReserveUprService.checkIsConfirm(atrBussReserveUprVo);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "upr准备金确认")
    @RequestMapping(value = "/upr/confirm", method = RequestMethod.POST)
    public BaseResponse<Object> reserveUprConfirm(HttpServletRequest request, @RequestBody AtrBussReserveUprVo atrBussReserveUprVo) {
        Long userId = this.loginUserId(request);
        atrBussReserveUprService.confirm(atrBussReserveUprVo, userId);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
    }
    @ApiOperation(value = "upr准备金明细查询")
    @RequestMapping(value = "/upr/find_by_id", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findReserveUpr(HttpServletRequest request, @RequestBody AtrBussReserveUprVo atrBussReserveUprVo, int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrBussReserveUprDetailVo> atrBussReserveUprVoPage = atrBussReserveUprService.findForDataTables(atrBussReserveUprVo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("uprDetailsVoList", atrBussReserveUprVoPage);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "upr准备金导出")
    @RequestMapping(value = "/upr/download", method = RequestMethod.POST)
    public void uprDownload(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrBussReserveUprVo atrBussReserveUprVo) throws IOException {
        Long userId = this.loginUserId(request);
        try {
            atrBussReserveUprService.downloadDataFile(response, atrBussReserveUprVo);
        } catch (Exception ex) {
            logger.error(ex.getLocalizedMessage(), ex);
            throw ex;
        }
    }

    @ApiOperation(value = "upr准备金导出")
    @RequestMapping(value = "/upr/find_upr_excel_list", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> uprExcelList(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrBussReserveUprVo atrBussReserveUprVo) throws IOException {
        try {
            List<AtrBussReserveUprDetailVo> osVoList = atrBussReserveUprService.findDownloadList(atrBussReserveUprVo);
            Map<String, Object> result = new HashMap<>();
            result.put("uprExportList" , osVoList);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
        } catch (Exception ex) {
            logger.error(ex.getLocalizedMessage(), ex);
            throw ex;
        }
    }
    @ApiOperation(value = "删除upr准备金信息")
    @RequestMapping(value = "/upr/delete/{reserveUprId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<Object> deleteUpr(HttpServletRequest request, @PathVariable("reserveUprId") Long reserveUprId) {
        try {
            Long userId = this.loginUserId(request);
            atrBussReserveUprService.delete(reserveUprId, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }



    @ApiOperation(value = "佣金递延准备金提取")
    @RequestMapping(value = "/dac/extract", method = RequestMethod.POST)
    public BaseResponse<Object> reserveDacExtract(HttpServletRequest request, @RequestBody @Validated AtrBussReserveDacVo atrBussReserveDacVo, BindingResult br) {
        if (br.hasErrors()) {
            // 该状态码为“其它格式转换错误”
            StringBuffer errorMessages = new StringBuffer();
            for (int i = 0; i < br.getErrorCount(); i++) {
                errorMessages.append(br.getAllErrors().get(i).getDefaultMessage() + ";");
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.FORMAT_TRANSFORM_ERROR,new CheckParamsUtil().getMessages(errorMessages.toString()));
        }
        Long userId = this.loginUserId(request);
        atrBussReserveDacService.reserveDacExtract(atrBussReserveDacVo, userId);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
    }

    @ApiOperation(value = "佣金递延准备金主表查询")
    @RequestMapping(value = "/dac/enquiry", method = RequestMethod.POST)
    public BaseResponse<Object> reserveDacEnquiry(@RequestBody AtrBussReserveDacVo atrBussReserveDacVo, int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrBussReserveDacVo> atrBussReserveDacVoPage = atrBussReserveDacService.searchPage(atrBussReserveDacVo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("atrReserveDacVoList", atrBussReserveDacVoPage);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
    }
    @ApiOperation(value = "校验佣金递延是否已确认版本")
    @RequestMapping(value = "/dac/checkIsConfirm", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> checkDacIsConfirm(@RequestBody AtrBussReserveDacVo atrBussReserveDacVo, HttpServletRequest request) {
        Boolean result = atrBussReserveDacService.checkIsConfirm(atrBussReserveDacVo);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "佣金递延准备金确认")
    @RequestMapping(value = "/dac/confirm", method = RequestMethod.POST)
    public BaseResponse<Object> reserveDacConfirm(HttpServletRequest request, @RequestBody AtrBussReserveDacVo atrBussReserveDacVo) {
        Long userId = this.loginUserId(request);
        atrBussReserveDacService.confirm(atrBussReserveDacVo, userId);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
    }
    @ApiOperation(value = "佣金递延准备金明细查询")
    @RequestMapping(value = "/dac/find_by_id", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findReserveDac(HttpServletRequest request, @RequestBody AtrBussReserveDacVo atrBussReserveDacVo, int _pageSize, int _pageNo) {

        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrBussReserveDacDetailVo> atrBussReserveDacVoPage = atrBussReserveDacService.findForDataTables(atrBussReserveDacVo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("dacDetailsVoList", atrBussReserveDacVoPage);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "dac准备金导出")
    @RequestMapping(value = "/dac/find_dac_excel_list", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> dacExcelList(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrBussReserveDacVo atrBussReserveDacVo) throws IOException {
        try {
            List<AtrBussReserveDacDetailVo> osVoList = atrBussReserveDacService.findDownloadList(atrBussReserveDacVo);
            Map<String, Object> result = new HashMap<>();
            result.put("dacExportList" , osVoList);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
        } catch (Exception ex) {
            logger.error(ex.getLocalizedMessage(), ex);
            throw ex;
        }
    }

    @ApiOperation(value = "删除dac准备金信息")
    @RequestMapping(value = "/dac/delete/{reserveDacId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<Object> deleteDac(HttpServletRequest request, @PathVariable("reserveDacId") Long reserveDacId) {
        try {
            Long userId = this.loginUserId(request);
            atrBussReserveDacService.delete(reserveDacId, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }


    @ApiOperation(value = "os准备金提取")
    @RequestMapping(value = "/os/extract", method = RequestMethod.POST)
    public BaseResponse<Object> reserveOsExtract(HttpServletRequest request, @RequestBody @Validated
             AtrBussReserveOsVo atrBussReserveOsVo, BindingResult br
    ) {
        if (br.hasErrors()) {
            // 该状态码为“其它格式转换错误”
            StringBuffer errorMessages = new StringBuffer();
            for (int i = 0; i < br.getErrorCount(); i++) {
                errorMessages.append(br.getAllErrors().get(i).getDefaultMessage() + ";");
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.FORMAT_TRANSFORM_ERROR,new CheckParamsUtil().getMessages(errorMessages.toString()));
        }
        Long userId = this.loginUserId(request);
        atrBussReserveOsService.reserveOsExtract(atrBussReserveOsVo, userId);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, "success");
    }

    @ApiOperation(value = "os准备金主表查询")
    @RequestMapping(value = "/os/enquiry", method = RequestMethod.POST)
    public BaseResponse<Object> reserveOsEnquiry(@RequestBody AtrBussReserveOsVo atrBussReserveOsVo, int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrBussReserveOsVo> atrBussReserveOsVoPage = atrBussReserveOsService.searchPage(atrBussReserveOsVo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("atrReserveOsVoList", atrBussReserveOsVoPage);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "校验os是否已确认版本")
    @RequestMapping(value = "/os/checkIsConfirm", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> checkIsConfirmOs(@RequestBody AtrBussReserveOsVo atrBussReserveOsVo, HttpServletRequest request) {
        Boolean result = atrBussReserveOsService.checkIsConfirm(atrBussReserveOsVo);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "os准备金确认")
    @RequestMapping(value = "/os/confirm", method = RequestMethod.POST)
    public BaseResponse<Object> reserveOsConfirm(HttpServletRequest request, @RequestBody AtrBussReserveOsVo atrBussReserveOsVo) {
        Long userId = this.loginUserId(request);
        atrBussReserveOsService.confirm(atrBussReserveOsVo, userId);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
    }
    @ApiOperation(value = "os准备金明细查询")
    @RequestMapping(value = "/os/find_by_id", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findReserveOs(HttpServletRequest request, @RequestBody AtrBussReserveOsVo atrBussReserveOsVo, int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrBussReserveOsDetailVo> atrBussReserveOsVoPage = atrBussReserveOsService.findForDataTables(atrBussReserveOsVo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("osDetailsVoList", atrBussReserveOsVoPage);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "os准备金导出List")
    @RequestMapping(value = "/os/find_os_download_list", method = RequestMethod.POST)
    public BaseResponse<Object> findOsDownloadList(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrBussReserveOsVo atrBussReserveOsVo){
        Long userId = this.loginUserId(request);
        List<AtrBussReserveOsVo> osVoList = atrBussReserveOsService.findOsDownloadList(atrBussReserveOsVo);
        Map<String, Object> result = new HashMap<>();
        result.put("osExportList" +
                "" +
                "", osVoList);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "删除OS准备金信息")
    @RequestMapping(value = "/os/delete/{reserveOsId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<Object> deleteOs(HttpServletRequest request, @PathVariable("reserveOsId") Long reserveOsId) {
        try {
            Long userId = this.loginUserId(request);
            atrBussReserveOsService.delete(reserveOsId, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "ear准备金提取")
    @RequestMapping(value = "/ear/extract", method = RequestMethod.POST)
    public BaseResponse<Object> reserveEarExtract(HttpServletRequest request, @RequestBody @Validated
            AtrBussReserveEarnedVo atrBussReserveEarnedVo, BindingResult br) {
        if (br.hasErrors()) {
            // 该状态码为“其它格式转换错误”
            StringBuffer errorMessages = new StringBuffer();
            for (int i = 0; i < br.getErrorCount(); i++) {
                errorMessages.append(br.getAllErrors().get(i).getDefaultMessage() + ";");
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.FORMAT_TRANSFORM_ERROR,new CheckParamsUtil().getMessages(errorMessages.toString()));
        }
        Long userId = this.loginUserId(request);
        atrBussReserveEarnedService.reserveEarnedExtract(atrBussReserveEarnedVo, userId);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
    }

    @ApiOperation(value = "ear准备金主表查询")
    @RequestMapping(value = "/ear/enquiry", method = RequestMethod.POST)
    public BaseResponse<Object> reserveEarEnquiry(@RequestBody AtrBussReserveEarnedVo atrBussReserveEarnedVo, int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrBussReserveEarnedVo> atrBussReserveEarnedVoPage = atrBussReserveEarnedService.searchPage(atrBussReserveEarnedVo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("atrReserveEarnedVoList", atrBussReserveEarnedVoPage);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "校验ear是否已确认版本")
    @RequestMapping(value = "/ear/checkIsConfirm", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> checkIsConfirm(@RequestBody AtrBussReserveEarnedVo atrBussReserveEarnedVo, HttpServletRequest request) {
        Boolean result = atrBussReserveEarnedService.checkIsConfirm(atrBussReserveEarnedVo);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "earned准备金确认")
    @RequestMapping(value = "/ear/confirm", method = RequestMethod.POST)
    public BaseResponse<Object> reserveEarConfirm(HttpServletRequest request, @RequestBody AtrBussReserveEarnedVo atrBussReserveEarnedVo) {
        Long userId = this.loginUserId(request);
        atrBussReserveEarnedService.confirm(atrBussReserveEarnedVo, userId);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
    }

    @ApiOperation(value = "earned准备金明细查询")
    @RequestMapping(value = "/ear/find_by_id", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findReserveEar(HttpServletRequest request, @RequestBody AtrBussReserveEarnedVo atrBussReserveEarnedVo, int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrBussReserveEarnedDetailVo> atrBussReserveEarnedVoPage = atrBussReserveEarnedService.findForDataTables(atrBussReserveEarnedVo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("earnedDetailsVoList", atrBussReserveEarnedVoPage);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "earned准备金导出")
    @RequestMapping(value = "/ear/find_earned_excel_list", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> earExcelList(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrBussReserveEarnedVo atrBussReserveEarnedVo) throws IOException {
        try {
            List<AtrBussReserveEarnedDetailVo> osVoList = atrBussReserveEarnedService.findDownloadList(atrBussReserveEarnedVo);
            Map<String, Object> result = new HashMap<>();
            result.put("earnedExportList" , osVoList);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
        } catch (Exception ex) {
            logger.error(ex.getLocalizedMessage(), ex);
            throw ex;
        }
    }

    @ApiOperation(value = "删除earned准备金信息")
    @RequestMapping(value = "/ear/delete/{reserveEarnedId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<Object> deleteEarned(HttpServletRequest request, @PathVariable("reserveEarnedId") Long reserveEarnedId) {
        try {
            Long userId = this.loginUserId(request);
            atrBussReserveEarnedService.delete(reserveEarnedId, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }
}
