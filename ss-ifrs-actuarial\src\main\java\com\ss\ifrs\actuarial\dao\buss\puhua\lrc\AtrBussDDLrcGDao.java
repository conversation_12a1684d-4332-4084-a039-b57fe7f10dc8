/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2023-02-10 17:38:42
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao.buss.puhua.lrc;


import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrAllocActionVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrAllocResultVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussDDLrcIcgCalc;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo;
import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrBussBecfViewVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.mapping.ResultSetType;
import org.apache.ibatis.session.ResultHandler;

import java.util.List;
import java.util.Map;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-10 17:38:42<br/>
 * Description: LRC 计算结果主表(合同组维度，直保&临分分入) Dao类<br/>
 * Related Table Name: ATR_BUSS_DD_LRC_ICG_CALC<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrBussDDLrcGDao extends IDao<AtrBussDDLrcIcgCalc, Long> {

    Page<List<Map<String, Object>>> findLrcIcgDetail(AtrDapDrawVo atrDapDrawVo, Pageable pageParam);

    Long countLrcGDetail(AtrBussBecfViewVo atrBussBecfViewVo);

    @Options(resultSetType = ResultSetType.FORWARD_ONLY, fetchSize = Integer.MIN_VALUE)
    void findLrcHandelIcgDetail(ResultHandler<Map<String, Object>> resultHandler, AtrBussBecfViewVo atrBussBecfViewVo);
}