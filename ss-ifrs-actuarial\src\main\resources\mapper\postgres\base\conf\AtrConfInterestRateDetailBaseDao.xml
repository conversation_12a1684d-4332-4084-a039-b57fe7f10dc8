<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2022-06-24 18:16:46 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.conf.AtrConfInterestRateDetailDao">
  <!-- 本文件由GIS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestRateDetail">
    <id column="interest_rate_detail_id" property="interestRateDetailId" jdbcType="NUMERIC" />
    <result column="interest_rate_id" property="interestRateId" jdbcType="NUMERIC" />
    <result column="norm_month" property="normMonth" jdbcType="NUMERIC" />
    <result column="norm_time" property="normTime" jdbcType="NUMERIC" />
    <result column="low_value" property="lowValue" jdbcType="NUMERIC" />
    <result column="low_profit_rate" property="lowProfitRate" jdbcType="NUMERIC" />
    <result column="high_value" property="highValue" jdbcType="NUMERIC" />
    <result column="high_profit_rate" property="highProfitRate" jdbcType="NUMERIC" />
    <result column="interpolated_spot_rate" property="interpolatedSpotRate" jdbcType="NUMERIC" />
    <result column="liquidity_premium" property="liquidityPremium" jdbcType="NUMERIC" />
    <result column="annual_spot_rate_with_premium" property="annualSpotRateWithPremium" jdbcType="NUMERIC" />
    <result column="monthly_spot_rate_with_premium" property="monthlySpotRateWithPremium" jdbcType="NUMERIC" />
    <result column="monthly_forward_rate_with_premium" property="monthlyForwardRateWithPremium" jdbcType="NUMERIC" />
    <result column="forward_discount_factor" property="forwardDiscountFactor" jdbcType="NUMERIC" />
    <result column="spot_discount_factor" property="spotDiscountFactor" jdbcType="NUMERIC" />
    <result column="creator_id" property="creatorId" jdbcType="NUMERIC" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    interest_rate_detail_id, interest_rate_id, norm_month, norm_time, low_value, low_profit_rate, 
    high_value, high_profit_rate, interpolated_spot_rate, liquidity_premium, annual_spot_rate_with_premium, 
    monthly_spot_rate_with_premium, monthly_forward_rate_with_premium, forward_discount_factor,
    spot_discount_factor, creator_id, create_time
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="interestRateDetailId != null ">
          and interest_rate_detail_id = #{interestRateDetailId,jdbcType=NUMERIC}
      </if>
      <if test="interestRateId != null ">
          and interest_rate_id = #{interestRateId,jdbcType=NUMERIC}
      </if>
      <if test="normMonth != null ">
          and norm_month = #{normMonth,jdbcType=NUMERIC}
      </if>
      <if test="normTime != null ">
          and norm_time = #{normTime,jdbcType=NUMERIC}
      </if>
      <if test="lowValue != null ">
          and low_value = #{lowValue,jdbcType=NUMERIC}
      </if>
      <if test="lowProfitRate != null ">
          and low_profit_rate = #{lowProfitRate,jdbcType=NUMERIC}
      </if>
      <if test="highValue != null ">
          and high_value = #{highValue,jdbcType=NUMERIC}
      </if>
      <if test="highProfitRate != null ">
          and high_profit_rate = #{highProfitRate,jdbcType=NUMERIC}
      </if>
      <if test="interpolatedSpotRate != null ">
          and interpolated_spot_rate = #{interpolatedSpotRate,jdbcType=NUMERIC}
      </if>
      <if test="liquidityPremium != null ">
          and liquidity_premium = #{liquidityPremium,jdbcType=NUMERIC}
      </if>
      <if test="annualSpotRateWithPremium != null ">
          and annual_spot_rate_with_premium = #{annualSpotRateWithPremium,jdbcType=NUMERIC}
      </if>
      <if test="monthlySpotRateWithPremium != null ">
          and monthly_spot_rate_with_premium = #{monthlySpotRateWithPremium,jdbcType=NUMERIC}
      </if>
      <if test="monthlyForwardRateWithPremium != null ">
          and monthly_forward_rate_with_premium = #{monthlyForwardRateWithPremium,jdbcType=NUMERIC}
      </if>
      <if test="forwardDiscountFactor != null ">
          and forward_discount_factor = #{forwardDiscountFactor,jdbcType=NUMERIC}
      </if>
      <if test="spotDiscountFactor != null ">
          and spot_discount_factor = #{spotDiscountFactor,jdbcType=NUMERIC}
      </if>
      <if test="creatorId != null ">
          and creator_id = #{creatorId,jdbcType=NUMERIC}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.interestRateDetailId != null ">
          and interest_rate_detail_id = #{condition.interestRateDetailId,jdbcType=NUMERIC}
      </if>
      <if test="condition.interestRateId != null ">
          and interest_rate_id = #{condition.interestRateId,jdbcType=NUMERIC}
      </if>
      <if test="condition.normMonth != null ">
          and norm_month = #{condition.normMonth,jdbcType=NUMERIC}
      </if>
      <if test="condition.normTime != null ">
          and norm_time = #{condition.normTime,jdbcType=NUMERIC}
      </if>
      <if test="condition.lowValue != null ">
          and low_value = #{condition.lowValue,jdbcType=NUMERIC}
      </if>
      <if test="condition.lowProfitRate != null ">
          and low_profit_rate = #{condition.lowProfitRate,jdbcType=NUMERIC}
      </if>
      <if test="condition.highValue != null ">
          and high_value = #{condition.highValue,jdbcType=NUMERIC}
      </if>
      <if test="condition.highProfitRate != null ">
          and high_profit_rate = #{condition.highProfitRate,jdbcType=NUMERIC}
      </if>
      <if test="condition.interpolatedSpotRate != null ">
          and interpolated_spot_rate = #{condition.interpolatedSpotRate,jdbcType=NUMERIC}
      </if>
      <if test="condition.liquidityPremium != null ">
          and liquidity_premium = #{condition.liquidityPremium,jdbcType=NUMERIC}
      </if>
      <if test="condition.annualSpotRateWithPremium != null ">
          and annual_spot_rate_with_premium = #{condition.annualSpotRateWithPremium,jdbcType=NUMERIC}
      </if>
      <if test="condition.monthlySpotRateWithPremium != null ">
          and monthly_spot_rate_with_premium = #{condition.monthlySpotRateWithPremium,jdbcType=NUMERIC}
      </if>
      <if test="condition.monthlyForwardRateWithPremium != null ">
          and monthly_forward_rate_with_premium = #{condition.monthlyForwardRateWithPremium,jdbcType=NUMERIC}
      </if>
      <if test="condition.forwardDiscountFactor != null ">
          and forward_discount_factor = #{condition.forwardDiscountFactor,jdbcType=NUMERIC}
      </if>
      <if test="condition.spotDiscountFactor != null ">
          and spot_discount_factor = #{condition.spotDiscountFactor,jdbcType=NUMERIC}
      </if>
      <if test="condition.creatorId != null ">
          and creator_id = #{condition.creatorId,jdbcType=NUMERIC}
      </if>
      <if test="condition.createTime != null ">
          and create_time = #{condition.createTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="interestRateDetailId != null ">
          and interest_rate_detail_id = #{interestRateDetailId,jdbcType=NUMERIC}
      </if>
      <if test="interestRateId != null ">
          and interest_rate_id = #{interestRateId,jdbcType=NUMERIC}
      </if>
      <if test="normMonth != null ">
          and norm_month = #{normMonth,jdbcType=NUMERIC}
      </if>
      <if test="normTime != null ">
          and norm_time = #{normTime,jdbcType=NUMERIC}
      </if>
      <if test="lowValue != null ">
          and low_value = #{lowValue,jdbcType=NUMERIC}
      </if>
      <if test="lowProfitRate != null ">
          and low_profit_rate = #{lowProfitRate,jdbcType=NUMERIC}
      </if>
      <if test="highValue != null ">
          and high_value = #{highValue,jdbcType=NUMERIC}
      </if>
      <if test="highProfitRate != null ">
          and high_profit_rate = #{highProfitRate,jdbcType=NUMERIC}
      </if>
      <if test="interpolatedSpotRate != null ">
          and interpolated_spot_rate = #{interpolatedSpotRate,jdbcType=NUMERIC}
      </if>
      <if test="liquidityPremium != null ">
          and liquidity_premium = #{liquidityPremium,jdbcType=NUMERIC}
      </if>
      <if test="annualSpotRateWithPremium != null ">
          and annual_spot_rate_with_premium = #{annualSpotRateWithPremium,jdbcType=NUMERIC}
      </if>
      <if test="monthlySpotRateWithPremium != null ">
          and monthly_spot_rate_with_premium = #{monthlySpotRateWithPremium,jdbcType=NUMERIC}
      </if>
      <if test="monthlyForwardRateWithPremium != null ">
          and monthly_forward_rate_with_premium = #{monthlyForwardRateWithPremium,jdbcType=NUMERIC}
      </if>
      <if test="forwardDiscountFactor != null ">
          and forward_discount_factor = #{forwardDiscountFactor,jdbcType=NUMERIC}
      </if>
      <if test="spotDiscountFactor != null ">
          and spot_discount_factor = #{spotDiscountFactor,jdbcType=NUMERIC}
      </if>
      <if test="creatorId != null ">
          and creator_id = #{creatorId,jdbcType=NUMERIC}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_interest_rate_detail
    where interest_rate_detail_id = #{interestRateDetailId,jdbcType=NUMERIC}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_interest_rate_detail
    where interest_rate_detail_id in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_interest_rate_detail
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestRateDetail">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_interest_rate_detail
    <include refid="Base_Select_By_Entity_Where" />
    order by norm_month
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_interest_rate_detail
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="interest_rate_detail_id" keyProperty="interestRateDetailId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestRateDetail">
    <selectKey resultType="long" keyProperty="interestRateDetailId" order="BEFORE">
      select nextval('atr_seq_conf_interest_rate_dtl') as sequenceNo
    </selectKey>
    insert into atr_conf_interest_rate_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="interestRateDetailId != null">
        interest_rate_detail_id,
      </if>
      <if test="interestRateId != null">
        interest_rate_id,
      </if>
      <if test="normMonth != null">
        norm_month,
      </if>
      <if test="normTime != null">
        norm_time,
      </if>
      <if test="lowValue != null">
        low_value,
      </if>
      <if test="lowProfitRate != null">
        low_profit_rate,
      </if>
      <if test="highValue != null">
        high_value,
      </if>
      <if test="highProfitRate != null">
        high_profit_rate,
      </if>
      <if test="interpolatedSpotRate != null">
        interpolated_spot_rate,
      </if>
      <if test="liquidityPremium != null">
        liquidity_premium,
      </if>
      <if test="annualSpotRateWithPremium != null">
        annual_spot_rate_with_premium,
      </if>
      <if test="monthlySpotRateWithPremium != null">
        monthly_spot_rate_with_premium,
      </if>
      <if test="monthlyForwardRateWithPremium != null">
        monthly_forward_rate_with_premium,
      </if>
      <if test="forwardDiscountFactor != null">
        forward_discount_factor,
      </if>
      <if test="spotDiscountFactor != null">
        spot_discount_factor,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="interestRateDetailId != null">
        #{interestRateDetailId,jdbcType=NUMERIC},
      </if>
      <if test="interestRateId != null">
        #{interestRateId,jdbcType=NUMERIC},
      </if>
      <if test="normMonth != null">
        #{normMonth,jdbcType=NUMERIC},
      </if>
      <if test="normTime != null">
        #{normTime,jdbcType=NUMERIC},
      </if>
      <if test="lowValue != null">
        #{lowValue,jdbcType=NUMERIC},
      </if>
      <if test="lowProfitRate != null">
        #{lowProfitRate,jdbcType=NUMERIC},
      </if>
      <if test="highValue != null">
        #{highValue,jdbcType=NUMERIC},
      </if>
      <if test="highProfitRate != null">
        #{highProfitRate,jdbcType=NUMERIC},
      </if>
      <if test="interpolatedSpotRate != null">
        #{interpolatedSpotRate,jdbcType=NUMERIC},
      </if>
      <if test="liquidityPremium != null">
        #{liquidityPremium,jdbcType=NUMERIC},
      </if>
      <if test="annualSpotRateWithPremium != null">
        #{annualSpotRateWithPremium,jdbcType=NUMERIC},
      </if>
      <if test="monthlySpotRateWithPremium != null">
        #{monthlySpotRateWithPremium,jdbcType=NUMERIC},
      </if>
      <if test="monthlyForwardRateWithPremium != null">
        #{monthlyForwardRateWithPremium,jdbcType=NUMERIC},
      </if>
      <if test="forwardDiscountFactor != null">
        #{forwardDiscountFactor,jdbcType=NUMERIC},
      </if>
      <if test="spotDiscountFactor != null">
        #{spotDiscountFactor,jdbcType=NUMERIC},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=NUMERIC},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert into atr_conf_interest_rate_detail
     (interest_rate_detail_id, interest_rate_id, 
      norm_month, norm_time, low_value, 
      low_profit_rate, high_value, high_profit_rate,
      interpolated_spot_rate, liquidity_premium, annual_spot_rate_with_premium,
      monthly_spot_rate_with_premium, monthly_forward_rate_with_premium,
      forward_discount_factor, spot_discount_factor,
      creator_id, create_time)
     values 
    <foreach collection="list" item="item" index="index" separator=",">
       (#{item.interestRateDetailId,jdbcType=NUMERIC}, #{item.interestRateId,jdbcType=NUMERIC}, 
        #{item.normMonth,jdbcType=NUMERIC}, #{item.normTime,jdbcType=NUMERIC}, #{item.lowValue,jdbcType=NUMERIC}, 
        #{item.lowProfitRate,jdbcType=NUMERIC}, #{item.highValue,jdbcType=NUMERIC}, #{item.highProfitRate,jdbcType=NUMERIC},
        #{item.interpolatedSpotRate,jdbcType=NUMERIC}, #{item.liquidityPremium,jdbcType=NUMERIC}, #{item.annualSpotRateWithPremium,jdbcType=NUMERIC},
        #{item.monthlySpotRateWithPremium,jdbcType=NUMERIC}, #{item.monthlyForwardRateWithPremium,jdbcType=NUMERIC},
        #{item.forwardDiscountFactor,jdbcType=NUMERIC}, #{item.spotDiscountFactor,jdbcType=NUMERIC},
        #{item.creatorId,jdbcType=NUMERIC}, #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestRateDetail">
    update atr_conf_interest_rate_detail
    <set>
      <if test="interestRateId != null">
        interest_rate_id = #{interestRateId,jdbcType=NUMERIC},
      </if>
      <if test="normMonth != null">
        norm_month = #{normMonth,jdbcType=NUMERIC},
      </if>
      <if test="normTime != null">
        norm_time = #{normTime,jdbcType=NUMERIC},
      </if>
      <if test="lowValue != null">
        low_value = #{lowValue,jdbcType=NUMERIC},
      </if>
      <if test="lowProfitRate != null">
        low_profit_rate = #{lowProfitRate,jdbcType=NUMERIC},
      </if>
      <if test="highValue != null">
        high_value = #{highValue,jdbcType=NUMERIC},
      </if>
      <if test="highProfitRate != null">
        high_profit_rate = #{highProfitRate,jdbcType=NUMERIC},
      </if>
      <if test="interpolatedSpotRate != null">
        interpolated_spot_rate = #{interpolatedSpotRate,jdbcType=NUMERIC},
      </if>
      <if test="liquidityPremium != null">
        liquidity_premium = #{liquidityPremium,jdbcType=NUMERIC},
      </if>
      <if test="annualSpotRateWithPremium != null">
        annual_spot_rate_with_premium = #{annualSpotRateWithPremium,jdbcType=NUMERIC},
      </if>
      <if test="monthlySpotRateWithPremium != null">
        monthly_spot_rate_with_premium = #{monthlySpotRateWithPremium,jdbcType=NUMERIC},
      </if>
      <if test="monthlyForwardRateWithPremium != null">
        monthly_forward_rate_with_premium = #{monthlyForwardRateWithPremium,jdbcType=NUMERIC},
      </if>
      <if test="forwardDiscountFactor != null">
        forward_discount_factor = #{forwardDiscountFactor,jdbcType=NUMERIC},
      </if>
      <if test="spotDiscountFactor != null">
        spot_discount_factor = #{spotDiscountFactor,jdbcType=NUMERIC},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=NUMERIC},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where interest_rate_detail_id = #{interestRateDetailId,jdbcType=NUMERIC}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestRateDetail">
    update atr_conf_interest_rate_detail
    <set>
      <if test="record.interestRateId != null">
        interest_rate_id = #{record.interestRateId,jdbcType=NUMERIC},
      </if>
      <if test="record.normMonth != null">
        norm_month = #{record.normMonth,jdbcType=NUMERIC},
      </if>
      <if test="record.normTime != null">
        norm_time = #{record.normTime,jdbcType=NUMERIC},
      </if>
      <if test="record.lowValue != null">
        low_value = #{record.lowValue,jdbcType=NUMERIC},
      </if>
      <if test="record.lowProfitRate != null">
        low_profit_rate = #{record.lowProfitRate,jdbcType=NUMERIC},
      </if>
      <if test="record.highValue != null">
        high_value = #{record.highValue,jdbcType=NUMERIC},
      </if>
      <if test="record.highProfitRate != null">
        high_profit_rate = #{record.highProfitRate,jdbcType=NUMERIC},
      </if>
      <if test="record.interpolatedSpotRate != null">
        interpolated_spot_rate = #{record.interpolatedSpotRate,jdbcType=NUMERIC},
      </if>
      <if test="record.liquidityPremium != null">
        liquidity_premium = #{record.liquidityPremium,jdbcType=NUMERIC},
      </if>
      <if test="record.annualSpotRateWithPremium != null">
        annual_spot_rate_with_premium = #{record.annualSpotRateWithPremium,jdbcType=NUMERIC},
      </if>
      <if test="record.monthlySpotRateWithPremium != null">
        monthly_spot_rate_with_premium = #{record.monthlySpotRateWithPremium,jdbcType=NUMERIC},
      </if>
      <if test="record.monthlyForwardRateWithPremium != null">
        monthly_forward_rate_with_premium = #{record.monthlyForwardRateWithPremium,jdbcType=NUMERIC},
      </if>
      <if test="record.forwardDiscountFactor != null">
        forward_discount_factor = #{record.forwardDiscountFactor,jdbcType=NUMERIC},
      </if>
      <if test="record.spotDiscountFactor != null">
        spot_discount_factor = #{record.spotDiscountFactor,jdbcType=NUMERIC},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=NUMERIC},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from atr_conf_interest_rate_detail
    where interest_rate_detail_id = #{interestRateDetailId,jdbcType=NUMERIC}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from atr_conf_interest_rate_detail
    where interest_rate_detail_id in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from atr_conf_interest_rate_detail
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestRateDetail">
    select count(1) from atr_conf_interest_rate_detail
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>