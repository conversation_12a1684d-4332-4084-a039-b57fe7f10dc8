package com.ss.ifrs.actuarial.dao.conf;

import org.apache.ibatis.annotations.Mapper;

import java.util.Map;

@Mapper
public interface AtrCodeDao {

    String getAtrCodeName(Map<String, Object> param);

    String getBplCodeName(Map<String, Object> param);

    String getLoaName(Map<String, Object> param);

    String getEntityName(Map<String, Object> param);

    String getEntityCode(Map<String, Object> paramMap);

    String getUserName(Map<String, Object> param);

}
