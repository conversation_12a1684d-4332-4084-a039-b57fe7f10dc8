/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-07-08 11:25:49
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2021-07-08 11:25:49<br/>
 * Description: null<br/>
 * Table Name: atr_conf_bussperiod<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
public class AtrConfBussPeriodBackVo implements Serializable {

    /**
     * Database column: atr_conf_bussperiod.center_id
     * Database remarks: ENTITY_ID|业务单位id
     */
    @ApiModelProperty(value = "ENTITY_ID|业务单位id", required = true)
    private Long entityId;

    /**
     * Database column: atr_conf_bussperiod.year_month
     * Database remarks: year_month|业务年月
     */
    @ApiModelProperty(value = "year_month|业务年月", required = true)
    private String yearMonth;

    /**
     * Database column: atr_conf_bussperiod.period_state
     * Database remarks: period_state|完成标识  0-待同步 1-执行中 2-完成
     */
    @ApiModelProperty(value = "period_state|完成标识  0-待同步 1-执行中 2-完成", required = false)
    private String periodState;

    /**
     * Database column: atr_conf_bussperiod.valid_is
     * Database remarks: valid_is|有效状态
     */
    @ApiModelProperty(value = "valid_is|有效状态", required = false)
    private String validIs;

    private String bizCode;

    private String factorType;

    private String BusinessSourceCode;

    private Long userId;

    private static final long serialVersionUID = 1L;

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getPeriodState() {
        return periodState;
    }

    public void setPeriodState(String periodState) {
        this.periodState = periodState;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public String getFactorType() {
        return factorType;
    }

    public void setFactorType(String factorType) {
        this.factorType = factorType;
    }

    public String getBusinessSourceCode() {
        return BusinessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        BusinessSourceCode = businessSourceCode;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
}