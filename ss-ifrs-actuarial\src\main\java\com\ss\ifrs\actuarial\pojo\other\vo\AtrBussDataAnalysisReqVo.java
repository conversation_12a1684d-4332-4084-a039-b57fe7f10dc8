package com.ss.ifrs.actuarial.pojo.other.vo;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

public class AtrBussDataAnalysisReqVo  implements Serializable {

    /**
     * 搜索的业务单位id
     */
    @ApiModelProperty("搜索的业务单位id")
    private Long entityId;

    @ApiModelProperty("模型Id")
    private Long modelDefId;

    /**
     * 开始会计年月
     */
    @ApiModelProperty("开始会计年月")
    private String startYearMonth;

    /**
     * 结束会计年月
     */
    @ApiModelProperty("结束会计年月")
    private String endYearMonth;

    /**
     * 需要查询的指标的规则id列表
     */
    @ApiModelProperty("需要查询的输出因子列表")
    @NotEmpty(message = "输出项不能为空")
    private List<String> factorCodeList;

    /**
     * 维度值
     */
    @ApiModelProperty("维度字段的数据库字段名")
    @NotEmpty(message = "dimensionDbName 不能为空")
    private String dimensionDbName;

    private String showFiled;

    private String factorCode;

    private static final long serialVersionUID = 1L;

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public Long getModelDefId() {
        return modelDefId;
    }

    public void setModelDefId(Long modelDefId) {
        this.modelDefId = modelDefId;
    }

    public String getStartYearMonth() {
        return startYearMonth;
    }

    public void setStartYearMonth(String startYearMonth) {
        this.startYearMonth = startYearMonth;
    }

    public String getEndYearMonth() {
        return endYearMonth;
    }

    public void setEndYearMonth(String endYearMonth) {
        this.endYearMonth = endYearMonth;
    }

    public List<String> getFactorCodeList() {
        return factorCodeList;
    }

    public void setFactorCodeList(List<String> factorCodeList) {
        this.factorCodeList = factorCodeList;
    }

    public String getDimensionDbName() {
        return dimensionDbName;
    }

    public void setDimensionDbName(String dimensionDbName) {
        this.dimensionDbName = dimensionDbName;
    }

    public String getShowFiled() {
        return showFiled;
    }

    public void setShowFiled(String showFiled) {
        this.showFiled = showFiled;
    }

    public String getFactorCode() {
        return factorCode;
    }

    public void setFactorCode(String factorCode) {
        this.factorCode = factorCode;
    }
}
