/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2021-02-23 15:17:10
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import java.io.Serializable;
import java.util.Date;

/**
 * Description: 计量单元过程数据-发展期<br/>
 */

public class AtrConfDevPeriodVo implements Serializable {

    public String unitNo   ;         // 计量单元
    public long evaluateMainId ;     // 主表ID
    public Long devPeriod ;         // 发展期
    public Date evaluateDate   ;    // 评估日
    public Date devStartDate    ;   // 发展期开始时间
    public Date devEndDate      ;  // 发展期结束时间
    public String newMonthIs   ;   // 是否当月新单
    public String newYearIs   ;    // 是否当年新单
    public String riskModeIs  ;    // 是否属于风险分布范围

    public Double curEarnedRate  ;      // 当期赚取比例
    public Double nonEarnedRate  ;      //未赚保费比例
    public Double undueRate      ;      //未到期比例
    public Double evaPremEarnedRate  ;  // 预期各评估期保费赚取比例
    public Double eopNonAmortRate    ;  // 当期期末剩余未摊销比例
    public Double eopAdjNonAmortRate  ;  // 当期期末调整剩余未摊销比例
    public Double eopNonAffirmRate   ;   // 保费当期期末剩余未确认比例
    public Double bopNonAffirmRate   ;   // 保费当期期初剩余未确认比例
    public Double eopAdjNonAffirmRate ;  // 保费当期期末调整剩余未确认比例
    public Double discountRate         ;  // 折现比例

    public Double evaPerNonEarned   ;    // 未来各评估期未赚保费
    public Double evaPerEarned      ;   // 未来各评估期已赚保费
    public Double expPremiumCf     ;    // 预期未来保费现金流
    public Double expPremiumCfPv   ;    // 预期未来保费现金流现值
    public Double expMaintenanceCf  ;   // 预期维持费用现金流
    public Double expMaintenanceCfPv ;  // 预期维持费用现金流现值
    public Double expLossCf    ;        // 预期赔付费用现金流
    public Double expLossCfPv   ;      // 预期赔付费用现金流现值
    public Double expIacfCf      ;     // 预期IACF现金流
    public Double expIacfCfPv   ;      // 预期IACF现金流现值
    public Double eopDocIacfCf    ;    // 预期跟单IACF现金流
    public Double eopDocIacfCfPv   ;   // 预期跟单IACF现金流现值
    public Double expNonDocIacfCf  ;   // 预期非跟单IACF现金流
    public Double expNonDocIacfCfPv ;  // 预期非跟单IACF现金流现值


    // 计量单元
    public String getUnitNo() {
        return unitNo;
    }

    public void setUnitNo(String unitNo) {
        this.unitNo = unitNo;
    }

    // 主表ID
    public long getEvaluateMainId() {
        return evaluateMainId;
    }

    public void setEvaluateMainId(long evaluateMainId) {
        this.evaluateMainId = evaluateMainId;
    }

    // 发展期
    public Long getDevPeriod() {
        return devPeriod;
    }

    public void setDevPeriod(Long devPeriod) {
        this.devPeriod = devPeriod;
    }
    // 评估日
    public Date getEvaluateDate() {
        return evaluateDate;
    }

    public void setEvaluateDate(Date evaluateDate) {
        this.evaluateDate = evaluateDate;
    }

    public Date getDevStartDate() {
        return devStartDate;
    }

    public void setDevStartDate(Date devStartDate) {
        this.devStartDate = devStartDate;
    }

    public Date getDevEndDate() {
        return devEndDate;
    }

    public void setDevEndDate(Date devEndDate) {
        this.devEndDate = devEndDate;
    }

    public String getNewMonthIs() {
        return newMonthIs;
    }

    public void setNewMonthIs(String newMonthIs) {
        this.newMonthIs = newMonthIs;
    }

    public String getNewYearIs() {
        return newYearIs;
    }

    public void setNewYearIs(String newYearIs) {
        this.newYearIs = newYearIs;
    }

    public String getRiskModeIs() {
        return riskModeIs;
    }

    public void setRiskModeIs(String riskModeIs) {
        this.riskModeIs = riskModeIs;
    }

    public Double getCurEarnedRate() {
        return curEarnedRate;
    }

    public void setCurEarnedRate(Double curEarnedRate) {
        this.curEarnedRate = curEarnedRate;
    }

    public Double getNonEarnedRate() {
        return nonEarnedRate;
    }

    public void setNonEarnedRate(Double nonEarnedRate) {
        this.nonEarnedRate = nonEarnedRate;
    }

    public Double getUndueRate() {
        return undueRate;
    }

    public void setUndueRate(Double undueRate) {
        this.undueRate = undueRate;
    }

    public Double getEvaPremEarnedRate() {
        return evaPremEarnedRate;
    }

    public void setEvaPremEarnedRate(Double evaPremEarnedRate) {
        this.evaPremEarnedRate = evaPremEarnedRate;
    }

    public Double getEopNonAmortRate() {
        return eopNonAmortRate;
    }

    public void setEopNonAmortRate(Double eopNonAmortRate) {
        this.eopNonAmortRate = eopNonAmortRate;
    }

    public Double getEopAdjNonAmortRate() {
        return eopAdjNonAmortRate;
    }

    public void setEopAdjNonAmortRate(Double eopAdjNonAmortRate) {
        this.eopAdjNonAmortRate = eopAdjNonAmortRate;
    }

    public Double getEopNonAffirmRate() {
        return eopNonAffirmRate;
    }

    public void setEopNonAffirmRate(Double eopNonAffirmRate) {
        this.eopNonAffirmRate = eopNonAffirmRate;
    }

    public Double getBopNonAffirmRate() {
        return bopNonAffirmRate;
    }

    public void setBopNonAffirmRate(Double bopNonAffirmRate) {
        this.bopNonAffirmRate = bopNonAffirmRate;
    }

    public Double getEopAdjNonAffirmRate() {
        return eopAdjNonAffirmRate;
    }

    public void setEopAdjNonAffirmRate(Double eopAdjNonAffirmRate) {
        this.eopAdjNonAffirmRate = eopAdjNonAffirmRate;
    }

    public Double getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(Double discountRate) {
        this.discountRate = discountRate;
    }

    public Double getEvaPerNonEarned() {
        return evaPerNonEarned;
    }

    public void setEvaPerNonEarned(Double evaPerNonEarned) {
        this.evaPerNonEarned = evaPerNonEarned;
    }

    public Double getEvaPerEarned() {
        return evaPerEarned;
    }

    public void setEvaPerEarned(Double evaPerEarned) {
        this.evaPerEarned = evaPerEarned;
    }

    public Double getExpPremiumCf() {
        return expPremiumCf;
    }

    public void setExpPremiumCf(Double expPremiumCf) {
        this.expPremiumCf = expPremiumCf;
    }

    public Double getExpPremiumCfPv() {
        return expPremiumCfPv;
    }

    public void setExpPremiumCfPv(Double expPremiumCfPv) {
        this.expPremiumCfPv = expPremiumCfPv;
    }

    public Double getExpMaintenanceCf() {
        return expMaintenanceCf;
    }

    public void setExpMaintenanceCf(Double expMaintenanceCf) {
        this.expMaintenanceCf = expMaintenanceCf;
    }

    public Double getExpMaintenanceCfPv() {
        return expMaintenanceCfPv;
    }

    public void setExpMaintenanceCfPv(Double expMaintenanceCfPv) {
        this.expMaintenanceCfPv = expMaintenanceCfPv;
    }

    public Double getExpLossCf() {
        return expLossCf;
    }

    public void setExpLossCf(Double expLossCf) {
        this.expLossCf = expLossCf;
    }

    public Double getExpLossCfPv() {
        return expLossCfPv;
    }

    public void setExpLossCfPv(Double expLossCfPv) {
        this.expLossCfPv = expLossCfPv;
    }

    public Double getExpIacfCf() {
        return expIacfCf;
    }

    public void setExpIacfCf(Double expIacfCf) {
        this.expIacfCf = expIacfCf;
    }

    public Double getExpIacfCfPv() {
        return expIacfCfPv;
    }

    public void setExpIacfCfPv(Double expIacfCfPv) {
        this.expIacfCfPv = expIacfCfPv;
    }

    public Double getEopDocIacfCf() {
        return eopDocIacfCf;
    }

    public void setEopDocIacfCf(Double eopDocIacfCf) {
        this.eopDocIacfCf = eopDocIacfCf;
    }

    public Double getEopDocIacfCfPv() {
        return eopDocIacfCfPv;
    }

    public void setEopDocIacfCfPv(Double eopDocIacfCfPv) {
        this.eopDocIacfCfPv = eopDocIacfCfPv;
    }

    public Double getExpNonDocIacfCf() {
        return expNonDocIacfCf;
    }

    public void setExpNonDocIacfCf(Double expNonDocIacfCf) {
        this.expNonDocIacfCf = expNonDocIacfCf;
    }

    public Double getExpNonDocIacfCfPv() {
        return expNonDocIacfCfPv;
    }

    public void setExpNonDocIacfCfPv(Double expNonDocIacfCfPv) {
        this.expNonDocIacfCfPv = expNonDocIacfCfPv;
    }
}