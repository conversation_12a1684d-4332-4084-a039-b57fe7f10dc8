<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by Taiping MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2024-05-08 10:55:27 -->
<!-- Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussIbnrcalcFinalTraceDao">
  <!-- 本文件由Taiping MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**BaseDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcFinalTrace">
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="action_no" property="actionNo" jdbcType="VARCHAR" />
    <result column="icp_id" property="icpId" jdbcType="BIGINT" />
    <result column="method_type" property="methodType" jdbcType="VARCHAR" />
    <result column="display_no" property="displayNo" jdbcType="INTEGER" />
    <result column="trace_type" property="traceType" jdbcType="VARCHAR" />
    <result column="factor" property="factor" jdbcType="NUMERIC" />
    <result column="amount" property="amount" jdbcType="NUMERIC" />
    <result column="ibnr" property="ibnr" jdbcType="NUMERIC" />
    <result column="os" property="os" jdbcType="NUMERIC" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    id, action_no, icp_id, method_type, display_no, trace_type, factor, amount, ibnr, 
    os
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="id != null ">
          and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="actionNo != null and actionNo != ''">
          and action_no = #{actionNo,jdbcType=VARCHAR}
      </if>
      <if test="icpId != null ">
          and icp_id = #{icpId,jdbcType=BIGINT}
      </if>
      <if test="methodType != null and methodType != ''">
          and method_type = #{methodType,jdbcType=VARCHAR}
      </if>
      <if test="displayNo != null ">
          and display_no = #{displayNo,jdbcType=INTEGER}
      </if>
      <if test="traceType != null and traceType != ''">
          and trace_type = #{traceType,jdbcType=VARCHAR}
      </if>
      <if test="factor != null ">
          and factor = #{factor,jdbcType=NUMERIC}
      </if>
      <if test="amount != null ">
          and amount = #{amount,jdbcType=NUMERIC}
      </if>
      <if test="ibnr != null ">
          and ibnr = #{ibnr,jdbcType=NUMERIC}
      </if>
      <if test="os != null ">
          and os = #{os,jdbcType=NUMERIC}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.id != null ">
          and id = #{condition.id,jdbcType=BIGINT}
      </if>
      <if test="condition.actionNo != null and condition.actionNo != ''">
          and action_no = #{condition.actionNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.icpId != null ">
          and icp_id = #{condition.icpId,jdbcType=BIGINT}
      </if>
      <if test="condition.methodType != null and condition.methodType != ''">
          and method_type = #{condition.methodType,jdbcType=VARCHAR}
      </if>
      <if test="condition.displayNo != null ">
          and display_no = #{condition.displayNo,jdbcType=INTEGER}
      </if>
      <if test="condition.traceType != null and condition.traceType != ''">
          and trace_type = #{condition.traceType,jdbcType=VARCHAR}
      </if>
      <if test="condition.factor != null ">
          and factor = #{condition.factor,jdbcType=NUMERIC}
      </if>
      <if test="condition.amount != null ">
          and amount = #{condition.amount,jdbcType=NUMERIC}
      </if>
      <if test="condition.ibnr != null ">
          and ibnr = #{condition.ibnr,jdbcType=NUMERIC}
      </if>
      <if test="condition.os != null ">
          and os = #{condition.os,jdbcType=NUMERIC}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="id != null ">
          and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="actionNo != null and actionNo != ''">
          and action_no = #{actionNo,jdbcType=VARCHAR}
      </if>
      <if test="icpId != null ">
          and icp_id = #{icpId,jdbcType=BIGINT}
      </if>
      <if test="methodType != null and methodType != ''">
          and method_type = #{methodType,jdbcType=VARCHAR}
      </if>
      <if test="displayNo != null ">
          and display_no = #{displayNo,jdbcType=INTEGER}
      </if>
      <if test="traceType != null and traceType != ''">
          and trace_type = #{traceType,jdbcType=VARCHAR}
      </if>
      <if test="factor != null ">
          and factor = #{factor,jdbcType=NUMERIC}
      </if>
      <if test="amount != null ">
          and amount = #{amount,jdbcType=NUMERIC}
      </if>
      <if test="ibnr != null ">
          and ibnr = #{ibnr,jdbcType=NUMERIC}
      </if>
      <if test="os != null ">
          and os = #{os,jdbcType=NUMERIC}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_ibnrcalc_final_trace"
    where id = #{id,jdbcType=BIGINT}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_ibnrcalc_final_trace"
    where id in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_ibnrcalc_final_trace"
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcFinalTrace">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_ibnrcalc_final_trace"
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_ibnrcalc_final_trace"
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="id" keyProperty="id" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcFinalTrace">
    insert into "atr_buss_ibnrcalc_final_trace"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="actionNo != null">
        action_no,
      </if>
      <if test="icpId != null">
        icp_id,
      </if>
      <if test="methodType != null">
        method_type,
      </if>
      <if test="displayNo != null">
        display_no,
      </if>
      <if test="traceType != null">
        trace_type,
      </if>
      <if test="factor != null">
        factor,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="ibnr != null">
        ibnr,
      </if>
      <if test="os != null">
        os,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="actionNo != null">
        #{actionNo,jdbcType=VARCHAR},
      </if>
      <if test="icpId != null">
        #{icpId,jdbcType=BIGINT},
      </if>
      <if test="methodType != null">
        #{methodType,jdbcType=VARCHAR},
      </if>
      <if test="displayNo != null">
        #{displayNo,jdbcType=INTEGER},
      </if>
      <if test="traceType != null">
        #{traceType,jdbcType=VARCHAR},
      </if>
      <if test="factor != null">
        #{factor,jdbcType=NUMERIC},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=NUMERIC},
      </if>
      <if test="ibnr != null">
        #{ibnr,jdbcType=NUMERIC},
      </if>
      <if test="os != null">
        #{os,jdbcType=NUMERIC},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert into "atr_buss_ibnrcalc_final_trace"
     (id, action_no, icp_id, 
      method_type, display_no, trace_type, 
      factor, amount, ibnr, 
      os)
     values 
    <foreach collection="list" item="item" index="index" separator=",">
       (#{item.id,jdbcType=BIGINT}, #{item.actionNo,jdbcType=VARCHAR}, #{item.icpId,jdbcType=BIGINT}, 
        #{item.methodType,jdbcType=VARCHAR}, #{item.displayNo,jdbcType=INTEGER}, #{item.traceType,jdbcType=VARCHAR}, 
        #{item.factor,jdbcType=NUMERIC}, #{item.amount,jdbcType=NUMERIC}, #{item.ibnr,jdbcType=NUMERIC}, 
        #{item.os,jdbcType=NUMERIC})
    </foreach>
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcFinalTrace">
    update "atr_buss_ibnrcalc_final_trace"
    <set>
      <if test="actionNo != null">
        action_no = #{actionNo,jdbcType=VARCHAR},
      </if>
      <if test="icpId != null">
        icp_id = #{icpId,jdbcType=BIGINT},
      </if>
      <if test="methodType != null">
        method_type = #{methodType,jdbcType=VARCHAR},
      </if>
      <if test="displayNo != null">
        display_no = #{displayNo,jdbcType=INTEGER},
      </if>
      <if test="traceType != null">
        trace_type = #{traceType,jdbcType=VARCHAR},
      </if>
      <if test="factor != null">
        factor = #{factor,jdbcType=NUMERIC},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=NUMERIC},
      </if>
      <if test="ibnr != null">
        ibnr = #{ibnr,jdbcType=NUMERIC},
      </if>
      <if test="os != null">
        os = #{os,jdbcType=NUMERIC},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcFinalTrace">
    update "atr_buss_ibnrcalc_final_trace"
    <set>
      <if test="record.actionNo != null">
        action_no = #{record.actionNo,jdbcType=VARCHAR},
      </if>
      <if test="record.icpId != null">
        icp_id = #{record.icpId,jdbcType=BIGINT},
      </if>
      <if test="record.methodType != null">
        method_type = #{record.methodType,jdbcType=VARCHAR},
      </if>
      <if test="record.displayNo != null">
        display_no = #{record.displayNo,jdbcType=INTEGER},
      </if>
      <if test="record.traceType != null">
        trace_type = #{record.traceType,jdbcType=VARCHAR},
      </if>
      <if test="record.factor != null">
        factor = #{record.factor,jdbcType=NUMERIC},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=NUMERIC},
      </if>
      <if test="record.ibnr != null">
        ibnr = #{record.ibnr,jdbcType=NUMERIC},
      </if>
      <if test="record.os != null">
        os = #{record.os,jdbcType=NUMERIC},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from "atr_buss_ibnrcalc_final_trace"
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from "atr_buss_ibnrcalc_final_trace"
    where id in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from "atr_buss_ibnrcalc_final_trace"
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcFinalTrace">
    select count(1) from "atr_buss_ibnrcalc_final_trace"
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>