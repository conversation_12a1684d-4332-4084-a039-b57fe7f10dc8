package com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@ToString
public class AtrBussIbnrcalcStep4CalcParamDetailVo {
    private List<BigDecimal> interestRatio;
    @JsonProperty(value = "inderect claim handling expense")
    private BigDecimal ICHE;
    @JsonProperty(value = "time value discount")
    private BigDecimal TVD;
    private BigDecimal PAD;
}
