package com.ss.ifrs.actuarial.service.puhua;

import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLrcActionVo;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo;
import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrBussBecfViewVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: AtrBussCalcService
 * @Description: 计量计算服务接口类
 * @Author: yinxh.
 * @CreateDate: 2021/3/8 17:56
 * @Version: 1.0
 */
public interface AtrBussPuHuaActionService {

    void becfDownload(AtrBussBecfViewVo atrBussBecfViewVo, HttpServletRequest request, HttpServletResponse response) throws Exception;
}
