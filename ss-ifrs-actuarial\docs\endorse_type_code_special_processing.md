# endorse_type_code特殊处理功能说明

## 功能概述

针对atr_dap_dd_unit中的endorse_type_code为15和16的类型的记录，实现了特殊的现金流计算逻辑。

## 业务需求

1. **数据格式处理**：endorse_type_code的数据格式为"01,15,16"这种逗号分隔的格式，需要拆分处理后进行比较
2. **关联查询**：当endorse_type_code出现了15和16的类型时，需要根据policy_no查询其他相同policy_no的记录
3. **时间条件**：在满足上面条件的基础上，当atr_dap_dd_unit的year_month和当前评估期相等时，执行特殊操作
4. **现金流计算**：满足条件的记录在计算发展期现金流时只有第0期的现金流
5. **已赚计算**：第0期的已赚金额=签单保费-历史累计已赚，已赚比例=第0期已赚金额/保费
6. **后续处理**：当月计算完成后，下一个评估月份该单没有发展期现金流，但单子还是需要正常进来

## 实现方案

### 1. 数据结构修改

#### AtrBussLrcDdIcu类
- 添加了`endorseTypeCode`字段用于存储批改类型代码
- 添加了`specialProcessType`字段用于存储特殊处理类型（0:正常处理, 1:只计算第0期, 2:不计算发展期）

#### SQL查询修改
- 在`AtrBussLrcDdCustDao.xml`中的`partitionBaseData`方法中添加了endorse_type_code字段的查询
- 在SQL中直接计算special_process_type，避免程序层面的复杂判断和内存占用

### 2. 核心处理逻辑

#### 特殊处理类型计算（SQL层面）
```sql
CASE
    WHEN us.endorse_type_code IS NULL
         OR (us.endorse_type_code NOT LIKE '%15%' AND us.endorse_type_code NOT LIKE '%16%') THEN 0  -- 当前记录不包含15/16，正常处理
    WHEN us.dap_year_month = #{yearMonth}
         AND (us.endorse_type_code LIKE '%15%' OR us.endorse_type_code LIKE '%16%') THEN 1  -- 当前评估期且包含15/16，只计算第0期
    WHEN us.dap_year_month < #{yearMonth}
         AND EXISTS (
             SELECT 1 FROM atr_dap_dd_unit check_unit
             WHERE check_unit.policy_no = us.policy_no
               AND check_unit.entity_id = #{entityId}
               AND check_unit.year_month <= #{yearMonth}
               AND check_unit.endorse_type_code IS NOT NULL
               AND (check_unit.endorse_type_code LIKE '%15%' OR check_unit.endorse_type_code LIKE '%16%')
         ) THEN 2  -- 历史数据且该保单曾有15/16，不计算发展期
    ELSE 0  -- 其他情况正常处理
END as special_process_type
```

#### 优化说明
- **避免不必要的JOIN**：直接使用当前记录的`endorse_type_code`进行判断，无需额外关联
- **EXISTS子查询**：仅在需要检查历史记录时才使用EXISTS，提高查询效率
- **逻辑简化**：减少了SQL复杂度，提升了可读性和性能

### 3. 计算逻辑修改

#### 发展期计算
- **特殊处理类型1**：只计算第0期，第0期已赚金额=签单保费-历史累计已赚
- **特殊处理类型2**：不计算任何发展期现金流，maxDevNo=0

#### 现金流计算
- **应收保费**：特殊处理类型2时设为0
- **净额结算**：特殊处理类型2时设为0
- **跟单获取费用**：特殊处理类型2时设为0
- **非跟单获取费用**：特殊处理类型2时设为0
- **减值现金流**：特殊处理类型2时设为0

## 使用方法

### 1. 数据准备
确保atr_dap_dd_unit表中的endorse_type_code字段包含正确的数据格式（如"01,15,16"）。

### 2. 执行计算
正常调用`AtrBussLrcDdService.entry()`方法，系统会自动识别并处理特殊的endorse_type_code。

### 3. 结果验证
- 检查包含15/16的保单在当前评估期只有第0期现金流
- 检查历史数据中包含15/16的保单在后续评估期没有发展期现金流
- 确认这些保单仍然参与ICG汇总计算

## 配置说明

无需额外配置，功能已集成到现有的计算流程中。

## 注意事项

1. **数据完整性**：确保endorse_type_code字段的数据格式正确
2. **性能考虑**：系统会在计算开始时一次性收集所有特殊保单号，避免重复查询
3. **向后兼容**：新功能不影响现有的正常业务逻辑
4. **测试验证**：建议在生产环境使用前进行充分的测试验证

## 测试用例

参考`AtrBussLrcDdServiceSpecialEndorseTypeTest`类中的测试用例：
- `testIsEndorseTypeContains1516`：测试endorse_type_code解析逻辑
- `testGetSpecialProcessType`：测试特殊处理类型判断逻辑
- `testCollectSpecialEndorseTypePolicies`：测试特殊保单收集逻辑

## 相关文件

### 修改的文件
1. `AtrBussLrcDdIcu.java` - 添加endorse_type_code字段
2. `AtrBussLrcDdService.java` - 添加特殊处理逻辑
3. `AtrBussLrcDdDao.java` - 添加查询方法接口
4. `AtrBussLrcDdCustDao.xml` - 添加SQL查询

### 新增的文件
1. `AtrBussLrcDdServiceSpecialEndorseTypeTest.java` - 测试用例
2. `endorse_type_code_special_processing.md` - 功能说明文档

## 版本信息

- **功能版本**：v1.0
- **实现日期**：2024年
- **开发者**：系统开发团队
