

delete from qtc_conf_calc_sql where sql_code in ('MA_LI_E01','MA_LI_E02','MA_LI_E03','MA_LI_E04'， 'MA_LR_G02') ;

INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_E04', 3510, e'insert into qtc_buss_evl_lic_pre(LIC_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
SELECT nextval(\'qtc_seq_buss_evl_lic_pre\'), \'IE4\', TTT.*
from (select t1.EVL_MAIN_ID,
             t2.ICG_NO,
             sum(case when t2.acc_time = \'P\' then t2.p_pfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'P\' then t2.p_pwr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'P\' then t2.p_cfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'P\' then t2.p_cwr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'P\' then t2.c_cfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'P\' then t2.c_cwr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.p_pfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.p_pwr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.p_cfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.p_cwr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.c_cfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.c_cwr*t1.cf_dire*-1 end)
      from QTC_BUSS_CALC_CFG t1,
           qtc_dap_ecf_lic_pv t2
      where t1.EVL_MAIN_ID = #evl_main_id#
        and t2.ENTITY_ID = t1.ENTITY_ID
        and t2.YEAR_MONTH = t1.YEAR_MONTH
        and t2.BUSS_MODEL = t1.BUSS_MODEL
        and t2.LOA_CODE = t1.loa_code
        and t2.cf_type = \'RA\'
      group by t1.EVL_MAIN_ID, t2.ICG_NO) TTT');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_E01', 3507, e'insert into qtc_buss_evl_lic_pre(LIC_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
SELECT nextval(\'qtc_seq_buss_evl_lic_pre\'), \'IE1\', TTT.*
from (select t1.EVL_MAIN_ID,
             t2.ICG_NO,
             sum(case when t2.acc_time = \'P\' then t2.p_pfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'P\' then t2.p_pwr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'P\' then t2.p_cfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'P\' then t2.p_cwr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'P\' then t2.c_cfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'P\' then t2.c_cwr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.p_pfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.p_pwr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.p_cfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.p_cwr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.c_cfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.c_cwr*t1.cf_dire*-1 end)
      from QTC_BUSS_CALC_CFG t1,
           qtc_dap_ecf_lic_pv t2
      where t1.EVL_MAIN_ID = #evl_main_id#
        and t2.ENTITY_ID = t1.ENTITY_ID
        and t2.YEAR_MONTH = t1.YEAR_MONTH
        and t2.BUSS_MODEL = t1.BUSS_MODEL
        and t2.LOA_CODE = t1.loa_code
        and t2.cf_type = \'OS\'
      group by t1.EVL_MAIN_ID, t2.ICG_NO) TTT');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_E03', 3509, e'insert into qtc_buss_evl_lic_pre(LIC_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
SELECT nextval(\'qtc_seq_buss_evl_lic_pre\'), \'IE3\', TTT.*
from (select t1.EVL_MAIN_ID,
             t2.ICG_NO,
             sum(case when t2.acc_time = \'P\' then t2.p_pfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'P\' then t2.p_pwr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'P\' then t2.p_cfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'P\' then t2.p_cwr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'P\' then t2.c_cfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'P\' then t2.c_cwr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.p_pfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.p_pwr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.p_cfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.p_cwr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.c_cfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.c_cwr*t1.cf_dire*-1 end)
      from QTC_BUSS_CALC_CFG t1,
           qtc_dap_ecf_lic_pv t2
      where t1.EVL_MAIN_ID = #evl_main_id#
        and t2.ENTITY_ID = t1.ENTITY_ID
        and t2.YEAR_MONTH = t1.YEAR_MONTH
        and t2.BUSS_MODEL = t1.BUSS_MODEL
        and t2.LOA_CODE = t1.loa_code
        and t2.cf_type = \'ULAE\'
      group by t1.EVL_MAIN_ID, t2.ICG_NO) TTT');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_E02', 3508, e'insert into qtc_buss_evl_lic_pre(LIC_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
SELECT nextval(\'qtc_seq_buss_evl_lic_pre\'), \'IE2\', TTT.*
from (select t1.EVL_MAIN_ID,
             t2.ICG_NO,
             sum(case when t2.acc_time = \'P\' then t2.p_pfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'P\' then t2.p_pwr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'P\' then t2.p_cfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'P\' then t2.p_cwr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'P\' then t2.c_cfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'P\' then t2.c_cwr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.p_pfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.p_pwr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.p_cfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.p_cwr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.c_cfr*t1.cf_dire*-1 end),
             sum(case when t2.acc_time = \'C\' then t2.c_cwr*t1.cf_dire*-1 end)
      from QTC_BUSS_CALC_CFG t1,
           qtc_dap_ecf_lic_pv t2
      where t1.EVL_MAIN_ID = #evl_main_id#
        and t2.ENTITY_ID = t1.ENTITY_ID
        and t2.YEAR_MONTH = t1.YEAR_MONTH
        and t2.BUSS_MODEL = t1.BUSS_MODEL
        and t2.LOA_CODE = t1.loa_code
        and t2.cf_type = \'IBNR\'
      group by t1.EVL_MAIN_ID, t2.ICG_NO) TTT');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_E05', 3511, e'insert into qtc_buss_evl_lic_pre(LIC_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
SELECT nextval(\'qtc_seq_buss_evl_lic_pre\'), \'IE5\', TTT.*
from (select t1.EVL_MAIN_ID,
             t2.ICG_NO,
              sum(case when t2.acc_time = \'P\' then t2.p_pfr end),
             sum(case when t2.acc_time = \'P\' then t2.p_pwr end),
             sum(case when t2.acc_time = \'P\' then t2.p_cfr end),
             sum(case when t2.acc_time = \'P\' then t2.p_cwr end),
             sum(case when t2.acc_time = \'P\' then t2.c_cfr end),
             sum(case when t2.acc_time = \'P\' then t2.c_cwr end),
             sum(case when t2.acc_time = \'C\' then t2.p_pfr end),
             sum(case when t2.acc_time = \'C\' then t2.p_pwr end),
             sum(case when t2.acc_time = \'C\' then t2.p_cfr end),
             sum(case when t2.acc_time = \'C\' then t2.p_cwr end),
             sum(case when t2.acc_time = \'C\' then t2.c_cfr end),
             sum(case when t2.acc_time = \'C\' then t2.c_cwr end)
      from QTC_BUSS_CALC_CFG t1,
           qtc_dap_ecf_lic_pv t2
      where t1.EVL_MAIN_ID = #evl_main_id#
        and t2.ENTITY_ID = t1.ENTITY_ID
        and t2.YEAR_MONTH = t1.YEAR_MONTH
        and t2.BUSS_MODEL = t1.BUSS_MODEL
        and t2.LOA_CODE = t1.loa_code
        and t2.cf_type = \'RD\'
      group by t1.EVL_MAIN_ID, t2.ICG_NO) TTT');


INSERT INTO qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_G02', 3021, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, buss_key, num1)
select nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RG2\',
       t1.evl_main_id,
       t3.icg_no ,
       num28*-1
from qtc_buss_calc_cfg t1 ,
     qtc_buss_evaluate_main t2 ,
     qtc_buss_evaluate_result t3
where t1.evl_main_id = #evl_main_id#
and t2.show_no = t1.show_no
and t2.year_month = t1.year_month
and t2.model_def_id = 3
and t3.evaluate_main_id = t2.evaluate_main_id
and t3.var1 = \'Lrc\'
and abs(num28) > 0');



commit ;
