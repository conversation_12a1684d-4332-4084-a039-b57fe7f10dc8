package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.constant.ActuarialConstant;
import com.ss.ifrs.actuarial.dao.AtrBussDDLrcIcgCalcDao;
import com.ss.ifrs.actuarial.dao.AtrBussDDLrcIcgCalcDetailDao;
import com.ss.ifrs.actuarial.dao.AtrBussDDLrcIcuCalcDao;
import com.ss.ifrs.actuarial.dao.AtrBussDDLrcIcuCalcDetailDao;
import com.ss.ifrs.actuarial.dao.AtrBussFOLrcIcgCalcDao;
import com.ss.ifrs.actuarial.dao.AtrBussFOLrcIcgCalcDetailDao;
import com.ss.ifrs.actuarial.dao.AtrBussFOLrcIcuCalcDao;
import com.ss.ifrs.actuarial.dao.AtrBussFOLrcIcuCalcDetailDao;
import com.ss.ifrs.actuarial.dao.AtrBussLrcActionDao;
import com.ss.ifrs.actuarial.dao.AtrBussTILrcIcgCalcDao;
import com.ss.ifrs.actuarial.dao.AtrBussTILrcIcgCalcDetailDao;
import com.ss.ifrs.actuarial.dao.AtrBussTILrcIcuCalcDao;
import com.ss.ifrs.actuarial.dao.AtrBussTILrcIcuCalcDetailDao;
import com.ss.ifrs.actuarial.dao.AtrBussTOLrcIcgCalcDao;
import com.ss.ifrs.actuarial.dao.AtrBussTOLrcIcgCalcDetailDao;
import com.ss.ifrs.actuarial.dao.AtrBussTOLrcIcuCalcDao;
import com.ss.ifrs.actuarial.dao.AtrBussTOLrcIcuCalcDetailDao;
import com.ss.ifrs.actuarial.feign.BbsConfAccountSetFeignClient;
import com.ss.ifrs.actuarial.feign.BmsConfCodeFeignClient;
import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrConfBecfOutPutVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcAction;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussDDLrcIcgCalcDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussDDLrcIcuCalcVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussFOLrcIcgCalcDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLrcActionVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussQuotaValueVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussTILrcIcgCalcDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussTILrcIcuCalcDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussTOLrcIcuCalcVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo;
import com.ss.ifrs.actuarial.service.AtrBussBecfQuotaService;
import com.ss.ifrs.actuarial.service.AtrBussLrcCashFlowService;
import com.ss.ifrs.actuarial.service.AtrConfCodeService;
import com.ss.ifrs.actuarial.service.AtrConfModelDefService;
import com.ss.ifrs.actuarial.service.AtrExportService;
import com.ss.ifrs.actuarial.util.Dates;
import com.ss.library.excel.ExcelSheet;
import com.ss.library.excel.ExcelSheetData;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ClassUtil;
import com.ss.library.utils.FilterUtil;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.model.SsException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
/**
 * @ClassName: AtrBussCalcServiceImpl
 * @Description: 计量计算服务接口实现类
 * @Author: yinxh.
 * @CreateDate: 2021/3/8 17:58
 * @Version: 1.0
 */
@Service(value = "atrBussLrcCashFlowService")
public class AtrBussLrcCashFlowServiceImpl implements AtrBussLrcCashFlowService {

	// 日志管理
	final Logger LOG  = LoggerFactory.getLogger(getClass());

	@Autowired
    BmsConfCodeFeignClient bmsConfCodeFeignClient;

	@Autowired(required = false)
	BbsConfAccountSetFeignClient accAccountPeriodFeignClient;

	@Autowired
	AtrConfCodeService atrConfCodeService;

	@Autowired
	AtrConfModelDefService atrConfModelDefService;

	@Autowired
	private AtrBussLrcActionDao atrBussLrcActionDao;

	@Autowired
	private AtrBussDDLrcIcgCalcDao atrBussDDLrcIcgCalcDao;
	@Autowired
	private AtrBussDDLrcIcgCalcDetailDao atrBussDDLrcIcgCalcDetailDao;
	@Autowired
	private AtrBussDDLrcIcuCalcDao atrBussDDLrcIcuCalcDao;
	@Autowired
	private AtrBussDDLrcIcuCalcDetailDao atrBussDDLrcIcuCalcDetailDao;

	@Autowired
	private AtrBussFOLrcIcgCalcDao atrBussFOLrcIcgCalcDao;
	@Autowired
	private AtrBussFOLrcIcgCalcDetailDao atrBussFOLrcIcgCalcDetailDao;
	@Autowired
	private AtrBussFOLrcIcuCalcDao atrBussFOLrcIcuCalcDao;
	@Autowired
	private AtrBussFOLrcIcuCalcDetailDao atrBussFOLrcIcuCalcDetailDao;

	@Autowired
	private AtrBussTILrcIcgCalcDao atrBussTILrcIcgCalcDao;
	@Autowired
	private AtrBussTILrcIcgCalcDetailDao atrBussTILrcIcgCalcDetailDao;
	@Autowired
	private AtrBussTILrcIcuCalcDao atrBussTILrcIcuCalcDao;
	@Autowired
	private AtrBussTILrcIcuCalcDetailDao atrBussTILrcIcuCalcDetailDao;

	@Autowired
	private AtrBussTOLrcIcgCalcDao atrBussTOLrcIcgCalcDao;
	@Autowired
	private AtrBussTOLrcIcgCalcDetailDao atrBussTOLrcIcgCalcDetailDao;
	@Autowired
	private AtrBussTOLrcIcuCalcDao atrBussTOLrcIcuCalcDao;
	@Autowired
	private AtrBussTOLrcIcuCalcDetailDao atrBussTOLrcIcuCalcDetailDao;

	@Autowired
	AtrExportService atrExportService;

	@Autowired
	AtrBussBecfQuotaService atrBussBecfQuotaService;


	@Resource
	private AtrBussLrcService atrBussLrcService;

	@Autowired
	private AtrBussLrcDdService atrBussLrcDdService;

	@Autowired
	private AtrBussLrcFoService atrBussLrcFoService;

	@Autowired
	private AtrBussLrcTotService atrBussLrcTotService;

	@Override
	public Page<AtrBussLrcActionVo> findForDataTables(AtrBussLrcActionVo atrBussLicCashFlowVo, Pageable pageParam) {
		return atrBussLrcActionDao.fuzzySearchPage(atrBussLicCashFlowVo, pageParam);

	}

	@Override
	@Async("ruleThreadPool")
	@Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = RuntimeException.class)
	public void save(AtrBussLrcActionVo atrBussLicCashFlowVo, Long userId) {
		Long entityId = atrBussLicCashFlowVo.getEntityId();
		String yearMonth = atrBussLicCashFlowVo.getYearMonth();
		String businessSourceCode = atrBussLicCashFlowVo.getBusinessSourceCode();
		atrBussLrcService.entry(entityId, yearMonth, businessSourceCode);
	}

	@Override
	public AtrBussLrcActionVo findById(Long id) {
		return atrBussLrcActionDao.findByid(id);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
	public void delete(Long id, Long userId) {
		atrBussLrcActionDao.deleteById(id);
	}


	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
	public Boolean confirm(AtrBussLrcActionVo atrBussLicCashFlowVo, Long userId) {
		Boolean confirmFlag = true;
		AtrBussLrcAction atrBussLrcAction = new AtrBussLrcAction();
		atrBussLrcAction.setEntityId(atrBussLicCashFlowVo.getEntityId());
		atrBussLrcAction.setYearMonth(atrBussLicCashFlowVo.getYearMonth());
		atrBussLrcAction.setBusinessSourceCode(atrBussLicCashFlowVo.getBusinessSourceCode());
		atrBussLrcAction.setConfirmIs(CommonConstant.VersionStatus.CONFIRMED);
		Long confirmCount = atrBussLrcActionDao.count(atrBussLrcAction);
		if (ObjectUtils.isNotEmpty(confirmCount) && confirmCount==0) {
			AtrBussLrcAction po = new AtrBussLrcAction();
			Date date = new Date();
			po.setId(atrBussLicCashFlowVo.getId());
			po.setConfirmIs(CommonConstant.VersionStatus.CONFIRMED);
			po.setConfirmUser(userId);
			po.setConfirmTime(date);
			po.setUpdatorId(userId);
			po.setUpdateTime(date);
			atrBussLrcActionDao.updateById(po);

			AtrConfBussPeriodVo atrConfBussPeriodVo = new AtrConfBussPeriodVo();
			atrConfBussPeriodVo.setEntityId(atrBussLicCashFlowVo.getEntityId());
			atrConfBussPeriodVo.setYearMonth(atrBussLicCashFlowVo.getYearMonth());
			atrConfBussPeriodVo.setProcCode(ActuarialConstant.ProcCode.EXPECTED_CF_LRC);
			atrConfModelDefService.confirmBeCFProc(atrConfBussPeriodVo, userId);
		} else {
			confirmFlag = false;
		}
		return confirmFlag;
	}

	@Override
	public Page<List<Map<String, Object>>> findPeriodData(AtrDapDrawVo atrDapDrawVo, Pageable pageable) {
		Page<List<Map<String, Object>>> feeTypeList = null;
		if(ObjectUtils.isEmpty(atrDapDrawVo) && ObjectUtils.isEmpty(atrDapDrawVo.getBusinessSourceCode())){
			return feeTypeList;
		}
		AtrBussQuotaValueVo atrBussQuotaValueVo = new AtrBussQuotaValueVo();
		atrBussQuotaValueVo.setBusinessSourceCode(atrDapDrawVo.getBusinessSourceCode());
		List<AtrConfBecfOutPutVo> a = atrBussBecfQuotaService.findBecfOutPut(atrBussQuotaValueVo);
		Optional<AtrConfBecfOutPutVo> optional = a.stream().filter(vo-> atrDapDrawVo.getFeeType().equals(vo.getRemark())).findFirst();
		Boolean isIcg = false;
		Boolean isDev = false;
		Integer startDevNo = 0;
		Integer endDevNo = ActuarialConstant.Export.CF_MAX_COLUMN_DEV;
		if (optional.isPresent()) {
			isIcg = "G".equals(optional.get().getDimension());
			isDev = "1".equals(optional.get().getType());
			if (ObjectUtils.isNotEmpty(optional.get().getStartDevNo())) {
				startDevNo = optional.get().getStartDevNo();
			}
			if (ObjectUtils.isNotEmpty(optional.get().getEndDevNo())) {
				endDevNo = optional.get().getEndDevNo();
			}
			atrDapDrawVo.setFeeType(optional.get().getOutCode());
		} else {
			return feeTypeList;
		}
		if (isDev) {
			Integer finalStartDevNo = startDevNo;
			Integer finalEndDevNo = endDevNo;
			if (isIcg) {
				List<Integer> icgDevNoList = this.findIcgDevNo(atrDapDrawVo);
				atrDapDrawVo.setDevNoList(icgDevNoList.stream()
						.filter(devNo -> devNo>= finalStartDevNo && devNo<= finalEndDevNo)
						.collect(Collectors.toList()));
			} else {
				List<Integer> icuDevNoList = this.findPeriodHeaderData(atrDapDrawVo);
				atrDapDrawVo.setDevNoList(icuDevNoList.stream()
						.filter(devNo -> devNo>= finalStartDevNo && devNo<= finalEndDevNo)
						.collect(Collectors.toList()));
			}
		}
		switch (atrDapDrawVo.getBusinessSourceCode()) {
			case "DD":
				if (isIcg) {
					feeTypeList= atrBussDDLrcIcgCalcDao.findLrcIcgDetail(atrDapDrawVo, pageable);
				} else {
					feeTypeList= atrBussDDLrcIcuCalcDao.findLrcDetailPage(atrDapDrawVo, pageable);
				}
				break;
			case "FO":
				if (isIcg) {
					feeTypeList= atrBussFOLrcIcgCalcDao.findLrcIcgDetail(atrDapDrawVo, pageable);
				} else {
					feeTypeList= atrBussFOLrcIcuCalcDao.findLrcDetailPage(atrDapDrawVo, pageable);
				}
				break;
			case "TI":
				if (isIcg) {
					feeTypeList= atrBussTILrcIcgCalcDao.findLrcIcgDetail(atrDapDrawVo, pageable);
				} else {
					feeTypeList= atrBussTILrcIcuCalcDao.findLrcDetailPage(atrDapDrawVo, pageable);
				}
				break;
			case "TO":
				if (isIcg) {
					feeTypeList= atrBussTOLrcIcgCalcDao.findLrcIcgDetail(atrDapDrawVo, pageable);
				} else {
					feeTypeList= atrBussTOLrcIcuCalcDao.findLrcDetailPage(atrDapDrawVo, pageable);
				}
				break;
			default:
				break;
		}
		return feeTypeList;
	}

	/*
	 * 查询发展期List
	 * */
	@Override
	public List<Integer> findPeriodHeaderData(AtrDapDrawVo atrDapDrawVo) {
		List<Integer> icuDevNoList = new ArrayList<>();
		switch (atrDapDrawVo.getBusinessSourceCode()) {
			case "DD":
				icuDevNoList = atrBussDDLrcIcuCalcDetailDao.findByVo(atrDapDrawVo);
				break;
			case "FO":
				icuDevNoList = atrBussFOLrcIcuCalcDetailDao.findByVo(atrDapDrawVo);
				break;
			case "TI":
				icuDevNoList = atrBussTILrcIcuCalcDetailDao.findByVo(atrDapDrawVo);
				break;
			case "TO":
				icuDevNoList = atrBussTOLrcIcuCalcDetailDao.findByVo(atrDapDrawVo);
				break;
		};
		return icuDevNoList;
	}

	public List<Integer> findIcgDevNo(AtrDapDrawVo atrDapDrawVo) {
		List<Integer> icuDevNoList = new ArrayList<>();
		switch (atrDapDrawVo.getBusinessSourceCode()) {
			case "DD":
				icuDevNoList = atrBussDDLrcIcgCalcDetailDao.findByVo(atrDapDrawVo);
				break;
			case "FO":
				icuDevNoList = atrBussFOLrcIcgCalcDetailDao.findByVo(atrDapDrawVo);
				break;
			case "TI":
				icuDevNoList = atrBussTILrcIcgCalcDetailDao.findByVo(atrDapDrawVo);
				break;
			case "TO":
				icuDevNoList = atrBussTOLrcIcgCalcDetailDao.findByVo(atrDapDrawVo);
				break;
		};
		return icuDevNoList;
	}



	@Override
	@Async("ruleThreadPool")
	public void download(AtrBussLrcActionVo atrBussLrcActionVo, HttpServletRequest request, HttpServletResponse response) throws Exception {
		AtrBussLrcAction po = atrBussLrcActionDao.findById(atrBussLrcActionVo.getId());
		if (ObjectUtils.isNotEmpty(po)) {
			String zipName = atrExportService.getZipName(atrBussLrcActionVo.getTemplateFileName(), atrBussLrcActionVo.getLogFileName());
			String logPath = atrExportService.getOutPutPathSave() + zipName;;
			Long logId = atrExportService.saveExportTrackLog(zipName, atrBussLrcActionVo.getTargetRouter(), logPath, atrBussLrcActionVo.getCreatorId(), false);
			boolean taskStatus = true;
			String errorMsg = null;
			atrBussLrcActionVo.setZipName(zipName);
			try{
				switch (po.getBusinessSourceCode()) {
					case "DD":
						this.downloadDD(po.getActionNo(), atrBussLrcActionVo, request, response);
						break;
					case "FO":
						this.downloadFO(po.getActionNo(), atrBussLrcActionVo, request, response);
						break;
					case "TI":
					case "TO":
						atrBussLrcActionVo.setBusinessSourceCode(po.getBusinessSourceCode());
						this.downloadTreaty(po.getActionNo(), atrBussLrcActionVo, request, response);
						break;
					default:
						break;
				}
			} catch (Exception e) {
				taskStatus = false;
				errorMsg = ExceptionUtils.getStackTrace(e);
				if (errorMsg.length() >= 3900) {
					errorMsg = errorMsg.substring(1, 3900);
				}
				LOG.info(errorMsg);
			} finally {
				atrExportService.updateExportTrackLog(logId,
						taskStatus ? CommonConstant.ExportLogTaskStatus.SUCCESSFUL : CommonConstant.ExportLogTaskStatus.FAIL
						, atrBussLrcActionVo.getCreatorId(), errorMsg);
			}

		}

	}

	@Override
	@Async("ruleThreadPool")
	public void download1(AtrBussLrcActionVo atrBussLrcActionVo, HttpServletRequest request, HttpServletResponse response) throws Exception {
		AtrBussLrcAction po = atrBussLrcActionDao.findById(atrBussLrcActionVo.getId());
		if (ObjectUtils.isNotEmpty(po)) {
			String zipName = atrExportService.getZipName(atrBussLrcActionVo.getTemplateFileName(), atrBussLrcActionVo.getLogFileName());
			String logPath = atrExportService.getOutPutPathSave() + zipName;;
			Long logId = atrExportService.saveExportTrackLog(zipName, atrBussLrcActionVo.getTargetRouter(), logPath, atrBussLrcActionVo.getCreatorId(), false);
			boolean taskStatus = true;
			String errorMsg = null;
			atrBussLrcActionVo.setZipName(zipName);
			atrBussLrcActionVo.setBusinessSourceCode(po.getBusinessSourceCode());
			try{
				this.downloadData(po.getActionNo(), atrBussLrcActionVo, request, response);
			} catch (Exception e) {
				taskStatus = false;
				errorMsg = ExceptionUtils.getStackTrace(e);
				if (errorMsg.length() >= 3900) {
					errorMsg = errorMsg.substring(1, 3900);
				}
				LOG.info(errorMsg);
			} finally {
				atrExportService.updateExportTrackLog(logId,
						taskStatus ? CommonConstant.ExportLogTaskStatus.SUCCESSFUL : CommonConstant.ExportLogTaskStatus.FAIL
						, atrBussLrcActionVo.getCreatorId(), errorMsg);
			}

		}
	}

	@Override
	public Page<AtrDapDrawVo> findPortfolioData(AtrDapDrawVo atrDapDrawVo, Pageable pageParam) {
		if (atrDapDrawVo == null || atrDapDrawVo.getId() == null) {
			return (Page<AtrDapDrawVo>) CollectionUtils.EMPTY_COLLECTION;
		}
		AtrBussLrcActionVo atrBussLrcActionVo = this.findById(atrDapDrawVo.getId());
		Page<AtrDapDrawVo> result = null;
		if(ObjectUtils.isNotEmpty(atrBussLrcActionVo)&& ObjectUtils.isNotEmpty(atrBussLrcActionVo.getActionNo()))
		{
			atrBussLrcActionVo.setPortfolioNo(FilterUtil.transitionSearch(atrDapDrawVo.getPortfolioNo()));
			atrBussLrcActionVo.setIcgNo(FilterUtil.transitionSearch(atrDapDrawVo.getIcgNo()));
			result = atrBussLrcActionDao.findPortfolioData(atrBussLrcActionVo, pageParam);
		}
		return result;
	}

	@Override
	public void calculateAll(AtrBussLrcActionVo lrcActionVo, Long userId) {
		Long entityId = lrcActionVo.getEntityId();
		String yearMonth = lrcActionVo.getYearMonth();
		atrBussLrcService.entry(entityId, yearMonth, "DD");
		atrBussLrcService.entry(entityId, yearMonth, "FO");
		atrBussLrcService.entry(entityId, yearMonth, "TI");
		atrBussLrcService.entry(entityId, yearMonth, "TO");
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
	public String confirmLrcCfVersion(AtrBussLrcActionVo lrcActionVo, Long userId) {
		List<String> businessModelList = bmsConfCodeFeignClient.findByCodeIdx("BusinessModel/Base");
		//po.setCurrencyCode(lrcActionVo.getCurrencyCode());
		if (ObjectUtils.isEmpty(businessModelList)) {
			return "not business model";
		}
		businessModelList.forEach(businessModel-> {
			AtrBussLrcAction po = new AtrBussLrcAction();
			po.setEntityId(lrcActionVo.getEntityId());
			po.setYearMonth(lrcActionVo.getYearMonth());
			po.setBusinessSourceCode(businessModel);
			po.setConfirmIs(CommonConstant.VersionStatus.CONFIRMED);
			Long confirmCount = atrBussLrcActionDao.count(po);
			po.setConfirmIs(CommonConstant.VersionStatus.PENDING);
			Long unConfirmCount = atrBussLrcActionDao.count(po);
			if (ObjectUtils.isNotEmpty(confirmCount) && confirmCount == 0 && ObjectUtils.isNotEmpty(unConfirmCount) && unConfirmCount > 0) {
				Date date = new Date();
				po.setConfirmIs(CommonConstant.VersionStatus.CONFIRMED);
				po.setConfirmUser(userId);
				po.setConfirmTime(date);
				atrBussLrcActionDao.updateConfirm(po);
			}
		});
		AtrConfBussPeriodVo atrConfBussPeriodVo = new AtrConfBussPeriodVo();
		atrConfBussPeriodVo.setEntityId(lrcActionVo.getEntityId());
		atrConfBussPeriodVo.setYearMonth(lrcActionVo.getYearMonth());
		atrConfBussPeriodVo.setProcCode(ActuarialConstant.ProcCode.EXPECTED_CF_LRC);
		atrConfModelDefService.confirmBeCFProc(atrConfBussPeriodVo, userId);
		return null;
	}

	@Override
	public void revoke(AtrBussLrcActionVo bussLrcActionVo, Long userId) {
		AtrBussLrcAction bussLrcAction = new AtrBussLrcAction();
		bussLrcAction.setEntityId(bussLrcActionVo.getEntityId());
		bussLrcAction.setYearMonth(bussLrcActionVo.getYearMonth());
		bussLrcAction.setBusinessSourceCode(bussLrcActionVo.getBusinessSourceCode());
		bussLrcAction.setConfirmIs(CommonConstant.VersionStatus.CONFIRMED);
		Long confirmCount = atrBussLrcActionDao.count(bussLrcAction);
		if (ObjectUtils.isNotEmpty(confirmCount) && confirmCount > 0) {
			Date date = new Date();
			bussLrcAction.setConfirmIs(CommonConstant.VersionStatus.PENDING);
			bussLrcAction.setConfirmUser(userId);
			bussLrcAction.setConfirmTime(date);
			bussLrcAction.setUpdatorId(userId);
			bussLrcAction.setUpdateTime(date);
			atrBussLrcActionDao.revoke(bussLrcAction);
		}
	}

	/*
	* todo 产品后续统一改为使用downloadData()方法
	* */
	private void downloadDD(String actionNo, AtrBussLrcActionVo atrBussLrcActionVo, HttpServletRequest request, HttpServletResponse response) throws Exception {

		AtrDapDrawVo atrDapDrawVo = new AtrDapDrawVo();
		atrDapDrawVo.setActionNo(actionNo);
		atrDapDrawVo.setPortfolioNo(atrBussLrcActionVo.getPortfolioNo());
		atrDapDrawVo.setIcgNo(atrBussLrcActionVo.getIcgNo());
		System.out.println("DD-LRC导出数据查询开始时间：" + LocalTime.now());

		List<Map<String, Object>> lrcIcgCalcList = atrBussDDLrcIcgCalcDao.findDateByVo(atrDapDrawVo);
		List<AtrBussDDLrcIcgCalcDetailVo> lrcIcgPeriodDetailList = atrBussDDLrcIcgCalcDetailDao.findDateByMainId(atrDapDrawVo);
		List<Integer> icuDevNoList = atrBussDDLrcIcuCalcDetailDao.findByVo(atrDapDrawVo);
		List<Integer> icgDevNoList = atrBussDDLrcIcgCalcDetailDao.findByVo(atrDapDrawVo);
		atrDapDrawVo.setDevNoList(icuDevNoList.stream().filter(devNo -> devNo<= ActuarialConstant.Export.CF_MAX_COLUMN_DEV).collect(Collectors.toList()));

		Long countIcuCalc = atrBussDDLrcIcuCalcDao.countDateByVo(atrDapDrawVo);
		Map<String,String> files = new HashMap<>();
		for (int i = 0; i<= countIcuCalc/ActuarialConstant.Export.CF_MAX_ROW ; i++) {
			List<ExcelSheet> sheetList = new ArrayList<>();
			List<Map<String, Object>> afterPremiumRateDetailList = new ArrayList<>();
			List<Map<String, Object>> recvDetailList = new ArrayList<>();
			List<Map<String, Object>> gepDetailList = new ArrayList<>();
			List<Map<String, Object>> gepBackUpDetailList = new ArrayList<>();
			List<Map<String, Object>> uepDetailList = new ArrayList<>();
			List<Map<String, Object>> iacfDetailList = new ArrayList<>();
			List<Map<String, Object>> mainDetailList = new ArrayList<>();
			List<Map<String, Object>> nonMainDetailList = new ArrayList<>();
			List<Map<String, Object>> adjDetailList = new ArrayList<>();
			List<Map<String, Object>> covDetailList = new ArrayList<>();
			List<Map<String, Object>> claimCovDetailList = new ArrayList<>();
			List<Map<String, Object>> csmDetailList = new ArrayList<>();
			atrDapDrawVo.setLimit(ActuarialConstant.Export.CF_MAX_ROW);
			atrDapDrawVo.setOffset(i * ActuarialConstant.Export.CF_MAX_ROW);

			atrDapDrawVo.setFeeType("AFTER_PREMIUM_IMPAIRMENT_RATE");
			afterPremiumRateDetailList = atrBussDDLrcIcuCalcDao.findDateByVo2(atrDapDrawVo);

			atrDapDrawVo.setFeeType("RECV_PREMIUM");
			recvDetailList = atrBussDDLrcIcuCalcDao.findDateByVo2(atrDapDrawVo);

			atrDapDrawVo.setFeeType("ED_PREMIUM");
			gepDetailList = atrBussDDLrcIcuCalcDao.findDateByVo2(atrDapDrawVo);

			atrDapDrawVo.setFeeType("ED_PREMIUM_BACKUP");
			gepBackUpDetailList = atrBussDDLrcIcuCalcDao.findDateByVo2(atrDapDrawVo);

			atrDapDrawVo.setFeeType("IACF_FEE");
			iacfDetailList = atrBussDDLrcIcuCalcDao.findDateByVo2(atrDapDrawVo);

			atrDapDrawVo.setFeeType("MAINTENANCE_FEE");
			mainDetailList = atrBussDDLrcIcuCalcDao.findDateByVo2(atrDapDrawVo);

			atrDapDrawVo.setFeeType("IACF_FEE_NON_POLICY");
			nonMainDetailList = atrBussDDLrcIcuCalcDao.findDateByVo2(atrDapDrawVo);

			atrDapDrawVo.setFeeType("ADJ_COMMISSION");
			adjDetailList = atrBussDDLrcIcuCalcDao.findDateByVo2(atrDapDrawVo);

			atrDapDrawVo.setFeeType("ULTIMATE_LOSS");
			claimCovDetailList = atrBussDDLrcIcuCalcDao.findDateByVo2(atrDapDrawVo);

			//保额不区分计量类型
			atrDapDrawVo.setFeeType("UE_PREMIUM");
			uepDetailList = atrBussDDLrcIcuCalcDao.findDateByVo2(atrDapDrawVo);

			atrDapDrawVo.setFeeType("COVERAGE_AMOUNT");
			covDetailList = atrBussDDLrcIcuCalcDao.findDateByVo2(atrDapDrawVo);

			if (ObjectUtils.isNotEmpty(lrcIcgCalcList) && i==0 ) {
				//8-CSM 比例
				csmDetailList = lrcIcgCalcList;
				csmDetailList.forEach(csmPeriodMap -> {
					List<AtrBussDDLrcIcgCalcDetailVo> adjPeriodDetailList = lrcIcgPeriodDetailList.stream()
							.filter(periodDetail -> csmPeriodMap.get("id").toString().equals(String.valueOf(periodDetail.getMainId())))
							.filter(periodDetail -> ObjectUtils.isNotEmpty(periodDetail.getCsmRate())).collect(Collectors.toList());
					adjPeriodDetailList.forEach(periodDetail -> {
						csmPeriodMap.put(periodDetail.getDevNo().toString(), periodDetail.getCsmRate());
					});
					icgDevNoList.forEach(devNO -> {
						if (!csmPeriodMap.containsKey(devNO.toString())) {
							csmPeriodMap.put(devNO.toString(), null);
						}
					});
				});
			}

			sheetDataMap(sheetList, afterPremiumRateDetailList, recvDetailList, gepDetailList, gepBackUpDetailList, uepDetailList);
			sheetList.add(getSheet(5, "Acquisition_Expenses",iacfDetailList, null));
			sheetList.add(getSheet(6, "Administration_Expenses",mainDetailList, null));
			sheetList.add(getSheet(7, "Non_Administration_Expenses",nonMainDetailList, null));
			sheetList.add(getSheet(8, "adjDetailList",adjDetailList, null));
			sheetList.add(getSheet(9, "Claim",claimCovDetailList, null));
			sheetList.add(getSheet(10, "Coverage_Unit",covDetailList, null));
			sheetList.add(getSheet(11, "CSM ",csmDetailList, null));
			List<Class> voClassList = new ArrayList<>();
			LOG.info("DD-LRC导出数据查询结束时间：" + LocalTime.now());
			Map<String,String> excelName = atrExportService.syncExportSheetList(null, sheetList, voClassList,
					atrBussLrcActionVo.getTemplateFileName(), atrBussLrcActionVo.getTargetRouter(), atrBussLrcActionVo.getCreatorId());
			files.putAll(excelName);
		}
		if (ObjectUtils.isNotEmpty(files)) {
			atrExportService.dealZip(atrBussLrcActionVo.getZipName(), files, atrBussLrcActionVo.getTargetRouter(), atrBussLrcActionVo.getCreatorId());
		}
		System.out.println("DD-LRC导出结束时间：" + LocalTime.now());

	}

	private void downloadData(String actionNo, AtrBussLrcActionVo atrBussLrcActionVo, HttpServletRequest request, HttpServletResponse response) throws Exception {

		AtrBussQuotaValueVo atrBussQuotaValueVo = new AtrBussQuotaValueVo();
		atrBussQuotaValueVo.setBusinessSourceCode(atrBussLrcActionVo.getBusinessSourceCode());
		List<AtrConfBecfOutPutVo> becfOutPuts = atrBussBecfQuotaService.findBecfOutPut(atrBussQuotaValueVo);

		AtrDapDrawVo atrDapDrawVo = new AtrDapDrawVo();
		atrDapDrawVo.setActionNo(actionNo);
		atrDapDrawVo.setPortfolioNo(atrBussLrcActionVo.getPortfolioNo());
		atrDapDrawVo.setIcgNo(atrBussLrcActionVo.getIcgNo());
		atrDapDrawVo.setBusinessSourceCode(atrBussLrcActionVo.getBusinessSourceCode());
		atrDapDrawVo.setLanguage(atrBussLrcActionVo.getLanguage());
 		List<Integer> icuDevNoList = this.findPeriodHeaderData(atrDapDrawVo);
		List<Integer> icgDevNoList = this.findIcgDevNo(atrDapDrawVo);
		Long countIcuCalc = atrBussDDLrcIcuCalcDao.countDateByVo(atrDapDrawVo);
		Map<String,String> files = new HashMap<>();
		for (int i = 0; i<= countIcuCalc/ActuarialConstant.Export.CF_MAX_ROW ; i++) {
			List<ExcelSheet> sheetList = new ArrayList<>();
			atrDapDrawVo.setLimit(ActuarialConstant.Export.CF_MAX_ROW);
			atrDapDrawVo.setOffset(i * ActuarialConstant.Export.CF_MAX_ROW);
			Pageable pageable = new Pageable(i, Math.toIntExact(ActuarialConstant.Export.CF_MAX_ROW));

			for(int j = 0; j< becfOutPuts.size(); j++){

				ExcelSheet excelSheet = genExcelSheet(j, becfOutPuts.get(j), atrDapDrawVo, pageable,  icuDevNoList, icgDevNoList);
				sheetList.add(excelSheet);
			}
 			List<Class> voClassList = new ArrayList<>();
			LOG.info("DD-LRC导出数据查询结束时间：" + LocalTime.now());
			Map<String,String> excelName = atrExportService.syncExportSheetList(null, sheetList, voClassList,
					atrBussLrcActionVo.getTemplateFileName(), atrBussLrcActionVo.getTargetRouter(), atrBussLrcActionVo.getCreatorId());
			files.putAll(excelName);
		}
		if (ObjectUtils.isNotEmpty(files)) {
			atrExportService.dealZip(atrBussLrcActionVo.getZipName(), files, atrBussLrcActionVo.getTargetRouter(), atrBussLrcActionVo.getCreatorId());
		}
	}


	private ExcelSheet genExcelSheet(int sheetNo, AtrConfBecfOutPutVo becfOutPutVo, AtrDapDrawVo atrDapDrawVo, Pageable pageable, List<Integer> icuDevNoList, List<Integer> icgDevNoList) {
		atrDapDrawVo.setFeeType(becfOutPutVo.getOutCode());
		Page<List<Map<String, Object>>> detailListPage = this.findPeriodData1(becfOutPutVo, atrDapDrawVo, pageable, icuDevNoList, icgDevNoList);
		return getSheet(sheetNo, becfOutPutVo.getRemark(), detailListPage.getContent(), null);
	}

 	public Page<List<Map<String, Object>>> findPeriodData1(AtrConfBecfOutPutVo becfOutPutVo, AtrDapDrawVo atrDapDrawVo, Pageable pageable, List<Integer> icuDevNoList, List<Integer> icgDevNoList) {
		Page<List<Map<String, Object>>> feeTypeList = null;
		if(ObjectUtils.isEmpty(atrDapDrawVo) && ObjectUtils.isEmpty(atrDapDrawVo.getBusinessSourceCode())){
			return feeTypeList;
		}
 	    Boolean isIcg = "G".equals(becfOutPutVo.getDimension());
		Boolean isDev = "1".equals(becfOutPutVo.getType());
		atrDapDrawVo.setDevNoList(Collections.emptyList());
		if (isIcg && isDev) {
			atrDapDrawVo.setDevNoList(icgDevNoList.stream().filter(devNo -> devNo<= ActuarialConstant.Export.CF_MAX_COLUMN_DEV).collect(Collectors.toList()));
		}
		if (!isIcg && isDev) {
			atrDapDrawVo.setDevNoList(icuDevNoList.stream().filter(devNo -> devNo<= ActuarialConstant.Export.CF_MAX_COLUMN_DEV).collect(Collectors.toList()));
		}
		switch (atrDapDrawVo.getBusinessSourceCode()) {
			case "DD":
				if (isIcg) {
					feeTypeList= atrBussDDLrcIcgCalcDao.findLrcIcgDetail(atrDapDrawVo, pageable);
				} else {
					feeTypeList= atrBussDDLrcIcuCalcDao.findLrcDetailPage(atrDapDrawVo, pageable);
				}
				break;
			case "FO":
				if (isIcg) {
					feeTypeList= atrBussFOLrcIcgCalcDao.findLrcIcgDetail(atrDapDrawVo, pageable);
				} else {
					feeTypeList= atrBussFOLrcIcuCalcDao.findLrcDetailPage(atrDapDrawVo, pageable);
				}
				break;
			case "TI":
				if (isIcg) {
					feeTypeList= atrBussTILrcIcgCalcDao.findLrcIcgDetail(atrDapDrawVo, pageable);
				} else {
					feeTypeList= atrBussTILrcIcuCalcDao.findLrcDetailPage(atrDapDrawVo, pageable);
				}
				break;
			case "TO":
				if (isIcg) {
					feeTypeList= atrBussTOLrcIcgCalcDao.findLrcIcgDetail(atrDapDrawVo, pageable);
				} else {
					feeTypeList= atrBussTOLrcIcuCalcDao.findLrcDetailPage(atrDapDrawVo, pageable);
				}
				break;
			default:
				break;
		}
		return feeTypeList;
	}



	private void sheetDataMap(List<ExcelSheet> sheetList, List<Map<String, Object>> afterPremiumRateDetailList, List<Map<String, Object>> recvDetailList, List<Map<String, Object>> gepDetailList, List<Map<String, Object>> gepBackUpDetailList, List<Map<String, Object>> uepDetailList) {
		sheetList.add(getSheet(0, "AFTER_PREMIUM",afterPremiumRateDetailList, null));
		sheetList.add(getSheet(1, "Prem_Receipt",recvDetailList, null));
		sheetList.add(getSheet(2, "Unearned_Premium",uepDetailList, null));
		sheetList.add(getSheet(3, "GEP_Persist",gepDetailList, null));
		sheetList.add(getSheet(4, "GEP_Persist_BACKUP",gepBackUpDetailList, null));
	}


	private void downloadFO(String actionNo, AtrBussLrcActionVo atrBussLrcActionVo, HttpServletRequest request, HttpServletResponse response) throws Exception {
		AtrDapDrawVo atrDapDrawVo = new AtrDapDrawVo();
		atrDapDrawVo.setActionNo(actionNo);
		atrDapDrawVo.setPortfolioNo(atrBussLrcActionVo.getPortfolioNo());
		atrDapDrawVo.setIcgNo(atrBussLrcActionVo.getIcgNo());
		LOG.info("FO-LRC导出开始时间：" + LocalTime.now());
		List<Map<String, Object>> lrcIcgCalcList = atrBussFOLrcIcgCalcDao.findDateByVo(atrDapDrawVo);
		List<AtrBussFOLrcIcgCalcDetailVo> lrcIcgPeriodDetailList = atrBussFOLrcIcgCalcDetailDao.findDateByMainId(atrDapDrawVo);
		List<Integer> icuDevNoList = atrBussFOLrcIcuCalcDetailDao.findByVo(atrDapDrawVo);
		List<Integer> icgDevNoList = atrBussFOLrcIcgCalcDetailDao.findByVo(atrDapDrawVo);
		atrDapDrawVo.setDevNoList(icuDevNoList.stream().filter(devNo -> devNo<= ActuarialConstant.Export.CF_MAX_COLUMN_DEV).collect(Collectors.toList()));

		Long countIcuCalc = atrBussFOLrcIcuCalcDao.countDateByVo(atrDapDrawVo);
		Map<String,String> files = new HashMap<>();
		for (int i = 0; i<= countIcuCalc/ActuarialConstant.Export.CF_MAX_ROW ; i++) {
			atrDapDrawVo.setLimit(ActuarialConstant.Export.CF_MAX_ROW);
			atrDapDrawVo.setOffset(i * ActuarialConstant.Export.CF_MAX_ROW);
			List<ExcelSheet> sheetList = new ArrayList<>();
			List<Map<String, Object>> afterPremiumRateDetailList = new ArrayList<>();
			List<Map<String, Object>> recvDetailList = new ArrayList<>();
			List<Map<String, Object>> gepDetailList = new ArrayList<>();
			List<Map<String, Object>> uepDetailList = new ArrayList<>();
			List<Map<String, Object>> gepBackUpDetailList = new ArrayList<>();
			List<Map<String, Object>> mainDetailList = new ArrayList<>();
			List<Map<String, Object>> nonMainDetailList = new ArrayList<>();
			List<Map<String, Object>> adjDetailList = new ArrayList<>();
			List<Map<String, Object>> covDetailList = new ArrayList<>();
			List<Map<String, Object>> claimCovDetailList = new ArrayList<>();
			List<Map<String, Object>> csmDetailList = new ArrayList<>();

			atrDapDrawVo.setFeeType("AFTER_PREMIUM_IMPAIRMENT_RATE");
			afterPremiumRateDetailList = atrBussFOLrcIcuCalcDao.findDateByVo(atrDapDrawVo);

			atrDapDrawVo.setFeeType("RECV_PREMIUM");
			recvDetailList = atrBussFOLrcIcuCalcDao.findDateByVo(atrDapDrawVo);

			atrDapDrawVo.setFeeType("ED_PREMIUM");
			gepDetailList = atrBussFOLrcIcuCalcDao.findDateByVo(atrDapDrawVo);

			atrDapDrawVo.setFeeType("ED_PREMIUM_BACKUP");
			gepBackUpDetailList = atrBussFOLrcIcuCalcDao.findDateByVo(atrDapDrawVo);

			atrDapDrawVo.setFeeType("MAINTENANCE_FEE");
			mainDetailList = atrBussFOLrcIcuCalcDao.findDateByVo(atrDapDrawVo);

			atrDapDrawVo.setFeeType("IACF_FEE_NON_POLICY");
			nonMainDetailList = atrBussDDLrcIcuCalcDao.findDateByVo2(atrDapDrawVo);

			atrDapDrawVo.setFeeType("ADJ_COMMISSION");
			adjDetailList = atrBussFOLrcIcuCalcDao.findDateByVo(atrDapDrawVo);

			atrDapDrawVo.setFeeType("ULTIMATE_LOSS");
			claimCovDetailList = atrBussFOLrcIcuCalcDao.findDateByVo(atrDapDrawVo);

			//保额不区分计量类型
			atrDapDrawVo.setFeeType("UE_PREMIUM");
			uepDetailList = atrBussFOLrcIcuCalcDao.findDateByVo(atrDapDrawVo);

			atrDapDrawVo.setFeeType("COVERAGE_AMOUNT");
			covDetailList = atrBussFOLrcIcuCalcDao.findDateByVo(atrDapDrawVo);


			if (ObjectUtils.isNotEmpty(lrcIcgCalcList) && i==0 ) {
				//8-CSM 比例
				csmDetailList = lrcIcgCalcList;
				csmDetailList.forEach(csmPeriodMap -> {
					List<AtrBussFOLrcIcgCalcDetailVo> adjPeriodDetailList = lrcIcgPeriodDetailList.stream()
							.filter(periodDetail -> csmPeriodMap.get("id").toString().equals(String.valueOf(periodDetail.getMainId())))
							.filter(periodDetail -> ObjectUtils.isNotEmpty(periodDetail.getCsmRate())).collect(Collectors.toList());
					adjPeriodDetailList.forEach(periodDetail -> {
						csmPeriodMap.put(periodDetail.getDevNo().toString(), periodDetail.getCsmRate());
					});
					icgDevNoList.forEach(devNO -> {
						if (!csmPeriodMap.containsKey(devNO.toString())) {
							csmPeriodMap.put(devNO.toString(), null);
						}
					});
				});
			}
			sheetDataMap(sheetList, afterPremiumRateDetailList, recvDetailList, gepDetailList, gepBackUpDetailList, uepDetailList);
			sheetList.add(getSheet(5, "Administration_Expenses",mainDetailList, null));
			sheetList.add(getSheet(6, "Non_Administration_Expenses",nonMainDetailList, null));
			sheetList.add(getSheet(7, "adjDetailList",adjDetailList, null));
			sheetList.add(getSheet(8, "Claim",claimCovDetailList, null));
			sheetList.add(getSheet(9, "Coverage_Unit",covDetailList, null));
			sheetList.add(getSheet(10, "CSM ",csmDetailList, null));

			List<Class> voClassList = new ArrayList<>(1);
			voClassList.add(AtrBussDDLrcIcuCalcVo.class);
			Map<String,String> excelName = atrExportService.syncExportSheetList(null, sheetList, voClassList,
					atrBussLrcActionVo.getTemplateFileName(), atrBussLrcActionVo.getTargetRouter(), atrBussLrcActionVo.getCreatorId());
			files.putAll(excelName);
		}
		if (ObjectUtils.isNotEmpty(files)) {
			atrExportService.dealZip(atrBussLrcActionVo.getZipName(), files, atrBussLrcActionVo.getTargetRouter(), atrBussLrcActionVo.getCreatorId());
		}
	}

	private void downloadTreaty(String actionNo, AtrBussLrcActionVo atrBussLrcActionVo, HttpServletRequest request, HttpServletResponse response) throws Exception {
		AtrDapDrawVo atrDapDrawVo = new AtrDapDrawVo();
		atrDapDrawVo.setActionNo(actionNo);
		atrDapDrawVo.setPortfolioNo(atrBussLrcActionVo.getPortfolioNo());
		atrDapDrawVo.setIcgNo(atrBussLrcActionVo.getIcgNo());
		List<Map<String, Object>> lrcIcuCalcList;
		List<AtrBussTILrcIcuCalcDetailVo> lrcIcuPeriodDetailList;
		List<Map<String, Object>> lrcIcgCalcList;
		List<AtrBussTILrcIcgCalcDetailVo> lrcIcgPeriodDetailList;
		List<Integer> icuDevNoList;
		List<Integer> icgDevNoList;
		Long countIcuCalc;
		Boolean isTreatyInType = atrBussLrcActionVo.getBusinessSourceCode().equals("TI");
		if (isTreatyInType) {
			lrcIcgCalcList = atrBussTILrcIcgCalcDao.findDateByVo(atrDapDrawVo);
			lrcIcgPeriodDetailList = atrBussTILrcIcgCalcDetailDao.findDateByMainId(atrDapDrawVo);
			icuDevNoList = atrBussTILrcIcuCalcDetailDao.findByVo(atrDapDrawVo);
			icgDevNoList = atrBussTILrcIcgCalcDetailDao.findByVo(atrDapDrawVo);
			atrDapDrawVo.setDevNoList(icuDevNoList.stream().filter(devNo -> devNo<= ActuarialConstant.Export.CF_MAX_COLUMN_DEV).collect(Collectors.toList()));
			countIcuCalc = atrBussTILrcIcuCalcDao.countDateByVo(atrDapDrawVo);
		} else {
			lrcIcgCalcList = atrBussTOLrcIcgCalcDao.findDateByVo(atrDapDrawVo);
			lrcIcgPeriodDetailList = ClassUtil.convert(atrBussTOLrcIcgCalcDetailDao.findDateByMainId(atrDapDrawVo),AtrBussTILrcIcgCalcDetailVo.class);
			icuDevNoList = atrBussTOLrcIcuCalcDetailDao.findByVo(atrDapDrawVo);
			icgDevNoList = atrBussTOLrcIcgCalcDetailDao.findByVo(atrDapDrawVo);
			atrDapDrawVo.setDevNoList(icuDevNoList.stream().filter(devNo -> devNo<= ActuarialConstant.Export.CF_MAX_COLUMN_DEV).collect(Collectors.toList()));
			countIcuCalc = atrBussTILrcIcuCalcDao.countDateByVo(atrDapDrawVo);
		}
		Map<String,String> files = new HashMap<>();
		LOG.error("TO-LRC导出数据查询开始时间：" + LocalTime.now());
		for (int i = 0; i<= countIcuCalc/ActuarialConstant.Export.CF_MAX_ROW ; i++) {
			atrDapDrawVo.setLimit(ActuarialConstant.Export.CF_MAX_ROW);
			atrDapDrawVo.setOffset(i * ActuarialConstant.Export.CF_MAX_ROW);
			List<ExcelSheet> sheetList = new ArrayList<>();
			List<Map<String, Object>> afterPremiumRateDetailList = new ArrayList<>();
			List<Map<String, Object>> recvDetailList = new ArrayList<>();
			List<Map<String, Object>> gepDetailList = new ArrayList<>();
			List<Map<String, Object>> gepBackUpDetailList = new ArrayList<>();
			List<Map<String, Object>> uepDetailList = new ArrayList<>();
			List<Map<String, Object>> mainDetailList = new ArrayList<>();
			List<Map<String, Object>> nonMainDetailList = new ArrayList<>();
			List<Map<String, Object>> adjDetailList = new ArrayList<>();
			List<Map<String, Object>> brokerDetailList = new ArrayList<>();
			List<Map<String, Object>> covDetailList = new ArrayList<>();
			List<Map<String, Object>> claimCovDetailList = new ArrayList<>();
			List<Map<String, Object>> csmDetailList = new ArrayList<>();

			atrDapDrawVo.setFeeType("AFTER_PREMIUM_IMPAIRMENT_RATE");
			afterPremiumRateDetailList = isTreatyInType? atrBussTILrcIcuCalcDao.findDateByVo(atrDapDrawVo) : atrBussTOLrcIcuCalcDao.findDateByVo(atrDapDrawVo);

			atrDapDrawVo.setFeeType("RECV_PREMIUM");
			recvDetailList = isTreatyInType? atrBussTILrcIcuCalcDao.findDateByVo(atrDapDrawVo) : atrBussTOLrcIcuCalcDao.findDateByVo(atrDapDrawVo);

			atrDapDrawVo.setFeeType("ED_PREMIUM");
			gepDetailList = isTreatyInType? atrBussTILrcIcuCalcDao.findDateByVo(atrDapDrawVo) : atrBussTOLrcIcuCalcDao.findDateByVo(atrDapDrawVo);

			atrDapDrawVo.setFeeType("ED_PREMIUM_BACKUP");
			gepBackUpDetailList = isTreatyInType? atrBussTILrcIcuCalcDao.findDateByVo(atrDapDrawVo) : atrBussTOLrcIcuCalcDao.findDateByVo(atrDapDrawVo);

			atrDapDrawVo.setFeeType("MAINTENANCE_FEE");
			mainDetailList = isTreatyInType? atrBussTILrcIcuCalcDao.findDateByVo(atrDapDrawVo) : atrBussTOLrcIcuCalcDao.findDateByVo(atrDapDrawVo);

			if (isTreatyInType) {
				atrDapDrawVo.setFeeType("IACF_FEE_NON_POLICY");
				nonMainDetailList = atrBussDDLrcIcuCalcDao.findDateByVo2(atrDapDrawVo);
			}

			atrDapDrawVo.setFeeType("ADJ_COMMISSION");
			adjDetailList = isTreatyInType? atrBussTILrcIcuCalcDao.findDateByVo(atrDapDrawVo) : atrBussTOLrcIcuCalcDao.findDateByVo(atrDapDrawVo);

			atrDapDrawVo.setFeeType("Brokerage_Fee");
			brokerDetailList = isTreatyInType? atrBussTILrcIcuCalcDao.findDateByVo(atrDapDrawVo) : atrBussTOLrcIcuCalcDao.findDateByVo(atrDapDrawVo);

			atrDapDrawVo.setFeeType("ULTIMATE_LOSS");
			claimCovDetailList = isTreatyInType? atrBussTILrcIcuCalcDao.findDateByVo(atrDapDrawVo) : atrBussTOLrcIcuCalcDao.findDateByVo(atrDapDrawVo);

			//保额不区分计量类型
			atrDapDrawVo.setFeeType("UE_PREMIUM");
			uepDetailList = isTreatyInType? atrBussTILrcIcuCalcDao.findDateByVo(atrDapDrawVo) : atrBussTOLrcIcuCalcDao.findDateByVo(atrDapDrawVo);
			atrDapDrawVo.setFeeType("COVERAGE_AMOUNT");
			covDetailList = isTreatyInType? atrBussTILrcIcuCalcDao.findDateByVo(atrDapDrawVo) : atrBussTOLrcIcuCalcDao.findDateByVo(atrDapDrawVo);

			if (ObjectUtils.isNotEmpty(lrcIcgCalcList) && i==0 ) {
				//8-CSM 比例
				csmDetailList = lrcIcgCalcList;
				csmDetailList.forEach(csmPeriodMap -> {
					List<AtrBussTILrcIcgCalcDetailVo> adjPeriodDetailList = lrcIcgPeriodDetailList.stream()
							.filter(periodDetail -> csmPeriodMap.get("id").toString().equals(String.valueOf(periodDetail.getMainId())))
							.filter(periodDetail -> ObjectUtils.isNotEmpty(periodDetail.getCsmRate())).collect(Collectors.toList());
					adjPeriodDetailList.forEach(periodDetail -> {
						csmPeriodMap.put(periodDetail.getDevNo().toString(), periodDetail.getCsmRate());
					});
					icgDevNoList.forEach(devNO -> {
						if (!csmPeriodMap.containsKey(devNO.toString())) {
							csmPeriodMap.put(devNO.toString(), null);
						}
					});
				});
			}

			sheetList.add(getSheet(0, "AFTER_PREMIUM",afterPremiumRateDetailList, null));
			sheetList.add(getSheet(1, "Prem_Receipt",recvDetailList, AtrBussTOLrcIcuCalcVo.class));
			sheetList.add(getSheet(2, "Unearned_Premium",uepDetailList, AtrBussTOLrcIcuCalcVo.class));
			sheetList.add(getSheet(3, "GEP_Persist",gepDetailList, AtrBussTOLrcIcuCalcVo.class));
			sheetList.add(getSheet(4, "GEP_Persist_BACKUP",gepBackUpDetailList, AtrBussDDLrcIcuCalcVo.class));
			sheetList.add(getSheet(5, "Administration_Expenses",mainDetailList, AtrBussTOLrcIcuCalcVo.class));
			if (isTreatyInType) {
				sheetList.add(getSheet(6, "Non_Administration_Expenses",nonMainDetailList, null));
				sheetList.add(getSheet(7, "adjDetailList",adjDetailList, AtrBussTOLrcIcuCalcVo.class));
				sheetList.add(getSheet(8, "Broker",brokerDetailList, AtrBussTOLrcIcuCalcVo.class));
				sheetList.add(getSheet(9, "Claim",claimCovDetailList, AtrBussTOLrcIcuCalcVo.class));
				sheetList.add(getSheet(10, "Coverage_Unit",covDetailList, AtrBussTOLrcIcuCalcVo.class));
				sheetList.add(getSheet(11, "CSM ",csmDetailList, AtrBussTOLrcIcuCalcVo.class));

			} else {
				sheetList.add(getSheet(6, "adjDetailList",adjDetailList, AtrBussTOLrcIcuCalcVo.class));
				sheetList.add(getSheet(7, "Broker",brokerDetailList, AtrBussTOLrcIcuCalcVo.class));
				sheetList.add(getSheet(8, "Claim",claimCovDetailList, AtrBussTOLrcIcuCalcVo.class));
				sheetList.add(getSheet(9, "Coverage_Unit",covDetailList, AtrBussTOLrcIcuCalcVo.class));
				sheetList.add(getSheet(10, "CSM ",csmDetailList, AtrBussTOLrcIcuCalcVo.class));
			}

			List<Class> voClassList = new ArrayList<>(1);
			voClassList.add(AtrBussDDLrcIcuCalcVo.class);
			Map<String,String> excelName = atrExportService.syncExportSheetList(null, sheetList, voClassList,
					atrBussLrcActionVo.getTemplateFileName(), atrBussLrcActionVo.getTargetRouter(), atrBussLrcActionVo.getCreatorId());
			files.putAll(excelName);
		}
		if (ObjectUtils.isNotEmpty(files)) {
			atrExportService.dealZip(atrBussLrcActionVo.getZipName(), files, atrBussLrcActionVo.getTargetRouter(), atrBussLrcActionVo.getCreatorId());
		}
	}


	//组装Excel的Sheet
	private ExcelSheet getSheet(int sheetNo,String sheetName, List<?> beanData, Class voClazz) {
		ExcelSheet sheet = new ExcelSheet();
		sheet.setSheetNo(sheetNo);
		sheet.setSheetName(sheetName);
		List<ExcelSheetData> sheetDataList = new ArrayList<>();
		ExcelSheetData sheetData = new ExcelSheetData();
		sheetData.setBeanKey("df");
		sheetData.setBeanData(beanData);
		//sheetData.setVoClazz(voClazz);
		sheetDataList.add(sheetData);
		sheet.setSheetDataList(sheetDataList);
		return sheet;
	}

	//组装Excel的Sheet
	private ExcelSheet getSheet1(int sheetNo,String sheetName, List<?> beanData, List<?> beanData2) {
		ExcelSheet sheet = new ExcelSheet();
		sheet.setSheetNo(sheetNo);
		sheet.setSheetName(sheetName);
		List<ExcelSheetData> sheetDataList = new ArrayList<>();
		ExcelSheetData sheetData = new ExcelSheetData();
		sheetData.setBeanKey("df");
		sheetData.setBeanData(beanData2);

		ExcelSheetData sheetData2 = new ExcelSheetData();
		sheetData.setBeanKey("dp");
		sheetData.setBeanData(beanData);
		//sheetData.setVoClazz(voClazz);
		sheetDataList.add(sheetData);
		sheetDataList.add(sheetData2);
		sheet.setSheetDataList(sheetDataList);
		return sheet;
	}
	/**
	 * List<Map>对象复制功能
	 * List<Map> 转化为 List<Map>
	 * @param list
	 */
	public static List<Map<String, Object>> convert(List<Map<String, Object>> list) {
		List<Map<String, Object>> newMapList = new ArrayList<>();
		for (Map map : list) {
			try {
				newMapList.add((Map) SerializationUtils.clone((Serializable) map));
			} catch (Exception ex) {
				throw new SsException(ex.getLocalizedMessage(), ex);
			}
		}
		return newMapList;
	}

	@Override
	public void calculateTransitionEarnedPremium(Long entityId, Long userId) {

		try {
            // 检查已有数据
            checkExistingTransitionData(entityId);

			// 评估期开始和结束
			String startYearMonth = "201601"; // 开始评估期
			String endYearMonth = "202412";   // 结束评估期
			String currentYearMonth = startYearMonth;

			// 循环计算每个月的已赚保费
			while (currentYearMonth.compareTo(endYearMonth) <= 0) {

				// 调用已赚保费计算方法
				atrBussLrcDdService.calculateEdPremium(currentYearMonth, entityId);
				atrBussLrcFoService.calculateEdPremium(currentYearMonth, entityId);
				atrBussLrcTotService.calculateTransitionEdPremium(currentYearMonth, entityId);
				
				// 计算下一个月
				currentYearMonth = Dates.getYearMonth(currentYearMonth, 1);
			}


			atrBussLrcDdService.backupAndCleanHistoricalData(entityId);
			atrBussLrcFoService.backupAndCleanHistoricalData();
			atrBussLrcTotService.backupAndCleanHistoricalData(entityId);
			
		} catch (Exception e) {
			LOG.error("计算转换已赚保费失败: {}", e.getMessage(), e);
			throw new RuntimeException("计算转换已赚保费失败: " + e.getMessage(), e);
		}
	}

	@Override
	public void reSetLrcCashFlow(AtrConfBussPeriodVo atrConfBussPeriodVo) {
		atrBussLrcActionDao.reSetLrcCashFlow(atrConfBussPeriodVo);
	}
    /**
     * 检查是否已存在过渡期已赚保费数据
     * 如果任一表中已有数据，则抛出异常
     * 采用短路逻辑：检查到某一表有数据，就不再继续检查其他表
     *
     * @param entityId 机构ID
     */
    private void checkExistingTransitionData(Long entityId) {
        // 检查DD业务表，如果有数据则直接抛异常
        if (atrBussLrcDdService.hasTransitionData(entityId)) {
            LOG.error("已存在过渡期已赚保费数据，不允许重复计算。DD业务已有数据。");
            throw new RuntimeException("已存在过渡期已赚保费数据，不允许重复计算。DD业务已有数据。");
        }
        
        // 检查FO业务表，如果有数据则直接抛异常
        if (atrBussLrcFoService.hasTransitionData(entityId)) {
            LOG.error("已存在过渡期已赚保费数据，不允许重复计算。FO业务已有数据。");
            throw new RuntimeException("已存在过渡期已赚保费数据，不允许重复计算。FO业务已有数据。");
        }
        
        // 检查TO业务表，如果有数据则直接抛异常
        if (atrBussLrcTotService.hasTransitionData(entityId)) {
            LOG.error("已存在过渡期已赚保费数据，不允许重复计算。TO业务已有数据。");
            throw new RuntimeException("已存在过渡期已赚保费数据，不允许重复计算。TO业务已有数据。");
        }
    }

}
