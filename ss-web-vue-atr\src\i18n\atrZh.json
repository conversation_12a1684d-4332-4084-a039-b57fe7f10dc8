{"atrCurrency": "币别", "atrYearMonth": "适用期", "atrNormMonth": "标准月份", "atrNormTime": "时长(年)", "atrLowValue": "低值", "atrLowProfitRate": "低值收益率(%)", "atrHighValue": "高值", "atrHighProfitRate": "高值收益率(%)", "atrInterpolatedSpotRate": "插值即期利率(%)", "atrLiquidityPremium": "流动性溢价", "atrAnnualSpotRateWithPremium": "含溢价年度即期利率(%)", "atrMonthlySpotRateWithPremium": "含溢价月度即期利率(%)", "atrMonthlyForwardRateWithPremium": "含溢价月度远期利率(%)", "atrForwardDiscountFactor": "远期折现因子", "atrSpotDiscountFactor": "即期折现因子", "atrMonImmProfit": "月度年化即期收益率(%)", "atrMonImmProfitFlow": "远期利率(包括流动性溢价)", "atrMonFarProfitFlow": "当月远期利率曲线(包括流动性溢价)", "atrCurveSegmentation": "利率曲线分段", "atrFirstInterpolation": "第一次插值", "atrSecondInterpolation": "第二次插值", "atrUltimateMethod": "终极方法", "atrConfInterestDelMsg": "该版本已确认已采用，无法删除!", "atrExcelIsNull": "请选择导入文件", "atrImportYearIs": "获取到配置的导入年份有误！", "atrConfigYear": "控制导入文件条数（年）", "atrFormatIsIntZeroError": "格式错误，应为大于0", "atrUploadIsNull": "上传文件不能为空", "atrInterestRateResultInfo": "无风险利率曲线计算结果信息", "atrCurrForwardRate": "当期远期利率曲线", "atrCurrInitialRate": "即期初始利率曲线", "atrWeightedMeanRate": "加权平均初始利率曲线", "atrPayTimeSwitch": "收取/支付时点", "atrInitialWeightedMean": "初始利率加权权重", "atrNewPolicyWeight": "新单权重", "atrPolicyWeight": "非新单权重", "atrQuotaYearMonth": "适应计量月份", "atrAccidentYearMonth": "事故年月", "atrRiskClass": "产品风险大类", "atrDevelopMonth": "发展期", "atrDevelopYear": "发展年", "atrQuotaType": "假设类型", "atrCoefficientValue": "假设值", "atrCoefficientDescribe": "假设值描述", "atrCoefficientValueDraw": "假设值提取", "atrLossRate": "比例", "atrDefaultValue": "系数", "atrQuotaDetail": "发展期假设", "atrBtnDevExport": "发展期导出", "atrBtnDevImport": "发展期导入", "atrQuotaExtra": "假设设置", "atrQuotaTypeIs": "获取假设类型有误！", "atrAllNumber": "只能正整数！", "atrNumber": "只能是数字！", "atrRate": "比例不能大于100！", "atBusinessrOrDev": "业务年份/发展年份", "rptBussPeriod": "业务年月", "atrLicYearMonth": "计量月份", "atrExtractYearMonth": "提取年月", "atrDataSource": "数据来源", "atrCalProductClass": "产品风险大类", "atrTraceabilityInterval": "追溯维度", "atrTracingIntervalNumber": "追溯区间数", "atrDrawTime": "提取时间", "atrDrawStartDate": "提取起始日期", "atrDrawEndDate": "提取结束日期", "atrDrawEndYear": "提取结束年月", "atrReserveOsExtract": "未决赔款准备金提取", "atrExtractDate": "提取日期", "atrReserveOsDetail": "未决准备金明细", "atrBtnDraw": "提取", "atrExecutorName": "执行人", "atrExecutorTime": "执行时间", "atrLicState": "状态", "atrLicResult": "LIC 金额", "atrConfirmResult": "确认结果", "atrLicConfirm": "LIC/CSM 确认", "atrConfirmName": "确认人", "atrConfirmTime": "确认时间", "atrDrawBegin": "LIC/CSM计算开始", "atrBtnCala": "计算", "atrItemYear": "年份", "atrBtnInflationRate": "比例总览", "atrCoefficient": "系数%", "atrConfirmState": "确认状态", "atrInflationMation": "通货膨胀系数信息", "atrDebtIncurred": "已发生责任负债", "atrTriangleFlowOfAccidentMonth": "事故年月与赔付年月三角流量", "atrTriangleFlowOfCumulativeMonth": "事故年月与累计赔付年月三角流量", "atrLossCoef": "平均损失发展系数", "atrAccumulatedLoss": "最终累计损失额(实际+预期)", "atrMonthlyLoss": "损失额(实际+预期)", "atrCashFlow": "期末现金流(假设)", "atrQuota": "假设", "atrStepFirst": "第一步", "atrStepSecond": "第二步", "atrStepThird": "第三步", "atrStepFourth": "第四步", "atrStepFifth": "第五步", "atrStepSixth": "第六步", "atrStepSeventh": "第七步", "atrDateOfAccident": "事故发生年月", "atrBtnLICNext": "计算(LIC)", "atrBtnCSMNext": "计算(CSM)", "gConfirm": "确认", "gConfirmSuccess": "确认成功", "gConfirmErrer": "确认失败", "gLicConfirmed": "已存在批准的版本", "gLicAccidentAllInd": "事故年月累计赔付：", "gLicPremium": "保费", "gLicLossRate": "2020年度损失率:", "gLicIBnr": "IBNR", "gLicPlus": "+", "atrCalcType": "计算类型", "atrLicConfirmed": "LIC 确认", "atrCSMConfirmed": "CSM 确认", "atrFormatIsRate": "格式错误，占比应在0-100内", "atrDeleteConfirmedMsg": "已批准的版本不能删除！", "atrPaidClaim": "已付索赔", "atrCaseReserve": "报案准备金", "atrIBNRAlgorithm": "IBNR 算法", "atrMeasureMethod": "计量方式", "sysAtrQualificationInfo": "未到期计提模式配置信息", "atrBtnLICCalc": "LIC计算", "atrBtnCSMCalc": "CSM计算", "atrBtnDetailData": "明细数据", "atrLrcDrawBegin": "LRC计算开始", "atrLrcDrawCondition": "提取条件", "atrLrcAmount": "LRC 金额", "atrLrcConfirmed": "LRC 确认", "atrLrcRealityAmount": "实际未赚金额", "atrRadioProductLine": "按产品线", "atrRadioPortfolioLine": "按合同组线", "atrCalcQueryCondition": "查询条件", "atrTabsMain": "主信息", "atrTabsResult": "结果", "atrTabsHistory": "历史数据", "atrAssumption": "假设", "atrMain": "主要", "atrMeasurementModel": "计量模型", "atrItem": "项", "atrAction": "操作", "atrQuantificationByProductLine": "按产品线进行量化", "atrQuantificationByPortfolio": "按投资组合进行量化", "atrMoreQuery": "更多查询条件", "atrAcquisitionSource": "采集源", "atrPremiumAmount": "保险费金额", "atrClaimsAcq": "赔款", "atrRa": "风险调整", "atrPcFcf": "年金现值(履约)", "atrPvRa": "年金现值(风险调整)", "atr∑PvRa": "∑年金现值(风险调整)", "atrExpenseAcq": "获取费用", "atrDiscountRate": "折现率", "atrHistoryData": "历史数据", "atrMonth1": "一月", "atrMonth2": "二月", "atrMonth3": "三月", "atrMonth4": "四月", "atrMonth5": "五月", "atrMonth6": "六月", "atrMonth7": "七月", "atrMonth8": "八月", "atrMonth9": "九月", "atrMonth10": "十月", "atrMonth11": "十一月", "atrMonth12": "十二月", "atrDevelopmentAssumption": "公司损失发展系数假设", "atrAcquisitionInfo": "假设信息", "atrWeight": "比重", "atrSelectBuss": "请选择业务类型", "atrSelectYear": "请选择年度", "atrSelectPortfolio": "请选择合同组", "atrBtnQuery": "查询", "atrCalcUwYear": "计量年度", "atrLicCalcDetail": "LIC明细数据", "atrLrcCalcDetail": "LRC明细数据", "atrRiskDistributionMode": "未到期计提模式", "atrMeasurementResult": "计量结果", "atrMeasurement": "计量", "atrMeasurementAll": "全量计量", "atrEvalConfirm": "版本确认不成功", "atrEvalPaaDetail": "计量明细数据", "atrEvalInCalculation": "计量计算处理中，请稍后查看结果", "atrEvalRepNoEarned": "报告期初未赚保费", "atrEvalCurNoEarned": "当期期末未赚保费", "atrEvalCexpIacfCf": "赔付与费用_摊销的保险获取现金流", "atrEvalEopIcPv": "投资成分期末现值", "atrCumulatedConfirmed": "累计已确认投资成分现值", "atrEvalCurAffirmPrePv": "本报告期累计已确认保费期末现值", "atrEvalContractRevenue": "保险合同收入", "atrEvalLrcNonLc": "未到期责任负债_非亏损部分", "atrEvalLrcLc": "未到期责任负债_亏损部分", "atrEvalIfieUnexpdInt": "IFIE_未到期_未到期计提利息", "atrEvalLossContractPl": "亏损合同损益", "atrEvalDocIacfFee": "跟单保险获取现金流", "atrEvalCurPremium": "当期收取保费", "atrEvalCurSurrenderChargeFee": "退保手续费", "atrEvalCurDocIacfFee": "当期支付的跟单保险获取现金流", "atrEvalCurNDocIacfFee": "当期支付的非跟单保险获取现金流", "atrEvalCurPolicyDay": "保单天数", "atrEvalCurIACFRate": "IACF支付时点", "atrEvalCurInvestment": "投成比例", "atrEvalCurDiscountRate": "折现率", "atrCurrencyCu": "本位币", "atrEvalDevelopItem": "假设项\\发展期", "atrEvalIcgItem": "输出项\\合同组", "atrEvalBeforeData": "计量前数据", "atrEvalCFDevelopPeriodData": "合同组预期现金流(发展期)", "atrEvalDuctData": "计量过程数据(当期)", "atrDuctData": "计量过程数据", "atrLicCurReportedOsPerformingCashFlow": "当期事故_已发生已报告未决赔款负债_履约现金流", "atrLicCurNoReportedOsPerformingCashFlow": "当期事故_已发生未报告未决赔款负债_履约现金流", "atrLicCurReportedRiskJudge": "当期事故_已发生已报告未决赔款负债_非金融风险调整", "atrLicCurNoReportedRiskJudge": "当期事故_已发生未报告未决赔款负债_非金融风险调整", "atrLicPreReportedOsPerformingCashFlow": "往期事故_已发生已报告未决赔款负债_履约现金流", "atrLicPreNoReportedOsPerformingCashFlow": "往期事故_已发生未报告未决赔款负债_履约现金流", "atrLicPreReportedRiskJudge": "往期事故_已发生已报告未决赔款负债_非金融风险调整", "atrLicPreNoReportedRiskJudge": "往期事故_已发生未报告未决赔款负债_非金融风险调整", "atrLicReportedOsPerformingCashFlow": "已发生已报告未决赔款负债_履约现金流", "atrLicNoReportedOsPerformingCashFlow": "已发生未报告未决赔款负债_履约现金流", "atrLicReportedRiskJudge": "已发生已报告未决赔款负债_非金融风险调整", "atrLicNoReportedClaimRiskJudge": "已发生未报告未决间接理赔费用现金流_非金融风险调整", "atrLicPayAndFeeReportedLibPmCF": "赔付与费用_已发生已报告未决赔款负债提转差_履约现金流", "atrLicPayAndFeeNoReportedLibPmCF": "赔付与费用_已发生未报告未决赔款负债提转差_履约现金流", "atrLicPayAndFeeReportedLibRJ": "赔付与费用_已发生已报告未决赔款负债提转差_非金融风险调整", "atrLicPayAndFeeNoReportedLibRJ": "赔付与费用_已发生未报告未决赔款负债提转差_非金融风险调整", "atrLicCurClaim": "当期已决", "atrLicCurAcidClaimFeePay": "当期事故_间接理赔费用_当期支付", "atrLicPreAcidClaimFeePay": "往期事故_间接理赔费用_当期支付", "atrLicIFIEReportedPmCF": "IFIE_已发生已报告_履约现金流", "atrLicIFIEReportedRJ": "IFIE_已发生已报告_非金融风险调整", "atrLicIFIENoReportPmCF": "IFIE_已发生未报告_履约现金流", "atrLicIFIENoReportRJ": "IFIE_已发生未报告_非金融风险调整", "atrLicPreAcidOsReportedPmCF": "往期事故期_未决计息_已发生已报告_履约现金流 ", "atrLicPreAcidOsReportedRJ": "往期事故期_未决计息_已发生已报告_非金融风险调整", "atrLicPreAcidOsNoReportedPmCF": "往期事故期_未决计息_已发生未报告_履约现金流", "atrLicPreAcidOsNoReportedRJ": "往期事故期_未决计息_已发生未报告_非金融风险调整", "atrLicIFIEOccPmCF": "IFIE_已发生_履约现金流", "atrLicIFIEOccRJ": "IFIE_已发生_非金融风险调整", "atrLicPrePayAdjPmCF": "往期赔付的调整_履约现金流", "atrLicPrePayAdjRJ": "往期赔付的调整_非金融风险调整", "atrLicCFPayFee": "现金流_支付的赔付与费用", "atrLicPreAcidOsPmCF": "往期事故期_未决计息_履约现金流 ", "atrLicPreAcidOsRJ": "往期事故期_未决计息_非金融风险调整", "atrLicCurAcidReportedOsCF": "当期事故_已发生已报告未决赔款现金流", "atrLicPreAcidReportedOsCF": "往期事故_已发生已报告未决赔款现金流", "atrLicCurAcidReportedClaimFeeCF": "当期事故_已发生已报告未决间接理赔费用现金流", "atrLicPreAcidReportedClaimFeeCF": "往期事故_已发生已报告未决间接理赔费用现金流", "atrLicCurAcidNoReportedOsCF": "当期事故_已发生未报告未决赔款现金流", "atrLicPreAcidNoReportedOsCF": "往期事故_已发生已报告未决赔款现金流", "atrLicCurAcidNoReportedClaimFeeCF": "当期事故_已发生未报告未决间接理赔费用现金流", "atrLicPreAcidNoReportedClaimFeeCF": "往期事故_已发生未报告未决间接理赔费用现金流", "atrLicOsPayDiscountFactor": "未决赔付折现因子", "atrPaaLic": "已发生理赔负债", "atrPaaLrc": "剩余保障负债", "atrValidVersionNo": "版本号不允许为空，请重新输入！", "atrValidVersionNoExists": "版本号已存在，请重新输入！", "atrValidCenterCode": "业务单位不允许为空，请重新输入！", "atrValidRiskClass": "产品风险大类不允许为空，请重新输入！", "atrValidYearMonth": "评估年月不允许为空，请重新输入！", "atrValidEvaluateApproach": "评估方法不允许为空，请重新输入！", "atrValidPortfolioNo": "合同组合不允许为空，请重新输入！", "atrValidIcgNo": "合同组不允许为空，请重新输入！", "atrValidUnitNo": "计量单元不允许为空，请重新输入！", "atrValidDevPeriod": "发展期次不允许为空，请重新输入！", "atrValidDataVal": "计量结果不允许为空，请重新输入！", "atrDataValFormat": "计量结果格式有误，请输入数字！", "atrDevPeriodFormat": "发展期格式有误，请输入数字！", "atrValidCenterMsg": "相关编码的业务单位不存在，请重新输入！", "atrValidRiskClassMsg": "相关编码的产品风险大类不存在，请重新输入！", "atrValidFactorMsg": "相关编码的计量项不存在，请重新输入！", "atrValidUniqueNotUnitMsg": "相同的业务单位、产品风险大类、评估年月、合同组和计量项已存在（不含计量单元和发展期次），请重新输入！", "atrValidUniqueUnitMsg": "相同的业务单位、产品风险大类、评估年月、合同组和计量项已存在（含计量单元和发展期次），请重新输入！", "atrValidNotDevPeriodMsg": "的计量项为非发展期的，但却存在发展期次，请重新输入！", "atrValidNumber14": "应为14位数字(如yyyyMMddHHmmss)，请重新输入！", "atrExcelVersionNo": "版本号", "atrExcelCenterCode": "业务单位", "atrExcelRiskClass": "产品风险大类", "atrEvaluationYearMonth": "评估期", "atrExcelEvaluateApproach": "评估方法", "atrExcelPortfolioNo": "合同组合", "atrExcelIcgNo": "合同组", "atrExcelUnitNo": "计量单元", "atrExcelDevPeriod": "发展期次", "atrExcelFactorCode": "计量项", "atrExcelDataVal": "计量结果", "atrValidUnitNoAndDevPeriod": "计量单元和发展期次应同时为空，或同时不为空，请重新输入！", "atrExcelFormatErrorMsg": "上传的文件格式不正确，请上传xls或者xlsx格式！", "atrPaidAmount": "已决赔款金额", "atrOsAmount": "未决赔款金额", "atrReserveBeforeRI": "再保前", "atrReserveBeforeRIPaid": "再保前(赔款)", "atrReserveBeforeRIExpense": "再保前(费用)", "atrReserveOutward": "再保分出", "atrReserveAfterRI": "再保后", "atrReserveOsTitle": "未决赔款准备金明细列表", "atrEvaluateDetailExcel": "计量结果明细数据", "atrEvaluateExportTitle": "计量结果导出信息", "atrModelFactorSource": "因子来源", "atrModelDimension": "因子维度", "atrModelName": "模型", "atrModelNodeType": "节点类型", "atrModelNodeRule": "节点规则", "atrModelDefInfo": "模型定义信息", "atrModelNodeInfo": "模型节点信息", "atrModelNodeRouteInfo": "模型节点路由信息", "atrModelRuleFactorInfo": "计量因子信息", "atrModelRuleInfo": "计量规则信息", "atrModelSourceRef": "源节点", "atrModelTargetRef": "目标节点", "atrModelRelationMsg": "此模型已被应用于规则，不能删除!", "atrReserveUprExtract": "未到期责任准备金提取", "atrReserveDacExtract": "佣金递延准备金提取", "atrReserveUpr": "未到期责任准备金", "atrReserveUprDetail": "未到期责任准备金明细", "atrReserveDac": "佣金递延准备金", "atrReserveDacDetail": "佣金递延准备金明细", "atrReserveConfirmDate": "所选版本计算日期不是月末", "atrReservePremium": "保费", "atrReserveUnearnedPremium": "未到期责任保费", "atrReserveCommission": "佣金", "atrReserveUnearnedCommission": "佣金递延", "atrDevelopFlag": "发展期标识", "modelDimension": "计量维度", "atrFactorType": "计量因子类型", "atrSystemCode": "适用平台", "atrModelFactorEntryRefInfo": "计量因子入账映射配置信息", "atrModelPlanInfo": "模型方案配置信息", "atrCalEach": "(各发展期)", "atrCalPrevious": "(上期)", "atrCalCurrent": "(当期)", "atrCalNext": "(下期)", "atrCalDevelop": "(未来发展期)", "atrSelectFactor": "选择计量因子", "atrAccrualDimension": "计提维度", "atrSelectEntryFactor": "选择入账计量因子", "atrModelRelationRuleMsg": "此模型已被应用于计量规则，不能删除!", "atrFactor": "计量因子", "atrIbnrType": "IBNR类型", "atrStatisZones": "提取区间数", "atrIbnrCalculate": "IBNR计算", "atrTotalCompensationFlowTriangle": "累计赔款流量三角", "atrReparationsCDFFlowTriangle": "赔款CDF流量三角", "atrBFMethodEstimateLossRate": "BF法估计损失率", "atrBFCalculatesIBNR": "BF计算的IBNR", "atrAccidentTime": "事故发生时间", "atrCalculateComplete": "计算完成！", "atrCalculateTips": "计算完成，结果请在第四步查看！", "atrPaymentDelayTime": "赔付延迟时间", "atrPaymentDelay": "赔付延迟时间(累计值)", "atrFC": "最终赔款", "atrTOS": "总未决赔款", "atrIBNR": "IBNR ", "atrPCTT": "预测赔款流量三角", "atrRisk": "产品风险", "atrEvalPVCFOfContractGroupData": "合同组预期现金流现值", "atrBussUnitNoList": "计量单元数据", "atrIcgDataList": "计量明细数据(合同组)", "atrUnitDataList": "计量明细数据", "ATR_MEAS_ERROR_001": "评估年月不在当前业务期间之前,请检查!", "ATR_MEAS_ERROR_002": "未配置会计期间本位币,请检查!", "ATR_MEAS_ERROR_003": "该业务模型未设置模型方案，请检查!", "ATR_MEAS_ERROR_004": "该业务模型未配置业务线，请检查!", "ATR_MEAS_ERROR_005": "该业务模型有业务线未配置假设数据，请检查!", "ATR_MEAS_ERROR_006": "假设数据为空或不符合录入规则，请检查!", "ATR_MEAS_ERROR_007": "该业务模型在此评估期未有确认的BECF数据，计量无法开始，请检查!", "atrCenterAndModelIsNull": "业务单位和计量模型、评估期搜索条件不能为空!", "atrBecfType": "现金流类型", "atrValuationDate": "评估日", "atrClaimCashFlowImport": "赔付现金流导入", "atrClaimRunNo": "编号", "atrClaimCfDate": "日期", "atrExpectedClaimCurrent": "当前事故年预期赔付", "atrExpectedClaimPast": "往年事故年预期赔付", "atrExpectedUlaeCurrent": "当前事故年间接理赔费用", "atrExpectedUlaePast": "往年事故年间接理赔费用", "atrPremiumCashFlowImport": "保费现金流导入", "atrLrcMaintenanceExpense": "维持费用", "atrLrcExpectedClaim": "预期理赔费用", "atrLrcCoverageUnit": "保额/限额", "atrLrcExpectedBrokerage": "经纪人费用", "atrLrcExpectedAdjustmentOfCommission": "预期手续费调整", "atrLrcExpectedClaimAdjustmentOfCommission": "预期理赔费用（含手续费调整）", "atrLrcCSMAmortizedRate": "CSM 摊销比例", "atrLrcAccumulatedEarnedRate": "累计已赚比例", "atrLrcCurEndRemainUnRate": "当期期末剩余未摊销比例", "atrLrcRptPerRemainUnRate": "截至报告期初剩余未摊销比例", "atrLrcLpCurEndRemainUnRate": "(上期)当期期末剩余未摊销比例", "atrLrclpRptPerRemainUnRate": "(上期)截至报告期初剩余未摊销比例", "atrLrcPremReceipt": "保费现金流", "atrLrcUEPProjection": "UEP Projection", "atrTitleAcqExp": "获取费用", "atrLrcBrokerageCf": "经纪人费用现金流", "atrLrcAdjCommCf": "调整手续费现金流", "atrTitleCfPut": "Lrc现金流", "atrTitlePolicyNoEndorsement": "业务号码(保单号-批改次数/账单号)", "atrConfQuotaDimension": "假设维度", "atrConfQuotaDimensionValue": "假设维度值", "atrConfDimensionTip": "紫色标记的假设值的颗粒度小于合同组合，用于在计量在对应维度上未找到假设值，能在合同组合取到假设值", "atrInsuredName": "被保险人", "atrUwYear": "承保年度", "atrDelinquencyRate": "拖欠比率", "atrDefaultRate": "不良贷款比例", "atrAlDateLoanOs": "借出贷款", "atrRunOffPattern": "Run-Off Pattern", "atrLiquiditySettings": "基础参数设置", "atrUltimateYear": "终极年限", "atrFinalLiquidityPeriod": "最终流动期限(LLP)", "atrUltimateRate": "终极利率", "atrUltimateLiquidityPremium": "终极流动性溢价", "atrValidNotPeriodMsg": "行的业务号不存在发展期数据，請重新輸入！", "atrValidHasMortMsg": "行已有相同配置，請重新輸入！", "atrValidSameMsg": "上传Excel有相同的保单号，請重新輸入！", "atrOsImportTitle": "未决信息导入", "atrUsedOsInfoIs": "是否使用导入 O/S 数据", "atrDOA": "事故发生月", "atrOS": "O/S", "atrCurClaimPaid": "当期实付赔款", "atrPaidMode": "赔付模式", "atrSelectValue": "选择值", "atrExpectedClaim": "预期赔付", "atrindirectClaimFeeRate": "间接理赔费用比例", "atrosIndirectClaimFeeRate": "O/S下间接理赔费用占比", "atrmanulAdjAddiClaim": "手动调整-附加理赔", "atribnrCur": "当前事故年IBNR", "atribnrPre": "往年事故年IBNR", "atrosCur": "当前事故年O/S", "atrosPre": "往年事故年O/S", "atrulaeCur": "当前事故年ULAE", "atrulaePre": "往年事故年ULAE", "atrAmortizedRate": "摊销比例", "atrExpClaimCur": "当前事故年当期赔付", "atrExpClaimPre": "往年事故年当期赔付", "atrDataKey": "接口数据ID", "atrLoaCode": "LOA编码", "atrCheckDateInDate": "审核通过日期", "atrAuditStartDate": "审核开始日期", "atrAuditEndDate": "审核结束日期", "atrAuditOrAccidentDate": "审核或者出险日期", "atrAccidentStartDate": "出险开始日期", "atrAccidentEndtDate": "出险结束日期", "atrEffectiveDateBom": "起保日期（月初）", "atrExpiryDateEom": "终保日期（月底）", "atrGrossPremium": "毛保费", "atrNetPremium": "净保费", "atrNonAcqExpense": "非跟单获取费用", "atrCoverageAmount": "保单理赔限额", "atrFacultativeIs": "是否临分", "atrPassedDates": "已承保天数", "atrPassedMonths": "已过月份", "atrRemainingMonths": "未到期月份", "atrRemainingPremTermPe": "剩余未交费期次", "atrRemainingMonthsFuture": "预期应收保费/未到期月份", "atrRemainingPremTermCb": "预期应收保费/未到期缴费期次", "atrPaymentQuarter": "预期应收保费/季度付款期间", "atrGepEdPremiumCoverageMonth": "已赚保费（每个覆盖月份）", "atrGepEdPremium": "已赚保费", "atrRemainingQuartersCb": "剩余季度", "atrExpectedPremiumReceivable": "预期应收保费", "atrEdPremiumGEP": "已赚保费（GEP）", "atrEdPremiumUEP": "未赚保费", "atrAdjustmentFee": "调整手续费", "atrExpectedBrokerage": "经纪人费用", "atrAccidentYearMonthDevelop": "事故年月\\发展期", "atrCfTypeClassTitle": "计量现金流版本信息", "atrCfTypeClass": "现金流类别", "atrLicCashFlowTitle": "预期赔付现金流明细", "atrLrcCashFlowTitle": "保费预期现金流信息明细", "atrLrcCfIcgFee": "合同组维度", "atrLrcCfPolicyFee": "保单维度", "atrExistConfirmSuccess": "存在已确认的选择，请重新选择！", "accDataPendingInfo": "业务数据信息", "atrExtractionTip": "提取处理中，请稍后查看结果", "atrInterestRateType": "利率类型", "atrLicCashFlowDetailTitle": "预期赔付现金流合同组合信息", "atrActionNo": "执行编号", "atrQuotaLoadPrePeriod": "加载上一期假设值", "atrQuotaLoadMsg": "上期评估期无可加载的假设值", "atrExtractInterval": "提取区间", "atrExtractIntervalNum": "提取区间数", "atrAdaptationEvaluationPeriod": "适配评估期", "atrWeightedParameterConfig": "加权参数配置", "atrUnderwritingDate": "核保日期", "atrSettledExpense": "已结算费用", "atrOSClaim": "O/S 赔款", "atrOSExpense": "O/S 费用", "atrOSYearMmonth": "O/S 统计年月", "atrAmount": "金额", "atrAssumpParaDetails": "假设值参数计算明细", "atrWeightedParameterInformation": "加权参数信息", "atrINNRCalculaConfig": "IBNR计算发展期配置", "atrINNRTimePeriod": "时间段", "atrINNRInterest": "利息", "atrReserveEarnedDetail": "已赚&释放准备金明细", "atrReleasedCommission": "释放佣金", "atrTransverseView": "横向查看", "atrDirectionView": "纵向查看", "atrConfQuotaClass": "假设值归类", "atrConfQuotaEffect": "假设值影响", "atrLicCashFlow": "预期赔付现金流", "atrLrcCashFlow": "保费预期现金流", "atrEvaluateReCalc": "重算", "atrEvaluateCfNoConfirm": "还未确认", "atrEvaluateCfSubmit": "确认撤销预期现金流已确认版本,重新计算？", "atrEvaluateToCfPage": "确认进入预期现金流页面？", "gLoadSuccessful": "加载成功", "gBtnToExcelQuantification": "导出计量结果", "atrWarnNoSearchData": "无计量结果数据", "atrUploadSelectFile": "选取文件", "atrDrawType": "提数方式", "atrControlExcelImportMsg": "导入文件条数限制在1至60000条之间！", "atrUlt": "已赔付进度（百分比）", "gAssumeBatchImportMsg": "批量导入会覆盖系统已有的假设值配置，请注意！", "gDmEndorseSeqNo": "批改序号", "gEvaluateDate": "当前评估日期", "gDmBorderDate": "合同确认日期", "atrExpectedLossRate": "预期损失率", "atrClaimExpenseRate": "间接理赔费用率", "atrRARate": "非金融风险调整率", "atrClassificationMark": "分类标志", "atrEvaluationMonth": "评估月份", "atrInsuranceTypeCode": "险种大类代码", "atrOrganizationCode": "机构代码", "atrAccidentMonth": "事故月份", "atrIBNREvalAmount": "IBNR评估金额", "atrEarnedWeight": "已赚权重", "atrDecidedWeight": "已决权重", "atrBackLookMonth": "回溯月份", "atrInstitutionalHierarchy": "机构层级", "atrCompensationMonth": "赔付月份", "atrRegulatoryCode": "监管大类代码", "atrInsuranceCode": "险类代码", "atrInTypeCode": "险种代码", "atrLegalRMB": "法定金额人民币", "atrAllocatioType": "分摊业务类型", "atrContractGroupNumber": "合同组号", "atrCONTRACTNO": "合约号", "atrDecidedPremium": "已决保费", "atrPendingPremium": "未决保费", "atrParticipate": "是否参与分摊", "atrPendingWeight": "未决权重", "atrSharingBase": "分摊基数", "atrAllocationRatio": "分摊比例", "atrIBNRResults": "IBNR结果", "atrIBNRSyncTable": "同步表", "atrMaintenanceCostRate": "维持费用率", "atrAdjFeeRate": "调整手续费用率", "atrProfitFeeRate": "纯益手续费用率", "atrBadDebt": "保费坏账调整", "atrInterestRateTemplate": "无风险曲线利率模板", "atrBecfFileName": "底层_LRC", "atrAioiBecfLicFileName": "底层_LIC", "atrAioiExpectedPremiumCF": "底层预期保费现金流", "atrDDBecfFileName": "直保", "atrTIBecfFileName": "合约分入", "atrFOBecfFileName": "临分分出", "atrTOBecfFileName": "合约分出", "atrTXBecfFileName": "超赔分出", "atrOutBecfFileName": "分出", "atrStatementPeriod": "账期", "atrIacf": "跟单获取费用", "atrAccumPaidIacf": "累计实付跟单获取费用", "atrAccumPaidPremium": "已收保费金额", "atrPreAccumEdPremium": "上期累计已赚保费", "atrPreAccumPaidPremium": "上期累计实收保费", "atrPreAccumNetFee": "上期累计实收净额结算手续费金额", "aioiInvCompAmount": "投资成分金额", "aioiRIPolicyNo": "分保单号码", "aioiRIEndorseSeqNo": "分批单号", "aioiBillNo": "账单号", "aioiInvCompRatio": "投资成分比例", "aioiEffectDate": "责任起期", "aioiExpiryDate": "责任止期", "aioiGrossPremium": "签单保费", "aioiEtLapse": "退保率", "aioiNetFee": "净额结算手续费金额", "aioiPreAccumNetFee": "上期累计实收净额结算手续费金额", "aioiLicCfTotal": "BF计算利率", "aioiLicCfAmountT1": "t-1估计_当期利率@t", "aioiLicCfAmountT2": "t-1估计_锁期利率@t", "aioiLicCfAmountT3": "t估计_当期利率@t", "aioiLicCfAmountT4": "t估计_锁期利率@t", "aioiLicCfAmountT5": "t-1估计_上期利率@t-1", "aioiLicCfAmountT6": "t-1估计_锁期利率@t-1", "aioiLicCfName": "预期赔付现金流", "aioiLicCfDvName": "预期赔付现金流折现", "atrIbnrImporRange": "导入时间范围", "atrIbnrProvisionRatio": "IBNR计提比例%", "atrIbnrAccidentQuarter": "事故季度", "atrIbnrCenter_code": "核算机构", "atrIbnrAmount": "IBNR金额", "atrIbnrRiAmount": "摊回IBNR金额", "atrIbnrCaseAmount": "Case金额", "atrIbnrRiCase": "摊回CASE金额", "atrIbnrRiDept": "再保业务部", "atrIbnrInfo": "IBNR信息", "atrIbnrXRiCaseInfo": "分出超赔合约CASE信息", "atrIbnrKindCode": "险别", "atrNotExitsIbnr": "Ibnr数据未导入或未确认，请检查!", "atrIbnrIsRun": "Ibnr有执行中的记录，请稍后再计算!", "atrPresentValueBeginCurRate": "t期初现值(Current Rate)", "atrPresentValueBeginLockRate": "t期初现值(Lock-in Rate)", "atrPresentValueEndCurRate": "t期末现值(Current Rate)", "atrPresentValueEndLockRate": "t期末现值(Lock-in Rate)", "atrPresentValueChangeCurRate": "期末现值变动(Current Rate)", "atrPresentValueChangeLockRate": "期末现值变动(Lock-in Rate)", "atrBtnEPIImport": "EPI导入", "atrDataType": "数据类型", "atrBtnEARNImport": "已赚保费导入", "atrDataImport": "数据导入", "atrInsuranceClass": "险类", "atrIcgNoName": "合同组名称", "atrContractNo": "合约号", "atrTreatyName": "合约名称", "atrImportDate": "导入日期", "atrContractEffectiveDate": "合约责任生效日", "atrContractExpiryDate": "合约责任截止日", "atrEPIImportDetail": "EPI导入数据详情", "atrEARNImportDetail": "已赚保费导入数据详情", "atrPaymentDetail": "缴费明细", "atrEarnedDetail": "已赚保费明细", "atrPaymentYearMonth": "缴费年月", "atrEarnedYearMonth": "已赚保费发生年月", "atrView": "查看", "preEdNetFee": "上期累计已赚签单净额结算手续费", "feeRate": "净额结算手续费率", "atrPreCumlEdIacf": "上期已赚跟单获取费用", "atrIaehcIn": "非跟单获取费用-对内", "atrPreCumlEdIaehcIn": "上期累计已赚非跟单获取费用-对内", "atrIaehcOut": "非跟单获取费用-对外", "atrPreCumlEdIaehcOut": "上期累计已赚非跟单获取费用-对外", "atrPreCumlPaidPremium": "上期累计实收保费", "atrPreCumlPaidNetFee": "上期累计实收净额结算手续费", "atrLapseRate": "预期退保率", "atrMtRate": "预期维持费用率", "atrClaimRate": "预期赔付率", "atrUlaeRate": "未到期间接理赔费用率", "atrCenter1": "一级机构", "atrCenter2": "二级机构", "atrCenter3": "三级机构", "atrCenter4": "四级机构", "atrPLResult": "合同组盈亏结果", "atrBecfItem": "现金流费用", "atrSubmitWaitingMsg": "提交数据量过大，后台正在运行，请稍后再查看结果", "gSureReOpen": "是否确定重开?", "accNoCompletedPeriod": "不存在已完成状态的会计期间去重开", "atrSectionoCode": "再保人分项", "atrReinsurerCode": "再保人编码", "atrRiEffectiveDate": "合约起期", "atrRiExpiryDate": "合约止期", "atrPrepaidFeeRate": "预付手续费率", "atrFloatingHandlingFeeCap": "浮动手续费率上限", "atrInvAmount": "投资成分", "atrDevNo": "发展期", "atrPaidEdPremium": "已写入已赚", "atrEdPremium": "已赚保费(含EPI)", "atrPaidNetFee": "已写入手续费", "atrEdNetFee": "已赚手续费(含EPI)"}