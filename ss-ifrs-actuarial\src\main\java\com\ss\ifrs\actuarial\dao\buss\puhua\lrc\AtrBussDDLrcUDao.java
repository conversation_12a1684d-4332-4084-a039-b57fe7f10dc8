/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2023-02-10 16:34:25
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao.buss.puhua.lrc;


import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussDDLrcIcuCalc;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo;
import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrBussBecfViewVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.mapping.ResultSetType;
import org.apache.ibatis.session.ResultHandler;

import java.util.List;
import java.util.Map;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-10 16:34:25<br/>
 * Description: LRC 计算结果主表(单维度，直保&临分分入) Dao类<br/>
 * Related Table Name: ATR_BUSS_DD_LRC_ICU_CALC<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrBussDDLrcUDao extends IDao<AtrBussDDLrcIcuCalc, Long> {

    Long countDateByVo(AtrDapDrawVo atrDapDrawVo);

    // 直接导出
    Page<List<Map<String, Object>>> findLrcDetailPage(AtrDapDrawVo atrDapDrawVo, Pageable pageParam);



    Long countLrcUDetail(AtrBussBecfViewVo atrBussBecfViewVo);

    // 现金流导出
    @Options(resultSetType = ResultSetType.FORWARD_ONLY, fetchSize = Integer.MIN_VALUE)
    void findLrcHandelDetailPage(ResultHandler<Map<String, Object>> resultHandler, AtrBussBecfViewVo atrBussBecfViewVo);
}