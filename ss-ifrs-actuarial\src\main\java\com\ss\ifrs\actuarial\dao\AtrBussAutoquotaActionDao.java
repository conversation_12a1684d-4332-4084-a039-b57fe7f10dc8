/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-08-28 15:11:17
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao;

import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussAutoquotaAction;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.*;
import com.ss.platform.pojo.bbs.vo.BbsConfLoaVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-08-28 15:11:17<br/>
 * Description: 假设值自动计算配置控制表 Dao类<br/>
 * Related Table Name: atr_buss_autoquota_action<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrBussAutoquotaActionDao extends IDao<AtrBussAutoquotaAction, Long> {

    void doAction(String actionNo);

    void approveAction(String actionNo);

    void deleteAction(Long id);

    Page<AtrBussAutoquotaActionVo> fuzzySearchPage(AtrBussAutoquotaActionVo po, Pageable pageParam);

    Page<AtrBussAutoquotaLoaVo> queryLoaPage(BbsConfLoaVo vo, Pageable pageParam);

    Page<AtrBussAutoquotaUnitResultVo> queryUnitPage(AtrBussAutoquotaGroupQueryVo po, Pageable pageParam);

    Page<AtrBussAutoquotaAccountResultVo> queryAccountPage(AtrBussAutoquotaGroupQueryVo po, Pageable pageParam);

    Page<AtrBussAutoquotaValueResultVo> queryQuotaValuePage(@Param("actionNo") String actionNo, Pageable pageParam);

    List<AtrBussAutoquotaWeightDetailVo> queryConfWeight();

    List<AtrBussAutoquotaWeightDetailVo> queryConfWeightByBuss(BbsConfLoaVo vo);
}