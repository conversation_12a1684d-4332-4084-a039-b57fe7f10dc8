/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-03-24 10:48:04
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao.conf;


import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfRiskRefVo;
import org.apache.ibatis.annotations.Mapper;

import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfRiskRefHis;
import com.ss.library.mybatis.interfaces.IDao;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2021-03-24 10:48:04<br/>
 * Description: LRC计量方式配置轨迹表 Dao类<br/>
 * Related Table Name: ATR_CONF_RISK_REFHIS<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入ssplatform-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrConfRiskRefHisDao extends IDao<AtrConfRiskRefHis, Long> {

    AtrConfRiskRefVo findByRiskRefHisId(Long riskRefId);
}