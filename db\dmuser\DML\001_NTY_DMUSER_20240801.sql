--再保批改序號有效性校驗
delete from dm_conf_checkrule dcc where rule_code  = 'REINS_OUTWARD_RI_ENDORSE_SEQ_NO_VALID_IS';

delete from dm_conf_checkrule dcc where rule_code  = 'REINS_OUTWARD_DETAIL_UNIQUE_CHECK';

insert into dmuser.dm_conf_checkrule (config_rule_id, rule_code, rule_e_name, rule_l_name, rule_c_name, rule_config, rule_version, valid_date, invalid_date, col_id, opt_type, match_value, rule_sql, valid_is, create_time, creator_id, update_time, updator_id, rule_direction, rule_type_code, original_is, biz_type_id, remark, check_type, display_no, serial_no, checked_msg, audit_state, checked_time, checked_id, apply_model, rule_type)
values  (nextval('dm_seq_conf_checkrule'), 'REINS_OUTWARD_DETAIL_UNIQUE_CHECK', 'DM uniqueness verification', 'DM唯一性校驗', 'DM唯一性校验', null, 1.00, '2024-07-31 01:41:44.859516', '9999-12-31 00:00:00.000000', null, '0', null, 'select id
from odsuser.ods_reins_outward_detail
where id in (select a.id
             from odsuser.ods_reins_outward_detail a
                      inner join dm_reins_treaty b
                                 on a.task_code = ''#TASK_CODE#'' and a.treaty_no = b.treaty_no
                                     and a.entity_code = b.entity_code and b.treaty_type_code in (''91'', ''92'')
                      inner join dm_reins_outward_detail c
                                 on a.ri_policy_no = c.ri_policy_no and left(a.ri_endorse_seq_no,3) = left(c.ri_endorse_seq_no,3)
                                     and a.endorse_seq_no = c.endorse_seq_no and
                                    a.expenses_type_code = c.expenses_type_code)', '1', '2024-07-31 01:41:44.859516', null, '2024-07-31 01:41:44.859516', 1, '0', '1', '1', (select biz_type_id from dm_conf_table where upper(biz_code) = 'REINS_OUTWARD_DETAIL'), null, '2', null, null, null, null, null, null, null, '2')