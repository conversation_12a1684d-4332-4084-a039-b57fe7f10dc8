package com.ss.ifrs.actuarial.service.impl;

import com.google.common.base.Joiner;
import com.ss.ifrs.actuarial.dao.AtrBussAutoquotaActionDao;
import com.ss.ifrs.actuarial.dao.AtrBussAutoquotaWeightDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfAutoquotaWeightDao;
import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussAutoquotaAction;
import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussAutoquotaWeight;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaActionVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaApproveVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaDrawVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaWeightDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaWeightResultVo;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfAutoquotaWeight;
import com.ss.ifrs.actuarial.service.AtrBussAutoquotaService;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.platform.util.CommonUtil;
import com.ss.library.utils.LazyValidator;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AtrBussAutoquotaServiceImpl implements AtrBussAutoquotaService {

    @Resource
    private AtrBussAutoquotaActionDao atrBussAutoquotaActionDao;

    @Resource
    private AtrBussAutoquotaWeightDao atrBussAutoquotaWeightDao;

    @Resource
    private AtrConfAutoquotaWeightDao atrConfAutoquotaWeightDao;

    @Resource
    private AtrLogActionErrorService atrLogActionErrorService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveAction(AtrBussAutoquotaDrawVo vo, Long userId) {
        LazyValidator.validate(v -> {
            v.notEmpty(vo.getEntityId(), "entity id cannot be empty");
            v.notEmpty(vo.getDeadline(), "deadline cannot be empty");
            v.notEmpty(vo.getDrawInterval(), "draw interval cannot be empty");
            v.notEmpty(vo.getDrawIntervalQuantity(), "draw interval quantity cannot be empty");
            v.notEmpty(vo.getAppliQuotaCodes(), "appli quota codes cannot be empty");
        });

        String actionNo = CommonUtil.nextActionNo();

        AtrBussAutoquotaAction po = new AtrBussAutoquotaAction();
        BeanUtils.copyProperties(vo, po);
        po.setActionNo(actionNo);
        po.setOperId(userId == null ? 1L : userId);
        po.setOperTime(new Date());
        po.setDrawState("1");
        po.setDrawStartTime(new Date());
        po.setApprovalState("0");
        po.setAppliQuotaCodes(Joiner.on(",").skipNulls().join(vo.getAppliQuotaCodes()));
        atrBussAutoquotaActionDao.save(po);

        if (CollectionUtils.isNotEmpty(vo.getWeights())) {
            for (AtrBussAutoquotaWeightDetailVo weightDetailVo : vo.getWeights()) {
                List<BigDecimal> weightValues = weightDetailVo.getWeightValues();
                List<AtrBussAutoquotaWeight> weights = new ArrayList<>();
                for (int i = 0; i < weightValues.size(); i++) {
                    AtrBussAutoquotaWeight weight = new AtrBussAutoquotaWeight();
                    weights.add(weight);
                    weight.setActionNo(actionNo);
                    weight.setBusinessSourceCode(weightDetailVo.getBusinessSourceCode());
                    weight.setLoaCode(weightDetailVo.getLoaCode());
                    weight.setOffsetYears((short) (-1 * i));
                    weight.setOperId(userId);
                    weight.setOperTime(new Date());
                    weight.setWeightValue(weightValues.get(i));
                }

                if (!weights.isEmpty()) {
                    atrBussAutoquotaWeightDao.saveList(weights);
                }
            }
        }

        return actionNo;
    }

    @Override
    public BaseResponse<?> doAction(String actionNo) {
        new Thread(() -> {
            try {
                atrBussAutoquotaActionDao.doAction(actionNo);
            } catch (Exception e) {
                handleActionError(actionNo, e);
                log.error("do action error", e);
            }
        }).start();
        String state = checkRunState(actionNo);
        if ("1".equals(state)) {
            return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "It will take some time to process");
        } else if ("2".equals(state)) {
            return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "The processing was successful");
        } else if ("3".equals(state)) {
            return new BaseResponse<>(ResCodeConstant.ResCode.OTHER_ERROR, "Processing failed");
        } else if ("4".equals(state)) {
            return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "Not processed");
        } else {
            return new BaseResponse<>(ResCodeConstant.ResCode.OTHER_ERROR, "Unknown status");
        }
    }

    /**
     * @return 1-执行中、2-成功、3-失败、4-不执行；
     */
    private String checkRunState(String actionNo) {
        AtrBussAutoquotaAction po = new AtrBussAutoquotaAction();
        po.setActionNo(actionNo);
        for (int i = 0; i < 5; i++) {
            try {
                Thread.sleep(1000);
            } catch (Exception ignore) {
            }
            List<AtrBussAutoquotaAction> pos = atrBussAutoquotaActionDao.findList(po);
            if (pos.size() > 1) {
                return "3";
            }
            if (pos.size() == 1) {
                String drawState = pos.get(0).getDrawState();
                if (!"1".equals(drawState)) {
                    return drawState;
                }
            }
        }
        return "1";
    }

    private void handleActionError(String actionNo, Exception e) {
        AtrBussAutoquotaAction po = new AtrBussAutoquotaAction();
        po.setActionNo(actionNo);
        List<AtrBussAutoquotaAction> pos = atrBussAutoquotaActionDao.findList(po);
        if (pos.size() == 1) {
            po = pos.get(0);
            po.setDrawState("3");
            po.setDrawEndTime(new Date());
            atrBussAutoquotaActionDao.updateById(po);
            atrLogActionErrorService.addLog("auto_quota_do_action", actionNo, e);
        }
    }

    @Override
    public void deleteAction(long id) {
        AtrBussAutoquotaAction action = atrBussAutoquotaActionDao.findById(id);

        if (action == null) {
            throw new RuntimeException("action " + id + "不存在");
        }
        if ("1".equals(action.getApprovalState())) {
            throw new RuntimeException("action " + id + "已审核通过， 不可删除");
        }

        atrBussAutoquotaActionDao.deleteAction(id);
    }

    @Override
    public void approveAction(AtrBussAutoquotaApproveVo vo, Long userId) {
        String actionNo = vo.getActionNo();
        List<String> yearmonths = vo.getAppliEvYearmonths();

        if (yearmonths == null || yearmonths.isEmpty()) {
            throw new RuntimeException("The evaluation period for adaptation cannot be empty");
        }

        AtrBussAutoquotaAction po = getUnApprovedAction(actionNo);
        po.setAppliEvYearmonths(Joiner.on(",").skipNulls().join(yearmonths));
        atrBussAutoquotaActionDao.updateById(po);

        atrBussAutoquotaActionDao.approveAction(actionNo);

        po.setApprovalState("1");
        po.setApprovalId(userId);
        atrBussAutoquotaActionDao.updateById(po);
    }

    @NotNull
    private AtrBussAutoquotaAction getUnApprovedAction(String actionNo) {
        AtrBussAutoquotaAction po = new AtrBussAutoquotaAction();
        po.setActionNo(actionNo);
        List<AtrBussAutoquotaAction> pos = atrBussAutoquotaActionDao.findList(po);

        if (pos.isEmpty()) {
            throw new RuntimeException("action " + actionNo + " does not exist");
        }

        if (pos.size() > 1) {
            throw new RuntimeException("There are multiple action = " + actionNo + " records, please check");
        }

        if ("1".equals(po.getApprovalState())) {
            throw new RuntimeException("action " + actionNo + " has been approved");
        }

        return pos.get(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveConfWeight(List<AtrBussAutoquotaWeightDetailVo> vos, Long userId) {
        atrConfAutoquotaWeightDao.deleteAll();
        List<AtrConfAutoquotaWeight> weights = new ArrayList<>();
        for (AtrBussAutoquotaWeightDetailVo vo : vos) {
            List<BigDecimal> weightValues = vo.getWeightValues();
            if (CollectionUtils.isNotEmpty(weightValues)) {
                for (int i = 0; i < weightValues.size(); i++) {
                    AtrConfAutoquotaWeight weight = new AtrConfAutoquotaWeight();
                    weights.add(weight);
                    weight.setLoaCode(vo.getLoaCode());
                    weight.setOperId(userId);
                    weight.setOperTime(new Date());
                    weight.setOffsetYears((short) (i * -1));
                    weight.setWeightValue(weightValues.get(i));
                }
            }
        }
        if (!weights.isEmpty()) {
            atrConfAutoquotaWeightDao.saveList(weights);
        }
    }

    @Override
    public Page<AtrBussAutoquotaActionVo> queryPage(AtrBussAutoquotaActionVo vo, Pageable pageParam) {
        return atrBussAutoquotaActionDao.fuzzySearchPage(vo, pageParam);
    }

    @Override
    public AtrBussAutoquotaWeightResultVo queryBussWeight(String actionNo) {
        AtrBussAutoquotaAction action = new AtrBussAutoquotaAction();
        action.setActionNo(actionNo);
        action = atrBussAutoquotaActionDao.findList(action).get(0);

        AtrBussAutoquotaWeightResultVo vo = new AtrBussAutoquotaWeightResultVo();
        BeanUtils.copyProperties(action, vo);

        List<BigDecimal> defaultWeightValues = new ArrayList<>();
        for (int i = 0; i < action.getDrawIntervalQuantity(); i++) {
            defaultWeightValues.add(null);
        }

        List<AtrBussAutoquotaWeightDetailVo> detailVos = atrBussAutoquotaWeightDao.queryForExitedWeight(actionNo);
        for (AtrBussAutoquotaWeightDetailVo detailVo : detailVos) {
            String valuesAgg = detailVo.getWeightValuesAgg();
            if (StringUtils.isBlank(valuesAgg)) {
                detailVo.setWeightValues(defaultWeightValues);
            } else {
                List<BigDecimal> weightValues = Arrays.stream(valuesAgg.split(",", -1))
                        .map(t -> StringUtils.isBlank(t) ? null : new BigDecimal(t))
                        .collect(Collectors.toList());
                detailVo.setWeightValues(weightValues);
            }
        }

        vo.setDetails(detailVos);

        return vo;
    }

}
