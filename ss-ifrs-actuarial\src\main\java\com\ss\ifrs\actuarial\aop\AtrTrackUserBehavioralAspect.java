package com.ss.ifrs.actuarial.aop;

import com.ss.ifrs.actuarial.feign.BmsTrackUserBehavioralFeignClient;
import com.ss.library.utils.HttpRequestUtil;
import com.ss.platform.core.annotation.TrackUserBehavioral;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.SystemConstant;
import com.ss.platform.pojo.com.vo.TrackUserBehavioralEventVo;
import com.ss.platform.pojo.com.vo.TrackUserBehavioralVo;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Title: SystemControllerLog Description: 切点类
 */
@Aspect
@Component
public class AtrTrackUserBehavioralAspect extends BaseApi {

	@Resource
	private BmsTrackUserBehavioralFeignClient logUserBehavioralFeignClient;

	// Controller层切点
	@Pointcut("@annotation(com.ss.platform.core.annotation.TrackUserBehavioral)")
	public void controllerAspect() {
	}

	/**
	 * @Description 前置通知 用于拦截Controller层记录用户的操作
	 */

	@Before("controllerAspect()")
	@Transactional
	public void after(JoinPoint joinPoint) {
		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
				.getRequest();
		String userCode = ObjectUtils.isEmpty(this.loginUserCode(request)) ? "admin" : this.loginUserCode(request);
		Long userId = ObjectUtils.isEmpty(this.loginUserId(request)) ? 1L : this.loginUserId(request);

		String ip = HttpRequestUtil.getIpAddress(request);
		TrackUserBehavioralVo addVo = new TrackUserBehavioralVo();// 操作日志表addVo

		try {
			// *========控制台输出=========*//
			String apiUrl = request.getRequestURI();
			TrackUserBehavioralEventVo confBehavioralEvent = new TrackUserBehavioralEventVo();
			confBehavioralEvent.setAppCode(SystemConstant.AtrIdentity.APP_CODE);
			// 筛选 url是否是传入 id参数
			if (hasDigit(apiUrl)) {
				apiUrl = StringUtils.substringBeforeLast(apiUrl, "/");
				if (hasDigit(apiUrl)) {
					apiUrl = StringUtils.substringBeforeLast(apiUrl, "/");
				}
			}
			confBehavioralEvent.setApiUrl("/" + apiUrl.split("/")[1]);
			List<TrackUserBehavioralEventVo> list = logUserBehavioralFeignClient.searchLogConfigForExtern(confBehavioralEvent);
			if (list.size() > 0) {
				// 查看日志配置，是否为日志
				for (TrackUserBehavioralEventVo item : list) {
					if (item.getIsLog().equals(CommonConstant.LogStatus.UNARCHIVED)) {// isLog值等于0 （1：是 0：否）
						return;
					}
				}
				// *========数据库日志=========*//
				addVo.setOperTime(new Date());
				addVo.setOperatorName(userCode);
				addVo.setOperatorId(userId);
				addVo.setReqUrl(request.getRequestURI());
				addVo.setLogType(CommonConstant.LogType.DATABASE);
				addVo.setClientIp(ip);
				addVo.setAppName(SystemConstant.AtrIdentity.APP_CODE);
				addVo.setOperDesc(getControllerMethodDescription(joinPoint));
				addVo.setOperStatus(CommonConstant.OperStatus.SUCCESS);// 成功
				addVo.setLogStatus(CommonConstant.LogStatus.UNARCHIVED);// 已归档历史表
				// 保存到操作日志表
				logUserBehavioralFeignClient.saveLogForExtern(addVo);
			}

		} catch (Exception e) {
			// 记录本地异常日志
			logger.error("异常信息：{}", e.getMessage());
			// 新增操作日志为系统异常
			addVo.setOperStatus(CommonConstant.OperStatus.EXCEPTION);// 系统异常
			addVo.setExceptionMsg(e.getMessage().substring(0, Math.min(e.getMessage().length(), 500)));
			logUserBehavioralFeignClient.saveLogForExtern(addVo);

		}
	}

	/**
	 * @Description 获取注解中对方法的描述信息 用于Controller层注解
	 */
	public static String getControllerMethodDescription(JoinPoint joinPoint) throws Exception {
		String targetName = joinPoint.getTarget().getClass().getName();
		String methodName = joinPoint.getSignature().getName();// 目标方法名
		Object[] arguments = joinPoint.getArgs();
		Class targetClass = Class.forName(targetName);
		Method[] methods = targetClass.getMethods();
		String description = "";
		for (Method method : methods) {
			if (method.getName().equals(methodName)) {
				Class[] clazzs = method.getParameterTypes();
				if (clazzs.length == arguments.length) {
					description = method.getAnnotation(TrackUserBehavioral.class).description();
					break;
				}
			}
		}
		return description;
	}

	// 判断一个字符串是否含有数字
	public boolean hasDigit(String content) {
		boolean flag = false;
		Pattern p = Pattern.compile(".*\\d+.*");
		Matcher m = p.matcher(content);
		if (m.matches()) {
			flag = true;
		}
		return flag;
	}
}
