package com.ss.ifrs.actuarial.dao;

import com.ss.ifrs.actuarial.pojo.ecf.vo.AtrSD7;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.AtrDapTreaty;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.dd.AtrDapLrcDdPaid;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.fo.AtrBussFoLrcIcgPremium;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.fo.AtrBussLrcFoIcu;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.fo.AtrDapLrcFoPaid;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.fo.AtrDuctLrcFoIcuPre;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface AtrBussLrcFoDao {

    long getIcuMaxMainId();

    long getIcgMaxMainId();

    void truncateBaseData();

    void partitionBaseData(Map<String, Object> commonParamMap);

    List<AtrBussLrcFoIcu> getPartBaseVos(Map<String, Object> paramMap);
    List<AtrDapLrcFoPaid> findDapPaid(Map<?, ?> paramMap);

    List<AtrDuctLrcFoIcuPre> findPreIcu(Map<?, ?> paramMap);

    List<AtrBussFoLrcIcgPremium> listPreIcgPremium(Map<String, Object> paramMap);

    List<AtrDapTreaty> listDapTreaty();

    /**
     * 清空过渡期已赚保费临时表
     */
    void truncateTransitionTempTable();
    
    /**
     * 向临时表中插入需要计算的保单数据
     * 
     * @param paramMap 参数Map
     */
    void insertTransitionTempData(Map<String, Object> paramMap);
    
    /**
     * 获取指定分区的过渡期已赚保费计算基础数据
     * 
     * @param paramMap 参数Map
     * @return 保单数据列表
     */
    List<AtrBussLrcFoIcu> getTransitionPartBaseVos(Map<String, Object> paramMap);
    
    /**
     * 从临时表批量删除已计算完成的数据
     * 
     * @param paramMap 参数Map
     */
    void deleteProcessedTransitionData(Map<String, Object> paramMap);

    /**
     * 创建atr_dap_fo_paid表的备份表
     */
    void createPaidBackupTable();

    /**
     * 从atr_dap_fo_paid表删除符合条件的数据
     * 
     * @return 删除的记录数
     */
    void deletePaidData();

    /**
     * 判断过渡期已赚保费表中是否存在指定机构的数据
     * 
     * @param entityId 机构ID
     * @return 存在返回true，不存在返回false
     */
    boolean hasTransitionData(Long entityId);

}