package com.ss.ifrs.actuarial.pojo.ecf.vo.lic;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter @Setter
public class AtrDuctLicLastCurCf {

    private String policyNo;
    private String kindCode;
    private String riskClassCode;
    private String treatyNo;
    private String accYearMonth;
    private String cfType;
    private Integer devNo;
    private BigDecimal amount;

}
