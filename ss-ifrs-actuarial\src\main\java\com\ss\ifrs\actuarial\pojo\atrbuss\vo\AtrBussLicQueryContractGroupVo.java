/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-03-09 10:18:30
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName: AtrBussLicQueryResVo
 * @Description: Lic计量查询数据返回对象-合同组数据
 * @Author: yinxh.
 * @CreateDate: 2021/3/10 18:32
 * @Version: 1.0
 */
public class AtrBussLicQueryContractGroupVo implements Serializable {
    
    /**
     * 合同组号
     */
    private String icgNo;
    
    /**
     * 发展年月list
     */
    private List<AtrBussLicQueryDevYearMonthVo> devYearMonthList;
    
    /**
         * 辅助计算列
    */
	private BigDecimal lossRate;
	private BigDecimal premium;
	private BigDecimal accidentAll; 
	private BigDecimal ibrm;
    
    private static final long serialVersionUID = 1L;
    
    public String getIcgNo() {
        return icgNo;
    }
    
    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }
    
    public List<AtrBussLicQueryDevYearMonthVo> getDevYearMonthList() {
        return devYearMonthList;
    }
    
    public void setDevYearMonthList(List<AtrBussLicQueryDevYearMonthVo> devYearMonthList) {
        this.devYearMonthList = devYearMonthList;
    }

	public BigDecimal getLossRate() {
		return lossRate;
	}

	public void setLossRate(BigDecimal lossRate) {
		this.lossRate = lossRate;
	}

	public BigDecimal getPremium() {
		return premium;
	}

	public void setPremium(BigDecimal premium) {
		this.premium = premium;
	}

	public BigDecimal getAccidentAll() {
		return accidentAll;
	}

	public void setAccidentAll(BigDecimal accidentAll) {
		this.accidentAll = accidentAll;
	}

	public BigDecimal getIbrm() {
		return ibrm;
	}

	public void setIbrm(BigDecimal ibrm) {
		this.ibrm = ibrm;
	}
    
    
}