/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-02-02 18:31:30
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao.dap;


import com.ss.ifrs.actuarial.pojo.atrdap.po.AtrDapTIIcpClaimAccidentAmount;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-02 18:31:30<br/>
 * Description: 事故费用接口表(合同组合维度, 合约分入) Dao类<br/>
 * Related Table Name: ATR_DAP_TI_ICP_CLAIM_ACCIDENT_AMOUNT<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrDapTIIcpClaimAccidentAmountDao extends IDao<AtrDapTIIcpClaimAccidentAmount, Long> {
}