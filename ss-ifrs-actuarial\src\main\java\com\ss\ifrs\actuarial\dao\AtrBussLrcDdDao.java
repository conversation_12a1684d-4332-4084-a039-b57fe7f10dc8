package com.ss.ifrs.actuarial.dao;

import com.ss.ifrs.actuarial.pojo.ecf.vo.AtrSD7;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.dd.*;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface AtrBussLrcDdDao {

    long getIcuMaxMainId();

    long getIcgMaxMainId();

    void truncateMaxExpiryDateTable();

    void truncatePrePaidPremiumTable();

    void insertMaxExpiryDateTable(Map<?, ?> paramMap);

    void insertPrePaidPremiumTable(Map<?, ?> paramMap);

    void truncateBaseData();

    void partitionBaseData(Map<?, ?> paramMap);

    List<AtrDapLrcDdPaymentPlan> findPaymentPlan(Map<?, ?> paramMap);

    List<AtrBussLrcDdIcu> getPartBaseVos(Map<?, ?> paramMap);

    List<AtrDuctLrcDdIcuPre> findPreIcu(Map<?, ?> paramMap);

    List<AtrDapLrcDdPaid> findDapPaid(Map<?, ?> paramMap);

    List<AtrSD7> findExpAlloc(Map<?, ?> paramMap);
    List<AtrSD7> finPreExpAlloc(Map<?, ?> paramMap);

    List<AtrBussDdLrcIcgPremium> listPreIcgPremium(Map<?, ?> paramMap);

    /**
     * 向临时表中插入需要计算的保单数据（已经过滤掉终保日期小于评估期第一天的保单）
     * 
     * @param paramMap 参数Map
     */
    void insertTransitionTempData(Map<String, Object> paramMap);
    
    /**
     * 获取指定分区的保单数据
     * 
     * @param paramMap 参数Map
     * @return 保单数据列表
     */
    List<AtrBussLrcDdIcu> getTransitionPartBaseVos(Map<String, Object> paramMap);

    
    /**
     * 清空转换已赚保费临时表
     */
    void truncateTransitionTempTable();

    /**
     * 清空需要删除保单临时表
     */
    void truncateTempPoliciesTable();
    
    /**
     * 根据条件找出需要清理的保单
     * 
     * @param paramMap 参数Map
     */
    void identifyPoliciesToClean(Map<String, Object> paramMap);
    
    /**
     * 创建atr_dap_dd_unit表的备份表
     */
    void createUnitBackupTable();
    
    /**
     * 创建atr_dap_dd_payment_plan表的备份表
     */
    void createPaymentPlanBackupTable();

    
    /**
     * 创建atr_dap_dd_paid表的备份表
     */
    void createPaidBackupTable();

    
    /**
     * 创建atr_dap_dd_exp_alloc表的备份表
     */
    void createExpAllocBackupTable();
    
    /**
     * 从atr_dap_dd_unit表删除符合条件的数据
     * 
     * @return 删除的记录数
     */
    int deleteUnitData();
    
    /**
     * 从atr_dap_dd_payment_plan表删除符合条件的数据
     * 
     * @return 删除的记录数
     */
    int deletePaymentPlanData();
    
    /**
     * 从atr_dap_dd_paid表删除符合条件的数据
     * 
     * @return 删除的记录数
     */
    int deletePaidData();
    
    /**
     * 从atr_dap_dd_exp_alloc表删除符合条件的数据
     * 
     * @return 删除的记录数
     */
    int deleteExpAllocData();

    /**
     * 判断过渡期已赚保费表中是否存在指定机构的数据
     * 
     * @param entityId 机构ID
     * @return 存在返回true，不存在返回false
     */
    boolean hasTransitionData(Long entityId);

}
