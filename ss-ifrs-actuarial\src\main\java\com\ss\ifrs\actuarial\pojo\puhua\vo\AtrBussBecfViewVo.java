/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-02-08 17:38:36
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.puhua.vo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-08 17:38:36<br/>
 * Description: LRC 预期现金流操作表<br/>
 * Table Name: ATR_BUSS_LRC_ACTION<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
public class AtrBussBecfViewVo implements Serializable {
    /**
     * Database column: ATR_BUSS_LRC_ACTION.ID
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    private String actionNo;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.CENTER_ID
     * Database remarks: 业务单位ID
     */
    @ApiModelProperty(value = "业务单位ID", required = true)
    private Long entityId;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.YEAR_MONTH
     * Database remarks: 业务年月|评估期
     */
    @ApiModelProperty(value = "业务年月|评估期", required = true)
    private String yearMonth;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.BUSINESS_TYPE
     * Database remarks: 业务类型|DD-直保&临分分入；FO-临分分出；TI-合约分入；TO-合约分出
     */
    @ApiModelProperty(value = "业务类型|DD-直保&临分分入；FO-临分分出；TI-合约分入；TO-合约分出", required = true)
    private String businessSourceCode;

    private String becfType;

    /**
     * Database column: atr_conf_model_def.creator_id
     * Database remarks: Creator_Id|创建人
     */
    @ApiModelProperty(value = "Creator_Id|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: atr_conf_model_def.create_time
     * Database remarks: Create_Time|创建时间
     */
    @ApiModelProperty(value = "Create_Time|创建时间", required = false)
    private Date createTime;

    private String entityCode;
    private String entityCName;
    private String entityLName;
    private String entityEName;


    private String language;

    private String lrcTemplateFileName;

    private String licTemplateFileName;

    private String logFileName;

    private String targetRouter;

    /**
     * 自动任务传输字段：重试次数
     */
    private Long retryOrder;

    private String zipName;
    /**
     * 自动任务传输字段：任务模式：A-自动
     */
    private String taskMode;

    private String loaCode;
    private String portfolioNo;
    private String icgNo;
    private String riskClassCode;


    private String yearMonthStart;
    private String yearMonthEnd;

    private String feeType;

    List<Integer> devNoList;

    private List<Long> lrcBecfIds;

    private List<Long> licBecfIds;

    private List<String> licBecfCodes;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getEntityCode() {
        return entityCode;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public String getEntityCName() {
        return entityCName;
    }

    public void setEntityCName(String entityCName) {
        this.entityCName = entityCName;
    }

    public String getEntityLName() {
        return entityLName;
    }

    public void setEntityLName(String entityLName) {
        this.entityLName = entityLName;
    }

    public String getEntityEName() {
        return entityEName;
    }

    public void setEntityEName(String entityEName) {
        this.entityEName = entityEName;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getLrcTemplateFileName() {
        return lrcTemplateFileName;
    }

    public void setLrcTemplateFileName(String lrcTemplateFileName) {
        this.lrcTemplateFileName = lrcTemplateFileName;
    }

    public String getLicTemplateFileName() {
        return licTemplateFileName;
    }

    public void setLicTemplateFileName(String licTemplateFileName) {
        this.licTemplateFileName = licTemplateFileName;
    }

    public String getLogFileName() {
        return logFileName;
    }

    public void setLogFileName(String logFileName) {
        this.logFileName = logFileName;
    }

    public String getTargetRouter() {
        return targetRouter;
    }

    public void setTargetRouter(String targetRouter) {
        this.targetRouter = targetRouter;
    }

    public Long getRetryOrder() {
        return retryOrder;
    }

    public void setRetryOrder(Long retryOrder) {
        this.retryOrder = retryOrder;
    }

    public String getZipName() {
        return zipName;
    }

    public void setZipName(String zipName) {
        this.zipName = zipName;
    }

    public String getTaskMode() {
        return taskMode;
    }

    public void setTaskMode(String taskMode) {
        this.taskMode = taskMode;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public String getYearMonthStart() {
        return yearMonthStart;
    }

    public void setYearMonthStart(String yearMonthStart) {
        this.yearMonthStart = yearMonthStart;
    }

    public String getYearMonthEnd() {
        return yearMonthEnd;
    }

    public void setYearMonthEnd(String yearMonthEnd) {
        this.yearMonthEnd = yearMonthEnd;
    }

    public List<Long> getLrcBecfIds() {
        return lrcBecfIds;
    }

    public void setLrcBecfIds(List<Long> lrcBecfIds) {
        this.lrcBecfIds = lrcBecfIds;
    }

    public List<Long> getLicBecfIds() {
        return licBecfIds;
    }

    public void setLicBecfIds(List<Long> licBecfIds) {
        this.licBecfIds = licBecfIds;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public List<Integer> getDevNoList() {
        return devNoList;
    }

    public void setDevNoList(List<Integer> devNoList) {
        this.devNoList = devNoList;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public String getBecfType() {
        return becfType;
    }

    public void setBecfType(String becfType) {
        this.becfType = becfType;
    }

    public List<String> getLicBecfCodes() {
        return licBecfCodes;
    }

    public void setLicBecfCodes(List<String> licBecfCodes) {
        this.licBecfCodes = licBecfCodes;
    }
}