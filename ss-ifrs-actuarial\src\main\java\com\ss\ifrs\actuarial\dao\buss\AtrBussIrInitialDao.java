/**
 * 
 * This file was generated by Taiping MyBatis Generator(v1.2.12).
 * Date: 2024-08-21 18:15:20
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao.buss;


import com.ss.ifrs.actuarial.pojo.atrbuss.ir.po.AtrBussIrInitial;
import com.ss.ifrs.actuarial.pojo.atrbuss.ir.vo.AtrBussIrDevVo;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestRate;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * This code was generated by Taiping MyBatis Generator(v1.2.12).
 * Create Date: 2024-08-21 18:15:20<br/>
 * Description: 当期初始利率主表 Dao类<br/>
 * Related Table Name: ATR_BUSS_IR_INITIAL<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrBussIrInitialDao extends IDao<AtrBussIrInitial, Long> {
    void updateByMainId(AtrConfInterestRate po);

    List<AtrBussIrDevVo> findBussIrInitialDevList(AtrBussIrInitial atrBussIrInitial);
 }