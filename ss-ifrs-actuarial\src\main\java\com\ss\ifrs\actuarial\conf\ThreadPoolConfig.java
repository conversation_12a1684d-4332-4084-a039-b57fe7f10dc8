package com.ss.ifrs.actuarial.conf;

import com.ss.platform.core.constant.SystemConstant;
import com.ss.platform.core.constant.ThreadConstant;
import com.ss.platform.core.thread.RuleThreadPoolConfig;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * 规则线程池配置
 * <AUTHOR>
 */
@Configuration
@EnableAsync
@EnableConfigurationProperties
public class ThreadPoolConfig extends RuleThreadPoolConfig {



    @Bean(ThreadConstant.RULE_THREAD_POOL)
    @Primary
    public ThreadPoolTaskExecutor ruleThreadPool(){
        return createThreadPool(SystemConstant.AtrIdentity.APP_CODE);
    }


}
