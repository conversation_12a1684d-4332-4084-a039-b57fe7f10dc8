package com.ss.ifrs.actuarial.constant;

import java.util.*;

/**
 * @ClassName: ActuarialConstant
 * @Description: 计量平台常量定义类
 * @Author: yinxh.
 * @CreateDate: 2021/3/9 18:35
 * @Version: 1.0
 */
public class ActuarialConstant {
    public ActuarialConstant() {
    }

    //精算平台输出数据-现金流
    public static class BussCaseFlows {
        public final static String BUSS_BECF = "BUSS_ACTION";
    }

    public static class ProcCode {

        public final static String ATR_CONFIG = "ATR_CONFIG";
        public final static String QTC_CONFIG_ASSUMP = "QTC_CONFIG_ASSUMP";
        public final static String ATR_CONFIG_IRCC = "ATR_CONFIG_IRCC";

        //计量预期现金流流程节点
        public final static String EXPECTED_CF = "ATR_EXPECTED_CF";
        public final static String EXPECTED_CF_LRC = "ATR_EXPECTED_CF_LRC";
        public final static String EXPECTED_CF_LIC = "ATR_EXPECTED_CF_LIC";

        public final static String ATR_DISCOUNTING = "ATR_DISCOUNTING";
        public final static String ATR_DISCOUNTING_LRC = "ATR_DISCOUNTING_LRC";
        public final static String ATR_DISCOUNTING_LIC = "ATR_DISCOUNTING_LIC";

    }

    public static class FactorType {
        public final static String LRC = "Lrc";

        public final static String LIC = "Lic";
    }

    //计量结果展示编码定义： A-计量主信息、B-LRC-保费数据信息、B-LIC-赔款信息
    public static class ShowMenu {
        public final static String MAIN_CODE = "A";

        public final static String LRC_CODE = "B-LRC";

        public final static String LIC_CODE = "B-LIC";
    }

    public static class EvaluateType {
        //计量类型：全量-A /单个业务线-S
        public final static String ALL = "A";

        public final static String SINGLE = "S";
    }

    public static class ExcelDirection {
        public static final String IMPORT = "0";
        public static final String EXPORT = "1";
    }

    // TODO 码表配置及国际化
    public final static HashMap<String, String> ATR_ERROR_MESSAGE_MAP = new HashMap() {
        {
            put("ATR_MEAS_ERROR_001", "评估年月不在当前业务期间之前,请检查!");
            put("ATR_MEAS_ERROR_002", "未配置会计期间本位币,请检查!");
            put("ATR_MEAS_ERROR_003", "该业务模型未设置模型方案，请检查!");
            put("ATR_MEAS_ERROR_004", "该业务模型未配置业务线，请检查!");
            put("ATR_MEAS_ERROR_005", "该业务模型有业务线未配置假设数据，请检查!");
        }
    };

    public static class Import {
        //LRC导入小工具业务主键字段
        public final static String LRC_BUSINESS_NO = "Business No.(Policy No.-Endorsement/Bill No.)";
    }

    public static class Export {
        //现金流导出最大行数
        public final static Long CF_MAX_ROW = 10000L;

        public final static Integer CF_MAX_ROW1 = 10000;
        //现金流发展期列导出最大列数
        public final static Integer CF_MAX_COLUMN_DEV = 121;
    }

    //IBNR的导出Sheet页国际化
    public static class ExportIbnr {
        public final static String SHEET0 = "IBNR Calculation";
        public final static String SHEET1 = "Step One";
        public final static String SHEET2 = "Step Two";
        public final static String SHEET3 = "Step Three";
        public final static String SHEET4 = "Step Four";
        public final static String ACC_YEAR_MONTH = "Date of accident (Year/Month)";
        public final static String CLAIM_TIME = "Claim Delayed Time";
        public final static String TOTAL = "Total";
        public final static String ITEM = "item";
        public final static List<List<String>> Sheet0HeadList = Arrays.asList(Arrays.asList("Business Unit"), Arrays.asList("Extract Interval"), Arrays.asList("Number of Extraction Interval"), Arrays.asList("Currency"), Arrays.asList("Data Extraction End Date"), Arrays.asList("IBNR Type"), Arrays.asList("Business Type"), Arrays.asList("Business Line"), Arrays.asList("Operator"), Arrays.asList("Operation Time"));
        public final static List<Object> ContentList = Arrays.asList("{df.entityCode} -- {df.entityEName}", "{df.extractInterval}", "{df.statisZones}", "{df.currencyCode}", "{df.endDate}", "{df.ibnrType}", "{df.businessSourceCode}", "{df.loaCode}", "{df.creatorName}", "{df.createTime}");
    }

    public static class QuotaClass {
        public final static String ACTUARIAL = "A";
        public final static String BECF = "E";
        public final static String QUANTIFICATION = "Q";
    }

    // 指标类型： 1-发展期; 0-非发展期
    public static class QuotaType {
        public final static String DEVELOP_PERIOD = "1";
        public final static String NON_DEVELOP_PERIOD = "0";
        // 指标数据类型：4-百分比
        public final static String VALUE_TYPE_PERCENTAGES = "4";
    }

    //事故年月类型假设分组编码
    public static class QuotaGroup {
        public final static String LIC = "4";

        //假设值配置分组排除类型
        public final static List<String> EXCLUDE_GROUP = Arrays.asList();
    }

    // 查询数据来源类型
    public static class DataSourceType {
        // 数据来源1 标识二次分摊同步表
        public final static String DATA_SOURCE_TYPE_ONE = "1";
        // 数据来源2 标识二次分摊同步结果表
        public final static String DATA_SOURCE_TYPE_TWO = "2";
    }

    public static class IsApportionment {
        // 是否参与分摊 1: 参与
        public final static String IS_APPORTIONMENT_YES = "1";
        // 0: 不参与
        public final static String IS_APPORTIONMENT_NO = "0";
    }

    public static class IsBalance {
        // 0: 平
        public final static String IS_BALANCE_YES = "0";
        // 1: 不平
        public final static String IS_BALANCE_NO = "1";
    }

    public static class IbNrType {
        // 再保前IBNR
        public final static String BEFORE_REINSURANCE = "41";
        // 再保 摊回 IBNR
        public final static String REINSURANCE_AMORTIZATION = "43";

        public static class IbNrExecutionStatus {
            public final static String TO_BE_EXECUTED = "0";
            public final static String IN_EXECUTION = "1";
            public final static String EXECUTED_SUCCESSFULLY = "2";
            public final static String EXECUTION_EXCEPTION = "3";
        }

        public static class IbNrBizCode {
            public final static String IBNR_SEC_SYNC = "IBNR_SEC_SYNC";
            public final static String IBNR_SEC_COMPUTE = "IBNR_SEC_COMPUTE";

        }
    }

    public static class InterestRateTime {
        //编码
        public final static String CODE = "interestRateTime";
        public static class CodeType {
            public final static String BEGINNING = "0";
            public final static String MIDDLE = "0.5";
            public final static String END = "1";
            public final static String BASE = "B";
        }
    }
}
