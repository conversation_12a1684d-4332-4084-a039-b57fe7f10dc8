<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2021-11-10 17:08:27 -->
<!-- Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.conf.AtrConfQuotaDetailHisDao">
  <!-- 本文件由 MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDetailHis">
    <id column="quota_detailhis_id" property="quotaDetailHisId" jdbcType="NUMERIC" />
    <result column="quota_detail_id" property="quotaDetailId" jdbcType="NUMERIC" />
    <result column="quota_id" property="quotaId" jdbcType="NUMERIC" />
    <result column="quota_period" property="quotaPeriod" jdbcType="NUMERIC" />
    <result column="quota_value" property="quotaValue" jdbcType="VARCHAR" />
    <result column="creator_id" property="creatorId" jdbcType="NUMERIC" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="updator_id" property="updatorId" jdbcType="NUMERIC" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="oper_type" property="operType" jdbcType="VARCHAR" />
    <result column="oper_id" property="operId" jdbcType="NUMERIC" />
    <result column="oper_time" property="operTime" jdbcType="TIMESTAMP" />
    <result column="serial_no" property="serialNo" jdbcType="NUMERIC" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    quota_detailhis_id, quota_detail_id, quota_id, quota_period, quota_value, 
    creator_id, create_time, updator_id, update_time, oper_type, oper_id, oper_time,serial_no
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="quotaDetailHisId != null ">
          and quota_detailhis_id = #{quotaDetailHisId,jdbcType=NUMERIC}
      </if>
      <if test="quotaDetailId != null ">
          and quota_detail_id = #{quotaDetailId,jdbcType=NUMERIC}
      </if>
      <if test="quotaId != null ">
          and quota_id = #{quotaId,jdbcType=NUMERIC}
      </if>
      <if test="quotaPeriod != null ">
          and quota_period = #{quotaPeriod,jdbcType=NUMERIC}
      </if>
      <if test="quotaValue != null and quotaValue != ''">
          and quota_value = #{quotaValue,jdbcType=VARCHAR}
      </if>
      <if test="creatorId != null ">
          and creator_id = #{creatorId,jdbcType=NUMERIC}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and updator_id = #{updatorId,jdbcType=NUMERIC}
      </if>
      <if test="updateTime != null ">
          and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="operType != null and operType != ''">
          and oper_type = #{operType,jdbcType=VARCHAR}
      </if>
      <if test="operId != null ">
          and oper_id = #{operId,jdbcType=NUMERIC}
      </if>
      <if test="operTime != null ">
          and oper_time = #{operTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.quotaDetailHisId != null ">
          and quota_detailhis_id = #{condition.quotaDetailHisId,jdbcType=NUMERIC}
      </if>
      <if test="condition.quotaDetailId != null ">
          and quota_detail_id = #{condition.quotaDetailId,jdbcType=NUMERIC}
      </if>
      <if test="condition.quotaId != null ">
          and quota_id = #{condition.quotaId,jdbcType=NUMERIC}
      </if>
      <if test="condition.quotaPeriod != null ">
          and quota_period = #{condition.quotaPeriod,jdbcType=NUMERIC}
      </if>
      <if test="condition.quotaValue != null and condition.quotaValue != ''">
          and quota_value = #{condition.quotaValue,jdbcType=VARCHAR}
      </if>
      <if test="condition.creatorId != null ">
          and creator_id = #{condition.creatorId,jdbcType=NUMERIC}
      </if>
      <if test="condition.createTime != null ">
          and create_time = #{condition.createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.updatorId != null ">
          and updator_id = #{condition.updatorId,jdbcType=NUMERIC}
      </if>
      <if test="condition.updateTime != null ">
          and update_time = #{condition.updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.operType != null and condition.operType != ''">
          and oper_type = #{condition.operType,jdbcType=VARCHAR}
      </if>
      <if test="condition.operId != null ">
          and oper_id = #{condition.operId,jdbcType=NUMERIC}
      </if>
      <if test="condition.operTime != null ">
          and oper_time = #{condition.operTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="quotaDetailHisId != null ">
          and quota_detailhis_id = #{quotaDetailHisId,jdbcType=NUMERIC}
      </if>
      <if test="quotaDetailId != null ">
          and quota_detail_id = #{quotaDetailId,jdbcType=NUMERIC}
      </if>
      <if test="quotaId != null ">
          and quota_id = #{quotaId,jdbcType=NUMERIC}
      </if>
      <if test="quotaPeriod != null ">
          and quota_period = #{quotaPeriod,jdbcType=NUMERIC}
      </if>
      <if test="quotaValue != null and quotaValue != ''">
          and quota_value = #{quotaValue,jdbcType=VARCHAR}
      </if>
      <if test="creatorId != null ">
          and creator_id = #{creatorId,jdbcType=NUMERIC}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and updator_id = #{updatorId,jdbcType=NUMERIC}
      </if>
      <if test="updateTime != null ">
          and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="operType != null and operType != ''">
          and oper_type = #{operType,jdbcType=VARCHAR}
      </if>
      <if test="operId != null ">
          and oper_id = #{operId,jdbcType=NUMERIC}
      </if>
      <if test="operTime != null ">
          and oper_time = #{operTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_quota_detailhis
    where quota_detailhis_id = #{quotaDetailHisId,jdbcType=NUMERIC}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_quota_detailhis
    where quota_detailhis_id in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_quota_detailhis
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDetailHis">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_quota_detailhis
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_quota_detailhis
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="quota_detailhis_id" keyProperty="quotaDetailHisId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDetailHis">
    <selectKey resultType="long" keyProperty="quotaDetailHisId" order="BEFORE">
      select nextval('atr_seq_conf_quota_detailhis') as sequenceNo 
    </selectKey>
    insert into atr_conf_quota_detailhis
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="quotaDetailHisId != null">
        quota_detailhis_id,
      </if>
      <if test="quotaDetailId != null">
        quota_detail_id,
      </if>
      <if test="quotaId != null">
        quota_id,
      </if>
      <if test="quotaPeriod != null">
        quota_period,
      </if>
      <if test="quotaValue != null">
        quota_value,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updatorId != null">
        updator_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="operType != null">
        oper_type,
      </if>
      <if test="operId != null">
        oper_id,
      </if>
      <if test="operTime != null">
        oper_time,
      </if>
      <if test="serialNo != null">
        serial_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="quotaDetailHisId != null">
        #{quotaDetailHisId,jdbcType=NUMERIC},
      </if>
      <if test="quotaDetailId != null">
        #{quotaDetailId,jdbcType=NUMERIC},
      </if>
      <if test="quotaId != null">
        #{quotaId,jdbcType=NUMERIC},
      </if>
      <if test="quotaPeriod != null">
        #{quotaPeriod,jdbcType=NUMERIC},
      </if>
      <if test="quotaValue != null">
        #{quotaValue,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=NUMERIC},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        #{updatorId,jdbcType=NUMERIC},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operType != null">
        #{operType,jdbcType=VARCHAR},
      </if>
      <if test="operId != null">
        #{operId,jdbcType=NUMERIC},
      </if>
      <if test="operTime != null">
        #{operTime,jdbcType=TIMESTAMP},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=NUMERIC},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert into atr_conf_quota_detailhis
     (quota_detailhis_id, quota_detail_id, 
      quota_id, quota_period, quota_value, 
      creator_id, create_time, updator_id, 
      update_time, oper_type, oper_id, 
      oper_time)
     values 
    <foreach collection="list" item="item" index="index" separator=",">
       (#{item.quotaDetailHisId,jdbcType=NUMERIC}, #{item.quotaDetailId,jdbcType=NUMERIC}, 
        #{item.quotaId,jdbcType=NUMERIC}, #{item.quotaPeriod,jdbcType=NUMERIC}, #{item.quotaValue,jdbcType=VARCHAR}, 
        #{item.creatorId,jdbcType=NUMERIC}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updatorId,jdbcType=NUMERIC}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.operType,jdbcType=VARCHAR}, #{item.operId,jdbcType=NUMERIC}, 
        #{item.operTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDetailHis">
    update atr_conf_quota_detailhis
    <set>
      <if test="quotaDetailId != null">
        quota_detail_id = #{quotaDetailId,jdbcType=NUMERIC},
      </if>
      <if test="quotaId != null">
        quota_id = #{quotaId,jdbcType=NUMERIC},
      </if>
      <if test="quotaPeriod != null">
        quota_period = #{quotaPeriod,jdbcType=NUMERIC},
      </if>
      <if test="quotaValue != null">
        quota_value = #{quotaValue,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=NUMERIC},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        updator_id = #{updatorId,jdbcType=NUMERIC},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operType != null">
        oper_type = #{operType,jdbcType=VARCHAR},
      </if>
      <if test="operId != null">
        oper_id = #{operId,jdbcType=NUMERIC},
      </if>
      <if test="operTime != null">
        oper_time = #{operTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where quota_detailhis_id = #{quotaDetailHisId,jdbcType=NUMERIC}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDetailHis">
    update atr_conf_quota_detailhis
    <set>
      <if test="record.quotaDetailId != null">
        quota_detail_id = #{record.quotaDetailId,jdbcType=NUMERIC},
      </if>
      <if test="record.quotaId != null">
        quota_id = #{record.quotaId,jdbcType=NUMERIC},
      </if>
      <if test="record.quotaPeriod != null">
        quota_period = #{record.quotaPeriod,jdbcType=NUMERIC},
      </if>
      <if test="record.quotaValue != null">
        quota_value = #{record.quotaValue,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=NUMERIC},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatorId != null">
        updator_id = #{record.updatorId,jdbcType=NUMERIC},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operType != null">
        oper_type = #{record.operType,jdbcType=VARCHAR},
      </if>
      <if test="record.operId != null">
        oper_id = #{record.operId,jdbcType=NUMERIC},
      </if>
      <if test="record.operTime != null">
        oper_time = #{record.operTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from atr_conf_quota_detailhis
    where quota_detailhis_id = #{quotaDetailHisId,jdbcType=NUMERIC}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from atr_conf_quota_detailhis
    where quota_detailhis_id in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from atr_conf_quota_detailhis
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDetailHis">
    select count(1) from atr_conf_quota_detailhis
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>