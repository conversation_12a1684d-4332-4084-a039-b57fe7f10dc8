/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-12-07 09:24:43
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-12-07 09:24:43<br/>
 * Description: ibnr计算-action<br/>
 * Table Name: atr_buss_ibnrcalc_action<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "ibnr计算-action")
public class AtrBussIbnrcalcAction implements Serializable {
    /**
     * Database column: atr_buss_ibnrcalc_action.id
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: atr_buss_ibnrcalc_action.action_no
     * Database remarks: action编号
     */
    @ApiModelProperty(value = "action编号", required = true)
    private String actionNo;

    /**
     * Database column: atr_buss_ibnrcalc_action.entity_id
     * Database remarks: null
     */
    @ApiModelProperty(value = "null", required = true)
    private Long entityId;

    /**
     * Database column: atr_buss_ibnrcalc_action.currency_code
     * Database remarks: 币别
     */
    @ApiModelProperty(value = "币别", required = true)
    private String currencyCode;

    /**
     * Database column: atr_buss_ibnrcalc_action.extraction_interval
     * Database remarks: 提取区间|ATR/SimpleExtractInterval
     */
    @ApiModelProperty(value = "提取区间|ATR/SimpleExtractInterval", required = true)
    private String extractionInterval;

    /**
     * Database column: atr_buss_ibnrcalc_action.extraction_zones
     * Database remarks: 提取区间数
     */
    @ApiModelProperty(value = "提取区间数", required = true)
    private Short extractionZones;

    /**
     * Database column: atr_buss_ibnrcalc_action.extraction_deadline
     * Database remarks: 提取结束年月
     */
    @ApiModelProperty(value = "提取结束年月", required = true)
    private String extractionDeadline;

    /**
     * Database column: atr_buss_ibnrcalc_action.ibnr_type
     * Database remarks: IBNR类型|ATR/IbnrType，  1-再保前赔款， 2-再保后赔款
     */
    @ApiModelProperty(value = "IBNR类型|ATR/IbnrType，  1-再保前赔款， 2-再保后赔款", required = false)
    private String ibnrType;

    /**
     * Database column: atr_buss_ibnrcalc_action.loa_code
     * Database remarks: 业务线
     */
    @ApiModelProperty(value = "业务线", required = false)
    private String loaCode;

    /**
     * Database column: atr_buss_ibnrcalc_action.status
     * Database remarks: 执行状态|ATR/QuantificationStatus， R-执行中、S-执行成功、E-执行异常
     */
    @ApiModelProperty(value = "执行状态|ATR/QuantificationStatus， R-执行中、S-执行成功、E-执行异常", required = true)
    private String status;

    /**
     * Database column: atr_buss_ibnrcalc_action.error_msg
     * Database remarks: 错误信息
     */
    @ApiModelProperty(value = "错误信息", required = false)
    private String errorMsg;

    /**
     * Database column: atr_buss_ibnrcalc_action.confirm_is
     * Database remarks: null|BBS/ConfirmState
     */
    @ApiModelProperty(value = "null|BBS/ConfirmState", required = true)
    private String confirmIs;

    /**
     * Database column: atr_buss_ibnrcalc_action.confirm_id
     * Database remarks: null
     */
    @ApiModelProperty(value = "null", required = false)
    private Long confirmId;

    /**
     * Database column: atr_buss_ibnrcalc_action.confirm_time
     * Database remarks: null
     */
    @ApiModelProperty(value = "null", required = false)
    private Date confirmTime;

    /**
     * Database column: atr_buss_ibnrcalc_action.confirm_method
     * Database remarks: 确认的算法|LR-Loss Ratio Method  损失率法, 
CL-Chain Ladder Method  链梯法, 
BF-Bornhuetter Ferguson Method B-F 法
     */
    @ApiModelProperty(value = "确认的算法|LR-Loss Ratio Method  损失率法, CL-Chain Ladder Method  链梯法, " +
            "BF-Bornhuetter Ferguson Method B-F 法", required = false)
    private String confirmMethod;

    /**
     * Database column: atr_buss_ibnrcalc_action.create_id
     * Database remarks: null
     */
    @ApiModelProperty(value = "null", required = false)
    private Long createId;

    /**
     * Database column: atr_buss_ibnrcalc_action.create_time
     * Database remarks: null
     */
    @ApiModelProperty(value = "null", required = false)
    private Date createTime;

    /**
     * Database column: atr_buss_ibnrcalc_action.complete_time
     * Database remarks: 完成时间
     */
    @ApiModelProperty(value = "完成时间", required = false)
    private Date completeTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getExtractionInterval() {
        return extractionInterval;
    }

    public void setExtractionInterval(String extractionInterval) {
        this.extractionInterval = extractionInterval;
    }

    public Short getExtractionZones() {
        return extractionZones;
    }

    public void setExtractionZones(Short extractionZones) {
        this.extractionZones = extractionZones;
    }

    public String getExtractionDeadline() {
        return extractionDeadline;
    }

    public void setExtractionDeadline(String extractionDeadline) {
        this.extractionDeadline = extractionDeadline;
    }

    public String getIbnrType() {
        return ibnrType;
    }

    public void setIbnrType(String ibnrType) {
        this.ibnrType = ibnrType;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getConfirmIs() {
        return confirmIs;
    }

    public void setConfirmIs(String confirmIs) {
        this.confirmIs = confirmIs;
    }

    public Long getConfirmId() {
        return confirmId;
    }

    public void setConfirmId(Long confirmId) {
        this.confirmId = confirmId;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public String getConfirmMethod() {
        return confirmMethod;
    }

    public void setConfirmMethod(String confirmMethod) {
        this.confirmMethod = confirmMethod;
    }

    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }
}