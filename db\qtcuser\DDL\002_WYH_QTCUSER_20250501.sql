delete from qtc_conf_code_adapter where CODE='findQtcVersionByYearMonth';
insert into qtc_conf_code_adapter (CODE_CONF_ID, CODE, PO_NAME, SEARCH_PARAMS, CREATOR_ID, CREATE_TIME, UPDATOR_ID, UPDATE_TIME)
values ((SELECT COALESCE( max(code_conf_id),0) +1 from qtc_conf_code_adapter), 'findQtcVersionByYearMonth', 'qtc_Buss_Evaluate_Main', 'year_month', null, null, null, null);
commit; 



delete from qtc_conf_code where upper_code_id = (select code_id from qtc_v_conf_code where code_code_idx = 'RunNode'  and upper_code_id=0);
delete from qtc_conf_code where code_code = 'RunNode' and upper_code_id=0;
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES ( nextval('qtc_seq_conf_code'), '0', 'RunNode', '数据检查节点', '数据检查节点', 'Data Check Node', NULL, NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES ( nextval('qtc_seq_conf_code'), (select code_id from qtc_v_conf_code where code_code_idx = 'RunNode'), 'A', '计量前', '计量前', '计量前', NULL, 1, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES ( nextval('qtc_seq_conf_code'), (select code_id from qtc_v_conf_code where code_code_idx = 'RunNode'), 'B', '计量检查', '计量检查', '计量检查', NULL, 4, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES ( nextval('qtc_seq_conf_code'), (select code_id from qtc_v_conf_code where code_code_idx = 'RunNode'), 'C', '分摊检查', '分摊检查', '分摊检查', NULL, 4, '1', NULL, NULL, NULL, NULL);
commit; 



delete from qtc_conf_bussperiod_detail where biz_type_id in (select biz_type_id from qtc_conf_table where biz_code in ('BUSS_ALLOC','ATR_PULL_STATS_CHECK','DAP_DATA_CHECK','QTC_DATA_PUSH_TO_ACC'));
delete from qtc_conf_table where biz_code in ('BUSS_ALLOC','ATR_PULL_STATS_CHECK','DAP_DATA_CHECK','QTC_DATA_PUSH_TO_ACC');
insert into qtc_conf_table (BIZ_TYPE_ID, BIZ_CODE, TYPE_CODE, TYPE_E_NAME, TYPE_L_NAME, TYPE_C_NAME, SYSTEM_CODE,  VALID_IS, DISPLAY_NO, CREATE_TIME, CREATOR_ID, UPDATE_TIME, UPDATOR_ID, DIRECTION)
values (1, 'ATR_PULL_STATS_CHECK', null, 'Actuarial platform status check', '精算平臺狀態檢查', '精算平台状态检查', 'ATR', '1', 1, '2024-12-20 16:23:31', 1, null, null, '1');
insert into qtc_conf_table (BIZ_TYPE_ID, BIZ_CODE, TYPE_CODE, TYPE_E_NAME, TYPE_L_NAME, TYPE_C_NAME, SYSTEM_CODE,   VALID_IS, DISPLAY_NO, CREATE_TIME, CREATOR_ID, UPDATE_TIME, UPDATOR_ID, DIRECTION)
values (4, 'QTC_DATA_PUSH_TO_ACC', null, 'Push the evaluate results to the Acc', '计量结果推送会计', '计量结果推送会计', 'QTC',  '1', 24, '2024-12-20 16:23:31', 1, null, null, '0');



do $$
DECLARE
v_year_month varchar ;
cur_period RECORD;
cur_table RECORD;
BEGIN
truncate table  qtc_conf_bussperiod_detail cascade ;
  FOR cur_period IN (SELECT buss_period_id,period_state
                       FROM qtc_conf_bussperiod
                      WHERE valid_is = '1') LOOP

    FOR cur_table IN(SELECT biz_type_id, biz_code
                        FROM qtc_conf_table) LOOP

    INSERT INTO qtcuser.qtc_conf_bussperiod_detail(period_detail_id, buss_period_id, biz_type_id, biz_code, ready_state, creator_id,create_time)
    VALUES (nextval('qtc_seq_conf_bussperiod_detail'),cur_period.buss_period_id,cur_table.biz_type_id,cur_table.biz_code,
    (CASE WHEN cur_period.period_state = '1' OR cur_period.period_state = '3' THEN '1' ELSE '0' END), 
     1, LOCALTIMESTAMP);

    END LOOP;
  END LOOP;

END $$;