package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

/**
 * <AUTHOR>
 * @Date 2021/4/15
 */
public class AtrBussItemMenuVo {

    private String riskClassCode;

    private String currencyCode;

    private String year;

    private String icgNo;

    private String portfolioNo;

    private String classEName;

    private String classCName;

    private String classLName;

    private String yearCName;

    private String yearTName;

    private String yearEName;

    private String icgNoCName;

    private String icgNoTName;

    private String icgNoEName;

    private String businessSourceCode;

    private Long entityId;

    private String startYear;

    private String endYear;

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }

    public String getClassEName() {
        return classEName;
    }

    public void setClassEName(String classEName) {
        this.classEName = classEName;
    }

    public String getClassCName() {
        return classCName;
    }

    public void setClassCName(String classCName) {
        this.classCName = classCName;
    }

    public String getClassLName() {
        return classLName;
    }

    public void setClassLName(String classLName) {
        this.classLName = classLName;
    }

    public String getYearCName() {
        return yearCName;
    }

    public void setYearCName(String yearCName) {
        this.yearCName = yearCName;
    }

    public String getYearTName() {
        return yearTName;
    }

    public void setYearTName(String yearTName) {
        this.yearTName = yearTName;
    }

    public String getYearEName() {
        return yearEName;
    }

    public void setYearEName(String yearEName) {
        this.yearEName = yearEName;
    }

    public String getIcgNoCName() {
        return icgNoCName;
    }

    public void setIcgNoCName(String icgNoCName) {
        this.icgNoCName = icgNoCName;
    }

    public String getIcgNoTName() {
        return icgNoTName;
    }

    public void setIcgNoTName(String icgNoTName) {
        this.icgNoTName = icgNoTName;
    }

    public String getIcgNoEName() {
        return icgNoEName;
    }

    public void setIcgNoEName(String icgNoEName) {
        this.icgNoEName = icgNoEName;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }	

    public String getStartYear() {
        return startYear;
    }

    public void setStartYear(String startYear) {
        this.startYear = startYear;
    }

    public String getEndYear() {
        return endYear;
    }

    public void setEndYear(String endYear) {
        this.endYear = endYear;
    }
}
