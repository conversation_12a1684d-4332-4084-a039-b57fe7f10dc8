<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2022-11-22 09:52:20 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.conf.AtrConfQuotaDefDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDef">
    <id column="QUOTA_DEF_ID" property="quotaDefId" jdbcType="DECIMAL" />
    <result column="QUOTA_CODE" property="quotaCode" jdbcType="VARCHAR" />
    <result column="QUOTA_C_NAME" property="quotaCName" jdbcType="VARCHAR" />
    <result column="QUOTA_L_NAME" property="quotaLName" jdbcType="VARCHAR" />
    <result column="QUOTA_E_NAME" property="quotaEName" jdbcType="VARCHAR" />
    <result column="CODE_TYPE" property="codeType" jdbcType="VARCHAR" />
    <result column="QUOTA_CLASS" property="quotaClass" jdbcType="VARCHAR" />
    <result column="QUOTA_GROUP" property="quotaGroup" jdbcType="VARCHAR" />
    <result column="QUOTA_TYPE" property="quotaType" jdbcType="VARCHAR" />
    <result column="QUOTA_VALUE_TYPE" property="quotaValueType" jdbcType="VARCHAR" />
    <result column="dimension" property="dimension" jdbcType="VARCHAR" />
    <result column="RULE_ID" property="ruleId" jdbcType="DECIMAL" />
    <result column="PERCENT_HUNDRED_IS" property="percentHundredIs" jdbcType="VARCHAR" />
    <result column="DISPLAY_NO" property="displayNo" jdbcType="DECIMAL" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="SERIAL_NO" property="serialNo" jdbcType="DECIMAL" />
    <result column="VALID_IS" property="validIs" jdbcType="VARCHAR" />
    <result column="QUOTA_EFFECT" property="quotaEffect" jdbcType="VARCHAR" />
    <result column="AUDIT_STATE" property="auditState" jdbcType="VARCHAR" />
    <result column="CHECKED_MSG" property="checkedMsg" jdbcType="VARCHAR" />
    <result column="CHECKED_ID" property="checkedId" jdbcType="DECIMAL" />
    <result column="CHECKED_TIME" property="checkedTime" jdbcType="TIMESTAMP" />
    <result column="CREATOR_ID" property="creatorId" jdbcType="DECIMAL" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATOR_ID" property="updatorId" jdbcType="DECIMAL" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="auto_calculated_is" property="autoCalculatedIs" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    QUOTA_DEF_ID, QUOTA_CODE, QUOTA_C_NAME, QUOTA_L_NAME, QUOTA_E_NAME, CODE_TYPE, QUOTA_CLASS, QUOTA_GROUP,
    QUOTA_TYPE, QUOTA_VALUE_TYPE, dimension, RULE_ID, PERCENT_HUNDRED_IS, DISPLAY_NO,remark,
    SERIAL_NO, VALID_IS, QUOTA_EFFECT, AUDIT_STATE, CHECKED_MSG, CHECKED_ID, CHECKED_TIME, CREATOR_ID,
    CREATE_TIME, UPDATOR_ID, UPDATE_TIME, auto_calculated_is
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="quotaDefId != null ">
        and QUOTA_DEF_ID = #{quotaDefId,jdbcType=DECIMAL}
      </if>
      <if test="quotaCode != null and quotaCode != ''">
        and QUOTA_CODE = #{quotaCode,jdbcType=VARCHAR}
      </if>
      <if test="quotaCName != null and quotaCName != ''">
        and QUOTA_C_NAME = #{quotaCName,jdbcType=VARCHAR}
      </if>
      <if test="quotaLName != null and quotaLName != ''">
        and QUOTA_L_NAME = #{quotaLName,jdbcType=VARCHAR}
      </if>
      <if test="quotaEName != null and quotaEName != ''">
        and QUOTA_E_NAME = #{quotaEName,jdbcType=VARCHAR}
      </if>
      <if test="codeType != null and codeType != ''">
        and CODE_TYPE = #{codeType,jdbcType=VARCHAR}
      </if>
      <if test="quotaClass != null and quotaClass != ''">
        and QUOTA_CLASS = #{quotaClass,jdbcType=VARCHAR}
      </if>
      <if test="quotaGroup != null and quotaGroup != ''">
        and QUOTA_GROUP = #{quotaGroup,jdbcType=VARCHAR}
      </if>
      <if test="quotaType != null and quotaType != ''">
        and QUOTA_TYPE = #{quotaType,jdbcType=VARCHAR}
      </if>
      <if test="quotaValueType != null and quotaValueType != ''">
        and QUOTA_VALUE_TYPE = #{quotaValueType,jdbcType=VARCHAR}
      </if>
      <if test="dimension != null and dimension != ''">
        and dimension = #{dimension,jdbcType=VARCHAR}
      </if>
      <if test="ruleId != null ">
        and RULE_ID = #{ruleId,jdbcType=DECIMAL}
      </if>
      <if test="percentHundredIs != null and percentHundredIs != ''">
        and PERCENT_HUNDRED_IS = #{percentHundredIs,jdbcType=VARCHAR}
      </if>
      <if test="displayNo != null ">
        and DISPLAY_NO = #{displayNo,jdbcType=DECIMAL}
      </if>
      <if test="remark != null and remark != ''">
        and remark = #{remark,jdbcType=VARCHAR}
      </if>
      <if test="serialNo != null ">
        and SERIAL_NO = #{serialNo,jdbcType=DECIMAL}
      </if>
      <if test="validIs != null and validIs != ''">
        and VALID_IS = #{validIs,jdbcType=VARCHAR}
      </if>
      <if test="quotaEffect != null and quotaEffect != ''">
        and QUOTA_EFFECT = #{quotaEffect,jdbcType=VARCHAR}
      </if>
      <if test="auditState != null and auditState != ''">
        and AUDIT_STATE = #{auditState,jdbcType=VARCHAR}
      </if>
      <if test="checkedMsg != null and checkedMsg != ''">
        and CHECKED_MSG = #{checkedMsg,jdbcType=VARCHAR}
      </if>
      <if test="checkedId != null ">
        and CHECKED_ID = #{checkedId,jdbcType=DECIMAL}
      </if>
      <if test="checkedTime != null ">
        and CHECKED_TIME = #{checkedTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creatorId != null ">
        and CREATOR_ID = #{creatorId,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null ">
        and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
        and UPDATOR_ID = #{updatorId,jdbcType=DECIMAL}
      </if>
      <if test="updateTime != null ">
        and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="autoCalculatedIs != null and autoCalculatedIs != ''">
        and AUTO_CALCULATED_IS = #{autoCalculatedIs,jdbcType=VARCHAR}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.quotaDefId != null ">
        and QUOTA_DEF_ID = #{condition.quotaDefId,jdbcType=DECIMAL}
      </if>
      <if test="condition.quotaCode != null and condition.quotaCode != ''">
        and QUOTA_CODE = #{condition.quotaCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.quotaCName != null and condition.quotaCName != ''">
        and QUOTA_C_NAME = #{condition.quotaCName,jdbcType=VARCHAR}
      </if>
      <if test="condition.quotaLName != null and condition.quotaLName != ''">
        and QUOTA_L_NAME = #{condition.quotaLName,jdbcType=VARCHAR}
      </if>
      <if test="condition.quotaEName != null and condition.quotaEName != ''">
        and QUOTA_E_NAME = #{condition.quotaEName,jdbcType=VARCHAR}
      </if>
      <if test="condition.codeType != null and condition.codeType != ''">
        and CODE_TYPE = #{condition.codeType,jdbcType=VARCHAR}
      </if>
      <if test="condition.quotaClass != null and condition.quotaClass != ''">
        and QUOTA_CLASS = #{condition.quotaClass,jdbcType=VARCHAR}
      </if>
      <if test="condition.quotaGroup != null and condition.quotaGroup != ''">
        and QUOTA_GROUP = #{condition.quotaGroup,jdbcType=VARCHAR}
      </if>
      <if test="condition.quotaType != null and condition.quotaType != ''">
        and QUOTA_TYPE = #{condition.quotaType,jdbcType=VARCHAR}
      </if>
      <if test="condition.quotaValueType != null and condition.quotaValueType != ''">
        and QUOTA_VALUE_TYPE = #{condition.quotaValueType,jdbcType=VARCHAR}
      </if>
      <if test="condition.dimension != null and condition.dimension != ''">
        and dimension = #{condition.dimension,jdbcType=VARCHAR}
      </if>
      <if test="condition.ruleId != null ">
        and RULE_ID = #{condition.ruleId,jdbcType=DECIMAL}
      </if>
      <if test="condition.percentHundredIs != null and condition.percentHundredIs != ''">
        and PERCENT_HUNDRED_IS = #{condition.percentHundredIs,jdbcType=VARCHAR}
      </if>
      <if test="condition.displayNo != null ">
        and DISPLAY_NO = #{condition.displayNo,jdbcType=DECIMAL}
      </if>
      <if test="condition.remark != null and condition.remark != ''">
        and remark = #{condition.remark,jdbcType=VARCHAR}
      </if>
      <if test="condition.serialNo != null ">
        and SERIAL_NO = #{condition.serialNo,jdbcType=DECIMAL}
      </if>
      <if test="condition.validIs != null and condition.validIs != ''">
        and VALID_IS = #{condition.validIs,jdbcType=VARCHAR}
      </if>
      <if test="condition.auditState != null and condition.auditState != ''">
        and AUDIT_STATE = #{condition.auditState,jdbcType=VARCHAR}
      </if>
      <if test="condition.checkedMsg != null and condition.checkedMsg != ''">
        and CHECKED_MSG = #{condition.checkedMsg,jdbcType=VARCHAR}
      </if>
      <if test="condition.checkedId != null ">
        and CHECKED_ID = #{condition.checkedId,jdbcType=DECIMAL}
      </if>
      <if test="condition.checkedTime != null ">
        and CHECKED_TIME = #{condition.checkedTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.creatorId != null ">
        and CREATOR_ID = #{condition.creatorId,jdbcType=DECIMAL}
      </if>
      <if test="condition.createTime != null ">
        and CREATE_TIME = #{condition.createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.updatorId != null ">
        and UPDATOR_ID = #{condition.updatorId,jdbcType=DECIMAL}
      </if>
      <if test="condition.updateTime != null ">
        and UPDATE_TIME = #{condition.updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.autoCalculatedIs != null and condition.autoCalculatedIs != ''">
        and AUTO_CALCULATED_IS = #{condition.autoCalculatedIs,jdbcType=VARCHAR}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="quotaDefId != null ">
        and QUOTA_DEF_ID = #{quotaDefId,jdbcType=DECIMAL}
      </if>
      <if test="quotaCode != null and quotaCode != ''">
        and QUOTA_CODE = #{quotaCode,jdbcType=VARCHAR}
      </if>
      <if test="quotaCName != null and quotaCName != ''">
        and QUOTA_C_NAME = #{quotaCName,jdbcType=VARCHAR}
      </if>
      <if test="quotaLName != null and quotaLName != ''">
        and QUOTA_L_NAME = #{quotaLName,jdbcType=VARCHAR}
      </if>
      <if test="quotaEName != null and quotaEName != ''">
        and QUOTA_E_NAME = #{quotaEName,jdbcType=VARCHAR}
      </if>
      <if test="codeType != null and codeType != ''">
        and CODE_TYPE = #{codeType,jdbcType=VARCHAR}
      </if>
      <if test="quotaClass != null and quotaClass != ''">
        and QUOTA_CLASS = #{quotaClass,jdbcType=VARCHAR}
      </if>
      <if test="quotaGroup != null and quotaGroup != ''">
        and QUOTA_GROUP = #{quotaGroup,jdbcType=VARCHAR}
      </if>
      <if test="quotaType != null and quotaType != ''">
        and QUOTA_TYPE = #{quotaType,jdbcType=VARCHAR}
      </if>
      <if test="quotaValueType != null and quotaValueType != ''">
        and QUOTA_VALUE_TYPE = #{quotaValueType,jdbcType=VARCHAR}
      </if>
      <if test="dimension != null and dimension != ''">
        and dimension = #{dimension,jdbcType=VARCHAR}
      </if>
      <if test="ruleId != null ">
        and RULE_ID = #{ruleId,jdbcType=DECIMAL}
      </if>
      <if test="percentHundredIs != null and percentHundredIs != ''">
        and PERCENT_HUNDRED_IS = #{percentHundredIs,jdbcType=VARCHAR}
      </if>
      <if test="displayNo != null ">
        and DISPLAY_NO = #{displayNo,jdbcType=DECIMAL}
      </if>
      <if test="remark != null and remark != ''">
        and remark = #{remark,jdbcType=VARCHAR}
      </if>
      <if test="serialNo != null ">
        and SERIAL_NO = #{serialNo,jdbcType=DECIMAL}
      </if>
      <if test="validIs != null and validIs != ''">
        and VALID_IS = #{validIs,jdbcType=VARCHAR}
      </if>
      <if test="auditState != null and auditState != ''">
        and AUDIT_STATE = #{auditState,jdbcType=VARCHAR}
      </if>
      <if test="checkedMsg != null and checkedMsg != ''">
        and CHECKED_MSG = #{checkedMsg,jdbcType=VARCHAR}
      </if>
      <if test="checkedId != null ">
        and CHECKED_ID = #{checkedId,jdbcType=DECIMAL}
      </if>
      <if test="checkedTime != null ">
        and CHECKED_TIME = #{checkedTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creatorId != null ">
        and CREATOR_ID = #{creatorId,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null ">
        and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
        and UPDATOR_ID = #{updatorId,jdbcType=DECIMAL}
      </if>
      <if test="updateTime != null ">
        and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="autoCalculatedIs != null and autoCalculatedIs != ''">
        and AUTO_CALCULATED_IS = #{autoCalculatedIs,jdbcType=VARCHAR}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List" />
    from ATR_CONF_QUOTA_DEF
    where QUOTA_DEF_ID = #{quotaDefId,jdbcType=DECIMAL}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select
    <include refid="Base_Column_List" />
    from ATR_CONF_QUOTA_DEF
    where QUOTA_DEF_ID in
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ATR_CONF_QUOTA_DEF
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDef">
    select
    <include refid="Base_Column_List" />
    from ATR_CONF_QUOTA_DEF
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select
    <include refid="Base_Column_List" />
    from ATR_CONF_QUOTA_DEF
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="QUOTA_DEF_ID" keyProperty="quotaDefId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDef">
    <selectKey resultType="long" keyProperty="quotaDefId" order="BEFORE">
      select nextval('atr_seq_conf_quota_def') as sequenceNo 
    </selectKey>
    insert into ATR_CONF_QUOTA_DEF
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="quotaDefId != null">
        QUOTA_DEF_ID,
      </if>
      <if test="quotaCode != null">
        QUOTA_CODE,
      </if>
      <if test="quotaCName != null">
        QUOTA_C_NAME,
      </if>
      <if test="quotaLName != null">
        QUOTA_L_NAME,
      </if>
      <if test="quotaEName != null">
        QUOTA_E_NAME,
      </if>
      <if test="codeType != null">
        CODE_TYPE,
      </if>
      <if test="quotaClass != null">
        QUOTA_CLASS,
      </if>
      <if test="quotaGroup != null">
        QUOTA_GROUP,
      </if>
      <if test="quotaType != null">
        QUOTA_TYPE,
      </if>
      <if test="quotaValueType != null">
        QUOTA_VALUE_TYPE,
      </if>
      <if test="dimension != null">
        dimension,
      </if>
      <if test="ruleId != null">
        RULE_ID,
      </if>
      <if test="percentHundredIs != null">
        PERCENT_HUNDRED_IS,
      </if>
      <if test="displayNo != null">
        DISPLAY_NO,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="serialNo != null">
        SERIAL_NO,
      </if>
      <if test="validIs != null">
        VALID_IS,
      </if>
      <if test="quotaEffect != null">
        QUOTA_EFFECT,
      </if>
      <if test="auditState != null">
        AUDIT_STATE,
      </if>
      <if test="checkedMsg != null">
        CHECKED_MSG,
      </if>
      <if test="checkedId != null">
        CHECKED_ID,
      </if>
      <if test="checkedTime != null">
        CHECKED_TIME,
      </if>
      <if test="creatorId != null">
        CREATOR_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updatorId != null">
        UPDATOR_ID,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="autoCalculatedIs != null">
        AUTO_CALCULATED_IS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="quotaDefId != null">
        #{quotaDefId,jdbcType=DECIMAL},
      </if>
      <if test="quotaCode != null">
        #{quotaCode,jdbcType=VARCHAR},
      </if>
      <if test="quotaCName != null">
        #{quotaCName,jdbcType=VARCHAR},
      </if>
      <if test="quotaLName != null">
        #{quotaLName,jdbcType=VARCHAR},
      </if>
      <if test="quotaEName != null">
        #{quotaEName,jdbcType=VARCHAR},
      </if>
      <if test="codeType != null">
        #{codeType,jdbcType=VARCHAR},
      </if>
      <if test="quotaClass != null">
        #{quotaClass,jdbcType=VARCHAR},
      </if>
      <if test="quotaGroup != null">
        #{quotaGroup,jdbcType=VARCHAR},
      </if>
      <if test="quotaType != null">
        #{quotaType,jdbcType=VARCHAR},
      </if>
      <if test="quotaValueType != null">
        #{quotaValueType,jdbcType=VARCHAR},
      </if>
      <if test="dimension != null">
        #{dimension,jdbcType=VARCHAR},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=DECIMAL},
      </if>
      <if test="percentHundredIs != null">
        #{percentHundredIs,jdbcType=VARCHAR},
      </if>
      <if test="displayNo != null">
        #{displayNo,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=DECIMAL},
      </if>
      <if test="validIs != null">
        #{validIs,jdbcType=VARCHAR},
      </if>
      <if test="quotaEffect != null">
        #{quotaEffect,jdbcType=VARCHAR},
      </if>
      <if test="auditState != null">
        #{auditState,jdbcType=VARCHAR},
      </if>
      <if test="checkedMsg != null">
        #{checkedMsg,jdbcType=VARCHAR},
      </if>
      <if test="checkedId != null">
        #{checkedId,jdbcType=DECIMAL},
      </if>
      <if test="checkedTime != null">
        #{checkedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        #{updatorId,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="autoCalculatedIs != null">
        #{autoCalculatedIs,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all
    <foreach collection="list" item="item" index="index">
      into ATR_CONF_QUOTA_DEF values
      (#{item.quotaDefId,jdbcType=DECIMAL},
      #{item.quotaCode,jdbcType=VARCHAR}, #{item.quotaCName,jdbcType=VARCHAR}, #{item.quotaLName,jdbcType=VARCHAR},
      #{item.quotaEName,jdbcType=VARCHAR}, #{item.codeType,jdbcType=VARCHAR},#{item.quotaClass,jdbcType=VARCHAR}, #{item.quotaGroup,jdbcType=VARCHAR},
      #{item.quotaType,jdbcType=VARCHAR}, #{item.quotaValueType,jdbcType=VARCHAR}, #{item.dimension,jdbcType=VARCHAR},
      #{item.ruleId,jdbcType=DECIMAL}, #{item.percentHundredIs,jdbcType=VARCHAR}, #{item.displayNo,jdbcType=DECIMAL},
      #{item.serialNo,jdbcType=DECIMAL}, #{item.validIs,jdbcType=VARCHAR},#{item.quotaEffect,jdbcType=VARCHAR}, #{item.auditState,jdbcType=VARCHAR},
      #{item.checkedMsg,jdbcType=VARCHAR}, #{item.checkedId,jdbcType=DECIMAL}, #{item.checkedTime,jdbcType=TIMESTAMP},
      #{item.creatorId,jdbcType=DECIMAL}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updatorId,jdbcType=DECIMAL},
      #{item.updateTime,jdbcType=TIMESTAMP}, #{item.autoCalculatedIs,jdbcType=VARCHAR})
    </foreach>
    select 1 
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDef">
    update ATR_CONF_QUOTA_DEF
    <set>
      <if test="quotaCode != null">
        QUOTA_CODE = #{quotaCode,jdbcType=VARCHAR},
      </if>
      <if test="quotaCName != null">
        QUOTA_C_NAME = #{quotaCName,jdbcType=VARCHAR},
      </if>
      <if test="quotaLName != null">
        QUOTA_L_NAME = #{quotaLName,jdbcType=VARCHAR},
      </if>
      <if test="quotaEName != null">
        QUOTA_E_NAME = #{quotaEName,jdbcType=VARCHAR},
      </if>
      <if test="codeType != null">
        CODE_TYPE = #{codeType,jdbcType=VARCHAR},
      </if>
      <if test="quotaClass != null">
         QUOTA_CLASS = #{quotaClass,jdbcType=VARCHAR},
      </if>
      <if test="quotaGroup != null">
        QUOTA_GROUP = #{quotaGroup,jdbcType=VARCHAR},
      </if>
      <if test="quotaType != null">
        QUOTA_TYPE = #{quotaType,jdbcType=VARCHAR},
      </if>
      <if test="quotaValueType != null">
        QUOTA_VALUE_TYPE = #{quotaValueType,jdbcType=VARCHAR},
      </if>
      <if test="dimension != null">
        dimension = #{dimension,jdbcType=VARCHAR},
      </if>
      <if test="ruleId != null">
        RULE_ID = #{ruleId,jdbcType=DECIMAL},
      </if>
      <if test="percentHundredIs != null">
        PERCENT_HUNDRED_IS = #{percentHundredIs,jdbcType=VARCHAR},
      </if>
      <if test="displayNo != null">
        DISPLAY_NO = #{displayNo,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        SERIAL_NO = #{serialNo,jdbcType=DECIMAL},
      </if>
      <if test="validIs != null">
        VALID_IS = #{validIs,jdbcType=VARCHAR},
      </if>
      <if test="quotaEffect != null">
        QUOTA_EFFECT = #{quotaEffect,jdbcType=VARCHAR},
      </if>
      <if test="auditState != null">
        AUDIT_STATE = #{auditState,jdbcType=VARCHAR},
      </if>
      <if test="checkedMsg != null">
        CHECKED_MSG = #{checkedMsg,jdbcType=VARCHAR},
      </if>
      <if test="checkedId != null">
        CHECKED_ID = #{checkedId,jdbcType=DECIMAL},
      </if>
      <if test="checkedTime != null">
        CHECKED_TIME = #{checkedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        CREATOR_ID = #{creatorId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        UPDATOR_ID = #{updatorId,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="autoCalculatedIs != null">
        AUTO_CALCULATED_IS = #{autoCalculatedIs,jdbcType=VARCHAR},
      </if>
    </set>
    where QUOTA_DEF_ID = #{quotaDefId,jdbcType=DECIMAL}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDef">
    update ATR_CONF_QUOTA_DEF
    <set>
      <if test="record.quotaCode != null">
        QUOTA_CODE = #{record.quotaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.quotaCName != null">
        QUOTA_C_NAME = #{record.quotaCName,jdbcType=VARCHAR},
      </if>
      <if test="record.quotaLName != null">
        QUOTA_L_NAME = #{record.quotaLName,jdbcType=VARCHAR},
      </if>
      <if test="record.quotaEName != null">
        QUOTA_E_NAME = #{record.quotaEName,jdbcType=VARCHAR},
      </if>
      <if test="record.codeType != null">
        CODE_TYPE = #{record.codeType,jdbcType=VARCHAR},
      </if>
      <if test="record.quotaClass != null">
        QUOTA_CLASS = #{record.quotaClass,jdbcType=VARCHAR},
      </if>
      <if test="record.quotaGroup != null">
        QUOTA_GROUP = #{record.quotaGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.quotaType != null">
        QUOTA_TYPE = #{record.quotaType,jdbcType=VARCHAR},
      </if>
      <if test="record.quotaValueType != null">
        QUOTA_VALUE_TYPE = #{record.quotaValueType,jdbcType=VARCHAR},
      </if>
      <if test="record.dimension != null">
        dimension = #{record.dimension,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleId != null">
        RULE_ID = #{record.ruleId,jdbcType=DECIMAL},
      </if>
      <if test="record.percentHundredIs != null">
        PERCENT_HUNDRED_IS = #{record.percentHundredIs,jdbcType=VARCHAR},
      </if>
      <if test="record.displayNo != null">
        DISPLAY_NO = #{record.displayNo,jdbcType=DECIMAL},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.serialNo != null">
        SERIAL_NO = #{record.serialNo,jdbcType=DECIMAL},
      </if>
      <if test="record.validIs != null">
        VALID_IS = #{record.validIs,jdbcType=VARCHAR},
      </if>
      <if test="record.quotaEffect != null">
        QUOTA_EFFECT = #{record.quotaEffect,jdbcType=VARCHAR},
      </if>
      <if test="record.auditState != null">
        AUDIT_STATE = #{record.auditState,jdbcType=VARCHAR},
      </if>
      <if test="record.checkedMsg != null">
        CHECKED_MSG = #{record.checkedMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.checkedId != null">
        CHECKED_ID = #{record.checkedId,jdbcType=DECIMAL},
      </if>
      <if test="record.checkedTime != null">
        CHECKED_TIME = #{record.checkedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creatorId != null">
        CREATOR_ID = #{record.creatorId,jdbcType=DECIMAL},
      </if>
      <if test="record.createTime != null">
        CREATE_TIME = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatorId != null">
        UPDATOR_ID = #{record.updatorId,jdbcType=DECIMAL},
      </if>
      <if test="record.updateTime != null">
        UPDATE_TIME = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.autoCalculatedIs != null">
        AUTO_CALCULATED_IS = #{record.autoCalculatedIs,jdbcType=VARCHAR},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from ATR_CONF_QUOTA_DEF
    where QUOTA_DEF_ID = #{quotaDefId,jdbcType=DECIMAL}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from ATR_CONF_QUOTA_DEF
    where QUOTA_DEF_ID in
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from ATR_CONF_QUOTA_DEF
    where
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDef">
    select count(1) from ATR_CONF_QUOTA_DEF
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>