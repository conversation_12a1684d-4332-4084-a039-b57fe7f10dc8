/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2022-08-31 22:54:30
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2022-08-31 22:54:30<br/>
 * Description: 计量平台业务年月业务线详情表<br/>
 * Table Name: ATR_CONF_BUSSPERIOD_DETAIL_LOA<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "计量平台业务年月业务线详情表")
public class AtrConfBussPeriodDetailLoaVo implements Serializable {
    /**
     * Database column: ATR_CONF_BUSSPERIOD_DETAIL_LOA.PERIOD_DETAIL_LOA_ID
     * Database remarks: period_detail_id|主键id
     */
    @ApiModelProperty(value = "period_detail_id|主键id", required = true)
    private Long periodDetailLoaId;

    /**
     * Database column: ATR_CONF_BUSSPERIOD_DETAIL_LOA.PERIOD_DETAIL_ID
     * Database remarks: period_detail_id|关联业务期间任务id
     */
    @ApiModelProperty(value = "period_detail_id|关联业务期间任务id", required = true)
    private Long periodDetailId;

    /**
     * Database column: ATR_CONF_BUSSPERIOD_DETAIL_LOA.EVALUATE_APPROACH
     * Database remarks: evaluate_approach|评估方法
     */
    @ApiModelProperty(value = "evaluate_approach|评估方法", required = true)
    private String evaluateApproach;

    /**
     * Database column: ATR_CONF_BUSSPERIOD_DETAIL_LOA.LOA_CODE
     * Database remarks: loa_code|关联业务线
     */
    @ApiModelProperty(value = "loa_code|关联业务线", required = true)
    private String loaCode;

    /**
     * Database column: ATR_CONF_BUSSPERIOD_DETAIL_LOA.TASK_TIME
     * Database remarks: task_time|任务执行时间
     */
    @ApiModelProperty(value = "task_time|任务执行时间", required = false)
    private Date taskTime;

    /**
     * Database column: ATR_CONF_BUSSPERIOD_DETAIL_LOA.READY_STATE
     * Database remarks: ready_state|准备状态
     */
    @ApiModelProperty(value = "ready_state|准备状态", required = false)
    private String readyState;

    /**
     * Database column: ATR_CONF_BUSSPERIOD_DETAIL_LOA.CREATOR_ID
     * Database remarks: creator_id|创建人id
     */
    @ApiModelProperty(value = "creator_id|创建人id", required = false)
    private Long creatorId;

    /**
     * Database column: ATR_CONF_BUSSPERIOD_DETAIL_LOA.CREATE_TIME
     * Database remarks: create_Time|创建时间
     */
    @ApiModelProperty(value = "create_Time|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: ATR_CONF_BUSSPERIOD_DETAIL_LOA.UPDATOR_ID
     * Database remarks: updator_id|最后更新人id
     */
    @ApiModelProperty(value = "updator_id|最后更新人id", required = false)
    private Long updatorId;

    /**
     * Database column: ATR_CONF_BUSSPERIOD_DETAIL_LOA.UPDATE_TIME
     * Database remarks: update_Time|最后更新时间
     */
    @ApiModelProperty(value = "update_Time|最后更新时间", required = false)
    private Date updateTime;

    private String loaCName;
    private String loaEName;
    private String loaLName;

    private static final long serialVersionUID = 1L;

    public Long getPeriodDetailLoaId() {
        return periodDetailLoaId;
    }

    public void setPeriodDetailLoaId(Long periodDetailLoaId) {
        this.periodDetailLoaId = periodDetailLoaId;
    }

    public Long getPeriodDetailId() {
        return periodDetailId;
    }

    public void setPeriodDetailId(Long periodDetailId) {
        this.periodDetailId = periodDetailId;
    }

    public String getEvaluateApproach() {
        return evaluateApproach;
    }

    public void setEvaluateApproach(String evaluateApproach) {
        this.evaluateApproach = evaluateApproach;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public Date getTaskTime() {
        return taskTime;
    }

    public void setTaskTime(Date taskTime) {
        this.taskTime = taskTime;
    }

    public String getReadyState() {
        return readyState;
    }

    public void setReadyState(String readyState) {
        this.readyState = readyState;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getLoaCName() {
        return loaCName;
    }

    public void setLoaCName(String loaCName) {
        this.loaCName = loaCName;
    }

    public String getLoaEName() {
        return loaEName;
    }

    public void setLoaEName(String loaEName) {
        this.loaEName = loaEName;
    }

    public String getLoaLName() {
        return loaLName;
    }

    public void setLoaLName(String loaLName) {
        this.loaLName = loaLName;
    }
}