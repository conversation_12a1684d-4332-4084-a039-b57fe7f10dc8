<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-06-05 13:55:06 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.reserve.AtrBussReserveEarnedDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveEarned">
    <result column="RESERVE_EARNED_ID" property="reserveEarnedId" jdbcType="DECIMAL" />
    <result column="VERSION_NO" property="versionNo" jdbcType="VARCHAR" />
    <result column="entity_id" property="entityId" jdbcType="DECIMAL" />
    <result column="START_DATE" property="startDate" jdbcType="TIMESTAMP" />
    <result column="END_DATE" property="endDate" jdbcType="TIMESTAMP" />
    <result column="risk_class_code" property="riskClassCode" jdbcType="VARCHAR" />
    <result column="RISK_CODE" property="riskCode" jdbcType="VARCHAR" />
    <result column="DATA_SOURCE" property="dataSource" jdbcType="CHAR" />
    <result column="ATR_TYPE" property="atrType" jdbcType="CHAR" />
    <result column="CONFIRM_IS" property="confirmIs" jdbcType="CHAR" />
    <result column="CONFIRM_ID" property="confirmId" jdbcType="DECIMAL" />
    <result column="CONFIRM_TIME" property="confirmTime" jdbcType="TIMESTAMP" />
    <result column="CREATOR_ID" property="creatorId" jdbcType="DECIMAL" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATOR_ID" property="updatorId" jdbcType="DECIMAL" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    RESERVE_EARNED_ID, VERSION_NO, entity_id, START_DATE, END_DATE, risk_class_code, RISK_CODE,
    DATA_SOURCE, ATR_TYPE, CONFIRM_IS, CONFIRM_ID, CONFIRM_TIME, CREATOR_ID, CREATE_TIME, 
    UPDATOR_ID, UPDATE_TIME
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="reserveEarnedId != null ">
          and RESERVE_EARNED_ID = #{reserveEarnedId,jdbcType=DECIMAL}
      </if>
      <if test="versionNo != null and versionNo != ''">
          and VERSION_NO = #{versionNo,jdbcType=VARCHAR}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="startDate != null ">
          and START_DATE = #{startDate,jdbcType=TIMESTAMP}
      </if>
      <if test="endDate != null ">
          and END_DATE = #{endDate,jdbcType=TIMESTAMP}
      </if>
      <if test="riskClassCode != null and riskClassCode != ''">
          and risk_class_code = #{riskClassCode,jdbcType=VARCHAR}
      </if>
      <if test="riskCode != null and riskCode != ''">
          and RISK_CODE = #{riskCode,jdbcType=VARCHAR}
      </if>
      <if test="dataSource != null ">
          and DATA_SOURCE = #{dataSource,jdbcType=CHAR}
      </if>
      <if test="atrType != null ">
          and ATR_TYPE = #{atrType,jdbcType=CHAR}
      </if>
      <if test="confirmIs != null ">
          and CONFIRM_IS = #{confirmIs,jdbcType=CHAR}
      </if>
      <if test="confirmId != null ">
          and CONFIRM_ID = #{confirmId,jdbcType=DECIMAL}
      </if>
      <if test="confirmTime != null ">
          and CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creatorId != null ">
          and CREATOR_ID = #{creatorId,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null ">
          and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and UPDATOR_ID = #{updatorId,jdbcType=DECIMAL}
      </if>
      <if test="updateTime != null ">
          and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.reserveEarnedId != null ">
          and RESERVE_EARNED_ID = #{condition.reserveEarnedId,jdbcType=DECIMAL}
      </if>
      <if test="condition.versionNo != null and condition.versionNo != ''">
          and VERSION_NO = #{condition.versionNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.entityId != null ">
          and entity_id = #{condition.entityId,jdbcType=DECIMAL}
      </if>
      <if test="condition.startDate != null ">
          and START_DATE = #{condition.startDate,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.endDate != null ">
          and END_DATE = #{condition.endDate,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.riskClassCode != null and condition.riskClassCode != ''">
          and risk_class_code = #{condition.riskClassCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.riskCode != null and condition.riskCode != ''">
          and RISK_CODE = #{condition.riskCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.dataSource != null ">
          and DATA_SOURCE = #{condition.dataSource,jdbcType=CHAR}
      </if>
      <if test="condition.atrType != null ">
          and ATR_TYPE = #{condition.atrType,jdbcType=CHAR}
      </if>
      <if test="condition.confirmIs != null ">
          and CONFIRM_IS = #{condition.confirmIs,jdbcType=CHAR}
      </if>
      <if test="condition.confirmId != null ">
          and CONFIRM_ID = #{condition.confirmId,jdbcType=DECIMAL}
      </if>
      <if test="condition.confirmTime != null ">
          and CONFIRM_TIME = #{condition.confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.creatorId != null ">
          and CREATOR_ID = #{condition.creatorId,jdbcType=DECIMAL}
      </if>
      <if test="condition.createTime != null ">
          and CREATE_TIME = #{condition.createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.updatorId != null ">
          and UPDATOR_ID = #{condition.updatorId,jdbcType=DECIMAL}
      </if>
      <if test="condition.updateTime != null ">
          and UPDATE_TIME = #{condition.updateTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="reserveEarnedId != null ">
          and RESERVE_EARNED_ID = #{reserveEarnedId,jdbcType=DECIMAL}
      </if>
      <if test="versionNo != null and versionNo != ''">
          and VERSION_NO = #{versionNo,jdbcType=VARCHAR}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="startDate != null ">
          and START_DATE = #{startDate,jdbcType=TIMESTAMP}
      </if>
      <if test="endDate != null ">
          and END_DATE = #{endDate,jdbcType=TIMESTAMP}
      </if>
      <if test="riskClassCode != null and riskClassCode != ''">
          and risk_class_code = #{riskClassCode,jdbcType=VARCHAR}
      </if>
      <if test="riskCode != null and riskCode != ''">
          and RISK_CODE = #{riskCode,jdbcType=VARCHAR}
      </if>
      <if test="dataSource != null ">
          and DATA_SOURCE = #{dataSource,jdbcType=CHAR}
      </if>
      <if test="atrType != null ">
          and ATR_TYPE = #{atrType,jdbcType=CHAR}
      </if>
      <if test="confirmIs != null ">
          and CONFIRM_IS = #{confirmIs,jdbcType=CHAR}
      </if>
      <if test="confirmId != null ">
          and CONFIRM_ID = #{confirmId,jdbcType=DECIMAL}
      </if>
      <if test="confirmTime != null ">
          and CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creatorId != null ">
          and CREATOR_ID = #{creatorId,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null ">
          and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and UPDATOR_ID = #{updatorId,jdbcType=DECIMAL}
      </if>
      <if test="updateTime != null ">
          and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_RESERVE_EARNED
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveEarned">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_RESERVE_EARNED
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_RESERVE_EARNED
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveEarned">
    <selectKey resultType="long" keyProperty="reserveEarnedId" order="BEFORE">
      select nextval('atr_seq_buss_reserve_earned') as sequenceNo
    </selectKey>
    insert into ATR_BUSS_RESERVE_EARNED
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="reserveEarnedId != null">
        RESERVE_EARNED_ID,
      </if>
      <if test="versionNo != null">
        VERSION_NO,
      </if>
      <if test="entityId != null">
        entity_id,
      </if>
      <if test="startDate != null">
        START_DATE,
      </if>
      <if test="endDate != null">
        END_DATE,
      </if>
      <if test="riskClassCode != null">
        risk_class_code,
      </if>
      <if test="riskCode != null">
        RISK_CODE,
      </if>
      <if test="dataSource != null">
        DATA_SOURCE,
      </if>
      <if test="atrType != null">
        ATR_TYPE,
      </if>
      <if test="confirmIs != null">
        CONFIRM_IS,
      </if>
      <if test="confirmId != null">
        CONFIRM_ID,
      </if>
      <if test="confirmTime != null">
        CONFIRM_TIME,
      </if>
      <if test="creatorId != null">
        CREATOR_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updatorId != null">
        UPDATOR_ID,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="reserveEarnedId != null">
        #{reserveEarnedId,jdbcType=DECIMAL},
      </if>
      <if test="versionNo != null">
        #{versionNo,jdbcType=VARCHAR},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=DECIMAL},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="riskClassCode != null">
        #{riskClassCode,jdbcType=VARCHAR},
      </if>
      <if test="riskCode != null">
        #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="dataSource != null">
        #{dataSource,jdbcType=CHAR},
      </if>
      <if test="atrType != null">
        #{atrType,jdbcType=CHAR},
      </if>
      <if test="confirmIs != null">
        #{confirmIs,jdbcType=CHAR},
      </if>
      <if test="confirmId != null">
        #{confirmId,jdbcType=DECIMAL},
      </if>
      <if test="confirmTime != null">
        #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        #{updatorId,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all 
    <foreach collection="list" item="item" index="index">
      into ATR_BUSS_RESERVE_EARNED values 
       (#{item.reserveEarnedId,jdbcType=DECIMAL}, 
        #{item.versionNo,jdbcType=VARCHAR}, #{item.entityId,jdbcType=DECIMAL}, #{item.startDate,jdbcType=TIMESTAMP},
        #{item.endDate,jdbcType=TIMESTAMP}, #{item.riskClassCode,jdbcType=VARCHAR}, #{item.riskCode,jdbcType=VARCHAR},
        #{item.dataSource,jdbcType=CHAR}, #{item.atrType,jdbcType=CHAR}, #{item.confirmIs,jdbcType=CHAR}, 
        #{item.confirmId,jdbcType=DECIMAL}, #{item.confirmTime,jdbcType=TIMESTAMP}, #{item.creatorId,jdbcType=DECIMAL}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updatorId,jdbcType=DECIMAL}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
    select 1 from dual
  </insert>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveEarned">
    update ATR_BUSS_RESERVE_EARNED
    <set>
      <if test="record.reserveEarnedId != null">
        RESERVE_EARNED_ID = #{record.reserveEarnedId,jdbcType=DECIMAL},
      </if>
      <if test="record.versionNo != null">
        VERSION_NO = #{record.versionNo,jdbcType=VARCHAR},
      </if>
      <if test="record.entityId != null">
        entity_id = #{record.entityId,jdbcType=DECIMAL},
      </if>
      <if test="record.startDate != null">
        START_DATE = #{record.startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endDate != null">
        END_DATE = #{record.endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.riskClassCode != null">
        risk_class_code = #{record.riskClassCode,jdbcType=VARCHAR},
      </if>
      <if test="record.riskCode != null">
        RISK_CODE = #{record.riskCode,jdbcType=VARCHAR},
      </if>
      <if test="record.dataSource != null">
        DATA_SOURCE = #{record.dataSource,jdbcType=CHAR},
      </if>
      <if test="record.atrType != null">
        ATR_TYPE = #{record.atrType,jdbcType=CHAR},
      </if>
      <if test="record.confirmIs != null">
        CONFIRM_IS = #{record.confirmIs,jdbcType=CHAR},
      </if>
      <if test="record.confirmId != null">
        CONFIRM_ID = #{record.confirmId,jdbcType=DECIMAL},
      </if>
      <if test="record.confirmTime != null">
        CONFIRM_TIME = #{record.confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creatorId != null">
        CREATOR_ID = #{record.creatorId,jdbcType=DECIMAL},
      </if>
      <if test="record.createTime != null">
        CREATE_TIME = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatorId != null">
        UPDATOR_ID = #{record.updatorId,jdbcType=DECIMAL},
      </if>
      <if test="record.updateTime != null">
        UPDATE_TIME = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from ATR_BUSS_RESERVE_EARNED
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveEarned">
    select count(1) from ATR_BUSS_RESERVE_EARNED
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List" />
    from ATR_BUSS_RESERVE_EARNED
    where reserve_earned_id = #{reserveEarnedId,jdbcType=NUMERIC}
  </select>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveUpr">
    update ATR_BUSS_RESERVE_EARNED
    <set>
      <if test="reserveEarnedId != null">
        RESERVE_EARNED_ID = #{reserveEarnedId,jdbcType=DECIMAL},
      </if>
      <if test="versionNo != null">
        VERSION_NO = #{versionNo,jdbcType=VARCHAR},
      </if>
      <if test="entityId != null">
        entity_id = #{entityId,jdbcType=DECIMAL},
      </if>
      <if test="startDate != null">
        START_DATE = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        END_DATE = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="riskClassCode != null">
        risk_class_code = #{riskClassCode,jdbcType=VARCHAR},
      </if>
      <if test="riskCode != null">
        RISK_CODE = #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="dataSource != null">
        DATA_SOURCE = #{dataSource,jdbcType=CHAR},
      </if>
      <if test="atrType != null">
        ATR_TYPE = #{atrType,jdbcType=CHAR},
      </if>
      <if test="confirmIs != null">
        CONFIRM_IS = #{confirmIs,jdbcType=CHAR},
      </if>
      <if test="confirmId != null">
        CONFIRM_ID = #{confirmId,jdbcType=DECIMAL},
      </if>
      <if test="confirmTime != null">
        CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        CREATOR_ID = #{creatorId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        UPDATOR_ID = #{updatorId,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where reserve_earned_id = #{reserveEarnedId,jdbcType=NUMERIC}
  </update>
</mapper>