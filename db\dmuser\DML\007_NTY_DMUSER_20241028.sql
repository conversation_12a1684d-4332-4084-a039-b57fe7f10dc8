INSERT INTO "dmuser"."dm_conf_checkrule" ("config_rule_id", "rule_code", "rule_e_name", "rule_l_name", "rule_c_name", "rule_config", "rule_version", "valid_date", "invalid_date", "col_id", "opt_type", "match_value", "rule_sql", "valid_is", "create_time", "creator_id", "update_time", "updator_id", "rule_direction", "rule_type_code", "original_is", "biz_type_id", "remark", "check_type", "display_no", "serial_no", "checked_msg", "audit_state", "checked_time", "checked_id", "apply_model", "rule_type") VALUES (nextval('dm_seq_conf_checkrule'), 'REINS_BILL_STATEMENT_APPROVAL_DATE_VALID_IS', 'Statement Approval Date Validity Inspection', '帳單確認日期有效性校驗', '账单确认日期有效性校验', NULL, '1.00', '2024-10-28 00:00:00', '9999-12-31 00:00:00', NULL, '0', NULL, 'select id from odsuser.ods_reins_bill x
where exists (
	select 1 from odsuser.ods_reins_bill t
	left join dmuser.dm_reins_treaty y on t.treaty_no = y.treaty_no
	where t.statement_approval_date < y.effective_date
	and x.id = t.id
)',
'1', '2024-10-28 11:46:30', NULL, '2024-10-28 11:46:30', NULL, '0', '1', '1',
(select biz_type_id from dm_conf_table where biz_code = 'REINS_BILL'),
'账单确认日期不能早于合约的生效日期', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2');