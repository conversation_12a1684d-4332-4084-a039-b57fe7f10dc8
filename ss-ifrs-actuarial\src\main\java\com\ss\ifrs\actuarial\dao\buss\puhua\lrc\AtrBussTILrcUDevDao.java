/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2023-03-08 10:07:11
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao.buss.puhua.lrc;

import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussTILrcIcuCalcDetail;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo;
import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrBussBecfViewVo;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2023-03-08 10:07:11<br/>
 * Description: LRC 计算结果明细(单维度，合约分入) Dao类<br/>
 * Related Table Name: ATR_BUSS_TI_LRC_ICU_CALC_DETAIL<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrBussTILrcUDevDao extends IDao<AtrBussTILrcIcuCalcDetail, Long> {

    List<Integer> findByVo(AtrDapDrawVo atrDapDrawVo);

    List<Integer> findByDevVo(AtrBussBecfViewVo atrBussBecfViewVo);
}