package com.ss.ifrs.actuarial.dao;

import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.AtrBussToLrcTUlR;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.AtrBussToLrcIcgPremium;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.AtrBussToLrcTUl;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.AtrDapTreaty;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface AtrBussLrcToDao {


    long getIcgMaxMainId();


    long getUltMaxMainId();
    
    /**
     * 获取R表及其发展期表的最大ID
     * @return 最大ID值
     */
    long getRTableMaxMainId();


    void truncateBaseDataT();


    void partitionBaseDataT(Map<String, Object> commonParamMap);


    List<AtrBussToLrcTUlR> listBasePolicyT();

    // 查找上一期的
    List<AtrBussToLrcTUl> listPreBussToLrcTUl(Map<String, Object> paramMap);

    // 查找上一期的详细维度数据（包含sectionoCode和reinsurerCode）
    List<AtrBussToLrcTUlR> listPreBussToLrcTUlRDetailed(Map<String, Object> paramMap);

    List<AtrDapTreaty> listDapTreaty(String yearMonth);

    List<AtrBussToLrcIcgPremium> listPreBussToLrcIcgPremium(Map<String, Object> paramMap);
    
    /**
     * 清空转换已赚保费临时表
     */
    void truncateTransitionTempTable();
    
    /**
     * 向临时表中插入需要计算的保单数据
     * 
     * @param paramMap 参数Map
     */
    void insertTransitionTempData(Map<String, Object> paramMap);
    
    /**
     * 获取指定分区的过渡期已赚保费计算基础数据
     * 
     * @param paramMap 参数Map
     * @return 保单数据列表
     */
    List<AtrBussToLrcTUlR> getTransitionPartBaseVos(Map<String, Object> paramMap);
    
    /**
     * 创建atr_dap_to_paid_t表的备份表
     */
    void createPaidBackupTable();

    /**
     * 从atr_dap_to_paid_t表删除符合条件的数据
     * 只有当一个treaty_no下的所有policy_no都在清理列表中时，
     * 才会删除该treaty_no下的所有数据
     * 
     * @return 删除的记录数
     */
    void deletePaidData();

    /**
     * 判断过渡期已赚保费表中是否存在指定机构的数据
     *
     * @param entityId 机构ID
     * @return 存在返回true，不存在返回false
     */
    boolean hasTransitionData(Long entityId);

    /**
     * 获取指定分区的保单数据，用于分批处理
     *
     * @param paramMap 参数Map，包含pn分区号
     * @return 保单数据列表
     */
    List<AtrBussToLrcTUlR> getPartPolicyData(Map<String, Object> paramMap);

    /**
     * 获取指定分区的上期数据，使用JOIN优化查询
     *
     * @param paramMap 参数Map，包含pn分区号、lastYearMonth、lastActionNo
     * @return 上期数据列表
     */
    List<AtrBussToLrcTUl> getPartPrePolicyData(Map<String, Object> paramMap);

    /**
     * 获取指定分区的上期详细数据，使用JOIN优化查询
     *
     * @param paramMap 参数Map，包含pn分区号、lastYearMonth、lastActionNo
     * @return 上期详细数据列表
     */
    List<AtrBussToLrcTUlR> getPartPrePolicyDetailedData(Map<String, Object> paramMap);
}