CREATE OR REPLACE PACKAGE dm_pack_buss_period IS

  FUNCTION func_get_period_detail_state(p_entity_id NUMBER,
                                        p_year_month VARCHAR2,
                                        p_state VARCHAR2) RETURN NUMBER;

  PROCEDURE proc_period_execution(p_entity_id NUMBER, p_state VARCHAR2);


  FUNCTION func_get_current_year_month(p_entity_id  NUMBER,
                                       p_year_month VARCHAR2,
                                       p_biz_code VARCHAR2) RETURN VARCHAR2;

  FUNCTION func_get_preparing_year_month(p_entity_id  NUMBER,
                                         p_year_month VARCHAR2,
                                         p_biz_code VARCHAR2) RETURN VARCHAR2;

  FUNCTION func_get_valid_year_month(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2,
                                     p_biz_code VARCHAR2) RETURN VARCHAR2;

  PROCEDURE proc_preparing_execution(p_entity_id NUMBER,
                                     p_year_month VARCHAR2,
                                     p_biz_type_id VARCHAR2,
                                     p_ready_state VARCHAR2);

END dm_pack_buss_period;
/
create or replace package body dm_pack_buss_period is

  FUNCTION func_get_period_detail_state(p_entity_id NUMBER,
                                        p_year_month VARCHAR2,
                                        p_state VARCHAR2)
    RETURN NUMBER IS
    v_count NUMBER;
    v_error_msg            VARCHAR2(2000);
  BEGIN

    --新增业务期间明细
    IF p_state = '0' THEN

      --无在准备中的输入,确保只有一个准备中业务期间
      SELECT COUNT(period_detail_id)
        INTO v_count
        FROM dm_conf_bussperiod_detail bpd
        LEFT JOIN dm_conf_bussperiod bp
          ON bp.buss_period_id = bpd.buss_period_id
       WHERE bpd.direction = '1'
         AND bpd.ready_state = '0'
         AND bp.entity_id = p_entity_id
         AND bp.year_month = p_year_month;

      --无待准备数据
      IF v_count = 0 THEN
        RETURN 1;
      ELSE
        RETURN 0;
      END IF;

      --RETURN 1;

    --业务期间己准备，所有输入数据己准备
    ELSIF p_state = '1' THEN
      SELECT COUNT(period_detail_id)
        INTO v_count
        FROM dm_conf_bussperiod_detail bpd
        LEFT JOIN dm_conf_bussperiod bp
          ON bp.buss_period_id = bpd.buss_period_id
       WHERE bpd.direction = '1'
         AND bpd.ready_state = '0'
         AND bp.entity_id = p_entity_id
         AND bp.year_month = p_year_month;

      --无待准备数据
      IF v_count = 0 THEN
        RETURN 1;
      ELSE
        RETURN 0;
      END IF;

    --业务期间处理中，所有输入数据己准备
    ELSIF p_state = '2' THEN
      SELECT COUNT(period_detail_id)
        INTO v_count
        FROM dm_conf_bussperiod_detail bpd
        LEFT JOIN dm_conf_bussperiod bp
          ON bp.buss_period_id = bpd.buss_period_id
       WHERE bpd.direction = '1'
         AND bpd.ready_state = '0'
         AND bp.entity_id = p_entity_id
         AND bp.year_month = p_year_month;

      --无待准备数据
      IF v_count = 0 THEN
        RETURN 1;
      ELSE
        RETURN 0;
      END IF;

    --业务期间已完成，所有输出数据己准备
    ELSIF p_state = '3' THEN
      SELECT COUNT(period_detail_id)
        INTO v_count
        FROM dm_conf_bussperiod_detail bpd
        LEFT JOIN dm_conf_bussperiod bp
          ON bp.buss_period_id = bpd.buss_period_id
       WHERE bpd.direction = '0'
         AND bpd.ready_state = '0'
         AND bp.entity_id = p_entity_id
         AND bp.year_month = p_year_month;

      --无待准备数据
      IF v_count = 0 THEN
        RETURN 1;
      ELSE
        RETURN 0;
      END IF;
    END IF;

    RETURN 0;

  EXCEPTION
    WHEN others THEN
      --意外处理
      v_error_msg := '[EXCEPTION][业务期间]业务期间状态处理:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);
    RETURN 0;
  END func_get_period_detail_state;

  PROCEDURE proc_period_execution(p_entity_id NUMBER, p_state VARCHAR2) is

    v_year_month VARCHAR2 (200);
    v_next_year_month VARCHAR2 (200);
    v_detail_reday_state NUMBER;
    v_count NUMBER;
    v_error_msg            VARCHAR2(2000);
  BEGIN

    --新增业务期间
    IF p_state = '0' THEN
      --检验是否存在准备中的业务期间(有且仅有一个在准备中的业务期间)
      SELECT COUNT(year_month)
        INTO v_count
        FROM dm_conf_bussperiod
       WHERE entity_id = p_entity_id
         AND period_state = '0';

      IF v_count=0 THEN
        --增加一个新的业务期间
        SELECT to_char(add_months(to_date(max(year_month), 'yyyymm'), 1), 'YYYYMM')
          INTO v_next_year_month
          FROM dm_conf_bussperiod
         WHERE entity_id = p_entity_id;

        --初始数据按当前时间处理
        IF v_next_year_month IS NULL THEN
          v_next_year_month := to_char(sysdate,'YYYYMM');
        END IF;

        INSERT INTO dm_conf_bussperiod
          (buss_period_id,
           entity_id,
           year_month,
           period_state,
           valid_is,
           creator_id,
           create_time
          )
        VALUES
         (dm_seq_conf_bussperiod.nextval,
          p_entity_id,
          v_next_year_month,
          '0',
          '1',
          '1',
          sysdate
          );

        --增加准备中的输入输出明细数据
        INSERT INTO dm_conf_bussperiod_detail
          (period_detail_id,
           buss_period_id,
           biz_type_id,
           direction,
           ready_state,
           creator_id,
           create_time)
        SELECT dm_seq_conf_bussperiod_detail.nextval,
               a.buss_period_id,
               biz_type_id,
               direction,
               ready_state,
               1 AS creator_id,
               SYSDATE AS create_time
          FROM (SELECT bp.buss_period_id,
                       ct.biz_type_id,
                       '1' direction, --输入
                       '0' ready_state--,
                       --bp.creator_id
                  FROM dm_conf_table ct
                  LEFT JOIN dm_conf_bussperiod bp
                    ON bp.entity_id = p_entity_id
                   AND bp.year_month = v_next_year_month
                 WHERE ct.valid_is = '1'
                UNION
                SELECT bp.buss_period_id,
                       ct.biz_type_id,
                       '0' direction, --输出
                       '0' ready_state--,
                       --bp.creator_id
                  FROM dm_conf_table_output ct
                  LEFT JOIN dm_conf_bussperiod bp
                    ON bp.entity_id = p_entity_id
                   AND bp.year_month = v_next_year_month
                 WHERE ct.valid_is = '1'
            ) a;
        COMMIT;

      END IF;
    --业务期间己准备
    ELSIF p_state = '1' THEN
      --检验是否存在准备中业务期间
      SELECT COUNT(year_month)
        INTO v_count
        FROM dm_conf_bussperiod
       WHERE entity_id = p_entity_id
         AND period_state = '0';

      IF v_count > 0 THEN

        SELECT MIN(year_month)
          INTO v_year_month
          FROM dm_conf_bussperiod
         WHERE entity_id = p_entity_id
           AND period_state = '0';

        --验证输入明细数据状态是否己准备
        v_detail_reday_state := func_get_period_detail_state(p_entity_id, v_year_month, p_state);

        IF v_detail_reday_state = 1 THEN
          --修改当前业务期间状态为己准备
          UPDATE dm_conf_bussperiod
            SET period_state = '1',
                updator_id = 1,
                update_time = sysdate
          WHERE entity_id = p_entity_id
            AND year_month = v_year_month;

          COMMIT;

          --增加下个准备中业务期间
          proc_period_execution(p_entity_id, '0');

        END IF;
      END IF;

      --确保有下个处理中业务期间
      proc_period_execution(p_entity_id, '2');

    --业务期间处理中
    ELSIF p_state = '2' THEN
      --检查在处理中的业务期间

      SELECT COUNT(year_month)
        INTO v_count
        FROM dm_conf_bussperiod
       WHERE entity_id = p_entity_id
         AND period_state = '2';

      --在无处理中的业务期间,取下一个已准备业务期间
      IF v_count = 0 THEN

        SELECT MIN(year_month)
          INTO v_next_year_month
          FROM dm_conf_bussperiod
         WHERE entity_id = p_entity_id
           AND period_state = '1';

        --存在下一个己准备的业务期间,开始处理
        IF v_next_year_month IS NOT NULL THEN

          --验证输入明细数据状态是否己准备，验证成功后执行以下逻辑
          v_detail_reday_state := func_get_period_detail_state(p_entity_id, v_next_year_month, p_state);

          IF v_detail_reday_state = 1 THEN

            --修改当前业务期间状态为处理中
            UPDATE dm_conf_bussperiod
              SET period_state = '2',
                  updator_id = 1,
                  update_time = sysdate
            WHERE entity_id = p_entity_id
              AND year_month = v_next_year_month;
            COMMIT;
          END IF;
        END IF;
      END IF;

    --业务期间已完成
    ELSIF p_state = '3' THEN

      SELECT MIN(year_month)
        INTO v_year_month
        FROM dm_conf_bussperiod
        WHERE entity_id = p_entity_id
        AND period_state = '2';

      IF v_year_month IS NOT NULL THEN

        --验证输出明细数据状态是否己完成，验证成功后执行以下逻辑
        v_detail_reday_state := func_get_period_detail_state(p_entity_id, v_year_month, p_state);

        IF v_detail_reday_state = 1 THEN

          --修改当前业务期间状态为已完成
          UPDATE dm_conf_bussperiod
            SET period_state = '3',
                updator_id = 1,
                update_time = sysdate
          WHERE entity_id = p_entity_id
            AND year_month = v_year_month;

          COMMIT;

          --下一个业务期间
          v_next_year_month :=  TO_CHAR(ADD_MONTHS(to_date(v_year_month,'yyyymm'),1),'YYYYMM');

          --检验下一个业务期间是否存在
          SELECT MIN(year_month)
            INTO v_year_month
            FROM dm_conf_bussperiod
            WHERE entity_id = p_entity_id
              AND year_month = v_next_year_month;

          IF v_year_month IS NULL THEN
            --增加下个准备中业务期间
             proc_period_execution(p_entity_id, '0');
          ELSE
            --修改下个业务期间状态为处理中
             proc_period_execution(p_entity_id, '2');
          END IF;
        END IF;
      END IF;
    END IF;

  EXCEPTION
    WHEN others THEN
      NULL;
        --意外处理
      v_error_msg := '[EXCEPTION][业务期间]业务期间切换:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);
  END proc_period_execution;

  FUNCTION func_get_current_year_month(p_entity_id  NUMBER,
                                       p_year_month VARCHAR2,
                                       p_biz_code VARCHAR2) RETURN VARCHAR2 IS
    v_year_month VARCHAR2(200) := NULL;
    v_count       NUMBER;
    v_error_msg            VARCHAR2(2000);
  BEGIN

    -- 限定只处理 处于处理中的业务年月
    SELECT COUNT(1)
      INTO v_count
    FROM (SELECT p.entity_id,p.year_month
            FROM dm_conf_bussperiod p
            JOIN dm_conf_bussperiod_detail d
              ON d.buss_period_id = p.buss_period_id
            JOIN dm_conf_table t1
              ON t1.biz_type_id = d.biz_type_id
           WHERE p.entity_id = p_entity_id
             AND (p.year_month = p_year_month OR p_year_month IS NULL)
             AND t1.biz_code = p_biz_code
             AND p.period_state = '2' -- 处理中
             AND d.direction = '1'
             AND d.ready_state = '0' -- 处理中
             AND p.valid_is = '1'
          UNION
          SELECT p.entity_id,p.year_month
            FROM dm_conf_bussperiod p
            JOIN dm_conf_bussperiod_detail d
              ON d.buss_period_id = p.buss_period_id
            JOIN dm_conf_table_output t2
              ON t2.biz_type_id = d.biz_type_id
           WHERE p.entity_id = p_entity_id
             AND (p.year_month = p_year_month OR p_year_month IS NULL)
             AND t2.biz_code = p_biz_code
             AND p.period_state = '2' -- 处理中
             AND d.direction = '0'
             AND d.ready_state = '0' -- 处理中
             AND p.valid_is = '1'
       ) t;

    IF v_count >0 THEN

      SELECT MIN(t.year_month)
        INTO v_year_month
      FROM (SELECT p.entity_id,p.year_month
              FROM dm_conf_bussperiod p
              JOIN dm_conf_bussperiod_detail d
                ON d.buss_period_id = p.buss_period_id
              JOIN dm_conf_table t1
                ON t1.biz_type_id = d.biz_type_id
             WHERE p.entity_id = p_entity_id
               AND (p.year_month = p_year_month OR p_year_month IS NULL)
               AND t1.biz_code = p_biz_code
               AND p.period_state = '2' -- 处理中
               AND d.direction = '1'
               AND d.ready_state = '0' -- 处理中
               AND p.valid_is = '1'
            UNION
            SELECT p.entity_id,p.year_month
              FROM dm_conf_bussperiod p
              JOIN dm_conf_bussperiod_detail d
                ON d.buss_period_id = p.buss_period_id
              JOIN dm_conf_table_output t2
                ON t2.biz_type_id = d.biz_type_id
             WHERE p.entity_id = p_entity_id
               AND (p.year_month = p_year_month OR p_year_month IS NULL)
               AND t2.biz_code = p_biz_code
               AND p.period_state = '2' -- 处理中
               AND d.direction = '0'
               AND d.ready_state = '0' -- 处理中
               AND p.valid_is = '1'
         ) t;
    END IF;

    RETURN v_year_month;

  EXCEPTION
    WHEN OTHERS THEN
      --意外处理
      v_error_msg := '[EXCEPTION][业务期间]获取当前处理中业务期间:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);
      RETURN NULL;
  END func_get_current_year_month;


  FUNCTION func_get_preparing_year_month(p_entity_id  NUMBER,
                                         p_year_month VARCHAR2,
                                         p_biz_code VARCHAR2) RETURN VARCHAR2 IS
    v_year_month VARCHAR2(200) := NULL;
    v_count       NUMBER;
    v_error_msg            VARCHAR2(2000);
  BEGIN

    -- 限定处于准备中的业务年月
    SELECT COUNT(1)
      INTO v_count
    FROM (SELECT p.entity_id,p.year_month
            FROM dm_conf_bussperiod p
            JOIN dm_conf_bussperiod_detail d
              ON d.buss_period_id = p.buss_period_id
            JOIN dm_conf_table t1
              ON t1.biz_type_id = d.biz_type_id
           WHERE p.entity_id = p_entity_id
             AND (p.year_month = p_year_month OR p_year_month IS NULL)
             AND (t1.biz_code = p_biz_code OR p_biz_code IS NULL)
             AND p.period_state = '0' -- 准备中
             AND d.direction = '1'
             AND d.ready_state = '0' -- 处理中
             AND p.valid_is = '1'
          UNION
          SELECT p.entity_id,p.year_month
            FROM dm_conf_bussperiod p
            JOIN dm_conf_bussperiod_detail d
              ON d.buss_period_id = p.buss_period_id
            JOIN dm_conf_table_output t2
              ON t2.biz_type_id = d.biz_type_id
           WHERE p.entity_id = p_entity_id
             AND (p.year_month = p_year_month OR p_year_month IS NULL)
             AND (t2.biz_code = p_biz_code OR p_biz_code IS NULL)
             AND p.period_state = '0' -- 准备中
             AND d.direction = '0'
             AND d.ready_state = '0' -- 处理中
             AND p.valid_is = '1'
       ) t;

    IF v_count >0 THEN

      SELECT MIN(t.year_month)
        INTO v_year_month
      FROM (SELECT p.entity_id,p.year_month
              FROM dm_conf_bussperiod p
              JOIN dm_conf_bussperiod_detail d
                ON d.buss_period_id = p.buss_period_id
              JOIN dm_conf_table t1
                ON t1.biz_type_id = d.biz_type_id
             WHERE p.entity_id = p_entity_id
               AND (p.year_month = p_year_month OR p_year_month IS NULL)
               AND (t1.biz_code = p_biz_code OR p_biz_code IS NULL)
               AND p.period_state = '0' -- 准备中
               AND d.direction = '1'
               AND d.ready_state = '0' -- 处理中
               AND p.valid_is = '1'
            UNION
            SELECT p.entity_id,p.year_month
              FROM dm_conf_bussperiod p
              JOIN dm_conf_bussperiod_detail d
                ON d.buss_period_id = p.buss_period_id
              JOIN dm_conf_table_output t2
                ON t2.biz_type_id = d.biz_type_id
             WHERE p.entity_id = p_entity_id
               AND (p.year_month = p_year_month OR p_year_month IS NULL)
               AND (t2.biz_code = p_biz_code OR p_biz_code IS NULL)
               AND p.period_state = '0' -- 准备中
               AND d.direction = '0'
               AND d.ready_state = '0' -- 处理中
               AND p.valid_is = '1'
         ) t;
    END IF;

    RETURN v_year_month;

  EXCEPTION
    WHEN OTHERS THEN
         --意外处理
      v_error_msg := '[EXCEPTION][业务期间]获取当前准备中业务期间:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);
      RETURN NULL;
  END func_get_preparing_year_month;


  FUNCTION func_get_valid_year_month(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2,
                                     p_biz_code VARCHAR2) RETURN VARCHAR2 IS
    v_year_month VARCHAR2(200) := NULL;
    v_count       NUMBER;
    v_error_msg            VARCHAR2(2000);
  BEGIN

    --已完成或确认的业务期间不能再处理
    SELECT COUNT(1)
      INTO v_count
    FROM (SELECT p.entity_id,p.year_month
            FROM dm_conf_bussperiod p
            JOIN dm_conf_bussperiod_detail d
              ON d.buss_period_id = p.buss_period_id
            JOIN dm_conf_table t1
              ON t1.biz_type_id = d.biz_type_id
           WHERE p.entity_id = p_entity_id
             AND (p.year_month = p_year_month OR p_year_month IS NULL)
             AND t1.biz_code = p_biz_code
             AND d.direction = '1'--输入
             AND p.period_state IN ('0','1','2') -- 待处理、已准备、处理中
             --AND ((p.period_state IN ('0','1')) -- 待处理、已准备
                 --OR (p.period_state = '2' AND d.ready_state = '0'))--处理中未确认
             AND p.valid_is = '1'
          UNION
          SELECT p.entity_id,p.year_month
            FROM dm_conf_bussperiod p
            JOIN dm_conf_bussperiod_detail d
              ON d.buss_period_id = p.buss_period_id
            JOIN dm_conf_table_output t2
              ON t2.biz_type_id = d.biz_type_id
           WHERE p.entity_id = p_entity_id
             AND (p.year_month = p_year_month OR p_year_month IS NULL)
             AND t2.biz_code = p_biz_code
             AND d.direction = '0' --输出
             AND ((p.period_state IN ('0','1')) -- 待处理、已准备
                 OR (p.period_state = '2' AND d.ready_state = '0'))--处理中未确认
             AND p.valid_is = '1'
      ) t;

    -- 条件不满足,结束判定
    IF v_count >0 THEN

       SELECT MIN(t.year_month)
         INTO v_year_month
        FROM (SELECT p.entity_id,p.year_month
                FROM dm_conf_bussperiod p
                JOIN dm_conf_bussperiod_detail d
                  ON d.buss_period_id = p.buss_period_id
                JOIN dm_conf_table t1
                  ON t1.biz_type_id = d.biz_type_id
               WHERE p.entity_id = p_entity_id
                 AND (p.year_month = p_year_month OR p_year_month IS NULL)
                 AND t1.biz_code = p_biz_code
                 AND d.direction = '1' --输入
                 AND p.period_state IN ('0','1','2') -- 待处理、已准备、处理中
                 --AND ((p.period_state IN ('0','1')) -- 待处理、已准备
                     --OR (p.period_state = '2' AND d.ready_state = '0'))--处理中未确认
                 AND p.valid_is = '1'
              UNION
              SELECT p.entity_id,p.year_month
                FROM dm_conf_bussperiod p
                JOIN dm_conf_bussperiod_detail d
                  ON d.buss_period_id = p.buss_period_id
                JOIN dm_conf_table_output t2
                  ON t2.biz_type_id = d.biz_type_id
               WHERE p.entity_id = p_entity_id
                 AND (p.year_month = p_year_month OR p_year_month IS NULL)
                 AND t2.biz_code = p_biz_code
                 AND d.direction = '0'--输出
                 AND ((p.period_state IN ('0','1')) -- 待处理、已准备
                     OR (p.period_state = '2' AND d.ready_state = '0'))--处理中未确认
                 AND p.valid_is = '1'
          ) t;
    END IF;

    RETURN v_year_month;

  EXCEPTION
    WHEN OTHERS THEN
      --意外处理
      v_error_msg := '[EXCEPTION][业务期间]获取当前有效业务期间:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);
      RETURN NULL;
  END func_get_valid_year_month;


  PROCEDURE proc_preparing_execution(p_entity_id NUMBER,
                                     p_year_month VARCHAR2,
                                     p_biz_type_id VARCHAR2,
                                     p_ready_state VARCHAR2) is
    v_year_month VARCHAR2 (200);
    v_detail_reday_state NUMBER;
    v_error_msg            VARCHAR2(2000);
  BEGIN
    --当前准备中业务期间，才能更改状态
    IF func_get_preparing_year_month(p_entity_id, p_year_month, NULL) IS NOT NULL THEN
      UPDATE dm_conf_bussperiod_detail
         SET ready_state = p_ready_state,
             exec_result = (CASE WHEN p_ready_state = '1' THEN 'success' ELSE NULL END)
       WHERE buss_period_id = (SELECT buss_period_id
                                 FROM dm_conf_bussperiod
                                WHERE entity_id = p_entity_id
                                  AND year_month = p_year_month
                                  AND period_state = '0')
         AND biz_type_id = p_biz_type_id
         AND direction = '1';
      COMMIT;

      IF p_ready_state = '1' THEN
        --验证输入明细数据状态是否己准备
        v_detail_reday_state := func_get_period_detail_state(p_entity_id, v_year_month, p_ready_state);

        IF v_detail_reday_state = '1' THEN
           proc_period_execution(p_entity_id, p_ready_state);
        END IF;
      END IF;
    END IF;

  EXCEPTION
    WHEN others THEN
      NULL;
      --意外处理
      v_error_msg := '[EXCEPTION][业务期间]准备中业务期间更改状态:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);
  END proc_preparing_execution;


end dm_pack_buss_period;
/
