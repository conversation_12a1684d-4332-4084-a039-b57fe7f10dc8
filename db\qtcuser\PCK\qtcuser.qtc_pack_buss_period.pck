create or replace PACKAGE qtc_pack_buss_period IS

  FUNCTION func_period_detail(p_center_id  NUMBER,
                              p_year_month VARCHAR2,
                              p_state      VARCHAR2) RETURN NUMBER;

  PROCEDURE proc_period_execution(p_center_id NUMBER,
                                  p_state     VARCHAR2);

END qtc_pack_buss_period;
/

create or replace PACKAGE BODY qtc_pack_buss_period IS


  FUNCTION func_period_detail(p_center_id  NUMBER,
                              p_year_month VARCHAR2,
                              p_state      VARCHAR2) RETURN NUMBER IS

    v_count NUMBER;
  BEGIN

    --新增业务期间明细
    IF p_state = '0' THEN


      RETURN 1;

      --业务期间己准备，所有输入数据己准备
    ELSIF p_state = '1' THEN
      SELECT COUNT(period_detail_id) INTO v_count
      FROM qtc_conf_bussperiod_detail bpd
      LEFT JOIN qtc_conf_bussperiod bp
        ON bp.buss_period_id = bpd.buss_period_id
      LEFT JOIN qtc_conf_table ct
        ON ct.biz_type_id = bpd.biz_type_id
     WHERE ct.direction = '1'
       AND bpd.ready_state = '0'
       AND bp.ENTITY_ID = p_center_id
       AND bp.year_month = p_year_month;

      --无待准备数据
      IF v_count = 0 THEN
        RETURN 1;
      ELSE
        RETURN 0;
      END IF;

      --业务期间处理中，所有输入数据己准备
    ELSIF p_state = '2' THEN
      SELECT COUNT(period_detail_id) INTO v_count
      FROM qtc_conf_bussperiod_detail bpd
      LEFT JOIN qtc_conf_bussperiod bp
        ON bp.buss_period_id = bpd.buss_period_id
      LEFT JOIN qtc_conf_table ct
        ON ct.biz_type_id = bpd.biz_type_id
     WHERE ct.direction = '1'
       AND bpd.ready_state = '0'
       AND bp.ENTITY_ID = p_center_id
       AND bp.year_month = p_year_month;

      --无待准备数据
      IF v_count = 0 THEN
        RETURN 1;
      ELSE
        RETURN 0;
      END IF;

      --业务期间已完成，所有输出数据己准备
    ELSIF p_state = '3' THEN
      SELECT COUNT(period_detail_id) INTO v_count
      FROM qtc_conf_bussperiod_detail bpd
      LEFT JOIN qtc_conf_bussperiod bp
        ON bp.buss_period_id = bpd.buss_period_id
      LEFT JOIN qtc_conf_table ct
        ON ct.biz_type_id = bpd.biz_type_id
     WHERE ct.direction = '0'
       AND bpd.ready_state = '0'
       AND bp.ENTITY_ID = p_center_id
       AND bp.year_month = p_year_month;

      --无待准备数据
      IF v_count = 0 THEN
        RETURN 1;
      ELSE
        RETURN 0;
      END IF;
    END IF;

    RETURN 0;

  EXCEPTION
    WHEN OTHERS THEN
      dbms_output.put_line(SQLERRM);
      dbms_output.put_line(dbms_utility.format_error_stack);
      RETURN 0;
  END func_period_detail;



  PROCEDURE proc_period_execution(p_center_id NUMBER,
                                  p_state     VARCHAR2) IS

    v_year_month         VARCHAR2(200);
    v_next_year_month    VARCHAR2(200);
    v_detail_reday_state NUMBER;
    v_count              NUMBER;
  BEGIN

    --新增业务期间
    IF p_state = '0' THEN
      --检验是否存在准备中的业务期间(有且仅有一个在准备中的业务期间)
      SELECT COUNT(year_month)
        INTO v_count
        FROM qtc_conf_bussperiod
       WHERE ENTITY_ID = p_center_id
         AND execution_state = '0';

      IF v_count = 0 THEN
        --增加一个新的业务期间
        SELECT to_char(add_months(to_date(year_month, 'yyyymm'), 1),
                       'YYYYMM')
          INTO v_next_year_month
          FROM (select *
                  from qtc_conf_bussperiod
                 WHERE ENTITY_ID = p_center_id
                 ORDER BY year_month DESC)
         where rownum = 1;



        --初始数据按当前时间处理
        IF v_next_year_month IS NULL THEN
          v_next_year_month := to_char(SYSDATE, 'YYYYMM');
        END IF;

        INSERT INTO qtc_conf_bussperiod
          (buss_period_id,
           ENTITY_ID,
           year_month,
           execution_state,
           valid_is,
           creator_id,
           create_time)
        VALUES
          (qtc_seq_conf_bussperiod.nextval,
           p_center_id,
           v_next_year_month,
           '0',
           '1',
           '1',
           SYSDATE);

        --TODO 增加准备中的输入输出明细数据
        --select func_period_detail(p_center_id, v_next_year_month, p_state)
        --into v_detail_reday_state from dual;
        INSERT INTO qtc_conf_bussperiod_detail
          (period_detail_id,
           buss_period_id,
           biz_type_id,
           ready_state,
           creator_id,
           create_time)
          SELECT qtc_seq_conf_bussperiod_detail.nextval,
                 a.*
            FROM (SELECT bp.buss_period_id,
                         ct.biz_type_id,
                         '0' ready_state,
                         bp.creator_id,
                         SYSDATE create_time
                    FROM qtc_conf_table ct
                    LEFT JOIN qtc_conf_bussperiod bp
                      ON bp.ENTITY_ID = p_center_id
                     AND bp.year_month = v_next_year_month
                   WHERE ct.valid_is = '1') a;

      END IF;
      --业务期间己准备
    ELSIF p_state = '1' THEN
      --检验是否存在准备中业务期间
      SELECT COUNT(tt.year_month)
        INTO v_count
        FROM (SELECT year_month
                FROM qtc_conf_bussperiod
               WHERE ENTITY_ID = p_center_id
                 AND execution_state = '0'
               ORDER BY year_month) tt
       WHERE rownum = 1;

      IF v_count > 0 THEN
        SELECT tt.year_month
          INTO v_year_month
          FROM (SELECT year_month
                  FROM qtc_conf_bussperiod
                 WHERE ENTITY_ID = p_center_id
                   AND execution_state = '0'
                 ORDER BY year_month) tt
         WHERE rownum = 1;
        --验证输入明细数据状态是否己准备
        SELECT func_period_detail(p_center_id, v_year_month, p_state) INTO v_detail_reday_state FROM dual;
        IF v_detail_reday_state = 1 THEN
          --修改当前业务期间状态为己准备
          UPDATE qtc_conf_bussperiod
             SET execution_state = '1',
                 updator_id      = 1,
                 update_time     = SYSDATE
           WHERE ENTITY_ID = p_center_id
             AND year_month = v_year_month;

          --增加下个准备中业务期间
          proc_period_execution(p_center_id, '0');

        END IF;
      END IF;

      --确保有下个处理中业务期间
      proc_period_execution(p_center_id, '2');

      --业务期间处理中
    ELSIF p_state = '2' THEN
      --检查在处理中的业务期间
      SELECT COUNT(tt2.year_month)
        INTO v_count
        FROM (SELECT year_month
                FROM qtc_conf_bussperiod
               WHERE ENTITY_ID = p_center_id
                 AND execution_state = '2'
               ORDER BY year_month) tt2
       WHERE rownum = 1;


      --确保无在处理中的业务期间
      IF v_count = 0 THEN
        SELECT tt3.year_month
          INTO v_next_year_month
          FROM (SELECT year_month
                  FROM qtc_conf_bussperiod
                 WHERE ENTITY_ID = p_center_id
                   AND execution_state = '1'
                 ORDER BY year_month) tt3
         WHERE rownum = 1;

        --存在下一个己准备的业务期间,开始处理
        IF v_next_year_month IS NOT NULL THEN

          --验证输出明细数据状态是否己准备完成，验证成功后执行以下逻辑
          SELECT func_period_detail(p_center_id, v_next_year_month, p_state) INTO v_detail_reday_state FROM dual;
          IF v_detail_reday_state = 1 THEN

            --修改当前业务期间状态为处理中
            UPDATE qtc_conf_bussperiod
               SET execution_state = '2',
                   updator_id      = 1,
                   update_time     = SYSDATE
             WHERE ENTITY_ID = p_center_id
               AND year_month = v_next_year_month;
          END IF;
        END IF;
      END IF;

      --业务期间已完成
    ELSIF p_state = '3' THEN
      SELECT tt4.year_month
        INTO v_year_month
        FROM (SELECT year_month
                FROM qtc_conf_bussperiod
               WHERE ENTITY_ID = p_center_id
                 AND execution_state = '2'
               ORDER BY year_month) tt4
       WHERE rownum = 1;

      IF v_year_month IS NOT NULL THEN

        --验证输出明细数据状态是否己完成，验证成功后执行以下逻辑
        SELECT func_period_detail(p_center_id, v_year_month, p_state) INTO v_detail_reday_state FROM dual;
        IF v_detail_reday_state = 1 THEN

          --修改当前业务期间状态为已完成
          UPDATE qtc_conf_bussperiod
             SET execution_state = '3',
                 updator_id      = 1,
                 update_time     = SYSDATE
           WHERE ENTITY_ID = p_center_id
             AND year_month = v_year_month;

          --下一个业务期间
          v_next_year_month := to_char(add_months(to_date(v_year_month, 'yyyymm'), 1), 'YYYYMM');

          --检验下一个业务期间是否存在
          SELECT max(year_month)
            INTO v_year_month
            FROM qtc_conf_bussperiod
           WHERE ENTITY_ID = p_center_id
             AND year_month = v_next_year_month;

          IF v_year_month IS NULL THEN
            --增加下个准备中业务期间
            proc_period_execution(p_center_id, '0');
          ELSE
            --修改下个业务期间状态为处理中
            proc_period_execution(p_center_id, '2');
          END IF;
        END IF;
      END IF;
    END IF;

  EXCEPTION
    WHEN OTHERS THEN
      --ROLLBACK;
      dbms_output.put_line(SQLERRM);
      dbms_output.put_line(dbms_utility.format_error_stack);
  END proc_period_execution;

END qtc_pack_buss_period;
/

