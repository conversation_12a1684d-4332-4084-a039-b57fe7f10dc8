/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-04-15 19:55:34
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2021-04-15 19:55:34<br/>
 * Description: LRC计量数据结果表<br/>
 * Table Name: ATR_BUSS_LRC_RESULT<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "LRC计量数据结果表")
public class AtrBussLrcResultResVo implements Serializable {

    private Long lrcMainId;

    private Long lrcResultId;

    private String yearMonth;

    private String policyNo;

    private String endorseNo;

    private String endorseSeq;

    private String productCode;

    private String atrType;

    private String riskCode;

    private Date atrDate;

    private Date startDate;

    private Date endDate;

    private Date endorseDate;

    private Date checkDate;

    private String currencyCode;

    private BigDecimal premium;

    private BigDecimal incomeAmount;

    private BigDecimal realityAmount;

    private BigDecimal lrcAmount;

    private String cmunitNo;

    private String icgNo;

    private String portfolioNo;

    //产品
    private String productEName;

    private String productLName;

    private String productCName;

    //险种
    private String riskEName;

    private String riskLName;

    private String riskCName;

    private String currencyCu;

    private String premiumCu;

    private String riskClassCode;

    private List<AtrBussLrcResultVo> atrBussLrcResultVoList;

    private static final long serialVersionUID = 1L;

    public Long getLrcMainId() {
        return lrcMainId;
    }

    public void setLrcMainId(Long lrcMainId) {
        this.lrcMainId = lrcMainId;
    }

    public Long getLrcResultId() {
        return lrcResultId;
    }

    public void setLrcResultId(Long lrcResultId) {
        this.lrcResultId = lrcResultId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getEndorseNo() {
        return endorseNo;
    }

    public void setEndorseNo(String endorseNo) {
        this.endorseNo = endorseNo;
    }

    public String getEndorseSeq() {
        return endorseSeq;
    }

    public void setEndorseSeq(String endorseSeq) {
        this.endorseSeq = endorseSeq;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getAtrType() {
        return atrType;
    }

    public void setAtrType(String atrType) {
        this.atrType = atrType;
    }

    public String getRiskCode() {
        return riskCode;
    }

    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    public Date getAtrDate() {
        return atrDate;
    }

    public void setAtrDate(Date atrDate) {
        this.atrDate = atrDate;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Date getEndorseDate() {
        return endorseDate;
    }

    public void setEndorseDate(Date endorseDate) {
        this.endorseDate = endorseDate;
    }

    public Date getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(Date checkDate) {
        this.checkDate = checkDate;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public BigDecimal getPremium() {
        return premium;
    }

    public void setPremium(BigDecimal premium) {
        this.premium = premium;
    }

    public BigDecimal getIncomeAmount() {
        return incomeAmount;
    }

    public void setIncomeAmount(BigDecimal incomeAmount) {
        this.incomeAmount = incomeAmount;
    }

    public BigDecimal getRealityAmount() {
        return realityAmount;
    }

    public void setRealityAmount(BigDecimal realityAmount) {
        this.realityAmount = realityAmount;
    }

    public BigDecimal getLrcAmount() {
        return lrcAmount;
    }

    public void setLrcAmount(BigDecimal lrcAmount) {
        this.lrcAmount = lrcAmount;
    }

    public String getCmunitNo() {
        return cmunitNo;
    }

    public void setCmunitNo(String cmunitNo) {
        this.cmunitNo = cmunitNo;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }

    public String getProductEName() {
        return productEName;
    }

    public void setProductEName(String productEName) {
        this.productEName = productEName;
    }

    public String getProductLName() {
        return productLName;
    }

    public void setProductLName(String productLName) {
        this.productLName = productLName;
    }

    public String getProductCName() {
        return productCName;
    }

    public void setProductCName(String productCName) {
        this.productCName = productCName;
    }

    public String getRiskEName() {
        return riskEName;
    }

    public void setRiskEName(String riskEName) {
        this.riskEName = riskEName;
    }

    public String getRiskLName() {
        return riskLName;
    }

    public void setRiskLName(String riskLName) {
        this.riskLName = riskLName;
    }

    public String getRiskCName() {
        return riskCName;
    }

    public void setRiskCName(String riskCName) {
        this.riskCName = riskCName;
    }

    public String getCurrencyCu() {
        return currencyCu;
    }

    public void setCurrencyCu(String currencyCu) {
        this.currencyCu = currencyCu;
    }

    public String getPremiumCu() {
        return premiumCu;
    }

    public void setPremiumCu(String premiumCu) {
        this.premiumCu = premiumCu;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public List<AtrBussLrcResultVo> getAtrBussLrcResultVoList() {
        return atrBussLrcResultVoList;
    }

    public void setAtrBussLrcResultVoList(List<AtrBussLrcResultVo> atrBussLrcResultVoList) {
        this.atrBussLrcResultVoList = atrBussLrcResultVoList;
    }
}