<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-02-06 17:15:22 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussIbnrImportDetailDao">
  <!-- 本配置文件由SS MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**IDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->

  <insert id="saveIbnrDetailList" flushCache="true" parameterType="java.util.List">
    insert into ATR_BUSS_IBNR_IMPORT_DETAIL(IBNR_DETAIL_ID, IBNR_MAIN_ID, BUSINESS_MODEL, IBNR_AMOUNT,ri_ibnr_amount,ri_case_amount,
    year_month,accident_quarter,risk_class_code,center_code,ri_dept,treaty_no, treaty_name)
    VALUES
    <foreach collection="list" item="item" index="index" separator="," >
      ( nextval('atr_seq_buss_ibnr_import_dtl'), #{mainId,jdbcType=DECIMAL}, #{item.businessModel,jdbcType=VARCHAR},
        #{item.ibnrAmount,jdbcType=DECIMAL}, #{item.riIbnrAmount,jdbcType=DECIMAL},#{item.riCaseAmount,jdbcType=DECIMAL},
        #{item.yearMonth,jdbcType=VARCHAR},#{item.accidentQuarter,jdbcType=VARCHAR}, #{item.riskClassCode,jdbcType=VARCHAR},#{item.centerCode,jdbcType=VARCHAR},
        #{item.riDept,jdbcType=VARCHAR}, #{item.treatyNo,jdbcType=VARCHAR}, #{item.treatyName,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <select id="findIbnrDetailList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select * from (
        SELECT
          ibnr_main_id,
          business_model,
          risk_class_code,
          year_month,
          accident_quarter,
          center_code,
          treaty_No,
          treaty_Name,
          SUM (ibnr_amount) AS ibnr_amount,
          SUM (ri_ibnr_amount) AS ri_ibnr_amount,
          SUM (ri_case_amount) AS ri_case_amount,
          ROW_NUMBER() OVER (PARTITION BY ibnr_main_id, business_model,center_code, risk_class_code, accident_quarter,treaty_No ORDER BY year_month DESC ) AS rn
        FROM atr_buss_ibnr_import_detail biid
        where ibnr_main_id = #{ibnrMainId,jdbcType=DECIMAL}
        and business_model!='TI'
        group by ibnr_main_id,
                 business_model,
                 risk_class_code,
                 accident_quarter,
                 year_month,
                 center_code,
                 treaty_No,
                 treaty_Name
    )
    where rn=1
  </select>


</mapper>