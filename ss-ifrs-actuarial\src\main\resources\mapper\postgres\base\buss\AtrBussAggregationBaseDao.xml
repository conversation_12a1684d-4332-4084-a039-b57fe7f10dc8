<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by Taiping MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2024-07-19 10:53:34 -->
<!-- Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.AtrBussAggregationDao">
  <!-- 本文件由Taiping MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**BaseDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussAggregation">
    <id column="ATR_AGGREGATION_ID" property="atrAggregationId" jdbcType="DECIMAL" />
    <result column="ENTITY_ID" property="entityId" jdbcType="DECIMAL" />
    <result column="YEAR_MONTH" property="yearMonth" jdbcType="VARCHAR" />
    <result column="BUSINESS_SOURCE_CODE" property="businessSourceCode" jdbcType="VARCHAR" />
    <result column="EVALUATE_APPROACH" property="evaluateApproach" jdbcType="VARCHAR" />
    <result column="MODEL_DEF_ID" property="modelDefId" jdbcType="DECIMAL" />
    <result column="LOA_CODE" property="loaCode" jdbcType="VARCHAR" />
    <result column="LOA_C_NAME" property="loaCName" jdbcType="VARCHAR" />
    <result column="LOA_L_NAME" property="loaLName" jdbcType="VARCHAR" />
    <result column="LOA_E_NAME" property="loaEName" jdbcType="VARCHAR" />
    <result column="SERIAL_NO" property="serialNo" jdbcType="DECIMAL" />
    <result column="CREATOR_ID" property="creatorId" jdbcType="DECIMAL" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    ATR_AGGREGATION_ID, ENTITY_ID, YEAR_MONTH, BUSINESS_SOURCE_CODE, EVALUATE_APPROACH, 
    MODEL_DEF_ID, LOA_CODE, LOA_C_NAME, LOA_L_NAME, LOA_E_NAME, SERIAL_NO,
    CREATOR_ID, CREATE_TIME
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="atrAggregationId != null ">
          and ATR_AGGREGATION_ID = #{atrAggregationId,jdbcType=DECIMAL}
      </if>
      <if test="entityId != null ">
          and ENTITY_ID = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
          and YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="businessSourceCode != null and businessSourceCode != ''">
          and BUSINESS_SOURCE_CODE = #{businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="evaluateApproach != null and evaluateApproach != ''">
          and EVALUATE_APPROACH = #{evaluateApproach,jdbcType=VARCHAR}
      </if>
      <if test="modelDefId != null ">
          and MODEL_DEF_ID = #{modelDefId,jdbcType=DECIMAL}
      </if>
      <if test="loaCode != null and loaCode != ''">
          and LOA_CODE = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="loaCName != null and loaCName != ''">
          and LOA_C_NAME = #{loaCName,jdbcType=VARCHAR}
      </if>
      <if test="loaLName != null and loaLName != ''">
          and LOA_L_NAME = #{loaLName,jdbcType=VARCHAR}
      </if>
      <if test="loaEName != null and loaEName != ''">
          and LOA_E_NAME = #{loaEName,jdbcType=VARCHAR}
      </if>
      <if test="serialNo != null ">
          and SERIAL_NO = #{serialNo,jdbcType=DECIMAL}
      </if>
      <if test="creatorId != null ">
          and CREATOR_ID = #{creatorId,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null ">
          and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.atrAggregationId != null ">
          and ATR_AGGREGATION_ID = #{condition.atrAggregationId,jdbcType=DECIMAL}
      </if>
      <if test="condition.entityId != null ">
          and ENTITY_ID = #{condition.entityId,jdbcType=DECIMAL}
      </if>
      <if test="condition.yearMonth != null and condition.yearMonth != ''">
          and YEAR_MONTH = #{condition.yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="condition.businessSourceCode != null and condition.businessSourceCode != ''">
          and BUSINESS_SOURCE_CODE = #{condition.businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.evaluateApproach != null and condition.evaluateApproach != ''">
          and EVALUATE_APPROACH = #{condition.evaluateApproach,jdbcType=VARCHAR}
      </if>
      <if test="condition.modelDefId != null ">
          and MODEL_DEF_ID = #{condition.modelDefId,jdbcType=DECIMAL}
      </if>
      <if test="condition.loaCode != null and condition.loaCode != ''">
          and LOA_CODE = #{condition.loaCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.loaCName != null and condition.loaCName != ''">
          and LOA_C_NAME = #{condition.loaCName,jdbcType=VARCHAR}
      </if>
      <if test="condition.loaLName != null and condition.loaLName != ''">
          and LOA_L_NAME = #{condition.loaLName,jdbcType=VARCHAR}
      </if>
      <if test="condition.loaEName != null and condition.loaEName != ''">
          and LOA_E_NAME = #{condition.loaEName,jdbcType=VARCHAR}
      </if>
      <if test="condition.serialNo != null ">
          and SERIAL_NO = #{condition.serialNo,jdbcType=DECIMAL}
      </if>
      <if test="condition.creatorId != null ">
          and CREATOR_ID = #{condition.creatorId,jdbcType=DECIMAL}
      </if>
      <if test="condition.createTime != null ">
          and CREATE_TIME = #{condition.createTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="atrAggregationId != null ">
          and ATR_AGGREGATION_ID = #{atrAggregationId,jdbcType=DECIMAL}
      </if>
      <if test="entityId != null ">
          and ENTITY_ID = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
          and YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="businessSourceCode != null and businessSourceCode != ''">
          and BUSINESS_SOURCE_CODE = #{businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="evaluateApproach != null and evaluateApproach != ''">
          and EVALUATE_APPROACH = #{evaluateApproach,jdbcType=VARCHAR}
      </if>
      <if test="modelDefId != null ">
          and MODEL_DEF_ID = #{modelDefId,jdbcType=DECIMAL}
      </if>
      <if test="loaCode != null and loaCode != ''">
          and LOA_CODE = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="loaCName != null and loaCName != ''">
          and LOA_C_NAME = #{loaCName,jdbcType=VARCHAR}
      </if>
      <if test="loaLName != null and loaLName != ''">
          and LOA_L_NAME = #{loaLName,jdbcType=VARCHAR}
      </if>
      <if test="loaEName != null and loaEName != ''">
          and LOA_E_NAME = #{loaEName,jdbcType=VARCHAR}
      </if>
      <if test="serialNo != null ">
          and SERIAL_NO = #{serialNo,jdbcType=DECIMAL}
      </if>
      <if test="creatorId != null ">
          and CREATOR_ID = #{creatorId,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null ">
          and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from QTC_BUSS_AGGREGATION
    where ATR_AGGREGATION_ID = #{atrAggregationId,jdbcType=DECIMAL}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from QTC_BUSS_AGGREGATION
    where ATR_AGGREGATION_ID in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from QTC_BUSS_AGGREGATION
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussAggregation">
    select 
    <include refid="Base_Column_List" />
    from QTC_BUSS_AGGREGATION
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from QTC_BUSS_AGGREGATION
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="ATR_AGGREGATION_ID" keyProperty="atrAggregationId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussAggregation">
    insert into QTC_BUSS_AGGREGATION
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="atrAggregationId != null">
        ATR_AGGREGATION_ID,
      </if>
      <if test="entityId != null">
        ENTITY_ID,
      </if>
      <if test="yearMonth != null">
        YEAR_MONTH,
      </if>
      <if test="businessSourceCode != null">
        BUSINESS_SOURCE_CODE,
      </if>
      <if test="evaluateApproach != null">
        EVALUATE_APPROACH,
      </if>
      <if test="modelDefId != null">
        MODEL_DEF_ID,
      </if>
      <if test="loaCode != null">
        LOA_CODE,
      </if>
      <if test="loaCName != null">
        LOA_C_NAME,
      </if>
      <if test="loaLName != null">
        LOA_L_NAME,
      </if>
      <if test="loaEName != null">
        LOA_E_NAME,
      </if>
      <if test="serialNo != null">
        SERIAL_NO,
      </if>
      <if test="creatorId != null">
        CREATOR_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="atrAggregationId != null">
        #{atrAggregationId,jdbcType=DECIMAL},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=DECIMAL},
      </if>
      <if test="yearMonth != null">
        #{yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceCode != null">
        #{businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="evaluateApproach != null">
        #{evaluateApproach,jdbcType=VARCHAR},
      </if>
      <if test="modelDefId != null">
        #{modelDefId,jdbcType=DECIMAL},
      </if>
      <if test="loaCode != null">
        #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="loaCName != null">
        #{loaCName,jdbcType=VARCHAR},
      </if>
      <if test="loaLName != null">
        #{loaLName,jdbcType=VARCHAR},
      </if>
      <if test="loaEName != null">
        #{loaEName,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=DECIMAL},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all 
    <foreach collection="list" item="item" index="index">
      into QTC_BUSS_AGGREGATION values 
       (#{item.atrAggregationId,jdbcType=DECIMAL}, 
        #{item.entityId,jdbcType=DECIMAL}, #{item.yearMonth,jdbcType=VARCHAR}, #{item.businessSourceCode,jdbcType=VARCHAR}, 
        #{item.evaluateApproach,jdbcType=VARCHAR}, #{item.modelDefId,jdbcType=DECIMAL}, 
        #{item.loaCode,jdbcType=VARCHAR}, #{item.loaCName,jdbcType=VARCHAR}, #{item.loaLName,jdbcType=VARCHAR}, 
        #{item.loaEName,jdbcType=VARCHAR}, #{item.serialNo,jdbcType=DECIMAL},
        #{item.creatorId,jdbcType=DECIMAL}, #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
    select 1 from dual
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussAggregation">
    update QTC_BUSS_AGGREGATION
    <set>
      <if test="entityId != null">
        ENTITY_ID = #{entityId,jdbcType=DECIMAL},
      </if>
      <if test="yearMonth != null">
        YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceCode != null">
        BUSINESS_SOURCE_CODE = #{businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="evaluateApproach != null">
        EVALUATE_APPROACH = #{evaluateApproach,jdbcType=VARCHAR},
      </if>
      <if test="modelDefId != null">
        MODEL_DEF_ID = #{modelDefId,jdbcType=DECIMAL},
      </if>
      <if test="loaCode != null">
        LOA_CODE = #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="loaCName != null">
        LOA_C_NAME = #{loaCName,jdbcType=VARCHAR},
      </if>
      <if test="loaLName != null">
        LOA_L_NAME = #{loaLName,jdbcType=VARCHAR},
      </if>
      <if test="loaEName != null">
        LOA_E_NAME = #{loaEName,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        SERIAL_NO = #{serialNo,jdbcType=DECIMAL},
      </if>
      <if test="creatorId != null">
        CREATOR_ID = #{creatorId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ATR_AGGREGATION_ID = #{atrAggregationId,jdbcType=DECIMAL}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussAggregation">
    update QTC_BUSS_AGGREGATION
    <set>
      <if test="record.entityId != null">
        ENTITY_ID = #{record.entityId,jdbcType=DECIMAL},
      </if>
      <if test="record.yearMonth != null">
        YEAR_MONTH = #{record.yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="record.businessSourceCode != null">
        BUSINESS_SOURCE_CODE = #{record.businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.evaluateApproach != null">
        EVALUATE_APPROACH = #{record.evaluateApproach,jdbcType=VARCHAR},
      </if>
      <if test="record.modelDefId != null">
        MODEL_DEF_ID = #{record.modelDefId,jdbcType=DECIMAL},
      </if>
      <if test="record.loaCode != null">
        LOA_CODE = #{record.loaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.loaCName != null">
        LOA_C_NAME = #{record.loaCName,jdbcType=VARCHAR},
      </if>
      <if test="record.loaLName != null">
        LOA_L_NAME = #{record.loaLName,jdbcType=VARCHAR},
      </if>
      <if test="record.loaEName != null">
        LOA_E_NAME = #{record.loaEName,jdbcType=VARCHAR},
      </if>
      <if test="record.serialNo != null">
        SERIAL_NO = #{record.serialNo,jdbcType=DECIMAL},
      </if>
      <if test="record.creatorId != null">
        CREATOR_ID = #{record.creatorId,jdbcType=DECIMAL},
      </if>
      <if test="record.createTime != null">
        CREATE_TIME = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from QTC_BUSS_AGGREGATION
    where ATR_AGGREGATION_ID = #{atrAggregationId,jdbcType=DECIMAL}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from QTC_BUSS_AGGREGATION
    where ATR_AGGREGATION_ID in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from QTC_BUSS_AGGREGATION
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussAggregation">
    select count(1) from QTC_BUSS_AGGREGATION
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>