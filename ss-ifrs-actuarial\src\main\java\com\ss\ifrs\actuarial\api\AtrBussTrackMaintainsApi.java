package com.ss.ifrs.actuarial.api;

import com.ss.ifrs.actuarial.service.AtrBussTrackMaintainsService;
import com.ss.ifrs.actuarial.service.AtrExportService;
import com.ss.platform.pojo.bms.track.vo.TrackMaintainsReqVo;
import com.ss.platform.pojo.bms.track.vo.TrackMaintainsVo;
import com.ss.platform.core.annotation.TrackUserBehavioralEnable;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/oper_track")
@TrackUserBehavioralEnable
@Api(value = "操作轨迹日志接口")
public class AtrBussTrackMaintainsApi extends BaseApi {

    @Autowired
    AtrBussTrackMaintainsService atrBussTrackMaintainsService;

    @Autowired
    AtrExportService atrExportService;

    /**
     * @Description: 日志分页查询
     * @return BaseResponse<Map<String,Object>> 返回类型
     * <AUTHOR>
     */
    @ApiOperation(value = "日志分页查询")
    @RequestMapping(value = "/enquiry", method = { RequestMethod.POST })
    public Page<TrackMaintainsReqVo> searchLog(@RequestBody TrackMaintainsVo trackMaintainsVo) throws Exception {
        Page<TrackMaintainsReqVo> result = atrBussTrackMaintainsService.fuzzySearchPage(trackMaintainsVo, trackMaintainsVo.getPageParam());
        return result;
    }

    @ApiOperation(value = "Excel导出")
    @RequestMapping(value = "/export_excel", method = RequestMethod.POST)
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, @RequestBody TrackMaintainsVo vo) throws Exception {
        Pageable pageParam = new Pageable(0,maxExcelPageSize);
        Page<TrackMaintainsReqVo> result = atrBussTrackMaintainsService.fuzzySearchPage(vo, pageParam);
       //this.exportPage(request, response,  result, TrackMaintainsReqVo.class, "df");
    }

    @ApiOperation(value = "获取计量平台导出模板目录")
    @RequestMapping(value = "/get_template_Path", method = RequestMethod.POST)
    public BaseResponse<String> getTemplatePath() throws Exception {
        String result = atrExportService.getTemplatePath();
        return new BaseResponse<String>(ResCodeConstant.ResCode.SUCCESS, result);
    }
}
