/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * String: 2023-03-08 10:07:11
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create String: 2023-03-08 10:07:11<br/>
 * Description: LRC计算结果主表<br/>
 * Table Name: ATR_BUSS_FO_LRC_ICU_CALC<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "LRC 计算结果主表(单维度，临分分出)")
public class AtrBussLrcIcuCalcExportVo implements Serializable {

    @ApiModelProperty(value = "执行编号|一次操作的全局唯一编号， 一次操作的维度是模块（LIC或LRC）、业务类型、业务单位、业务年月、币别", required = true)
    private String actionNo;

    @ApiModelProperty(value = "公共项/保单号", required = true)
    private String policyNo;


    @ApiModelProperty(value = "公共项/批单序号", required = true)
    private String endorseSeqNo;


    @ApiModelProperty(value = "公共项/分保单号", required = false)
    private String rePolicyNo;


    @ApiModelProperty(value = "公共项/分保批单序号", required = false)
    private String reEndorseSeqNo;


    @ApiModelProperty(value = "公共项/币种", required = true)
    private String currencyCode;


    @ApiModelProperty(value = "公共项/业务年月（评估期的年月）", required = true)
    private String yearMonth;


    @ApiModelProperty(value = "公共项/合同组合号码", required = true)
    private String portfolioNo;


    @ApiModelProperty(value = "公共项/合同组号码", required = true)
    private String icgNo;

    @ApiModelProperty(value = "公共项/评估方法", required = true)
    private String evaluateApproach;


    @ApiModelProperty(value = "公共项/OA编码", required = true)
    private String loaCode;

    @ApiModelProperty(value = "公共项/计量单元编号", required = false)
    private String cmunitNo;


    @ApiModelProperty(value = "公共项/产品代码", required = false)
    private String productCode;


    @ApiModelProperty(value = "公共项/评估日期 （存日期）", required = false)
    private String evaluateDate;


    @ApiModelProperty(value = "公共项/合同确认日期 (即初始确认日期)", required = false)
    private String contractDate;


    @ApiModelProperty(value = "公共项/保单起保日期（存日期）", required = false)
    private String effectiveDateInDate;

    @ApiModelProperty(value = "公共项/审核通过日期（存日期）", required = false)
    private String checkDateInDate;


    @ApiModelProperty(value = "公共项/保单终保日期（存日期）", required = false)
    private String expiryDateInDate;



     @ApiModelProperty(value = "公共项/保单起保日期（月初）", required = false)
     private String effectiveDateBom;


     @ApiModelProperty(value = "公共项/保单终保日期（月底）", required = false)
     private String expiryDateEom;


     @ApiModelProperty(value = "公共项/缴费频率", required = false)
     private String premiumFrequency;


     @ApiModelProperty(value = "公共项/缴费期次", required = false)
     private Long premiumTerm;

     @ApiModelProperty(value = "公共项/毛保费", required = false)
     private BigDecimal grossPremium;


     @ApiModelProperty(value = "公共项/保单理赔限额", required = false)
     private BigDecimal coverageAmount;


     @ApiModelProperty(value = "公共项/已承保天数", required = false)
     private Long passedDates;


     @ApiModelProperty(value = "公共项/已过月份", required = false)
     private Long passedMonths;


     @ApiModelProperty(value = "公共项/未到期月份（评估日期-终保日期）", required = false)
     private Long remainingMonths;


     @ApiModelProperty(value = "预期应收保费/剩余未交费期次（评估日期-终保日期）", required = false)
     private Long remainingPremTermPe;


     @ApiModelProperty(value = "预期应收保费/未到期月份（间隔第一期未来年度预付时间）", required = false)
     private Long remainingMonthsFuture;


     @ApiModelProperty(value = "预期应收保费/未到期缴费期次（评估日期-合同边界日期）", required = false)
     private Long remainingPremTermCb;

     /**
      * Database column: ATR_BUSS_FO_LRC_ICU_CALC.PAYMENT_QUARTER
      * Database remarks: 预期应收保费/季度付款期间（年度保费）
     */
    @ApiModelProperty(value = "预期应收保费/季度付款期间（年度保费）", required = false)
    private Long paymentQuarter;

    /**
     * Database column: ATR_BUSS_FO_LRC_ICU_CALC.ed_premium_per_coverage_day
     * Database remarks: GEP_Persist 已赚保费/已赚保费（每个覆盖月份）
     */
    @ApiModelProperty(value = "GEP_Persist 已赚保费/已赚保费（每个覆盖月份）", required = false)
    private BigDecimal edPremiumPerCoverageDay;

    /**
     * Database column: ATR_BUSS_FO_LRC_ICU_CALC.ED_PREMIUM
     * Database remarks: GEP_Persist 已赚保费/已赚保费
     */
    @ApiModelProperty(value = "GEP_Persist 已赚保费/已赚保费", required = false)
    private BigDecimal edPremium;

    /**
     * Database column: ATR_BUSS_FO_LRC_ICU_CALC.PRI_CUR_END_REMAIN_CSM_RATE
     * Database remarks: 未赚保费/(上期)当期期末剩余未摊销比例
     */
    @ApiModelProperty(value = "未赚保费/(上期)当期期末剩余未摊销比例", required = false)
    private BigDecimal priCurEndRemainCsmRate;

    /**
     * Database column: ATR_BUSS_FO_LRC_ICU_CALC.PRI_UNTIL_REPORT_REMAIN_CSM_RATE
     * Database remarks: 未赚保费/(上期)截至报告期初剩余未摊销比例
     */
    @ApiModelProperty(value = "未赚保费/(上期)截至报告期初剩余未摊销比例", required = false)
    private BigDecimal priUntilReportRemainCsmRate;

    /**
     * Database column: ATR_BUSS_FO_LRC_ICU_CALC.CUMULATIVE_ED_RATE
     * Database remarks: 未赚保费/累计已赚比例
     */
    @ApiModelProperty(value = "未赚保费/累计已赚比例", required = false)
    private BigDecimal cumulativeEdRate;

    /**
     * Database column: ATR_BUSS_FO_LRC_ICU_CALC.CUR_END_REMAIN_CSM_RATE
     * Database remarks: 未赚保费/当期期末剩余未摊销比例
     */
    @ApiModelProperty(value = "未赚保费/当期期末剩余未摊销比例", required = false)
    private BigDecimal curEndRemainCsmRate;

    /**
     * Database column: ATR_BUSS_FO_LRC_ICU_CALC.UNTIL_REPORT_REMAIN_CSM_RATE
     * Database remarks: 未赚保费/截至报告期初剩余未摊销比例
     */
    @ApiModelProperty(value = "未赚保费/截至报告期初剩余未摊销比例", required = false)
    private BigDecimal untilReportRemainCsmRate;

    /**
     * Database column: ATR_BUSS_FO_LRC_ICU_CALC.ELR
     * Database remarks: Claim_byUWQ_AQ_Vert/ELR
     */
    @ApiModelProperty(value = "Claim_byUWQ_AQ_Vert/ELR", required = false)
    private BigDecimal elr;

    private BigDecimal iacfFeeRate;

    private BigDecimal QR003;

    private BigDecimal QR010;

    private String entityCode;

    private String entityEName;

    private static final long serialVersionUID = 1L;

    private BigDecimal ep1;
    private BigDecimal ep2;
    private BigDecimal ep3;
    private BigDecimal ep4;
    private BigDecimal ep5;
    private BigDecimal ep6;
    private BigDecimal ep7;
    private BigDecimal ep8;
    private BigDecimal ep9;
    private BigDecimal ep10;
    private BigDecimal ep11;
    private BigDecimal ep12;
    private BigDecimal ep13;
    private BigDecimal ep14;
    private BigDecimal ep15;
    private BigDecimal ep16;
    private BigDecimal ep17;
    private BigDecimal ep18;
    private BigDecimal ep19;
    private BigDecimal ep20;
    private BigDecimal ep21;
    private BigDecimal ep22;
    private BigDecimal ep23;
    private BigDecimal ep24;
    private BigDecimal ep25;
    private BigDecimal ep26;
    private BigDecimal ep27;
    private BigDecimal ep28;
    private BigDecimal ep29;
    private BigDecimal ep30;
    private BigDecimal ep31;
    private BigDecimal ep32;
    private BigDecimal ep33;
    private BigDecimal ep34;
    private BigDecimal ep35;
    private BigDecimal ep36;
    private BigDecimal ep37;
    private BigDecimal ep38;
    private BigDecimal ep39;
    private BigDecimal ep40;
    private BigDecimal ep41;
    private BigDecimal ep42;
    private BigDecimal ep43;
    private BigDecimal ep44;
    private BigDecimal ep45;
    private BigDecimal ep46;
    private BigDecimal ep47;
    private BigDecimal ep48;
    private BigDecimal ep49;
    private BigDecimal ep50;
    private BigDecimal ep51;
    private BigDecimal ep52;
    private BigDecimal ep53;
    private BigDecimal ep54;
    private BigDecimal ep55;
    private BigDecimal ep56;
    private BigDecimal ep57;
    private BigDecimal ep58;
    private BigDecimal ep59;
    private BigDecimal ep60;
    private BigDecimal ep61;
    private BigDecimal ep62;
    private BigDecimal ep63;
    private BigDecimal ep64;
    private BigDecimal ep65;
    private BigDecimal ep66;
    private BigDecimal ep67;
    private BigDecimal ep68;
    private BigDecimal ep69;
    private BigDecimal ep70;
    private BigDecimal ep71;
    private BigDecimal ep72;
    private BigDecimal ep73;
    private BigDecimal ep74;
    private BigDecimal ep75;
    private BigDecimal ep76;
    private BigDecimal ep77;
    private BigDecimal ep78;
    private BigDecimal ep79;
    private BigDecimal ep80;
    private BigDecimal ep81;
    private BigDecimal ep82;
    private BigDecimal ep83;
    private BigDecimal ep84;
    private BigDecimal ep85;
    private BigDecimal ep86;
    private BigDecimal ep87;
    private BigDecimal ep88;
    private BigDecimal ep89;
    private BigDecimal ep90;
    private BigDecimal ep91;
    private BigDecimal ep92;
    private BigDecimal ep93;
    private BigDecimal ep94;
    private BigDecimal ep95;
    private BigDecimal ep96;
    private BigDecimal ep97;
    private BigDecimal ep98;
    private BigDecimal ep99;
    private BigDecimal ep100;
    private BigDecimal ep101;
    private BigDecimal ep102;
    private BigDecimal ep103;
    private BigDecimal ep104;
    private BigDecimal ep105;
    private BigDecimal ep106;
    private BigDecimal ep107;
    private BigDecimal ep108;
    private BigDecimal ep109;
    private BigDecimal ep110;
    private BigDecimal ep111;
    private BigDecimal ep112;
    private BigDecimal ep113;
    private BigDecimal ep114;
    private BigDecimal ep115;
    private BigDecimal ep116;
    private BigDecimal ep117;
    private BigDecimal ep118;
    private BigDecimal ep119;
    private BigDecimal ep120;
    private BigDecimal ep121;
    private BigDecimal ep122;
    private BigDecimal ep123;
    private BigDecimal ep124;
    private BigDecimal ep125;
    private BigDecimal ep126;
    private BigDecimal ep127;
    private BigDecimal ep128;
    private BigDecimal ep129;
    private BigDecimal ep130;
    private BigDecimal ep131;
    private BigDecimal ep132;
    private BigDecimal ep133;
    private BigDecimal ep134;
    private BigDecimal ep135;
    private BigDecimal ep136;
    private BigDecimal ep137;
    private BigDecimal ep138;
    private BigDecimal ep139;
    private BigDecimal ep140;
    private BigDecimal ep141;
    private BigDecimal ep142;
    private BigDecimal ep143;
    private BigDecimal ep144;

    public void setIacfFeeRate(BigDecimal iacfFeeRate) {
        this.iacfFeeRate = iacfFeeRate;
    }

    public void setQR003(BigDecimal QR003) {
        this.QR003 = QR003;
    }

    public void setQR010(BigDecimal QR010) {
        this.QR010 = QR010;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public void setEntityEName(String entityEName) {
        this.entityEName = entityEName;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public void setEndorseSeqNo(String endorseSeqNo) {
        this.endorseSeqNo = endorseSeqNo;
    }

    public void setRePolicyNo(String rePolicyNo) {
        this.rePolicyNo = rePolicyNo;
    }

    public void setReEndorseSeqNo(String reEndorseSeqNo) {
        this.reEndorseSeqNo = reEndorseSeqNo;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public void setEvaluateApproach(String evaluateApproach) {
        this.evaluateApproach = evaluateApproach;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public void setCmunitNo(String cmunitNo) {
        this.cmunitNo = cmunitNo;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public void setEvaluateDate(String evaluateDate) {
        this.evaluateDate = evaluateDate;
    }

    public void setContractDate(String contractDate) {
        this.contractDate = contractDate;
    }

    public void setEffectiveDateInDate(String effectiveDateInDate) {
        this.effectiveDateInDate = effectiveDateInDate;
    }

    public void setCheckDateInDate(String checkDateInDate) {
        this.checkDateInDate = checkDateInDate;
    }

    public void setExpiryDateInDate(String expiryDateInDate) {
        this.expiryDateInDate = expiryDateInDate;
    }

    public void setPaymentQuarter(Long paymentQuarter) {
        this.paymentQuarter = paymentQuarter;
    }

    public void setEdPremiumPerCoverageDay(BigDecimal edPremiumPerCoverageDay) {
        this.edPremiumPerCoverageDay = edPremiumPerCoverageDay;
    }

    public void setEdPremium(BigDecimal edPremium) {
        this.edPremium = edPremium;
    }

    public void setPriCurEndRemainCsmRate(BigDecimal priCurEndRemainCsmRate) {
        this.priCurEndRemainCsmRate = priCurEndRemainCsmRate;
    }

    public void setPriUntilReportRemainCsmRate(BigDecimal priUntilReportRemainCsmRate) {
        this.priUntilReportRemainCsmRate = priUntilReportRemainCsmRate;
    }

    public void setCumulativeEdRate(BigDecimal cumulativeEdRate) {
        this.cumulativeEdRate = cumulativeEdRate;
    }

    public void setCurEndRemainCsmRate(BigDecimal curEndRemainCsmRate) {
        this.curEndRemainCsmRate = curEndRemainCsmRate;
    }

    public void setUntilReportRemainCsmRate(BigDecimal untilReportRemainCsmRate) {
        this.untilReportRemainCsmRate = untilReportRemainCsmRate;
    }

    public void setElr(BigDecimal elr) {
        this.elr = elr;
    }

    public void setEp1(BigDecimal ep1) {
        this.ep1 = ep1;
    }

    public void setEp2(BigDecimal ep2) {
        this.ep2 = ep2;
    }

    public void setEp3(BigDecimal ep3) {
        this.ep3 = ep3;
    }

    public void setEp4(BigDecimal ep4) {
        this.ep4 = ep4;
    }

    public void setEp5(BigDecimal ep5) {
        this.ep5 = ep5;
    }

    public void setEp6(BigDecimal ep6) {
        this.ep6 = ep6;
    }

    public void setEp7(BigDecimal ep7) {
        this.ep7 = ep7;
    }

    public void setEp8(BigDecimal ep8) {
        this.ep8 = ep8;
    }

    public void setEp9(BigDecimal ep9) {
        this.ep9 = ep9;
    }

    public void setEp10(BigDecimal ep10) {
        this.ep10 = ep10;
    }

    public void setEp11(BigDecimal ep11) {
        this.ep11 = ep11;
    }

    public void setEp12(BigDecimal ep12) {
        this.ep12 = ep12;
    }

    public void setEp13(BigDecimal ep13) {
        this.ep13 = ep13;
    }

    public void setEp14(BigDecimal ep14) {
        this.ep14 = ep14;
    }

    public void setEp15(BigDecimal ep15) {
        this.ep15 = ep15;
    }

    public void setEp16(BigDecimal ep16) {
        this.ep16 = ep16;
    }

    public void setEp17(BigDecimal ep17) {
        this.ep17 = ep17;
    }

    public void setEp18(BigDecimal ep18) {
        this.ep18 = ep18;
    }

    public void setEp19(BigDecimal ep19) {
        this.ep19 = ep19;
    }

    public void setEp20(BigDecimal ep20) {
        this.ep20 = ep20;
    }

    public void setEp21(BigDecimal ep21) {
        this.ep21 = ep21;
    }

    public void setEp22(BigDecimal ep22) {
        this.ep22 = ep22;
    }

    public void setEp23(BigDecimal ep23) {
        this.ep23 = ep23;
    }

    public void setEp24(BigDecimal ep24) {
        this.ep24 = ep24;
    }

    public void setEp25(BigDecimal ep25) {
        this.ep25 = ep25;
    }

    public void setEp26(BigDecimal ep26) {
        this.ep26 = ep26;
    }

    public void setEp27(BigDecimal ep27) {
        this.ep27 = ep27;
    }

    public void setEp28(BigDecimal ep28) {
        this.ep28 = ep28;
    }

    public void setEp29(BigDecimal ep29) {
        this.ep29 = ep29;
    }

    public void setEp30(BigDecimal ep30) {
        this.ep30 = ep30;
    }

    public void setEp31(BigDecimal ep31) {
        this.ep31 = ep31;
    }

    public void setEp32(BigDecimal ep32) {
        this.ep32 = ep32;
    }

    public void setEp33(BigDecimal ep33) {
        this.ep33 = ep33;
    }

    public void setEp34(BigDecimal ep34) {
        this.ep34 = ep34;
    }

    public void setEp35(BigDecimal ep35) {
        this.ep35 = ep35;
    }

    public void setEp36(BigDecimal ep36) {
        this.ep36 = ep36;
    }

    public void setEp37(BigDecimal ep37) {
        this.ep37 = ep37;
    }

    public void setEp38(BigDecimal ep38) {
        this.ep38 = ep38;
    }

    public void setEp39(BigDecimal ep39) {
        this.ep39 = ep39;
    }

    public void setEp40(BigDecimal ep40) {
        this.ep40 = ep40;
    }

    public void setEp41(BigDecimal ep41) {
        this.ep41 = ep41;
    }

    public void setEp42(BigDecimal ep42) {
        this.ep42 = ep42;
    }

    public void setEp43(BigDecimal ep43) {
        this.ep43 = ep43;
    }

    public void setEp44(BigDecimal ep44) {
        this.ep44 = ep44;
    }

    public void setEp45(BigDecimal ep45) {
        this.ep45 = ep45;
    }

    public void setEp46(BigDecimal ep46) {
        this.ep46 = ep46;
    }

    public void setEp47(BigDecimal ep47) {
        this.ep47 = ep47;
    }

    public void setEp48(BigDecimal ep48) {
        this.ep48 = ep48;
    }

    public void setEp49(BigDecimal ep49) {
        this.ep49 = ep49;
    }

    public void setEp50(BigDecimal ep50) {
        this.ep50 = ep50;
    }

    public void setEp51(BigDecimal ep51) {
        this.ep51 = ep51;
    }

    public void setEp52(BigDecimal ep52) {
        this.ep52 = ep52;
    }

    public void setEp53(BigDecimal ep53) {
        this.ep53 = ep53;
    }

    public void setEp54(BigDecimal ep54) {
        this.ep54 = ep54;
    }

    public void setEp55(BigDecimal ep55) {
        this.ep55 = ep55;
    }

    public void setEp56(BigDecimal ep56) {
        this.ep56 = ep56;
    }

    public void setEp57(BigDecimal ep57) {
        this.ep57 = ep57;
    }

    public void setEp58(BigDecimal ep58) {
        this.ep58 = ep58;
    }

    public void setEp59(BigDecimal ep59) {
        this.ep59 = ep59;
    }

    public void setEp60(BigDecimal ep60) {
        this.ep60 = ep60;
    }

    public void setEp61(BigDecimal ep61) {
        this.ep61 = ep61;
    }

    public void setEp62(BigDecimal ep62) {
        this.ep62 = ep62;
    }

    public void setEp63(BigDecimal ep63) {
        this.ep63 = ep63;
    }

    public void setEp64(BigDecimal ep64) {
        this.ep64 = ep64;
    }

    public void setEp65(BigDecimal ep65) {
        this.ep65 = ep65;
    }

    public void setEp66(BigDecimal ep66) {
        this.ep66 = ep66;
    }

    public void setEp67(BigDecimal ep67) {
        this.ep67 = ep67;
    }

    public void setEp68(BigDecimal ep68) {
        this.ep68 = ep68;
    }

    public void setEp69(BigDecimal ep69) {
        this.ep69 = ep69;
    }

    public void setEp70(BigDecimal ep70) {
        this.ep70 = ep70;
    }

    public void setEp71(BigDecimal ep71) {
        this.ep71 = ep71;
    }

    public void setEp72(BigDecimal ep72) {
        this.ep72 = ep72;
    }

    public void setEp73(BigDecimal ep73) {
        this.ep73 = ep73;
    }

    public void setEp74(BigDecimal ep74) {
        this.ep74 = ep74;
    }

    public void setEp75(BigDecimal ep75) {
        this.ep75 = ep75;
    }

    public void setEp76(BigDecimal ep76) {
        this.ep76 = ep76;
    }

    public void setEp77(BigDecimal ep77) {
        this.ep77 = ep77;
    }

    public void setEp78(BigDecimal ep78) {
        this.ep78 = ep78;
    }

    public void setEp79(BigDecimal ep79) {
        this.ep79 = ep79;
    }

    public void setEp80(BigDecimal ep80) {
        this.ep80 = ep80;
    }

    public void setEp81(BigDecimal ep81) {
        this.ep81 = ep81;
    }

    public void setEp82(BigDecimal ep82) {
        this.ep82 = ep82;
    }

    public void setEp83(BigDecimal ep83) {
        this.ep83 = ep83;
    }

    public void setEp84(BigDecimal ep84) {
        this.ep84 = ep84;
    }

    public void setEp85(BigDecimal ep85) {
        this.ep85 = ep85;
    }

    public void setEp86(BigDecimal ep86) {
        this.ep86 = ep86;
    }

    public void setEp87(BigDecimal ep87) {
        this.ep87 = ep87;
    }

    public void setEp88(BigDecimal ep88) {
        this.ep88 = ep88;
    }

    public void setEp89(BigDecimal ep89) {
        this.ep89 = ep89;
    }

    public void setEp90(BigDecimal ep90) {
        this.ep90 = ep90;
    }

    public void setEp91(BigDecimal ep91) {
        this.ep91 = ep91;
    }

    public void setEp92(BigDecimal ep92) {
        this.ep92 = ep92;
    }

    public void setEp93(BigDecimal ep93) {
        this.ep93 = ep93;
    }

    public void setEp94(BigDecimal ep94) {
        this.ep94 = ep94;
    }

    public void setEp95(BigDecimal ep95) {
        this.ep95 = ep95;
    }

    public void setEp96(BigDecimal ep96) {
        this.ep96 = ep96;
    }

    public void setEp97(BigDecimal ep97) {
        this.ep97 = ep97;
    }

    public void setEp98(BigDecimal ep98) {
        this.ep98 = ep98;
    }

    public void setEp99(BigDecimal ep99) {
        this.ep99 = ep99;
    }

    public void setEp100(BigDecimal ep100) {
        this.ep100 = ep100;
    }

    public void setEp101(BigDecimal ep101) {
        this.ep101 = ep101;
    }

    public void setEp102(BigDecimal ep102) {
        this.ep102 = ep102;
    }

    public void setEp103(BigDecimal ep103) {
        this.ep103 = ep103;
    }

    public void setEp104(BigDecimal ep104) {
        this.ep104 = ep104;
    }

    public void setEp105(BigDecimal ep105) {
        this.ep105 = ep105;
    }

    public void setEp106(BigDecimal ep106) {
        this.ep106 = ep106;
    }

    public void setEp107(BigDecimal ep107) {
        this.ep107 = ep107;
    }

    public void setEp108(BigDecimal ep108) {
        this.ep108 = ep108;
    }

    public void setEp109(BigDecimal ep109) {
        this.ep109 = ep109;
    }

    public void setEp110(BigDecimal ep110) {
        this.ep110 = ep110;
    }

    public void setEp111(BigDecimal ep111) {
        this.ep111 = ep111;
    }

    public void setEp112(BigDecimal ep112) {
        this.ep112 = ep112;
    }

    public void setEp113(BigDecimal ep113) {
        this.ep113 = ep113;
    }

    public void setEp114(BigDecimal ep114) {
        this.ep114 = ep114;
    }

    public void setEp115(BigDecimal ep115) {
        this.ep115 = ep115;
    }

    public void setEp116(BigDecimal ep116) {
        this.ep116 = ep116;
    }

    public void setEp117(BigDecimal ep117) {
        this.ep117 = ep117;
    }

    public void setEp118(BigDecimal ep118) {
        this.ep118 = ep118;
    }

    public void setEp119(BigDecimal ep119) {
        this.ep119 = ep119;
    }

    public void setEp120(BigDecimal ep120) {
        this.ep120 = ep120;
    }

    public void setEp121(BigDecimal ep121) {
        this.ep121 = ep121;
    }

    public void setEp122(BigDecimal ep122) {
        this.ep122 = ep122;
    }

    public void setEp123(BigDecimal ep123) {
        this.ep123 = ep123;
    }

    public void setEp124(BigDecimal ep124) {
        this.ep124 = ep124;
    }

    public void setEp125(BigDecimal ep125) {
        this.ep125 = ep125;
    }

    public void setEp126(BigDecimal ep126) {
        this.ep126 = ep126;
    }

    public void setEp127(BigDecimal ep127) {
        this.ep127 = ep127;
    }

    public void setEp128(BigDecimal ep128) {
        this.ep128 = ep128;
    }

    public void setEp129(BigDecimal ep129) {
        this.ep129 = ep129;
    }

    public void setEp130(BigDecimal ep130) {
        this.ep130 = ep130;
    }

    public void setEp131(BigDecimal ep131) {
        this.ep131 = ep131;
    }

    public void setEp132(BigDecimal ep132) {
        this.ep132 = ep132;
    }

    public void setEp133(BigDecimal ep133) {
        this.ep133 = ep133;
    }

    public void setEp134(BigDecimal ep134) {
        this.ep134 = ep134;
    }

    public void setEp135(BigDecimal ep135) {
        this.ep135 = ep135;
    }

    public void setEp136(BigDecimal ep136) {
        this.ep136 = ep136;
    }

    public void setEp137(BigDecimal ep137) {
        this.ep137 = ep137;
    }

    public void setEp138(BigDecimal ep138) {
        this.ep138 = ep138;
    }

    public void setEp139(BigDecimal ep139) {
        this.ep139 = ep139;
    }

    public void setEp140(BigDecimal ep140) {
        this.ep140 = ep140;
    }

    public void setEp141(BigDecimal ep141) {
        this.ep141 = ep141;
    }

    public void setEp142(BigDecimal ep142) {
        this.ep142 = ep142;
    }

    public void setEp143(BigDecimal ep143) {
        this.ep143 = ep143;
    }

    public void setEp144(BigDecimal ep144) {
        this.ep144 = ep144;
    }


    public String getActionNo() {
        return actionNo;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public String getEndorseSeqNo() {
        return endorseSeqNo;
    }

    public String getRePolicyNo() {
        return rePolicyNo;
    }

    public String getReEndorseSeqNo() {
        return reEndorseSeqNo;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public String getEvaluateApproach() {
        return evaluateApproach;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public String getCmunitNo() {
        return cmunitNo;
    }

    public String getProductCode() {
        return productCode;
    }

    public String getEvaluateDate() {
        return evaluateDate;
    }

    public String getContractDate() {
        return contractDate;
    }

    public String getEffectiveDateInDate() {
        return effectiveDateInDate;
    }

    public String getCheckDateInDate() {
        return checkDateInDate;
    }

    public String getExpiryDateInDate() {
        return expiryDateInDate;
    }

    public Long getPaymentQuarter() {
        return paymentQuarter;
    }

    public BigDecimal getEdPremiumPerCoverageDay() {
        return edPremiumPerCoverageDay;
    }

    public BigDecimal getEdPremium() {
        return edPremium;
    }

    public BigDecimal getPriCurEndRemainCsmRate() {
        return priCurEndRemainCsmRate;
    }

    public BigDecimal getPriUntilReportRemainCsmRate() {
        return priUntilReportRemainCsmRate;
    }

    public BigDecimal getCumulativeEdRate() {
        return cumulativeEdRate;
    }

    public BigDecimal getCurEndRemainCsmRate() {
        return curEndRemainCsmRate;
    }

    public BigDecimal getUntilReportRemainCsmRate() {
        return untilReportRemainCsmRate;
    }

    public BigDecimal getElr() {
        return elr;
    }

    public BigDecimal getIacfFeeRate() {
        return iacfFeeRate;
    }

    public BigDecimal getQR003() {
        return QR003;
    }

    public BigDecimal getQR010() {
        return QR010;
    }

    public String getEntityCode() {
        return entityCode;
    }

    public String getEntityEName() {
        return entityEName;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public BigDecimal getEp1() {
        return ep1;
    }

    public BigDecimal getEp2() {
        return ep2;
    }

    public BigDecimal getEp3() {
        return ep3;
    }

    public BigDecimal getEp4() {
        return ep4;
    }

    public BigDecimal getEp5() {
        return ep5;
    }

    public BigDecimal getEp6() {
        return ep6;
    }

    public BigDecimal getEp7() {
        return ep7;
    }

    public BigDecimal getEp8() {
        return ep8;
    }

    public BigDecimal getEp9() {
        return ep9;
    }

    public BigDecimal getEp10() {
        return ep10;
    }

    public BigDecimal getEp11() {
        return ep11;
    }

    public BigDecimal getEp12() {
        return ep12;
    }

    public BigDecimal getEp13() {
        return ep13;
    }

    public BigDecimal getEp14() {
        return ep14;
    }

    public BigDecimal getEp15() {
        return ep15;
    }

    public BigDecimal getEp16() {
        return ep16;
    }

    public BigDecimal getEp17() {
        return ep17;
    }

    public BigDecimal getEp18() {
        return ep18;
    }

    public BigDecimal getEp19() {
        return ep19;
    }

    public BigDecimal getEp20() {
        return ep20;
    }

    public BigDecimal getEp21() {
        return ep21;
    }

    public BigDecimal getEp22() {
        return ep22;
    }

    public BigDecimal getEp23() {
        return ep23;
    }

    public BigDecimal getEp24() {
        return ep24;
    }

    public BigDecimal getEp25() {
        return ep25;
    }

    public BigDecimal getEp26() {
        return ep26;
    }

    public BigDecimal getEp27() {
        return ep27;
    }

    public BigDecimal getEp28() {
        return ep28;
    }

    public BigDecimal getEp29() {
        return ep29;
    }

    public BigDecimal getEp30() {
        return ep30;
    }

    public BigDecimal getEp31() {
        return ep31;
    }

    public BigDecimal getEp32() {
        return ep32;
    }

    public BigDecimal getEp33() {
        return ep33;
    }

    public BigDecimal getEp34() {
        return ep34;
    }

    public BigDecimal getEp35() {
        return ep35;
    }

    public BigDecimal getEp36() {
        return ep36;
    }

    public BigDecimal getEp37() {
        return ep37;
    }

    public BigDecimal getEp38() {
        return ep38;
    }

    public BigDecimal getEp39() {
        return ep39;
    }

    public BigDecimal getEp40() {
        return ep40;
    }

    public BigDecimal getEp41() {
        return ep41;
    }

    public BigDecimal getEp42() {
        return ep42;
    }

    public BigDecimal getEp43() {
        return ep43;
    }

    public BigDecimal getEp44() {
        return ep44;
    }

    public BigDecimal getEp45() {
        return ep45;
    }

    public BigDecimal getEp46() {
        return ep46;
    }

    public BigDecimal getEp47() {
        return ep47;
    }

    public BigDecimal getEp48() {
        return ep48;
    }

    public BigDecimal getEp49() {
        return ep49;
    }

    public BigDecimal getEp50() {
        return ep50;
    }

    public BigDecimal getEp51() {
        return ep51;
    }

    public BigDecimal getEp52() {
        return ep52;
    }

    public BigDecimal getEp53() {
        return ep53;
    }

    public BigDecimal getEp54() {
        return ep54;
    }

    public BigDecimal getEp55() {
        return ep55;
    }

    public BigDecimal getEp56() {
        return ep56;
    }

    public BigDecimal getEp57() {
        return ep57;
    }

    public BigDecimal getEp58() {
        return ep58;
    }

    public BigDecimal getEp59() {
        return ep59;
    }

    public BigDecimal getEp60() {
        return ep60;
    }

    public BigDecimal getEp61() {
        return ep61;
    }

    public BigDecimal getEp62() {
        return ep62;
    }

    public BigDecimal getEp63() {
        return ep63;
    }

    public BigDecimal getEp64() {
        return ep64;
    }

    public BigDecimal getEp65() {
        return ep65;
    }

    public BigDecimal getEp66() {
        return ep66;
    }

    public BigDecimal getEp67() {
        return ep67;
    }

    public BigDecimal getEp68() {
        return ep68;
    }

    public BigDecimal getEp69() {
        return ep69;
    }

    public BigDecimal getEp70() {
        return ep70;
    }

    public BigDecimal getEp71() {
        return ep71;
    }

    public BigDecimal getEp72() {
        return ep72;
    }

    public BigDecimal getEp73() {
        return ep73;
    }

    public BigDecimal getEp74() {
        return ep74;
    }

    public BigDecimal getEp75() {
        return ep75;
    }

    public BigDecimal getEp76() {
        return ep76;
    }

    public BigDecimal getEp77() {
        return ep77;
    }

    public BigDecimal getEp78() {
        return ep78;
    }

    public BigDecimal getEp79() {
        return ep79;
    }

    public BigDecimal getEp80() {
        return ep80;
    }

    public BigDecimal getEp81() {
        return ep81;
    }

    public BigDecimal getEp82() {
        return ep82;
    }

    public BigDecimal getEp83() {
        return ep83;
    }

    public BigDecimal getEp84() {
        return ep84;
    }

    public BigDecimal getEp85() {
        return ep85;
    }

    public BigDecimal getEp86() {
        return ep86;
    }

    public BigDecimal getEp87() {
        return ep87;
    }

    public BigDecimal getEp88() {
        return ep88;
    }

    public BigDecimal getEp89() {
        return ep89;
    }

    public BigDecimal getEp90() {
        return ep90;
    }

    public BigDecimal getEp91() {
        return ep91;
    }

    public BigDecimal getEp92() {
        return ep92;
    }

    public BigDecimal getEp93() {
        return ep93;
    }

    public BigDecimal getEp94() {
        return ep94;
    }

    public BigDecimal getEp95() {
        return ep95;
    }

    public BigDecimal getEp96() {
        return ep96;
    }

    public BigDecimal getEp97() {
        return ep97;
    }

    public BigDecimal getEp98() {
        return ep98;
    }

    public BigDecimal getEp99() {
        return ep99;
    }

    public BigDecimal getEp100() {
        return ep100;
    }

    public BigDecimal getEp101() {
        return ep101;
    }

    public BigDecimal getEp102() {
        return ep102;
    }

    public BigDecimal getEp103() {
        return ep103;
    }

    public BigDecimal getEp104() {
        return ep104;
    }

    public BigDecimal getEp105() {
        return ep105;
    }

    public BigDecimal getEp106() {
        return ep106;
    }

    public BigDecimal getEp107() {
        return ep107;
    }

    public BigDecimal getEp108() {
        return ep108;
    }

    public BigDecimal getEp109() {
        return ep109;
    }

    public BigDecimal getEp110() {
        return ep110;
    }

    public BigDecimal getEp111() {
        return ep111;
    }

    public BigDecimal getEp112() {
        return ep112;
    }

    public BigDecimal getEp113() {
        return ep113;
    }

    public BigDecimal getEp114() {
        return ep114;
    }

    public BigDecimal getEp115() {
        return ep115;
    }

    public BigDecimal getEp116() {
        return ep116;
    }

    public BigDecimal getEp117() {
        return ep117;
    }

    public BigDecimal getEp118() {
        return ep118;
    }

    public BigDecimal getEp119() {
        return ep119;
    }

    public BigDecimal getEp120() {
        return ep120;
    }

    public BigDecimal getEp121() {
        return ep121;
    }

    public BigDecimal getEp122() {
        return ep122;
    }

    public BigDecimal getEp123() {
        return ep123;
    }

    public BigDecimal getEp124() {
        return ep124;
    }

    public BigDecimal getEp125() {
        return ep125;
    }

    public BigDecimal getEp126() {
        return ep126;
    }

    public BigDecimal getEp127() {
        return ep127;
    }

    public BigDecimal getEp128() {
        return ep128;
    }

    public BigDecimal getEp129() {
        return ep129;
    }

    public BigDecimal getEp130() {
        return ep130;
    }

    public BigDecimal getEp131() {
        return ep131;
    }

    public BigDecimal getEp132() {
        return ep132;
    }

    public BigDecimal getEp133() {
        return ep133;
    }

    public BigDecimal getEp134() {
        return ep134;
    }

    public BigDecimal getEp135() {
        return ep135;
    }

    public BigDecimal getEp136() {
        return ep136;
    }

    public BigDecimal getEp137() {
        return ep137;
    }

    public BigDecimal getEp138() {
        return ep138;
    }

    public BigDecimal getEp139() {
        return ep139;
    }

    public BigDecimal getEp140() {
        return ep140;
    }

    public BigDecimal getEp141() {
        return ep141;
    }

    public BigDecimal getEp142() {
        return ep142;
    }

    public BigDecimal getEp143() {
        return ep143;
    }

    public BigDecimal getEp144() {
        return ep144;
    }

    public String getEffectiveDateBom() {
        return effectiveDateBom;
    }

    public void setEffectiveDateBom(String effectiveDateBom) {
        this.effectiveDateBom = effectiveDateBom;
    }

    public String getExpiryDateEom() {
        return expiryDateEom;
    }

    public void setExpiryDateEom(String expiryDateEom) {
        this.expiryDateEom = expiryDateEom;
    }

    public String getPremiumFrequency() {
        return premiumFrequency;
    }

    public void setPremiumFrequency(String premiumFrequency) {
        this.premiumFrequency = premiumFrequency;
    }

    public Long getPremiumTerm() {
        return premiumTerm;
    }

    public void setPremiumTerm(Long premiumTerm) {
        this.premiumTerm = premiumTerm;
    }

    public BigDecimal getGrossPremium() {
        return grossPremium;
    }

    public void setGrossPremium(BigDecimal grossPremium) {
        this.grossPremium = grossPremium;
    }

    public BigDecimal getCoverageAmount() {
        return coverageAmount;
    }

    public void setCoverageAmount(BigDecimal coverageAmount) {
        this.coverageAmount = coverageAmount;
    }

    public Long getPassedDates() {
        return passedDates;
    }

    public void setPassedDates(Long passedDates) {
        this.passedDates = passedDates;
    }

    public Long getPassedMonths() {
        return passedMonths;
    }

    public void setPassedMonths(Long passedMonths) {
        this.passedMonths = passedMonths;
    }

    public Long getRemainingMonths() {
        return remainingMonths;
    }

    public void setRemainingMonths(Long remainingMonths) {
        this.remainingMonths = remainingMonths;
    }

    public Long getRemainingPremTermPe() {
        return remainingPremTermPe;
    }

    public void setRemainingPremTermPe(Long remainingPremTermPe) {
        this.remainingPremTermPe = remainingPremTermPe;
    }

    public Long getRemainingMonthsFuture() {
        return remainingMonthsFuture;
    }

    public void setRemainingMonthsFuture(Long remainingMonthsFuture) {
        this.remainingMonthsFuture = remainingMonthsFuture;
    }

    public Long getRemainingPremTermCb() {
        return remainingPremTermCb;
    }

    public void setRemainingPremTermCb(Long remainingPremTermCb) {
        this.remainingPremTermCb = remainingPremTermCb;
    }
}