package com.ss.ifrs.actuarial.pojo.other.vo;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class AtrBussDataAnalysisRespVo implements Serializable {

    /**
     * 规则id
     */
    @ApiModelProperty("规则id")
    private Long itemRuleId;

    /**
     * 值
     */
    @ApiModelProperty("值")
    private BigDecimal value;


    /**
     * 维度值
     */
    @ApiModelProperty("维度值")
    private String dimension;

    /**
     * 计量因子id
     */
    @ApiModelProperty("计量因子id")
    private Long factorDefId;

    /**
     * 计量因子编码
     */
    @ApiModelProperty("计量因子编码")
    private String factorCode;

    /**
     * 计量因子英文名称
     */
    @ApiModelProperty("计量因子英文名称")
    private String factorEName;

    /**
     * 计量因子中文名称
     */
    @ApiModelProperty("计量因子中文名称")
    private String factorCName;

    /**
     * 计量因子本地名称
     */
    @ApiModelProperty("计量因子本地名称")
    private String factorLName;

    private static final long serialVersionUID = 1L;


    public Long getItemRuleId() {
        return itemRuleId;
    }

    public void setItemRuleId(Long itemRuleId) {
        this.itemRuleId = itemRuleId;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public Long getFactorDefId() {
        return factorDefId;
    }

    public void setFactorDefId(Long factorDefId) {
        this.factorDefId = factorDefId;
    }

    public String getFactorCode() {
        return factorCode;
    }

    public void setFactorCode(String factorCode) {
        this.factorCode = factorCode;
    }

    public String getFactorEName() {
        return factorEName;
    }

    public void setFactorEName(String factorEName) {
        this.factorEName = factorEName;
    }

    public String getFactorCName() {
        return factorCName;
    }

    public void setFactorCName(String factorCName) {
        this.factorCName = factorCName;
    }

    public String getFactorLName() {
        return factorLName;
    }

    public void setFactorLName(String factorLName) {
        this.factorLName = factorLName;
    }
}
