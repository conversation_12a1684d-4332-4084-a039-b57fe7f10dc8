package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussAutoquotaAction;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter @Getter
public class AtrBussAutoquotaActionVo extends AtrBussAutoquotaAction {

    @ApiModelProperty(value = "提取截止日期 (起)")
    private Date deadlineBegin;

    @ApiModelProperty(value = "提取截止日期 (止)")
    private Date deadlineEnd;

    @ApiModelProperty(value = "操作时间 (起)")
    private Date operTimeBegin;

    @ApiModelProperty(value = "操作时间 (止)")
    private Date operTimeEnd;

    @ApiModelProperty(value = "当前会话语言")
    private String language;

    @ApiModelProperty(value = "loa 名称")
    private String loaName;

    @ApiModelProperty(value = "entity 代码")
    private String entityCode;

    @ApiModelProperty(value = "entity 名称")
    private String entityName;

}
