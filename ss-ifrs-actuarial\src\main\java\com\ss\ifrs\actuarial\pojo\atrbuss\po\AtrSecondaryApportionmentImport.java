/**
 * 
 * This file was generated by Taiping MyBatis Generator(v1.2.12).
 * Date: 2024-07-29 15:26:38
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by Taiping MyBatis Generator(v1.2.12).
 * Create Date: 2024-07-29 15:26:38<br/>
 * Description: IBNR-二次分摊导入表<br/>
 * Table Name: ATR_SECONDARY_APPORTIONMENT_IMPORT<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "IBNR-二次分摊导入表")
public class AtrSecondaryApportionmentImport implements Serializable {
    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_IMPORT.ID
     * Database remarks: Id|主键
     */
    @ApiModelProperty(value = "Id|主键", required = true)
    private Long id;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_IMPORT.RISK_FLAG
     * Database remarks: 分类标志1监管大类2评估分类
     */
    @ApiModelProperty(value = "分类标志1监管大类2评估分类", required = false)
    private String riskFlag;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_IMPORT.DRAW_MONTH
     * Database remarks: 评估月份
     */
    @ApiModelProperty(value = "评估月份", required = false)
    private String drawMonth;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_IMPORT.RISK_CATAGORY
     * Database remarks: 险种大类代码
     */
    @ApiModelProperty(value = "险种大类代码", required = false)
    private String riskCatagory;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_IMPORT.COM_CODE
     * Database remarks: 机构代码
     */
    @ApiModelProperty(value = "机构代码", required = false)
    private String comCode;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_IMPORT.IBNR_TYPE
     * Database remarks: 数据类型：与评估分类保持一致，1再保前2再保前直接3再保前分入4再保后5再保后直接6再保后分入
     */
    @ApiModelProperty(value = "数据类型：与评估分类保持一致，1再保前2再保前直接3再保前分入4再保后5再保后直接6再保后分入", required = false)
    private String ibnrType;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_IMPORT.ACC_MONTH
     * Database remarks: 事故月份
     */
    @ApiModelProperty(value = "事故月份", required = false)
    private String accMonth;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_IMPORT.IBNR
     * Database remarks: IBNR评估金额(折币币种-RMB)
     */
    @ApiModelProperty(value = "IBNR评估金额(折币币种-RMB)", required = false)
    private BigDecimal ibnr;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_IMPORT.PREMIUM_WEIGHT
     * Database remarks: 已赚权重
     */
    @ApiModelProperty(value = "已赚权重", required = false)
    private BigDecimal premiumWeight;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_IMPORT.PAID_WEIGHT
     * Database remarks: 已决权重
     */
    @ApiModelProperty(value = "已决权重", required = false)
    private BigDecimal paidWeight;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_IMPORT.BACK_MONTH
     * Database remarks: 回溯月份
     */
    @ApiModelProperty(value = "回溯月份", required = false)
    private Integer backMonth;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_IMPORT.COM_LEVEL
     * Database remarks: 机构层级 1-总公司 2-分公司
     */
    @ApiModelProperty(value = "机构层级 1-总公司 2-分公司", required = false)
    private String comLevel;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_IMPORT.IBNR01
     * Database remarks: ibnr预留字段
     */
    @ApiModelProperty(value = "ibnr预留字段", required = false)
    private String ibnr01;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_IMPORT.IBNR02
     * Database remarks: ibnr预留字段
     */
    @ApiModelProperty(value = "ibnr预留字段", required = false)
    private String ibnr02;

    private String yearMonth;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRiskFlag() {
        return riskFlag;
    }

    public void setRiskFlag(String riskFlag) {
        this.riskFlag = riskFlag;
    }

    public String getDrawMonth() {
        return drawMonth;
    }

    public void setDrawMonth(String drawMonth) {
        this.drawMonth = drawMonth;
    }

    public String getRiskCatagory() {
        return riskCatagory;
    }

    public void setRiskCatagory(String riskCatagory) {
        this.riskCatagory = riskCatagory;
    }

    public String getComCode() {
        return comCode;
    }

    public void setComCode(String comCode) {
        this.comCode = comCode;
    }

    public String getIbnrType() {
        return ibnrType;
    }

    public void setIbnrType(String ibnrType) {
        this.ibnrType = ibnrType;
    }

    public String getAccMonth() {
        return accMonth;
    }

    public void setAccMonth(String accMonth) {
        this.accMonth = accMonth;
    }

    public BigDecimal getIbnr() {
        return ibnr;
    }

    public void setIbnr(BigDecimal ibnr) {
        this.ibnr = ibnr;
    }

    public BigDecimal getPremiumWeight() {
        return premiumWeight;
    }

    public void setPremiumWeight(BigDecimal premiumWeight) {
        this.premiumWeight = premiumWeight;
    }

    public BigDecimal getPaidWeight() {
        return paidWeight;
    }

    public void setPaidWeight(BigDecimal paidWeight) {
        this.paidWeight = paidWeight;
    }

    public Integer getBackMonth() {
        return backMonth;
    }

    public void setBackMonth(Integer backMonth) {
        this.backMonth = backMonth;
    }

    public String getComLevel() {
        return comLevel;
    }

    public void setComLevel(String comLevel) {
        this.comLevel = comLevel;
    }

    public String getIbnr01() {
        return ibnr01;
    }

    public void setIbnr01(String ibnr01) {
        this.ibnr01 = ibnr01;
    }

    public String getIbnr02() {
        return ibnr02;
    }

    public void setIbnr02(String ibnr02) {
        this.ibnr02 = ibnr02;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }
}