/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2022-06-06 10:15:00
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo;

import com.ss.platform.core.annotation.SsTranslateCode;
import com.ss.platform.core.constant.SystemConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2022-06-06 10:15:00<br/>
 * Description: IBNR提取主表<br/>
 * Table Name: atr_buss_reserve_ibnr<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "IBNR提取主表")
public class AtrBussReserveIbnrVo implements Serializable {
    /**
     * Database column: atr_buss_reserve_ibnr.reserve_ibnr_id
     * Database remarks: reserve_ibnr_id|主键
     */
    @ApiModelProperty(value = "reserve_ibnr_id|主键", required = true)
    private Long reserveIbnrId;

    /**
     * Database column: atr_buss_reserve_ibnr.draw_time
     * Database remarks: Draw_Time|提取时间
     */
    @ApiModelProperty(value = "Draw_Time|提取时间", required = true)
    private Date drawTime;

    /**
     * Database column: atr_buss_reserve_ibnr.draw_status
     * Database remarks: Draw_Status|提取状态
     */
    @ApiModelProperty(value = "Draw_Status|提取状态", required = true)
    private String drawStatus;

    /**
     * Database column: atr_buss_reserve_ibnr.task_code
     * Database remarks: Task_Code|任务号
     */
    @ApiModelProperty(value = "Task_Code|任务号", required = true)
    private String taskCode;

    /**
     * Database column: atr_buss_reserve_ibnr.draw_user
     * Database remarks: Draw_User|提取人
     */
    @ApiModelProperty(value = "Draw_User|提取人", required = true)
    private Long drawUser;

    /**
     * Database column: atr_buss_reserve_ibnr.draw_type
     * Database remarks: Draw_Type|提取类型：1-正常提取；2-Excel导入
     */
    @ApiModelProperty(value = "Draw_Type|提取类型：1-正常提取；2-Excel导入", required = true)
    private String drawType;

    /**
     * Database column: atr_buss_reserve_ibnr.center_id
     * Database remarks: Center_Id|业务单位
     */
    @ApiModelProperty(value = "Center_Id|业务单位", required = true)
    private Long entityId;

    /**
     * Database column: atr_buss_reserve_ibnr.risk_class
     * Database remarks: risk_Class|风险大类
     */
    @ApiModelProperty(value = "risk_Class|风险大类", required = true)
    private String loaCode;

    /**
     * Database column: atr_buss_reserve_ibnr.currency
     * Database remarks: Currency|币别
     */
    @ApiModelProperty(value = "Currency|币别", required = true)
    private String currencyCode;

    /**
     * Database column: atr_buss_reserve_ibnr.ibnr_type
     * Database remarks: Ibnr_Type|IBNR类型
     */
    @ApiModelProperty(value = "Ibnr_Type|IBNR类型", required = true)
    @SsTranslateCode(context = SystemConstant.AtrIdentity.APP_CONTEXT,codeCodeIdx = "IbnrType")
    private String ibnrType;

    /**
     * Database column: atr_buss_reserve_ibnr.business_type
     * Database remarks: Business_Type|业务类型
     */
    @ApiModelProperty(value = "Business_Type|业务类型", required = true)
    @SsTranslateCode(context = SystemConstant.BbsIdentity.APP_CONTEXT,codeCodeIdx = "BusinessType/IBNR")
    private String businessSourceCode;

    /**
     * Database column: atr_buss_reserve_ibnr.statis_zones
     * Database remarks: Statis_Zones|提取区间数
     */
    @ApiModelProperty(value = "Statis_Zones|提取区间数", required = true)
    private Long statisZones;

    /**
     * Database column: atr_buss_reserve_ibnr.end_date
     * Database remarks: End_Date|提取终止日期
     */
    @ApiModelProperty(value = "End_Date|提取终止日期", required = true)
    private Date endDate;

    /**
     * Database column: atr_buss_reserve_ibnr.create_time
     * Database remarks: Create_Time|创建时间
     */
    @ApiModelProperty(value = "Create_Time|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: atr_buss_reserve_ibnr.creator_id
     * Database remarks: Creator_Id|创建人
     */
    @ApiModelProperty(value = "Creator_Id|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: atr_buss_reserve_ibnr.update_time
     * Database remarks: Update_Time|最后修改时间
     */
    @ApiModelProperty(value = "Update_Time|最后修改时间", required = false)
    private Date updateTime;

    /**
     * Database column: atr_buss_reserve_ibnr.updator_id
     * Database remarks: Updator_Id|最后修改人
     */
    @ApiModelProperty(value = "Updator_Id|最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: atr_buss_reserve_ibnr.confirm_id
     * Database remarks: confirm_Id|确认人
     */
    @ApiModelProperty(value = "confirm_Id|确认人", required = false)
    private Long confirmId;

    /**
     * Database column: atr_buss_reserve_ibnr.confirm_time
     * Database remarks: confirm_time|确认时间
     */
    @ApiModelProperty(value = "confirm_time|确认时间", required = false)
    private Date confirmTime;

    /**
     * Database column: atr_buss_reserve_ibnr.confirm_is
     * Database remarks: confirm_Is|是否确认
     */
    @ApiModelProperty(value = "confirm_Is|是否确认", required = false)
    private String confirmIs;

    /**
     * Database column: atr_buss_reserve_ibnr.extraction_Method
     * Database remarks: extraction_Method|Ibnr生成方式
     */
    @ApiModelProperty(value = "extraction_Method|Ibnr生成方式", required = false)
    private String extractionMethod;

    /**
     * Database column: atr_buss_reserve_ibnr.extract_interval
     * Database remarks: extract_interval|提取区间
     */
    @ApiModelProperty(value = "extraction_Method|Ibnr生成方式", required = false)
    @SsTranslateCode(context = SystemConstant.AtrIdentity.APP_CONTEXT,codeCodeIdx = "ExtractInterval")
    private String extractInterval;

    private String entityCode;
    private String entityCName;
    private String entityLName;
    private String entityEName;

    private String loaCName;
    private String loaLName;
    private String loaEName;

    private String creatorCode;
    private String creatorName;

    private String fileName;

    //语言
    private String language;

    //表编码，用于Excel导出
    private String bizCode;

    private String targetRouter;

    private String templateFileName;


    private String portfolioNo;

    private List<AtrBussReserveIbnrDetailVo> list;

    private static final long serialVersionUID = 1L;

    public String getLoaCName() {
        return loaCName;
    }

    public void setLoaCName(String loaCName) {
        this.loaCName = loaCName;
    }

    public String getLoaLName() {
        return loaLName;
    }

    public void setLoaLName(String loaLName) {
        this.loaLName = loaLName;
    }

    public String getLoaEName() {
        return loaEName;
    }

    public void setLoaEName(String loaEName) {
        this.loaEName = loaEName;
    }

    public Long getConfirmId() {
        return confirmId;
    }

    public void setConfirmId(Long confirmId) {
        this.confirmId = confirmId;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public String getConfirmIs() {
        return confirmIs;
    }

    public void setConfirmIs(String confirmIs) {
        this.confirmIs = confirmIs;
    }

    public String getEntityCode() {
        return entityCode;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public String getEntityCName() {
        return entityCName;
    }

    public void setEntityCName(String entityCName) {
        this.entityCName = entityCName;
    }

    public String getEntityLName() {
        return entityLName;
    }

    public void setEntityLName(String entityLName) {
        this.entityLName = entityLName;
    }

    public String getEntityEName() {
        return entityEName;
    }

    public void setEntityEName(String entityEName) {
        this.entityEName = entityEName;
    }

    public Long getReserveIbnrId() {
        return reserveIbnrId;
    }

    public void setReserveIbnrId(Long reserveIbnrId) {
        this.reserveIbnrId = reserveIbnrId;
    }

    public Date getDrawTime() {
        return drawTime;
    }

    public void setDrawTime(Date drawTime) {
        this.drawTime = drawTime;
    }

    public String getDrawStatus() {
        return drawStatus;
    }

    public void setDrawStatus(String drawStatus) {
        this.drawStatus = drawStatus;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public Long getDrawUser() {
        return drawUser;
    }

    public void setDrawUser(Long drawUser) {
        this.drawUser = drawUser;
    }

    public String getDrawType() {
        return drawType;
    }

    public void setDrawType(String drawType) {
        this.drawType = drawType;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }	

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String Currency) {
        this.currencyCode = Currency;
    }

    public String getIbnrType() {
        return ibnrType;
    }

    public void setIbnrType(String ibnrType) {
        this.ibnrType = ibnrType;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public Long getStatisZones() {
        return statisZones;
    }

    public void setStatisZones(Long statisZones) {
        this.statisZones = statisZones;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public String getCreatorCode() {
        return creatorCode;
    }

    public void setCreatorCode(String creatorCode) {
        this.creatorCode = creatorCode;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public List<AtrBussReserveIbnrDetailVo> getList() {
        return list;
    }

    public void setList(List<AtrBussReserveIbnrDetailVo> list) {
        this.list = list;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public String getTargetRouter() {
        return targetRouter;
    }

    public void setTargetRouter(String targetRouter) {
        this.targetRouter = targetRouter;
    }

    public String getExtractionMethod() {
        return extractionMethod;
    }

    public void setExtractionMethod(String extractionMethod) {
        this.extractionMethod = extractionMethod;
    }

    public String getExtractInterval() {
        return extractInterval;
    }

    public void setExtractInterval(String extractInterval) {
        this.extractInterval = extractInterval;
    }

    public String getTemplateFileName() {
        return templateFileName;
    }

    public void setTemplateFileName(String templateFileName) {
        this.templateFileName = templateFileName;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }
}