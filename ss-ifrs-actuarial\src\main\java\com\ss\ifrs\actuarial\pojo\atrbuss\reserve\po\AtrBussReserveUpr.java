/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2022-02-14 10:46:48
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2022-02-14 10:46:48<br/>
 * Description: upr提取主表<br/>
 * Table Name: atr_buss_reserve_upr<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "upr提取主表")
public class AtrBussReserveUpr implements Serializable {
    /**
     * Database column: atr_buss_reserve_upr.reserve_upr_id
     * Database remarks: 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    private Long reserveUprId;

    /**
     * Database column: atr_buss_reserve_upr.version_no
     * Database remarks: version_no|版本号
     */
    @ApiModelProperty(value = "version_no|版本号", required = false)
    private String versionNo;

    /**
     * Database column: atr_buss_reserve_upr.center_id
     * Database remarks: 业务单位id
     */
    @ApiModelProperty(value = "业务单位id", required = false)
    private Long entityId;

    /**
     * Database column: atr_buss_reserve_upr.year_month
     * Database remarks: 提取年月
     */
    @ApiModelProperty(value = "提取年月", required = false)
    private String yearMonth;

    /**
     * Database column: atr_buss_reserve_upr.risk_class
     * Database remarks: 险类
     */
//    @ApiModelProperty(value = "险类", required = false)
//    private String riskClassCode;

    private String loaCode;

    /**
     * Database column: atr_buss_reserve_upr.risk_code
     * Database remarks: 险种
     */
    @ApiModelProperty(value = "险种", required = false)
    private String riskCode;

    /**
     * Database column: atr_buss_reserve_upr.data_source
     * Database remarks: null
     */
    private String dataSource;

    /**
     * Database column: atr_buss_reserve_upr.atr_type
     * Database remarks: atr_type|提取类型
     */
    @ApiModelProperty(value = "atr_type|提取类型", required = false)
    private String atrType;

    /**
     * Database column: atr_buss_reserve_upr.draw_time
     * Database remarks: draw_end_time|提取终止时间
     */
    @ApiModelProperty(value = "draw_end_time|提取终止时间", required = false)
    private Date drawTime;

    /**
     * Database column: atr_buss_reserve_upr.confirm_is
     * Database remarks: is_confirm|是否确认
     */
    @ApiModelProperty(value = "is_confirm|是否确认", required = false)
    private String confirmIs;

    /**
     * Database column: atr_buss_reserve_upr.confirm_id
     * Database remarks: confirm_id|确认人
     */
    @ApiModelProperty(value = "confirm_id|确认人", required = false)
    private Long confirmId;

    /**
     * Database column: atr_buss_reserve_upr.confirm_time
     * Database remarks: confirm_time|确认时间
     */
    @ApiModelProperty(value = "confirm_time|确认时间", required = false)
    private Date confirmTime;

    /**
     * Database column: atr_buss_reserve_upr.creator_id
     * Database remarks: Creator_Id|创建人
     */
    @ApiModelProperty(value = "Creator_Id|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: atr_buss_reserve_upr.create_time
     * Database remarks: Create_Time|创建时间
     */
    @ApiModelProperty(value = "Create_Time|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: atr_buss_reserve_upr.updator_id
     * Database remarks: Updator_Id|最后修改人
     */
    @ApiModelProperty(value = "Updator_Id|最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: atr_buss_reserve_upr.update_time
     * Database remarks: Update_Time|最后修改时间
     */
    @ApiModelProperty(value = "Update_Time|最后修改时间", required = false)
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public Long getReserveUprId() {
        return reserveUprId;
    }

    public void setReserveUprId(Long reserveUprId) {
        this.reserveUprId = reserveUprId;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

//    public String getRiskClassCode() {
//        return riskClassCode;
//    }
//
//    public void setRiskClassCode(String riskClassCode) {
//        this.riskClassCode = riskClassCode;
//    }


    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public String getRiskCode() {
        return riskCode;
    }

    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public String getAtrType() {
        return atrType;
    }

    public void setAtrType(String atrType) {
        this.atrType = atrType;
    }

    public Date getDrawTime() {
        return drawTime;
    }

    public void setDrawTime(Date drawTime) {
        this.drawTime = drawTime;
    }

    public String getConfirmIs() {
        return confirmIs;
    }

    public void setConfirmIs(String confirmIs) {
        this.confirmIs = confirmIs;
    }

    public Long getConfirmId() {
        return confirmId;
    }

    public void setConfirmId(Long confirmId) {
        this.confirmId = confirmId;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}