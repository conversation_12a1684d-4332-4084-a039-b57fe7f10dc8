package com.ss.ifrs.actuarial.dao;

import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapPlanImportMainVo;
import com.ss.ifrs.actuarial.pojo.ecf.vo.AtrSD7;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.ti.*;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface AtrBussLrcTiDao {

    long getIcuMaxMainId();

    long getIcgMaxMainId();

    void truncateBaseData();

    void partitionBaseData(Map<String, Object> paramMap);

    List<AtrBussLrcTiIcu> findInitIcu(String yearMonth);

    List<AtrDapLrcTiPaid> findDapPaid(Map<?, ?> paramMap);

    List<AtrDapLrcTiPayPlan> findPayPlan(Map<?, ?> paramMap);

    List<AtrDapLrcTiEdPlan> findEdPlan(Map<?, ?> paramMap);

    List<AtrBussRiskKindCodeToAcc> listRiskKindCodeToAcc();

    AtrBussBaseEntity getDefaultBaseEntityCode();

    /**
     * 查询所有合约险类的已赚保费发展期数据
     * @return 所有合约险类的已赚保费发展期数据列表
     */
    List<AtrDapLrcTiEdPlan> findAllEdPlanByDevPeriod(Map<String,Object> paramMap);

    List<AtrDapExpAlloc> findExpAlloc(Map<?, ?> paramMap);

    List<AtrDuctLrcTiIcuPre> findPreIcu(Map<String, Object> paramMap);


    void batchSaveDapTiPaymentPlan(List<AtrDapLrcTiPayPlan> paramList);


    List<AtrBussTiLrcIcgPremium> listPreIcgPremium(Map<String,Object> paramMap);

    /**
     * 批量保存TI已赚保费计划数据
     * @param edPlanList 已赚保费计划对象列表
     * @return 影响行数
     */
    int batchSaveDapTiEdPlan(List<AtrDapLrcTiEdPlan> edPlanList);

    /**
     * 查询EPI导入数据列表
     * @param entityId 业务单位ID
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<Map<String, Object>> findEpiImportList(AtrDapPlanImportMainVo atrDapPlanImportMainVo, Pageable pageable);

    /**
     * 查询已赚保费导入数据列表
     * @param entityId 业务单位ID
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<Map<String, Object>> findEarnedImportList(AtrDapPlanImportMainVo atrDapPlanImportMainVo, Pageable pageable);

    /**
     * 查询EPI导入数据详情
     * @param entityId 业务单位ID
     * @param treatyNo 合约号
     * @param riskClassCode 险类代码
     * @return 详情数据列表
     */
    List<Map<String, Object>> findEpiImportDetail(AtrDapPlanImportMainVo atrDapPlanImportMainVo);

    /**
     * 查询已赚保费导入数据详情
     * @param atrDapPlanImportMainVo 业务单位ID
     * @return 详情数据列表
     */
    List<Map<String, Object>> findEarnedImportDetail(AtrDapPlanImportMainVo atrDapPlanImportMainVo);

    /**
     * 删除EPI导入数据
     * @param entityId 业务单位ID
     * @param treatyNo 合约号
     * @param riskClassCode 险类代码
     * @return 影响行数
     */
    int deleteEpiImportData(Long entityId,String yearMonth, String treatyNo, String riskClassCode);

    /**
     * 删除已赚保费导入数据
     * @param entityId 业务单位ID
     * @param treatyNo 合约号
     * @param riskClassCode 险类代码
     * @return 影响行数
     */
    int deleteEarnedImportData(Long entityId,String yearMonth, String treatyNo, String riskClassCode);

    Page<AtrDapPlanImportMainVo> fuzzySearchPage(AtrDapPlanImportMainVo atrDapPlanImportMainVo, Pageable pageParam);

}