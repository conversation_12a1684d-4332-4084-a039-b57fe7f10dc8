package com.ss.ifrs.actuarial.api;

import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfRiskRefVo;
import com.ss.ifrs.actuarial.service.AtrConfRiskRefService;
import com.ss.ifrs.actuarial.service.AtrExportService;
import com.ss.platform.core.annotation.TrackUserBehavioral;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.platform.util.CheckParamsUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/3/23
 */
@RestController
@RequestMapping("/conf")
@Api(value = "风险分布模式配置")
public class AtrConfRiskRefApi extends BaseApi{

    @Autowired
    AtrConfRiskRefService atrConfRiskRefService;

    @Autowired
    AtrExportService atrExportService;

    @ApiOperation(value = "增加指标配置信息")
    @RequestMapping(value = "/riskRef/add", method = RequestMethod.POST)
    public BaseResponse<Object> add(@RequestBody @Validated
                                                 AtrConfRiskRefVo atrConfRiskRefVo, HttpServletRequest request, BindingResult br
    ) {
        if (br.hasErrors()) {
            // 该状态码为“其它格式转换错误”
            StringBuffer errorMessages = new StringBuffer();
            for (int i = 0; i < br.getErrorCount(); i++) {
                errorMessages.append(br.getAllErrors().get(i).getDefaultMessage() + ";");
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.FORMAT_TRANSFORM_ERROR,new CheckParamsUtil().getMessages(errorMessages.toString()));
        }
        Long userId = this.loginUserId(request);
        atrConfRiskRefVo.setCreatorId(userId);
        atrConfRiskRefVo.setCreateTime(new Date());
        try {
            atrConfRiskRefService.save(atrConfRiskRefVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "根据id查询指标配置信息")
    @RequestMapping(value = "/riskRef/find_by_id/{riskRefId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<AtrConfRiskRefVo> findById(@PathVariable("riskRefId") Long riskRefId) {
        AtrConfRiskRefVo atrConfRiskRefVo = atrConfRiskRefService.findById(riskRefId);
        return new BaseResponse<AtrConfRiskRefVo>(ResCodeConstant.ResCode.SUCCESS, atrConfRiskRefVo);
    }

    @ApiOperation(value = "根据id查询指标配置信息")
    @RequestMapping(value = "/riskRef/his/find_by_id/{riskRefHisId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<AtrConfRiskRefVo> findHisById(@PathVariable("riskRefHisId") Long riskRefHisId) {
        AtrConfRiskRefVo atrConfRiskRefVo = atrConfRiskRefService.findHisById(riskRefHisId);
        return new BaseResponse<AtrConfRiskRefVo>(ResCodeConstant.ResCode.SUCCESS, atrConfRiskRefVo);
    }

    @ApiOperation(value = "查询指标配置列表")
    @RequestMapping(value = "/riskRef/enquiry", method = RequestMethod.POST)
    public BaseResponse<Map<String, Object>> enquiry(@RequestBody AtrConfRiskRefVo atrConfRiskRefVo, int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrConfRiskRefVo> atrConfRiskRefVoPage = atrConfRiskRefService.searchPage(atrConfRiskRefVo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("atrConfRiskRefVoList", atrConfRiskRefVoPage);
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "修改指标配置信息")
        @RequestMapping(value = "/riskRef/edit", method = RequestMethod.POST)
    public BaseResponse<Object> edit(@RequestBody @Validated AtrConfRiskRefVo atrConfRiskRefVo, HttpServletRequest request, BindingResult br) {
        if (br.hasErrors()) {
            // 该状态码为“其它格式转换错误”
            StringBuffer errorMessages = new StringBuffer();
            for (int i = 0; i < br.getErrorCount(); i++) {
                errorMessages.append(br.getAllErrors().get(i).getDefaultMessage() + ";");
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.FORMAT_TRANSFORM_ERROR,new CheckParamsUtil().getMessages(errorMessages.toString()));
        }
        atrConfRiskRefVo.setUpdatorId(this.loginUserId(request));
        atrConfRiskRefVo.setUpdateTime(new Date());
        atrConfRiskRefVo.setAuditState("0");
        atrConfRiskRefVo.setCheckedMsg(null);
        atrConfRiskRefVo.setCheckedId(null);
        atrConfRiskRefVo.setCheckedTime(null);
        try {
            Long userId = this.loginUserId(request);
            atrConfRiskRefService.update(atrConfRiskRefVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }


    @ApiOperation(value = "批量删除指标配置信息")
    @RequestMapping(value = "/riskRef/delete_by_id/{riskRefId}", method = RequestMethod.GET)
    public BaseResponse<Object> delete(HttpServletRequest request, @PathVariable("riskRefId") Long riskRefId) {
        try {
            Long userId = this.loginUserId(request);
            atrConfRiskRefService.delete(riskRefId, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "有效状态更新")
    @RequestMapping(value = "/riskRef/valid_status", method = RequestMethod.POST)
    public BaseResponse<Object> validStatus(HttpServletRequest request, @RequestBody AtrConfRiskRefVo atrConfRiskRefVo) {
        atrConfRiskRefVo.setUpdatorId(this.loginUserId(request));
        Long userId = this.loginUserId(request);
        try {
            atrConfRiskRefService.updateValid(atrConfRiskRefVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, "SUCCESS");
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "批量审核指标配置")
    @RequestMapping(value = "/riskRef/batch_audit", method = RequestMethod.POST)
    public BaseResponse<Object> batchAudit(@RequestBody List<AtrConfRiskRefVo> atrConfRiskRefVos, HttpServletRequest request, String auditState, String checkedMsg) {
        Long userId = this.loginUserId(request);
        for (AtrConfRiskRefVo atrConfRiskRefVo : atrConfRiskRefVos) {
            atrConfRiskRefVo.setAuditState(auditState);
            atrConfRiskRefVo.setCheckedMsg(checkedMsg);
            atrConfRiskRefVo.setCheckedId(this.loginUserId(request));
            atrConfRiskRefVo.setCheckedTime(new Date());
        }
        try {
            atrConfRiskRefService.auditList(atrConfRiskRefVos, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "审核指标配置")
    @RequestMapping(value = "/riskRef/audit", method = RequestMethod.POST)
    public BaseResponse<Object> audit(@RequestBody AtrConfRiskRefVo atrConfRiskRefVo, HttpServletRequest request, String checkedState, String checkedMsg) {
        Long userId = this.loginUserId(request);
        atrConfRiskRefVo.setAuditState(checkedState);
        atrConfRiskRefVo.setCheckedMsg(checkedMsg);
        atrConfRiskRefVo.setCheckedTime(new Date());
        atrConfRiskRefVo.setCheckedId(this.loginUserId(request));
        try {
            atrConfRiskRefService.audit(atrConfRiskRefVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "校验有无配置")
    @RequestMapping(value = "/riskRef/check_pk", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> checkPK(@RequestBody AtrConfRiskRefVo atrConfRiskRefVo) {
        String result = "0";
        try {
            Long sum = atrConfRiskRefService.checkNaturalPk(atrConfRiskRefVo);
            if(sum > 0){
                result = "1";
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, result);
        }
    }

    @ApiOperation(value = "导出风险分布模式配置")
    @TrackUserBehavioral(description = "exportExcelModel")
    @RequestMapping(value = "/riskRef/export_excel", method = RequestMethod.POST)
    public BaseResponse<Map<String, Object>> exportExcelModel(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrConfRiskRefVo atrConfRiskRefVo) throws Exception {
        Pageable pageParam = new Pageable(0, maxExcelPageSize);
        String language = request.getHeader("ss-Language");
        atrConfRiskRefVo.setLanguage(language);
        Page<AtrConfRiskRefVo> atrConfRiskRefVoPage = atrConfRiskRefService.searchPage(atrConfRiskRefVo, pageParam);
        try {
            Long userId = this.loginUserId(request);
            atrExportService.exportPage(request, response,  atrConfRiskRefVoPage, AtrConfRiskRefVo.class, "df", atrConfRiskRefVo.getTemplateFileName(), atrConfRiskRefVo.getTargetRouter(), userId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, null);
    }
}
