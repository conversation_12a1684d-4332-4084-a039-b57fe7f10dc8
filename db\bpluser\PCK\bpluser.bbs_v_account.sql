CREATE OR REPLACE VIEW BBS_V_ACCOUNT AS
select   account_id,
         upper_account_id,
         account_level,
         entity_id,
         book_code,
         case when book_code = 'BookI17' then substr(account_code,2)
              else account_code end as account_code,
         case when book_code = 'BookI17' then substr(account_c_name,2)
              else account_c_name end as account_c_name,
         case when book_code = 'BookI17' then substr(account_e_name,2)
              else account_e_name end as account_e_name,
         case when book_code = 'BookI17' then substr(account_l_name,2)
              else account_l_name end as account_l_name,
         final_level_is,
         account_category_code,
         account_entry_code,
         valid_is,
         checked_time,
         checked_id,
         audit_state,
         effective_time,
         expire_time,
         checked_msg,
         creator_id,
         create_time,
         updator_id,
         update_time from (
                              select item.account_id,
                                     item.upper_account_id,
                                     item.account_level,
                                     item.entity_id,
                                     item.book_code,
                                     case when item.book_code = 'BookI17' then
                                              replace(SYS_CONNECT_BY_PATH(item.account_code,'#/') ||'#/','#/','/')
                                          else
                                              item.account_code
                                         end as account_code,
                                     case when item.book_code = 'BookI17' then
                                              replace(SYS_CONNECT_BY_PATH(item.account_c_name,'#/') ||'#/','#/','/')
                                          else
                                              item.account_c_name
                                         end as account_c_name,
                                     case when item.book_code = 'BookI17' then
                                              replace(SYS_CONNECT_BY_PATH(item.account_e_name,'#/') ||'#/','#/','/')
                                          else
                                              item.account_e_name
                                         end as account_e_name,
                                     case when item.book_code = 'BookI17' then
                                              replace(SYS_CONNECT_BY_PATH(item.account_l_name,'#/') ||'#/','#/','/')
                                          else
                                              item.account_l_name
                                         end as account_l_name,
                                     --    CASE
                                     --        WHEN (( SELECT count(sub.account_id) AS count
                                     --           FROM bbs_accountitem sub
                                     --          WHERE ((sub.entity_id = item.entity_id) AND ((sub.book_code) = (item.book_code)) AND (sub.upper_account_id = item.account_id) AND ((sub.valid_is) = '1') AND ((sub.audit_state) = '1'))) > 0) THEN '0'
                                     --        ELSE '1'
                                     --    END AS last_stage,
                                     item.final_level_is,
                                     item.account_category_code,
                                     item.account_entry_code,
                                     item.valid_is,
                                     item.checked_time,
                                     item.checked_id,
                                     item.audit_state,
                                     item.effective_time,
                                     item.expire_time,
                                     item.checked_msg,
                                     item.creator_id,
                                     item.create_time,
                                     item.updator_id,
                                     item.update_time
                              from BBS_ACCOUNT item 
                                  START WITH item.upper_account_id = 0
                              CONNECT BY PRIOR item.account_id = item.upper_account_id
                                     and item.entity_id = item.entity_id
                                     and item.book_code = item.book_code)v
order by v.account_id
;


grant SELECT  on bpluser.BBS_V_ACCOUNT to expuser,accuser,dmuser,rptuser,atruser;
 