<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by Taiping MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2024-08-21 18:15:20 -->
<!-- Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.AtrBussIrWeightDao">
  <!-- 本配置文件由Taiping MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**BaseDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->

  <resultMap id="DevResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.ir.vo.AtrBussIrDevVo">
    <result column="UPLOAD_RATE_ID" property="uploadRateId" jdbcType="DECIMAL" />
    <result column="ENTITY_ID" property="entityId" jdbcType="DECIMAL" />
    <result column="YEAR_MONTH" property="yearMonth" jdbcType="VARCHAR" />
    <result column="ICG_NO" property="icgNo" jdbcType="VARCHAR" />
    <result column="CURRENCY_CODE" property="currencyCode" jdbcType="VARCHAR" />
    <result column="ID" property="id" jdbcType="DECIMAL"/>
    <result column="MAIN_ID" property="mainId" jdbcType="DECIMAL"/>
    <result column="DEV_NO" property="devNo" jdbcType="DECIMAL"/>
    <result column="RATE_BASE" property="rateBase" jdbcType="DECIMAL"/>
    <result column="RATE_B" property="rateB" jdbcType="DECIMAL"/>
    <result column="RATE_E" property="rateE" jdbcType="DECIMAL"/>
  </resultMap>
  <!-- 通用列表查询语句-->
  <select id="findBussIrWeightDevList" flushCache="false" useCache="true" resultMap="DevResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.ir.po.AtrBussIrForward">
    select
      abiw.UPLOAD_RATE_ID, abiw.ENTITY_ID, abiw.YEAR_MONTH, abiw.CURRENCY_CODE,abiw.ICG_NO, DEV_NO, RATE_BASE, RATE_B, RATE_E
    from ATR_BUSS_IR_Weight abiw left join
         ATR_BUSS_IR_Weight_dev abifd on  abiw.ID= abifd.MAIN_ID
    where abiw.UPLOAD_RATE_ID = #{uploadRateId,jdbcType=NUMERIC}
        order by abiw.ICG_NO,DEV_NO
  </select>

  <select id="findBussIrWeightIcgList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.ir.po.AtrBussIrForward">
    select
      YEAR_MONTH, ICG_NO, premium
    from ATR_BUSS_IR_Weight abiw
    where abiw.UPLOAD_RATE_ID = #{uploadRateId,jdbcType=NUMERIC}
    order by YEAR_MONTH, ICG_NO
  </select>


  <select id="findBussIrWeightLessUploadId" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.ir.po.AtrBussIrForward">
    select
      YEAR_MONTH, ICG_NO, PREMIUM
    from ATR_BUSS_IR_Weight abiw
    where year_month &lt;= (select distinct year_month from atr_buss_ir_weight where UPLOAD_RATE_ID = #{uploadRateId,jdbcType=NUMERIC} )
    order by YEAR_MONTH, ICG_NO
  </select>
 </mapper>