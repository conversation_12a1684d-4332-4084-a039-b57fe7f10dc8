package com.ss.ifrs.actuarial.service.impl.puhua;

import com.ss.ifrs.actuarial.dao.AtrBussLrcTiDao;
import com.ss.ifrs.actuarial.dao.dap.AtrDapPlanImportMainDao;
import com.ss.ifrs.actuarial.feign.BmsConfCodeFeignClient;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrImportClaim;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrTemplateVo;
import com.ss.ifrs.actuarial.pojo.atrdap.po.AtrDapPlanImportMain;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapPlanImportMainVo;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.ti.AtrDapLrcTiEdPlan;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.ti.AtrDapLrcTiPayPlan;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrDapTreatyVo;
import com.ss.ifrs.actuarial.service.*;
import com.ss.ifrs.actuarial.service.puhua.AtrBussEpiEarnImportService;
import com.ss.ifrs.actuarial.util.Dates;
import com.ss.ifrs.actuarial.util.ExcelUtil;
import com.ss.library.excel.ExcelSheet;
import com.ss.library.excel.ExcelSheetData;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ClassUtil;
import com.ss.platform.core.model.SsException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @ClassName: AtrBussCalcServiceImpl
 * @Description: 计量计算服务接口实现类
 * @Author: yinxh.
 * @CreateDate: 2021/3/8 17:58
 * @Version: 1.0
 */
@Service(value = "atrBussEpiEarnImportService")
public class AtrBussEpiEarnImportServiceImpl implements AtrBussEpiEarnImportService {

	// 日志管理
	final Logger LOG  = LoggerFactory.getLogger(getClass());
	@Autowired
    BmsConfCodeFeignClient bmsConfCodeFeignClient;
	
	@Autowired
	AtrConfCodeService atrConfCodeService;

	@Autowired
	AtrConfModelDefService atrConfModelDefService;

	@Autowired
	AtrExportService atrExportService;

	@Autowired
	AtrBussBecfQuotaService atrBussBecfQuotaService;

    @Autowired
    private AtrBussLrcTiDao atrBussLrcTiDao;

	@Autowired
	private AtrDapTreatyService atrDapTreatyService;

	@Autowired
    private AtrDapPlanImportMainDao atrDapPlanImportMainDao;


    @Override
    public Page<AtrDapPlanImportMainVo> searchPage(AtrDapPlanImportMainVo atrDapPlanImportMainVo, Pageable pageParam) {
        Page<AtrDapPlanImportMainVo> atrDapPlanImportMainVoPage = atrBussLrcTiDao.fuzzySearchPage(atrDapPlanImportMainVo, pageParam);
        return atrDapPlanImportMainVoPage;
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = RuntimeException.class)
    public void delete(Long id) {
        AtrDapPlanImportMain atrDapPlanImportMain = atrDapPlanImportMainDao.findById(id);
        if (ObjectUtils.isEmpty(atrDapPlanImportMain) || "1".equals(atrDapPlanImportMain.getConfirmIs())) {
            return;
        }
        HashMap map = new HashMap();
        map.put("planMainId", atrDapPlanImportMain.getPlanMainId());
        atrDapPlanImportMainDao.deleteByMap(map);
        atrDapPlanImportMainDao.deleteById(atrDapPlanImportMain.getPlanMainId());
        return;
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = RuntimeException.class)
    public Boolean confirm(AtrDapPlanImportMainVo atrDapPlanImportMainVo, Long userId) {
        AtrDapPlanImportMain po = atrDapPlanImportMainDao.findById(atrDapPlanImportMainVo.getPlanMainId());
        if (ObjectUtils.isNotEmpty(po)) {
            Integer count = atrDapPlanImportMainDao.countConfirm(po);
            if (count > 0 ) {
                return false;
            }
            po.setConfirmIs("1");
            po.setConfirmId(userId);
            po.setConfirmTime(new Date());
            po.setUpdatorId(userId);
            po.setUpdateTime(new Date());
            atrDapPlanImportMainDao.updateById(po);
        }
        return true;
    }

    @Override
	public void epiImport(AtrDapPlanImportMainVo atrDapPlanImportMainVo,MultipartFile file) throws Exception {
		if (file == null || file.isEmpty()) {
            throw new Exception("导入文件不能为空");
        }
        
        // 读取Excel文件，按列位置读取第一个工作表
        List<Map<Integer, String>> dataList = ExcelUtil.readExcelSecondSheetByPosition(file);
        
        if (dataList.isEmpty()) {
            throw new Exception("Excel文件内容为空");
        }
        
        // 获取表头信息，确定发展期列的位置
        Map<Integer, String> headers = dataList.get(0);
        
        // 确定数据起始行，通常从第2行开始(索引为1)
        int dataStartRowIndex = 1;
        
        // 创建结果列表
        List<AtrDapLrcTiPayPlan> resultList = new ArrayList<>();

        AtrDapPlanImportMain po = ClassUtil.convert(atrDapPlanImportMainVo, AtrDapPlanImportMain.class);
        if (ObjectUtils.isNotEmpty(dataList)) {
            po.setCreateTime(new Date());
            po.setConfirmIs("0");
            po.setVersionNo(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            atrDapPlanImportMainDao.save(po);
        }

        // 从数据行开始处理
        for (int i = dataStartRowIndex; i < dataList.size(); i++) {
            Map<Integer, String> rowData = dataList.get(i);
            
            // 检查行数据是否为空
            if (rowData == null || rowData.isEmpty()) {
                continue;
            }
            
            // 获取基本信息
			String firYearMonth = ExcelUtil.getValueByPosition(rowData, 0, ""); // 假设合约号在第1列(索引为0)
			String yearMonth = ExcelUtil.getValueByPosition(rowData, 1, ""); // 假设合约号在第1列(索引为0)
			String contractNo = ExcelUtil.getValueByPosition(rowData, 2, ""); // 假设合约号在第2列(索引为1)
            if (StringUtils.isBlank(contractNo)) {
                continue; // 跳过无合约号的行
            }

			String treatyName = ExcelUtil.getValueByPosition(rowData, 3, ""); // 假设险类在第3列(索引为2)
			String riskClassCode = ExcelUtil.getValueByPosition(rowData, 4, ""); // 假设险类在第3列(索引为2)
            String riskCode = ExcelUtil.getValueByPosition(rowData, 5, ""); // 假设险类在第3列(索引为2)
            String effectiveDateStr = ExcelUtil.getValueByPosition(rowData, 6, ""); // 假设合约责任生效日在第4列(索引为3)
			String expiryDateStr = ExcelUtil.getValueByPosition(rowData, 7, "");
			// 处理合约责任生效日
            Date effectiveDate = null;
            String effectiveYearMonth = "";
			Date expiryDate = null;

            try {
                effectiveDate = Dates.toDate(effectiveDateStr);
                effectiveYearMonth = Dates.toChar(effectiveDate, "yyyyMM");
				expiryDate = Dates.toDate(expiryDateStr);
			} catch (Exception e) {
                LOG.error("合约号为[{}]的合约责任生效日格式错误: {}", contractNo, effectiveDateStr);
                throw e;
            }
            
            // 从第6列(索引为5)开始，处理发展期数据
            // 动态处理，不限于固定的列数
            int colIndex = 8; // 起始列索引
            boolean hasMoreData = true;
            
            while (hasMoreData) {
                String premiumValue = ExcelUtil.getValueByPosition(rowData, colIndex, "");
                
                // 如果读到空值，认为已经到达该行数据的末尾
                if (StringUtils.isBlank(premiumValue)) {
                    hasMoreData = false;
                    continue;
                }
                
                try {
                    BigDecimal premium = BigDecimal.valueOf(Double.valueOf(premiumValue));
                    
                    // 发展期从0开始，所以colIndex=5对应发展期0
                    int developmentPeriod = colIndex - 8;
                    
                    // 创建EPI对象
                    AtrDapLrcTiPayPlan payPlan = new AtrDapLrcTiPayPlan();
                    payPlan.setEntityId(atrDapPlanImportMainVo.getEntityId());
                    payPlan.setPlanMainId(po.getPlanMainId());
                    payPlan.setFirstYearMonth(firYearMonth);
					payPlan.setYearMonth(yearMonth);
					payPlan.setTreatyNo(contractNo);
					payPlan.setTreatyName(treatyName);
					payPlan.setRiskClassCode(riskClassCode);
                    payPlan.setRiskCode(riskCode);
                    payPlan.setDevNo(developmentPeriod);
                    payPlan.setPremium(premium);
                    payPlan.setEffectiveDate(effectiveDate);
					payPlan.setExpiryDate(expiryDate);
                    // 添加到结果列表
                    resultList.add(payPlan);
                } catch (NumberFormatException e) {
                    LOG.error("合约号为[{}]的第{}列数据[{}]格式错误", contractNo, colIndex, premiumValue);
					throw e;
                }
                
                // 移动到下一列
                colIndex++;
            }
        }

        // 保存数据
        saveImportedData(resultList);
	}
    
    /**
     * 保存导入的数据
     * @param dataList 导入的数据列表
     */
    private void saveImportedData1(List<AtrDapLrcTiPayPlan> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            LOG.info("没有需要保存的数据");
            return;
        }
        
        try {
			atrBussLrcTiDao.batchSaveDapTiPaymentPlan(dataList);
            LOG.info("成功保存{}条支付计划数据", dataList.size());
        } catch (Exception e) {
            LOG.error("保存支付计划数据时发生错误: {}", e.getMessage(), e);
            throw e;
        }
    }

    public void saveImportedData(List<AtrDapLrcTiPayPlan> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            LOG.info("没有需要保存的数据");
            return;
        }
        try {
            int spitLen = 500;
            int queueSize = dataList.size() <= spitLen ? 1 : (int) Math.ceil(dataList.size() / spitLen) + 1;
            List handleList = null;
            for (int i = 0; i < queueSize; i++) {
                if ((i + 1) == queueSize) {
                    int startIndex = i * spitLen;
                    int endIndex = dataList.size();
                    handleList = dataList.subList(startIndex, endIndex);
                } else {
                    int startIndex = i * spitLen;
                    int endIndex = (i + 1) * spitLen;
                    handleList = dataList.subList(startIndex, endIndex);
                }
                //确保 保存的handleList中有数据
                if (CollectionUtils.isNotEmpty(handleList)) {
                    atrBussLrcTiDao.batchSaveDapTiPaymentPlan(dataList);
                }
            }
            LOG.info("成功保存{}条支付计划数据", dataList.size());
        } catch (Exception e) {
            LOG.error("保存支付计划数据时发生错误: {}", e.getMessage(), e);
            throw e;
        }
    }

 	/**
	 * List<Map>对象复制功能
	 * List<Map> 转化为 List<Map>
	 * @param list
	 */
	public static List<Map<String, Object>> convert(List<Map<String, Object>> list) {
		List<Map<String, Object>> newMapList = new ArrayList<>();
		for (Map map : list) {
			try {
				newMapList.add((Map) SerializationUtils.clone((Serializable) map));
			} catch (Exception ex) {
				throw new SsException(ex.getLocalizedMessage(), ex);
			}
		}
		return newMapList;
	}

	/**
     * 导入已赚保费数据
     * @param atrDapPlanImportMainVo
     * @param file 导入文件
     * @throws Exception 异常
     */
    @Override
    public void earnedImport(AtrDapPlanImportMainVo atrDapPlanImportMainVo, MultipartFile file) throws Exception {
        if (file == null || file.isEmpty()) {
            throw new Exception("导入文件不能为空");
        }
        
        // 读取Excel文件，按列位置读取第一个工作表
        List<Map<Integer, String>> dataList = ExcelUtil.readExcelSecondSheetByPosition(file);
        
        if (dataList.isEmpty()) {
            throw new Exception("Excel文件内容为空");
        }
        
        // 确定数据起始行，通常从第2行开始(索引为1)
        int dataStartRowIndex = 1;
        
        // 创建结果列表
        List<AtrDapLrcTiEdPlan> resultList = new ArrayList<>();

        AtrDapPlanImportMain po = ClassUtil.convert(atrDapPlanImportMainVo, AtrDapPlanImportMain.class);
        if (ObjectUtils.isNotEmpty(dataList)) {
            po.setCreateTime(new Date());
            po.setConfirmIs("0");
            po.setVersionNo(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            atrDapPlanImportMainDao.save(po);
        }
        // 从数据行开始处理
        for (int i = dataStartRowIndex; i < dataList.size(); i++) {
            Map<Integer, String> rowData = dataList.get(i);
            
            // 检查行数据是否为空
            if (rowData == null || rowData.isEmpty()) {
                continue;
            }


			String firYearMonth = ExcelUtil.getValueByPosition(rowData, 0, ""); // 假设合约号在第1列(索引为0)
			String yearMonth = ExcelUtil.getValueByPosition(rowData, 1, ""); // 假设合约号在第1列(索引为0)
			String contractNo = ExcelUtil.getValueByPosition(rowData, 2, ""); // 假设合约号在第2列(索引为1)
			if (StringUtils.isBlank(contractNo)) {
				continue; // 跳过无合约号的行
			}
            String treatyName = ExcelUtil.getValueByPosition(rowData, 3, ""); // 假设险类在第3列(索引为2)
			String riskClassCode = ExcelUtil.getValueByPosition(rowData, 4, ""); // 假设险类在第3列(索引为2)
            String riskCode = ExcelUtil.getValueByPosition(rowData, 5, ""); // 假设险类在第3列(索引为2)
            String effectiveDateStr = ExcelUtil.getValueByPosition(rowData, 6, ""); // 假设合约责任生效日在第4列(索引为3)
			String expiryDateStr = ExcelUtil.getValueByPosition(rowData, 7, "");

            // 处理合约责任生效日
            Date effectiveDate = null;
            Date expiryDate = null;
            
            try {
                effectiveDate = Dates.toDate(effectiveDateStr);
                expiryDate = Dates.toDate(expiryDateStr);
            } catch (Exception e) {
                LOG.error("合约号为[{}]的合约责任生效日格式错误: {}", contractNo, effectiveDateStr);
                throw e;
            }
            
            // 从第6列(索引为5)开始，处理发展期数据
            // 动态处理，不限于固定的列数
            int colIndex = 8; // 起始列索引
            boolean hasMoreData = true;
            
            while (hasMoreData) {
                String earnedValue = ExcelUtil.getValueByPosition(rowData, colIndex, "");
                
                // 如果读到空值，认为已经到达该行数据的末尾
                if (StringUtils.isBlank(earnedValue)) {
                    hasMoreData = false;
                    continue;
                }
                
                try {
                    BigDecimal premium = BigDecimal.valueOf(Double.parseDouble(earnedValue));
                    
                    // 发展期从0开始，所以colIndex=5对应发展期0
                    int developmentPeriod = colIndex - 8;

                    
                    // 创建已赚保费计划对象
                    AtrDapLrcTiEdPlan edPlan = new AtrDapLrcTiEdPlan();
                    edPlan.setEntityId(atrDapPlanImportMainVo.getEntityId());
                    edPlan.setPlanMainId(po.getPlanMainId());
					edPlan.setFirstYearMonth(firYearMonth);
					edPlan.setYearMonth(yearMonth);
                    edPlan.setTreatyNo(contractNo);
					edPlan.setTreatyName(treatyName);
					edPlan.setRiskClassCode(riskClassCode);
                    edPlan.setRiskCode(riskCode);
                    edPlan.setYearMonth(yearMonth);
                    edPlan.setDevNo(developmentPeriod);
                    edPlan.setPremium(premium);
                    edPlan.setEffectiveDate(effectiveDate);
                    edPlan.setExpiryDate(expiryDate);
                    
                    // 添加到结果列表
                    resultList.add(edPlan);
                } catch (NumberFormatException e) {
                    LOG.error("合约号为[{}]的第{}列数据[{}]格式错误", contractNo, colIndex, earnedValue);
                    throw e;
                }
                
                // 移动到下一列
                colIndex++;
            }
        }
        
        // 保存数据
        saveEarnedImportedData(resultList);
    }



    public void saveEarnedImportedData(List<AtrDapLrcTiEdPlan> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        int spitLen = 500;
        int queueSize = dataList.size() <= spitLen ? 1 : (int) Math.ceil(dataList.size() / spitLen) + 1;
        List handleList = null;
        for (int i = 0; i < queueSize; i++) {
            if ((i + 1) == queueSize) {
                int startIndex = i * spitLen;
                int endIndex = dataList.size();
                handleList = dataList.subList(startIndex, endIndex);
            } else {
                int startIndex = i * spitLen;
                int endIndex = (i + 1) * spitLen;
                handleList = dataList.subList(startIndex, endIndex);
            }
            //确保 保存的handleList中有数据
            if (CollectionUtils.isNotEmpty(handleList)) {
                atrBussLrcTiDao.batchSaveDapTiEdPlan(dataList);
            }
        }
    }


    /**
     * 保存导入的已赚保费数据
     * @param dataList 导入的数据列表
     */
    private void saveEarnedImportedData1(List<AtrDapLrcTiEdPlan> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            LOG.info("没有需要保存的已赚保费数据");
            return;
        }
        
        try {
            // 批量保存到atr_dap_ti_ed_plan表
            atrBussLrcTiDao.batchSaveDapTiEdPlan(dataList);
            LOG.info("成功保存{}条已赚保费数据", dataList.size());
        } catch (Exception e) {
            LOG.error("保存已赚保费数据时发生错误: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取导入数据列表
     * 根据数据类型查询不同的表
     * @param atrDapPlanImportMainVo
     * @param pageable 分页参数
     * @return 分页结果
     */
    @Override
    public Page<Map<String, Object>> getImportDataList(AtrDapPlanImportMainVo atrDapPlanImportMainVo, Pageable pageable) {
        if (atrDapPlanImportMainVo.getPlanMainId() == null) {
            throw new IllegalArgumentException("业务单位ID不能为空");
        }
        
        if (atrDapPlanImportMainVo.getDataType() == null) {
            throw new IllegalArgumentException("数据类型不能为空");
        }
        try {
            if ("EPI".equals(atrDapPlanImportMainVo.getDataType())) {
                // 查询EPI导入数据列表
                return atrBussLrcTiDao.findEpiImportList(atrDapPlanImportMainVo, pageable);
            } else if ("EARN".equals(atrDapPlanImportMainVo.getDataType())) {
                // 查询已赚保费导入数据列表
                return atrBussLrcTiDao.findEarnedImportList(atrDapPlanImportMainVo, pageable);
            } else {
                throw new IllegalArgumentException("不支持的数据类型: " + atrDapPlanImportMainVo.getDataType());
            }
        } catch (Exception e) {
            LOG.error("获取导入数据列表失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取EPI导入数据详情
     * @param atrDapPlanImportMainVo
     * @return 详情数据列表
     */
    @Override
    public List<Map<String, Object>> getEpiImportDetail(AtrDapPlanImportMainVo atrDapPlanImportMainVo) {
        if ( atrDapPlanImportMainVo.getTreatyNo() == null || atrDapPlanImportMainVo.getRiskClassCode() == null) {
            throw new IllegalArgumentException("合约号和险类代码不能为空");
        }
        
        try {
            return atrBussLrcTiDao.findEpiImportDetail(atrDapPlanImportMainVo);
        } catch (Exception e) {
            LOG.error("获取EPI导入数据详情失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取已赚保费导入数据详情
     * @param entityId 业务单位ID
     * @param treatyNo 合约号
     * @param riskClassCode 险类代码
     * @return 详情数据列表
     */
    @Override
    public List<Map<String, Object>> getEarnedImportDetail(AtrDapPlanImportMainVo atrDapPlanImportMainVo) {
        if ( atrDapPlanImportMainVo.getTreatyNo() == null || atrDapPlanImportMainVo.getRiskClassCode() == null) {
            throw new IllegalArgumentException("合约号和险类代码不能为空");
        }
        
        try {
            return atrBussLrcTiDao.findEarnedImportDetail(atrDapPlanImportMainVo);
        } catch (Exception e) {
            LOG.error("获取已赚保费导入数据详情失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 删除导入数据
     * @param entityId 业务单位ID
     * @param treatyNo 合约号
     * @param riskClassCode 险类代码
     * @param dataType 数据类型 (EPI/EARNED)
     * @return 是否删除成功
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public boolean deleteImportData(Long entityId, String yearMonth, String treatyNo, String riskClassCode, String dataType) {
        if (entityId == null || treatyNo == null || riskClassCode == null || dataType == null) {
            throw new IllegalArgumentException("业务单位ID、合约号、险类代码和数据类型不能为空");
        }
        
        try {
            int rowsAffected = 0;
            if ("EPI".equals(dataType)) {
                // 删除EPI导入数据
                rowsAffected = atrBussLrcTiDao.deleteEpiImportData(entityId, yearMonth, treatyNo, riskClassCode);
            } else if ("EARNED".equals(dataType)) {
                // 删除已赚保费导入数据
                rowsAffected = atrBussLrcTiDao.deleteEarnedImportData(entityId, yearMonth, treatyNo, riskClassCode);
            } else {
                throw new IllegalArgumentException("不支持的数据类型: " + dataType);
            }
            
            LOG.info("成功删除{}条{}数据", rowsAffected, "EPI".equals(dataType) ? "缴费计划" : "已赚保费");
            return rowsAffected > 0;
        } catch (Exception e) {
            LOG.error("删除导入数据失败: {}", e.getMessage(), e);
            throw e;
        }
    }

	@Override
	public void downloadEpiTemplate(AtrTemplateVo atrTemplateVo, HttpServletRequest request, HttpServletResponse response) throws Exception {
		List<AtrDapTreatyVo> atrDapTreatyVos =  atrDapTreatyService.findDapTreatyList(atrTemplateVo);
		List<ExcelSheet> sheetList  = new ArrayList<>();
		List<ExcelSheetData> sheetDataList = new ArrayList<>();
		ExcelSheetData excelSheetData = new ExcelSheetData("df", atrDapTreatyVos);
		sheetDataList.add(excelSheetData);
		ExcelSheet sheet  = new ExcelSheet(1, sheetDataList, false);
		sheetList.add(sheet);
		atrExportService.exportExcelSheetList(request, response, sheetList, atrTemplateVo.getTemplateFileName(), "",  atrTemplateVo.getTargetRouter(), 1L);
	}

}
