/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2023-02-20 10:16:00
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao.conf;
 
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDymDtlDevHis;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDymHis;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-20 10:16:00<br/>
 * Description: 假设值事故年月的发展期明细表轨迹 （基于事故年月） Dao类<br/>
 * Related Table Name: ATR_CONF_QUOTA_DYM_DTL_DEVHIS<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrConfQuotaDymDtlDevHisDao extends IDao<AtrConfQuotaDymDtlDevHis, Long> {
    void saveHis(AtrConfQuotaDymHis confQuotaDymHis);
}