package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.ti;

import com.ss.ifrs.actuarial.util.abp.IgnoreCol;
import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Tab("atr_buss_ti_lrc_g_dev")
public class AtrBussLrcTiIcgDev {

    /** 主表ID */
    private Long mainId;

    /** 发展期序号 */
    private Integer devNo;

    /** 业务年月 */
    private String yearMonth;

    /** 退保费 */
    private BigDecimal lapse;

    /** 预期维持费用 */
    private BigDecimal mtFee;

    /** 预期赔付 */
    private BigDecimal claim;

    /** 预期间接理赔费用 */
    private BigDecimal ulae;

    /** 预期非金融风险调整 */
    private BigDecimal ra;

    /** 已赚比例 */
    private BigDecimal edRate;

    /** 已赚保费 */
    private BigDecimal edPremium;
    
    /** 已赚净额结算 */
    private BigDecimal edNetFee;
    
    /** 已赚跟单获取费用 */
    private BigDecimal edIacf;
    
    /** 已赚非跟单获取费用-对内 */
    private BigDecimal edIaehcIn;
    
    /** 已赚非跟单获取费用-对外 */
    private BigDecimal edIaehcOut;

    /** 应收保费 */
    private BigDecimal recvPremium;

    /** 净额结算手续费 */
    private BigDecimal netFee;

    /** 跟单获取费用 */
    private BigDecimal iacf;

    /** 非跟单获取费用对内 */
    private BigDecimal iaehcIn;

    /**非跟单获取费用对外 */
    private BigDecimal iaehcOut;

    /** 减值 */
    private BigDecimal badDebt;


}
