<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-02-06 17:15:22 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussIbnrImportMainDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrImportMain">
    <id column="IBNR_MAIN_ID" property="ibnrMainId" jdbcType="DECIMAL" />
    <result column="entity_id" property="entityId" jdbcType="DECIMAL" />
    <result column="YEAR_MONTH" property="yearMonth" jdbcType="VARCHAR" />
    <result column="ibnr_range" property="ibnrRange" jdbcType="VARCHAR" />
    <result column="provision_ratio" property="provisionRatio" jdbcType="TIMESTAMP" />
    <result column="USE_IMPORT_OS_IS" property="useImportOsIs" jdbcType="VARCHAR" />
    <result column="VERSION_NO" property="versionNo" jdbcType="VARCHAR" />
    <result column="CONFIRM_IS" property="confirmIs" jdbcType="CHAR" />
    <result column="CONFIRM_ID" property="confirmId" jdbcType="DECIMAL" />
    <result column="CONFIRM_TIME" property="confirmTime" jdbcType="TIMESTAMP" />
    <result column="CREATOR_ID" property="creatorId" jdbcType="DECIMAL" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATOR_ID" property="updatorId" jdbcType="DECIMAL" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    IBNR_MAIN_ID, entity_id, YEAR_MONTH, ibnr_range, provision_ratio, USE_IMPORT_OS_IS,
    VERSION_NO, CONFIRM_IS, CONFIRM_ID, CONFIRM_TIME, CREATOR_ID, CREATE_TIME,
    UPDATOR_ID, UPDATE_TIME
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="ibnrMainId != null ">
          and IBNR_MAIN_ID = #{ibnrMainId,jdbcType=DECIMAL}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
          and YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="ibnrRange != null and ibnrRange != ''">
          and ibnr_range = #{ibnrRange,jdbcType=VARCHAR}
      </if>
      <if test="provisionRatio != null ">
          and provision_ratio = #{provisionRatio,jdbcType=TIMESTAMP}
      </if>
      <if test="useImportOsIs != null and useImportOsIs != ''">
          and USE_IMPORT_OS_IS = #{useImportOsIs,jdbcType=VARCHAR}
      </if>
      <if test="versionNo != null and versionNo != ''">
          and VERSION_NO = #{versionNo,jdbcType=VARCHAR}
      </if>
      <if test="confirmIs != null ">
          and CONFIRM_IS = #{confirmIs,jdbcType=CHAR}
      </if>
      <if test="confirmId != null ">
          and CONFIRM_ID = #{confirmId,jdbcType=DECIMAL}
      </if>
      <if test="confirmTime != null ">
          and CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creatorId != null ">
          and CREATOR_ID = #{creatorId,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null ">
          and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and UPDATOR_ID = #{updatorId,jdbcType=DECIMAL}
      </if>
      <if test="updateTime != null ">
          and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.ibnrMainId != null ">
          and IBNR_MAIN_ID = #{condition.ibnrMainId,jdbcType=DECIMAL}
      </if>
      <if test="condition.entityId != null ">
          and entity_id = #{condition.entityId,jdbcType=DECIMAL}
      </if>
      <if test="condition.yearMonth != null and condition.yearMonth != ''">
          and YEAR_MONTH = #{condition.yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="condition.ibnrRange != null and condition.ibnrRange != ''">
          and ibnr_range = #{condition.ibnrRange,jdbcType=VARCHAR}
      </if>
      <if test="condition.provisionRatio != null ">
          and provision_ratio = #{condition.provisionRatio,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.useImportOsIs != null and condition.useImportOsIs != ''">
          and USE_IMPORT_OS_IS = #{condition.useImportOsIs,jdbcType=VARCHAR}
      </if>
      <if test="condition.versionNo != null and condition.versionNo != ''">
          and VERSION_NO = #{condition.versionNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.confirmIs != null ">
          and CONFIRM_IS = #{condition.confirmIs,jdbcType=CHAR}
      </if>
      <if test="condition.confirmId != null ">
          and CONFIRM_ID = #{condition.confirmId,jdbcType=DECIMAL}
      </if>
      <if test="condition.confirmTime != null ">
          and CONFIRM_TIME = #{condition.confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.creatorId != null ">
          and CREATOR_ID = #{condition.creatorId,jdbcType=DECIMAL}
      </if>
      <if test="condition.createTime != null ">
          and CREATE_TIME = #{condition.createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.updatorId != null ">
          and UPDATOR_ID = #{condition.updatorId,jdbcType=DECIMAL}
      </if>
      <if test="condition.updateTime != null ">
          and UPDATE_TIME = #{condition.updateTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="ibnrMainId != null ">
          and IBNR_MAIN_ID = #{ibnrMainId,jdbcType=DECIMAL}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
          and YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="ibnrRange != null and ibnrRange != ''">
          and ibnr_range = #{ibnrRange,jdbcType=VARCHAR}
      </if>
      <if test="provisionRatio != null ">
          and provision_ratio = #{provisionRatio,jdbcType=TIMESTAMP}
      </if>
      <if test="useImportOsIs != null and useImportOsIs != ''">
          and USE_IMPORT_OS_IS = #{useImportOsIs,jdbcType=VARCHAR}
      </if>
      <if test="versionNo != null and versionNo != ''">
          and VERSION_NO = #{versionNo,jdbcType=VARCHAR}
      </if>
      <if test="confirmIs != null ">
          and CONFIRM_IS = #{confirmIs,jdbcType=CHAR}
      </if>
      <if test="confirmId != null ">
          and CONFIRM_ID = #{confirmId,jdbcType=DECIMAL}
      </if>
      <if test="confirmTime != null ">
          and CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creatorId != null ">
          and CREATOR_ID = #{creatorId,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null ">
          and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and UPDATOR_ID = #{updatorId,jdbcType=DECIMAL}
      </if>
      <if test="updateTime != null ">
          and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_IBNR_IMPORT_MAIN
    where IBNR_MAIN_ID = #{ibnrMainId,jdbcType=DECIMAL}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_IBNR_IMPORT_MAIN
    where IBNR_MAIN_ID in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_IBNR_IMPORT_MAIN
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrImportMain">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_IBNR_IMPORT_MAIN
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_IBNR_IMPORT_MAIN
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="IBNR_MAIN_ID" keyProperty="ibnrMainId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrImportMain">
    <selectKey resultType="long" keyProperty="ibnrMainId" order="BEFORE">
      select nextval('atr_seq_buss_ibnr_import_main') as  sequenceNo 
    </selectKey>
    insert into ATR_BUSS_IBNR_IMPORT_MAIN
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ibnrMainId != null">
        IBNR_MAIN_ID,
      </if>
      <if test="entityId != null">
        entity_id,
      </if>
      <if test="yearMonth != null">
        YEAR_MONTH,
      </if>
      <if test="ibnrRange != null">
        ibnr_range,
      </if>
      <if test="provisionRatio != null">
        provision_ratio,
      </if>
      <if test="useImportOsIs != null">
        USE_IMPORT_OS_IS,
      </if>
      <if test="versionNo != null">
        VERSION_NO,
      </if>
      <if test="confirmIs != null">
        CONFIRM_IS,
      </if>
      <if test="confirmId != null">
        CONFIRM_ID,
      </if>
      <if test="confirmTime != null">
        CONFIRM_TIME,
      </if>
      <if test="creatorId != null">
        CREATOR_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updatorId != null">
        UPDATOR_ID,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ibnrMainId != null">
        #{ibnrMainId,jdbcType=DECIMAL},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=DECIMAL},
      </if>
      <if test="yearMonth != null">
        #{yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="ibnrRange != null">
        #{ibnrRange,jdbcType=VARCHAR},
      </if>
      <if test="provisionRatio != null">
        #{provisionRatio,jdbcType=TIMESTAMP},
      </if>
      <if test="useImportOsIs != null">
        #{useImportOsIs,jdbcType=VARCHAR},
      </if>
      <if test="versionNo != null">
        #{versionNo,jdbcType=VARCHAR},
      </if>
      <if test="confirmIs != null">
        #{confirmIs,jdbcType=CHAR},
      </if>
      <if test="confirmId != null">
        #{confirmId,jdbcType=DECIMAL},
      </if>
      <if test="confirmTime != null">
        #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        #{updatorId,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all 
    <foreach collection="list" item="item" index="index">
      into ATR_BUSS_IBNR_IMPORT_MAIN values 
       (#{item.ibnrMainId,jdbcType=DECIMAL}, 
        #{item.entityId,jdbcType=DECIMAL}, #{item.yearMonth,jdbcType=VARCHAR}, #{item.ibnrRange,jdbcType=VARCHAR},
        #{item.provisionRatio,jdbcType=TIMESTAMP}, #{item.useImportOsIs,jdbcType=VARCHAR}, 
        #{item.versionNo,jdbcType=VARCHAR}, #{item.confirmIs,jdbcType=CHAR}, #{item.confirmId,jdbcType=DECIMAL}, 
        #{item.confirmTime,jdbcType=TIMESTAMP}, #{item.creatorId,jdbcType=DECIMAL},
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updatorId,jdbcType=DECIMAL}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
    select 1 
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrImportMain">
    update ATR_BUSS_IBNR_IMPORT_MAIN
    <set>
      <if test="entityId != null">
        entity_id = #{entityId,jdbcType=DECIMAL},
      </if>
      <if test="yearMonth != null">
        YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="ibnrRange != null">
        ibnr_range = #{ibnrRange,jdbcType=VARCHAR},
      </if>
      <if test="provisionRatio != null">
        provision_ratio = #{provisionRatio,jdbcType=TIMESTAMP},
      </if>
      <if test="useImportOsIs != null">
        USE_IMPORT_OS_IS = #{useImportOsIs,jdbcType=VARCHAR},
      </if>
      <if test="versionNo != null">
        VERSION_NO = #{versionNo,jdbcType=VARCHAR},
      </if>
      <if test="confirmIs != null">
        CONFIRM_IS = #{confirmIs,jdbcType=CHAR},
      </if>
      <if test="confirmId != null">
        CONFIRM_ID = #{confirmId,jdbcType=DECIMAL},
      </if>
      <if test="confirmTime != null">
        CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        CREATOR_ID = #{creatorId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        UPDATOR_ID = #{updatorId,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where IBNR_MAIN_ID = #{ibnrMainId,jdbcType=DECIMAL}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrImportMain">
    update ATR_BUSS_IBNR_IMPORT_MAIN
    <set>
      <if test="record.entityId != null">
        entity_id = #{record.entityId,jdbcType=DECIMAL},
      </if>
      <if test="record.yearMonth != null">
        YEAR_MONTH = #{record.yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="record.ibnrRange != null">
        ibnr_range = #{record.ibnrRange,jdbcType=VARCHAR},
      </if>
      <if test="record.provisionRatio != null">
        provision_ratio = #{record.provisionRatio,jdbcType=TIMESTAMP},
      </if>
      <if test="record.useImportOsIs != null">
        USE_IMPORT_OS_IS = #{record.useImportOsIs,jdbcType=VARCHAR},
      </if>
      <if test="record.versionNo != null">
        VERSION_NO = #{record.versionNo,jdbcType=VARCHAR},
      </if>
      <if test="record.confirmIs != null">
        CONFIRM_IS = #{record.confirmIs,jdbcType=CHAR},
      </if>
      <if test="record.confirmId != null">
        CONFIRM_ID = #{record.confirmId,jdbcType=DECIMAL},
      </if>
      <if test="record.confirmTime != null">
        CONFIRM_TIME = #{record.confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creatorId != null">
        CREATOR_ID = #{record.creatorId,jdbcType=DECIMAL},
      </if>
      <if test="record.createTime != null">
        CREATE_TIME = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatorId != null">
        UPDATOR_ID = #{record.updatorId,jdbcType=DECIMAL},
      </if>
      <if test="record.updateTime != null">
        UPDATE_TIME = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from ATR_BUSS_IBNR_IMPORT_MAIN
    where IBNR_MAIN_ID = #{ibnrMainId,jdbcType=DECIMAL}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from ATR_BUSS_IBNR_IMPORT_MAIN
    where IBNR_MAIN_ID in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from ATR_BUSS_IBNR_IMPORT_MAIN
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultType="Long" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrImportMain">
    select count(1) from ATR_BUSS_IBNR_IMPORT_MAIN
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>