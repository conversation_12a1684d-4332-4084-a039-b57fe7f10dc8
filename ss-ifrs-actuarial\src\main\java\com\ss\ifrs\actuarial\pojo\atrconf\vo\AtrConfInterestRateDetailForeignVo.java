package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "无风险利率曲线配置明细-国外版")
@ExcelIgnoreUnannotated
public class AtrConfInterestRateDetailForeignVo {
    /**
     * Database column: atr_conf_interest_rate_detail.interest_rate_detail_id
     * Database remarks: interest_rate_detail_id|主键
     */
    @ApiModelProperty(value = "interest_rate_detail_id|主键", required = true)
    private Long interestRateDetailId;

    /**
     * Database column: atr_conf_interest_rate_detail.interest_rate_id
     * Database remarks: interest_rate_id|主键
     */
    @ApiModelProperty(value = "interest_rate_id|主键", required = true)
    private Long interestRateId;

    /**
     * Database column: ATR_CONF_INTEREST_RATE.NORM_MONTH
     * Database remarks: norm_month|标准月份
     */
    @ApiModelProperty(value = "norm_month|标准月份", required = false)
    @NotNull(message = "The norm month can't be null|标准月份不能为空|標準月份不能為空")
    //@DecimalMax(value = "2048", message = "norm month can's must be less than 2048|标准月份必须小于2048|標準月份必須小於2048")
    //@ExcelProperty(value = "标准期限(月)")
    @ExcelProperty(index = 1)
    private Short normMonth;

    /**
     * Database column: ATR_CONF_INTEREST_RATE.NORM_TIME
     * Database remarks: norm_time|标准期限(年)
     */
    @ApiModelProperty(value = "norm_time|标准期限(年)", required = false)
    @NotNull(message = "The norm time can't be null|标准期限(年)不能为空|標準期限(年)不能為空")
    //@DecimalMax(value = "2048", message = "norm time can's must be less than 2048|标准期限(年)必须小于2048|標準期限(年)必須小於2048")
    //@ExcelProperty(value = "标准期限(年)")
    @ExcelProperty(index = 2)
    private BigDecimal normTime;

    /**
     * Database column: ATR_CONF_INTEREST_RATE.MON_IMM_PROFIT
     * Database remarks: mon_imm_profit|月度年化即期收益率(%)
     */
    @ApiModelProperty(value = "mon_imm_profit|月度年化即期收益率(%)", required = false)
    @NotNull(message = "The mon imm profit can't be null|高值收益率(%)不能为空|高值收益(%)不能為空")
    @ExcelProperty(index = 3)
    private BigDecimal monImmProfit;

    /**
     * Database column: ATR_CONF_INTEREST_RATE.MON_IMM_PROFIT_FLOW
     * Database remarks: mon_imm_profit_flow|月度年化即期收益率(%)_含流动性溢价
     */
    @ApiModelProperty(value = "mon_imm_profit_flow|月度年化即期收益率(%)_含流动性溢价", required = false)
    @NotNull(message = "The high profit rate can't be null|月度年化即期收益率(%)_含流动性溢价不能为空|月度年化即期收益率(%)_含流動性溢價不能為空")
    //@DecimalMax(value = "2048", message = "high profit rate can's must be less than 2048|月度年化即期收益率(%)_含流动性溢价必须小于2048|月度年化即期收益率(%)_含流動性溢價必須小於2048")
    //@ExcelProperty(value = {"月度年化即期收益率(%)_含流动性溢价"})
    @ExcelProperty(index = 4)
    private BigDecimal monImmProfitFlow;

    /**
     * Database column: ATR_CONF_INTEREST_RATE.MON_FAR_PROFIT_FLOW
     * Database remarks: mon_far_profit_flow|月度月化远期收益率(%)_含流动性溢价
     */
    @ApiModelProperty(value = "mon_far_profit_flow|月度月化远期收益率(%)_含流动性溢价", required = false)
    @NotNull(message = "The mon far profit flow can't be null|月度月化远期收益率(%)_含流动性溢价不能为空|月度年化遠期收益率(%)_含流動性溢價不能為空")
    //@DecimalMax(value = "2048", message = "mon far profit flow can's must be less than 2048|月度月化远期收益率(%)_含流动性溢价必须小于2048|月度年化遠期收益率(%)_含流動性溢價必須小於2048")
    //@ExcelProperty(value = {"月度月化远期收益率(%)_含流动性溢价"})
    @ExcelProperty(index = 5)
    private BigDecimal monFarProfitFlow;

    /**
     * Database column: atr_conf_interest_rate_detail.creator_id
     * Database remarks: Creator_Id|创建人
     */
    @ApiModelProperty(value = "Creator_Id|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: atr_conf_interest_rate_detail.create_time
     * Database remarks: Create_Time|创建时间
     */
    @ApiModelProperty(value = "Create_Time|创建时间", required = false)
    private Date createTime;



    private static final long serialVersionUID = 1L;

    public Long getInterestRateDetailId() {
        return interestRateDetailId;
    }

    public void setInterestRateDetailId(Long interestRateDetailId) {
        this.interestRateDetailId = interestRateDetailId;
    }

    public Long getInterestRateId() {
        return interestRateId;
    }

    public void setInterestRateId(Long interestRateId) {
        this.interestRateId = interestRateId;
    }

    public Short getNormMonth() {
        return normMonth;
    }

    public void setNormMonth(Short normMonth) {
        this.normMonth = normMonth;
    }

    public BigDecimal getNormTime() {
        return normTime;
    }

    public void setNormTime(BigDecimal normTime) {
        this.normTime = normTime;
    }

    public BigDecimal getMonImmProfit() {
        return monImmProfit;
    }

    public void setMonImmProfit(BigDecimal monImmProfit) {
        this.monImmProfit = monImmProfit;
    }

    public BigDecimal getMonImmProfitFlow() {
        return monImmProfitFlow;
    }

    public void setMonImmProfitFlow(BigDecimal monImmProfitFlow) {
        this.monImmProfitFlow = monImmProfitFlow;
    }

    public BigDecimal getMonFarProfitFlow() {
        return monFarProfitFlow;
    }

    public void setMonFarProfitFlow(BigDecimal monFarProfitFlow) {
        this.monFarProfitFlow = monFarProfitFlow;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
