create or replace PACKAGE rpt_pack_report_item IS

    FUNCTION func_rule_sql_relyon_anal(p_entity_id  NUMBER,
                                       p_book_code  VARCHAR2,
                                       p_year_month VARCHAR2,
                                       p_rely_on    VARCHAR2,
                                       p_rule_expr  VARCHAR2) RETURN VARCHAR2;

    FUNCTION func_verify_rule_sql(p_rely_on VARCHAR2, p_rule_expr VARCHAR2)
        RETURN NUMBER;

    PROCEDURE proc_arithmetic(p_entity_id  NUMBER,
                              p_book_code  VARCHAR2,
                              p_year_month VARCHAR2,
                              p_deal_level NUMBER,
                              p_oper_id    NUMBER,
                              p_task_code  VARCHAR2);

    PROCEDURE proc_buss_sql(p_entity_id  NUMBER,
                            p_book_code  VARCHAR2,
                            p_year_month VARCHAR2,
                            p_oper_id    NUMBER,
                            p_task_code  VARCHAR2);

    PROCEDURE proc_draw_data(p_entity_id  NUMBER,
                             p_book_code  VARCHAR2,
                             p_year_month VARCHAR2,
                             p_oper_id    NUMBER,
                             p_task_code  VARCHAR2);

    PROCEDURE proc_draw_financials_data(p_entity_id  NUMBER,
                                        p_book_code  VARCHAR2,
                                        p_year_month VARCHAR2,
                                        p_expr_type  VARCHAR2,
                                        p_oper_id    NUMBER,
                                        p_task_code  VARCHAR2);

    PROCEDURE proc_draw_fin_process_data(p_entity_id      NUMBER,
                                         p_book_code      VARCHAR2,
                                         p_year_month     VARCHAR2,
                                         p_report_item_id NUMBER,
                                         p_oper_id        NUMBER,
                                         p_task_code      VARCHAR2);

    PROCEDURE proc_draw_fin_result_data(p_entity_id  NUMBER,
                                        p_book_code  VARCHAR2,
                                        p_year_month VARCHAR2,
                                        p_expr_type  VARCHAR2,
                                        p_oper_id    NUMBER,
                                        p_task_code  VARCHAR2);

    PROCEDURE proc_summary(p_entity_id  NUMBER,
                           p_book_code  VARCHAR2,
                           p_year_month VARCHAR2,
                           p_deal_level NUMBER,
                           p_oper_id    NUMBER,
                           p_task_code  VARCHAR2);

    PROCEDURE proc_ternary(p_entity_id  NUMBER,
                           p_book_code  VARCHAR2,
                           p_year_month VARCHAR2,
                           p_deal_level NUMBER,
                           p_oper_id    NUMBER,
                           p_task_code  VARCHAR2);

    PROCEDURE proc_update_deal_level(p_entity_id      NUMBER,
                                     p_book_code      VARCHAR2,
                                     p_report_item_id NUMBER,
                                     p_deal_level     NUMBER,
                                     p_oper_id        NUMBER);

    PROCEDURE proc_calculate_dimiension_data(p_entity_id           NUMBER,
                                             p_book_code           VARCHAR2,
                                             p_year_month          VARCHAR2,
                                             p_report_item_id      NUMBER,
                                             p_report_item_rule_id NUMBER,
                                             p_oper_id             NUMBER,
                                             p_serial_no           NUMBER,
                                             p_rule_serial_no      NUMBER);

    PROCEDURE proc_draw_qtc_evaluate(p_entity_id        NUMBER,
                                     p_user_id          NUMBER,
                                     p_start_year_month varchar2,
                                     p_end_year_month   varchar2,
                                     p_qtc_version_no   varchar2,
                                     p_icg_no           varchar2,
                                     p_model_id         NUMBER,
                                     p_loa_code         varchar2);

    PROCEDURE proc_draw_carryover_data(p_entity_id  NUMBER,
                                       p_book_code  VARCHAR2,
                                       p_year_month VARCHAR2,
                                       p_oper_id    NUMBER,
                                       p_task_code  VARCHAR2);

END rpt_pack_report_item;
/

create or replace PACKAGE BODY rpt_pack_report_item IS

    FUNCTION func_rule_sql_relyon_anal(p_entity_id  NUMBER,
                                       p_book_code  VARCHAR2,
                                       p_year_month VARCHAR2,
                                       p_rely_on    VARCHAR2,
                                       p_rule_expr  VARCHAR2) RETURN VARCHAR2 AS
        /***********************************************************************
    NAME : rpt_pack_report_item_func_sql_relyon_analytical
    DESCRIPTION : 列报项sql脚本附加条件解析
    DATE :2022-01-19
    AUTHOR :YINXH

    -------
    MODIFY LOG

      UPDATE DATE :
      UPDATE BY   :
      UPDATE DESC :
    ***********************************************************************/
        v_log_msg         VARCHAR2(4000); --日志信息
        v_error_msg       VARCHAR2(2000); --异常信息

        v_execute_sql     VARCHAR2(4000); --脚本解析结果
        v_entity_id_flag  VARCHAR2(1); --附件条件勾选标记1：业务单位
        v_book_code_flag  VARCHAR2(1); --附件条件勾选标记2：账套
        v_year_month_flag VARCHAR2(1); --附件条件勾选标记3：会计期间
    BEGIN
        BEGIN

            -- 判断脚本是否包含WHERE条件语句，若无则添加 WHERE 1 = 1
            IF instr(upper(p_rule_expr), upper('where')) > 0 THEN
                v_execute_sql := p_rule_expr || ' ';
            ELSE
                v_execute_sql := p_rule_expr || ' where 1 = 1 ';
            END IF;

            -- RELY_ON:脚本附件条件，示例：'101'，分别代表已勾选核算单位，未勾选账套，已勾选会计期间
            IF p_rely_on IS NOT NULL AND length(coalesce(p_rely_on, '0')) = 3 THEN

                -- 解析附加条件
                v_entity_id_flag := substr(p_rely_on, 1, 1);
                v_book_code_flag := substr(p_rely_on, 2, 1);
                v_year_month_flag := substr(p_rely_on, 3, 1);

                -- 拼接附加条件1，如果包含{entity_id}，就做替换，否则就做追加
                IF v_entity_id_flag = '1' THEN
                    IF instr(upper(p_rule_expr), upper('{entity_id}')) > 0 THEN
                        v_execute_sql := REPLACE(upper(v_execute_sql),
                                                 upper('{entity_id}'),
                                                 p_entity_id || '');
                    ELSE
                        v_execute_sql := v_execute_sql || ' and entity_id = ' ||
                                         p_entity_id;
                    END IF;
                END IF;

                -- 拼接附加条件3
                IF v_year_month_flag = '1' THEN
                    IF instr(upper(p_rule_expr), upper('{year_month}')) > 0 THEN
                        v_execute_sql := REPLACE(upper(v_execute_sql),
                                                 upper('{year_month}'),
                                                 '''' || p_year_month || '''');
                    ELSE
                        v_execute_sql := v_execute_sql || ' and year_month = ''' ||
                                         p_year_month || '''';
                    END IF;
                END IF;

                -- 拼接附加条件2
                IF v_book_code_flag = '1' THEN
                    IF instr(upper(p_rule_expr), upper('{book_code}')) > 0 THEN
                        v_execute_sql := REPLACE(upper(v_execute_sql),
                                                 upper('{book_code}'),
                                                 '''' || p_book_code || '''');
                    ELSE
                        v_execute_sql := v_execute_sql || ' and book_code = ''' ||
                                         p_book_code || '''';
                    END IF;
                END IF;

            ELSE
                v_execute_sql := p_rule_expr;

            END IF;

        EXCEPTION
            WHEN OTHERS THEN
                v_error_msg := '脚本附加条件解析失败，请检查脚本Sql：' || v_execute_sql || '，附加条件：' ||
                               p_rely_on || '; ';
                v_log_msg := substr(v_error_msg || '**SQLERRM: ' || SQLERRM ||
                                    '**Error line: ' ||
                                    dbms_utility.format_error_backtrace() || '',
                                    1,
                                    4000);
                -- 往外层抛自定义异常
                raise_application_error(-20003, v_log_msg);

        END;

        --返回脚本结果
        RETURN v_execute_sql;

        -- 此处不做异常捕获，外层做捕获异常处理，并记录异常信息
    END func_rule_sql_relyon_anal;

    FUNCTION func_verify_rule_sql(p_rely_on VARCHAR2, p_rule_expr VARCHAR2)
        RETURN NUMBER AS
        /***********************************************************************
    NAME : rpt_pack_report_item_func_verify_sql
    DESCRIPTION : 列报项验证SQL脚本
    DATE :2022-01-19
    AUTHOR :YINXH

    -------
    MODIFY LOG

      UPDATE DATE :
      UPDATE BY   :
      UPDATE DESC :
    ***********************************************************************/

        v_log_msg            VARCHAR2(4000); --日志信息
        v_error_msg          VARCHAR2(2000); --异常信息

        v_result             NUMBER; --脚本执行结果值
        v_execute_sql        VARCHAR2(4000); --脚本解析结果
        -- 校验默认数据
        v_entity_id_default  NUMBER       := 1;
        v_book_code_default  VARCHAR2(10) := 'BookI17';
        v_year_month_default VARCHAR2(10) := '202101';

    BEGIN

        -- 调用函数：解析附加条件后拼接成可执行SQL脚本返回
        SELECT rpt_pack_report_item.func_rule_sql_relyon_anal(v_entity_id_default,
                                                              v_book_code_default,
                                                              v_year_month_default,
                                                              p_rely_on,
                                                              p_rule_expr)
        INTO v_execute_sql
        FROM dual;

        BEGIN
            -- 不分场景试执行脚本
            EXECUTE IMMEDIATE v_execute_sql;
        EXCEPTION
            WHEN OTHERS THEN
                -- 往上层返回信息
                v_result := 0;
                v_error_msg := '脚本验证执行失败，请检查脚本Sql：' || v_execute_sql || '，附加条件：' ||
                               p_rely_on || '; ';
                v_log_msg := substr(v_error_msg || '**SQLERRM: ' || SQLERRM ||
                                    '**Error line: ' ||
                                    dbms_utility.format_error_backtrace() || '',
                                    1,
                                    4000);
                -- 往外层抛自定义异常
                raise_application_error(-20003, v_log_msg);

        END;

        --返回脚本结果 0-执行不通过；1-执行通过
        RETURN coalesce(v_result, 1);

        -- 此处不做异常捕获，外层做捕获异常处理，并记录异常信息
    END func_verify_rule_sql;

    PROCEDURE proc_arithmetic(p_entity_id  NUMBER,
                              p_book_code  VARCHAR2,
                              p_year_month VARCHAR2,
                              p_deal_level NUMBER,
                              p_oper_id    NUMBER,
                              p_task_code  VARCHAR2) AS
        /***********************************************************************
    NAME : rpt_pack_report_item_proc_arithmetic
    PARAM :
    DESCRIPTION : 列报项取数[四则运算]
    DATE :2021-10-18
    AUTHOR :YINXH

    -------
    MODIFY LOG

      UPDATE DATE :
      UPDATE BY   :
      UPDATE DESC :
    ***********************************************************************/

        v_log_msg                    VARCHAR2(4000); --日志信息
        v_error_msg                  VARCHAR2(2000); --异常信息

        v_report_item_id             NUMBER(11, 0); --列报项ID
        v_report_item_code           VARCHAR2(100); --列报项编码
        v_rule_expr                  VARCHAR2(4000); --表达式

        v_count_rule_is_num          NUMBER(11, 0); --表达式是否全为数字项

        cur_item                     bpl_pack_common.type_array_table; --科目项记录集合
        v_currency                   VARCHAR2(3); --当前账套下的币别
        v_item_amount                NUMBER(32, 8); --科目金额
        v_rule_expr_analytical       VARCHAR2(4000); --解析后的规则表达式
        v_rule_expr_amount           NUMBER(32, 8); --规则表达式解析后的计算结果

        v_item_amount_cu             NUMBER(32, 8); --科目金额[本位币]
        v_rule_expr_analytical_cu    VARCHAR2(4000); --解析后的规则表达式[本位币]
        v_rule_expr_amount_cu        NUMBER(32, 8); --规则表达式解析后的计算结果[本位币]

        v_new_serial_no              NUMBER(6, 0); --新版本号
        v_new_report_data_id         NUMBER(11, 0); --新列项提数过程表ID

        v_acc_serial_no              NUMBER(6, 0); --会计引擎财务数据版本号

        v_report_item_rule_serial_no NUMBER(6, 0); --列报项规则版本号

    BEGIN

        FOR cur_item_rule IN (SELECT DISTINCT r1.report_item_id,
                                              r2.report_item_code,
                                              r1.expr_desc,
                                              r1.valid_is,
                                              r1.audit_state,
                                              r1.NEED_DIMENSION_IS,
                                              r1.report_item_rule_id
                              FROM rpt_conf_report_item_rule r1
                                       LEFT JOIN rpt_conf_report_item r2
                                                 ON r2.report_item_id = r1.report_item_id
                              WHERE r1.entity_id = p_entity_id   --业务单位id
                                AND r1.book_code = p_book_code   --账套编码
                                AND r1.deal_level = p_deal_level --计算优先级
                                AND r1.deal_type = '2'           --2-列项计算
                                AND r1.data_source = '4'         --4-四则运算
                                and (exists
                                         (select 1
                                          from RPT_BUSS_REPORT_TASK
                                          where TASK_CODE = p_task_code
                                            and TASK_TYPE = 1) or exists
                                         (select 1
                                          from RPT_BUSS_REPORT_TASK rt
                                                   left join RPT_BUSS_REPORT_TASK_ITEM rti
                                                             on rt.TASK_ID = rti.TASK_ID
                                          where rt.TASK_CODE = p_task_code
                                            and rt.TASK_TYPE = 2
                                            and rti.ITEM_ID = r1.REPORT_ITEM_ID))
                              ORDER BY r2.report_item_code ASC)
            LOOP

                BEGIN
                    -- 获取相关变量
                    v_report_item_id := cur_item_rule.report_item_id;
                    v_report_item_code := cur_item_rule.report_item_code;
                    v_rule_expr := cur_item_rule.expr_desc;

                    -- 获取财务科目数据版本号(批量数据，同一批版本号相同)
                    SELECT MAX(bh.serial_no)
                    INTO v_acc_serial_no
                    FROM rpt_dap_ledger_balance bh
                    WHERE bh.entity_id = p_entity_id
                      AND bh.book_code = p_book_code
                      AND bh.year_month = p_year_month;

                    -- 获取表达式全为数字项计数值,
                    SELECT COUNT(1)
                    INTO v_count_rule_is_num
                    FROM dual
                    WHERE regexp_like(regexp_replace(v_rule_expr, '\(|\)|\*|\-|\+|\/'),
                                      '(^[+-]?\d{0,}\.?\d{0,}$)');

                    -- 如果全为数字项，则计数值大于0
                    IF v_count_rule_is_num > 0 THEN

                        BEGIN
                            --拼接规则表达式解析后的计算SQL脚本，返回规则表达式解析后的计算结果
                            EXECUTE IMMEDIATE 'select (' || v_rule_expr || ') from dual'
                                INTO v_rule_expr_amount;

                        EXCEPTION
                            WHEN OTHERS THEN
                                v_error_msg := '列项规则解析执行发生错误：' || v_rule_expr || ', ' ||
                                               SQLERRM;
                                -- 往外层抛自定义异常
                                raise_application_error(-20002, v_error_msg);

                        END;

                        -- 未取到科目值，补0
                        IF v_rule_expr_amount IS NULL THEN
                            v_rule_expr_amount := 0.00;
                        END IF;

                        -- 先获取当前账套下的币别
                        SELECT r1.currency_code
                        INTO v_currency
                        FROM accuser.acc_conf_accountperiod r1
                        WHERE r1.entity_id = p_entity_id
                          AND r1.book_code = p_book_code
                          AND r1.year_month = p_year_month;

                        -- 再获取当前业务单位、账套编码、会计期间对应列项提数表的版本新版本号
                        SELECT coalesce((MAX(serial_no) + 1), 1)
                        INTO v_new_serial_no
                        FROM rpt_duct_report_item_data
                        WHERE entity_id = p_entity_id
                          AND book_code = p_book_code
                          AND year_month = p_year_month
                          and TASK_CODE = p_task_code
                          AND currency_code = v_currency
                          AND report_item_id = v_report_item_id;

                        -- 接着清除列项提数表当前条件对应的数据
                        DELETE
                        FROM rpt_duct_report_item_data
                        WHERE entity_id = p_entity_id
                          AND book_code = p_book_code
                          AND year_month = p_year_month
                          and TASK_CODE = p_task_code
                          AND currency_code = v_currency
                          AND report_item_id = v_report_item_id;

                        -- 获取列项提数过程表最新主键ID
                        v_new_report_data_id := rpt_seq_duct_rpt_item_data.nextval;

                        -- 获取列项规则版本号
                        SELECT serial_no
                        INTO v_report_item_rule_serial_no
                        FROM rpt_conf_report_item_rule
                        WHERE report_item_id = v_report_item_id
                          AND entity_id = p_entity_id
                          AND book_code = p_book_code;

                        -- 插入列项取数过程表
                        INSERT INTO rpt_duct_report_item_data
                        (report_item_data_id,
                         serial_no,
                         entity_id,
                         book_code,
                         year_month,
                         report_item_id,
                         expr_type,
                         article,
                         expr_desc,
                         expr_desc_analytic,
                         currency_code,
                         currency_cu_code,
                         amount,
                         amount_cu,
                         acc_serial_no,
                         creator_id,
                         create_time,
                         report_item_rule_serial_no,
                         TASK_CODE)
                        VALUES (v_new_report_data_id,
                                v_new_serial_no,
                                p_entity_id,
                                p_book_code,
                                p_year_month,
                                v_report_item_id,
                                NULL, --取数规则
                                NULL, --专项
                                v_rule_expr,
                                NULL, --纯数字项无解析表达式
                                v_currency,
                                v_currency,
                                v_rule_expr_amount,
                                v_rule_expr_amount,
                                v_acc_serial_no, --财务数据版本号
                                p_oper_id,
                                SYSDATE,
                                v_report_item_rule_serial_no,
                                p_task_code);

                        -- 记录列项规则提数轨迹
                        INSERT INTO rpt_duct_report_item_datahis
                        (report_item_data_his_id,
                         report_item_data_id,
                         serial_no,
                         entity_id,
                         book_code,
                         year_month,
                         report_item_id,
                         expr_type,
                         article,
                         expr_desc,
                         expr_desc_analytic,
                         currency_code,
                         currency_cu_code,
                         amount,
                         amount_cu,
                         acc_serial_no,
                         creator_id,
                         create_time,
                         report_item_rule_serial_no,
                         TASK_CODE)
                        SELECT rpt_seq_duct_rpt_item_datahis.nextval,
                               rd.report_item_data_id,
                               rd.serial_no,
                               rd.entity_id,
                               rd.book_code,
                               rd.year_month,
                               rd.report_item_id,
                               rd.expr_type,
                               rd.article,
                               rd.expr_desc,
                               rd.expr_desc_analytic,
                               rd.currency_code,
                               rd.currency_cu_code,
                               rd.amount,
                               rd.amount_cu,
                               rd.acc_serial_no,
                               rd.creator_id,
                               rd.create_time,
                               rd.report_item_rule_serial_no,
                               rd.TASK_CODE
                        FROM rpt_duct_report_item_data rd
                        WHERE rd.report_item_data_id = v_new_report_data_id;

                        -- 处理维度计算
                        if cur_item_rule.need_dimension_is = '1' then
                            -- 删除原数据
                            delete
                            from rpt_duct_dimension_data
                            where item_rule_id = cur_item_rule.report_item_rule_id
                              and entity_id = p_entity_id
                              AND book_code = p_book_code
                              AND year_month = p_year_month
                              AND currency_code = v_currency;

                            -- 插入维度数据过程表
                            insert into rpt_duct_dimension_data
                            (dimension_duct_data_id,
                             item_id,
                             item_rule_id,
                             BOOK_CODE,
                             YEAR_MONTH,
                             ENTITY_ID,
                             SERIAL_NO,
                             value,
                             value_cu,
                             CURRENCY_CODE,
                             CURRENCY_CU_CODE,
                             REPORT_ITEM_RULE_SERIAL_NO,
                             dimension1,
                             dimension2,
                             dimension3,
                             dimension4,
                             dimension5,
                             dimension6,
                             dimension7,
                             dimension8,
                             dimension9,
                             dimension10,
                             dimension11,
                             dimension12,
                             dimension13,
                             dimension14,
                             dimension15,
                             dimension16,
                             dimension17,
                             create_time,
                             creator_id)
                            values (rpt_seq_duct_dimension_data.nextval,
                                    v_report_item_id,
                                    cur_item_rule.report_item_rule_id,
                                    p_book_code,
                                    p_year_month,
                                    p_entity_id,
                                    v_new_serial_no,
                                    v_rule_expr_amount,
                                    v_rule_expr_amount,
                                    v_currency,
                                    v_currency,
                                    v_report_item_rule_serial_no,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    sysdate,
                                    p_oper_id);
                        end if;

                    ELSE
                        --非全数字项处理：
                        --获取表达式所有科目项的原币别，然后按币别循环以下处理过程，取得每个币别下的列报项及科目项金额
                        --判定使用本位币变量，不能使用原币，原币字段在做手工账的时候可能为0
                        FOR cur_currency IN (SELECT DISTINCT r1.currency_code,
                                                             r1.currency_cu_code
                                             FROM rpt_duct_report_item_data r1
                                                      LEFT JOIN rpt_conf_report_item r2
                                                                ON r2.report_item_id = r1.report_item_id
                                             WHERE r1.entity_id = p_entity_id --业务单位id
                                               AND r1.book_code = p_book_code --账套编码
                                               AND r1.year_month = p_year_month
                                               AND r2.report_item_code IN
                                                   (SELECT DISTINCT regexp_replace(regexp_substr(v_rule_expr,
                                                                                                 '\[(.+?)\]',
                                                                                                 1,
                                                                                                 LEVEL),
                                                                                   '\[|\]',
                                                                                   '')
                                                    FROM dual
                                                    CONNECT BY regexp_substr(v_rule_expr,
                                                                             '\[(.+?)\]',
                                                                             1,
                                                                             LEVEL) IS NOT NULL))
                            LOOP

                                -- 遍历币别初始表达式解析变量
                                v_rule_expr_analytical := v_rule_expr;
                                v_rule_expr_analytical_cu := v_rule_expr;

                                -- 获取当前业务单位、账套编码、会计期间对应列项提数表的版本新版本号
                                SELECT coalesce((MAX(serial_no) + 1), 1)
                                INTO v_new_serial_no
                                FROM rpt_duct_report_item_data
                                WHERE entity_id = p_entity_id
                                  AND book_code = p_book_code
                                  AND year_month = p_year_month
                                  and TASK_CODE = p_task_code
                                  AND currency_code = cur_currency.currency_code
                                  AND report_item_id = v_report_item_id;

                                -- 先清除列项提数表当前条件对应的数据
                                DELETE
                                FROM rpt_duct_report_item_data
                                WHERE entity_id = p_entity_id
                                  AND book_code = p_book_code
                                  AND year_month = p_year_month
                                  and TASK_CODE = p_task_code
                                  AND currency_code = cur_currency.currency_code
                                  AND report_item_id = v_report_item_id;

                                -- 获取列项规则版本号
                                SELECT serial_no
                                INTO v_report_item_rule_serial_no
                                FROM rpt_conf_report_item_rule
                                WHERE report_item_id = v_report_item_id
                                  AND entity_id = p_entity_id
                                  AND book_code = p_book_code;

                                -- 再插入列项取数过程表
                                v_new_report_data_id := rpt_seq_duct_rpt_item_data.nextval;
                                INSERT INTO rpt_duct_report_item_data
                                (report_item_data_id,
                                 serial_no,
                                 entity_id,
                                 book_code,
                                 year_month,
                                 report_item_id,
                                 expr_type,
                                 article,
                                 expr_desc,
                                 expr_desc_analytic,
                                 currency_code,
                                 currency_cu_code,
                                 amount,
                                 amount_cu,
                                 acc_serial_no,
                                 creator_id,
                                 create_time,
                                 report_item_rule_serial_no,
                                 TASK_CODE)
                                VALUES (v_new_report_data_id,
                                        v_new_serial_no,
                                        p_entity_id,
                                        p_book_code,
                                        p_year_month,
                                        v_report_item_id,
                                        NULL, --取数规则
                                        NULL, --专项
                                        v_rule_expr,
                                        NULL, --解析表达式
                                        cur_currency.currency_code,
                                        cur_currency.currency_cu_code,
                                        NULL,
                                        NULL,
                                        v_acc_serial_no, -- 财务数据版本号
                                        p_oper_id,
                                        SYSDATE,
                                        v_report_item_rule_serial_no,
                                        p_task_code);

                                -- 正则匹配获取[]包含项，排除数字项，并去重
                                FOR cur_item IN (SELECT DISTINCT regexp_substr(v_rule_expr,
                                                                               '\[(.+?)\]',
                                                                               1,
                                                                               LEVEL) AS cal_item
                                                 FROM dual
                                                 CONNECT BY regexp_substr(v_rule_expr,
                                                                          '\[(.+?)\]',
                                                                          1,
                                                                          LEVEL) IS NOT NULL)
                                    LOOP

                                        -- 从列项过程表取数
                                        SELECT sum(rb.amount), sum(rb.amount_cu)
                                        INTO v_item_amount, v_item_amount_cu
                                        FROM rpt_duct_report_item_data rb,
                                             rpt_conf_report_item ri
                                        WHERE rb.report_item_id = ri.report_item_id --列报项ID
                                          AND ri.report_item_code =
                                              regexp_replace(cur_item.cal_item, '\[|\]', '')
                                          AND rb.entity_id = p_entity_id
                                          AND rb.book_code = p_book_code
                                          AND rb.year_month = p_year_month
                                          and rb.TASK_CODE = p_task_code
                                          AND rb.currency_code = cur_currency.currency_code;

                                        -- 未取到科目值，补0
                                        IF v_item_amount_cu IS NULL THEN
                                            v_item_amount := 0.00;
                                            v_item_amount_cu := 0.00;
                                        END IF;

                                        -- [汇总币别]比较科目项的值，用正则(需转义处理)匹配替换表达式中科目项的编码
                                        IF v_item_amount_cu > 0 THEN
                                            -- 当科目项的值为正数时，直接替换，数字转字符串 cast(123 as VARCHAR2)
                                            v_rule_expr_analytical := REPLACE(v_rule_expr_analytical,
                                                                              cur_item.cal_item,
                                                                              CAST(v_item_amount AS
                                                                                  VARCHAR2));

                                            -- [本位币]当科目项的值为正数时，直接替换，数字转字符串 cast(123 as VARCHAR2)
                                            v_rule_expr_analytical_cu := REPLACE(v_rule_expr_analytical_cu,
                                                                                 cur_item.cal_item,
                                                                                 CAST(v_item_amount_cu AS
                                                                                     VARCHAR2));

                                        ELSIF v_item_amount_cu < 0 THEN
                                            -- 当科目项的值为负数时，需要添加小括号
                                            v_rule_expr_analytical := REPLACE(v_rule_expr_analytical,
                                                                              cur_item.cal_item,
                                                                              '(' ||
                                                                              CAST(v_item_amount AS
                                                                                  VARCHAR2) || ')');

                                            -- [本位币]当科目项的值为负数时，需要添加小括号
                                            v_rule_expr_analytical_cu := REPLACE(v_rule_expr_analytical_cu,
                                                                                 cur_item.cal_item,
                                                                                 '(' ||
                                                                                 CAST(v_item_amount_cu AS
                                                                                     VARCHAR2) || ')');

                                        ELSE
                                            -- 若查询不到科目项的值，作为除数时，需要替换为1
                                            IF instr(v_rule_expr, ('/' || cur_item.cal_item)) > 0 THEN
                                                v_rule_expr_analytical := REPLACE(v_rule_expr_analytical,
                                                                                  ('/' ||
                                                                                   cur_item.cal_item),
                                                                                  '/1');
                                                -- [本位币]
                                                v_rule_expr_analytical_cu := REPLACE(v_rule_expr_analytical_cu,
                                                                                     ('/' ||
                                                                                      cur_item.cal_item),
                                                                                     '/1');

                                            ELSE
                                                --不作为除数时，直接替换为0
                                                v_rule_expr_analytical := REPLACE(v_rule_expr_analytical,
                                                                                  cur_item.cal_item,
                                                                                  '0');
                                                -- [本位币]
                                                v_rule_expr_analytical_cu := REPLACE(v_rule_expr_analytical_cu,
                                                                                     cur_item.cal_item,
                                                                                     '0');

                                            END IF;
                                        END IF;

                                        -- 插入列报项取数过程明细表【必须保留旧版本数据】
                                        INSERT INTO rpt_duct_report_item_data_sub
                                        (report_item_data_details_id,
                                         report_item_data_id,
                                         expr_code, --取数项编码
                                         expr_desc, --取数项表达式
                                         currency_code,
                                         currency_cu_code,
                                         amount,
                                         amount_cu,
                                         creator_id,
                                         create_time)
                                        VALUES (rpt_seq_duct_rpt_item_data_dtl.nextval,
                                                v_new_report_data_id,
                                                regexp_replace(cur_item.cal_item, '\[|\]', ''), --列报项编码
                                                cur_item.cal_item, --解析出来的列报项编码，带[]
                                                cur_currency.currency_code,
                                                cur_currency.currency_cu_code,
                                                v_item_amount,
                                                v_item_amount_cu,
                                                p_oper_id,
                                                SYSDATE);

                                        -- 处理维度计算
                                        if cur_item_rule.need_dimension_is = '1' then
                                            -- 从维度过程表中获取表达式中列报项的维度数据，并插入明细表
                                            delete
                                            from rpt_duct_dimension_data_sub sub
                                            where sub.entity_id = p_entity_id
                                              and sub.book_code = p_book_code
                                              and sub.year_month = p_year_month
                                              and sub.item_rule_id = cur_item_rule.report_item_rule_id
                                              and sub.currency_code = cur_currency.currency_code;
                                            insert into rpt_duct_dimension_data_sub
                                            (dimension_duct_data_sub_id,
                                             item_id,
                                             item_rule_id,
                                             book_code,
                                             year_month,
                                             entity_id,
                                             serial_no,
                                             value,
                                             value_cu,
                                             currency_code,
                                             currency_cu_code,
                                             expr_code,
                                             report_item_rule_serial_no,
                                             dimension1,
                                             dimension2,
                                             dimension3,
                                             dimension4,
                                             dimension5,
                                             dimension6,
                                             dimension7,
                                             dimension8,
                                             dimension9,
                                             dimension10,
                                             dimension11,
                                             dimension12,
                                             dimension13,
                                             dimension14,
                                             dimension15,
                                             dimension16,
                                             dimension17,
                                             create_time,
                                             creator_id)
                                            select rpt_seq_duct_dimension_data_sub.nextval,
                                                   v_report_item_id,
                                                   cur_item_rule.report_item_rule_id,
                                                   t.book_code,
                                                   t.year_month,
                                                   t.entity_id,
                                                   v_new_serial_no,
                                                   t.value,
                                                   t.value_cu,
                                                   t.currency_code,
                                                   t.currency_cu_code,
                                                   regexp_replace(cur_item.cal_item, '\[|\]', ''),
                                                   v_report_item_rule_serial_no,
                                                   t.dimension1,
                                                   t.dimension2,
                                                   t.dimension3,
                                                   t.dimension4,
                                                   t.dimension5,
                                                   t.dimension6,
                                                   t.dimension7,
                                                   t.dimension8,
                                                   t.dimension9,
                                                   t.dimension10,
                                                   t.dimension11,
                                                   t.dimension12,
                                                   t.dimension13,
                                                   t.dimension14,
                                                   t.dimension15,
                                                   t.dimension16,
                                                   t.dimension17,
                                                   sysdate,
                                                   p_oper_id
                                            from (select data.book_code,
                                                         data.year_month,
                                                         data.entity_id,
                                                         data.value,
                                                         data.value_cu,
                                                         data.currency_code,
                                                         data.currency_cu_code,
                                                         data.dimension1,
                                                         data.dimension2,
                                                         data.dimension3,
                                                         data.dimension4,
                                                         data.dimension5,
                                                         data.dimension6,
                                                         data.dimension7,
                                                         data.dimension8,
                                                         data.dimension9,
                                                         data.dimension10,
                                                         data.dimension11,
                                                         data.dimension12,
                                                         data.dimension13,
                                                         data.dimension14,
                                                         data.dimension15,
                                                         data.dimension16,
                                                         data.dimension17
                                                  from rpt_duct_dimension_data data
                                                           left join rpt_conf_report_item item
                                                                     on data.item_id = item.report_item_id
                                                  where data.entity_id = p_entity_id
                                                    and data.currency_code =
                                                        cur_currency.currency_code
                                                    and data.currency_cu_code =
                                                        cur_currency.currency_cu_code
                                                    and data.book_code = p_book_code
                                                    and data.year_month = p_year_month
                                                    and item.report_item_code =
                                                        regexp_replace(cur_item.cal_item,
                                                                       '\[|\]',
                                                                       '')) t;
                                        end if;

                                    END LOOP;
                                --遍历科目项

                                -- 计算表达式的值
                                IF v_rule_expr_analytical_cu IS NOT NULL THEN

                                    BEGIN
                                        --[原币]拼接规则表达式解析后的计算SQL脚本，返回规则表达式解析后的计算结果
                                        EXECUTE IMMEDIATE 'select (' || v_rule_expr_analytical ||
                                                          ') from dual'
                                            INTO v_rule_expr_amount;
                                        --[本位币]拼接规则表达式解析后的计算SQL脚本，返回规则表达式解析后的计算结果
                                        EXECUTE IMMEDIATE 'select (' || v_rule_expr_analytical_cu ||
                                                          ') from dual'
                                            INTO v_rule_expr_amount_cu;

                                    EXCEPTION
                                        WHEN OTHERS THEN
                                            v_error_msg := '列项规则解析执行发生错误：' || v_rule_expr_analytical ||
                                                           ' | ' || v_rule_expr_analytical_cu || ', ' ||
                                                           SQLERRM;
                                            -- 往外层抛自定义异常
                                            raise_application_error(-20002, v_error_msg);

                                    END;

                                    -- 更新数据到列项取数过程表
                                    UPDATE rpt_duct_report_item_data
                                    SET expr_desc_analytic = v_rule_expr_analytical || ' | ' ||
                                                             v_rule_expr_analytical_cu,
                                        amount             = v_rule_expr_amount,
                                        amount_cu          = v_rule_expr_amount_cu
                                    WHERE report_item_data_id = v_new_report_data_id;
                                END IF;

                                -- 记录列项规则提数轨迹
                                INSERT INTO rpt_duct_report_item_datahis
                                (report_item_data_his_id,
                                 report_item_data_id,
                                 serial_no,
                                 entity_id,
                                 book_code,
                                 year_month,
                                 report_item_id,
                                 expr_type,
                                 article,
                                 expr_desc,
                                 expr_desc_analytic,
                                 currency_code,
                                 currency_cu_code,
                                 amount,
                                 amount_cu,
                                 acc_serial_no,
                                 creator_id,
                                 create_time,
                                 report_item_rule_serial_no,
                                 TASK_CODE)
                                SELECT rpt_seq_duct_rpt_item_datahis.nextval,
                                       rd.report_item_data_id,
                                       rd.serial_no,
                                       rd.entity_id,
                                       rd.book_code,
                                       rd.year_month,
                                       rd.report_item_id,
                                       rd.expr_type,
                                       rd.article,
                                       rd.expr_desc,
                                       rd.expr_desc_analytic,
                                       rd.currency_code,
                                       rd.currency_cu_code,
                                       rd.amount,
                                       rd.amount_cu,
                                       rd.acc_serial_no,
                                       rd.creator_id,
                                       rd.create_time,
                                       rd.report_item_rule_serial_no,
                                       rd.TASK_CODE
                                FROM rpt_duct_report_item_data rd
                                WHERE rd.report_item_data_id = v_new_report_data_id;

                            END LOOP; -- 遍历币别
                    END IF;

                    --【提取结果数据】
                    -- 有效且审核通过状态的列项规则提数到结果表
                    IF cur_item_rule.valid_is = '1' AND cur_item_rule.audit_state = '1' THEN
                        -- 先清除列项提数表当前条件对应的数据
                        DELETE
                        FROM rpt_buss_report_item_data
                        WHERE entity_id = p_entity_id
                          AND book_code = p_book_code
                          AND year_month = p_year_month
                          and TASK_CODE = p_task_code
                          AND report_item_id = v_report_item_id;

                        -- 按本位币从过程表提取列报项数据到结果表
                        INSERT INTO rpt_buss_report_item_data
                        (report_item_data_id,
                         serial_no,
                         entity_id,
                         book_code,
                         year_month,
                         report_item_id,
                         expr_type,
                         article,
                         currency_code,
                         amount,
                         creator_id,
                         create_time,
                         report_item_rule_serial_no,
                         TASK_CODE)
                        SELECT rpt_seq_buss_rpt_item_data.nextval,
                               td.serial_no,
                               td.entity_id,
                               td.book_code,
                               td.year_month,
                               td.report_item_id,
                               td.expr_type,
                               td.article,
                               td.currency_cu_code,
                               td.sum_amount_cu,
                               p_oper_id,
                               SYSDATE,
                               td.report_item_rule_serial_no,
                               td.task_code
                        FROM (SELECT max(rd.serial_no) serial_no,
                                     rd.entity_id,
                                     rd.book_code,
                                     rd.year_month,
                                     rd.report_item_id,
                                     rd.expr_type,
                                     rd.article,
                                     rd.currency_cu_code,
                                     SUM(rd.amount_cu) sum_amount_cu,
                                     rd.report_item_rule_serial_no,
                                     rd.TASK_CODE
                              FROM rpt_duct_report_item_data rd
                              WHERE rd.entity_id = p_entity_id
                                AND rd.book_code = p_book_code
                                AND rd.year_month = p_year_month
                                and rd.TASK_CODE = p_task_code
                                AND rd.report_item_id = v_report_item_id
                              GROUP BY rd.currency_cu_code,
                                       rd.entity_id,
                                       rd.book_code,
                                       rd.year_month,
                                       rd.report_item_id,
                                       rd.expr_type,
                                       rd.article,
                                       rd.report_item_rule_serial_no,
                                       rd.TASK_CODE) td;
                        --取本位币，此处不允许直接使用序号，因此需要包裹一层

                        -- 记录列项规则提数结果表轨迹
                        INSERT INTO rpt_buss_report_item_datahis
                        (report_item_data_his_id,
                         report_item_data_id,
                         serial_no,
                         entity_id,
                         book_code,
                         year_month,
                         report_item_id,
                         expr_type,
                         article,
                         currency_code,
                         amount,
                         creator_id,
                         create_time,
                         report_item_rule_serial_no,
                         TASK_CODE)
                        SELECT rpt_seq_buss_rpt_item_datahis.nextval,
                               rd.report_item_data_id,
                               rd.serial_no,
                               rd.entity_id,
                               rd.book_code,
                               rd.year_month,
                               rd.report_item_id,
                               rd.expr_type,
                               rd.article,
                               rd.currency_code,
                               rd.amount,
                               rd.creator_id,
                               rd.create_time,
                               rd.report_item_rule_serial_no,
                               rd.TASK_CODE
                        FROM rpt_buss_report_item_data rd
                        WHERE rd.entity_id = p_entity_id
                          AND rd.book_code = p_book_code
                          AND rd.year_month = p_year_month
                          and TASK_CODE = p_task_code
                          AND rd.report_item_id = v_report_item_id;

                    END IF;

                    if cur_item_rule.need_dimension_is = '1' then
                        rpt_pack_report_item.proc_calculate_dimiension_data(p_entity_id,
                                                                            p_book_code,
                                                                            p_year_month,
                                                                            v_report_item_id,
                                                                            cur_item_rule.report_item_rule_id,
                                                                            p_oper_id,
                                                                            v_new_serial_no,
                                                                            v_report_item_rule_serial_no);
                    end if;

                    --提交事务
                    COMMIT;

                EXCEPTION
                    WHEN OTHERS THEN
                        v_log_msg := substr('列报项[' || v_report_item_code || ']四则运算取数异常：' ||
                                            v_error_msg || '; ' || '**SQLERRM: ' ||
                                            SQLERRM || '**Error line: ' ||
                                            dbms_utility.format_error_backtrace() || '',
                                            1,
                                            4000);

                        -- 写入取数日志
                        INSERT INTO rpt_log_report_item_draw_task
                        (draw_task_id,
                         entity_id,
                         book_code,
                         year_month,
                         expr_type,
                         expr_desc,
                         task_name,
                         task_params,
                         taks_status,
                         log_msg,
                         oper_id,
                         oper_time,
                         TASK_CODE)
                        VALUES (rpt_seq_log_rpt_item_draw_task.nextval,
                                p_entity_id,
                                p_book_code,
                                p_year_month,
                                NULL,
                                v_rule_expr,
                                'rpt_pack_report_item_proc_arithmetic',
                                v_report_item_code,
                                '2',
                                v_log_msg,
                                p_oper_id,
                                SYSDATE,
                                p_task_code);

                        --提交日志写入事务
                        COMMIT;

                        -- 往外层抛自定义异常
                        raise_application_error(-20003, v_log_msg);

                END;
            END LOOP; --遍历当前计算优先级规则

    END proc_arithmetic;

    PROCEDURE proc_buss_sql(p_entity_id  NUMBER,
                            p_book_code  VARCHAR2,
                            p_year_month VARCHAR2,
                            p_oper_id    NUMBER,
                            p_task_code  VARCHAR2) AS
        /***********************************************************************
    NAME : rpt_pack_report_item_proc_buss_sql
    DESCRIPTION : 列报项业务SQL取数[SQL脚本]
    DATE :2021-10-11
    AUTHOR :YINXH

    -------
    MODIFY LOG

      UPDATE DATE :
      UPDATE BY   :
      UPDATE DESC :
    ***********************************************************************/

        v_log_msg                    VARCHAR2(4000); --日志信息
        v_error_msg                  VARCHAR2(2000); --错误信息

        v_currency                   VARCHAR2(3); --当前账套下的币别
        v_rule_expr_amount           NUMBER(32, 8); --规则表达式解析后的计算结果
        v_new_serial_no              NUMBER(6, 0); --新版本号
        v_new_report_data_id         NUMBER(11, 0); --新列项提数过程表ID
        v_acc_serial_no              NUMBER(6, 0); --会计引擎财务数据版本号
        v_execute_sql                VARCHAR2(4000); --脚本解析结果

        v_report_item_rule_serial_no NUMBER(6, 0); --列报项规则版本号

    BEGIN

        FOR cur_item_rule IN (SELECT t.report_item_id,
                                     r1.report_item_code,
                                     t.expr_type,
                                     t.expr_desc, --表达式(SQL脚本)
                                     t.valid_is,
                                     t.audit_state,
                                     t.rely_on    --附加条件
                              FROM rpt_conf_report_item_rule t
                                       LEFT JOIN rpt_conf_report_item r1
                                                 ON r1.report_item_id = t.report_item_id
                              WHERE t.entity_id = p_entity_id   --业务单位id
                                AND t.book_code = p_book_code   --账套编码
                                AND t.deal_type = '1'           --取数方式：1-提数统计
                                AND t.data_source IN ('2', '3') --数据来源：2-业务数据、3-SQL取数
                                and (exists
                                         (select 1
                                          from RPT_BUSS_REPORT_TASK
                                          where TASK_CODE = p_task_code
                                            and TASK_TYPE = 1) or exists
                                         (select 1
                                          from RPT_BUSS_REPORT_TASK rt
                                                   left join RPT_BUSS_REPORT_TASK_ITEM rti
                                                             on rt.TASK_ID = rti.TASK_ID
                                          where rt.TASK_CODE = p_task_code
                                            and rt.TASK_TYPE = 2
                                            and r1.REPORT_ITEM_ID = rti.ITEM_ID)))
            LOOP

                BEGIN

                    -- 获取财务科目数据版本号(批量数据，同一批版本号相同)
                    SELECT MAX(bh.serial_no)
                    INTO v_acc_serial_no
                    FROM rpt_dap_ledger_balance bh
                    WHERE bh.entity_id = p_entity_id
                      AND bh.book_code = p_book_code
                      AND bh.year_month = p_year_month;

                    --当前账套下的币别
                    SELECT r1.currency_code
                    INTO v_currency
                    FROM accuser.acc_conf_accountperiod r1
                    WHERE r1.entity_id = p_entity_id
                      AND r1.book_code = p_book_code
                      AND r1.year_month = p_year_month;

                    -- 计算表达式的值
                    -- 调用函数：解析附加条件后拼接成可执行SQL脚本返回
                    SELECT rpt_pack_report_item.func_rule_sql_relyon_anal(p_entity_id,
                                                                          p_book_code,
                                                                          p_year_month,
                                                                          cur_item_rule.rely_on,
                                                                          cur_item_rule.expr_desc)
                    INTO v_execute_sql
                    FROM dual;
                    BEGIN

                        EXECUTE IMMEDIATE v_execute_sql
                            INTO v_rule_expr_amount;

                    EXCEPTION
                        WHEN OTHERS THEN
                            v_error_msg := '列项规则解析执行发生错误：' || v_execute_sql || '; ';
                            v_log_msg := substr(v_error_msg || '**SQLERRM: ' || SQLERRM ||
                                                '**Error line: ' ||
                                                dbms_utility.format_error_backtrace() || '',
                                                1,
                                                4000);
                            -- 往外层抛自定义异常
                            raise_application_error(-20003, v_log_msg);

                    END;

                    -- 未取到科目值，补0
                    IF v_rule_expr_amount IS NULL THEN
                        v_rule_expr_amount := 0.00;
                    END IF;

                    -- 获取当前业务单位、账套编码、会计期间对应列项提数表的版本新版本号
                    SELECT coalesce((MAX(serial_no) + 1), 1)
                    INTO v_new_serial_no
                    FROM rpt_duct_report_item_data
                    WHERE entity_id = p_entity_id
                      AND book_code = p_book_code
                      AND year_month = p_year_month
                      and task_code = p_task_code
                      AND currency_code = v_currency
                      AND report_item_id = cur_item_rule.report_item_id;

                    -- 先清除列项提数表当前条件对应的数据
                    DELETE
                    FROM rpt_duct_report_item_data
                    WHERE entity_id = p_entity_id
                      AND book_code = p_book_code
                      AND year_month = p_year_month
                      and TASK_CODE = p_task_code
                      AND currency_code = v_currency
                      AND report_item_id = cur_item_rule.report_item_id;

                    -- 再插入列项取数过程表
                    v_new_report_data_id := rpt_seq_duct_rpt_item_data.nextval;

                    -- 获取列项规则版本号
                    SELECT serial_no
                    INTO v_report_item_rule_serial_no
                    FROM rpt_conf_report_item_rule
                    WHERE report_item_id = cur_item_rule.report_item_id
                      AND entity_id = p_entity_id
                      AND book_code = p_book_code;

                    INSERT INTO rpt_duct_report_item_data
                    (report_item_data_id,
                     serial_no,
                     entity_id,
                     book_code,
                     year_month,
                     report_item_id,
                     expr_type,
                     article,
                     expr_desc,
                     expr_desc_analytic,
                     currency_code,
                     currency_cu_code,
                     amount,
                     amount_cu,
                     acc_serial_no,
                     creator_id,
                     create_time,
                     report_item_rule_serial_no,
                     TASK_CODE)
                    VALUES (v_new_report_data_id,
                            v_new_serial_no,
                            p_entity_id,
                            p_book_code,
                            p_year_month,
                            cur_item_rule.report_item_id,
                            cur_item_rule.expr_type,
                            NULL, --专项
                            cur_item_rule.expr_desc,
                            cur_item_rule.expr_desc, --解析表达式
                            v_currency,
                            v_currency,
                            v_rule_expr_amount,
                            v_rule_expr_amount,
                            v_acc_serial_no, --财务数据版本号
                            p_oper_id,
                            SYSDATE,
                            v_report_item_rule_serial_no,
                            p_task_code);

                    -- 记录列项规则提数轨迹
                    INSERT INTO rpt_duct_report_item_datahis
                    (report_item_data_his_id,
                     report_item_data_id,
                     serial_no,
                     entity_id,
                     book_code,
                     year_month,
                     report_item_id,
                     expr_type,
                     article,
                     expr_desc,
                     expr_desc_analytic,
                     currency_code,
                     currency_cu_code,
                     amount,
                     amount_cu,
                     acc_serial_no,
                     creator_id,
                     create_time,
                     report_item_rule_serial_no,
                     TASK_CODE)
                    SELECT rpt_seq_duct_rpt_item_datahis.nextval,
                           rd.report_item_data_id,
                           rd.serial_no,
                           rd.entity_id,
                           rd.book_code,
                           rd.year_month,
                           rd.report_item_id,
                           rd.expr_type,
                           rd.article,
                           rd.expr_desc,
                           rd.expr_desc_analytic,
                           rd.currency_code,
                           rd.currency_cu_code,
                           rd.amount,
                           rd.amount_cu,
                           rd.acc_serial_no,
                           rd.creator_id,
                           rd.create_time,
                           rd.report_item_rule_serial_no,
                           rd.TASK_CODE
                    FROM rpt_duct_report_item_data rd
                    WHERE rd.report_item_data_id = v_new_report_data_id;

                    --【提取结果数据】
                    -- 有效且审核通过状态的列项规则提数到结果表
                    IF cur_item_rule.valid_is = '1' AND cur_item_rule.audit_state = '1' THEN
                        -- 先清除列项提数表当前条件对应的数据
                        DELETE
                        FROM rpt_buss_report_item_data
                        WHERE entity_id = p_entity_id
                          AND book_code = p_book_code
                          AND year_month = p_year_month
                          and TASK_CODE = p_task_code
                          AND report_item_id = cur_item_rule.report_item_id;

                        -- 按本位币从过程表提取列报项数据到结果表
                        INSERT INTO rpt_buss_report_item_data
                        (report_item_data_id,
                         serial_no,
                         entity_id,
                         book_code,
                         year_month,
                         report_item_id,
                         expr_type,
                         article,
                         currency_code,
                         amount,
                         creator_id,
                         create_time,
                         report_item_rule_serial_no,
                         TASK_CODE)
                        SELECT rpt_seq_buss_rpt_item_data.nextval,
                               td.serial_no,
                               td.entity_id,
                               td.book_code,
                               td.year_month,
                               td.report_item_id,
                               td.expr_type,
                               td.article,
                               td.currency_cu_code,
                               td.sum_amount_cu,
                               p_oper_id,
                               SYSDATE,
                               td.report_item_rule_serial_no,
                               td.task_code
                        FROM (SELECT max(rd.serial_no) serial_no,
                                     rd.entity_id,
                                     rd.book_code,
                                     rd.year_month,
                                     rd.report_item_id,
                                     rd.expr_type,
                                     rd.article,
                                     rd.currency_cu_code,
                                     SUM(rd.amount_cu) sum_amount_cu,
                                     rd.report_item_rule_serial_no,
                                     rd.TASK_CODE
                              FROM rpt_duct_report_item_data rd
                              WHERE rd.entity_id = p_entity_id
                                AND rd.book_code = p_book_code
                                AND rd.year_month = p_year_month
                                and rd.TASK_CODE = p_task_code
                                AND rd.report_item_id = cur_item_rule.report_item_id
                              GROUP BY rd.currency_cu_code,
                                       rd.entity_id,
                                       rd.book_code,
                                       rd.year_month,
                                       rd.report_item_id,
                                       rd.expr_type,
                                       rd.article,
                                       rd.report_item_rule_serial_no,
                                       rd.TASK_CODE) td;
                        --取本位币

                        -- 记录列项规则提数结果表轨迹
                        INSERT INTO rpt_buss_report_item_datahis
                        (report_item_data_his_id,
                         report_item_data_id,
                         serial_no,
                         entity_id,
                         book_code,
                         year_month,
                         report_item_id,
                         expr_type,
                         article,
                         currency_code,
                         amount,
                         creator_id,
                         create_time,
                         report_item_rule_serial_no,
                         TASK_CODE)
                        SELECT rpt_seq_buss_rpt_item_datahis.nextval,
                               rd.report_item_data_id,
                               rd.serial_no,
                               rd.entity_id,
                               rd.book_code,
                               rd.year_month,
                               rd.report_item_id,
                               rd.expr_type,
                               rd.article,
                               rd.currency_code,
                               rd.amount,
                               rd.creator_id,
                               rd.create_time,
                               rd.report_item_rule_serial_no,
                               rd.TASK_CODE
                        FROM rpt_buss_report_item_data rd
                        WHERE rd.entity_id = p_entity_id
                          AND rd.book_code = p_book_code
                          AND rd.year_month = p_year_month
                          and rd.TASK_CODE = p_task_code
                          AND rd.report_item_id = cur_item_rule.report_item_id;
                    END IF;

                    -- 提交事务
                    COMMIT;

                EXCEPTION
                    WHEN OTHERS THEN
                        v_log_msg := substr('列报项[' || cur_item_rule.report_item_code ||
                                            ']业务数据或SQL取数异常：' || v_error_msg ||
                                            '；**SQLERRM: ' || SQLERRM || '**Error line: ' ||
                                            dbms_utility.format_error_backtrace() || '',
                                            1,
                                            4000);

                        -- 写入取数日志
                        INSERT INTO rpt_log_report_item_draw_task
                        (draw_task_id,
                         entity_id,
                         book_code,
                         year_month,
                         expr_type,
                         expr_desc,
                         task_name,
                         task_params,
                         taks_status,
                         log_msg,
                         oper_id,
                         oper_time,
                         TASK_CODE)
                        VALUES (rpt_seq_log_rpt_item_draw_task.nextval,
                                p_entity_id,
                                p_book_code,
                                p_year_month,
                                NULL,
                                cur_item_rule.expr_desc,
                                'rpt_pack_report_item_proc_buss_sql',
                                cur_item_rule.report_item_code,
                                '2',
                                v_log_msg,
                                p_oper_id,
                                SYSDATE,
                                p_task_code);

                        -- 提交日志写入事务
                        COMMIT;

                        -- 往外层抛自定义异常
                        raise_application_error(-20002, v_log_msg);

                END;
            END LOOP;
    END proc_buss_sql;

    PROCEDURE proc_draw_data(p_entity_id  NUMBER,
                             p_book_code  VARCHAR2,
                             p_year_month VARCHAR2,
                             p_oper_id    NUMBER,
                             p_task_code  VARCHAR2) AS
        /***********************************************************************
    NAME : rpt_pack_report_item_proc_draw_data
    PARAM :
    DESCRIPTION : 报表列报项提取数据
    DATE :2021-10-18
    AUTHOR :YINXH

    -------
    MODIFY LOG

      UPDATE DATE :
      UPDATE BY   :
      UPDATE DESC :
    ***********************************************************************/

        v_log_msg    VARCHAR2(4000); --日志信息
        v_error_msg  VARCHAR2(2000); --异常信息
        v_count      number(11);
        v_task_count number(11);
        V_TASK_TYPE  number(11);

    BEGIN
        /**
    * 提数规则：
    *  1、先按取数方式的顺序捞取列报项规则进行提数
    *  2、当取数方式为列项计算和列项汇总时，再按计算优先级获取列项规则
    */

        -- 查询条件为空，返回 null 值
        IF p_entity_id IS NULL OR p_book_code IS NULL OR p_year_month IS NULL THEN

            --抛出自定义异常信息 【会中断事务】
            v_error_msg := '[EXCEPTION]报表列报项提数传参不能为空：' || p_entity_id || ']-[' ||
                           p_book_code || '-]' || ']-[' || p_year_month || ']';

            raise_application_error(-20002, v_error_msg);

        END IF;

        -- 查出任务信息
        select count(1)
        into v_task_count
        from RPT_BUSS_REPORT_TASK
        where TASK_CODE = p_task_code;
        if v_task_count != 0 then
            select TASK_TYPE
            into V_TASK_TYPE
            from RPT_BUSS_REPORT_TASK
            where TASK_CODE = p_task_code;
        end if;

        -- 判断是否有条件
        select count(1)
        into v_count
        from RPT_BUSS_REPORT_TASK_CONDITION
        where TASK_CODE = p_task_code;
        -- 一、【提取结转数据】
        rpt_pack_report_item.proc_draw_carryover_data(p_entity_id,
                                                      p_book_code,
                                                      p_year_month,
                                                      p_oper_id,
                                                      p_task_code);

        -- 二、【提数统计】：先取财务数据，分别按取数规则进行科目取数
        -- 取数规则按1-期初余额取数
        rpt_pack_report_item.proc_draw_financials_data(p_entity_id,
                                                       p_book_code,
                                                       p_year_month,
                                                       '1',
                                                       p_oper_id,
                                                       p_task_code);


        -- 取数规则按2-期末余额取数
        rpt_pack_report_item.proc_draw_financials_data(p_entity_id,
                                                       p_book_code,
                                                       p_year_month,
                                                       '2',
                                                       p_oper_id,
                                                       p_task_code);

        -- 取数规则按3-发生额取数
        rpt_pack_report_item.proc_draw_financials_data(p_entity_id,
                                                       p_book_code,
                                                       p_year_month,
                                                       '3',
                                                       p_oper_id,
                                                       p_task_code);

        -- 取数规则按4-本年增减变动取数
        rpt_pack_report_item.proc_draw_financials_data(p_entity_id,
                                                       p_book_code,
                                                       p_year_month,
                                                       '4',
                                                       p_oper_id,
                                                       p_task_code);

        -- 业务数据及SQL脚本取数
        rpt_pack_report_item.proc_buss_sql(p_entity_id,
                                           p_book_code,
                                           p_year_month,
                                           p_oper_id,
                                           p_task_code);
        -- 三、【列项计算\列项汇总】：按计算优先级分级处理[从高到低计算优先级(值小级高)]
        FOR cur_deal_level IN (SELECT DISTINCT ir.deal_level
                               FROM rpt_conf_report_item_rule ir
                               WHERE ir.entity_id = p_entity_id --业务单位id
                                 AND ir.book_code = p_book_code --账套编码
                                 AND ir.deal_type IN ('2', '3') --2-列项计算\3-列项汇总
                                 and (exists
                                          (select 1
                                           from RPT_BUSS_REPORT_TASK
                                           where TASK_CODE = p_task_code
                                             and TASK_TYPE = 1) or exists
                                          (select 1
                                           from RPT_BUSS_REPORT_TASK rt
                                                    left join RPT_BUSS_REPORT_TASK_ITEM rti
                                                              on rt.TASK_ID = rti.TASK_ID
                                           where rt.TASK_CODE = p_task_code
                                             and rt.TASK_TYPE = 2
                                             and rti.ITEM_ID = ir.REPORT_ITEM_ID))
                               ORDER BY deal_level ASC)
            LOOP

                -- 列项计算>>四则运算
                rpt_pack_report_item.proc_arithmetic(p_entity_id,
                                                     p_book_code,
                                                     p_year_month,
                                                     cur_deal_level.deal_level,
                                                     p_oper_id,
                                                     p_task_code);

                -- 列项计算>>条件运算
                rpt_pack_report_item.proc_ternary(p_entity_id,
                                                  p_book_code,
                                                  p_year_month,
                                                  cur_deal_level.deal_level,
                                                  p_oper_id,
                                                  p_task_code);

                -- 列项汇总
                rpt_pack_report_item.proc_summary(p_entity_id,
                                                  p_book_code,
                                                  p_year_month,
                                                  cur_deal_level.deal_level,
                                                  p_oper_id,
                                                  p_task_code);

            END LOOP;
        -- 报表列报项提取数据结束，写入取数日志
        INSERT INTO rpt_log_report_item_draw_task
        (draw_task_id,
         entity_id,
         book_code,
         year_month,
         expr_type,
         expr_desc,
         task_name,
         task_params,
         taks_status,
         log_msg,
         oper_id,
         oper_time,
         task_code)
        VALUES (rpt_seq_log_rpt_item_draw_task.nextval,
                p_entity_id,
                p_book_code,
                p_year_month,
                NULL,
                NULL,
                'rpt_pack_report_item_proc_draw_data',
                NULL,
                '1',
                '报表列报项提取数据成功',
                p_oper_id,
                SYSDATE,
                p_task_code);

        --提交事务
        COMMIT;

    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;

            v_log_msg := substr('报表列报项提取数据发生异常，请检查！：' || v_error_msg ||
                                '；**SQLERRM: ' || SQLERRM || '**Error line: ' ||
                                dbms_utility.format_error_backtrace() || '',
                                1,
                                4000);

            -- 写入取数日志
            INSERT INTO rpt_log_report_item_draw_task
            (draw_task_id,
             entity_id,
             book_code,
             year_month,
             expr_type,
             expr_desc,
             task_name,
             task_params,
             taks_status,
             log_msg,
             oper_id,
             oper_time,
             TASK_CODE)
            VALUES (rpt_seq_log_rpt_item_draw_task.nextval,
                    p_entity_id,
                    p_book_code,
                    p_year_month,
                    NULL,
                    NULL,
                    'rpt_pack_report_item_proc_draw_data',
                    NULL,
                    '2',
                    v_log_msg,
                    p_oper_id,
                    SYSDATE,
                    p_task_code);

            --提交事务
            COMMIT;

            --往外层抛出异常信息
            raise_application_error(-20004, v_log_msg);
    END proc_draw_data;

    PROCEDURE proc_draw_financials_data(p_entity_id  NUMBER,
                                        p_book_code  VARCHAR2,
                                        p_year_month VARCHAR2,
                                        p_expr_type  VARCHAR2,
                                        p_oper_id    NUMBER,
                                        p_task_code  VARCHAR2) IS
        /***********************************************************************
    NAME : rpt_pack_report_item_proc_draw_financials_data
    PARAM :
    DESCRIPTION : 列报项财务科目数据提数[过程数据+结果数据]
    DATE :2021-10-18
    AUTHOR :YINXH

    -------
    MODIFY LOG

      UPDATE DATE :
      UPDATE BY   :
      UPDATE DESC :
    ***********************************************************************/
        v_task_count  number(11);
        V_TASK_TYPE   number(11);
        cur_item_rule rpt_conf_report_item_rule%rowtype;
        v_item_row    sys_refcursor;
        v_sql         varchar2(4000);
    BEGIN
        -- 查出任务信息
        select count(1)
        into v_task_count
        from RPT_BUSS_REPORT_TASK
        where TASK_CODE = p_task_code;
        if v_task_count != 0 then
            select TASK_TYPE
            into V_TASK_TYPE
            from RPT_BUSS_REPORT_TASK
            where TASK_CODE = p_task_code;
        end if;

        --【提取过程数据】
        if v_task_count != 0 and v_TASK_TYPE = 2 then
            -- 条件提数
            v_sql := 'SELECT t.*
                              FROM rpt_conf_report_item_rule t
                              WHERE t.entity_id = ' ||
                     p_entity_id || '
                                AND t.book_code = ''' ||
                     p_book_code || '''
                                AND t.expr_type = ''' ||
                     p_expr_type || '''
                                AND t.deal_type = ''1'' --取数方式：1-提数统计
                                AND t.data_source = ''1'' and exists  (select 1 from RPT_BUSS_REPORT_TASK_ITEM ti ' ||
                     'where ti.ITEM_ID = t.report_item_id and  ti.TASK_CODE = ''' ||
                     p_task_code || ''')';

        else
            -- 全量提数
            v_sql := 'SELECT t.*
                              FROM rpt_conf_report_item_rule t
                              WHERE t.entity_id = ' ||
                     p_entity_id || '
                                AND t.book_code = ''' ||
                     p_book_code || '''
                                AND t.expr_type = ''' ||
                     p_expr_type || '''
                                AND t.deal_type = ''1'' --取数方式：1-提数统计
                                AND t.data_source = ''1''';
        end if;

        OPEN v_item_row FOR v_sql;

        LOOP
            fetch v_item_row
                into cur_item_rule;
            exit when v_item_row%notfound;

            rpt_pack_report_item.proc_draw_fin_process_data(cur_item_rule.entity_id,
                                                            cur_item_rule.book_code,
                                                            p_year_month,
                                                            cur_item_rule.report_item_id,
                                                            p_oper_id,
                                                            p_task_code);

        END LOOP;

        --【提取结果数据】
        rpt_pack_report_item.proc_draw_fin_result_data(p_entity_id,
                                                       p_book_code,
                                                       p_year_month,
                                                       p_expr_type,
                                                       p_oper_id,
                                                       p_task_code);

    END proc_draw_financials_data;

    PROCEDURE proc_draw_fin_process_data(p_entity_id      NUMBER,
                                         p_book_code      VARCHAR2,
                                         p_year_month     VARCHAR2,
                                         p_report_item_id NUMBER,
                                         p_oper_id        NUMBER,
                                         p_task_code      VARCHAR2) AS
        /***********************************************************************
    NAME : proc_draw_fin_process_data
    PARAM :
    DESCRIPTION : 列报项财务科目过程数据提数(科目|专项)
    DATE :2021-10-18
    AUTHOR :YINXH

    -------
    MODIFY LOG

      UPDATE DATE 2023-07-07:
      UPDATE BY   YINXH:
      UPDATE DESC 增加专项数据提取:
    ***********************************************************************/

        v_task_count                 number(11);
        V_TASK_TYPE                  number(11);
        v_log_msg                    VARCHAR2(4000); --日志信息
        v_error_msg                  VARCHAR2(2000); --异常信息

        v_report_item_code           VARCHAR2(200); --列报项编码
        v_expr_type                  VARCHAR2(2); --取数规则：1-期末余额、2-本年增减变动、3-发生额
        v_expr_type_name             VARCHAR2(250); --取数规则名称：1-期末余额、2-本年增减变动、3-发生额
        v_period_type                VARCHAR2(2); --取数期间：1-当期-当月、2-上期-上月、3-当期-当年、4-上期-上年
        v_expr_desc                  VARCHAR2(4000); --表达式(科目)
        v_need_dimension_is          VARCHAR2(2); --是否需要分维度计算

        v_count_rule_is_num          NUMBER(11, 0); --表达式是否全为数字项

        v_year_month                 VARCHAR2(10); --实际取值会计期间

        v_currency                   VARCHAR2(3); --当前账套下的币别

        v_item_amount                NUMBER(32, 8); --科目金额
        v_rule_expr_analytical       VARCHAR2(4000); --解析后的规则表达式
        v_rule_expr_amount           NUMBER(32, 8); --规则表达式解析后的计算结果

        v_item_amount_cu             NUMBER(32, 8); --科目金额[本位币]
        v_rule_expr_analytical_cu    VARCHAR2(4000); --解析后的规则表达式[本位币]
        v_rule_expr_amount_cu        NUMBER(32, 8); --规则表达式解析后的计算结果[本位币]

        v_new_serial_no              NUMBER(6, 0); --新版本号
        v_new_report_data_id         NUMBER(11, 0); --新列项提数过程表ID

        v_acc_serial_no              NUMBER(6, 0); --会计引擎财务数据版本号

        v_report_item_rule_serial_no NUMBER(6, 0); --列报项规则版本号

        v_report_item_rule_id        NUMBER(6, 0); --列报项规则id

        v_article_rule_count         NUMBER         := 0; --列项规则专项配置计数

        v_sql                        VARCHAR2(4000) := ''; --科目取数完整sql
        v_select                     VARCHAR2(2000) := ''; --查询内容
        v_table                      VARCHAR2(2000) := ''; --查询表名
        v_base_condition             VARCHAR2(2000) := ''; --基础条件
        v_article_condition          VARCHAR2(2000) := ''; --专项条件
        v_task_condition             VARCHAR2(2000) := ''; --条件取数的条件

        v_acc_serial_no_sql          VARCHAR2(4000) := ''; --科目取数版本号完整sql
        v_acc_serial_no_select       VARCHAR2(2000) := ''; --版本号查询内容
        v_acc_serial_no_table        VARCHAR2(2000) := ''; --版本号查询表名
        v_acc_serial_no_condition    VARCHAR2(2000) := ''; --版本号查询条件
        v_dimension_article_filed    VARCHAR2(2000) := ''; -- 维度的专项字段
        v_dimension_filed            VARCHAR2(2000) := ''; -- 维度专项对应的维度字段
        v_dimension_insert_sub_sql   VARCHAR2(4000) := ''; -- 插入维度过程明细表的sql
        v_dimension_mapping_filed    VARCHAR2(2000) := ''; -- 维度专项字段和维度字段映射
        v_dimension_select           VARCHAR2(2000) := ''; -- 维度数据需要的源数据表字段

    BEGIN

        select count(1)
        into v_task_count
        from RPT_BUSS_REPORT_TASK
        where TASK_CODE = p_task_code;
        if v_task_count != 0 then
            select TASK_TYPE
            into V_TASK_TYPE
            from RPT_BUSS_REPORT_TASK
            where TASK_CODE = p_task_code;
        end if;

        -- 获取相关变量【上层调用函数已过滤非有效且审核通过状态的数据，此处不再加这两个过滤条件】
        SELECT r1.report_item_code,
               t.expr_type,
               (SELECT rc.code_c_name
                FROM rpt_v_conf_code rc
                WHERE rc.code_code_idx = 'ExprType/' || t.expr_type) AS expr_type_name,
               t.period_type,
               t.expr_desc,
               t.serial_no,
               t.report_item_rule_id,
               t.need_dimension_is
        INTO v_report_item_code,
            v_expr_type,
            v_expr_type_name,
            v_period_type,
            v_expr_desc,
            v_report_item_rule_serial_no,
            v_report_item_rule_id,
            v_need_dimension_is
        FROM rpt_conf_report_item_rule t
                 LEFT JOIN rpt_conf_report_item r1
                           ON r1.report_item_id = t.report_item_id
        WHERE t.report_item_id = p_report_item_id --列报项ID
          AND t.entity_id = p_entity_id           --业务单位id
          AND t.book_code = p_book_code;
        --账套编码

        -- 构建维度数据的聚合条件
        select listagg('rb.' || dimension_mapping_field, ','),
               listagg(dimension_field, ','),
               listagg('rb.' || dimension_mapping_field || ' ' ||
                       dimension_field,
                       ',')
        into v_dimension_article_filed,
            v_dimension_filed,
            v_dimension_mapping_filed
        from rpt_conf_dimension;

        -- 判断表达式是否为全数字项
        SELECT COUNT(1)
        INTO v_count_rule_is_num
        FROM dual
        WHERE regexp_like(regexp_replace(v_expr_desc, '\(|\)|\*|\-|\+|\/'),
                          '(^[+-]?\d{0,}\.?\d{0,}$)');
        -- 如果全为数字项，则直接计算
        IF v_count_rule_is_num > 0 THEN

            BEGIN
                --拼接规则表达式解析后的计算SQL脚本，返回规则表达式解析后的计算结果
                EXECUTE IMMEDIATE 'select (' || v_expr_desc || ') from dual'
                    INTO v_rule_expr_amount;

            EXCEPTION
                WHEN OTHERS THEN
                    v_error_msg := '列项规则解析执行发生错误：' || v_expr_desc || ', ' || SQLERRM;
                    -- 往外层抛自定义异常
                    raise_application_error(-20002, v_error_msg);

            END;

            -- 未取到科目值，补0
            IF v_rule_expr_amount IS NULL THEN
                v_rule_expr_amount := 0.00;
            END IF;

            -- 先获取当前账套下的币别
            SELECT r1.currency_code
            INTO v_currency
            FROM accuser.acc_conf_accountperiod r1
            WHERE r1.entity_id = p_entity_id
              AND r1.book_code = p_book_code;

            -- 再获取当前业务单位、账套编码、会计期间对应列项提数表的版本新版本号
            SELECT coalesce((MAX(serial_no) + 1), 1)
            INTO v_new_serial_no
            FROM rpt_duct_report_item_data
            WHERE entity_id = p_entity_id
              AND book_code = p_book_code
              AND year_month = p_year_month
              and task_code = p_task_code
              AND currency_code = v_currency
              AND report_item_id = p_report_item_id;

            -- 接着清除列项提数表当前条件对应的数据
            DELETE
            FROM rpt_duct_report_item_data
            WHERE entity_id = p_entity_id
              AND book_code = p_book_code
              AND year_month = p_year_month
              and task_code = p_task_code
              AND currency_code = v_currency
              AND report_item_id = p_report_item_id;

            -- 获取列项提数过程表最新主键ID
            v_new_report_data_id := rpt_seq_duct_rpt_item_data.nextval;

            -- 插入列项取数过程表
            INSERT INTO rpt_duct_report_item_data
            (report_item_data_id,
             serial_no,
             entity_id,
             book_code,
             year_month,
             report_item_id,
             expr_type,
             article,
             expr_desc,
             expr_desc_analytic,
             currency_code,
             currency_cu_code,
             amount,
             amount_cu,
             acc_serial_no,
             creator_id,
             create_time,
             report_item_rule_serial_no,
             TASK_CODE)
            VALUES (v_new_report_data_id,
                    v_new_serial_no,
                    p_entity_id,
                    p_book_code,
                    p_year_month,
                    p_report_item_id,
                    v_expr_type,
                    NULL, --专项
                    v_expr_desc,
                    NULL, --纯数字项无解析表达式
                    v_currency,
                    v_currency,
                    v_rule_expr_amount,
                    v_rule_expr_amount,
                    NULL,
                    p_oper_id,
                    SYSDATE,
                    v_report_item_rule_serial_no,
                    p_task_code);

            -- 记录列项规则提数轨迹
            INSERT INTO rpt_duct_report_item_datahis
            (report_item_data_his_id,
             report_item_data_id,
             serial_no,
             entity_id,
             book_code,
             year_month,
             report_item_id,
             expr_type,
             article,
             expr_desc,
             expr_desc_analytic,
             currency_code,
             currency_cu_code,
             amount,
             amount_cu,
             acc_serial_no,
             creator_id,
             create_time,
             report_item_rule_serial_no,
             TASK_CODE)
            SELECT rpt_seq_duct_rpt_item_datahis.nextval,
                   rd.report_item_data_id,
                   rd.serial_no,
                   rd.entity_id,
                   rd.book_code,
                   rd.year_month,
                   rd.report_item_id,
                   rd.expr_type,
                   rd.article,
                   rd.expr_desc,
                   rd.expr_desc_analytic,
                   rd.currency_code,
                   rd.currency_cu_code,
                   rd.amount,
                   rd.amount_cu,
                   rd.acc_serial_no,
                   rd.creator_id,
                   rd.create_time,
                   rd.report_item_rule_serial_no,
                   rd.TASK_CODE
            FROM rpt_duct_report_item_data rd
            WHERE rd.report_item_data_id = v_new_report_data_id;

            -- 如果需要分维度计算，分解为维度数据，插入维度数据过程表
            if v_need_dimension_is = '1' and
               (V_TASK_TYPE is null or V_TASK_TYPE = 1) then
                -- 删除原数据
                delete
                from rpt_duct_dimension_data
                where entity_id = p_entity_id
                  AND book_code = p_book_code
                  AND year_month = p_year_month
                  and item_rule_id = v_report_item_rule_id
                  AND currency_code = v_currency;

                -- 插入维度数据过程表
                insert into rpt_duct_dimension_data
                (dimension_duct_data_id,
                 item_id,
                 item_rule_id,
                 BOOK_CODE,
                 YEAR_MONTH,
                 ENTITY_ID,
                 SERIAL_NO,
                 value,
                 value_cu,
                 CURRENCY_CODE,
                 CURRENCY_CU_CODE,
                 REPORT_ITEM_RULE_SERIAL_NO,
                 dimension1,
                 dimension2,
                 dimension3,
                 dimension4,
                 dimension5,
                 dimension6,
                 dimension7,
                 dimension8,
                 dimension9,
                 dimension10,
                 dimension11,
                 dimension12,
                 dimension13,
                 dimension14,
                 dimension15,
                 dimension16,
                 dimension17,
                 create_time,
                 creator_id)
                values (rpt_seq_duct_dimension_data.nextval,
                        p_report_item_id,
                        v_report_item_rule_id,
                        p_book_code,
                        p_year_month,
                        p_entity_id,
                        v_new_serial_no,
                        v_rule_expr_amount,
                        v_rule_expr_amount,
                        v_currency,
                        v_currency,
                        v_report_item_rule_serial_no,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        sysdate,
                        p_oper_id);
            end if;

            --提交事务
            COMMIT;

        ELSE
            --非全数字项处理
            -- 处理条件取数的条件
            if V_TASK_TYPE is not null and V_TASK_TYPE = 2 then
                for cur_condition in (select *
                                      from RPT_BUSS_REPORT_TASK_CONDITION
                                      where TASK_CODE = p_task_code)
                    loop
                        v_task_condition := v_task_condition || ' and rb.' ||
                                            cur_condition.SQL_FILED || ' = ''' ||
                                            cur_condition.CONDITION_VALUE || '''';

                    end loop;
            end if;
            --dbms_output.put_line(v_task_condition);
            --获取表达式所有科目项的原币别，然后按币别循环以下处理过程，取得每个币别下的列报项及科目项金额
            --判定使用本位币变量，不能使用原币，原币字段在做手工账的时候可能为0
            FOR cur_currency IN (SELECT DISTINCT r1.currency_code,
                                                 r1.currency_cu_code
                                 FROM rpt_dap_ledger_balance r1
                                          LEFT JOIN bpluser.bbs_account bp1
                                                    ON bp1.account_id = r1.account_id
                                                        AND bp1.entity_id = r1.entity_id
                                                        AND bp1.book_code = r1.book_code
                                 WHERE r1.entity_id = p_entity_id   --业务单位id
                                   AND r1.book_code = p_book_code   --账套编码
                                   AND r1.year_month = p_year_month --会计期间
                                   AND bp1.ACCOUNT_CODE_IDX IN
                                       (SELECT DISTINCT regexp_replace(regexp_substr(v_expr_desc,
                                                                                     '\[(.+?)\]',
                                                                                     1,
                                                                                     LEVEL),
                                                                       '\[|\]',
                                                                       '')
                                        FROM dual
                                        CONNECT BY regexp_substr(v_expr_desc,
                                                                 '\[(.+?)\]',
                                                                 1,
                                                                 LEVEL) IS NOT NULL))
                LOOP
                    -- 遍历币别初始表达式解析变量
                    v_rule_expr_analytical := v_expr_desc;
                    v_rule_expr_analytical_cu := v_expr_desc;

                    -- 先获取当前业务单位、账套编码、会计期间对应列项提数表的版本新版本号
                    SELECT coalesce((MAX(serial_no) + 1), 1)
                    INTO v_new_serial_no
                    FROM rpt_duct_report_item_data
                    WHERE entity_id = p_entity_id
                      AND book_code = p_book_code
                      AND year_month = p_year_month
                      and task_code = p_task_code
                      AND currency_code = cur_currency.currency_code
                      AND report_item_id = p_report_item_id;

                    -- 再清除列项提数表当前条件对应的数据
                    DELETE
                    FROM rpt_duct_report_item_data
                    WHERE entity_id = p_entity_id
                      AND book_code = p_book_code
                      AND year_month = p_year_month
                      AND currency_code = cur_currency.currency_code
                      AND report_item_id = p_report_item_id
                      and TASK_CODE = p_task_code;

                    -- 先删除维度明细表原数据
                    delete
                    from rpt_duct_dimension_data_sub
                    where entity_id = p_entity_id
                      AND book_code = p_book_code
                      AND year_month = p_year_month
                      and item_rule_id = v_report_item_rule_id
                      AND currency_code = cur_currency.currency_code;

                    -- 获取列项提数过程表最新主键ID
                    v_new_report_data_id := rpt_seq_duct_rpt_item_data.nextval;

                    -- 插入列项取数过程表[需要先插入数据，否则外键关联的明细表无法插入数据]
                    INSERT INTO rpt_duct_report_item_data
                    (report_item_data_id,
                     serial_no,
                     entity_id,
                     book_code,
                     year_month,
                     report_item_id,
                     expr_type,
                     article,
                     expr_desc,
                     expr_desc_analytic,
                     currency_code,
                     currency_cu_code,
                     amount,
                     amount_cu,
                     acc_serial_no,
                     creator_id,
                     create_time,
                     report_item_rule_serial_no,
                     TASK_CODE)
                    VALUES (v_new_report_data_id,
                            v_new_serial_no,
                            p_entity_id,
                            p_book_code,
                            p_year_month,
                            p_report_item_id,
                            v_expr_type,
                            NULL, --专项
                            v_expr_desc,
                            NULL, --解析表达式
                            cur_currency.currency_code,
                            cur_currency.currency_cu_code,
                            NULL,
                            NULL,
                            NULL, --财务数据版本号
                            p_oper_id,
                            SYSDATE,
                            v_report_item_rule_serial_no,
                            p_task_code);


                    /**
                    *   【当期-当月，上期-上月，当年，上年】期末余额都是累计值，本年增减变动时累计的年度借贷发生额差：
                    *  取数规则确定【20230807】：
                    *
                    *      选项1：期初余额【取数期间为当期时取当月的期初余额；取数期间为上期时取上月的期初余额】
                    *           期初余额【取数期间为当年时取当年年初的期初余额；取数期间为上年时取上年年初的期初余额】
                    *
                    *      选项2：期末余额【取数期间为当期时取当月的期末余额；取数期间为上期时取上月的期末余额】
                    *           期末余额【取数期间为当年时取当年的期末余额；取数期间为上年时取上年年末的期末余额】
                    *
                    *      选项3：发生额【取数期间为当期时取当月的借贷发生额差；注：发生额仅可选取数期间为当期】
                    *
                    *      选项4：本年增减变动【取数期间为当年时取当年累计的年度借贷发生额差；取数期间为上年时取上年累计的年度借贷发生额差】
                    *
                    *
                    */

                    /**
                    *   科目、专项取数原则：
                    *    1、如果列报项规则配置了专项，则提数解析规则时，只从专项余额表(专项表只存末级科目的数据，通过科目配置表的last_state=1进行标识)取数，
                    *    2、如果列报项规则没有配置专项，则提数解析规则时，只从科目余额表(有末级科目和非末级科目)取数
                    */

                    --检查是否规则配置有专项(专项)
                    SELECT COUNT(1)
                    INTO v_article_rule_count
                    FROM rptuser.rpt_conf_item_rule_article t
                    WHERE t.report_item_rule_id = v_report_item_rule_id;

                    --科目|专项特殊判断处理
                    IF v_article_rule_count > 0 THEN
                        --取专项余额表
                        v_acc_serial_no_table := ' rpt_dap_article_balance ';

                        --专项提取条件1(取末级科目，标识=1)
                        -- 优化去除末级科目标识
                        -- v_article_condition := ' AND ba.final_level_is = ''' || 1 || '''';
                        v_article_condition := ' ';

                        --获取规则专项配置(多个专项处理)
                        FOR article_rule IN (SELECT t.article_type, t.article_value
                                             FROM rptuser.rpt_conf_item_rule_article t
                                             WHERE t.report_item_rule_id =
                                                   v_report_item_rule_id)
                            LOOP
                            --专项提取条件2(多个专项循环处理)
                            --如果配置了专项，但配置值为空，取值时应该匹配专项对应的字段值不为空的的数据，如果不为空，则根据计量因子专项值匹配获取
                                IF article_rule.article_value IS NULL THEN
                                    v_article_condition := v_article_condition || ' and rb.' ||
                                                           article_rule.article_type ||
                                                           ' is not null ';
                                ELSIF article_rule.article_value IS NOT NULL THEN
                                    v_article_condition := v_article_condition || ' and rb.' ||
                                                           article_rule.article_type ||
                                                           ' in (''' ||
                                                           REPLACE(article_rule.article_value,
                                                                   ',',
                                                                   ('''' || ',' || '''')) ||
                                                           ''')';
                                END IF;

                            END LOOP;

                    ELSE
                        --取科目余额表
                        v_acc_serial_no_table := ' rpt_dap_ledger_balance ';

                        --专项提取条件为空
                        v_article_condition := '';

                    END IF;

                    -- 条件取数的时候，表名指定为科目余额表
                    if V_TASK_TYPE is not null and V_TASK_TYPE = 2 then
                        v_acc_serial_no_table := ' rpt_dap_article_balance ';
                    end if;

                    --指定获取科目数据版本号查询内容
                    v_acc_serial_no_select := 'SELECT NVL(MAX(serial_no), 0) ';

                    --指定提取科目、专项余额数据表名
                    v_table := v_acc_serial_no_table || ' rb ';

                    -- 期初余额取数
                    IF v_expr_type = '1' THEN
                        -- 按取数区间划分
                        IF v_period_type = '1' THEN
                            -- 取当期-当月：取当月的期初余额
                            v_year_month := p_year_month;
                        ELSIF v_period_type = '2' THEN
                            -- 取上期-上月: 取上月的期初余额
                            SELECT to_char(add_months(to_date(p_year_month, 'YYYYMM'), -1),
                                           'YYYYMM')
                            INTO v_year_month
                            FROM dual;
                        ELSIF v_period_type = '3' THEN
                            -- 取当年：取当年年初的期初余额
                            SELECT to_char(to_date(p_year_month, 'YYYYMM'), 'YYYY') || '01'
                            INTO v_year_month
                            FROM dual;
                        ELSE
                            -- 取上年: 取上年年初的期初余额
                            SELECT to_char(add_months(to_date(p_year_month, 'YYYYMM'),
                                                      -12),
                                           'YYYY') || '01'
                            INTO v_year_month
                            FROM dual;
                        END IF;

                        -- 获取财务科目数据版本号(批量数据，同一批版本号相同)(v_year_month为变动条件，不能前置处理)
                        v_acc_serial_no_condition := 'entity_id = ' || p_entity_id || '' ||
                                                     ' AND book_code = ''' ||
                                                     p_book_code || '''' ||
                                                     ' AND year_month = ''' ||
                                                     v_year_month || '''';

                        -- 合并完整sql
                        v_acc_serial_no_sql := v_acc_serial_no_select || ' from ' ||
                                               v_acc_serial_no_table || ' WHERE ' ||
                                               v_acc_serial_no_condition;

                        -- 执行sql
                        EXECUTE IMMEDIATE v_acc_serial_no_sql
                            INTO v_acc_serial_no;

                        -- 提取期初余额数据
                        v_select := 'SELECT SUM(rb.opening_balance), SUM(rb.opening_balance_cu) ';
                        v_dimension_select :=
                                ' SUM(rb.opening_balance) value, SUM(rb.opening_balance_cu) value_cu ';

                        --基础条件(v_year_month为变动条件，不能前置处理)
                        v_base_condition := ' rb.account_id =  [account_id]' ||
                                            ' AND rb.entity_id = ' || p_entity_id || '' ||
                                            ' AND rb.book_code = ''' || p_book_code || '''' ||
                                            ' AND rb.year_month = ''' || v_year_month || '''' ||
                                            ' AND rb.currency_code = ''' ||
                                            cur_currency.currency_code || '''';

                        --合并完整sql
                        v_sql := v_select || ' from ' || v_table || ' WHERE ' ||
                                 v_base_condition || v_article_condition ||
                                 v_task_condition;
                        --执行sql
                        --                         EXECUTE IMMEDIATE v_sql
                        --                             INTO v_item_amount, v_item_amount_cu;

                        -- 期末余额取数
                    ELSIF v_expr_type = '2' THEN
                        -- 按取数区间划分
                        IF v_period_type = '1' THEN
                            -- 取当期-当月：取当月的期末余额
                            v_year_month := p_year_month;
                        ELSIF v_period_type = '2' THEN
                            -- 取上期-上月: 取上月的期末余额
                            SELECT to_char(add_months(to_date(p_year_month, 'YYYYMM'), -1),
                                           'YYYYMM')
                            INTO v_year_month
                            FROM dual;
                        ELSIF v_period_type = '3' THEN
                            -- 取当年：取当年的期末余额(与当期取值一致)
                            v_year_month := p_year_month;
                        ELSE
                            -- 取上年: 取上年年末的期末余额
                            SELECT to_char(add_months(to_date(p_year_month, 'YYYYMM'),
                                                      -12),
                                           'YYYY') || '12'
                            INTO v_year_month
                            FROM dual;
                        END IF;

                        -- 获取财务科目数据版本号(批量数据，同一批版本号相同)(v_year_month为变动条件，不能前置处理)
                        v_acc_serial_no_condition := 'entity_id = ' || p_entity_id || '' ||
                                                     ' AND book_code = ''' ||
                                                     p_book_code || '''' ||
                                                     ' AND year_month = ''' ||
                                                     v_year_month || '''';

                        -- 合并完整sql
                        v_acc_serial_no_sql := v_acc_serial_no_select || ' from ' ||
                                               v_acc_serial_no_table || ' WHERE ' ||
                                               v_acc_serial_no_condition;

                        -- 执行sql
                        EXECUTE IMMEDIATE v_acc_serial_no_sql
                            INTO v_acc_serial_no;

                        -- 提取期末余额
                        v_select := 'SELECT SUM(rb.closing_balance), SUM(rb.closing_balance_cu) ';
                        v_dimension_select :=
                                ' SUM(rb.closing_balance) value, SUM(rb.closing_balance_cu) value_cu ';

                        --基础条件(v_year_month为变动条件，不能前置处理)
                        v_base_condition := ' rb.account_id =  [account_id]' ||
                                            ' AND rb.entity_id = ' || p_entity_id || '' ||
                                            ' AND rb.book_code = ''' || p_book_code || '''' ||
                                            ' AND rb.year_month = ''' || v_year_month || '''' ||
                                            ' AND rb.currency_code = ''' ||
                                            cur_currency.currency_code || '''';

                        --合并完整sql
                        v_sql := v_select || ' from ' || v_table || ' WHERE ' ||
                                 v_base_condition || v_article_condition ||
                                 v_task_condition;

                        --执行sql
                        --                         EXECUTE IMMEDIATE v_sql
                        --                             INTO v_item_amount, v_item_amount_cu;

                        -- 发生额取数
                    ELSIF v_expr_type = '3' THEN
                        -- 取当期：按月份取当月的借贷发生额差【发生额仅可选取数期间为当期】
                        IF v_period_type = '1' THEN
                            v_year_month := p_year_month;
                        END IF;

                        -- 获取财务科目数据版本号(批量数据，同一批版本号相同)(v_year_month为变动条件，不能前置处理)
                        v_acc_serial_no_condition := 'entity_id = ' || p_entity_id || '' ||
                                                     ' AND book_code = ''' ||
                                                     p_book_code || '''' ||
                                                     ' AND year_month = ''' ||
                                                     v_year_month || '''';

                        -- 合并完整sql
                        v_acc_serial_no_sql := v_acc_serial_no_select || ' from ' ||
                                               v_acc_serial_no_table || ' WHERE ' ||
                                               v_acc_serial_no_condition;

                        -- 执行sql
                        EXECUTE IMMEDIATE v_acc_serial_no_sql
                            INTO v_acc_serial_no;

                        -- 提取上月期末余额
                        v_select :=
                                'SELECT SUM(rb.debit_amount - rb.credit_amount), SUM(rb.debit_amount_cu - rb.credit_amount_cu) ';
                        v_dimension_select :=
                                ' SUM(rb.debit_amount - rb.credit_amount) value, SUM(rb.debit_amount_cu - rb.credit_amount_cu) value_cu ';

                        --基础条件(v_year_month为变动条件，不能前置处理)
                        v_base_condition := ' rb.account_id =  [account_id]' ||
                                            ' AND rb.entity_id = ' || p_entity_id || '' ||
                                            ' AND rb.book_code = ''' || p_book_code || '''' ||
                                            ' AND rb.year_month = ''' || v_year_month || '''' ||
                                            ' AND rb.currency_code = ''' ||
                                            cur_currency.currency_code || '''';

                        --合并完整sql
                        v_sql := v_select || ' from ' || v_table || ' WHERE ' ||
                                 v_base_condition || v_article_condition ||
                                 v_task_condition;

                        --执行sql
                        --                         EXECUTE IMMEDIATE v_sql
                        --                             INTO v_item_amount, v_item_amount_cu;

                        -- 本年增减变动取数
                    ELSIF v_expr_type = '4' THEN
                        -- 取当年：取当年累计的年度借贷发生额差
                        IF v_period_type = '3' THEN
                            v_year_month := p_year_month;
                        ELSIF v_period_type = '4' THEN
                            -- 取上年：取上年累计的年度借贷发生额差
                            SELECT to_char(add_months(to_date(p_year_month, 'YYYYMM'),
                                                      -12),
                                           'YYYYMM')
                            INTO v_year_month
                            FROM dual;
                        END IF;

                        -- 获取财务科目数据版本号(批量数据，同一批版本号相同)(v_year_month为变动条件，不能前置处理)
                        v_acc_serial_no_condition := 'entity_id = ' || p_entity_id || '' ||
                                                     ' AND book_code = ''' ||
                                                     p_book_code || '''' ||
                                                     ' AND year_month = ''' ||
                                                     v_year_month || '''';

                        -- 合并完整sql
                        v_acc_serial_no_sql := v_acc_serial_no_select || ' from ' ||
                                               v_acc_serial_no_table || ' WHERE ' ||
                                               v_acc_serial_no_condition;

                        -- 执行sql
                        EXECUTE IMMEDIATE v_acc_serial_no_sql
                            INTO v_acc_serial_no;

                        -- 提取上月期末余额
                        v_select :=
                                'SELECT SUM(rb.debit_amount_year - rb.credit_amount_year), SUM(rb.debit_amount_year_cu - rb.credit_amount_year_cu) ';
                        v_dimension_select :=
                                ' SUM(rb.debit_amount_year - rb.credit_amount_year) value, SUM(rb.debit_amount_year_cu - rb.credit_amount_year_cu) value_cu ';

                        --基础条件(v_year_month为变动条件，不能前置处理)
                        v_base_condition := ' rb.account_id =  [account_id]' ||
                                            ' AND rb.entity_id = ' || p_entity_id || '' ||
                                            ' AND rb.book_code = ''' || p_book_code || '''' ||
                                            ' AND rb.year_month = ''' || v_year_month || '''' ||
                                            ' AND rb.currency_code = ''' ||
                                            cur_currency.currency_code || '''';

                        --合并完整sql
                        v_sql := v_select || ' from ' || v_table || ' WHERE ' ||
                                 v_base_condition || v_article_condition ||
                                 v_task_condition;

                        --执行sql
                        --                         EXECUTE IMMEDIATE v_sql
                        --                             INTO v_item_amount, v_item_amount_cu;

                    END IF;

                    -- 正则匹配获取[]包含项，排除数字项，并去重
                    FOR cur_item IN (select account_id,
                                            account_code_idx,
                                            final_level_is,
                                            '[' || account_code_idx || ']' as cal_item
                                     from bpluser.bbs_account a
                                     where a.account_code_idx in
                                           (SELECT DISTINCT regexp_replace(regexp_substr(v_expr_desc,
                                                                                         '\[(.+?)\]',
                                                                                         1,
                                                                                         LEVEL),
                                                                           '\[|\]',
                                                                           '') AS cal_item
                                            FROM dual b
                                            CONNECT BY regexp_replace(regexp_substr(v_expr_desc,
                                                                                    '\[(.+?)\]',
                                                                                    1,
                                                                                    LEVEL),
                                                                      '\[|\]',
                                                                      '') IS NOT NULL)
                                       and a.book_code = p_book_code
                                       and a.entity_id = p_entity_id)
                        LOOP
                            --dbms_output.put_line(v_expr_desc||':'||cur_item.account_id ||','||cur_item.account_code_idx);

                            dbms_output.put_line(v_sql);
                            v_sql := replace(v_sql, '[account_id]', cast(cur_item.ACCOUNT_ID as varchar2));
                            EXECUTE IMMEDIATE v_sql
                                INTO v_item_amount, v_item_amount_cu;

                            -- when execute end ,replace account id with [account_id] str
                            v_sql := replace(v_sql, 'rb.account_id =  ' || cur_item.ACCOUNT_ID,
                                             'rb.account_id =  [account_id]');

                            -- 未取到科目值，补0
                            IF v_item_amount_cu IS NULL THEN
                                v_item_amount := 0.00;
                                v_item_amount_cu := 0.00;
                            END IF;

                            -- [汇总币别]比较科目项的值，用正则(需转义处理)匹配替换表达式中科目项的编码
                            IF v_item_amount_cu > 0 THEN
                                -- 当科目项的值为正数时，直接替换，数字转字符串 cast(123 as varchar2)
                                v_rule_expr_analytical := REPLACE(v_rule_expr_analytical,
                                                                  cur_item.cal_item,
                                                                  CAST(v_item_amount AS VARCHAR2));

                                -- [本位币]当科目项的值为正数时，直接替换，数字转字符串 cast(123 as varchar2)
                                v_rule_expr_analytical_cu := REPLACE(v_rule_expr_analytical_cu,
                                                                     cur_item.cal_item,
                                                                     CAST(v_item_amount_cu AS
                                                                         VARCHAR2));

                            ELSIF v_item_amount_cu < 0 THEN
                                -- 当科目项的值为负数时，需要添加小括号
                                v_rule_expr_analytical := REPLACE(v_rule_expr_analytical,
                                                                  cur_item.cal_item,
                                                                  '(' ||
                                                                  CAST(v_item_amount AS VARCHAR2) || ')');

                                -- [本位币]当科目项的值为负数时，需要添加小括号
                                v_rule_expr_analytical_cu := REPLACE(v_rule_expr_analytical_cu,
                                                                     cur_item.cal_item,
                                                                     '(' ||
                                                                     CAST(v_item_amount_cu AS
                                                                         VARCHAR2) || ')');

                            ELSE

                                -- 若查询不到科目项的值，作为除数时，需要替换为1
                                IF instr(v_expr_desc, ('/' || cur_item.cal_item)) > 0 THEN
                                    -- [原币]
                                    v_rule_expr_analytical := REPLACE(v_rule_expr_analytical,
                                                                      ('/' || cur_item.cal_item),
                                                                      '/1');
                                    -- [本位币]
                                    v_rule_expr_analytical_cu := REPLACE(v_rule_expr_analytical_cu,
                                                                         ('/' || cur_item.cal_item),
                                                                         '/1');

                                ELSE
                                    --不作为除数时，直接替换为0
                                    v_rule_expr_analytical := REPLACE(v_rule_expr_analytical,
                                                                      cur_item.cal_item,
                                                                      '0');
                                    -- [本位币]
                                    v_rule_expr_analytical_cu := REPLACE(v_rule_expr_analytical_cu,
                                                                         cur_item.cal_item,
                                                                         '0');

                                END IF;
                            END IF;

                            -- 插入列报项取数过程明细表【必须保留旧版本数据】
                            INSERT INTO rpt_duct_report_item_data_sub
                            (report_item_data_details_id,
                             report_item_data_id,
                             expr_code, --取数项编码
                             expr_desc, --取数项表达式
                             currency_code,
                             currency_cu_code,
                             amount,
                             amount_cu,
                             creator_id,
                             create_time)
                            VALUES (rpt_seq_duct_rpt_item_data_dtl.nextval,
                                    v_new_report_data_id,
                                    cur_item.account_code_idx, --列报项编码
                                    cur_item.cal_item, --解析出来的列报项编码，带[]
                                    cur_currency.currency_code,
                                    cur_currency.currency_cu_code,
                                    v_item_amount,
                                    v_item_amount_cu,
                                    p_oper_id,
                                    SYSDATE);

                            -- 插入维度数据明细表
                            if v_need_dimension_is = '1' and
                               (V_TASK_TYPE is null or V_TASK_TYPE = 1) then
                                -- 需要维度数据的，统一从专项余额表获取数据

                                -- 构建插入明细表sql
                                v_dimension_insert_sub_sql := 'insert into rpt_duct_dimension_data_sub（
                                dimension_duct_data_sub_id,
                                item_id,
                                item_rule_id,
                                BOOK_CODE,
                                YEAR_MONTH,
                                ENTITY_ID,
                                SERIAL_NO,
                                value,
                                value_cu,
                                CURRENCY_CODE,
                                CURRENCY_CU_CODE,
                                expr_code,
                                REPORT_ITEM_RULE_SERIAL_NO,
                                ' ||
                                                              v_dimension_filed || ',
                                create_time,
                                creator_id
                                ）select rpt_seq_duct_dimension_data_sub.nextval,
                                ' ||
                                                              p_report_item_id || ',
                                ' ||
                                                              v_report_item_rule_id || ',
                                ''' ||
                                                              p_book_code || ''',
                                ''' ||
                                                              p_year_month || ''',
                                ' ||
                                                              p_entity_id || ',
                                ' ||
                                                              v_new_serial_no || ',
                                value,
                                value_cu,
                                CURRENCY_CODE,
                                CURRENCY_CU_CODE,
                                ''' ||
                                                              cur_item.account_code_idx || ''',
                                ' ||
                                                              v_report_item_rule_serial_no || ',
                                ' ||
                                                              v_dimension_filed || ',
                                sysdate,
                                ' || case
                                                                                                                            when p_oper_id is null
                                                                                                                                then
                                                                                                                                'null'
                                                                                                                            else
                                                                                                                                CAST(p_oper_id as VARCHAR2)
                                                                  end || '
                                from (select
                                rb.YEAR_MONTH  year_month,
                                ' ||
                                                              v_dimension_select || ',
                                rb.CURRENCY_CODE CURRENCY_CODE,
                                rb.CURRENCY_CU_CODE CURRENCY_CU_CODE,
                                ' ||
                                                              v_dimension_mapping_filed || '
                                from  rpt_dap_article_balance  rb  where ' ||
                                                              v_base_condition || v_article_condition || '
                                group by rb.year_month, rb.CURRENCY_CODE, rb.CURRENCY_CU_CODE,
                                ' ||
                                                              v_dimension_article_filed || ')t';
                                EXECUTE IMMEDIATE v_dimension_insert_sub_sql;

                            end if;

                        END LOOP;
                    --遍历科目项

                    -- 替换未处理的科目项为0
                    FOR cur_item IN (SELECT DISTINCT regexp_substr(v_rule_expr_analytical_cu,
                                                                   '\[(.+?)\]',
                                                                   1,
                                                                   LEVEL) AS cal_item
                                     FROM dual
                                     CONNECT BY regexp_substr(v_rule_expr_analytical_cu,
                                                              '\[(.+?)\]',
                                                              1,
                                                              LEVEL) IS NOT NULL)
                        LOOP
                            -- 若查询不到科目项的值，作为除数时，需要替换为1
                            IF instr(v_rule_expr_analytical_cu, ('/' || cur_item.cal_item)) > 0 THEN
                                -- [原币]
                                v_rule_expr_analytical := REPLACE(v_rule_expr_analytical,
                                                                  ('/' || cur_item.cal_item),
                                                                  '/1');
                                -- [本位币]
                                v_rule_expr_analytical_cu := REPLACE(v_rule_expr_analytical_cu,
                                                                     ('/' || cur_item.cal_item),
                                                                     '/1');

                            ELSE
                                --不作为除数时，直接替换为0
                                v_rule_expr_analytical := REPLACE(v_rule_expr_analytical,
                                                                  cur_item.cal_item,
                                                                  '0');
                                -- [本位币]
                                v_rule_expr_analytical_cu := REPLACE(v_rule_expr_analytical_cu,
                                                                     cur_item.cal_item,
                                                                     '0');

                            END IF;

                        end loop;

                    -- 计算表达式的值
                    IF v_rule_expr_analytical_cu IS NOT NULL THEN

                        BEGIN
                            --[原币]拼接规则表达式解析后的计算SQL脚本，返回规则表达式解析后的计算结果
                            EXECUTE IMMEDIATE 'select (' || v_rule_expr_analytical ||
                                              ') from dual'
                                INTO v_rule_expr_amount;
                            --[本位币]拼接规则表达式解析后的计算SQL脚本，返回规则表达式解析后的计算结果
                            EXECUTE IMMEDIATE 'select (' || v_rule_expr_analytical_cu ||
                                              ') from dual'
                                INTO v_rule_expr_amount_cu;

                        EXCEPTION
                            WHEN OTHERS THEN
                                v_error_msg := '列项规则解析执行发生错误：' || v_rule_expr_analytical ||
                                               ' | ' || v_rule_expr_analytical_cu || ', ' ||
                                               SQLERRM;
                                -- 往外层抛自定义异常
                                raise_application_error(-20002, v_error_msg);

                        END;

                        -- 更新列报项过程表数据【记录每个表达式中每个科目原币和本位币解析计算表达式】
                        UPDATE rpt_duct_report_item_data
                        SET expr_desc_analytic = v_rule_expr_analytical || ' | ' ||
                                                 v_rule_expr_analytical_cu,
                            amount             = v_rule_expr_amount,
                            amount_cu          = v_rule_expr_amount_cu,
                            acc_serial_no      = v_acc_serial_no
                        WHERE report_item_data_id = v_new_report_data_id;
                    END IF;

                    -- 记录列报项提数过程表轨迹
                    INSERT INTO rpt_duct_report_item_datahis
                    (report_item_data_his_id,
                     report_item_data_id,
                     serial_no,
                     entity_id,
                     book_code,
                     year_month,
                     report_item_id,
                     expr_type,
                     article,
                     expr_desc,
                     expr_desc_analytic,
                     currency_code,
                     currency_cu_code,
                     amount,
                     amount_cu,
                     acc_serial_no,
                     creator_id,
                     create_time,
                     report_item_rule_serial_no,
                     TASK_CODE)
                    SELECT rpt_seq_duct_rpt_item_datahis.nextval,
                           rd.report_item_data_id,
                           rd.serial_no,
                           rd.entity_id,
                           rd.book_code,
                           rd.year_month,
                           rd.report_item_id,
                           rd.expr_type,
                           rd.article,
                           rd.expr_desc,
                           rd.expr_desc_analytic,
                           rd.currency_code,
                           rd.currency_cu_code,
                           rd.amount,
                           rd.amount_cu,
                           rd.acc_serial_no,
                           rd.creator_id,
                           rd.create_time,
                           rd.report_item_rule_serial_no,
                           rd.TASK_CODE
                    FROM rpt_duct_report_item_data rd
                    WHERE rd.report_item_data_id = v_new_report_data_id;

                    --提交事务
                    COMMIT;
                END LOOP; -- 遍历币别

        END IF;

        -- 处理维度数据的币别，汇总等
        if v_need_dimension_is = '1' and
           (V_TASK_TYPE is null or V_TASK_TYPE = 1) then
            rpt_pack_report_item.proc_calculate_dimiension_data(p_entity_id,
                                                                p_book_code,
                                                                p_year_month,
                                                                p_report_item_id,
                                                                v_report_item_rule_id,
                                                                p_oper_id,
                                                                v_new_serial_no,
                                                                v_report_item_rule_serial_no);
        end if;

        --异常捕获处理
    EXCEPTION
        WHEN OTHERS THEN
            v_log_msg := substr('列报项[' || v_report_item_code || ']按' ||
                                v_expr_type_name || '提取财务科目过程数据异常：' ||
                                v_error_msg || '; ' || '**SQLERRM: ' || SQLERRM ||
                                '**Error line: ' ||
                                dbms_utility.format_error_backtrace() || '',
                                1,
                                4000);

            --调试打印异常信息
            dbms_output.put_line(v_log_msg);

            -- 写入取数日志
            INSERT INTO rpt_log_report_item_draw_task
            (draw_task_id,
             entity_id,
             book_code,
             year_month,
             expr_type,
             expr_desc,
             task_name,
             task_params,
             taks_status,
             log_msg,
             oper_id,
             oper_time,
             TASK_CODE)
            VALUES (rpt_seq_log_rpt_item_draw_task.nextval,
                    p_entity_id,
                    p_book_code,
                    p_year_month,
                    v_expr_type,
                    v_expr_desc,
                    'rpt_pack_report_item.func_draw_fin_process_data',
                    v_report_item_code,
                    '2',
                    v_log_msg,
                    p_oper_id,
                    SYSDATE,
                    p_task_code);

            --提交日志写入事务
            COMMIT;

            -- 往外层抛自定义异常
            raise_application_error(-20003, v_log_msg);

    END proc_draw_fin_process_data;

    PROCEDURE proc_draw_fin_result_data(p_entity_id  NUMBER,
                                        p_book_code  VARCHAR2,
                                        p_year_month VARCHAR2,
                                        p_expr_type  VARCHAR2,
                                        p_oper_id    NUMBER,
                                        p_task_code  VARCHAR2) AS
        /***********************************************************************
    NAME : rpt_pack_report_item_proc_draw_financials_result_data
    PARAM :
    DESCRIPTION : 列报项财务科目结果数据提数
    DATE :2021-10-18
    AUTHOR :YINXH

    -------
    MODIFY LOG

      UPDATE DATE :
      UPDATE BY   :
      UPDATE DESC :
    ***********************************************************************/

        v_log_msg   VARCHAR2(4000); --日志信息
        v_error_msg VARCHAR2(2000); --异常信息

    BEGIN

        -- 先清除列项提数表当前条件对应的数据
        DELETE
        FROM rpt_buss_report_item_data
        WHERE entity_id = p_entity_id
          AND book_code = p_book_code
          AND year_month = p_year_month
          and TASK_CODE = p_task_code
          AND report_item_id IN (SELECT t.report_item_id
                                 FROM rpt_conf_report_item_rule t
                                 WHERE t.entity_id = p_entity_id
                                   AND t.book_code = p_book_code
                                   AND t.expr_type = p_expr_type
                                   AND t.deal_type = '1'   --取数方式：1-提数统计
                                   AND t.data_source = '1' --数据来源：1-财务数据
                                   AND t.valid_is = '1'
                                   AND t.audit_state = '1');

        -- 再按本位币从过程表提取列报项数据到结果表
        INSERT INTO rpt_buss_report_item_data
        (report_item_data_id,
         serial_no,
         entity_id,
         book_code,
         year_month,
         report_item_id,
         expr_type,
         article,
         currency_code,
         amount,
         creator_id,
         create_time,
         report_item_rule_serial_no,
         TASK_CODE)
        SELECT rpt_seq_buss_rpt_item_data.nextval,
               td.serial_no,
               td.entity_id,
               td.book_code,
               td.year_month,
               td.report_item_id,
               td.expr_type,
               td.article,
               td.currency_cu_code,
               td.sum_amount_cu,
               p_oper_id,
               SYSDATE,
               td.report_item_rule_serial_no,
               td.TASK_CODE
        FROM (SELECT max(rd.serial_no) serial_no,
                     rd.entity_id,
                     rd.book_code,
                     rd.year_month,
                     rd.report_item_id,
                     rd.expr_type,
                     rd.article,
                     rd.currency_cu_code,
                     SUM(rd.amount_cu) sum_amount_cu,
                     rd.report_item_rule_serial_no,
                     rd.TASK_CODE
              FROM rpt_duct_report_item_data rd
              WHERE rd.entity_id = p_entity_id
                AND rd.book_code = p_book_code
                AND rd.year_month = p_year_month
                and rd.TASK_CODE = p_task_code
                AND rd.report_item_id IN
                    (SELECT t.report_item_id
                     FROM rpt_conf_report_item_rule t
                     WHERE t.entity_id = p_entity_id
                       AND t.book_code = p_book_code
                       AND t.expr_type = p_expr_type
                       AND t.deal_type = '1'   --取数方式：1-提数统计
                       AND t.data_source = '1' --数据来源：1-财务数据
                       AND t.valid_is = '1'
                       AND t.audit_state = '1')
              GROUP BY rd.currency_cu_code,
                       rd.entity_id,
                       rd.book_code,
                       rd.year_month,
                       rd.report_item_id,
                       rd.expr_type,
                       rd.article,
                       rd.report_item_rule_serial_no,
                       rd.TASK_CODE) td;
        --取本位币

        -- 接着记录列项规则提数结果表轨迹
        INSERT INTO rpt_buss_report_item_datahis
        (report_item_data_his_id,
         report_item_data_id,
         serial_no,
         entity_id,
         book_code,
         year_month,
         report_item_id,
         expr_type,
         article,
         currency_code,
         amount,
         creator_id,
         create_time,
         report_item_rule_serial_no,
         TASK_CODE)
        SELECT rpt_seq_buss_rpt_item_datahis.nextval,
               rd.report_item_data_id,
               rd.serial_no,
               rd.entity_id,
               rd.book_code,
               rd.year_month,
               rd.report_item_id,
               rd.expr_type,
               rd.article,
               rd.currency_code,
               rd.amount,
               rd.creator_id,
               rd.create_time,
               rd.report_item_rule_serial_no,
               rd.TASK_CODE
        FROM rpt_buss_report_item_data rd
        WHERE rd.entity_id = p_entity_id
          AND rd.book_code = p_book_code
          AND rd.year_month = p_year_month
          and rd.TASK_CODE = p_task_code
          AND rd.report_item_id IN
              (SELECT t.report_item_id
               FROM rpt_conf_report_item_rule t
               WHERE t.entity_id = p_entity_id
                 AND t.book_code = p_book_code
                 AND t.expr_type = p_expr_type
                 AND t.deal_type = '1'   --取数方式：1-提数统计
                 AND t.data_source = '1' --数据来源：1-财务数据
                 AND t.valid_is = '1'
                 AND t.audit_state = '1');

        --提交事务
        COMMIT;

        --异常捕获处理
    EXCEPTION
        WHEN OTHERS THEN
            v_error_msg := '列报项提取财务科目结果数据异常，取数规则为：' || p_expr_type || '; ';
            v_log_msg := substr(v_error_msg || '**SQLERRM: ' || SQLERRM ||
                                '**Error line: ' ||
                                dbms_utility.format_error_backtrace() || '',
                                1,
                                4000);

            -- 写入取数日志
            INSERT INTO rpt_log_report_item_draw_task
            (draw_task_id,
             entity_id,
             book_code,
             year_month,
             expr_type,
             expr_desc,
             task_name,
             task_params,
             taks_status,
             log_msg,
             oper_id,
             oper_time,
             TASK_CODE)
            VALUES (rpt_seq_log_rpt_item_draw_task.nextval,
                    p_entity_id,
                    p_book_code,
                    p_year_month,
                    p_expr_type,
                    NULL,
                    'rpt_pack_report_item_proc_draw_financials_result_data',
                    NULL,
                    '2',
                    v_log_msg,
                    p_oper_id,
                    SYSDATE,
                    p_task_code);

            --提交日志写入事务
            COMMIT;

            -- 往外层抛自定义异常
            raise_application_error(-20003, v_log_msg);

    END proc_draw_fin_result_data;

    PROCEDURE proc_summary(p_entity_id  NUMBER,
                           p_book_code  VARCHAR2,
                           p_year_month VARCHAR2,
                           p_deal_level NUMBER,
                           p_oper_id    NUMBER,
                           p_task_code  VARCHAR2) AS
        /***********************************************************************
    NAME : rpt_pack_report_item_proc_summary
    DESCRIPTION : 列报项取数[列项汇总]
    DATE :2021-10-11
    AUTHOR :YINXH

    -------
    MODIFY LOG

      UPDATE DATE :
      UPDATE BY   :
      UPDATE DESC :
    ***********************************************************************/

        v_log_msg                    VARCHAR2(4000); --日志信息
        v_error_msg                  VARCHAR2(2000); --异常信息

        v_report_item_id             NUMBER(11, 0); --列报项ID
        v_report_item_code           VARCHAR2(200); --列报项编码
        v_rule_expr                  VARCHAR2(4000); --表达式

        v_rule_expr_analytical       VARCHAR2(4000); --解析后的规则表达式
        v_rule_expr_amount           NUMBER(32, 8); --规则表达式解析后的计算结果

        v_rule_expr_analytical_cu    VARCHAR2(4000); --解析后的规则表达式[本位币]
        v_rule_expr_amount_cu        NUMBER(32, 8); --规则表达式解析后的计算结果[本位币]

        v_new_serial_no              NUMBER(6, 0); --新版本号
        v_new_report_data_id         NUMBER(11, 0); --新列项提数过程表ID

        v_acc_serial_no              NUMBER(6, 0); --会计引擎财务数据版本号

        v_report_item_rule_serial_no NUMBER(6, 0); --列报项规则版本号

    BEGIN

        FOR cur_item_rule IN (SELECT DISTINCT r1.report_item_id,
                                              r2.report_item_code,
                                              r1.valid_is,
                                              r1.audit_state
                              FROM rpt_conf_report_item_rule r1
                                       LEFT JOIN rpt_conf_report_item r2
                                                 ON r1.report_item_id = r2.report_item_id
                              WHERE r1.entity_id = p_entity_id   --业务单位id
                                AND r1.book_code = p_book_code   --账套编码
                                AND r1.deal_level = p_deal_level --计算优先级
                                AND r1.deal_type = '3'           --3-列项汇总
                                and (exists
                                         (select 1
                                          from RPT_BUSS_REPORT_TASK
                                          where TASK_CODE = p_task_code
                                            and TASK_TYPE = 1) or exists
                                         (select 1
                                          from RPT_BUSS_REPORT_TASK rt
                                                   left join RPT_BUSS_REPORT_TASK_ITEM rti
                                                             on rt.TASK_ID = rti.TASK_ID
                                          where rt.TASK_CODE = p_task_code
                                            and rt.TASK_TYPE = 2
                                            and rti.ITEM_ID = r1.REPORT_ITEM_ID))
                              ORDER BY r2.report_item_code ASC)
            LOOP

                BEGIN

                    -- 获取相关变量
                    v_report_item_id := cur_item_rule.report_item_id;
                    v_report_item_code := cur_item_rule.report_item_code;

                    -- 获取财务科目数据版本号(批量数据，同一批版本号相同)
                    SELECT MAX(bh.serial_no)
                    INTO v_acc_serial_no
                    FROM rpt_dap_ledger_balance bh
                    WHERE bh.entity_id = p_entity_id
                      AND bh.book_code = p_book_code
                      AND bh.year_month = p_year_month;

                    --获取所有列报项的原币别，然后按币别循环以下处理过程
                    FOR cur_currency IN (WITH r3 AS
                                                  (SELECT r1.report_item_id,
                                                          r1.upper_report_item_id,
                                                          LEVEL - 1 AS report_item_level
                                                   FROM rpt_conf_report_item r1
                                                   WHERE r1.valid_is = '1'
                                                   START WITH r1.report_item_id =
                                                              v_report_item_id
                                                   CONNECT BY PRIOR report_item_id =
                                                              upper_report_item_id)

                                         SELECT DISTINCT rd.currency_code,
                                                         rd.currency_cu_code
                                         FROM r3,
                                              rpt_duct_report_item_data rd
                                         WHERE r3.report_item_id <> v_report_item_id --去除当前列项
                                           AND r3.report_item_id = rd.report_item_id --主键关联
                                           AND r3.report_item_level = 1              --取下一层子级列项
                                           AND EXISTS
                                             (SELECT 1
                                              FROM rpt_conf_report_item_rule
                                              WHERE report_item_id =
                                                    r3.report_item_id
                                                AND entity_id = p_entity_id
                                                AND book_code = p_book_code)
                                           AND rd.entity_id = p_entity_id
                                           AND rd.book_code = p_book_code
                                           AND rd.year_month = p_year_month
                                         GROUP BY rd.currency_code,
                                                  rd.currency_cu_code)
                        LOOP

                            --过程数据提取：查询该列项所有子列项并汇总求和
                            WITH r3 AS
                                     (SELECT r1.report_item_id,
                                             r1.report_item_code,
                                             r1.upper_report_item_id,
                                             LEVEL - 1 AS report_item_level
                                      FROM rpt_conf_report_item r1
                                      WHERE r1.valid_is = '1'
                                      START WITH r1.report_item_id = v_report_item_id
                                      CONNECT BY PRIOR report_item_id = upper_report_item_id)

                            SELECT SUM(rd.amount),
                                   SUM(rd.amount_cu),
                                   listagg('[' || r3.report_item_code || ']', '+')
                                           within GROUP (ORDER BY r3.report_item_level),
                                   listagg('[' || rd.amount || ']', '+') within GROUP (ORDER BY r3.report_item_level),
                                   listagg('[' || rd.amount_cu || ']', '+') within GROUP (ORDER BY r3.report_item_level)
                            INTO v_rule_expr_amount,
                                v_rule_expr_amount_cu,
                                v_rule_expr,
                                v_rule_expr_analytical,
                                v_rule_expr_analytical_cu
                            FROM r3,
                                 rpt_duct_report_item_data rd
                            WHERE r3.report_item_id <> v_report_item_id --去除当前列项
                              AND r3.report_item_id = rd.report_item_id --主键关联
                              AND r3.report_item_level = 1              --取下一层子级列项
                              and rd.TASK_CODE = p_task_code
                              AND EXISTS
                                (SELECT 1
                                 FROM rpt_conf_report_item_rule
                                 WHERE report_item_id = r3.report_item_id
                                   AND entity_id = p_entity_id
                                   AND book_code = p_book_code)
                              AND rd.entity_id = p_entity_id
                              AND rd.book_code = p_book_code
                              AND rd.year_month = p_year_month
                              AND rd.currency_code = cur_currency.currency_code;

                            -- 未取到科目值，补0
                            IF v_rule_expr_amount_cu IS NULL THEN
                                v_rule_expr_amount := 0.00;
                                v_rule_expr_amount_cu := 0.00;
                            END IF;

                            -- 获取当前业务单位、账套编码、会计期间对应列项提数表的版本新版本号
                            SELECT coalesce((MAX(serial_no) + 1), 1)
                            INTO v_new_serial_no
                            FROM rpt_duct_report_item_data
                            WHERE entity_id = p_entity_id
                              AND book_code = p_book_code
                              AND year_month = p_year_month
                              and TASK_CODE = p_task_code
                              AND currency_code = cur_currency.currency_code
                              AND report_item_id = v_report_item_id;

                            -- 先清除列项提数表当前条件对应的数据
                            DELETE
                            FROM rpt_duct_report_item_data
                            WHERE entity_id = p_entity_id
                              AND book_code = p_book_code
                              AND year_month = p_year_month
                              and TASK_CODE = p_task_code
                              AND currency_code = cur_currency.currency_code
                              AND report_item_id = v_report_item_id;

                            -- 获取列项规则版本号
                            SELECT serial_no
                            INTO v_report_item_rule_serial_no
                            FROM rpt_conf_report_item_rule
                            WHERE report_item_id = v_report_item_id
                              AND entity_id = p_entity_id
                              AND book_code = p_book_code;

                            -- 再插入列项取数过程表
                            v_new_report_data_id := rpt_seq_duct_rpt_item_data.nextval;

                            INSERT INTO rpt_duct_report_item_data
                            (report_item_data_id,
                             serial_no,
                             entity_id,
                             book_code,
                             year_month,
                             report_item_id,
                             expr_type,
                             article,
                             expr_desc,
                             expr_desc_analytic,
                             currency_code,
                             currency_cu_code,
                             amount,
                             amount_cu,
                             acc_serial_no,
                             creator_id,
                             create_time,
                             report_item_rule_serial_no,
                             TASK_CODE)
                            VALUES (v_new_report_data_id,
                                    v_new_serial_no,
                                    p_entity_id,
                                    p_book_code,
                                    p_year_month,
                                    v_report_item_id,
                                    NULL, --取数规则
                                    NULL, --专项
                                    v_rule_expr,
                                    (v_rule_expr_analytical || ' | ' || v_rule_expr_analytical_cu), --解析表达式
                                    cur_currency.currency_code,
                                    cur_currency.currency_cu_code,
                                    v_rule_expr_amount,
                                    v_rule_expr_amount_cu,
                                    v_acc_serial_no, --财务数据版本号
                                    p_oper_id,
                                    SYSDATE,
                                    v_report_item_rule_serial_no,
                                    p_task_code);

                            -- 记录列项规则提数轨迹
                            INSERT INTO rpt_duct_report_item_datahis
                            (report_item_data_his_id,
                             report_item_data_id,
                             serial_no,
                             entity_id,
                             book_code,
                             year_month,
                             report_item_id,
                             expr_type,
                             article,
                             expr_desc,
                             expr_desc_analytic,
                             currency_code,
                             currency_cu_code,
                             amount,
                             amount_cu,
                             acc_serial_no,
                             creator_id,
                             create_time,
                             report_item_rule_serial_no,
                             TASK_CODE)
                            SELECT rpt_seq_duct_rpt_item_datahis.nextval,
                                   rd.report_item_data_id,
                                   rd.serial_no,
                                   rd.entity_id,
                                   rd.book_code,
                                   rd.year_month,
                                   rd.report_item_id,
                                   rd.expr_type,
                                   rd.article,
                                   rd.expr_desc,
                                   rd.expr_desc_analytic,
                                   rd.currency_code,
                                   rd.currency_cu_code,
                                   rd.amount,
                                   rd.amount_cu,
                                   rd.acc_serial_no,
                                   rd.creator_id,
                                   rd.create_time,
                                   rd.report_item_rule_serial_no,
                                   rd.TASK_CODE
                            FROM rpt_duct_report_item_data rd
                            WHERE rd.report_item_data_id = v_new_report_data_id;

                        END LOOP;

                    --【提取结果数据】
                    -- 有效且审核通过状态的列项规则提数到结果表
                    IF cur_item_rule.valid_is = '1' AND cur_item_rule.audit_state = '1' THEN
                        -- 先清除列项提数表当前条件对应的数据
                        DELETE
                        FROM rpt_buss_report_item_data
                        WHERE entity_id = p_entity_id
                          AND book_code = p_book_code
                          AND year_month = p_year_month
                          and TASK_CODE = p_task_code
                          AND report_item_id = v_report_item_id;

                        -- 按本位币从过程表提取列报项数据到结果表
                        INSERT INTO rpt_buss_report_item_data
                        (report_item_data_id,
                         serial_no,
                         entity_id,
                         book_code,
                         year_month,
                         report_item_id,
                         expr_type,
                         article,
                         currency_code,
                         amount,
                         creator_id,
                         create_time,
                         report_item_rule_serial_no,
                         TASK_CODE)
                        SELECT rpt_seq_buss_rpt_item_data.nextval,
                               td.serial_no,
                               td.entity_id,
                               td.book_code,
                               td.year_month,
                               td.report_item_id,
                               td.expr_type,
                               td.article,
                               td.currency_cu_code,
                               td.sum_amount_cu,
                               p_oper_id,
                               SYSDATE,
                               td.report_item_rule_serial_no,
                               td.task_code
                        FROM (SELECT max(rd.serial_no) serial_no,
                                     rd.entity_id,
                                     rd.book_code,
                                     rd.year_month,
                                     rd.report_item_id,
                                     rd.expr_type,
                                     rd.article,
                                     rd.currency_cu_code,
                                     SUM(rd.amount_cu) sum_amount_cu,
                                     rd.report_item_rule_serial_no,
                                     rd.TASK_CODE
                              FROM rpt_duct_report_item_data rd
                              WHERE rd.entity_id = p_entity_id
                                AND rd.book_code = p_book_code
                                AND rd.year_month = p_year_month
                                and rd.TASK_CODE = p_task_code
                                AND rd.report_item_id = v_report_item_id
                              GROUP BY rd.currency_cu_code,
                                       rd.entity_id,
                                       rd.book_code,
                                       rd.year_month,
                                       rd.report_item_id,
                                       rd.expr_type,
                                       rd.article,
                                       rd.report_item_rule_serial_no,
                                       rd.TASK_CODE) td;
                        --取本位币，此处不允许直接使用序号，因此需要包裹一层

                        -- 记录列项规则提数结果表轨迹
                        INSERT INTO rpt_buss_report_item_datahis
                        (report_item_data_his_id,
                         report_item_data_id,
                         serial_no,
                         entity_id,
                         book_code,
                         year_month,
                         report_item_id,
                         expr_type,
                         article,
                         currency_code,
                         amount,
                         creator_id,
                         create_time,
                         report_item_rule_serial_no,
                         TASK_CODE)
                        SELECT rpt_seq_buss_rpt_item_datahis.nextval,
                               rd.report_item_data_id,
                               rd.serial_no,
                               rd.entity_id,
                               rd.book_code,
                               rd.year_month,
                               rd.report_item_id,
                               rd.expr_type,
                               rd.article,
                               rd.currency_code,
                               rd.amount,
                               rd.creator_id,
                               rd.create_time,
                               rd.report_item_rule_serial_no,
                               rd.TASK_CODE
                        FROM rpt_buss_report_item_data rd
                        WHERE rd.entity_id = p_entity_id
                          AND rd.book_code = p_book_code
                          AND rd.year_month = p_year_month
                          and rd.TASK_CODE = p_task_code
                          AND rd.report_item_id = v_report_item_id;
                    END IF;

                    --提交事务
                    COMMIT;

                EXCEPTION
                    WHEN OTHERS THEN
                        v_log_msg := substr('列报项[' || v_report_item_code || ']列项汇总取数异常：' ||
                                            v_error_msg || '; ' || '**SQLERRM: ' ||
                                            SQLERRM || '**Error line: ' ||
                                            dbms_utility.format_error_backtrace() || '',
                                            1,
                                            4000);

                        -- 写入取数日志
                        INSERT INTO rpt_log_report_item_draw_task
                        (draw_task_id,
                         entity_id,
                         book_code,
                         year_month,
                         expr_type,
                         expr_desc,
                         task_name,
                         task_params,
                         taks_status,
                         log_msg,
                         oper_id,
                         oper_time,
                         TASK_CODE)
                        VALUES (rpt_seq_log_rpt_item_draw_task.nextval,
                                p_entity_id,
                                p_book_code,
                                p_year_month,
                                NULL,
                                v_rule_expr,
                                'rpt_pack_report_item_proc_summary',
                                v_report_item_code,
                                '2',
                                v_log_msg,
                                p_oper_id,
                                SYSDATE,
                                p_task_code);

                        --提交日志写入事务
                        COMMIT;

                        -- 往外层抛自定义异常
                        raise_application_error(-20003, v_log_msg);

                END;
            END LOOP; --遍历当前计算优先级规则
    END proc_summary;

    PROCEDURE proc_ternary(p_entity_id  NUMBER,
                           p_book_code  VARCHAR2,
                           p_year_month VARCHAR2,
                           p_deal_level NUMBER,
                           p_oper_id    NUMBER,
                           p_task_code  VARCHAR2) AS
        /***********************************************************************
    NAME : rpt_pack_report_item_proc_ternary
    PARAM :
    DESCRIPTION : 列报项取数[条件运算]
    DATE :2021-10-18
    AUTHOR :YINXH

    -------
    MODIFY LOG

      UPDATE DATE :
      UPDATE BY   :
      UPDATE DESC :
    ***********************************************************************/

        v_log_msg                      VARCHAR2(4000); --日志信息
        v_error_msg                    VARCHAR2(2000); --异常信息

        v_report_item_id               NUMBER(11, 0); --列报项ID
        v_report_item_code             VARCHAR2(100); --列报项编码

        v_currency                     VARCHAR2(3); --当前账套下的币别

        v_true_expr                    VARCHAR2(2000); --比较结果为true的表达式
        v_false_expr                   VARCHAR2(2000); --比较结果为false的表达式
        v_rule_expr_compare            VARCHAR2(2000); --比较表达式拼接串
        v_rule_expr_all                VARCHAR2(2000); --全表达式拼接串

        v_count_rule_is_num            NUMBER(11, 0); --表达式是否全为数字项

        v_rule_expr_analytical_compare VARCHAR2(4000); --比较表达式拼接串
        v_true_expr_analytical         VARCHAR2(4000); --比较结果为true的解析后表达式
        v_false_expr_analytical        VARCHAR2(4000); --比较结果为false的解析后表达式
        v_rule_expr_analytical_all     VARCHAR2(4000); --全表达式拼接串

        v_rule_expr_anal_compare_cu    VARCHAR2(4000); --比较表达式拼接串[本位币]
        v_true_expr_analytical_cu      VARCHAR2(4000); --比较结果为true的解析后表达式[本位币]
        v_false_expr_analytical_cu     VARCHAR2(4000); --比较结果为false的解析后表达式[本位币]
        v_rule_expr_analytical_all_cu  VARCHAR2(4000); --全表达式拼接串

        v_item_amount                  NUMBER(32, 8); --科目金额
        v_item_amount_cu               NUMBER(32, 8); --科目金额[本位币]

        v_expr_compare_result          BOOLEAN; --条件表达式比较结果值

        v_expr_compare_final_amount    NUMBER(32, 8); --条件表达式最终金额
        v_expr_compare_final_amount_cu NUMBER(32, 8); --条件表达式最终金额[本位币]

        v_new_serial_no                NUMBER(6, 0); --新版本号
        v_new_report_data_id           NUMBER(11, 0); --新列项提数过程表ID

        v_acc_serial_no                NUMBER(6, 0); --会计引擎财务数据版本号

        v_report_item_rule_serial_no   NUMBER(6, 0); --列报项规则版本号

    BEGIN

        FOR cur_item_rule IN (SELECT DISTINCT r1.report_item_id,
                                              r2.report_item_code,
                                              r3.true_expr,
                                              r3.false_expr,
                                              -- 将运算符编码转换为运算符：p_expr_operator:0-<;1-<=;2->;3->=;4-<>;5-=
                                              (r3.base_expr ||
                                               coalesce(rc.code_e_name, '') ||
                                               r3.other_expr) AS rule_expr_compare,
                                              (r3.base_expr ||
                                               coalesce(rc.code_e_name, '') ||
                                               r3.other_expr || '?' ||
                                               r3.true_expr || ':' ||
                                               r3.false_expr) AS rule_expr_all,
                                              r1.valid_is,
                                              r1.audit_state
                              FROM rpt_conf_report_item_rule r1
                                       LEFT JOIN rpt_conf_report_item r2
                                                 ON r2.report_item_id = r1.report_item_id
                                       LEFT JOIN rpt_conf_item_rule_sub r3
                                                 ON r3.report_item_rule_id =
                                                    r1.report_item_rule_id
                                       LEFT JOIN rpt_v_conf_code rc
                                                 ON rc.code_code_idx =
                                                    'OperationType/' || r3.expr_operator
                              WHERE r1.entity_id = p_entity_id   --业务单位id
                                AND r1.book_code = p_book_code   --账套编码
                                AND r1.deal_level = p_deal_level --计算优先级
                                AND r1.deal_type = '2'           --2-列项计算
                                AND r1.data_source = '5'         --5-条件运算
                                and (exists
                                         (select 1
                                          from RPT_BUSS_REPORT_TASK
                                          where TASK_CODE = p_task_code
                                            and TASK_TYPE = 1) or exists
                                         (select 1
                                          from RPT_BUSS_REPORT_TASK rt
                                                   left join RPT_BUSS_REPORT_TASK_ITEM rti
                                                             on rt.TASK_ID = rti.TASK_ID
                                          where rt.TASK_CODE = p_task_code
                                            and rt.TASK_TYPE = 2
                                            and rti.ITEM_ID = r1.REPORT_ITEM_ID))
                              ORDER BY r2.report_item_code ASC)
            LOOP

                BEGIN

                    -- 获取相关变量
                    v_report_item_id := cur_item_rule.report_item_id;
                    v_report_item_code := cur_item_rule.report_item_code;
                    v_true_expr := cur_item_rule.true_expr;
                    v_false_expr := cur_item_rule.false_expr;
                    v_rule_expr_compare := cur_item_rule.rule_expr_compare;
                    v_rule_expr_all := cur_item_rule.rule_expr_all;

                    -- 获取财务科目数据版本号(批量数据，同一批版本号相同)
                    SELECT MAX(bh.serial_no)
                    INTO v_acc_serial_no
                    FROM rpt_dap_ledger_balance bh
                    WHERE bh.entity_id = p_entity_id
                      AND bh.book_code = p_book_code
                      AND bh.year_month = p_year_month;

                    -- 获取表达式全为数字项计数值,
                    SELECT COUNT(1)
                    INTO v_count_rule_is_num
                    FROM dual
                    WHERE regexp_like(regexp_replace(v_rule_expr_all,
                                                     '\(|\)|\*|\-|\+|\/|\>|\>=|\<|\<=|\=|\<>|\?|\:'),
                                      '(^[+-]?\d{0,}\.?\d{0,}$)');

                    -- 如果全为数字项，则计数值大于0
                    IF v_count_rule_is_num > 0 THEN

                        BEGIN
                            -- 先计算比较表达式的值【本位币比较】
                            EXECUTE IMMEDIATE 'select case when (' || v_rule_expr_compare ||
                                              ') then 1 else 0 end from dual'
                                INTO v_expr_compare_result;

                            -- 根据比较结果选取列项最终表达式作为结果
                            IF v_expr_compare_result = TRUE THEN

                                EXECUTE IMMEDIATE 'select (' || v_true_expr || ') from dual'
                                    INTO v_expr_compare_final_amount;

                            ELSE

                                EXECUTE IMMEDIATE 'select (' || v_false_expr || ') from dual'
                                    INTO v_expr_compare_final_amount;

                            END IF;

                        EXCEPTION
                            WHEN OTHERS THEN
                                v_error_msg := '列项规则解析执行发生错误：' || v_rule_expr_all || ', ' ||
                                               SQLERRM;
                                -- 往外层抛自定义异常
                                raise_application_error(-20002, v_error_msg);

                        END;

                        -- 未取到科目值，补0
                        IF v_expr_compare_final_amount IS NULL THEN
                            v_expr_compare_final_amount := 0.00;
                        END IF;

                        --当前账套下的币别
                        SELECT r1.currency_code
                        INTO v_currency
                        FROM accuser.acc_conf_accountperiod r1
                        WHERE r1.entity_id = p_entity_id
                          AND r1.book_code = p_book_code
                          AND r1.year_month = p_year_month;

                        -- 获取当前业务单位、账套编码、会计期间对应列项提数表的版本新版本号
                        SELECT coalesce((MAX(serial_no) + 1), 1)
                        INTO v_new_serial_no
                        FROM rpt_duct_report_item_data
                        WHERE entity_id = p_entity_id
                          AND book_code = p_book_code
                          AND year_month = p_year_month
                          and TASK_CODE = p_task_code
                          AND currency_code = v_currency
                          AND report_item_id = v_report_item_id;

                        -- 先清除列项提数表当前条件对应的数据
                        DELETE
                        FROM rpt_duct_report_item_data
                        WHERE entity_id = p_entity_id
                          AND book_code = p_book_code
                          AND year_month = p_year_month
                          and TASK_CODE = p_task_code
                          AND currency_code = v_currency
                          AND report_item_id = v_report_item_id;

                        -- 获取列项规则版本号
                        SELECT serial_no
                        INTO v_report_item_rule_serial_no
                        FROM rpt_conf_report_item_rule
                        WHERE report_item_id = v_report_item_id
                          AND entity_id = p_entity_id
                          AND book_code = p_book_code;

                        -- 再插入列项取数过程表
                        v_new_report_data_id := rpt_seq_duct_rpt_item_data.nextval;

                        INSERT INTO rpt_duct_report_item_data
                        (report_item_data_id,
                         serial_no,
                         entity_id,
                         book_code,
                         year_month,
                         report_item_id,
                         expr_type,
                         article,
                         expr_desc,
                         expr_desc_analytic,
                         currency_code,
                         currency_cu_code,
                         amount,
                         amount_cu,
                         acc_serial_no,
                         creator_id,
                         create_time,
                         report_item_rule_serial_no,
                         TASK_CODE)
                        VALUES (v_new_report_data_id,
                                v_new_serial_no,
                                p_entity_id,
                                p_book_code,
                                p_year_month,
                                v_report_item_id,
                                NULL, --取数规则
                                NULL, --专项
                                v_rule_expr_all,
                                v_rule_expr_all, --解析表达式
                                v_currency,
                                v_currency,
                                v_expr_compare_final_amount,
                                v_expr_compare_final_amount,
                                v_acc_serial_no, --财务数据版本号
                                p_oper_id,
                                SYSDATE,
                                v_report_item_rule_serial_no,
                                p_task_code);

                        -- 记录列项规则提数轨迹
                        INSERT INTO rpt_duct_report_item_datahis
                        (report_item_data_his_id,
                         report_item_data_id,
                         serial_no,
                         entity_id,
                         book_code,
                         year_month,
                         report_item_id,
                         expr_type,
                         article,
                         expr_desc,
                         expr_desc_analytic,
                         currency_code,
                         currency_cu_code,
                         amount,
                         amount_cu,
                         acc_serial_no,
                         creator_id,
                         create_time,
                         report_item_rule_serial_no,
                         TASK_CODE)
                        SELECT rpt_seq_duct_rpt_item_datahis.nextval,
                               rd.report_item_data_id,
                               rd.serial_no,
                               rd.entity_id,
                               rd.book_code,
                               rd.year_month,
                               rd.report_item_id,
                               rd.expr_type,
                               rd.article,
                               rd.expr_desc,
                               rd.expr_desc_analytic,
                               rd.currency_code,
                               rd.currency_cu_code,
                               rd.amount,
                               rd.amount_cu,
                               rd.acc_serial_no,
                               rd.creator_id,
                               rd.create_time,
                               rd.report_item_rule_serial_no,
                               rd.TASK_CODE
                        FROM rpt_duct_report_item_data rd
                        WHERE rd.report_item_data_id = v_new_report_data_id;

                    ELSE
                        --非全数字项处理：
                        --获取表达式所有科目项的原币别，然后按币别循环以下处理过程，取得每个币别下的列报项及科目项金额
                        --判定使用本位币变量，不能使用原币，原币字段在做手工账的时候可能为0
                        FOR cur_currency IN (SELECT DISTINCT r1.currency_code,
                                                             r1.currency_cu_code
                                             FROM rpt_duct_report_item_data r1
                                                      LEFT JOIN rpt_conf_report_item r2
                                                                ON r2.report_item_id = r1.report_item_id
                                             WHERE r1.entity_id = p_entity_id --业务单位id
                                               AND r1.book_code = p_book_code --账套编码
                                               AND r1.year_month = p_year_month
                                               AND r2.report_item_code IN
                                                   (SELECT DISTINCT regexp_replace(regexp_substr(v_rule_expr_all,
                                                                                                 '\[(.+?)\]',
                                                                                                 1,
                                                                                                 LEVEL),
                                                                                   '\[|\]',
                                                                                   '')
                                                    FROM dual
                                                    CONNECT BY regexp_substr(v_rule_expr_all,
                                                                             '\[(.+?)\]',
                                                                             1,
                                                                             LEVEL) IS NOT NULL))
                            LOOP
                                -- 遍历币别初始表达式解析变量
                                v_rule_expr_analytical_compare := v_rule_expr_compare;
                                v_true_expr_analytical := v_true_expr;
                                v_false_expr_analytical := v_false_expr;
                                v_rule_expr_analytical_all := v_rule_expr_all;

                                v_rule_expr_anal_compare_cu := v_rule_expr_compare;
                                v_true_expr_analytical_cu := v_true_expr;
                                v_false_expr_analytical_cu := v_false_expr;
                                v_rule_expr_analytical_all_cu := v_rule_expr_all;

                                -- 获取当前业务单位、账套编码、会计期间对应列项提数表的版本新版本号
                                SELECT coalesce((MAX(serial_no) + 1), 1)
                                INTO v_new_serial_no
                                FROM rpt_duct_report_item_data
                                WHERE entity_id = p_entity_id
                                  AND book_code = p_book_code
                                  AND year_month = p_year_month
                                  and TASK_CODE = p_task_code
                                  AND currency_code = cur_currency.currency_code
                                  AND report_item_id = v_report_item_id;

                                -- 先清除列项提数表当前条件对应的数据
                                DELETE
                                FROM rpt_duct_report_item_data
                                WHERE entity_id = p_entity_id
                                  AND book_code = p_book_code
                                  AND year_month = p_year_month
                                  and TASK_CODE = p_task_code
                                  AND currency_code = cur_currency.currency_code
                                  AND report_item_id = v_report_item_id;

                                -- 获取列项规则版本号
                                SELECT serial_no
                                INTO v_report_item_rule_serial_no
                                FROM rpt_conf_report_item_rule
                                WHERE report_item_id = v_report_item_id
                                  AND entity_id = p_entity_id
                                  AND book_code = p_book_code;

                                -- 再插入列项取数过程表
                                v_new_report_data_id := rpt_seq_duct_rpt_item_data.nextval;

                                INSERT INTO rpt_duct_report_item_data
                                (report_item_data_id,
                                 serial_no,
                                 entity_id,
                                 book_code,
                                 year_month,
                                 report_item_id,
                                 expr_type,
                                 article,
                                 expr_desc,
                                 expr_desc_analytic,
                                 currency_code,
                                 currency_cu_code,
                                 amount,
                                 amount_cu,
                                 acc_serial_no,
                                 creator_id,
                                 create_time,
                                 report_item_rule_serial_no,
                                 TASK_CODE)
                                VALUES (v_new_report_data_id,
                                        v_new_serial_no,
                                        p_entity_id,
                                        p_book_code,
                                        p_year_month,
                                        v_report_item_id,
                                        NULL, --取数规则
                                        NULL, --专项
                                        v_rule_expr_all,
                                        NULL, --解析表达式
                                        cur_currency.currency_code,
                                        cur_currency.currency_cu_code,
                                        NULL,
                                        NULL,
                                        v_acc_serial_no, --财务数据版本号
                                        p_oper_id,
                                        SYSDATE,
                                        v_report_item_rule_serial_no,
                                        p_task_code);

                                -- 正则匹配获取[]包含项，排除数字项，并去重
                                FOR cur_item IN (SELECT DISTINCT regexp_substr(v_rule_expr_all,
                                                                               '\[(.+?)\]',
                                                                               1,
                                                                               LEVEL) AS cal_item
                                                 FROM dual
                                                 CONNECT BY regexp_substr(v_rule_expr_all,
                                                                          '\[(.+?)\]',
                                                                          1,
                                                                          LEVEL) IS NOT NULL)
                                    LOOP
                                    -- 正则匹配获取[]包含项，排除数字项，并去重

                                    -- 从列项过程表取数
                                        SELECT rb.amount, rb.amount_cu
                                        INTO v_item_amount, v_item_amount_cu
                                        FROM rpt_duct_report_item_data rb,
                                             rpt_conf_report_item ri
                                        WHERE rb.report_item_id = ri.report_item_id --列报项ID
                                          AND ri.report_item_code =
                                              regexp_replace(cur_item.cal_item, '\[|\]', '')
                                          AND rb.entity_id = p_entity_id
                                          AND rb.book_code = p_book_code
                                          AND rb.year_month = p_year_month
                                          and rb.TASK_CODE = p_task_code
                                          AND rb.currency_code = cur_currency.currency_code;

                                        -- 未取到科目值，补0
                                        IF v_item_amount_cu IS NULL THEN
                                            v_item_amount := 0.00;
                                            v_item_amount_cu := 0.00;
                                        END IF;

                                        -- [汇总币别]比较科目项的值，用正则(需转义处理)匹配替换表达式中科目项的编码
                                        IF v_item_amount_cu > 0 THEN
                                            -- 当科目项的值为正数时，直接替换，数字转字符串 cast(123 as VARCHAR2)
                                            -- 比较表达式替换
                                            v_rule_expr_analytical_compare := REPLACE(v_rule_expr_analytical_compare,
                                                                                      cur_item.cal_item,
                                                                                      CAST(v_item_amount AS
                                                                                          VARCHAR2));
                                            -- 比较为true时的表达式替换
                                            v_true_expr_analytical := REPLACE(v_true_expr_analytical,
                                                                              cur_item.cal_item,
                                                                              CAST(v_item_amount AS
                                                                                  VARCHAR2));
                                            -- 比较为false时的表达式替换
                                            v_false_expr_analytical := REPLACE(v_false_expr_analytical,
                                                                               cur_item.cal_item,
                                                                               CAST(v_item_amount AS
                                                                                   VARCHAR2));
                                            --全表达式替换
                                            v_rule_expr_analytical_all := REPLACE(v_rule_expr_analytical_all,
                                                                                  cur_item.cal_item,
                                                                                  CAST(v_item_amount AS
                                                                                      VARCHAR2));

                                            -- [本位币]当科目项的值为正数时，直接替换，数字转字符串 cast(123 as VARCHAR2)
                                            -- 比较表达式替换
                                            v_rule_expr_anal_compare_cu := REPLACE(v_rule_expr_anal_compare_cu,
                                                                                   cur_item.cal_item,
                                                                                   CAST(v_item_amount_cu AS
                                                                                       VARCHAR2));
                                            -- 比较为true时的表达式替换
                                            v_true_expr_analytical_cu := REPLACE(v_true_expr_analytical_cu,
                                                                                 cur_item.cal_item,
                                                                                 CAST(v_item_amount_cu AS
                                                                                     VARCHAR2));
                                            -- 比较为false时的表达式替换
                                            v_false_expr_analytical_cu := REPLACE(v_false_expr_analytical_cu,
                                                                                  cur_item.cal_item,
                                                                                  CAST(v_item_amount_cu AS
                                                                                      VARCHAR2));
                                            --全表达式替换
                                            v_rule_expr_analytical_all_cu := REPLACE(v_rule_expr_analytical_all_cu,
                                                                                     cur_item.cal_item,
                                                                                     CAST(v_item_amount_cu AS
                                                                                         VARCHAR2));

                                        ELSIF v_item_amount_cu < 0 THEN
                                            -- 当科目项的值为负数时，需要添加小括号
                                            -- 比较表达式替换
                                            v_rule_expr_analytical_compare := REPLACE(v_rule_expr_analytical_compare,
                                                                                      cur_item.cal_item,
                                                                                      '(' ||
                                                                                      CAST(v_item_amount AS
                                                                                          VARCHAR2) || ')');
                                            -- 比较为true时的表达式替换
                                            v_true_expr_analytical := REPLACE(v_true_expr_analytical,
                                                                              cur_item.cal_item,
                                                                              '(' ||
                                                                              CAST(v_item_amount AS
                                                                                  VARCHAR2) || ')');
                                            -- 比较为false时的表达式替换
                                            v_false_expr_analytical := REPLACE(v_false_expr_analytical,
                                                                               cur_item.cal_item,
                                                                               '(' ||
                                                                               CAST(v_item_amount AS
                                                                                   VARCHAR2) || ')');
                                            --全表达式替换
                                            v_rule_expr_analytical_all := REPLACE(v_rule_expr_analytical_all,
                                                                                  cur_item.cal_item,
                                                                                  '(' ||
                                                                                  CAST(v_item_amount AS
                                                                                      VARCHAR2) || ')');

                                            -- [本位币]当科目项的值为负数时，需要添加小括号
                                            -- 比较表达式替换
                                            v_rule_expr_anal_compare_cu := REPLACE(v_rule_expr_anal_compare_cu,
                                                                                   cur_item.cal_item,
                                                                                   '(' ||
                                                                                   CAST(v_item_amount_cu AS
                                                                                       VARCHAR2) || ')');
                                            -- 比较为true时的表达式替换
                                            v_true_expr_analytical_cu := REPLACE(v_true_expr_analytical_cu,
                                                                                 cur_item.cal_item,
                                                                                 '(' ||
                                                                                 CAST(v_item_amount_cu AS
                                                                                     VARCHAR2) || ')');
                                            -- 比较为false时的表达式替换
                                            v_false_expr_analytical_cu := REPLACE(v_false_expr_analytical_cu,
                                                                                  cur_item.cal_item,
                                                                                  '(' ||
                                                                                  CAST(v_item_amount_cu AS
                                                                                      VARCHAR2) || ')');
                                            --全表达式替换
                                            v_rule_expr_analytical_all_cu := REPLACE(v_rule_expr_analytical_all_cu,
                                                                                     cur_item.cal_item,
                                                                                     '(' ||
                                                                                     CAST(v_item_amount_cu AS
                                                                                         VARCHAR2) || ')');

                                        ELSE
                                            -- 若查询不到科目项的值，作为除数时，需要替换为1
                                            IF instr(v_rule_expr_all, ('/' || cur_item.cal_item)) > 0 THEN
                                                v_rule_expr_analytical_compare :=
                                                        REPLACE(v_rule_expr_analytical_compare,
                                                                ('/' ||
                                                                 cur_item.cal_item),
                                                                '/1');
                                                v_true_expr_analytical := REPLACE(v_true_expr_analytical,
                                                                                  ('/' ||
                                                                                   cur_item.cal_item),
                                                                                  '/1');
                                                v_false_expr_analytical := REPLACE(v_false_expr_analytical,
                                                                                   ('/' ||
                                                                                    cur_item.cal_item),
                                                                                   '/1');
                                                v_rule_expr_analytical_all := REPLACE(v_rule_expr_analytical_all,
                                                                                      ('/' ||
                                                                                       cur_item.cal_item),
                                                                                      '/1');

                                                -- [本位币]
                                                v_rule_expr_anal_compare_cu := REPLACE(v_rule_expr_anal_compare_cu,
                                                                                       ('/' ||
                                                                                        cur_item.cal_item),
                                                                                       '/1');
                                                v_true_expr_analytical_cu := REPLACE(v_true_expr_analytical_cu,
                                                                                     ('/' ||
                                                                                      cur_item.cal_item),
                                                                                     '/1');
                                                v_false_expr_analytical_cu := REPLACE(v_false_expr_analytical_cu,
                                                                                      ('/' ||
                                                                                       cur_item.cal_item),
                                                                                      '/1');
                                                v_rule_expr_analytical_all_cu := REPLACE(v_rule_expr_analytical_all_cu,
                                                                                         ('/' ||
                                                                                          cur_item.cal_item),
                                                                                         '/1');

                                            ELSE
                                                --不作为除数时，直接替换为0
                                                v_rule_expr_analytical_compare :=
                                                        REPLACE(v_rule_expr_analytical_compare,
                                                                cur_item.cal_item,
                                                                '0');
                                                v_true_expr_analytical := REPLACE(v_true_expr_analytical,
                                                                                  cur_item.cal_item,
                                                                                  '0');
                                                v_false_expr_analytical := REPLACE(v_false_expr_analytical,
                                                                                   cur_item.cal_item,
                                                                                   '0');
                                                v_rule_expr_analytical_all := REPLACE(v_rule_expr_analytical_all,
                                                                                      cur_item.cal_item,
                                                                                      '0');

                                                -- [本位币]
                                                v_rule_expr_anal_compare_cu := REPLACE(v_rule_expr_anal_compare_cu,
                                                                                       cur_item.cal_item,
                                                                                       '0');
                                                v_true_expr_analytical_cu := REPLACE(v_true_expr_analytical_cu,
                                                                                     cur_item.cal_item,
                                                                                     '0');
                                                v_false_expr_analytical_cu := REPLACE(v_false_expr_analytical_cu,
                                                                                      cur_item.cal_item,
                                                                                      '0');
                                                v_rule_expr_analytical_all_cu := REPLACE(v_rule_expr_analytical_all_cu,
                                                                                         cur_item.cal_item,
                                                                                         '0');

                                            END IF;
                                        END IF;

                                        -- 插入列报项取数过程明细表【必须保留旧版本数据】
                                        INSERT INTO rpt_duct_report_item_data_sub
                                        (report_item_data_details_id,
                                         report_item_data_id,
                                         expr_code, --取数项编码
                                         expr_desc, --取数项表达式
                                         currency_code,
                                         currency_cu_code,
                                         amount,
                                         amount_cu,
                                         creator_id,
                                         create_time)
                                        VALUES (rpt_seq_duct_rpt_item_data_dtl.nextval,
                                                v_new_report_data_id,
                                                regexp_replace(cur_item.cal_item, '\[|\]', ''), --列报项编码
                                                cur_item.cal_item, --解析出来的列报项编码，带[]
                                                cur_currency.currency_code,
                                                cur_currency.currency_cu_code,
                                                v_item_amount,
                                                v_item_amount_cu,
                                                p_oper_id,
                                                SYSDATE);

                                    END LOOP;
                                --遍历科目项

                                -- 计算表达式的值
                                IF v_rule_expr_anal_compare_cu IS NOT NULL THEN
                                    BEGIN
                                        -- 先计算比较表达式的值【本位币比较】
                                        EXECUTE IMMEDIATE 'select case when (' ||
                                                          v_rule_expr_anal_compare_cu ||
                                                          ') then 1 else 0 end  from dual'
                                            INTO v_expr_compare_result;

                                        -- 根据比较结果选取列项最终表达式作为结果
                                        IF v_expr_compare_result = TRUE THEN

                                            EXECUTE IMMEDIATE 'select (' || v_true_expr_analytical ||
                                                              ') from dual'
                                                INTO v_expr_compare_final_amount;

                                            EXECUTE IMMEDIATE 'select (' || v_true_expr_analytical_cu ||
                                                              ') from dual'
                                                INTO v_expr_compare_final_amount_cu;

                                        ELSE

                                            EXECUTE IMMEDIATE 'select (' || v_false_expr_analytical ||
                                                              ') from dual'
                                                INTO v_expr_compare_final_amount;

                                            EXECUTE IMMEDIATE 'select (' ||
                                                              v_false_expr_analytical_cu ||
                                                              ') from dual'
                                                INTO v_expr_compare_final_amount_cu;

                                        END IF;

                                    EXCEPTION
                                        WHEN OTHERS THEN
                                            v_error_msg := '列项规则解析执行发生错误：' ||
                                                           v_rule_expr_analytical_all || ' | ' ||
                                                           v_rule_expr_analytical_all_cu || ', ' ||
                                                           SQLERRM;
                                            -- 往外层抛自定义异常
                                            raise_application_error(-20002, v_error_msg);

                                    END;

                                    -- 更新数据到列项取数过程表
                                    UPDATE rpt_duct_report_item_data
                                    SET expr_desc_analytic = v_rule_expr_analytical_all ||
                                                             ' | ' ||
                                                             v_rule_expr_analytical_all_cu,
                                        amount             = v_expr_compare_final_amount,
                                        amount_cu          = v_expr_compare_final_amount_cu
                                    WHERE report_item_data_id = v_new_report_data_id;
                                END IF;

                                -- 记录列项规则提数轨迹
                                INSERT INTO rpt_duct_report_item_datahis
                                (report_item_data_his_id,
                                 report_item_data_id,
                                 serial_no,
                                 entity_id,
                                 book_code,
                                 year_month,
                                 report_item_id,
                                 expr_type,
                                 article,
                                 expr_desc,
                                 expr_desc_analytic,
                                 currency_code,
                                 currency_cu_code,
                                 amount,
                                 amount_cu,
                                 acc_serial_no,
                                 creator_id,
                                 create_time,
                                 report_item_rule_serial_no,
                                 TASK_CODE)
                                SELECT rpt_seq_duct_rpt_item_datahis.nextval,
                                       rd.report_item_data_id,
                                       rd.serial_no,
                                       rd.entity_id,
                                       rd.book_code,
                                       rd.year_month,
                                       rd.report_item_id,
                                       rd.expr_type,
                                       rd.article,
                                       rd.expr_desc,
                                       rd.expr_desc_analytic,
                                       rd.currency_code,
                                       rd.currency_cu_code,
                                       rd.amount,
                                       rd.amount_cu,
                                       rd.acc_serial_no,
                                       rd.creator_id,
                                       rd.create_time,
                                       rd.report_item_rule_serial_no,
                                       rd.TASK_CODE
                                FROM rpt_duct_report_item_data rd
                                WHERE rd.report_item_data_id = v_new_report_data_id;

                            END LOOP; -- 遍历币别
                    END IF;

                    --【提取结果数据】
                    -- 有效且审核通过状态的列项规则提数到结果表
                    IF cur_item_rule.valid_is = '1' AND cur_item_rule.audit_state = '1' THEN
                        -- 先清除列项提数表当前条件对应的数据
                        DELETE
                        FROM rpt_buss_report_item_data
                        WHERE entity_id = p_entity_id
                          AND book_code = p_book_code
                          AND year_month = p_year_month
                          and TASK_CODE = p_task_code
                          AND report_item_id = v_report_item_id;

                        -- 按本位币从过程表提取列报项数据到结果表
                        INSERT INTO rpt_buss_report_item_data
                        (report_item_data_id,
                         serial_no,
                         entity_id,
                         book_code,
                         year_month,
                         report_item_id,
                         expr_type,
                         article,
                         currency_code,
                         amount,
                         creator_id,
                         create_time,
                         report_item_rule_serial_no,
                         TASK_CODE)
                        SELECT rpt_seq_buss_rpt_item_data.nextval,
                               td.serial_no,
                               td.entity_id,
                               td.book_code,
                               td.year_month,
                               td.report_item_id,
                               td.expr_type,
                               td.article,
                               td.currency_cu_code,
                               td.sum_amount_cu,
                               p_oper_id,
                               SYSDATE,
                               td.report_item_rule_serial_no,
                               td.task_code
                        FROM (SELECT max(rd.serial_no) serial_no,
                                     rd.entity_id,
                                     rd.book_code,
                                     rd.year_month,
                                     rd.report_item_id,
                                     rd.expr_type,
                                     rd.article,
                                     rd.currency_cu_code,
                                     SUM(rd.amount_cu) sum_amount_cu,
                                     rd.report_item_rule_serial_no,
                                     rd.TASK_CODE
                              FROM rpt_duct_report_item_data rd
                              WHERE rd.entity_id = p_entity_id
                                AND rd.book_code = p_book_code
                                AND rd.year_month = p_year_month
                                and rd.TASK_CODE = p_task_code
                                AND rd.report_item_id = v_report_item_id
                              GROUP BY rd.currency_cu_code,
                                       rd.entity_id,
                                       rd.book_code,
                                       rd.year_month,
                                       rd.report_item_id,
                                       rd.expr_type,
                                       rd.article,
                                       rd.report_item_rule_serial_no,
                                       rd.TASK_CODE) td;
                        --取本位币，此处不允许直接使用序号，因此需要包裹一层

                        -- 记录列项规则提数结果表轨迹
                        INSERT INTO rpt_buss_report_item_datahis
                        (report_item_data_his_id,
                         report_item_data_id,
                         serial_no,
                         entity_id,
                         book_code,
                         year_month,
                         report_item_id,
                         expr_type,
                         article,
                         currency_code,
                         amount,
                         creator_id,
                         create_time,
                         report_item_rule_serial_no,
                         TASK_CODE)
                        SELECT rpt_seq_buss_rpt_item_datahis.nextval,
                               rd.report_item_data_id,
                               rd.serial_no,
                               rd.entity_id,
                               rd.book_code,
                               rd.year_month,
                               rd.report_item_id,
                               rd.expr_type,
                               rd.article,
                               rd.currency_code,
                               rd.amount,
                               rd.creator_id,
                               rd.create_time,
                               rd.report_item_rule_serial_no,
                               rd.TASK_CODE
                        FROM rpt_buss_report_item_data rd
                        WHERE rd.entity_id = p_entity_id
                          AND rd.book_code = p_book_code
                          AND rd.year_month = p_year_month
                          and rd.TASK_CODE = p_task_code
                          AND rd.report_item_id = v_report_item_id;
                    END IF;

                    --提交事务
                    COMMIT;
                EXCEPTION
                    WHEN OTHERS THEN
                        v_log_msg := substr('列报项[' || v_report_item_code || ']条件运算取数异常：' ||
                                            v_error_msg || '; ' || '**SQLERRM: ' ||
                                            SQLERRM || '**Error line: ' ||
                                            dbms_utility.format_error_backtrace() || '',
                                            1,
                                            4000);

                        -- 写入取数日志
                        INSERT INTO rpt_log_report_item_draw_task
                        (draw_task_id,
                         entity_id,
                         book_code,
                         year_month,
                         expr_type,
                         expr_desc,
                         task_name,
                         task_params,
                         taks_status,
                         log_msg,
                         oper_id,
                         oper_time,
                         TASK_CODE)
                        VALUES (rpt_seq_log_rpt_item_draw_task.nextval,
                                p_entity_id,
                                p_book_code,
                                p_year_month,
                                NULL,
                                v_rule_expr_all,
                                'rpt_pack_report_item_proc_ternary',
                                v_report_item_code,
                                '2',
                                v_log_msg,
                                p_oper_id,
                                SYSDATE,
                                p_task_code);

                        --提交日志写入事务
                        COMMIT;

                        -- 往外层抛自定义异常
                        raise_application_error(-20003, v_log_msg);

                END;
            END LOOP; --遍历当前计算优先级规则
    END proc_ternary;

    PROCEDURE proc_update_deal_level(p_entity_id      NUMBER,
                                     p_book_code      VARCHAR2,
                                     p_report_item_id NUMBER,
                                     p_deal_level     NUMBER,
                                     p_oper_id        NUMBER) AS
        /***********************************************************************
    NAME : rpt_pack_report_item_rule_proc_update_deal_level
    DESCRIPTION : 列报项规则自动更新引用列项的计算优先级
    DATE :2021-12-07
    AUTHOR :YINXH

    -------
    MODIFY LOG

      UPDATE DATE :
      UPDATE BY   :
      UPDATE DESC :
    ***********************************************************************/

        v_log_msg         VARCHAR2(4000); --日志信息
        v_error_msg       VARCHAR2(2000); --异常信息

        v_rule_deal_level NUMBER; --当前规则引用层级的计算优先级
        v_deal_level      NUMBER; --当前计算优先级

    BEGIN
        /**
      处理逻辑：当前列项A，计算优先级为A1; 表达式包含A的列项B，计算优先级为B1;
            父级列项C，计算优先级为C1, A1应该高于B1\C1
        1、需要先判断A是否有父级列项C，如果有，要获取父级列项C计算优先级，并比较A与C的计算优先级高低(值低级高)，
          如果A的等级低于或等于C(A1>=C1),则C1=A1+1;
        2、再获取B的计算优先级，并比较A与B的计算优先级高低(值低级高)，
          如果A的等级低于或等于B(A1>=B1),则B1=A1+1;
        3、递归循环规则引用层级
        4、仅列项计算和列项汇总
        5、记录更新轨迹

    */

        -- 递归获取所有表达式包含当前列项的列项计算优先级
        -- 循环处理中引用计算优先级变量，需要初始化赋值
        v_deal_level := p_deal_level;

        FOR cur_rule_level IN (WITH tb_result AS
                                        (SELECT a1.report_item_id,
                                                a2.report_item_code,
                                                (CASE
                                                     WHEN a1.deal_type = '2' AND
                                                          a1.data_source = '4' THEN
                                                         a1.expr_desc
                                                     WHEN a1.deal_type = '2' AND
                                                          a1.data_source = '5' THEN
                                                         (a3.base_expr ||
                                                          coalesce(rc.code_e_name, '') ||
                                                          a3.other_expr || '?' || a3.true_expr || ':' ||
                                                          a3.false_expr)
                                                    END)        rule_expr,
                                                a1.deal_level,
                                                LEVEL - 1 AS rule_level
                                         FROM rpt_conf_report_item_rule a1
                                                  LEFT JOIN rpt_conf_report_item a2
                                                            ON a2.report_item_id = a1.report_item_id
                                                  LEFT JOIN rpt_conf_item_rule_sub a3
                                                            ON a3.report_item_rule_id =
                                                               a1.report_item_rule_id
                                                  LEFT JOIN rpt_v_conf_code rc
                                                            ON rc.code_code = a3.expr_operator
                                                                AND rc.upper_code_id IN
                                                                    (SELECT code_id
                                                                     FROM rpt_v_conf_code
                                                                     WHERE upper(code_code_idx) =
                                                                           upper('OperationType')
                                                                       AND upper_code_id = 0)
                                                                AND rc.valid_is = '1'
                                         START WITH a1.report_item_id =
                                                    p_report_item_id           --参数3
                                                AND a1.deal_type IN ('2', '3') --当前列项是列项计算和列项汇总
                                                AND a1.entity_id = p_entity_id --参数1
                                                AND a1.book_code = p_book_code --参数2
                                         CONNECT BY (CASE
                                                         WHEN a1.deal_type = '2' AND
                                                              a1.data_source = '4' THEN
                                                             a1.expr_desc
                                                         WHEN a1.deal_type = '2' AND
                                                              a1.data_source = '5' THEN
                                                             (a3.base_expr ||
                                                              coalesce(rc.code_e_name, '') ||
                                                              a3.other_expr || '?' ||
                                                              a3.true_expr || ':' ||
                                                              a3.false_expr)
                                             END) LIKE
                                                    '%[' || a2.report_item_code || ']%')
                               SELECT DISTINCT t.rule_level
                               FROM tb_result t
                               WHERE t.report_item_id <> p_report_item_id --参数3，过滤当前项
                               ORDER BY t.rule_level ASC --依照循环递归层级排序
            )
            LOOP

                -- 同一引用层级，A的计算优先级应该不变
                v_rule_deal_level := v_deal_level;

                FOR cur_rule IN (WITH tb_result AS
                                          (SELECT a1.report_item_id,
                                                  a2.report_item_code,
                                                  (CASE
                                                       WHEN a1.deal_type = '2' AND
                                                            a1.data_source = '4' THEN
                                                           a1.expr_desc
                                                       WHEN a1.deal_type = '2' AND
                                                            a1.data_source = '5' THEN
                                                           (a3.base_expr ||
                                                            coalesce(rc.code_e_name, '') ||
                                                            a3.other_expr || '?' || a3.true_expr || ':' ||
                                                            a3.false_expr)
                                                      END)        rule_expr,
                                                  a1.deal_level,
                                                  LEVEL - 1 AS rule_level
                                           FROM rpt_conf_report_item_rule a1
                                                    LEFT JOIN rpt_conf_report_item a2
                                                              ON a2.report_item_id = a1.report_item_id
                                                    LEFT JOIN rpt_conf_item_rule_sub a3
                                                              ON a3.report_item_rule_id =
                                                                 a1.report_item_rule_id
                                                    LEFT JOIN rpt_v_conf_code rc
                                                              ON rc.code_code = a3.expr_operator
                                                                  AND rc.upper_code_id IN
                                                                      (SELECT code_id
                                                                       FROM rpt_v_conf_code
                                                                       WHERE upper(code_code_idx) =
                                                                             upper('OperationType')
                                                                         AND upper_code_id = 0)
                                                                  AND rc.valid_is = '1'
                                           START WITH a1.report_item_id = p_report_item_id --参数3
                                                  AND a1.deal_type IN ('2', '3')           --当前列项是列项计算和列项汇总
                                                  AND a1.entity_id = p_entity_id           --参数1
                                                  AND a1.book_code = p_book_code           --参数2
                                           CONNECT BY (CASE
                                                           WHEN a1.deal_type = '2' AND
                                                                a1.data_source = '4' THEN
                                                               a1.expr_desc
                                                           WHEN a1.deal_type = '2' AND
                                                                a1.data_source = '5' THEN
                                                               (a3.base_expr ||
                                                                coalesce(rc.code_e_name, '') ||
                                                                a3.other_expr || '?' || a3.true_expr || ':' ||
                                                                a3.false_expr)
                                               END) LIKE
                                                      '%[' || report_item_code || ']%')

                                 SELECT DISTINCT t.report_item_id,
                                                 t.report_item_code,
                                                 t.rule_expr,
                                                 t.deal_level,
                                                 t.rule_level
                                 FROM tb_result t
                                 WHERE t.report_item_id <> p_report_item_id     --参数3，过滤当前项
                                   AND t.rule_level = cur_rule_level.rule_level --按规则引用层级递归
                                 ORDER BY t.rule_level ASC --依照循环递归层级排序
                    )
                    LOOP

                        -- 如果A的等级低于或等于C的等级(A1>=C1),则C1=A1+1,
                        IF v_rule_deal_level >= cur_rule.deal_level THEN
                            -- 更新父级计算优先级
                            v_deal_level := v_rule_deal_level + 1;

                            UPDATE rpt_conf_report_item_rule
                            SET deal_level = v_deal_level,
                                serial_no  =
                                    (serial_no + 1)
                            WHERE report_item_id = cur_rule.report_item_id
                              AND entity_id = p_entity_id --参数1
                              AND book_code = p_book_code;
                            --参数2

                            -- 记录轨迹表
                            INSERT INTO rpt_conf_report_item_rulehis
                            (report_item_rule_his_id,
                             report_item_rule_id,
                             serial_no,
                             entity_id,
                             book_code,
                             report_item_id,
                             deal_type,
                             data_source,
                             expr_type,
                             period_type,
                             expr_desc,
                             deal_level,
                             valid_is,
                             remark,
                             creator_id,
                             create_time,
                             updator_id,
                             update_time,
                             oper_type,
                             oper_id,
                             oper_time,
                             audit_state,
                             checked_id,
                             checked_time,
                             checked_msg)
                            SELECT rpt_seq_conf_rpt_item_rulehis.nextval,
                                   rd.report_item_rule_id,
                                   rd.serial_no,
                                   rd.entity_id,
                                   rd.book_code,
                                   rd.report_item_id,
                                   rd.deal_type,
                                   rd.data_source,
                                   rd.expr_type,
                                   rd.period_type,
                                   rd.expr_desc,
                                   rd.deal_level,
                                   rd.valid_is,
                                   rd.remark,
                                   rd.creator_id,
                                   rd.create_time,
                                   rd.updator_id,
                                   rd.update_time,
                                   '2', --2-modify
                                   p_oper_id,
                                   SYSDATE,
                                   rd.audit_state,
                                   rd.checked_id,
                                   rd.checked_time,
                                   rd.checked_msg
                            FROM rpt_conf_report_item_rule rd
                            WHERE rd.report_item_id = cur_rule.report_item_id
                              AND rd.entity_id = p_entity_id --参数1
                              AND rd.book_code = p_book_code; --参数2

                        END IF;
                    END LOOP; --列项汇总规则循环
            END LOOP;
        --规则引用层级循环

        --提交事务
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            -- 发生异常回滚
            ROLLBACK;

            v_log_msg := substr('列报项规则自动更新引用列项的计算优先级异常：' || v_error_msg || '; ' ||
                                '**SQLERRM: ' || SQLERRM || '**Error line: ' ||
                                dbms_utility.format_error_backtrace() || '',
                                1,
                                4000);

            -- 往外层抛自定义异常
            raise_application_error(-20003, v_log_msg);

    END proc_update_deal_level;

    PROCEDURE proc_calculate_dimiension_data(p_entity_id           NUMBER,
                                             p_book_code           VARCHAR2,
                                             p_year_month          VARCHAR2,
                                             p_report_item_id      NUMBER,
                                             p_report_item_rule_id NUMBER,
                                             p_oper_id             NUMBER,
                                             p_serial_no           NUMBER,
                                             p_rule_serial_no      NUMBER) as

        /***********************************************************************
    NAME : proc_calculate_dimiension_data_sub
    DESCRIPTION : 列报项计算维度数据，将过程明细表（存储的是规则表达式中各个code
                  的按维度拆分的数据）的数据排列组合至过程表（存储的该规则下表达式计算
                  后按维度和币别拆分的数据）。然后将过程表数据，聚合本位币数据进结果表
                  1. 对过程明细表按币别循环
                  2. 对过程明细表查询出对应规则表达式编码所有维度的组合，循环
                  3. 获取上一步循环中组合值对应的编码的值，进行表达式解析
                  4. 解析表达式并计算存入过程表
                  5. 计算过程表本位币数据进结果表
    DATE :2023-09-19
    AUTHOR :XZX

    -------
    MODIFY LOG

      UPDATE DATE :
      UPDATE BY   :
      UPDATE DESC :
    ***********************************************************************/

        v_log_msg           VARCHAR2(4000); --日志信息
        v_error_msg         VARCHAR2(2000); --异常信息
        v_count_rule_is_num NUMBER(11, 0); --表达式是否全为数字项
        v_expr_desc_ori     VARCHAR2(4000); --原始表达式
        v_expr_desc         VARCHAR2(4000); --用于替换计算的原币表达式
        v_expr_desc_cu      VARCHAR2(4000); --用于替换计算的本位币表达式
        v_value             NUMBER(32, 8); -- 原币的值
        v_value_cu          NUMBER(32, 8); -- 本位币的值
        v_sql               varchar2(4000); -- sql

    begin
        select EXPR_DESC
        into v_expr_desc_ori
        from rpt_conf_report_item_rule
        where REPORT_ITEM_RULE_ID = p_report_item_rule_id;

        -- 如果规则不存在，直接返回
        if v_expr_desc_ori is null then
            return;
        end if;

        -- 判断是否全为数字项
        SELECT COUNT(1)
        INTO v_count_rule_is_num
        FROM dual
        WHERE regexp_like(regexp_replace(v_expr_desc_ori, '\(|\)|\*|\-|\+|\/'),
                          '(^[+-]?\d{0,}\.?\d{0,}$)');

        -- 只处理非全数字项的明细表数据，全数字项没有明细表数据
        IF v_count_rule_is_num <= 0 then
            -- 删除原过程表中的数据
            delete
            from rpt_duct_dimension_data
            where entity_id = p_entity_id
              and book_code = p_book_code
              and year_month = p_year_month
              and item_rule_id = p_report_item_rule_id;

            -- 非全数字项，在前置提取财务数据和列项四则计算函数中，表达式中的code对应的数据分维度插入到明细表

            -- 先按币别循环
            for cur_currency in (select distinct currency_code, currency_cu_code
                                 from rpt_duct_dimension_data_sub sub
                                 where sub.entity_id = p_entity_id
                                   and sub.book_code = p_book_code
                                   and sub.year_month = p_year_month
                                   and sub.item_rule_id = p_report_item_rule_id)
                loop
                    -- 查询所有维度的组合排列情况，然后组装数据到过程表
                    FOR cur_dimension IN (select dimension1,
                                                 dimension2,
                                                 dimension3,
                                                 dimension4,
                                                 dimension5,
                                                 dimension6,
                                                 dimension7,
                                                 dimension8,
                                                 dimension9,
                                                 dimension10,
                                                 dimension11,
                                                 dimension12,
                                                 dimension13,
                                                 dimension14,
                                                 dimension15,
                                                 dimension16,
                                                 dimension17
                                          from rpt_duct_dimension_data_sub sub
                                          where sub.entity_id = p_entity_id
                                            and sub.book_code = p_book_code
                                            and sub.year_month = p_year_month
                                            and sub.item_rule_id =
                                                p_report_item_rule_id
                                            and sub.currency_code =
                                                cur_currency.currency_code
                                            and sub.CURRENCY_CU_CODE =
                                                cur_currency.currency_cu_code
                                          group by dimension1,
                                                   dimension2,
                                                   dimension3,
                                                   dimension4,
                                                   dimension5,
                                                   dimension6,
                                                   dimension7,
                                                   dimension8,
                                                   dimension9,
                                                   dimension10,
                                                   dimension11,
                                                   dimension12,
                                                   dimension13,
                                                   dimension14,
                                                   dimension15,
                                                   dimension16,
                                                   dimension17)
                        loop
                            -- 表达式初始化
                            v_expr_desc := v_expr_desc_ori;
                            v_expr_desc_cu := v_expr_desc_ori;
                            -- 循环所有编码，然后组装表达式
                            FOR cur_item IN (SELECT DISTINCT regexp_substr(v_expr_desc_ori,
                                                                           '\[(.+?)\]',
                                                                           1,
                                                                           LEVEL) AS cal_item
                                             FROM dual
                                             CONNECT BY regexp_substr(v_expr_desc,
                                                                      '\[(.+?)\]',
                                                                      1,
                                                                      LEVEL) IS NOT NULL)
                                LOOP
                                -- 查询出编码对应的原币的值和本位币的值
                                -- 可能会有null值，此处拼接sql查询
                                    v_sql := 'select sum(value), sum(value_cu)
              from rpt_duct_dimension_data_sub sub
             where sub.entity_id = ' || p_entity_id || '
               and sub.book_code = ''' || p_book_code || '''
               and sub.year_month = ''' || p_year_month || '''
               and sub.item_rule_id = ' || p_report_item_rule_id || '
               and sub.currency_code = ''' ||
                                             cur_currency.currency_code || '''
               and sub.expr_code =
                   regexp_replace(''' || cur_item.cal_item ||
                                             ''', ''\[|\]'', '''')
               and sub.CURRENCY_CU_CODE = ''' ||
                                             cur_currency.currency_cu_code || '''
               and dimension1 ' || case
                                                                                                                     when cur_dimension.dimension1 is null
                                                                                                                         then
                                                                                                                         'is null'
                                                                                                                     else
                                                                                                                             '= ''' || cur_dimension.dimension1 || ''''
                                                 end || '
               and dimension2 ' || case
                                                                                               when cur_dimension.dimension2 is null
                                                                                                   then
                                                                                                   'is null'
                                                                                               else
                                                                                                       '= ''' || cur_dimension.dimension2 || ''''
                                                 end || '
               and dimension3 ' || case
                                                                                               when cur_dimension.dimension3 is null
                                                                                                   then
                                                                                                   'is null'
                                                                                               else
                                                                                                       '= ''' || cur_dimension.dimension3 || ''''
                                                 end || '
               and dimension4 ' || case
                                                                                               when cur_dimension.dimension4 is null
                                                                                                   then
                                                                                                   'is null'
                                                                                               else
                                                                                                       '= ''' || cur_dimension.dimension4 || ''''
                                                 end || '
               and dimension5 ' || case
                                                                                               when cur_dimension.dimension5 is null
                                                                                                   then
                                                                                                   'is null'
                                                                                               else
                                                                                                       '= ''' || cur_dimension.dimension5 || ''''
                                                 end || '
               and dimension6 ' || case
                                                                                               when cur_dimension.dimension6 is null
                                                                                                   then
                                                                                                   'is null'
                                                                                               else
                                                                                                       '= ''' || cur_dimension.dimension6 || ''''
                                                 end || '
               and dimension7 ' || case
                                                                                               when cur_dimension.dimension7 is null
                                                                                                   then
                                                                                                   'is null'
                                                                                               else
                                                                                                       '= ''' || cur_dimension.dimension7 || ''''
                                                 end || '
               and dimension8 ' || case
                                                                                               when cur_dimension.dimension8 is null
                                                                                                   then
                                                                                                   'is null'
                                                                                               else
                                                                                                       '= ''' || cur_dimension.dimension8 || ''''
                                                 end || '
               and dimension9 ' || case
                                                                                               when cur_dimension.dimension9 is null
                                                                                                   then
                                                                                                   'is null'
                                                                                               else
                                                                                                       '= ''' || cur_dimension.dimension9 || ''''
                                                 end || '
               and dimension10 ' || case
                                                                                                when cur_dimension.dimension10 is null
                                                                                                    then
                                                                                                    'is null'
                                                                                                else
                                                                                                        '= ''' || cur_dimension.dimension10 || ''''
                                                 end || '
               and dimension11 ' || case
                                                                                                when cur_dimension.dimension11 is null
                                                                                                    then
                                                                                                    'is null'
                                                                                                else
                                                                                                        '= ''' || cur_dimension.dimension11 || ''''
                                                 end || '
               and dimension12 ' || case
                                                                                                when cur_dimension.dimension12 is null
                                                                                                    then
                                                                                                    'is null'
                                                                                                else
                                                                                                        '= ''' || cur_dimension.dimension12 || ''''
                                                 end || '
               and dimension13 ' || case
                                                                                                when cur_dimension.dimension13 is null
                                                                                                    then
                                                                                                    'is null'
                                                                                                else
                                                                                                        '= ''' || cur_dimension.dimension13 || ''''
                                                 end || '
               and dimension14 ' || case
                                                                                                when cur_dimension.dimension14 is null
                                                                                                    then
                                                                                                    'is null'
                                                                                                else
                                                                                                        '= ''' || cur_dimension.dimension14 || ''''
                                                 end || '
               and dimension15 ' || case
                                                                                                when cur_dimension.dimension15 is null
                                                                                                    then
                                                                                                    'is null'
                                                                                                else
                                                                                                        '= ''' || cur_dimension.dimension15 || ''''
                                                 end || '
               and dimension16 ' || case
                                                                                                when cur_dimension.dimension16 is null
                                                                                                    then
                                                                                                    'is null'
                                                                                                else
                                                                                                        '= ''' || cur_dimension.dimension16 || ''''
                                                 end || '
               and dimension17 ' || case
                                                                                                when cur_dimension.dimension17 is null
                                                                                                    then
                                                                                                    'is null'
                                                                                                else
                                                                                                        '= ''' || cur_dimension.dimension17 || ''''
                                                 end;

                                    EXECUTE IMMEDIATE v_sql
                                        INTO v_value, v_value_cu;

                                    -- 未取到值，补0
                                    IF v_value IS NULL THEN
                                        v_value := 0.00;
                                        v_value_cu := 0.00;
                                    END IF;

                                    -- [汇总币别]用正则(需转义处理)匹配替换表达式中的编码
                                    IF v_value_cu > 0 THEN
                                        -- 当值为正数时，直接替换，数字转字符串 cast(123 as varchar2)
                                        v_expr_desc := REPLACE(v_expr_desc,
                                                               cur_item.cal_item,
                                                               CAST(v_value AS VARCHAR2));

                                        -- [本位币]当值为正数时，直接替换，数字转字符串 cast(123 as varchar2)
                                        v_expr_desc_cu := REPLACE(v_expr_desc_cu,
                                                                  cur_item.cal_item,
                                                                  CAST(v_value_cu AS VARCHAR2));

                                    ELSIF v_value_cu < 0 THEN
                                        -- 当值为负数时，需要添加小括号
                                        v_expr_desc := REPLACE(v_expr_desc,
                                                               cur_item.cal_item,
                                                               '(' || CAST(v_value AS VARCHAR2) || ')');

                                        -- [本位币]当值为负数时，需要添加小括号
                                        v_expr_desc_cu := REPLACE(v_expr_desc_cu,
                                                                  cur_item.cal_item,
                                                                  '(' || CAST(v_value_cu AS VARCHAR2) || ')');

                                    ELSE

                                        -- 若查询不到值，作为除数时，需要替换为1
                                        IF instr(v_expr_desc_ori, ('/' || cur_item.cal_item)) > 0 THEN
                                            -- [原币]
                                            v_expr_desc := REPLACE(v_expr_desc,
                                                                   ('/' || cur_item.cal_item),
                                                                   '/1');
                                            -- [本位币]
                                            v_expr_desc_cu := REPLACE(v_expr_desc_cu,
                                                                      ('/' || cur_item.cal_item),
                                                                      '/1');

                                        ELSE
                                            --不作为除数时，直接替换为0
                                            v_expr_desc := REPLACE(v_expr_desc, cur_item.cal_item, '0');
                                            -- [本位币]
                                            v_expr_desc_cu := REPLACE(v_expr_desc_cu,
                                                                      cur_item.cal_item,
                                                                      '0');

                                        END IF;
                                    END IF;
                                end loop;
                            -- 计算表达式的值
                            IF v_expr_desc_cu IS NOT NULL THEN

                                BEGIN
                                    --[原币]拼接规则表达式解析后的计算SQL脚本，返回规则表达式解析后的计算结果
                                    EXECUTE IMMEDIATE 'select (' || v_expr_desc || ') from dual'
                                        INTO v_value;
                                    --[本位币]拼接规则表达式解析后的计算SQL脚本，返回规则表达式解析后的计算结果
                                    EXECUTE IMMEDIATE 'select (' || v_expr_desc_cu ||
                                                      ') from dual'
                                        INTO v_value_cu;

                                EXCEPTION
                                    WHEN OTHERS THEN
                                        v_error_msg := '列项规则解析执行发生错误：' || v_expr_desc || ' | ' ||
                                                       v_expr_desc_cu || ', ' || SQLERRM;
                                        -- 往外层抛自定义异常
                                        raise_application_error(-20002, v_error_msg);
                                end;
                            END if;

                            -- 保存数据进过程表
                            insert into rpt_duct_dimension_data
                            (dimension_duct_data_id,
                             item_id,
                             item_rule_id,
                             book_code,
                             year_month,
                             entity_id,
                             serial_no,
                             value,
                             value_cu,
                             currency_code,
                             currency_cu_code,
                             report_item_rule_serial_no,
                             dimension1,
                             dimension2,
                             dimension3,
                             dimension4,
                             dimension5,
                             dimension6,
                             dimension7,
                             dimension8,
                             dimension9,
                             dimension10,
                             dimension11,
                             dimension12,
                             dimension13,
                             dimension14,
                             dimension15,
                             dimension16,
                             dimension17,
                             create_time,
                             creator_id)
                            values (rpt_seq_duct_dimension_data.nextval,
                                    p_report_item_id,
                                    p_report_item_rule_id,
                                    p_book_code,
                                    p_year_month,
                                    p_entity_id,
                                    p_serial_no,
                                    v_value,
                                    v_value_cu,
                                    cur_currency.currency_code,
                                    cur_currency.currency_cu_code,
                                    p_rule_serial_no,
                                    cur_dimension.dimension1,
                                    cur_dimension.dimension2,
                                    cur_dimension.dimension3,
                                    cur_dimension.dimension4,
                                    cur_dimension.dimension5,
                                    cur_dimension.dimension6,
                                    cur_dimension.dimension7,
                                    cur_dimension.dimension8,
                                    cur_dimension.dimension9,
                                    cur_dimension.dimension10,
                                    cur_dimension.dimension11,
                                    cur_dimension.dimension12,
                                    cur_dimension.dimension13,
                                    cur_dimension.dimension14,
                                    cur_dimension.dimension15,
                                    cur_dimension.dimension16,
                                    cur_dimension.dimension17,
                                    sysdate,
                                    p_oper_id);
                        end loop;
                end loop;
        end if;

        -- 删除原结果表的数据

        delete
        from rpt_buss_dimension_data
        where entity_id = p_entity_id
          and book_code = p_book_code
          and year_month = p_year_month
          and item_rule_id = p_report_item_rule_id;

        -- 聚合各维度的本位币数据，不需要币别区分
        insert into rpt_buss_dimension_data
        (dimension_data_id,
         item_id,
         item_rule_id,
         book_code,
         year_month,
         entity_id,
         serial_no,
         value,
         currency_code,
         report_item_rule_serial_no,
         dimension1,
         dimension2,
         dimension3,
         dimension4,
         dimension5,
         dimension6,
         dimension7,
         dimension8,
         dimension9,
         dimension10,
         dimension11,
         dimension12,
         dimension13,
         dimension14,
         dimension15,
         dimension16,
         dimension17,
         create_time,
         creator_id)
        select rpt_seq_buss_dimension_data.nextval,
               p_report_item_id,
               p_report_item_rule_id,
               p_book_code,
               p_year_month,
               p_entity_id,
               p_serial_no,
               t.value_cu,
               t.currency_cu_code,
               p_rule_serial_no,
               t.dimension1,
               t.dimension2,
               t.dimension3,
               t.dimension4,
               t.dimension5,
               t.dimension6,
               t.dimension7,
               t.dimension8,
               t.dimension9,
               t.dimension10,
               t.dimension11,
               t.dimension12,
               t.dimension13,
               t.dimension14,
               t.dimension15,
               t.dimension16,
               t.dimension17,
               sysdate,
               p_oper_id
        from (select sum(value_cu) value_cu,
                     currency_cu_code,
                     dimension1,
                     dimension2,
                     dimension3,
                     dimension4,
                     dimension5,
                     dimension6,
                     dimension7,
                     dimension8,
                     dimension9,
                     dimension10,
                     dimension11,
                     dimension12,
                     dimension13,
                     dimension14,
                     dimension15,
                     dimension16,
                     dimension17
              from rpt_duct_dimension_data
              where entity_id = p_entity_id
                and book_code = p_book_code
                and year_month = p_year_month
                and item_rule_id = p_report_item_rule_id
              group by currency_cu_code,
                       dimension1,
                       dimension2,
                       dimension3,
                       dimension4,
                       dimension5,
                       dimension6,
                       dimension7,
                       dimension8,
                       dimension9,
                       dimension10,
                       dimension11,
                       dimension12,
                       dimension13,
                       dimension14,
                       dimension15,
                       dimension16,
                       dimension17) t;
        -- 插入轨迹表
        insert into rpt_buss_dimension_datahis
        (his_id,
         dimension_data_id,
         item_id,
         item_rule_id,
         book_code,
         year_month,
         entity_id,
         serial_no,
         value,
         currency_code,
         report_item_rule_serial_no,
         dimension1,
         dimension2,
         dimension3,
         dimension4,
         dimension5,
         dimension6,
         dimension7,
         dimension8,
         dimension9,
         dimension10,
         dimension11,
         dimension12,
         dimension13,
         dimension14,
         dimension15,
         dimension16,
         dimension17,
         create_time,
         creator_id)
        select rpt_seq_buss_dimension_datahis.nextval,
               t.dimension_data_id,
               t.item_id,
               t.item_rule_id,
               t.book_code,
               t.year_month,
               t.entity_id,
               t.serial_no,
               t.value,
               t.currency_code,
               t.report_item_rule_serial_no,
               t.dimension1,
               t.dimension2,
               t.dimension3,
               t.dimension4,
               t.dimension5,
               t.dimension6,
               t.dimension7,
               t.dimension8,
               t.dimension9,
               t.dimension10,
               t.dimension11,
               t.dimension12,
               t.dimension13,
               t.dimension14,
               t.dimension15,
               t.dimension16,
               t.dimension17,
               t.create_time,
               t.creator_id
        from (select dimension_data_id,
                     item_id,
                     item_rule_id,
                     book_code,
                     year_month,
                     entity_id,
                     serial_no,
                     value,
                     currency_code,
                     report_item_rule_serial_no,
                     dimension1,
                     dimension2,
                     dimension3,
                     dimension4,
                     dimension5,
                     dimension6,
                     dimension7,
                     dimension8,
                     dimension9,
                     dimension10,
                     dimension11,
                     dimension12,
                     dimension13,
                     dimension14,
                     dimension15,
                     dimension16,
                     dimension17,
                     create_time,
                     creator_id
              from rpt_buss_dimension_data
              where entity_id = p_entity_id
                and book_code = p_book_code
                and year_month = p_year_month
                and item_rule_id = p_report_item_rule_id) t;

    EXCEPTION
        WHEN OTHERS THEN
            -- 发生异常回滚
            ROLLBACK;

            v_log_msg := substr('列报项规则(id:' || p_report_item_rule_id ||
                                ')计算维度数据异常：' || v_error_msg || '; ' ||
                                '**SQLERRM: ' || SQLERRM || '**Error line: ' ||
                                dbms_utility.format_error_backtrace() || '',
                                1,
                                4000);

            -- 往外层抛自定义异常
            raise_application_error(-20003, v_log_msg);

    end proc_calculate_dimiension_data;

    PROCEDURE proc_draw_qtc_evaluate(p_entity_id        NUMBER,
                                     p_user_id          NUMBER,
                                     p_start_year_month varchar2,
                                     p_end_year_month   varchar2,
                                     p_qtc_version_no   varchar2,
                                     p_icg_no           varchar2,
                                     p_model_id         number,
                                     p_loa_code         varchar2) is
        /***********************************************************************
        NAME : proc_draw_qtc_evaluate
        DESCRIPTION : 按条件提取计量输出表对应的因子数据至buss表，因不涉及计算，所以不入过程表
        DATE :2024-01-11
        AUTHOR :XZX
    ***********************************************************************/
        v_log_msg       VARCHAR2(4000); --日志信息
        v_error_msg     VARCHAR2(2000); --异常信息
        v_new_serial_no NUMBER(6, 0); --新版本号
        v_sql           VARCHAR2(4000) := '';
        v_condition     VARCHAR2(4000) := '';
    begin
        -- 查找最新的版本号
        select coalesce(max(SERIAL_NO), 0) + 1
        into v_new_serial_no
        from rpt_buss_report_item_data
        WHERE entity_id = p_entity_id
          AND year_month between p_start_year_month and p_end_year_month
          AND report_item_id IN (SELECT t.report_item_id
                                 FROM rpt_conf_report_item_rule t
                                 WHERE t.entity_id = p_entity_id
                                   AND t.deal_type = '1'   --取数方式：1-提数统计
                                   AND t.data_source = '6' --数据来源：4-计量数据
                                   AND t.valid_is = '1'
                                   AND t.audit_state = '1');
        --删除对应数据
        insert into RPT_BUSS_REPORT_ITEM_DATAHIS
        (REPORT_ITEM_DATA_HIS_ID,
         REPORT_ITEM_DATA_ID,
         SERIAL_NO,
         ENTITY_ID,
         BOOK_CODE,
         YEAR_MONTH,
         REPORT_ITEM_ID,
         EXPR_TYPE,
         ARTICLE,
         CURRENCY_CODE,
         AMOUNT,
         CREATOR_ID,
         CREATE_TIME,
         REPORT_ITEM_RULE_SERIAL_NO)
        select rpt_seq_buss_rpt_item_datahis.nextval,
               REPORT_ITEM_DATA_ID,
               SERIAL_NO,
               ENTITY_ID,
               BOOK_CODE,
               YEAR_MONTH,
               REPORT_ITEM_ID,
               EXPR_TYPE,
               ARTICLE,
               CURRENCY_CODE,
               AMOUNT,
               CREATOR_ID,
               CREATE_TIME,
               REPORT_ITEM_RULE_SERIAL_NO
        from rpt_buss_report_item_data
        WHERE entity_id = p_entity_id
          AND year_month between p_start_year_month and p_end_year_month
          AND report_item_id IN
              (SELECT t.report_item_id
               FROM rpt_conf_report_item_rule t
               WHERE t.entity_id = p_entity_id
                 AND t.deal_type = '1'   --取数方式：1-提数统计
                 AND t.data_source = '6' --数据来源：4-计量数据
                 AND t.valid_is = '1'
                 AND t.audit_state = '1');

        DELETE
        FROM rpt_buss_report_item_data
        WHERE entity_id = p_entity_id
          AND year_month between p_start_year_month and p_end_year_month
          AND report_item_id IN (SELECT t.report_item_id
                                 FROM rpt_conf_report_item_rule t
                                 WHERE t.entity_id = p_entity_id
                                   AND t.deal_type = '1'   --取数方式：1-提数统计
                                   AND t.data_source = '6' --数据来源：4-计量数据
                                   AND t.valid_is = '1'
                                   AND t.audit_state = '1');
        commit;

        -- 提前处理条件
        v_condition := ' where t.ENTITY_ID = ' || p_entity_id || ' ' || '
                        and t.YEAR_MONTH between ''' ||
                       p_start_year_month || ''' ' || '
                        and ''' || p_end_year_month ||
                       ''' ';
        if p_model_id is not null then
            v_condition := v_condition || ' and t.MODEL_DEF_ID = ' || p_model_id || ' ';
        end if;

        if p_icg_no is not null and p_icg_no <> '' then
            v_condition := v_condition || ' and t.ICG_NO = ''' || p_icg_no ||
                           ''' ';
        end if;
        if p_loa_code is not null and p_loa_code <> '' then
            v_condition := v_condition || ' and t.LOA_CODE = ''' || p_loa_code ||
                           ''' ';
        end if;
        -- 给版本号就确定版本号，不给就取已确认的数据
        if p_qtc_version_no is not null and p_qtc_version_no <> '' then
            v_condition := v_condition || 'and t.VERSION_NO = ''' ||
                           p_qtc_version_no || ''' ';
        ELSE
            v_condition := v_condition || ' and t.CONFIRM_IS = ''1'' ';
        end if;
        -- 针对配置的计量的列报项进行提数
        for rec_item in (SELECT DISTINCT r1.report_item_id,
                                         r2.report_item_code,
                                         r1.expr_desc,
                                         r1.expr_type,
                                         r1.valid_is,
                                         r1.audit_state,
                                         r1.NEED_DIMENSION_IS,
                                         r1.report_item_rule_id,
                                         r1.SERIAL_NO,
                                         m.MODEL_CODE,
                                         m.TAB_COL_NAME
                         FROM rpt_conf_report_item_rule r1
                                  LEFT JOIN rpt_conf_report_item r2
                                            ON r2.report_item_id = r1.report_item_id
                                  left join RPT_CONF_ITEM_RULE_QTC_MODEL m
                                            on r1.REPORT_ITEM_RULE_ID = m.REPORT_ITEM_RULE_ID
                         WHERE r1.entity_id = p_entity_id --业务单位id
                           AND r1.deal_type = '1'         -- 1-提取统计
                           AND r1.data_source = '6' -- 4 -计量数据
            )
            loop
                v_sql := 'insert into rpt_buss_report_item_data (report_item_data_id,
                                                       serial_no,
                                                       entity_id,
                                                       book_code,
                                                       year_month,
                                                       report_item_id,
                                                       expr_type,
                                                       article,
                                                       currency_code,
                                                       amount,
                                                       creator_id,
                                                       create_time,
                                                       report_item_rule_serial_no)
                select rpt_seq_buss_rpt_item_datahis.nextval,
                       ' || v_new_serial_no || ' ,
                       ' || p_entity_id || ',
                       ''-'',
                       d.year_month,
                       ' || rec_item.REPORT_ITEM_ID || ',
                       ''' || rec_item.expr_type || ''',
                       null,
                       d.CURRENCY_CODE,
                       d.amount,
                       ' || p_user_id || ',
                       sysdate,
                       ' || rec_item.SERIAL_NO || '
                 from (select sum(t.' ||
                         rec_item.TAB_COL_NAME || ')  as amount,
                             t.YEAR_MONTH as year_month,
                             t.CURRENCY_CODE,
                             t.entity_id
                      from RPT_DAP_QTC_EVALUATE t
                     ' || v_condition || '
                      group by t.YEAR_MONTH, t.CURRENCY_CODE,t.entity_id) d';
                dbms_output.put_line(v_sql);
                EXECUTE IMMEDIATE v_sql;
            end loop;
        commit;
    EXCEPTION
        WHEN OTHERS THEN
            -- 发生异常回滚
            ROLLBACK;
            v_log_msg := substr('提取计量数据异常：' || v_error_msg || '; ' ||
                                '**SQLERRM: ' || SQLERRM || '**Error line: ' ||
                                dbms_utility.format_error_backtrace() || '',
                                1,
                                4000);

            -- 往外层抛自定义异常
            raise_application_error(-20003, v_log_msg);

    end proc_draw_qtc_evaluate;

    PROCEDURE proc_draw_carryover_data(p_entity_id  NUMBER,
                                       p_book_code  VARCHAR2,
                                       p_year_month VARCHAR2,
                                       p_oper_id    NUMBER,
                                       p_task_code  varchar2) is

        v_report_item_id        NUMBER(11, 0); -- 需要提取的列报项的id
        v_report_item_amount    NUMBER(32, 8); -- 需要提取的列报项的金额
        v_new_serial_no         NUMBER(6, 0); --新版本号
        v_last_year_year_month  VARCHAR2(32); -- 上年年末的会计期间
        v_last_month_year_month VARCHAR2(32); -- 上月月末的会计期间
        v_acc_serial_no         NUMBER(6, 0); --会计引擎财务数据版本号
        v_draw_task_code        varchar2(64); -- 需要提取的数据的任务编码
    begin
        /***********************************************************************
    NAME : proc_draw_history_report_item_data
    PARAM :
    DESCRIPTION : 提取历史列报项数据
    DATE :2023-03-01
    AUTHOR :XZX

    -------
    MODIFY LOG

      UPDATE DATE :
      UPDATE BY   :
      UPDATE DESC :
    ***********************************************************************/

        --新版本号
        select coalesce(max(SERIAL_NO), 0) + 1
        into v_new_serial_no
        from rpt_duct_report_item_data
        WHERE entity_id = p_entity_id
          AND year_month = p_year_month
          and task_code = p_task_code
          AND report_item_id IN (SELECT t.report_item_id
                                 FROM rpt_conf_report_item_rule t
                                 WHERE t.entity_id = p_entity_id
                                   AND t.deal_type = '5'           --取数方式：5-结转数据
                                   AND t.data_source in ('7', '8') -- 7-上年，8上月
                                   AND t.valid_is = '1'
                                   AND t.audit_state = '1');

        -- 获取财务科目数据版本号(批量数据，同一批版本号相同)
        SELECT MAX(bh.serial_no)
        INTO v_acc_serial_no
        FROM rpt_dap_ledger_balance bh
        WHERE bh.entity_id = p_entity_id
          AND bh.book_code = p_book_code
          AND bh.year_month = p_year_month;

        -- 上年年末的会计期间
        select to_char(add_months(to_date(p_year_month, 'YYYYMM'), -12), 'YYYY') || '12'
        into v_last_year_year_month
        from dual;
        -- 上月月末的会计期间
        select to_char(add_months(to_date(p_year_month, 'YYYYMM'), -1),
                       'YYYYMM')
        into v_last_month_year_month
        from dual;

        v_draw_task_code := v_last_year_year_month || p_book_code ||
                            p_entity_id;

        DELETE
        FROM rpt_duct_report_item_data
        WHERE entity_id = p_entity_id
          AND book_code = p_book_code
          AND year_month = p_year_month
          and TASK_CODE = p_task_code
          AND report_item_id IN (SELECT t.report_item_id
                                 FROM rpt_conf_report_item_rule t
                                 WHERE t.entity_id = p_entity_id
                                   AND t.deal_type = '5'           --取数方式：5-结转数据
                                   AND t.data_source in ('7', '8') -- 7-上年，8上月
                                   AND t.valid_is = '1'
                                   AND t.audit_state = '1');

        -- 过程表数据记录
        INSERT INTO rpt_duct_report_item_data
        (report_item_data_id,
         serial_no,
         entity_id,
         book_code,
         year_month,
         report_item_id,
         expr_type,
         article,
         expr_desc,
         expr_desc_analytic,
         currency_code,
         currency_cu_code,
         amount,
         amount_cu,
         acc_serial_no,
         creator_id,
         create_time,
         report_item_rule_serial_no,
         TASK_CODE)
        select rpt_seq_duct_rpt_item_data.nextval,
               v_new_serial_no,
               p_entity_id,
               p_book_code,
               p_year_month,
               td.report_item_id,
               td.expr_type,
               null,
               td.expr_desc,
               to_char(td.amount),
               td.currency_code,
               td.currency_code,
               td.amount,
               td.amount,
               v_acc_serial_no,
               p_oper_id,
               sysdate,
               td.report_item_rule_serial_no,
               p_task_code
        from (SELECT rd.expr_type,
                     rd.currency_code,
                     rd.amount,
                     t.serial_no as report_item_rule_serial_no,
                     t.report_item_id,
                     t.expr_desc
              FROM rpt_conf_report_item_rule t
                       left join rpt_conf_report_item i
                                 on i.report_item_code =
                                    regexp_replace(t.expr_desc, '\[|\]')
                       left join rpt_buss_report_item_data rd
                                 on rd.report_item_id = i.report_item_id
              WHERE t.entity_id = p_entity_id
                AND t.book_code = p_book_code
                AND t.deal_type = '5'   --取数方式：5-结转数据
                AND t.data_source = '7' --数据来源：7-上年
                AND t.valid_is = '1'
                AND t.audit_state = '1'
                and rd.entity_id = p_entity_id
                AND rd.book_code = p_book_code
                AND rd.year_month = v_last_year_year_month
                and rd.task_code = v_draw_task_code

              union all
              SELECT rd.expr_type,
                     rd.currency_code,
                     rd.amount,
                     t.serial_no as report_item_rule_serial_no,
                     t.report_item_id,
                     t.expr_desc
              FROM rpt_conf_report_item_rule t
                       left join rpt_conf_report_item i
                                 on i.report_item_code =
                                    regexp_replace(t.expr_desc, '\[|\]')
                       left join rpt_buss_report_item_data rd
                                 on rd.report_item_id = i.report_item_id
              WHERE t.entity_id = p_entity_id
                AND t.book_code = p_book_code
                AND t.deal_type = '5'   --取数方式：5-结转数据
                AND t.data_source = '8' --数据来源：8-上月
                AND t.valid_is = '1'
                AND t.audit_state = '1'
                and rd.entity_id = p_entity_id
                AND rd.book_code = p_book_code
                AND rd.year_month = v_last_month_year_month
                and rd.task_code = v_draw_task_code) td
        where (exists (select 1
                       from RPT_BUSS_REPORT_TASK
                       where TASK_CODE = p_task_code
                         and TASK_TYPE = 1) or exists
                   (select 1
                    from RPT_BUSS_REPORT_TASK rt
                             left join RPT_BUSS_REPORT_TASK_ITEM rti
                                       on rt.TASK_ID = rti.TASK_ID
                    where rt.TASK_CODE = p_task_code
                      and rt.TASK_TYPE = 2
                      and rti.ITEM_ID = td.REPORT_ITEM_ID));

        -- 记录过程表的历史轨迹
        INSERT INTO rpt_duct_report_item_datahis
        (report_item_data_his_id,
         report_item_data_id,
         serial_no,
         entity_id,
         book_code,
         year_month,
         report_item_id,
         expr_type,
         article,
         expr_desc,
         expr_desc_analytic,
         currency_code,
         currency_cu_code,
         amount,
         amount_cu,
         acc_serial_no,
         creator_id,
         create_time,
         report_item_rule_serial_no)
        SELECT rpt_seq_duct_rpt_item_datahis.nextval,
               rd.report_item_data_id,
               rd.serial_no,
               rd.entity_id,
               rd.book_code,
               rd.year_month,
               rd.report_item_id,
               rd.expr_type,
               rd.article,
               rd.expr_desc,
               rd.expr_desc_analytic,
               rd.currency_code,
               rd.currency_cu_code,
               rd.amount,
               rd.amount_cu,
               rd.acc_serial_no,
               rd.creator_id,
               rd.create_time,
               rd.report_item_rule_serial_no
        FROM rpt_duct_report_item_data rd
        WHERE entity_id = p_entity_id
          AND book_code = p_book_code
          AND year_month = p_year_month
          and task_code = p_task_code
          AND report_item_id IN
              (SELECT t.report_item_id
               FROM rpt_conf_report_item_rule t
               WHERE t.entity_id = p_entity_id
                 AND t.deal_type = '5'           --取数方式：5-结转数据
                 AND t.data_source in ('7', '8') -- 7-上年，8上月
                 AND t.valid_is = '1'
                 AND t.audit_state = '1');

        -- 清理数据
        delete
        from rpt_buss_report_item_data
        WHERE entity_id = p_entity_id
          and book_code = p_book_code
          AND year_month = p_year_month
          and task_code = p_task_code
          AND report_item_id IN (SELECT t.report_item_id
                                 FROM rpt_conf_report_item_rule t
                                 WHERE t.entity_id = p_entity_id
                                   AND t.deal_type = '5'           --取数方式：5-结转数据
                                   AND t.data_source in ('7', '8') -- 7-上年，8上月
                                   AND t.valid_is = '1'
                                   AND t.audit_state = '1');

        -- 提取结果数据至结果表
        INSERT INTO rpt_buss_report_item_data
        (report_item_data_id,
         serial_no,
         entity_id,
         book_code,
         year_month,
         report_item_id,
         expr_type,
         article,
         currency_code,
         amount,
         creator_id,
         create_time,
         report_item_rule_serial_no,
         TASK_CODE)
        SELECT rpt_seq_buss_rpt_item_data.nextval,
               td.serial_no,
               td.entity_id,
               td.book_code,
               td.year_month,
               td.report_item_id,
               td.expr_type,
               td.article,
               td.currency_cu_code,
               td.sum_amount_cu,
               p_oper_id,
               SYSDATE,
               td.report_item_rule_serial_no,
               td.task_code
        FROM (SELECT max(rd.serial_no) serial_no,
                     rd.entity_id,
                     rd.book_code,
                     rd.year_month,
                     rd.report_item_id,
                     rd.expr_type,
                     rd.article,
                     rd.currency_cu_code,
                     SUM(rd.amount_cu) sum_amount_cu,
                     rd.report_item_rule_serial_no,
                     rd.TASK_CODE
              FROM rpt_duct_report_item_data rd
              WHERE rd.entity_id = p_entity_id
                AND rd.book_code = p_book_code
                AND rd.year_month = p_year_month
                and rd.TASK_CODE = p_task_code
                AND rd.report_item_id IN
                    (SELECT t.report_item_id
                     FROM rpt_conf_report_item_rule t
                     WHERE t.entity_id = p_entity_id
                       AND t.deal_type = '5'           --取数方式：5-结转数据
                       AND t.data_source in ('7', '8') -- 7-上年，8上月
                       AND t.valid_is = '1'
                       AND t.audit_state = '1')
              GROUP BY rd.currency_cu_code,
                       rd.entity_id,
                       rd.book_code,
                       rd.year_month,
                       rd.report_item_id,
                       rd.expr_type,
                       rd.article,
                       rd.report_item_rule_serial_no,
                       rd.TASK_CODE) td;

        -- 接着记录列项规则提数结果表轨迹
        INSERT INTO rpt_buss_report_item_datahis
        (report_item_data_his_id,
         report_item_data_id,
         serial_no,
         entity_id,
         book_code,
         year_month,
         report_item_id,
         expr_type,
         article,
         currency_code,
         amount,
         creator_id,
         create_time,
         report_item_rule_serial_no,
         TASK_CODE)
        SELECT rpt_seq_buss_rpt_item_datahis.nextval,
               rd.report_item_data_id,
               rd.serial_no,
               rd.entity_id,
               rd.book_code,
               rd.year_month,
               rd.report_item_id,
               rd.expr_type,
               rd.article,
               rd.currency_code,
               rd.amount,
               rd.creator_id,
               rd.create_time,
               rd.report_item_rule_serial_no,
               rd.TASK_CODE
        FROM rpt_buss_report_item_data rd
        WHERE rd.entity_id = p_entity_id
          AND rd.book_code = p_book_code
          AND rd.year_month = p_year_month
          and rd.TASK_CODE = p_task_code
          AND rd.report_item_id IN
              (SELECT t.report_item_id
               FROM rpt_conf_report_item_rule t
               WHERE t.entity_id = p_entity_id
                 AND t.book_code = p_book_code
                 AND t.deal_type = '5'           --取数方式：5-结转数据
                 AND t.data_source in ('7', '8') --数据来源：7-上年，8-上月
                 AND t.valid_is = '1'
                 AND t.audit_state = '1');

    end proc_draw_carryover_data;
END rpt_pack_report_item;
/

