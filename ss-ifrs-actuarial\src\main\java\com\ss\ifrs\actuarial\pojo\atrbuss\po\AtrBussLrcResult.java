/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-04-19 11:10:21
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2021-04-19 11:10:21<br/>
 * Description: LRC计量数据结果表<br/>
 * Table Name: ATR_BUSS_LRC_RESULT<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "LRC计量数据结果表")
public class AtrBussLrcResult implements Serializable {
    /**
     * Database column: ATR_BUSS_LRC_RESULT.LRC_MAIN_ID
     * Database remarks: lrc_main_id|主表ID
     */
    @ApiModelProperty(value = "lrc_main_id|主表ID", required = true)
    private Long lrcMainId;

    /**
     * Database column: ATR_BUSS_LRC_RESULT.LRC_RESULT_ID
     * Database remarks: lrc_result_id|主键
     */
    @ApiModelProperty(value = "lrc_result_id|主键", required = true)
    private Long lrcResultId;

    /**
     * Database column: ATR_BUSS_LRC_RESULT.YEAR_MONTH
     * Database remarks: year_month|计量期
     */
    @ApiModelProperty(value = "year_month|计量期", required = false)
    private String yearMonth;

    /**
     * Database column: ATR_BUSS_LRC_RESULT.POLICY_NO
     * Database remarks: policy_no|保单号
     */
    @ApiModelProperty(value = "policy_no|保单号", required = false)
    private String policyNo;

    /**
     * Database column: ATR_BUSS_LRC_RESULT.ENDORSE_NO
     * Database remarks: endorse_no|批单号码
     */
    @ApiModelProperty(value = "endorse_no|批单号码", required = false)
    private String endorseNo;

    /**
     * Database column: ATR_BUSS_LRC_RESULT.ENDORSE_SEQ
     * Database remarks: endorse_seq|批改序号
     */
    @ApiModelProperty(value = "endorse_seq|批改序号", required = false)
    private String endorseSeq;

    /**
     * Database column: ATR_BUSS_LRC_RESULT.PRODUCT_CODE
     * Database remarks: product_code|产品代码
     */
    @ApiModelProperty(value = "product_code|产品代码", required = false)
    private String productCode;

    /**
     * Database column: ATR_BUSS_LRC_RESULT.ATR_TYPE
     * Database remarks: atr_type|计量方式
     */
    @ApiModelProperty(value = "atr_type|计量方式", required = false)
    private String atrType;

    /**
     * Database column: ATR_BUSS_LRC_RESULT.RISK_CODE
     * Database remarks: risk_code|险种代码
     */
    @ApiModelProperty(value = "risk_code|险种代码", required = false)
    private String riskCode;

    /**
     * Database column: ATR_BUSS_LRC_RESULT.ATR_DATE
     * Database remarks: atr_date|计量时间点
     */
    @ApiModelProperty(value = "atr_date|计量时间点", required = false)
    private Date atrDate;

    /**
     * Database column: ATR_BUSS_LRC_RESULT.START_DATE
     * Database remarks: start_date|起保时间
     */
    @ApiModelProperty(value = "start_date|起保时间", required = false)
    private Date startDate;

    /**
     * Database column: ATR_BUSS_LRC_RESULT.END_DATE
     * Database remarks: end_date|终保时间
     */
    @ApiModelProperty(value = "end_date|终保时间", required = false)
    private Date endDate;

    /**
     * Database column: ATR_BUSS_LRC_RESULT.ENDORSE_DATE
     * Database remarks: endorse_date|批改日期
     */
    @ApiModelProperty(value = "endorse_date|批改日期", required = false)
    private Date endorseDate;

    /**
     * Database column: ATR_BUSS_LRC_RESULT.CHECK_DATE
     * Database remarks: check_date|双核日期
     */
    @ApiModelProperty(value = "check_date|双核日期", required = false)
    private Date checkDate;

    /**
     * Database column: ATR_BUSS_LRC_RESULT.CURRENCY
     * Database remarks: currency|创建时间
     */
    @ApiModelProperty(value = "currency|创建时间", required = false)
    private String currencyCode;

    /**
     * Database column: ATR_BUSS_LRC_RESULT.PREMIUM
     * Database remarks: premium|保费
     */
    @ApiModelProperty(value = "premium|保费", required = false)
    private BigDecimal premium;

    /**
     * Database column: ATR_BUSS_LRC_RESULT.INCOME_AMOUNT
     * Database remarks: income_amount|已赚金额
     */
    @ApiModelProperty(value = "income_amount|已赚金额", required = false)
    private BigDecimal incomeAmount;

    /**
     * Database column: ATR_BUSS_LRC_RESULT.REALITY_AMOUNT
     * Database remarks: reality_amount|实际未赚金额
     */
    @ApiModelProperty(value = "reality_amount|实际未赚金额", required = false)
    private BigDecimal realityAmount;

    /**
     * Database column: ATR_BUSS_LRC_RESULT.LRC_AMOUNT
     * Database remarks: lrc_amount|预期未赚金额
     */
    @ApiModelProperty(value = "lrc_amount|预期未赚金额", required = false)
    private BigDecimal lrcAmount;

    /**
     * Database column: ATR_BUSS_LRC_RESULT.CMUNIT_NO
     * Database remarks: cmunit_no|计量单元
     */
    @ApiModelProperty(value = "cmunit_no|计量单元", required = false)
    private String cmunitNo;

    /**
     * Database column: ATR_BUSS_LRC_RESULT.ICG_NO
     * Database remarks: icg_no|合同组
     */
    @ApiModelProperty(value = "icg_no|合同组", required = false)
    private String icgNo;

    /**
     * Database column: ATR_BUSS_LRC_RESULT.PORTFOLIO_NO
     * Database remarks: null
     */
    private String portfolioNo;



    private static final long serialVersionUID = 1L;

    public Long getLrcMainId() {
        return lrcMainId;
    }

    public void setLrcMainId(Long lrcMainId) {
        this.lrcMainId = lrcMainId;
    }

    public Long getLrcResultId() {
        return lrcResultId;
    }

    public void setLrcResultId(Long lrcResultId) {
        this.lrcResultId = lrcResultId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getEndorseNo() {
        return endorseNo;
    }

    public void setEndorseNo(String endorseNo) {
        this.endorseNo = endorseNo;
    }

    public String getEndorseSeq() {
        return endorseSeq;
    }

    public void setEndorseSeq(String endorseSeq) {
        this.endorseSeq = endorseSeq;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getAtrType() {
        return atrType;
    }

    public void setAtrType(String atrType) {
        this.atrType = atrType;
    }

    public String getRiskCode() {
        return riskCode;
    }

    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    public Date getAtrDate() {
        return atrDate;
    }

    public void setAtrDate(Date atrDate) {
        this.atrDate = atrDate;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Date getEndorseDate() {
        return endorseDate;
    }

    public void setEndorseDate(Date endorseDate) {
        this.endorseDate = endorseDate;
    }

    public Date getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(Date checkDate) {
        this.checkDate = checkDate;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public BigDecimal getPremium() {
        return premium;
    }

    public void setPremium(BigDecimal premium) {
        this.premium = premium;
    }

    public BigDecimal getIncomeAmount() {
        return incomeAmount;
    }

    public void setIncomeAmount(BigDecimal incomeAmount) {
        this.incomeAmount = incomeAmount;
    }

    public BigDecimal getRealityAmount() {
        return realityAmount;
    }

    public void setRealityAmount(BigDecimal realityAmount) {
        this.realityAmount = realityAmount;
    }

    public BigDecimal getLrcAmount() {
        return lrcAmount;
    }

    public void setLrcAmount(BigDecimal lrcAmount) {
        this.lrcAmount = lrcAmount;
    }

    public String getCmunitNo() {
        return cmunitNo;
    }

    public void setCmunitNo(String cmunitNo) {
        this.cmunitNo = cmunitNo;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }

}