package com.ss.ifrs.actuarial.service.impl;


import com.ss.ifrs.actuarial.dao.buss.reserve.AtrBussReserveUprDao;
import com.ss.ifrs.actuarial.dao.buss.reserve.AtrBussReserveUprDetailDao;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveUpr;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveExcelVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveUprDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveUprVo;
import com.ss.ifrs.actuarial.service.AtrBussReserveUprService;
import com.ss.ifrs.actuarial.util.AtrExcelHead;
import com.ss.ifrs.actuarial.util.AtrExcelSheet;
import com.ss.ifrs.actuarial.util.EasyExcelUtil;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ClassUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class AtrBussReserveUprServiceImpl implements AtrBussReserveUprService {

    @Autowired
    AtrBussReserveUprDao atrBussReserveUprDao;
    @Autowired
    AtrBussReserveUprDetailDao atrBussReserveUprDetailDao;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor= UnexpectedRollbackException.class)
    public void reserveUprExtract(AtrBussReserveUprVo atrBussReserveUprVo, Long userId) {
        AtrBussReserveUpr atrBussReserveUpr = ClassUtil.convert(atrBussReserveUprVo, AtrBussReserveUpr.class);
        SimpleDateFormat dateFormatVersion = new SimpleDateFormat("yyyyMMddHHmmss");
        SimpleDateFormat dateFormatMonth= new SimpleDateFormat("yyyyMM");
        atrBussReserveUpr.setCreatorId(userId);
        atrBussReserveUpr.setCreateTime(new Date());
        atrBussReserveUpr.setDataSource("0");
        atrBussReserveUpr.setConfirmIs("0");
        atrBussReserveUpr.setVersionNo(dateFormatVersion.format(new Date()));
        atrBussReserveUpr.setYearMonth(dateFormatMonth.format(atrBussReserveUpr.getDrawTime()));
        atrBussReserveUprDao.save(atrBussReserveUpr);

        Map<String, Object> param = new HashMap<>(2);
        param.put("reserveUprId", atrBussReserveUpr.getReserveUprId());
        atrBussReserveUprDao.reserveUprExtract(param);
    }

    @Override
    public Page<AtrBussReserveUprVo> searchPage(AtrBussReserveUprVo atrBussReserveUprVo, Pageable pageParam) {
        Page<AtrBussReserveUprVo> atrBussReserveUprVoPage = atrBussReserveUprDao.fuzzySearchPage(atrBussReserveUprVo, pageParam);
        return  atrBussReserveUprVoPage;
    }

    @Override
    public Page<AtrBussReserveUprDetailVo> findForDataTables(AtrBussReserveUprVo atrBussReserveUprVo, Pageable pageParam) {
        if (ObjectUtils.isNotEmpty(atrBussReserveUprVo.getColumnList())) {
            String columnSql = String.join(",", atrBussReserveUprVo.getColumnList());
            atrBussReserveUprVo.setColumnSql(columnSql + ",");
        }

        Page<AtrBussReserveUprDetailVo> atrBussReserveUprVoPage = atrBussReserveUprDetailDao.fuzzySearchPage(atrBussReserveUprVo, pageParam);
        return atrBussReserveUprVoPage;
    }

    @Override
    public void confirm(AtrBussReserveUprVo atrBussReserveUprVo, Long userId) {
        if(ObjectUtils.isEmpty(atrBussReserveUprVo.getReserveUprId()) && checkIsConfirm(atrBussReserveUprVo)) {
            return;
        }
        AtrBussReserveUpr po =  atrBussReserveUprDao.findById(atrBussReserveUprVo.getReserveUprId());
        po.setConfirmIs("1");
        po.setConfirmId(userId);
        po.setConfirmTime(new Date());
        po.setUpdatorId(userId);
        po.setUpdateTime(new Date());
        atrBussReserveUprDao.updateById(po);
    }

    @Override
    public void downloadDataFile(HttpServletResponse response, AtrBussReserveUprVo atrBussReserveUprVo) throws IOException {

        List<AtrBussReserveExcelVo> atrBussReserveUprVoPage = atrBussReserveUprDetailDao.searchListById(atrBussReserveUprVo);
        String excelName = atrBussReserveUprVo.getAtrExcel().getFileName();
        for (AtrExcelSheet sheet : atrBussReserveUprVo.getAtrExcel().getAtrSheetList()) {
            EasyExcelUtil.setResponse(response, excelName+  ".xlsx");
            EasyExcelUtil.export(response, head(sheet.getHeadList()), atrBussReserveUprVoPage);
        }
    }

    @Override
    public List<AtrBussReserveUprDetailVo> findDownloadList(AtrBussReserveUprVo atrBussReserveUprVo) {
        if (ObjectUtils.isNotEmpty(atrBussReserveUprVo.getColumnList())) {
            String columnSql = String.join(",", atrBussReserveUprVo.getColumnList());
            atrBussReserveUprVo.setColumnSql(columnSql + ",");
        }
        return atrBussReserveUprDetailDao.fuzzySearchPage(atrBussReserveUprVo);
    }

    @Override
    public void delete(Long reserveUprId, Long userId) {
        AtrBussReserveUpr po =  atrBussReserveUprDao.findById(reserveUprId);
        if(ObjectUtils.isNotEmpty(po) && CommonConstant.VersionStatus.PENDING.equals(po.getConfirmIs())) {
            atrBussReserveUprDetailDao.deleteByMainId(reserveUprId);
            atrBussReserveUprDao.deleteById(reserveUprId);
        }
    }

    @Override
    public Boolean checkIsConfirm(AtrBussReserveUprVo atrBussReserveUprVo) {
        Map<String, Object> param = new HashMap<>();
        param.put("entityId", atrBussReserveUprVo.getEntityId());
        param.put("yearMonth", atrBussReserveUprVo.getYearMonth());
        Long count = atrBussReserveUprDao.reserveUprConfirm(param);
        if(ObjectUtils.isNotEmpty(count) && count >0) {
            return true;
        }
        return false;
    }



    public static List<List<String>> head(List<AtrExcelHead> headList){
        List<List<String>> list = new ArrayList<List<String>>();
        headList.forEach(e -> {
            if(e.getChildHeadList()!= null && e.getChildHeadList().size() > 0) {
                e.getChildHeadList().forEach(c -> {
                    List<String> head = new ArrayList<>();
                    head.add(e.getLabel());
                    head.add(c.getLabel());
                    list.add(head);
                });
            } else {
                List<String> head = new ArrayList<>();
                head.add(e.getLabel());
                list.add(head);
            }
        });
        return list;
    }
}
