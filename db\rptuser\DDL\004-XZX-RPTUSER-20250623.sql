call RPT_PACK_COMMONUTILS_ADD_TABLE_COLUMN('RPT_DAP_ARTICLE_BALANCE','center_code','varchar(8)','核算机构编码');
call RPT_PACK_COMMONUTILS_ADD_TABLE_COLUMN('RPT_DAP_LEDGER_BALANCE','center_code','varchar(8)','核算机构编码');


call RPT_PACK_COMMONUTILS_ADD_TABLE_COLUMN('RPT_DAP_ARTICLE_BAL_HIS','center_code','varchar(8)','核算机构编码');
call RPT_PACK_COMMONUTILS_ADD_TABLE_COLUMN('RPT_DAP_LEDGER_BALANCEHIS','center_code','varchar(8)','核算机构编码');

call RPT_PACK_COMMONUTILS_ADD_TABLE_COLUMN('RPT_CONF_REPORT_ITEM_RULE', 'center_code', 'varchar(8)',
                                           '核算机构编码');

call RPT_PACK_COMMONUTILS_ADD_TABLE_COLUMN('rpt_conf_report_item_rulehis', 'center_code', 'varchar(8)',
                                           '核算机构编码');

call RPT_PACK_COMMONUTILS_ADD_TABLE_COLUMN('RPT_CONF_REPORT_TEMPLATE','auto_generate','char(1)','是否自动生成：1是，2否');

call RPT_PACK_COMMONUTILS_ADD_TABLE_COLUMN('RPT_CONF_REPORT_TEMPLATE','show_Type','varchar(10)','展示类型：用逗号隔开，来判断展示的地方');

call RPT_PACK_COMMONUTILS_ADD_TABLE_COLUMN('rpt_conf_report_templatehis','auto_generate','char(1)','是否自动生成：1是，2否');

call RPT_PACK_COMMONUTILS_ADD_TABLE_COLUMN('rpt_conf_report_templatehis','show_Type','varchar(10)',
                                           '展示类型：用逗号隔开，来判断展示的地方');


call RPT_PACK_COMMONUTILS_ADD_TABLE_COLUMN('RPT_CONF_REPORT_TASK_CONDITION', 'show_type', 'char',
                                           '展示类型：1-自定义报表，2-会计报表预览');