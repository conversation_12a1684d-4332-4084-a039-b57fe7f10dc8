package com.ss.ifrs.actuarial.dao.conf;

import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfCbTreasuryYc;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Sort;

import java.math.BigDecimal;
import java.util.List;

/**
 * 中债国债收益率曲线数据访问接口
 */
@Mapper
public interface AtrConfCbTreasuryYcDao extends IDao<AtrConfCbTreasuryYc, Long> {
    
    /**
     * 根据interest_rate_id和tenor_years删除记录
     *
     * @param interestRateId 利率ID
     * @param tenorYears 标准期限(年)
     */
    void deleteById(@Param("interestRateId") Long interestRateId, @Param("tenorYears") BigDecimal tenorYears);

    void deleteByInterestRateId(Long interestRateId);
    /**
     * 根据interest_rate_id和tenor_years更新记录
     *
     * @param record 要更新的记录
     */
    void updateById(AtrConfCbTreasuryYc record);
    
    /**
     * 批量更新记录
     *
     * @param records 要批量更新的记录列表
     */
    void updateBatchById(List<AtrConfCbTreasuryYc> records);

    List<AtrConfCbTreasuryYc> findAllByTenorYearsLessThan(Integer tenorYears);
    
    /**
     * 根据利率ID查询中债国债收益率曲线数据
     *
     * @param interestRateId 利率ID
     * @return 中债国债收益率曲线数据列表
     */
    List<AtrConfCbTreasuryYc> findByInterestRateId(@Param("interestRateId") Long interestRateId);
}