/**
 *
 * This file was generated by Taiping MyBatis Generator(v1.2.12).
 * Date: 2024-07-29 15:26:38
 * Author liebin.zheng
 *
 * Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved.
 *
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by Taiping MyBatis Generator(v1.2.12).
 * Create Date: 2024-07-29 15:26:38<br/>
 * Description: IBNR-二次分摊结果表<br/>
 * Table Name: ATR_SECONDARY_APPORTIONMENT_RESULT_IMPORT<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "IBNR-二次分摊结果表")
public class AtrSecondaryApportionmentResultImportVo implements Serializable {
    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_RESULT_IMPORT.ID
     * Database remarks: Id|主键
     */
    @ApiModelProperty(value = "Id|主键", required = true)
    private Long id;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_RESULT_IMPORT.PAY_FLAG
     * Database remarks: 数据类型,   41-再保前IBNR,   44-再保前IBNRALAE,   43-再保摊回IBNR,   45-再保摊回IBNRALAE,   48-再保前IBNRULAE,   49-再保摊回IBNRULAE,   50-已发生已报告再保前ULAE,   51-已发生已报告摊回ULAE
     */
    @ApiModelProperty(value = "数据类型,   41-再保前IBNR,   44-再保前IBNRALAE,   43-再保摊回IBNR,   45-再保摊回IBNRALAE,   48-再保前IBNRULAE,   49-再保摊回IBNRULAE,   50-已发生已报告再保前ULAE,   51-已发生已报告摊回ULAE", required = false)
    private String payFlag;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_RESULT_IMPORT.BUSINESS_TYPE
     * Database remarks: 业务类型1直接2分入
     */
    @ApiModelProperty(value = "业务类型1直接2分入", required = false)
    private String businessType;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_RESULT_IMPORT.ACCIDENT_MONTH
     * Database remarks: 事故月份
     */
    @ApiModelProperty(value = "事故月份", required = false)
    private String accidentMonth;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_RESULT_IMPORT.PAYMENT_MONTH
     * Database remarks: 赔付月份
     */
    @ApiModelProperty(value = "赔付月份", required = false)
    private String paymentMonth;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_RESULT_IMPORT.RISK_CATEGORY_CODE
     * Database remarks: 监管大类代码
     */
    @ApiModelProperty(value = "监管大类代码", required = false)
    private String riskCategoryCode;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_RESULT_IMPORT.COM_CODE
     * Database remarks: 机构代码
     */
    @ApiModelProperty(value = "机构代码", required = false)
    private String comCode;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_RESULT_IMPORT.CLASS_CODE
     * Database remarks: 险类代码
     */
    @ApiModelProperty(value = "险类代码", required = false)
    private String classCode;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_RESULT_IMPORT.RISK_CODE
     * Database remarks: 险种代码
     */
    @ApiModelProperty(value = "险种代码", required = false)
    private String riskCode;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_RESULT_IMPORT.SUM_PAID_CNY
     * Database remarks: 法定金额人民币
     */
    @ApiModelProperty(value = "法定金额人民币", required = false)
    private BigDecimal sumPaidCny;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_RESULT_IMPORT.IBNR01
     * Database remarks: ibnr预留字段
     */
    @ApiModelProperty(value = "险别代码", required = false)
    private String kindCode;

    /**
     * Database column: ATR_SECONDARY_APPORTIONMENT_RESULT_IMPORT.IBNR02
     * Database remarks: ibnr预留字段
     */
    @ApiModelProperty(value = "ibnr预留字段", required = false)
    private String ibnr02;

    private Long entityId;

    private String yearMonth;

    private String riskFlag;

    private String ibnrType;

    private BigDecimal ibnr;

    /**
     *  已赚权重
     */
    private BigDecimal premiumWeight;

    /**
     *  已决权重
     */
    private BigDecimal paidWeight;

    private BigDecimal caseWeight;

    private BigDecimal backMonth;

    private String comLevel;


    @ApiModelProperty(value = "已赚保费", required = false)
    private BigDecimal epPremium;

    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.PAID_PREMIUM
     * Database remarks: 已决保费
     */
    @ApiModelProperty(value = "已决保费", required = false)
    private BigDecimal paidPremium;

    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.CASE_PREMIUM
     * Database remarks: 未决保费
     */
    @ApiModelProperty(value = "未决保费", required = false)
    private BigDecimal casePremium;

    private String icgNo;

    private String treatyNo;

    private String policyNo;

    private String portfolioNo;

    private String riDirectionCode;

    private String treatyTypeCode;

    private String businessSourceCode;

    private String riPolicyNo;

    private String riStatementNo;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPayFlag() {
        return payFlag;
    }

    public void setPayFlag(String payFlag) {
        this.payFlag = payFlag;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getAccidentMonth() {
        return accidentMonth;
    }

    public void setAccidentMonth(String accidentMonth) {
        this.accidentMonth = accidentMonth;
    }

    public String getPaymentMonth() {
        return paymentMonth;
    }

    public void setPaymentMonth(String paymentMonth) {
        this.paymentMonth = paymentMonth;
    }

    public String getRiskCategoryCode() {
        return riskCategoryCode;
    }

    public void setRiskCategoryCode(String riskCategoryCode) {
        this.riskCategoryCode = riskCategoryCode;
    }

    public String getComCode() {
        return comCode;
    }

    public void setComCode(String comCode) {
        this.comCode = comCode;
    }

    public String getClassCode() {
        return classCode;
    }

    public void setClassCode(String classCode) {
        this.classCode = classCode;
    }

    public String getRiskCode() {
        return riskCode;
    }

    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    public BigDecimal getSumPaidCny() {
        return sumPaidCny;
    }

    public void setSumPaidCny(BigDecimal sumPaidCny) {
        this.sumPaidCny = sumPaidCny;
    }

    public String getKindCode() {
        return kindCode;
    }

    public void setKindCode(String kindCode) {
        this.kindCode = kindCode;
    }

    public String getIbnr02() {
        return ibnr02;
    }

    public void setIbnr02(String ibnr02) {
        this.ibnr02 = ibnr02;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getRiskFlag() {
        return riskFlag;
    }

    public void setRiskFlag(String riskFlag) {
        this.riskFlag = riskFlag;
    }

    public String getIbnrType() {
        return ibnrType;
    }

    public void setIbnrType(String ibnrType) {
        this.ibnrType = ibnrType;
    }

    public BigDecimal getIbnr() {
        return ibnr;
    }

    public void setIbnr(BigDecimal ibnr) {
        this.ibnr = ibnr;
    }

    public BigDecimal getPremiumWeight() {
        return premiumWeight;
    }

    public void setPremiumWeight(BigDecimal premiumWeight) {
        this.premiumWeight = premiumWeight;
    }

    public BigDecimal getPaidWeight() {
        return paidWeight;
    }

    public void setPaidWeight(BigDecimal paidWeight) {
        this.paidWeight = paidWeight;
    }

    public BigDecimal getBackMonth() {
        return backMonth;
    }

    public void setBackMonth(BigDecimal backMonth) {
        this.backMonth = backMonth;
    }

    public String getComLevel() {
        return comLevel;
    }

    public void setComLevel(String comLevel) {
        this.comLevel = comLevel;
    }

    public BigDecimal getEpPremium() {
        return epPremium;
    }

    public void setEpPremium(BigDecimal epPremium) {
        this.epPremium = epPremium;
    }

    public BigDecimal getPaidPremium() {
        return paidPremium;
    }

    public void setPaidPremium(BigDecimal paidPremium) {
        this.paidPremium = paidPremium;
    }

    public BigDecimal getCasePremium() {
        return casePremium;
    }

    public void setCasePremium(BigDecimal casePremium) {
        this.casePremium = casePremium;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public String getTreatyNo() {
        return treatyNo;
    }

    public void setTreatyNo(String treatyNo) {
        this.treatyNo = treatyNo;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }

    public String getRiDirectionCode() {
        return riDirectionCode;
    }

    public void setRiDirectionCode(String riDirectionCode) {
        this.riDirectionCode = riDirectionCode;
    }

    public String getTreatyTypeCode() {
        return treatyTypeCode;
    }

    public void setTreatyTypeCode(String treatyTypeCode) {
        this.treatyTypeCode = treatyTypeCode;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getRiPolicyNo() {
        return riPolicyNo;
    }

    public void setRiPolicyNo(String riPolicyNo) {
        this.riPolicyNo = riPolicyNo;
    }

    public String getRiStatementNo() {
        return riStatementNo;
    }

    public void setRiStatementNo(String riStatementNo) {
        this.riStatementNo = riStatementNo;
    }

    public BigDecimal getCaseWeight() {
        return caseWeight;
    }

    public void setCaseWeight(BigDecimal caseWeight) {
        this.caseWeight = caseWeight;
    }
}