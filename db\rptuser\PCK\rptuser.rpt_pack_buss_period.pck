create or replace package rpt_pack_buss_period is

  -- Author  : ZOPT
  -- Created : 2022/5/10 16:15:02
  -- Purpose : 
  
  -- Public type declarations


FUNCTION func_period_detail(p_entity_id  NUMBER,
                       p_year_month varchar2,
                       p_state      varchar2) RETURN BOOLEAN;

PROCEDURE proc_period_execution(p_entity_id NUMBER, p_state varchar2);

end rpt_pack_buss_period;

 
/
create or replace package body rpt_pack_buss_period is

  -- Private type declarations
FUNCTION func_period_detail(p_entity_id NUMBER, p_year_month varchar2, p_state varchar2)
  RETURN BOOLEAN AS 
  v_count NUMBER;
BEGIN
    
  --新增业务期间明细
  IF p_state = '0' THEN
  
    INSERT INTO rpt_conf_bussperiod_detail
      (period_detail_id, 
       buss_period_id, 
       biz_type_id, 
       ready_state, 
       creator_id, 
       create_time) 
    SELECT rpt_seq_conf_bussprd_dtl.NEXTVAL, 
           bp.buss_period_id,
           ct.biz_type_id,
           '0' ready_state,
           bp.creator_id,
           sysdate create_time
    FROM rpt_conf_table ct
    LEFT JOIN rpt_conf_bussperiod bp 
      ON bp.entity_id = p_entity_id
     AND bp.year_month = p_year_month
    WHERE ct.valid_is = '1';
    RETURN TRUE;
    
  --业务期间己准备，所有输入数据己准备
  ELSIF p_state = '1' THEN
    SELECT COUNT(period_detail_id) INTO v_count
      FROM rpt_conf_bussperiod_detail bpd
      LEFT JOIN rpt_conf_bussperiod bp 
        ON bp.buss_period_id = bpd.buss_period_id
      LEFT JOIN rpt_conf_table ct
        ON ct.biz_type_id = bpd.biz_type_id
     WHERE ct.direction = '1'
       AND bpd.ready_state = '0'
       AND bp.entity_id = p_entity_id
       AND bp.year_month = p_year_month;
    
    --无待准备数据
    IF v_count = 0 THEN
      RETURN TRUE;
    ELSE
      RETURN FALSE;
    END IF;
    
  --业务期间处理中，所有输入数据己准备
  ELSIF p_state = '2' THEN
    SELECT COUNT(period_detail_id) INTO v_count
      FROM rpt_conf_bussperiod_detail bpd
      LEFT JOIN rpt_conf_bussperiod bp 
        ON bp.buss_period_id = bpd.buss_period_id
      LEFT JOIN rpt_conf_table ct
        ON ct.biz_type_id = bpd.biz_type_id
     WHERE ct.direction = '1'
       AND bpd.ready_state = '0'
       AND bp.entity_id = p_entity_id
       AND bp.year_month = p_year_month;
    
    --无待准备数据
    IF v_count = 0 THEN
      RETURN TRUE;
    ELSE
      RETURN FALSE;
    END IF;
    
  --业务期间已完成，所有输出数据己准备
  ELSIF p_state = '3' THEN
    SELECT COUNT(period_detail_id) INTO v_count
      FROM rpt_conf_bussperiod_detail bpd
      LEFT JOIN rpt_conf_bussperiod bp 
        ON bp.buss_period_id = bpd.buss_period_id
      LEFT JOIN rpt_conf_table ct
        ON ct.biz_type_id = bpd.biz_type_id
     WHERE ct.direction = '0'
       AND bpd.ready_state = '0'
       AND bp.entity_id = p_entity_id
       AND bp.year_month = p_year_month;
    
    --无待准备数据
    IF v_count = 0 THEN
      RETURN TRUE;
    ELSE
      RETURN FALSE;
    END IF;
  END IF;

  RETURN FALSE;

EXCEPTION
  WHEN others THEN  
  DBMS_OUTPUT.PUT_LINE(SQLERRM);
  RETURN FALSE;
END func_period_detail;




PROCEDURE proc_period_execution(p_entity_id NUMBER, p_state varchar2)
 AS 

  v_year_month varchar2(32);
  v_book_code varchar2(32);
  v_next_year_month varchar2(32);
  v_detail_reday_state BOOLEAN;
  v_sql varchar2(32);
  v_count number;
 BEGIN
  
  --新增业务期间
  IF p_state = '0' THEN
    
    --检验是否存在准备中的业务期间(有且仅有一个在准备中的业务期间)
     SELECT count(1) INTO v_count 
      FROM rpt_conf_bussperiod 
     WHERE entity_id = p_entity_id
       AND execution_state = '0';
 
    if v_count > 0 then
     SELECT year_month INTO v_year_month 
      FROM rpt_conf_bussperiod 
     WHERE entity_id = p_entity_id
       AND execution_state = '0';
    end if;
       
 
    --获取账套
    SELECT book_code INTO v_book_code 
      FROM rpt_conf_bussperiod 
     WHERE entity_id = p_entity_id
       and rownum = 1;
       --AND execution_state = '3'; 
       DBMS_OUTPUT.PUT_LINE('('||v_book_code||')');
 

    IF v_year_month IS NULL THEN
      --增加一个新的业务期间
      SELECT TO_CHAR(ADD_MONTHS(to_date( year_month, 'YYYYMM' ),+1),'YYYYMM') INTO v_next_year_month    
        FROM rpt_conf_bussperiod 
       WHERE entity_id = p_entity_id
       and rownum=1
       ORDER BY year_month DESC
       ; 
      
      --初始数据按当前时间处理
      IF v_next_year_month IS NULL THEN
        v_next_year_month := to_char(sysdate,'YYYYMM');
      END IF;
      
      INSERT INTO rpt_conf_bussperiod
        (buss_period_id, 
         entity_id,
         book_code,
         year_month, 
         execution_state, 
         valid_is, 
         creator_id, 
         create_time
        )
      VALUES 
       (rpt_seq_conf_bussprd.NEXTVAL, 
        p_entity_id,
        v_book_code,
        v_next_year_month, 
        '0', 
        '1', 
        '1', 
        sysdate
        );
      
      --TODO 增加准备中的输入输出明细数据
      v_detail_reday_state := rpt_pack_buss_period.func_period_detail(p_entity_id, v_next_year_month, p_state);
    
    END IF;
  --业务期间己准备
  ELSIF p_state = '1' THEN
    --检验是否存在准备中业务期间
    SELECT year_month INTO v_year_month 
      FROM rpt_conf_bussperiod 
     WHERE entity_id = p_entity_id
       AND execution_state = '0' 
       and rownum=1
     ORDER BY year_month; 
    
    IF v_year_month IS NOT NULL THEN
      
      --验证输入明细数据状态是否己准备
      v_detail_reday_state := rpt_pack_buss_period.func_period_detail(p_entity_id, v_year_month, p_state);
      dbms_output.put_line( '('||  case v_detail_reday_state when true then 'TRUE' else 'FALSE' end ||')');


      IF v_detail_reday_state THEN
        --修改当前业务期间状态为己准备
        UPDATE rpt_conf_bussperiod 
          SET execution_state = '1', 
              updator_id = 1,
              update_time = sysdate
        WHERE entity_id = p_entity_id
          AND year_month = v_year_month;
        
        --增加下个准备中业务期间
        rpt_pack_buss_period.proc_period_execution(p_entity_id, '0');
        
      END IF;
    END IF;
    
    --确保有下个处理中业务期间
    rpt_pack_buss_period.proc_period_execution(p_entity_id, '2');
    
  --业务期间处理中
  ELSIF p_state = '2' THEN
    --检查在处理中的业务期间
    SELECT count(1) INTO v_count 
    FROM rpt_conf_bussperiod 
    WHERE entity_id = p_entity_id
      AND execution_state = '2' 
      and rownum=1
    ORDER BY year_month; 
    
    if v_count > 0 then 
      SELECT year_month INTO v_year_month 
      FROM rpt_conf_bussperiod 
      WHERE entity_id = p_entity_id
        AND execution_state = '2' 
        and rownum=1
      ORDER BY year_month;
    end if;
    
    --确保无在处理中的业务期间
    IF v_year_month IS NULL THEN
      SELECT year_month INTO v_next_year_month 
      FROM rpt_conf_bussperiod 
      WHERE entity_id = p_entity_id
        AND execution_state = '1' 
        and rownum=1
      ORDER BY year_month 
      ; 
      
      --存在下一个己准备的业务期间,开始处理
      IF v_next_year_month IS NOT NULL THEN
        
        --验证输出明细数据状态是否己准备完成，验证成功后执行以下逻辑
        v_detail_reday_state := rpt_pack_buss_period.func_period_detail(p_entity_id, v_next_year_month, p_state);
        IF v_detail_reday_state THEN
          
          --修改当前业务期间状态为处理中
          UPDATE rpt_conf_bussperiod 
            SET execution_state = '2', 
                updator_id = 1,
                update_time = sysdate
          WHERE entity_id = p_entity_id
            AND year_month = v_next_year_month;
        END IF;
      END IF; 
    END IF; 
    
  --业务期间已完成
  ELSIF p_state = '3' THEN
    SELECT year_month INTO v_year_month 
    FROM rpt_conf_bussperiod 
    WHERE entity_id = p_entity_id
      AND execution_state = '2' 
      and rownum=1
    ORDER BY year_month ; 
    
    IF v_year_month IS NOT NULL THEN
      
      --验证输出明细数据状态是否己完成，验证成功后执行以下逻辑
      v_detail_reday_state := rpt_pack_buss_period.func_period_detail(p_entity_id, v_year_month, p_state);
      IF v_detail_reday_state THEN
      
        --修改当前业务期间状态为已完成
        UPDATE rpt_conf_bussperiod 
          SET execution_state = '3', 
              updator_id = 1,
              update_time = sysdate
        WHERE entity_id = p_entity_id
          AND year_month = v_year_month;
        
        --下一个业务期间
        v_next_year_month := TO_CHAR(ADD_MONTHS(to_date( v_year_month, 'YYYYMM' ),+1),'YYYYMM');
    
        --检验下一个业务期间是否存在
        SELECT year_month INTO v_year_month 
        FROM rpt_conf_bussperiod 
        WHERE entity_id = p_entity_id
          AND year_month = v_next_year_month; 
        
        IF v_year_month IS NULL THEN    
          --增加下个准备中业务期间
          rpt_pack_buss_period.proc_period_execution(p_entity_id, '0');
        ELSE
          --修改下个业务期间状态为处理中
          rpt_pack_buss_period.proc_period_execution(p_entity_id, '2');
        END IF;
      END IF;
    END IF;
  END IF;
  
EXCEPTION
  WHEN others THEN  
  --ROLLBACK;
   DBMS_OUTPUT.PUT_LINE(SQLERRM);

END proc_period_execution;

end rpt_pack_buss_period;
/
