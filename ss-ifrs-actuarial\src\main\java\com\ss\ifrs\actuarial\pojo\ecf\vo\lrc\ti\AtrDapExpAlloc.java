package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.ti;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AtrDapExpAlloc {
    private String yearMonth;
    private String treatyNo;
    private String riskClassCode;
    private String schemeStatus;
    private String deptId;
    private String channelId;
    private String centerCode;
    private String finProductCode;
    private String finDetailCode;
    private String finSubProductCode;
    private BigDecimal amount;
}
