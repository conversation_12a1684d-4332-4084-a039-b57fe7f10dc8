package com.ss.ifrs.actuarial.feign;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.ss.platform.pojo.com.vo.TrackUserBehavioralVo;
import com.ss.platform.pojo.com.vo.TrackUserBehavioralEventVo;
import com.ss.platform.core.conf.FeignAuthConfig;

/**
 * 调用BPL日志管理的接口
 *
 * <AUTHOR>
 */
@FeignClient(name = "SS-PLATFORM-COMMON", configuration = {FeignAuthConfig.class})
public interface BmsTrackUserBehavioralFeignClient {

    /**
     * 保存操作的日志接口
     *
     * @param TrackUserBehavioralVo
     */
    @RequestMapping(value = "/bplLog/logExtern/add", method = RequestMethod.POST)
    void saveLogForExtern(@RequestBody @Validated TrackUserBehavioralVo TrackUserBehavioralVo);

    /**
     * 查询日志配置列表接口
     *
     * @param logConfig
     * @return
     */
    @RequestMapping(value = "/bplLog/logConfigExtern/find_list", method = RequestMethod.POST)
    List<TrackUserBehavioralEventVo> searchLogConfigForExtern(@RequestBody TrackUserBehavioralEventVo logConfig);

}
