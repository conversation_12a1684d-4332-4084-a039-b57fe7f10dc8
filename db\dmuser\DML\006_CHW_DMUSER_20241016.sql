update dm_conf_checkrule set rule_sql = 'select id
from odsuser.ods_reins_outward_detail t
where exists (select 1
              from odsuser.ods_reins_outward_detail t2
              where t2.task_code = ''#TASK_CODE#''
                and t.entity_code = t2.entity_code
                and t.ri_policy_no = t2.ri_policy_no
                and split_part(t.ri_endorse_seq_no,''-'',1) = split_part(t2.ri_endorse_seq_no,''-'',1)
                and t.correction_seq_no = t2.correction_seq_no
                and t.policy_no = t2.policy_no
                and t.endorse_seq_no = t2.endorse_seq_no
                and t.treaty_no = t2.treaty_no
                and t.claim_no = t2.claim_no
                and t.section_no_code = t2.section_no_code
                and t.reinsurer_code = t2.reinsurer_code
                and t.risk_code = t2.risk_code
                and t.gl_posting_date = t2.gl_posting_date
                and t.expenses_type_code = t2.expenses_type_code
                and t.id <> t2.id)' where rule_code = 'PK_SRC_CHECK_REINS_OUTWARD_DETAIL';

update dm_conf_checkrule set rule_sql = 'select id
  from odsuser.ods_reins_outward_detail src
 where exists (select 1
          from dm_reins_outward_detail tgt
         where tgt.entity_code = src.entity_code
           and tgt.ri_policy_no = src.ri_policy_no
           and split_part(tgt.ri_endorse_seq_no,''-'',1) = split_part(src.ri_endorse_seq_no,''-'',1)
           and tgt.correction_seq_no = src.correction_seq_no
           and tgt.policy_no = src.policy_no
           and tgt.endorse_seq_no = src.endorse_seq_no
           and tgt.treaty_no = src.treaty_no
           and tgt.claim_no = src.claim_no
           and tgt.section_no_code = src.section_no_code
           and tgt.reinsurer_code = src.reinsurer_code
           and tgt.risk_code = src.risk_code
           and tgt.gl_posting_date = src.gl_posting_date
           and tgt.expenses_type_code = src.expenses_type_code)' where rule_code = 'PK_CHECK_REINS_OUTWARD_DETAIL';

update dm_conf_checkrule set valid_is = '0' where rule_code = 'REINS_OUTWARD_DETAIL_UNIQUE_CHECK';



update dm_conf_checkrule
set rule_sql = 'select id from odsuser.ods_acc_payment where (extend_column2 is null or extend_column2 != ''1'') and id in (select x.id from odsuser.ods_acc_payment x
left join dm_policy_premium y on y.entity_code = x.entity_code and y.policy_no = x.policy_no
where y.id is null
and x.policy_no is not null
and substr(x.extend_column1,1,2) in (''BA'',''BB'',''BO'',''BP'',''CF'',''DI'',''IC'',''OP'',''JR'',''IR'')
and substr(x.entry_type_code,1,2) in (''DN'',''CN''))'
where rule_code = 'ACC_PAYMENT_BEFORE_REINS_G_DATA_VALID_IS';


update dm_conf_checkrule set rule_sql = 'select id from odsuser.ods_acc_payment where (extend_column2 is null or extend_column2 != ''1'') and id in (select x.id from odsuser.ods_acc_payment x
left join dm_reins_outward_detail y on y.entity_code = x.entity_code and y.ri_policy_no = x.ri_policy_no
where y.id is null
and x.ri_policy_no is not null
and substr(x.extend_column1,1,2) in (''AF'',''FO'')
and substr(x.entry_type_code,1,2) in (''DN'',''CN''))' where rule_code = 'ACC_PAYMENT_FAC_OUT_G_DATA_VALID_IS';



update dm_conf_checkrule set rule_sql = 'select id from odsuser.ods_acc_payment where (extend_column2 is null or extend_column2 != ''1'') and  id in (select x.id from odsuser.ods_acc_payment x
left join dm_reins_bill_detail y on y.entity_code = x.entity_code and y.ri_statement_no = x.ri_statement_no
where ((y.id is null and x.ri_statement_no is not null) or x.ri_statement_no is null)
and substr(x.extend_column1,1,2) in (''AP'',''RP'',''X1'',''X2'',''X3'',''X4'',''X5'',''XX'')
and substr(x.entry_type_code,1,2) in (''DN'',''CN''))' where rule_code = 'ACC_PAYMENT_OVER_COMPENSATED_OUT_G_DATA_VALID_IS';



update dm_conf_checkrule set rule_sql = 'select id from odsuser.ods_acc_payment where (extend_column2 is null or extend_column2 != ''1'') and id in (select x.id from odsuser.ods_acc_payment x
left join dm_policy_premium y on y.entity_code = x.entity_code and split_part(y.policy_no,''-'',1) = x.policy_no
where y.id is null
and x.policy_no is not null
and substr(x.extend_column1,1,2) in (''BA'',''BB'',''BO'',''BP'',''CF'',''DI'',''IC'',''OP'',''JR'',''IR'')
and substr(x.entry_type_code,1,2) not in (''DN'',''CN''))' where rule_code = 'ACC_PAYMENT_BEFORE_REINS_S_DATA_VALID_IS';


update dm_conf_checkrule set rule_sql = 'select id from odsuser.ods_acc_payment where (extend_column2 is null or extend_column2 != ''1'') and id in (select x.id from odsuser.ods_acc_payment x
     left join dm_policy_premium y on y.entity_code = x.entity_code and split_part(y.policy_no,''-'',1) = x.policy_no
     where y.id is null
     and x.policy_no is not null
     and substr(x.extend_column1,1,2) in (''AF'',''FO'')
     and substr(x.entry_type_code,1,2) not in (''DN'',''CN''))' where rule_code = 'ACC_PAYMENT_FAC_OUT_S_DATA_VALID_IS';


update dm_conf_checkrule set rule_sql = 'select x.id from odsuser.ods_acc_payment x
     where (extend_column2 is null or extend_column2 != ''1'') and (
     (x.treaty_no is null
     and left(x.extend_column1,2) in (''CP'',''QS'',''S1'',''S2'',''XP'',''SP'',''DC'',''AP'',''RP'',''X1'',''X2'',''X3'',''X4'',''X5'',''XX'') ) or ( x.treaty_no is not null and not exists
    (select 1
     from bpluser.bbs_conf_treaty ct
     where exists (select 1
                   from bpluser.bbs_entity b
                   where b.entity_id = ct.entity_id
                     and b.entity_code = x.entity_code)
       and ct.treaty_no = x.treaty_no)))' where rule_code = 'ACC_PAYMENT_TREATY_OUT_TREATY_NO_VALID_IS';



