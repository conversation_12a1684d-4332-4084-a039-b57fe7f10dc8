<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-03-02 18:17:47 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussFOLicIcgCalcDetailDao">
  <!-- 本配置文件由SS MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**IDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->
  <resultMap id="CustResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussFOLicIcgCalcDetailVo">
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="MAIN_ID" property="mainId" jdbcType="DECIMAL" />
    <result column="DEV_NO" property="devNo" jdbcType="DECIMAL" />
    <result column="IBNR_CUR" property="ibnrCur" jdbcType="DECIMAL" />
    <result column="IBNR_PRE" property="ibnrPre" jdbcType="DECIMAL" />
    <result column="OS_CUR" property="osCur" jdbcType="DECIMAL" />
    <result column="OS_PRE" property="osPre" jdbcType="DECIMAL" />
    <result column="ULAE_CUR" property="ulaeCur" jdbcType="DECIMAL" />
    <result column="ULAE_PRE" property="ulaePre" jdbcType="DECIMAL" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Cust_Column_List">
    ID, MAIN_ID, DEV_NO, IBNR_CUR, IBNR_PRE, OS_CUR, OS_PRE, ULAE_CUR, ULAE_PRE
  </sql>

  <select id="findByVo" flushCache="false" useCache="true" resultMap="CustResultMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
    select
     dev_no,
	sum(IBNR_CUR) as IBNR_CUR,
	sum(IBNR_PRE) as IBNR_PRE,
	sum(OS_CUR) as OS_CUR,
	sum(OS_PRE) as OS_PRE,
	sum(ULAE_CUR) as ULAE_CUR,
	sum(ULAE_PRE) as ULAE_PRE
    from ATR_BUSS_FO_LIC_ICG_CALC_DETAIL
    where main_id IN
    (SELECT id FROM ATR_BUSS_FO_LIC_ICG_CALC
        WHERE CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
        and ACTION_NO = #{actionNo,jdbcType=VARCHAR}
        and PORTFOLIO_NO = #{portfolioNo,jdbcType=VARCHAR}
        and ICG_NO = #{icgNo,jdbcType=VARCHAR})
	GROUP BY dev_no
	ORDER BY dev_no
  </select>


    <select id="findByMainId" flushCache="false" useCache="true" resultMap="CustResultMap"  parameterType="java.lang.Long">
        select
        <include refid="Cust_Column_List" />
        from ATR_BUSS_FO_LIC_ICG_CALC_DETAIL
        where main_id = #{mainId,jdbcType=DECIMAL}
    </select>
</mapper>