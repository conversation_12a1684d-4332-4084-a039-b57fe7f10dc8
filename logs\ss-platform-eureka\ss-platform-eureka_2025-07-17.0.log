[2m2025-07-17 17:13:29.815[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m The following profiles are active: dev
[2m2025-07-17 17:13:30.242[0;39m [dev] [33m WARN[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-17 17:13:30.333[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=ff1a2d2c-d19d-3931-a591-81b4348e9e38
[2m2025-07-17 17:13:30.573[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 7602 (http)
[2m2025-07-17 17:13:30.617[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 791 ms
[2m2025-07-17 17:13:30.664[0;39m [dev] [33m WARN[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-17 17:13:30.665[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-17 17:13:30.670[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.netflix.config.DynamicPropertyFactory [0;39m [2m:[0;39m DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@69fe0ed4
[2m2025-07-17 17:13:31.230[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-17 17:13:31.231[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-17 17:13:31.295[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-17 17:13:31.295[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-17 17:13:31.612[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.web.DefaultSecurityFilterChain    [0;39m [2m:[0;39m Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@28367da7, org.springframework.security.web.context.SecurityContextPersistenceFilter@7c91fe86, org.springframework.security.web.header.HeaderWriterFilter@7e2c6702, org.springframework.security.web.authentication.logout.LogoutFilter@4f235e8e, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@781c2497, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@13d289c7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@13aed42b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1a0f349, org.springframework.security.web.session.SessionManagementFilter@3dea226b, org.springframework.security.web.access.ExceptionTranslationFilter@609b041c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@56d6a1b1]
[2m2025-07-17 17:13:31.619[0;39m [dev] [33m WARN[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-17 17:13:31.619[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-17 17:13:31.697[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService 'applicationTaskExecutor'
[2m2025-07-17 17:13:32.154[0;39m [dev] [33m WARN[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mockingLoadBalancerClientRibbonWarnLogger[0;39m [2m:[0;39m You already have RibbonLoadBalancerClient on your classpath. It will be used by default. As Spring Cloud Ribbon is in maintenance mode. We recommend switching to BlockingLoadBalancerClient instead. In order to use it, set the value of `spring.cloud.loadbalancer.ribbon.enabled` to `false` or remove spring-cloud-starter-netflix-ribbon from your project.
[2m2025-07-17 17:13:32.206[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-17 17:13:32.219[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Initializing Eureka in region us-east-1
[2m2025-07-17 17:13:32.219[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Client configured to neither register nor query for data.
[2m2025-07-17 17:13:32.222[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Discovery Client initialized at timestamp 1752743612222 with initial instances count: 0
[2m2025-07-17 17:13:32.239[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initializing ...
[2m2025-07-17 17:13:32.240[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Adding new peer nodes [**********************************************/eureka/]
[2m2025-07-17 17:13:32.521[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-17 17:13:32.521[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-17 17:13:32.521[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-17 17:13:32.521[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-17 17:13:32.579[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Replica node URL:  **********************************************/eureka/
[2m2025-07-17 17:13:32.583[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Finished initializing remote region registries. All known remote regions: []
[2m2025-07-17 17:13:32.584[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initialized
[2m2025-07-17 17:13:32.589[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 2 endpoint(s) beneath base path '/actuator'
[2m2025-07-17 17:13:32.630[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application SS-PLATFORM-EUREKA with eureka with status UP
[2m2025-07-17 17:13:32.631[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[      Thread-19][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Setting the eureka configuration..
[2m2025-07-17 17:13:32.631[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[      Thread-19][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka data center value eureka.datacenter is not set, defaulting to default
[2m2025-07-17 17:13:32.633[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[      Thread-19][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka environment value eureka.environment is not set, defaulting to test
[2m2025-07-17 17:13:32.638[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[      Thread-19][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m isAws returned false
[2m2025-07-17 17:13:32.639[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[      Thread-19][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Initialized server context
[2m2025-07-17 17:13:32.639[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[      Thread-19][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Got 1 instances from neighboring DS node
[2m2025-07-17 17:13:32.639[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[      Thread-19][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Renew threshold is: 1
[2m2025-07-17 17:13:32.639[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[      Thread-19][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Changing status to UP
[2m2025-07-17 17:13:32.643[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[      Thread-19][0;39m [36me.s.EurekaServerInitializerConfiguration[0;39m [2m:[0;39m Started Eureka Server
[2m2025-07-17 17:13:32.654[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 7602 (http) with context path ''
[2m2025-07-17 17:13:32.654[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 7602
[2m2025-07-17 17:13:32.893[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m Started EurekaServerApplication in 4.151 seconds (JVM running for 5.185)
[2m2025-07-17 17:13:33.359[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[2)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-17 17:13:33.362[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[2)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 3 ms
[2m2025-07-17 17:14:32.642[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-17 17:15:32.654[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-17 17:16:32.664[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-17 17:17:32.675[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-17 17:18:32.677[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-17 17:19:32.677[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-17 17:20:32.690[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-17 17:21:32.703[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-17 17:22:32.709[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-17 17:23:32.713[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-17 17:24:32.723[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-17 17:25:32.727[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-17 17:25:33.649[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=false)
[2m2025-07-17 17:25:34.280[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=true)
[2m2025-07-17 17:25:44.418[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=false)
[2m2025-07-17 17:25:44.942[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[nio-7602-exec-6][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=true)
[2m2025-07-17 17:25:45.870[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=false)
[2m2025-07-17 17:25:46.403[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[nio-7602-exec-9][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=true)
[2m2025-07-17 17:25:46.666[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=false)
[2m2025-07-17 17:25:47.187[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=true)
[2m2025-07-17 17:26:32.739[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-17 17:27:13.103[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-17 17:27:13.630[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-17 17:27:32.745[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-17 17:28:32.593[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-17 17:28:32.749[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-17 17:29:32.750[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-17 17:30:32.752[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-17 17:31:32.757[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-17 17:32:32.772[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-17 17:33:32.782[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-17 17:34:32.786[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-17 17:35:32.795[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-17 17:36:32.804[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-17 17:37:32.806[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-17 17:38:32.812[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-17 17:39:32.822[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-17 17:40:32.827[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-17 17:41:32.839[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-17 17:42:32.852[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-17 17:43:32.603[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-17 17:43:32.853[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-17 17:44:32.860[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-17 17:45:32.871[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-17 17:46:32.879[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-17 17:47:32.880[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-17 17:48:32.894[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-17 17:49:32.898[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-17 17:50:32.912[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-17 17:51:32.915[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-17 17:52:32.923[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-17 17:53:32.927[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-17 17:54:32.934[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-17 17:55:32.947[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-17 17:56:32.960[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-17 17:57:32.973[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-17 17:58:32.608[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-17 17:58:32.981[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-17 17:59:32.984[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-17 18:00:32.995[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-17 18:01:20.137[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status DOWN (replication=false)
[2m2025-07-17 18:01:20.143[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application SS-PLATFORM-EUREKA with eureka with status DOWN
[2m2025-07-17 18:01:20.149[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[nio-7602-exec-9][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status DOWN (replication=false)
[2m2025-07-17 18:01:20.155[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[nio-7602-exec-7][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status DOWN (replication=false)
[2m2025-07-17 18:01:20.157[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status DOWN (replication=false)
[2m2025-07-17 18:01:20.163[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shutting down ...
[2m2025-07-17 18:01:20.165[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.batchSize.Will start computing stats again.
[2m2025-07-17 18:01:20.167[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[nio-7602-exec-6][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status DOWN (replication=false)
[2m2025-07-17 18:01:20.167[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.executionTime.Will start computing stats again.
[2m2025-07-17 18:01:20.168[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.executionTime.Will start computing stats again.
[2m2025-07-17 18:01:20.168[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.batchSize.Will start computing stats again.
[2m2025-07-17 18:01:20.179[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shut down
[2m2025-07-17 18:01:20.185[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Shutting down ExecutorService 'applicationTaskExecutor'
[2m2025-07-17 18:01:20.192[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Shutting down DiscoveryClient ...
[2m2025-07-17 18:01:20.193[0;39m [dev] [32m INFO[0;39m [35m7172[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Completed shut down of DiscoveryClient
[2m2025-07-17 18:02:14.682[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m The following profiles are active: dev
[2m2025-07-17 18:02:15.114[0;39m [dev] [33m WARN[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-17 18:02:15.199[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=ff1a2d2c-d19d-3931-a591-81b4348e9e38
[2m2025-07-17 18:02:15.430[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 7602 (http)
[2m2025-07-17 18:02:15.474[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 780 ms
[2m2025-07-17 18:02:15.521[0;39m [dev] [33m WARN[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-17 18:02:15.521[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-17 18:02:15.528[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.netflix.config.DynamicPropertyFactory [0;39m [2m:[0;39m DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@748d2277
[2m2025-07-17 18:02:16.088[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-17 18:02:16.089[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-17 18:02:16.161[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-17 18:02:16.161[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-17 18:02:16.513[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.web.DefaultSecurityFilterChain    [0;39m [2m:[0;39m Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3d4ecc67, org.springframework.security.web.context.SecurityContextPersistenceFilter@29dcdd1c, org.springframework.security.web.header.HeaderWriterFilter@10e56da9, org.springframework.security.web.authentication.logout.LogoutFilter@a323a5b, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@14a1769d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@524f5ea5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@919086, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@60dcf9ec, org.springframework.security.web.session.SessionManagementFilter@4f235e8e, org.springframework.security.web.access.ExceptionTranslationFilter@538b3c88, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@117b2cc6]
[2m2025-07-17 18:02:16.520[0;39m [dev] [33m WARN[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-17 18:02:16.520[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-17 18:02:16.623[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService 'applicationTaskExecutor'
[2m2025-07-17 18:02:17.190[0;39m [dev] [33m WARN[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mockingLoadBalancerClientRibbonWarnLogger[0;39m [2m:[0;39m You already have RibbonLoadBalancerClient on your classpath. It will be used by default. As Spring Cloud Ribbon is in maintenance mode. We recommend switching to BlockingLoadBalancerClient instead. In order to use it, set the value of `spring.cloud.loadbalancer.ribbon.enabled` to `false` or remove spring-cloud-starter-netflix-ribbon from your project.
[2m2025-07-17 18:02:17.260[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-17 18:02:17.276[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Initializing Eureka in region us-east-1
[2m2025-07-17 18:02:17.277[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Client configured to neither register nor query for data.
[2m2025-07-17 18:02:17.282[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Discovery Client initialized at timestamp 1752746537280 with initial instances count: 0
[2m2025-07-17 18:02:17.305[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initializing ...
[2m2025-07-17 18:02:17.307[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Adding new peer nodes [**********************************************/eureka/]
[2m2025-07-17 18:02:17.651[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-17 18:02:17.651[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-17 18:02:17.651[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-17 18:02:17.651[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-17 18:02:17.721[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Replica node URL:  **********************************************/eureka/
[2m2025-07-17 18:02:17.728[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Finished initializing remote region registries. All known remote regions: []
[2m2025-07-17 18:02:17.729[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initialized
[2m2025-07-17 18:02:17.736[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 2 endpoint(s) beneath base path '/actuator'
[2m2025-07-17 18:02:17.805[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application SS-PLATFORM-EUREKA with eureka with status UP
[2m2025-07-17 18:02:17.807[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[      Thread-20][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Setting the eureka configuration..
[2m2025-07-17 18:02:17.808[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[      Thread-20][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka data center value eureka.datacenter is not set, defaulting to default
[2m2025-07-17 18:02:17.808[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[      Thread-20][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka environment value eureka.environment is not set, defaulting to test
[2m2025-07-17 18:02:17.816[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[      Thread-20][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m isAws returned false
[2m2025-07-17 18:02:17.817[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[      Thread-20][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Initialized server context
[2m2025-07-17 18:02:17.817[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[      Thread-20][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Got 1 instances from neighboring DS node
[2m2025-07-17 18:02:17.817[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[      Thread-20][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Renew threshold is: 1
[2m2025-07-17 18:02:17.817[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[      Thread-20][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Changing status to UP
[2m2025-07-17 18:02:17.824[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[      Thread-20][0;39m [36me.s.EurekaServerInitializerConfiguration[0;39m [2m:[0;39m Started Eureka Server
[2m2025-07-17 18:02:17.843[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 7602 (http) with context path ''
[2m2025-07-17 18:02:17.844[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 7602
[2m2025-07-17 18:02:18.169[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m Started EurekaServerApplication in 4.649 seconds (JVM running for 5.339)
[2m2025-07-17 18:02:18.333[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[2)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-17 18:02:18.354[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[2)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 20 ms
[2m2025-07-17 18:02:41.173[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=false)
[2m2025-07-17 18:02:41.825[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=true)
[2m2025-07-17 18:02:47.834[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=false)
[2m2025-07-17 18:02:48.362[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[nio-7602-exec-6][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=true)
[2m2025-07-17 18:02:52.488[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=false)
[2m2025-07-17 18:02:53.012[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[nio-7602-exec-9][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=true)
[2m2025-07-17 18:02:53.912[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=false)
[2m2025-07-17 18:02:54.432[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=true)
[2m2025-07-17 18:03:17.818[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-17 18:03:56.589[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-17 18:03:57.118[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[nio-7602-exec-9][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-17 18:04:17.823[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-17 18:04:29.922[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application SS-PLATFORM-EUREKA with eureka with status DOWN
[2m2025-07-17 18:04:29.926[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shutting down ...
[2m2025-07-17 18:04:29.934[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[io-7602-exec-10][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status DOWN (replication=false)
[2m2025-07-17 18:04:29.936[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status DOWN (replication=false)
[2m2025-07-17 18:04:29.936[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[nio-7602-exec-9][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status DOWN (replication=false)
[2m2025-07-17 18:04:29.943[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shut down
[2m2025-07-17 18:04:29.946[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Shutting down ExecutorService 'applicationTaskExecutor'
[2m2025-07-17 18:04:29.949[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status DOWN (replication=false)
[2m2025-07-17 18:04:29.950[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Shutting down DiscoveryClient ...
[2m2025-07-17 18:04:29.951[0;39m [dev] [32m INFO[0;39m [35m29988[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Completed shut down of DiscoveryClient
[2m2025-07-17 18:05:47.156[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m The following profiles are active: dev
[2m2025-07-17 18:05:47.741[0;39m [dev] [33m WARN[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-17 18:05:47.848[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=ff1a2d2c-d19d-3931-a591-81b4348e9e38
[2m2025-07-17 18:05:48.140[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 7602 (http)
[2m2025-07-17 18:05:48.209[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 1035 ms
[2m2025-07-17 18:05:48.272[0;39m [dev] [33m WARN[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-17 18:05:48.272[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-17 18:05:48.279[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.netflix.config.DynamicPropertyFactory [0;39m [2m:[0;39m DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@c755b2
[2m2025-07-17 18:05:49.077[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-17 18:05:49.078[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-17 18:05:49.178[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-17 18:05:49.179[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-17 18:05:49.536[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.web.DefaultSecurityFilterChain    [0;39m [2m:[0;39m Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5ce3409b, org.springframework.security.web.context.SecurityContextPersistenceFilter@43e3a390, org.springframework.security.web.header.HeaderWriterFilter@75ad30c1, org.springframework.security.web.authentication.logout.LogoutFilter@3d512652, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@2fba0dac, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@50dc49e1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@653a5967, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1d283d1, org.springframework.security.web.session.SessionManagementFilter@8f39224, org.springframework.security.web.access.ExceptionTranslationFilter@362a561e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@63de4fa]
[2m2025-07-17 18:05:49.545[0;39m [dev] [33m WARN[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-17 18:05:49.546[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-17 18:05:49.667[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService 'applicationTaskExecutor'
[2m2025-07-17 18:05:50.316[0;39m [dev] [33m WARN[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mockingLoadBalancerClientRibbonWarnLogger[0;39m [2m:[0;39m You already have RibbonLoadBalancerClient on your classpath. It will be used by default. As Spring Cloud Ribbon is in maintenance mode. We recommend switching to BlockingLoadBalancerClient instead. In order to use it, set the value of `spring.cloud.loadbalancer.ribbon.enabled` to `false` or remove spring-cloud-starter-netflix-ribbon from your project.
[2m2025-07-17 18:05:50.376[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-17 18:05:50.391[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Initializing Eureka in region us-east-1
[2m2025-07-17 18:05:50.392[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Client configured to neither register nor query for data.
[2m2025-07-17 18:05:50.396[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Discovery Client initialized at timestamp 1752746750395 with initial instances count: 0
[2m2025-07-17 18:05:50.415[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initializing ...
[2m2025-07-17 18:05:50.417[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Adding new peer nodes [**********************************************/eureka/]
[2m2025-07-17 18:05:50.694[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-17 18:05:50.694[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-17 18:05:50.694[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-17 18:05:50.695[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-17 18:05:50.761[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Replica node URL:  **********************************************/eureka/
[2m2025-07-17 18:05:50.766[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Finished initializing remote region registries. All known remote regions: []
[2m2025-07-17 18:05:50.767[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initialized
[2m2025-07-17 18:05:50.775[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 2 endpoint(s) beneath base path '/actuator'
[2m2025-07-17 18:05:50.831[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application SS-PLATFORM-EUREKA with eureka with status UP
[2m2025-07-17 18:05:50.832[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Setting the eureka configuration..
[2m2025-07-17 18:05:50.833[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka data center value eureka.datacenter is not set, defaulting to default
[2m2025-07-17 18:05:50.833[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka environment value eureka.environment is not set, defaulting to test
[2m2025-07-17 18:05:50.839[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m isAws returned false
[2m2025-07-17 18:05:50.839[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Initialized server context
[2m2025-07-17 18:05:50.839[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Got 1 instances from neighboring DS node
[2m2025-07-17 18:05:50.839[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Renew threshold is: 1
[2m2025-07-17 18:05:50.839[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Changing status to UP
[2m2025-07-17 18:05:50.847[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36me.s.EurekaServerInitializerConfiguration[0;39m [2m:[0;39m Started Eureka Server
[2m2025-07-17 18:05:50.863[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 7602 (http) with context path ''
[2m2025-07-17 18:05:50.863[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 7602
[2m2025-07-17 18:05:51.169[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m Started EurekaServerApplication in 5.229 seconds (JVM running for 5.954)
[2m2025-07-17 18:05:52.130[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[4)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-17 18:05:52.134[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[4)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 4 ms
[2m2025-07-17 18:05:54.443[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=false)
[2m2025-07-17 18:05:55.087[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=true)
[2m2025-07-17 18:06:04.965[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=false)
[2m2025-07-17 18:06:05.427[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[nio-7602-exec-7][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=false)
[2m2025-07-17 18:06:05.495[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=true)
[2m2025-07-17 18:06:05.496[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=true)
[2m2025-07-17 18:06:06.661[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[io-7602-exec-10][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=false)
[2m2025-07-17 18:06:07.192[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=true)
[2m2025-07-17 18:06:50.853[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-17 18:07:50.862[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-17 18:07:57.814[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[io-7602-exec-10][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-17 18:07:58.344[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-17 18:08:50.864[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-17 18:09:50.879[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-17 18:10:51.034[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 155ms
[2m2025-07-17 18:11:51.035[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-17 18:12:51.041[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-17 18:13:51.050[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-17 18:14:51.059[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-17 18:15:51.063[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-17 18:16:51.064[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-17 18:17:51.067[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-17 18:18:51.069[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-17 18:19:51.079[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-17 18:20:50.774[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-17 18:20:51.083[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-17 18:21:51.085[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-17 18:22:51.087[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-17 18:23:51.091[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-17 18:24:51.093[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-17 18:25:51.106[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-17 18:26:51.118[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-17 18:27:51.123[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-17 18:28:51.138[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-17 18:29:51.142[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-17 18:30:51.154[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-17 18:31:51.165[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-17 18:32:51.171[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-17 18:33:51.179[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-17 18:34:51.181[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-17 18:35:50.366[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status DOWN (replication=false)
[2m2025-07-17 18:35:50.369[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application SS-PLATFORM-EUREKA with eureka with status DOWN
[2m2025-07-17 18:35:50.370[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[io-7602-exec-10][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status DOWN (replication=false)
[2m2025-07-17 18:35:50.377[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shutting down ...
[2m2025-07-17 18:35:50.379[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.batchSize.Will start computing stats again.
[2m2025-07-17 18:35:50.381[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.executionTime.Will start computing stats again.
[2m2025-07-17 18:35:50.382[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.executionTime.Will start computing stats again.
[2m2025-07-17 18:35:50.382[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.batchSize.Will start computing stats again.
[2m2025-07-17 18:35:50.390[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status DOWN (replication=false)
[2m2025-07-17 18:35:50.398[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shut down
[2m2025-07-17 18:35:50.403[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status DOWN (replication=false)
[2m2025-07-17 18:35:50.405[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Shutting down ExecutorService 'applicationTaskExecutor'
[2m2025-07-17 18:35:50.409[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status DOWN (replication=false)
[2m2025-07-17 18:35:50.410[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Shutting down DiscoveryClient ...
[2m2025-07-17 18:35:50.411[0;39m [dev] [32m INFO[0;39m [35m8476[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Completed shut down of DiscoveryClient
