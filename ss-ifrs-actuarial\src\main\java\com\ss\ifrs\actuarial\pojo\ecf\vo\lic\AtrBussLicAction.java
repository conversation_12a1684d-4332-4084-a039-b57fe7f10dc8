package com.ss.ifrs.actuarial.pojo.ecf.vo.lic;

import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Data;

import java.util.Date;

@Data
@Tab("atr_buss_lic_action")
public class AtrBussLicAction {

    private String actionNo;

    private Long entityId;

    private String yearMonth;

    private String businessSourceCode;

    private String status;

    private String confirmIs;

    private Long confirmUser;

    private Date confirmTime;

    private Long creatorId;

    private Date createTime;

    private Long updatorId;

    private Date updateTime;

}
