package com.ss.ifrs.actuarial.service.impl;

import com.alibaba.excel.exception.ExcelAnalysisException;
import com.ss.ifrs.actuarial.dao.conf.*;
import com.ss.ifrs.actuarial.pojo.atrconf.po.*;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.*;
import com.ss.ifrs.actuarial.service.AtrConfQuotaDefService;
import com.ss.ifrs.actuarial.service.AtrConfQuotaDymService;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ClassUtil;
import com.ss.library.utils.ExcelImportUtil;
import com.ss.library.utils.StringUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AtrConfQuotaDymServiceImpl implements AtrConfQuotaDymService {

    @Autowired
    AtrConfQuotaDymDao atrConfQuotaDymDao;

    @Autowired
    AtrConfQuotaDymDetailDao atrConfQuotaDymDetailDao;

    @Autowired
    AtrConfQuotaDymDetailDevDao atrConfQuotaDymDetailDevDao;

    @Autowired
    AtrConfQuotaDymHisDao atrConfQuotaDymHisDao;

    @Autowired
    AtrConfQuotaDymDetailHisDao atrConfQuotaDymDetailHisDao;

    @Autowired
    AtrConfQuotaDymDtlDevHisDao atrConfQuotaDymDtlDevHisDao;

    @Autowired
    AtrConfQuotaDefService atrConfQuotaDefService;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void addOrUpdate(AtrConfQuotaDymMainVo atrConfQuotaDymVo, Long userId) {
        AtrConfQuotaDymVo confQuotaDymVo = new AtrConfQuotaDymVo();
        confQuotaDymVo.setEntityId(atrConfQuotaDymVo.getEntityId());
        confQuotaDymVo.setDimensionValue(atrConfQuotaDymVo.getDimensionValue());
        if("add".equals(atrConfQuotaDymVo.getType()) ){
            List<AtrConfQuotaDymVo> atrConfQuotaDymVoList = atrConfQuotaDymVo.getConfQuotaDymVoList();
            AtrConfQuotaDym atrConfQuotaDym = new AtrConfQuotaDym();
            atrConfQuotaDym = ClassUtil.convert(atrConfQuotaDymVo, AtrConfQuotaDym.class);
            atrConfQuotaDym.setSerialNo(1);
            atrConfQuotaDym.setAuditState(CommonConstant.AuditStatus.TO_BE_AUDITED);
            atrConfQuotaDym.setCreateTime(new Date());
            if ("G".equals(atrConfQuotaDymVo.getDimension())) {
                atrConfQuotaDym.setLoaCode(atrConfQuotaDymVo.getDimensionValue());
            } else {
                atrConfQuotaDym.setLoaCode(atrConfQuotaDymVo.getDimensionValue());
            }
            atrConfQuotaDym.setValidIs(CommonConstant.ValidStatus.VALID);
            for(AtrConfQuotaDymVo vo : atrConfQuotaDymVoList) {
                atrConfQuotaDym.setQuotaDefId(vo.getQuotaDefId());
                atrConfQuotaDymDao.save(atrConfQuotaDym);
                for(AtrConfQuotaDymDetailVo dymDetailVo: vo.getConfQuotaDymDetailVoList()) {
                    AtrConfQuotaDymDetail confQuotaDymDetail = new AtrConfQuotaDymDetail();
                    confQuotaDymDetail.setQuotaId(atrConfQuotaDym.getQuotaId());
                    confQuotaDymDetail.setAccidentYearMonth(dymDetailVo.getAccidentYearMonth());
                    confQuotaDymDetail.setQuotaValue(dymDetailVo.getQuotaValue());
                    confQuotaDymDetail.setSerialNo(atrConfQuotaDym.getSerialNo());
                    atrConfQuotaDymDetailDao.save(confQuotaDymDetail);
                    if(ObjectUtils.isNotEmpty(dymDetailVo.getConfQuotaDymDetailDevVoList())) {
                        int devPeriod = 0;
                        for(AtrConfQuotaDymDetailDevVo dymDetailDevVo : dymDetailVo.getConfQuotaDymDetailDevVoList()) {
                            AtrConfQuotaDymDetailDev quotaDymDetailDev = new AtrConfQuotaDymDetailDev();
                            quotaDymDetailDev.setQuotaDetailId(confQuotaDymDetail.getQuotaDetailId());
                            quotaDymDetailDev.setDevPeriod(new Long(devPeriod++));
                            quotaDymDetailDev.setQuotaValue(dymDetailDevVo.getQuotaValue());
                            quotaDymDetailDev.setSerialNo(confQuotaDymDetail.getSerialNo());
                            atrConfQuotaDymDetailDevDao.save(quotaDymDetailDev);
                        }
                    }
                }
            }
            this.dealSaveDetailHis(atrConfQuotaDym, true, userId, CommonConstant.OperType.ADD);
        } else {
            Integer serialNo =  atrConfQuotaDymDao.findMaxSerialNo(confQuotaDymVo) ;
            serialNo = ObjectUtils.isEmpty(serialNo) ? 1 : serialNo +1;
            List<AtrConfQuotaDymVo> atrConfQuotaDymVoList = atrConfQuotaDymVo.getConfQuotaDymVoList();
            AtrConfQuotaDym atrConfQuotaDym = new AtrConfQuotaDym();
            atrConfQuotaDym = ClassUtil.convert(atrConfQuotaDymVo, AtrConfQuotaDym.class);
            atrConfQuotaDym.setSerialNo(serialNo);
            atrConfQuotaDym.setAuditState(CommonConstant.AuditStatus.TO_BE_AUDITED);
            if ("G".equals(atrConfQuotaDymVo.getDimension())) {
                atrConfQuotaDym.setLoaCode(atrConfQuotaDymVo.getDimensionValue());
            }
            atrConfQuotaDym.setValidIs(CommonConstant.ValidStatus.VALID);
            this.deleteByDimensionVo(atrConfQuotaDymVo);
            for(AtrConfQuotaDymVo vo : atrConfQuotaDymVoList) {
                atrConfQuotaDym.setQuotaDefId(vo.getQuotaDefId());
                atrConfQuotaDymDao.save(atrConfQuotaDym);
                for(AtrConfQuotaDymDetailVo dymDetailVo: vo.getConfQuotaDymDetailVoList()) {
                    AtrConfQuotaDymDetail confQuotaDymDetail = new AtrConfQuotaDymDetail();
                    confQuotaDymDetail.setQuotaId(atrConfQuotaDym.getQuotaId());
                    confQuotaDymDetail.setAccidentYearMonth(dymDetailVo.getAccidentYearMonth());
                    confQuotaDymDetail.setQuotaValue(dymDetailVo.getQuotaValue());
                    confQuotaDymDetail.setSerialNo(atrConfQuotaDym.getSerialNo());
                    atrConfQuotaDymDetailDao.save(confQuotaDymDetail);

                    if(ObjectUtils.isNotEmpty(dymDetailVo.getConfQuotaDymDetailDevVoList())) {
                        int devPeriod = 0;
                        for(AtrConfQuotaDymDetailDevVo dymDetailDevVo : dymDetailVo.getConfQuotaDymDetailDevVoList()) {
                            AtrConfQuotaDymDetailDev quotaDymDetailDev = new AtrConfQuotaDymDetailDev();
                            quotaDymDetailDev.setQuotaDetailId(confQuotaDymDetail.getQuotaDetailId());
                            quotaDymDetailDev.setDevPeriod(new Long(devPeriod++));
                            quotaDymDetailDev.setQuotaValue(dymDetailDevVo.getQuotaValue());
                            quotaDymDetailDev.setSerialNo(confQuotaDymDetail.getSerialNo());
                            atrConfQuotaDymDetailDevDao.save(quotaDymDetailDev);
                        }
                    }
                }
            }
            this.dealSaveDetailHis(atrConfQuotaDym, true, userId, CommonConstant.OperType.MODIFY);
        }

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void delete(AtrConfQuotaDymMainVo atrConfQuotaDymVo, Long userId) {
        List<AtrConfQuotaDymVo> confQuotaDymVoList = atrConfQuotaDymDao.findByDimensionVo(atrConfQuotaDymVo);
        if (ObjectUtils.isNotEmpty(confQuotaDymVoList) && confQuotaDymVoList.size() >0) {
            AtrConfQuotaDym atrConfQuotaDym = ClassUtil.convert(confQuotaDymVoList.get(0), AtrConfQuotaDym.class);
            atrConfQuotaDym.setSerialNo(ObjectUtils.isEmpty(atrConfQuotaDym.getSerialNo()) ? 1 :  atrConfQuotaDym.getSerialNo() +1);
            this.dealSaveDetailHis(atrConfQuotaDym, true, userId, CommonConstant.OperType.MODIFY);
            this.deleteByDimensionVo(atrConfQuotaDymVo);
        }
    }



    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deleteByDimensionVo(AtrConfQuotaDymMainVo atrConfQuotaDymVo) {
        List<AtrConfQuotaDymVo> confQuotaDymVoList = atrConfQuotaDymDao.findByDimensionVo(atrConfQuotaDymVo);
        if (ObjectUtils.isNotEmpty(confQuotaDymVoList)) {
            atrConfQuotaDymDetailDevDao.deleteByDimension(atrConfQuotaDymVo);
            atrConfQuotaDymDetailDao.deleteByDimension(atrConfQuotaDymVo);
            atrConfQuotaDymDao.deleteByDimension(atrConfQuotaDymVo);
        }
    }

    @Override
    public AtrConfQuotaDymVo findHisById(Long riskRefHisId) {
        return null;
    }

    @Override
    public Page<AtrConfQuotaDymVo> searchPage(AtrConfQuotaDymVo atrConfQuotaDymVo, Pageable pageParam) {
        Page<AtrConfQuotaDymVo> confMortgQuotaVoPage = atrConfQuotaDymDao.fuzzySearchPage(atrConfQuotaDymVo, pageParam);
        return confMortgQuotaVoPage;
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void audit(AtrConfQuotaDymMainVo atrConfQuotaDymVo, Long userId) {
        List<AtrConfQuotaDymVo> confQuotaDymVoList = atrConfQuotaDymDao.findByDimensionVo(atrConfQuotaDymVo);
        if (ObjectUtils.isNotEmpty(confQuotaDymVoList)) {
            AtrConfQuotaDymVo confQuotaDymVo = ClassUtil.convert(atrConfQuotaDymVo, AtrConfQuotaDymVo.class);
            Integer serialNo = atrConfQuotaDymDao.findMaxSerialNo(confQuotaDymVo);
            Date currentDate = new Date();
            atrConfQuotaDymVo.setCheckedTime(currentDate);
            atrConfQuotaDymVo.setCheckedId(userId);
            atrConfQuotaDymVo.setSerialNo(serialNo);
            atrConfQuotaDymDao.updateByDimensionVo(atrConfQuotaDymVo);
            this.dealSaveDetailHis(ClassUtil.convert(atrConfQuotaDymVo, AtrConfQuotaDym.class), false, userId, CommonConstant.OperType.MODIFY);

        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public String disableValid(AtrConfQuotaDymMainVo atrConfQuotaDymVo, Long userId) {
        String validIs = atrConfQuotaDymVo.getValidIs();
        atrConfQuotaDymVo.setValidIs(null);
        List<AtrConfQuotaDymVo> confQuotaDymVoList = atrConfQuotaDymDao.findByDimensionVo(atrConfQuotaDymVo);

        AtrConfQuotaDymVo confQuotaDymVo = ClassUtil.convert(atrConfQuotaDymVo, AtrConfQuotaDymVo.class);
        Integer serialNo = atrConfQuotaDymDao.findMaxSerialNo(confQuotaDymVo);
        if (null != confQuotaDymVoList && confQuotaDymVoList.size() > 0) {
            try {
                Date currentDate = new Date();
                atrConfQuotaDymVo.setValidIs(validIs);
                atrConfQuotaDymVo.setAuditState("0");
                atrConfQuotaDymVo.setCheckedMsg("");
                atrConfQuotaDymVo.setUpdateTime(currentDate);
                atrConfQuotaDymVo.setSerialNo(serialNo);
                atrConfQuotaDymDao.updateByDimensionVo(atrConfQuotaDymVo);

                this.dealSaveDetailHis(ClassUtil.convert(atrConfQuotaDymVo, AtrConfQuotaDym.class), false, userId, CommonConstant.OperType.MODIFY);
                return atrConfQuotaDymVo.getValidIs();
            } catch (UnexpectedRollbackException e) {
                //LOG.error(e.getLocalizedMessage(), e);
                throw e;
            }
        } else {
            return atrConfQuotaDymVo.getValidIs();
        }
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public String batchAudit(ArrayList<AtrConfQuotaDymMainVo> confQuotaDymVos, Long userId) {
        /**
         * 业务逻辑：
         *  1、审核状态变为待审核状态 0-待审核；
         */
        try {
            confQuotaDymVos.stream().forEach(confQuotaDymVo -> {
                // 调用单个审核
                this.audit(confQuotaDymVo, userId);
            });
            return ResCodeConstant.ResCode.SUCCESS;
        } catch (UnexpectedRollbackException e) {
            throw e;
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void excelImport(MultipartFile file, AtrConfQuotaDymVo atrConfQuotaDymVo, Long userId) throws Exception {
        try {
            Map<Integer, List<Map<String, String>>> sheetDataMap = ExcelImportUtil.addExcel(file);
        } catch (ExcelAnalysisException e) {
            throw e;
        }
    }

    @Override
    public String findValidateDimension(AtrConfQuotaDymVo confQuotaDymVo) {
        // validateFlag：0表示不存在相关数据，1表示存在。默认不存在
        String validateFlag = "0";
        if(ObjectUtils.isNotEmpty(confQuotaDymVo) && StringUtil.isNotEmpty(confQuotaDymVo.getDimensionValue())){
            AtrConfQuotaDym confQuotaDym = new AtrConfQuotaDym();
            confQuotaDym.setEntityId(confQuotaDymVo.getEntityId());
            confQuotaDym.setDimensionValue(confQuotaDymVo.getDimensionValue());
            Long count = atrConfQuotaDymDao.count(confQuotaDym);
            if(null != count && count > 0){
                validateFlag = "1";
            }
        }
        return validateFlag;
    }

    @Override
    public List<AtrConfQuotaDymVo> findConfQuotaByVo(AtrConfQuotaDymVo confQuotaDymVo) {
        List<AtrConfQuotaDymVo> confQuotaGroupDefVoList = new ArrayList<>();
        return confQuotaGroupDefVoList;
    }

    @Override
    public Map<String, Object> findQuotaByDimensionVo(AtrConfQuotaDymMainVo confQuotaDymVo) {
        AtrConfQuotaDefVo confQuotaDefVo = ClassUtil.convert(confQuotaDymVo, AtrConfQuotaDefVo.class);
        List<AtrConfQuotaDefVo> confQuotaDefVoList = atrConfQuotaDefService.findQuotaDefList(confQuotaDefVo);
        Map<Long, AtrConfQuotaDefVo> map = confQuotaDefVoList.stream().collect(Collectors.toMap(AtrConfQuotaDefVo:: getQuotaDefId, AtrConfQuotaDefVo-> AtrConfQuotaDefVo));


        List<AtrConfQuotaDymVo> confQuotaDymVoList = atrConfQuotaDymDao.findByDimensionVo(confQuotaDymVo);
        List<AtrConfQuotaDymDetailVo> confQuotaDymDetailVoList = atrConfQuotaDymDetailDao.findByDimensionVo(confQuotaDymVo);
        List<AtrConfQuotaDymDetailDevVo> confQuotaDymDetailDevVoList = atrConfQuotaDymDetailDevDao.findByDimensionVo(confQuotaDymVo);

        List<String> commonYearMonth = confQuotaDymDetailVoList.stream().filter(detailVo -> "0".equals(detailVo.getQuotaType())).map(AtrConfQuotaDymDetailVo::getAccidentYearMonth).distinct().collect(Collectors.toList());
        Map<Long, Object> devMap = new HashMap<>();

        //普通假设
        List<AtrConfQuotaDymVo> commonConfQuotaDymVoList = new ArrayList<>();
        //发展期假设
        List<AtrConfQuotaDymVo> devConfQuotaDymVoList = new ArrayList<>();

        confQuotaDymVoList.forEach(confQuotaDymEachVo -> {
            if ("0".equals(map.get(confQuotaDymEachVo.getQuotaDefId()).getQuotaType())) {
                confQuotaDymEachVo.setQuotaCode(map.get(confQuotaDymEachVo.getQuotaDefId()).getQuotaCode());
                confQuotaDymEachVo.setConfQuotaDymDetailVoList(confQuotaDymDetailVoList.stream().filter(detailVo -> confQuotaDymEachVo.getQuotaId().equals(detailVo.getQuotaId())).collect(Collectors.toList()));
                commonConfQuotaDymVoList.add(confQuotaDymEachVo);
            } else if ("1".equals(map.get(confQuotaDymEachVo.getQuotaDefId()).getQuotaType())) {
                Map<String, Object> devDataMap = new HashMap<>();
                //List<String> devYearMonth = confQuotaDymDetailVoList.stream().filter(detailVo -> "1".equals(detailVo.getQuotaType())).map(AtrConfQuotaDymDetailVo::getDamageYearMonth).distinct().collect(Collectors.toList());
                //List<Long> devPeriod = confQuotaDymDetailDevVoList.stream().map(AtrConfQuotaDymDetailDevVo:: getDevPeriod).distinct().sorted().collect(Collectors.toList());

                List<AtrConfQuotaDymDetailDevVo> atrConfQuotaDymDetailDevVoList = new ArrayList<>();
                List<AtrConfQuotaDymDetailVo> confQuotaDymDetailChildVo = confQuotaDymDetailVoList.stream().filter(detailVo -> confQuotaDymEachVo.getQuotaId().equals(detailVo.getQuotaId())).collect(Collectors.toList());
                confQuotaDymDetailChildVo.forEach(detailChildVo -> {
                    detailChildVo.setConfQuotaDymDetailDevVoList(confQuotaDymDetailDevVoList.stream().filter(detailDevVo -> detailChildVo.getQuotaDetailId().equals(detailDevVo.getQuotaDetailId())).collect(Collectors.toList()));
                    atrConfQuotaDymDetailDevVoList.addAll(detailChildVo.getConfQuotaDymDetailDevVoList());
                });

                confQuotaDymEachVo.setConfQuotaDymDetailVoList(confQuotaDymDetailChildVo);
                devConfQuotaDymVoList.add(confQuotaDymEachVo);

                devDataMap.put("devYearMonth", confQuotaDymDetailChildVo.stream().filter(detailVo -> "1".equals(detailVo.getQuotaType())).map(AtrConfQuotaDymDetailVo::getAccidentYearMonth).distinct().collect(Collectors.toList()));
                devDataMap.put("devPeriod", atrConfQuotaDymDetailDevVoList.stream().map(AtrConfQuotaDymDetailDevVo:: getDevPeriod).distinct().sorted().collect(Collectors.toList()));
                devMap.put(confQuotaDymEachVo.getQuotaDefId(), devDataMap);

            }
        });
        Map<String, Object> resultMap = new HashMap<>(8);
        resultMap.put("commonYearMonth", commonYearMonth);
        resultMap.put("dev", devMap);
        resultMap.put("commonQuota", commonConfQuotaDymVoList);
        resultMap.put("devQuota", devConfQuotaDymVoList);
        return resultMap;
    }

    @Override
    public AtrConfQuotaDymVo findAtrConfQuotaVo(AtrConfQuotaDymVo atrConfQuotaDymVo) {
        AtrConfQuotaDymVo vo = atrConfQuotaDymDao.findAuditInformation(atrConfQuotaDymVo);
        return vo;
    }

    @Override
    public Long countQuota(AtrConfQuotaDymVo confQuotaDymVo) {
        return atrConfQuotaDymDao.countQuota(confQuotaDymVo);

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Map<String, Object> loadPrePeriodQuota(AtrConfQuotaDymVo confQuotaDymVo) {
        Map<String,Object> map = new HashMap<String, Object>();
        String preYearMonth = atrConfQuotaDymDao.selectPreviousPeriod(confQuotaDymVo);
        if (ObjectUtils.isEmpty(preYearMonth)) {
            return map;
        }
        confQuotaDymVo.setPerYearMonth(preYearMonth);
        atrConfQuotaDymDao.loadPrePeriodQuota(confQuotaDymVo);
        atrConfQuotaDymDetailDao.loadPrePeriodQuotaDymDetail(confQuotaDymVo);
        atrConfQuotaDymDetailDevDao.loadPrePeriodQuotaDymDetailDev(confQuotaDymVo);
        return map;
    }


    /**
     * <AUTHOR>
     * @Date 2023/2/17
     * @Description 保存明细轨迹信息
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void dealSaveDetailHis(AtrConfQuotaDym po,Boolean serialUpdateFlag, Long userId, String operType) {
        AtrConfQuotaDymHis confQuotaDymHis = new AtrConfQuotaDymHis();
        ClassUtil.copyProperties(po, confQuotaDymHis);
        confQuotaDymHis.setOperId(userId);
        confQuotaDymHis.setOperTime(new Date());
        confQuotaDymHis.setOperType(operType);
        atrConfQuotaDymHisDao.saveHis(confQuotaDymHis);
        if (serialUpdateFlag) {
            atrConfQuotaDymDetailHisDao.saveHis(confQuotaDymHis);
            atrConfQuotaDymDtlDevHisDao.saveHis(confQuotaDymHis);
        }
    }
}
