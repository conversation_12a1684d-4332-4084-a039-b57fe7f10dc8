package com.ss.ifrs.actuarial.service.impl;

import com.alibaba.excel.exception.ExcelAnalysisException;
import com.ss.ifrs.actuarial.dao.conf.AtrConfMortgQuotaDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfMortgQuotaHisDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfMortgQuotaPeriodDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfMortgQuotaPeriodHisDao;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfMortgQuota;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfMortgQuotaHis;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfMortgQuotaPeriod;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfMortgQuotaPeriodHis;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfMortgQuotaPeriodVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfMortgQuotaVo;
import com.ss.ifrs.actuarial.service.AtrConfMortgQuotaService;
import com.ss.ifrs.actuarial.service.AtrImportService;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ClassUtil;
import com.ss.platform.util.ExcelExportUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class AtrConfMortgQuotaServiceImpl implements AtrConfMortgQuotaService {

    @Autowired
    AtrConfMortgQuotaDao atrConfMortgQuotaDao;

    @Autowired
    AtrConfMortgQuotaPeriodDao atrConfMortgQuotaPeriodDao;

    @Autowired
    AtrConfMortgQuotaHisDao atrConfMortgQuotaHisDao;

    @Autowired
    AtrConfMortgQuotaPeriodHisDao atrConfMortgQuotaPeriodHisDao;

    @Autowired
    private AtrImportService atrImportService;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void addOrUpdate(AtrConfMortgQuotaVo atrConfMortgQuotaVo, Long userId) {
        Date nowTime = new Date();
        AtrConfMortgQuota atrConfMortgQuota = null;
        if (ObjectUtils.isEmpty(atrConfMortgQuotaVo.getMortgQuotaId())) {// 如果是增加操作则判断是否存在数据
            atrConfMortgQuota = ClassUtil.convert(atrConfMortgQuotaVo, AtrConfMortgQuota.class);
            atrConfMortgQuota.setCreateTime(nowTime);
            atrConfMortgQuota.setCreatorId(userId);
            atrConfMortgQuota.setSerialNo(1L);
            atrConfMortgQuotaDao.save(atrConfMortgQuota);

            atrConfMortgQuotaPeriodDao.deleteByMortgQuotaId(atrConfMortgQuota.getMortgQuotaId());

            // 保存因子值数据
            List<AtrConfMortgQuotaPeriodVo> confMortgQuotaPeriodVoList = atrConfMortgQuotaVo.getConfMortgQuotaPeriodVoList();
            if (CollectionUtils.isNotEmpty(confMortgQuotaPeriodVoList)) {
                List<AtrConfMortgQuotaPeriod> confMortgQuotaPeriodList = ClassUtil.convert(confMortgQuotaPeriodVoList, AtrConfMortgQuotaPeriod.class);
                for (AtrConfMortgQuotaPeriod mortgQuotaPeriod : confMortgQuotaPeriodList) {
                    mortgQuotaPeriod.setMortgQuotaId(atrConfMortgQuota.getMortgQuotaId());
                    mortgQuotaPeriod.setSerialNo(1L);
                    atrConfMortgQuotaPeriodDao.save(mortgQuotaPeriod);
                    this.saveHistoryDataPeriod(mortgQuotaPeriod);
                }
            }

            // 保存历史轨迹数据
            this.saveHistoryData(atrConfMortgQuota, userId, CommonConstant.OperType.ADD);
           // ExpConfBenefitFactorHis his = saveHistoryData(expBenefitFactor, userId, CommonConstant.OperType.ADD);
            //saveHistoryDataRef(expBenefitFactor.getBenefitFactorId(), his.getBenefitFactorHisId());

        } else {
            atrConfMortgQuota = atrConfMortgQuotaDao.findById(atrConfMortgQuotaVo.getMortgQuotaId());
            Long serialNo = null;
            if(ObjectUtils.isNotEmpty(atrConfMortgQuota.getSerialNo())){
                serialNo = atrConfMortgQuota.getSerialNo();
            }
            atrConfMortgQuota = ClassUtil.convert(atrConfMortgQuotaVo, AtrConfMortgQuota.class);
            atrConfMortgQuota.setUpdateTime(nowTime);
            atrConfMortgQuota.setUpdatorId(userId);
            atrConfMortgQuota.setAuditState("0");
            atrConfMortgQuota.setSerialNo(ObjectUtils.isEmpty(serialNo) ? 1 : ++serialNo);
            atrConfMortgQuotaDao.updateById(atrConfMortgQuota);

            atrConfMortgQuotaPeriodDao.deleteByMortgQuotaId(atrConfMortgQuota.getMortgQuotaId());

            // 保存因子值数据
            List<AtrConfMortgQuotaPeriodVo> confMortgQuotaPeriodVoList = atrConfMortgQuotaVo.getConfMortgQuotaPeriodVoList();
            if (CollectionUtils.isNotEmpty(confMortgQuotaPeriodVoList)) {
                List<AtrConfMortgQuotaPeriod> confMortgQuotaPeriodList = ClassUtil.convert(confMortgQuotaPeriodVoList, AtrConfMortgQuotaPeriod.class);
                for (AtrConfMortgQuotaPeriod mortgQuotaPeriod : confMortgQuotaPeriodList) {
                    mortgQuotaPeriod.setMortgQuotaId(atrConfMortgQuota.getMortgQuotaId());
                    mortgQuotaPeriod.setSerialNo(atrConfMortgQuota.getSerialNo());
                    atrConfMortgQuotaPeriodDao.save(mortgQuotaPeriod);
                    this.saveHistoryDataPeriod(mortgQuotaPeriod);
                }
            }
            // 保存历史轨迹数据
            this.saveHistoryData(atrConfMortgQuota, userId, CommonConstant.OperType.MODIFY);

            //ExpConfBenefitFactorHis his = saveHistoryData(expBenefitFactor, userId, CommonConstant.OperType.MODIFY);
            //saveUpdatorHistoryDataRef(expConfBenefitFactorVo.getBenefitFactorId(), his.getBenefitFactorHisId(), userId, expBenefitFactor.getSerialNo());
        }
    }

    @Override
    public void saveList(List<AtrConfMortgQuotaVo> atrConfQuotaVos, Long userId) {

    }

    @Override
    public AtrConfMortgQuotaVo findById(Long mortgQuotaId) {
        AtrConfMortgQuotaVo atrConfMortgQuotaVo = atrConfMortgQuotaDao.findByPkId(mortgQuotaId);
        if (ObjectUtils.isNotEmpty(atrConfMortgQuotaVo)) {
            AtrConfMortgQuotaPeriod atrConfMortgQuotaPeriod = new AtrConfMortgQuotaPeriod();
            atrConfMortgQuotaPeriod.setMortgQuotaId(mortgQuotaId);
            List<AtrConfMortgQuotaPeriod> confMortgQuotaPeriodList = atrConfMortgQuotaPeriodDao.findList(atrConfMortgQuotaPeriod);
            atrConfMortgQuotaVo.setConfMortgQuotaPeriodVoList(ClassUtil.convert(confMortgQuotaPeriodList, AtrConfMortgQuotaPeriodVo.class));
        }
       return atrConfMortgQuotaVo;
    }

    @Override
    public AtrConfMortgQuotaVo findHisById(Long riskRefHisId) {
        return null;
    }

    @Override
    public Page<AtrConfMortgQuotaVo> searchPage(AtrConfMortgQuotaVo atrConfMortgQuotaVo, Pageable pageParam) {
        Page<AtrConfMortgQuotaVo> confMortgQuotaVoPage = atrConfMortgQuotaDao.fuzzySearchPage(atrConfMortgQuotaVo, pageParam);
        return confMortgQuotaVoPage;
    }



    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void delete(Long mortgQuotaId, Long userId) {
        //写入轨迹表
        AtrConfMortgQuota atrConfMortgQuota = atrConfMortgQuotaDao.findById(mortgQuotaId);
        if (ObjectUtils.isNotEmpty(atrConfMortgQuota)) {
            Long serialNo = atrConfMortgQuota.getSerialNo();
            atrConfMortgQuota.setSerialNo(ObjectUtils.isEmpty(serialNo) ? 1 : ++serialNo);
            this.saveHistoryData(atrConfMortgQuota, userId, CommonConstant.OperType.DELETE);
            atrConfMortgQuotaPeriodDao.deleteByMortgQuotaId(mortgQuotaId);
            atrConfMortgQuotaDao.deleteById(mortgQuotaId);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void updateValid(AtrConfMortgQuotaVo atrConfMortgQuotaVo, Long userId) {
        AtrConfMortgQuota po = new AtrConfMortgQuota();
        Long mortgQuotaId = atrConfMortgQuotaVo.getMortgQuotaId();
        if (mortgQuotaId != null) {
            po = ClassUtil.convert(this.findById(mortgQuotaId), AtrConfMortgQuota.class);
            po.setValidIs(atrConfMortgQuotaVo.getValidIs());
        }
        if (po != null) {
            po.setUpdateTime(new Date());
            //待审核时，审核意见应为空
            po.setAuditState("0");
            po.setCheckedMsg(null);
            po.setCheckedId(null);
            po.setCheckedTime(null);
            atrConfMortgQuotaDao.updateById(po);
            this.saveHistoryData(po, userId, CommonConstant.OperType.MODIFY);

        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void auditList(List<AtrConfMortgQuotaVo> atrConfQuotaVos, Long userId) {
        List<AtrConfMortgQuota> atrConfMortgQuotaList = ClassUtil.convert(atrConfQuotaVos, AtrConfMortgQuota.class);
        atrConfMortgQuotaList.stream().forEach(atrConfMortgQuota->{
            //this.dealSaveHis(atrConfMortgQuota, userId, CommonConstant.OperType.AUDIT);
            atrConfMortgQuotaDao.updateById(atrConfMortgQuota);
        });
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void audit(AtrConfMortgQuotaVo atrConfMortgQuotaVo, Long userId) {
        AtrConfMortgQuota atrConfMortgQuota = atrConfMortgQuotaDao.findById(atrConfMortgQuotaVo.getMortgQuotaId());
        if (ObjectUtils.isNotEmpty(atrConfMortgQuota)) {
            AtrConfMortgQuota po = ClassUtil.convert(atrConfMortgQuotaVo, AtrConfMortgQuota.class);
            atrConfMortgQuotaDao.updateById(po);
            this.saveHistoryData(po, userId, CommonConstant.OperType.AUDIT);
        }
    }

    @Override
    public Long checkNaturalPk(AtrConfMortgQuotaVo atrConfMortgQuotaVo) {
        return atrConfMortgQuotaDao.checkNaturalPk(atrConfMortgQuotaVo);
    }

    @Override
    public AtrConfMortgQuotaVo validData(MultipartFile file, AtrConfMortgQuotaVo atrConfMortgQuotaVo) throws Exception {

        AtrConfMortgQuotaVo mortgQuotaVo = new AtrConfMortgQuotaVo();
        List<AtrConfMortgQuotaVo> mortgQuotaVoList= ExcelExportUtil.read(file.getInputStream(), AtrConfMortgQuotaVo.class, 0);
        List<AtrConfMortgQuotaPeriodVo> mortgQuotaPeriodVoList= ExcelExportUtil.read(file.getInputStream(), AtrConfMortgQuotaPeriodVo.class, 1);
        AtrConfMortgQuota po = new AtrConfMortgQuota();
        po.setEntityId(atrConfMortgQuotaVo.getEntityId());
        po.setYearMonth(atrConfMortgQuotaVo.getYearMonth());
        List<AtrConfMortgQuota> confMortgQuotaList = atrConfMortgQuotaDao.findList(po);
        List<String> mortgPolicyNoList = mortgQuotaVoList.stream().map(AtrConfMortgQuotaVo::getPolicyNo).collect(Collectors.toList());
        List<String> mortgPeriodPolicyNoList = mortgQuotaPeriodVoList.stream().map(AtrConfMortgQuotaPeriodVo::getPolicyNo).collect(Collectors.toList());
        List<String> oldMortPolicyNoList = confMortgQuotaList.stream().map(AtrConfMortgQuota::getPolicyNo).collect(Collectors.toList());
        Set<String> sets = new HashSet<String>(mortgPolicyNoList);
        if (mortgPolicyNoList.size() != sets.size()) {
            mortgQuotaVo.setInValidType("same");
            return mortgQuotaVo;
        }
        Long Line = 0L;
        for (String policyNo :mortgPolicyNoList) {
            Line ++;
            if(!mortgPeriodPolicyNoList.contains(policyNo)) {
                mortgQuotaVo.setInValidType("notPeriod");
                mortgQuotaVo.setLine(Line);
                break;
            }
            if(oldMortPolicyNoList.contains(policyNo)) {
                mortgQuotaVo.setInValidType("hasMort");
                mortgQuotaVo.setLine(Line);
                break;
            }
        }
        return mortgQuotaVo;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void excelImport(MultipartFile file, AtrConfMortgQuotaVo atrConfMortgQuotaVo, Long userId) throws Exception {

        Date nowTime = new Date();
        try {
            List<AtrConfMortgQuotaVo> mortgQuotaVoList= ExcelExportUtil.read(file.getInputStream(), AtrConfMortgQuotaVo.class, 0);
            List<AtrConfMortgQuotaPeriodVo> mortgQuotaPeriodVoList= ExcelExportUtil.read(file.getInputStream(), AtrConfMortgQuotaPeriodVo.class, 1);
            List<AtrConfMortgQuota> mortgQuotaList = ClassUtil.convert(mortgQuotaVoList, AtrConfMortgQuota.class);
            atrConfMortgQuotaVo.setSerialNo(1L);
            mortgQuotaList.forEach(mortgQuota -> {
                mortgQuota.setEntityId(atrConfMortgQuotaVo.getEntityId());
                mortgQuota.setYearMonth(atrConfMortgQuotaVo.getYearMonth());
                mortgQuota.setValidIs(CommonConstant.ValidStatus.VALID);
                mortgQuota.setAuditState(CommonConstant.AuditStatus.TO_BE_AUDITED);
                mortgQuota.setCreateTime(nowTime);
                mortgQuota.setCreatorId(userId);
                mortgQuota.setSerialNo(1L);
                atrConfMortgQuotaDao.save(mortgQuota);
            });
            mortgQuotaPeriodVoList.forEach(mortgQuotaPeriod -> {
                atrConfMortgQuotaPeriodDao.savePeriodList(mortgQuotaPeriod, atrConfMortgQuotaVo);
            });
             atrImportService.importFile(file,atrConfMortgQuotaVo.getTargetRouter(),userId);
        } catch (ExcelAnalysisException e) {
            throw e;
        }
    }


    // 保存历史轨迹数据
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public AtrConfMortgQuotaHis saveHistoryData(AtrConfMortgQuota entity, Long operId, String operType) {
        AtrConfMortgQuotaHis entityHis = ClassUtil.convert(entity, AtrConfMortgQuotaHis.class);
        entityHis.setOperId(operId);
        entityHis.setOperTime(new Date());
        entityHis.setOperType(operType);
        atrConfMortgQuotaHisDao.save(entityHis);
        return entityHis;
    }

    // 保存关联表历史轨迹数据
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void saveHistoryDataPeriod(AtrConfMortgQuotaPeriod entity) {
        AtrConfMortgQuotaPeriodHis confMortgQuotaPeriodHis = new AtrConfMortgQuotaPeriodHis();
        confMortgQuotaPeriodHis = ClassUtil.convert(entity, AtrConfMortgQuotaPeriodHis.class);
        atrConfMortgQuotaPeriodHisDao.save(confMortgQuotaPeriodHis);
    }
}
