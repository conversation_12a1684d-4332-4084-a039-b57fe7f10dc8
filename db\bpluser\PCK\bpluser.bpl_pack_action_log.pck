create or replace package bpl_pack_action_log is

  PROCEDURE PROC_ADD_ACTIONLOG(P_ACTLOGID     IN NUMBER,
                               P_TASKCODE     IN VARCHAR2,
                               P_SYSTEMCODE   IN VARCHAR2,
                               P_CENTERID     IN NUMBER,
                               <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>     IN VARCHAR2,
                               P_YEAR<PERSON>NTH    IN VARCHAR2,
                               P_PROCID       IN NUMBER,
                               P_PARENTPROCID IN NUMBER,
                               P_CHECKSTATE   IN VARCHAR2,
                               P_CREATORID    IN NUMBER);

  PROCEDURE PROC_ADD_ACTIONLOGDETAILS(P_ACTLOGID    IN NUMBER,
                                      P_RULEID      IN NUMBER,
                                      P_WARNINGTYPE IN VARCHAR2,
                                      <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>   IN VARCHAR2,
                                      P_<PERSON>ECKSTATE  IN VARCHAR2,
                                      P_ERROMESSAGE IN VARCHAR2,
                                      P_BUSMESSAGE  IN VARCHAR2,
                                      P_CREATORID   IN NUMBER);
  PROCEDURE PROC_ADD_ACTIONLOGHIS(P_CENTERID  IN NUMBER,
                                  P_BOOKCODE  IN VARCHAR2,
                                  P_<PERSON><PERSON><PERSON>MONTH IN VARCHAR2,
                                  P_PROCID    IN NUMBER);

  PROCEDURE proc_add_loghis(p_centerid IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2, p_procid IN NUMBER);

  PROCEDURE PROC_SAVE_ACTIONLOG(P_TASKCODE     IN VARCHAR2,
                                P_SYSTEMCODE   IN VARCHAR2,
                                P_CENTERID     IN NUMBER,
                                P_BOOKCODE     IN VARCHAR2,
                                P_YEARMONTH    IN VARCHAR2,
                                P_PROCID       IN NUMBER,
                                P_PARENTPROCID IN NUMBER,
                                P_CHECKSTATE   IN VARCHAR2,
                                p_warningType IN VARCHAR2,
                                P_BUSID        IN NUMBER,
                                P_ERROMESSAGE  IN VARCHAR2,
                                P_CREATORID    IN NUMBER);

  PROCEDURE proc_update_logst(p_actlogid IN NUMBER, p_creatorid IN NUMBER);


  PROCEDURE PROC_ADD_ANNUAL_LOGDETAIL(P_ACTLOGID    IN NUMBER,
                                      P_RULEID      IN NUMBER,
                                      P_CHECKEXPR   IN VARCHAR2,
                                      P_CHECKSTATE  IN VARCHAR2,
                                      P_ERROMESSAGE IN VARCHAR2,
                                      P_BUSMESSAGE  IN VARCHAR2,
                                      P_REMARK2     IN VARCHAR2,
                                      P_REMARK3     IN VARCHAR2,
                                      P_REMARK4     IN VARCHAR2,
                                      P_REMARK5     IN VARCHAR2,
                                      P_CREATORID   IN NUMBER);


  PROCEDURE proc_add_logdetailhis(p_centerid  NUMBER,
                                  p_bookcode  VARCHAR2,
                                  p_yearmonth VARCHAR2,
                                  p_procid    NUMBER);

  PROCEDURE proc_add_detailhis(p_centerid  NUMBER,
                               p_bookcode  VARCHAR2,
                               p_yearmonth VARCHAR2,
                               p_procid    NUMBER,
                               p_busid     NUMBER);

end bpl_pack_action_log;
/
create or replace package body bpl_pack_action_log is

  /***********************************************************************
  NAME : proc_add_actionlog
  DESCRIPTION : 插入业务日志记录
  DATE :2020-12-19
  AUTHOR :YINXH
  -------
  MODIFY LOG
  UPDATE DATE :
  UPDATE BY   :
  UPDATE DESC :
  ***********************************************************************/
  PROCEDURE proc_add_actionlog(p_actlogid IN NUMBER, p_taskCode IN VARCHAR2, p_systemcode IN VARCHAR2, p_centerid IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2, p_procid IN NUMBER, p_parentprocid IN NUMBER, p_checkstate IN VARCHAR2,
     p_creatorid IN NUMBER) IS
  BEGIN
    -- 1、先插入轨迹表：业务日志轨迹表、业务日志明细轨迹表
    BPL_PACK_ACTION_LOG.PROC_ADD_ACTIONLOGHIS(P_CENTERID,P_BOOKCODE,P_YEARMONTH,P_PROCID);
    -- 2、写入业务日志表
    INSERT INTO bms_log_buss_action
      (ACT_LOG_ID,
       TASK_CODE,
       SERIAL_NO,
       SYSTEM_CODE,
       ENTITY_ID,
       BOOK_CODE,
       YEAR_MONTH,
       PARENT_PROC_ID,
       PROC_ID,
       STATE, --当前节点的检查状态
       CREATOR_ID,
       CREATE_TIME,
       UPDATOR_ID,
       UPDATE_TIME)
    VALUES
      (P_ACTLOGID,
       p_taskCode,
       (SELECT COALESCE(MAX(SERIAL_NO) + 1, 1)
          FROM bms_log_buss_actionhis
         WHERE ENTITY_ID = P_CENTERID
           AND (BOOK_CODE = P_BOOKCODE OR P_BOOKCODE IS NULL)
           AND YEAR_MONTH = P_YEARMONTH
           AND PROC_ID = P_PROCID), --根据核算单位id、账套编码、会计期间获取最新版本序号
       p_systemcode,
       P_CENTERID,
       P_BOOKCODE,
       P_YEARMONTH,
       P_PARENTPROCID,
       P_PROCID,
       P_CHECKSTATE,
       P_CREATORID,
       LOCALTIMESTAMP,
       NULL,
       NULL);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      --抛出异常提示信息
      DBMS_OUTPUT.PUT_LINE(SQLERRM || '：插入业务日志表异常，请检查');
      DBMS_OUTPUT.PUT_LINE('**Error line: ' ||
                           DBMS_UTILITY.FORMAT_ERROR_BACKTRACE());

 END proc_add_actionlog;

 /***********************************************************************
  NAME : proc_add_actionlogdetails
  DESCRIPTION : 插入业务日志明细表
  DATE :2020-12-22
  AUTHOR :YINXH
  -------
  MODIFY LOG
  UPDATE DATE :
  UPDATE BY   :
  UPDATE DESC :
  ***********************************************************************/
  PROCEDURE proc_add_actionlogdetails(p_actlogid IN NUMBER, p_ruleid IN NUMBER, p_warningType IN VARCHAR2, p_checkexpr IN VARCHAR2, p_checkstate IN VARCHAR2, p_erromessage IN VARCHAR2, p_busmessage IN VARCHAR2, p_creatorid IN NUMBER) IS
  BEGIN
    -- 写入业务日志明细表
    INSERT INTO bms_log_buss_action_detail
      (DTL_LOG_ID,
       ACT_LOG_ID,
       STATE,
       warning_type,
       RULE_ID,
       ERROR_MESSAGE,
       BUS_MESSAGE,
       DEAL_IS,
       HANDLE_ID,
       HANDLER_TIME,
       CHECK_EXPR,
       REMARK2,
       REMARK3,
       REMARK4,
       REMARK5,
       CREATE_TIME,
       CREATOR_ID,
       UPDATE_TIME,
       UPDATOR_ID)
    VALUES
      (BPL_SEQ_LOG_ACTION_DETAIL.NEXTVAL,
       P_ACTLOGID,
       P_CHECKSTATE,
       p_warningType,
       P_RULEID,
       P_ERROMESSAGE,
       P_BUSMESSAGE,
       (CASE P_CHECKSTATE WHEN '1' THEN NULL ELSE '0' END), --正常时为null，检查异常时：0-未处理，1-已处理
       NULL,
       NULL,
       P_CHECKEXPR,
       NULL,
       NULL,
       NULL,
       NULL,
       LOCALTIMESTAMP,
       P_CREATORID,
       NULL,
       NULL);

  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      DBMS_OUTPUT.PUT_LINE(SQLERRM || '：插入业务日志表异常，请检查');
      DBMS_OUTPUT.PUT_LINE('**Error line: ' ||
                           DBMS_UTILITY.FORMAT_ERROR_BACKTRACE());
  END proc_add_actionlogdetails;

  /***********************************************************************
  NAME : proc_add_actionloghis
  DESCRIPTION : 插入业务日志轨迹
  DATE :2020-12-22
  AUTHOR :YINXH
  -------
  MODIFY LOG
  UPDATE DATE :
  UPDATE BY   :
  UPDATE DESC :
  ***********************************************************************/
  PROCEDURE proc_add_actionloghis(p_centerid IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2, p_procid IN NUMBER) IS
  BEGIN
  -- 写入业务日志明细轨迹表
  INSERT INTO bms_log_buss_action_detailhis
    (DTL_LOG_HIS_ID,
     DTL_LOG_ID,
     ACT_LOG_ID,
     TASK_SEQ,
     STATE,
     warning_type,
     RULE_ID,
     ERROR_MESSAGE,
     BUS_MESSAGE,
     DEAL_IS,
     HANDLE_ID,
     HANDLER_TIME,
     CHECK_EXPR,
     REMARK2,
     REMARK3,
     REMARK4,
     REMARK5,
     CREATE_TIME,
     CREATOR_ID,
     UPDATE_TIME,
     UPDATOR_ID)
    SELECT BPL_SEQ_LOG_ACTION_DETAILHIS.NEXTVAL,
           B.DTL_LOG_ID,
           B.ACT_LOG_ID,
           NULL, --该字段后续可能会移除
           B.STATE,
           B.warning_type,
           B.RULE_ID,
           B.ERROR_MESSAGE,
           B.BUS_MESSAGE,
           B.DEAL_IS,
           B.HANDLE_ID,
           B.HANDLER_TIME,
           B.CHECK_EXPR,
           B.REMARK2,
           B.REMARK3,
           B.REMARK4,
           B.REMARK5,
           B.CREATE_TIME,
           B.CREATOR_ID,
           B.UPDATE_TIME,
           B.UPDATOR_ID
      FROM bms_log_buss_action_detail B
     WHERE B.ACT_LOG_ID IN (SELECT ACT_LOG_ID
                              FROM bms_log_buss_action
                             WHERE ENTITY_ID = P_CENTERID
                               AND (BOOK_CODE = P_BOOKCODE OR P_BOOKCODE IS NULL)
                               AND YEAR_MONTH = P_YEARMONTH
                               AND PROC_ID = P_PROCID);
  -- 清除业务操作日志明细表的旧数据
  DELETE FROM bms_log_buss_action_detail
   WHERE act_log_id IN ( SELECT act_log_id FROM bms_log_buss_action WHERE ENTITY_ID = p_centerid AND (BOOK_CODE = P_BOOKCODE OR P_BOOKCODE IS NULL) AND year_month = p_yearmonth AND proc_id = p_procid );

  -- 再插入轨迹表：业务日志轨迹表
  INSERT INTO bms_log_buss_actionhis
    (ACT_LOG_HIS_ID,
     ACT_LOG_ID,
     TASK_CODE,
     TASK_SEQ,
     SERIAL_NO,
     SYSTEM_CODE,
     ENTITY_ID,
     BOOK_CODE,
     YEAR_MONTH,
     PARENT_PROC_ID,
     PROC_ID,
     STATE,
     CREATE_TIME,
     CREATOR_ID,
     UPDATE_TIME,
     UPDATOR_ID)
    SELECT BPL_SEQ_LOG_ACTIONHIS.NEXTVAL,
           A.ACT_LOG_ID,
           A.TASK_CODE,
           NULL,
           A.SERIAL_NO,
           A.SYSTEM_CODE,
           A.ENTITY_ID,
           A.BOOK_CODE,
           A.YEAR_MONTH,
           A.PARENT_PROC_ID,
           A.PROC_ID,
           A.STATE,
           A.CREATE_TIME,
           A.CREATOR_ID,
           A.UPDATE_TIME,
           A.UPDATOR_ID
      FROM bms_log_buss_action A
     WHERE A.ACT_LOG_ID IN (SELECT ACT_LOG_ID
                              FROM bms_log_buss_action
                             WHERE ENTITY_ID = P_CENTERID
                               AND (BOOK_CODE = P_BOOKCODE OR P_BOOKCODE IS NULL)
                               AND YEAR_MONTH = P_YEARMONTH
                               AND PROC_ID = P_PROCID);

  -- 清除业务日志表旧数据
  DELETE FROM bms_log_buss_action
   WHERE ACT_LOG_ID IN (SELECT ACT_LOG_ID
                          FROM bms_log_buss_action
                         WHERE ENTITY_ID = P_CENTERID
                           AND (BOOK_CODE = P_BOOKCODE OR P_BOOKCODE IS NULL)
                           AND YEAR_MONTH = P_YEARMONTH
                           AND PROC_ID = P_PROCID);
  EXCEPTION
  WHEN OTHERS THEN
    ROLLBACK;
    dbms_output.put_line(SQLERRM || '：插入业务日志表异常，请检查');
    dbms_output.put_line('**Error line: ' || dbms_utility.format_error_backtrace());

END proc_add_actionloghis;

  /***********************************************************************
  NAME : proc_add_loghis
  DESCRIPTION : 插入业务日志轨迹
  DATE :2020-12-22
  AUTHOR :YINXH
  -------
  MODIFY LOG
  UPDATE DATE :
  UPDATE BY   :
  UPDATE DESC :
  ***********************************************************************/
  PROCEDURE proc_add_loghis(p_centerid IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2, p_procid IN NUMBER) IS

  BEGIN-- 写入业务日志明细轨迹表
    INSERT INTO bms_log_buss_action_detailhis (
      DTL_LOG_HIS_ID,
      dtl_log_id,
      act_log_id,
      task_seq,
      STATE,
      warning_type,
      rule_id,
      error_message,
      bus_message,
      deal_is,
      handle_id,
      handler_time,
      check_expr,
      remark2,
      remark3,
      remark4,
      remark5,
      create_time,
      creator_id,
      update_time,
      updator_id
    ) SELECT
      bpl_seq_log_action_detailhis.nextval,
      b.dtl_log_id,
      b.act_log_id,
      NULL,--该字段后续可能会移除
      b.STATE,
      B.warning_type,
      b.rule_id,
      b.error_message,
      b.bus_message,
      b.deal_is,
      b.handle_id,
      b.handler_time,
      b.check_expr,
      b.remark2,
      b.remark3,
      b.remark4,
      b.remark5,
      b.create_time,
      b.creator_id,
      b.update_time,
      b.updator_id
    FROM bms_log_buss_action_detail b
    WHERE b.act_log_id IN ( SELECT act_log_id FROM bms_log_buss_action WHERE ENTITY_ID = p_centerid AND (BOOK_CODE = P_BOOKCODE OR P_BOOKCODE IS NULL) AND year_month = p_yearmonth AND proc_id = p_procid );

  -- 再插入轨迹表：业务日志轨迹表
  INSERT INTO bms_log_buss_actionhis (
    act_log_his_id,
    act_log_id,
    task_code,
    task_seq,
    serial_no,
    system_code,
    ENTITY_ID,
    book_code,
    year_month,
    PARENT_PROC_ID,
    proc_id,
    STATE,
    create_time,
    creator_id,
    update_time,
    updator_id
  ) SELECT
    bpl_seq_log_actionhis.nextval,
    A.act_log_id,
    A.task_code,
    NULL,
    A.serial_no,
    A.system_code,
    A.ENTITY_ID,
    A.book_code,
    A.year_month,
    A.PARENT_PROC_ID,
    A.proc_id,
    A.STATE,
    A.create_time,
    A.creator_id,
    A.update_time,
    A.updator_id
  FROM
    bms_log_buss_action A
  WHERE
    A.act_log_id IN ( SELECT act_log_id FROM bms_log_buss_action WHERE ENTITY_ID = p_centerid AND (BOOK_CODE = P_BOOKCODE OR P_BOOKCODE IS NULL) AND year_month = p_yearmonth AND proc_id = p_procid );

  EXCEPTION
  WHEN OTHERS THEN
    ROLLBACK;
     dbms_output.put_line(SQLERRM || '：插入业务日志表异常，请检查');
     dbms_output.put_line('**Error line: ' || dbms_utility.format_error_backtrace());
  END proc_add_loghis;

  PROCEDURE proc_save_actionlog1(p_taskCode IN VARCHAR2, p_systemcode IN VARCHAR2, p_centerid IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2, p_procid IN NUMBER, p_parentprocid IN NUMBER, p_checkstate IN VARCHAR2, p_warningType IN VARCHAR2, p_busid IN NUMBER,  p_erromessage IN VARCHAR2, p_creatorid IN NUMBER) IS
  v_num NUMBER ( 2 );
  v_actlogid NUMBER ( 18 );
  v_bus_key VARCHAR2 ( 1000 );
  BEGIN
    -- 根据 p_centerid,p_bookcode,p_yearmonth,p_procid  判定日志主表是否存在数据
    SELECT COUNT(G.ACT_LOG_ID)
      INTO V_NUM
      FROM bms_log_buss_action G
     WHERE G.ENTITY_ID = P_CENTERID
       AND (G.BOOK_CODE = P_BOOKCODE OR P_BOOKCODE IS NULL)
       AND G.YEAR_MONTH = P_YEARMONTH
       AND G.PROC_ID = P_PROCID;

    IF v_num = 0 THEN
      v_actlogid := bpl_seq_log_action.nextval;
      bpl_pack_action_log.proc_add_actionlog (v_actlogid, p_taskCode, p_systemcode, p_centerid, p_bookcode, p_yearmonth, p_procid, p_parentprocid, p_checkstate, p_creatorid );
    ELSE
      -- 查询原有主键
      SELECT G.ACT_LOG_ID
        INTO V_ACTLOGID
        FROM bms_log_buss_action G
       WHERE G.ENTITY_ID = P_CENTERID
         AND (G.BOOK_CODE = P_BOOKCODE OR P_BOOKCODE IS NULL)
         AND G.YEAR_MONTH = P_YEARMONTH
         AND G.PROC_ID = P_PROCID;
      -- 保存轨迹数据
      bpl_pack_action_log.proc_add_loghis ( p_centerid, p_bookcode, p_yearmonth, p_procid );
    END IF;

    -- 根据 p_busId  判定日志子表是否存在数据
    SELECT COUNT(G.ACT_LOG_ID)
      INTO V_NUM
      FROM bms_log_buss_action_detail G
     WHERE G.ACT_LOG_ID = V_ACTLOGID
       AND (G.RULE_ID = P_BUSID OR (P_BUSID IS NULL AND G.RULE_ID IS NULL));
    -- 生成业务主键
    v_bus_key := p_centerid || '#' || p_bookcode || '#' || p_yearmonth || '#' || p_procid || '#' || p_busId;
    IF v_num = 0 THEN
      BPL_PACK_ACTION_LOG.PROC_ADD_ACTIONLOGDETAILS(V_ACTLOGID, --业务日志主键id
                                                   P_BUSID, --规则编码
                                                   NULL, --校验脚本
                                                   P_CHECKSTATE, --校验状态
                                                   P_WARNINGTYPE,
                                                   P_ERROMESSAGE, --校验异常原因
                                                   V_BUS_KEY, --业务异常主键
                                                   P_CREATORID);
    ELSE
      UPDATE bms_log_buss_action_detail G
         SET STATE         = P_CHECKSTATE,
             ERROR_MESSAGE = P_ERROMESSAGE,
             UPDATE_TIME   = LOCALTIMESTAMP,
             UPDATOR_ID    = P_CREATORID
       WHERE G.ACT_LOG_ID = V_ACTLOGID
         AND (G.RULE_ID = P_BUSID OR
             (P_BUSID IS NULL AND G.RULE_ID IS NULL));
    END IF;
    -- 根据子表状态，修改主表状态
    bpl_pack_action_log.proc_update_logst ( v_actlogid, p_creatorid );
  END proc_save_actionlog1;




  PROCEDURE proc_save_actionlog(p_taskCode IN VARCHAR2, p_systemcode IN VARCHAR2, p_centerid IN NUMBER, p_bookcode IN VARCHAR2, p_yearmonth IN VARCHAR2, p_procid IN NUMBER, p_parentprocid IN NUMBER, p_checkstate IN VARCHAR2, p_warningType IN VARCHAR2, p_busid IN NUMBER,  p_erromessage IN VARCHAR2, p_creatorid IN NUMBER) IS
  v_num NUMBER ( 2 );
  v_actlogid NUMBER ( 18 );
  v_detlogid NUMBER ( 18 );
  v_bus_key VARCHAR2 ( 1000 );
  BEGIN
    -- 根据 p_centerid,p_bookcode,p_yearmonth,p_procid  判定日志主表是否存在数据
    SELECT COUNT(G.ACT_LOG_ID)
      INTO V_NUM
      FROM bms_log_buss_action G
     WHERE G.ENTITY_ID = P_CENTERID
       AND (G.BOOK_CODE = P_BOOKCODE)
       AND G.YEAR_MONTH = P_YEARMONTH
       AND G.PROC_ID = P_PROCID;

    IF v_num = 0 THEN
      v_actlogid := bpl_seq_log_action.nextval;
      bpl_pack_action_log.proc_add_actionlog( V_ACTLOGID,
                                              p_taskCode,
                                              p_systemcode,
                                              P_CENTERID,
                                              P_BOOKCODE,
                                              P_YEARMONTH,
                                              P_PROCID,
                                              P_PARENTPROCID,
                                              P_CHECKSTATE,
                                              P_CREATORID);
    ELSE
      -- 查询原有主键
      SELECT G.ACT_LOG_ID
        INTO V_ACTLOGID
        FROM bms_log_buss_action G
       WHERE G.ENTITY_ID = P_CENTERID
         AND (G.BOOK_CODE = P_BOOKCODE)
         AND G.YEAR_MONTH = P_YEARMONTH
         AND G.PROC_ID = P_PROCID;
       -- 保存日志轨迹数据
       bpl_pack_action_log.proc_add_loghis ( p_centerid, p_bookcode, p_yearmonth, p_procid );
    END IF;

    -- 根据 p_busId  判定日志子表是否存在数据
    SELECT COUNT(G.ACT_LOG_ID)
      INTO V_NUM
      FROM bms_log_buss_action_detail G
     WHERE G.ACT_LOG_ID = V_ACTLOGID
       AND (G.RULE_ID = P_BUSID OR (P_BUSID IS NULL AND G.RULE_ID IS NULL));
    -- 生成业务主键
    v_bus_key := p_centerid || '#' || p_bookcode || '#' || p_yearmonth || '#' || p_procid || '#' || p_busId;

    -- 保存轨迹数据
    IF p_busId is null THEN
      bpl_pack_action_log.proc_add_logdetailhis( p_centerid, p_bookcode, p_yearmonth, p_procid );
    ELSE
      bpl_pack_action_log.proc_add_detailhis( p_centerid, p_bookcode, p_yearmonth, p_procid, p_busId );
    END IF;

    IF v_num = 0 THEN
      BPL_PACK_ACTION_LOG.PROC_ADD_ACTIONLOGDETAILS(V_ACTLOGID, --业务日志主键id
                                                   P_BUSID, --规则编码
                                                   P_WARNINGTYPE, --校验脚本
                                                   NULL, --校验脚本
                                                   P_CHECKSTATE, --校验状态
                                                   P_ERROMESSAGE, --校验异常原因
                                                   V_BUS_KEY, --业务异常主键
                                                   P_CREATORID);
    ELSE
      UPDATE bms_log_buss_action_detail G
         SET STATE         = P_CHECKSTATE,
             ERROR_MESSAGE = P_ERROMESSAGE,
             UPDATE_TIME   = SYSDATE,
             UPDATOR_ID    = P_CREATORID
       WHERE G.ACT_LOG_ID = V_ACTLOGID
         AND (G.RULE_ID = P_BUSID OR
             (P_BUSID IS NULL AND G.RULE_ID IS NULL));

      DELETE FROM bms_log_buss_action_detail G
       WHERE G.ACT_LOG_ID = V_ACTLOGID
         AND (G.RULE_ID IS NOT NULL AND P_BUSID IS NULL);
    END IF;

     -- 根据子表状态，修改主表状态
    bpl_pack_action_log.proc_update_logst ( v_actlogid, p_creatorid );

  END proc_save_actionlog;


  /***********************************************************************
    NAME : bpl_proc_update_actionlog
    DESCRIPTION : 根据日志明细表通过状态，更新业务日志主表数据通过状态。
    DATE :2021-02-07
    AUTHOR :OQJ
    -------
    MODIFY LOG
    UPDATE DATE :
    UPDATE BY   :
    UPDATE DESC :
  ***********************************************************************/
  PROCEDURE proc_update_logst(p_actlogid IN NUMBER, p_creatorid IN NUMBER) IS

  v_num NUMBER (11);
  BEGIN
    -- 判断子表是否存在不通过的记录
    SELECT COUNT(G.DTL_LOG_ID)
      INTO V_NUM
      FROM bms_log_buss_action_detail G
     WHERE G.ACT_LOG_ID = P_ACTLOGID
       AND G.STATE = '0'
       AND G.warning_type='2';
    -- 不通过状态记录大于0，则更新主表状态为不通过
    IF v_num > 0 THEN
      UPDATE bms_log_buss_action T
         SET STATE       = '0',
             UPDATE_TIME = LOCALTIMESTAMP,
             UPDATOR_ID  = P_CREATORID
       WHERE T.ACT_LOG_ID = P_ACTLOGID;
    ELSE
      UPDATE bms_log_buss_action T
         SET STATE       = '1',
             UPDATE_TIME = LOCALTIMESTAMP,
             UPDATOR_ID  = P_CREATORID
       WHERE T.ACT_LOG_ID = P_ACTLOGID;
    END IF;
  EXCEPTION
  WHEN OTHERS THEN
    ROLLBACK;
     dbms_output.put_line(SQLERRM || '：更新业务日志表状态异常，请检查');
     dbms_output.put_line('**Error line: ' || dbms_utility.format_error_backtrace());
  END proc_update_logst;


  PROCEDURE proc_add_annual_logdetail(p_actlogid IN NUMBER, p_ruleid IN NUMBER, p_checkexpr IN VARCHAR2, p_checkstate IN VARCHAR2,  p_erromessage IN VARCHAR2,p_busmessage IN VARCHAR2, p_remark2 IN VARCHAR2, p_remark3 IN VARCHAR2, p_remark4 IN VARCHAR2,p_remark5 IN VARCHAR2,
                               p_creatorid IN NUMBER) is
  BEGIN
    -- 写入业务日志明细表
    INSERT INTO bms_log_buss_action_detail
      (dtl_log_id,
       act_log_id,
       state,
       rule_id,
       error_message,
       bus_message,
       deal_is,
       check_expr,
       remark2,
       remark3,
       remark4,
       remark5,
       create_time,
       creator_id )
    VALUES
      (bpl_seq_log_action_detail.nextval,
       p_actlogid,
       p_checkstate,
       p_ruleid,
       p_erromessage,
       p_busmessage,
       ( CASE p_checkstate WHEN '1' THEN NULL ELSE'0' END ),--正常时为null，检查异常时：0-未处理，1-已处理
       p_checkexpr,
       p_remark2,
       p_remark3,
       p_remark4,
       p_remark5,
       LOCALTIMESTAMP,
       p_creatorid );
    EXCEPTION
    WHEN OTHERS THEN
      dbms_output.put_line(SQLERRM || '：插入业务日志表异常，请检查');
  END proc_add_annual_logdetail;


  PROCEDURE proc_add_logdetailhis(p_centerid NUMBER, p_bookcode VARCHAR2, p_yearmonth VARCHAR2, p_procid NUMBER)
   AS
  /***********************************************************************
    NAME : exp_proc_Add_actionlog
    DESCRIPTION : 插入业务日志明细表轨迹
    DATE :2021-02-07
    AUTHOR :OQJ
    ***********************************************************************/
    BEGIN-- 写入业务日志明细轨迹表
    INSERT INTO bms_log_buss_action_detailhis
      (DTL_LOG_HIS_ID,
       DTL_LOG_ID,
       ACT_LOG_ID,
       TASK_SEQ,
       STATE,
       WARNING_TYPE,
       RULE_ID,
       ERROR_MESSAGE,
       BUS_MESSAGE,
       DEAL_IS,
       HANDLE_ID,
       HANDLER_TIME,
       CHECK_EXPR,
       REMARK2,
       REMARK3,
       REMARK4,
       REMARK5,
       CREATE_TIME,
       CREATOR_ID,
       UPDATE_TIME,
       UPDATOR_ID)
      SELECT BPL_SEQ_LOG_ACTION_DETAILHIS.NEXTVAL,
             B.DTL_LOG_ID,
             B.ACT_LOG_ID,
             NULL, --该字段后续可能会移除
             B.STATE,
             B.WARNING_TYPE,
             B.RULE_ID,
             B.ERROR_MESSAGE,
             B.BUS_MESSAGE,
             B.DEAL_IS,
             B.HANDLE_ID,
             B.HANDLER_TIME,
             B.CHECK_EXPR,
             B.REMARK2,
             B.REMARK3,
             B.REMARK4,
             B.REMARK5,
             B.CREATE_TIME,
             B.CREATOR_ID,
             B.UPDATE_TIME,
             B.UPDATOR_ID
        FROM bms_log_buss_action_detail B
       WHERE B.ACT_LOG_ID IN
             (SELECT ACT_LOG_ID
                FROM bms_log_buss_action
               WHERE ENTITY_ID = P_CENTERID
                 AND (BOOK_CODE = P_BOOKCODE OR P_BOOKCODE IS NULL)
                 AND YEAR_MONTH = P_YEARMONTH
                 AND PROC_ID = P_PROCID);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      DBMS_OUTPUT.PUT_LINE('**SQLSTATE:' || Sqlcode ||';'||'**SQLERRM:'|| SQLERRM ||'【插入业务日志轨迹表异常，请检查】');
  END proc_add_logdetailhis;


  PROCEDURE proc_add_detailhis(p_centerid NUMBER, p_bookcode VARCHAR2, p_yearmonth VARCHAR2, p_procid NUMBER, p_busid NUMBER)
   AS
  BEGIN-- 写入业务日志明细轨迹表
    INSERT INTO bms_log_buss_action_detailhis
      (DTL_LOG_HIS_ID,
       DTL_LOG_ID,
       ACT_LOG_ID,
       TASK_SEQ,
       STATE,
       WARNING_TYPE,
       RULE_ID,
       ERROR_MESSAGE,
       BUS_MESSAGE,
       DEAL_IS,
       HANDLE_ID,
       HANDLER_TIME,
       CHECK_EXPR,
       REMARK2,
       REMARK3,
       REMARK4,
       REMARK5,
       CREATE_TIME,
       CREATOR_ID,
       UPDATE_TIME,
       UPDATOR_ID)
      SELECT BPL_SEQ_LOG_ACTION_DETAILHIS.NEXTVAL,
             B.DTL_LOG_ID,
             B.ACT_LOG_ID,
             NULL, --该字段后续可能会移除
             B.STATE,
             B.WARNING_TYPE,
             B.RULE_ID,
             B.ERROR_MESSAGE,
             B.BUS_MESSAGE,
             B.DEAL_IS,
             B.HANDLE_ID,
             B.HANDLER_TIME,
             B.CHECK_EXPR,
             B.REMARK2,
             B.REMARK3,
             B.REMARK4,
             B.REMARK5,
             B.CREATE_TIME,
             B.CREATOR_ID,
             B.UPDATE_TIME,
             B.UPDATOR_ID
        FROM bms_log_buss_action_detail B
    WHERE
      b.act_log_id IN ( SELECT act_log_id FROM bms_log_buss_action WHERE ENTITY_ID = p_centerid AND (book_code = p_bookcode or p_bookcode is null)  AND year_month = p_yearmonth AND proc_id = p_procid) AND (b.rule_id = p_busId or  (p_busId is null and b.rule_id is null));
    --COMMIT;--提交事务
    EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
  --抛出异常提示信息
    DBMS_OUTPUT.PUT_LINE('**SQLSTATE:' || Sqlcode ||';'||'**SQLERRM:'|| SQLERRM ||'【插入业务日志表异常，请检查】');


  END proc_add_detailhis;



END bpl_pack_action_log;
/
