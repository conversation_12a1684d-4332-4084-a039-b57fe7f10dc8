/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-08-11 10:56:32
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-08-11 10:56:32<br/>
 * Description: LIC 计算结果明细(合同组维度，合约分入)<br/>
 * Table Name: ATR_BUSS_TI_LIC_ICG_CALC_DETAIL<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "LIC 计算结果明细(合同组维度，合约分入)")
public class AtrBussTILicIcgCalcDetail implements Serializable {
    /**
     * Database column: ATR_BUSS_TI_LIC_ICG_CALC_DETAIL.ID
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: ATR_BUSS_TI_LIC_ICG_CALC_DETAIL.MAIN_ID
     * Database remarks: 结果表的ID
     */
    @ApiModelProperty(value = "结果表的ID", required = true)
    private Long mainId;

    /**
     * Database column: ATR_BUSS_TI_LIC_ICG_CALC_DETAIL.DEV_NO
     * Database remarks: 发展期
     */
    @ApiModelProperty(value = "发展期", required = false)
    private Short devNo;

    /**
     * Database column: ATR_BUSS_TI_LIC_ICP_CALC_DETAIL.ULT_ORI
     * Database remarks: ULT% 原始配置值
     */
    @ApiModelProperty(value = "ULT% 原始配置值", required = false)
    private BigDecimal ultOri;

    /**
     * Database column: ATR_BUSS_TI_LIC_ICP_CALC_DETAIL.ULT
     * Database remarks: ULT%
     */
    @ApiModelProperty(value = "ULT%", required = false)
    private BigDecimal ult;

    /**
     * Database column: ATR_BUSS_TI_LIC_ICP_CALC_DETAIL.PAID_MODE
     * Database remarks: 赔付模式
     */
    @ApiModelProperty(value = "赔付模式", required = false)
    private BigDecimal paidMode;

    /**
     * Database column: ATR_BUSS_TI_LIC_ICG_CALC_DETAIL.IBNR_CUR
     * Database remarks: 当前事故年IBNR
     */
    @ApiModelProperty(value = "当前事故年IBNR", required = false)
    private BigDecimal ibnrCur;

    /**
     * Database column: ATR_BUSS_TI_LIC_ICG_CALC_DETAIL.IBNR_PRE
     * Database remarks: 往年事故年IBNR
     */
    @ApiModelProperty(value = "往年事故年IBNR", required = false)
    private BigDecimal ibnrPre;

    /**
     * Database column: ATR_BUSS_TI_LIC_ICG_CALC_DETAIL.OS_CUR
     * Database remarks: 当前事故年O/S
     */
    @ApiModelProperty(value = "当前事故年O/S", required = false)
    private BigDecimal osCur;

    /**
     * Database column: ATR_BUSS_TI_LIC_ICG_CALC_DETAIL.OS_PRE
     * Database remarks: 往年事故年O/S
     */
    @ApiModelProperty(value = "往年事故年O/S", required = false)
    private BigDecimal osPre;

    /**
     * Database column: ATR_BUSS_TI_LIC_ICG_CALC_DETAIL.ULAE_CUR
     * Database remarks: 当前事故年间接理赔费用
     */
    @ApiModelProperty(value = "当前事故年间接理赔费用", required = false)
    private BigDecimal ulaeCur;

    /**
     * Database column: ATR_BUSS_TI_LIC_ICG_CALC_DETAIL.ULAE_PRE
     * Database remarks: 当前事故年间接理赔费用
     */
    @ApiModelProperty(value = "当前事故年间接理赔费用", required = false)
    private BigDecimal ulaePre;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMainId() {
        return mainId;
    }

    public void setMainId(Long mainId) {
        this.mainId = mainId;
    }

    public Short getDevNo() {
        return devNo;
    }

    public void setDevNo(Short devNo) {
        this.devNo = devNo;
    }

    public BigDecimal getUltOri() {
        return ultOri;
    }

    public void setUltOri(BigDecimal ultOri) {
        this.ultOri = ultOri;
    }

    public BigDecimal getUlt() {
        return ult;
    }

    public void setUlt(BigDecimal ult) {
        this.ult = ult;
    }

    public BigDecimal getPaidMode() {
        return paidMode;
    }

    public void setPaidMode(BigDecimal paidMode) {
        this.paidMode = paidMode;
    }

    public BigDecimal getIbnrCur() {
        return ibnrCur;
    }

    public void setIbnrCur(BigDecimal ibnrCur) {
        this.ibnrCur = ibnrCur;
    }

    public BigDecimal getIbnrPre() {
        return ibnrPre;
    }

    public void setIbnrPre(BigDecimal ibnrPre) {
        this.ibnrPre = ibnrPre;
    }

    public BigDecimal getOsCur() {
        return osCur;
    }

    public void setOsCur(BigDecimal osCur) {
        this.osCur = osCur;
    }

    public BigDecimal getOsPre() {
        return osPre;
    }

    public void setOsPre(BigDecimal osPre) {
        this.osPre = osPre;
    }

    public BigDecimal getUlaeCur() {
        return ulaeCur;
    }

    public void setUlaeCur(BigDecimal ulaeCur) {
        this.ulaeCur = ulaeCur;
    }

    public BigDecimal getUlaePre() {
        return ulaePre;
    }

    public void setUlaePre(BigDecimal ulaePre) {
        this.ulaePre = ulaePre;
    }
}