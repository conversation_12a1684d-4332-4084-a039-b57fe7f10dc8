package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.conf.AppConfig;
import com.ss.ifrs.actuarial.dao.conf.AtrConfExcelTableColumnDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfExcelTableDao;
import com.ss.ifrs.actuarial.feign.BmsConfCodeFeignClient;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfCode;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfCodeVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaDefVo;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrExcelCellVo;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrExcelHeaderVo;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrExcelVo;
import com.ss.ifrs.actuarial.service.AtrBussReserveIbnrDetailService;
import com.ss.ifrs.actuarial.service.AtrBussReserveIbnrService;
import com.ss.ifrs.actuarial.service.AtrConfCodeService;
import com.ss.ifrs.actuarial.service.AtrConfExcelService;
import com.ss.ifrs.actuarial.util.ExcelGenerateUtils;
import com.ss.library.constant.ExcelConstant;
import com.ss.library.utils.StringUtil;
import com.ss.platform.pojo.com.po.ConfCode;
import com.ss.platform.util.LanguageUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.RichTextString;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/19
 **/
@Service
@Transactional
public class AtrConfExcelServiceImpl implements AtrConfExcelService {

    final Logger LOG = LoggerFactory.getLogger(getClass());

    @Autowired
    AtrConfExcelTableDao atrConfExcelTableDao;

    @Autowired
    AtrConfExcelTableColumnDao atrConfExcelTableColumnDao;

    @Autowired
    BmsConfCodeFeignClient bmsConfCodeFeignClient;

    @Autowired
    AtrBussReserveIbnrDetailService atrBussReserveIbnrDetailService;

    @Autowired
    AtrBussReserveIbnrService atrBussReserveIbnrService;

    @Autowired
    AtrConfCodeService atrConfCodeService;

    @Autowired
    AppConfig appConfig;


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void exportExcel(HttpServletResponse response, AtrExcelVo atrExcelVo, List<Object> objList) throws Exception {

        String language = atrExcelVo.getLanguage();
        List<AtrExcelHeaderVo> headerCells = atrExcelVo.getAtrExcelHeaderVoList();
        String fileName = LanguageUtil.getLocalesName(language, atrExcelVo.getFileEName(), atrExcelVo.getFileCName(), atrExcelVo.getFileTName());

        //获取文件的相应保存路径
        String prefixPath = appConfig.getBasePath();
        String uploadBasePath = appConfig.getUploadBasePath();
        String fileTemplate = appConfig.getFileTemplate();

        try {
            String filePath = prefixPath + uploadBasePath + fileTemplate;

            //把所有数据信息加载到单元格（包含表头和数据）
            List<AtrExcelCellVo> excelCells = this.generateExcelCell(headerCells, objList, language);
            //生成并导出Excel文件
            ExcelGenerateUtils.generateExportExcel(response, excelCells, fileName, filePath);

        }catch (IOException ex) {
            LOG.error(ex.getLocalizedMessage(), ex);
            throw ex;
        }catch (Exception ex) {
            LOG.error(ex.getLocalizedMessage(), ex);
            throw ex;
        }
    }

    public List<AtrExcelCellVo> generateExcelCell(List<AtrExcelHeaderVo> headerCells, List<Object> objList, String language){
        List<AtrExcelCellVo> cells = new ArrayList<>();

        List<Object> objectList = objList;
        AtrExcelCellVo dmExcelCellVo;
        Class<?> clazz;
        String comment = null;
        String columnCellType = ExcelConstant.ExcelCellType.STRING;
        String colLength;

        int i=0;
        int j=1;
        for(AtrExcelHeaderVo atrExcelHeaderVo : headerCells){
            switch (atrExcelHeaderVo.getDataType().toUpperCase()) {
                case "TIMESTAMP":
                case "DATE":
                    columnCellType = ExcelConstant.ExcelCellType.DATE;
                    break;
                case "NUMERIC":
                case "NUMBER":
                    columnCellType = ExcelConstant.ExcelCellType.DOUBLE;
                    /*if (dmConfTableColumnVo.getColLength().contains(",")) {
                        columnCellType = ExcelConstant.ExcelCellType.DOUBLE;
                    } else {
                        columnCellType = ExcelConstant.ExcelCellType.INTEGER;
                    }*/
                    break;
                case "INTEGER":
                    columnCellType = ExcelConstant.ExcelCellType.INTEGER;
                    break;
                case "VARCHAR":
                case "VARCHAR2":
                case "CHAR":
                default:
                    columnCellType = ExcelConstant.ExcelCellType.STRING;
                    break;
            }

            // 长度
            colLength = atrExcelHeaderVo.getLength();
            // 表头赋值
            dmExcelCellVo = new AtrExcelCellVo(null, 0, i,
                    LanguageUtil.getLocalesName(language,atrExcelHeaderVo.getPropertyEName(),atrExcelHeaderVo.getPropertyCName(),atrExcelHeaderVo.getPropertyTName()),
                    columnCellType, comment, colLength, language, HorizontalAlignment.CENTER);
            cells.add(dmExcelCellVo);

            //dataMap.get(atrExcelHeaderVo.getProperty()).forEach();

            j=1;
            // 非表头赋值
            for(Object obj : objectList){
                clazz = obj.getClass();
                // 如果字段是枚举，则根据枚举编码和枚举值查找对应的名称
                Object fieldValue = getFieldValueByName(atrExcelHeaderVo.getProperty(), obj);
                if(StringUtil.isNotEmpty(atrExcelHeaderVo.getCodeType())){
                    fieldValue = getNameByCodeType(fieldValue.toString(), atrExcelHeaderVo.getCodeType(), language);
                }

                //设置单元格的值
                if (fieldValue instanceof Boolean) {
                    dmExcelCellVo = new AtrExcelCellVo(null, j, i, (Boolean) fieldValue, columnCellType, null, null,
                            language, atrExcelHeaderVo.getAlignment());
                } else if (fieldValue instanceof Date) {
                    dmExcelCellVo = new AtrExcelCellVo(null, j, i, (Date) fieldValue, columnCellType, null, null,
                            language, atrExcelHeaderVo.getAlignment());
                } else if (fieldValue instanceof String) {
                    dmExcelCellVo = new AtrExcelCellVo(null, j, i, (String) fieldValue, columnCellType, null, null,
                            language, atrExcelHeaderVo.getAlignment());
                    String content = (String) fieldValue;
                    XSSFRichTextString xts = new XSSFRichTextString(content);
                    int index = content.indexOf("*");
                    if (index != -1) {
                        /*xts.applyFont(index, index + 1, redFont);
                        xts.applyFont(index + 1, content.length(), font);
                        *//*String string = xts.getString();*//*
                        xssfCell.setCellValue(xts);*/
                    } else {
                        //xssfCell.setCellValue((String) cellValue);
                        dmExcelCellVo = new AtrExcelCellVo(null, j, i, (String) fieldValue, columnCellType, null, null,
                                language, atrExcelHeaderVo.getAlignment());
                    }
                } else if (fieldValue instanceof Double) {
                    dmExcelCellVo = new AtrExcelCellVo(null, j, i, (Double) fieldValue, columnCellType, null, null,
                            language, atrExcelHeaderVo.getAlignment());
                } else if (fieldValue instanceof BigDecimal) {
                    dmExcelCellVo = new AtrExcelCellVo(null, j, i, ((BigDecimal) fieldValue).doubleValue(), columnCellType, null, null,
                            language, atrExcelHeaderVo.getAlignment());
                } else if (fieldValue instanceof Integer) {
                    dmExcelCellVo = new AtrExcelCellVo(null, j, i, (Integer) fieldValue, columnCellType, null, null,
                            language, atrExcelHeaderVo.getAlignment());
                } else if (fieldValue instanceof Calendar) {
                    dmExcelCellVo = new AtrExcelCellVo(null, j, i, (Calendar) fieldValue, columnCellType, null, null,
                            language, atrExcelHeaderVo.getAlignment());
                } else if (fieldValue instanceof RichTextString) {
                    dmExcelCellVo = new AtrExcelCellVo(null, j, i, (RichTextString) fieldValue, columnCellType, null, null,
                            language, atrExcelHeaderVo.getAlignment());
                }
                cells.add(dmExcelCellVo);
                j++;
            }

            i++;
        }

        return cells;
    }


    private String generateComment(String codeType, String language) {
        if (StringUtils.isEmpty(codeType)) {
            return null;
        }
        List<ConfCode> bplCodes = bmsConfCodeFeignClient.findCodeByCodeType(codeType);
        if (CollectionUtils.isEmpty(bplCodes)) {
            return null;
        }

        StringBuffer sb = new StringBuffer();
        for (ConfCode bplCodeVo : bplCodes) {
            sb.append(bplCodeVo.getCodeCode());
            sb.append("-");
            sb.append(LanguageUtil.getLocalesName(language, bplCodeVo.getCodeEName(), bplCodeVo.getCodeCName(),
                    bplCodeVo.getCodeLName()));
            sb.append("\n");
        }
        return sb.toString();
    }


    /**
     * 根据属性名获取属性值
     *
     * @param fieldName
     * @param object
     * @return
     */
    private Object getFieldValueByName(String fieldName, Object object) {
        try {
            //Field field = object.getClass().getField(fieldName);
            Field field = object.getClass().getDeclaredField(fieldName);
            //设置对象的访问权限，保证对private的属性的访问
            field.setAccessible(true);
            return  field.get(object);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * @Method getNameByCodeType
     * <AUTHOR>
     * @Date 2022/9/2
     * @Description 根据枚举获取其国际化名称
     * @param codeCode 枚举值
     * @param codeType 枚举
     * @param language 语言
     * @Return
     */
    private String getNameByCodeType(String codeCode, String codeType, String language){
        AtrConfCodeVo atrConfCodeVo = new AtrConfCodeVo();
        atrConfCodeVo.setCodeCode(codeCode);
        atrConfCodeVo.setCodeCodeIdx(codeType);
        // 根据枚举codeType和枚举值codeCode查找枚举对象
        AtrConfCode atrConfCode = atrConfCodeService.findByCodeAndIdx(atrConfCodeVo);
        if(null != atrConfCode){
            return LanguageUtil.getLocalesName(language, atrConfCode.getCodeEName(),atrConfCode.getCodeCName(),atrConfCode.getCodeLName());
        }else {
            return null;
        }
    }



    public List<AtrExcelCellVo> generateCells(List<AtrConfQuotaDefVo> atrConfQuotaDefVos, String language) {
        List<AtrExcelCellVo> cells = new ArrayList<>();

        for (int i = 0; i < atrConfQuotaDefVos.size(); i++) {
            AtrConfQuotaDefVo confQuotaDefVo = atrConfQuotaDefVos.get(i);
            String titleName = null;
            //titleName = LanguageUtil.getLocalesName(language, confQuotaDefVo.getColEName(),
            //        dmConfTableColumnVo.getColCName(), dmConfTableColumnVo.getColTName());
            titleName = LanguageUtil.getLocalesName(language, confQuotaDefVo.getQuotaEName(),
                    confQuotaDefVo.getQuotaCName(), confQuotaDefVo.getQuotaLName());
            if (ObjectUtils.isEmpty(titleName)) {
                titleName = confQuotaDefVo.getQuotaCode();
            } else {
                titleName = confQuotaDefVo.getQuotaCode() + "-" + titleName;
            }
            String comment = null;
            if ("5".equals(confQuotaDefVo.getQuotaValueType())) {
                comment = generateComment(confQuotaDefVo.getCodeType(), language);
            } else {
                comment = LanguageUtil.getLocalesName(language, confQuotaDefVo.getRuleEName(),
                        confQuotaDefVo.getRuleCName(), confQuotaDefVo.getRuleLName());
            }

            String columnCellType = ExcelConstant.ExcelCellType.STRING;
            String colType = confQuotaDefVo.getQuotaValueType();
            String colLength = null;
            switch (colType.toUpperCase()) {
                case "3":
                    columnCellType = ExcelConstant.ExcelCellType.DATE;
                    break;
                case "2":
                    columnCellType = ExcelConstant.ExcelCellType.DOUBLE;
                    colLength = "16,2";

/*                    if (dmConfTableColumnVo.getColLength().contains(",")) {
                        columnCellType = ExcelConstant.ExcelCellType.DOUBLE;
                    } else {
                        columnCellType = ExcelConstant.ExcelCellType.INTEGER;
                    }*/
                    break;
                case "4":
                    columnCellType = ExcelConstant.ExcelCellType.DOUBLE;
                    colLength = "9,6";
                    break;
                case "1":
                default:
                    columnCellType = ExcelConstant.ExcelCellType.STRING;
                    colLength = "64";
                    break;
            }
            // 长度
            AtrExcelCellVo dmExcelCellVo = new AtrExcelCellVo(null, 0, i, titleName, columnCellType, comment, colLength,
                    language);
            cells.add(dmExcelCellVo);
        }
        return cells;
    }


}
