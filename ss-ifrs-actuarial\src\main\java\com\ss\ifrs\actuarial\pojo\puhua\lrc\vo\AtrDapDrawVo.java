/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-02-02 18:13:50
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.puhua.lrc.vo;

import com.ss.ifrs.actuarial.pojo.atrdap.po.AtrBussDDLicIcgCalcDetail;
import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-02 18:13:50<br/>
 * Description: 当期实付赔款(合同组维度，直保&临分分入)<br/>
 * Table Name: ATR_DAP_DD_CLAIM_PAID<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "当期实付赔款(合同组维度，直保&临分分入)")
public class AtrDapDrawVo implements Serializable {

    private Long id;

    private Long entityId;

    private String currencyCode;

    private String yearMonth;

    private String accidentYearMonth;

    private String portfolioNo;

    private String icgNo;

    private String evaluateApproach;

    private String loaCode;

    private BigDecimal paidAmount;

    private String businessSourceCode;

    private BigDecimal os;

    private BigDecimal ibnr;

    private Date drawTime;

    private Long mainId;

    private BigDecimal paidMode;

    private String taskCode;

    private Integer devNo;

    private BigDecimal ultOri;

    private BigDecimal ult;

    private BigDecimal ulae;

    private String calcType;

    private Long creatorId;

    private Date CreateTime;

    private Long userId;

    private String feeType;

    private BigDecimal mapMainId;

    private String actionNo;

    List<Integer> devNoList;

    private List<AtrDapDrawVo> devYearMonthList;

    private List<AtrBussDDLicIcgCalcDetail> ultList;

    private Long limit;

    private Long offset;

    private String language;

    private String reinsCode;

    private String riskCode;

    private String businessNature;

    private String reinsFlag;

    private String shareholderFlag;

    private String comCode;

    private String centerCode;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getAccidentYearMonth() {
        return accidentYearMonth;
    }

    public void setAccidentYearMonth(String accidentYearMonth) {
        this.accidentYearMonth = accidentYearMonth;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public String getEvaluateApproach() {
        return evaluateApproach;
    }

    public void setEvaluateApproach(String evaluateApproach) {
        this.evaluateApproach = evaluateApproach;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(BigDecimal paidAmount) {
        this.paidAmount = paidAmount;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public BigDecimal getOs() {
        return os;
    }

    public void setOs(BigDecimal os) {
        this.os = os;
    }

    public BigDecimal getIbnr() {
        return ibnr;
    }

    public void setIbnr(BigDecimal ibnr) {
        this.ibnr = ibnr;
    }

    public Date getDrawTime() {
        return drawTime;
    }

    public void setDrawTime(Date drawTime) {
        this.drawTime = drawTime;
    }

    public Long getMainId() {
        return mainId;
    }

    public void setMainId(Long mainId) {
        this.mainId = mainId;
    }

    public List<AtrDapDrawVo> getDevYearMonthList() {
        return devYearMonthList;
    }

    public void setDevYearMonthList(List<AtrDapDrawVo> devYearMonthList) {
        this.devYearMonthList = devYearMonthList;
    }

    public BigDecimal getPaidMode() {
        return paidMode;
    }

    public void setPaidMode(BigDecimal paidMode) {
        this.paidMode = paidMode;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public Integer getDevNo() {
        return devNo;
    }

    public void setDevNo(Integer devNo) {
        this.devNo = devNo;
    }

    public BigDecimal getUltOri() {
        return ultOri;
    }

    public void setUltOri(BigDecimal ultOri) {
        this.ultOri = ultOri;
    }

    public BigDecimal getUlt() {
        return ult;
    }

    public void setUlt(BigDecimal ult) {
        this.ult = ult;
    }

    public BigDecimal getUlae() {
        return ulae;
    }

    public void setUlae(BigDecimal ulae) {
        this.ulae = ulae;
    }

    public String getCalcType() {
        return calcType;
    }

    public void setCalcType(String calcType) {
        this.calcType = calcType;
    }

    public List<AtrBussDDLicIcgCalcDetail> getUltList() {
        return ultList;
    }

    public void setUltList(List<AtrBussDDLicIcgCalcDetail> ultList) {
        this.ultList = ultList;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return CreateTime;
    }

    public void setCreateTime(Date createTime) {
        CreateTime = createTime;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public BigDecimal getMapMainId() {
        return mapMainId;
    }

    public void setMapMainId(BigDecimal mapMainId) {
        this.mapMainId = mapMainId;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public List<Integer> getDevNoList() {
        return devNoList;
    }

    public void setDevNoList(List<Integer> devNoList) {
        this.devNoList = devNoList;
    }

    public Long getLimit() {
        return limit;
    }

    public void setLimit(Long limit) {
        this.limit = limit;
    }

    public Long getOffset() {
        return offset;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getReinsCode() {
        return reinsCode;
    }

    public void setReinsCode(String reinsCode) {
        this.reinsCode = reinsCode;
    }

    public String getRiskCode() {
        return riskCode;
    }

    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    public String getBusinessNature() {
        return businessNature;
    }

    public void setBusinessNature(String businessNature) {
        this.businessNature = businessNature;
    }

    public String getReinsFlag() {
        return reinsFlag;
    }

    public void setReinsFlag(String reinsFlag) {
        this.reinsFlag = reinsFlag;
    }

    public String getShareholderFlag() {
        return shareholderFlag;
    }

    public void setShareholderFlag(String shareholderFlag) {
        this.shareholderFlag = shareholderFlag;
    }

    public String getComCode() {
        return comCode;
    }

    public void setComCode(String comCode) {
        this.comCode = comCode;
    }

    public String getCenterCode() {
        return centerCode;
    }

    public void setCenterCode(String centerCode) {
        this.centerCode = centerCode;
    }
}