/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2024-04-17 14:06:49
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2024-04-17 14:06:49<br/>
 * Description: ibnr计算配置-发展期配置<br/>
 * Table Name: atr_conf_ibnrcalc_dev<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "ibnr计算配置-发展期配置")
public class AtrConfIbnrcalcQuotaDevVo implements Serializable {
    /**
     * Database column: atr_conf_ibnrcalc_dev.id
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: atr_conf_ibnrcalc_dev.loa_code
     * Database remarks: 业务线
     */
    @ApiModelProperty(value = "业务线", required = true)
    private String loaCode;

    /**
     * Database column: atr_conf_ibnrcalc_dev.dev_no
     * Database remarks: 发展期
     */
    @ApiModelProperty(value = "发展期", required = true)
    private Short devNo;

    /**
     * Database column: atr_conf_ibnrcalc_dev.interest_ratio
     * Database remarks: 利息比率
     */
    @ApiModelProperty(value = "利息比率", required = false)
    private BigDecimal interestRatio;

    /**
     * Database column: atr_conf_ibnrcalc_dev.time_period
     * Database remarks: 时间段
     */
    @ApiModelProperty(value = "时间段", required = false)
    private BigDecimal timePeriod;

    /**
     * Database column: atr_conf_ibnrcalc_dev.oper_id
     * Database remarks: 操作人
     */
    @ApiModelProperty(value = "操作人", required = true)
    private Long operId;

    /**
     * Database column: atr_conf_ibnrcalc_dev.oper_time
     * Database remarks: 操作时间
     */
    @ApiModelProperty(value = "操作时间", required = true)
    private Date operTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public Short getDevNo() {
        return devNo;
    }

    public void setDevNo(Short devNo) {
        this.devNo = devNo;
    }

    public BigDecimal getInterestRatio() {
        return interestRatio;
    }

    public void setInterestRatio(BigDecimal interestRatio) {
        this.interestRatio = interestRatio;
    }

    public BigDecimal getTimePeriod() {
        return timePeriod;
    }

    public void setTimePeriod(BigDecimal timePeriod) {
        this.timePeriod = timePeriod;
    }

    public Long getOperId() {
        return operId;
    }

    public void setOperId(Long operId) {
        this.operId = operId;
    }

    public Date getOperTime() {
        return operTime;
    }

    public void setOperTime(Date operTime) {
        this.operTime = operTime;
    }
}