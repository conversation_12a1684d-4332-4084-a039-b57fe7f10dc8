<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2022-11-11 11:22:45 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussBecfMainDao">
  <!-- 本配置文件由SS MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**IDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->
  <!-- 通用查询结果对象-->
  <resultMap id="VoResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussBecfMainVo">
    <id column="BECF_MAIN_ID" property="becfMainId" jdbcType="DECIMAL" />
    <result column="entity_id" property="entityId" jdbcType="DECIMAL" />
    <result column="YEAR_MONTH" property="yearMonth" jdbcType="VARCHAR" />
    <result column="CURRENCY_CODE" property="currencyCode" jdbcType="VARCHAR" />
    <result column="VALUATION_DATE" property="valuationDate" jdbcType="TIMESTAMP" />
    <result column="BECF_TYPE" property="becfType" jdbcType="VARCHAR" />
    <result column="VERSION_NO" property="versionNo" jdbcType="VARCHAR" />
    <result column="CONFIRM_IS" property="confirmIs" jdbcType="CHAR" />
    <result column="CONFIRM_ID" property="confirmId" jdbcType="DECIMAL" />
    <result column="CONFIRM_TIME" property="confirmTime" jdbcType="TIMESTAMP" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="CREATOR_ID" property="creatorId" jdbcType="DECIMAL" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATOR_ID" property="updatorId" jdbcType="DECIMAL" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="entity_code" property="entityCode" jdbcType="VARCHAR" />
    <result column="entity_e_name" property="entityEName" jdbcType="VARCHAR" />
    <result column="entity_l_name" property="entityLName" jdbcType="VARCHAR" />
    <result column="entity_c_name" property="entityCName" jdbcType="VARCHAR" />

    <result column="confirm_name" property="confirmName" jdbcType="VARCHAR" />
    <result column="creator_name" property="creatorName" jdbcType="VARCHAR" />
    <result column="updator_name" property="updatorName" jdbcType="VARCHAR" />

  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Vo_Column_List">
    t.BECF_MAIN_ID, t.entity_id, t.YEAR_MONTH, t.currency_code, t.VALUATION_DATE, t.BECF_TYPE, t.VERSION_NO,
    t.CONFIRM_IS, t.CONFIRM_ID, t.CONFIRM_TIME, t.REMARK, t.CREATOR_ID, t.CREATE_TIME, t.UPDATOR_ID, 
    t.UPDATE_TIME
  </sql>


  <select id="fuzzySearchPage" flushCache="false"  parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussBecfMainVo" resultMap="VoResultMap" useCache="true">
    select
    <include refid="Vo_Column_List" />
    ,t2.entity_code
    ,t2.entity_c_name
    ,t2.entity_l_name
    ,t2.entity_e_name
    ,cfr.user_name as confirm_name
    ,cr.user_name as creator_name
    ,up.user_name as updator_name
    from ATR_BUSS_BECF_MAIN t
    LEFT JOIN bpluser.bbs_conf_entity t2
    ON t2.entity_id = t.entity_id
    left join bpluser.bpl_saa_user cfr
    on t.CONFIRM_ID = cfr.user_id
    left join bpluser.bpl_saa_user cr
    on t.creator_id = cr.user_id
    left join bpluser.bpl_saa_user up
    on t.updator_id = up.user_id
    <where>
      <include refid="Fuzzy_Query_Conditions"></include>
    </where>
    order by t.YEAR_MONTH asc, t.VERSION_NO desc
  </select>

  <select id="findByMainId" flushCache="false"  parameterType="Long" resultMap="VoResultMap" useCache="true">
    select
    <include refid="Vo_Column_List" />
    ,t2.entity_code
    ,t2.entity_c_name
    ,t2.entity_l_name
    ,t2.entity_e_name
    ,cfr.user_name as confirm_name
    ,cr.user_name as creator_name
    ,up.user_name as updator_name
    from ATR_BUSS_BECF_MAIN t
    LEFT JOIN bpluser.bbs_conf_entity t2
    ON t2.entity_id = t.entity_id
    left join bpluser.bpl_saa_user cfr
    on t.CONFIRM_ID = cfr.user_id
    left join bpluser.bpl_saa_user cr
    on t.creator_id = cr.user_id
    left join bpluser.bpl_saa_user up
    on t.updator_id = up.user_id
    where BECF_MAIN_ID = #{becfMainId,jdbcType=DECIMAL}
  </select>

  <!-- 按对象查询会计期间记录的WHERE部分 -->
  <sql id="Fuzzy_Query_Conditions">
    <trim prefixOverrides="and">
      <if test="entityId != null ">
        and t.entity_id = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
        and t.YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="becfType != null and becfType != ''">
        and t.BECF_TYPE = #{becfType,jdbcType=VARCHAR}
      </if>
      <if test="valuationDateStart != null ">
        and t.VALUATION_DATE >= #{valuationDateStart,jdbcType=TIMESTAMP}
      </if>
      <if test="valuationDateEnd != null ">
        and t.VALUATION_DATE &lt;= #{valuationDateEnd,jdbcType=TIMESTAMP}
      </if>
      <if test="confirmIs != null and confirmIs != ''">
        and t.CONFIRM_IS = #{confirmIs,jdbcType=CHAR}
      </if>
      <if test="versionNo != null and versionNo != ''">
        and t.VERSION_NO = #{versionNo,jdbcType=CHAR}
      </if>
      <if test="creatorName != null and creatorName != ''">
        and cr.user_name = #{creatorName,jdbcType=VARCHAR}
      </if>
    </trim>
  </sql>


  <select id="countConfirm" flushCache="false" useCache="true" resultType="Integer" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussBecfMain">
    select count(1) from ATR_BUSS_BECF_MAIN
    where entity_id = #{entityId,jdbcType=DECIMAL}
      and YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
      and CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
      and BECF_TYPE = #{becfType,jdbcType=VARCHAR}
      and CONFIRM_IS = '1'
  </select>
</mapper>