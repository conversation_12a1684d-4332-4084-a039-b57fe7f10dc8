package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.dd;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class AtrDuctLrcDdIcuPre {

    /** 保单号 */
    private String policyNo;

    /** 批单序号 */
    private String endorseSeqNo;

    /** 险别代码 */
    private String kindCode;

    /** 上期累计已赚保费 */
    private BigDecimal preCumlEdPremium;

    /** 上期累计已赚净额结算手续费 */
    private BigDecimal preCumlEdNetFee;

    /** 上期累计已赚跟单获取费用 */
    private BigDecimal preCumlEdIacf;

    /** 上期累计已赚非跟单获取费用-对内 */
    private BigDecimal preCumlEdIaehcIn;

    /** 上期累计已赚非跟单获取费用-对外 */
    private BigDecimal preCumlEdIaehcOut;

    /** 当期已赚保费 */
    private BigDecimal curEdPremium;

    /** 当期已赚净额结算手续费 */
    private BigDecimal curEdNetFee;

    /** 当期已赚跟单获取费用 */
    private BigDecimal curEdIacf;

    /** 当期已赚非跟单获取费用-对内 */
    private BigDecimal curEdIaehcIn;

    private BigDecimal curEdIaehcOut;

}
