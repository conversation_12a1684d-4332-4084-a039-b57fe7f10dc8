/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-03-08 14:14:11
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.puhua.lrc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-03-08 14:14:11<br/>
 * Description: LRC 计算结果明细(合同组维度，合约分入)<br/>
 * Table Name: ATR_BUSS_TI_LRC_ICG_CALC_DETAIL<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "LRC 计算结果明细(合同组维度，合约分入)")
public class AtrBussTILrcGDevVo implements Serializable {
    /**
     * Database column: ATR_BUSS_TI_LRC_ICG_CALC_DETAIL.ID
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICG_CALC_DETAIL.MAIN_ID
     * Database remarks: 结果表的ID
     */
    @ApiModelProperty(value = "结果表的ID", required = true)
    private Long mainId;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICG_CALC_DETAIL.DEV_NO
     * Database remarks: 发展期
     */
    @ApiModelProperty(value = "发展期", required = false)
    private Short devNo;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICG_CALC_DETAIL.COVERAGE_AMOUNT
     * Database remarks: 保额/限额
     */
    @ApiModelProperty(value = "保额/限额", required = false)
    private BigDecimal coverageAmount;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICG_CALC_DETAIL.CSM_RATE
     * Database remarks: CSM 摊销比例
     */
    @ApiModelProperty(value = "CSM 摊销比例", required = false)
    private BigDecimal csmRate;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMainId() {
        return mainId;
    }

    public void setMainId(Long mainId) {
        this.mainId = mainId;
    }

    public Short getDevNo() {
        return devNo;
    }

    public void setDevNo(Short devNo) {
        this.devNo = devNo;
    }

    public BigDecimal getCoverageAmount() {
        return coverageAmount;
    }

    public void setCoverageAmount(BigDecimal coverageAmount) {
        this.coverageAmount = coverageAmount;
    }

    public BigDecimal getCsmRate() {
        return csmRate;
    }

    public void setCsmRate(BigDecimal csmRate) {
        this.csmRate = csmRate;
    }
}