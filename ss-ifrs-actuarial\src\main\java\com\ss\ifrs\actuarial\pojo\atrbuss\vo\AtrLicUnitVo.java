
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * <br/>
 * Remark:LIC过程数据VO
 * <br/>
 */

public class AtrLicUnitVo implements Serializable {
	
	private static final long serialVersionUID = 1L;
	 
    /** 主键 */
    private Long licMainId;

    /** 事故年月 */
    private String yearMonth;
    
    /** 发展期 */
    private Long devYearMonth;
    
    /** 合同组 */
    private String icgNo;
    
    /** 数据类型 */
    private String dataType;

    /** 计算节点 */
    private String countNode;
    
    private BigDecimal amount;
    
    private String codeType;
    
    private Short section;
    
    // 金额/比例 数据精确度(默认保留两位小数)
    private int leg = 2;

	public Long getLicMainId() {
		return licMainId;
	}

	public void setLicMainId(Long licMainId) {
		this.licMainId = licMainId;
	}

	public String getYearMonth() {
		return yearMonth;
	}

	public void setYearMonth(String yearMonth) {
		this.yearMonth = yearMonth;
	}

	public Long getDevYearMonth() {
		return devYearMonth;
	}

	public void setDevYearMonth(Long devYearMonth) {
		this.devYearMonth = devYearMonth;
	}

	public String getIcgNo() {
		return icgNo;
	}

	public void setIcgNo(String icgNo) {
		this.icgNo = icgNo;
	}

	public String getDataType() {
		return dataType;
	}

	public void setDataType(String dataType) {
		this.dataType = dataType;
	}

	public String getCountNode() {
		return countNode;
	}

	public void setCountNode(String countNode) {
		this.countNode = countNode;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public String getCodeType() {
		return codeType;
	}

	public void setCodeType(String codeType) {
		this.codeType = codeType;
	}

	public Short getSection() {
		return section;
	}

	public void setSection(Short section) {
		this.section = section;
	}

	public int getLeg() {
		return leg;
	}

	public void setLeg(int leg) {
		this.leg = leg;
	}   
   
}