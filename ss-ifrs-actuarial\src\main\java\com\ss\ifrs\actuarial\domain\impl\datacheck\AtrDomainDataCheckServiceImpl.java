package com.ss.ifrs.actuarial.domain.impl.datacheck;

import com.ss.ifrs.actuarial.annotation.TrackActuarialProcess;
import com.ss.ifrs.actuarial.constant.ActuarialConstant;
import com.ss.ifrs.actuarial.domain.datacheck.AtrDomainDataCheckService;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;

import com.ss.library.thread.ThreadUtils;
import com.ss.library.utils.FastJsonUtil;
import com.ss.platform.core.annotation.TrackWorkflowAction;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.ThreadConstant;
import com.ss.platform.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

@Service
@Slf4j
public class AtrDomainDataCheckServiceImpl implements AtrDomainDataCheckService {

 
/*
    @Override
    @TrackActuarialProcess
    public void dataCheck(AtrConfBussPeriodVo accountPeriodVo) {
        *//*
         *  数据检查流程：
         * 1、查询费用分摊检查流程的下属子流程
         * 2、下属子流程循环进行数据检查
         * *//*
        try {
            ExpConfCheckRuleVo accCheckRule = new ExpConfCheckRuleVo();
            accCheckRule.setEntityId(accountPeriodVo.getEntityId());
            accCheckRule.setProcId(accountPeriodVo.getProcId());
            List<ExpConfCheckRuleVo> actReProcDefVos = expBussCheckRuleService.getActProcByCheckRule(accCheckRule);
            if (CollectionUtils.isEmpty(actReProcDefVos)) {
                throw new BusinessException(ExpResponseEnum.ERROR_DATA_CHECK_001);
            }
            List<Future<?>> futureList = new ArrayList<>();
            for (ExpConfCheckRuleVo actReProcDefVo : actReProcDefVos) {
                AtrConfBussPeriodVo param = FastJsonUtil.toJavaBean(FastJsonUtil.toJson(accountPeriodVo), AtrConfBussPeriodVo.class);
                param.setProcId(actReProcDefVo.getProcId());
                futureList.add(ThreadUtils.execAsync(() -> {
                    domainDataCheckService.dataCheckProcess(param, actReProcDefVo);
                }, ThreadConstant.RULE_THREAD_POOL));
            }
            ThreadUtils.waitAllAndThrows(futureList);
        } catch (Exception e) {
            log.error("Error processing rules for task {}, entity {}, book {}, proc {}, creator {}",
                    accountPeriodVo.getTaskCode(), accountPeriodVo.getEntityId(), accountPeriodVo.getBookCode(),
                    accountPeriodVo.getProcId(), accountPeriodVo.getCreatorId(), e);
            throw e;
        }
    }

    *//*
     *  @Description:下属流程节点数据检查
     * *//*
    @TrackWorkflowAction(actionState = "1")
    public void dataCheckProcess(AtrConfBussPeriodVo accountPeriodVo, ExpConfCheckRuleVo accConfCheckRuleVo) {
        Long entityId = accountPeriodVo.getEntityId();
        accConfCheckRuleVo.setEntityId(entityId);
        accConfCheckRuleVo.setProcId(accountPeriodVo.getProcId());
        List<ExpConfCheckRuleVo> dataCheckRuleList = expBussCheckRuleService.getRecCheckRuleList(accConfCheckRuleVo);
        if (CollectionUtils.isNotEmpty(dataCheckRuleList)) {
            List<Future<?>> futureList = new ArrayList<>();
            for (ExpConfCheckRuleVo confCheckRuleVo : dataCheckRuleList) {
                confCheckRuleVo.setParentProcId(accConfCheckRuleVo.getParentProcId());
                futureList.add(ThreadUtils.execAsync(() -> {
                    processRuleCheck(accountPeriodVo, confCheckRuleVo);
                }, ThreadConstant.DATA_SLICE_THREAD_POOL));
            }
           ThreadUtils.waitAllAndThrows(futureList);
        }
        trackDataCheckService.saveStatCheckResult(accountPeriodVo);
    }

    *//*
     * @Description: 执行一条数据检查规则并记录日志到流程明细表
     * *//*
    private void processRuleCheck(AtrConfBussPeriodVo accountPeriodVo, ExpConfCheckRuleVo recCheckRule) {
        String ruleExpr = "";
        StringBuffer ruleExprSql = new StringBuffer();
        Map<String, Object> resultMap = null;
        String actionLogState = "";
        Integer execRuleResult = 0;
        List<String> recItemList = new ArrayList<>();
        log.info("recCheckRule checkID:{}", recCheckRule.getCheckRuleId());
        if (ObjectUtils.isNotEmpty(recCheckRule.getRuleExpr())) {
            ruleExpr = expBussCheckRuleService.analyticalRuleRelyon(accountPeriodVo, recCheckRule.getRelyOn(),
                    recCheckRule.getRuleExpr());
            ruleExprSql.append(ruleExpr);
            resultMap = this.execRuleExprSql(ruleExprSql);
        }
        if (ObjectUtils.isNotEmpty(resultMap)) {
            execRuleResult = (Integer) resultMap.get("execResult");
            recItemList = (List<String>) resultMap.get("execList");
        }
        actionLogState = this.getActionState(recCheckRule, execRuleResult);
        recCheckRule.setCheckState(actionLogState);
        trackDataCheckService.trackCheckRuleResult(accountPeriodVo, recCheckRule, recItemList);
    }

    *//*
     * @Description 规则脚本执行结果
     * @Param : [ruleExprSql, trim]
     * @return Integer
     * *//*
    private Map<String, Object> execRuleExprSql(StringBuffer ruleExprSql) {
        Map sqlResultMap = new HashMap(4);
        List<String> sqlValueResult = new ArrayList<>();
        String ruleExpr = ruleExprSql.toString();
        int execRuleResult = 0;
        int countIndex = ruleExpr.indexOf("COUNT(");
        if (countIndex > 0) {
            execRuleResult = expBussCheckRuleService.executeExprCountSql(ruleExpr);
            sqlValueResult.add(Integer.toString(execRuleResult));
        } else {
            ruleExprSql = new StringBuffer("");
            String prefixExprSql = ruleExpr.substring(0, ruleExpr.indexOf("FROM"));
            String suffixExprSql = ruleExpr.substring(ruleExpr.indexOf("FROM"));
            ruleExprSql.append(prefixExprSql).append("as expression ").append(suffixExprSql);
            List<String> recItemList = expBussCheckRuleService.executeRuleExprSql(ruleExprSql.toString());
            sqlValueResult = recItemList;
            if (ObjectUtils.isNotEmpty(recItemList)) {
                execRuleResult = 1;
            }
        }
        sqlResultMap.put("execResult", execRuleResult);
        sqlResultMap.put("execList", sqlValueResult);
        return sqlResultMap;
    }


    *//*
     * @Description 规则方向加执行结果得出流程状态
     * @Param : [ruleExprSql, trim]
     * @return Integer
     * *//*
    private String getActionState(ExpConfCheckRuleVo checkRule, Integer execRuleResult) {
        String result;
        if (ObjectUtils.isEmpty(execRuleResult)) {
            result = CommonConstant.BussActionState.STATE_TWO;
        } else {
            if (CommonConstant.RuleDirection.FORWARD.equals(checkRule.getRuleDirection())) {
                result = execRuleResult > 0 ? CommonConstant.BussActionState.STATE_ONE :
                        CommonConstant.BussActionState.STATE_TWO;
            } else {
                result = execRuleResult > 0 ? CommonConstant.BussActionState.STATE_TWO :
                        CommonConstant.BussActionState.STATE_ONE;
            }
        }
        if (CommonConstant.RuleType.WARNING.equals(checkRule.getWarningType()) && result.equals(CommonConstant.BussActionState.STATE_TWO)) {
            result = CommonConstant.BussActionState.STATE_THREE;
        }
        return result;
    }*/
}
