package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.dao.AtrBussLrcDao;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.AtrBussLrcAction;
import com.ss.ifrs.actuarial.util.EcfUtil;
import com.ss.library.utils.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 预期保费现金流 service
 * <AUTHOR>
 */
@Service
@Slf4j
public class AtrBussLrcService {

    @Resource
    private AtrBussLrcDao atrBussLrcDao;

    public void entry(Long entityId, String yearMonth, String businessSourceCode) {
        String status = "S";
        String actionNo = EcfUtil.createActionNo();
        try {
            createAction(actionNo, entityId, yearMonth, businessSourceCode);
            if ("DD".equals(businessSourceCode)) {
                AtrBussLrcDdService service = SpringContextUtil.getBean(AtrBussLrcDdService.class);
                service.entry(actionNo, entityId, yearMonth);
            } else if ("FO".equals(businessSourceCode)) {
                AtrBussLrcFoService service = SpringContextUtil.getBean(AtrBussLrcFoService.class);
                service.entry(actionNo, entityId, yearMonth);
            } else if ("TI".equals(businessSourceCode)) {
                AtrBussLrcTiService service = SpringContextUtil.getBean(AtrBussLrcTiService.class);
                service.entry(actionNo, entityId, yearMonth);
            } else if ("TO".equals(businessSourceCode)) {
                AtrBussLrcToService service = SpringContextUtil.getBean(AtrBussLrcToService.class);
                service.entry(actionNo, entityId, yearMonth);
            }
        } catch (Exception e) {
            status = "E";
            log.error("{} lrc error", businessSourceCode, e);
            throw new RuntimeException(e);
        } finally {
            updateAction(actionNo, status);
        }
    }

    private void createAction(String actionNo, Long entityId, String yearMonth, String businessSourceCode) {
        AtrBussLrcAction vo = new AtrBussLrcAction();
        vo.setActionNo(actionNo);
        vo.setYearMonth(yearMonth);
        vo.setEntityId(entityId);
        vo.setBusinessSourceCode(businessSourceCode);
        vo.setCreateTime(new Date());
        atrBussLrcDao.createAction(vo);
    }

    private void updateAction(String actionNo, String status) {
        AtrBussLrcAction vo = new AtrBussLrcAction();
        vo.setActionNo(actionNo);
        vo.setUpdateTime(new Date());
        vo.setStatus(status);
        atrBussLrcDao.updateActionStatus(vo);
    }

}
