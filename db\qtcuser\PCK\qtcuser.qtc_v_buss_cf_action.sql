CREATE OR REPLACE VIEW QTC_V_BUSS_CF_ACTION AS
SELECT 'Prem' AS cf_calc_type,
    t.task_code AS action_no,
    t.entity_id,
    t.year_month,
    t.currency_code,
    'DD' AS business_source_code,
    t.loa_code
   FROM qtc_dap_ecf_dd_icu t
  WHERE  t.loa_code IS NOT NULL
  GROUP BY t.task_code, t.entity_id, t.year_month, t.currency_code,   t.loa_code
UNION
 SELECT 'Prem' AS cf_calc_type,
    t.task_code,
    t.entity_id,
    t.year_month,
    t.currency_code,
    'FO' AS business_source_code,
    t.loa_code
   FROM qtc_dap_ecf_fo_icu t
  WHERE  t.loa_code IS NOT NULL
  GROUP BY t.task_code, t.entity_id, t.year_month, t.currency_code,  t.loa_code
UNION
 SELECT 'Prem' AS cf_calc_type,
    t.task_code AS action_no,
    t.entity_id,
    t.year_month,
    t.currency_code,
    'TI' AS business_source_code,
    t.loa_code
   FROM qtc_dap_ecf_ti_icu t
  WHERE   t.loa_code IS NOT NULL
  GROUP BY t.task_code, t.entity_id, t.year_month, t.currency_code,  t.loa_code
UNION
 SELECT 'Prem' AS cf_calc_type,
    t.task_code AS action_no,
    t.entity_id,
    t.year_month,
    t.currency_code,
    'TO' AS business_source_code,
    t.loa_code
   FROM qtc_dap_ecf_to_icu t
  WHERE  t.loa_code IS NOT NULL
  GROUP BY t.task_code, t.entity_id, t.year_month, t.currency_code, t.loa_code
UNION
 SELECT 'Claim' AS cf_calc_type,
    t.task_code AS action_no,
    t.entity_id,
    t.year_month,
    '' AS currency_code,
    'DD' AS business_source_code,
    t.loa_code
   FROM qtc_dap_ecf_claim_dd_icg_cf t
  WHERE  t.loa_code IS NOT NULL
  GROUP BY t.task_code, t.entity_id, t.year_month,t.loa_code
UNION
 SELECT 'Claim' AS cf_calc_type,
    t.task_code AS action_no,
    t.entity_id,
    t.year_month,
    '' AS currency_code,
    'FO' AS business_source_code,
    t.loa_code
   FROM qtc_dap_ecf_claim_dd_icg_cf t
  WHERE t.loa_code IS NOT NULL
  GROUP BY t.task_code, t.entity_id, t.year_month,t.loa_code
UNION
 SELECT 'Claim' AS cf_calc_type,
    t.task_code AS action_no,
    t.entity_id,
    t.year_month,
    '' AS currency_code,
    'TI' AS business_source_code,
    t.loa_code
   FROM qtc_dap_ecf_claim_dd_icg_cf t
  WHERE  t.loa_code IS NOT NULL
  GROUP BY t.task_code, t.entity_id, t.year_month,  t.loa_code
UNION
 SELECT 'Claim' AS cf_calc_type,
    t.task_code AS action_no,
    t.entity_id,
    t.year_month,
    '' AS currency_code,
    'TO' AS business_source_code,
    t.loa_code
   FROM qtc_dap_ecf_claim_dd_icg_cf t
  WHERE t.loa_code IS NOT NULL
  GROUP BY t.task_code, t.entity_id, t.year_month,t.loa_code;