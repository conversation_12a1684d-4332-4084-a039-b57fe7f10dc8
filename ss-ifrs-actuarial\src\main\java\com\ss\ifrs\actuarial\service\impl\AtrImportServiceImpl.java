package com.ss.ifrs.actuarial.service.impl;


import com.ss.ifrs.actuarial.conf.AppConfig;
import com.ss.ifrs.actuarial.feign.BmsTrackExportFeignClient;
import com.ss.ifrs.actuarial.service.AtrImportService;
import com.ss.platform.pojo.bms.log.vo.BmsLogExportTrackVo;
import com.ss.platform.core.constant.SystemConstant;
import com.ss.library.utils.FileUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;

@Service
public class AtrImportServiceImpl implements AtrImportService {

    protected final Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    AppConfig appConfig;


    @Autowired
    BmsTrackExportFeignClient bmsTrackExportFeignClient;

    /*
    * 传入Page对象导出
    * */
    @Override
    public void importFile(MultipartFile file, String targetRouter, Long userId) throws Exception {

        String fileName = file.getOriginalFilename().substring(0,file.getOriginalFilename().lastIndexOf("."));

        String targetFilePath = getOutPutPath();
        String saveFilePath = getOutPutPathSave();
        Date outPutTime = new Date();
        SimpleDateFormat formatterTime = new SimpleDateFormat("HHmmss");
        String formatterTimeCode = formatterTime.format(outPutTime);
        String savefileName = fileName + formatterTimeCode;
        try {
            File folder = new File(targetFilePath);
            if (!folder.exists()) {
                folder.mkdirs();
            }
            FileUtil.uploadFile(file, targetFilePath, savefileName);

            String exportFileName = fileName + ".xlsx";
            BmsLogExportTrackVo bmsLogExportTrackVo = new BmsLogExportTrackVo();
            bmsLogExportTrackVo.setFileName(exportFileName);
            bmsLogExportTrackVo.setTargetRouter(targetRouter);
            bmsLogExportTrackVo.setFilePath(saveFilePath + savefileName + ".xlsx");
            bmsLogExportTrackVo.setSystemCode(SystemConstant.AtrIdentity.APP_CODE);
            bmsLogExportTrackVo.setDirection("0");
            bmsLogExportTrackVo.setCreateTime(outPutTime);
            bmsLogExportTrackVo.setCreatorId(userId);
            bmsTrackExportFeignClient.save(bmsLogExportTrackVo);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            throw e;
        }
    }


    /*
     * 传入Page对象导出
     * */
    @Override
    public void importExcelTemplate(MultipartFile file) throws Exception {
        String fileName = file.getOriginalFilename().substring(0,file.getOriginalFilename().lastIndexOf("."));

        String targetFilePath = getTemplatePath();
        try {
            File folder = new File(targetFilePath);
            if (!folder.exists()) {
                folder.mkdirs();
            }
            FileUtil.uploadFile(file, targetFilePath, fileName);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            throw e;
        }
    }
    protected String getTemplatePath() {
        String basePath = appConfig.getBasePath();
        String templatePath = appConfig.getExportExcelTemplate();
        String srcFilePath = basePath + templatePath;
        return srcFilePath;
    }

    protected String getOutPutPath() {
        Date outPutTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy/MM/dd");
        String formattedDate = formatter.format(outPutTime);
        String basePath = appConfig.getBasePath();
        String outPutPath = appConfig.getExportExcelOutTemplate();
        String targetFilePath = basePath + outPutPath + formattedDate + "/";
        return targetFilePath;
    }

    protected String getOutPutPathSave() {
        Date outPutTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy/MM/dd");
        String formattedDate = formatter.format(outPutTime);
        String outPutPath = appConfig.getExportExcelOutTemplate();
        String saveFilePath = outPutPath + formattedDate + "/";
        return saveFilePath;
    }

}
