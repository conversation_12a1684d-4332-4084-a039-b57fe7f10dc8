package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to;

import com.ss.ifrs.actuarial.util.abp.IgnoreCol;
import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Tab("atr_buss_to_lrc_g_dev")
@Data
public class AtrBussLrcToIcgDev {

    private Long mainId;

    private Integer devNo;

    private BigDecimal edRatio;

    private BigDecimal edPremium;

    /** 已赚净额结算 (ed_net_fee) */
    private BigDecimal edNetFee;

    private BigDecimal recvPremium;

    private BigDecimal netFee;

    @IgnoreCol
    private Date devDate;

}
