create table RPT_CONF_CHECKRULE(
   CHECK_RULE_ID int8 NOT NULL,
   ENTITY_ID int8 NOT NULL,
   BOOK_CODE varchar(8),
   RULE_CODE varchar(10),
   RULE_E_NAME varchar(1000),
   RULE_C_NAME varchar(1000),
   RULE_L_NAME varchar(1000),
   EFFECTIVE_DATE DATE,
   EXPRIRATION_DATE DATE,
   FIR_VALUE varchar(200),
   RULE_OPERATOR varchar(1),
   SEC_VALUE varchar(200),
   ERROR_MSG_EN varchar(1000),
   ERROR_MSG_CN varchar(1000),
   ERROR_MSG_TN varchar(1000),
   REMAR<PERSON> varchar(4000),
   VALID_IS varchar(1),
   CHECKED_TIME TIMESTAMP (6),
   CHECKED_ID int8,
   AUDIT_STATE varchar(1),
   CHECKED_MSG varchar(256),
   SERIAL_NO int4,
   DISPLAY_NO int4,
   CREATOR_ID int8,
   UPDATOR_ID int8,
   CREATE_TIME TIMESTAMP (6),
   UPDATE_TIME TIMESTAMP (6),
   PRIMARY KEY (CHECK_RULE_ID)
);

COMMENT ON COLUMN RPT_CONF_CHECKRULE.CHECK_RULE_ID IS '主键ID';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.ENTITY_ID IS '机构ID';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.BOOK_CODE IS '账套编码';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.RULE_CODE IS '规则编码';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.RULE_E_NAME IS '规则英文名称';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.RULE_C_NAME IS '规则简体中文名称';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.RULE_L_NAME IS '规则繁体中文名称';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.EFFECTIVE_DATE IS '生效日期';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.EXPRIRATION_DATE IS '失效日期';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.FIR_VALUE IS '因子值A';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.RULE_OPERATOR IS '操作类型';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.SEC_VALUE IS '因子值B';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.ERROR_MSG_EN IS '英文异常提示信息';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.ERROR_MSG_CN IS '简体中文异常提示信息';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.ERROR_MSG_TN IS '繁体中文异常提示信息';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.REMARK IS '备注';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.VALID_IS IS '有效标志';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.CHECKED_TIME IS '审核时间';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.CHECKED_ID IS '审核人';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.AUDIT_STATE IS '审核状态';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.CHECKED_MSG IS '审核意见';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.SERIAL_NO IS '版本号';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.DISPLAY_NO IS '排序';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.CREATOR_ID IS '创建人';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.UPDATOR_ID IS '最后变更经手人';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN RPT_CONF_CHECKRULE.UPDATE_TIME IS '最后变更时间';

CREATE SEQUENCE rptuser.rpt_seq_conf_checkrule
    INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1001
CACHE 40;

ALTER SEQUENCE rptuser.rpt_seq_conf_checkrule OWNER TO rptuser;