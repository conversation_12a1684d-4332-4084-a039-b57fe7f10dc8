<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2022-07-08 15:17:45 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.conf.AtrConfRiskRefDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfRiskRef">
    <id column="risk_ref_id" property="riskRefId" jdbcType="NUMERIC" />
    <result column="entity_id" property="entityId" jdbcType="NUMERIC" />
    <result column="atr_type" property="atrType" jdbcType="VARCHAR" />
    <result column="business_model" property="businessModel" jdbcType="VARCHAR" />
    <result column="business_direction" property="businessDirection" jdbcType="VARCHAR" />
    <result column="loa_code" property="loaCode" jdbcType="VARCHAR" />
    <result column="checked_id" property="checkedId" jdbcType="NUMERIC" />
    <result column="checked_time" property="checkedTime" jdbcType="TIMESTAMP" />
    <result column="audit_state" property="auditState" jdbcType="CHAR" />
    <result column="checked_msg" property="checkedMsg" jdbcType="VARCHAR" />
    <result column="valid_is" property="validIs" jdbcType="CHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="creator_id" property="creatorId" jdbcType="NUMERIC" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="updator_id" property="updatorId" jdbcType="NUMERIC" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="serial_no" property="serialNo" jdbcType="NUMERIC" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    risk_ref_id, entity_id, atr_type, business_model, business_direction, loa_code, checked_id,
    checked_time, audit_state, checked_msg, valid_is, remark, creator_id, create_time, 
    updator_id, update_time,serial_no
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="riskRefId != null ">
          and risk_ref_id = #{riskRefId,jdbcType=NUMERIC}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=NUMERIC}
      </if>
      <if test="atrType != null and atrType != ''">
          and atr_type = #{atrType,jdbcType=VARCHAR}
      </if>
      <if test="businessModel != null and businessModel != ''">
          and business_model = #{businessModel,jdbcType=VARCHAR}
      </if>
      <if test="businessDirection != null and businessDirection != ''">
          and business_direction = #{businessDirection,jdbcType=VARCHAR}
      </if>
      <if test="loaCode != null and loaCode != ''">
          and loa_code = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="checkedId != null ">
          and checked_id = #{checkedId,jdbcType=NUMERIC}
      </if>
      <if test="checkedTime != null ">
          and checked_time = #{checkedTime,jdbcType=TIMESTAMP}
      </if>
      <if test="auditState != null ">
          and audit_state = #{auditState,jdbcType=CHAR}
      </if>
      <if test="checkedMsg != null and checkedMsg != ''">
          and checked_msg = #{checkedMsg,jdbcType=VARCHAR}
      </if>
      <if test="validIs != null ">
          and valid_is = #{validIs,jdbcType=CHAR}
      </if>
      <if test="remark != null and remark != ''">
          and remark = #{remark,jdbcType=VARCHAR}
      </if>
      <if test="creatorId != null ">
          and creator_id = #{creatorId,jdbcType=NUMERIC}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and updator_id = #{updatorId,jdbcType=NUMERIC}
      </if>
      <if test="updateTime != null ">
          and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.riskRefId != null ">
          and risk_ref_id = #{condition.riskRefId,jdbcType=NUMERIC}
      </if>
      <if test="condition.entityId != null ">
          and entity_id = #{condition.entityId,jdbcType=NUMERIC}
      </if>
      <if test="condition.atrType != null and condition.atrType != ''">
          and atr_type = #{condition.atrType,jdbcType=VARCHAR}
      </if>
      <if test="condition.businessModel != null and condition.businessModel != ''">
          and business_model = #{condition.businessModel,jdbcType=VARCHAR}
      </if>
      <if test="condition.businessDirection != null and condition.businessDirection != ''">
          and business_direction = #{condition.businessDirection,jdbcType=VARCHAR}
      </if>
      <if test="condition.loaCode != null and condition.loaCode != ''">
          and loa_code = #{condition.loaCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.checkedId != null ">
          and checked_id = #{condition.checkedId,jdbcType=NUMERIC}
      </if>
      <if test="condition.checkedTime != null ">
          and checked_time = #{condition.checkedTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.auditState != null ">
          and audit_state = #{condition.auditState,jdbcType=CHAR}
      </if>
      <if test="condition.checkedMsg != null and condition.checkedMsg != ''">
          and checked_msg = #{condition.checkedMsg,jdbcType=VARCHAR}
      </if>
      <if test="condition.validIs != null ">
          and valid_is = #{condition.validIs,jdbcType=CHAR}
      </if>
      <if test="condition.remark != null and condition.remark != ''">
          and remark = #{condition.remark,jdbcType=VARCHAR}
      </if>
      <if test="condition.creatorId != null ">
          and creator_id = #{condition.creatorId,jdbcType=NUMERIC}
      </if>
      <if test="condition.createTime != null ">
          and create_time = #{condition.createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.updatorId != null ">
          and updator_id = #{condition.updatorId,jdbcType=NUMERIC}
      </if>
      <if test="condition.updateTime != null ">
          and update_time = #{condition.updateTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="riskRefId != null ">
          and risk_ref_id = #{riskRefId,jdbcType=NUMERIC}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=NUMERIC}
      </if>
      <if test="atrType != null and atrType != ''">
          and atr_type = #{atrType,jdbcType=VARCHAR}
      </if>
      <if test="businessModel != null and businessModel != ''">
          and business_model = #{businessModel,jdbcType=VARCHAR}
      </if>
      <if test="businessDirection != null and businessDirection != ''">
          and business_direction = #{businessDirection,jdbcType=VARCHAR}
      </if>
      <if test="loaCode != null and loaCode != ''">
          and loa_code = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="checkedId != null ">
          and checked_id = #{checkedId,jdbcType=NUMERIC}
      </if>
      <if test="checkedTime != null ">
          and checked_time = #{checkedTime,jdbcType=TIMESTAMP}
      </if>
      <if test="auditState != null ">
          and audit_state = #{auditState,jdbcType=CHAR}
      </if>
      <if test="checkedMsg != null and checkedMsg != ''">
          and checked_msg = #{checkedMsg,jdbcType=VARCHAR}
      </if>
      <if test="validIs != null ">
          and valid_is = #{validIs,jdbcType=CHAR}
      </if>
      <if test="remark != null and remark != ''">
          and remark = #{remark,jdbcType=VARCHAR}
      </if>
      <if test="creatorId != null ">
          and creator_id = #{creatorId,jdbcType=NUMERIC}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and updator_id = #{updatorId,jdbcType=NUMERIC}
      </if>
      <if test="updateTime != null ">
          and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from ATR_CONF_RISK_REF
    where RISK_REF_ID = #{riskRefId,jdbcType=DECIMAL}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from ATR_CONF_RISK_REF
    where RISK_REF_ID in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ATR_CONF_RISK_REF
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfRiskRef">
    select 
    <include refid="Base_Column_List" />
    from ATR_CONF_RISK_REF
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from ATR_CONF_RISK_REF
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="risk_ref_id" keyProperty="riskRefId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfRiskRef">
    <selectKey resultType="long" keyProperty="riskRefId" order="BEFORE">
      select nextval('ATR_SEQ_CONF_RISK_REF') as sequenceNo 
    </selectKey>
    insert into ATR_CONF_RISK_REF
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="riskRefId != null">
        risk_ref_id,
      </if>
      <if test="entityId != null">
        entity_id,
      </if>
      <if test="atrType != null">
        atr_type,
      </if>
      <if test="businessModel != null">
        business_model,
      </if>
      <if test="businessDirection != null">
        business_direction,
      </if>
      <if test="loaCode != null">
        loa_code,
      </if>
      <if test="checkedId != null">
        checked_id,
      </if>
      <if test="checkedTime != null">
        checked_time,
      </if>
      <if test="auditState != null">
        audit_state,
      </if>
      <if test="checkedMsg != null">
        checked_msg,
      </if>
      <if test="validIs != null">
        valid_is,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updatorId != null">
        updator_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="serialNo != null">
        serial_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="riskRefId != null">
        #{riskRefId,jdbcType=NUMERIC},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=NUMERIC},
      </if>
      <if test="atrType != null">
        #{atrType,jdbcType=VARCHAR},
      </if>
      <if test="businessModel != null">
        #{businessModel,jdbcType=VARCHAR},
      </if>
      <if test="businessDirection != null">
        #{businessDirection,jdbcType=VARCHAR},
      </if>
      <if test="loaCode != null">
        #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="checkedId != null">
        #{checkedId,jdbcType=NUMERIC},
      </if>
      <if test="checkedTime != null">
        #{checkedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditState != null">
        #{auditState,jdbcType=CHAR},
      </if>
      <if test="checkedMsg != null">
        #{checkedMsg,jdbcType=VARCHAR},
      </if>
      <if test="validIs != null">
        #{validIs,jdbcType=CHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=NUMERIC},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        #{updatorId,jdbcType=NUMERIC},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=NUMERIC},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all 
    <foreach collection="list" item="item" index="index">
      into ATR_CONF_RISK_REF values 
       (#{item.riskRefId,jdbcType=NUMERIC}, #{item.entityId,jdbcType=NUMERIC}, #{item.atrType,jdbcType=VARCHAR},
        #{item.businessModel,jdbcType=VARCHAR}, #{item.businessDirection,jdbcType=VARCHAR},
        #{item.loaCode,jdbcType=VARCHAR}, #{item.checkedId,jdbcType=NUMERIC}, #{item.checkedTime,jdbcType=TIMESTAMP},
        #{item.auditState,jdbcType=CHAR}, #{item.checkedMsg,jdbcType=VARCHAR}, #{item.validIs,jdbcType=CHAR},
        #{item.remark,jdbcType=VARCHAR}, #{item.creatorId,jdbcType=NUMERIC}, #{item.createTime,jdbcType=TIMESTAMP},
        #{item.updatorId,jdbcType=NUMERIC}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
    select 1 
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfRiskRef">
    update ATR_CONF_RISK_REF
    <set>
      <if test="entityId != null">
        entity_id = #{entityId,jdbcType=NUMERIC},
      </if>
      <if test="atrType != null">
        atr_type = #{atrType,jdbcType=VARCHAR},
      </if>
      <if test="businessModel != null">
        business_model = #{businessModel,jdbcType=VARCHAR},
      </if>
      <if test="businessDirection != null">
        business_direction = #{businessDirection,jdbcType=VARCHAR},
      </if>
      <if test="loaCode != null">
        loa_code = #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="checkedId != null">
        checked_id = #{checkedId,jdbcType=NUMERIC},
      </if>
      <if test="checkedTime != null">
        checked_time = #{checkedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditState != null">
        audit_state = #{auditState,jdbcType=CHAR},
      </if>
      <if test="checkedMsg != null">
        checked_msg = #{checkedMsg,jdbcType=VARCHAR},
      </if>
      <if test="validIs != null">
        valid_is = #{validIs,jdbcType=CHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=NUMERIC},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        updator_id = #{updatorId,jdbcType=NUMERIC},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="serialNo != null">
        serial_no = #{serialNo,jdbcType=NUMERIC},
      </if>
    </set>
    where risk_ref_id = #{riskRefId,jdbcType=NUMERIC}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfRiskRef">
    update ATR_CONF_RISK_REF
    <set>
      <if test="record.entityId != null">
        entity_id = #{record.entityId,jdbcType=NUMERIC},
      </if>
      <if test="record.atrType != null">
        atr_type = #{record.atrType,jdbcType=VARCHAR},
      </if>
      <if test="record.businessModel != null">
        business_model = #{record.businessModel,jdbcType=VARCHAR},
      </if>
      <if test="record.businessDirection != null">
        business_direction = #{record.businessDirection,jdbcType=VARCHAR},
      </if>
      <if test="record.loaCode != null">
        loa_code = #{record.loaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.checkedId != null">
        checked_id = #{record.checkedId,jdbcType=NUMERIC},
      </if>
      <if test="record.checkedTime != null">
        checked_time = #{record.checkedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.auditState != null">
        audit_state = #{record.auditState,jdbcType=CHAR},
      </if>
      <if test="record.checkedMsg != null">
        checked_msg = #{record.checkedMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.validIs != null">
        valid_is = #{record.validIs,jdbcType=CHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=NUMERIC},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatorId != null">
        updator_id = #{record.updatorId,jdbcType=NUMERIC},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.serialNo != null">
        serial_no = #{record.serialNo,jdbcType=NUMERIC},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from ATR_CONF_RISK_REF
    where RISK_REF_ID = #{riskRefId,jdbcType=DECIMAL}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from ATR_CONF_RISK_REF
    where RISK_REF_ID in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from ATR_CONF_RISK_REF
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfRiskRef">
    select count(1) from ATR_CONF_RISK_REF
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>