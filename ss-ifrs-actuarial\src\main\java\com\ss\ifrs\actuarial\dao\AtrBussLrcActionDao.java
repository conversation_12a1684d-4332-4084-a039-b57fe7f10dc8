/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-02-08 17:38:36
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao;


import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcAction;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLrcActionVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-08 17:38:36<br/>
 * Description: LRC 预期现金流操作表 Dao类<br/>
 * Related Table Name: ATR_BUSS_LRC_ACTION<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrBussLrcActionDao extends IDao<AtrBussLrcAction, Long> {

    Page<AtrBussLrcActionVo> fuzzySearchPage(AtrBussLrcActionVo atrBussLrcActionVo, Pageable pageParam);

    AtrBussLrcActionVo findByid(Long id);

    Page<AtrDapDrawVo> findPortfolioData(AtrBussLrcActionVo atrBussLrcActionVo, Pageable pageParam);

    Integer updateConfirm(AtrBussLrcAction atrBussLrcAction);

    Integer revoke(AtrBussLrcAction atrBussLrcAction);

    String createActionNo(@Param("ran") double ran);
    void calcStep1(Map<?, ?> paramMap);
    void calcStep2Threads(Map<?, ?> paramMap);
    void calcStep3(Map<?, ?> paramMap);
    void calcStep4Clean(Map<?, ?> paramMap);
    void addErrorLog(Map<?, ?> paramMap);

    void reSetLrcCashFlow(AtrConfBussPeriodVo atrConfBussPeriodVo);
}