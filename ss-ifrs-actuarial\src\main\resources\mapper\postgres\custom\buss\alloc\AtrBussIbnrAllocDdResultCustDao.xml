<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by Taiping MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2025-03-24 19:27:36 -->
<!-- Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.alloc.AtrBussIbnrAllocDdResultDao">
  <!-- 本配置文件由Taiping MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**BaseDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->

  <insert id="saveDuctAllocEp">
    INSERT INTO atr_duct_ibnr_alloc_ep (ID, action_no, data_type, entity_id,
             year_month, acc_year_month,accident_quarter,
             portfolio_no,icg_no, icg_no_name, pl_judge_rslt, center_code,
             fin_detail_code, fin_product_code, fin_sub_product_code,
             fin_acc_channel, dept_id, channel_id, company_code4,
             risk_class_code, policy_no, kind_code,business_type, ep_amount )
    SELECT
       nextval( 'atr_seq_duct_ibnr_alloc_ep' ) AS ID,
       #{allocAction.actionNo,jdbcType=VARCHAR} as action_no,
       '1' as data_type,
       bla.entity_id,
       bla.year_month,
       bdl.year_month as acc_year_month,
       TO_CHAR( to_date( bdl.year_month, 'YYYYMM' ), 'YYYY"Q"Q' ),
       bdl.portfolio_no,
       bdl.icg_no,
       bdl.icg_name AS icg_no_name,
       bdl.pl_judge_rslt,
       bdl.center_code,
       bdl.fin_detail_code,
       bdl.fin_product_code,
       bdl.fin_sub_product_code,
       bdl.fin_acc_channel,
       bdl.dept_id,
       bdl.channel_id,
       bdl.company_code4,
       bdl.risk_class_code,
       bdl.policy_no,
       bdl.kind_code,
       bdl.business_type,
       bdl.cur_ed_premium ep_amount
    FROM atr_buss_lrc_action bla,
      atr_buss_dd_lrc_u bdl
    WHERE bla.entity_id = #{allocAction.entityId,jdbcType=DECIMAL}
      AND bla.business_source_code = 'DD'
      AND bla.confirm_is='1'
      AND bla.year_month between substr(${allocAction.yearMonth}||'',1,4)||'01' and #{allocAction.yearMonth,jdbcType=VARCHAR}
      AND bla.action_no = bdl.action_no
  </insert>

  <insert id="saveDuctAllocEpSum">
      INSERT INTO atr_duct_ibnr_alloc_ep (ID, action_no, data_type, entity_id, year_month,
       acc_year_month,accident_quarter,portfolio_no,
       icg_no,icg_no_name,pl_judge_rslt, center_code,
       fin_detail_code, fin_product_code, fin_sub_product_code,
       fin_acc_channel, dept_id, channel_id, company_code4,
       risk_class_code, policy_no, kind_code, business_type,
       ep_amount, ep_sum_amount )
      SELECT
          nextval( 'atr_seq_duct_ibnr_alloc_ep' ) AS ID,
          diae.action_no,
          '2' as data_type,
          diae.entity_id,
          diae.year_month,
          diae.acc_year_month,
          accident_quarter,
          diae.portfolio_no,
          diae.icg_no,
          diae.icg_no_name,
          diae.pl_judge_rslt,
          diae.center_code,
          diae.fin_detail_code, diae.fin_product_code, diae.fin_sub_product_code,
          diae.fin_acc_channel, diae.dept_id, diae.channel_id, diae.company_code4,
          diae.risk_class_code,
          diae.policy_no,
          diae.kind_code,
          diae.business_type,
          diae.ep_amount,
          SUM(ep_amount) OVER (PARTITION BY entity_id,accident_quarter,center_code,risk_class_code) AS ep_sum_amount
      FROM
          atr_duct_ibnr_alloc_ep diae
      WHERE diae.action_no = #{actionNo,jdbcType=VARCHAR} and data_type='1'
  </insert>

    <insert id="saveDuctImportSum">
        INSERT INTO atr_duct_ibnr_import_ep (
            ibnr_main_id,
            action_no,
            business_model,
            year_month,
            accident_quarter,
            risk_class_code,
            center_code,
            ibnr_amount,
            create_time
        )
        select  ibnr_main_id,
                #{allocAction.actionNo,jdbcType=VARCHAR} as action_no,
                business_model,
                year_month,
                accident_quarter,
                risk_class_code,
                center_code,
                ibnr_amount,
                LOCALTIMESTAMP as create_time
        from (
        SELECT
            ibnr_main_id,
            business_model,
            risk_class_code,
            year_month,
            accident_quarter,
            center_code,
            SUM (ibnr_amount) AS ibnr_amount,
            ROW_NUMBER() OVER (PARTITION BY ibnr_main_id, business_model,center_code, risk_class_code, accident_quarter ORDER BY year_month DESC ) AS rn
        FROM atr_buss_ibnr_import_detail biid
        where ibnr_main_id = #{ibnrImport.ibnrMainId,jdbcType=DECIMAL}
          and business_model='DD'
        group by ibnr_main_id,business_model,risk_class_code, accident_quarter,year_month,center_code
       ) t where rn=1
    </insert>

    <insert id="saveBussAllocData">
        INSERT INTO atr_buss_ibnr_alloc_dd_result (
            ID,
            action_no,
            entity_id,
            year_month,
            portfolio_no,
            icg_no,
            icg_no_name,
            pl_judge_rslt,
            acc_year_month,
            risk_class_code,
            center_code,
            fin_detail_code, fin_product_code, fin_sub_product_code,
            fin_acc_channel, dept_id, channel_id, company_code4,
            policy_no,
            kind_code,
            business_type,
            ep_amount,
            ep_ratio,
            ibnr_amount,
            create_time
        )
        SELECT
            nextval( 'atr_seq_buss_ibnr_alloc_dd_result' ) AS ID,
            biaa.action_no,
            biaa.entity_id,
            biaa.year_month,
            diae.portfolio_no,
            diae.icg_no,
            diae.icg_no_name,
            diae.pl_judge_rslt,
            diae.acc_year_month,
            diae.risk_class_code,
            diae.center_code,
            diae.fin_detail_code, diae.fin_product_code, diae.fin_sub_product_code,
            diae.fin_acc_channel, diae.dept_id, diae.channel_id, diae.company_code4,
            diae.policy_no,
            diae.kind_code,
            diae.business_type,
            diae.ep_amount,
            round(diae.ep_amount/diae.ep_sum_amount,8),
            round(biid.ibnr_amount * diae.ep_amount/diae.ep_sum_amount,8),
            LOCALTIMESTAMP as create_time
        FROM atr_buss_ibnr_alloc_action biaa
     LEFT JOIN atr_duct_ibnr_import_ep biid ON
         biaa.action_no=biid.action_no and  biaa.business_source_code = biid.business_model
     LEFT JOIN  atr_duct_ibnr_alloc_ep diae
         on biaa.entity_id=diae.entity_id
        and biaa.action_no=diae.action_no
        and biid.accident_quarter=diae.accident_quarter
        and biid.risk_class_code=diae.risk_class_code
        and biid.center_code =diae.center_code
    WHERE biaa.action_no = #{allocAction.actionNo,jdbcType=VARCHAR}
      and data_type='2'
      and diae.ep_sum_amount!=0
    </insert>

    <insert id="saveBussAllocDataTrans">
        INSERT INTO atr_buss_ibnr_alloc_dd_result (
            ID,
            action_no,
            entity_id,
            year_month,
            acc_year_month,
            portfolio_no,
            icg_no,
            icg_no_name,
            policy_no,
            risk_class_code,
            pl_judge_rslt,center_code,
            fin_detail_code, fin_product_code, fin_sub_product_code,
            fin_acc_channel, dept_id, channel_id, company_code4,
            kind_code,
            business_type,
            ep_amount,
            ibnr_amount,
            create_time
        )
        SELECT
            nextval( 'atr_seq_buss_ibnr_alloc_dd_result' ) AS ID,
            biaa.action_no,
            biaa.entity_id,
            biaa.year_month,
            ddlo.acc_year_month,
            ddlo.portfolio_no,
            ddlo.icg_no,
            '' as icg_no_name,
            ddlo.policy_no,
            ddlo.risk_class_code,
            ddlo.pl_judge_rslt,ddlo.center_code,
            ddlo.fin_detail_code, ddlo.fin_product_code, ddlo.fin_sub_product_code,
            ddlo.fin_acc_channel, ddlo.dept_id, ddlo.channel_id, ddlo.company_code4,
            ddlo.kind_code,
            ddlo.business_type,
            ddlo.amount,
            ddlo.amount*biim.provision_ratio as ibnr_amount,
            LOCALTIMESTAMP as create_time
        FROM atr_buss_ibnr_alloc_action biaa
         LEFT JOIN atr_buss_ibnr_import_main biim ON biim.ibnr_main_id = #{ibnrImport.ibnrMainId,jdbcType=DECIMAL}
         LEFT JOIN atr_dap_dd_os ddlo ON biaa.year_month = ddlo.year_month
        WHERE biaa.action_no = #{allocAction.actionNo,jdbcType=VARCHAR}
          and ddlo.acc_year_month between
            to_char(to_date(biaa.year_month,'yyyymm') - (25 * INTERVAL '3 months'),'yyyymm') and biaa.year_month
    </insert>


    <insert id="saveBussAllocDataTransDm">
        INSERT INTO atr_buss_ibnr_alloc_dd_result (
            ID,
            action_no,
            entity_id,
            year_month,
            acc_year_month,
            portfolio_no,
            icg_no,
            policy_no,
            risk_class_code,
            kind_code,
            ep_amount,
            ibnr_amount,
            create_time
        )
        SELECT
            nextval( 'atr_seq_buss_ibnr_alloc_dd_result' ) AS ID,
            biaa.action_no,
            biaa.entity_id,
            biaa.year_month,
            ddlo.acc_year_month,
            ddlo.portfolio_no,
            ddlo.icg_no,
            ddlo.policy_no,
            ddlo.risk_class_code,
            ddlo.kind_code,
            ddlo.amount,
            ddlo.amount*biim.provision_ratio as ibnr_amount,
            LOCALTIMESTAMP as create_time
        FROM atr_buss_ibnr_alloc_action biaa
                 LEFT JOIN atr_buss_ibnr_import_main biim ON biim.ibnr_main_id = #{ibnrImport.ibnrMainId,jdbcType=DECIMAL}
                 LEFT JOIN  (select to_char(accident_date_time,'yyyymm') as acc_year_month,
                                    dd.portfolio_no,
                                     dd.icg_no,
                                     T.policy_no,
                                     dd.risk_class_code,
                                     dd.product_code as kind_code,
                                    T.outstanding_amount as amount from dmuser.dm_claim_outstanding T
                     left join dmuser.dm_buss_cmunit_direct dd on t.policy_no=dd.policy_no where ri_direction_code='D' ) ddlo ON 1=1
        WHERE biaa.action_no = #{allocAction.actionNo,jdbcType=VARCHAR}
          and ddlo.acc_year_month between
            to_char(to_date(biaa.year_month,'yyyymm') - (25 * INTERVAL '3 months'),'yyyymm') and biaa.year_month
    </insert>

    <resultMap id="ResultVoResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrAllocResultVo">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="action_no" property="actionNo" jdbcType="VARCHAR" />
        <result column="entity_id" property="entityId" jdbcType="BIGINT" />
        <result column="year_month" property="yearMonth" jdbcType="VARCHAR" />
        <result column="portfolio_no" property="portfolioNo" jdbcType="VARCHAR" />
        <result column="icg_no" property="icgNo" jdbcType="VARCHAR" />
        <result column="icg_no_name" property="icgNoName" jdbcType="VARCHAR" />
        <result column="pl_judge_rslt" property="plJudgeRslt" jdbcType="VARCHAR" />
        <result column="acc_year_month" property="accYearMonth" jdbcType="VARCHAR" />
        <result column="treaty_no" property="treatyNo" jdbcType="VARCHAR" />
        <result column="risk_class_code" property="riskClassCode" jdbcType="VARCHAR" />
        <result column="center_code" property="centerCode" jdbcType="VARCHAR" />
        <result column="policy_no" property="policyNo" jdbcType="VARCHAR" />
        <result column="kind_code" property="kindCode" jdbcType="VARCHAR" />
        <result column="ep_amount" property="epAmount" jdbcType="NUMERIC" />
        <result column="ep_ratio" property="epRatio" jdbcType="NUMERIC" />
        <result column="ibnr_amount" property="ibnrAmount" jdbcType="NUMERIC" />
        <result column="fin_detail_code" property="finDetailCode" jdbcType="VARCHAR" />
        <result column="fin_product_code" property="finProductCode" jdbcType="VARCHAR" />
        <result column="fin_sub_product_code" property="finSubProductCode" jdbcType="VARCHAR" />
        <result column="fin_acc_channel" property="finAccChannel" jdbcType="VARCHAR" />
        <result column="center_code" property="centerCode" jdbcType="VARCHAR" />
        <result column="dept_id" property="deptId" jdbcType="VARCHAR" />
        <result column="channel_id" property="channelId" jdbcType="VARCHAR" />
        <result column="company_code4" property="companyCode4" jdbcType="VARCHAR" />
        <result column="creator_id" property="creatorId" jdbcType="BIGINT" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>


    <select id="countAllocList" resultType="Long">
        select count(1)
        FROM atr_buss_ibnr_alloc_action biaa,
             atr_buss_ibnr_alloc_dd_result biadr
        where biaa.action_No = biadr.action_No
        <choose>
            <when test="actionNo != null">
                and biaa.action_no = #{actionNo,jdbcType=VARCHAR}
            </when>
            <otherwise>
                <if test="entityId != null ">
                    and biaa.entity_id = #{entityId,jdbcType=BIGINT}
                </if>
                <if test="yearMonth != null and yearMonth != ''">
                    and biaa.year_month = #{yearMonth,jdbcType=VARCHAR}
                </if>
                <if test="yearMonthStart != null and yearMonthStart != ''">
                    and biaa.year_month >= #{yearMonthStart}
                </if>
                <if test="yearMonthEnd != null and yearMonthEnd != ''">
                    and biaa.year_month  <![CDATA[ <= ]]> #{yearMonthEnd}
                </if>
                <if test="businessSourceCode != null and businessSourceCode != ''">
                    and biaa.business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
                </if>
                <if test="status != null and status != ''">
                    and biaa.STATUS = #{status,jdbcType=VARCHAR}
                </if>
                <if test="confirmIs != null and confirmIs != ''">
                    and biaa.CONFIRM_IS = #{confirmIs,jdbcType=VARCHAR}
                </if>
            </otherwise>
        </choose>
    </select>

    <select id="findAllocList" fetchSize="5000" resultMap="ResultVoResultMap">
        select biaa.action_no, biaa.year_month,biadr.acc_year_month,biadr.portfolio_no,
         biadr.icg_no, biadr.icg_no_name, biadr.pl_judge_rslt,
         biadr.policy_no,biadr.risk_class_code, biadr.center_code,
        biadr.fin_detail_code, biadr.fin_product_code, biadr.fin_sub_product_code, biadr.fin_acc_channel,
        biadr.dept_id, biadr.channel_id, biadr.company_code4,
        biadr.kind_code,biadr.ep_amount, biadr.ep_ratio,biadr.ibnr_amount
        FROM atr_buss_ibnr_alloc_action biaa ,atr_buss_ibnr_alloc_dd_result biadr
         where  biaa.action_No = biadr.action_No
            <choose>
                <when test="actionNo != null">
                    and biaa.action_no = #{actionNo,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    <if test="entityId != null ">
                        and biaa.entity_id = #{entityId,jdbcType=BIGINT}
                    </if>
                    <if test="yearMonth != null and yearMonth != ''">
                        and biaa.year_month = #{yearMonth,jdbcType=VARCHAR}
                    </if>
                    <if test="yearMonthStart != null and yearMonthStart != ''">
                        and biaa.year_month >= #{yearMonthStart}
                    </if>
                    <if test="yearMonthEnd != null and yearMonthEnd != ''">
                        and biaa.year_month  <![CDATA[ <= ]]> #{yearMonthEnd}
                    </if>
                    <if test="businessSourceCode != null and businessSourceCode != ''">
                        and biaa.business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
                    </if>
                    <if test="status != null and status != ''">
                        and biaa.STATUS = #{status,jdbcType=VARCHAR}
                    </if>
                    <if test="confirmIs != null and confirmIs != ''">
                        and biaa.CONFIRM_IS = #{confirmIs,jdbcType=VARCHAR}
                    </if>
                </otherwise>
            </choose>
    </select>


    <delete id="deleteDapIbnr">
        delete from atr_dap_dd_ibnr
        where entity_id = #{entityId,jdbcType=BIGINT}
          and year_month = #{yearMonth,jdbcType=VARCHAR}
    </delete>

    <insert id="confirm">
        INSERT INTO atr_dap_dd_ibnr (
            year_month,
            entity_id,
            acc_year_month,
            portfolio_no,
            icg_no,
            risk_class_code,
            pl_judge_rslt,center_code,
            fin_detail_code, fin_product_code, fin_sub_product_code,
            fin_acc_channel, dept_id, channel_id, company_code4,
            policy_no,
            kind_code,
            business_type,
            amount,
            draw_time
        )
        SELECT
            biaa.year_month,
            biaa.entity_id,
            biadr.acc_year_month,
            biadr.portfolio_no,
            biadr.icg_no,
            biadr.risk_class_code,
            biadr.pl_judge_rslt,biadr.center_code,
            biadr.fin_detail_code, biadr.fin_product_code, biadr.fin_sub_product_code,
            biadr.fin_acc_channel, biadr.dept_id, biadr.channel_id, biadr.company_code4,
            biadr.policy_no,
            biadr.kind_code,
            biadr.business_type,
            biadr.ibnr_amount,
            LOCALTIMESTAMP as create_time
        FROM atr_buss_ibnr_alloc_action biaa
        LEFT JOIN atr_buss_ibnr_alloc_dd_result biadr
            ON biadr.action_no =biaa.action_no
        WHERE biaa.confirm_is='1'
          and biadr.action_no is not null
        <choose>
            <when test="actionNo != null">
                and biaa.action_no = #{actionNo,jdbcType=VARCHAR}
            </when>
            <otherwise>
                <if test="entityId != null ">
                    and biaa.entity_id = #{entityId,jdbcType=BIGINT}
                </if>
                <if test="yearMonth != null and yearMonth != ''">
                    and biaa.year_month = #{yearMonth,jdbcType=VARCHAR}
                </if>
                <if test="businessSourceCode != null and businessSourceCode != ''">
                    and biaa.business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
                </if>
            </otherwise>
        </choose>
    </insert>
</mapper>