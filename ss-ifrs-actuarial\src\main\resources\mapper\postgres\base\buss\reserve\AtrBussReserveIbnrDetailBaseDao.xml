<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2022-06-06 10:15:00 -->
<!-- Copyright (c) 2017-2027, All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.reserve.AtrBussReserveIbnrDetailDao">
  <!-- 本文件由GIS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveIbnrDetail">
    <id column="ibnr_detail_id" property="ibnrDetailId" jdbcType="NUMERIC" />
    <result column="reserve_ibnr_id" property="reserveIbnrId" jdbcType="NUMERIC" />
    <result column="data_type" property="dataType" jdbcType="VARCHAR" />
    <result column="damage_year_month" property="damageYearMonth" jdbcType="VARCHAR" />
    <result column="dev_period" property="devPeriod" jdbcType="INTEGER" />
    <result column="CURRENCY_CODE" property="currencyCode" jdbcType="VARCHAR" />
    <result column="value" property="value" jdbcType="NUMERIC" />
    <result column="exchange_rate" property="exchangeRate" jdbcType="NUMERIC" />
    <result column="loa_code" property="loaCode" jdbcType="VARCHAR" />
    <result column="PORTFOLIO_NO" property="portfolioNo" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    ibnr_detail_id, reserve_ibnr_id, data_type, damage_year_month, dev_period, CURRENCY_CODE,
    value, exchange_rate
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="ibnrDetailId != null ">
        and ibnr_detail_id = #{ibnrDetailId,jdbcType=NUMERIC}
      </if>
      <if test="reserveIbnrId != null ">
        and reserve_ibnr_id = #{reserveIbnrId,jdbcType=NUMERIC}
      </if>
      <if test="dataType != null and dataType != ''">
        and data_type = #{dataType,jdbcType=VARCHAR}
      </if>
      <if test="damageYearMonth != null and damageYearMonth != ''">
        and damage_year_month = #{damageYearMonth,jdbcType=VARCHAR}
      </if>
      <if test="devPeriod != null ">
        and dev_period = #{devPeriod,jdbcType=INTEGER}
      </if>
      <if test="currencyCode != null and currencyCode != ''">
        and CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="value != null ">
        and value = #{value,jdbcType=NUMERIC}
      </if>
      <if test="exchangeRate != null ">
        and exchange_rate = #{exchangeRate,jdbcType=NUMERIC}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.ibnrDetailId != null ">
        and ibnr_detail_id = #{condition.ibnrDetailId,jdbcType=NUMERIC}
      </if>
      <if test="condition.reserveIbnrId != null ">
        and reserve_ibnr_id = #{condition.reserveIbnrId,jdbcType=NUMERIC}
      </if>
      <if test="condition.dataType != null and condition.dataType != ''">
        and data_type = #{condition.dataType,jdbcType=VARCHAR}
      </if>
      <if test="condition.damageYearMonth != null and condition.damageYearMonth != ''">
        and damage_year_month = #{condition.damageYearMonth,jdbcType=VARCHAR}
      </if>
      <if test="condition.devPeriod != null ">
        and dev_period = #{condition.devPeriod,jdbcType=INTEGER}
      </if>
      <if test="condition.currencyCode != null and condition.currencyCode != ''">
        and CURRENCY_CODE = #{condition.currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.value != null ">
        and value = #{condition.value,jdbcType=NUMERIC}
      </if>
      <if test="condition.exchangeRate != null ">
        and exchange_rate = #{condition.exchangeRate,jdbcType=NUMERIC}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="ibnrDetailId != null ">
        and ibnr_detail_id = #{ibnrDetailId,jdbcType=NUMERIC}
      </if>
      <if test="reserveIbnrId != null ">
        and reserve_ibnr_id = #{reserveIbnrId,jdbcType=NUMERIC}
      </if>
      <if test="dataType != null and dataType != ''">
        and data_type = #{dataType,jdbcType=VARCHAR}
      </if>
      <if test="damageYearMonth != null and damageYearMonth != ''">
        and damage_year_month = #{damageYearMonth,jdbcType=VARCHAR}
      </if>
      <if test="devPeriod != null ">
        and dev_period = #{devPeriod,jdbcType=INTEGER}
      </if>
      <if test="currencyCode != null and currencyCode != ''">
        and CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="value != null ">
        and value = #{value,jdbcType=NUMERIC}
      </if>
      <if test="exchangeRate != null ">
        and exchange_rate = #{exchangeRate,jdbcType=NUMERIC}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List" />
    from atr_buss_reserve_ibnr_detail
    where ibnr_detail_id = #{ibnrDetailId,jdbcType=NUMERIC}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select
    <include refid="Base_Column_List" />
    from atr_buss_reserve_ibnr_detail
    where ibnr_detail_id in
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from atr_buss_reserve_ibnr_detail
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveIbnrDetail">
    select
    <include refid="Base_Column_List" />
    from atr_buss_reserve_ibnr_detail
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select
    <include refid="Base_Column_List" />
    from atr_buss_reserve_ibnr_detail
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="ibnr_detail_id" keyProperty="ibnrDetailId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveIbnrDetail">
    <selectKey resultType="long" keyProperty="ibnrDetailId" order="BEFORE">
      select nextval('ATR_SEQ_BUSS_RESERVE_IBNR_Dtl') as sequenceNo 
    </selectKey>
    insert into atr_buss_reserve_ibnr_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ibnrDetailId != null">
        ibnr_detail_id,
      </if>
      <if test="reserveIbnrId != null">
        reserve_ibnr_id,
      </if>
      <if test="dataType != null">
        data_type,
      </if>
      <if test="damageYearMonth != null">
        damage_year_month,
      </if>
      <if test="devPeriod != null">
        dev_period,
      </if>
      <if test="currencyCode != null">
        CURRENCY_CODE,
      </if>
      <if test="value != null">
        value,
      </if>
      <if test="exchangeRate != null">
        exchange_rate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ibnrDetailId != null">
        #{ibnrDetailId,jdbcType=NUMERIC},
      </if>
      <if test="reserveIbnrId != null">
        #{reserveIbnrId,jdbcType=NUMERIC},
      </if>
      <if test="dataType != null">
        #{dataType,jdbcType=VARCHAR},
      </if>
      <if test="damageYearMonth != null">
        #{damageYearMonth,jdbcType=VARCHAR},
      </if>
      <if test="devPeriod != null">
        #{devPeriod,jdbcType=INTEGER},
      </if>
      <if test="currencyCode != null">
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        #{value,jdbcType=NUMERIC},
      </if>
      <if test="exchangeRate != null">
        #{exchangeRate,jdbcType=NUMERIC},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert into atr_buss_reserve_ibnr_detail
    (ibnr_detail_id, reserve_ibnr_id, data_type,
    damage_year_month, dev_period, CURRENCY_CODE,
    value,
    exchange_rate)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.ibnrDetailId,jdbcType=NUMERIC}, #{item.reserveIbnrId,jdbcType=NUMERIC}, #{item.dataType,jdbcType=VARCHAR},
      #{item.damageYearMonth,jdbcType=VARCHAR}, #{item.devPeriod,jdbcType=INTEGER}, #{item.currencyCode,jdbcType=VARCHAR},
      #{item.value,jdbcType=NUMERIC},
      #{item.exchangeRate,jdbcType=NUMERIC})
    </foreach>
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveIbnrDetail">
    update atr_buss_reserve_ibnr_detail
    <set>
      <if test="reserveIbnrId != null">
        reserve_ibnr_id = #{reserveIbnrId,jdbcType=NUMERIC},
      </if>
      <if test="dataType != null">
        data_type = #{dataType,jdbcType=VARCHAR},
      </if>
      <if test="damageYearMonth != null">
        damage_year_month = #{damageYearMonth,jdbcType=VARCHAR},
      </if>
      <if test="devPeriod != null">
        dev_period = #{devPeriod,jdbcType=INTEGER},
      </if>
      <if test="currencyCode != null">
        CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        value = #{value,jdbcType=NUMERIC},
      </if>
      <if test="exchangeRate != null">
        exchange_rate = #{exchangeRate,jdbcType=NUMERIC},
      </if>
    </set>
    where ibnr_detail_id = #{ibnrDetailId,jdbcType=NUMERIC}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveIbnrDetail">
    update atr_buss_reserve_ibnr_detail
    <set>
      <if test="record.reserveIbnrId != null">
        reserve_ibnr_id = #{record.reserveIbnrId,jdbcType=NUMERIC},
      </if>
      <if test="record.dataType != null">
        data_type = #{record.dataType,jdbcType=VARCHAR},
      </if>
      <if test="record.damageYearMonth != null">
        damage_year_month = #{record.damageYearMonth,jdbcType=VARCHAR},
      </if>
      <if test="record.devPeriod != null">
        dev_period = #{record.devPeriod,jdbcType=INTEGER},
      </if>
      <if test="record.currencyCode != null">
        CURRENCY_CODE = #{record.currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.value != null">
        value = #{record.value,jdbcType=NUMERIC},
      </if>
      <if test="record.exchangeRate != null">
        exchange_rate = #{record.exchangeRate,jdbcType=NUMERIC},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from atr_buss_reserve_ibnr_detail
    where ibnr_detail_id = #{ibnrDetailId,jdbcType=NUMERIC}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from atr_buss_reserve_ibnr_detail
    where ibnr_detail_id in
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from atr_buss_reserve_ibnr_detail
    where
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveIbnrDetail">
    select count(1) from atr_buss_reserve_ibnr_detail
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>