package com.ss.ifrs.actuarial.pojo.other.vo;

/**
 * <AUTHOR>
 */
public class AtrExcelParseVo {
    /**
     * 字段
     */
    private String colCode;
    /**
     * 类型
     */
    private String colType;
    /**
     * 长度
     */
    private String colLength;
    /**
     * 单元格值
     */
    private Object colValue;
    /**
     * 是否必录标识
     */
    private String needIs;

    public AtrExcelParseVo(){

    }
    public AtrExcelParseVo(String colCode, String colType, String colLength, String needIs){
        this.colCode = colCode;
        this.colLength = colLength;
        this.colType = colType;
        this.needIs = needIs;
    }
    public AtrExcelParseVo(String colCode, String colType, String colLength, String needIs, Object colValue){
        this.colCode = colCode;
        this.colLength = colLength;
        this.colType = colType;
        this.needIs = needIs;
        this.colValue = colValue;
    }
    public String getColCode() {
        return colCode;
    }

    public void setColCode(String colCode) {
        this.colCode = colCode;
    }

    public String getColType() {
        return colType;
    }

    public void setColType(String colType) {
        this.colType = colType;
    }

    public String getColLength() {
        return colLength;
    }

    public void setColLength(String colLength) {
        this.colLength = colLength;
    }

    public Object getColValue() {
        return colValue;
    }

    public void setColValue(Object colValue) {
        this.colValue = colValue;
    }

    public String getNeedIs() {
        return needIs;
    }

    public void setNeedIs(String needIs) {
        this.needIs = needIs;
    }
}
