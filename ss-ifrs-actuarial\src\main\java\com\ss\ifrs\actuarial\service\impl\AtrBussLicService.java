package com.ss.ifrs.actuarial.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.ss.ifrs.actuarial.dao.AtrBussLicDao;
import com.ss.ifrs.actuarial.pojo.ecf.vo.AtrBussQuota;
import com.ss.ifrs.actuarial.pojo.ecf.vo.AtrSD7;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lic.AtrBussLicAction;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lic.AtrBussLicG;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lic.AtrBussLicGAmount;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lic.AtrBussLicGDevAmount;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lic.AtrBussLicU;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lic.AtrBussLicUAmount;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lic.AtrBussLicUDevAmount;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lic.AtrDuctLicDevAmount;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lic.AtrLogLic;
import com.ss.ifrs.actuarial.util.Dates;
import com.ss.ifrs.actuarial.util.EcfUtil;
import com.ss.ifrs.actuarial.util.IndexFactory;
import com.ss.ifrs.actuarial.util.ThreadUtil;
import com.ss.ifrs.actuarial.util.abp.AsyncBatchProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 预期赔付现金流 service
 * <AUTHOR>
 */
@Service
@Scope(value = "prototype")
@Slf4j
public class AtrBussLicService extends AbstractAtrBussEcfService {

    @Resource
    private AtrBussLicDao atrBussLicDao;

    private static final String Q_CLAIM_SETTLED_PATTERN = EcfUtil.Q_LIC_CLAIM_SETTLED_PATTERN;
    private static final String Q_ULAE_RATIO = EcfUtil.Q_LIC_ULAE_RATIO;
    private static final String Q_RA_RATIO = EcfUtil.Q_LIC_RA_RATIO;
    private static final String Q_RD_RATIO = EcfUtil.Q_LIC_RD_RATIO;

    public static final String AT_CUR_CF = "CUR_CF";
    public static final String AT_LAST_CF = "LAST_CF";
    public static final String AT_MOVEMENT = "MOVEMENT";

    public static final String L1 = "L1"; // 当期期初折现
    public static final String L2 = "L2"; // 当期锁期折现
    public static final String L3 = "L3"; // 当期期末折现
    public static final String L4 = "L4"; // 当期期末锁期折现
    public static final String C1 = "C1"; // 当期现金流变动折现
    public static final String C2 = "C2"; // 当期现金流变动锁期折现

    private AtomicLong mainIdGen;
    private AtomicLong mainGIdGen;

    private final IndexFactory<Double> initIrIndex = new IndexFactory<>();
    // 上期锁期折现率和期末折现率
    private final IndexFactory<BigDecimal> prevLastEndDiscountIndex = new IndexFactory<>();

    // 使用IndexFactory替换嵌套Map结构，提高代码可读性
    private final IndexFactory<AtrBussLicGDevAmount> summaryDevDataIndex = new IndexFactory<>();

    // 用于存储上期合同组现金流数据的索引
    private final IndexFactory<List<AtrBussLicGDevAmount>> previousGroupCFIndex = new IndexFactory<>();

    private final Map<List<String>, List<AtrDuctLicDevAmount>> threadDevs = new ConcurrentHashMap<>();
    
    // 仅保存分组键，不再保存整个AtrBussLicU对象列表
    private final Set<List<String>> summaryKeys = new HashSet<>();

    private final static BigDecimal ZERO = BigDecimal.ZERO;
    private final static BigDecimal ONE = BigDecimal.ONE;


    public void entry(Long entityId, String yearMonth, String businessSourceCode) {
        this.actionNo = EcfUtil.createActionNo();
        initEnvParams(actionNo, entityId, yearMonth, businessSourceCode);
        createAction();
        String status = "S";
        try (AsyncBatchProcessor abp = createAsyncBatchProcessor()) {
            initAbp(abp);
            logDebug("开始收集数据 - collectData");
            collectData();
            logDebug("开始分片计算 - calcByPartition");
            calcByPartition();
            logDebug("开始汇总处理 - summarizeAndSave");
            summarizeAndSave();
            logDebug("批处理结束 - abp-end");
            abp.end();
            logDebug("处理完成 - end");

        } catch (Exception e) {
            status = "E";
            logError(e);
            log.error("{} lic error", businessSourceCode, e);
            throw new RuntimeException(e);
        } finally {
            updateAction(status);
        }
    }

    protected void initEnvParams(String actionNo, Long entityId, String yearMonth, String businessSourceCode) {
        super.initEnvParams(actionNo, entityId, yearMonth, businessSourceCode,"LIC");
        this.logIdGen = new AtomicLong(atrBussLicDao.getMaxLogId());
        commonParamMap.put("module", "LIC");
    }

    /**
     * 按分片进行计算
     * 每个分片计算完成后再处理下一个分片
     */
    private void calcByPartition() {
        int parts = (int) commonParamMap.get("parts");
        
        for (int partNo = 0; partNo < parts; partNo++) {
            // 创建分区参数
            Map<String, Object> partParams = new HashMap<>(commonParamMap);
            partParams.put("pn", partNo);
            // 获取当前分区的数据
            List<AtrBussLicU> partDataList = atrBussLicDao.getTempData(partParams);
            if (partDataList.isEmpty()) {
                continue; // 跳过空分区
            }
            logDebug("开始分片计算:pn"+partNo);
            // 分片数据处理和计算
            processAndCalculatePartition(partDataList);
            logDebug("结束分片计算:pn"+partNo);
        }
    }
    
    /**
     * 处理并计算单个分片的数据
     * @param partDataList 分片数据列表
     */
    private void processAndCalculatePartition(List<AtrBussLicU> partDataList) {
        // 第一步：处理数据
        final List<AtrBussLicU> processedAccList = new ArrayList<>();
        partDataList.forEach(data -> {
            if (data == null) {
                return;
            }
            Long id = mainIdGen.incrementAndGet();
            data.setId(id);
            data.setActionNo(actionNo);
            data.setYearMonth(yearMonth);
            data.setEntityId(entityId);
            data.setOffsetMonths(Dates.monthsBetween(data.getAccYearMonth(), yearMonth));

            processedAccList.add(data);
            // 插入数据库
            abp.insert(data);

        });

        ThreadUtil.runThreadsThrow(processedAccList, acc -> {
            Map<String, BigDecimal> curCfAmountMap = new HashMap<>();
            calcCF(acc, curCfAmountMap, "OS");
            calcCF(acc, curCfAmountMap, "IBNR");
            if (!StringUtils.equalsAny(businessSourceCode, "FO", "TO")) {
                calcCF(acc, curCfAmountMap, "ULAE");
            }
            calcCF(acc, curCfAmountMap, "RA");
            // 为TO和FO业务添加RD计算
            if (StringUtils.equalsAny(businessSourceCode, "FO", "TO")) {
                calcCF(acc, curCfAmountMap, "RD");
            }
        }, () -> EcfUtil.THREADS_CALC);

        summarizePartitionData(processedAccList);

        processedAccList.clear();
    }
    
    /**
     * 对分片数据按三个维度进行汇总
     * @param processedAccList 处理后的数据列表
     */
    private void summarizePartitionData(List<AtrBussLicU> processedAccList) {

        // 只提取并保存分组键
        processedAccList.stream()
            .map(acc -> Arrays.asList(
                acc.getPortfolioNo(),
                acc.getIcgNo(),
                acc.getAccYearMonth()
            ))
            .forEach(summaryKeys::add);

    }

    private void summarizeAndSave() {
        // 使用汇总键创建汇总列表
        List<AtrBussLicG> summaryList = createSummaryList();
        
        for (AtrBussLicG summary : summaryList) {
            // 插入合同组基本信息
            abp.insert(summary);
            
            // 处理金额汇总 - 只汇总当期数据
            for (String cfType : Arrays.asList("OS", "IBNR", "ULAE", "RA")) {
                if (StringUtils.equalsAny(businessSourceCode,"TO","FO") && "ULAE".equals(cfType)) {
                    continue;
                }
                
                // 1. 收集与当前合同组和现金流类型相关的所有数据（仅包含CUR_CF类型）
                List<AtrDuctLicDevAmount> aggregatedDevList = collectDevAmountsForGroup(summary, cfType);
                
                // 2. 在内存中汇总当期数据，但不插入数据库
                if (!aggregatedDevList.isEmpty()) {
                    summarizeAndSaveDevAmount(summary, cfType, aggregatedDevList);
                }
                
                calculateGroupMovement(summary, cfType);
            }

            // 处理TO业务的RD计算
            if (StringUtils.equalsAny(businessSourceCode,"TO","FO")) {
                // 收集与RD相关的数据并汇总
                List<AtrDuctLicDevAmount> rdDevList = collectDevAmountsForGroup(summary, "RD");
                if (!rdDevList.isEmpty()) {
                    summarizeAndSaveDevAmount(summary, "RD", rdDevList);
                }
                
                // 插入当期RD现金流数据
                insertCurrentCashflow(summary, "RD");
                
                // 计算RD的折现
                BigDecimal prevEndDiscount = prevLastEndDiscountIndex.one(
                        Arrays.asList(summary.getPortfolioNo(), summary.getIcgNo(), summary.getAccYearMonth(), "RD", L3), ZERO);
                // 保存期初折现（L1）
                saveAmount(summary,"RD",L1,prevEndDiscount);
                List<AtrBussLicGDevAmount> rd = summaryDevDataIndex.list(Arrays.asList(summary.getId(), "RD", AtrBussLicService.AT_CUR_CF));
                // 计算期末折现（C1）
                BigDecimal v = calculateEndDiscountByRD(yearMonth, rd);
                saveAmount(summary,"RD",C1,v);
            }
        }
        
        // 在所有汇总数据保存完成后，计算折现
        logDebug("计算折现开始");
        calculateAllDiscounts(summaryList);
        logDebug("计算折现结束");

        // 清理summaryKeys以释放内存
        summaryKeys.clear();
        
        // 释放summaryList的内存
        summaryList.clear();
    }
    
    /**
     * 收集与指定合同组和现金流类型相关的所有发展期数据
     */
    private List<AtrDuctLicDevAmount> collectDevAmountsForGroup(AtrBussLicG summary, String cfType) {
        // 获取与当前汇总记录和当前cfType相关的所有键
        Set<List<String>> keysForCfType = threadDevs.keySet().stream()
            .filter(key -> key.size() > 4 
                && summary.getPortfolioNo().equals(key.get(0))
                && summary.getIcgNo().equals(key.get(1))
                && summary.getAccYearMonth().equals(key.get(2))
                && cfType.equals(key.get(4)))
            .collect(Collectors.toSet());
        
        // 对同一cfType的所有发展期数据进行汇总
        List<AtrDuctLicDevAmount> aggregatedDevList = new ArrayList<>();
        for (List<String> key : keysForCfType) {
            List<AtrDuctLicDevAmount> devList = threadDevs.get(key);
            if (devList != null && !devList.isEmpty()) {
                // 创建副本以避免并发修改异常
                aggregatedDevList.addAll(new ArrayList<>(devList));
            }
        }
        
        return aggregatedDevList;
    }

    /**
     * 计算合同组级别的变动
     * 对比当期和上期的现金流，计算变动
     * 同时负责所有数据的数据库插入操作
     * @param summary 当前合同组
     * @param cfType 现金流类型
     */
    private void calculateGroupMovement(AtrBussLicG summary, String cfType) {
        // 1. 先处理当期现金流数据的插入
        insertCurrentCashflow(summary, cfType);
        
        // 2. 处理上期现金流数据的插入
        insertPreviousCashflow(summary, cfType);
        
        // 3. 计算并插入变动数据
        calculateAndInsertMovement(summary, cfType);
    }
    
    /**
     * 插入当期现金流数据
     */
    private void insertCurrentCashflow(AtrBussLicG summary, String cfType) {
        // 获取当期现金流发展期数据
        List<AtrBussLicGDevAmount> currentDevList = summaryDevDataIndex.list(
            Arrays.asList(summary.getId(), cfType, AT_CUR_CF)
        );
        
        // 如果当期没有数据，直接返回，但不影响后续变动计算
        if (currentDevList == null || currentDevList.isEmpty()) {
            return;
        }
        
        // 插入当期现金流数据
        for (AtrBussLicGDevAmount current : currentDevList) {
            abp.insert(current);
        }
    }
    
    /**
     * 插入上期现金流数据
     */
    private void insertPreviousCashflow(AtrBussLicG summary, String cfType) {
        // 获取上期现金流数据 - 直接从previousGroupCFIndex获取
        List<AtrBussLicGDevAmount> previousDevList = previousGroupCFIndex.one(
            Arrays.asList(summary.getPortfolioNo(), summary.getIcgNo(), summary.getAccYearMonth(), cfType)
        );
        
        // 如果上期数据不存在，直接返回
        if (previousDevList == null || previousDevList.isEmpty()) {
            return;
        }
        
        // 为每个上期数据创建对应的LAST_CF记录并插入
        for (AtrBussLicGDevAmount prev : previousDevList) {
            AtrBussLicGDevAmount lastCf = new AtrBussLicGDevAmount();
            lastCf.setMainId(summary.getId());
            lastCf.setDevNo(prev.getDevNo());
            lastCf.setYearMonth(yearMonth);
            lastCf.setCfType(cfType);
            lastCf.setAmountType(AT_LAST_CF);
            lastCf.setAmount(prev.getAmount());
            
            // 添加到索引中
            summaryDevDataIndex.add(
                Arrays.asList(summary.getId(), cfType, AT_LAST_CF), 
                lastCf
            );
            
            // 插入数据库
            abp.insert(lastCf);
        }
    }
    
    /**
     * 计算并插入变动数据
     */
    private void calculateAndInsertMovement(AtrBussLicG summary, String cfType) {
        // 获取当期现金流发展期数据
        List<AtrBussLicGDevAmount> currentDevList = summaryDevDataIndex.list(
            Arrays.asList(summary.getId(), cfType, AT_CUR_CF)
        );
        
        // 获取上期现金流发展期数据 - 直接从previousGroupCFIndex获取
        List<AtrBussLicGDevAmount> previousDevList = previousGroupCFIndex.one(
            Arrays.asList(summary.getPortfolioNo(), summary.getIcgNo(), summary.getAccYearMonth(), cfType)
        );
        
        List<AtrBussLicGDevAmount> movementList = new ArrayList<>();


        
        // 当前期和上期都为空，无需处理
        if ((currentDevList == null || currentDevList.isEmpty()) && (previousDevList == null || previousDevList.isEmpty())) {
            return;
        }
        
        if (previousDevList == null || previousDevList.isEmpty()) {
            // 上期不存在，则变动 = 当期值
            for (AtrBussLicGDevAmount current : currentDevList) {
                AtrBussLicGDevAmount movement = new AtrBussLicGDevAmount();
                movement.setMainId(summary.getId());
                movement.setDevNo(current.getDevNo());
                movement.setYearMonth(yearMonth);
                movement.setCfType(cfType);
                movement.setAmountType(AT_MOVEMENT);
                movement.setAmount(current.getAmount());
                movementList.add(movement);
            }
        } else if (currentDevList == null || currentDevList.isEmpty()) {
            for (AtrBussLicGDevAmount prev : previousDevList) {
                int prevDevNo = prev.getDevNo();
                int currentDevNo = prevDevNo - 1;
                
                // 只处理发展期大于等于0的情况
                if (currentDevNo >= 0) {
                    AtrBussLicGDevAmount movement = new AtrBussLicGDevAmount();
                    movement.setMainId(summary.getId());
                    movement.setDevNo(currentDevNo);
                    movement.setYearMonth(yearMonth);
                    movement.setCfType(cfType);
                    movement.setAmountType(AT_MOVEMENT);
                    movement.setAmount(ZERO.subtract(prev.getAmount())); // -上期值
                    movementList.add(movement);
                }
            }
        } else {
            // 创建上期发展期索引，方便查找
            Map<Integer, BigDecimal> previousDevMap = new HashMap<>();
            for (AtrBussLicGDevAmount prev : previousDevList) {
                previousDevMap.put(prev.getDevNo(), prev.getAmount());
            }
            
            // 对当期的每个发展期，计算变动
            for (AtrBussLicGDevAmount current : currentDevList) {
                int currentDevNo = current.getDevNo();
                
                // 上期对应发展期的值（如果存在的话）
                BigDecimal previousAmount = previousDevMap.getOrDefault(currentDevNo + 1, ZERO);
                
                // 计算变动：当期值 - 上期值
                BigDecimal movementAmount = current.getAmount().subtract(previousAmount);
                
                // 创建变动记录
                AtrBussLicGDevAmount movement = new AtrBussLicGDevAmount();
                movement.setMainId(summary.getId());
                movement.setDevNo(current.getDevNo());
                movement.setYearMonth(yearMonth);
                movement.setCfType(cfType);
                movement.setAmountType(AT_MOVEMENT);
                movement.setAmount(movementAmount);
                movementList.add(movement);
            }
            
            // 处理上期存在但当期不存在的发展期
            Map<Integer, AtrBussLicGDevAmount> currentDevMap = currentDevList.stream()
                .collect(Collectors.toMap(AtrBussLicGDevAmount::getDevNo, dev -> dev));
            
            for (AtrBussLicGDevAmount prev : previousDevList) {
                int prevDevNo = prev.getDevNo();
                int currentDevNo = prevDevNo - 1;
                
                // 如果当期没有对应的发展期，且发展期大于0，则创建变动记录
                if (!currentDevMap.containsKey(currentDevNo) && currentDevNo >= 0) {
                    AtrBussLicGDevAmount movement = new AtrBussLicGDevAmount();
                    movement.setMainId(summary.getId());
                    movement.setDevNo(currentDevNo);
                    movement.setYearMonth(yearMonth);
                    movement.setCfType(cfType);
                    movement.setAmountType(AT_MOVEMENT);
                    movement.setAmount(ZERO.subtract(prev.getAmount())); // -上期值
                    movementList.add(movement);
                }
            }
        }
        
        // 将变动数据添加到索引
        for (AtrBussLicGDevAmount movement : movementList) {
            summaryDevDataIndex.add(
                Arrays.asList(summary.getId(), cfType, AT_MOVEMENT),
                movement
            );
            // 插入数据库
            abp.insert(movement);
        }
    }

    /**
     * 从汇总键创建汇总列表
     * @return 汇总列表
     */
    private List<AtrBussLicG> createSummaryList() {
        List<AtrBussLicG> summaryList = new ArrayList<>();
        
        // 创建一个集合来存储所有需要处理的键（当期+上期）
        Set<List<String>> allKeys = new HashSet<>(summaryKeys);

        Set<List<String>> previousKeys = new HashSet<>();
        for (List<?> key : previousGroupCFIndex.keys()) {
            // 确保键有足够的元素
            if (key.size() >= 3) {
                // 安全地转换每个元素
                String portfolioNo = String.valueOf(key.get(0));
                String icgNo = String.valueOf(key.get(1));
                String accYearMonth = String.valueOf(key.get(2));
                previousKeys.add(Arrays.asList(portfolioNo, icgNo, accYearMonth));
            }
        }
        
        // 合并到allKeys中
        allKeys.addAll(previousKeys);
        
        // 从所有键创建汇总记录
        for (List<String> key : allKeys) {
            // 确保键有足够的元素
            if (key.size() < 3) {
                continue;
            }
            
            // 直接从键创建汇总记录
            AtrBussLicG summary = new AtrBussLicG();
            summary.setId(mainGIdGen.incrementAndGet());
            summary.setActionNo(actionNo);
            summary.setEntityId(entityId);
            summary.setYearMonth(yearMonth);
            summary.setBusinessSourceCode(businessSourceCode);
            summary.setPortfolioNo(key.get(0));
            summary.setIcgNo(key.get(1));
            summary.setAccYearMonth(key.get(2));
            
            summaryList.add(summary);
        }

        return summaryList;
    }

    private void summarizeAndSaveDevAmount(AtrBussLicG summary, String cfType,
                                           List<AtrDuctLicDevAmount> devList) {
        if (devList != null && !devList.isEmpty()) {
            // 按不同的amountType分组处理数据
            Map<String, List<AtrDuctLicDevAmount>> amountTypeMap = devList.stream()
                .collect(Collectors.groupingBy(AtrDuctLicDevAmount::getAmountType));
            
            // 只处理CUR_CF类型的数据，因为LAST_CF和MOVEMENT是在合同组层面直接计算的
            List<AtrDuctLicDevAmount> curCfList = amountTypeMap.get(AT_CUR_CF);
            if (curCfList != null && !curCfList.isEmpty()) {
                processByAmountType(summary, cfType, curCfList, AT_CUR_CF);
            }
            
            // 不再处理LAST_CF和MOVEMENT，这些在calculateGroupMovement中处理
        }
    }
    
    /**
     * 按金额类型处理发展期数据，仅在内存中汇总，不进行数据库插入
     */
    private void processByAmountType(AtrBussLicG summary, String cfType,
                                     List<AtrDuctLicDevAmount> devList, String amountType) {
        if (devList != null && !devList.isEmpty()) {
            Map<Integer, BigDecimal> devAmountMap = devList.stream()
                    .collect(Collectors.groupingBy(
                            AtrDuctLicDevAmount::getDevNo,
                            Collectors.reducing(
                                    ZERO,           // 初始值：BigDecimal 的 0
                                    AtrDuctLicDevAmount::getAmount, // 提取要聚合的值：amount (BigDecimal)
                                    BigDecimal::add            // 聚合操作：将两个 BigDecimal 相加
                            )
                    ));

            for (Map.Entry<Integer, BigDecimal> devAmount : devAmountMap.entrySet()) {
                AtrBussLicGDevAmount summaryDev = new AtrBussLicGDevAmount();
                summaryDev.setMainId(summary.getId());
                summaryDev.setDevNo(devAmount.getKey());
                summaryDev.setYearMonth(yearMonth);
                summaryDev.setCfType(cfType);
                summaryDev.setAmountType(amountType);
                summaryDev.setAmount(devAmount.getValue());
                
                // 添加到索引中，使用[mainId, cfType, amountType]作为键
                summaryDevDataIndex.add(
                    Arrays.asList(summary.getId(), cfType, amountType), 
                    summaryDev
                );
            }
        }
    }

    private void calcCF(AtrBussLicU acc, Map<String, BigDecimal> curCfAmountMap, String cfType) {
        Long mainId = acc.getId();
        String portfolioNo = acc.getPortfolioNo();
        String icgNo = acc.getIcgNo();
        String accYearMonth = acc.getAccYearMonth();
        String riskClassCode = acc.getRiskClassCode();
        int offsetMonths = Math.toIntExact(acc.getOffsetMonths());

        // 当期计算
        List<AtrBussLicUDevAmount> curCfDevList = new ArrayList<>();
        AtrBussLicUAmount am = null;
        BigDecimal curCfAmount = null;
        if (StringUtils.equalsAny(cfType, "IBNR", "OS")) {
            BigDecimal rsvAmount = getRsvAmount(acc, cfType);
            if (rsvAmount != null) {
                curCfAmount = rsvAmount;
            }
        } else if ("ULAE".equals(cfType) && !StringUtils.equalsAny(businessSourceCode,"TO","FO")) {
            // ULAE=( 0.5 * OS + IBNR) * ULAE %
            BigDecimal os = nvl(curCfAmountMap.get("OS"));
            BigDecimal ibnr = nvl(curCfAmountMap.get("IBNR"));
            curCfAmount = BigDecimal.valueOf(0.5).multiply(os).add(ibnr)
                    .multiply(BigDecimal.valueOf(getQuota(Q_ULAE_RATIO, riskClassCode, icgNo)));
        } else if ("RA".equals(cfType)) {
            BigDecimal os = nvl(curCfAmountMap.get("OS"));
            BigDecimal ibnr = nvl(curCfAmountMap.get("IBNR"));
            BigDecimal ulae = nvl(curCfAmountMap.get("ULAE"));
            curCfAmount =  ulae.add(os).add(ibnr).multiply(BigDecimal.valueOf(getQuota(Q_RA_RATIO, riskClassCode, icgNo)));
        } else if ("RD".equals(cfType) && StringUtils.equalsAny(businessSourceCode,"TO","FO")) {
            // RD = (OS + IBNR) * RD %
            BigDecimal os = nvl(curCfAmountMap.get("OS"));
            BigDecimal ibnr = nvl(curCfAmountMap.get("IBNR"));
            curCfAmount = os.add(ibnr).multiply(BigDecimal.valueOf(getQuota(Q_RD_RATIO, icgNo)));
        }

        if (curCfAmount != null) {
            am = new AtrBussLicUAmount();
            am.setMainId(mainId);
            am.setYearMonth(yearMonth);
            am.setAmountType(AT_CUR_CF);
            am.setCfType(cfType);
            am.setAmount(curCfAmount);
            abp.insert(am);
            curCfAmountMap.put(cfType, am.getAmount());
        }

        // Current CF
        BigDecimal sumClaimRate = sumClaimSettledPattern(riskClassCode, icgNo, 1 + offsetMonths);
        if (am != null) {
            int claimSettledPatternMaxDevNo = getClaimSettledPatternMaxDevNo(riskClassCode, icgNo);
            int maxDevNo = Math.max(claimSettledPatternMaxDevNo - offsetMonths, 1);
            for (int i = 1; i <= maxDevNo; i++) {
                Double q1 =  getQuotaOrNull(Q_CLAIM_SETTLED_PATTERN, riskClassCode, icgNo, i + offsetMonths);
                BigDecimal qv = null;
                if (sumClaimRate.equals(ZERO)) {
                    if (i == 1) {
                        qv = ONE;
                    }
                } else {
                    if (q1 != null) {
                        qv = BigDecimal.valueOf(q1).divide(sumClaimRate, 8, RoundingMode.HALF_UP);
                    }
                }
                if (qv != null) {
                    AtrBussLicUDevAmount dev = new AtrBussLicUDevAmount();
                    dev.setMainId(mainId);
                    dev.setDevNo(i);
                    dev.setYearMonth(yearMonth);
                    dev.setCfType(cfType);
                    dev.setAmountType(AT_CUR_CF);
                    dev.setAmount(qv.multiply(am.getAmount()));
                    abp.insert(dev);
                    curCfDevList.add(dev);
                }
            }

            // 处理赔付模式摊分的尾差
            handleDevAmountTailDifference(curCfDevList, am.getAmount(), icgNo, riskClassCode, cfType);
        }

        // 添加当期数据到threadDevs
        List<String> devKey = Arrays.asList(portfolioNo, icgNo, accYearMonth, riskClassCode, cfType);
        
        // 添加当期数据
        mergeDuctLicDevAmount(devKey, curCfDevList);

    }

    private BigDecimal sumClaimSettledPattern(String riskClassCode, String icgNo, int startNo) {
        List<AtrBussQuota> quotas = quotaIndex.list(Arrays.asList(AtrBussLicService.Q_CLAIM_SETTLED_PATTERN, riskClassCode,icgNo));

        if (quotas.isEmpty()) {
            return ZERO;
        }
        BigDecimal sum = ZERO;
        for (AtrBussQuota quota : quotas) {
            if (quota.getDevNo() >= startNo) {
                sum = sum.add(BigDecimal.valueOf(quota.getValue()));
            }
        }
        return sum;
    }

    private int getClaimSettledPatternMaxDevNo(String riskClassCode, String icgNo) {
        List<AtrBussQuota> quotas = quotaIndex.list(Arrays.asList(AtrBussLicService.Q_CLAIM_SETTLED_PATTERN, riskClassCode, icgNo));
        if (quotas.isEmpty()) {
            return -1;
        }
        return quotas.get(quotas.size() - 1).getDevNo();
    }

    private BigDecimal getRsvAmount(AtrBussLicU acc, String cfType) {
        // 直接使用对象属性，避免不必要的索引查找
        return cfType.equals(acc.getCfType()) ? acc.getAmount() : null;
    }

    private void collectData() {
        initMainIdGen();
        logDebug("initAcc");
        initCalcU();
        logDebug("collectQuotaDef");
        collectQuotaDef();
        logDebug("collectQuota");
        collectQuota();
        logDebug("collectInitIr");
        collectInitIr();
        logDebug("collectPreviousDiscounts");
        collectPreviousDiscounts();
        logDebug("collectPreviousGroupCF");
        collectPreviousGroupCF();
    }

    private void collectInitIr() {
        List<AtrSD7> list = atrBussLicDao.collectInitIr(commonParamMap);
        for (AtrSD7 a : list) {
            String yearMonth = a.getS1();
            int devNo = a.getD1().intValue();
            initIrIndex.add(Arrays.asList(yearMonth, devNo), nvl(a.getD2()));
        }
    }

    /**
     * 收集上一期合同组的现金流数据，用于计算变动
     */
    private void collectPreviousGroupCF() {
        if (commonParamMap.get("lastActionNo") == null || StringUtils.isBlank(commonParamMap.get("lastActionNo").toString())) {
            return;
        }
        
        Map<String, Object> params = new HashMap<>(commonParamMap);
        List<AtrBussLicGDevAmount> list = atrBussLicDao.collectPreviousGroupCF(params);
        
        // 按合同组和现金流类型进行索引
        for (AtrBussLicGDevAmount dev : list) {
            String portfolioNo = dev.getPortfolioNo();
            String icgNo = dev.getIcgNo();
            String accYearMonth = dev.getAccYearMonth();
            String cfType = dev.getCfType();
            
            List<String> key = Arrays.asList(portfolioNo, icgNo, accYearMonth, cfType);
            
            // 将同一组合条件的多条记录放入同一个列表中
            List<AtrBussLicGDevAmount> devList = previousGroupCFIndex.one(key);
            if (devList == null) {
                devList = new ArrayList<>();
                previousGroupCFIndex.add(key, devList);
            }
            devList.add(dev);
        }
    }

    private void initMainIdGen() {
        mainIdGen = new AtomicLong(atrBussLicDao.getMaxMainId());
        mainGIdGen = new AtomicLong(atrBussLicDao.getMaxMainGId());
    }

    private void initCalcU() {
        atrBussLicDao.deleteTempData();

        atrBussLicDao.initTempData(commonParamMap);
    }

    private void updateAction(String status) {
        AtrBussLicAction action = new AtrBussLicAction();
        action.setActionNo(actionNo);
        action.setStatus(status);
        action.setUpdateTime(new Date());
        atrBussLicDao.updateActionStatus(action);
    }

    private void initAbp(AsyncBatchProcessor abp) {
        this.abp = abp;
        abp.addType(AtrBussLicU.class);
        abp.addType(AtrBussLicUAmount.class);
        abp.addType(AtrBussLicUDevAmount.class);
        abp.addType(AtrBussLicG.class);
        abp.addType(AtrBussLicGAmount.class);
        abp.addType(AtrBussLicGDevAmount.class);
    }

    private void createAction() {
        AtrBussLicAction vo = new AtrBussLicAction();
        vo.setActionNo(actionNo);
        vo.setYearMonth(yearMonth);
        vo.setEntityId(entityId);
        vo.setBusinessSourceCode(businessSourceCode);
        vo.setCreateTime(new Date());
        atrBussLicDao.createAction(vo);
    }

    private void logDebug(String mark) {
        AtrLogLic vo = new AtrLogLic();
        vo.setMark(mark);
        super.logDebug(vo);
    }

    private void logError(Exception e) {
        AtrLogLic vo = new AtrLogLic();
        vo.setMark("error");
        super.logError(vo, e);
    }


    /**
     * 计算所有汇总数据的折现
     * @param summaryList 汇总数据列表
     */
    private void calculateAllDiscounts(List<AtrBussLicG> summaryList) {
        for (AtrBussLicG summary : summaryList) {
            Long summaryId = summary.getId();
            
            for (String cfType : Arrays.asList("OS", "IBNR", "ULAE", "RA")) {
                // 获取内存中的汇总后的发展期数据进行折现计算
                
                // 跳过TO和FO的ULAE
                if (StringUtils.equalsAny(businessSourceCode,"TO","FO") && "ULAE".equals(cfType)) {
                    continue;
                }

                List<AtrBussLicGDevAmount> lastCfDevList = summaryDevDataIndex.list(
                        Arrays.asList(summaryId, cfType, AT_LAST_CF)
                );
                
                List<AtrBussLicGDevAmount> movementCfDevList = summaryDevDataIndex.list(
                    Arrays.asList(summaryId, cfType, AT_MOVEMENT)
                );

                calculateGDevCashflowDiscounts(summary, cfType, lastCfDevList, movementCfDevList);
            }
        }
    }
    
    /**
     * 计算汇总后现金流的各种折现
     * @param summary 汇总对象
     * @param cfType 现金流类型
     */
    private void calculateGDevCashflowDiscounts(AtrBussLicG summary, String cfType, List<AtrBussLicGDevAmount> lastDevList,List<AtrBussLicGDevAmount> moveDevList) {

        String accYearMonth = summary.getAccYearMonth();
        String portfolioNo = summary.getPortfolioNo();
        String icgNo = summary.getIcgNo();
        
        // 获取上一期的期末折现+变动折现数据，作为当期的期初折现 (L1)
        BigDecimal prevEndDiscount = prevLastEndDiscountIndex.one(
            Arrays.asList(portfolioNo, icgNo, accYearMonth, cfType, L3), ZERO);
        saveAmount(summary, cfType, L1, prevEndDiscount); // 当期期初折现
        // 计算当期期初锁期折现 (L2)
        BigDecimal initialLockedDiscount = calculateInitialLockedDiscount(summary, cfType);
        saveAmount(summary, cfType, L2, initialLockedDiscount);
        
        // 计算期末折现 (L3/C1)
        BigDecimal endDiscount = calculateEndDiscountFromGDev(yearMonth, lastDevList,false);
        saveAmount(summary, cfType, L3, endDiscount);
        BigDecimal moveDiscount = calculateEndDiscountFromGDev(yearMonth, moveDevList,true);
        saveAmount(summary, cfType, C1, moveDiscount);

        BigDecimal endLockDiscount = calculateLockedEndDiscountFromGDev(accYearMonth,lastDevList,false);
        saveAmount(summary, cfType, L4, endLockDiscount);

        BigDecimal moveLockDiscount = calculateLockedEndDiscountFromGDev(accYearMonth, moveDevList,true);
        saveAmount(summary, cfType, C2, moveLockDiscount);

    }
    
    /**
     * 使用汇总后数据计算锁期期末折现
     * 公式：IFERROR(SUM(评估时点0!D132+SUMPRODUCT(评估时点0!E132:HK132, 事故发生时点锁期折现率!F125:HL125)/事故发生时点锁期折现率!E125),0)
     * @param accYearMonthStr 事故年月
     * @param devList 汇总后发展期列表
     * @return 锁期期末折现
     */
    private BigDecimal calculateLockedEndDiscountFromGDev(String accYearMonthStr, List<AtrBussLicGDevAmount> devList,Boolean isMov) {
        // 当前评估期
        int index;
        if ( isMov ){
            index = 0;
        }else{
            index = 1;
        }
        String yearMonthStr = commonParamMap.get("yearMonth").toString();
        // 获取评估期到出现时间的间隔月份
        DateTime yearMonth = DateUtil.parse(yearMonthStr + "01", "yyyyMMdd");
        DateTime accYearMonth = DateUtil.parse(accYearMonthStr + "01", "yyyyMMdd");
        long betweenMonth = DateUtil.betweenMonth(yearMonth, accYearMonth, true);
        
        // 获取上一期计算的当期的现金流
        BigDecimal prevPeriodAmount = ZERO;
        for (AtrBussLicGDevAmount item : devList) {
            if (item.getDevNo() == index) {
                prevPeriodAmount = item.getAmount();
                break;
            }
        }

        
        // 计算 SUMPRODUCT(E132:HK132, F125:HL125) / E125
        BigDecimal sumProduct = ZERO;
        
        // 锁期基准折现率，根据事故发生时点获取
        Double baseDiscountRate = initIrIndex.one(Arrays.asList(accYearMonthStr, betweenMonth), 1.0);
        
        for (AtrBussLicGDevAmount dev : devList) {
            int devNo = dev.getDevNo();
            if (devNo <= index) {
                continue;
            }
            if (!isMov){
                devNo--;
            }
            
            // 获取对应发展期的锁期折现率(事故月为基准)
            Double discountRate = initIrIndex.one(Arrays.asList(accYearMonthStr, devNo+betweenMonth), null);
            
            if (discountRate != null) {
                sumProduct = sumProduct.add( dev.getAmount().multiply(BigDecimal.valueOf(discountRate)));
            }
        }
        
        // 如果有基准折现率，进行除法运算
        if (baseDiscountRate != null && baseDiscountRate != 0.0) {
            sumProduct = sumProduct.divide(BigDecimal.valueOf(baseDiscountRate),8,RoundingMode.HALF_UP);
        }
        
        // 返回最终结果，即 prevPeriodAmount + sumProduct
        return prevPeriodAmount.add(sumProduct);
    }

    /**
     * 计算当期期初锁期折现 (L2)
     * 公式：IFERROR(SUMPRODUCT(评估时点1!D132:HK132,事故发生时点锁期折现率!$E125:$HL125)/OFFSET(事故发生时点锁期折现率!$D125,0,$E$4-1),0)
     * @param summary 汇总对象
     * @param cfType 现金流类型
     * @return 期初锁期折现
     */
    private BigDecimal calculateInitialLockedDiscount(AtrBussLicG summary, String cfType) {
        Long summaryId = summary.getId();
        String accYearMonthStr = summary.getAccYearMonth();
        
        // 获取上一期的现金流数据
        List<AtrBussLicGDevAmount> lastPeriodDevList = summaryDevDataIndex.list(
            Arrays.asList(summaryId, cfType, AT_LAST_CF)
        );
        
        if (lastPeriodDevList == null || lastPeriodDevList.isEmpty()) {
            return ZERO;
        }
        // 获取评估期和事故期的时间差（即发展期基数）
        String lastYearMonthStr = commonParamMap.get("lastYearMonth").toString();
        DateTime lastYearMonth = DateUtil.parse(lastYearMonthStr + "01", "yyyyMMdd");
        DateTime accYearMonth = DateUtil.parse(accYearMonthStr + "01", "yyyyMMdd");
        long devPeriodBase = DateUtil.betweenMonth(accYearMonth, lastYearMonth, true);
        
        // 基准折现率，使用事故月和发展期基数
        Double baseDiscountRate = initIrIndex.one(Arrays.asList(accYearMonthStr, devPeriodBase), 0.0);
        if (baseDiscountRate == null || baseDiscountRate == 0.0) {
            return ZERO;
        }
        
        // 计算 SUMPRODUCT
        BigDecimal sumProduct = ZERO;
        for (AtrBussLicGDevAmount dev : lastPeriodDevList) {
            int devNo = dev.getDevNo();
            
            // 获取对应事故月和发展期的锁期折现率
            Double discountRate =initIrIndex.one(
                Arrays.asList(accYearMonthStr, devPeriodBase + devNo), 
                null
            );
            
            if (discountRate != null) {
                sumProduct = sumProduct.add(dev.getAmount().multiply(BigDecimal.valueOf( discountRate)));
            }

        }
        
        // 除以基准折现率
        return sumProduct.divide(BigDecimal.valueOf(baseDiscountRate),8,RoundingMode.HALF_UP);
    }

    private BigDecimal calculateEndDiscountByRD(String evalYearMonth, List<AtrBussLicGDevAmount> devList){
        // 计算 SUMPRODUCT(E132:HK132, E6:HK6)
        BigDecimal sumProduct = ZERO;
        for (AtrBussLicGDevAmount dev : devList) {
            int devNo = dev.getDevNo();
            Double discountRate = initIrIndex.one(Arrays.asList(evalYearMonth, devNo), null);
            if (discountRate != null) {
                sumProduct = sumProduct.add(dev.getAmount().multiply(BigDecimal.valueOf(discountRate))) ;
            }
        }
        return sumProduct;
    }
    
    /**
     * 使用汇总后数据计算期末折现
     * 公式：IFERROR(评估时点0!D132+SUMPRODUCT(评估时点0!E132:HK132, 评估时点折现率!E6:HK6),0)
     * @param evalYearMonth 评估时点年月
     * @param devList 汇总后发展期列表
     * @return 期末折现
     */
    private BigDecimal calculateEndDiscountFromGDev(String evalYearMonth, List<AtrBussLicGDevAmount> devList,Boolean isMov) {

        int index;
        if ( isMov ){
            index = 0;
        }else{
            index = 1;
        }
        // 获取第1期的现金流
        BigDecimal prevPeriodAmount = ZERO;
        for (AtrBussLicGDevAmount item : devList) {
            if (item.getDevNo() == index) {
                prevPeriodAmount = item.getAmount();
                break;
            }
        }
        
        // 计算 SUMPRODUCT(E132:HK132, E6:HK6)
        BigDecimal sumProduct =ZERO;
        
        for (AtrBussLicGDevAmount dev : devList) {
            int devNo = dev.getDevNo();
            if (devNo <= index) {
                continue;
            }
            if (!isMov){
                devNo--;
            }
            // 获取对应发展期的评估时点折现率
            BigDecimal discountRate = BigDecimal.valueOf(initIrIndex.one(Arrays.asList(evalYearMonth, devNo), 0d));

            sumProduct = sumProduct.add(dev.getAmount().multiply(discountRate));
        }
        
        // 返回最终结果，即 prevPeriodAmount + sumProduct
        return prevPeriodAmount.add(sumProduct);
    }

    /**
     * 保存金额
     * @param summary 汇总对象
     * @param cfType 现金流类型
     * @param amountType 金额类型编码(L1, L2, L3, L4, C1, C2)
     * @param amount 金额
     */
    private void saveAmount(AtrBussLicG summary, String cfType, String amountType, BigDecimal amount) {
        AtrBussLicGAmount discount = new AtrBussLicGAmount();
        discount.setMainId(summary.getId());
        discount.setYearMonth(yearMonth);
        discount.setCfType(cfType);
        discount.setAmountType(amountType);
        discount.setAmount(amount);
        abp.insert(discount);
    }

    /**
     * 收集上期的期末折现和锁期折现数据，仅用于设置当期的L1和L2
     */
    private void collectPreviousDiscounts() {
        if ( commonParamMap.get("lastActionNo") != null) {
            // 收集上期期末折现数据L3+C1作为当期的期初折现(L1)
            List<AtrSD7> allData = atrBussLicDao.collectPrevDiscounts(commonParamMap);
            for (AtrSD7 a : allData) {
                String portfolioNo = a.getS1();
                String icgNo = a.getS2();
                String accYearMonth = a.getS3();
                String cfType = a.getS4();
                String amountType = a.getS5(); // 直接从SQL结果获取amountType
                double amount = nvl(a.getD1());

                // 保存为L1(当期期初折现)
                prevLastEndDiscountIndex.add(Arrays.asList(portfolioNo, icgNo, accYearMonth, cfType, amountType), BigDecimal.valueOf(amount));
            }

        }

    }

    private void processByAmountTypeForMap(Map<Integer, BigDecimal> devAmountMap, List<AtrDuctLicDevAmount> devList) {
        if (devList != null && !devList.isEmpty()) {
            for (AtrDuctLicDevAmount dev : devList) {
                devAmountMap.merge(dev.getDevNo(), dev.getAmount(), BigDecimal::add);
            }
        }
    }

    private void handleDevAmountTailDifference(List<AtrBussLicUDevAmount> devList, BigDecimal totalAmount, 
                                           String icgNo, String riskClassCode, String cfType) {
        // 计算摊分后的总金额
        BigDecimal distributedSum = devList.stream()
                .map(dev -> nvl(dev.getAmount()))
                .reduce(ZERO, BigDecimal::add);
        
        // 计算尾差
        BigDecimal difference = totalAmount.subtract(distributedSum);
        
        // 如果尾差不为0且存在发展期，则修正尾差
        if (difference.abs().compareTo(ZERO) > 0 && !devList.isEmpty()) {
            // 寻找金额最大的发展期
            AtrBussLicUDevAmount targetDev = null;
            BigDecimal maxAmount = ZERO;
            
            for (AtrBussLicUDevAmount dev : devList) {
                if (dev.getAmount() != null && dev.getAmount().compareTo(ZERO) > 0) {
                    if (targetDev == null || dev.getAmount().compareTo(maxAmount) > 0) {
                        targetDev = dev;
                        maxAmount = dev.getAmount();
                    }
                }
            }
            
            // 将尾差添加到金额最大的发展期
            if (targetDev != null) {
                targetDev.setAmount(targetDev.getAmount().add(difference));
                
                // 校验修正后的总和是否与原始金额一致
                BigDecimal revisedSum = devList.stream()
                        .map(dev -> nvl(dev.getAmount()))
                        .reduce(ZERO, BigDecimal::add);
                
                // 如果仍不一致，抛出异常
                if (revisedSum.compareTo(totalAmount) != 0) {
                    throw new RuntimeException(String.format(
                        "赔付现金流展开一致性校验失败: ICG号=%s, 险类=%s, 现金流类型=%s, 原始金额=%s，摊分后金额=%s，尾差处理后金额=%s",
                        icgNo, riskClassCode, cfType, totalAmount, distributedSum, revisedSum));
                }
            }
        }
    }


    /**
     * 创建现金流的 key，  用以关联当前和上一个版本的当期现金流
     */
    private List<?> createIcuCurCfKey(Object vo, String cfType) {
        List<Object> key = new ArrayList<>();
        key.add(EcfUtil.readField(vo, "policyNo"));
        key.add(EcfUtil.readField(vo, "kindCode"));
        key.add(EcfUtil.readField(vo, "riskClassCode"));
        key.add(EcfUtil.readField(vo, "treatyNo"));
        key.add(EcfUtil.readField(vo, "accYearMonth"));
        key.add(cfType);
        return key;
    }

    /**
     * 合并相同合同组、险类、出险年月下的 CUR_CF / LAST_CF / MOVEMENT 的发展期的金额
     */
    private void mergeDuctLicDevAmount(List<String> key, List<AtrBussLicUDevAmount> icuDevList) {
        // 使用synchronized块保护对threadDevs的访问和修改
        synchronized (threadDevs) {
            List<AtrDuctLicDevAmount> list = threadDevs.computeIfAbsent(key, k -> new ArrayList<>());
            OUT: for (AtrBussLicUDevAmount dev : icuDevList) {
                for (AtrDuctLicDevAmount duct : list) {
                    if (duct.getAmountType().equals(dev.getAmountType()) && duct.getDevNo().intValue() == dev.getDevNo()) {
                        if (duct.getAmount() == null) {
                            duct.setAmount(dev.getAmount());
                        } else {
                            duct.setAmount(duct.getAmount().add(dev.getAmount()));
                        }
                        continue OUT;
                    }
                }
                AtrDuctLicDevAmount duct = new AtrDuctLicDevAmount();
                list.add(duct);
                duct.setAmountType(dev.getAmountType());
                duct.setDevNo(dev.getDevNo());
                duct.setAmount(dev.getAmount());
            }
        }
    }

}

