package com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class AtrBussIbnrcalcTVDInfoVo {
    @ApiModelProperty(value = "发展期")
    private Integer devNo;
    @ApiModelProperty(value = "Expect % Reported")
    private BigDecimal slExpectedReportedRatio;
    @ApiModelProperty(value = "利息比率")
    private BigDecimal interestRatio;
    @ApiModelProperty(value = "时间段")
    private BigDecimal timePeriod;
    @ApiModelProperty(value = "时间段")
    private String yearMonth;
    @ApiModelProperty(value = "可修改列")
    private String modify;
    @ApiModelProperty(value = "Discounted Unpaid")
    private BigDecimal discountUnpaid;
    @ApiModelProperty(value = "Time Value Discount")
    private BigDecimal timeValueDiscount;
}
