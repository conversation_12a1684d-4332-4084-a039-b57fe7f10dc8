<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-01-11 16:41:01 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussLrcToDao">

    <select id="getIcgMaxMainId" resultType="long">
        select greatest((select COALESCE(max(id),0) from atr_buss_to_lrc_g),
                       (select COALESCE(max(main_id),0) from atr_buss_to_lrc_g_dev))
    </select>

    <select id="getUltMaxMainId" resultType="long">
        select greatest((select COALESCE(max(id),0) from atr_buss_to_lrc_t_ul),
                       (select COALESCE(max(main_id),0) from atr_buss_to_lrc_t_ul_dev))
    </select>

    <select id="getRTableMaxMainId" resultType="long">
        select greatest((select COALESCE(max(id),0) from atr_buss_to_lrc_t_ul_r),
                       (select COALESCE(max(main_id),0) from atr_buss_to_lrc_t_ul_r_dev))
    </select>


    <update id="truncateBaseDataT">
        truncate table atr_temp_to_t_lrc_u
    </update>

    <insert id="partitionBaseDataT" >
        INSERT INTO atr_temp_to_t_lrc_u(entity_id, year_month, risk_class_code, treaty_no, ri_policy_no,
                                        ri_endorse_seq_no,
                                        policy_no, endorse_seq_no, risk_code, policy_contract_date, kind_code,
                                        portfolio_no, policy_icg_no,
                                        icg_no, cmunit_no, company_code1, company_code2, company_code3,
                                        company_code4, effective_date, expiry_date, ri_effective_date, ri_expiry_date,
                                        approval_date, contract_date, issue_date, premium,cur_paid_premium, net_fee,cur_net_fee, fixed_fee_rate,
                                        prepaid_fee_rate, fin_acc_channel, fin_product_code, fin_detail_code,
                                        fin_sub_product_code, pl_judge_rslt,
                                        treaty_name, policy_issue_date, policy_confirm_date, policy_approval_date,
                                        policy_premium, policy_netfee,
                                        dept_id, channel_id, center_code,reinsurer_code,icg_name,sectiono_code,buss_year_month,
                                        endorse_type_code, special_process_type, ri_ceding_rate, pn)
        WITH special_process_check AS (
            SELECT
                policy_no,
                -- 检查是否存在15/16类型的批单在当前评估月
                CASE WHEN MAX(CASE WHEN year_month = #{yearMonth,jdbcType=VARCHAR}
                                   AND EXISTS (
                                       SELECT 1 FROM unnest(string_to_array(endorse_type_code, ',')) as endorse_code
                                       WHERE trim(endorse_code) IN ('15', '16')
                                   ) THEN 1 ELSE 0 END) = 1
                     THEN true ELSE false END as has_current_month_1516,
                -- 检查是否存在15/16类型的批单（不限月份）
                CASE WHEN MAX(CASE WHEN EXISTS (
                                       SELECT 1 FROM unnest(string_to_array(endorse_type_code, ',')) as endorse_code
                                       WHERE trim(endorse_code) IN ('15', '16')
                                   ) THEN 1 ELSE 0 END) = 1
                     THEN true ELSE false END as has_any_1516
            FROM atr_dap_to_paid_t check_unit
            WHERE check_unit.entity_id = #{entityId,jdbcType=BIGINT}
              AND check_unit.year_month &lt;= #{yearMonth,jdbcType=VARCHAR}
              AND check_unit.endorse_type_code IS NOT NULL
            GROUP BY policy_no
        ),
        unique_treaties AS (SELECT *,
                                        ROW_NUMBER() OVER (
                                            PARTITION BY treaty_no
                                            ORDER BY contract_date DESC, ctid DESC
                                            ) as rn
                                 FROM atr_dap_treaty),
        treaty_premium_sum AS (
            SELECT
                a.treaty_no,
                SUM(a.premium) as total_premium
            FROM atr_dap_to_paid_t a
            WHERE a.year_month &lt;= #{yearMonth,jdbcType=VARCHAR}
            GROUP BY a.treaty_no
        )
        SELECT a.entity_id,
               #{yearMonth,jdbcType=VARCHAR},
               min(a.risk_class_code),
               a.treaty_no,
               min(a.ri_policy_no),
               min(a.ri_endorse_seq_no),
               a.policy_no,
               a.endorse_seq_no,
               min(a.risk_code),
               min(a.policy_contract_date),
               a.kind_code,
               max(b.portfolio_no),
               max(a.policy_icg_no),
               max(b.icg_no),
               max(a.cmunit_no),
               max(a.company_code1),
               max(a.company_code2),
               max(a.company_code3),
               max(a.company_code4),
               max(a.effective_date),
               max(a.expiry_date),
               max(a.ri_effective_date),
               max(a.ri_expiry_date),
               max(a.approval_date),
               max(b.contract_date),
               max(a.issue_date),
               SUM(a.premium),
               SUM(case when a.year_month = #{yearMonth,jdbcType=VARCHAR} then a.premium else 0 end),
               SUM(a.net_fee),
               SUM(case when a.year_month = #{yearMonth,jdbcType=VARCHAR} then a.net_fee else 0 end),
               min(b.fixed_fee_rate),
               min(b.prepaid_fee_rate),
               min(a.fin_acc_channel),
               min(a.fin_product_code),
               min(a.fin_detail_code),
               min(a.fin_sub_product_code),
               min(b.pl_judge_rslt),
               COALESCE(MAX(a.treaty_name), MAX(b.treaty_name)) as treaty_name,
               MAX(a.policy_issue_date)                    as policy_issue_date,
               MAX(a.policy_confirm_date)                  as policy_confirm_date,
               MAX(a.policy_approval_date)                 as policy_approval_date,
               MAX(a.policy_premium)                       as policy_premium,
               MAX(a.policy_netfee)                        as policy_netfee,
               MAX(a.dept_id)                              as dept_id,
               MAX(a.channel_id)                           as channel_id,
               MAX(a.center_code)                          as center_code,
               a.reinsurer_code,
               max(b.icg_name),
               a.sectiono_code,
               max(a.year_month),
               max(a.endorse_type_code) as endorse_type_code,
               CASE
                   WHEN spc.policy_no IS NULL THEN 0
                   WHEN spc.has_current_month_1516 = true THEN 1
                   WHEN spc.has_any_1516 = true AND spc.has_current_month_1516 = false THEN 2
                   ELSE 0
               END as special_process_type,
               CASE
                   WHEN tps.total_premium IS NULL OR tps.total_premium = 0 THEN 0
                   ELSE SUM(a.premium) / tps.total_premium
               END as ri_ceding_rate,
               -- 添加分区号，用于后续分批处理，使用配置的parts值
               trunc(random() * #{parts,jdbcType=INTEGER}) as pn
        FROM atr_dap_to_paid_t a
                 LEFT JOIN
                 (SELECT * FROM unique_treaties WHERE rn = 1) b ON a.treaty_no = b.treaty_no
                 LEFT JOIN special_process_check spc ON a.policy_no = spc.policy_no
                 LEFT JOIN treaty_premium_sum tps ON a.treaty_no = tps.treaty_no
        WHERE a.year_month &lt;= #{yearMonth,jdbcType=VARCHAR}
        GROUP BY a.entity_id,a.treaty_no,a.policy_no,a.endorse_seq_no,a.kind_code,a.sectiono_code,a.reinsurer_code,
                 spc.policy_no, spc.has_current_month_1516, spc.has_any_1516, tps.total_premium
    </insert>



    <select id="listBasePolicyT" resultType="com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.AtrBussToLrcTUlR">
        SELECT
            entity_id         as entityId,
            buss_year_month   as bussYearMonth,
            year_month        as yearMonth,
            treaty_no         AS treatyNo,
            policy_no         as policyNo,
            ri_policy_no      as riPolicyNo,
            ri_endorse_seq_no as riEndorseSeqNo,
            policy_icg_no     as policyIcgNo,
            risk_class_code   as riskClassCode,
            risk_code         as riskCode,
            portfolio_no      as portfolioNo,
            icg_no            as icgNo,
            endorse_seq_no    as endorseSeqNo,
            kind_code         as kindCode,
            effective_date    AS effectiveDate,
            expiry_date       AS expiryDate,
            ri_effective_date as riEffectiveDate,
            ri_expiry_date    as riExpiryDate,
            premium           AS premium,
            net_fee           AS netFee,
            fixed_fee_rate    AS fixedFeeRate,
            prepaid_fee_rate  AS prepaidFeeRate,
            cmunit_no         as cmunitNo,
            company_code1     as companyCode1,
            company_code2     as companyCode2,
            company_code3     as companyCode3,
            company_code4     as companyCode4,
            approval_date     as approvalDate,
            policy_contract_date as policyContractDate,
            contract_date     as contractDate,
            issue_date        as issueDate,
            fin_acc_channel   as finAccChannel,
            fin_product_code  as finProductCode,
            fin_detail_code   as finDetailCode,
            fin_sub_product_code as finSubProductCode,
            pl_judge_rslt     as plJudgeRslt,
            treaty_name       as treatyName,
            policy_issue_date as policyIssueDate,
            policy_confirm_date as policyConfirmDate,
            policy_approval_date as policyApprovalDate,
            policy_premium    as policyPremium,
            policy_netfee     as policyNetfee,
            dept_id           as deptId,
            channel_id        as channelId,
            center_code       as centerCode,
            sectiono_code     as sectionoCode,
            reinsurer_code    as reinsurerCode,
            cur_paid_premium  as curPaidPremium,
            cur_net_fee as curNetFee,
            endorse_type_code as endorseTypeCode,
            special_process_type as specialProcessType,
            ri_ceding_rate    as riCedingRate,
            pn                as pn
        FROM
            atr_temp_to_t_lrc_u
    </select>

    <select id="listPreBussToLrcTUl" resultType="com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.AtrBussToLrcTUl">
        SELECT t.treaty_no             as treatyNo,
               t.policy_no             AS policyNo,
               t.endorse_seq_no        AS endorseSeqNo,
               t.kind_code             AS kindCode,
               t.cur_premium           AS curPremium,
               t.cur_net_fee           AS curNetFee,
               t.pre_cuml_paid_premium AS preCumlPaidPremium,
               t.pre_cuml_paid_net_fee AS preCumlPaidNetFee,
               t.cur_ed_premium        AS curEdPremium,
               t.cur_ed_net_fee        AS curEdNetFee,
               t.pre_cuml_ed_premium   AS preCumlEdPremium,
               t.pre_cuml_ed_net_fee   AS preCumlEdNetFee
        FROM atr_buss_to_lrc_t_ul t
        where t.year_month = #{lastYearMonth,jdbcType=VARCHAR}
          and t.action_no = #{lastActionNo,jdbcType=VARCHAR}
    </select>

    <select id="listPreBussToLrcTUlRDetailed" resultType="com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.AtrBussToLrcTUlR">
        SELECT r.treaty_no             as treatyNo,
               r.policy_no             AS policyNo,
               r.endorse_seq_no        AS endorseSeqNo,
               r.kind_code             AS kindCode,
               r.sectiono_code         AS sectionoCode,
               r.reinsurer_code        AS reinsurerCode,
               r.pre_cuml_ed_premium   AS preCumlEdPremium,
               r.pre_cuml_ed_net_fee   AS preCumlEdNetFee,
               r.cur_ed_premium        AS curEdPremium,
               r.cur_ed_net_fee        AS curEdNetFee
        FROM atr_buss_to_lrc_t_ul_r r
        where r.year_month = #{lastYearMonth,jdbcType=VARCHAR}
          and r.action_no = #{lastActionNo,jdbcType=VARCHAR}
    </select>

    <select id="listDapTreaty" resultType="com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.AtrDapTreaty">
        SELECT
            treaty_no                   AS treatyNo,
            sectiono_code               AS sectionoCode,
            reinsurer_code              AS reinsurerCode,
            icg_name                    AS icgName,
            effective_date              AS effectiveDate,
            expiry_date                 AS expiryDate,
            brokerage_rate              AS brokerageRate,
            fixed_fee_rate              AS fixedFeeRate,
            prepaid_fee_rate            AS prepaidFeeRate,
            floating_handling_fee_cap   AS floatingHandlingFeeCap,
            profit_fee_rate             AS profitFeeRate,
            profit_mng_fee_rate         AS profitMngFeeRate,
            est_premium                 AS estPremium
        FROM
            atr_dap_treaty
        where year_month = #{yearMonth,jdbcType=VARCHAR}
    </select>

    <select id="listPreBussToLrcIcgPremium" resultType="com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.AtrBussToLrcIcgPremium">
        select a.portfolio_no                       as portfolioNo,
               a.icg_no                             as icgNo,
               a.risk_class_code                    as riskClassCode,
               sum((a.old_premium + a.new_premium)) as oldPremium
        FROM atr_buss_to_lrc_icg_premium a
        WHERE action_no = #{lastActionNo,jdbcType=VARCHAR}
          AND year_month = #{lastYearMonth,jdbcType=VARCHAR}
          and main_treaty_type = 'T'
        group by risk_class_code, portfolio_no, icg_no
    </select>
    
    <!-- 清空过渡期已赚保费临时表 -->
    <update id="truncateTransitionTempTable">
        TRUNCATE TABLE atr_temp_to_transition_ed_premium
    </update>
    
    <!-- 向临时表中插入需要计算的保单数据 -->
    <insert id="insertTransitionTempData" parameterType="java.util.Map">
        INSERT INTO atr_temp_to_transition_ed_premium (
            pn, 
            treaty_no,
            policy_no, 
            endorse_seq_no, 
            kind_code, 
            effective_date, 
            expiry_date, 
            premium
        )
        SELECT 
            trunc(random() * #{parts}) AS pn,
            treaty_no,
            policy_no, 
            endorse_seq_no, 
            kind_code, 
            effective_date, 
            expiry_date, 
            SUM(premium) AS premium
        FROM 
            atr_dap_to_paid_t
        WHERE 
            entity_id = #{entityId}
            AND year_month &lt;= #{yearMonth}
            AND expiry_date >= #{evStartDate}
        GROUP BY 
            treaty_no, policy_no, endorse_seq_no, kind_code, effective_date, expiry_date
    </insert>
    
    <!-- 获取指定分区的过渡期已赚保费计算基础数据 -->
    <select id="getTransitionPartBaseVos" resultType="com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.AtrBussToLrcTUlR">
        SELECT 
            treaty_no AS "treatyNo",
            policy_no AS "policyNo",
            endorse_seq_no AS "endorseSeqNo",
            kind_code AS "kindCode",
            effective_date AS "effectiveDate",
            expiry_date AS "expiryDate",
            premium AS "premium"
        FROM 
            atr_temp_to_transition_ed_premium
        WHERE 
            pn = #{pn}
    </select>
    
    <!-- 创建atr_dap_to_paid_t表的备份表 -->
    <update id="createPaidBackupTable">
        DROP TABLE IF EXISTS atr_dap_to_paid_t_bak;
        CREATE TABLE atr_dap_to_paid_t_bak AS
        SELECT * FROM atr_dap_to_paid_t
    </update>

    <!-- 从atr_dap_to_paid_t表删除符合条件的数据 -->
    <delete id="deletePaidData">
        DELETE FROM atr_dap_to_paid_t
        WHERE treaty_no IN (
            SELECT t.treaty_no 
            FROM atr_dap_to_paid_t t
            GROUP BY t.treaty_no
            HAVING COUNT(DISTINCT t.policy_no) = (
                SELECT COUNT(DISTINCT t2.policy_no)
                FROM atr_dap_to_paid_t t2
                JOIN atr_temp_policies_to_clean c ON t2.policy_no = c.policy_no
                WHERE t2.treaty_no = t.treaty_no
            )
            AND COUNT(DISTINCT t.policy_no) > 0
        )
    </delete>
    
    <!-- 判断过渡期已赚保费表中是否存在数据 -->
    <select id="hasTransitionData" resultType="boolean">
        SELECT EXISTS (
            SELECT 1
            FROM atr_buss_to_transition_ed_premium
            WHERE entity_id = #{entityId}
        )
    </select>

    <!-- 获取指定分区的保单数据，用于分批处理 -->
    <select id="getPartPolicyData" resultType="com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.AtrBussToLrcTUlR">
        SELECT
            entity_id         as entityId,
            buss_year_month   as bussYearMonth,
            year_month        as yearMonth,
            treaty_no         AS treatyNo,
            policy_no         as policyNo,
            ri_policy_no      as riPolicyNo,
            ri_endorse_seq_no as riEndorseSeqNo,
            policy_icg_no     as policyIcgNo,
            risk_class_code   as riskClassCode,
            risk_code         as riskCode,
            portfolio_no      as portfolioNo,
            icg_no            as icgNo,
            endorse_seq_no    as endorseSeqNo,
            kind_code         as kindCode,
            effective_date    AS effectiveDate,
            expiry_date       AS expiryDate,
            ri_effective_date as riEffectiveDate,
            ri_expiry_date    as riExpiryDate,
            premium           AS premium,
            net_fee           AS netFee,
            fixed_fee_rate    AS fixedFeeRate,
            prepaid_fee_rate  AS prepaidFeeRate,
            cmunit_no         as cmunitNo,
            company_code1     as companyCode1,
            company_code2     as companyCode2,
            company_code3     as companyCode3,
            company_code4     as companyCode4,
            approval_date     as approvalDate,
            policy_contract_date as policyContractDate,
            contract_date     as contractDate,
            issue_date        as issueDate,
            fin_acc_channel   as finAccChannel,
            fin_product_code  as finProductCode,
            fin_detail_code   as finDetailCode,
            fin_sub_product_code as finSubProductCode,
            pl_judge_rslt     as plJudgeRslt,
            treaty_name       as treatyName,
            policy_issue_date as policyIssueDate,
            policy_confirm_date as policyConfirmDate,
            policy_approval_date as policyApprovalDate,
            policy_premium    as policyPremium,
            policy_netfee     as policyNetfee,
            dept_id           as deptId,
            channel_id        as channelId,
            center_code       as centerCode,
            sectiono_code     as sectionoCode,
            reinsurer_code    as reinsurerCode,
            cur_paid_premium  as curPaidPremium,
            cur_net_fee       as curNetFee,
            endorse_type_code as endorseTypeCode,
            special_process_type as specialProcessType,
            ri_ceding_rate    as riCedingRate
        FROM
            atr_temp_to_t_lrc_u
        WHERE
            pn = #{pn}
    </select>

    <!-- 获取指定分区的上期数据，使用JOIN优化查询 -->
    <select id="getPartPrePolicyData" resultType="com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.AtrBussToLrcTUl">
        SELECT
            temp.treaty_no             as treatyNo,
            temp.policy_no             AS policyNo,
            temp.endorse_seq_no        AS endorseSeqNo,
            temp.kind_code             AS kindCode,
            pre.cur_premium            AS curPremium,
            pre.cur_net_fee            AS curNetFee,
            pre.pre_cuml_paid_premium  AS preCumlPaidPremium,
            pre.pre_cuml_paid_net_fee  AS preCumlPaidNetFee,
            pre.cur_ed_premium         AS curEdPremium,
            pre.cur_ed_net_fee         AS curEdNetFee,
            pre.pre_cuml_ed_premium    AS preCumlEdPremium,
            pre.pre_cuml_ed_net_fee    AS preCumlEdNetFee
        FROM
            (SELECT DISTINCT treaty_no, policy_no, endorse_seq_no, kind_code
             FROM atr_temp_to_t_lrc_u
             WHERE pn = #{pn}) temp
        LEFT JOIN atr_buss_to_lrc_t_ul pre
            ON temp.treaty_no = pre.treaty_no
            AND temp.policy_no = pre.policy_no
            AND temp.endorse_seq_no = pre.endorse_seq_no
            AND temp.kind_code = pre.kind_code
            AND pre.year_month = #{lastYearMonth,jdbcType=VARCHAR}
            AND pre.action_no = #{lastActionNo,jdbcType=VARCHAR}
    </select>

    <!-- 获取指定分区的上期详细数据，使用JOIN优化查询 -->
    <select id="getPartPrePolicyDetailedData" resultType="com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.AtrBussToLrcTUlR">
        SELECT
            temp.treaty_no             as treatyNo,
            temp.policy_no             AS policyNo,
            temp.endorse_seq_no        AS endorseSeqNo,
            temp.kind_code             AS kindCode,
            temp.sectiono_code         AS sectionoCode,
            temp.reinsurer_code        AS reinsurerCode,
            pre.pre_cuml_ed_premium    AS preCumlEdPremium,
            pre.pre_cuml_ed_net_fee    AS preCumlEdNetFee,
            pre.cur_ed_premium         AS curEdPremium,
            pre.cur_ed_net_fee         AS curEdNetFee
        FROM
            (SELECT DISTINCT treaty_no, policy_no, endorse_seq_no, kind_code, sectiono_code, reinsurer_code
             FROM atr_temp_to_t_lrc_u
             WHERE pn = #{pn}) temp
        LEFT JOIN atr_buss_to_lrc_t_ul_r pre
            ON temp.treaty_no = pre.treaty_no
            AND temp.policy_no = pre.policy_no
            AND temp.endorse_seq_no = pre.endorse_seq_no
            AND temp.kind_code = pre.kind_code
            AND temp.sectiono_code = pre.sectiono_code
            AND temp.reinsurer_code = pre.reinsurer_code
            AND pre.year_month = #{lastYearMonth,jdbcType=VARCHAR}
            AND pre.action_no = #{lastActionNo,jdbcType=VARCHAR}
    </select>
</mapper>