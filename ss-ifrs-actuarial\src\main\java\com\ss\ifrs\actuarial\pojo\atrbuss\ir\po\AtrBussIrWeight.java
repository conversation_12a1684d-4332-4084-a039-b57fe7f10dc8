/**
 * 
 * This file was generated by Taiping MyBatis Generator(v1.2.12).
 * Date: 2024-08-21 18:15:20
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.ir.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by Taiping MyBatis Generator(v1.2.12).
 * Create Date: 2024-08-21 18:15:20<br/>
 * Description: 加权平均初始利率主表<br/>
 * Table Name: ATR_BUSS_IR_WEIGHT<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "加权平均初始利率主表")
public class AtrBussIrWeight implements Serializable {

    @ApiModelProperty(value = "上传数据的ID", required = true)
    private Long uploadRateId;

    /**
     * Database column: ATR_BUSS_IR_WEIGHT.ID
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: ATR_BUSS_IR_WEIGHT.ENTITY_ID
     * Database remarks: 机构ID
     */
    @ApiModelProperty(value = "机构ID", required = true)
    private Long entityId;

    /**
     * Database column: ATR_BUSS_IR_WEIGHT.YEAR_MONTH
     * Database remarks: 评估年月
     */
    @ApiModelProperty(value = "评估年月", required = true)
    private String yearMonth;

    /**
     * Database column: ATR_BUSS_IR_WEIGHT.CURRENCY_CODE
     * Database remarks: 币别
     */
    @ApiModelProperty(value = "币别", required = true)
    private String currencyCode;

    /**
     * Database column: ATR_BUSS_IR_WEIGHT.ICG_NO
     * Database remarks: 合同组编码
     */
    @ApiModelProperty(value = "合同组编码", required = true)
    private String icgNo;

    /**
     * Database column: ATR_BUSS_IR_WEIGHT.PREMIUM
     * Database remarks: 保费
     */
    @ApiModelProperty(value = "保费", required = true)
    private BigDecimal premium;

    /**
     * Database column: ATR_BUSS_IR_WEIGHT.CREATE_TIME
     * Database remarks: 创建时间
     */
    @ApiModelProperty(value = "创建时间", required = false)
    private Date createTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public BigDecimal getPremium() {
        return premium;
    }

    public void setPremium(BigDecimal premium) {
        this.premium = premium;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUploadRateId() {
        return uploadRateId;
    }

    public void setUploadRateId(Long uploadRateId) {
        this.uploadRateId = uploadRateId;
    }
}