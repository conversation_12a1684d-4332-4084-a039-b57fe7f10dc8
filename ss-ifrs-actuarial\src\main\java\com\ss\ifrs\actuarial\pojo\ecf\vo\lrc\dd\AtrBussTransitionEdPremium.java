package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.dd;

import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 过渡期已赚保费
 *
 */
@Data
@Tab("atr_buss_dd_transition_ed_premium")
public class AtrBussTransitionEdPremium implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 机构ID
     */
    private Long entityId;

    /**
     * 保单号
     */
    private String policyNo;

    /**
     * 批改序号
     */
    private String endorseSeqNo;

    /**
     * 险类
     */
    private String kindCode;

    /**
     * 评估期
     */
    private String yearMonth;

    /**
     * 已赚保费
     */
    private BigDecimal edPremium;
} 