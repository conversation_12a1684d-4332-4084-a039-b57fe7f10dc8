CALL qtc_pack_commonutils_proc_DROP_TABLE('qtc_conf_check_rule');
CREATE TABLE qtcuser.qtc_conf_check_rule (
  rule_id int8 NOT NULL,
  entity_id int8,
  rule_code varchar(16) COLLATE pg_catalog.default,
  rule_e_name varchar(64) COLLATE pg_catalog.default,
  rule_c_name varchar(64) COLLATE pg_catalog.default,
  rule_l_name varchar(64) COLLATE pg_catalog.default,
  rule_type varchar(3) COLLATE pg_catalog.default,
  run_node varchar(3) COLLATE pg_catalog.default,
  err_level varchar(3) COLLATE pg_catalog.default,
  checked_id int8,
  checked_time timestamp(6),
  audit_state char(1) COLLATE pg_catalog.default,
  checked_msg varchar(1000) COLLATE pg_catalog.default,
  valid_is char(1) COLLATE pg_catalog.default,
  remark varchar(4000) COLLATE pg_catalog.default,
  creator_id int8,
  create_time timestamp(6),
  updator_id int8,
  update_time timestamp(6),
  CONSTRAINT qtc_conf_check_rule_pkey PRIMARY KEY (rule_id)
)
TABLESPACE qtc_space
;

ALTER TABLE qtcuser.qtc_conf_check_rule 
  OWNER TO qtcuser;

COMMENT ON COLUMN qtcuser.qtc_conf_check_rule.entity_id IS '机构ID';

COMMENT ON COLUMN qtcuser.qtc_conf_check_rule.rule_code IS '规则编码';

COMMENT ON COLUMN qtcuser.qtc_conf_check_rule.rule_c_name IS '规则名称';

COMMENT ON COLUMN qtcuser.qtc_conf_check_rule.rule_type IS '规则类型：Lrc-LRC ；Lic-LIC';

COMMENT ON COLUMN qtcuser.qtc_conf_check_rule.run_node IS '执行节点：1-数据检查；2-结果校验';

COMMENT ON COLUMN qtcuser.qtc_conf_check_rule.err_level IS '告警级别：1-预警；2-异常';

COMMENT ON COLUMN qtcuser.qtc_conf_check_rule.checked_id IS '审核人';

COMMENT ON COLUMN qtcuser.qtc_conf_check_rule.checked_time IS '审核时间';

COMMENT ON COLUMN qtcuser.qtc_conf_check_rule.audit_state IS '审核状态';

COMMENT ON COLUMN qtcuser.qtc_conf_check_rule.checked_msg IS '审核意见';

COMMENT ON COLUMN qtcuser.qtc_conf_check_rule.valid_is IS '有效性';

COMMENT ON COLUMN qtcuser.qtc_conf_check_rule.remark IS '备注';

COMMENT ON COLUMN qtcuser.qtc_conf_check_rule.creator_id IS '创建人';

COMMENT ON COLUMN qtcuser.qtc_conf_check_rule.create_time IS '创建时间';

COMMENT ON COLUMN qtcuser.qtc_conf_check_rule.updator_id IS '最后修改人';

COMMENT ON COLUMN qtcuser.qtc_conf_check_rule.update_time IS '最后修改时间';

COMMENT ON TABLE qtcuser.qtc_conf_check_rule IS '计量-数据校验配置表';


  
CALL qtcuser.qtc_pack_commonutils_proc_DROP_SEQUENCE('qtc_seq_conf_check_rule');
create sequence qtc_seq_conf_check_rule
              minvalue 1
              start with 1
              increment by 1;