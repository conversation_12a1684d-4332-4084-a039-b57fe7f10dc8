create or replace package dm_pack_table_deal is

PROCEDURE proc_add_src_table(p_tablename VARCHAR2, p_tablecomment VARCHAR2);

PROCEDURE proc_add_tgt_table(p_tablename VARCHAR2, p_tablecomment VARCHAR2);

PROCEDURE proc_add_stat_table(p_tablename VARCHAR2, p_tablecomment VARCHAR2);

--添加字段
PROCEDURE proc_ADD_TABLE_COLUMN(p_table IN VARCHAR2, p_column IN VARCHAR2, p_columntype IN VARCHAR2,p_comment IN VARCHAR2,p_schema IN VARCHAR2);

PROCEDURE proc_DROP_TABLE(p_table IN VARCHAR2,p_schema IN VARCHAR2);
--删除字段
PROCEDURE proc_DROP_TABLE_COLUMN(p_table IN VARCHAR2, p_column IN VARCHAR2,p_schema IN VARCHAR2);

PROCEDURE proc_DROP_SEQUENCE(p_sequence_name IN VARCHAR2,p_schema IN VARCHAR2);

PROCEDURE proc_modify_table_comment(p_tablename VARCHAR2, p_tablecomment VARCHAR2, p_schema VARCHAR2);

PROCEDURE MODIFY_TABLE_COLUMNCOMMENT(p_table VARCHAR2, p_column VARCHAR2, p_columnComment VARCHAR2, p_schema VARCHAR2);

end dm_pack_table_deal;
/
create or replace package body dm_pack_table_deal is

PROCEDURE proc_add_src_table(p_tablename VARCHAR2, p_tablecomment VARCHAR2)
 is

 V_COUNT NUMBER;
 v_schema VARCHAR2(100) := 'odsuser';
 v_table_name VARCHAR2(100);
 v_column VARCHAR2(100);
 v_columntype VARCHAR2(100);
 v_comment varchar2(1000);
 v_biz_code VARCHAR2(100);
 v_sequence_name VARCHAR2(100);
 v_trigger_name VARCHAR2(100);
 --v_constraint_name VARCHAR2(100);
 --v_synonym_name VARCHAR2(100);
 v_synonym_table VARCHAR2(100);
 BEGIN
  -- Routine body goes here...
        select  COUNT(1)
          INTO V_COUNT from all_tables where UPPER(OWNER) = UPPER(v_schema)
        and UPPER(table_name) = UPPER(p_tablename);
        IF V_COUNT < 1 THEN
          v_biz_code := replace(upper(p_tablename),upper('ods_'),'');
          v_synonym_table := p_tablename;
          v_table_name := v_schema||'.'||p_tablename;
          --创建表
           EXECUTE IMMEDIATE  'CREATE TABLE ' || v_table_name|| ' (ID NUMBER(11))';
           EXECUTE IMMEDIATE 'comment on column '||v_table_name||'.ID is ''主键Id''';
           EXECUTE IMMEDIATE  'comment on table ' || v_table_name || ' is ''' ||
                              P_TABLECOMMENT || '''';

          --增加默认字段
          for rec_column_ref in (
            select
              src_column,
              col_type,
              col_length,
              col_type||'(' || col_length || ')' as cloumnType,
              col_desc
            from dm_conf_table_column_ref
            where original_is='1' and upper(SRC_COLUMN) <> upper('ID')
             ORDER BY display_no) loop
             v_column := rec_column_ref.src_column;
             v_columntype := rec_column_ref.cloumnType;
             v_comment := rec_column_ref.col_desc;
             SELECT count(*)
                  INTO v_count
                  FROM user_tab_columns
                 WHERE table_name = upper(v_table_name)
                   AND column_name = upper(v_column);
                IF v_count = 0 THEN
                   EXECUTE IMMEDIATE 'alter table ' || v_table_name || ' add ' || v_column ||' ' || v_columntype;
                   EXECUTE IMMEDIATE 'comment on column ' || v_table_name || '.' || v_column ||' is ''' || v_comment || '''';
                  null;
                END IF;
          end loop;
         v_sequence_name := 'ods_seq_'||v_biz_code;
          --创建序列
          SELECT count(*)
              INTO v_count
          from all_sequences
          WHERE sequence_name = upper(v_sequence_name)
             and UPPER(SEQUENCE_OWNER) = UPPER(v_schema);

          v_sequence_name := v_schema||'.'||v_sequence_name;
         if v_count <= 0 then
           EXECUTE IMMEDIATE 'create sequence '||v_sequence_name||'
                  minvalue 1
                  maxvalue 9999999999999999999999999999
                  start with 1
                  increment by 1
                  cache 20';
           --EXECUTE IMMEDIATE 'grant select on '||v_sequence_name||' to dmuser';
         end if;

         --触发器
         v_trigger_name := 'ODS_TR_'||v_biz_code;
         EXECUTE IMMEDIATE 'create or replace trigger '||v_schema||'.'||v_trigger_name||'
                  before insert on '||v_table_name||'
                  for each row
                  declare
                   -- local variable here
                   begin
                    -- Column "ID" uses sequence di_test_id
                   select '||v_sequence_name||'.nextval into :new.ID from dual;
                   end '||v_trigger_name||' ;';

         v_sequence_name := 'dm_seq_stat_'||v_biz_code;
              --创建序列
          SELECT count(*)
              INTO v_count
          from all_sequences
          WHERE sequence_name = upper(v_sequence_name)
             and UPPER(SEQUENCE_OWNER) = UPPER(v_schema);

         if v_count <= 0 then
           EXECUTE IMMEDIATE 'create sequence '||v_sequence_name||'
                  minvalue 1
                  maxvalue 9999999999999999999999999999
                  start with 1
                  increment by 1
                  cache 20';
           --EXECUTE IMMEDIATE 'grant select on '||v_sequence_name||' to dmuser';
         end if;



         --EXECUTE IMMEDIATE '';
         --主键

         --v_constraint_name := 'ODS_PK_SRC_'||v_biz_code;
          --EXECUTE IMMEDIATE 'ALTER TABLE ' || v_table_name || ' ADD CONSTRAINT ' || v_constraint_name || ' PRIMARY KEY (id)';

         --同义词
         EXECUTE IMMEDIATE 'create or replace synonym '||v_synonym_table||' for '||v_table_name;

        END IF;
END proc_add_src_table;



PROCEDURE proc_add_tgt_table(p_tablename VARCHAR2, p_tablecomment VARCHAR2)
 is

   V_COUNT NUMBER;
   --v_schema VARCHAR2(100) := 'dmuser';
 BEGIN
  -- Routine body goes here...
 SELECT COUNT(1)
          INTO V_COUNT
          FROM user_tables
         WHERE UPPER(table_name) = UPPER(p_tablename);

        IF V_COUNT < 1 THEN

          EXECUTE IMMEDIATE  'CREATE TABLE ' || P_TABLENAME|| ' (ID NUMBER(11))';
                     EXECUTE IMMEDIATE 'comment on column '||P_TABLENAME||'.ID is ''主键Id''';
          EXECUTE IMMEDIATE  'comment on table ' || P_TABLENAME || ' is ''' ||
                              P_TABLECOMMENT || '''';
          for rec_column_ref in (select tgt_column as columnName, tgt_col_type, tgt_col_length,  tgt_col_type||'(' || tgt_col_length || ')' as cloumnType,  tgt_col_desc as comments from dm_conf_table_column_ref where original_is='1' ORDER BY display_no) loop

             dm_pack_commonutils.ADD_TABLE_COLUMN(P_TABLENAME, rec_column_ref.columnName, rec_column_ref.cloumnType, rec_column_ref.comments);
          end loop;

        END IF;
END proc_add_tgt_table;

PROCEDURE proc_add_stat_table(p_tablename VARCHAR2, p_tablecomment VARCHAR2)
 is
   V_COUNT NUMBER;
   --v_schema VARCHAR2(100) := 'dmuser';
 BEGIN
     -- Routine body goes here...
 SELECT COUNT(1)
          INTO V_COUNT
          FROM user_tables
         WHERE UPPER(table_name) = UPPER(p_tablename);

        IF V_COUNT < 1 THEN

          EXECUTE IMMEDIATE  'CREATE TABLE ' || P_TABLENAME|| ' (
                id                 NUMBER(11) not null,
                task_code          VARCHAR2(20) not null,
                draw_time          TIMESTAMP(6) not null,
                task_status        VARCHAR2(1) not null,
                quantity           NUMBER(10)
          )';
          EXECUTE IMMEDIATE 'comment on column '||P_TABLENAME||'.ID is ''主键Id''';
          EXECUTE IMMEDIATE 'comment on column '||P_TABLENAME||'.task_code is ''task_code||任务编号''';
          EXECUTE IMMEDIATE 'comment on column '||P_TABLENAME||'.draw_time is ''draw_time||提数时间''';
          EXECUTE IMMEDIATE 'comment on column '||P_TABLENAME||'.task_status is ''task_status||任务状态''';
          EXECUTE IMMEDIATE 'comment on column '||P_TABLENAME||'.quantity is ''quantity|数量''';
          EXECUTE IMMEDIATE  'comment on table ' || P_TABLENAME || ' is ''' ||
                              P_TABLECOMMENT || '''';
        END IF;
END proc_add_stat_table;


PROCEDURE proc_ADD_TABLE_COLUMN(p_table IN VARCHAR2, p_column IN VARCHAR2, p_columntype IN VARCHAR2,p_comment IN VARCHAR2,p_schema IN VARCHAR2) IS
  v_count NUMBER(15);
BEGIN
  SELECT count(*)
    INTO v_count
    FROM all_tab_columns
   WHERE upper(table_name) = upper(p_table)
     AND upper(column_name) = upper(p_column)
     and upper(OWNER) = upper(p_schema);
  IF v_count = 0 THEN
     EXECUTE IMMEDIATE 'alter table '||p_schema||'.' || p_table || ' add ' || p_column ||' ' || p_columntype;
     EXECUTE IMMEDIATE 'comment on column ' ||p_schema||'.' || p_table || '.' || p_column ||' is ''' || p_comment || '''';
    null;
  END IF;
END proc_ADD_TABLE_COLUMN;

PROCEDURE proc_DROP_TABLE(p_table IN VARCHAR2,p_schema IN VARCHAR2) IS
  v_count NUMBER(15);
BEGIN
  SELECT count(*)
    INTO v_count
    FROM all_tables
   WHERE table_name = upper(p_table)
   and upper(OWNER) = upper(p_schema);
  IF v_count > 0 THEN
    EXECUTE IMMEDIATE 'drop table ' ||p_schema||'.'|| upper(p_table) || ' purge ';
  END IF;
END proc_DROP_TABLE;
  
PROCEDURE proc_DROP_TABLE_COLUMN(p_table IN VARCHAR2, p_column IN VARCHAR2,p_schema IN VARCHAR2) IS
  v_count NUMBER(15);
BEGIN
  SELECT count(*)
    INTO v_count
    FROM all_tab_columns
   WHERE upper(table_name) = upper(p_table)
     AND upper(column_name) = upper(p_column)
     and upper(OWNER) = upper(p_schema);
  IF v_count > 0 THEN
    EXECUTE IMMEDIATE 'alter table '||p_schema||'.'  || p_table || ' drop column ' || p_column;
  END IF;
END proc_DROP_TABLE_COLUMN;

PROCEDURE proc_DROP_SEQUENCE(p_sequence_name IN VARCHAR2,p_schema IN VARCHAR2) IS
  v_count NUMBER(15):=0;
BEGIN
  SELECT count(*)
    INTO v_count
    from all_sequences
    WHERE sequence_name = upper(p_sequence_name)
    and upper(SEQUENCE_OWNER) = upper(p_schema);
  IF v_count > 0 THEN
    EXECUTE IMMEDIATE ' drop SEQUENCE ' ||p_schema||'.'||  p_sequence_name ;
  END IF;
END proc_DROP_SEQUENCE;
  
PROCEDURE proc_modify_table_comment(p_tablename VARCHAR2, p_tablecomment VARCHAR2, p_schema VARCHAR2)
 is

 V_COUNT NUMBER;
 BEGIN
  -- Routine body goes here...
SELECT COUNT(1)
          INTO V_COUNT
          FROM all_tables
         WHERE UPPER(table_name) = UPPER(p_tablename)
         and upper(OWNER) = upper(p_schema);

        IF V_COUNT > 0 THEN
            EXECUTE  IMMEDIATE  'comment on table '||p_schema||'.'|| P_TABLENAME || ' is ''' ||
                              P_TABLECOMMENT || '''';
        END IF;
END proc_modify_table_comment;



--变更表的字段注释
PROCEDURE MODIFY_TABLE_COLUMNCOMMENT(p_table VARCHAR2, p_column VARCHAR2, p_columnComment VARCHAR2, p_schema VARCHAR2) is
 v_count NUMBER(1);
BEGIN
  v_count := 0;
  SELECT COUNT(*)
    INTO v_count
    FROM all_tab_columns b
   WHERE b.TABLE_NAME = upper(p_table)
     and b.COLUMN_NAME = upper(p_column)
     and upper(OWNER) = upper(p_schema);
  if v_count > 0 then
    execute immediate 'comment on column '||p_schema||'.'|| p_table || '.' || p_column || ' is '''|| p_columnComment ||'''';
  end if;


end MODIFY_TABLE_COLUMNCOMMENT;

end dm_pack_table_deal;
/
