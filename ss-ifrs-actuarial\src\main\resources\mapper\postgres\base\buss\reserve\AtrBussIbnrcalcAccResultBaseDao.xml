<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-12-12 11:40:09 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussIbnrcalcAccResultDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**BaseDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcAccResult">
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="action_no" property="actionNo" jdbcType="VARCHAR" />
    <result column="icp_id" property="icpId" jdbcType="BIGINT" />
    <result column="accident_node" property="accidentNode" jdbcType="VARCHAR" />
    <result column="ed_premium_ori" property="edPremiumOri" jdbcType="NUMERIC" />
    <result column="ed_premium" property="edPremium" jdbcType="NUMERIC" />
    <result column="setteld_amount" property="setteldAmount" jdbcType="NUMERIC" />
    <result column="os_amount" property="osAmount" jdbcType="NUMERIC" />
    <result column="reported_amount" property="reportedAmount" jdbcType="NUMERIC" />
    <result column="reported_loss_ratio" property="reportedLossRatio" jdbcType="NUMERIC" />
    <result column="expected_reported_ratio" property="expectedReportedRatio" jdbcType="NUMERIC" />
    <result column="lr_expected_loss_ratio" property="lrExpectedLossRatio" jdbcType="NUMERIC" />
    <result column="lr_ultimate_loss" property="lrUltimateLoss" jdbcType="NUMERIC" />
    <result column="lr_ibnr" property="lrIbnr" jdbcType="NUMERIC" />
    <result column="cl_proj_reported_ultimate" property="clProjReportedUltimate" jdbcType="NUMERIC" />
    <result column="cl_ibnr" property="clIbnr" jdbcType="NUMERIC" />
    <result column="bf_expected_loss_ratio" property="bfExpectedLossRatio" jdbcType="NUMERIC" />
    <result column="bf_proj_reported_ultimate" property="bfProjReportedUltimate" jdbcType="NUMERIC" />
    <result column="bf_ibnr" property="bfIbnr" jdbcType="NUMERIC" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    id, action_no, icp_id, accident_node, ed_premium_ori, ed_premium, setteld_amount, 
    os_amount, reported_amount, reported_loss_ratio, expected_reported_ratio, lr_expected_loss_ratio, 
    lr_ultimate_loss, lr_ibnr, cl_proj_reported_ultimate, cl_ibnr, bf_expected_loss_ratio, 
    bf_proj_reported_ultimate, bf_ibnr
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="id != null ">
          and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="actionNo != null and actionNo != ''">
          and action_no = #{actionNo,jdbcType=VARCHAR}
      </if>
      <if test="icpId != null ">
          and icp_id = #{icpId,jdbcType=BIGINT}
      </if>
      <if test="accidentNode != null and accidentNode != ''">
          and accident_node = #{accidentNode,jdbcType=VARCHAR}
      </if>
      <if test="edPremiumOri != null ">
          and ed_premium_ori = #{edPremiumOri,jdbcType=NUMERIC}
      </if>
      <if test="edPremium != null ">
          and ed_premium = #{edPremium,jdbcType=NUMERIC}
      </if>
      <if test="setteldAmount != null ">
          and setteld_amount = #{setteldAmount,jdbcType=NUMERIC}
      </if>
      <if test="osAmount != null ">
          and os_amount = #{osAmount,jdbcType=NUMERIC}
      </if>
      <if test="reportedAmount != null ">
          and reported_amount = #{reportedAmount,jdbcType=NUMERIC}
      </if>
      <if test="reportedLossRatio != null ">
          and reported_loss_ratio = #{reportedLossRatio,jdbcType=NUMERIC}
      </if>
      <if test="expectedReportedRatio != null ">
          and expected_reported_ratio = #{expectedReportedRatio,jdbcType=NUMERIC}
      </if>
      <if test="lrExpectedLossRatio != null ">
          and lr_expected_loss_ratio = #{lrExpectedLossRatio,jdbcType=NUMERIC}
      </if>
      <if test="lrUltimateLoss != null ">
          and lr_ultimate_loss = #{lrUltimateLoss,jdbcType=NUMERIC}
      </if>
      <if test="lrIbnr != null ">
          and lr_ibnr = #{lrIbnr,jdbcType=NUMERIC}
      </if>
      <if test="clProjReportedUltimate != null ">
          and cl_proj_reported_ultimate = #{clProjReportedUltimate,jdbcType=NUMERIC}
      </if>
      <if test="clIbnr != null ">
          and cl_ibnr = #{clIbnr,jdbcType=NUMERIC}
      </if>
      <if test="bfExpectedLossRatio != null ">
          and bf_expected_loss_ratio = #{bfExpectedLossRatio,jdbcType=NUMERIC}
      </if>
      <if test="bfProjReportedUltimate != null ">
          and bf_proj_reported_ultimate = #{bfProjReportedUltimate,jdbcType=NUMERIC}
      </if>
      <if test="bfIbnr != null ">
          and bf_ibnr = #{bfIbnr,jdbcType=NUMERIC}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.id != null ">
          and id = #{condition.id,jdbcType=BIGINT}
      </if>
      <if test="condition.actionNo != null and condition.actionNo != ''">
          and action_no = #{condition.actionNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.icpId != null ">
          and icp_id = #{condition.icpId,jdbcType=BIGINT}
      </if>
      <if test="condition.accidentNode != null and condition.accidentNode != ''">
          and accident_node = #{condition.accidentNode,jdbcType=VARCHAR}
      </if>
      <if test="condition.edPremiumOri != null ">
          and ed_premium_ori = #{condition.edPremiumOri,jdbcType=NUMERIC}
      </if>
      <if test="condition.edPremium != null ">
          and ed_premium = #{condition.edPremium,jdbcType=NUMERIC}
      </if>
      <if test="condition.setteldAmount != null ">
          and setteld_amount = #{condition.setteldAmount,jdbcType=NUMERIC}
      </if>
      <if test="condition.osAmount != null ">
          and os_amount = #{condition.osAmount,jdbcType=NUMERIC}
      </if>
      <if test="condition.reportedAmount != null ">
          and reported_amount = #{condition.reportedAmount,jdbcType=NUMERIC}
      </if>
      <if test="condition.reportedLossRatio != null ">
          and reported_loss_ratio = #{condition.reportedLossRatio,jdbcType=NUMERIC}
      </if>
      <if test="condition.expectedReportedRatio != null ">
          and expected_reported_ratio = #{condition.expectedReportedRatio,jdbcType=NUMERIC}
      </if>
      <if test="condition.lrExpectedLossRatio != null ">
          and lr_expected_loss_ratio = #{condition.lrExpectedLossRatio,jdbcType=NUMERIC}
      </if>
      <if test="condition.lrUltimateLoss != null ">
          and lr_ultimate_loss = #{condition.lrUltimateLoss,jdbcType=NUMERIC}
      </if>
      <if test="condition.lrIbnr != null ">
          and lr_ibnr = #{condition.lrIbnr,jdbcType=NUMERIC}
      </if>
      <if test="condition.clProjReportedUltimate != null ">
          and cl_proj_reported_ultimate = #{condition.clProjReportedUltimate,jdbcType=NUMERIC}
      </if>
      <if test="condition.clIbnr != null ">
          and cl_ibnr = #{condition.clIbnr,jdbcType=NUMERIC}
      </if>
      <if test="condition.bfExpectedLossRatio != null ">
          and bf_expected_loss_ratio = #{condition.bfExpectedLossRatio,jdbcType=NUMERIC}
      </if>
      <if test="condition.bfProjReportedUltimate != null ">
          and bf_proj_reported_ultimate = #{condition.bfProjReportedUltimate,jdbcType=NUMERIC}
      </if>
      <if test="condition.bfIbnr != null ">
          and bf_ibnr = #{condition.bfIbnr,jdbcType=NUMERIC}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="id != null ">
          and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="actionNo != null and actionNo != ''">
          and action_no = #{actionNo,jdbcType=VARCHAR}
      </if>
      <if test="icpId != null ">
          and icp_id = #{icpId,jdbcType=BIGINT}
      </if>
      <if test="accidentNode != null and accidentNode != ''">
          and accident_node = #{accidentNode,jdbcType=VARCHAR}
      </if>
      <if test="edPremiumOri != null ">
          and ed_premium_ori = #{edPremiumOri,jdbcType=NUMERIC}
      </if>
      <if test="edPremium != null ">
          and ed_premium = #{edPremium,jdbcType=NUMERIC}
      </if>
      <if test="setteldAmount != null ">
          and setteld_amount = #{setteldAmount,jdbcType=NUMERIC}
      </if>
      <if test="osAmount != null ">
          and os_amount = #{osAmount,jdbcType=NUMERIC}
      </if>
      <if test="reportedAmount != null ">
          and reported_amount = #{reportedAmount,jdbcType=NUMERIC}
      </if>
      <if test="reportedLossRatio != null ">
          and reported_loss_ratio = #{reportedLossRatio,jdbcType=NUMERIC}
      </if>
      <if test="expectedReportedRatio != null ">
          and expected_reported_ratio = #{expectedReportedRatio,jdbcType=NUMERIC}
      </if>
      <if test="lrExpectedLossRatio != null ">
          and lr_expected_loss_ratio = #{lrExpectedLossRatio,jdbcType=NUMERIC}
      </if>
      <if test="lrUltimateLoss != null ">
          and lr_ultimate_loss = #{lrUltimateLoss,jdbcType=NUMERIC}
      </if>
      <if test="lrIbnr != null ">
          and lr_ibnr = #{lrIbnr,jdbcType=NUMERIC}
      </if>
      <if test="clProjReportedUltimate != null ">
          and cl_proj_reported_ultimate = #{clProjReportedUltimate,jdbcType=NUMERIC}
      </if>
      <if test="clIbnr != null ">
          and cl_ibnr = #{clIbnr,jdbcType=NUMERIC}
      </if>
      <if test="bfExpectedLossRatio != null ">
          and bf_expected_loss_ratio = #{bfExpectedLossRatio,jdbcType=NUMERIC}
      </if>
      <if test="bfProjReportedUltimate != null ">
          and bf_proj_reported_ultimate = #{bfProjReportedUltimate,jdbcType=NUMERIC}
      </if>
      <if test="bfIbnr != null ">
          and bf_ibnr = #{bfIbnr,jdbcType=NUMERIC}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_ibnrcalc_acc_result"
    where id = #{id,jdbcType=BIGINT}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_ibnrcalc_acc_result"
    where id in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_ibnrcalc_acc_result"
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcAccResult">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_ibnrcalc_acc_result"
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_ibnrcalc_acc_result"
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="id" keyProperty="id" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcAccResult">
    insert into "atr_buss_ibnrcalc_acc_result"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="actionNo != null">
        action_no,
      </if>
      <if test="icpId != null">
        icp_id,
      </if>
      <if test="accidentNode != null">
        accident_node,
      </if>
      <if test="edPremiumOri != null">
        ed_premium_ori,
      </if>
      <if test="edPremium != null">
        ed_premium,
      </if>
      <if test="setteldAmount != null">
        setteld_amount,
      </if>
      <if test="osAmount != null">
        os_amount,
      </if>
      <if test="reportedAmount != null">
        reported_amount,
      </if>
      <if test="reportedLossRatio != null">
        reported_loss_ratio,
      </if>
      <if test="expectedReportedRatio != null">
        expected_reported_ratio,
      </if>
      <if test="lrExpectedLossRatio != null">
        lr_expected_loss_ratio,
      </if>
      <if test="lrUltimateLoss != null">
        lr_ultimate_loss,
      </if>
      <if test="lrIbnr != null">
        lr_ibnr,
      </if>
      <if test="clProjReportedUltimate != null">
        cl_proj_reported_ultimate,
      </if>
      <if test="clIbnr != null">
        cl_ibnr,
      </if>
      <if test="bfExpectedLossRatio != null">
        bf_expected_loss_ratio,
      </if>
      <if test="bfProjReportedUltimate != null">
        bf_proj_reported_ultimate,
      </if>
      <if test="bfIbnr != null">
        bf_ibnr,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="actionNo != null">
        #{actionNo,jdbcType=VARCHAR},
      </if>
      <if test="icpId != null">
        #{icpId,jdbcType=BIGINT},
      </if>
      <if test="accidentNode != null">
        #{accidentNode,jdbcType=VARCHAR},
      </if>
      <if test="edPremiumOri != null">
        #{edPremiumOri,jdbcType=NUMERIC},
      </if>
      <if test="edPremium != null">
        #{edPremium,jdbcType=NUMERIC},
      </if>
      <if test="setteldAmount != null">
        #{setteldAmount,jdbcType=NUMERIC},
      </if>
      <if test="osAmount != null">
        #{osAmount,jdbcType=NUMERIC},
      </if>
      <if test="reportedAmount != null">
        #{reportedAmount,jdbcType=NUMERIC},
      </if>
      <if test="reportedLossRatio != null">
        #{reportedLossRatio,jdbcType=NUMERIC},
      </if>
      <if test="expectedReportedRatio != null">
        #{expectedReportedRatio,jdbcType=NUMERIC},
      </if>
      <if test="lrExpectedLossRatio != null">
        #{lrExpectedLossRatio,jdbcType=NUMERIC},
      </if>
      <if test="lrUltimateLoss != null">
        #{lrUltimateLoss,jdbcType=NUMERIC},
      </if>
      <if test="lrIbnr != null">
        #{lrIbnr,jdbcType=NUMERIC},
      </if>
      <if test="clProjReportedUltimate != null">
        #{clProjReportedUltimate,jdbcType=NUMERIC},
      </if>
      <if test="clIbnr != null">
        #{clIbnr,jdbcType=NUMERIC},
      </if>
      <if test="bfExpectedLossRatio != null">
        #{bfExpectedLossRatio,jdbcType=NUMERIC},
      </if>
      <if test="bfProjReportedUltimate != null">
        #{bfProjReportedUltimate,jdbcType=NUMERIC},
      </if>
      <if test="bfIbnr != null">
        #{bfIbnr,jdbcType=NUMERIC},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert into "atr_buss_ibnrcalc_acc_result"
     (id, action_no, icp_id, 
      accident_node, ed_premium_ori, ed_premium, 
      setteld_amount, os_amount, reported_amount, 
      reported_loss_ratio, expected_reported_ratio, 
      lr_expected_loss_ratio, lr_ultimate_loss, 
      lr_ibnr, cl_proj_reported_ultimate, 
      cl_ibnr, bf_expected_loss_ratio, 
      bf_proj_reported_ultimate, bf_ibnr
      )
     values 
    <foreach collection="list" item="item" index="index" separator=",">
       (#{item.id,jdbcType=BIGINT}, #{item.actionNo,jdbcType=VARCHAR}, #{item.icpId,jdbcType=BIGINT}, 
        #{item.accidentNode,jdbcType=VARCHAR}, #{item.edPremiumOri,jdbcType=NUMERIC}, #{item.edPremium,jdbcType=NUMERIC}, 
        #{item.setteldAmount,jdbcType=NUMERIC}, #{item.osAmount,jdbcType=NUMERIC}, #{item.reportedAmount,jdbcType=NUMERIC}, 
        #{item.reportedLossRatio,jdbcType=NUMERIC}, #{item.expectedReportedRatio,jdbcType=NUMERIC}, 
        #{item.lrExpectedLossRatio,jdbcType=NUMERIC}, #{item.lrUltimateLoss,jdbcType=NUMERIC}, 
        #{item.lrIbnr,jdbcType=NUMERIC}, #{item.clProjReportedUltimate,jdbcType=NUMERIC}, 
        #{item.clIbnr,jdbcType=NUMERIC}, #{item.bfExpectedLossRatio,jdbcType=NUMERIC}, 
        #{item.bfProjReportedUltimate,jdbcType=NUMERIC}, #{item.bfIbnr,jdbcType=NUMERIC}
        )
    </foreach>
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcAccResult">
    update "atr_buss_ibnrcalc_acc_result"
    <set>
      <if test="actionNo != null">
        action_no = #{actionNo,jdbcType=VARCHAR},
      </if>
      <if test="icpId != null">
        icp_id = #{icpId,jdbcType=BIGINT},
      </if>
      <if test="accidentNode != null">
        accident_node = #{accidentNode,jdbcType=VARCHAR},
      </if>
      <if test="edPremiumOri != null">
        ed_premium_ori = #{edPremiumOri,jdbcType=NUMERIC},
      </if>
      <if test="edPremium != null">
        ed_premium = #{edPremium,jdbcType=NUMERIC},
      </if>
      <if test="setteldAmount != null">
        setteld_amount = #{setteldAmount,jdbcType=NUMERIC},
      </if>
      <if test="osAmount != null">
        os_amount = #{osAmount,jdbcType=NUMERIC},
      </if>
      <if test="reportedAmount != null">
        reported_amount = #{reportedAmount,jdbcType=NUMERIC},
      </if>
      <if test="reportedLossRatio != null">
        reported_loss_ratio = #{reportedLossRatio,jdbcType=NUMERIC},
      </if>
      <if test="expectedReportedRatio != null">
        expected_reported_ratio = #{expectedReportedRatio,jdbcType=NUMERIC},
      </if>
      <if test="lrExpectedLossRatio != null">
        lr_expected_loss_ratio = #{lrExpectedLossRatio,jdbcType=NUMERIC},
      </if>
      <if test="lrUltimateLoss != null">
        lr_ultimate_loss = #{lrUltimateLoss,jdbcType=NUMERIC},
      </if>
      <if test="lrIbnr != null">
        lr_ibnr = #{lrIbnr,jdbcType=NUMERIC},
      </if>
      <if test="clProjReportedUltimate != null">
        cl_proj_reported_ultimate = #{clProjReportedUltimate,jdbcType=NUMERIC},
      </if>
      <if test="clIbnr != null">
        cl_ibnr = #{clIbnr,jdbcType=NUMERIC},
      </if>
      <if test="bfExpectedLossRatio != null">
        bf_expected_loss_ratio = #{bfExpectedLossRatio,jdbcType=NUMERIC},
      </if>
      <if test="bfProjReportedUltimate != null">
        bf_proj_reported_ultimate = #{bfProjReportedUltimate,jdbcType=NUMERIC},
      </if>
      <if test="bfIbnr != null">
        bf_ibnr = #{bfIbnr,jdbcType=NUMERIC},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcAccResult">
    update "atr_buss_ibnrcalc_acc_result"
    <set>
      <if test="record.actionNo != null">
        action_no = #{record.actionNo,jdbcType=VARCHAR},
      </if>
      <if test="record.icpId != null">
        icp_id = #{record.icpId,jdbcType=BIGINT},
      </if>
      <if test="record.accidentNode != null">
        accident_node = #{record.accidentNode,jdbcType=VARCHAR},
      </if>
      <if test="record.edPremiumOri != null">
        ed_premium_ori = #{record.edPremiumOri,jdbcType=NUMERIC},
      </if>
      <if test="record.edPremium != null">
        ed_premium = #{record.edPremium,jdbcType=NUMERIC},
      </if>
      <if test="record.setteldAmount != null">
        setteld_amount = #{record.setteldAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.osAmount != null">
        os_amount = #{record.osAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.reportedAmount != null">
        reported_amount = #{record.reportedAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.reportedLossRatio != null">
        reported_loss_ratio = #{record.reportedLossRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.expectedReportedRatio != null">
        expected_reported_ratio = #{record.expectedReportedRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.lrExpectedLossRatio != null">
        lr_expected_loss_ratio = #{record.lrExpectedLossRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.lrUltimateLoss != null">
        lr_ultimate_loss = #{record.lrUltimateLoss,jdbcType=NUMERIC},
      </if>
      <if test="record.lrIbnr != null">
        lr_ibnr = #{record.lrIbnr,jdbcType=NUMERIC},
      </if>
      <if test="record.clProjReportedUltimate != null">
        cl_proj_reported_ultimate = #{record.clProjReportedUltimate,jdbcType=NUMERIC},
      </if>
      <if test="record.clIbnr != null">
        cl_ibnr = #{record.clIbnr,jdbcType=NUMERIC},
      </if>
      <if test="record.bfExpectedLossRatio != null">
        bf_expected_loss_ratio = #{record.bfExpectedLossRatio,jdbcType=NUMERIC},
      </if>
      <if test="record.bfProjReportedUltimate != null">
        bf_proj_reported_ultimate = #{record.bfProjReportedUltimate,jdbcType=NUMERIC},
      </if>
      <if test="record.bfIbnr != null">
        bf_ibnr = #{record.bfIbnr,jdbcType=NUMERIC},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from "atr_buss_ibnrcalc_acc_result"
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from "atr_buss_ibnrcalc_acc_result"
    where id in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from "atr_buss_ibnrcalc_acc_result"
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcAccResult">
    select count(1) from "atr_buss_ibnrcalc_acc_result"
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>