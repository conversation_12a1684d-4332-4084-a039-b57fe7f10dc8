package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInflationFactor;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Setter @Getter
public class AtrConfInflationFactorPageVo extends AtrConfInflationFactor {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "业务年")
    private String bussYear;

    @ApiModelProperty(value = "通货膨胀系数")
    private BigDecimal infFactor;

    @ApiModelProperty(value = "是否确认|BBS/ConfirmState")
    private String confirmIs;

    @ApiModelProperty(value = "确认人")
    private Long confirmId;

    @ApiModelProperty(value = "确认时间")
    private Date confirmTime;

    @ApiModelProperty(value = "是否确认的描述")
    private String confirmIsName;

    @ApiModelProperty(value = "确认人")
    private String confirmUserName;

}
