/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2025-03-24 19:27:36
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2025-03-24 19:27:36<br/>
 * Description: 分摊操作表<br/>
 * Table Name: atr_buss_ibnr_alloc_action<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "分摊操作表")
public class AtrBussIbnrAllocAction implements Serializable {
    /**
     * Database column: atr_buss_ibnr_alloc_action.id
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: atr_buss_ibnr_alloc_action.action_no
     * Database remarks: 执行编号
     */
    @ApiModelProperty(value = "执行编号", required = true)
    private String actionNo;

    /**
     * Database column: atr_buss_ibnr_alloc_action.entity_id
     * Database remarks: 业务单位ID
     */
    @ApiModelProperty(value = "业务单位ID", required = true)
    private Long entityId;

    /**
     * Database column: atr_buss_ibnr_alloc_action.year_month
     * Database remarks: 业务年月|评估期
     */
    @ApiModelProperty(value = "业务年月|评估期", required = true)
    private String yearMonth;

    /**
     * Database column: atr_buss_ibnr_alloc_action.business_source_code
     * Database remarks: 业务类型|DD-直保&临分分入；FO-临分分出；TI-合约分入；TO-合约分出
     */
    @ApiModelProperty(value = "业务类型|DD-直保&临分分入；FO-临分分出；TI-合约分入；TO-合约分出", required = true)
    private String businessSourceCode;

    /**
     * Database column: atr_buss_ibnr_alloc_action.status
     * Database remarks: 执行状态|R-执行中；E-执行异常；S-执行成功
     */
    @ApiModelProperty(value = "执行状态|R-执行中；E-执行异常；S-执行成功", required = true)
    private String status;

    /**
     * Database column: atr_buss_ibnr_alloc_action.confirm_is
     * Database remarks: 是否确认|1-是、0-否
     */
    @ApiModelProperty(value = "是否确认|1-是、0-否", required = true)
    private String confirmIs;

    /**
     * Database column: atr_buss_ibnr_alloc_action.confirm_user
     * Database remarks: 确认人
     */
    @ApiModelProperty(value = "确认人", required = false)
    private Long confirmUser;

    /**
     * Database column: atr_buss_ibnr_alloc_action.confirm_time
     * Database remarks: 确认时间
     */
    @ApiModelProperty(value = "确认时间", required = false)
    private Date confirmTime;

    /**
     * Database column: atr_buss_ibnr_alloc_action.creator_id
     * Database remarks: 创建人
     */
    @ApiModelProperty(value = "创建人", required = false)
    private Long creatorId;

    /**
     * Database column: atr_buss_ibnr_alloc_action.create_time
     * Database remarks: 创建时间
     */
    @ApiModelProperty(value = "创建时间", required = false)
    private Date createTime;

    /**
     * Database column: atr_buss_ibnr_alloc_action.updator_id
     * Database remarks: 最后修改人
     */
    @ApiModelProperty(value = "最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: atr_buss_ibnr_alloc_action.update_time
     * Database remarks: 最后修改时间
     */
    @ApiModelProperty(value = "最后修改时间", required = false)
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getConfirmIs() {
        return confirmIs;
    }

    public void setConfirmIs(String confirmIs) {
        this.confirmIs = confirmIs;
    }

    public Long getConfirmUser() {
        return confirmUser;
    }

    public void setConfirmUser(Long confirmUser) {
        this.confirmUser = confirmUser;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}