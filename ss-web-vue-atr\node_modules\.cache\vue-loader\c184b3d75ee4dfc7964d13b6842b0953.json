{"remainingRequest": "O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\src\\pages\\atr\\expectedCashFlow\\puhua\\lrcCashFlowApp\\components\\lrcViewDetailIndex.vue?vue&type=script&lang=js", "dependencies": [{"path": "O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\src\\pages\\atr\\expectedCashFlow\\puhua\\lrcCashFlowApp\\components\\lrcViewDetailIndex.vue", "mtime": 1753845795780}, {"path": "O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1742174260879}, {"path": "O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1742174263321}, {"path": "O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1742174260879}, {"path": "O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1742174262792}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["lrcViewDetailIndex.vue"], "names": [], "mappings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file": "lrcViewDetailIndex.vue", "sourceRoot": "src/pages/atr/expectedCashFlow/puhua/lrcCashFlowApp/components", "sourcesContent": ["<template>\r\n    <el-dialog :title=\"'atrLrcCashFlowTitle' | translate\" custom-class=\"gv-dialog-form\" :visible.sync=\"dialogFormVisible\"\r\n               width=\"96%\" :close-on-click-modal=\"false\" top=\"5vh\" append-to-body>\r\n        <gv-form :model=\"form\" ref=\"form\" :rules=\"rules\">\r\n            <el-collapse v-model=\"mixinObject.activeNames\">\r\n                <el-collapse-item :title=\"'gTitleBasics' | translate('Basics Data')\" name=\"1\" class=\"table-line\">\r\n                    <div class=\"gv-row\">\r\n                        <gv-form-item key-name=\"gCenterCode\" prop=\"entityCode\" >\r\n                            <gv-auto-complete context-name=\"common\" url=\"/basic_center/find_Branch\" code-name=\"entityCode,entityCName\"\r\n                                              :is-readonly=\"isReadonlySave\"\r\n                                              label-name=\"entityCode,entityCName\"\r\n                                              v-model=\"form.entityCode\" ></gv-auto-complete>\r\n                        </gv-form-item>\r\n                        <gv-form-item key-name=\"dmBusinessType\" prop=\"businessSourceCode\">\r\n                            <gv-select :disabled=\"isReadonlySave\" size=\"mini\" options-set=\"0\" code-type=\"BusinessModel/Base\" v-model=\"form.businessSourceCode\"></gv-select>\r\n                        </gv-form-item>\r\n                        <gv-form-item key-name=\"atrEvaluationYearMonth\" prop=\"yearMonth\">\r\n                            <el-input maxlength=\"6\" placeholder=\"yyyymm\" v-model=\"form.yearMonth\" :disabled=\"isReadonlySave\">\r\n                            </el-input>\r\n                        </gv-form-item>\r\n                    </div>\r\n                    <div class=\"gv-row\">\r\n                        <gv-form-item key-name=\"gDmPortfolioNo\" prop=\"portfolioNo\" v-if=\"type !=='add'\">\r\n                            <el-input v-model=\"form.portfolioNo\" :disabled=\"isReadonlySave\"></el-input>\r\n                        </gv-form-item>\r\n                        <gv-form-item key-name=\"gDmContractGroupNo\" prop=\"icgNo\" v-if=\"type !=='add'\">\r\n                            <el-input v-model=\"form.icgNo\" :disabled=\"isReadonlySave\"></el-input>\r\n                        </gv-form-item>\r\n                      <gv-form-item key-name=\"atrInsuranceClass\"  v-if=\"showRiskClassCode\" prop=\"riskClassCode\">\r\n                        <el-input v-model=\"form.riskClassCode\" :disabled=\"isReadonlySave\"></el-input>\r\n                      </gv-form-item>\r\n                    </div>\r\n                </el-collapse-item>\r\n\r\n                <!--aioi-->\r\n                <el-collapse-item :title=\"'atrAcquisitionInfo' | translate\" name=\"2\" v-if=\"form.businessSourceCode !== 'TO' && form.businessSourceCode !== 'FO'\">\r\n                    <div class=\"gv-atr-collapse-content\">\r\n                        <div id=\"tabs-atr\">\r\n                            <table class=\"custom-table\">\r\n                                <thead>\r\n                                  <!-- 第一行表头 -->\r\n                                  <tr>\r\n                                    <template v-for=\"(field, index) in quotaTable.firstRowFields\">\r\n                                      <th v-if=\"field.rowspan\" \r\n                                          :key=\"'first-'+index\"\r\n                                          :rowspan=\"field.rowspan\" \r\n                                          :class=\"field.className\"\r\n                                          :style=\"{ backgroundColor: field.headColor, textAlign: 'center' }\">\r\n                                        {{ field.labelKey | translate }}\r\n                                      </th>\r\n                                      <th v-else \r\n                                          :key=\"('first-'+index)\"\r\n                                          :colspan=\"field.colspan\" \r\n                                          :class=\"field.className\"\r\n                                          :style=\"{ backgroundColor: field.headColor, textAlign: 'center' }\">\r\n                                        {{ field.labelKey | translate }}\r\n                                      </th>\r\n                                    </template>\r\n                                  </tr>\r\n                                  <!-- 第二行表头 -->\r\n                                  <tr>\r\n                                    <template v-for=\"(field, index) in quotaTable.secondRowFields\">\r\n                                      <th v-if=\"field.prop !== 'insuranceType'\"\r\n                                          :key=\"'second-'+index\" \r\n                                          :class=\"field.className\"\r\n                                          :style=\"{ backgroundColor: field.headColor, textAlign: 'center' }\">\r\n                                        {{ field.labelKey | translate }}\r\n                                      </th>\r\n                                    </template>\r\n                                  </tr>\r\n                                </thead>\r\n                                <tbody>\r\n                                  <tr v-for=\"(row, rowIndex) in bussQuotaVoList\" :key=\"rowIndex\">\r\n                                    <!-- 险种列 -->\r\n                                    <td style=\"text-align: center;\">{{ row.insuranceType }}</td>\r\n                                    <!-- 数据单元格 -->\r\n                                    <template v-for=\"(field, fieldIndex) in quotaTable.secondRowFields\">\r\n                                      <td v-if=\"field.prop !== 'insuranceType'\"\r\n                                          :key=\"'cell-'+fieldIndex\" \r\n                                          :class=\"field.className\" \r\n                                          style=\"text-align: right;\">\r\n                                        <!-- 如果是发展期指标(quotaType=1)，只显示图标不显示值 -->\r\n                                        <template v-if=\"field.quotaType === '1'\">\r\n                                          <i class=\"el-icon-my-line-graph\"\r\n                                             style=\"margin-left: 5px; cursor: pointer;\"\r\n                                             @click=\"onListBtn(row, 'develop', field.prop)\"></i>\r\n                                        </template>\r\n                                        <!-- 否则根据字段类型格式化数据 -->\r\n                                        <template v-else>\r\n                                          <span v-if=\"field.fieldType === '2'\">\r\n                                            {{ row[field.prop] | amount(true, 2) }}\r\n                                          </span>\r\n                                          <span v-else-if=\"field.fieldType === '4'\">\r\n                                            {{ row[field.prop] | amountZero(false, 2) }}%\r\n                                          </span>\r\n                                          <span v-else>\r\n                                            {{ row[field.prop] }}\r\n                                          </span>\r\n                                        </template>\r\n                                      </td>\r\n                                    </template>\r\n                                  </tr>\r\n                                </tbody>\r\n                            </table>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"gv-atr-collapse-content\" v-if=\"periodShow\">\r\n                        <div>\r\n                            <span class=\"rectangularr\"></span>\r\n                            <span class=\"gv-panel-titleBar\">{{quotaDefName}}</span>\r\n                            <div  class=\"pull-right\">\r\n                                <el-button class=\"mr15\" style=\"margin-right: 8px\" type=\"text\" @click=\"onFold\"><i class=\"el-dialog__close el-icon el-icon-close\"></i></el-button>\r\n                            </div>\r\n                        </div>\r\n                        <div>\r\n                            <el-table :data=\"bussQuotaDevelopVoList\" border :header-cell-style=\"{ backgroundColor: '#f5f7fa', color: '#606266' }\">\r\n                                <el-table-column prop=\"quotaPeriod\" :label=\"'atrDevelopMonth' | translate\" width=\"180\"></el-table-column>\r\n                                <el-table-column\r\n                                    v-for=\"(col, index) in developmentColumns\"\r\n                                    :key=\"index\"\r\n                                    :prop=\"col.prop\"\r\n                                    :label=\"col.label\"\r\n                                    align=\"right\">\r\n                                    <template v-slot=\"scope\">\r\n                                        {{ scope.row[col.prop] | amountZero(false, 2) }} %\r\n                                    </template>\r\n                                </el-table-column>\r\n                            </el-table>\r\n                        </div>\r\n                    </div>\r\n                </el-collapse-item>\r\n                <el-collapse-item :title=\"'atrAioiExpectedPremiumCF' | translate\" name=\"3\">\r\n                    <el-tabs v-model=\"lrcLicTab\"  v-if=\"loding\" @tab-click=\"handleClick\">\r\n                        <el-tab-pane v-for=\"obj in this.premTypeArray\" :key=\"obj.remark\" :label=\"obj.outCName\"\r\n                                     :name=\"obj.remark\" >\r\n                            <template  >\r\n                                <gv-data-table-list v-if=\"obj.remark===lrcLicTab && tabMap[obj.remark]\" :ref=\"obj.remark+'Table'\" @upListData=\"handleSetList\" :table=\"tabMap[obj.remark].table\" :list=\"tabMap[obj.remark].list\" :id=\"obj.remark\"   :listTableTop=\"true\" :paging=\"true\"\r\n                                                    :currentPage=\"mixinObject.searchSet.currentPage\" :MaxHeight='\"400px\"' :total=\"mixinObject.searchSet.total\" >\r\n                                </gv-data-table-list>\r\n                            </template>\r\n                       </el-tab-pane>\r\n                    </el-tabs>\r\n                </el-collapse-item>\r\n            </el-collapse>\r\n            <el-row class=\"toolbar-btn txt-center\">\r\n                <el-button class=\"gv-btn gv-btn-primary\" :disabled=\"isReadonlySave\" v-if=\"type!='view'\" type=\"primary\" @click=\"onSubmit()\">{{\r\n                        'atrBtnDraw' | translate }}\r\n                </el-button>\r\n                <el-button class=\"gv-btn gv-btn-white\" @click=\"onClose()\">{{ 'gBtnClose' | translate}}</el-button>\r\n            </el-row>\r\n        </gv-form>\r\n    </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport feildCard from './card.js'\r\nexport default {\r\n    name: 'lrcAppEditIndex',\r\n    props: {\r\n        type:'',\r\n        licAction:{},\r\n        title:'',\r\n        licShowDataForLossCurrent:'',\r\n        id: '',\r\n        riskClass:'',\r\n        yearMonth:'',\r\n        dataType: '',\r\n        drawTime: '',\r\n        statisZones: '',\r\n        centerId: '',\r\n        currency: '',\r\n        portfolioNo: '',\r\n        mainId: '',\r\n    },\r\n    data: function () {\r\n        return {\r\n            form: {\r\n                centerId: '',\r\n                entityCode: '',\r\n                riskClass: null,\r\n                currency: '',\r\n                ibnrType: '',\r\n                businessSourceCode: '',\r\n                statisZones: '',\r\n                drawTime: '',\r\n                dataType: '',\r\n                riskCName: '',\r\n                loaCode: '',\r\n                loaName: '',\r\n                portfolioNo: null,\r\n                yearMonth: '',\r\n                currencyName: '',\r\n                taskCode: '',\r\n                riskClassCode: '',\r\n                recvDetailVoList:[],\r\n                gepDetailVoList:[],\r\n                covDetailVoList:[],\r\n                uepDetailVoList:[],\r\n                adjDetailVoList:[],\r\n                mainDetailVoList:[],\r\n                iacfDetailVoList:[],\r\n                nonMaimDetailVoList:[],\r\n                csmDetailVoList:[],\r\n            },\r\n            loding: true,\r\n            isReadonly: false,\r\n            isDisabled: false,// 禁用下拉选项\r\n\r\n          tableField: {\r\n            DD: {\r\n              edPremiumDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt', 'cmunitNo',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate', 'comfirmDate','approvalDate',\r\n                'currencyCode', 'grossPremium','preAccumEdPremium' ],\r\n              edNetFeeDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode' ,'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt', 'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                  'currencyCode','feeRate','netFee', 'preCumlEdNetFee'],\r\n              edIacfDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','iacf','preCumlEdIacf'\r\n                 ],\r\n              edIaehcInDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt',  'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','iaehcIn','preCumlEdIaehcIn'],\r\n              edIaehcOutDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','iaehcOut','preCumlEdIaehcOut'],\r\n              premiumDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt',  'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','premium','preCumlPaidPremium'],\r\n              netFeeDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt',  'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode', 'netFee','preCumlPaidNetFee'],\r\n              badDebtDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt',  'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','badDebt'],\r\n              iacfDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','iacf','preCumlEdIacf'],\r\n              iaehcInDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','iaehcIn'],\r\n              iaehcOutDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt',  'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','iaehcOut'],\r\n              lapseDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode',\r\n                 'lapseRate'],\r\n              mtFeeDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode',\r\n                'mtRate'],\r\n              claimDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode',\r\n                'claimRate'],\r\n              ulaeDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode',\r\n                'ulaeRate'],\r\n              raDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode',\r\n                'raRatio'],\r\n\r\n            },\r\n            TI: {\r\n              edPremiumDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate',\r\n                'grossPremium','preCumlPaidPremium' ],\r\n              edNetFeeDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','feeRate','netFee', 'preCumlEdNetFee'],\r\n              edIacfDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','iacf','preCumlEdIacf'\r\n              ],\r\n              edIaehcInDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','iaehcIn','preCumlEdIaehcIn'],\r\n              edIaehcOutDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','iaehcOut','preCumlEdIaehcOut'],\r\n              premiumDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','premium','preCumlPaidPremium'],\r\n              netFeeDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate', 'netFee','preCumlPaidNetFee'],\r\n              iacfDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','iacf','preCumlEdIacf'],\r\n              iaehcInDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','iaehcIn'],\r\n              iaehcOutDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','iaehcOut'],\r\n              lapseDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode','currencyCode',\r\n                'lapseRate'],\r\n              mtFeeDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode','currencyCode',\r\n                'mtRate'],\r\n              claimDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode','currencyCode',\r\n                'claimRate'],\r\n              ulaeDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode','currencyCode',\r\n                'ulaeRate'],\r\n              raDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode','currencyCode',\r\n                'raRatio'],\r\n            },\r\n            TO: {\r\n              edPremiumDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','policyNo', 'endorseSeqNo','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                'grossPremium','preEdPremium' ],\r\n              premiumCfDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','policyNo', 'endorseSeqNo','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo',\r\n                'evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                'grossPremium', 'preAccumEdPremium' ],\r\n              netFeeCfDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','policyNo', 'endorseSeqNo','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                 'netFee','preAccumNetFee'],\r\n              edNetFeeDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','policyNo', 'endorseSeqNo','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',\r\n                'effectiveDate','expiryDate', 'feeRate','netFee', 'preEdNetFee'],\r\n              invAmountDetailTableFields:[\r\n                  \r\n              ]\r\n            },\r\n            TX: {\r\n              edPremiumDetailTableFields:[\r\n                'actionNO','entityId','treatyNo','policyNo', 'endorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                'grossPremium','preEdPremium' ],\r\n              premiumCfDetailTableFields:[\r\n                'actionNO','entityId','treatyNo','policyNo', 'endorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo',\r\n                'evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                'grossPremium' ]\r\n            },\r\n            FO: {\r\n              edPremiumDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                'grossPremium','preEdPremium' ],\r\n              recvPremiumDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo',\r\n                'evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                'grossPremium', 'preAccumEdPremium' ],\r\n              netFeeDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                'netFee','preAccumNetFee'],\r\n              edNetFeeDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',\r\n                'effectiveDate','expiryDate','netFee', 'preEdNetFee']\r\n            },\r\n          },\r\n            isReadonlySave: false,\r\n            rules: {},\r\n            dialogFormVisible: true,\r\n            pickerOptions: {\r\n                disabledDate: function (time) {\r\n                    return time.getTime() < Date.now() - 8.64e7;\r\n                }\r\n            },\r\n            activeName: 'first',\r\n\r\n            /****************aioi假设值*****************/\r\n            //版本指标假设业务值\r\n            quotaTable: {\r\n                basic: {\r\n                    headerstyle:true,\r\n                    cellstyle:{},\r\n                    custom: true, // 添加自定义渲染支持\r\n                },\r\n                fields: [],\r\n                firstRowFields: [], // 第一行表头字段\r\n                secondRowFields: [] // 第二行表头字段\r\n            },\r\n            //版本指标发展期业务值\r\n            devPeriodTable: {\r\n                basic: {\r\n                    fieldInit: false,\r\n                    cellstyle: true,\r\n                    headerstyle: true\r\n                },\r\n                fields: [{\r\n                    prop: \"quotaPeriod\", //属性\r\n                    labelKey: 'atrDevelopMonth',\r\n                    sortable:false,\r\n                    showOverflowTooltip: true,\r\n                    width:\"150px\",\r\n                }]\r\n            },\r\n            developmentColumns: [], // 存储发展期表格列\r\n            quotaDtl:{\r\n                quota:''\r\n            },\r\n            bussQuotaVoList:[],\r\n            bussQuotaDevelopVoList:[],\r\n            quotaObj:'',\r\n            loading: false,\r\n            periodShow: false,\r\n            quotaDefName:'',\r\n            palettes:['#DAD4F0','#ECE9F7'],\r\n            rowPalettes:['purple-to-gray','light-gray'],\r\n            lrcLicTab: '',\r\n            premTypeArray:[],\r\n            tabMap: {\r\n            },\r\n            showRiskClassCode: false,\r\n        }\r\n    },\r\n    watch: {\r\n        dialogFormVisible: function (n, o) {\r\n            !n && this.$emit('close');\r\n        },\r\n        'licAction.businessSourceCode': function(newVal) {\r\n            this.showRiskClassCode = newVal === 'DD' || newVal === 'TI';\r\n        },\r\n        'form.riskClassCode': function(newVal, oldVal) {\r\n            // 当险类代码变化且有值时，重新获取假设数据\r\n            if (newVal && newVal !== oldVal) {\r\n                this.findBussQuotaDataById();\r\n            }\r\n        }\r\n    },\r\n\r\n    methods: {\r\n        // 确认按钮（表单提交）\r\n        onSubmit: function() {\r\n            var _this = this,\r\n                url;\r\n            this.$refs.form.validate(function(valid) {\r\n                if (valid) {\r\n                    Vue.gvUtil.confirm({\r\n                        msg: Vue.gvUtil.getInzTranslate('gSaveSubmit')\r\n                    }).then(function() {\r\n                        // 新增\r\n                        url = Vue.gvUtil.getUrl({\r\n                            apiName: 'lrcCashFlowAdd',\r\n                            contextName: 'actuarial'\r\n                        });\r\n                        Vue.gvUtil.http.post(url, _this.form).then(function (res) {\r\n                            if (_this.isDialog) {\r\n                                // _this.dialogSuccessSubmit();\r\n                            } else {\r\n                                _this.successSubmit(res)\r\n                            }\r\n                            if (res.resCode === '0017') {\r\n                                msg: Vue.gvUtil.getInzTranslate('gSaveError');\r\n                            }\r\n\r\n                        });\r\n                    }).catch(function(){});\r\n                } else {\r\n                    Vue.gvUtil.message(Vue.gvUtil.getInzTranslate('gValidateContent'));\r\n                    return false;\r\n                }\r\n            });\r\n        },\r\n        // 清除表单\r\n        resetForm: function (formName) {\r\n            var lrcCfMainId = this.form.lrcCfMainId;\r\n            var entityCode = this.form.entityCode;\r\n            this.$refs[formName].resetFields();\r\n            this.form.lrcCfMainId = '';\r\n            if (this.type === 'edit') {\r\n                this.form.lrcCfMainId = lrcCfMainId;\r\n                this.form.entityCode = entityCode;\r\n            }\r\n        },\r\n        // 关闭\r\n        onClose: function () {\r\n            this.dialogFormVisible = false;\r\n            this.$emit('close');\r\n        },\r\n        onEditorChange: function (val) {\r\n            this.form.modelContent = val.text;\r\n        },\r\n\r\n        // 初始化页面，低层直接调用\r\n        initPage: function () {\r\n            if (this.type !== 'add') {\r\n                this.requestData();\r\n            }\r\n            if (this.type === 'view') {\r\n                this.isReadonly = true;\r\n                this.isDisabled = true;\r\n                this.isReadonlySave = true;\r\n            }\r\n            if (this.type === 'add') {\r\n                var user = sessionStorage.getItem('user');\r\n                if (user) {\r\n                    user = JSON.parse(user);\r\n                    this.form.centerId = user.userCenterId;\r\n\r\n                }\r\n                this.selectCenterId();\r\n            }\r\n        },\r\n        //配置初始化核算单位，并查询数据\r\n        selectCenterId: function () {\r\n            this.form.entityCode = Vue.gvUtil.setentityCode()\r\n        },\r\n        // 初始化校验，低层直接调用\r\n        initRules: function () {\r\n            this.rules = {\r\n                entityCode: [{\r\n                    trigger: 'blur',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }, {\r\n                    trigger: 'change',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }],\r\n                businessType: [{\r\n                    trigger: 'blur',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }, {\r\n                    trigger: 'change',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }],\r\n                yearMonth: [{\r\n                    trigger: 'blur',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }, {\r\n                    trigger: 'change',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }],\r\n                currency: [{\r\n                    trigger: 'blur',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }, {\r\n                    trigger: 'change',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }],\r\n                drawTime: [{\r\n                    trigger: 'blur',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }, {\r\n                    trigger: 'change',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }],\r\n            };\r\n        },\r\n        requestData: function () {\r\n            var _this = this,\r\n                url = Vue.gvUtil.getUrl({\r\n                    apiName: 'bussLrcCashFlowFindByPk',\r\n                    urlParams: {\r\n                        id: _this.licAction.id\r\n                    },\r\n                    contextName: 'actuarial'\r\n                });\r\n            Vue.gvUtil.http.get(url).then(function (res) {\r\n                if (res.resCode === '0000') {\r\n                    // $.extend(true, _this.form, res.resData);\r\n                    //表单基础数据查询和格式化\r\n                    res.resData.portfolioNo = _this.licAction.portfolioNo;\r\n                    res.resData.icgNo = _this.licAction.icgNo;\r\n                    res.resData.riskClassCode = _this.licAction.riskClassCode;\r\n                    _this.form=res.resData\r\n                    _this.handleCenterData(res.resData);\r\n                    _this.handleCurrencyData(res.resData);\r\n                    \r\n                    // 只有当业务类型不是TO和FO时才获取假设数据\r\n                    if (_this.licAction.businessSourceCode !== 'TO' && _this.licAction.businessSourceCode !== 'FO') {\r\n                        _this.findBussQuotaDataById();\r\n                    }\r\n                    \r\n                    _this.initLrcFeeType(_this.licAction.businessSourceCode);\r\n                    _this.timer = setTimeout(function(){\r\n                        _this.requestData1();\r\n                    },1000)// 进入该分支说明当前并没有在计时，那么就开始一个计时\r\n                    //表单LRC的明细数据\r\n                }\r\n            });\r\n        },\r\n\r\n        //业务单位国际化\r\n        initLrcFeeType: function(businessSourceCode) {\r\n            var param = {\r\n              businessSourceCode : businessSourceCode,\r\n              becfType:'Lrc'\r\n            };\r\n            var _this = this,\r\n                url = Vue.gvUtil.getUrl({\r\n                    apiName: 'findLrcFeeTypeByCodeIdx',\r\n                    contextName: 'actuarial'\r\n                });\r\n            Vue.gvUtil.http.post(url, param).then(function(res) {\r\n                if (res.resCode === '0000') {\r\n                    var shareData = res.resData;\r\n                    if (!Vue.gvUtil.isEmpty(shareData)) {\r\n                        _this.premTypeArray = shareData;\r\n                        _this.lrcLicTab = shareData[0].remark\r\n                    }\r\n                }\r\n            });\r\n        },\r\n\r\n        requestData1: function () {\r\n            var _this = this,\r\n                url = Vue.gvUtil.getUrl({\r\n                    apiName: 'lrcFindPeriodHeader',\r\n                    contextName: 'actuarial'\r\n                });\r\n            var urlParams={\r\n                actionNo: _this.form.actionNo,\r\n                businessSourceCode : _this.licAction.businessSourceCode,\r\n                icgNo: _this.form.icgNo,\r\n            };\r\n            _this.loding = false\r\n            Vue.gvUtil.http.post(url,urlParams).then(function (res) {\r\n                if (res.resCode === '0000') {\r\n                    for(let item in _this.premTypeArray){\r\n                        var devNoFiled =_this.getPeriodFiled(res.resData.devNo, _this.premTypeArray[item]);\r\n                        let key = _this.premTypeArray[item].remark;\r\n                        let fee = _this.premTypeArray[item].remark;\r\n                        let fields = feildCard.getfieldList(_this[`tableField`][`${_this.licAction.businessSourceCode}`][`${fee}DetailTableFields`]);\r\n                        if (_this.premTypeArray[item].type=='1') {\r\n                            fields.push(devNoFiled)\r\n                        }\r\n                        _this.$set(_this.tabMap,key,{\r\n                            table: {\r\n                                basic: {\r\n                                    api: \"findLrcDataDetail\", //分页列表请求api\r\n                                    vo: \"lrcCashFlowVoList\", //分页列表返回的vo\r\n                                    context: \"actuarial\", //分页列表请求上下文\r\n                                    isShowMore: true\r\n                                },\r\n                                search: { //查询域元数据\r\n                                    actionNo: _this.form.actionNo,\r\n                                    icgNo: _this.form.icgNo,\r\n                                    businessSourceCode:_this.licAction.businessSourceCode,\r\n                                    feeType: key,\r\n                                },\r\n                                fields: fields,\r\n                            },\r\n\r\n                            list:[]\r\n                        })\r\n                    }\r\n                    _this.searchList();\r\n                }\r\n                _this.loding = true\r\n            });\r\n        },\r\n\r\n\r\n        getPeriodFiled: function (periods, becfVo) {\r\n            var devNoField = {\r\n                width: \"auto\",\r\n                sortable: false,\r\n                labelKey: 'atrDevelopMonth',\r\n                childrenFields:[]\r\n            };\r\n            var periodList=[];\r\n            var start =0;\r\n            var end = periods.length-1;\r\n            if (!Vue.gvUtil.isEmpty(becfVo.startDevNo)) {\r\n                start = becfVo.startDevNo;\r\n            }\r\n            if (!Vue.gvUtil.isEmpty(becfVo.endDevNo)) {\r\n                end = becfVo.endDevNo;\r\n            }\r\n            for(start; start<= end; start++) {\r\n                periodList[periods[start].toString()] = {\r\n                    prop: periods[start].toString(),\r\n                    quotaEName: periods[start].toString(),\r\n                    quotaCName: periods[start].toString(),\r\n                    quotaTName: periods[start].toString(),\r\n                    fieldType: '2'\r\n                }\r\n            }\r\n            devNoField.childrenFields = Vue.gvUtil.fieldSplic(periodList)\r\n            return devNoField;\r\n        },\r\n\r\n\r\n        requestData3: function (feeType) {\r\n            var _this = this,\r\n                url = Vue.gvUtil.getUrl({\r\n                    apiName: 'lrcFindDate',\r\n                    contextName: 'actuarial'\r\n                });\r\n            var urlParams={\r\n                actionNo: _this.form.actionNo,\r\n                icgNO: _this.form.icgNO,\r\n                businessSourceCode:_this.licAction.businessSourceCode,\r\n                feeType:_this.licAction.businessSourceCode,\r\n            };\r\n            Vue.gvUtil.http.post(url,urlParams).then(function (res) {\r\n                if (res.resCode === '0000') {\r\n                   _this.tabMap.get(feeType).list = res.resData.lrcCashFlowVoList\r\n                }\r\n            });\r\n        },\r\n\r\n\r\n        selectRowCurrency: function (row) {\r\n            if(row) {\r\n                this.form.currency = row.currencyCode;\r\n            } else {\r\n                this.form.currency = '';\r\n            }\r\n        },\r\n        // 业务单位国际化处理\r\n        handleCenterData: function (centerVo) {\r\n            var _this = this;\r\n            _this.form.entityCode = centerVo.entityCode + ' -- ' + Vue.gvUtil.getInzName(centerVo.entityEName, centerVo.entityCName, centerVo.entityTName);\r\n        },\r\n        // 业务单位国际化处理\r\n        handleCurrencyData: function (currencyVo) {\r\n            var _this = this;\r\n            if (Vue.gvUtil.isEmpty(Vue.gvUtil.getInzName(currencyVo.currencyEName, currencyVo.currencyCName, currencyVo.currencyTName))) {\r\n                return _this.form.currencyName = currencyVo.currency;\r\n            }\r\n            _this.form.currencyName = currencyVo.currency + ' -- ' + Vue.gvUtil.getInzName(currencyVo.currencyEName, currencyVo.currencyCName, currencyVo.currencyTName);\r\n        },\r\n        // 保存成功后回调的方法\r\n        successSubmit: function (data) {\r\n            Vue.gvUtil.message(Vue.gvUtil.getInzTranslate('atrExtractionTip'),1500,'success');\r\n            this.$emit('searchList');\r\n            this.$emit('findLicVersionData');\r\n            this.onClose();\r\n        },\r\n\r\n        /*aioi*/\r\n        //版本操作列事件\r\n        onListBtn: function (row, flag, prop) {\r\n            if (flag === 'develop') {\r\n                this.onViewQuotaDevelopPeriod(row, null, prop);\r\n            }\r\n        },\r\n        //查询版本的计量假设数据\r\n        findBussQuotaDataById:function () {\r\n            var actionVo = {\r\n                actionNo : this.licAction.actionNo,\r\n                dimensionValue : this.licAction.icgNo,\r\n                riskClassCode: this.form.riskClassCode\r\n            };\r\n            var _this = this,\r\n                url = Vue.gvUtil.getUrl({\r\n                    apiName: 'findAtrBussQuotaData',\r\n                    contextName: 'actuarial'\r\n                });\r\n            Vue.gvUtil.http.post(url, actionVo).then(function (res) {\r\n                if (res.resCode === '0000') {\r\n                    var obj =  res.resData.data;\r\n                    var objKeys = Object.keys(obj)\r\n                    if (Vue.gvUtil.isEmpty(obj) || objKeys.length==0) {\r\n                        actionVo.dimensionValue = _this.licAction.portfolioNo;\r\n                        Vue.gvUtil.http.post(url, actionVo).then(function (res) {\r\n                            if (res.resCode === '0000') {\r\n                                obj =  res.resData.data;\r\n                                _this.initQuotaTableAndData(obj);\r\n                            }\r\n                        });\r\n                    } else {\r\n                        _this.initQuotaTableAndData(obj);\r\n                    }\r\n                }\r\n            });\r\n        },\r\n        initQuotaTableAndData: function (obj) {\r\n            var data = {};\r\n            var _this = this;\r\n            var objKeys = Object.keys(obj);\r\n            \r\n            // 准备表格结构 - 第一行和第二行的表头结构\r\n            var firstRowFields = [];\r\n            var secondRowFields = [];\r\n            \r\n            // 第一列显示险种代码\r\n            firstRowFields.push({\r\n                prop: 'insuranceType',\r\n                labelKey: 'atrInsuranceClass',\r\n                width: \"120px\",\r\n                rowspan: 2, // 第一列需要跨越两行\r\n                headerAlign: 'center',\r\n                headColor: _this.palettes[0]\r\n            });\r\n            \r\n            // 创建只包含当前险类的 bussQuotaVoList\r\n            var row = {\r\n                insuranceType: this.form.riskClassCode || '' // 显示当前险类代码\r\n            };\r\n            \r\n            // 处理API返回的一般假设和事故年月等分组\r\n            for (var i = 0; i < objKeys.length; i++) {\r\n                var quota = obj[objKeys[i]];\r\n                var children = [];  // 存储该分组下的所有指标\r\n                \r\n                // 如果没有子项，跳过\r\n                if (!quota.childQuota) continue;\r\n                \r\n                // 获取该组下所有的不同指标代码（非险种），如\"#lic_ulae_ratio\"和\"#lic_ra_ratio\"\r\n                var uniqueBaseCodes = new Set();\r\n                var childQuota = quota.childQuota;\r\n                var childKeys = Object.keys(childQuota);\r\n                \r\n                for (var j = 0; j < childKeys.length; j++) {\r\n                    var childItem = childQuota[childKeys[j]];\r\n                    // 从完整代码中提取基础指标代码，例如从\"#lic_ulae_ratio01\"提取\"#lic_ulae_ratio\"\r\n                    var baseCode = childItem.quotaCode;\r\n                    uniqueBaseCodes.add(baseCode);\r\n                }\r\n                \r\n                // 将Set转换为数组\r\n                var baseCodes = Array.from(uniqueBaseCodes);\r\n                \r\n                // 为每个指标创建一个表头列\r\n                for (var j = 0; j < baseCodes.length; j++) {\r\n                    var baseCode = baseCodes[j];\r\n                    \r\n                    // 找到第一个匹配此指标的项目，用于获取名称\r\n                    var sampleItem = null;\r\n                    for (var k = 0; k < childKeys.length; k++) {\r\n                        if (childQuota[childKeys[k]].quotaCode === baseCode) {\r\n                            sampleItem = childQuota[childKeys[k]];\r\n                            break;\r\n                        }\r\n                    }\r\n                    \r\n                    if (!sampleItem) continue;\r\n                    \r\n                    // 添加第二行表头字段（指标名称）\r\n                    var fieldObj = {\r\n                        prop: baseCode,\r\n                        labelKey: Vue.gvUtil.getInzName(\r\n                            sampleItem.quotaEName,\r\n                            sampleItem.quotaCName,\r\n                            sampleItem.quotaLName\r\n                        ),\r\n                        headColor: _this.palettes[i % 2],\r\n                        className: _this.rowPalettes[i % 2],\r\n                        headerAlign: 'center',\r\n                        fieldType: sampleItem.quotaValueType,\r\n                        quotaType: sampleItem.quotaType, // 添加quotaType属性用于判断是否是发展期指标\r\n                        width: \"120px\"\r\n                    };\r\n                    \r\n                    // 查找当前险类的值\r\n                    var currentRiskClassCode = this.form.riskClassCode;\r\n                    var fullFieldName = baseCode + currentRiskClassCode;\r\n                    \r\n                    // 检查该险类的这个指标是否存在\r\n                    if (childQuota[fullFieldName]) {\r\n                        // 如果存在，添加到行数据中\r\n                        row[baseCode] = childQuota[fullFieldName].value;\r\n                    }\r\n                    \r\n                    secondRowFields.push(fieldObj);\r\n                    children.push(fieldObj);\r\n                }\r\n                \r\n                // 创建第一行表头的分组项\r\n                var groupField = {\r\n                    prop: objKeys[i],\r\n                    labelKey: Vue.gvUtil.getInzName(\r\n                        quota.quotaEName,\r\n                        quota.quotaCName,\r\n                        quota.quotaLName\r\n                    ),\r\n                    headerAlign: 'center',\r\n                    headColor: _this.palettes[i % 2],\r\n                    colspan: children.length, // 子项数量\r\n                    children: children\r\n                };\r\n                \r\n                if (children.length > 0) {\r\n                    firstRowFields.push(groupField);\r\n                }\r\n            }\r\n            \r\n            // 设置表头结构\r\n            _this.quotaTable.fields = [];\r\n            _this.quotaTable.firstRowFields = firstRowFields;\r\n            _this.quotaTable.secondRowFields = secondRowFields;\r\n            \r\n            // 添加行数据\r\n            _this.bussQuotaVoList = [row];\r\n            \r\n            // 保存原始数据以便后续使用\r\n            _this.quotaObj = obj;\r\n            _this.initTableHeader();\r\n        },\r\n        \r\n        // 从数据中提取所有险种代码\r\n        extractRiskClassCodes: function(obj) {\r\n            var riskClasses = [];\r\n            var codePattern = /(\\d+)$/; // 匹配字段名末尾的数字\r\n            \r\n            for (var groupKey in obj) {\r\n                var group = obj[groupKey];\r\n                if (!group.childQuota) continue;\r\n                \r\n                for (var fieldKey in group.childQuota) {\r\n                    var matches = fieldKey.match(codePattern);\r\n                    if (matches && matches[1]) {\r\n                        var riskClass = matches[1];\r\n                        if (riskClasses.indexOf(riskClass) === -1) {\r\n                            riskClasses.push(riskClass);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            \r\n            return riskClasses;\r\n        },\r\n\r\n        //初始化合同组输出表头\r\n        initTableHeader: function (){\r\n            var _this = this;\r\n            \r\n            // 添加自定义样式\r\n            var style = document.createElement('style');\r\n            style.innerHTML = `\r\n                .custom-table {\r\n                    width: 100%;\r\n                    border-collapse: collapse;\r\n                    margin-bottom: 20px;\r\n                }\r\n                .custom-table th, .custom-table td {\r\n                    border: 1px solid #dfe6ec;\r\n                    padding: 8px;\r\n                }\r\n                .custom-table thead th {\r\n                    background-color: #f5f7fa;\r\n                    font-weight: bold;\r\n                    color: #606266;\r\n                }\r\n                .custom-table tbody tr:hover {\r\n                    background-color: #f5f7fa;\r\n                }\r\n                .purple-to-gray {\r\n                    background-color: #ECE9F7;\r\n                }\r\n                .light-gray {\r\n                    background-color: #F5F7FA;\r\n                }\r\n            `;\r\n            document.head.appendChild(style);\r\n            \r\n            // 自定义表格渲染\r\n            _this.quotaTable.basic.custom = true;\r\n            \r\n            _this.loading = true;\r\n        },\r\n        //查看发展期指标数据\r\n        onViewQuotaDevelopPeriod: function (row, value, prop) {\r\n            var quotaDef;\r\n            var objKeys = Object.keys(this.quotaObj);\r\n            \r\n            // 构建完整的字段名称，例如 \"#lic_ulae_ratio01\"\r\n            var fullFieldName = prop + this.form.riskClassCode;\r\n            \r\n            // 在表格数据中查找匹配的指标\r\n            for (var i = 0; i < objKeys.length; i++) {\r\n                var childQuota = this.quotaObj[objKeys[i]].childQuota;\r\n                if (!childQuota) continue;\r\n                \r\n                // 直接通过完整字段名查找\r\n                if (childQuota[fullFieldName]) {\r\n                    quotaDef = childQuota[fullFieldName];\r\n                    break;\r\n                } else if (childQuota[prop]) {\r\n                    // 回退到只使用基础指标代码\r\n                    quotaDef = childQuota[prop];\r\n                    break;\r\n                }\r\n            }\r\n            \r\n            if (quotaDef) {\r\n                this.quotaDtl.quota = quotaDef;\r\n                this.findBussQuotaPeriod(prop);\r\n            } else {\r\n                console.error('未找到匹配的指标定义', fullFieldName);\r\n                Vue.gvUtil.message(Vue.gvUtil.getInzTranslate('gError'));\r\n            }\r\n        },\r\n        //关闭指标发展期框\r\n        onFold: function () {\r\n            this.periodShow = false;\r\n        },\r\n\r\n        //查询计量假设配置发展期数据\r\n        findBussQuotaPeriod: function (quotaCode) {\r\n            this.quotaDefName = Vue.gvUtil.getInzName(this.quotaDtl.quota.quotaEName, this.quotaDtl.quota.quotaCName, this.quotaDtl.quota.quotaLName);\r\n            this.periodShow = true;   // 显示页面\r\n            \r\n            // 显示加载指示器或处理逻辑\r\n            this.loading = true;\r\n            \r\n            var param = {\r\n                actionNo : this.licAction.actionNo,\r\n                dimensionValue : this.licAction.icgNo,\r\n                quotaCode: quotaCode,\r\n                riskClassCode: this.form.riskClassCode\r\n            };\r\n            \r\n            var _this = this;\r\n            var url = Vue.gvUtil.getUrl({\r\n                apiName: 'findAtrBussQuotaDataDetail',\r\n                contextName: 'actuarial'\r\n            });\r\n            \r\n            Vue.gvUtil.http.post(url, param).then(function (res) {\r\n                if (res.resCode === '0000' && res.resData) {\r\n                    _this.bussQuotaDevelopVoList = [];\r\n                    var obj = res.resData;\r\n                    var objKeys = Object.keys(obj);\r\n                    \r\n                    if (Vue.gvUtil.isEmpty(obj) || objKeys.length == 0) {\r\n                        param.dimensionValue = _this.licAction.portfolioNo;\r\n                        Vue.gvUtil.http.post(url, param).then(function (res) {\r\n                            if (res.resCode === '0000') {\r\n                                obj = res.resData;\r\n                                _this.initQuotaDetailTableAndData(obj);\r\n                            }\r\n                        }).finally(function() {\r\n                            _this.loading = false;\r\n                        });\r\n                    } else {\r\n                        _this.initQuotaDetailTableAndData(obj);\r\n                        _this.loading = false;\r\n                    }\r\n                } else {\r\n                    _this.loading = false;\r\n                }\r\n            }).catch(function() {\r\n                _this.loading = false;\r\n            }).finally(function() {\r\n                _this.periodShow = true;\r\n            });\r\n        },\r\n\r\n        // 更新发展期数据处理方法\r\n        initQuotaDetailTableAndData: function (obj) {\r\n            var _this = this;\r\n            \r\n            // 清空当前数据\r\n            _this.bussQuotaDevelopVoList = [];\r\n            _this.developmentColumns = [];\r\n            \r\n            if (!obj) return;\r\n            \r\n            var objKeys = Object.keys(obj);\r\n            if (objKeys.length === 0) return;\r\n            \r\n            // 创建列配置\r\n            for (var i = 0; i < objKeys.length; i++) {\r\n                _this.developmentColumns.push({\r\n                    prop: objKeys[i],\r\n                    label: objKeys[i]\r\n                });\r\n            }\r\n            \r\n            // 创建行数据\r\n            var rowData = {\r\n                quotaPeriod: _this.quotaDefName || '发展期'\r\n            };\r\n            \r\n            // 填充行数据\r\n            for (var i = 0; i < objKeys.length; i++) {\r\n                var key = objKeys[i];\r\n                if (obj[key] && !Vue.gvUtil.isEmpty(obj[key].quotaValue)) {\r\n                    rowData[key] = obj[key].quotaValue; // 使用原始值，由模板负责格式化\r\n                } else {\r\n                    rowData[key] = '';\r\n                }\r\n            }\r\n            \r\n            // 添加行数据\r\n            _this.bussQuotaDevelopVoList.push(rowData);\r\n        },\r\n\r\n        handleClick: function (tab, event) {\r\n            this.$nextTick(() => {\r\n                this.searchList(tab.name);\r\n            })\r\n        },\r\n\r\n        getParamsMixin: function getParamsMixin(params) {\r\n            this.cacheFilters = Object.assign({\r\n                _pageSize: this.mixinObject.searchSet.pageSize,\r\n                _pageNo: this.mixinObject.searchSet.pageNo\r\n            }, params);\r\n            return this.cacheFilters;\r\n        },\r\n\r\n        /**\r\n         * 页码变动\r\n         * @param val 码数\r\n         */\r\n        onHandleCurrentChange: function onHandleCurrentChange(val) {\r\n            if (typeof val === 'undefined') {\r\n                return;\r\n            }\r\n            this.mixinObject.searchSet.pageNo = val - 1;\r\n            this.mixinObject.isInit = true;\r\n            this.searchList('uprDetailsVoList');\r\n        },\r\n\r\n        /**\r\n         * 查询行数变动\r\n         * @param 行数\r\n         */\r\n        onHandleSizeChange: function onHandleSizeChange(val) {\r\n            this.mixinObject.searchSet.pageSize = val;\r\n            this.mixinObject.isInit = true;\r\n            this.searchList('uprDetailsVoList');\r\n        },\r\n\r\n        /**\r\n         * 获取查询数据\r\n         */\r\n        searchList: function searchList(tabName) {\r\n            tabName = Vue.gvUtil.isEmpty(this.lrcLicTab)? tabName : this.lrcLicTab;\r\n            var urlParams={\r\n                actionNo: this.form.actionNo,\r\n                icgNo: this.form.icgNo,\r\n                businessSourceCode:this.licAction.businessSourceCode,\r\n                riskClassCode: this.form.riskClassCode,\r\n                feeType: tabName,\r\n            };\r\n            var voName= 'lrcCashFlowVoList';\r\n            if (!this.mixinObject.isInit) {\r\n                this.mixinObject.searchSet.pageNo = 0;\r\n                this.mixinObject.searchSet.currentPage = 1;\r\n            } else {\r\n                this.mixinObject.isInit = false;\r\n            }\r\n            var params = this.getParamsMixin(),\r\n                url = Vue.gvUtil.getUrl({\r\n                    apiName: 'findLrcDataDetail',\r\n                    contextName: 'actuarial',\r\n                    serachParms: { _pageSize: params._pageSize, _pageNo: params._pageNo }\r\n                }),\r\n                _this = this,\r\n                list = [];\r\n            Vue.gvUtil.http.post(url, urlParams).then(function (res) {\r\n                if (res.resCode === '0000') {\r\n                    _this.uprDetailsVoList = res.resData[voName].content;\r\n                    _this.$set(_this.tabMap[tabName],'list',res.resData[voName].content)\r\n                    _this.mixinObject.searchSet.total = res.resData[voName]['total'] ? res.resData[voName].total : res.resData[voName].totalElements;\r\n                    _this.lrcLicTab = tabName;\r\n                } else {\r\n                    _this.mixinObject.searchSet.total = 0;\r\n                    _this.lrcLicTab = tabName;\r\n                }\r\n            });\r\n        },\r\n\r\n        handleSetList(list){\r\n            let tableObj = this.tabMap[this.lrcLicTab]\r\n            this.$set(tableObj,'list',list)\r\n            this.$set(this.tabMap,this.lrcLicTab,tableObj)\r\n\r\n        },\r\n    },\r\n    mounted() {\r\n        this.$nextTick(() => {\r\n            this.showRiskClassCode = this.licAction && (this.licAction.businessSourceCode === 'DD' || this.licAction.businessSourceCode === 'TI');\r\n        });\r\n    },\r\n}\r\n\r\n</script>"]}]}