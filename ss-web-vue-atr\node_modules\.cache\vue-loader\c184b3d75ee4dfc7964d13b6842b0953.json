{"remainingRequest": "O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\src\\pages\\atr\\expectedCashFlow\\puhua\\lrcCashFlowApp\\components\\lrcViewDetailIndex.vue?vue&type=script&lang=js", "dependencies": [{"path": "O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\src\\pages\\atr\\expectedCashFlow\\puhua\\lrcCashFlowApp\\components\\lrcViewDetailIndex.vue", "mtime": 1753792119299}, {"path": "O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1742174260879}, {"path": "O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1742174263321}, {"path": "O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1742174260879}, {"path": "O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1742174262792}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["lrcViewDetailIndex.vue"], "names": [], "mappings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file": "lrcViewDetailIndex.vue", "sourceRoot": "src/pages/atr/expectedCashFlow/puhua/lrcCashFlowApp/components", "sourcesContent": ["<template>\r\n    <el-dialog :title=\"'atrLrcCashFlowTitle' | translate\" custom-class=\"gv-dialog-form\" :visible.sync=\"dialogFormVisible\"\r\n               width=\"96%\" :close-on-click-modal=\"false\" top=\"5vh\" append-to-body>\r\n        <gv-form :model=\"form\" ref=\"form\" :rules=\"rules\">\r\n            <el-collapse v-model=\"mixinObject.activeNames\">\r\n                <el-collapse-item :title=\"'gTitleBasics' | translate('Basics Data')\" name=\"1\" class=\"table-line\">\r\n                    <div class=\"gv-row\">\r\n                        <gv-form-item key-name=\"gCenterCode\" prop=\"entityCode\" >\r\n                            <gv-auto-complete context-name=\"common\" url=\"/basic_center/find_Branch\" code-name=\"entityCode,entityCName\"\r\n                                              :is-readonly=\"isReadonlySave\"\r\n                                              label-name=\"entityCode,entityCName\"\r\n                                              v-model=\"form.entityCode\" ></gv-auto-complete>\r\n                        </gv-form-item>\r\n                        <gv-form-item key-name=\"dmBusinessType\" prop=\"businessSourceCode\">\r\n                            <gv-select :disabled=\"isReadonlySave\" size=\"mini\" options-set=\"0\" code-type=\"BusinessModel/Base\" v-model=\"form.businessSourceCode\"></gv-select>\r\n                        </gv-form-item>\r\n                        <gv-form-item key-name=\"atrEvaluationYearMonth\" prop=\"yearMonth\">\r\n                            <el-input maxlength=\"6\" placeholder=\"yyyymm\" v-model=\"form.yearMonth\" :disabled=\"isReadonlySave\">\r\n                            </el-input>\r\n                        </gv-form-item>\r\n                    </div>\r\n                    <div class=\"gv-row\">\r\n                        <gv-form-item key-name=\"gDmPortfolioNo\" prop=\"portfolioNo\" v-if=\"type !=='add'\">\r\n                            <el-input v-model=\"form.portfolioNo\" :disabled=\"isReadonlySave\"></el-input>\r\n                        </gv-form-item>\r\n                        <gv-form-item key-name=\"gDmContractGroupNo\" prop=\"icgNo\" v-if=\"type !=='add'\">\r\n                            <el-input v-model=\"form.icgNo\" :disabled=\"isReadonlySave\"></el-input>\r\n                        </gv-form-item>\r\n                      <gv-form-item key-name=\"atrInsuranceClass\"  v-if=\"showRiskClassCode\" prop=\"riskClassCode\">\r\n                        <el-input v-model=\"form.riskClassCode\" :disabled=\"isReadonlySave\"></el-input>\r\n                      </gv-form-item>\r\n                    </div>\r\n                </el-collapse-item>\r\n\r\n                <!--aioi-->\r\n                <el-collapse-item :title=\"'atrAcquisitionInfo' | translate\" name=\"2\" v-if=\"form.businessSourceCode !== 'TO' && form.businessSourceCode !== 'FO'\">\r\n                    <div class=\"gv-atr-collapse-content\">\r\n                        <div id=\"tabs-atr\">\r\n                            <table class=\"custom-table\">\r\n                                <thead>\r\n                                  <!-- 第一行表头 -->\r\n                                  <tr>\r\n                                    <template v-for=\"(field, index) in quotaTable.firstRowFields\">\r\n                                      <th v-if=\"field.rowspan\" \r\n                                          :key=\"'first-'+index\"\r\n                                          :rowspan=\"field.rowspan\" \r\n                                          :class=\"field.className\"\r\n                                          :style=\"{ backgroundColor: field.headColor, textAlign: 'center' }\">\r\n                                        {{ field.labelKey | translate }}\r\n                                      </th>\r\n                                      <th v-else \r\n                                          :key=\"('first-'+index)\"\r\n                                          :colspan=\"field.colspan\" \r\n                                          :class=\"field.className\"\r\n                                          :style=\"{ backgroundColor: field.headColor, textAlign: 'center' }\">\r\n                                        {{ field.labelKey | translate }}\r\n                                      </th>\r\n                                    </template>\r\n                                  </tr>\r\n                                  <!-- 第二行表头 -->\r\n                                  <tr>\r\n                                    <template v-for=\"(field, index) in quotaTable.secondRowFields\">\r\n                                      <th v-if=\"field.prop !== 'insuranceType'\"\r\n                                          :key=\"'second-'+index\" \r\n                                          :class=\"field.className\"\r\n                                          :style=\"{ backgroundColor: field.headColor, textAlign: 'center' }\">\r\n                                        {{ field.labelKey | translate }}\r\n                                      </th>\r\n                                    </template>\r\n                                  </tr>\r\n                                </thead>\r\n                                <tbody>\r\n                                  <tr v-for=\"(row, rowIndex) in bussQuotaVoList\" :key=\"rowIndex\">\r\n                                    <!-- 险种列 -->\r\n                                    <td style=\"text-align: center;\">{{ row.insuranceType }}</td>\r\n                                    <!-- 数据单元格 -->\r\n                                    <template v-for=\"(field, fieldIndex) in quotaTable.secondRowFields\">\r\n                                      <td v-if=\"field.prop !== 'insuranceType'\"\r\n                                          :key=\"'cell-'+fieldIndex\" \r\n                                          :class=\"field.className\" \r\n                                          style=\"text-align: right;\">\r\n                                        <!-- 如果是发展期指标(quotaType=1)，只显示图标不显示值 -->\r\n                                        <template v-if=\"field.quotaType === '1'\">\r\n                                          <i class=\"el-icon-my-line-graph\"\r\n                                             style=\"margin-left: 5px; cursor: pointer;\"\r\n                                             @click=\"onListBtn(row, 'develop', field.prop)\"></i>\r\n                                        </template>\r\n                                        <!-- 否则根据字段类型格式化数据 -->\r\n                                        <template v-else>\r\n                                          <span v-if=\"field.fieldType === '2'\">\r\n                                            {{ row[field.prop] | amount(true, 2) }}\r\n                                          </span>\r\n                                          <span v-else-if=\"field.fieldType === '4'\">\r\n                                            {{ row[field.prop] | amountZero(false, 2) }}%\r\n                                          </span>\r\n                                          <span v-else>\r\n                                            {{ row[field.prop] }}\r\n                                          </span>\r\n                                        </template>\r\n                                      </td>\r\n                                    </template>\r\n                                  </tr>\r\n                                </tbody>\r\n                            </table>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"gv-atr-collapse-content\" v-if=\"periodShow\">\r\n                        <div>\r\n                            <span class=\"rectangularr\"></span>\r\n                            <span class=\"gv-panel-titleBar\">{{quotaDefName}}</span>\r\n                            <div  class=\"pull-right\">\r\n                                <el-button class=\"mr15\" style=\"margin-right: 8px\" type=\"text\" @click=\"onFold\"><i class=\"el-dialog__close el-icon el-icon-close\"></i></el-button>\r\n                            </div>\r\n                        </div>\r\n                        <div>\r\n                            <el-table :data=\"bussQuotaDevelopVoList\" border :header-cell-style=\"{ backgroundColor: '#f5f7fa', color: '#606266' }\">\r\n                                <el-table-column prop=\"quotaPeriod\" :label=\"'atrDevelopMonth' | translate\" width=\"180\"></el-table-column>\r\n                                <el-table-column\r\n                                    v-for=\"(col, index) in developmentColumns\"\r\n                                    :key=\"index\"\r\n                                    :prop=\"col.prop\"\r\n                                    :label=\"col.label\"\r\n                                    align=\"right\">\r\n                                    <template v-slot=\"scope\">\r\n                                        {{ scope.row[col.prop] | amountZero(false, 2) }} %\r\n                                    </template>\r\n                                </el-table-column>\r\n                            </el-table>\r\n                        </div>\r\n                    </div>\r\n                </el-collapse-item>\r\n                <el-collapse-item :title=\"'atrAioiExpectedPremiumCF' | translate\" name=\"3\">\r\n                    <el-tabs v-model=\"lrcLicTab\"  v-if=\"loding\" @tab-click=\"handleClick\">\r\n                        <el-tab-pane v-for=\"obj in this.premTypeArray\" :key=\"obj.remark\" :label=\"obj.outCName\"\r\n                                     :name=\"obj.remark\" >\r\n                            <template  >\r\n                                <gv-data-table-list v-if=\"obj.remark===lrcLicTab && tabMap[obj.remark]\" :ref=\"obj.remark+'Table'\" @upListData=\"handleSetList\" :table=\"tabMap[obj.remark].table\" :list=\"tabMap[obj.remark].list\" :id=\"obj.remark\"   :listTableTop=\"true\" :paging=\"true\"\r\n                                                    :currentPage=\"mixinObject.searchSet.currentPage\" :MaxHeight='\"400px\"' :total=\"mixinObject.searchSet.total\" >\r\n                                </gv-data-table-list>\r\n                            </template>\r\n                       </el-tab-pane>\r\n                    </el-tabs>\r\n                </el-collapse-item>\r\n            </el-collapse>\r\n            <el-row class=\"toolbar-btn txt-center\">\r\n                <el-button class=\"gv-btn gv-btn-primary\" :disabled=\"isReadonlySave\" v-if=\"type!='view'\" type=\"primary\" @click=\"onSubmit()\">{{\r\n                        'atrBtnDraw' | translate }}\r\n                </el-button>\r\n                <el-button class=\"gv-btn gv-btn-white\" @click=\"onClose()\">{{ 'gBtnClose' | translate}}</el-button>\r\n            </el-row>\r\n        </gv-form>\r\n    </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport feildCard from './card.js'\r\nexport default {\r\n    name: 'lrcAppEditIndex',\r\n    props: {\r\n        type:'',\r\n        licAction:{},\r\n        title:'',\r\n        licShowDataForLossCurrent:'',\r\n        id: '',\r\n        riskClass:'',\r\n        yearMonth:'',\r\n        dataType: '',\r\n        drawTime: '',\r\n        statisZones: '',\r\n        centerId: '',\r\n        currency: '',\r\n        portfolioNo: '',\r\n        mainId: '',\r\n    },\r\n    data: function () {\r\n        return {\r\n            form: {\r\n                centerId: '',\r\n                entityCode: '',\r\n                riskClass: null,\r\n                currency: '',\r\n                ibnrType: '',\r\n                businessSourceCode: '',\r\n                statisZones: '',\r\n                drawTime: '',\r\n                dataType: '',\r\n                riskCName: '',\r\n                loaCode: '',\r\n                loaName: '',\r\n                portfolioNo: null,\r\n                yearMonth: '',\r\n                currencyName: '',\r\n                taskCode: '',\r\n                riskClassCode: '',\r\n                recvDetailVoList:[],\r\n                gepDetailVoList:[],\r\n                covDetailVoList:[],\r\n                uepDetailVoList:[],\r\n                adjDetailVoList:[],\r\n                mainDetailVoList:[],\r\n                iacfDetailVoList:[],\r\n                nonMaimDetailVoList:[],\r\n                csmDetailVoList:[],\r\n            },\r\n            loding: true,\r\n            isReadonly: false,\r\n            isDisabled: false,// 禁用下拉选项\r\n\r\n          tableField: {\r\n            DD: {\r\n              edPremiumDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt', 'cmunitNo',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate', 'comfirmDate','approvalDate',\r\n                'currencyCode', 'grossPremium','preAccumEdPremium' ],\r\n              edNetFeeDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode' ,'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt', 'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                  'currencyCode','feeRate','netFee', 'preCumlEdNetFee'],\r\n              edIacfDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','iacf','preCumlEdIacf'\r\n                 ],\r\n              edIaehcInDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt',  'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','iaehcIn','preCumlEdIaehcIn'],\r\n              edIaehcOutDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','iaehcOut','preCumlEdIaehcOut'],\r\n              premiumDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt',  'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','premium','preCumlPaidPremium'],\r\n              netFeeDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt',  'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode', 'netFee','preCumlPaidNetFee'],\r\n              badDebtDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt',  'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','badDebt'],\r\n              iacfDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','iacf','preCumlEdIacf'],\r\n              iaehcInDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','iaehcIn'],\r\n              iaehcOutDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt',  'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','iaehcOut'],\r\n              lapseDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode',\r\n                 'lapseRate'],\r\n              mtFeeDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode',\r\n                'mtRate'],\r\n              claimDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode',\r\n                'claimRate'],\r\n              ulaeDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode',\r\n                'ulaeRate'],\r\n              raDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode',\r\n                'raRatio'],\r\n\r\n            },\r\n            TI: {\r\n              edPremiumDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate',\r\n                'grossPremium','preCumlPaidPremium' ],\r\n              edNetFeeDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','feeRate','netFee', 'preCumlEdNetFee'],\r\n              edIacfDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','iacf','preCumlEdIacf'\r\n              ],\r\n              edIaehcInDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','iaehcIn','preCumlEdIaehcIn'],\r\n              edIaehcOutDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','iaehcOut','preCumlEdIaehcOut'],\r\n              premiumDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','premium','preCumlPaidPremium'],\r\n              netFeeDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate', 'netFee','preCumlPaidNetFee'],\r\n              iacfDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','iacf','preCumlEdIacf'],\r\n              iaehcInDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','iaehcIn'],\r\n              iaehcOutDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','iaehcOut'],\r\n              lapseDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode','currencyCode',\r\n                'lapseRate'],\r\n              mtFeeDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode','currencyCode',\r\n                'mtRate'],\r\n              claimDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode','currencyCode',\r\n                'claimRate'],\r\n              ulaeDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode','currencyCode',\r\n                'ulaeRate'],\r\n              raDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode','currencyCode',\r\n                'raRatio'],\r\n            },\r\n            TO: {\r\n              edPremiumDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','policyNo', 'endorseSeqNo','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                'grossPremium','preEdPremium' ],\r\n              premiumCfDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','policyNo', 'endorseSeqNo','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo',\r\n                'evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                'grossPremium', 'preAccumEdPremium' ],\r\n              netFeeCfDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','policyNo', 'endorseSeqNo','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                 'netFee','preAccumNetFee'],\r\n              edNetFeeDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','policyNo', 'endorseSeqNo','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',\r\n                'effectiveDate','expiryDate', 'feeRate','netFee', 'preEdNetFee']\r\n            },\r\n            TX: {\r\n              edPremiumDetailTableFields:[\r\n                'actionNO','entityId','treatyNo','policyNo', 'endorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                'grossPremium','preEdPremium' ],\r\n              premiumCfDetailTableFields:[\r\n                'actionNO','entityId','treatyNo','policyNo', 'endorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo',\r\n                'evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                'grossPremium' ]\r\n            },\r\n            FO: {\r\n              edPremiumDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                'grossPremium','preEdPremium' ],\r\n              recvPremiumDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo',\r\n                'evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                'grossPremium', 'preAccumEdPremium' ],\r\n              netFeeDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                'netFee','preAccumNetFee'],\r\n              edNetFeeDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',\r\n                'effectiveDate','expiryDate','netFee', 'preEdNetFee']\r\n            },\r\n          },\r\n            isReadonlySave: false,\r\n            rules: {},\r\n            dialogFormVisible: true,\r\n            pickerOptions: {\r\n                disabledDate: function (time) {\r\n                    return time.getTime() < Date.now() - 8.64e7;\r\n                }\r\n            },\r\n            activeName: 'first',\r\n\r\n            /****************aioi假设值*****************/\r\n            //版本指标假设业务值\r\n            quotaTable: {\r\n                basic: {\r\n                    headerstyle:true,\r\n                    cellstyle:{},\r\n                    custom: true, // 添加自定义渲染支持\r\n                },\r\n                fields: [],\r\n                firstRowFields: [], // 第一行表头字段\r\n                secondRowFields: [] // 第二行表头字段\r\n            },\r\n            //版本指标发展期业务值\r\n            devPeriodTable: {\r\n                basic: {\r\n                    fieldInit: false,\r\n                    cellstyle: true,\r\n                    headerstyle: true\r\n                },\r\n                fields: [{\r\n                    prop: \"quotaPeriod\", //属性\r\n                    labelKey: 'atrDevelopMonth',\r\n                    sortable:false,\r\n                    showOverflowTooltip: true,\r\n                    width:\"150px\",\r\n                }]\r\n            },\r\n            developmentColumns: [], // 存储发展期表格列\r\n            quotaDtl:{\r\n                quota:''\r\n            },\r\n            bussQuotaVoList:[],\r\n            bussQuotaDevelopVoList:[],\r\n            quotaObj:'',\r\n            loading: false,\r\n            periodShow: false,\r\n            quotaDefName:'',\r\n            palettes:['#DAD4F0','#ECE9F7'],\r\n            rowPalettes:['purple-to-gray','light-gray'],\r\n            lrcLicTab: '',\r\n            premTypeArray:[],\r\n            tabMap: {\r\n            },\r\n            showRiskClassCode: false,\r\n        }\r\n    },\r\n    watch: {\r\n        dialogFormVisible: function (n, o) {\r\n            !n && this.$emit('close');\r\n        },\r\n        'licAction.businessSourceCode': function(newVal) {\r\n            this.showRiskClassCode = newVal === 'DD' || newVal === 'TI';\r\n        },\r\n        'form.riskClassCode': function(newVal, oldVal) {\r\n            // 当险类代码变化且有值时，重新获取假设数据\r\n            if (newVal && newVal !== oldVal) {\r\n                this.findBussQuotaDataById();\r\n            }\r\n        }\r\n    },\r\n\r\n    methods: {\r\n        // 确认按钮（表单提交）\r\n        onSubmit: function() {\r\n            var _this = this,\r\n                url;\r\n            this.$refs.form.validate(function(valid) {\r\n                if (valid) {\r\n                    Vue.gvUtil.confirm({\r\n                        msg: Vue.gvUtil.getInzTranslate('gSaveSubmit')\r\n                    }).then(function() {\r\n                        // 新增\r\n                        url = Vue.gvUtil.getUrl({\r\n                            apiName: 'lrcCashFlowAdd',\r\n                            contextName: 'actuarial'\r\n                        });\r\n                        Vue.gvUtil.http.post(url, _this.form).then(function (res) {\r\n                            if (_this.isDialog) {\r\n                                // _this.dialogSuccessSubmit();\r\n                            } else {\r\n                                _this.successSubmit(res)\r\n                            }\r\n                            if (res.resCode === '0017') {\r\n                                msg: Vue.gvUtil.getInzTranslate('gSaveError');\r\n                            }\r\n\r\n                        });\r\n                    }).catch(function(){});\r\n                } else {\r\n                    Vue.gvUtil.message(Vue.gvUtil.getInzTranslate('gValidateContent'));\r\n                    return false;\r\n                }\r\n            });\r\n        },\r\n        // 清除表单\r\n        resetForm: function (formName) {\r\n            var lrcCfMainId = this.form.lrcCfMainId;\r\n            var entityCode = this.form.entityCode;\r\n            this.$refs[formName].resetFields();\r\n            this.form.lrcCfMainId = '';\r\n            if (this.type === 'edit') {\r\n                this.form.lrcCfMainId = lrcCfMainId;\r\n                this.form.entityCode = entityCode;\r\n            }\r\n        },\r\n        // 关闭\r\n        onClose: function () {\r\n            this.dialogFormVisible = false;\r\n            this.$emit('close');\r\n        },\r\n        onEditorChange: function (val) {\r\n            this.form.modelContent = val.text;\r\n        },\r\n\r\n        // 初始化页面，低层直接调用\r\n        initPage: function () {\r\n            if (this.type !== 'add') {\r\n                this.requestData();\r\n            }\r\n            if (this.type === 'view') {\r\n                this.isReadonly = true;\r\n                this.isDisabled = true;\r\n                this.isReadonlySave = true;\r\n            }\r\n            if (this.type === 'add') {\r\n                var user = sessionStorage.getItem('user');\r\n                if (user) {\r\n                    user = JSON.parse(user);\r\n                    this.form.centerId = user.userCenterId;\r\n\r\n                }\r\n                this.selectCenterId();\r\n            }\r\n        },\r\n        //配置初始化核算单位，并查询数据\r\n        selectCenterId: function () {\r\n            this.form.entityCode = Vue.gvUtil.setentityCode()\r\n        },\r\n        // 初始化校验，低层直接调用\r\n        initRules: function () {\r\n            this.rules = {\r\n                entityCode: [{\r\n                    trigger: 'blur',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }, {\r\n                    trigger: 'change',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }],\r\n                businessType: [{\r\n                    trigger: 'blur',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }, {\r\n                    trigger: 'change',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }],\r\n                yearMonth: [{\r\n                    trigger: 'blur',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }, {\r\n                    trigger: 'change',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }],\r\n                currency: [{\r\n                    trigger: 'blur',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }, {\r\n                    trigger: 'change',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }],\r\n                drawTime: [{\r\n                    trigger: 'blur',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }, {\r\n                    trigger: 'change',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }],\r\n            };\r\n        },\r\n        requestData: function () {\r\n            var _this = this,\r\n                url = Vue.gvUtil.getUrl({\r\n                    apiName: 'bussLrcCashFlowFindByPk',\r\n                    urlParams: {\r\n                        id: _this.licAction.id\r\n                    },\r\n                    contextName: 'actuarial'\r\n                });\r\n            Vue.gvUtil.http.get(url).then(function (res) {\r\n                if (res.resCode === '0000') {\r\n                    // $.extend(true, _this.form, res.resData);\r\n                    //表单基础数据查询和格式化\r\n                    res.resData.portfolioNo = _this.licAction.portfolioNo;\r\n                    res.resData.icgNo = _this.licAction.icgNo;\r\n                    res.resData.riskClassCode = _this.licAction.riskClassCode;\r\n                    _this.form=res.resData\r\n                    _this.handleCenterData(res.resData);\r\n                    _this.handleCurrencyData(res.resData);\r\n                    \r\n                    // 只有当业务类型不是TO和FO时才获取假设数据\r\n                    if (_this.licAction.businessSourceCode !== 'TO' && _this.licAction.businessSourceCode !== 'FO') {\r\n                        _this.findBussQuotaDataById();\r\n                    }\r\n                    \r\n                    _this.initLrcFeeType(_this.licAction.businessSourceCode);\r\n                    _this.timer = setTimeout(function(){\r\n                        _this.requestData1();\r\n                    },1000)// 进入该分支说明当前并没有在计时，那么就开始一个计时\r\n                    //表单LRC的明细数据\r\n                }\r\n            });\r\n        },\r\n\r\n        //业务单位国际化\r\n        initLrcFeeType: function(businessSourceCode) {\r\n            var param = {\r\n              businessSourceCode : businessSourceCode,\r\n              becfType:'Lrc'\r\n            };\r\n            var _this = this,\r\n                url = Vue.gvUtil.getUrl({\r\n                    apiName: 'findLrcFeeTypeByCodeIdx',\r\n                    contextName: 'actuarial'\r\n                });\r\n            Vue.gvUtil.http.post(url, param).then(function(res) {\r\n                if (res.resCode === '0000') {\r\n                    var shareData = res.resData;\r\n                    if (!Vue.gvUtil.isEmpty(shareData)) {\r\n                        _this.premTypeArray = shareData;\r\n                        _this.lrcLicTab = shareData[0].remark\r\n                    }\r\n                }\r\n            });\r\n        },\r\n\r\n        requestData1: function () {\r\n            var _this = this,\r\n                url = Vue.gvUtil.getUrl({\r\n                    apiName: 'lrcFindPeriodHeader',\r\n                    contextName: 'actuarial'\r\n                });\r\n            var urlParams={\r\n                actionNo: _this.form.actionNo,\r\n                businessSourceCode : _this.licAction.businessSourceCode,\r\n                icgNo: _this.form.icgNo,\r\n            };\r\n            _this.loding = false\r\n            Vue.gvUtil.http.post(url,urlParams).then(function (res) {\r\n                if (res.resCode === '0000') {\r\n                    for(let item in _this.premTypeArray){\r\n                        var devNoFiled =_this.getPeriodFiled(res.resData.devNo, _this.premTypeArray[item]);\r\n                        let key = _this.premTypeArray[item].remark;\r\n                        let fee = _this.premTypeArray[item].remark;\r\n                        let fields = feildCard.getfieldList(_this[`tableField`][`${_this.licAction.businessSourceCode}`][`${fee}DetailTableFields`]);\r\n                        if (_this.premTypeArray[item].type=='1') {\r\n                            fields.push(devNoFiled)\r\n                        }\r\n                        _this.$set(_this.tabMap,key,{\r\n                            table: {\r\n                                basic: {\r\n                                    api: \"findLrcDataDetail\", //分页列表请求api\r\n                                    vo: \"lrcCashFlowVoList\", //分页列表返回的vo\r\n                                    context: \"actuarial\", //分页列表请求上下文\r\n                                    isShowMore: true\r\n                                },\r\n                                search: { //查询域元数据\r\n                                    actionNo: _this.form.actionNo,\r\n                                    icgNo: _this.form.icgNo,\r\n                                    businessSourceCode:_this.licAction.businessSourceCode,\r\n                                    feeType: key,\r\n                                },\r\n                                fields: fields,\r\n                            },\r\n\r\n                            list:[]\r\n                        })\r\n                    }\r\n                    _this.searchList();\r\n                }\r\n                _this.loding = true\r\n            });\r\n        },\r\n\r\n\r\n        getPeriodFiled: function (periods, becfVo) {\r\n            var devNoField = {\r\n                width: \"auto\",\r\n                sortable: false,\r\n                labelKey: 'atrDevelopMonth',\r\n                childrenFields:[]\r\n            };\r\n            var periodList=[];\r\n            var start =0;\r\n            var end = periods.length-1;\r\n            if (!Vue.gvUtil.isEmpty(becfVo.startDevNo)) {\r\n                start = becfVo.startDevNo;\r\n            }\r\n            if (!Vue.gvUtil.isEmpty(becfVo.endDevNo)) {\r\n                end = becfVo.endDevNo;\r\n            }\r\n            for(start; start<= end; start++) {\r\n                periodList[periods[start].toString()] = {\r\n                    prop: periods[start].toString(),\r\n                    quotaEName: periods[start].toString(),\r\n                    quotaCName: periods[start].toString(),\r\n                    quotaTName: periods[start].toString(),\r\n                    fieldType: '2'\r\n                }\r\n            }\r\n            devNoField.childrenFields = Vue.gvUtil.fieldSplic(periodList)\r\n            return devNoField;\r\n        },\r\n\r\n\r\n        requestData3: function (feeType) {\r\n            var _this = this,\r\n                url = Vue.gvUtil.getUrl({\r\n                    apiName: 'lrcFindDate',\r\n                    contextName: 'actuarial'\r\n                });\r\n            var urlParams={\r\n                actionNo: _this.form.actionNo,\r\n                icgNO: _this.form.icgNO,\r\n                businessSourceCode:_this.licAction.businessSourceCode,\r\n                feeType:_this.licAction.businessSourceCode,\r\n            };\r\n            Vue.gvUtil.http.post(url,urlParams).then(function (res) {\r\n                if (res.resCode === '0000') {\r\n                   _this.tabMap.get(feeType).list = res.resData.lrcCashFlowVoList\r\n                }\r\n            });\r\n        },\r\n\r\n\r\n        selectRowCurrency: function (row) {\r\n            if(row) {\r\n                this.form.currency = row.currencyCode;\r\n            } else {\r\n                this.form.currency = '';\r\n            }\r\n        },\r\n        // 业务单位国际化处理\r\n        handleCenterData: function (centerVo) {\r\n            var _this = this;\r\n            _this.form.entityCode = centerVo.entityCode + ' -- ' + Vue.gvUtil.getInzName(centerVo.entityEName, centerVo.entityCName, centerVo.entityTName);\r\n        },\r\n        // 业务单位国际化处理\r\n        handleCurrencyData: function (currencyVo) {\r\n            var _this = this;\r\n            if (Vue.gvUtil.isEmpty(Vue.gvUtil.getInzName(currencyVo.currencyEName, currencyVo.currencyCName, currencyVo.currencyTName))) {\r\n                return _this.form.currencyName = currencyVo.currency;\r\n            }\r\n            _this.form.currencyName = currencyVo.currency + ' -- ' + Vue.gvUtil.getInzName(currencyVo.currencyEName, currencyVo.currencyCName, currencyVo.currencyTName);\r\n        },\r\n        // 保存成功后回调的方法\r\n        successSubmit: function (data) {\r\n            Vue.gvUtil.message(Vue.gvUtil.getInzTranslate('atrExtractionTip'),1500,'success');\r\n            this.$emit('searchList');\r\n            this.$emit('findLicVersionData');\r\n            this.onClose();\r\n        },\r\n\r\n        /*aioi*/\r\n        //版本操作列事件\r\n        onListBtn: function (row, flag, prop) {\r\n            if (flag === 'develop') {\r\n                this.onViewQuotaDevelopPeriod(row, null, prop);\r\n            }\r\n        },\r\n        //查询版本的计量假设数据\r\n        findBussQuotaDataById:function () {\r\n            var actionVo = {\r\n                actionNo : this.licAction.actionNo,\r\n                dimensionValue : this.licAction.icgNo,\r\n                riskClassCode: this.form.riskClassCode\r\n            };\r\n            var _this = this,\r\n                url = Vue.gvUtil.getUrl({\r\n                    apiName: 'findAtrBussQuotaData',\r\n                    contextName: 'actuarial'\r\n                });\r\n            Vue.gvUtil.http.post(url, actionVo).then(function (res) {\r\n                if (res.resCode === '0000') {\r\n                    var obj =  res.resData.data;\r\n                    var objKeys = Object.keys(obj)\r\n                    if (Vue.gvUtil.isEmpty(obj) || objKeys.length==0) {\r\n                        actionVo.dimensionValue = _this.licAction.portfolioNo;\r\n                        Vue.gvUtil.http.post(url, actionVo).then(function (res) {\r\n                            if (res.resCode === '0000') {\r\n                                obj =  res.resData.data;\r\n                                _this.initQuotaTableAndData(obj);\r\n                            }\r\n                        });\r\n                    } else {\r\n                        _this.initQuotaTableAndData(obj);\r\n                    }\r\n                }\r\n            });\r\n        },\r\n        initQuotaTableAndData: function (obj) {\r\n            var data = {};\r\n            var _this = this;\r\n            var objKeys = Object.keys(obj);\r\n            \r\n            // 准备表格结构 - 第一行和第二行的表头结构\r\n            var firstRowFields = [];\r\n            var secondRowFields = [];\r\n            \r\n            // 第一列显示险种代码\r\n            firstRowFields.push({\r\n                prop: 'insuranceType',\r\n                labelKey: 'atrInsuranceClass',\r\n                width: \"120px\",\r\n                rowspan: 2, // 第一列需要跨越两行\r\n                headerAlign: 'center',\r\n                headColor: _this.palettes[0]\r\n            });\r\n            \r\n            // 创建只包含当前险类的 bussQuotaVoList\r\n            var row = {\r\n                insuranceType: this.form.riskClassCode || '' // 显示当前险类代码\r\n            };\r\n            \r\n            // 处理API返回的一般假设和事故年月等分组\r\n            for (var i = 0; i < objKeys.length; i++) {\r\n                var quota = obj[objKeys[i]];\r\n                var children = [];  // 存储该分组下的所有指标\r\n                \r\n                // 如果没有子项，跳过\r\n                if (!quota.childQuota) continue;\r\n                \r\n                // 获取该组下所有的不同指标代码（非险种），如\"#lic_ulae_ratio\"和\"#lic_ra_ratio\"\r\n                var uniqueBaseCodes = new Set();\r\n                var childQuota = quota.childQuota;\r\n                var childKeys = Object.keys(childQuota);\r\n                \r\n                for (var j = 0; j < childKeys.length; j++) {\r\n                    var childItem = childQuota[childKeys[j]];\r\n                    // 从完整代码中提取基础指标代码，例如从\"#lic_ulae_ratio01\"提取\"#lic_ulae_ratio\"\r\n                    var baseCode = childItem.quotaCode;\r\n                    uniqueBaseCodes.add(baseCode);\r\n                }\r\n                \r\n                // 将Set转换为数组\r\n                var baseCodes = Array.from(uniqueBaseCodes);\r\n                \r\n                // 为每个指标创建一个表头列\r\n                for (var j = 0; j < baseCodes.length; j++) {\r\n                    var baseCode = baseCodes[j];\r\n                    \r\n                    // 找到第一个匹配此指标的项目，用于获取名称\r\n                    var sampleItem = null;\r\n                    for (var k = 0; k < childKeys.length; k++) {\r\n                        if (childQuota[childKeys[k]].quotaCode === baseCode) {\r\n                            sampleItem = childQuota[childKeys[k]];\r\n                            break;\r\n                        }\r\n                    }\r\n                    \r\n                    if (!sampleItem) continue;\r\n                    \r\n                    // 添加第二行表头字段（指标名称）\r\n                    var fieldObj = {\r\n                        prop: baseCode,\r\n                        labelKey: Vue.gvUtil.getInzName(\r\n                            sampleItem.quotaEName,\r\n                            sampleItem.quotaCName,\r\n                            sampleItem.quotaLName\r\n                        ),\r\n                        headColor: _this.palettes[i % 2],\r\n                        className: _this.rowPalettes[i % 2],\r\n                        headerAlign: 'center',\r\n                        fieldType: sampleItem.quotaValueType,\r\n                        quotaType: sampleItem.quotaType, // 添加quotaType属性用于判断是否是发展期指标\r\n                        width: \"120px\"\r\n                    };\r\n                    \r\n                    // 查找当前险类的值\r\n                    var currentRiskClassCode = this.form.riskClassCode;\r\n                    var fullFieldName = baseCode + currentRiskClassCode;\r\n                    \r\n                    // 检查该险类的这个指标是否存在\r\n                    if (childQuota[fullFieldName]) {\r\n                        // 如果存在，添加到行数据中\r\n                        row[baseCode] = childQuota[fullFieldName].value;\r\n                    }\r\n                    \r\n                    secondRowFields.push(fieldObj);\r\n                    children.push(fieldObj);\r\n                }\r\n                \r\n                // 创建第一行表头的分组项\r\n                var groupField = {\r\n                    prop: objKeys[i],\r\n                    labelKey: Vue.gvUtil.getInzName(\r\n                        quota.quotaEName,\r\n                        quota.quotaCName,\r\n                        quota.quotaLName\r\n                    ),\r\n                    headerAlign: 'center',\r\n                    headColor: _this.palettes[i % 2],\r\n                    colspan: children.length, // 子项数量\r\n                    children: children\r\n                };\r\n                \r\n                if (children.length > 0) {\r\n                    firstRowFields.push(groupField);\r\n                }\r\n            }\r\n            \r\n            // 设置表头结构\r\n            _this.quotaTable.fields = [];\r\n            _this.quotaTable.firstRowFields = firstRowFields;\r\n            _this.quotaTable.secondRowFields = secondRowFields;\r\n            \r\n            // 添加行数据\r\n            _this.bussQuotaVoList = [row];\r\n            \r\n            // 保存原始数据以便后续使用\r\n            _this.quotaObj = obj;\r\n            _this.initTableHeader();\r\n        },\r\n        \r\n        // 从数据中提取所有险种代码\r\n        extractRiskClassCodes: function(obj) {\r\n            var riskClasses = [];\r\n            var codePattern = /(\\d+)$/; // 匹配字段名末尾的数字\r\n            \r\n            for (var groupKey in obj) {\r\n                var group = obj[groupKey];\r\n                if (!group.childQuota) continue;\r\n                \r\n                for (var fieldKey in group.childQuota) {\r\n                    var matches = fieldKey.match(codePattern);\r\n                    if (matches && matches[1]) {\r\n                        var riskClass = matches[1];\r\n                        if (riskClasses.indexOf(riskClass) === -1) {\r\n                            riskClasses.push(riskClass);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            \r\n            return riskClasses;\r\n        },\r\n\r\n        //初始化合同组输出表头\r\n        initTableHeader: function (){\r\n            var _this = this;\r\n            \r\n            // 添加自定义样式\r\n            var style = document.createElement('style');\r\n            style.innerHTML = `\r\n                .custom-table {\r\n                    width: 100%;\r\n                    border-collapse: collapse;\r\n                    margin-bottom: 20px;\r\n                }\r\n                .custom-table th, .custom-table td {\r\n                    border: 1px solid #dfe6ec;\r\n                    padding: 8px;\r\n                }\r\n                .custom-table thead th {\r\n                    background-color: #f5f7fa;\r\n                    font-weight: bold;\r\n                    color: #606266;\r\n                }\r\n                .custom-table tbody tr:hover {\r\n                    background-color: #f5f7fa;\r\n                }\r\n                .purple-to-gray {\r\n                    background-color: #ECE9F7;\r\n                }\r\n                .light-gray {\r\n                    background-color: #F5F7FA;\r\n                }\r\n            `;\r\n            document.head.appendChild(style);\r\n            \r\n            // 自定义表格渲染\r\n            _this.quotaTable.basic.custom = true;\r\n            \r\n            _this.loading = true;\r\n        },\r\n        //查看发展期指标数据\r\n        onViewQuotaDevelopPeriod: function (row, value, prop) {\r\n            var quotaDef;\r\n            var objKeys = Object.keys(this.quotaObj);\r\n            \r\n            // 构建完整的字段名称，例如 \"#lic_ulae_ratio01\"\r\n            var fullFieldName = prop + this.form.riskClassCode;\r\n            \r\n            // 在表格数据中查找匹配的指标\r\n            for (var i = 0; i < objKeys.length; i++) {\r\n                var childQuota = this.quotaObj[objKeys[i]].childQuota;\r\n                if (!childQuota) continue;\r\n                \r\n                // 直接通过完整字段名查找\r\n                if (childQuota[fullFieldName]) {\r\n                    quotaDef = childQuota[fullFieldName];\r\n                    break;\r\n                } else if (childQuota[prop]) {\r\n                    // 回退到只使用基础指标代码\r\n                    quotaDef = childQuota[prop];\r\n                    break;\r\n                }\r\n            }\r\n            \r\n            if (quotaDef) {\r\n                this.quotaDtl.quota = quotaDef;\r\n                this.findBussQuotaPeriod(prop);\r\n            } else {\r\n                console.error('未找到匹配的指标定义', fullFieldName);\r\n                Vue.gvUtil.message(Vue.gvUtil.getInzTranslate('gError'));\r\n            }\r\n        },\r\n        //关闭指标发展期框\r\n        onFold: function () {\r\n            this.periodShow = false;\r\n        },\r\n\r\n        //查询计量假设配置发展期数据\r\n        findBussQuotaPeriod: function (quotaCode) {\r\n            this.quotaDefName = Vue.gvUtil.getInzName(this.quotaDtl.quota.quotaEName, this.quotaDtl.quota.quotaCName, this.quotaDtl.quota.quotaLName);\r\n            this.periodShow = true;   // 显示页面\r\n            \r\n            // 显示加载指示器或处理逻辑\r\n            this.loading = true;\r\n            \r\n            var param = {\r\n                actionNo : this.licAction.actionNo,\r\n                dimensionValue : this.licAction.icgNo,\r\n                quotaCode: quotaCode,\r\n                riskClassCode: this.form.riskClassCode\r\n            };\r\n            \r\n            var _this = this;\r\n            var url = Vue.gvUtil.getUrl({\r\n                apiName: 'findAtrBussQuotaDataDetail',\r\n                contextName: 'actuarial'\r\n            });\r\n            \r\n            Vue.gvUtil.http.post(url, param).then(function (res) {\r\n                if (res.resCode === '0000' && res.resData) {\r\n                    _this.bussQuotaDevelopVoList = [];\r\n                    var obj = res.resData;\r\n                    var objKeys = Object.keys(obj);\r\n                    \r\n                    if (Vue.gvUtil.isEmpty(obj) || objKeys.length == 0) {\r\n                        param.dimensionValue = _this.licAction.portfolioNo;\r\n                        Vue.gvUtil.http.post(url, param).then(function (res) {\r\n                            if (res.resCode === '0000') {\r\n                                obj = res.resData;\r\n                                _this.initQuotaDetailTableAndData(obj);\r\n                            }\r\n                        }).finally(function() {\r\n                            _this.loading = false;\r\n                        });\r\n                    } else {\r\n                        _this.initQuotaDetailTableAndData(obj);\r\n                        _this.loading = false;\r\n                    }\r\n                } else {\r\n                    _this.loading = false;\r\n                }\r\n            }).catch(function() {\r\n                _this.loading = false;\r\n            }).finally(function() {\r\n                _this.periodShow = true;\r\n            });\r\n        },\r\n\r\n        // 更新发展期数据处理方法\r\n        initQuotaDetailTableAndData: function (obj) {\r\n            var _this = this;\r\n            \r\n            // 清空当前数据\r\n            _this.bussQuotaDevelopVoList = [];\r\n            _this.developmentColumns = [];\r\n            \r\n            if (!obj) return;\r\n            \r\n            var objKeys = Object.keys(obj);\r\n            if (objKeys.length === 0) return;\r\n            \r\n            // 创建列配置\r\n            for (var i = 0; i < objKeys.length; i++) {\r\n                _this.developmentColumns.push({\r\n                    prop: objKeys[i],\r\n                    label: objKeys[i]\r\n                });\r\n            }\r\n            \r\n            // 创建行数据\r\n            var rowData = {\r\n                quotaPeriod: _this.quotaDefName || '发展期'\r\n            };\r\n            \r\n            // 填充行数据\r\n            for (var i = 0; i < objKeys.length; i++) {\r\n                var key = objKeys[i];\r\n                if (obj[key] && !Vue.gvUtil.isEmpty(obj[key].quotaValue)) {\r\n                    rowData[key] = obj[key].quotaValue; // 使用原始值，由模板负责格式化\r\n                } else {\r\n                    rowData[key] = '';\r\n                }\r\n            }\r\n            \r\n            // 添加行数据\r\n            _this.bussQuotaDevelopVoList.push(rowData);\r\n        },\r\n\r\n        handleClick: function (tab, event) {\r\n            this.$nextTick(() => {\r\n                this.searchList(tab.name);\r\n            })\r\n        },\r\n\r\n        getParamsMixin: function getParamsMixin(params) {\r\n            this.cacheFilters = Object.assign({\r\n                _pageSize: this.mixinObject.searchSet.pageSize,\r\n                _pageNo: this.mixinObject.searchSet.pageNo\r\n            }, params);\r\n            return this.cacheFilters;\r\n        },\r\n\r\n        /**\r\n         * 页码变动\r\n         * @param val 码数\r\n         */\r\n        onHandleCurrentChange: function onHandleCurrentChange(val) {\r\n            if (typeof val === 'undefined') {\r\n                return;\r\n            }\r\n            this.mixinObject.searchSet.pageNo = val - 1;\r\n            this.mixinObject.isInit = true;\r\n            this.searchList('uprDetailsVoList');\r\n        },\r\n\r\n        /**\r\n         * 查询行数变动\r\n         * @param 行数\r\n         */\r\n        onHandleSizeChange: function onHandleSizeChange(val) {\r\n            this.mixinObject.searchSet.pageSize = val;\r\n            this.mixinObject.isInit = true;\r\n            this.searchList('uprDetailsVoList');\r\n        },\r\n\r\n        /**\r\n         * 获取查询数据\r\n         */\r\n        searchList: function searchList(tabName) {\r\n            tabName = Vue.gvUtil.isEmpty(this.lrcLicTab)? tabName : this.lrcLicTab;\r\n            var urlParams={\r\n                actionNo: this.form.actionNo,\r\n                icgNo: this.form.icgNo,\r\n                businessSourceCode:this.licAction.businessSourceCode,\r\n                riskClassCode: this.form.riskClassCode,\r\n                feeType: tabName,\r\n            };\r\n            var voName= 'lrcCashFlowVoList';\r\n            if (!this.mixinObject.isInit) {\r\n                this.mixinObject.searchSet.pageNo = 0;\r\n                this.mixinObject.searchSet.currentPage = 1;\r\n            } else {\r\n                this.mixinObject.isInit = false;\r\n            }\r\n            var params = this.getParamsMixin(),\r\n                url = Vue.gvUtil.getUrl({\r\n                    apiName: 'findLrcDataDetail',\r\n                    contextName: 'actuarial',\r\n                    serachParms: { _pageSize: params._pageSize, _pageNo: params._pageNo }\r\n                }),\r\n                _this = this,\r\n                list = [];\r\n            Vue.gvUtil.http.post(url, urlParams).then(function (res) {\r\n                if (res.resCode === '0000') {\r\n                    _this.uprDetailsVoList = res.resData[voName].content;\r\n                    _this.$set(_this.tabMap[tabName],'list',res.resData[voName].content)\r\n                    _this.mixinObject.searchSet.total = res.resData[voName]['total'] ? res.resData[voName].total : res.resData[voName].totalElements;\r\n                    _this.lrcLicTab = tabName;\r\n                } else {\r\n                    _this.mixinObject.searchSet.total = 0;\r\n                    _this.lrcLicTab = tabName;\r\n                }\r\n            });\r\n        },\r\n\r\n        handleSetList(list){\r\n            let tableObj = this.tabMap[this.lrcLicTab]\r\n            this.$set(tableObj,'list',list)\r\n            this.$set(this.tabMap,this.lrcLicTab,tableObj)\r\n\r\n        },\r\n    },\r\n    mounted() {\r\n        this.$nextTick(() => {\r\n            this.showRiskClassCode = this.licAction && (this.licAction.businessSourceCode === 'DD' || this.licAction.businessSourceCode === 'TI');\r\n        });\r\n    },\r\n}\r\n\r\n</script>"]}]}