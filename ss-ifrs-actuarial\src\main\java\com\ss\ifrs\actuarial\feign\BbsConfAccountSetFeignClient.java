package com.ss.ifrs.actuarial.feign;

import com.ss.platform.pojo.bbs.vo.BbsConfAccountSetVo;
import com.ss.platform.core.conf.FeignAuthConfig;
import com.ss.platform.pojo.base.po.BaseResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * 调用会计引擎的接口
 *
 * <AUTHOR>
 */
@FeignClient(name = "SS-PLATFORM-COMMON", configuration = { FeignAuthConfig.class })
public interface BbsConfAccountSetFeignClient {

    @ApiOperation(value = "根据业务单位ID查找数据")
    @RequestMapping(value = "/bbs_account_set/find_by_entityId/{entityId}", method = RequestMethod.GET)
    BaseResponse<BbsConfAccountSetVo> findByCenterId(@PathVariable("entityId") Long centerId);
}
