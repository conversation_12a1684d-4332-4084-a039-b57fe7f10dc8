/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-03-09 10:18:30
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2021-03-09 10:18:30<br/>
 * Description: LIC计量数据主表<br/>
 * Table Name: ATR_BUSS_LIC_MAIN<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "LIC计量数据主表")
public class AtrBussCalcMain implements Serializable {
    /**
     * Database column: ATR_BUSS_LIC_MAIN.LIC_MAIN_ID
     * Database remarks: lic_main_id|主键
     */
    @ApiModelProperty(value = "lic_main_id|主键", required = true)
    private Long licMainId;

    /**
     * Database column: ATR_BUSS_LIC_MAIN.CENTER_ID
     * Database remarks: Center_Id|核算单位ID
     */
    @ApiModelProperty(value = "Center_Id|核算单位ID", required = false)
    private Long entityId;

    /**
     * Database column: ATR_BUSS_LIC_MAIN.YEAR_MONTH
     * Database remarks: year_month|计量月份
     */
    @ApiModelProperty(value = "year_month|计量月份", required = false)
    private String yearMonth;

    /**
     * Database column: ATR_BUSS_LIC_MAIN.DATA_SOURCE
     * Database remarks: data_source|数据来源
     */
    @ApiModelProperty(value = "data_source|数据来源", required = false)
    private String dataSource;

    /**
     * Database column: ATR_BUSS_LIC_MAIN.RISK_CLASS
     * Database remarks: risk_class|险类
     */
    @ApiModelProperty(value = "risk_class|险类", required = false)
    private String riskClassCode;

    /**
     * Database column: ATR_BUSS_LIC_MAIN.CURRENCY
     * Database remarks: currency|币种
     */
    @ApiModelProperty(value = "currency|币种", required = false)
    private String currencyCode;

    /**
     * Database column: ATR_BUSS_LIC_MAIN.DIMENSION
     * Database remarks: trace_section|追溯区间
     */
    @ApiModelProperty(value = "trace_section|追溯区间", required = false)
    private String dimension;

    /**
     * Database column: ATR_BUSS_LIC_MAIN.SECTION
     * Database remarks: section_num|追溯区间数
     */
    @ApiModelProperty(value = "section_num|追溯区间数", required = false)
    private Short section;

    /**
     * Database column: ATR_BUSS_LIC_MAIN.DRAW_TIME
     * Database remarks: draw_time|提数时间
     */
    @ApiModelProperty(value = "draw_time|提数时间", required = false)
    private Date drawTime;

    /**
     * Database column: ATR_BUSS_LIC_MAIN.VERSION_NO
     * Database remarks: version_no|版本
     */
    @ApiModelProperty(value = "version_no|版本", required = false)
    private String versionNo;

    /**
     * Database column: ATR_BUSS_LIC_MAIN.EXECUTE_TIME
     * Database remarks: execute_time|执行时间
     */
    @ApiModelProperty(value = "execute_time|执行时间", required = false)
    private Date executeTime;

    /**
     * Database column: ATR_BUSS_LIC_MAIN.EXECUTOR_ID
     * Database remarks: executor_id|执行人
     */
    @ApiModelProperty(value = "executor_id|执行人", required = false)
    private Long executorId;

    /**
     * Database column: ATR_BUSS_LIC_MAIN.STATE
     * Database remarks: state|状态
     */
    @ApiModelProperty(value = "state|状态", required = false)
    private String state;

    /**
     * Database column: ATR_BUSS_LIC_MAIN.IS_CONFIRM
     * Database remarks: is_confirm|是否确认
     */
    @ApiModelProperty(value = "is_confirm|是否确认", required = false)
    private String isConfirm;

    /**
     * Database column: ATR_BUSS_LIC_MAIN.CONFIRM_ID
     * Database remarks: confirm_id|确认人
     */
    @ApiModelProperty(value = "confirm_id|确认人", required = false)
    private Long confirmId;

    /**
     * Database column: ATR_BUSS_LIC_MAIN.CONFIRM_TIME
     * Database remarks: confirm_time|确认时间
     */
    @ApiModelProperty(value = "confirm_time|确认时间", required = false)
    private Date confirmTime;

    /**
     * Database column: ATR_BUSS_LIC_MAIN.CREATOR_ID
     * Database remarks: Creator_Id|创建人
     */
    @ApiModelProperty(value = "Creator_Id|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: ATR_BUSS_LIC_MAIN.CREATE_TIME
     * Database remarks: Create_Time|创建时间
     */
    @ApiModelProperty(value = "Create_Time|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: ATR_BUSS_LIC_MAIN.UPDATOR_ID
     * Database remarks: Updator_Id|最后修改人
     */
    @ApiModelProperty(value = "Updator_Id|最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: ATR_BUSS_LIC_MAIN.UPDATE_TIME
     * Database remarks: Update_Time|最后修改时间
     */
    @ApiModelProperty(value = "Update_Time|最后修改时间", required = false)
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public Long getLicMainId() {
        return licMainId;
    }

    public void setLicMainId(Long licMainId) {
        this.licMainId = licMainId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }	

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public Short getSection() {
        return section;
    }

    public void setSection(Short section) {
        this.section = section;
    }

    public Date getDrawTime() {
        return drawTime;
    }

    public void setDrawTime(Date drawTime) {
        this.drawTime = drawTime;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

    public Date getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(Date executeTime) {
        this.executeTime = executeTime;
    }

    public Long getExecutorId() {
        return executorId;
    }

    public void setExecutorId(Long executorId) {
        this.executorId = executorId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getIsConfirm() {
        return isConfirm;
    }

    public void setIsConfirm(String isConfirm) {
        this.isConfirm = isConfirm;
    }

    public Long getConfirmId() {
        return confirmId;
    }

    public void setConfirmId(Long confirmId) {
        this.confirmId = confirmId;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}