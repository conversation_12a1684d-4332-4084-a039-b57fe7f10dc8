<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2022-07-05 11:22:36 -->
<!-- Copyright (c) 2017-2027,  All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.reserve.AtrBussReserveIbnrDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveIbnr">
    <id column="reserve_ibnr_id" property="reserveIbnrId" jdbcType="NUMERIC" />
    <result column="draw_time" property="drawTime" jdbcType="TIMESTAMP" />
    <result column="draw_status" property="drawStatus" jdbcType="VARCHAR" />
    <result column="task_code" property="taskCode" jdbcType="VARCHAR" />
    <result column="draw_user" property="drawUser" jdbcType="NUMERIC" />
    <result column="draw_type" property="drawType" jdbcType="VARCHAR" />
    <result column="entity_id" property="entityId" jdbcType="NUMERIC" />
    <result column="loa_code" property="loaCode" jdbcType="VARCHAR" />
    <result column="CURRENCY_CODE" property="currencyCode" jdbcType="VARCHAR" />
    <result column="ibnr_type" property="ibnrType" jdbcType="VARCHAR" />
    <result column="business_source_code" property="businessSourceCode" jdbcType="VARCHAR" />
    <result column="statis_zones" property="statisZones" jdbcType="NUMERIC" />
    <result column="end_date" property="endDate" jdbcType="TIMESTAMP" />
    <result column="confirm_is" property="confirmIs" jdbcType="CHAR" />
    <result column="confirm_id" property="confirmId" jdbcType="NUMERIC" />
    <result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="creator_id" property="creatorId" jdbcType="NUMERIC" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="updator_id" property="updatorId" jdbcType="NUMERIC" />
    <result column="extraction_method" property="extractionMethod" jdbcType="VARCHAR" />
    <result column="extract_interval" property="extractInterval" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    reserve_ibnr_id, draw_time, draw_status, task_code, draw_user, draw_type, entity_id,
    loa_code, CURRENCY_CODE, ibnr_type, business_source_code, statis_zones, end_date, confirm_is,
    confirm_id, confirm_time, create_time, creator_id, update_time, updator_id, extraction_method, extract_interval
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="reserveIbnrId != null ">
        and reserve_ibnr_id = #{reserveIbnrId,jdbcType=NUMERIC}
      </if>
      <if test="drawTime != null ">
        and draw_time = #{drawTime,jdbcType=TIMESTAMP}
      </if>
      <if test="drawStatus != null and drawStatus != ''">
        and draw_status = #{drawStatus,jdbcType=VARCHAR}
      </if>
      <if test="taskCode != null and taskCode != ''">
        and task_code = #{taskCode,jdbcType=VARCHAR}
      </if>
      <if test="drawUser != null ">
        and draw_user = #{drawUser,jdbcType=NUMERIC}
      </if>
      <if test="drawType != null and drawType != ''">
        and draw_type = #{drawType,jdbcType=VARCHAR}
      </if>
      <if test="entityId != null ">
        and entity_id = #{entityId,jdbcType=NUMERIC}
      </if>
      <if test="loaCode != null and loaCode != ''">
        and loa_code = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="currencyCode != null and currencyCode != ''">
        and CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="ibnrType != null and ibnrType != ''">
        and ibnr_type = #{ibnrType,jdbcType=VARCHAR}
      </if>
      <if test="businessSourceCode != null and businessSourceCode != ''">
        and business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="statisZones != null ">
        and statis_zones = #{statisZones,jdbcType=NUMERIC}
      </if>
      <if test="endDate != null ">
        and end_date = #{endDate,jdbcType=TIMESTAMP}
      </if>
      <if test="confirmIs != null ">
        and confirm_is = #{confirmIs,jdbcType=CHAR}
      </if>
      <if test="confirmId != null ">
        and confirm_id = #{confirmId,jdbcType=NUMERIC}
      </if>
      <if test="confirmTime != null ">
        and confirm_time = #{confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="createTime != null ">
        and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creatorId != null ">
        and creator_id = #{creatorId,jdbcType=NUMERIC}
      </if>
      <if test="updateTime != null ">
        and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
        and updator_id = #{updatorId,jdbcType=NUMERIC}
      </if>
      <if test="extractionMethod != null and extractionMethod != ''">
        and extraction_method = #{extractionMethod,jdbcType=VARCHAR}
      </if>
      <if test="extractInterval != null and extractInterval != ''">
        and extract_interval = #{extractInterval,jdbcType=VARCHAR}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.reserveIbnrId != null ">
        and reserve_ibnr_id = #{condition.reserveIbnrId,jdbcType=NUMERIC}
      </if>
      <if test="condition.drawTime != null ">
        and draw_time = #{condition.drawTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.drawStatus != null and condition.drawStatus != ''">
        and draw_status = #{condition.drawStatus,jdbcType=VARCHAR}
      </if>
      <if test="condition.taskCode != null and condition.taskCode != ''">
        and task_code = #{condition.taskCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.drawUser != null ">
        and draw_user = #{condition.drawUser,jdbcType=NUMERIC}
      </if>
      <if test="condition.drawType != null and condition.drawType != ''">
        and draw_type = #{condition.drawType,jdbcType=VARCHAR}
      </if>
      <if test="condition.entityId != null ">
        and entity_id = #{condition.entityId,jdbcType=NUMERIC}
      </if>
      <if test="condition.loaCode != null and condition.loaCode != ''">
        and loa_code = #{condition.loaCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.currencyCode != null and condition.currencyCode != ''">
        and CURRENCY_CODE = #{condition.currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.ibnrType != null and condition.ibnrType != ''">
        and ibnr_type = #{condition.ibnrType,jdbcType=VARCHAR}
      </if>
      <if test="condition.businessSourceCode != null and condition.businessSourceCode != ''">
        and business_source_code = #{condition.businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.statisZones != null ">
        and statis_zones = #{condition.statisZones,jdbcType=NUMERIC}
      </if>
      <if test="condition.endDate != null ">
        and end_date = #{condition.endDate,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.confirmIs != null ">
        and confirm_is = #{condition.confirmIs,jdbcType=CHAR}
      </if>
      <if test="condition.confirmId != null ">
        and confirm_id = #{condition.confirmId,jdbcType=NUMERIC}
      </if>
      <if test="condition.confirmTime != null ">
        and confirm_time = #{condition.confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.createTime != null ">
        and create_time = #{condition.createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.creatorId != null ">
        and creator_id = #{condition.creatorId,jdbcType=NUMERIC}
      </if>
      <if test="condition.updateTime != null ">
        and update_time = #{condition.updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.updatorId != null ">
        and updator_id = #{condition.updatorId,jdbcType=NUMERIC}
      </if>
      <if test="condition.extractionMethod != null">
        and extraction_method = #{condition.extractionMethod,jdbcType=VARCHAR}
      </if>
      <if test="condition.extractInterval != null">
        and extract_interval = #{condition.extractInterval,jdbcType=VARCHAR}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="reserveIbnrId != null ">
        and reserve_ibnr_id = #{reserveIbnrId,jdbcType=NUMERIC}
      </if>
      <if test="drawTime != null ">
        and draw_time = #{drawTime,jdbcType=TIMESTAMP}
      </if>
      <if test="drawStatus != null and drawStatus != ''">
        and draw_status = #{drawStatus,jdbcType=VARCHAR}
      </if>
      <if test="taskCode != null and taskCode != ''">
        and task_code = #{taskCode,jdbcType=VARCHAR}
      </if>
      <if test="drawUser != null ">
        and draw_user = #{drawUser,jdbcType=NUMERIC}
      </if>
      <if test="drawType != null and drawType != ''">
        and draw_type = #{drawType,jdbcType=VARCHAR}
      </if>
      <if test="entityId != null ">
        and entity_id = #{entityId,jdbcType=NUMERIC}
      </if>
      <if test="loaCode != null and loaCode != ''">
        and loa_code = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="currencyCode != null and currencyCode != ''">
        and CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="ibnrType != null and ibnrType != ''">
        and ibnr_type = #{ibnrType,jdbcType=VARCHAR}
      </if>
      <if test="businessSourceCode != null and businessSourceCode != ''">
        and business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="statisZones != null ">
        and statis_zones = #{statisZones,jdbcType=NUMERIC}
      </if>
      <if test="endDate != null ">
        and end_date = #{endDate,jdbcType=TIMESTAMP}
      </if>
      <if test="confirmIs != null ">
        and confirm_is = #{confirmIs,jdbcType=CHAR}
      </if>
      <if test="confirmId != null ">
        and confirm_id = #{confirmId,jdbcType=NUMERIC}
      </if>
      <if test="confirmTime != null ">
        and confirm_time = #{confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="createTime != null ">
        and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creatorId != null ">
        and creator_id = #{creatorId,jdbcType=NUMERIC}
      </if>
      <if test="updateTime != null ">
        and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
        and updator_id = #{updatorId,jdbcType=NUMERIC}
      </if>
      <if test="extractionMethod != null">
        and extraction_method = #{extractionMethod,jdbcType=VARCHAR}
      </if>
      <if test="extractInterval != null">
        and extract_interval = #{extractInterval,jdbcType=VARCHAR}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List" />
    from atr_buss_reserve_ibnr
    where reserve_ibnr_id = #{reserveIbnrId,jdbcType=NUMERIC}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select
    <include refid="Base_Column_List" />
    from atr_buss_reserve_ibnr
    where reserve_ibnr_id in
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from atr_buss_reserve_ibnr
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveIbnr">
    select
    <include refid="Base_Column_List" />
    from atr_buss_reserve_ibnr
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select
    <include refid="Base_Column_List" />
    from atr_buss_reserve_ibnr
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="reserve_ibnr_id" keyProperty="reserveIbnrId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveIbnr">
    <selectKey resultType="long" keyProperty="reserveIbnrId" order="BEFORE">
      select nextval('ATR_SEQ_BUSS_RESERVE_IBNR') as sequenceNo 
    </selectKey>
    insert into atr_buss_reserve_ibnr
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="reserveIbnrId != null">
        reserve_ibnr_id,
      </if>
      <if test="drawTime != null">
        draw_time,
      </if>
      <if test="drawStatus != null">
        draw_status,
      </if>
      <if test="taskCode != null">
        task_code,
      </if>
      <if test="drawUser != null">
        draw_user,
      </if>
      <if test="drawType != null">
        draw_type,
      </if>
      <if test="entityId != null">
        entity_id,
      </if>
      <if test="loaCode != null">
        loa_code,
      </if>
      <if test="currencyCode != null">
        CURRENCY_CODE,
      </if>
      <if test="ibnrType != null">
        ibnr_type,
      </if>
      <if test="businessSourceCode != null">
        business_source_code,
      </if>
      <if test="statisZones != null">
        statis_zones,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="confirmIs != null">
        confirm_is,
      </if>
      <if test="confirmId != null">
        confirm_id,
      </if>
      <if test="confirmTime != null">
        confirm_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updatorId != null">
        updator_id,
      </if>
      <if test="extractionMethod != null">
        extraction_method,
      </if>
      <if test="extractInterval != null">
        extract_interval,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="reserveIbnrId != null">
        #{reserveIbnrId,jdbcType=NUMERIC},
      </if>
      <if test="drawTime != null">
        #{drawTime,jdbcType=TIMESTAMP},
      </if>
      <if test="drawStatus != null">
        #{drawStatus,jdbcType=VARCHAR},
      </if>
      <if test="taskCode != null">
        #{taskCode,jdbcType=VARCHAR},
      </if>
      <if test="drawUser != null">
        #{drawUser,jdbcType=NUMERIC},
      </if>
      <if test="drawType != null">
        #{drawType,jdbcType=VARCHAR},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=NUMERIC},
      </if>
      <if test="loaCode != null">
        #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="currencyCode != null">
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="ibnrType != null">
        #{ibnrType,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceCode != null">
        #{businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="statisZones != null">
        #{statisZones,jdbcType=NUMERIC},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmIs != null">
        #{confirmIs,jdbcType=CHAR},
      </if>
      <if test="confirmId != null">
        #{confirmId,jdbcType=NUMERIC},
      </if>
      <if test="confirmTime != null">
        #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=NUMERIC},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        #{updatorId,jdbcType=NUMERIC},
      </if>
      <if test="extractionMethod != null">
        #{extractionMethod,jdbcType=VARCHAR},
      </if>
      <if test="extractInterval != null">
        #{extractInterval,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert into atr_buss_reserve_ibnr
    (reserve_ibnr_id, draw_time, draw_status,
    task_code, draw_user, draw_type,
    entity_id, loa_code, CURRENCY_CODE,
    ibnr_type, business_source_code, statis_zones,
    end_date, confirm_is, confirm_id,
    confirm_time, create_time,
    creator_id, update_time, updator_id,extraction_method,extract_interval
    )
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.reserveIbnrId,jdbcType=NUMERIC}, #{item.drawTime,jdbcType=TIMESTAMP}, #{item.drawStatus,jdbcType=VARCHAR},
      #{item.taskCode,jdbcType=VARCHAR}, #{item.drawUser,jdbcType=NUMERIC}, #{item.drawType,jdbcType=VARCHAR},
      #{item.entityId,jdbcType=NUMERIC}, #{item.loaCode,jdbcType=VARCHAR}, #{item.currencyCode,jdbcType=VARCHAR},
      #{item.ibnrType,jdbcType=VARCHAR}, #{item.businessSourceCode,jdbcType=VARCHAR}, #{item.statisZones,jdbcType=NUMERIC},
      #{item.endDate,jdbcType=TIMESTAMP}, #{item.confirmIs,jdbcType=CHAR}, #{item.confirmId,jdbcType=NUMERIC},
      #{item.confirmTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP},
      #{item.creatorId,jdbcType=NUMERIC}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updatorId,jdbcType=NUMERIC},
      #{item.extractionMethod,jdbcType=VARCHAR}, #{item.extractInterval,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveIbnr">
    update atr_buss_reserve_ibnr
    <set>
      <if test="drawTime != null">
        draw_time = #{drawTime,jdbcType=TIMESTAMP},
      </if>
      <if test="drawStatus != null">
        draw_status = #{drawStatus,jdbcType=VARCHAR},
      </if>
      <if test="taskCode != null">
        task_code = #{taskCode,jdbcType=VARCHAR},
      </if>
      <if test="drawUser != null">
        draw_user = #{drawUser,jdbcType=NUMERIC},
      </if>
      <if test="drawType != null">
        draw_type = #{drawType,jdbcType=VARCHAR},
      </if>
      <if test="entityId != null">
        entity_id = #{entityId,jdbcType=NUMERIC},
      </if>
      <if test="loaCode != null">
        loa_code = #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="currencyCode != null">
        CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="ibnrType != null">
        ibnr_type = #{ibnrType,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceCode != null">
        business_source_code = #{businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="statisZones != null">
        statis_zones = #{statisZones,jdbcType=NUMERIC},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmIs != null">
        confirm_is = #{confirmIs,jdbcType=CHAR},
      </if>
      <if test="confirmId != null">
        confirm_id = #{confirmId,jdbcType=NUMERIC},
      </if>
      <if test="confirmTime != null">
        confirm_time = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=NUMERIC},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        updator_id = #{updatorId,jdbcType=NUMERIC},
      </if>
      <if test="extractionMethod != null">
        extraction_method = #{extractionMethod,jdbcType=VARCHAR},
      </if>
      <if test="extractInterval != null">
        extract_interval = #{extractInterval,jdbcType=VARCHAR},
      </if>
    </set>
    where reserve_ibnr_id = #{reserveIbnrId,jdbcType=NUMERIC}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveIbnr">
    update atr_buss_reserve_ibnr
    <set>
      <if test="record.drawTime != null">
        draw_time = #{record.drawTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.drawStatus != null">
        draw_status = #{record.drawStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.taskCode != null">
        task_code = #{record.taskCode,jdbcType=VARCHAR},
      </if>
      <if test="record.drawUser != null">
        draw_user = #{record.drawUser,jdbcType=NUMERIC},
      </if>
      <if test="record.drawType != null">
        draw_type = #{record.drawType,jdbcType=VARCHAR},
      </if>
      <if test="record.entityId != null">
        entity_id = #{record.entityId,jdbcType=NUMERIC},
      </if>
      <if test="record.loaCode != null">
        loa_code = #{record.loaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.currencyCode != null">
        CURRENCY_CODE = #{record.currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.ibnrType != null">
        ibnr_type = #{record.ibnrType,jdbcType=VARCHAR},
      </if>
      <if test="record.businessSourceCode != null">
        business_source_code = #{record.businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.statisZones != null">
        statis_zones = #{record.statisZones,jdbcType=NUMERIC},
      </if>
      <if test="record.endDate != null">
        end_date = #{record.endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.confirmIs != null">
        confirm_is = #{record.confirmIs,jdbcType=CHAR},
      </if>
      <if test="record.confirmId != null">
        confirm_id = #{record.confirmId,jdbcType=NUMERIC},
      </if>
      <if test="record.confirmTime != null">
        confirm_time = #{record.confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=NUMERIC},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatorId != null">
        updator_id = #{record.updatorId,jdbcType=NUMERIC},
      </if>
      <if test="record.extractionMethod != null">
        extraction_method = #{record.extractionMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.extractInterval != null">
        extract_interval = #{record.extractInterval,jdbcType=VARCHAR},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from atr_buss_reserve_ibnr
    where reserve_ibnr_id = #{reserveIbnrId,jdbcType=NUMERIC}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from atr_buss_reserve_ibnr
    where reserve_ibnr_id in
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from atr_buss_reserve_ibnr
    where
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveIbnr">
    select count(1) from atr_buss_reserve_ibnr
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>