/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2025-03-24 19:27:36
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo;

import java.io.Serializable;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2025-03-24 19:27:36<br/>
 * Description: null<br/>
 * Table Name: atr_buss_ibnr_import_calim<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
public class AtrBussIbnrImportClaimVo implements Serializable {
    /**
     * Database column: atr_buss_ibnr_import_calim.ibnr_claim_id
     * Database remarks: null
     */
    private Long ibnrClaimId;

    /**
     * Database column: atr_buss_ibnr_import_calim.ibnr_main_id
     * Database remarks: null
     */
    private Long claimMainId;

    /**
     * Database column: atr_buss_ibnr_import_calim.treaty_no
     * Database remarks: null
     */
    private String treatyNo;

    private String treatyName;

    /**
     * Database column: atr_buss_ibnr_import_calim.policy_no
     * Database remarks: null
     */
    private String policyNo;

    /**
     * Database column: atr_buss_ibnr_import_calim.risk_kind_code
     * Database remarks: null
     */
    private String kindCode;

    /**
     * Database column: atr_buss_ibnr_import_calim.claim_no
     * Database remarks: null
     */
    private String claimNo;

    private static final long serialVersionUID = 1L;

    public Long getIbnrClaimId() {
        return ibnrClaimId;
    }

    public void setIbnrClaimId(Long ibnrClaimId) {
        this.ibnrClaimId = ibnrClaimId;
    }

    public Long getClaimMainId() {
        return claimMainId;
    }

    public void setClaimMainId(Long claimMainId) {
        this.claimMainId = claimMainId;
    }

    public String getTreatyNo() {
        return treatyNo;
    }

    public void setTreatyNo(String treatyNo) {
        this.treatyNo = treatyNo;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getKindCode() {
        return kindCode;
    }

    public void setKindCode(String kindCode) {
        this.kindCode = kindCode;
    }

    public String getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(String claimNo) {
        this.claimNo = claimNo;
    }

    public String getTreatyName() {
        return treatyName;
    }

    public void setTreatyName(String treatyName) {
        this.treatyName = treatyName;
    }
}