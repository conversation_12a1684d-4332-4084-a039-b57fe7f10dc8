/**
 *
 * This file was generated by Taiping MyBatis Generator(v1.2.12).
 * Date: 2024-07-29 18:59:47
 * Author liebin.zheng
 *
 * Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved.
 *
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by Taiping MyBatis Generator(v1.2.12).
 * Create Date: 2024-07-29 18:59:47<br/>
 * Description: IBNR-二次分摊结果输出表<br/>
 * Table Name: ATR_SECONDARY_ALLOCATION_RESULT<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "IBNR-二次分摊结果输出表")
public class AtrSecondaryAllocationResultVo implements Serializable {
    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.ID
     * Database remarks: ID|主键
     */
    @ApiModelProperty(value = "ID|主键", required = true)
    private Long id;

    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.ICG_NO
     * Database remarks: ICGNO|合同组号
     */
    @ApiModelProperty(value = "icgNo|合同组号", required = false)
    private String icgNo;

    @ApiModelProperty(value = "portfolioNo|合同组合号", required = false)
    private String portfolioNo;

    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.TREATY_NO
     * Database remarks: TREATY_NO|合约号
     */
    @ApiModelProperty(value = "TREATY_NO|合约号", required = false)
    private String treatyNo;

    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.DRAW_MONTH
     * Database remarks: 评估月份
     */
    @ApiModelProperty(value = "评估月份", required = false)
    private String drawMonth;

    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.RISK_CATAGORY
     * Database remarks: null
     */
    private String riskCatagory;

    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.COM_CODE
     * Database remarks: 机构代码
     */
    @ApiModelProperty(value = "机构代码", required = false)
    private String comCode;

    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.BUSINESS_TYPE
     * Database remarks: 业务类型1直接2分入
     */
    @ApiModelProperty(value = "业务类型1直接2分入", required = false)
    private String businessType;

    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.ACC_MONTH
     * Database remarks: null
     */
    private String accMonth;

    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.CLASS_CODE
     * Database remarks: 险类代码
     */
    @ApiModelProperty(value = "险类代码", required = false)
    private String classCode;

    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.RISK_CODE
     * Database remarks: 险种代码
     */
    @ApiModelProperty(value = "险种代码", required = false)
    private String riskCode;

    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.EP_PREMIUM
     * Database remarks: 已赚保费
     */
    @ApiModelProperty(value = "已赚保费", required = false)
    private BigDecimal epPremium;

    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.PAID_PREMIUM
     * Database remarks: 已决保费
     */
    @ApiModelProperty(value = "已决保费", required = false)
    private BigDecimal paidPremium;

    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.CASE_PREMIUM
     * Database remarks: 未决保费
     */
    @ApiModelProperty(value = "未决保费", required = false)
    private BigDecimal casePremium;

    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.IS_APPORTIONMENT
     * Database remarks: 是否参与分摊 0:不参与 1：参与
     */
    @ApiModelProperty(value = "是否参与分摊 0:不参与 1：参与", required = false)
    private String isApportionment;

    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.EP_WEIGHT
     * Database remarks: 已赚权重
     */
    @ApiModelProperty(value = "已赚权重", required = false)
    private BigDecimal epWeight;

    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.PAID_WEIGHT
     * Database remarks: 已决权重
     */
    @ApiModelProperty(value = "已决权重", required = false)
    private BigDecimal paidWeight;

    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.CASE_WEIGHT
     * Database remarks: 未决权重
     */
    @ApiModelProperty(value = "未决权重", required = false)
    private BigDecimal caseWeight;

    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.BASIS_ALLOCATION
     * Database remarks: 分摊基数
     */
    @ApiModelProperty(value = "分摊基数", required = false)
    private BigDecimal basisAllocation;

    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.SCALE_ALLOCATION
     * Database remarks: 分摊比例
     */
    @ApiModelProperty(value = "分摊比例", required = false)
    private BigDecimal scaleAllocation;

    /**
     * Database column: ATR_SECONDARY_ALLOCATION_RESULT.IBNR
     * Database remarks: IBNR
     */
    @ApiModelProperty(value = "IBNR", required = false)
    private BigDecimal ibnr;

    private String yearMonth;

    private Long entityId;

    @ApiModelProperty(value = "数据类型,   41-再保前IBNR,   44-再保前IBNRALAE,   43-再保摊回IBNR,   45-再保摊回IBNRALAE,   48-再保前IBNRULAE,   49-再保摊回IBNRULAE,   50-已发生已报告再保前ULAE,   51-已发生已报告摊回ULAE", required = false)
    private String payFlag;

    private String allocationIdentification;

    private String targetRouter;

    private String templateFileName;

    private String actionNo;

    private String currencyCode;

    private String businessSourceCode;

    /**
     *  分摊平衡 1: 平衡 2: 不平衡
     */
    private String allocationBalanceType;

    /**
     *  分摊前维度金额
     */
    private BigDecimal allocationBfIbnr;

    private BigDecimal sumPaidCny;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public String getTreatyNo() {
        return treatyNo;
    }

    public void setTreatyNo(String treatyNo) {
        this.treatyNo = treatyNo;
    }

    public String getDrawMonth() {
        return drawMonth;
    }

    public void setDrawMonth(String drawMonth) {
        this.drawMonth = drawMonth;
    }

    public String getRiskCatagory() {
        return riskCatagory;
    }

    public void setRiskCatagory(String riskCatagory) {
        this.riskCatagory = riskCatagory;
    }

    public String getComCode() {
        return comCode;
    }

    public void setComCode(String comCode) {
        this.comCode = comCode;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getAccMonth() {
        return accMonth;
    }

    public void setAccMonth(String accMonth) {
        this.accMonth = accMonth;
    }

    public String getClassCode() {
        return classCode;
    }

    public void setClassCode(String classCode) {
        this.classCode = classCode;
    }

    public String getRiskCode() {
        return riskCode;
    }

    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    public BigDecimal getEpPremium() {
        return epPremium;
    }

    public void setEpPremium(BigDecimal epPremium) {
        this.epPremium = epPremium;
    }

    public BigDecimal getPaidPremium() {
        return paidPremium;
    }

    public void setPaidPremium(BigDecimal paidPremium) {
        this.paidPremium = paidPremium;
    }

    public BigDecimal getCasePremium() {
        return casePremium;
    }

    public void setCasePremium(BigDecimal casePremium) {
        this.casePremium = casePremium;
    }

    public String getIsApportionment() {
        return isApportionment;
    }

    public void setIsApportionment(String isApportionment) {
        this.isApportionment = isApportionment;
    }

    public BigDecimal getEpWeight() {
        return epWeight;
    }

    public void setEpWeight(BigDecimal epWeight) {
        this.epWeight = epWeight;
    }

    public BigDecimal getPaidWeight() {
        return paidWeight;
    }

    public void setPaidWeight(BigDecimal paidWeight) {
        this.paidWeight = paidWeight;
    }

    public BigDecimal getCaseWeight() {
        return caseWeight;
    }

    public void setCaseWeight(BigDecimal caseWeight) {
        this.caseWeight = caseWeight;
    }

    public BigDecimal getBasisAllocation() {
        return basisAllocation;
    }

    public void setBasisAllocation(BigDecimal basisAllocation) {
        this.basisAllocation = basisAllocation;
    }

    public BigDecimal getScaleAllocation() {
        return scaleAllocation;
    }

    public void setScaleAllocation(BigDecimal scaleAllocation) {
        this.scaleAllocation = scaleAllocation;
    }

    public BigDecimal getIbnr() {
        return ibnr;
    }

    public void setIbnr(BigDecimal ibnr) {
        this.ibnr = ibnr;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getPayFlag() {
        return payFlag;
    }

    public void setPayFlag(String payFlag) {
        this.payFlag = payFlag;
    }

    public String getAllocationIdentification() {
        return allocationIdentification;
    }

    public void setAllocationIdentification(String allocationIdentification) {
        this.allocationIdentification = allocationIdentification;
    }


    public String getTargetRouter() {
        return targetRouter;
    }

    public void setTargetRouter(String targetRouter) {
        this.targetRouter = targetRouter;
    }

    public String getTemplateFileName() {
        return templateFileName;
    }

    public void setTemplateFileName(String templateFileName) {
        this.templateFileName = templateFileName;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getAllocationBalanceType() {
        return allocationBalanceType;
    }

    public void setAllocationBalanceType(String allocationBalanceType) {
        this.allocationBalanceType = allocationBalanceType;
    }

    public BigDecimal getAllocationBfIbnr() {
        return allocationBfIbnr;
    }

    public void setAllocationBfIbnr(BigDecimal allocationBfIbnr) {
            this.allocationBfIbnr = allocationBfIbnr;
    }

    public BigDecimal getSumPaidCny() {
        return sumPaidCny;
    }

    public void setSumPaidCny(BigDecimal sumPaidCny) {
        this.sumPaidCny = sumPaidCny;
    }
}