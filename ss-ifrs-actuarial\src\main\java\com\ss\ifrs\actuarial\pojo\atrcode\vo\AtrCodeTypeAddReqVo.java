package com.ss.ifrs.actuarial.pojo.atrcode.vo;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;

public class AtrCodeTypeAddReqVo {

    private Long codeTypeId;

    @ApiModelProperty(value = "codeType|类型", required = false)
    @NotBlank(message = "The codeType can't be null|类型不能为空|類型不能為空")
    @Size(max = 50, message = "The codeType's length is too long|类型过长|類型過長")
    private String codeType;

    @ApiModelProperty(value = "codeCName|中文名称", required = false)
    @NotBlank(message = "The Chinese Name can't be null|中文名称不能为空|中文名稱不能為空")
    @Size(max = 200, message = "The Chinese Name's length is too long|中文名称过长|中文名稱過長")
    private String codeCName;

    @ApiModelProperty(value = "codeTName|繁体名称", required = false)
    @NotBlank(message = "The Traditional Chinese Name can't be null|繁体名称不能为空|繁體名稱不能為空")
    @Size(max = 200, message = "The Traditional Chinese Name's length is too long|繁体名称过长|繁體名稱過長")
    private String codeLName;

    @ApiModelProperty(value = "codeEName|英文", required = false)
    @NotBlank(message = "The English Name can't be null|英文名称不能为空|英文名稱不能為空")
    @Size(max = 200, message = "The English Name's length is too long|英文名称过长|英文名稱過長")
    private String codeEName;

    private String creatorUser;

    private Date createDate;

    private String validIs;

    public Long getCodeTypeId() {
        return codeTypeId;
    }

    public void setCodeTypeId(Long codeTypeId) {
        this.codeTypeId = codeTypeId;
    }

    public String getCodeType() {
        return codeType;
    }

    public void setCodeType(String codeType) {
        this.codeType = codeType;
    }

    public String getCodeCName() {
        return codeCName;
    }

    public void setCodeCName(String codeCName) {
        this.codeCName = codeCName;
    }

    public String getCodeLName() {
        return codeLName;
    }

    public void setCodeLName(String codeLName) {
        this.codeLName = codeLName;
    }

    public String getCodeEName() {
        return codeEName;
    }

    public void setCodeEName(String codeEName) {
        this.codeEName = codeEName;
    }

    public String getCreatorUser() {
        return creatorUser;
    }

    public void setCreatorUser(String creatorUser) {
        this.creatorUser = creatorUser;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }
}
