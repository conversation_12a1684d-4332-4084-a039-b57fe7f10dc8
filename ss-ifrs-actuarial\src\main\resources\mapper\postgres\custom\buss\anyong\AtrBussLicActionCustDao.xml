<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-01-13 16:44:16 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussLicCashFlowDao">
  <!-- 本配置文件由SS MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**IDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->
  <resultMap id="CustResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLicCashFlowVo">
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="ACTION_NO" property="actionNo" jdbcType="VARCHAR" />
    <result column="entity_id" property="entityId" jdbcType="DECIMAL" />
    <result column="YEAR_MONTH" property="yearMonth" jdbcType="VARCHAR" />
    <result column="business_source_code" property="businessSourceCode" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="VARCHAR" />
    <result column="CONFIRM_IS" property="confirmIs" jdbcType="VARCHAR" />
    <result column="CONFIRM_USER" property="confirmUser" jdbcType="DECIMAL" />
    <result column="CONFIRM_TIME" property="confirmTime" jdbcType="TIMESTAMP" />
    <result column="CREATOR_ID" property="creatorId" jdbcType="DECIMAL" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATOR_ID" property="updatorId" jdbcType="DECIMAL" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="portfolio_No" property="portfolioNo" jdbcType="VARCHAR" />
    <result column="ICG_NO" property="icgNo" jdbcType="VARCHAR" />
    <result column="loa_code" property="loaCode" jdbcType="VARCHAR" />
    <result column="entity_code" property="entityCode" jdbcType="VARCHAR" />
    <result column="entity_c_name" property="entityCName" jdbcType="VARCHAR" />
    <result column="entity_l_name" property="entityLName" jdbcType="VARCHAR" />
    <result column="entity_e_name" property="entityEName" jdbcType="VARCHAR" />

    <result column="user_name" property="userName" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Cust_Column_List">
    a.ID, a.ACTION_NO,  a.entity_id, a.YEAR_MONTH, a.business_source_code, a.STATUS,
    a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME,
    a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name
  </sql>

  <sql id="Cust_Column_List1">
    a.ID, a.ACTION_NO,  a.entity_id, a.YEAR_MONTH,  a.business_source_code, a.STATUS,
    a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME,
    a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name, u.user_name as creatorName
  </sql>

  <sql id="Cust_Column_List2">
    a.ID, a.ACTION_NO,  a.entity_id, a.YEAR_MONTH, a.business_source_code, a.STATUS,
    a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME,
    a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name,
    u.user_name as creatorName, b.PORTFOLIO_NO ,b.ICG_NO ,b.LOA_CODE
  </sql>

  <sql id="Cust_Select_By_Entity_Where">
    <where>
      <if test="id != null ">
        and a.ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="entityId != null ">
        and a.entity_id = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
        and a.YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="businessSourceCode != null and businessSourceCode != ''">
        and a.business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="confirmIs != null and confirmIs != ''">
        and a.CONFIRM_IS = #{confirmIs,jdbcType=VARCHAR}
      </if>
      <if test="confirmUser != null ">
        and a.CONFIRM_USER = #{confirmUser,jdbcType=DECIMAL}
      </if>
      <if test="confirmTime != null ">
        and a.CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creatorId != null ">
        and a.CREATOR_ID = #{creatorId,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null ">
        and a.CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
        and a.UPDATOR_ID = #{updatorId,jdbcType=DECIMAL}
      </if>
      <if test="updateTime != null ">
        and a.UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>

  <select id="fuzzySearchPage" flushCache="false" useCache="true" resultMap="CustResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLicCashFlowVo">
    select
    <include refid="Cust_Column_List1" />
    from ATR_BUSS_LIC_ACTION a
    left join bpluser.bbs_conf_entity c on a.entity_id = c.entity_id
    left join bpluser.bpl_saa_user u on a.creator_id = u.user_id
    <include refid="Cust_Select_By_Entity_Where" />
    order by ID desc
  </select>

  <select id="findByid" flushCache="false" useCache="true" resultMap="CustResultMap"  parameterType="java.lang.Long">
    select
    <include refid="Cust_Column_List" />
    from ATR_BUSS_LIC_ACTION a
    left join bpluser.bbs_conf_entity c on a.entity_id = c.entity_id
    left join bpluser.BBS_CONF_CURRENCY cur on a.currency_code = cur.CURRENCY_CODE
    where a.ID = #{id,jdbcType=DECIMAL}
  </select>


  <select id="calculation" parameterType="java.util.Map" statementType="CALLABLE">
    call atr_pack_lic_proc_calc(
      #{businessSourceCode, mode=IN, jdbcType=VARCHAR},
      #{entityId, mode=IN, jdbcType=BIGINT},
      #{yearMonth,mode=IN,jdbcType=VARCHAR},
      #{currency,mode=IN,jdbcType=VARCHAR},
      #{portfolioNo, mode=IN, jdbcType=VARCHAR},
      #{userId,mode=IN,jdbcType=BIGINT}
      )
  </select>

  <select id="findByVo" flushCache="false" useCache="true" resultMap="BaseResultMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLicCashFlow">
    select * from(
    select
    <include refid="Base_Column_List"/>
    from ATR_BUSS_LIC_ACTION
    where entity_id = #{entityId,jdbcType=DECIMAL}
    and YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
    and CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
    and business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
    and CREATOR_ID = #{creatorId,jdbcType=DECIMAL}
    order by CREATE_TIME desc
    ) 
	 limit 1
  </select>

  <select id="findPortfolioData" flushCache="false" useCache="true" resultMap="CustResultMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
    select
    <include refid="Cust_Column_List2" />

    <if test="businessSourceCode != null and businessSourceCode != ''">
      <choose>
        <when test='businessSourceCode == "DD"'>
          from ATR_BUSS_DD_LIC_ICG_CALC b
        </when>
        <when test='businessSourceCode == "FO"'>
          from ATR_BUSS_FO_LIC_ICG_CALC b
        </when>
        <when test='businessSourceCode == "TI"'>
          from ATR_BUSS_TI_LIC_ICG_CALC b
        </when>
        <when test='businessSourceCode == "TO"'>
          from ATR_BUSS_TO_LIC_ICG_CALC b
        </when>
        <otherwise>
          from ATR_BUSS_DD_LIC_ICG_CALC b
        </otherwise>
      </choose>
    </if>
    left join ATR_BUSS_LIC_ACTION a
    on a.ACTION_NO = b.ACTION_NO
    and a.entity_id = b.entity_id
    and a.year_month = b.year_month
    and a.currency_code = b.currency_code
    left join bpluser.bbs_conf_entity c on a.entity_id = c.entity_id
    left join bpluser.bpl_saa_user u on a.creator_id = u.user_id
    where a.action_no = #{actionNo,jdbcType=VARCHAR}
    and a.entity_id = #{entityId,jdbcType=DECIMAL}
    and a.year_month = #{yearMonth,jdbcType=VARCHAR}
    and a.currency_code = #{currencyCode,jdbcType=VARCHAR}
    <if test="portfolioNo != null and portfolioNo != ''">
      <choose>
        <when test='portfolioNo.indexOf("%")!=-1'>
          and UPPER(b.PORTFOLIO_NO) LIKE UPPER(#{portfolioNo,jdbcType=VARCHAR})
        </when>
        <otherwise>
          and UPPER(b.PORTFOLIO_NO) = UPPER(#{portfolioNo,jdbcType=VARCHAR})
        </otherwise>
      </choose>
    </if>
    <if test="icgNo != null and icgNo != ''">
      <choose>
        <when test='icgNo.indexOf("%")!=-1'>
          and UPPER(b.ICG_NO) LIKE UPPER(#{icgNo,jdbcType=VARCHAR})
        </when>
        <otherwise>
          and UPPER(b.ICG_NO) = UPPER(#{icgNo,jdbcType=VARCHAR})
        </otherwise>
      </choose>
    </if>
    order by b.ID desc
  </select>

  <select id="updateConfirm" flushCache="false" useCache="true" resultType="Integer"  parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLicCashFlow">
    update ATR_BUSS_LIC_ACTION
    <set>
      <if test="confirmIs != null">
        CONFIRM_IS = #{confirmIs,jdbcType=VARCHAR},
      </if>
      <if test="confirmUser != null">
        CONFIRM_USER = #{confirmUser,jdbcType=DECIMAL},
      </if>
      <if test="confirmTime != null">
        CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        CREATOR_ID = #{creatorId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        UPDATOR_ID = #{updatorId,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = (
     SELECT T.ID
      FROM ATR_BUSS_LIC_ACTION T
      WHERE entity_id = #{entityId, jdbcType = DECIMAL}
      AND YEAR_MONTH = #{yearMonth, jdbcType = VARCHAR}
      AND business_source_code = #{businessSourceCode, jdbcType = VARCHAR}
      and t.status = 'S'
      ORDER BY CREATE_TIME DESC
	   limit 1
    )
  </select>


  <select id="revoke" flushCache="false" useCache="true" resultType="Integer" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLicCashFlow">
    update ATR_BUSS_LIC_ACTION
    <set>
      <if test="confirmIs != null">
        CONFIRM_IS = #{confirmIs,jdbcType=VARCHAR},
      </if>
      <if test="confirmUser != null">
        CONFIRM_USER = #{confirmUser,jdbcType=DECIMAL},
      </if>
      <if test="confirmTime != null">
        CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        UPDATOR_ID = #{updatorId,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    WHERE entity_id = #{entityId, jdbcType = DECIMAL}
    AND YEAR_MONTH = #{yearMonth, jdbcType = VARCHAR}
    AND business_source_code = #{businessSourceCode, jdbcType = VARCHAR}
  </select>

  <update id="reSetLicCashFlow">
    update ATR_BUSS_LIC_ACTION
    set confirm_is='0',
        CONFIRM_USER=null,
        confirm_time=null
    where entity_id = #{entityId,jdbcType=DECIMAL}
      and year_month >= #{yearMonth,jdbcType=VARCHAR}
      and confirm_is = '1'
  </update>
</mapper>