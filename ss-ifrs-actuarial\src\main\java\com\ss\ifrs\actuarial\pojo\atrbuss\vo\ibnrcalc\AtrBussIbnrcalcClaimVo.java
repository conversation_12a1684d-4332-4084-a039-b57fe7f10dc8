package com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class AtrBussIbnrcalcClaimVo {
    @ApiModelProperty(value = "已决费用")
    private BigDecimal settledFee;
    @ApiModelProperty(value = "已决赔款(含费用)，通胀前")
    private BigDecimal settled;
    @ApiModelProperty(value = "未决赔款")
    private BigDecimal os;
    @ApiModelProperty(value = "费用损失比例")
    private BigDecimal ratio;
}
