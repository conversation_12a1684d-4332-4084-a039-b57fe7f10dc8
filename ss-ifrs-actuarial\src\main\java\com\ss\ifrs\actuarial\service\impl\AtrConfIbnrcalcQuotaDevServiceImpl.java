package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.dao.conf.AtrConfIbnrcalcQuotaDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfIbnrcalcQuotaDevDao;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfIbnrcalcQuota;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfIbnrcalcQuotaDev;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfIbnrcalcQuotaDetailVo;
import com.ss.ifrs.actuarial.service.AtrConfIbnrcalcQuotaDevService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Service
@Transactional
public class AtrConfIbnrcalcQuotaDevServiceImpl implements AtrConfIbnrcalcQuotaDevService {
    @Autowired
    private AtrConfIbnrcalcQuotaDevDao atrConfIbnrcalcQuotaDevDao;
    @Autowired
    private AtrConfIbnrcalcQuotaDao atrConfIbnrCalcQuotaDao;
    @Override
    public List<AtrConfIbnrcalcQuotaDetailVo> queryAllIbnrcalcDev() {
        List<AtrConfIbnrcalcQuotaDetailVo> atrConfIbnrcalcQuotaDetailVos = atrConfIbnrcalcQuotaDevDao.queryAllIbnrcalcDev();
        List<AtrConfIbnrcalcQuota> loaCodePADList = atrConfIbnrCalcQuotaDao.findAll();
        HashMap<String, BigDecimal> loadCodePadMap = new HashMap<>();
        loaCodePADList.forEach(item -> {
            loadCodePadMap.put(item.getLoaCode(),item.getPad());
        });
        atrConfIbnrcalcQuotaDetailVos.forEach(item -> {
            String[] interest = item.getInterestRatio().split(",", -1);
            String[] timePeriod = item.getTimePeriod().split(",", -1);
            ArrayList<BigDecimal> interestDecimal = new ArrayList<>();
            ArrayList<BigDecimal> timePeriodDecimal = new ArrayList<>();
            item.setPAD(loadCodePadMap.get(item.getLoaCode()));
            // interest_ratio 利息比例
            for (String i : interest) {
                if (StringUtils.isBlank(i))
                    interestDecimal.add(null);
                else
                    interestDecimal.add(new BigDecimal(i).multiply(BigDecimal.valueOf(100)));
            }
            // 时间段
            for (String j : timePeriod){
                if (StringUtils.isBlank(j))
                    timePeriodDecimal.add(null);
                else
                    timePeriodDecimal.add(new BigDecimal(j));
            }
            item.setInterestRatioValues(interestDecimal);
            item.setTimePeriodValues(timePeriodDecimal);
        });
        return atrConfIbnrcalcQuotaDetailVos;
    }

    @Transactional
    @Override
    public void saveOrUpdateIbnrcalc(List<AtrConfIbnrcalcQuotaDetailVo> atrConfIbnrcalcQuotaDetailVos,Long useId) {
        // 先删掉所有数据
        atrConfIbnrCalcQuotaDao.deleteAll();
        atrConfIbnrcalcQuotaDevDao.deleteAll();
        ArrayList<AtrConfIbnrcalcQuotaDev> atrConfIbnrcalcQuotaDevs = new ArrayList<>();
        ArrayList<AtrConfIbnrcalcQuota> atrConfIbnrcalcQuotas = new ArrayList<>();
        // 数据解析-业务线
        atrConfIbnrcalcQuotaDetailVos.forEach(item -> {

            AtrConfIbnrcalcQuota atrConfIbnrcalcQuota = new AtrConfIbnrcalcQuota();
            atrConfIbnrcalcQuota.setPad(item.getPAD());
            atrConfIbnrcalcQuota.setLoaCode(item.getLoaCode());
            atrConfIbnrcalcQuota.setOperTime(new Date());
            atrConfIbnrcalcQuota.setOperId(useId);
            List<BigDecimal> interestRatioValues = item.getInterestRatioValues();
            List<BigDecimal> timePeriodValues = item.getTimePeriodValues();
            short devNo_temp = 0;
            // 发展期
            for (int j = 0; j < interestRatioValues.size(); j++) {
                AtrConfIbnrcalcQuotaDev atrConfIbnrcalcQuotaDev = new AtrConfIbnrcalcQuotaDev();
                atrConfIbnrcalcQuotaDev.setDevNo(devNo_temp++);
                atrConfIbnrcalcQuotaDev.setOperTime(new Date());
                atrConfIbnrcalcQuotaDev.setLoaCode(item.getLoaCode());
                if ( interestRatioValues.get(j) != null )
                    atrConfIbnrcalcQuotaDev.setInterestRatio(interestRatioValues.get(j).divide(BigDecimal.valueOf(100)));
                else
                    atrConfIbnrcalcQuotaDev.setInterestRatio(BigDecimal.ZERO);
                atrConfIbnrcalcQuotaDev.setTimePeriod(timePeriodValues.get(j));
                atrConfIbnrcalcQuotaDev.setOperId(useId);
                atrConfIbnrcalcQuotaDevs.add(atrConfIbnrcalcQuotaDev);
            }
            if ( atrConfIbnrcalcQuota.getPad() != null)
                atrConfIbnrcalcQuotas.add(atrConfIbnrcalcQuota);
        });
        // 存入数据库
        if ( atrConfIbnrcalcQuotaDevs.size() != 0 )
            atrConfIbnrcalcQuotaDevDao.saveList(atrConfIbnrcalcQuotaDevs);
        if ( atrConfIbnrcalcQuotas.size() != 0 )
            atrConfIbnrCalcQuotaDao.saveList(atrConfIbnrcalcQuotas);
    }
}
