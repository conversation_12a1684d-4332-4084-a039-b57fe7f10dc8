package com.ss.ifrs.actuarial.service.impl.puhua;

import com.alibaba.excel.enums.WriteDirectionEnum;
import com.ss.ifrs.actuarial.conf.AppConfig;
import com.ss.ifrs.actuarial.constant.ActuarialConstant;
import com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGAccAmountDao;
import com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGAccDao;
import com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGAccDevAmountDao;
import com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussLicGDao;
import com.ss.ifrs.actuarial.dao.buss.puhua.lic.AtrBussPHLicActionDao;
import com.ss.ifrs.actuarial.feign.BmsConfCodeFeignClient;
import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLicCashFlow;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLicCashFlowVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfCodeVo;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrBussDDLicIcgCalcVo;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo;
import com.ss.ifrs.actuarial.pojo.puhua.lrc.vo.AtrBussLicGAccAmountVo;
import com.ss.ifrs.actuarial.pojo.puhua.lrc.vo.AtrBussLicGDevVo;
import com.ss.ifrs.actuarial.pojo.puhua.lrc.vo.AtrBussLicGVo;
import com.ss.ifrs.actuarial.service.AtrConfCodeService;
import com.ss.ifrs.actuarial.service.AtrConfModelDefService;
import com.ss.ifrs.actuarial.service.AtrExportService;
import com.ss.ifrs.actuarial.service.impl.AtrBussLicService;
import com.ss.ifrs.actuarial.service.puhua.AtrBussPuHuaLicActionService;
import com.ss.library.excel.ExcelSheet;
import com.ss.library.excel.ExcelSheetData;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ClassUtil;
import com.ss.library.utils.FilterUtil;
import com.ss.library.utils.SpringContextUtil;
import com.ss.library.utils.StringUtil;
import com.ss.platform.core.constant.CommonConstant;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName: AtrBussLicCashFlowServiceImpl
 * @Description: 计量预期赔付现金流接口实现类
 * @Author: xiaomj.
 * @CreateDate: 2023/2/7 19:54
 * @Version: 1.0
 */
@Service(value = "atrBussPuHuaLicActionService")
public class AtrBussLicCashFlowServiceImpl implements AtrBussPuHuaLicActionService {

	// 日志管理
	final Logger LOG  = LoggerFactory.getLogger(getClass());

	@Autowired
    BmsConfCodeFeignClient bmsConfCodeFeignClient;

	@Autowired
	AtrConfModelDefService atrConfModelDefService;

	@Autowired
	private AtrBussPHLicActionDao atrBussLicCashFlowDao;
  
	@Autowired
	private AtrBussLicGDao atrBussLicGDao;

	@Autowired
	AtrBussLicGAccDao atrBussLicGAccDao;

	@Autowired
	AtrBussLicGAccAmountDao atrBussLicGAccAmountDao;

	@Autowired
	AtrBussLicGAccDevAmountDao atrBussLicGAccDevAmountDao;

	@Autowired
    AtrConfCodeService atrConfCodeService;

	@Autowired
	AtrExportService atrExportService;

	@Autowired
	private AppConfig appConfig;

	/*
	* 方法返回MAP类型值，key公用值
	* */
	private final static String MAP_RETURN_PUBLIC_KEY = "resultList";

	@Override
	public Page<AtrBussLicCashFlowVo> findForDataTables(AtrBussLicCashFlowVo atrBussLicCashFlowVo, Pageable pageParam) {
		return atrBussLicCashFlowDao.fuzzySearchPage(atrBussLicCashFlowVo, pageParam);
	}

	@Override
	@Async("ruleThreadPool")
	@Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = RuntimeException.class)
	public void save(AtrBussLicCashFlowVo atrBussLicCashFlowVo, Long userId) {
		this.procCalc(atrBussLicCashFlowVo);
	}

	@Override
	public AtrBussLicCashFlowVo findById(Long id) {
		return atrBussLicCashFlowDao.findByid(id);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
	public void delete(Long id, Long userId) {
		atrBussLicCashFlowDao.deleteById(id);
	}

	@Override
	public Map<String, Object> findClaimPaid(AtrDapDrawVo atrDapDrawVo) {
		if (atrDapDrawVo == null || atrDapDrawVo.getEntityId() == null
			 ) {
			return new HashMap<>();
		}
		Map<String, Object> map = new HashMap<String, Object>();
		if (ObjectUtils.isNotEmpty(atrDapDrawVo.getBusinessSourceCode())){
			List<AtrBussDDLicIcgCalcVo> resultListDD = atrBussLicGDao.findAccidentClaimPaid(atrDapDrawVo);
			map.put(MAP_RETURN_PUBLIC_KEY, resultListDD);
		}
		return map;
	}

	@Override
	public List<AtrDapDrawVo> findPaidMode(AtrDapDrawVo atrDapDrawVo) {
		if (atrDapDrawVo == null || atrDapDrawVo.getMainId() == null) {
			return new ArrayList<AtrDapDrawVo>();
		}
		List<AtrDapDrawVo> resultList = new ArrayList<>();
		switch (atrDapDrawVo.getBusinessSourceCode()){
			case "DD":
				// 查找事故年月
			case "FO":
			case "TI":
			case "TO":
			default:
				resultList = atrBussLicGAccAmountDao.findYearMonth(atrDapDrawVo.getMainId());
				for (AtrDapDrawVo vo : resultList) {
					vo.setMainId(atrDapDrawVo.getMainId());
					List<AtrDapDrawVo> devYearMonthList = atrBussLicGAccDevAmountDao.findPaidMode(vo);
					vo.setDevYearMonthList(devYearMonthList);
				}
				break;
		}

		return resultList;
	}

/*	@Override
	public Map<String, Object> findUlt(AtrDapDrawVo atrDapDrawVo) {
		if (atrDapDrawVo == null || atrDapDrawVo.getMainId() == null) {
			return new HashMap<>();
		}
		Map<String, Object> map = new HashMap<String, Object>();
		switch (atrDapDrawVo.getBusinessSourceCode()){
			case "DD":
				AtrBussDDLicIcgCalcDetail poDD = new AtrBussDDLicIcgCalcDetail();
				poDD.setMainId(atrDapDrawVo.getMainId());
				List<AtrBussDDLicIcgCalcDetail> detailDD = atrBussLicGAccDevAmountDao.findList(poDD);
				map.put(MAP_RETURN_PUBLIC_KEY, detailDD);
				break;
			case "FO":
				AtrBussFOLicIcgCalcDetail poFO = new AtrBussFOLicIcgCalcDetail();
				poFO.setMainId(atrDapDrawVo.getMainId());
				List<AtrBussFOLicIcgCalcDetail> detailFO = atrBussFOLicIcgCalcDetailDao.findList(poFO);
				map.put(MAP_RETURN_PUBLIC_KEY, detailFO);
				break;
			case "TI":
				AtrBussTILicIcgCalcDetail poTI = new AtrBussTILicIcgCalcDetail();
				poTI.setMainId(atrDapDrawVo.getMainId());
				List<AtrBussTILicIcgCalcDetail> detailTI = atrBussTILicIcgCalcDetailDao.findList(poTI);
				map.put(MAP_RETURN_PUBLIC_KEY, detailTI);
				break;
			case "TO":
				AtrBussTOLicIcgCalcDetail poTO = new AtrBussTOLicIcgCalcDetail();
				poTO.setMainId(atrDapDrawVo.getMainId());
				List<AtrBussTOLicIcgCalcDetail> detailTO = atrBussTOLicIcgCalcDetailDao.findList(poTO);
				map.put(MAP_RETURN_PUBLIC_KEY, detailTO);
				break;
			default:
				break;
		}

		return map;
	}*/

	@Override
	public AtrDapDrawVo findId(AtrDapDrawVo vo) {
		if (vo == null || vo.getEntityId() == null
				  || vo.getYearMonth() == null
				|| vo.getPortfolioNo() == null || vo.getActionNo() == null) {
			return null;
		}
		AtrDapDrawVo drawVo = new AtrDapDrawVo();
		if (ObjectUtils.isNotEmpty(vo.getBusinessSourceCode())){
			AtrBussLicGVo calcVoDD =  atrBussLicGDao.findId(vo);
			if (ObjectUtils.isNotEmpty(calcVoDD)&&ObjectUtils.isNotEmpty(calcVoDD.getId())) {
				drawVo.setMainId(calcVoDD.getId());
			}
		}
		return drawVo;
	}

	@Override
	public Map<String, Object> showExpectedClaim(AtrDapDrawVo atrDapDrawVo) {
		if (atrDapDrawVo == null || atrDapDrawVo.getMainId() == null) {
			return new HashMap<>();
		}
		Map<String, Object> map = new HashMap<String, Object>();
		switch (atrDapDrawVo.getBusinessSourceCode()){
			case "DD":
			case "FO":
			case "TI":
			case "TO":
			default:
				List<AtrBussLicGDevVo> detailTO = atrBussLicGAccDao.findByMainId(atrDapDrawVo.getMainId());
				map.put(MAP_RETURN_PUBLIC_KEY, detailTO);
				break;
		}

		return map;
	}

	@Override
	public Map<String, Object> findCalcClaimPaid(AtrDapDrawVo atrDapDrawVo) {
		if (atrDapDrawVo == null  || atrDapDrawVo.getActionNo() == null) {
			return new HashMap<>();
		}
		Map<String, Object> map = new HashMap<String, Object>();
		switch (atrDapDrawVo.getBusinessSourceCode()){
			case "DD":
			case "FO":
			case "TI":
			case "TO":
			default:
				List<AtrBussLicGVo> detailDD = atrBussLicGDao.findClaimPaid(atrDapDrawVo);
				for (AtrBussLicGVo vo : detailDD) {
					atrDapDrawVo.setIcgNo(vo.getIcgNo());
					atrDapDrawVo.setAccidentYearMonth(vo.getAccidentYearMonth());
					List<AtrBussLicGDevVo> detailList = atrBussLicGAccDao.findDevByVo(atrDapDrawVo);
					vo.setDetailList(detailList);
				}
				map.put(MAP_RETURN_PUBLIC_KEY, detailDD);
				break;
		}

		return map;
	}

	private void procCalc(AtrBussLicCashFlowVo vo) {
		Long entityId = vo.getEntityId();
		String yearMonth = vo.getYearMonth();
		String businessSourceCode = vo.getBusinessSourceCode();
		SpringContextUtil.getBean(AtrBussLicService.class).entry(entityId, yearMonth, businessSourceCode);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = UnexpectedRollbackException.class)
	public Boolean confirm(AtrBussLicCashFlowVo bussLicCashFlowVo, Long userId)  throws UnexpectedRollbackException {
		Boolean confirmFlag = true;
		AtrBussLicCashFlow atrBussLicCashFlow = new AtrBussLicCashFlow();
		atrBussLicCashFlow.setEntityId(bussLicCashFlowVo.getEntityId());
		atrBussLicCashFlow.setYearMonth(bussLicCashFlowVo.getYearMonth());
		atrBussLicCashFlow.setBusinessSourceCode(bussLicCashFlowVo.getBusinessSourceCode());
		atrBussLicCashFlow.setConfirmIs(CommonConstant.VersionStatus.CONFIRMED);
		Long confirmCount = atrBussLicCashFlowDao.count(atrBussLicCashFlow);
		if (ObjectUtils.isNotEmpty(confirmCount) && confirmCount==0) {
			try {
				AtrBussLicCashFlow po = ClassUtil.convert(bussLicCashFlowVo, AtrBussLicCashFlow.class);

				Date date = new Date();
				po.setConfirmIs("1");
				po.setConfirmUser(userId);
				po.setConfirmTime(date);
				po.setUpdatorId(userId);
				po.setUpdateTime(date);
				atrBussLicCashFlowDao.updateById(po);

				AtrConfBussPeriodVo atrConfBussPeriodVo = new AtrConfBussPeriodVo();
				atrConfBussPeriodVo.setEntityId(bussLicCashFlowVo.getEntityId());
				atrConfBussPeriodVo.setYearMonth(bussLicCashFlowVo.getYearMonth());
				atrConfBussPeriodVo.setProcCode(ActuarialConstant.ProcCode.EXPECTED_CF_LIC);
				atrConfModelDefService.confirmBeCFProc(atrConfBussPeriodVo, userId);
			} catch (UnexpectedRollbackException e) {
				LOG.error(e.getLocalizedMessage(), e);
				throw e;
			}
		} else {
			confirmFlag = false;
		}
		return confirmFlag;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
	public void revoke(AtrBussLicCashFlowVo bussLicCashFlowVo, Long userId) {
		AtrBussLicCashFlow atrBussLicCashFlow = new AtrBussLicCashFlow();
		atrBussLicCashFlow.setEntityId(bussLicCashFlowVo.getEntityId());
		atrBussLicCashFlow.setYearMonth(bussLicCashFlowVo.getYearMonth());
		atrBussLicCashFlow.setBusinessSourceCode(bussLicCashFlowVo.getBusinessSourceCode());
		atrBussLicCashFlow.setConfirmIs(CommonConstant.VersionStatus.CONFIRMED);
		Long confirmCount = atrBussLicCashFlowDao.count(atrBussLicCashFlow);
		if (ObjectUtils.isNotEmpty(confirmCount) && confirmCount > 0) {
			Date date = new Date();
			atrBussLicCashFlow.setConfirmIs(CommonConstant.VersionStatus.PENDING);
			atrBussLicCashFlow.setConfirmUser(userId);
			atrBussLicCashFlow.setConfirmTime(date);
			atrBussLicCashFlow.setUpdatorId(userId);
			atrBussLicCashFlow.setUpdateTime(date);
			atrBussLicCashFlowDao.revoke(atrBussLicCashFlow);
		}
	}

	@Override
	//@TrackActuarialProcess
	public void calculateAll(AtrBussLicCashFlowVo licCashFlowVo, Long userId) {
		Long entityId = licCashFlowVo.getEntityId();
		String yearMonth = licCashFlowVo.getYearMonth();
		SpringContextUtil.getBean(AtrBussLicService.class).entry(entityId, yearMonth, "DD");
		SpringContextUtil.getBean(AtrBussLicService.class).entry(entityId, yearMonth, "FO");
		SpringContextUtil.getBean(AtrBussLicService.class).entry(entityId, yearMonth, "TI");
		SpringContextUtil.getBean(AtrBussLicService.class).entry(entityId, yearMonth, "TO");
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
	public String confirmLicCfVersion(AtrBussLicCashFlowVo licCashFlowVo, Long userId) {
		List<String> businessModelList = bmsConfCodeFeignClient.findByCodeIdx("BusinessModel/Base");
		if (ObjectUtils.isEmpty(businessModelList)) {
			return "not business model";
		}
 		//po.setCurrencyCode(licCashFlowVo.getCurrencyCode());
		businessModelList.forEach(businessModel-> {
			AtrBussLicCashFlow po = new AtrBussLicCashFlow();
			po.setEntityId(licCashFlowVo.getEntityId());
			po.setYearMonth(licCashFlowVo.getYearMonth());
			po.setBusinessSourceCode(businessModel);
			po.setConfirmIs(CommonConstant.VersionStatus.CONFIRMED);
			Long confirmCount = atrBussLicCashFlowDao.count(po);
			po.setConfirmIs("0");
			Long unConfirmCount = atrBussLicCashFlowDao.count(po);
			if (ObjectUtils.isNotEmpty(confirmCount) && confirmCount == 0 && ObjectUtils.isNotEmpty(unConfirmCount) && unConfirmCount > 0) {
 				Date date = new Date();
				po.setConfirmIs(CommonConstant.VersionStatus.CONFIRMED);
				po.setConfirmUser(userId);
				po.setConfirmTime(date);
				atrBussLicCashFlowDao.updateConfirm(po);
			}
		});
		AtrConfBussPeriodVo atrConfBussPeriodVo = new AtrConfBussPeriodVo();
		atrConfBussPeriodVo.setEntityId(licCashFlowVo.getEntityId());
		atrConfBussPeriodVo.setYearMonth(licCashFlowVo.getYearMonth());
		atrConfBussPeriodVo.setProcCode(ActuarialConstant.ProcCode.EXPECTED_CF_LIC);
		atrConfModelDefService.confirmBeCFProc(atrConfBussPeriodVo, userId);
		return null;
	}

	@Override
	public Page<AtrDapDrawVo> findPortfolioData(AtrDapDrawVo atrDapDrawVo, Pageable pageParam) {
		if (atrDapDrawVo == null || atrDapDrawVo.getId() == null) {
			return new Page<AtrDapDrawVo>();
		}
		atrDapDrawVo.setPortfolioNo(FilterUtil.transitionSearch(atrDapDrawVo.getPortfolioNo()));
		atrDapDrawVo.setIcgNo(FilterUtil.transitionSearch(atrDapDrawVo.getIcgNo()));
		AtrBussLicCashFlowVo atrBussLicCashFlowVo = atrBussLicCashFlowDao.findByid(atrDapDrawVo.getId());
		Page<AtrDapDrawVo> result = new Page<>();
		if(ObjectUtils.isNotEmpty(atrBussLicCashFlowVo)&&ObjectUtils.isNotEmpty(atrBussLicCashFlowVo.getEntityId())
				&&ObjectUtils.isNotEmpty(atrBussLicCashFlowVo.getBusinessSourceCode())&&ObjectUtils.isNotEmpty(atrBussLicCashFlowVo.getActionNo())
				 &&ObjectUtils.isNotEmpty(atrBussLicCashFlowVo.getYearMonth())){
			atrDapDrawVo.setBusinessSourceCode(atrBussLicCashFlowVo.getBusinessSourceCode());
			atrDapDrawVo.setEntityId(atrBussLicCashFlowVo.getEntityId());
			atrDapDrawVo.setCurrencyCode(atrBussLicCashFlowVo.getCurrencyCode());

			atrDapDrawVo.setActionNo(atrBussLicCashFlowVo.getActionNo());
			atrDapDrawVo.setYearMonth(atrBussLicCashFlowVo.getYearMonth());
			result = atrBussLicCashFlowDao.findPortfolioData(atrDapDrawVo, pageParam);
		}
		return result;
	}

	@Override
	public void exportLicResultExcel(HttpServletRequest request, HttpServletResponse response, AtrBussLicCashFlowVo atrBussLicCashFlowVo, Long userId) throws Exception {
		AtrBussLicCashFlow atrBussLicCashFlow = atrBussLicCashFlowDao.findById(atrBussLicCashFlowVo.getId());
		if (ObjectUtils.isEmpty(atrBussLicCashFlow)) {
			return;
		}
		if (ObjectUtils.isEmpty(atrBussLicCashFlowVo.getTemplateFileName())) {
			throw new RuntimeException("Excel Template is null");
		}
		//获取语言
		String language = request.getHeader("ss-Language");
		if(StringUtil.isEmpty(language)){
			language = "en";
		}
		atrBussLicCashFlowVo.setEntityId(atrBussLicCashFlow.getEntityId());
		atrBussLicCashFlowVo.setCurrencyCode(atrBussLicCashFlow.getCurrencyCode());
		atrBussLicCashFlowVo.setBusinessSourceCode(atrBussLicCashFlow.getBusinessSourceCode());
		atrBussLicCashFlowVo.setActionNo(atrBussLicCashFlow.getActionNo());
		atrBussLicCashFlowVo.setLanguage(language);
		List<AtrDapDrawVo> accidentDevNoList = atrBussLicGDao.findDevNo(atrBussLicCashFlowVo);
		List<AtrDapDrawVo> icgCalcDetailDevNoList = atrBussLicGDao.listDiscount(atrBussLicCashFlowVo);
		String templateExcelFilePath = generateTemplateExcel(request, icgCalcDetailDevNoList, accidentDevNoList, atrBussLicCashFlowVo);

		atrBussLicCashFlowVo.setCreatorId(userId);
		atrBussLicCashFlowVo.setAccidentDevNoList(accidentDevNoList.stream().map(AtrDapDrawVo::getDevNo).collect(Collectors.toList()));
		atrBussLicCashFlowVo.setIcgDetailDevNoList(icgCalcDetailDevNoList.stream().map(AtrDapDrawVo::getDevNo).mapToInt(i -> i).boxed().collect(Collectors.toList()));
		this.fillLicDataToExcel(request, response, atrBussLicCashFlowVo, templateExcelFilePath);
	}

	@Override
	public void exportLicAioiExcel(HttpServletRequest request, HttpServletResponse response, AtrBussLicCashFlowVo atrBussLicCashFlowVo, Long userId) throws Exception {
		AtrBussLicCashFlow atrBussLicCashFlow = atrBussLicCashFlowDao.findById(atrBussLicCashFlowVo.getId());
		if (ObjectUtils.isEmpty(atrBussLicCashFlow)) {
			return;
		}
		if (ObjectUtils.isEmpty(atrBussLicCashFlowVo.getTemplateFileName())) {
			throw new RuntimeException("Excel Template is null");
		}
		//获取语言
		String language = request.getHeader("ss-Language");
		if(StringUtil.isEmpty(language)){
			language = "en";
		}
		atrBussLicCashFlowVo.setEntityId(atrBussLicCashFlow.getEntityId());
		atrBussLicCashFlowVo.setCurrencyCode(atrBussLicCashFlow.getCurrencyCode());
		atrBussLicCashFlowVo.setBusinessSourceCode(atrBussLicCashFlow.getBusinessSourceCode());
		atrBussLicCashFlowVo.setActionNo(atrBussLicCashFlow.getActionNo());
		atrBussLicCashFlowVo.setLanguage(language);

		// 查询现金流数据
		List<AtrDapDrawVo> cashFlowList = atrBussLicGDao.listCashFlow(atrBussLicCashFlowVo);
		// 收集所有 devNo 并排序，用于生成表头列
		List<Integer> devNoList = cashFlowList.stream()
			.map(AtrDapDrawVo::getDevNo)
			.distinct()
			.sorted()
			.collect(Collectors.toList());
		
		// 查询折现数据
		List<AtrDapDrawVo> discountList = atrBussLicGDao.listDiscount(atrBussLicCashFlowVo);

		atrBussLicCashFlowVo.setTemplateFileName(atrBussLicCashFlowVo.getTemplateFileName()+"_"+language);
		// 生成模板
		//String templateExcelFilePath = generateNewTemplateExcel(devNoList, atrBussLicCashFlowVo);

		atrBussLicCashFlowVo.setCreatorId(userId);
		
		// 对现金流和折现数据进行预处理
		List<Map<String, Object>> cashFlowData = formatCashFlowData(cashFlowList, devNoList);
		List<Map<String, Object>> discountData = formatDiscountData(discountList);

		List<Map<String, Object>> icgCashFlowData = new ArrayList<>();
		List<Integer> devIcgNoList = new ArrayList<>();
		if ("TO".equals(atrBussLicCashFlow.getBusinessSourceCode()) || "FO".equals(atrBussLicCashFlow.getBusinessSourceCode())) {
			// 查询再保人不履约现金流
			List<AtrDapDrawVo> icgCashFlowList = atrBussLicGDao.listIcGCashFlow(atrBussLicCashFlowVo);
			// 收集所有 devNo 并排序，用于生成表头列
			devIcgNoList = icgCashFlowList.stream()
					.map(AtrDapDrawVo::getDevNo)
					.distinct()
					.sorted()
					.collect(Collectors.toList());
			icgCashFlowData = formatCashFlowData(icgCashFlowList, devIcgNoList);
		}
		// 生成模板
		String templateExcelFilePath = generateNewTemplateExcel(devNoList, devIcgNoList, atrBussLicCashFlowVo);

		// 填充数据到Excel
		fillNewLicDataToExcel(request, response, atrBussLicCashFlowVo, templateExcelFilePath, cashFlowData, discountData, icgCashFlowData);
	}

	/**
	 * @Description: 生成符合要求的Excel模板，包含两个Sheet
	 * @Author: wyh.
	 * @CreateDate: 2024/2/27
	 */
	private String generateNewTemplateExcel(List<Integer> devNoList, List<Integer> devIcgNoList, AtrBussLicCashFlowVo atrBussLicCashFlowVo) {
		// 获取原始模板路径
		String basePath = appConfig.getBasePath();
		String excelPath = appConfig.getExportExcelTemplate();
		String templatePath = appConfig.getExportExcelOutTemplate();
		String originalTemplateFile = basePath + excelPath + atrBussLicCashFlowVo.getTemplateFileName() + ".xlsx";
		
		// 生成临时模板文件路径
		Date outPutTime = new Date();
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
		String timestamp = formatter.format(outPutTime);
		String tempTemplateFile = basePath + templatePath + atrBussLicCashFlowVo.getTemplateFileName() + "_" + timestamp + ".xlsx";
		
		// 按devNoList动态修改模板
		try (FileInputStream fis = new FileInputStream(originalTemplateFile);
			 Workbook workbook = WorkbookFactory.create(fis)) {
			
			// 获取第一个Sheet
			Sheet sheet = workbook.getSheetAt(0);
			
			// 获取标题行
			Row titleRow = sheet.getRow(0);
			Row headerRow = sheet.getRow(1);
			if (headerRow == null) {
				headerRow = sheet.createRow(1);
			}


			// 基础列数量（合同组、保单号、险别代码、事故发生月、现金流类型）
			int baseColumnCount = 8;
			if ("TI".equals(atrBussLicCashFlowVo.getBusinessSourceCode())) {
				baseColumnCount = 7;
			}
			// 从现有列获取样式作为参考
			CellStyle titleCellStyle = null;
			CellStyle headerCellStyle = null;
			
			// 获取已有单元格的样式，确保至少有一个已有列
			if (titleRow.getCell(0) != null) {
				titleCellStyle = titleRow.getCell(0).getCellStyle();
			}
			if (headerRow.getCell(0) != null) {
				headerCellStyle = headerRow.getCell(0).getCellStyle();
			}
			
			// 设置devNo列标题
			for (int i = 0; i < devNoList.size(); i++) {
				Integer devNo = devNoList.get(i);
				// 列索引从0开始，前5列是固定列
				int columnIndex = baseColumnCount + i;
				
				// 创建或获取标题行单元格
				Cell titleRowCell = titleRow.getCell(columnIndex);
				if (titleRowCell == null) {
					titleRowCell = titleRow.createCell(columnIndex);
				}
				titleRowCell.setCellValue(devNo);
				
				// 应用标题行样式
				if (titleCellStyle != null) {
					titleRowCell.setCellStyle(titleCellStyle);
				}
				
				// 创建或获取头部行单元格
				Cell cell = headerRow.getCell(columnIndex);
				if (cell == null) {
					cell = headerRow.createCell(columnIndex);
				}
				cell.setCellValue("{df." + (i+1) + "}");
				
				// 应用头部行样式
				if (headerCellStyle != null) {
					cell.setCellStyle(headerCellStyle);
				}
				
				// 设置列宽
				sheet.setColumnWidth(columnIndex, 12 * 256); // 12个字符宽度
			}

			if (ObjectUtils.isNotEmpty(devIcgNoList)) {
				this.generateRdTemplateExcel(devIcgNoList, workbook);
			}
			// 保存修改后的模板
			try (FileOutputStream fos = new FileOutputStream(tempTemplateFile)) {
				workbook.write(fos);
			}
			
			LOG.info("成功生成动态模板: {}", tempTemplateFile);
			return tempTemplateFile;
			
		} catch (Exception e) {
			LOG.error("动态修改模板失败", e);
			// 失败时返回原始模板
			return originalTemplateFile;
		}
	}


	/**
	 * @Description: 生成符合要求的Excel模板，包含三个Sheet
	 * @Author: wyh.
	 * @CreateDate: 2024/2/27
	 */
	private void generateRdTemplateExcel(List<Integer> devNoList, Workbook workbook) {
		// 获取原始模板路径
			// 获取第一个Sheet
			Sheet sheet = workbook.getSheetAt(2);
			if (ObjectUtils.isEmpty(sheet)){
				return;
			}
			// 获取标题行
			Row titleRow = sheet.getRow(0);
			Row headerRow = sheet.getRow(1);
			if (headerRow == null) {
				headerRow = sheet.createRow(1);
			}
			// 基础列数量（事故发生月、现金流类型）
			int baseColumnCount =  4;

			// 从现有列获取样式作为参考
			CellStyle titleCellStyle = null;
			CellStyle headerCellStyle = null;

			// 获取已有单元格的样式，确保至少有一个已有列
			if (titleRow.getCell(0) != null) {
				titleCellStyle = titleRow.getCell(0).getCellStyle();
			}
			if (headerRow.getCell(0) != null) {
				headerCellStyle = headerRow.getCell(0).getCellStyle();
			}

			// 设置devNo列标题
			for (int i = 0; i < devNoList.size(); i++) {
				Integer devNo = devNoList.get(i);
				// 列索引从0开始，前5列是固定列
				int columnIndex = baseColumnCount + i;

				// 创建或获取标题行单元格
				Cell titleRowCell = titleRow.getCell(columnIndex);
				if (titleRowCell == null) {
					titleRowCell = titleRow.createCell(columnIndex);
				}
				titleRowCell.setCellValue(devNo);
				// 应用标题行样式
				if (titleCellStyle != null) {
					titleRowCell.setCellStyle(titleCellStyle);
				}
				// 创建或获取头部行单元格
				Cell cell = headerRow.getCell(columnIndex);
				if (cell == null) {
					cell = headerRow.createCell(columnIndex);
				}
				cell.setCellValue("{df." + (i+1) + "}");
				// 应用头部行样式
				if (headerCellStyle != null) {
					cell.setCellStyle(headerCellStyle);
				}
				// 设置列宽
				sheet.setColumnWidth(columnIndex, 12 * 256); // 12个字符宽度
			}
	}

	/**
	 * @Description: 将现金流数据格式化为图1所示的格式
	 * @Author: wyh.
	 * @CreateDate: 2024/2/27
	 */
	private List<Map<String, Object>> formatCashFlowData(List<AtrDapDrawVo> cashFlowList, List<Integer> devNoList) {
		// 创建结果列表
		List<Map<String, Object>> resultList = new ArrayList<>();

		
		// 分组的key
		Map<String, Map<String, Object>> groupingMap = new HashMap<>();
		
		// 按合同组、保单号、险别代码、事故年月、费用类型分组
		for (AtrDapDrawVo vo : cashFlowList) {
			String groupKey = vo.getIcgNo() + "|" + vo.getTreatyNo() + "|" + vo.getPolicyNo() + "|" +
					vo.getKindCode() + "|" + vo.getAccidentYearMonth() + "|" + vo.getFeeType();
			
			// 如果该组合不存在，创建一个新行
			if (!groupingMap.containsKey(groupKey)) {
				Map<String, Object> rowData = new LinkedHashMap<>();
				// 使用与Excel模板中的占位符完全匹配的字段名
				rowData.put("yearMonth", vo.getYearMonth());
				rowData.put("icgNo", vo.getIcgNo());
				rowData.put("icgNoName", vo.getIcgNo());
				if ("TI".equals(vo.getBusinessSourceCode())) {
					rowData.put("treatyNo", vo.getTreatyNo());
					rowData.put("riskClassCode", vo.getRiskClassCode());
				} else {
					rowData.put("businessSourceCode", vo.getBusinessSourceCode());
					rowData.put("riskClassCode", vo.getRiskClassCode());
					rowData.put("kindCode", vo.getKindCode());
				}
				rowData.put("accYearMonth", vo.getAccidentYearMonth());
				rowData.put("feeType", vo.getFeeType());
				
				// 初始化所有发展期列为null
				for (int i = 0; i < devNoList.size(); i++) {
					// 发展期列的序号从1开始
					rowData.put(String.valueOf(i+1), null);
				}
				
				groupingMap.put(groupKey, rowData);
				resultList.add(rowData);
			}
			
			// 设置对应devNo的数据
			Map<String, Object> rowData = groupingMap.get(groupKey);
			if (vo.getDevNo() != null) {
				// 找到当前devNo在devNoList中的索引位置
				int index = devNoList.indexOf(vo.getDevNo());
				if (index >= 0 && index < devNoList.size()) {
					// 索引从0开始，但列名从1开始
					rowData.put(String.valueOf(index+1), vo.getAmount());
				}
			}
		}
		
		return resultList;
	}
	
	/**
	 * @Description: 将折现数据格式化为图2所示的格式
	 * @Author: wyh.
	 * @CreateDate: 2024/2/27
	 */
	private List<Map<String, Object>> formatDiscountData(List<AtrDapDrawVo> discountList) {
		// 创建结果列表，直接处理数据不使用过于复杂的嵌套Map
		List<Map<String, Object>> resultList = new ArrayList<>();
		
		// 分组的key
		Map<String, Map<String, Object>> groupingMap = new HashMap<>();
		
		// 按合同组、事故发生月、费用类型分组
		for (AtrDapDrawVo vo : discountList) {
			String groupKey = vo.getIcgNo() + "|" + vo.getAccidentYearMonth() + "|" + vo.getFeeType();
			
			// 如果该组合不存在，创建一个新行
			if (!groupingMap.containsKey(groupKey)) {
				Map<String, Object> rowData = new LinkedHashMap<>();
				// 使用与Excel模板中的占位符完全匹配的字段名
				rowData.put("icgNo", vo.getIcgNo());
				rowData.put("accYearMonth", vo.getAccidentYearMonth());
				rowData.put("feeType", vo.getFeeType());
				
				// 初始化所有金额列为null
				rowData.put("L1", null);
				rowData.put("L2", null);
				rowData.put("L3", null);
				rowData.put("L4", null);
				rowData.put("C1", null);
				rowData.put("C2", null);
				
				groupingMap.put(groupKey, rowData);
				resultList.add(rowData);
			}
			
			// 设置对应金额类型的数据
			if (vo.getAmountType() != null) {
				Map<String, Object> rowData = groupingMap.get(groupKey);
				rowData.put(vo.getAmountType(), vo.getAmount());
			}
		}
		
		return resultList;
	}
	
	/**
	 * @Description: 填充格式化后的数据到Excel上
	 * @Author: wyh.
	 * @CreateDate: 2024/2/27
	 */
	private void fillNewLicDataToExcel(HttpServletRequest request, HttpServletResponse response, 
			AtrBussLicCashFlowVo atrBussLicCashFlowVo, String templateFile, 
			List<Map<String, Object>> cashFlowData,
		    List<Map<String, Object>> discountData,
		    List<Map<String, Object>> icgCashFlowData) throws Exception {
		
		// 确保数据不为空
		if (cashFlowData.isEmpty() || discountData.isEmpty()) {
			LOG.warn("导出数据为空，cashFlowData size: {}, discountData size: {}", 
					cashFlowData.size(), discountData.size());
		}
		
		// 创建Sheet数据列表
		List<ExcelSheet> sheetList = new ArrayList<>();
		
		// 第一个sheet - 现金流数据
		List<ExcelSheetData> cashFlowSheetDataList = new ArrayList<>();
		ExcelSheetData cashFlowSheetData = new ExcelSheetData("df", cashFlowData);
		cashFlowSheetDataList.add(cashFlowSheetData);
		ExcelSheet cashFlowSheet = new ExcelSheet(0, cashFlowSheetDataList);
		sheetList.add(cashFlowSheet);
		
		// 第二个sheet - 折现数据
		List<ExcelSheetData> discountSheetDataList = new ArrayList<>();
		ExcelSheetData discountSheetData = new ExcelSheetData("df", discountData);
		discountSheetDataList.add(discountSheetData);
		ExcelSheet discountSheet = new ExcelSheet(1, discountSheetDataList);
		sheetList.add(discountSheet);

		if (ObjectUtils.isNotEmpty(icgCashFlowData)) {
			// 第三个sheet - 现金流数据
			List<ExcelSheetData> icgCashFlowSheetDataList = new ArrayList<>();
			ExcelSheetData icgCashFlowSheetData = new ExcelSheetData("df", icgCashFlowData);
			icgCashFlowSheetDataList.add(icgCashFlowSheetData);
			ExcelSheet icgCashFlowSheet = new ExcelSheet(2, icgCashFlowSheetDataList);
			sheetList.add(icgCashFlowSheet);
		}

		atrExportService.exportSheetList(request,response, sheetList, templateFile,
				atrBussLicCashFlowVo.getTemplateFileName(),atrBussLicCashFlowVo.getLogFileName(), atrBussLicCashFlowVo.getTargetRouter(), atrBussLicCashFlowVo.getCreatorId());


	}

	@Override
	public List<AtrBussLicGAccAmountVo> showExpectedClaimTotal(AtrDapDrawVo atrDapDrawVo) {
		List<AtrBussLicGAccAmountVo>  atrBussLicIcgAmountVos = atrBussLicGDao.showExpectedClaimTotal(atrDapDrawVo);
		return atrBussLicIcgAmountVos;
	}


	/**
	 * @Description: 生成LIC填充数据Excel模板
	 * @Author: wyh.
	 * @CreateDate: 2024/2/27
	 */
	private String generateTemplateExcel(HttpServletRequest request, List<AtrDapDrawVo> icgCalcDetailDevNoList, List<AtrDapDrawVo> accidentDevNoList, AtrBussLicCashFlowVo atrBussLicCashFlowVo) throws Exception{
		List<ExcelSheet> sheetList = new ArrayList<>();
		List<ExcelSheetData> ultSheetDataList = new ArrayList<>();
		ExcelSheetData icgWeightSheetData = new ExcelSheetData("dp", icgCalcDetailDevNoList, WriteDirectionEnum.HORIZONTAL );
		ultSheetDataList.add(icgWeightSheetData);
		ExcelSheet ultSheet = new ExcelSheet(1, ultSheetDataList);

		List<ExcelSheetData> claimPatternSheetDataList = new ArrayList<>();
		ExcelSheetData claimPatternSheetData = new ExcelSheetData("dp", accidentDevNoList, WriteDirectionEnum.HORIZONTAL);
		claimPatternSheetDataList.add(claimPatternSheetData);
		ExcelSheet claimPatternSheet = new ExcelSheet(2, claimPatternSheetDataList);

		List<ExcelSheetData> licSheetDataList = new ArrayList<>();
		ExcelSheetData licSheetData = new ExcelSheetData("dp", icgCalcDetailDevNoList, WriteDirectionEnum.HORIZONTAL);
		licSheetDataList.add(licSheetData);
		ExcelSheet licSheet = new ExcelSheet(3, claimPatternSheetDataList);

		sheetList.add(ultSheet);
		sheetList.add(claimPatternSheet);
		sheetList.add(licSheet);
		return atrExportService.generateExcelBySheetList(request, sheetList, atrBussLicCashFlowVo.getTemplateFileName());
	}

	/**
	 * @Description: 填充LIC结果数据到Excel上
	 * @Author: wyh.
	 * @CreateDate: 2024/2/27
	 */
	private void fillLicDataToExcel(HttpServletRequest request, HttpServletResponse response, AtrBussLicCashFlowVo atrBussLicCashFlowVo,String templateFile) throws Exception{
		//1.查询实付赔款
		AtrDapDrawVo atrDapDrawVo = ClassUtil.convert(atrBussLicCashFlowVo, AtrDapDrawVo.class);
		List<ExcelSheet> sheetList = new ArrayList<>();
		//1.查询LIC
		List<Map<String, Object>> licMap = findLic(atrBussLicCashFlowVo);
		List<ExcelSheetData> licDataList = new ArrayList<>();
		ExcelSheetData licDataListSheetData = new ExcelSheetData("df", licMap);
		licDataList.add(licDataListSheetData);
		ExcelSheet licSheet = new ExcelSheet(0, licDataList);
		sheetList.add(licSheet);

		List<AtrBussLicGAccAmountVo> licIcgCalcVoList = this.showExpectedClaimTotal(atrDapDrawVo);
		List<ExcelSheetData> claimPaidDataList = new ArrayList<>();
		ExcelSheetData claimPaidDataListSheetData = new ExcelSheetData("df", licIcgCalcVoList);
		claimPaidDataList.add(claimPaidDataListSheetData);
		ExcelSheet claimPaidSheet = new ExcelSheet(1, claimPaidDataList);
		sheetList.add(claimPaidSheet);
		atrExportService.exportSheetList(request,response, sheetList,
				templateFile,
				atrBussLicCashFlowVo.getTemplateFileName(),
				atrBussLicCashFlowVo.getLogFileName(),
				atrBussLicCashFlowVo.getTargetRouter(),
				atrBussLicCashFlowVo.getCreatorId());

	}

	/**
	 * @Description: 查询ult比例结果,按合同组号分组查询
	 * @Author: wyh.
	 * @CreateDate: 2024/2/27
	 */
 	private List<Map<String, Object>> findClaimUlt(AtrBussLicCashFlowVo atrBussLicCashFlowVo) {
		List<Map<String, Object>> ultResultListMap = atrBussLicGDao.findUltDev(atrBussLicCashFlowVo);
		return ultResultListMap;
	}

	/**
	 * @Description: 查询赔付模式结果,按合同组号分组查询
	 * @Author: wyh.
	 * @CreateDate: 2024/2/27
	 */
	private List<Map<String, Object>> findClaimPattern(AtrBussLicCashFlowVo atrBussLicCashFlowVo) {
		List<Map<String, Object>> ultResultListMap = atrBussLicGDao.findClaimPattern(atrBussLicCashFlowVo);
		return ultResultListMap;
	}

	/**
	 * @Description: 查询LIC预期赔付结果,按合同组号分组查询
	 * @Author: wyh.
	 * @CreateDate: 2024/2/27
	 */
	private List<Map<String, Object>> findLic(AtrBussLicCashFlowVo atrBussLicCashFlowVo) {
		AtrConfCodeVo sysCodeVo = new AtrConfCodeVo();
		sysCodeVo.setCodeCodeIdx("ExpectedClaimType");
		List<AtrConfCodeVo> confCodeVoList = atrConfCodeService.findCodeByCodeIdx(sysCodeVo);
		List<Map<String, Object>> licResultListMap = atrBussLicGDao.findLicDev(confCodeVoList, atrBussLicCashFlowVo);
		return licResultListMap;
	}

}
