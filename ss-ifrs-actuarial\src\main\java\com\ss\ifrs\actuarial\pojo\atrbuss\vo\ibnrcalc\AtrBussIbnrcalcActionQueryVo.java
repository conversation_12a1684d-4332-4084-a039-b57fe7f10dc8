/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-12-04 15:35:00
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter @Getter
public class AtrBussIbnrcalcActionQueryVo {

    @ApiModelProperty(value = "entityId", required = true)
    private Long entityId;

    @ApiModelProperty(value = "币别", required = true)
    private String currencyCode;

    @ApiModelProperty(value = "提取区间|见SimpleExtractInterval", required = true)
    private String extractionInterval;

    @ApiModelProperty(value = "提取区间数", required = true)
    private Short extractionZones;

    @ApiModelProperty(value = "提取结束年月", required = true)
    private String extractionDeadline;

    @ApiModelProperty(value = "IBNR类型|见IbnrType，  1-再保前赔款， 2-再保后赔款", required = false)
    private String ibnrType;

    @ApiModelProperty(value = "业务线", required = false)
    private String loaCode;

    @ApiModelProperty(value = "是否确认", required = false)
    private String confirmIs;

    @ApiModelProperty(value = "执行状态", required = false)
    private String status;

}