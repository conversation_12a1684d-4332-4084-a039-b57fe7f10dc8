CREATE OR REPLACE VIEW QTC_V_EVALUATE_RESULT_HIS AS
       SELECT M.EVALUATE_MAIN_ID,
              M<PERSON>VERSION_NO,
              <PERSON><PERSON>NTITY_ID,
              <PERSON><PERSON>YEAR_MONTH,
              M.BUSINESS_MODEL || M<PERSON>IN<PERSON>S_DIRECTION AS BUSINESS_MODEL,
              M.<PERSON>_DEF_ID,
              <PERSON><PERSON>,
              <PERSON><PERSON>_CODE,
              SUBSTR(M.YEAR_MONTH, 1, 4) AS YEAR,
              TO_NUMBER(SUBSTR(<PERSON><PERSON>YEAR_MONTH, 5, 2)) AS MONTH,
              NUM1,
              NUM2,
              NUM3,
              NUM4,
              NUM5,
              NUM6,
              NUM7,
              NUM8,
              NUM9,
              NUM10,
              NUM11,
              NUM12,
              NUM13,
              NUM14,
              NUM15,
              NUM16,
              NUM17,
              NUM18,NUM19,NUM20,NUM21
      FROM
             ( select T1.EVALUATE_MAIN_ID,NUM1, <PERSON>UM2,<PERSON>UM3, <PERSON>UM4,<PERSON>UM5,<PERSON>UM6,<PERSON>UM7, <PERSON>UM8,<PERSON>UM9,
                   <PERSON><PERSON><PERSON>,<PERSON><PERSON>11, <PERSON><PERSON>12, <PERSON>UM13, <PERSON>UM14, <PERSON>UM15,<PERSON><PERSON>16,<PERSON><PERSON>17,<PERSON><PERSON><PERSON> <PERSON> NUM18, NULL AS N<PERSON>19,NULL AS NUM20, NULL AS NUM21
            from (SELECT t.EVALUATE_MAIN_ID,
                         SUM(NUM1) NUM1,
                         SUM(NUM2) NUM2,
                         SUM(NUM3) NUM3,
                         SUM(NUM4) NUM4,
                         SUM(NUM5) NUM5,
                         SUM(NUM6) NUM6,
                         SUM(NUM7) NUM7,
                         SUM(NUM8) NUM8,
                         SUM(NUM9) NUM9
                  FROM QTC_BUSS_EVALUATE_MAIN M,qtc_buss_evaluate_result t
                  WHERE M.EVALUATE_MAIN_ID =t.evaluate_main_id AND m.confirm_is='1' AND m.model_def_id=3 and VAR1 = 'Lrc' 
                  GROUP BY t.EVALUATE_MAIN_ID) t1 LEFT JOIN
                 (select t.EVALUATE_MAIN_ID,
                         SUM(CASE WHEN TYPE_NO = 'A1' THEN NUM1 END) NUM10,
                         SUM(CASE WHEN TYPE_NO = 'A1' THEN NUM2 END) NUM11,
                         SUM(CASE WHEN TYPE_NO = 'B1' THEN NUM1 END) NUM12,
                         SUM(CASE WHEN TYPE_NO = 'B1' THEN NUM2 END) NUM13,
                         SUM(CASE WHEN TYPE_NO = 'C1' THEN NUM1 END) NUM14,
                         SUM(CASE WHEN TYPE_NO = 'C1' THEN NUM2 END) NUM15,
                         SUM(CASE WHEN TYPE_NO = 'D1' THEN NUM1 END) NUM16,
                         SUM(CASE WHEN TYPE_NO = 'D1' THEN NUM2 END) NUM17
                  from QTC_BUSS_EVALUATE_MAIN M,QTC_BUSS_EVL_PAA_CAL_ICG t
                  where M.EVALUATE_MAIN_ID =t.evaluate_main_id AND m.confirm_is='1' AND m.model_def_id=3  
                    AND TYPE_NO in ('A1', 'B1', 'C1', 'D1')
                  group by t.EVALUATE_MAIN_ID) t2
             ON t1.EVALUATE_MAIN_ID = t2.EVALUATE_MAIN_ID
            union all  
            select T1.EVALUATE_MAIN_ID,NUM1, NUM2,NUM3, NUM4,NUM5,NUM6,NUM7, NUM8,
                              NUM9, NUM10,NUM11, NUM12, NUM13, NUM14, NUM15,NUM16,NULL AS NUM17,NULL AS NUM18, NULL AS NUM19,NULL AS NUM20, NULL AS NUM21
            from (SELECT t.EVALUATE_MAIN_ID,
                         SUM(NUM1) NUM1,
                         SUM(NUM2) NUM2,
                         SUM(NUM3) NUM3,
                         SUM(NUM4) NUM4,
                         SUM(NUM5) NUM5,
                         SUM(NUM6) NUM6,
                         SUM(NUM7) NUM7,
                         SUM(NUM8) NUM8
                  FROM QTC_BUSS_EVALUATE_MAIN M,qtc_buss_evaluate_result t
                  WHERE M.EVALUATE_MAIN_ID =t.evaluate_main_id AND m.confirm_is='1' AND m.model_def_id=1 AND VAR1 = 'Lrc'
                  GROUP BY t.EVALUATE_MAIN_ID) t1 LEFT JOIN
                 (select t.EVALUATE_MAIN_ID,
                         SUM(CASE WHEN TYPE_NO = 'A1' THEN NUM1 END) NUM9,
                         SUM(CASE WHEN TYPE_NO = 'A1' THEN NUM2 END) NUM10,
                         SUM(CASE WHEN TYPE_NO = 'B1' THEN NUM1 END) NUM11,
                         SUM(CASE WHEN TYPE_NO = 'B1' THEN NUM2 END) NUM12,
                         SUM(CASE WHEN TYPE_NO = 'C1' THEN NUM1 END) NUM13,
                         SUM(CASE WHEN TYPE_NO = 'C1' THEN NUM2 END) NUM14,
                         SUM(CASE WHEN TYPE_NO = 'D1' THEN NUM1 END) NUM15,
                         SUM(CASE WHEN TYPE_NO = 'D1' THEN NUM2 END) NUM16
                from QTC_BUSS_EVALUATE_MAIN M,QTC_BUSS_EVL_GMM_CAL_ICG t
                  where M.EVALUATE_MAIN_ID =t.evaluate_main_id AND m.confirm_is='1' AND m.model_def_id=1 
                    AND TYPE_NO in ('A1', 'B1', 'C1', 'D1')
                  group by t.EVALUATE_MAIN_ID) t2 ON t1.EVALUATE_MAIN_ID = t2.EVALUATE_MAIN_ID
            union all
            select T1.EVALUATE_MAIN_ID,NUM1, NUM2,NUM3, NUM4,NUM5,NUM6,NUM7, NUM8,NUM9,NUM10,NUM11,NUM12,
                   NUM13,NUM14, NUM15, NUM16, NUM17, NUM18,NUM19,NUM20,NUM21
            from (SELECT t.EVALUATE_MAIN_ID,
                         SUM(NUM1) NUM1,
                         SUM(NUM2) NUM2,
                         SUM(NUM3) NUM3,
                         SUM(NUM4) NUM4,
                         SUM(NUM5) NUM5,
                         SUM(NUM6) NUM6,
                         SUM(NUM7) NUM7,
                         SUM(NUM8) NUM8,
                         SUM(NUM9) NUM9,
                         SUM(NUM7) NUM10,
                         SUM(NUM8) NUM11,
                         SUM(NUM9) NUM12
                  FROM QTC_BUSS_EVALUATE_MAIN M,qtc_buss_evaluate_result t
                  WHERE M.EVALUATE_MAIN_ID =t.evaluate_main_id AND m.confirm_is='1' AND m.model_def_id=4 AND VAR1 = 'Lrc'
                  GROUP BY t.EVALUATE_MAIN_ID) t1,
                 (select t.EVALUATE_MAIN_ID,
                         SUM(CASE WHEN TYPE_NO = 'A1' THEN NUM1 END) NUM13,
                         SUM(CASE WHEN TYPE_NO = 'A1' THEN NUM2 END) NUM14,
                         SUM(CASE WHEN TYPE_NO = 'A1' THEN NUM3 END) NUM15,
                         SUM(CASE WHEN TYPE_NO = 'B1' THEN NUM1 END) NUM16,
                         SUM(CASE WHEN TYPE_NO = 'B1' THEN NUM2 END) NUM17,
                         SUM(CASE WHEN TYPE_NO = 'B1' THEN NUM3 END) NUM18,
                         SUM(CASE WHEN TYPE_NO = 'C1' THEN NUM1 END) NUM19,
                         SUM(CASE WHEN TYPE_NO = 'C1' THEN NUM2 END) NUM20,
                         SUM(CASE WHEN TYPE_NO = 'C1' THEN NUM3 END) NUM21
                  from QTC_BUSS_EVALUATE_MAIN M,QTC_BUSS_EVL_PAA_CAL_ICG t
                  where M.EVALUATE_MAIN_ID =t.evaluate_main_id AND m.confirm_is='1' AND m.model_def_id=4 
                    AND TYPE_NO in ('A1', 'B1', 'C1')
                  group by t.EVALUATE_MAIN_ID) t2
            where t1.EVALUATE_MAIN_ID = t2.EVALUATE_MAIN_ID
            union all
            select T1.EVALUATE_MAIN_ID,NUM1, NUM2,NUM3, NUM4,NUM5,NUM6,NUM7, NUM8,NUM9,NUM10,
                              NUM11,NUM12,NUM13,NUM14, NUM15, NUM16, NUM17, NUM18,NUM19,NULL AS NUM20, NULL AS NUM21
            from (SELECT t.EVALUATE_MAIN_ID,
                         SUM(NUM1) NUM1,
                         SUM(NUM2) NUM2,
                         SUM(NUM3) NUM3,
                         SUM(NUM4) NUM4,
                         SUM(NUM5) NUM5,
                         SUM(NUM6) NUM6,
                         SUM(NUM7) NUM7,
                         SUM(NUM8) NUM8,
                         SUM(NUM9) NUM9,
                         SUM(NUM7) NUM10
                  FROM QTC_BUSS_EVALUATE_MAIN M,qtc_buss_evaluate_result t
                  WHERE M.EVALUATE_MAIN_ID =t.evaluate_main_id AND m.confirm_is='1' AND m.model_def_id=2 AND VAR1 = 'Lrc'
                  GROUP BY t.EVALUATE_MAIN_ID) t1,
                 (select t.EVALUATE_MAIN_ID,
                         SUM(CASE WHEN TYPE_NO = 'A1' THEN NUM1 END) NUM11,
                         SUM(CASE WHEN TYPE_NO = 'A1' THEN NUM2 END) NUM12,
                         SUM(CASE WHEN TYPE_NO = 'A1' THEN NUM3 END) NUM13,
                         SUM(CASE WHEN TYPE_NO = 'B1' THEN NUM1 END) NUM14,
                         SUM(CASE WHEN TYPE_NO = 'B1' THEN NUM2 END) NUM15,
                         SUM(CASE WHEN TYPE_NO = 'B1' THEN NUM3 END) NUM16,
                         SUM(CASE WHEN TYPE_NO = 'C1' THEN NUM1 END) NUM17,
                         SUM(CASE WHEN TYPE_NO = 'C1' THEN NUM2 END) NUM18,
                         SUM(CASE WHEN TYPE_NO = 'C1' THEN NUM3 END) NUM19
                  FROM QTC_BUSS_EVALUATE_MAIN M,QTC_BUSS_EVL_GMM_CAL_ICG t
                  where M.EVALUATE_MAIN_ID =t.evaluate_main_id AND m.confirm_is='1' AND m.model_def_id=2 
                    AND TYPE_NO in ('A1', 'B1', 'C1')
                  group by t.EVALUATE_MAIN_ID) t2
            where t1.EVALUATE_MAIN_ID = t2.EVALUATE_MAIN_ID
         ) T LEFT JOIN QTC_BUSS_EVALUATE_MAIN M
           ON M.EVALUATE_MAIN_ID = T.EVALUATE_MAIN_ID
        ORDER BY M.ENTITY_ID, M.YEAR_MONTH DESC, M.LOA_CODE DESC;
