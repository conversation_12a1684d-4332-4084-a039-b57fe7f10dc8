package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.dao.buss.reserve.AtrBussReserveDacDao;
import com.ss.ifrs.actuarial.dao.buss.reserve.AtrBussReserveDacDetailDao;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveDac;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveDacDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveDacVo;
import com.ss.ifrs.actuarial.service.AtrBussReserveDacService;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ClassUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class AtrBussReserveDacServiceImpl implements AtrBussReserveDacService {

    @Autowired
    AtrBussReserveDacDao atrBussReserveDacDao;
    @Autowired
    AtrBussReserveDacDetailDao atrBussReserveDacDetailDao;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor= UnexpectedRollbackException.class)
    public void reserveDacExtract(AtrBussReserveDacVo atrBussReserveDacVo, Long userId) {
        AtrBussReserveDac atrBussReserveDac = ClassUtil.convert(atrBussReserveDacVo, AtrBussReserveDac.class);
        SimpleDateFormat dateFormatVersion = new SimpleDateFormat("yyyyMMddHHmmss");
        SimpleDateFormat dateFormatMonth= new SimpleDateFormat("yyyyMM");
        atrBussReserveDac.setCreatorId(userId);
        atrBussReserveDac.setCreateTime(new Date());
        atrBussReserveDac.setDataSource("0");
        atrBussReserveDac.setConfirmIs("0");
        atrBussReserveDac.setVersionNo(dateFormatVersion.format(new Date()));
        atrBussReserveDac.setYearMonth(dateFormatMonth.format(atrBussReserveDac.getDrawTime()));
        atrBussReserveDacDao.save(atrBussReserveDac);

        Map<String, Object> param = new HashMap<>(2);
        param.put("reserveDacId", atrBussReserveDac.getReserveDacId());
        atrBussReserveDacDao.reserveDacExtract(param);
    }

    @Override
    public Page<AtrBussReserveDacVo> searchPage(AtrBussReserveDacVo atrBussReserveDacVo, Pageable pageParam) {
        Page<AtrBussReserveDacVo> atrBussReserveDacVoPage = atrBussReserveDacDao.fuzzySearchPage(atrBussReserveDacVo, pageParam);
        return  atrBussReserveDacVoPage;
    }

    @Override
    public Page<AtrBussReserveDacDetailVo> findForDataTables(AtrBussReserveDacVo atrBussReserveDacVo, Pageable pageParam) {
        if (ObjectUtils.isNotEmpty(atrBussReserveDacVo.getColumnList())) {
            String columnSql = String.join(",", atrBussReserveDacVo.getColumnList());
            atrBussReserveDacVo.setColumnSql(columnSql + ",");
        }

        Page<AtrBussReserveDacDetailVo> atrBussReserveDacVoPage = atrBussReserveDacDetailDao.fuzzySearchPage(atrBussReserveDacVo, pageParam);
        return atrBussReserveDacVoPage;
    }

    @Override
    public void confirm(AtrBussReserveDacVo atrBussReserveDacVo, Long userId) {
        if(ObjectUtils.isEmpty(atrBussReserveDacVo.getReserveDacId()) && checkIsConfirm(atrBussReserveDacVo)) {
            return;
        }
        AtrBussReserveDac po =  atrBussReserveDacDao.findById(atrBussReserveDacVo.getReserveDacId());
        po.setConfirmIs("1");
        po.setConfirmId(userId);
        po.setConfirmTime(new Date());
        po.setUpdatorId(userId);
        po.setUpdateTime(new Date());
        atrBussReserveDacDao.updateById(po);
    }

    @Override
    public List<AtrBussReserveDacDetailVo> findDownloadList(AtrBussReserveDacVo atrBussReserveDacVo) {
        if (ObjectUtils.isNotEmpty(atrBussReserveDacVo.getColumnList())) {
            String columnSql = String.join(",", atrBussReserveDacVo.getColumnList());
            atrBussReserveDacVo.setColumnSql(columnSql + ",");
        }
        return atrBussReserveDacDetailDao.fuzzySearchPage(atrBussReserveDacVo);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor= UnexpectedRollbackException.class)
    public void delete(Long reserveDacId, Long userId) {
        AtrBussReserveDac po =  atrBussReserveDacDao.findById(reserveDacId);
        if(ObjectUtils.isNotEmpty(po) && CommonConstant.VersionStatus.PENDING.equals(po.getConfirmIs())) {
            atrBussReserveDacDetailDao.deleteByMainId(reserveDacId);
            atrBussReserveDacDao.deleteById(reserveDacId);
        }
    }

    @Override
    public Boolean checkIsConfirm(AtrBussReserveDacVo atrBussReserveDacVo) {
        Map<String, Object> param = new HashMap<>();
        param.put("entityId", atrBussReserveDacVo.getEntityId());
        param.put("yearMonth", atrBussReserveDacVo.getYearMonth());
        Long count = atrBussReserveDacDao.reserveDacConfirm(param);
        if(ObjectUtils.isNotEmpty(count) && count >0) {
            return true;
        }
        return false;
    }
}
