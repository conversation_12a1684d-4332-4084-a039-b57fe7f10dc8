/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-06-05 14:31:46
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-06-05 14:31:46<br/>
 * Description: upr结果明细表<br/>
 * Table Name: ATR_BUSS_RESERVE_EARNED_DETAIL<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "upr结果明细表")
public class AtrBussReserveEarnedDetail implements Serializable {
    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.EARNED_DETAIL_ID
     * Database remarks: 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    private Long earnedDetailId;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.RESERVE_EARNED_ID
     * Database remarks: 主表id
     */
    @ApiModelProperty(value = "主表id", required = true)
    private Long reserveEarnedId;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.RISK_CLASS
     * Database remarks: risk_class|风险大类
     */
    @ApiModelProperty(value = "risk_class|风险大类", required = false)
    private String riskClassCode;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.RISK_CODE
     * Database remarks: 险种
     */
    @ApiModelProperty(value = "险种", required = false)
    private String riskCode;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.CMUNIT_NO
     * Database remarks: 计量单元号
     */
    @ApiModelProperty(value = "计量单元号", required = false)
    private String cmunitNo;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.POLICY_NO
     * Database remarks: 保单号
     */
    @ApiModelProperty(value = "保单号", required = false)
    private String policyNo;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.ENDORSE_SEQ_NO
     * Database remarks: 批改序号
     */
    @ApiModelProperty(value = "批改序号", required = false)
    private String endorseSeqNo;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.ENDORSE_NO
     * Database remarks: endorse_no|批单号
     */
    @ApiModelProperty(value = "endorse_no|批单号", required = false)
    private String endorseNo;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.ICG_NO
     * Database remarks: 合同组号码
     */
    @ApiModelProperty(value = "合同组号码", required = false)
    private String icgNo;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.ICP_NO
     * Database remarks: null
     */
    private String icpNo;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.BUSINESS_TYPE
     * Database remarks: businessSourceCode|业务类型
     */
    @ApiModelProperty(value = "businessSourceCode|业务类型", required = false)
    private String businessSourceCode;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.REINS_TYPE
     * Database remarks: reins_type|再保类型(DN直保，RN再保)
     */
    @ApiModelProperty(value = "reins_type|再保类型(DN直保，RN再保)", required = false)
    private String reinsType;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.START_DATE
     * Database remarks: start_date|起保时间
     */
    @ApiModelProperty(value = "start_date|起保时间", required = false)
    private Date startDate;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.END_DATE
     * Database remarks: end_date|终保时间
     */
    @ApiModelProperty(value = "end_date|终保时间", required = false)
    private Date endDate;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.CHECK_DATE
     * Database remarks: Check Date|核保日期
     */
    @ApiModelProperty(value = "Check Date|核保日期", required = false)
    private Date checkDate;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.ATR_METHOD
     * Database remarks: Atr Method|计提方法
     */
    @ApiModelProperty(value = "Atr Method|计提方法", required = false)
    private String atrMethod;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.LOA_CODE
     * Database remarks: loa_code|业务线
     */
    @ApiModelProperty(value = "loa_code|业务线", required = false)
    private String loaCode;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.CURRENCY
     * Database remarks: currency|币别
     */
    @ApiModelProperty(value = "currency|币别", required = false)
    private String currencyCode;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.PREMIUM
     * Database remarks: premium|保费
     */
    @ApiModelProperty(value = "premium|保费", required = false)
    private BigDecimal premium;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.COMM
     * Database remarks: Comm|佣金
     */
    @ApiModelProperty(value = "Comm|佣金", required = false)
    private BigDecimal Comm;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.DRAW_PREMIUM_START
     * Database remarks: draw_premium_Start|未了责任保费_开始
     */
    @ApiModelProperty(value = "draw_premium_Start|未了责任保费_开始", required = false)
    private BigDecimal drawPremiumStart;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.DRAW_PREMIUM_END
     * Database remarks: draw_premium_End|未了责任保费_结束
     */
    @ApiModelProperty(value = "draw_premium_End|未了责任保费_结束", required = false)
    private BigDecimal drawPremiumEnd;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.DRAW_COMM_START
     * Database remarks: draw_comm_Start|递延佣金_开始
     */
    @ApiModelProperty(value = "draw_comm_Start|递延佣金_开始", required = false)
    private BigDecimal drawCommStart;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.DRAW_COMM_END
     * Database remarks: draw_Comm_End|递延佣金_结束
     */
    @ApiModelProperty(value = "draw_Comm_End|递延佣金_结束", required = false)
    private BigDecimal drawCommEnd;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.DRAW_PREM_EARNED
     * Database remarks: draw_prem_Earned|已赚保费
     */
    @ApiModelProperty(value = "draw_prem_Earned|已赚保费", required = false)
    private BigDecimal drawPremEarned;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED_DETAIL.DRAW_COMM_EARNED
     * Database remarks: draw_Comm_Earned|释放佣金
     */
    @ApiModelProperty(value = "draw_Comm_Earned|释放佣金", required = false)
    private BigDecimal drawCommEarned;

    private static final long serialVersionUID = 1L;

    public Long getEarnedDetailId() {
        return earnedDetailId;
    }

    public void setEarnedDetailId(Long earnedDetailId) {
        this.earnedDetailId = earnedDetailId;
    }

    public Long getReserveEarnedId() {
        return reserveEarnedId;
    }

    public void setReserveEarnedId(Long reserveEarnedId) {
        this.reserveEarnedId = reserveEarnedId;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public String getRiskCode() {
        return riskCode;
    }

    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    public String getCmunitNo() {
        return cmunitNo;
    }

    public void setCmunitNo(String cmunitNo) {
        this.cmunitNo = cmunitNo;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getEndorseSeqNo() {
        return endorseSeqNo;
    }

    public void setEndorseSeqNo(String endorseSeqNo) {
        this.endorseSeqNo = endorseSeqNo;
    }

    public String getEndorseNo() {
        return endorseNo;
    }

    public void setEndorseNo(String endorseNo) {
        this.endorseNo = endorseNo;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public String getIcpNo() {
        return icpNo;
    }

    public void setIcpNo(String icpNo) {
        this.icpNo = icpNo;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getReinsType() {
        return reinsType;
    }

    public void setReinsType(String reinsType) {
        this.reinsType = reinsType;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Date getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(Date checkDate) {
        this.checkDate = checkDate;
    }

    public String getAtrMethod() {
        return atrMethod;
    }

    public void setAtrMethod(String atrMethod) {
        this.atrMethod = atrMethod;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public BigDecimal getPremium() {
        return premium;
    }

    public void setPremium(BigDecimal premium) {
        this.premium = premium;
    }

    public BigDecimal getComm() {
        return Comm;
    }

    public void setComm(BigDecimal Comm) {
        this.Comm = Comm;
    }

    public BigDecimal getDrawPremiumStart() {
        return drawPremiumStart;
    }

    public void setDrawPremiumStart(BigDecimal drawPremiumStart) {
        this.drawPremiumStart = drawPremiumStart;
    }

    public BigDecimal getDrawPremiumEnd() {
        return drawPremiumEnd;
    }

    public void setDrawPremiumEnd(BigDecimal drawPremiumEnd) {
        this.drawPremiumEnd = drawPremiumEnd;
    }

    public BigDecimal getDrawCommStart() {
        return drawCommStart;
    }

    public void setDrawCommStart(BigDecimal drawCommStart) {
        this.drawCommStart = drawCommStart;
    }

    public BigDecimal getDrawCommEnd() {
        return drawCommEnd;
    }

    public void setDrawCommEnd(BigDecimal drawCommEnd) {
        this.drawCommEnd = drawCommEnd;
    }

    public BigDecimal getDrawPremEarned() {
        return drawPremEarned;
    }

    public void setDrawPremEarned(BigDecimal drawPremEarned) {
        this.drawPremEarned = drawPremEarned;
    }

    public BigDecimal getDrawCommEarned() {
        return drawCommEarned;
    }

    public void setDrawCommEarned(BigDecimal drawCommEarned) {
        this.drawCommEarned = drawCommEarned;
    }
}