package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.annotation.TrackActuarialProcess;
import com.ss.ifrs.actuarial.constant.ActuarialConstant;
import com.ss.ifrs.actuarial.dao.conf.AtrConfModelDefDao;
import com.ss.ifrs.actuarial.feign.BmsActProcFeignClient;
import com.ss.ifrs.actuarial.feign.BmsConfCodeFeignClient;
import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcAction;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.ifrs.actuarial.service.AtrConfBussPeriodService;
import com.ss.ifrs.actuarial.service.AtrConfModelDefService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AtrConfModelDefServiceImpl implements AtrConfModelDefService {

    @Autowired
    AtrConfModelDefService modelDefService;

    @Autowired
    BmsConfCodeFeignClient bmsConfCodeFeignClient;

    @Autowired
    BmsActProcFeignClient bmsActProcFeignClient;

    @Autowired
    private AtrConfModelDefDao atrConfModelDefDao;

    @Autowired
    AtrConfBussPeriodService atrConfBussPeriodService;

    @Override
    public Boolean confirmBeCFProc(AtrConfBussPeriodVo bussPeriodVo, Long userId) {
        Boolean result = false;
        switch (bussPeriodVo.getProcCode()) {
            case ActuarialConstant.ProcCode.EXPECTED_CF_LRC:
                result = modelDefService.confirmLrcBeCF(bussPeriodVo, userId);
                break;
            case ActuarialConstant.ProcCode.EXPECTED_CF_LIC:
                result = modelDefService.confirmLicBeCF(bussPeriodVo, userId);
                break;
        }
        return result;
    }

    @TrackActuarialProcess
    public Boolean confirmLrcBeCF(AtrConfBussPeriodVo bussPeriodVo, Long userId) {
        Boolean result = false;

        AtrBussLrcAction bussAction = new AtrBussLrcAction();
        bussAction.setEntityId(bussPeriodVo.getEntityId());
        bussAction.setYearMonth(bussPeriodVo.getYearMonth());

        Integer lrcConfirmCount = atrConfModelDefDao.countLrcBecfConfirm(bussAction);
        if(ObjectUtils.isNotEmpty(lrcConfirmCount)) {
            result = lrcConfirmCount == 0;
        }
        if (result) {
            bussPeriodVo.setParentProcCode(ActuarialConstant.ProcCode.EXPECTED_CF);
            bussPeriodVo.setCreatorId(userId);

            Integer licConfirmCount = atrConfModelDefDao.countLicBecfConfirm(bussAction);
            if (ObjectUtils.isNotEmpty(licConfirmCount) && licConfirmCount == 0) {
               // atrConfBussPeriodService.executionPeriod(bussPeriodVo.getEntityId(), bussPeriodVo.getYearMonth(), ActuarialConstant.BussCaseFlows.BUSS_BECF);
            }
        }
        return result;
    }

    @TrackActuarialProcess
    public Boolean confirmLicBeCF(AtrConfBussPeriodVo bussPeriodVo, Long userId) {
        Boolean result = false;

        AtrBussLrcAction bussAction = new AtrBussLrcAction();
        bussAction.setEntityId(bussPeriodVo.getEntityId());
        bussAction.setYearMonth(bussPeriodVo.getYearMonth());

        //计算现金流确认版本数（业务模型数）
        Integer licConfirmCount = atrConfModelDefDao.countLicBecfConfirm(bussAction);
        if(ObjectUtils.isNotEmpty(licConfirmCount)) {
            result = licConfirmCount == 0;
        }
        if (result) {
            bussPeriodVo.setParentProcCode(ActuarialConstant.ProcCode.EXPECTED_CF);
            bussPeriodVo.setCreatorId(userId);
            Integer lrcConfirmCount = atrConfModelDefDao.countLrcBecfConfirm(bussAction);
            if (ObjectUtils.isNotEmpty(lrcConfirmCount) && lrcConfirmCount == 0) {
//                atrConfBussPeriodService.executionPeriod(bussPeriodVo.getEntityId(), bussPeriodVo.getYearMonth(), ActuarialConstant.BussCaseFlows.BUSS_BECF);
            }
        }
        return result;
    }

}
