/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-10-26 18:18:36
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-10-26 18:18:36<br/>
 * Description: 佣金递延准备金明细表<br/>
 * Table Name: atr_buss_reserve_dac_detail<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "佣金递延准备金明细表")
public class AtrBussReserveDacDetail implements Serializable {
    /**
     * Database column: atr_buss_reserve_dac_detail.dac_detail_id
     * Database remarks: 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    private Long dacDetailId;

    /**
     * Database column: atr_buss_reserve_dac_detail.reserve_dac_id
     * Database remarks: 主表id
     */
    @ApiModelProperty(value = "主表id", required = true)
    private Long reserveDacId;

    /**
     * Database column: atr_buss_reserve_dac_detail.risk_class_code
     * Database remarks: risk_class_code|险类
     */
    @ApiModelProperty(value = "risk_class_code|险类", required = false)
    private String riskClassCode;

    /**
     * Database column: atr_buss_reserve_dac_detail.risk_code
     * Database remarks: 险种
     */
    @ApiModelProperty(value = "险种", required = false)
    private String riskCode;

    /**
     * Database column: atr_buss_reserve_dac_detail.cmunit_no
     * Database remarks: 计量单元号
     */
    @ApiModelProperty(value = "计量单元号", required = false)
    private String cmunitNo;

    /**
     * Database column: atr_buss_reserve_dac_detail.policy_no
     * Database remarks: 保单号
     */
    @ApiModelProperty(value = "保单号", required = false)
    private String policyNo;

    /**
     * Database column: atr_buss_reserve_dac_detail.endorse_seq_no
     * Database remarks: 批改序号
     */
    @ApiModelProperty(value = "批改序号", required = false)
    private String endorseSeqNo;

    /**
     * Database column: atr_buss_reserve_dac_detail.endorse_no
     * Database remarks: endorse_no|批单号
     */
    @ApiModelProperty(value = "endorse_no|批单号", required = false)
    private String endorseNo;

    /**
     * Database column: atr_buss_reserve_dac_detail.business_source_code
     * Database remarks: business_source_code|业务类型
     */
    @ApiModelProperty(value = "business_source_code|业务类型", required = false)
    private String businessSourceCode;

    /**
     * Database column: atr_buss_reserve_dac_detail.reins_type
     * Database remarks: reins_type|再保类型(DN直保，RN再保)
     */
    @ApiModelProperty(value = "reins_type|再保类型(DN直保，RN再保)", required = false)
    private String reinsType;

    /**
     * Database column: atr_buss_reserve_dac_detail.start_date
     * Database remarks: start_date|开始时间
     */
    @ApiModelProperty(value = "start_date|开始时间", required = false)
    private Date startDate;

    /**
     * Database column: atr_buss_reserve_dac_detail.end_date
     * Database remarks: end_date|结束时间
     */
    @ApiModelProperty(value = "end_date|结束时间", required = false)
    private Date endDate;

    /**
     * Database column: atr_buss_reserve_dac_detail.currency_code
     * Database remarks: currency_code|币别
     */
    @ApiModelProperty(value = "currency_code|币别", required = false)
    private String currencyCode;

    /**
     * Database column: atr_buss_reserve_dac_detail.commission
     * Database remarks: commission|佣金
     */
    @ApiModelProperty(value = "commission|佣金", required = false)
    private BigDecimal commission;

    /**
     * Database column: atr_buss_reserve_dac_detail.draw_commission
     * Database remarks: draw_commission|未了责任佣金
     */
    @ApiModelProperty(value = "draw_commission|未了责任佣金", required = false)
    private BigDecimal drawCommission;

    /**
     * Database column: atr_buss_reserve_dac_detail.draw_commission_adjust
     * Database remarks: draw_commission_adjust|提取佣金
     */
    @ApiModelProperty(value = "draw_commission_adjust|提取佣金", required = false)
    private BigDecimal drawCommissionAdjust;

    /**
     * Database column: atr_buss_reserve_dac_detail.icg_no
     * Database remarks: 合同组号码
     */
    @ApiModelProperty(value = "合同组号码", required = false)
    private String icgNo;

    /**
     * Database column: atr_buss_reserve_dac_detail.approval_date
     * Database remarks: approval_date|审核日期
     */
    @ApiModelProperty(value = "approval_date|审核日期", required = false)
    private Date approvalDate;

    /**
     * Database column: atr_buss_reserve_dac_detail.atr_method
     * Database remarks: Atr Method|计提方法
     */
    @ApiModelProperty(value = "Atr Method|计提方法", required = false)
    private String atrMethod;

    /**
     * Database column: atr_buss_reserve_dac_detail.loa_code
     * Database remarks: loa_code|业务线
     */
    @ApiModelProperty(value = "loa_code|业务线", required = false)
    private String loaCode;

    private static final long serialVersionUID = 1L;

    public Long getDacDetailId() {
        return dacDetailId;
    }

    public void setDacDetailId(Long dacDetailId) {
        this.dacDetailId = dacDetailId;
    }

    public Long getReserveDacId() {
        return reserveDacId;
    }

    public void setReserveDacId(Long reserveDacId) {
        this.reserveDacId = reserveDacId;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public String getRiskCode() {
        return riskCode;
    }

    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    public String getCmunitNo() {
        return cmunitNo;
    }

    public void setCmunitNo(String cmunitNo) {
        this.cmunitNo = cmunitNo;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getEndorseSeqNo() {
        return endorseSeqNo;
    }

    public void setEndorseSeqNo(String endorseSeqNo) {
        this.endorseSeqNo = endorseSeqNo;
    }

    public String getEndorseNo() {
        return endorseNo;
    }

    public void setEndorseNo(String endorseNo) {
        this.endorseNo = endorseNo;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getReinsType() {
        return reinsType;
    }

    public void setReinsType(String reinsType) {
        this.reinsType = reinsType;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public BigDecimal getCommission() {
        return commission;
    }

    public void setCommission(BigDecimal commission) {
        this.commission = commission;
    }

    public BigDecimal getDrawCommission() {
        return drawCommission;
    }

    public void setDrawCommission(BigDecimal drawCommission) {
        this.drawCommission = drawCommission;
    }

    public BigDecimal getDrawCommissionAdjust() {
        return drawCommissionAdjust;
    }

    public void setDrawCommissionAdjust(BigDecimal drawCommissionAdjust) {
        this.drawCommissionAdjust = drawCommissionAdjust;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public Date getApprovalDate() {
        return approvalDate;
    }

    public void setApprovalDate(Date approvalDate) {
        this.approvalDate = approvalDate;
    }

    public String getAtrMethod() {
        return atrMethod;
    }

    public void setAtrMethod(String atrMethod) {
        this.atrMethod = atrMethod;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }
}