package com.ss.ifrs.actuarial.conf;

import com.ss.library.mybatis.config.BaseDataSourceConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * HikariCP连接池配置
 */
@Configuration
public class DataSourceConfig extends BaseDataSourceConfig {

	@Bean
	public DataSource dataSource() {
		return super.dataSource();
	}
}