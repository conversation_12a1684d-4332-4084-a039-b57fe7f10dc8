/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-03-09 10:18:30
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName: AtrBussLicStageVo
 * @Description: Lic计量比例数据返回对象
 * @Author: OQJ
 * @CreateDate: 2021/3/13 18:32
 * @Version: 1.0
 */
public class AtrBussLicStageVo implements Serializable {
   
    
    /**
     * 主表ID
     */
    private Long licMainId;
    
    private String yearMonth;
    
    private String icgNo;
    
    /**
     * 系数类型
     */
    private String stageType;
    
    private String stageCName;
    private String stageLName;
    private String stageEName;
    
    /*
         * 占比
     */
    private Double stageRate;
    
    private int leg = 4;
    
    /**
     * 发展年月list
     */
    private List<AtrBussLicQueryDevYearMonthVo> devYearMonthList;
    
    /**
        * 辅助计算列
    */
    private BigDecimal lossRate;
       
    private static final long serialVersionUID = 1L;

	public Long getLicMainId() {
		return licMainId;
	}

	public void setLicMainId(Long licMainId) {
		this.licMainId = licMainId;
	}

	public String getYearMonth() {
		return yearMonth;
	}

	public void setYearMonth(String yearMonth) {
		this.yearMonth = yearMonth;
	}

	public String getIcgNo() {
		return icgNo;
	}

	public void setIcgNo(String icgNo) {
		this.icgNo = icgNo;
	}

	public String getStageType() {
		return stageType;
	}

	public void setStageType(String stageType) {
		this.stageType = stageType;
	}

	public String getStageCName() {
		return stageCName;
	}

	public void setStageCName(String stageCName) {
		this.stageCName = stageCName;
	}

	public String getStageLName() {
		return stageLName;
	}

	public void setStageLName(String stageLName) {
		this.stageLName = stageLName;
	}

	public String getStageEName() {
		return stageEName;
	}

	public void setStageEName(String stageEName) {
		this.stageEName = stageEName;
	}

	public Double getStageRate() {
		return stageRate;
	}

	public void setStageRate(Double stageRate) {
		this.stageRate = stageRate;
	}

	public List<AtrBussLicQueryDevYearMonthVo> getDevYearMonthList() {
		return devYearMonthList;
	}

	public void setDevYearMonthList(List<AtrBussLicQueryDevYearMonthVo> devYearMonthList) {
		this.devYearMonthList = devYearMonthList;
	}

	public BigDecimal getLossRate() {
		return lossRate;
	}

	public void setLossRate(BigDecimal lossRate) {
		this.lossRate = lossRate;
	}

	public int getLeg() {
		return leg;
	}

	public void setLeg(int leg) {
		this.leg = leg;
	}
	
}