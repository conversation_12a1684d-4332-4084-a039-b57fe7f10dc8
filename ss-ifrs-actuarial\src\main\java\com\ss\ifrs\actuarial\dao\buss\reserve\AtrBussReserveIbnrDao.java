/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2022-06-06 10:15:00
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao.buss.reserve;

import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveIbnr;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveIbnrVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2022-06-06 10:15:00<br/>
 * Description: IBNR提取主表 Dao类<br/>
 * Related Table Name: atr_buss_reserve_ibnr<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrBussReserveIbnrDao extends IDao<AtrBussReserveIbnr, Long> {

    Page<AtrBussReserveIbnrVo> fuzzySearchPage(AtrBussReserveIbnrVo atrBussReserveIbnrVo, Pageable pageParam);

    AtrBussReserveIbnrVo findByReserveIbnrId(Long reserveIbnrId);

    void ibnrDataGenerate(Long reserveIbnrId);

    List<AtrBussReserveIbnrVo> checkIbnrConfirm(AtrBussReserveIbnrVo atrBussReserveIbnrVo);

    void syncIbnrImport(Map<String, Object> paramMap);

    void syncIbnrImportDetail(Map<String, Object> paramMap);
}