package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 合约信息
 */
@Data
public class AtrDapTreaty {

    /** 合约号 */
    private String treatyNo;
    private String sectionoCode;
    private String reinsurerCode;
    /** 合约名称 */
    private String treatyName;
    private String riskClassCodes;
    private String riskCode;
    /** 合同组合号码 */
    private String portfolioNo;
    /** 合同组号码 */
    private String icgNo;
    /** 合同组名称 */
    private String icgName;
    /** 计量单元编号 */
    private String cmunitNo;
    private String companyCode1;
    private String companyCode2;
    private String companyCode3;
    private String companyCode4;
    private String riskCategoryCode;
    private String businessClass;
    private String businessNature;
    /** 财务渠道 */
    private String finAccChannel;
    /** 财务产品代码 */
    private String finProductCode;
    /** 财务明细代码 */
    private String finDetailCode;
    /** 财务子产品代码 */
    private String finSubProductCode;
    /** 盈亏判定结果 */
    private String plJudgeRslt;
    private String agentCode;
    private String salesmanCode;
    /** 合约起始日期 */
    private Date effectiveDate;
    /** 合约终止日期 */
    private Date expiryDate;
    /** 合同确认日期 */
    private Date contractDate;
    /** 合同签发日 */
    private Date issueDate;
    /** 经纪费率 */
    private BigDecimal brokerageRate;
    /** 固定手续费率 */
    private BigDecimal fixedFeeRate;
    /** 预付手续费率 */
    private BigDecimal prepaidFeeRate;
    /** 浮动手续费率上限 */
    private BigDecimal floatingHandlingFeeCap;
    /** 纯益手续费率 */
    private BigDecimal profitFeeRate;
    /** 纯益管理费率 */
    private BigDecimal profitMngFeeRate;
    /** 预估保费 */
    private BigDecimal estPremium;


}
