<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-09-05 16:09:55 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.conf.AtrConfAutoquotaWeightDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**BaseDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfAutoquotaWeight">
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="loa_code" property="loaCode" jdbcType="VARCHAR" />
    <result column="offset_years" property="offsetYears" jdbcType="SMALLINT" />
    <result column="weight_value" property="weightValue" jdbcType="NUMERIC" />
    <result column="oper_id" property="operId" jdbcType="BIGINT" />
    <result column="oper_time" property="operTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    id, loa_code, offset_years, weight_value, oper_id, oper_time
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="id != null ">
          and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="loaCode != null and loaCode != ''">
          and loa_code = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="offsetYears != null ">
          and offset_years = #{offsetYears,jdbcType=SMALLINT}
      </if>
      <if test="weightValue != null ">
          and weight_value = #{weightValue,jdbcType=NUMERIC}
      </if>
      <if test="operId != null ">
          and oper_id = #{operId,jdbcType=BIGINT}
      </if>
      <if test="operTime != null ">
          and oper_time = #{operTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.id != null ">
          and id = #{condition.id,jdbcType=BIGINT}
      </if>
      <if test="condition.loaCode != null and condition.loaCode != ''">
          and loa_code = #{condition.loaCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.offsetYears != null ">
          and offset_years = #{condition.offsetYears,jdbcType=SMALLINT}
      </if>
      <if test="condition.weightValue != null ">
          and weight_value = #{condition.weightValue,jdbcType=NUMERIC}
      </if>
      <if test="condition.operId != null ">
          and oper_id = #{condition.operId,jdbcType=BIGINT}
      </if>
      <if test="condition.operTime != null ">
          and oper_time = #{condition.operTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="id != null ">
          and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="loaCode != null and loaCode != ''">
          and loa_code = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="offsetYears != null ">
          and offset_years = #{offsetYears,jdbcType=SMALLINT}
      </if>
      <if test="weightValue != null ">
          and weight_value = #{weightValue,jdbcType=NUMERIC}
      </if>
      <if test="operId != null ">
          and oper_id = #{operId,jdbcType=BIGINT}
      </if>
      <if test="operTime != null ">
          and oper_time = #{operTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from "atr_conf_autoquota_weight"
    where id = #{id,jdbcType=BIGINT}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from "atr_conf_autoquota_weight"
    where id in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "atr_conf_autoquota_weight"
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfAutoquotaWeight">
    select 
    <include refid="Base_Column_List" />
    from "atr_conf_autoquota_weight"
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from "atr_conf_autoquota_weight"
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="id" keyProperty="id" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfAutoquotaWeight">
    insert into "atr_conf_autoquota_weight"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="loaCode != null">
        loa_code,
      </if>
      <if test="offsetYears != null">
        offset_years,
      </if>
      <if test="weightValue != null">
        weight_value,
      </if>
      <if test="operId != null">
        oper_id,
      </if>
      <if test="operTime != null">
        oper_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="loaCode != null">
        #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="offsetYears != null">
        #{offsetYears,jdbcType=SMALLINT},
      </if>
      <if test="weightValue != null">
        #{weightValue,jdbcType=NUMERIC},
      </if>
      <if test="operId != null">
        #{operId,jdbcType=BIGINT},
      </if>
      <if test="operTime != null">
        #{operTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert into "atr_conf_autoquota_weight"
     (id, loa_code, offset_years, 
      weight_value, oper_id, oper_time
      )
     values 
    <foreach collection="list" item="item" index="index" separator=",">
       (#{item.id,jdbcType=BIGINT}, #{item.loaCode,jdbcType=VARCHAR}, #{item.offsetYears,jdbcType=SMALLINT}, 
        #{item.weightValue,jdbcType=NUMERIC}, #{item.operId,jdbcType=BIGINT}, #{item.operTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfAutoquotaWeight">
    update "atr_conf_autoquota_weight"
    <set>
      <if test="loaCode != null">
        loa_code = #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="offsetYears != null">
        offset_years = #{offsetYears,jdbcType=SMALLINT},
      </if>
      <if test="weightValue != null">
        weight_value = #{weightValue,jdbcType=NUMERIC},
      </if>
      <if test="operId != null">
        oper_id = #{operId,jdbcType=BIGINT},
      </if>
      <if test="operTime != null">
        oper_time = #{operTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfAutoquotaWeight">
    update "atr_conf_autoquota_weight"
    <set>
      <if test="record.loaCode != null">
        loa_code = #{record.loaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.offsetYears != null">
        offset_years = #{record.offsetYears,jdbcType=SMALLINT},
      </if>
      <if test="record.weightValue != null">
        weight_value = #{record.weightValue,jdbcType=NUMERIC},
      </if>
      <if test="record.operId != null">
        oper_id = #{record.operId,jdbcType=BIGINT},
      </if>
      <if test="record.operTime != null">
        oper_time = #{record.operTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from "atr_conf_autoquota_weight"
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from "atr_conf_autoquota_weight"
    where id in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from "atr_conf_autoquota_weight"
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfAutoquotaWeight">
    select count(1) from "atr_conf_autoquota_weight"
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>