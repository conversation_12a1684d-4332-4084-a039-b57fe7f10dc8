package com.ss.ifrs.actuarial.pojo.ecf.vo.lic;

import com.ss.ifrs.actuarial.util.abp.IgnoreCol;
import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Tab("atr_buss_lic_u")
public class AtrBussLicU {

    private Long id;

    private String actionNo;

    private Long entityId;

    private String yearMonth;

    private String businessSourceCode;

    private String riskClassCode;

    private String portfolioNo;

    private String icgNo;

    private String policyNo;

    private String kindCode;

    private String treatyNo;

    private String accYearMonth;

    private String riDept;
    
    private String deptId;
    
    private String channelId;
    
    private String centerCode;
    
    private String finProductCode;
    
    private String finDetailCode;
    
    private String finSubProductCode;
    
    private String finAccChannel;
    
    private String companyCode4;
    
    private String plJudgeRslt;

    @IgnoreCol
    private Integer offsetMonths;
    @IgnoreCol
    private String cfType;
    @IgnoreCol
    private BigDecimal amount;

}
