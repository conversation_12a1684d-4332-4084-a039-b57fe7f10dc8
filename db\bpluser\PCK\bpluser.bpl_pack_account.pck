CREATE OR REPLACE PACKAGE bpl_pack_account IS
    --处理新增科目
    PROCEDURE conf_account(p_userid varchar2);
    PROCEDURE conf_account_map(p_userid varchar2);
END bpl_pack_account;
/
CREATE OR REPLACE PACKAGE BODY bpl_pack_account IS

    PROCEDURE conf_account(p_userid varchar2) IS
        v_count     NUMBER(1);
        v_entity_id NUMBER(11);
        cursor cur_level is
            SELECT t.ACCOUNT_LEVEL
            FROM bbs_conf_coa_imp t
            where t.base_account_code is not null
            group by t.ACCOUNT_LEVEL
            order by t.ACCOUNT_LEVEL;
        rec_level cur_level%rowtype;

        cursor cur_deal_level is
            SELECT t.ACCOUNT_LEVEL
            FROM bbs_conf_coa_deal t
            group by t.ACCOUNT_LEVEL
            order by t.ACCOUNT_LEVEL;
        rec_deal_level cur_deal_level%rowtype;

    BEGIN
        v_count := 0;
        -- 更新entity_id
        for rec_entity_code in (select distinct entity_code
                                from bbs_conf_coa_imp) loop
                if rec_entity_code.entity_code is not null and
                   rec_entity_code.entity_code <> '' then
                    select max(entity_id)
                    into v_entity_id
                    from bbs_entity
                    where entity_code = rec_entity_code.entity_code;
                    if v_entity_id is not null then
                        update bbs_conf_coa_imp
                        set entity_id = v_entity_id
                        where entity_code = rec_entity_code.entity_code;
                        v_entity_id := null;
                    end if;
                end if;

            end loop;
        commit;
        delete from bbs_conf_coa_deal;
        for rec_level in cur_level loop

                if rec_level.ACCOUNT_LEVEL >= 1 then
                    insert into bbs_conf_coa_deal
                    (ID,
                     ACCOUNT_LEVEL,
                     ENTITY_ID,
                     BOOK_CODE,
                     ACCOUNT_CODE,
                     ACCOUNT_E_NAME,
                     ACCOUNT_C_NAME,
                     ACCOUNT_L_NAME,
                     FINAL_LEVEL_IS,
                     ACCOUNT_CATEGORY_CODE,
                     ACCOUNT_ENTRY_CODE,
                     ACCOUNT_CODE_IDX,
                     UPPER_ACCOUNT_CODE_IDX)
                    SELECT bbs_seq_conf_coa_deal.NEXTVAL as ID,
                           1 as ACCOUNT_LEVEL,
                           coalesce(t.entity_id, 1) as ENTITY_ID,
                           'BookI17' as BOOK_CODE,
                           t.account_code1 as ACCOUNT_CODE,
                           t.account_e_name1 as ACCOUNT_E_NAME,
                           t.account_c_name1 as ACCOUNT_C_NAME,
                           t.account_l_name1 as ACCOUNT_L_NAME,
                           decode(t.FINAL_LEVEL_IS, 'Y', '1', '0') as FINAL_LEVEL_IS,
                           (case
                                when trim(t.base_account_category_code) in ('资产类', '資產類', 'Asset') then
                                    '1'
                                when trim(t.base_account_category_code) in
                                     ('负债类', '負債類', 'Liability') then
                                    '2'
                                when trim(t.base_account_category_code) in ('权益类', '權益類', 'Equity') then
                                    '3'
                                when trim(t.base_account_category_code) in
                                     ('损益类', '損益類', 'Profit or Loss') then
                                    '4'
                                when trim(t.base_account_category_code) in ('公共类', '公共類', 'Public') then
                                    '5'
                                else
                                    ''
                               end) as ACCOUNT_CATEGORY_CODE,
                           decode(t.FINAL_LEVEL_IS,
                                  'Y',
                                  decode(t.account_entry_code,
                                         '借',
                                         'D',
                                         'Debit',
                                         'D',
                                         'C'),
                                  'C') as ACCOUNT_ENTRY_CODE,
                           substr(t.base_account_code, 1, 4) || '/' as ACCOUNT_CODE_IDX,
                           '' as UPPER_ACCOUNT_CODE_IDX
                    FROM bbs_conf_coa_imp t
                    where base_account_code is not null;
                end if;

                if rec_level.account_level >= 2 then
                    insert into bbs_conf_coa_deal
                    (ID,
                     ACCOUNT_LEVEL,
                     ENTITY_ID,
                     BOOK_CODE,
                     ACCOUNT_CODE,
                     ACCOUNT_E_NAME,
                     ACCOUNT_C_NAME,
                     ACCOUNT_L_NAME,
                     FINAL_LEVEL_IS,
                     ACCOUNT_CATEGORY_CODE,
                     ACCOUNT_ENTRY_CODE,
                     ACCOUNT_CODE_IDX,
                     UPPER_ACCOUNT_CODE_IDX)
                    SELECT bbs_seq_conf_coa_deal.NEXTVAL as ID,
                           2 as ACCOUNT_LEVEL,
                           coalesce(t.entity_id, 1) as ENTITY_ID,
                           'BookI17' as BOOK_CODE,
                           t.account_code2 as ACCOUNT_CODE,
                           t.account_e_name2 as ACCOUNT_E_NAME,
                           t.account_c_name2 as ACCOUNT_C_NAME,
                           t.account_l_name2 as ACCOUNT_L_NAME,
                           decode(t.FINAL_LEVEL_IS, 'Y', '1', '0') as FINAL_LEVEL_IS,
                           (case
                                when trim(t.base_account_category_code) in
                                     ('资产类', '資產類', 'Asset') then
                                    '1'
                                when trim(t.base_account_category_code) in
                                     ('负债类', '負債類', 'Liability') then
                                    '2'
                                when trim(t.base_account_category_code) in
                                     ('权益类', '權益類', 'Equity') then
                                    '3'
                                when trim(t.base_account_category_code) in
                                     ('损益类', '損益類', 'Profit or Loss') then
                                    '4'
                                when trim(t.base_account_category_code) in
                                     ('公共类', '公共類', 'Public') then
                                    '5'
                                else
                                    ''
                               end) as ACCOUNT_CATEGORY_CODE,
                           decode(t.FINAL_LEVEL_IS,
                                  'Y',
                                  decode(t.account_entry_code,
                                         '借',
                                         'D',
                                         'Debit',
                                         'D',
                                         'C'),
                                  'C') as ACCOUNT_ENTRY_CODE,
                           substr(t.base_account_code, 1, 8) as ACCOUNT_CODE_IDX,
                           substr(t.base_account_code, 1, 5) as UPPER_ACCOUNT_CODE_IDX
                    FROM bbs_conf_coa_imp t
                    where t.account_level >= 2
                      and base_account_code is not null;
                end if;

                if rec_level.account_level >= 3 then
                    insert into bbs_conf_coa_deal
                    (ID,
                     ACCOUNT_LEVEL,
                     ENTITY_ID,
                     BOOK_CODE,
                     ACCOUNT_CODE,
                     ACCOUNT_E_NAME,
                     ACCOUNT_C_NAME,
                     ACCOUNT_L_NAME,
                     FINAL_LEVEL_IS,
                     ACCOUNT_CATEGORY_CODE,
                     ACCOUNT_ENTRY_CODE,
                     ACCOUNT_CODE_IDX,
                     UPPER_ACCOUNT_CODE_IDX)
                    SELECT bbs_seq_conf_coa_deal.NEXTVAL as ID,
                           3 as ACCOUNT_LEVEL,
                           coalesce(t.entity_id, 1) as ENTITY_ID,
                           'BookI17' as BOOK_CODE,
                           t.account_code3 as ACCOUNT_CODE,
                           t.account_e_name3 as ACCOUNT_E_NAME,
                           t.account_c_name3 as ACCOUNT_C_NAME,
                           t.account_l_name3 as ACCOUNT_L_NAME,
                           decode(t.FINAL_LEVEL_IS, 'Y', '1', '0') as FINAL_LEVEL_IS,
                           (case
                                when trim(t.base_account_category_code) in
                                     ('资产类', '資產類', 'Asset') then
                                    '1'
                                when trim(t.base_account_category_code) in
                                     ('负债类', '負債類', 'Liability') then
                                    '2'
                                when trim(t.base_account_category_code) in
                                     ('权益类', '權益類', 'Equity') then
                                    '3'
                                when trim(t.base_account_category_code) in
                                     ('损益类', '損益類', 'Profit or Loss') then
                                    '4'
                                when trim(t.base_account_category_code) in
                                     ('公共类', '公共類', 'Public') then
                                    '5'
                                else
                                    ''
                               end) as ACCOUNT_CATEGORY_CODE,
                           decode(t.FINAL_LEVEL_IS,
                                  'Y',
                                  decode(t.account_entry_code,
                                         '借',
                                         'D',
                                         'Debit',
                                         'D',
                                         'C'),
                                  'C') as ACCOUNT_ENTRY_CODE,
                           substr(t.base_account_code, 1, 11) as ACCOUNT_CODE_IDX,
                           substr(t.base_account_code, 1, 8) as UPPER_ACCOUNT_CODE_IDX
                    FROM bbs_conf_coa_imp t
                    where t.account_level >= 3
                      and base_account_code is not null;
                end if;

                if rec_level.account_level >= 4 then
                    insert into bbs_conf_coa_deal
                    (ID,
                     ACCOUNT_LEVEL,
                     ENTITY_ID,
                     BOOK_CODE,
                     ACCOUNT_CODE,
                     ACCOUNT_E_NAME,
                     ACCOUNT_C_NAME,
                     ACCOUNT_L_NAME,
                     FINAL_LEVEL_IS,
                     ACCOUNT_CATEGORY_CODE,
                     ACCOUNT_ENTRY_CODE,
                     ACCOUNT_CODE_IDX,
                     UPPER_ACCOUNT_CODE_IDX)
                    SELECT bbs_seq_conf_coa_deal.NEXTVAL as ID,
                           4 as ACCOUNT_LEVEL,
                           coalesce(t.entity_id, 1) as ENTITY_ID,
                           'BookI17' as BOOK_CODE,
                           t.account_code4 as ACCOUNT_CODE,
                           t.account_e_name4 as ACCOUNT_E_NAME,
                           t.account_c_name4 as ACCOUNT_C_NAME,
                           t.account_l_name4 as ACCOUNT_L_NAME,
                           decode(t.FINAL_LEVEL_IS, 'Y', '1', '0') as FINAL_LEVEL_IS,
                           (case
                                when trim(t.base_account_category_code) in
                                     ('资产类', '資產類', 'Asset') then
                                    '1'
                                when trim(t.base_account_category_code) in
                                     ('负债类', '負債類', 'Liability') then
                                    '2'
                                when trim(t.base_account_category_code) in
                                     ('权益类', '權益類', 'Equity') then
                                    '3'
                                when trim(t.base_account_category_code) in
                                     ('损益类', '損益類', 'Profit or Loss') then
                                    '4'
                                when trim(t.base_account_category_code) in
                                     ('公共类', '公共類', 'Public') then
                                    '5'
                                else
                                    ''
                               end) as ACCOUNT_CATEGORY_CODE,
                           decode(t.FINAL_LEVEL_IS,
                                  'Y',
                                  decode(t.account_entry_code,
                                         '借',
                                         'D',
                                         'Debit',
                                         'D',
                                         'C'),
                                  'C') as ACCOUNT_ENTRY_CODE,
                           substr(t.base_account_code, 1, 14) as ACCOUNT_CODE_IDX,
                           substr(t.base_account_code, 1, 11) as UPPER_ACCOUNT_CODE_IDX
                    FROM bbs_conf_coa_imp t
                    where t.account_level >= 4
                      and base_account_code is not null;
                end if;

                if rec_level.account_level >= 5 then
                    insert into bbs_conf_coa_deal
                    (ID,
                     ACCOUNT_LEVEL,
                     ENTITY_ID,
                     BOOK_CODE,
                     ACCOUNT_CODE,
                     ACCOUNT_E_NAME,
                     ACCOUNT_C_NAME,
                     ACCOUNT_L_NAME,
                     FINAL_LEVEL_IS,
                     ACCOUNT_CATEGORY_CODE,
                     ACCOUNT_ENTRY_CODE,
                     ACCOUNT_CODE_IDX,
                     UPPER_ACCOUNT_CODE_IDX)
                    SELECT bbs_seq_conf_coa_deal.NEXTVAL as ID,
                           5 as ACCOUNT_LEVEL,
                           coalesce(t.entity_id, 1) as ENTITY_ID,
                           'BookI17' as BOOK_CODE,
                           t.account_code5 as ACCOUNT_CODE,
                           t.account_e_name5 as ACCOUNT_E_NAME,
                           t.account_c_name5 as ACCOUNT_C_NAME,
                           t.account_l_name5 as ACCOUNT_L_NAME,
                           decode(t.FINAL_LEVEL_IS, 'Y', '1', '0') as FINAL_LEVEL_IS,
                           (case
                                when trim(t.base_account_category_code) in
                                     ('资产类', '資產類', 'Asset') then
                                    '1'
                                when trim(t.base_account_category_code) in
                                     ('负债类', '負債類', 'Liability') then
                                    '2'
                                when trim(t.base_account_category_code) in
                                     ('权益类', '權益類', 'Equity') then
                                    '3'
                                when trim(t.base_account_category_code) in
                                     ('损益类', '損益類', 'Profit or Loss') then
                                    '4'
                                when trim(t.base_account_category_code) in
                                     ('公共类', '公共類', 'Public') then
                                    '5'
                                else
                                    ''
                               end) as ACCOUNT_CATEGORY_CODE,
                           decode(t.FINAL_LEVEL_IS,
                                  'Y',
                                  decode(t.account_entry_code,
                                         '借',
                                         'D',
                                         'Debit',
                                         'D',
                                         'C'),
                                  'C') as ACCOUNT_ENTRY_CODE,
                           substr(t.base_account_code, 1, 17) as ACCOUNT_CODE_IDX,
                           substr(t.base_account_code, 1, 14) as UPPER_ACCOUNT_CODE_IDX
                    FROM bbs_conf_coa_imp t
                    where t.account_level >= 5
                      and base_account_code is not null;
                end if;

                if rec_level.account_level >= 6 then
                    insert into bbs_conf_coa_deal
                    (ID,
                     ACCOUNT_LEVEL,
                     ENTITY_ID,
                     BOOK_CODE,
                     ACCOUNT_CODE,
                     ACCOUNT_E_NAME,
                     ACCOUNT_C_NAME,
                     ACCOUNT_L_NAME,
                     FINAL_LEVEL_IS,
                     ACCOUNT_CATEGORY_CODE,
                     ACCOUNT_ENTRY_CODE,
                     ACCOUNT_CODE_IDX,
                     UPPER_ACCOUNT_CODE_IDX)
                    SELECT bbs_seq_conf_coa_deal.NEXTVAL as ID,
                           6 as ACCOUNT_LEVEL,
                           coalesce(t.entity_id, 1) as ENTITY_ID,
                           'BookI17' as BOOK_CODE,
                           t.account_code6 as ACCOUNT_CODE,
                           t.account_e_name6 as ACCOUNT_E_NAME,
                           t.account_c_name6 as ACCOUNT_C_NAME,
                           t.account_l_name6 as ACCOUNT_L_NAME,
                           decode(t.FINAL_LEVEL_IS, 'Y', '1', '0') as FINAL_LEVEL_IS,
                           (case
                                when trim(t.base_account_category_code) in
                                     ('资产类', '資產類', 'Asset') then
                                    '1'
                                when trim(t.base_account_category_code) in
                                     ('负债类', '負債類', 'Liability') then
                                    '2'
                                when trim(t.base_account_category_code) in
                                     ('权益类', '權益類', 'Equity') then
                                    '3'
                                when trim(t.base_account_category_code) in
                                     ('损益类', '損益類', 'Profit or Loss') then
                                    '4'
                                when trim(t.base_account_category_code) in
                                     ('公共类', '公共類', 'Public') then
                                    '5'
                                else
                                    ''
                               end) as ACCOUNT_CATEGORY_CODE,
                           decode(t.FINAL_LEVEL_IS,
                                  'Y',
                                  decode(t.account_entry_code,
                                         '借',
                                         'D',
                                         'Debit',
                                         'D',
                                         'C'),
                                  'C') as ACCOUNT_ENTRY_CODE,
                           substr(t.base_account_code, 1, 20) as ACCOUNT_CODE_IDX,
                           substr(t.base_account_code, 1, 17) as UPPER_ACCOUNT_CODE_IDX
                    FROM bbs_conf_coa_imp t
                    where t.account_level >= 6
                      and base_account_code is not null;
                end if;

                if rec_level.account_level >= 7 then
                    insert into bbs_conf_coa_deal
                    (ID,
                     ACCOUNT_LEVEL,
                     ENTITY_ID,
                     BOOK_CODE,
                     ACCOUNT_CODE,
                     ACCOUNT_E_NAME,
                     ACCOUNT_C_NAME,
                     ACCOUNT_L_NAME,
                     FINAL_LEVEL_IS,
                     ACCOUNT_CATEGORY_CODE,
                     ACCOUNT_ENTRY_CODE,
                     ACCOUNT_CODE_IDX,
                     UPPER_ACCOUNT_CODE_IDX)
                    SELECT bbs_seq_conf_coa_deal.NEXTVAL as ID,
                           7 as ACCOUNT_LEVEL,
                           coalesce(t.entity_id, 1) as ENTITY_ID,
                           'BookI17' as BOOK_CODE,
                           t.account_code7 as ACCOUNT_CODE,
                           t.account_e_name7 as ACCOUNT_E_NAME,
                           t.account_c_name7 as ACCOUNT_C_NAME,
                           t.account_l_name7 as ACCOUNT_L_NAME,
                           decode(t.FINAL_LEVEL_IS, 'Y', '1', '0') as FINAL_LEVEL_IS,
                           (case
                                when trim(t.base_account_category_code) in
                                     ('资产类', '資產類', 'Asset') then
                                    '1'
                                when trim(t.base_account_category_code) in
                                     ('负债类', '負債類', 'Liability') then
                                    '2'
                                when trim(t.base_account_category_code) in
                                     ('权益类', '權益類', 'Equity') then
                                    '3'
                                when trim(t.base_account_category_code) in
                                     ('损益类', '損益類', 'Profit or Loss') then
                                    '4'
                                when trim(t.base_account_category_code) in
                                     ('公共类', '公共類', 'Public') then
                                    '5'
                                else
                                    ''
                               end) as ACCOUNT_CATEGORY_CODE,
                           decode(t.FINAL_LEVEL_IS,
                                  'Y',
                                  decode(t.account_entry_code,
                                         '借',
                                         'D',
                                         'Debit',
                                         'D',
                                         'C'),
                                  'C') as ACCOUNT_ENTRY_CODE,
                           substr(t.base_account_code, 1, 23) as ACCOUNT_CODE_IDX,
                           substr(t.base_account_code, 1, 20) as UPPER_ACCOUNT_CODE_IDX
                    FROM bbs_conf_coa_imp t
                    where t.account_level >= 7
                      and base_account_code is not null;
                end if;

                if rec_level.account_level >= 8 then
                    insert into bbs_conf_coa_deal
                    (ID,
                     ACCOUNT_LEVEL,
                     ENTITY_ID,
                     BOOK_CODE,
                     ACCOUNT_CODE,
                     ACCOUNT_E_NAME,
                     ACCOUNT_C_NAME,
                     ACCOUNT_L_NAME,
                     FINAL_LEVEL_IS,
                     ACCOUNT_CATEGORY_CODE,
                     ACCOUNT_ENTRY_CODE,
                     ACCOUNT_CODE_IDX,
                     UPPER_ACCOUNT_CODE_IDX)
                    SELECT bbs_seq_conf_coa_deal.NEXTVAL as ID,
                           8 as ACCOUNT_LEVEL,
                           coalesce(t.entity_id, 1) as ENTITY_ID,
                           'BookI17' as BOOK_CODE,
                           t.account_code8 as ACCOUNT_CODE,
                           t.account_e_name8 as ACCOUNT_E_NAME,
                           t.account_c_name8 as ACCOUNT_C_NAME,
                           t.account_l_name8 as ACCOUNT_L_NAME,
                           decode(t.FINAL_LEVEL_IS, 'Y', '1', '0') as FINAL_LEVEL_IS,
                           (case
                                when trim(t.base_account_category_code) in
                                     ('资产类', '資產類', 'Asset') then
                                    '1'
                                when trim(t.base_account_category_code) in
                                     ('负债类', '負債類', 'Liability') then
                                    '2'
                                when trim(t.base_account_category_code) in
                                     ('权益类', '權益類', 'Equity') then
                                    '3'
                                when trim(t.base_account_category_code) in
                                     ('损益类', '損益類', 'Profit or Loss') then
                                    '4'
                                when trim(t.base_account_category_code) in
                                     ('公共类', '公共類', 'Public') then
                                    '5'
                                else
                                    ''
                               end) as ACCOUNT_CATEGORY_CODE,
                           decode(t.FINAL_LEVEL_IS,
                                  'Y',
                                  decode(t.account_entry_code,
                                         '借',
                                         'D',
                                         'Debit',
                                         'D',
                                         'C'),
                                  'C') as ACCOUNT_ENTRY_CODE,
                           substr(t.base_account_code, 1, 26) as ACCOUNT_CODE_IDX,
                           substr(t.base_account_code, 1, 23) as UPPER_ACCOUNT_CODE_IDX
                    FROM bbs_conf_coa_imp t
                    where t.account_level >= 8
                      and base_account_code is not null;
                end if;

            end loop;

        --删除重复数据
        DELETE FROM bbs_conf_coa_deal t
        WHERE t.ROWID > (SELECT MIN(X.ROWID)
                         FROM bbs_conf_coa_deal X
                         WHERE X.ACCOUNT_CODE_IDX = t.ACCOUNT_CODE_IDX
                           and x.book_code = t.book_code);
        --上级科目为空填充
        update bbs_conf_coa_deal t
        set t.upper_ACCOUNT_CODE_IDX =
                (SELECT item.Account_Code_Idx
                 FROM bbs_account item
                 where item.book_code = t.book_code
                   and t.ACCOUNT_CODE_IDX like item.account_code_idx || '%'
                   and item.account_level = t.account_level - 1)
        where t.UPPER_ACCOUNT_CODE_IDX is null
          and t.account_level != 1;

        commit;
        --更新末级标识
        update bbs_conf_coa_deal t
        set t.final_level_is = '0'
        where t.final_level_is = '1'
          and exists
            (SELECT 1
             FROM bbs_conf_coa_deal a
             where a.ACCOUNT_CODE_IDX like t.ACCOUNT_CODE_IDX || '%'
               and a.ACCOUNT_CODE_IDX != t.ACCOUNT_CODE_IDX);
        commit;
        update bbs_conf_coa_deal t
        set t.final_level_is = '1'
        where t.final_level_is = '0'
          and not exists
            (SELECT 1
             FROM bbs_conf_coa_deal a
             where a.ACCOUNT_CODE_IDX like t.ACCOUNT_CODE_IDX || '%'
               and a.ACCOUNT_CODE_IDX != t.ACCOUNT_CODE_IDX);
        commit;
        --非末级，不允许借贷方向有值
        update bbs_conf_coa_deal t
        set t.account_entry_code = null
        where t.final_level_is = '0';
        commit;

        /*update (
          SELECT t.account_e_name as account_e_name,
                 t.account_c_name as account_c_name,
                 t.account_l_name as account_l_name,
                 replace(a.account_e_name, b.account_e_name, '') as account_e_name_cu,,
                 replace(a.account_c_name, b.account_c_name, '') as account_c_name_cu,,
                 replace(a.account_l_name, b.account_l_name, '') as account_l_name_cu
            FROM bbs_conf_coa_deal t, bbs_account a, bbs_account b
           where t.ACCOUNT_ID = a.account_code
             and a.book_code = b.book_code
             and a.book_code = 'BookI4'
             and a.upper_account_id = b.account_id
        )
        set account_e_name = account_e_name_cu
            account_c_name = account_c_name_cu
            account_l_name = account_l_name_cu;
        commit;*/

        --写科目体系临时表
        delete from bbs_conf_coa_result;

        for rec_deal_level in cur_deal_level loop
                dbms_output.put_line(rec_deal_level.account_level);
                insert into bbs_conf_coa_result
                (account_id,
                 upper_account_id,
                 account_level,
                 entity_id,
                 book_code,
                 account_code,
                 account_e_name,
                 account_c_name,
                 account_l_name,
                 final_level_is,
                 account_category_code,
                 account_entry_code,
                 valid_is,
                 checked_time,
                 checked_id,
                 audit_state,
                 effective_time,
                 expire_time,
                 checked_msg,
                 creator_id,
                 create_time,
                 updator_id,
                 update_time,
                 ACCOUNT_CODE_IDX,
                 account_ship,
                 serial_no)
                SELECT (case
                            when (SELECT item.account_id
                                  FROM bbs_account item
                                  where item.book_code = t.book_code
                                    and item.ACCOUNT_CODE_IDX = t.ACCOUNT_CODE_IDX) is not null then
                                (SELECT item.account_id
                                 FROM bbs_account item
                                 where item.book_code = t.book_code
                                   and item.ACCOUNT_CODE_IDX = t.ACCOUNT_CODE_IDX) --科目体系
                            else
                                bpl_seq_configaccountcode.nextval --科目体系不存在，获取当批新增上级
                    end) as account_id,
                       (case
                            when t.account_level = 1 then
                                0
                            else
                                (case
                                     when (SELECT item.account_id
                                           FROM bbs_account item
                                           where item.book_code = t.book_code
                                             and item.account_code_idx =
                                                 t.upper_ACCOUNT_CODE_IDX) is not null then
                                         (SELECT item.account_id
                                          FROM bbs_account item
                                          where item.book_code = t.book_code
                                            and item.account_code_idx = t.upper_ACCOUNT_CODE_IDX) --科目体系
                                     else
                                         (SELECT item.account_id
                                          FROM bbs_conf_coa_result item
                                          where item.book_code = t.book_code
                                            and item.account_code_idx = t.upper_ACCOUNT_CODE_IDX) --科目体系不存在，获取当批新增上级
                                    end)
                           end) as upper_account_id, --为空不置0了，让其报错，容易发现问题
                       t.account_level,
                       t.entity_id,
                       t.book_code,
                       t.account_code,
                       t.account_e_name,
                       t.account_c_name,
                       t.account_l_name,
                       t.final_level_is,
                       t.account_category_code,
                       t.account_entry_code,
                       '1' as valid_is,
                       sysdate as checked_time,
                       1 as checked_id,
                       '1' as audit_state,
                       null as effective_time,
                       null as expire_time,
                       (case
                            when (SELECT item.account_id
                                  FROM bbs_account item
                                  where item.book_code = t.book_code
                                    and item.ACCOUNT_CODE_IDX = t.ACCOUNT_CODE_IDX) is not null then
                                    'U' || to_char(sysdate, ' YYYYMMDD')
                            else
                                    'I' || to_char(sysdate, ' YYYYMMDD')
                           end) as checked_msg,
                       1 as creator_id,
                       sysdate as create_time,
                       1 as updator_id,
                       sysdate as update_time,
                       t.ACCOUNT_CODE_IDX,
                       '' as account_ship,
                       1 as serial_no
                FROM bbs_conf_coa_deal t
                where t.account_level = rec_deal_level.account_level;
                commit;
            end loop;
        /* 无效科目更新
          update  bbs_conf_coa_result a set a.valid_is = '0' where a.account_id in (
          SELECT t.upper_account_id FROM bbs_conf_coa_result t where t.valid_is ='0' and t.account_level = 5)
          and a.valid_is ='1';

          update  bbs_conf_coa_result a set a.valid_is = '0'  where a.account_id in (
          SELECT t.upper_account_id FROM bbs_conf_coa_result t where t.valid_is ='0' and t.account_level = 5)
          and not exists( SELECT 1 FROM bbs_conf_coa_result b where b.upper_account_id = a.account_id and b.valid_is ='1')
          and a.valid_is ='1';

          update  bbs_conf_coa_result a set a.valid_is = '0'  where a.account_id in (
          SELECT t.upper_account_id FROM bbs_conf_coa_result t where t.valid_is ='0' and t.account_level = 4)
          and not exists( SELECT 1 FROM bbs_conf_coa_result b where b.upper_account_id = a.account_id and b.valid_is ='1')
          and a.valid_is ='1';

          update  bbs_conf_coa_result a set a.valid_is = '0'  where a.account_id in (
          SELECT t.upper_account_id FROM bbs_conf_coa_result t where t.valid_is ='0' and t.account_level = 3)
          and not exists( SELECT 1 FROM bbs_conf_coa_result b where b.upper_account_id = a.account_id and b.valid_is ='1')
          and a.valid_is ='1';

          update  bbs_conf_coa_result a set a.valid_is = '0'  where a.account_id in (
          SELECT t.upper_account_id FROM bbs_conf_coa_result t where t.valid_is ='0' and t.account_level = 2)
          and not exists( SELECT 1 FROM bbs_conf_coa_result b where b.upper_account_id = a.account_id and b.valid_is ='1')
          and a.valid_is ='1';

        commit;*/

        --更新科目体系表
        DELETE FROM bpluser.bbs_account t
        where t.account_id in (SELECT a.ACCOUNT_ID FROM bbs_conf_coa_result a);

        insert into bpluser.bbs_account
        SELECT T.* FROM bbs_conf_coa_result t;
        commit;

    exception
        when others then
            rollback;
            dbms_output.put_line('conf_account:' || to_char(SQLCODE) || ',' ||
                                 sqlerrm);
    end conf_account;

    PROCEDURE conf_account_map(p_userid varchar2) is

    begin

        --更新科目映射表
        DELETE FROM BBS_CONF_ACCOUNT_MAPPING t
        where t.base_account_code is not null
          and t.base_account_code in
              (SELECT a.account_code_idx FROM bbs_conf_coa_result a);
        DELETE FROM bbs_conf_account_mapping t
        where t.other_account_code is not null
          and t.other_account_code in
              (SELECT a.other_account_code FROM bbs_conf_coa_imp a);
        insert into bbs_conf_account_mapping
        (MAPPING_ID,
         BASE_BOOK_CODE,
         BASE_ACCOUNT_CODE,
         BASE_ACCOUNT_ID,
         OTHER_BOOK_CODE,
         OTHER_ACCOUNT_CODE,
         OTHER_ACCOUNT_ID,
         VALID_IS,
         CHECKED_TIME,
         CHECKED_ID,
         AUDIT_STATE,
         CHECKED_MSG,
         CREATOR_ID,
         CREATE_TIME,
         UPDATOR_ID,
         UPDATE_TIME,
         REMARK,
         REMARK1,
         EXP_FEETYPE,
         entity_id)
        SELECT bbs_seq_conf_account_map.nextval MAPPING_ID,
               'BookI17' BASE_BOOK_CODE,
               t.base_account_code BASE_ACCOUNT_CODE,
               (SELECT a.account_id
                FROM bbs_account a
                where a.book_code = 'BookI17'
                  and a.account_code_idx = t.base_account_code) BASE_ACCOUNT_ID,
               'BookI4' OTHER_BOOK_CODE,
               t.other_account_code OTHER_ACCOUNT_CODE,
               (SELECT a.account_id
                FROM bbs_account a
                where a.book_code = 'BookI4'
                  and a.account_code = t.other_account_code) OTHER_ACCOUNT_ID,
               '1' VALID_IS,
               sysdate CHECKED_TIME,
               1 CHECKED_ID,
               '1' AUDIT_STATE,
               '' CHECKED_MSG,
               1 CREATOR_ID,
               sysdate CREATE_TIME,
               1 UPDATOR_ID,
               sysdate UPDATE_TIME,
               to_char(sysdate, 'YYYYMMDD') REMARK,
               '' REMARK1,
               '' EXP_FEETYPE,
               entity_id
        FROM bbs_conf_coa_imp t
        where t.other_account_code is not null
          and t.base_account_code is not null
          and coalesce(t.valid_is, '1') = '1'
          and exists (SELECT 1
                      FROM bbs_conf_coa_result a
                      where a.account_code_idx = t.base_account_code
                        and a.final_level_is = '1')
          and t.base_account_code not like '6408%' --排除费用分摊 6601-6601
        ;

        --沿用管理费用科目
        insert into bbs_conf_account_mapping
        (MAPPING_ID,
         BASE_BOOK_CODE,
         BASE_ACCOUNT_CODE,
         BASE_ACCOUNT_ID,
         OTHER_BOOK_CODE,
         OTHER_ACCOUNT_CODE,
         OTHER_ACCOUNT_ID,
         VALID_IS,
         CHECKED_TIME,
         CHECKED_ID,
         AUDIT_STATE,
         CHECKED_MSG,
         CREATOR_ID,
         CREATE_TIME,
         UPDATOR_ID,
         UPDATE_TIME,
         REMARK,
         REMARK1,
         EXP_FEETYPE,
         entity_id)
        SELECT bbs_seq_conf_account_map.nextval MAPPING_ID,
               'BookI17' BASE_BOOK_CODE,
               t.base_account_code BASE_ACCOUNT_CODE,
               (SELECT a.account_id
                FROM bbs_account a
                where a.book_code = 'BookI17'
                  and a.account_code_idx = t.base_account_code) BASE_ACCOUNT_ID,
               'BookI4' OTHER_BOOK_CODE,
               t.other_account_code OTHER_ACCOUNT_CODE,
               (SELECT a.account_id
                FROM bbs_account a
                where a.book_code = 'BookI4'
                  and a.account_code = t.other_account_code) OTHER_ACCOUNT_ID,
               '1' VALID_IS,
               sysdate CHECKED_TIME,
               1 CHECKED_ID,
               '1' AUDIT_STATE,
               '' CHECKED_MSG,
               1 CREATOR_ID,
               sysdate CREATE_TIME,
               1 UPDATOR_ID,
               sysdate UPDATE_TIME,
               to_char(sysdate, 'YYYYMMDD') REMARK,
               '' REMARK1,
               '' EXP_FEETYPE,
               entity_id
        FROM bbs_conf_coa_imp t
        where t.other_account_code like '6601%'
          and exists (SELECT 1
                      FROM bbs_account a
                      where a.book_code = 'BookI4'
                        and t.other_account_code = a.account_code
                        and a.final_level_is = '1');

        --特殊处理一：1002/01/01/01/ 有对应的凭证数据，但是它是非末级将映射到 1002/01/01/01/01/
        insert into bbs_conf_account_mapping
        (MAPPING_ID,
         BASE_BOOK_CODE,
         BASE_ACCOUNT_CODE,
         BASE_ACCOUNT_ID,
         OTHER_BOOK_CODE,
         OTHER_ACCOUNT_CODE,
         OTHER_ACCOUNT_ID,
         VALID_IS,
         CHECKED_TIME,
         CHECKED_ID,
         AUDIT_STATE,
         CHECKED_MSG,
         CREATOR_ID,
         CREATE_TIME,
         UPDATOR_ID,
         UPDATE_TIME,
         REMARK,
         REMARK1,
         EXP_FEETYPE,
         entity_id)
        SELECT bbs_seq_conf_account_map.nextval MAPPING_ID,
               'BookI17' BASE_BOOK_CODE,
               t.base_account_code BASE_ACCOUNT_CODE,
               (SELECT a.account_id
                FROM bbs_account a
                where a.book_code = 'BookI17'
                  and a.account_code_idx = t.base_account_code || '01/') BASE_ACCOUNT_ID,
               'BookI4' OTHER_BOOK_CODE,
               t.other_account_code OTHER_ACCOUNT_CODE,
               (SELECT a.account_id
                FROM bbs_account a
                where a.book_code = 'BookI4'
                  and a.account_code = t.other_account_code) OTHER_ACCOUNT_ID,
               '1' VALID_IS,
               sysdate CHECKED_TIME,
               1 CHECKED_ID,
               '1' AUDIT_STATE,
               '' CHECKED_MSG,
               1 CREATOR_ID,
               sysdate CREATE_TIME,
               1 UPDATOR_ID,
               sysdate UPDATE_TIME,
               to_char(sysdate, 'YYYYMMDD') REMARK,
               '' REMARK1,
               '' EXP_FEETYPE,
               entity_id
        FROM bbs_conf_coa_imp t
        where t.other_account_code is not null
          and t.base_account_code is not null
          and coalesce(t.valid_is, '1') = '1'
          and t.other_account_code = '1002/01/01/01/';

        --特殊处理二：I4科目【2261/01/03/】映射两个I17科目【1171/02/04/04/】、【1171/02/04/05/】
        --           存入保证金科目，查看202112科目都是合约数据，所以只映射1171/02/04/05/
        update bbs_conf_account_mapping t
        set valid_is = '0'
        where t.other_account_code = '2261/01/03/'
          and t.base_account_code = '1171/02/04/04/';
        commit;
    exception
        when others then
            rollback;
            dbms_output.put_line('conf_account_map:' || to_char(SQLCODE) || ',' ||
                                 sqlerrm);
    end conf_account_map;

END bpl_pack_account;

/
