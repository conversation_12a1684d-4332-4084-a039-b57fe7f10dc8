[2m2025-07-29 11:46:59.350[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m4212[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-29 11:51:59.372[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m4212[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-29 11:56:59.373[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m4212[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-29 12:01:59.387[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m4212[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-29 12:06:59.400[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m4212[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-29 12:11:59.405[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m4212[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-29 12:16:59.411[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m4212[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-29 12:21:59.427[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m4212[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-29 12:26:59.436[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m4212[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-29 12:31:59.444[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m4212[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-29 12:36:59.446[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m4212[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-29 12:38:22.994[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m4212[0;39m [2m---[0;39m [2m[     Thread-193][0;39m [36mc.n.l.PollingServerListUpdater          [0;39m [2m:[0;39m Shutting down the Executor Pool for PollingServerListUpdater
