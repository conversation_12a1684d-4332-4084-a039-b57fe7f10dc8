package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/4/15
 */
public class AtrBussLrcMainResVo {

    private Long lrcMainId;

    private Long entityId;

    private String yearMonth;

    private String dataSource;

    private Date drawTime;

    private String versionNo;

    private Long executorId;

    private Date executeTime;

    private String state;

    private String isConfirm;

    private Long confirmId;

    private String riskClassCode;

    private String currencyCode;

    private String atrType;

    private String entityCode;

    private String entityEName;

    private String entityCName;

    private String entityLName;

    private String classEName;

    private String classCName;

    private String classLName;

    private BigDecimal premium;

    private String currencyCu;

    private String premiumCu;

    private BigDecimal lrcAmount;

    public Long getLrcMainId() {
        return lrcMainId;
    }

    public void setLrcMainId(Long lrcMainId) {
        this.lrcMainId = lrcMainId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public Date getDrawTime() {
        return drawTime;
    }

    public void setDrawTime(Date drawTime) {
        this.drawTime = drawTime;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

    public Long getExecutorId() {
        return executorId;
    }

    public void setExecutorId(Long executorId) {
        this.executorId = executorId;
    }

    public Date getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(Date executeTime) {
        this.executeTime = executeTime;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getIsConfirm() {
        return isConfirm;
    }

    public void setIsConfirm(String isConfirm) {
        this.isConfirm = isConfirm;
    }

    public Long getConfirmId() {
        return confirmId;
    }

    public void setConfirmId(Long confirmId) {
        this.confirmId = confirmId;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public String getClassEName() {
        return classEName;
    }

    public void setClassEName(String classEName) {
        this.classEName = classEName;
    }

    public String getClassCName() {
        return classCName;
    }

    public void setClassCName(String classCName) {
        this.classCName = classCName;
    }

    public String getClassLName() {
        return classLName;
    }

    public void setClassLName(String classLName) {
        this.classLName = classLName;
    }

    public String getAtrType() {
        return atrType;
    }

    public void setAtrType(String atrType) {
        this.atrType = atrType;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getEntityCode() {
        return entityCode;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public String getEntityEName() {
        return entityEName;
    }

    public void setEntityEName(String entityEName) {
        this.entityEName = entityEName;
    }

    public String getEntityCName() {
        return entityCName;
    }

    public void setEntityCName(String entityCName) {
        this.entityCName = entityCName;
    }

    public String getEntityLName() {
        return entityLName;
    }

    public void setEntityLName(String entityLName) {
        this.entityLName = entityLName;
    }

    public BigDecimal getPremium() {
        return premium;
    }

    public void setPremium(BigDecimal premium) {
        this.premium = premium;
    }

    public String getCurrencyCu() {
        return currencyCu;
    }

    public void setCurrencyCu(String currencyCu) {
        this.currencyCu = currencyCu;
    }

    public String getPremiumCu() {
        return premiumCu;
    }

    public void setPremiumCu(String premiumCu) {
        this.premiumCu = premiumCu;
    }

    public BigDecimal getLrcAmount() {
        return lrcAmount;
    }

    public void setLrcAmount(BigDecimal lrcAmount) {
        this.lrcAmount = lrcAmount;
    }
}
