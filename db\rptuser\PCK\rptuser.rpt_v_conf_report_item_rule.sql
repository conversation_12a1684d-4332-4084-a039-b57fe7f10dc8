CREATE OR REPLACE VIEW RPT_V_CONF_REPORT_ITEM_RULE AS
SELECT a1.report_item_rule_id,
       a1.serial_no,
       a1.entity_id,
       a1.book_code,
       a1.report_item_id,
       a2.report_item_code,
       a1.deal_type,
       a1.data_source,
       a1.expr_type,
       a1.period_type,
       CASE
           WHEN a1.deal_type = '2' AND a1.data_source = '4' THEN a1.expr_desc
           WHEN a1.deal_type = '2' AND a1.data_source = '5' THEN ((((((a3.base_expr || COALESCE(rc.code_e_name, '')) || a3.other_expr) || '?') || a3.true_expr) || ':') || a3.false_expr)
           WHEN a1.deal_type = '3' THEN a4.summary_expr
           ELSE a1.expr_desc
           END AS rule_expr,
       a1.deal_level,
       a1.valid_is,
       a1.remark,
       a1.creator_id,
       a1.create_time,
       a1.updator_id,
       a1.update_time,
       a1.audit_state,
       a1.checked_id,
       a1.checked_time,
       a1.checked_msg
FROM rptuser.rpt_conf_report_item_rule a1
         LEFT JOIN rptuser.rpt_conf_report_item a2 ON a2.report_item_id = a1.report_item_id
         LEFT JOIN rptuser.rpt_conf_item_rule_sub a3 ON a3.report_item_rule_id = a1.report_item_rule_id
         LEFT JOIN rptuser.rpt_v_conf_code rc ON rc.code_code = a3.expr_operator AND rc.code_code_idx like 'OperationType/%'
         LEFT JOIN rptuser.rpt_v_conf_item_rule_summary a4 ON a4.report_item_id = a1.report_item_id AND a4.entity_id = a1.entity_id AND a4.book_code = a1.book_code
ORDER BY a2.report_item_code;