package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.ti;

import com.ss.ifrs.actuarial.util.abp.IgnoreCol;
import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Data
@Tab("atr_buss_ti_lrc_g")
public class AtrBussLrcTiIcg {

    /** ID */
    private Long id;

    /** 执行编号 */
    private String actionNo;

    /** 业务单位ID */
    private Long entityId;

    /** 评估期年月 */
    private String yearMonth;

    /** 险类代码 */
    private String riskClassCode;

    /** 合同组合编号 */
    private String portfolioNo;

    /** 合同组号码 */
    private String icgNo;
    
    /** 合同组名称 */
    private String icgName;

    /* 总保费，应收保费现金流进行汇总 */
    private BigDecimal totalPremium;

    /* 总净额，未来净额结算现金流汇总 */
    private BigDecimal totalNetFee;

    /* 跟单IACF， */
    private BigDecimal iacf;

    /* 非跟单IAEHC(对内) */
    private BigDecimal iaehcIn;

    /* 非跟单IAEHC(对外) */
    private BigDecimal iaehcOut;

    /* 总减值 */
    private BigDecimal badDebt;

    /* 当期确认的投资成分 */
    private BigDecimal investAmount;

    /** 退保率 */
    private BigDecimal lapseRate;

    /** 维持费用率 */
    private BigDecimal mtRate;

    /** 赔付率 */
    private BigDecimal claimRate;

    /** 未到期间接理赔费用率 */
    private BigDecimal ulaeRate;

    /** 未到期非金融风险调整率 */
    private BigDecimal raRate;
    
    /** 盈亏判定结果 */
    private String plJudgeRslt;


    @IgnoreCol
    private int remainingMonths;

    @IgnoreCol
    private int maxClmQuotaDevNo;

    @IgnoreCol
    private Map<Integer, BigDecimal> devEdPremiumMap = new HashMap<>();

    @IgnoreCol
    private Map<Integer, BigDecimal> devRecvPremiumMap = new HashMap<>();

    @IgnoreCol
    private Map<Integer, BigDecimal> devIacfMap = new HashMap<>();

    @IgnoreCol
    private Map<Integer, BigDecimal> devNetFeeMap = new HashMap<>();

    @IgnoreCol
    private Map<Integer, BigDecimal> devIaehcInMap = new HashMap<>();

    @IgnoreCol
    private Map<Integer, BigDecimal> devBadDebtMap = new HashMap<>();

    @IgnoreCol
    private Map<Integer, BigDecimal> devIaehcOutMap = new HashMap<>();
    
    /** 发展期已赚净额结算手续费 */
    @IgnoreCol
    private Map<Integer, BigDecimal> devEdNetFeeMap = new HashMap<>();
    
    /** 发展期已赚跟单获取费用 */
    @IgnoreCol
    private Map<Integer, BigDecimal> devEdIacfMap = new HashMap<>();
    
    /** 发展期已赚非跟单获取费用(对内) */
    @IgnoreCol
    private Map<Integer, BigDecimal> devEdIaehcInMap = new HashMap<>();
    
    /** 发展期已赚非跟单获取费用(对外) */
    @IgnoreCol
    private Map<Integer, BigDecimal> devEdIaehcOutMap = new HashMap<>();

}
