<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2022-08-19 14:51:44 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.conf.AtrConfExcelTableColumnDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfExcelTableColumn">
    <id column="col_id" property="colId" jdbcType="NUMERIC" />
    <result column="col_code" property="colCode" jdbcType="VARCHAR" />
    <result column="biz_type_id" property="bizTypeId" jdbcType="NUMERIC" />
    <result column="col_e_name" property="colEName" jdbcType="VARCHAR" />
    <result column="col_l_name" property="colLName" jdbcType="VARCHAR" />
    <result column="col_c_name" property="colCName" jdbcType="VARCHAR" />
    <result column="col_type" property="colType" jdbcType="VARCHAR" />
    <result column="col_length" property="colLength" jdbcType="VARCHAR" />
    <result column="col_desc" property="colDesc" jdbcType="VARCHAR" />
    <result column="original_is" property="originalIs" jdbcType="VARCHAR" />
    <result column="valid_is" property="validIs" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="creator_id" property="creatorId" jdbcType="NUMERIC" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="updator_id" property="updatorId" jdbcType="NUMERIC" />
    <result column="display_no" property="displayNo" jdbcType="NUMERIC" />
    <result column="need_is" property="needIs" jdbcType="VARCHAR" />
    <result column="enumeration_is" property="enumerationIs" jdbcType="VARCHAR" />
    <result column="template_display_is" property="templateDisplayIs" jdbcType="VARCHAR" />
    <result column="code_type" property="codeType" jdbcType="VARCHAR" />
    <result column="need_is_rule_id" property="needIsRuleId" jdbcType="NUMERIC" />
    <result column="enumeration_is_rule_id" property="enumerationIsRuleId" jdbcType="NUMERIC" />
    <result column="constraint_type" property="constraintType" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    col_id, col_code, biz_type_id, col_e_name, col_l_name, col_c_name, col_type, col_length,
    col_desc, original_is, valid_is, create_time, creator_id, update_time, updator_id, 
    display_no, need_is, enumeration_is, template_display_is, code_type, need_is_rule_id, 
    enumeration_is_rule_id, constraint_type
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="colId != null ">
          and col_id = #{colId,jdbcType=NUMERIC}
      </if>
      <if test="colCode != null and colCode != ''">
          and col_code = #{colCode,jdbcType=VARCHAR}
      </if>
      <if test="bizTypeId != null ">
          and biz_type_id = #{bizTypeId,jdbcType=NUMERIC}
      </if>
      <if test="colEName != null and colEName != ''">
          and col_e_name = #{colEName,jdbcType=VARCHAR}
      </if>
      <if test="colLName != null and colLName != ''">
          and col_l_name = #{colLName,jdbcType=VARCHAR}
      </if>
      <if test="colCName != null and colCName != ''">
          and col_c_name = #{colCName,jdbcType=VARCHAR}
      </if>
      <if test="colType != null and colType != ''">
          and col_type = #{colType,jdbcType=VARCHAR}
      </if>
      <if test="colLength != null and colLength != ''">
          and col_length = #{colLength,jdbcType=VARCHAR}
      </if>
      <if test="colDesc != null and colDesc != ''">
          and col_desc = #{colDesc,jdbcType=VARCHAR}
      </if>
      <if test="originalIs != null and originalIs != ''">
          and original_is = #{originalIs,jdbcType=VARCHAR}
      </if>
      <if test="validIs != null and validIs != ''">
          and valid_is = #{validIs,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creatorId != null ">
          and creator_id = #{creatorId,jdbcType=NUMERIC}
      </if>
      <if test="updateTime != null ">
          and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and updator_id = #{updatorId,jdbcType=NUMERIC}
      </if>
      <if test="displayNo != null ">
          and display_no = #{displayNo,jdbcType=NUMERIC}
      </if>
      <if test="needIs != null and needIs != ''">
          and need_is = #{needIs,jdbcType=VARCHAR}
      </if>
      <if test="enumerationIs != null and enumerationIs != ''">
          and enumeration_is = #{enumerationIs,jdbcType=VARCHAR}
      </if>
      <if test="templateDisplayIs != null and templateDisplayIs != ''">
          and template_display_is = #{templateDisplayIs,jdbcType=VARCHAR}
      </if>
      <if test="codeType != null and codeType != ''">
          and code_type = #{codeType,jdbcType=VARCHAR}
      </if>
      <if test="needIsRuleId != null ">
          and need_is_rule_id = #{needIsRuleId,jdbcType=NUMERIC}
      </if>
      <if test="enumerationIsRuleId != null ">
          and enumeration_is_rule_id = #{enumerationIsRuleId,jdbcType=NUMERIC}
      </if>
      <if test="constraintType != null and constraintType != ''">
          and constraint_type = #{constraintType,jdbcType=VARCHAR}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.colId != null ">
          and col_id = #{condition.colId,jdbcType=NUMERIC}
      </if>
      <if test="condition.colCode != null and condition.colCode != ''">
          and col_code = #{condition.colCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.bizTypeId != null ">
          and biz_type_id = #{condition.bizTypeId,jdbcType=NUMERIC}
      </if>
      <if test="condition.colEName != null and condition.colEName != ''">
          and col_e_name = #{condition.colEName,jdbcType=VARCHAR}
      </if>
      <if test="condition.colLName != null and condition.colLName != ''">
          and col_l_name = #{condition.colLName,jdbcType=VARCHAR}
      </if>
      <if test="condition.colCName != null and condition.colCName != ''">
          and col_c_name = #{condition.colCName,jdbcType=VARCHAR}
      </if>
      <if test="condition.colType != null and condition.colType != ''">
          and col_type = #{condition.colType,jdbcType=VARCHAR}
      </if>
      <if test="condition.colLength != null and condition.colLength != ''">
          and col_length = #{condition.colLength,jdbcType=VARCHAR}
      </if>
      <if test="condition.colDesc != null and condition.colDesc != ''">
          and col_desc = #{condition.colDesc,jdbcType=VARCHAR}
      </if>
      <if test="condition.originalIs != null and condition.originalIs != ''">
          and original_is = #{condition.originalIs,jdbcType=VARCHAR}
      </if>
      <if test="condition.validIs != null and condition.validIs != ''">
          and valid_is = #{condition.validIs,jdbcType=VARCHAR}
      </if>
      <if test="condition.createTime != null ">
          and create_time = #{condition.createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.creatorId != null ">
          and creator_id = #{condition.creatorId,jdbcType=NUMERIC}
      </if>
      <if test="condition.updateTime != null ">
          and update_time = #{condition.updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.updatorId != null ">
          and updator_id = #{condition.updatorId,jdbcType=NUMERIC}
      </if>
      <if test="condition.displayNo != null ">
          and display_no = #{condition.displayNo,jdbcType=NUMERIC}
      </if>
      <if test="condition.needIs != null and condition.needIs != ''">
          and need_is = #{condition.needIs,jdbcType=VARCHAR}
      </if>
      <if test="condition.enumerationIs != null and condition.enumerationIs != ''">
          and enumeration_is = #{condition.enumerationIs,jdbcType=VARCHAR}
      </if>
      <if test="condition.templateDisplayIs != null and condition.templateDisplayIs != ''">
          and template_display_is = #{condition.templateDisplayIs,jdbcType=VARCHAR}
      </if>
      <if test="condition.codeType != null and condition.codeType != ''">
          and code_type = #{condition.codeType,jdbcType=VARCHAR}
      </if>
      <if test="condition.needIsRuleId != null ">
          and need_is_rule_id = #{condition.needIsRuleId,jdbcType=NUMERIC}
      </if>
      <if test="condition.enumerationIsRuleId != null ">
          and enumeration_is_rule_id = #{condition.enumerationIsRuleId,jdbcType=NUMERIC}
      </if>
      <if test="condition.constraintType != null and condition.constraintType != ''">
          and constraint_type = #{condition.constraintType,jdbcType=VARCHAR}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="colId != null ">
          and col_id = #{colId,jdbcType=NUMERIC}
      </if>
      <if test="colCode != null and colCode != ''">
          and col_code = #{colCode,jdbcType=VARCHAR}
      </if>
      <if test="bizTypeId != null ">
          and biz_type_id = #{bizTypeId,jdbcType=NUMERIC}
      </if>
      <if test="colEName != null and colEName != ''">
          and col_e_name = #{colEName,jdbcType=VARCHAR}
      </if>
      <if test="colLName != null and colLName != ''">
          and col_l_name = #{colLName,jdbcType=VARCHAR}
      </if>
      <if test="colCName != null and colCName != ''">
          and col_c_name = #{colCName,jdbcType=VARCHAR}
      </if>
      <if test="colType != null and colType != ''">
          and col_type = #{colType,jdbcType=VARCHAR}
      </if>
      <if test="colLength != null and colLength != ''">
          and col_length = #{colLength,jdbcType=VARCHAR}
      </if>
      <if test="colDesc != null and colDesc != ''">
          and col_desc = #{colDesc,jdbcType=VARCHAR}
      </if>
      <if test="originalIs != null and originalIs != ''">
          and original_is = #{originalIs,jdbcType=VARCHAR}
      </if>
      <if test="validIs != null and validIs != ''">
          and valid_is = #{validIs,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creatorId != null ">
          and creator_id = #{creatorId,jdbcType=NUMERIC}
      </if>
      <if test="updateTime != null ">
          and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and updator_id = #{updatorId,jdbcType=NUMERIC}
      </if>
      <if test="displayNo != null ">
          and display_no = #{displayNo,jdbcType=NUMERIC}
      </if>
      <if test="needIs != null and needIs != ''">
          and need_is = #{needIs,jdbcType=VARCHAR}
      </if>
      <if test="enumerationIs != null and enumerationIs != ''">
          and enumeration_is = #{enumerationIs,jdbcType=VARCHAR}
      </if>
      <if test="templateDisplayIs != null and templateDisplayIs != ''">
          and template_display_is = #{templateDisplayIs,jdbcType=VARCHAR}
      </if>
      <if test="codeType != null and codeType != ''">
          and code_type = #{codeType,jdbcType=VARCHAR}
      </if>
      <if test="needIsRuleId != null ">
          and need_is_rule_id = #{needIsRuleId,jdbcType=NUMERIC}
      </if>
      <if test="enumerationIsRuleId != null ">
          and enumeration_is_rule_id = #{enumerationIsRuleId,jdbcType=NUMERIC}
      </if>
      <if test="constraintType != null and constraintType != ''">
          and constraint_type = #{constraintType,jdbcType=VARCHAR}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_excel_table_column
    where col_id = #{colId,jdbcType=NUMERIC}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_excel_table_column
    where col_id in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_excel_table_column
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfExcelTableColumn">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_excel_table_column
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_excel_table_column
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="col_id" keyProperty="colId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfExcelTableColumn">
    insert into atr_conf_excel_table_column
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="colId != null">
        col_id,
      </if>
      <if test="colCode != null">
        col_code,
      </if>
      <if test="bizTypeId != null">
        biz_type_id,
      </if>
      <if test="colEName != null">
        col_e_name,
      </if>
      <if test="colLName != null">
        col_l_name,
      </if>
      <if test="colCName != null">
        col_c_name,
      </if>
      <if test="colType != null">
        col_type,
      </if>
      <if test="colLength != null">
        col_length,
      </if>
      <if test="colDesc != null">
        col_desc,
      </if>
      <if test="originalIs != null">
        original_is,
      </if>
      <if test="validIs != null">
        valid_is,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updatorId != null">
        updator_id,
      </if>
      <if test="displayNo != null">
        display_no,
      </if>
      <if test="needIs != null">
        need_is,
      </if>
      <if test="enumerationIs != null">
        enumeration_is,
      </if>
      <if test="templateDisplayIs != null">
        template_display_is,
      </if>
      <if test="codeType != null">
        code_type,
      </if>
      <if test="needIsRuleId != null">
        need_is_rule_id,
      </if>
      <if test="enumerationIsRuleId != null">
        enumeration_is_rule_id,
      </if>
      <if test="constraintType != null">
        constraint_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="colId != null">
        #{colId,jdbcType=NUMERIC},
      </if>
      <if test="colCode != null">
        #{colCode,jdbcType=VARCHAR},
      </if>
      <if test="bizTypeId != null">
        #{bizTypeId,jdbcType=NUMERIC},
      </if>
      <if test="colEName != null">
        #{colEName,jdbcType=VARCHAR},
      </if>
      <if test="colLName != null">
        #{colLName,jdbcType=VARCHAR},
      </if>
      <if test="colCName != null">
        #{colCName,jdbcType=VARCHAR},
      </if>
      <if test="colType != null">
        #{colType,jdbcType=VARCHAR},
      </if>
      <if test="colLength != null">
        #{colLength,jdbcType=VARCHAR},
      </if>
      <if test="colDesc != null">
        #{colDesc,jdbcType=VARCHAR},
      </if>
      <if test="originalIs != null">
        #{originalIs,jdbcType=VARCHAR},
      </if>
      <if test="validIs != null">
        #{validIs,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=NUMERIC},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        #{updatorId,jdbcType=NUMERIC},
      </if>
      <if test="displayNo != null">
        #{displayNo,jdbcType=NUMERIC},
      </if>
      <if test="needIs != null">
        #{needIs,jdbcType=VARCHAR},
      </if>
      <if test="enumerationIs != null">
        #{enumerationIs,jdbcType=VARCHAR},
      </if>
      <if test="templateDisplayIs != null">
        #{templateDisplayIs,jdbcType=VARCHAR},
      </if>
      <if test="codeType != null">
        #{codeType,jdbcType=VARCHAR},
      </if>
      <if test="needIsRuleId != null">
        #{needIsRuleId,jdbcType=NUMERIC},
      </if>
      <if test="enumerationIsRuleId != null">
        #{enumerationIsRuleId,jdbcType=NUMERIC},
      </if>
      <if test="constraintType != null">
        #{constraintType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert into atr_conf_excel_table_column
     (col_id, col_code, biz_type_id, 
      col_e_name, col_l_name, col_c_name,
      col_type, col_length, col_desc, 
      original_is, valid_is, create_time, 
      creator_id, update_time, updator_id, 
      display_no, need_is, enumeration_is, 
      template_display_is, code_type, 
      need_is_rule_id, enumeration_is_rule_id, 
      constraint_type)
     values 
    <foreach collection="list" item="item" index="index" separator=",">
       (#{item.colId,jdbcType=NUMERIC}, #{item.colCode,jdbcType=VARCHAR}, #{item.bizTypeId,jdbcType=NUMERIC}, 
        #{item.colEName,jdbcType=VARCHAR}, #{item.colLName,jdbcType=VARCHAR}, #{item.colCName,jdbcType=VARCHAR},
        #{item.colType,jdbcType=VARCHAR}, #{item.colLength,jdbcType=VARCHAR}, #{item.colDesc,jdbcType=VARCHAR}, 
        #{item.originalIs,jdbcType=VARCHAR}, #{item.validIs,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.creatorId,jdbcType=NUMERIC}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updatorId,jdbcType=NUMERIC}, 
        #{item.displayNo,jdbcType=NUMERIC}, #{item.needIs,jdbcType=VARCHAR}, #{item.enumerationIs,jdbcType=VARCHAR}, 
        #{item.templateDisplayIs,jdbcType=VARCHAR}, #{item.codeType,jdbcType=VARCHAR}, 
        #{item.needIsRuleId,jdbcType=NUMERIC}, #{item.enumerationIsRuleId,jdbcType=NUMERIC}, 
        #{item.constraintType,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfExcelTableColumn">
    update atr_conf_excel_table_column
    <set>
      <if test="colCode != null">
        col_code = #{colCode,jdbcType=VARCHAR},
      </if>
      <if test="bizTypeId != null">
        biz_type_id = #{bizTypeId,jdbcType=NUMERIC},
      </if>
      <if test="colEName != null">
        col_e_name = #{colEName,jdbcType=VARCHAR},
      </if>
      <if test="colLName != null">
        col_l_name = #{colLName,jdbcType=VARCHAR},
      </if>
      <if test="colCName != null">
        col_c_name = #{colCName,jdbcType=VARCHAR},
      </if>
      <if test="colType != null">
        col_type = #{colType,jdbcType=VARCHAR},
      </if>
      <if test="colLength != null">
        col_length = #{colLength,jdbcType=VARCHAR},
      </if>
      <if test="colDesc != null">
        col_desc = #{colDesc,jdbcType=VARCHAR},
      </if>
      <if test="originalIs != null">
        original_is = #{originalIs,jdbcType=VARCHAR},
      </if>
      <if test="validIs != null">
        valid_is = #{validIs,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=NUMERIC},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        updator_id = #{updatorId,jdbcType=NUMERIC},
      </if>
      <if test="displayNo != null">
        display_no = #{displayNo,jdbcType=NUMERIC},
      </if>
      <if test="needIs != null">
        need_is = #{needIs,jdbcType=VARCHAR},
      </if>
      <if test="enumerationIs != null">
        enumeration_is = #{enumerationIs,jdbcType=VARCHAR},
      </if>
      <if test="templateDisplayIs != null">
        template_display_is = #{templateDisplayIs,jdbcType=VARCHAR},
      </if>
      <if test="codeType != null">
        code_type = #{codeType,jdbcType=VARCHAR},
      </if>
      <if test="needIsRuleId != null">
        need_is_rule_id = #{needIsRuleId,jdbcType=NUMERIC},
      </if>
      <if test="enumerationIsRuleId != null">
        enumeration_is_rule_id = #{enumerationIsRuleId,jdbcType=NUMERIC},
      </if>
      <if test="constraintType != null">
        constraint_type = #{constraintType,jdbcType=VARCHAR},
      </if>
    </set>
    where col_id = #{colId,jdbcType=NUMERIC}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfExcelTableColumn">
    update atr_conf_excel_table_column
    <set>
      <if test="record.colCode != null">
        col_code = #{record.colCode,jdbcType=VARCHAR},
      </if>
      <if test="record.bizTypeId != null">
        biz_type_id = #{record.bizTypeId,jdbcType=NUMERIC},
      </if>
      <if test="record.colEName != null">
        col_e_name = #{record.colEName,jdbcType=VARCHAR},
      </if>
      <if test="record.colLName != null">
        col_l_name = #{record.colLName,jdbcType=VARCHAR},
      </if>
      <if test="record.colCName != null">
        col_c_name = #{record.colCName,jdbcType=VARCHAR},
      </if>
      <if test="record.colType != null">
        col_type = #{record.colType,jdbcType=VARCHAR},
      </if>
      <if test="record.colLength != null">
        col_length = #{record.colLength,jdbcType=VARCHAR},
      </if>
      <if test="record.colDesc != null">
        col_desc = #{record.colDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.originalIs != null">
        original_is = #{record.originalIs,jdbcType=VARCHAR},
      </if>
      <if test="record.validIs != null">
        valid_is = #{record.validIs,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=NUMERIC},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatorId != null">
        updator_id = #{record.updatorId,jdbcType=NUMERIC},
      </if>
      <if test="record.displayNo != null">
        display_no = #{record.displayNo,jdbcType=NUMERIC},
      </if>
      <if test="record.needIs != null">
        need_is = #{record.needIs,jdbcType=VARCHAR},
      </if>
      <if test="record.enumerationIs != null">
        enumeration_is = #{record.enumerationIs,jdbcType=VARCHAR},
      </if>
      <if test="record.templateDisplayIs != null">
        template_display_is = #{record.templateDisplayIs,jdbcType=VARCHAR},
      </if>
      <if test="record.codeType != null">
        code_type = #{record.codeType,jdbcType=VARCHAR},
      </if>
      <if test="record.needIsRuleId != null">
        need_is_rule_id = #{record.needIsRuleId,jdbcType=NUMERIC},
      </if>
      <if test="record.enumerationIsRuleId != null">
        enumeration_is_rule_id = #{record.enumerationIsRuleId,jdbcType=NUMERIC},
      </if>
      <if test="record.constraintType != null">
        constraint_type = #{record.constraintType,jdbcType=VARCHAR},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from atr_conf_excel_table_column
    where col_id = #{colId,jdbcType=NUMERIC}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from atr_conf_excel_table_column
    where col_id in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from atr_conf_excel_table_column
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfExcelTableColumn">
    select count(1) from atr_conf_excel_table_column
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>