/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2022-02-17 17:42:26
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2022-02-17 17:42:26<br/>
 * Description: null<br/>
 * Table Name: atr_buss_reserve_os_detail<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 * <AUTHOR>
 */
public class AtrBussReserveOsDetail implements Serializable {
    /**
     * Database column: atr_buss_reserve_os_detail.os_detail_id
     * Database remarks: os_detail_id|主键
     */
    @ApiModelProperty(value = "os_detail_id|主键", required = true)
    private Long osDetailId;

    /**
     * Database column: atr_buss_reserve_os_detail.reserve_os_id
     * Database remarks: reserve_os_id|主表id
     */
    @ApiModelProperty(value = "reserve_os_id|主表id", required = true)
    private Long reserveOsId;

    /**
     * Database column: atr_buss_reserve_os_detail.risk_class
     * Database remarks: risk_class|风险大类
     */
    @ApiModelProperty(value = "risk_class|风险大类", required = false)
    private String riskClassCode;

    private String loaCode;
    /**
     * Database column: atr_buss_reserve_os_detail.risk_code
     * Database remarks: risk_code|风险
     */
    @ApiModelProperty(value = "risk_code|风险", required = false)
    private String riskCode;

    /**
     * Database column: atr_buss_reserve_os_detail.treaty_class
     * Database remarks: treaty_class|合约大类
     */
    @ApiModelProperty(value = "treaty_class|合约大类", required = false)
    private String treatyClass;

    /**
     * Database column: atr_buss_reserve_os_detail.claim_no
     * Database remarks: claim_no|立案号
     */
    @ApiModelProperty(value = "claim_no|立案号", required = false)
    private String claimNo;

    /**
     * Database column: atr_buss_reserve_os_detail.policy_no
     * Database remarks: policy_no|保单号
     */
    @ApiModelProperty(value = "policy_no|保单号", required = false)
    private String policyNo;

    /**
     * Database column: atr_buss_reserve_os_detail.endorse_seq_no
     * Database remarks: endorse_seq_no|批改序号
     */
    @ApiModelProperty(value = "endorse_seq_no|批改序号", required = false)
    private String endorseSeqNo;

    /**
     * Database column: atr_buss_reserve_os_detail.endorse_no
     * Database remarks: endorse_no|批单号
     */
    @ApiModelProperty(value = "endorse_no|批单号", required = false)
    private String endorseNo;

    /**
     * Database column: atr_buss_reserve_os_detail.business_type
     * Database remarks: businessSourceCode|业务类型
     */
    @ApiModelProperty(value = "businessSourceCode|业务类型", required = false)
    private String businessSourceCode;

    /**
     * Database column: atr_buss_reserve_os_detail.reins_type
     * Database remarks: reins_type|再保类型(DN直保，RN再保)
     */
    @ApiModelProperty(value = "reins_type|再保类型(DN直保，RN再保)", required = false)
    private String reinsType;

    /**
     * Database column: atr_buss_reserve_os_detail.damage_date
     * Database remarks: damage_date|事故时间
     */
    @ApiModelProperty(value = "damage_date|事故时间", required = false)
    private Date damageDate;

    /**
     * Database column: atr_buss_reserve_os_detail.start_date
     * Database remarks: start_date|起保日期
     */
    @ApiModelProperty(value = "start_date|起保日期", required = false)
    private Date startDate;

    /**
     * Database column: atr_buss_reserve_os_detail.end_date
     * Database remarks: end_date|终保日期
     */
    @ApiModelProperty(value = "end_date|终保日期", required = false)
    private Date endDate;

    /**
     * Database column: atr_buss_reserve_os_detail.currency
     * Database remarks: currency|币别
     */
    @ApiModelProperty(value = "currency|币别", required = false)
    private String currencyCode;

    /**
     * Database column: atr_buss_reserve_os_detail.os_amount
     * Database remarks: null
     */
    private BigDecimal osAmount;

    /**
     * Database column: atr_buss_reserve_os_detail.paid_amount
     * Database remarks: null
     */
    private BigDecimal paidAmount;

    private static final long serialVersionUID = 1L;

    public Long getOsDetailId() {
        return osDetailId;
    }

    public void setOsDetailId(Long osDetailId) {
        this.osDetailId = osDetailId;
    }

    public Long getReserveOsId() {
        return reserveOsId;
    }

    public void setReserveOsId(Long reserveOsId) {
        this.reserveOsId = reserveOsId;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }


    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public String getRiskCode() {
        return riskCode;
    }

    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    public String getTreatyClass() {
        return treatyClass;
    }

    public void setTreatyClass(String treatyClass) {
        this.treatyClass = treatyClass;
    }

    public String getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(String claimNo) {
        this.claimNo = claimNo;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getEndorseSeqNo() {
        return endorseSeqNo;
    }

    public void setEndorseSeqNo(String endorseSeqNo) {
        this.endorseSeqNo = endorseSeqNo;
    }

    public String getEndorseNo() {
        return endorseNo;
    }

    public void setEndorseNo(String endorseNo) {
        this.endorseNo = endorseNo;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getReinsType() {
        return reinsType;
    }

    public void setReinsType(String reinsType) {
        this.reinsType = reinsType;
    }

    public Date getDamageDate() {
        return damageDate;
    }

    public void setDamageDate(Date damageDate) {
        this.damageDate = damageDate;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public BigDecimal getOsAmount() {
        return osAmount;
    }

    public void setOsAmount(BigDecimal osAmount) {
        this.osAmount = osAmount;
    }

    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(BigDecimal paidAmount) {
        this.paidAmount = paidAmount;
    }
}