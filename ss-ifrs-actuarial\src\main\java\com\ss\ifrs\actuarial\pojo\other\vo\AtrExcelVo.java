package com.ss.ifrs.actuarial.pojo.other.vo;

import java.util.List;

/**
 * @ClassName AtrExcelVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/9/8
 **/
public class AtrExcelVo {

    /**
     * @Method AtrExcelVo
     * <AUTHOR>
     * @Date 2022/9/8
     * @Description
     * @param fileEName 文件英文名称
     * @param fileCName 文件中文名称
     * @param fileTName 文件繁体名称
     * @param language 语言
     * @param atrExcelHeaderVoList Excel表头信息List
     * @Return
     */
    public AtrExcelVo(String fileEName, String fileCName, String fileTName, String language, List<AtrExcelHeaderVo> atrExcelHeaderVoList) {
        this.fileEName = fileEName;
        this.fileCName = fileCName;
        this.fileTName = fileTName;
        this.language = language;
        this.atrExcelHeaderVoList = atrExcelHeaderVoList;
    }

    /**
     * Excel英文名称
     */
    private String fileEName;

    /**
     * Excel简体名称
     */
    private String fileCName;

    /**
     * Excel繁体名称
     */
    private String fileTName;

    /**
     * 语言
     */
    private String language;

    /**
     * Excel表头信息列表
     */
    private List<AtrExcelHeaderVo> atrExcelHeaderVoList;


    public String getFileEName() {
        return fileEName;
    }

    public void setFileEName(String fileEName) {
        this.fileEName = fileEName;
    }

    public String getFileCName() {
        return fileCName;
    }

    public void setFileCName(String fileCName) {
        this.fileCName = fileCName;
    }

    public String getFileTName() {
        return fileTName;
    }

    public void setFileTName(String fileTName) {
        this.fileTName = fileTName;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public List<AtrExcelHeaderVo> getAtrExcelHeaderVoList() {
        return atrExcelHeaderVoList;
    }

    public void setAtrExcelHeaderVoList(List<AtrExcelHeaderVo> atrExcelHeaderVoList) {
        this.atrExcelHeaderVoList = atrExcelHeaderVoList;
    }
}
