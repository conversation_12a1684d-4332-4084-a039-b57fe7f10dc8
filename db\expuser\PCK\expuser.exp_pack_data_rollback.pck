create or replace package EXP_PACK_DATA_ROL<PERSON><PERSON><PERSON>K authid current_user  is

  -- Author  : WUYH
  -- Created : 2023-7-26 14:35:39
  -- Purpose :
  PROCEDURE proc_month_bak(p_entityid  NUMBER, p_yearmonth VARCHAR2);

  PROCEDURE proc_all_bak(p_yearmonth VARCHAR2);

end EXP_PACK_DATA_ROLLBACK;
/
create or replace package body EXP_PACK_DATA_ROLLBACK IS

   PROCEDURE proc_month_bak(p_entityid  NUMBER, p_yearmonth VARCHAR2) AS
   BEGIN

    DELETE FROM BPLUSER.BPL_LOG_ACTION_DETAIL
     WHERE ACT_LOG_ID IN (SELECT ACT_LOG_ID
                            FROM BPLUSER.BPL_LOG_ACTION
                           WHERE YEAR_MONTH = p_yearmonth
                             AND ENTITY_ID = p_entityid);

    DELETE FROM BPLUSER.BPL_LOG_ACTION
     WHERE YEAR_MONTH = p_yearmonth
       AND ENTITY_ID = p_entityid;

    DELETE FROM EXPUSER.EXP_BUSS_ALLOCATIONHIS
     WHERE YEAR_MONTH = p_yearmonth
       AND ENTITY_ID = p_entityid;

    DELETE FROM EXPUSER.EXP_BUSS_ALLOCATION
     WHERE YEAR_MONTH = p_yearmonth
       AND ENTITY_ID = p_entityid;
    COMMIT;
    DELETE FROM EXPUSER.exp_buss_allocation_icghis
     WHERE YEAR_MONTH = p_yearmonth
       AND ENTITY_ID = p_entityid;

    DELETE FROM EXPUSER.exp_buss_allocation_icg
     WHERE YEAR_MONTH = p_yearmonth
       AND ENTITY_ID = p_entityid;

    DELETE FROM EXPUSER.exp_buss_allocation_porthis
     WHERE YEAR_MONTH = p_yearmonth
       AND ENTITY_ID = p_entityid;

    DELETE FROM EXPUSER.exp_buss_allocation_port
     WHERE YEAR_MONTH = p_yearmonth
       AND ENTITY_ID = p_entityid;

    DELETE FROM EXPUSER.EXP_TEMP_TRIAL_ALLOCATION
     WHERE YEAR_MONTH = p_yearmonth
       AND ENTITY_ID = p_entityid;

    COMMIT;

    DELETE FROM EXPUSER.EXP_TEMP_TRIAL_ALLOCATION_ICG
     WHERE YEAR_MONTH = p_yearmonth
       AND ENTITY_ID = p_entityid;

    DELETE FROM EXPUSER.EXP_TEMP_TRIAL_ALLOCATION_PORT
     WHERE YEAR_MONTH = p_yearmonth
       AND ENTITY_ID = p_entityid;

    DELETE FROM EXPUSER.exp_duct_cmunit_allocation
     WHERE YEAR_MONTH = p_yearmonth
       AND ENTITY_ID = p_entityid;

    DELETE FROM EXPUSER.exp_duct_cmunit_summary
     WHERE YEAR_MONTH = p_yearmonth
       AND ENTITY_ID = p_entityid;

    DELETE FROM EXPUSER.exp_duct_recognitionrstdetail
     WHERE YEAR_MONTH = p_yearmonth
       AND ENTITY_ID = p_entityid;


    DELETE FROM EXPUSER.EXP_BUSS_RECOGNITIONRSTDETAIL
     WHERE RECOGNITIONRST_ID IN
           (SELECT RECOGNITIONRST_ID
              FROM EXPUSER.EXP_BUSS_RECOGNITIONRST T
             WHERE T.ENTITY_ID = p_entityid
               AND T.YEAR_MONTH = p_yearmonth);

    DELETE FROM EXPUSER.EXP_BUSS_RECOGNITIONRST T
     WHERE T.ENTITY_ID = p_entityid
       AND T.YEAR_MONTH = p_yearmonth;

    DELETE FROM EXPUSER.EXP_BUSS_RECOGNITIONRSTDTLHIS T
     WHERE RECOGNITIONRST_ID IN
           (SELECT RECOGNITIONRST_ID
              FROM EXPUSER.EXP_BUSS_RECOGNITIONRSTHIS T
             WHERE T.ENTITY_ID = p_entityid
               AND T.YEAR_MONTH = p_yearmonth);

    DELETE FROM EXPUSER.EXP_BUSS_RECOGNITIONRSTHIS T
     WHERE T.ENTITY_ID = p_entityid
       AND T.YEAR_MONTH = p_yearmonth;

    DELETE FROM EXPUSER.EXP_DAP_FINANCIAL_DATA_DETAIL
     WHERE FINANCIAL_ID IN (SELECT FINANCIAL_ID
                              FROM EXPUSER.EXP_DAP_FINANCIAL_DATA
                             WHERE YEAR_MONTH = p_yearmonth
                               AND ENTITY_ID = p_entityid);

    DELETE FROM EXPUSER.EXP_DAP_FINANCIAL_DATA
     WHERE YEAR_MONTH = p_yearmonth
       AND ENTITY_ID = p_entityid;

    DELETE FROM EXPUSER.EXP_DAP_CMUNIT_DATA
     WHERE YEAR_MONTH = p_yearmonth
       AND ENTITY_ID = p_entityid;

    UPDATE EXPUSER.EXP_CONF_BUSSPERIOD_DETAIL A
       SET READY_STATE = 0
     WHERE BUSS_PERIOD_ID IN
           (SELECT BUSS_PERIOD_ID
              FROM EXPUSER.EXP_CONF_BUSSPERIOD
             WHERE ENTITY_ID = p_entityid
               AND YEAR_MONTH = p_yearmonth);

    UPDATE EXPUSER.EXP_CONF_BUSSPERIOD
       SET EXECUTION_STATE = '0'
     WHERE ENTITY_ID = p_entityid
       AND YEAR_MONTH = p_yearmonth;

     COMMIT;
   END proc_month_bak;





   PROCEDURE PROC_ALL_BAK(p_yearmonth VARCHAR2) AS
       v_error_msg      VARCHAR2(4000); --异常信息
   BEGIN
      --分摊数据相关表
      EXECUTE IMMEDIATE 'TRUNCATE TABLE exp_buss_allocationhis';
      EXECUTE IMMEDIATE 'TRUNCATE TABLE exp_buss_allocation';
      EXECUTE IMMEDIATE 'TRUNCATE TABLE exp_buss_allocation_icghis';
      EXECUTE IMMEDIATE 'TRUNCATE TABLE exp_buss_allocation_icg';
      EXECUTE IMMEDIATE 'TRUNCATE TABLE exp_buss_allocation_porthis';
      EXECUTE IMMEDIATE 'TRUNCATE TABLE exp_buss_allocation_port';
      EXECUTE IMMEDIATE 'TRUNCATE TABLE EXP_TEMP_TRIAL_ALLOCATION';
      EXECUTE IMMEDIATE 'TRUNCATE TABLE EXP_TEMP_TRIAL_ALLOCATION_ICG';
      EXECUTE IMMEDIATE 'TRUNCATE TABLE Exp_Temp_Trial_Allocation_Port';
      EXECUTE IMMEDIATE 'TRUNCATE TABLE exp_duct_cmunit_allocation';
      EXECUTE IMMEDIATE 'TRUNCATE TABLE exp_duct_cmunit_summary';
      EXECUTE IMMEDIATE 'TRUNCATE TABLE exp_duct_recognitionrstdetail';

      --费用指认数据相关表
      EXECUTE IMMEDIATE 'TRUNCATE TABLE EXP_BUSS_RECOGNITIONRSTDTLHIS';
      EXECUTE IMMEDIATE 'TRUNCATE TABLE EXP_BUSS_RECOGNITIONRSTDETAIL';
      EXECUTE IMMEDIATE 'TRUNCATE TABLE EXP_BUSS_RECOGNITIONRSTHIS';
      EXECUTE IMMEDIATE 'TRUNCATE TABLE EXP_BUSS_RECOGNITIONRST';


      --分摊准备数据相关表
      EXECUTE IMMEDIATE 'TRUNCATE TABLE EXP_DAP_FINANCIAL_DATA_DETAIL';
      EXECUTE IMMEDIATE 'TRUNCATE TABLE EXP_DAP_FINANCIAL_DATA';
      EXECUTE IMMEDIATE 'TRUNCATE TABLE EXP_DAP_CMUNIT_DATA';

      --业务期间相关表
      EXECUTE IMMEDIATE 'TRUNCATE TABLE expuser.exp_conf_bussperiod_detail';
      EXECUTE IMMEDIATE 'TRUNCATE TABLE expuser.exp_conf_bussperiod';

     --流程日志信息相关表
     DELETE FROM BPLUSER.BPL_LOG_ACTION_DETAIL T
      WHERE T.ACT_LOG_ID IN (SELECT T.ACT_LOG_ID
                               FROM BPLUSER.BPL_LOG_ACTION T
                              WHERE T.SYSTEM_CODE = 'EXP');

     DELETE FROM BPLUSER.BPL_LOG_ACTION T WHERE T.SYSTEM_CODE = 'EXP';
     COMMIT;


	    exp_pack_commonutils.DROP_SEQUENCE('EXP_SEQ_RECOGNITIONRULEPROC');
      exp_pack_commonutils.DROP_SEQUENCE('EXP_SEQ_ACTUARIALDATASUM');
      exp_pack_commonutils.DROP_SEQUENCE('EXP_SEQ_AWAITSHAREDATA');
      exp_pack_commonutils.DROP_SEQUENCE('EXP_SEQ_BUSS_ALLOCATION');
      exp_pack_commonutils.DROP_SEQUENCE('EXP_SEQ_BUSS_ALLOCATIONHIS');
      exp_pack_commonutils.DROP_SEQUENCE('EXP_SEQ_BUSS_ALLOCATION_ICG');
      exp_pack_commonutils.DROP_SEQUENCE('EXP_SEQ_BUSS_ALLOCATION_PORT');
      exp_pack_commonutils.DROP_SEQUENCE('EXP_SEQ_RECOGNITIONRSTDETAIL');
      exp_pack_commonutils.DROP_SEQUENCE('EXP_SEQ_RERSTDETAILHIS');
      exp_pack_commonutils.DROP_SEQUENCE('EXP_SEQ_TEMP_TRIAL_ALLOCATION');
      exp_pack_commonutils.DROP_SEQUENCE('EXP_SEQ_TEMP_TRIAL_ALLOC_ICG');
      exp_pack_commonutils.DROP_SEQUENCE('EXP_SEQ_TEMP_TRIAL_ALLOC_PORT');
      exp_pack_commonutils.DROP_SEQUENCE('EXP_SEQ_DAP_CMUNIT_DATA');
      exp_pack_commonutils.DROP_SEQUENCE('EXP_SEQ_DAP_FINANCIAL_DATA');
      exp_pack_commonutils.DROP_SEQUENCE('EXP_SEQ_DAP_FINANCIAL_DATA_DTL');

      exp_pack_commonutils.CREATE_SEQUENCE('EXP_SEQ_RECOGNITIONRULEPROC',1,1);
      exp_pack_commonutils.CREATE_SEQUENCE('EXP_SEQ_ACTUARIALDATASUM',1,1);
      exp_pack_commonutils.CREATE_SEQUENCE('EXP_SEQ_AWAITSHAREDATA',1,1);
      exp_pack_commonutils.CREATE_SEQUENCE('EXP_SEQ_BUSS_ALLOCATION',1,1);
      exp_pack_commonutils.CREATE_SEQUENCE('EXP_SEQ_BUSS_ALLOCATIONHIS',1,1);
      exp_pack_commonutils.CREATE_SEQUENCE('EXP_SEQ_BUSS_ALLOCATION_ICG',1,1);
      exp_pack_commonutils.CREATE_SEQUENCE('EXP_SEQ_BUSS_ALLOCATION_PORT',1,1);
      exp_pack_commonutils.CREATE_SEQUENCE('EXP_SEQ_RECOGNITIONRSTDETAIL',1,1);
      exp_pack_commonutils.CREATE_SEQUENCE('EXP_SEQ_RERSTDETAILHIS',1,1);
      exp_pack_commonutils.CREATE_SEQUENCE('EXP_SEQ_TEMP_TRIAL_ALLOCATION',1,1);
      exp_pack_commonutils.CREATE_SEQUENCE('EXP_SEQ_TEMP_TRIAL_ALLOC_ICG',1,1);
      exp_pack_commonutils.CREATE_SEQUENCE('EXP_SEQ_TEMP_TRIAL_ALLOC_PORT',1,1);
      exp_pack_commonutils.CREATE_SEQUENCE('EXP_SEQ_DAP_CMUNIT_DATA',1,1);
      exp_pack_commonutils.CREATE_SEQUENCE('EXP_SEQ_DAP_FINANCIAL_DATA',1,1);
      exp_pack_commonutils.CREATE_SEQUENCE('EXP_SEQ_DAP_FINANCIAL_DATA_DTL',1,1);
      
      ---------初始化业务期间---------
      insert into exp_conf_bussperiod (BUSS_PERIOD_ID, ENTITY_ID, YEAR_MONTH, EXECUTION_STATE, VALID_IS, CREATOR_ID, CREATE_TIME, UPDATOR_ID, UPDATE_TIME)
      values (exp_seq_conf_bussperiod.nextval, 1, p_yearmonth, '0', '1', 1, sysdate, 0, null);
      COMMIT;

      --1、循环xxx_conf_period
      FOR cur_period IN (SELECT buss_period_id,
                                execution_state
                           FROM exp_conf_bussperiod
                          WHERE valid_is = '1') LOOP
        --2、循环xxx_conf_table
        FOR cur_table IN (SELECT biz_type_id,
                                 biz_code,
                                 direction,
                                 system_code
                            FROM exp_conf_table) LOOP

        --插入xxx_conf_bussperiod_detail
          INSERT INTO exp_conf_bussperiod_detail
            (period_detail_id,
             buss_period_id,
             biz_type_id,
             task_time,
             exec_result,
             direction,
             ready_state,
             creator_id,
             create_time,
             updator_id,
             update_time,
             system_code)
          VALUES
            (exp_seq_conf_bussperiod_detail.nextval,
             cur_period.buss_period_id,
             cur_table.biz_type_id,
             NULL,
             NULL,
             cur_table.direction, --0-输出，1-输入
             (CASE WHEN cur_period.execution_state = '1' OR cur_period.execution_state = '3' THEN '1' ELSE '0' END), --业务期间：1-已准备中或3-已完成，则准备状态为1-已准备，其它情况为0-准备中
             1,
             SYSDATE,
             NULL,
             NULL,
             cur_table.system_code);
              --提交事务
            COMMIT;
        END LOOP;
      END LOOP;
  EXCEPTION
    WHEN OTHERS THEN
        v_error_msg := substr('过渡期发生异常，请检查！：' ||  '**SQLERRM: ' || SQLERRM || '**Error line: ' || dbms_utility.format_error_backtrace() || '', 1, 4000);
              --往外层抛出异常信息
      raise_application_error(-20003, v_error_msg);
   END PROC_ALL_BAK;
end EXP_PACK_DATA_ROLLBACK;
/
