/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-08-11 10:53:13
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrdap.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-08-11 10:53:13<br/>
 * Description: LIC 计算结果主表(合同组维度，直保&临分分入)<br/>
 * Table Name: ATR_BUSS_DD_LIC_ICG_CALC<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "LIC 计算结果主表(合同组维度，直保&临分分入)")
public class AtrBussDDLicIcgCalc implements Serializable {
    /**
     * Database column: ATR_BUSS_DD_LIC_ICG_CALC.ID
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICG_CALC.ACTION_NO
     * Database remarks: 执行编号
     */
    @ApiModelProperty(value = "执行编号", required = true)
    private String actionNo;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICG_CALC.CALC_TYPE
     * Database remarks: 计算类型
     */
    @ApiModelProperty(value = "计算类型", required = true)
    private String calcType;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICG_CALC.TASK_CODE
     * Database remarks: 任务号
     */
    @ApiModelProperty(value = "任务号", required = true)
    private String taskCode;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICG_CALC.ENTITY_ID
     * Database remarks: 业务单位ID
     */
    @ApiModelProperty(value = "业务单位ID", required = true)
    private Long entityId;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICG_CALC.CURRENCY_CODE
     * Database remarks: 币种
     */
    @ApiModelProperty(value = "币种", required = true)
    private String currencyCode;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICG_CALC.YEAR_MONTH
     * Database remarks: 业务年月|评估期的年月
     */
    @ApiModelProperty(value = "业务年月|评估期的年月", required = true)
    private String yearMonth;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICG_CALC.PORTFOLIO_NO
     * Database remarks: 合同组合号码
     */
    @ApiModelProperty(value = "合同组合号码", required = true)
    private String portfolioNo;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICG_CALC.ICG_NO
     * Database remarks: 合同组号码
     */
    @ApiModelProperty(value = "合同组号码", required = true)
    private String icgNo;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICG_CALC.EVALUATE_APPROACH
     * Database remarks: 评估方法
     */
    @ApiModelProperty(value = "评估方法", required = true)
    private String evaluateApproach;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICG_CALC.MODEL_DEF_ID
     * Database remarks: 计量模型
     */
    @ApiModelProperty(value = "计量模型", required = true)
    private Long modelDefId;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICG_CALC.LOA_CODE
     * Database remarks: LOA编码
     */
    @ApiModelProperty(value = "LOA编码", required = true)
    private String loaCode;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICG_CALC.COVERAGE_AMOUNT
     * Database remarks: 保额/限额
     */
    @ApiModelProperty(value = "保额/限额", required = false)
    private BigDecimal coverageAmount;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICG_CALC.ICG_COUNT
     * Database remarks: 合同组数量
     */
    @ApiModelProperty(value = "合同组数量", required = false)
    private Long icgCount;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICG_CALC.RISK_RATIO
     * Database remarks: 风险占比
     */
    @ApiModelProperty(value = "风险占比", required = false)
    private BigDecimal riskRatio;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICG_CALC.CLAIM_PAID_CUR
     * Database remarks: 当前事故年当期赔付
     */
    @ApiModelProperty(value = "当前事故年当期赔付", required = false)
    private BigDecimal claimPaidCur;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICG_CALC.CLAIM_PAID_PRE
     * Database remarks: 往年事故年当期赔付
     */
    @ApiModelProperty(value = "往年事故年当期赔付", required = false)
    private BigDecimal claimPaidPre;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public String getCalcType() {
        return calcType;
    }

    public void setCalcType(String calcType) {
        this.calcType = calcType;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public String getEvaluateApproach() {
        return evaluateApproach;
    }

    public void setEvaluateApproach(String evaluateApproach) {
        this.evaluateApproach = evaluateApproach;
    }

    public Long getModelDefId() {
        return modelDefId;
    }

    public void setModelDefId(Long modelDefId) {
        this.modelDefId = modelDefId;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public BigDecimal getCoverageAmount() {
        return coverageAmount;
    }

    public void setCoverageAmount(BigDecimal coverageAmount) {
        this.coverageAmount = coverageAmount;
    }

    public Long getIcgCount() {
        return icgCount;
    }

    public void setIcgCount(Long icgCount) {
        this.icgCount = icgCount;
    }

    public BigDecimal getRiskRatio() {
        return riskRatio;
    }

    public void setRiskRatio(BigDecimal riskRatio) {
        this.riskRatio = riskRatio;
    }

    public BigDecimal getClaimPaidCur() {
        return claimPaidCur;
    }

    public void setClaimPaidCur(BigDecimal claimPaidCur) {
        this.claimPaidCur = claimPaidCur;
    }

    public BigDecimal getClaimPaidPre() {
        return claimPaidPre;
    }

    public void setClaimPaidPre(BigDecimal claimPaidPre) {
        this.claimPaidPre = claimPaidPre;
    }
}