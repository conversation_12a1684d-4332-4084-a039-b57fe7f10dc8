<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2024-03-21 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.conf.AtrConfCbTreasuryYcDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**BaseDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfCbTreasuryYc">
    <result column="interest_rate_id" property="interestRateId" jdbcType="NUMERIC" />
    <result column="tenor_years" property="tenorYears" jdbcType="NUMERIC" />
    <result column="avg_value" property="avgValue" jdbcType="NUMERIC" />
    <result column="rev_tenor_years" property="revTenorYears" jdbcType="NUMERIC" />
    <result column="creator_id" property="creatorId" jdbcType="NUMERIC" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    interest_rate_id, tenor_years, avg_value, rev_tenor_years, creator_id, created_time
  </sql>

  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="interestRateId != null">
        and interest_rate_id = #{interestRateId,jdbcType=NUMERIC}
      </if>
      <if test="tenorYears != null">
        and tenor_years = #{tenorYears,jdbcType=NUMERIC}
      </if>
      <if test="avgValue != null">
        and avg_value = #{avgValue,jdbcType=NUMERIC}
      </if>
      <if test="revTenorYears != null">
        and rev_tenor_years = #{revTenorYears,jdbcType=NUMERIC}
      </if>
      <if test="creatorId != null">
        and creator_id = #{creatorId,jdbcType=NUMERIC}
      </if>
      <if test="createdTime != null">
        and created_time = #{createdTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>

  <!-- 按interest_rate_id查询记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_cb_treasury_yc
    where interest_rate_id = #{interestRateId,jdbcType=NUMERIC}
  </select>

  <!-- 按interest_rate_id数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_cb_treasury_yc
    where interest_rate_id in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </select>

  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_cb_treasury_yc
  </select>

  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfCbTreasuryYc">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_cb_treasury_yc
    <include refid="Base_Select_By_Entity_Where" />
    order by tenor_years
  </select>

  <!-- 插入一条记录(为空的字段不操作) -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfCbTreasuryYc">
    insert into atr_conf_cb_treasury_yc
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="interestRateId != null">
        interest_rate_id,
      </if>
      <if test="tenorYears != null">
        tenor_years,
      </if>
      <if test="avgValue != null">
        avg_value,
      </if>
      <if test="revTenorYears != null">
        rev_tenor_years,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="interestRateId != null">
        #{interestRateId,jdbcType=NUMERIC},
      </if>
      <if test="tenorYears != null">
        #{tenorYears,jdbcType=NUMERIC},
      </if>
      <if test="avgValue != null">
        #{avgValue,jdbcType=NUMERIC},
      </if>
      <if test="revTenorYears != null">
        #{revTenorYears,jdbcType=NUMERIC},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=NUMERIC},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert into atr_conf_cb_treasury_yc
     (interest_rate_id, tenor_years, avg_value, 
      rev_tenor_years, creator_id, created_time)
     values 
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.interestRateId,jdbcType=NUMERIC}, #{item.tenorYears,jdbcType=NUMERIC}, #{item.avgValue,jdbcType=NUMERIC}, 
       #{item.revTenorYears,jdbcType=NUMERIC}, #{item.creatorId,jdbcType=NUMERIC}, #{item.createdTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <!-- 根据interest_rate_id和tenor_years更新记录 -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfCbTreasuryYc">
    update atr_conf_cb_treasury_yc
    <set>
      <if test="avgValue != null">
        avg_value = #{avgValue,jdbcType=NUMERIC},
      </if>
      <if test="revTenorYears != null">
        rev_tenor_years = #{revTenorYears,jdbcType=NUMERIC},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=NUMERIC},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where interest_rate_id = #{interestRateId,jdbcType=NUMERIC}
    and tenor_years = #{tenorYears,jdbcType=NUMERIC}
  </update>

  <!-- 按interest_rate_id和tenor_years删除记录 -->
  <delete id="deleteById" flushCache="true">
    delete from atr_conf_cb_treasury_yc
    where interest_rate_id = #{interestRateId,jdbcType=NUMERIC}
    and tenor_years = #{tenorYears,jdbcType=NUMERIC}
  </delete>

  <delete id="deleteByInterestRateId" flushCache="true">
    delete from atr_conf_cb_treasury_yc
    where interest_rate_id = #{interestRateId,jdbcType=NUMERIC}
  </delete>

  <!-- 按interest_rate_id批量删除记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from atr_conf_cb_treasury_yc
    where interest_rate_id in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </delete>

  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultType="java.lang.Integer" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfCbTreasuryYc">
    select count(1) from atr_conf_cb_treasury_yc
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper> 