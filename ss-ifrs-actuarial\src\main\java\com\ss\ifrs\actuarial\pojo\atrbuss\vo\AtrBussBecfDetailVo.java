/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2022-11-11 11:22:45
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2022-11-11 11:22:45<br/>
 * Description: BECF明细<br/>
 * Table Name: ATR_BUSS_BECF_DETAIL<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "BECF明细")
public class AtrBussBecfDetailVo implements Serializable {
    /**
     * Database column: ATR_BUSS_BECF_DETAIL.BECF_DETAIL_ID
     * Database remarks: becf_detail_id|主键
     */
    @ApiModelProperty(value = "becf_detail_id|主键", required = true)
    private Long becfDetailId;

    /**
     * Database column: ATR_BUSS_BECF_DETAIL.BECF_MAIN_ID
     * Database remarks: becf_main_id|主键
     */
    @ApiModelProperty(value = "becf_main_id|主键", required = true)
    private Long becfMainId;

    /**
     * Database column: ATR_BUSS_BECF_DETAIL.PORTFOLIO_NO
     * Database remarks: portfolio_no|合同组合
     */
    @ApiModelProperty(value = "portfolio_no|合同组合", required = false)
    private String portfolioNo;

    /**
     * Database column: ATR_BUSS_BECF_DETAIL.ICG_NO
     * Database remarks: icg_no|合同组
     */
    @ApiModelProperty(value = "icg_no|合同组", required = false)
    private String icgNo;

    /**
     * Database column: ATR_BUSS_BECF_DETAIL.RUN_NO
     * Database remarks: run_No|编号
     */
    @ApiModelProperty(value = "run_No|编号", required = false)
    private Long runNo;

    /**
     * Database column: ATR_BUSS_BECF_DETAIL.CF_DATE
     * Database remarks: cf_date|现金流日期
     */
    @ApiModelProperty(value = "cf_date|现金流日期", required = false)
    private String cfDate;

    /**
     * Database column: ATR_BUSS_BECF_DETAIL.EXPECTED_CLAIM_CURRENT
     * Database remarks: expected_claim_current|当前事故年预期赔付
     */
    @ApiModelProperty(value = "expected_claim_current|当前事故年预期赔付", required = false)
    private BigDecimal expectedClaimCurrent;

    /**
     * Database column: ATR_BUSS_BECF_DETAIL.EXPECTED_CLAIM_PAST
     * Database remarks: expected_claim_past|往前事故年预期赔付
     */
    @ApiModelProperty(value = "expected_claim_past|往前事故年预期赔付", required = false)
    private BigDecimal expectedClaimPast;

    /**
     * Database column: ATR_BUSS_BECF_DETAIL.EXPECTED_ULAE_CURRENT
     * Database remarks: expected_ulae_current|当前事故年间接理赔费用
     */
    @ApiModelProperty(value = "expected_ulae_current|当前事故年间接理赔费用", required = false)
    private BigDecimal expectedUlaeCurrent;

    /**
     * Database column: ATR_BUSS_BECF_DETAIL.EXPECTED_ULAE_PAST
     * Database remarks: expected_ulae_past|往前事故年间接理赔费用
     */
    @ApiModelProperty(value = "expected_ulae_past|往前事故年间接理赔费用", required = false)
    private BigDecimal expectedUlaePast;

    private static final long serialVersionUID = 1L;

    public Long getBecfDetailId() {
        return becfDetailId;
    }

    public void setBecfDetailId(Long becfDetailId) {
        this.becfDetailId = becfDetailId;
    }

    public Long getBecfMainId() {
        return becfMainId;
    }

    public void setBecfMainId(Long becfMainId) {
        this.becfMainId = becfMainId;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public Long getRunNo() {
        return runNo;
    }

    public void setRunNo(Long runNo) {
        this.runNo = runNo;
    }

    public String getCfDate() {
        return cfDate;
    }

    public void setCfDate(String cfDate) {
        this.cfDate = cfDate;
    }

    public BigDecimal getExpectedClaimCurrent() {
        return expectedClaimCurrent;
    }

    public void setExpectedClaimCurrent(BigDecimal expectedClaimCurrent) {
        this.expectedClaimCurrent = expectedClaimCurrent;
    }

    public BigDecimal getExpectedClaimPast() {
        return expectedClaimPast;
    }

    public void setExpectedClaimPast(BigDecimal expectedClaimPast) {
        this.expectedClaimPast = expectedClaimPast;
    }

    public BigDecimal getExpectedUlaeCurrent() {
        return expectedUlaeCurrent;
    }

    public void setExpectedUlaeCurrent(BigDecimal expectedUlaeCurrent) {
        this.expectedUlaeCurrent = expectedUlaeCurrent;
    }

    public BigDecimal getExpectedUlaePast() {
        return expectedUlaePast;
    }

    public void setExpectedUlaePast(BigDecimal expectedUlaePast) {
        this.expectedUlaePast = expectedUlaePast;
    }
}