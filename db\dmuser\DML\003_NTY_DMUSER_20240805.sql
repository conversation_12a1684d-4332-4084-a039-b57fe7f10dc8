UPDATE dm_conf_checkrule SET rule_sql = 'select id
		from odsuser.ods_claim_outstanding t
		where not exists (select 1
		from dm_policy_premium cm
		where cm.entity_code = t.entity_code
		and cm.policy_no = t.policy_no)
		and business_type_code = ''BF''', valid_is = '1', remark = '再保前未决信息的保单号与风险费用信息的保单号一致性' where rule_code = 'CLAIM_OUTSTANDING_POLICY_VALID_IS';


update dm_conf_table_column set col_length = '20' where biz_type_id = (
    select biz_type_id  from dm_conf_table where biz_code = 'REINS_OUTWARD') and col_code = upper('RI_ENDORSE_SEQ_NO') ;

update dm_conf_table_column set col_length = '20' where biz_type_id = (
    select biz_type_id  from dm_conf_table where biz_code = 'REINS_OUTWARD_DETAIL') and col_code = upper('RI_ENDORSE_SEQ_NO') ;


update dm_conf_checkrule set valid_is = 0 where rule_code = 'POLICY_PAYMENT_PLAN_PREMUIM_UNIFORMITY_IS';