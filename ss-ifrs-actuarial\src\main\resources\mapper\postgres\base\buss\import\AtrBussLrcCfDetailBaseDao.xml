<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2022-12-29 15:55:50 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussLrcCfDetailDao">
  <!-- 本文件由GIS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfDetail">
    <id column="LRC_CF_DETAIL_ID" property="lrcCfDetailId" jdbcType="DECIMAL" />
    <result column="LRC_CF_MAIN_ID" property="lrcCfMainId" jdbcType="DECIMAL" />
    <result column="PORTFOLIO_NO" property="portfolioNo" jdbcType="VARCHAR" />
    <result column="ICG_NO" property="icgNo" jdbcType="VARCHAR" />
    <result column="RUN_NO" property="runNo" jdbcType="DECIMAL" />
    <result column="CF_DATE" property="cfDate" jdbcType="VARCHAR" />
    <result column="MAINTENANCE_EXPENSE" property="maintenanceExpense" jdbcType="DECIMAL" />
    <result column="EXPECTED_CLAIM" property="expectedClaim" jdbcType="DECIMAL" />
    <result column="COVERAGE_UNIT" property="coverageUnit" jdbcType="DECIMAL" />
    <result column="EXPECTED_CLAIM_ADJ_COMMISSION" property="expectedClaimAdjCommission" jdbcType="DECIMAL" />
    <result column="CSM_AMORTIZED_RATE" property="csmAmortizedRate" jdbcType="DECIMAL" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    LRC_CF_DETAIL_ID, LRC_CF_MAIN_ID, PORTFOLIO_NO, ICG_NO, RUN_NO, CF_DATE, MAINTENANCE_EXPENSE, 
    EXPECTED_CLAIM, COVERAGE_UNIT,  EXPECTED_CLAIM_ADJ_COMMISSION,
    CSM_AMORTIZED_RATE
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="lrcCfDetailId != null ">
        and LRC_CF_DETAIL_ID = #{lrcCfDetailId,jdbcType=DECIMAL}
      </if>
      <if test="lrcCfMainId != null ">
        and LRC_CF_MAIN_ID = #{lrcCfMainId,jdbcType=DECIMAL}
      </if>
      <if test="portfolioNo != null and portfolioNo != ''">
        and PORTFOLIO_NO = #{portfolioNo,jdbcType=VARCHAR}
      </if>
      <if test="icgNo != null and icgNo != ''">
        and ICG_NO = #{icgNo,jdbcType=VARCHAR}
      </if>
      <if test="runNo != null ">
        and RUN_NO = #{runNo,jdbcType=DECIMAL}
      </if>
      <if test="cfDate != null and cfDate != ''">
        and CF_DATE = #{cfDate,jdbcType=VARCHAR}
      </if>
      <if test="maintenanceExpense != null ">
        and MAINTENANCE_EXPENSE = #{maintenanceExpense,jdbcType=DECIMAL}
      </if>
      <if test="expectedClaim != null ">
        and EXPECTED_CLAIM = #{expectedClaim,jdbcType=DECIMAL}
      </if>
      <if test="coverageUnit != null ">
        and COVERAGE_UNIT = #{coverageUnit,jdbcType=DECIMAL}
      </if>
      <if test="expectedClaimAdjCommission != null ">
        and EXPECTED_CLAIM_ADJ_COMMISSION = #{expectedClaimAdjCommission,jdbcType=DECIMAL}
      </if>
      <if test="csmAmortizedRate != null ">
        and CSM_AMORTIZED_RATE = #{csmAmortizedRate,jdbcType=DECIMAL}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.lrcCfDetailId != null ">
        and LRC_CF_DETAIL_ID = #{condition.lrcCfDetailId,jdbcType=DECIMAL}
      </if>
      <if test="condition.lrcCfMainId != null ">
        and LRC_CF_MAIN_ID = #{condition.lrcCfMainId,jdbcType=DECIMAL}
      </if>
      <if test="condition.portfolioNo != null and condition.portfolioNo != ''">
        and PORTFOLIO_NO = #{condition.portfolioNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.icgNo != null and condition.icgNo != ''">
        and ICG_NO = #{condition.icgNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.runNo != null ">
        and RUN_NO = #{condition.runNo,jdbcType=DECIMAL}
      </if>
      <if test="condition.cfDate != null and condition.cfDate != ''">
        and CF_DATE = #{condition.cfDate,jdbcType=VARCHAR}
      </if>
      <if test="condition.maintenanceExpense != null ">
        and MAINTENANCE_EXPENSE = #{condition.maintenanceExpense,jdbcType=DECIMAL}
      </if>
      <if test="condition.expectedClaim != null ">
        and EXPECTED_CLAIM = #{condition.expectedClaim,jdbcType=DECIMAL}
      </if>
      <if test="condition.coverageUnit != null ">
        and COVERAGE_UNIT = #{condition.coverageUnit,jdbcType=DECIMAL}
      </if>
      <if test="condition.expectedClaimAdjCommission != null ">
        and EXPECTED_CLAIM_ADJ_COMMISSION = #{condition.expectedClaimAdjCommission,jdbcType=DECIMAL}
      </if>
      <if test="condition.csmAmortizedRate != null ">
        and CSM_AMORTIZED_RATE = #{condition.csmAmortizedRate,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="lrcCfDetailId != null ">
        and LRC_CF_DETAIL_ID = #{lrcCfDetailId,jdbcType=DECIMAL}
      </if>
      <if test="lrcCfMainId != null ">
        and LRC_CF_MAIN_ID = #{lrcCfMainId,jdbcType=DECIMAL}
      </if>
      <if test="portfolioNo != null and portfolioNo != ''">
        and PORTFOLIO_NO = #{portfolioNo,jdbcType=VARCHAR}
      </if>
      <if test="icgNo != null and icgNo != ''">
        and ICG_NO = #{icgNo,jdbcType=VARCHAR}
      </if>
      <if test="runNo != null ">
        and RUN_NO = #{runNo,jdbcType=DECIMAL}
      </if>
      <if test="cfDate != null and cfDate != ''">
        and CF_DATE = #{cfDate,jdbcType=VARCHAR}
      </if>
      <if test="maintenanceExpense != null ">
        and MAINTENANCE_EXPENSE = #{maintenanceExpense,jdbcType=DECIMAL}
      </if>
      <if test="expectedClaim != null ">
        and EXPECTED_CLAIM = #{expectedClaim,jdbcType=DECIMAL}
      </if>
      <if test="coverageUnit != null ">
        and COVERAGE_UNIT = #{coverageUnit,jdbcType=DECIMAL}
      </if>
      <if test="expectedClaimAdjCommission != null ">
        and EXPECTED_CLAIM_ADJ_COMMISSION = #{expectedClaimAdjCommission,jdbcType=DECIMAL}
      </if>
      <if test="csmAmortizedRate != null ">
        and CSM_AMORTIZED_RATE = #{csmAmortizedRate,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List" />
    from ATR_BUSS_LRC_CF_DETAIL
    where LRC_CF_DETAIL_ID = #{lrcCfDetailId,jdbcType=DECIMAL}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select
    <include refid="Base_Column_List" />
    from ATR_BUSS_LRC_CF_DETAIL
    where LRC_CF_DETAIL_ID in
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ATR_BUSS_LRC_CF_DETAIL
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfDetail">
    select
    <include refid="Base_Column_List" />
    from ATR_BUSS_LRC_CF_DETAIL
    <include refid="Base_Select_By_Entity_Where" />
    order by PORTFOLIO_NO,ICG_NO,cf_Date
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select
    <include refid="Base_Column_List" />
    from ATR_BUSS_LRC_CF_DETAIL
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="LRC_CF_DETAIL_ID" keyProperty="lrcCfDetailId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfDetail">
    <selectKey resultType="long" keyProperty="lrcCfDetailId" order="BEFORE">
      select nextval('atr_seq_buss_lrc_cf_detail') as sequenceNo 
    </selectKey>
    insert into ATR_BUSS_LRC_CF_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="lrcCfDetailId != null">
        LRC_CF_DETAIL_ID,
      </if>
      <if test="lrcCfMainId != null">
        LRC_CF_MAIN_ID,
      </if>
      <if test="portfolioNo != null">
        PORTFOLIO_NO,
      </if>
      <if test="icgNo != null">
        ICG_NO,
      </if>
      <if test="runNo != null">
        RUN_NO,
      </if>
      <if test="cfDate != null">
        CF_DATE,
      </if>
      <if test="maintenanceExpense != null">
        MAINTENANCE_EXPENSE,
      </if>
      <if test="expectedClaim != null">
        EXPECTED_CLAIM,
      </if>
      <if test="coverageUnit != null">
        COVERAGE_UNIT,
      </if>
      <if test="expectedClaimAdjCommission != null">
        EXPECTED_CLAIM_ADJ_COMMISSION,
      </if>
      <if test="csmAmortizedRate != null">
        CSM_AMORTIZED_RATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="lrcCfDetailId != null">
        #{lrcCfDetailId,jdbcType=DECIMAL},
      </if>
      <if test="lrcCfMainId != null">
        #{lrcCfMainId,jdbcType=DECIMAL},
      </if>
      <if test="portfolioNo != null">
        #{portfolioNo,jdbcType=VARCHAR},
      </if>
      <if test="icgNo != null">
        #{icgNo,jdbcType=VARCHAR},
      </if>
      <if test="runNo != null">
        #{runNo,jdbcType=DECIMAL},
      </if>
      <if test="cfDate != null">
        #{cfDate,jdbcType=VARCHAR},
      </if>
      <if test="maintenanceExpense != null">
        #{maintenanceExpense,jdbcType=DECIMAL},
      </if>
      <if test="expectedClaim != null">
        #{expectedClaim,jdbcType=DECIMAL},
      </if>
      <if test="coverageUnit != null">
        #{coverageUnit,jdbcType=DECIMAL},
      </if>
      <if test="expectedClaimAdjCommission != null">
        #{expectedClaimAdjCommission,jdbcType=DECIMAL},
      </if>
      <if test="csmAmortizedRate != null">
        #{csmAmortizedRate,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all
    <foreach collection="list" item="item" index="index">
      into ATR_BUSS_LRC_CF_DETAIL values
      (#{item.lrcCfDetailId,jdbcType=DECIMAL},
      #{item.lrcCfMainId,jdbcType=DECIMAL}, #{item.portfolioNo,jdbcType=VARCHAR}, #{item.icgNo,jdbcType=VARCHAR},
      #{item.runNo,jdbcType=DECIMAL}, #{item.cfDate,jdbcType=VARCHAR}, #{item.maintenanceExpense,jdbcType=DECIMAL},
      #{item.expectedClaim,jdbcType=DECIMAL}, #{item.coverageUnit,jdbcType=DECIMAL},
      #{item.expectedClaimAdjCommission,jdbcType=DECIMAL}, #{item.csmAmortizedRate,jdbcType=DECIMAL}
    </foreach>
    select 1 
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfDetail">
    update ATR_BUSS_LRC_CF_DETAIL
    <set>
      <if test="lrcCfMainId != null">
        LRC_CF_MAIN_ID = #{lrcCfMainId,jdbcType=DECIMAL},
      </if>
      <if test="portfolioNo != null">
        PORTFOLIO_NO = #{portfolioNo,jdbcType=VARCHAR},
      </if>
      <if test="icgNo != null">
        ICG_NO = #{icgNo,jdbcType=VARCHAR},
      </if>
      <if test="runNo != null">
        RUN_NO = #{runNo,jdbcType=DECIMAL},
      </if>
      <if test="cfDate != null">
        CF_DATE = #{cfDate,jdbcType=VARCHAR},
      </if>
      <if test="maintenanceExpense != null">
        MAINTENANCE_EXPENSE = #{maintenanceExpense,jdbcType=DECIMAL},
      </if>
      <if test="expectedClaim != null">
        EXPECTED_CLAIM = #{expectedClaim,jdbcType=DECIMAL},
      </if>
      <if test="coverageUnit != null">
        COVERAGE_UNIT = #{coverageUnit,jdbcType=DECIMAL},
      </if>
      <if test="expectedClaimAdjCommission != null">
        EXPECTED_CLAIM_ADJ_COMMISSION = #{expectedClaimAdjCommission,jdbcType=DECIMAL},
      </if>
      <if test="csmAmortizedRate != null">
        CSM_AMORTIZED_RATE = #{csmAmortizedRate,jdbcType=DECIMAL},
      </if>
    </set>
    where LRC_CF_DETAIL_ID = #{lrcCfDetailId,jdbcType=DECIMAL}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfDetail">
    update ATR_BUSS_LRC_CF_DETAIL
    <set>
      <if test="record.lrcCfMainId != null">
        LRC_CF_MAIN_ID = #{record.lrcCfMainId,jdbcType=DECIMAL},
      </if>
      <if test="record.portfolioNo != null">
        PORTFOLIO_NO = #{record.portfolioNo,jdbcType=VARCHAR},
      </if>
      <if test="record.icgNo != null">
        ICG_NO = #{record.icgNo,jdbcType=VARCHAR},
      </if>
      <if test="record.runNo != null">
        RUN_NO = #{record.runNo,jdbcType=DECIMAL},
      </if>
      <if test="record.cfDate != null">
        CF_DATE = #{record.cfDate,jdbcType=VARCHAR},
      </if>
      <if test="record.maintenanceExpense != null">
        MAINTENANCE_EXPENSE = #{record.maintenanceExpense,jdbcType=DECIMAL},
      </if>
      <if test="record.expectedClaim != null">
        EXPECTED_CLAIM = #{record.expectedClaim,jdbcType=DECIMAL},
      </if>
      <if test="record.coverageUnit != null">
        COVERAGE_UNIT = #{record.coverageUnit,jdbcType=DECIMAL},
      </if>
      <if test="record.expectedClaimAdjCommission != null">
        EXPECTED_CLAIM_ADJ_COMMISSION = #{record.expectedClaimAdjCommission,jdbcType=DECIMAL},
      </if>
      <if test="record.csmAmortizedRate != null">
        CSM_AMORTIZED_RATE = #{record.csmAmortizedRate,jdbcType=DECIMAL},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from ATR_BUSS_LRC_CF_DETAIL
    where LRC_CF_DETAIL_ID = #{lrcCfDetailId,jdbcType=DECIMAL}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from ATR_BUSS_LRC_CF_DETAIL
    where LRC_CF_DETAIL_ID in
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from ATR_BUSS_LRC_CF_DETAIL
    where
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfDetail">
    select count(1) from ATR_BUSS_LRC_CF_DETAIL
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>