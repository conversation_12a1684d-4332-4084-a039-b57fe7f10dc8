/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2022-02-15 17:55:55
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao.buss.reserve;

import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveDacDetail;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveDacDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveDacVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2022-02-15 17:55:55<br/>
 * Description: dac结果明细表 Dao类<br/>
 * Related Table Name: atr_buss_reserve_dac_detail<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrBussReserveDacDetailDao extends IDao<AtrBussReserveDacDetail, Long> {
    Page<AtrBussReserveDacDetailVo> fuzzySearchPage(AtrBussReserveDacVo atrBussReserveDacVo, Pageable pageParam);

    List<AtrBussReserveDacDetailVo> fuzzySearchPage(AtrBussReserveDacVo atrBussReserveDacVo);

    void deleteByMainId(Long reserveDacId);
}