package com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Map;

@Getter
@Setter
public class AtrBussIbnrcalcReportClaimAmountVo {
    private String accidentNode;
    private BigDecimal ibnr;
    private BigDecimal os;
    private BigDecimal unpaidAmount;
    private BigDecimal sum;
    // key为futureNode,value为ReportedAmount
    private Map<String,BigDecimal> futureDataInfo;
}
