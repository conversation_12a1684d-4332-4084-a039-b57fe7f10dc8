/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-01-13 16:44:16
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-01-13 16:44:16<br/>
 * Description: LIC赔付现金流提取主表<br/>
 * Table Name: ATR_BUSS_LIC_ACTION<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "LIC赔付现金流提取主表")
public class AtrBussLicCashFlow implements Serializable {
    /**
     * Database column: ATR_BUSS_LIC_ACTION.ID
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: ATR_BUSS_LIC_ACTION.ACTION_NO
     * Database remarks: 执行编号|一次操作的全局唯一编号， 一次操作的维度是模块（LIC或LRC）、业务类型、业务单位、业务年月、币别
     */
    @ApiModelProperty(value = "执行编号|一次操作的全局唯一编号， 一次操作的维度是模块（LIC或LRC）、业务类型、业务单位、业务年月、币别", required = true)
    private String actionNo;

    /**
     * Database column: ATR_BUSS_LIC_ACTION.TASK_CODE
     * Database remarks: 版本号
     */
    @ApiModelProperty(value = "版本号", required = true)
    private String taskCode;

    /**
     * Database column: ATR_BUSS_LIC_ACTION.CENTER_ID
     * Database remarks: 业务单位ID
     */
    @ApiModelProperty(value = "业务单位ID", required = true)
    private Long entityId;

    /**
     * Database column: ATR_BUSS_LIC_ACTION.YEAR_MONTH
     * Database remarks: 业务年月|评估期
     */
    @ApiModelProperty(value = "业务年月|评估期", required = true)
    private String yearMonth;

    /**
     * Database column: ATR_BUSS_LIC_ACTION.CURRENCY
     * Database remarks: 币别
     */
    @ApiModelProperty(value = "币别", required = true)
    private String currencyCode;

    /**
     * Database column: ATR_BUSS_LIC_ACTION.PORTFOLIO_NO
     * Database remarks: 合同组合号码
     */
    @ApiModelProperty(value = "合同组合号码", required = true)
    private String portfolioNo;

    /**
     * Database column: ATR_BUSS_LIC_ACTION.BUSINESS_TYPE
     * Database remarks: 业务类型|DD-直保/临分分入；FO-再保临分(分出)；TI-再保合约(分入)；TO-再保合约(分出)
     */
    @ApiModelProperty(value = "业务类型|DD-直保/临分分入；FO-再保临分(分出)；TI-再保合约(分入)；TO-再保合约(分出)", required = true)
    private String businessSourceCode;

    /**
     * Database column: ATR_BUSS_LIC_ACTION.STATUS
     * Database remarks: 执行状态|R-执行中；E-执行异常；S-执行成功
     */
    @ApiModelProperty(value = "执行状态|R-执行中；E-执行异常；S-执行成功", required = true)
    private String status;

    /**
     * Database column: ATR_BUSS_LIC_ACTION.DRAW_TYPE
     * Database remarks: 提取类型|1-全量初始化；2-自动任务；3-页面手工
     */
    @ApiModelProperty(value = "提取类型|1-全量初始化；2-自动任务；3-页面手工", required = false)
    private String drawType;

    /**
     * Database column: ATR_BUSS_LIC_ACTION.DRAW_TIME
     * Database remarks: 提取时间
     */
    @ApiModelProperty(value = "提取时间", required = false)
    private Date drawTime;

    /**
     * Database column: ATR_BUSS_LIC_ACTION.DRAW_USER
     * Database remarks: 提取人
     */
    @ApiModelProperty(value = "提取人", required = false)
    private Long drawUser;

    /**
     * Database column: ATR_BUSS_LIC_ACTION.CONFIRM_IS
     * Database remarks: 是否确认|1-是、0-否
     */
    @ApiModelProperty(value = "是否确认|1-是、0-否", required = true)
    private String confirmIs;

    /**
     * Database column: ATR_BUSS_LIC_ACTION.CONFIRM_USER
     * Database remarks: 确认人
     */
    @ApiModelProperty(value = "确认人", required = false)
    private Long confirmUser;

    /**
     * Database column: ATR_BUSS_LIC_ACTION.CONFIRM_TIME
     * Database remarks: 确认时间
     */
    @ApiModelProperty(value = "确认时间", required = false)
    private Date confirmTime;

    /**
     * Database column: ATR_BUSS_LIC_ACTION.CREATOR_ID
     * Database remarks: 创建人
     */
    @ApiModelProperty(value = "创建人", required = false)
    private Long creatorId;

    /**
     * Database column: ATR_BUSS_LIC_ACTION.CREATE_TIME
     * Database remarks: 创建时间
     */
    @ApiModelProperty(value = "创建时间", required = false)
    private Date createTime;

    /**
     * Database column: ATR_BUSS_LIC_ACTION.UPDATOR_ID
     * Database remarks: 最后修改人
     */
    @ApiModelProperty(value = "最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: ATR_BUSS_LIC_ACTION.UPDATE_TIME
     * Database remarks: 最后修改时间
     */
    @ApiModelProperty(value = "最后修改时间", required = false)
    private Date updateTime;

    private String entityCode;
    private String entityCName;
    private String entityLName;
    private String entityEName;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getDrawType() {
        return drawType;
    }

    public void setDrawType(String drawType) {
        this.drawType = drawType;
    }

    public Date getDrawTime() {
        return drawTime;
    }

    public void setDrawTime(Date drawTime) {
        this.drawTime = drawTime;
    }

    public Long getDrawUser() {
        return drawUser;
    }

    public void setDrawUser(Long drawUser) {
        this.drawUser = drawUser;
    }

    public String getConfirmIs() {
        return confirmIs;
    }

    public void setConfirmIs(String confirmIs) {
        this.confirmIs = confirmIs;
    }

    public Long getConfirmUser() {
        return confirmUser;
    }

    public void setConfirmUser(Long confirmUser) {
        this.confirmUser = confirmUser;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getEntityCode() {
        return entityCode;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public String getEntityCName() {
        return entityCName;
    }

    public void setEntityCName(String entityCName) {
        this.entityCName = entityCName;
    }

    public String getEntityLName() {
        return entityLName;
    }

    public void setEntityLName(String entityLName) {
        this.entityLName = entityLName;
    }

    public String getEntityEName() {
        return entityEName;
    }

    public void setEntityEName(String entityEName) {
        this.entityEName = entityEName;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}