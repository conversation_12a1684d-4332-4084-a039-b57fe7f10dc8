[2m2025-07-24 11:10:14.908[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m The following profiles are active: dev
[2m2025-07-24 11:10:16.390[0;39m [dev] [33m WARN[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-24 11:10:16.594[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=ff1a2d2c-d19d-3931-a591-81b4348e9e38
[2m2025-07-24 11:10:17.007[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 7602 (http)
[2m2025-07-24 11:10:17.945[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 2990 ms
[2m2025-07-24 11:10:18.041[0;39m [dev] [33m WARN[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-24 11:10:18.043[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-24 11:10:18.057[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.netflix.config.DynamicPropertyFactory [0;39m [2m:[0;39m DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@181b8c4b
[2m2025-07-24 11:10:19.255[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-24 11:10:19.257[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-24 11:10:19.428[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-24 11:10:19.429[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-24 11:10:21.214[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.web.DefaultSecurityFilterChain    [0;39m [2m:[0;39m Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@49038f97, org.springframework.security.web.context.SecurityContextPersistenceFilter@13aed42b, org.springframework.security.web.header.HeaderWriterFilter@74500e4f, org.springframework.security.web.authentication.logout.LogoutFilter@647fb583, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@6c6928c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@78065fcd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4fbaa7f5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@36211bbc, org.springframework.security.web.session.SessionManagementFilter@1a717d79, org.springframework.security.web.access.ExceptionTranslationFilter@4899799b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5d829ef0]
[2m2025-07-24 11:10:21.262[0;39m [dev] [33m WARN[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-24 11:10:21.262[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-24 11:10:21.683[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService 'applicationTaskExecutor'
[2m2025-07-24 11:10:22.606[0;39m [dev] [33m WARN[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mockingLoadBalancerClientRibbonWarnLogger[0;39m [2m:[0;39m You already have RibbonLoadBalancerClient on your classpath. It will be used by default. As Spring Cloud Ribbon is in maintenance mode. We recommend switching to BlockingLoadBalancerClient instead. In order to use it, set the value of `spring.cloud.loadbalancer.ribbon.enabled` to `false` or remove spring-cloud-starter-netflix-ribbon from your project.
[2m2025-07-24 11:10:22.738[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-24 11:10:22.791[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Initializing Eureka in region us-east-1
[2m2025-07-24 11:10:22.791[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Client configured to neither register nor query for data.
[2m2025-07-24 11:10:22.802[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Discovery Client initialized at timestamp 1753326622801 with initial instances count: 0
[2m2025-07-24 11:10:22.850[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initializing ...
[2m2025-07-24 11:10:22.853[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Adding new peer nodes [**********************************************/eureka/]
[2m2025-07-24 11:10:23.368[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-24 11:10:23.369[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-24 11:10:23.369[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-24 11:10:23.369[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-24 11:10:23.475[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Replica node URL:  **********************************************/eureka/
[2m2025-07-24 11:10:23.486[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Finished initializing remote region registries. All known remote regions: []
[2m2025-07-24 11:10:23.487[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initialized
[2m2025-07-24 11:10:23.501[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 2 endpoint(s) beneath base path '/actuator'
[2m2025-07-24 11:10:23.593[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application SS-PLATFORM-EUREKA with eureka with status UP
[2m2025-07-24 11:10:23.597[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[      Thread-30][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Setting the eureka configuration..
[2m2025-07-24 11:10:23.597[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[      Thread-30][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka data center value eureka.datacenter is not set, defaulting to default
[2m2025-07-24 11:10:23.597[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[      Thread-30][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka environment value eureka.environment is not set, defaulting to test
[2m2025-07-24 11:10:23.614[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[      Thread-30][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m isAws returned false
[2m2025-07-24 11:10:23.614[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[      Thread-30][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Initialized server context
[2m2025-07-24 11:10:23.614[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[      Thread-30][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Got 1 instances from neighboring DS node
[2m2025-07-24 11:10:23.615[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[      Thread-30][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Renew threshold is: 1
[2m2025-07-24 11:10:23.615[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[      Thread-30][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Changing status to UP
[2m2025-07-24 11:10:23.625[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[      Thread-30][0;39m [36me.s.EurekaServerInitializerConfiguration[0;39m [2m:[0;39m Started Eureka Server
[2m2025-07-24 11:10:23.644[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 7602 (http) with context path ''
[2m2025-07-24 11:10:23.646[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 7602
[2m2025-07-24 11:10:24.158[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m Started EurekaServerApplication in 11.456 seconds (JVM running for 22.571)
[2m2025-07-24 11:10:24.820[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[3)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-24 11:10:24.829[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[3)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 8 ms
[2m2025-07-24 11:10:38.311[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=false)
[2m2025-07-24 11:10:39.033[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=true)
[2m2025-07-24 11:10:48.088[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=false)
[2m2025-07-24 11:10:48.609[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[nio-7602-exec-6][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=true)
[2m2025-07-24 11:10:49.325[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=false)
[2m2025-07-24 11:10:49.849[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[nio-7602-exec-9][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=true)
[2m2025-07-24 11:10:52.824[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=false)
[2m2025-07-24 11:10:53.354[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=true)
[2m2025-07-24 11:11:23.628[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-24 11:11:52.339[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-24 11:11:52.868[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-24 11:12:23.631[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-24 11:13:23.641[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-24 11:14:23.652[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-24 11:15:23.657[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-24 11:16:23.663[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-24 11:17:23.667[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-24 11:18:23.669[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-24 11:19:23.678[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-24 11:20:23.687[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-24 11:21:23.697[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-24 11:22:23.708[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-24 11:23:23.714[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-24 11:24:23.721[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-24 11:25:23.493[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-24 11:25:23.725[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-24 11:26:23.730[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-24 11:27:23.730[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-24 11:28:23.744[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-24 11:29:23.749[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-24 11:30:23.762[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-24 11:30:38.821[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[io-7602-exec-10][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status DOWN (replication=false)
[2m2025-07-24 11:30:39.344[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status DOWN (replication=true)
[2m2025-07-24 11:31:23.777[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-24 11:31:45.176[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-24 11:31:45.702[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-24 11:32:23.780[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-24 11:33:23.792[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-24 11:34:23.803[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-24 11:35:23.813[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-24 11:36:23.823[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-24 11:37:09.355[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status DOWN (replication=false)
[2m2025-07-24 11:37:09.874[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[nio-7602-exec-7][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status DOWN (replication=true)
[2m2025-07-24 11:37:13.050[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status DOWN (replication=false)
[2m2025-07-24 11:37:13.063[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status DOWN (replication=false)
[2m2025-07-24 11:37:13.067[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application SS-PLATFORM-EUREKA with eureka with status DOWN
[2m2025-07-24 11:37:13.072[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shutting down ...
[2m2025-07-24 11:37:13.073[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.batchSize.Will start computing stats again.
[2m2025-07-24 11:37:13.074[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.executionTime.Will start computing stats again.
[2m2025-07-24 11:37:13.075[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.executionTime.Will start computing stats again.
[2m2025-07-24 11:37:13.076[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.batchSize.Will start computing stats again.
[2m2025-07-24 11:37:13.077[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[nio-7602-exec-9][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status DOWN (replication=false)
[2m2025-07-24 11:37:13.077[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[io-7602-exec-10][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status DOWN (replication=false)
[2m2025-07-24 11:37:13.085[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shut down
[2m2025-07-24 11:37:13.088[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Shutting down ExecutorService 'applicationTaskExecutor'
[2m2025-07-24 11:37:13.092[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Shutting down DiscoveryClient ...
[2m2025-07-24 11:37:13.093[0;39m [dev] [32m INFO[0;39m [35m27404[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Completed shut down of DiscoveryClient
