package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.ti;

import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Tab("atr_buss_ti_lrc_u_dev")
public class AtrBussLrcTiIcuDev {

    /** 主表ID */
    private Long mainId;

    /** 发展期序号 */
    private Integer devNo;

    /** 业务年月 */
    private String yearMonth;

    /** 应收保费 */
    private BigDecimal recvPremium;

    /** 净额结算手续费 */
    private BigDecimal netFee;

    /** 跟单获取费用 */
    private BigDecimal iacf;

    /** 非跟单获取费用-对内 */
    private BigDecimal iaehcIn;

    /* 非跟单获取费用-对外 */
    private BigDecimal iaehcOut;

    /** 减值 */
    private BigDecimal badDebt;

    /** 已赚比例 */
    private BigDecimal edRate;

    /** 已赚保费 */
    private BigDecimal edPremium;

    /** 已赚净额结算 */
    private BigDecimal edNetFee;

    /** 已赚跟单获取费用 */
    private BigDecimal edIacf;

    /** 已赚非跟单获取费用（对内） */
    private BigDecimal edIaehcIn;

    /**  已赚非跟单获取费用（对外） */
    private BigDecimal edIaehcOut;

}
