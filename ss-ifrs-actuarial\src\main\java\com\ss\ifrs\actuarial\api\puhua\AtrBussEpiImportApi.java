package com.ss.ifrs.actuarial.api.puhua;

import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussClaimImportMainVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussIbnrImportMainVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLrcActionVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrTemplateVo;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapPlanImportMainVo;
import com.ss.ifrs.actuarial.service.AtrExportService;
import com.ss.ifrs.actuarial.service.puhua.AtrBussEpiEarnImportService;
import com.ss.ifrs.actuarial.service.puhua.AtrBussPuHuaLrcActionService;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ClassUtil;
import com.ss.library.utils.ExceptionUtil;
import com.ss.platform.core.annotation.LogScheduleTask;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.annotation.TrackUserBehavioral;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @ClassName: AtrBussEpiImportApi
 * @Description: EPI与已赚导入接口
 * @Author: yinxh.
 * @CreateDate: 2021/3/8 17:46
 * @Version: 1.0
 */
@RestController
@RequestMapping( "/dap_import")
public class AtrBussEpiImportApi extends BaseApi{

	// 日志管理
	final Logger LOG  = LoggerFactory.getLogger(getClass());

	@Autowired
	private AtrBussEpiEarnImportService atrBussLrcCashFlowService;

	@Autowired
	private AtrBussEpiEarnImportService atrBussEpiEarnImportService;

	@ApiOperation(value = "Plan导入主表查询")
	@TrackUserBehavioral(description = "enquiry CashFlow")
	@RequestMapping(value = "/enquiry", method = RequestMethod.POST)
	public BaseResponse<Map<String, Object>> enquiryPage(@RequestBody AtrDapPlanImportMainVo atrDapPlanImportMainVo, int _pageSize, int _pageNo) {
		Pageable pageParam = new Pageable(_pageNo, _pageSize);
		Map<String, Object> result = new HashMap<>();
		Page<AtrDapPlanImportMainVo> dapPlanImportMainVoPage = atrBussLrcCashFlowService.searchPage(atrDapPlanImportMainVo, pageParam);
		result.put("dapPlanImportMainVoList", dapPlanImportMainVoPage);
		return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
	}

	@ApiOperation(value = "根据id删除信息")
	@RequestMapping(value = "/delete_by_pk/{planMainId}", method = RequestMethod.GET)
	@PermissionRequest(required = false)
	public BaseResponse deleteById(@PathVariable("planMainId") Long planMainId) {
		 atrBussEpiEarnImportService.delete(planMainId);
		return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS);
	}

	@ApiOperation(value = "确认")
	@RequestMapping(value = "/confirm", method = RequestMethod.POST)
	public BaseResponse<Object> confirm(@RequestBody AtrDapPlanImportMainVo atrDapPlanImportMainVo, HttpServletRequest request) {
		Long userId = this.loginUserId(request);
		try {
			Boolean confirmFlag =  atrBussEpiEarnImportService.confirm(atrDapPlanImportMainVo, userId);
			return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, confirmFlag);
		} catch (Exception e) {
			logger.error(e.getLocalizedMessage(), e);
			return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
		}
	}

	@ApiOperation(value = "模板下载")
	@RequestMapping(value = "/downloadTemplate",method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public void downloadTemplate(@RequestBody AtrTemplateVo atrTemplateVo, HttpServletRequest request, HttpServletResponse response){
		try {
			Long userId = this.loginUserId(request);
			atrBussLrcCashFlowService.downloadEpiTemplate(atrTemplateVo, request, response);
		} catch (Exception e) {
			logger.error(e.getLocalizedMessage(), e);
		}
	}

	@ApiOperation(value = "EPI导入")
	@RequestMapping(value = "/epi/import",method = RequestMethod.POST)
	public BaseResponse<Object> epiImport(HttpServletRequest request, @RequestParam(value = "file", required = false) MultipartFile file,
										  AtrDapPlanImportMainVo atrDapPlanImportMainVo){
		Map<String, Object> resultMap = new HashMap<>();
        try {
			atrDapPlanImportMainVo.setCreatorId(this.loginUserId(request));
			atrBussLrcCashFlowService.epiImport(atrDapPlanImportMainVo,file);
			resultMap.put("success", true);
			resultMap.put("message", "导入成功");
			return new BaseResponse(ResCodeConstant.ResCode.SUCCESS, resultMap);
        } catch (Exception e) {
			String errorMsg = ExceptionUtil.getMessage(e);
			logger.error("假设值导入失败: {}", errorMsg, e);
			resultMap.put("success", false);
			resultMap.put("message", "导入失败: " + errorMsg);

			return new BaseResponse<>(ResCodeConstant.ResCode.SERVICE_UNREACHABLE, resultMap);
        }
    }


	/**
	 * 导入已赚保费数据
	 * @param file 上传的Excel文件
	 * @return 响应结果
	 */
	@ApiOperation(value = "已赚导入")
	@PostMapping("/earned/import")
	public BaseResponse<Object> importEarnedData(HttpServletRequest request,
			@RequestParam(value = "file") MultipartFile file,
			 AtrDapPlanImportMainVo atrDapPlanImportMainVo) {
		Map<String, Object> resultMap = new HashMap<>();
		try {
			atrDapPlanImportMainVo.setCreatorId(this.loginUserId(request));
			atrBussLrcCashFlowService.earnedImport(atrDapPlanImportMainVo, file);
			resultMap.put("success", true);
			resultMap.put("message", "导入成功");
			return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, resultMap);
		} catch (Exception e) {
			String errorMsg = ExceptionUtil.getMessage(e);
			logger.error("已赚保费导入失败: {}", errorMsg, e);
			resultMap.put("success", false);
			resultMap.put("message", "导入失败: " + errorMsg);
			return new BaseResponse<>(ResCodeConstant.ResCode.SERVICE_UNREACHABLE, resultMap);
		}
	}


	@ApiOperation(value = "获取导入数据详情")
	@RequestMapping(value = "/get_import_detail", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<Object> getImportDetail(
			@RequestBody AtrDapPlanImportMainVo atrDapPlanImportMainVo) {
		Map<String, Object> resultMap = new HashMap<>();
		try {
			if ("EPI".equals(atrDapPlanImportMainVo.getDataType())) {
				List<Map<String, Object>> details = atrBussLrcCashFlowService.getEpiImportDetail(atrDapPlanImportMainVo);
				resultMap.put("detailData", details);
				resultMap.put("success", true);
			} else if ("EARN".equals(atrDapPlanImportMainVo.getDataType())) {
				List<Map<String, Object>> details = atrBussLrcCashFlowService.getEarnedImportDetail(atrDapPlanImportMainVo);
				resultMap.put("detailData", details);
				resultMap.put("success", true);
			}
			return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, resultMap);
		} catch (Exception e) {
			logger.error("获取导入数据详情失败: {}", e.getMessage(), e);
			resultMap.put("success", false);
			resultMap.put("message", "获取详情失败: " + e.getMessage());
			return new BaseResponse<>(ResCodeConstant.ResCode.SERVICE_UNREACHABLE, resultMap);
		}
	}

	@ApiOperation(value = "获取导入数据列表")
	@RequestMapping(value = "/get_import_data_list", method = RequestMethod.POST)
	public BaseResponse<Object> getImportDataList(
			@RequestBody AtrDapPlanImportMainVo atrDapPlanImportMainVo,
			int _pageNo, int _pageSize) {
		Pageable pageParam = new Pageable(_pageNo, _pageSize);
		try {
			Page<Map<String, Object>> result = atrBussLrcCashFlowService.getImportDataList(atrDapPlanImportMainVo, pageParam);
			Map<String, Object> map = new HashMap<>();
			map.put("importDataList", result);
			return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, map);
		} catch (Exception e) {
			logger.error("获取导入数据列表失败: {}", e.getMessage(), e);
			return new BaseResponse<>(ResCodeConstant.ResCode.SERVICE_UNREACHABLE, e.getMessage());
		}
	}

	@ApiOperation(value = "删除导入数据")
	@RequestMapping(value = "/delete_import_data", method = RequestMethod.POST)
	public BaseResponse<Object> deleteImportData(
			@RequestBody Map<String, Object> params,HttpServletRequest request) {
		Long entityId = loginUserEntityId(request);
		String yearMonth = params.get("yearMonth").toString();
		String dataType = params.get("dataType").toString();
		String treatyNo = params.get("treatyNo").toString();
		String riskClassCode = params.get("riskClassCode").toString();
		try {
			boolean success = atrBussLrcCashFlowService.deleteImportData(entityId, yearMonth, treatyNo, riskClassCode, dataType);
			Map<String, Object> resultMap = new HashMap<>();
			resultMap.put("success", success);
			return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, resultMap);
		} catch (Exception e) {
			logger.error("删除导入数据失败: {}", e.getMessage(), e);
			return new BaseResponse<>(ResCodeConstant.ResCode.SERVICE_UNREACHABLE, e.getMessage());
		}
	}
}
