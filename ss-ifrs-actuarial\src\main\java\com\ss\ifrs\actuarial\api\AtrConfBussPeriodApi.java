package com.ss.ifrs.actuarial.api;

import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodBackVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodDetailVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConvertJsonVo;
import com.ss.ifrs.actuarial.pojo.domain.vo.buss.BussPeriodReqVo;
import com.ss.ifrs.actuarial.service.AtrConfBussPeriodDetailService;
import com.ss.ifrs.actuarial.service.AtrConfBussPeriodService;
import com.ss.platform.pojo.bms.job.vo.BmsScheduleJobVo;
import com.ss.platform.core.annotation.LogScheduleTask;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.ifrs.actuarial.constant.ActuarialConstant;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/atr_period")
@Api(value = "业务期间接口")
public class AtrConfBussPeriodApi extends BaseApi {
	// 日志管理
	final Logger LOG = LoggerFactory.getLogger(getClass());

	@Autowired
	private AtrConfBussPeriodService atrConfBussPeriodService;

	@Autowired
	private AtrConfBussPeriodDetailService atrConfBussPeriodDetailService;

	@ApiOperation("查询业务期间列表")
	@RequestMapping(value = "/enquiry", method = RequestMethod.POST)
	public BaseResponse<Object> enquiry(@RequestBody AtrConfBussPeriodVo atrConfBussPeriodVo, int _pageNo,
			int _pageSize) {
		Pageable pageableParam = new Pageable(_pageNo, _pageSize, null);
		Page<AtrConfBussPeriodVo> atrConfBussPeriodVoPage = atrConfBussPeriodService
				.findForDataTables(atrConfBussPeriodVo, pageableParam);
		// 返回结果集
		Map<String, Object> resultMap = new HashMap<>();
		resultMap.put("confBussPeriodVoList", atrConfBussPeriodVoPage);
		return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, resultMap);
	}

	@ApiOperation("查找业务期间列表对象")
	@RequestMapping(value = "/find_period_by_entityId", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<Object> findPeriodByEntityId(HttpServletRequest request,
			@RequestBody AtrConfBussPeriodVo atrConfBussPeriodVo) throws ParseException {
		atrConfBussPeriodVo.setBizCode(ActuarialConstant.BussCaseFlows.BUSS_BECF); // 计量任务
		AtrConfBussPeriodVo vo = atrConfBussPeriodService.findCurrentPeriod(atrConfBussPeriodVo);
		return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, vo);
	}

	@ApiOperation("查看业务期间对象详情列表")
	@RequestMapping(value = "/find_period_dtl_by_entityId", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<Map<String, Object>> findPeriodDtlByEntity(HttpServletRequest request,
			@RequestBody AtrConfBussPeriodVo atrConfBussPeriodVo) throws ParseException {
		List<AtrConfBussPeriodDetailVo> voList = atrConfBussPeriodDetailService
				.findPeriodDtlByEntityId(atrConfBussPeriodVo);
		Map<String, Object> resultMap = new HashMap<>();
		resultMap.put("confPeriodDtlVoList", voList);
		return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, resultMap);
	}

	/**
	 * 查询业务期间列表对象对应的定时任务信息
	 * 
	 * @param scheduleJobVo
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/find_schedule_by_bizCode", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<BmsScheduleJobVo> findJobDetail(@RequestBody BmsScheduleJobVo scheduleJobVo) throws Exception {
		BmsScheduleJobVo rs = atrConfBussPeriodDetailService.findJobByBizCode(scheduleJobVo);
		return new BaseResponse<BmsScheduleJobVo>(ResCodeConstant.ResCode.SUCCESS, rs);
	}

	/**
	 * 执行业务期间列表对象对应的定时任务
	 * 
	 * @param scheduleJobVo
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/immediateExecutionTask", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<BmsScheduleJobVo> immediateExecutionTask(@RequestBody BmsScheduleJobVo scheduleJobVo) {
		BmsScheduleJobVo rs = atrConfBussPeriodDetailService.findJobByBizCode(scheduleJobVo);
		try {
			atrConfBussPeriodDetailService.immediateExecutionTask(scheduleJobVo);
		} catch (Exception e) {
			LOG.error(e.getLocalizedMessage(), e);
			return new BaseResponse<BmsScheduleJobVo>(ResCodeConstant.ResCode.NULL_RESULT, rs);
		}
		return new BaseResponse<BmsScheduleJobVo>(ResCodeConstant.ResCode.SUCCESS, rs);
	}

	@ApiOperation("置当前及之后有效状态为无效")
	@RequestMapping(value = "/valid_status_next", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<String> updateValidIsNext(HttpServletRequest request,
			@RequestBody AtrConfBussPeriodVo atrConfBussPeriodVo) {

		// 获取当前操作人Id
		Long userId = this.loginUserId(request);

		try {
			String returnMsg = atrConfBussPeriodService.updateValidIsNext(atrConfBussPeriodVo, userId);

			return new BaseResponse<String>(ResCodeConstant.ResCode.SUCCESS, returnMsg);
		} catch (UnexpectedRollbackException e) {
			LOG.error(e.getLocalizedMessage(), e);
			return new BaseResponse<String>(ResCodeConstant.ResCode.DB_ERROR, null);
		}

	}

	@ApiOperation("檢查當前會計區間之前是否存在無效區間")
	@RequestMapping(value = "/findBefore", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<AtrConfBussPeriodVo> findBefore(@RequestBody AtrConfBussPeriodVo atrConfBussPeriodVo) {

		AtrConfBussPeriodVo accConfAccountPeriodVoResult = atrConfBussPeriodService.findBefore(atrConfBussPeriodVo);

		// 返回结果集
		return new BaseResponse<AtrConfBussPeriodVo>(ResCodeConstant.ResCode.SUCCESS, accConfAccountPeriodVoResult);

	}

	@ApiOperation("激活有效状态")
	@RequestMapping(value = "/valid_status", method = RequestMethod.POST)
	public BaseResponse<String> validStatus(HttpServletRequest request,
			@RequestBody AtrConfBussPeriodVo atrConfBussPeriodVo) {

		// 获取当前操作人Id
		Long userId = this.loginUserId(request);

		try {
			String returnMsg = atrConfBussPeriodService.updateValidIs(atrConfBussPeriodVo, userId);

			return new BaseResponse<String>(ResCodeConstant.ResCode.SUCCESS, returnMsg);
		} catch (UnexpectedRollbackException e) {
			LOG.error(e.getLocalizedMessage(), e);
			return new BaseResponse<String>(ResCodeConstant.ResCode.DB_ERROR, null);
		}

	}
	
	@ApiOperation("切换业务单位下的业务期间状态")
	@RequestMapping(value = "/sync_period_status", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	@LogScheduleTask(bizCode = "ATR_SYNC_PERIOD_STATUS", argsValue = { "#convertJsonVo.entityId",
			"#convertJsonVo.yearMonth",
			"#convertJsonVo.taskMode",
			"#convertJsonVo.taskCode"})
	public BaseResponse<String> syncPeriodStatusByEntity(HttpServletRequest request,
	                                                     @RequestBody AtrConvertJsonVo convertJsonVo) {
		// 不捕捉异常信息
		if (ObjectUtils.isEmpty(convertJsonVo.getEntityId())) {
			return new BaseResponse<String>(ResCodeConstant.ResCode.RULE_UNMATCH, null);
		}
		atrConfBussPeriodService.syncPeriodStatusByEntity(convertJsonVo.getEntityId(), convertJsonVo.getPeriodState());
		return new BaseResponse<String>(ResCodeConstant.ResCode.SUCCESS, null);
		
	}

	@ApiOperation(value = "流程概览接口")
	@RequestMapping(value = "/findOwActionLog", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	BaseResponse<List<AtrConfBussPeriodVo>> findAtrOwActionLog(@RequestBody AtrConfBussPeriodVo confBussPeriodVo) {
		List<AtrConfBussPeriodVo> atrConfBussPeriodVos = atrConfBussPeriodService.findOwActionLog(confBussPeriodVo);
		return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, atrConfBussPeriodVos);
	}


	@ApiOperation("重开评估期")
	@RequestMapping(value = "/reOpenBussPeriod", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<Boolean> reOpenAccountPeriod(@RequestBody AtrConfBussPeriodBackVo confBussPeriodVo) {
		Boolean result = atrConfBussPeriodService.reOpenBussPeriod(confBussPeriodVo);
		return new BaseResponse<Boolean>(ResCodeConstant.ResCode.SUCCESS, result);
	}

	@ApiOperation(value = "校验业务期间是否完成可执行状态")
	@RequestMapping(value = "/check_year_month_executable", method = RequestMethod.POST)
	public BaseResponse<Object> checkYearMonthExecutable(@RequestBody BussPeriodReqVo bussPeriodReqVo) {
		if (ObjectUtils.isNotEmpty(bussPeriodReqVo)) {
			return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, atrConfBussPeriodService.checkYearMonthExecutable(bussPeriodReqVo));
		}
		return new BaseResponse<>(ResCodeConstant.ResCode.RULE_UNMATCH, null);
	}



	@ApiOperation("检查是否有重开状态或没有已完成的账期")
	@RequestMapping(value = "/checkAccountPeriod", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<Map<String, Object>> checkAccountPeriod(HttpServletRequest request, @RequestBody AtrConfBussPeriodVo atrConfBussPeriodVo) {
		Map<String, Object> result = atrConfBussPeriodService.checkReOpenAccountPeriod(atrConfBussPeriodVo);
		return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
	}

	@ApiOperation(value = "会计期间重开接口")
	@RequestMapping(value = "/reopen_period", method = {RequestMethod.POST})
	@PermissionRequest(required = false)
	public BaseResponse reopenPeriod(@RequestBody AtrConfBussPeriodVo atrConfBussPeriodVo) {
		atrConfBussPeriodService.reOpenAccountPeriod(atrConfBussPeriodVo);
		return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS);
	}
}
