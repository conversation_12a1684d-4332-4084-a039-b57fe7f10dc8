package com.ss.ifrs.actuarial.api;

import com.ss.ifrs.actuarial.service.AtrImportService;
import com.ss.platform.core.annotation.PermissionRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/file_upload")
@Api(value = "计量文件上传接口")
public class AtrFileUploadApi {

    @Autowired
    AtrImportService atrImportService;

    @ApiOperation(value = "上传Excel导出模板")
    @RequestMapping(value = "/excel_template/import", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public void importExcelTemplate(HttpServletRequest request, @RequestParam(value = "file", required = false) MultipartFile file) throws Exception {
        try {
            atrImportService.importExcelTemplate(file);
        } catch (Exception e) {
            throw e;
        }
    }

}
