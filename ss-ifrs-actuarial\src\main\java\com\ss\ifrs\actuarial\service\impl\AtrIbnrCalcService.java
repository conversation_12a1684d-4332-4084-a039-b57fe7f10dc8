package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.dao.AtrBussIbnrImportMainDao;
import com.ss.ifrs.actuarial.dao.AtrBussIbnrcalcAccFutureDataDao;
import com.ss.ifrs.actuarial.dao.AtrBussIbnrcalcAccResultDao;
import com.ss.ifrs.actuarial.dao.AtrBussIbnrcalcActionDao;
import com.ss.ifrs.actuarial.dao.AtrBussIbnrcalcFinalTraceDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfBussPeriodDao;
import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcAccResult;
import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcAction;
import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcFinalTrace;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrCalcJobVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcActionAddVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcClaimVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcConfirmParamVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcDeleteParamVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcFinalTraceVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcParamVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcReportAmountVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcReportClaimAmountVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcStep3CalcParamDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcStep3CalcParamVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcStep4CalcParamDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcStep4CalcParamVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcStep4DataVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcStep4UpdateInfoVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcStepAccDataVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcStepAccDevDataVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcStepDevDataVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcStepQueryVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcStepVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcTVDInfoVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcTVDiscountInfoVO;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcTVDiscountVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrInflationRatioOverviewVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrInflationRatioRangeVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrInflationRatioVo;
import com.ss.ifrs.actuarial.service.AtrConfBussPeriodService;
import com.ss.ifrs.actuarial.util.dc.DataConverter;
import com.ss.ifrs.actuarial.util.dc.DictParam;
import com.ss.library.utils.LazyValidator;
import com.ss.library.utils.ReflectionUtils;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.util.ClassUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.beans.PropertyDescriptor;
import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.lang.invoke.SerializedLambda;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.function.Supplier;

@Service
public class AtrIbnrCalcService {

    @Resource
    private AtrBussIbnrcalcActionDao atrBussIbnrcalcActionDao;
    @Resource
    private AtrInflationConfService atrInflationConfService;
    @Resource
    private AtrBussIbnrcalcAccResultDao atrBussIbnrcalcAccResultDao;
    @Resource
    private AtrBussIbnrImportMainDao atrBussIbnrImportMainDao;
    @Resource
    private AtrBussIbnrcalcAccFutureDataDao atrBussIbnrcalcAccFutureDataDao;
    @Resource
    private AtrBussIbnrcalcFinalTraceDao atrBussIbnrcalcFinalTraceDao;
    @Resource
    private AtrLogActionErrorService atrLogActionErrorService;
    @Resource
    private AtrConfBussPeriodDao atrConfBussPeriodDao;
    @Resource
    private AtrConfBussPeriodService atrConfBussPeriodService;

    /**
     * 自动任务
     */
    public void autoJob(AtrBussIbnrCalcJobVo vo) {
        String actionNo = atrBussIbnrcalcActionDao.createActionNo(Math.random());
        try {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("entityId", vo.getEntityId());
            paramMap.put("yearMonth", vo.getYearMonth());
            paramMap.put("actionNo", actionNo);
            atrBussIbnrcalcActionDao.autoJob(paramMap);
            syncIbnrImport(findActionPo(actionNo));
            atrConfBussPeriodService.syncPeriodStatus(vo.getEntityId(), CommonConstant.BussPeriod.PeriodStatus.PREPARED);
        } catch (Exception e) {
            updateActionError(actionNo, e);
            throw e;
        }
    }

    public String addAction(AtrBussIbnrcalcActionAddVo vo, Long userId) {
        LazyValidator.validate(v -> {
            v.notEmpty(vo.getEntityId(), "entity id");
            v.notEmpty(vo.getCurrencyCode(), "currency");
            v.notEmpty(vo.getExtractionInterval(), "extraction interval");
            v.notEmpty(vo.getExtractionZones(), "extraction zones");
            v.notEmpty(vo.getExtractionDeadline(), "extraction deadline");
        });

        String actionNo = atrBussIbnrcalcActionDao.createActionNo(Math.random());

        AtrBussIbnrcalcAction po = new AtrBussIbnrcalcAction();
        BeanUtils.copyProperties(vo, po);
        po.setActionNo(actionNo);
        po.setConfirmIs("0");
        po.setCreateId(userId);
        po.setCreateTime(new Date());
        po.setStatus("R");
        atrBussIbnrcalcActionDao.save(po);
        Long id = po.getId();

        new Thread(() -> {
            calcAction(id, actionNo);
        }).start();

        int total = 5;
        while (total-- > 0) {
            try {
                Thread.sleep(2000);
            } catch (Exception ignored) {
            }

            po = atrBussIbnrcalcActionDao.findById(id);
            String status = po.getStatus();
            if ("E".equals(status)) {
                return "The calculation failed";
            }
            if ("S".equals(status)) {
                return "The calculation was successful";
            }
        }

        return "The calculation will take a few minutes, please check the calculation results later";
    }

    private void updateActionError(String actionNo, Throwable e) {
        AtrBussIbnrcalcAction po = new AtrBussIbnrcalcAction();
        po.setActionNo(actionNo);
        po = atrBussIbnrcalcActionDao.findList(po).get(0);
        po.setStatus("E");
        StringWriter sw = new StringWriter();
        ExceptionUtils.getRootCause(e).printStackTrace(new PrintWriter(sw));
        po.setErrorMsg(StringUtils.substring(sw.toString(), 0, 3000));
        atrBussIbnrcalcActionDao.updateById(po);
    }

    private void calcAction(Long actionId, String actionNo) {
        try {
            atrBussIbnrcalcActionDao.calcStep1(actionNo);
            atrBussIbnrcalcActionDao.calcStep2(actionNo);
            atrBussIbnrcalcActionDao.calcStep3(actionNo);
            atrBussIbnrcalcActionDao.calcStep4(actionNo);

            AtrBussIbnrcalcAction po = atrBussIbnrcalcActionDao.findById(actionId);
            po.setStatus("S");
            po.setCompleteTime(new Date());
            atrBussIbnrcalcActionDao.updateById(po);
        } catch (Exception e) {
            AtrBussIbnrcalcAction po = atrBussIbnrcalcActionDao.findById(actionId);
            po.setStatus("E");
            po.setCompleteTime(new Date());
            po.setErrorMsg(StringUtils.substring(getRootMsg(e), 0, 1000));
            atrBussIbnrcalcActionDao.updateById(po);

            atrLogActionErrorService.addLog("ibnr_calc_calc", actionNo, e);
        }
    }

    private String getRootMsg(Throwable t) {
        Throwable rootCause = ExceptionUtils.getRootCause(t);
        if (rootCause != null) {
            return rootCause.getMessage();
        } else {
            return t.getMessage();
        }
    }

    public AtrBussIbnrcalcStepVo getAllStep(AtrBussIbnrcalcStepQueryVo queryVo) {
        LazyValidator.validate(v -> {
            v.notEmpty(queryVo.getActionNo(), "action no.");
            v.notEmpty(queryVo.getIcpId(), "icp id");
        });
        return getAllActionStep(queryVo.getActionNo(), queryVo.getIcpId()).get(0);
    }

    public List<AtrBussIbnrcalcStepVo> getAllActionStep(String actionNo, Long targetIcpId) {
        getCompleteActionPo(actionNo);

        AtrBussIbnrcalcStepQueryVo queryVo = new AtrBussIbnrcalcStepQueryVo();
        queryVo.setActionNo(actionNo);
        queryVo.setIcpId(targetIcpId);

        List<AtrBussIbnrcalcStepVo> vos = atrBussIbnrcalcActionDao.findStepBase(queryVo);
        new DataConverter()
                .addDictParam(DictParam.ofEntityCode())
                .addDictParam(DictParam.ofEntityName())
                .addDictParam(DictParam.ofLoa())
                .addDictParam(DictParam.ofAtrCode("extractionInterval", "SimpleExtractInterval"))
                .addDictParam(DictParam.ofAtrCode("ibnrType", "IbnrType"))
                .translate(vos);

        List<String> accidentNodes = atrBussIbnrcalcActionDao.findAccidentNodes(actionNo);

        List<Long> devNos = atrBussIbnrcalcActionDao.findDevNos(actionNo);

        AtrInflationRatioRangeVo ratioRangeVo = atrBussIbnrcalcActionDao.queryBussInfRatioRange(actionNo);
        List<AtrInflationRatioVo> inflationRatioVos = atrBussIbnrcalcActionDao.queryAllBussInfRatio(actionNo);
        AtrInflationRatioOverviewVo infRatioData = atrInflationConfService
                .overviewConfRatio(ratioRangeVo, inflationRatioVos);

        List<AtrBussIbnrcalcStepAccDevDataVo> accDevVos = atrBussIbnrcalcActionDao.findAccDevData(queryVo);

        List<AtrBussIbnrcalcStepDevDataVo> devVos = atrBussIbnrcalcActionDao.findDevData(queryVo);

        List<AtrBussIbnrcalcStepAccDataVo> accVos = atrBussIbnrcalcActionDao.findAccData(queryVo);

        for (AtrBussIbnrcalcStepVo vo : vos) {
            vo.setAccidentNodeDesc("Y".equals(vo.getExtractionInterval()) ? "Accident Year" : "Accident Year Month");
            vo.setAccidentNodes(accidentNodes);
            vo.setDevNos(devNos);
            vo.setInfRatioData(infRatioData);

            long icpId = vo.getIcpId();

            for (AtrBussIbnrcalcStepAccDevDataVo accDevVo : accDevVos) {
                if (icpId == accDevVo.getIcpId()) {
                    vo.getAccDevData().add(accDevVo);
                }
            }

            for (AtrBussIbnrcalcStepDevDataVo devVo : devVos) {
                if (icpId == devVo.getIcpId()) {
                    vo.getDevData().add(devVo);
                }
            }

            for (AtrBussIbnrcalcStepAccDataVo accVo : accVos) {
                if (icpId == accVo.getIcpId()) {
                    vo.getAccData().add(accVo);
                }
            }

        }

        for (AtrBussIbnrcalcStepVo vo : vos) {
            vo.setAccData(addTotal(vo.getAccData()));
        }
        for (AtrBussIbnrcalcStepVo vo : vos) {
            vo.setAtrBussIbnrcalcStep4DataVo(getStep4Info(vo.getActionNo(), vo.getIcpId(), vo.getExtractionInterval()));
        }
        return vos;
    }

    private List<AtrBussIbnrcalcStepAccDataVo> addTotal(List<AtrBussIbnrcalcStepAccDataVo> vos) {
        AtrBussIbnrcalcStepAccDataVo totalVo = new AtrBussIbnrcalcStepAccDataVo();
        totalVo.setAccidentNode("Total");
        sum(totalVo, vos, totalVo::getEdPremiumOri);
        sum(totalVo, vos, totalVo::getEdPremium);
        sum(totalVo, vos, totalVo::getSetteldAmount);
        sum(totalVo, vos, totalVo::getOsAmount);
        sum(totalVo, vos, totalVo::getReportedAmount);
        sum(totalVo, vos, totalVo::getLrUltimateLoss);
        sum(totalVo, vos, totalVo::getLrIbnr);
        sum(totalVo, vos, totalVo::getClProjReportedUltimate);
        sum(totalVo, vos, totalVo::getClIbnr);
        sum(totalVo, vos, totalVo::getBfProjReportedUltimate);
        sum(totalVo, vos, totalVo::getBfIbnr);

        vos = new ArrayList<>(vos);
        vos.add(totalVo);
        return vos;
    }

    private void sum(AtrBussIbnrcalcStepAccDataVo totalVo,
                     List<AtrBussIbnrcalcStepAccDataVo> vos, ReadMethod<?> readMethod) {
        String field = getFieldName(readMethod);
        PropertyDescriptor pd = BeanUtils.getPropertyDescriptor(totalVo.getClass(), field);
        assert pd != null;

        BigDecimal s = BigDecimal.ZERO;
        for (AtrBussIbnrcalcStepAccDataVo vo : vos) {
            Object value = ReflectionUtils.invokeMethod(pd.getReadMethod(), vo);
            if (value != null) {
                s = s.add((BigDecimal) value);
            }
        }

        ReflectionUtils.invokeMethod(pd.getWriteMethod(), totalVo, s);
    }

    private String getFieldName(ReadMethod<?> readMethod) {
        try {
            Method declaredMethod = readMethod.getClass().getDeclaredMethod("writeReplace");
            declaredMethod.setAccessible(Boolean.TRUE);
            SerializedLambda serializedLambda = (SerializedLambda) declaredMethod.invoke(readMethod);
            String method = serializedLambda.getImplMethodName();
            String attr = method.startsWith("is") ? method.substring(2) : method.substring(3);
            return StringUtils.uncapitalize(attr);
        } catch (ReflectiveOperationException e) {
            throw new RuntimeException(e);
        }
    }

    @Transactional
    public void calcStep3(AtrBussIbnrcalcStep3CalcParamVo vo) {
        LazyValidator.validate(v -> {
            v.notEmpty(vo.getActionNo(), "action no.");
            v.notEmpty(vo.getIcpId(), "icp id");
        });

        getCompleteActionPo(vo.getActionNo());

        AtrBussIbnrcalcAccResult accResultQ = new AtrBussIbnrcalcAccResult();
        accResultQ.setActionNo(vo.getActionNo());
        accResultQ.setIcpId(vo.getIcpId());
        List<AtrBussIbnrcalcAccResult> accResults = atrBussIbnrcalcAccResultDao.findList(accResultQ);

        String edPremiumChangeIs = "0";

        for (AtrBussIbnrcalcStep3CalcParamDetailVo detail : vo.getDetails()) {
            String accidentNode = detail.getAccidentNode();
            for (AtrBussIbnrcalcAccResult accResult : accResults) {
                if (accResult.getAccidentNode().equals(accidentNode)) {
                    if (!equals(accResult.getEdPremium(), detail.getEdPremium())) {
                        edPremiumChangeIs = "1";
                    }
                    accResult.setEdPremium(detail.getEdPremium());
                    accResult.setLrExpectedLossRatio(detail.getLrExpectedLossRatio());
                    accResult.setBfExpectedLossRatio(detail.getBfExpectedLossRatio());

                    atrBussIbnrcalcAccResultDao.updateById(accResult);
                }
            }
        }

        AtrBussIbnrcalcParamVo paramVo = new AtrBussIbnrcalcParamVo();
        paramVo.setActionNo(vo.getActionNo());
        paramVo.setIcpId(vo.getIcpId());
        paramVo.setEdPremiumChangeIs(edPremiumChangeIs);
        atrBussIbnrcalcActionDao.calcStep3Manual(paramVo);
    }

    @Transactional
    public void calcStep4(AtrBussIbnrcalcStep4CalcParamVo vo) {
        LazyValidator.validate(v -> {
            v.notEmpty(vo.getActionNo(), "action no.");
            v.notEmpty(vo.getIcpId(), "icp id");
        });

        Map<String, AtrBussIbnrcalcStep4UpdateInfoVo> diff = getDiff(vo);
        diff.values().forEach(item -> {
            atrBussIbnrcalcActionDao.calcStep4Manual(vo.getActionNo(), item.getModifyType(), item.getMethodType(), vo.getIcpId());
        });
    }

    /**
     * 获取变动点，返回的map的value中的值分别为修改的评估方法以及修改标识
     *
     * @return
     */
    private Map<String, AtrBussIbnrcalcStep4UpdateInfoVo> getDiff(AtrBussIbnrcalcStep4CalcParamVo vo) {
        List<AtrBussIbnrcalcTVDiscountVo> atrBussIbnrcalcTVDiscountVos = atrBussIbnrcalcActionDao.queryIbnrcalcInterestRatio(vo.getActionNo(), vo.getIcpId());
        AtrBussIbnrcalcFinalTrace traceParam = new AtrBussIbnrcalcFinalTrace();
        traceParam.setActionNo(vo.getActionNo());
        traceParam.setIcpId(vo.getIcpId());
        List<AtrBussIbnrcalcFinalTrace> atrBussIbnrcalcFinalTraceDaoList = atrBussIbnrcalcFinalTraceDao.findList(traceParam);
        List<BigDecimal> interestRatioCL = vo.getCL().getInterestRatio();
        List<BigDecimal> interestRatioLR = vo.getLR().getInterestRatio();
        List<BigDecimal> interestRatioBF = vo.getBF().getInterestRatio();
        HashMap<String, AtrBussIbnrcalcStep4UpdateInfoVo> result = new HashMap<>();
        for (int i = 0; i < atrBussIbnrcalcTVDiscountVos.size(); i++) {
            // LR
            if (!equals(atrBussIbnrcalcTVDiscountVos.get(i).getLrInterestRatio(), interestRatioLR.get(i))) {
                atrBussIbnrcalcActionDao.updateIbnrcalcInterestRatio(vo.getActionNo(), vo.getIcpId(), i, "LR", interestRatioLR.get(i));
                if (result.get("LR") == null)
                    result.put("LR", new AtrBussIbnrcalcStep4UpdateInfoVo("LR", 1));

            }
            // BF
            if (!equals(atrBussIbnrcalcTVDiscountVos.get(i).getBfInterestRatio(), interestRatioBF.get(i))) {
                atrBussIbnrcalcActionDao.updateIbnrcalcInterestRatio(vo.getActionNo(), vo.getIcpId(), i, "BF", interestRatioBF.get(i));
                if (result.get("BF") == null)
                    result.put("BF", new AtrBussIbnrcalcStep4UpdateInfoVo("BF", 1));

            }
            // CL
            if (!equals(atrBussIbnrcalcTVDiscountVos.get(i).getClInterestRatio(), interestRatioCL.get(i))) {
                atrBussIbnrcalcActionDao.updateIbnrcalcInterestRatio(vo.getActionNo(), vo.getIcpId(), i, "CL", interestRatioCL.get(i));
                if (result.get("CL") == null)
                    result.put("CL", new AtrBussIbnrcalcStep4UpdateInfoVo("CL", 1));
            }
        }
        Map<String, BigDecimal> finalFactoryMap = new HashMap<>();
        atrBussIbnrcalcFinalTraceDaoList.forEach(item -> {
            finalFactoryMap.put(item.getMethodType() + item.getTraceType(), item.getFactor());
        });

        List<String> methodTypes = Arrays.asList("CL", "LR", "BF");
        List<String> traceTypes = Arrays.asList("ICHE", "TVD", "PAD");
        methodTypes.forEach(item -> {
            AtrBussIbnrcalcStep4CalcParamDetailVo atrBussIbnrcalcStep4CalcParamDetailVo = (AtrBussIbnrcalcStep4CalcParamDetailVo) ClassUtil.getFieldValue(vo, item);
            traceTypes.forEach(t -> {
                BigDecimal temp = (BigDecimal) ClassUtil.getFieldValue(atrBussIbnrcalcStep4CalcParamDetailVo, t);
                BigDecimal tempP = temp.divide(BigDecimal.valueOf(100));
                if (!equals(tempP, finalFactoryMap.get(item + t))) {
                    // 不相等的逻辑，做两个操作更新配置信息，获取修改类型
                    atrBussIbnrcalcFinalTraceDao.updateIbnrcalcFinalFactor(vo.getActionNo(), vo.getIcpId(), item, t, temp);
                    if (result.get(item) == null) {
                        result.put(item, new AtrBussIbnrcalcStep4UpdateInfoVo(item, "ICHE".equals(t) ? 2 : "TVD".equals(t) ? 3 : 4));
                    }
                }
            });
        });
        return result;
    }

    private boolean equals(BigDecimal a, BigDecimal b) {
        if (a == null && b == null) {
            return true;
        }
        if (a == null || b == null) {
            return false;
        }
        return a.compareTo(b) == 0;
    }

    private AtrBussIbnrcalcAction getCompleteActionPo(String actionNo) {
        AtrBussIbnrcalcAction action = new AtrBussIbnrcalcAction();
        action.setActionNo(actionNo);
        List<AtrBussIbnrcalcAction> actions = atrBussIbnrcalcActionDao.findList(action);
        if (actions.isEmpty()) {
            throw new RuntimeException("Action " + actionNo + " doesn't exist");
        }
        action = actions.get(0);
        if (!"S".equals(action.getStatus())) {
            throw new RuntimeException("It is not completed and cannot be viewed at this time");
        }

        return action;
    }

    @Transactional
    public void confirm(AtrBussIbnrcalcConfirmParamVo vo, Long userId) {
        LazyValidator.validate(v -> {
            v.notEmpty(vo.getActionNo(), "action no.");
            v.notEmpty(vo.getConfirmMethod(), "confirm method");
        });

        AtrBussIbnrcalcAction action = getCompleteActionPo(vo.getActionNo());
        action.setConfirmIs("1");
        action.setConfirmId(userId);
        action.setConfirmTime(new Date());
        action.setConfirmMethod(vo.getConfirmMethod());
        atrBussIbnrcalcActionDao.updateById(action);

        if ("M".equals(action.getExtractionInterval())) {
            syncIbnrImport(action);
        }
    }

    private void syncIbnrImport(AtrBussIbnrcalcAction action) {
        Map<String, Object> paramMap = new LinkedHashMap<>();
        paramMap.put("actionNo", action.getActionNo());
        paramMap.put("threshold", 0.009); // 阈值, 如果计算出的 IBNR 的绝对值少于它， 则不同步
        paramMap.put("userId", action.getConfirmId() == null ? 1 : action.getConfirmId());
        paramMap.put("entityId", action.getEntityId());
        paramMap.put("currencyCode", action.getCurrencyCode());
        paramMap.put("yearMonth", action.getExtractionDeadline());

        int checkCount = atrBussIbnrcalcActionDao.checkNeedToSyncImport(paramMap);
        if (checkCount > 0) {
            long importMainId = atrBussIbnrImportMainDao.nextSeq();
            paramMap.put("importMainId", importMainId);
            atrBussIbnrcalcActionDao.revokeIbnrImportConfirmation(paramMap);
            atrBussIbnrcalcActionDao.syncIbnrImportMain(paramMap);
            atrBussIbnrcalcActionDao.syncIbnrImportDetail(paramMap);
        }
    }

    public void delete(AtrBussIbnrcalcDeleteParamVo vo) {
        LazyValidator.validate(v -> {
            v.notEmpty(vo.getActionNo(), "action no.");
        });

        atrBussIbnrcalcActionDao.delete(vo.getActionNo());
    }

    private HashMap<String, Object> getStep4Info(String actionNo, Long icpId, String interval) {
        // 查询claimInfo以及ICHE因子
        AtrBussIbnrcalcClaimVo atrBussIbnrcalcClaimVo = atrBussIbnrcalcActionDao.queryIbnrcalcClaimInfo(actionNo, icpId);
        HashMap<String, Object> result = new HashMap<>();
        result.put("claimInfo", atrBussIbnrcalcClaimVo);
        AtrBussIbnrcalcStep4DataVo atrBussIbnrcalcStep4DataVoCL = new AtrBussIbnrcalcStep4DataVo();
        AtrBussIbnrcalcStep4DataVo atrBussIbnrcalcStep4DataVoLR = new AtrBussIbnrcalcStep4DataVo();
        AtrBussIbnrcalcStep4DataVo atrBussIbnrcalcStep4DataVoBF = new AtrBussIbnrcalcStep4DataVo();
        result.put("CL", atrBussIbnrcalcStep4DataVoCL);
        result.put("LR", atrBussIbnrcalcStep4DataVoLR);
        result.put("BF", atrBussIbnrcalcStep4DataVoBF);
        result.put("descTable2", Arrays.asList("Time", "Expected % Reported", "Interest Ratio", "Time Period", "Y".equalsIgnoreCase(interval) ? "Year" : "Year Month", "Discounted Unpaid", "Time Value Discount"));
        // 获取reportAmount信息
        List<AtrBussIbnrcalcReportAmountVo> atrBussIbnrcalcReportAmountVos = atrBussIbnrcalcAccFutureDataDao.listIbnrcalcReportAmountInfo(icpId, actionNo);
        Map<String, AtrBussIbnrcalcReportClaimAmountVo> atrBussIbnrcalcReportClaimAmountVoHashMapCL = new TreeMap<>();
        Map<String, AtrBussIbnrcalcReportClaimAmountVo> atrBussIbnrcalcReportClaimAmountVoHashMapBF = new TreeMap<>();
        Map<String, AtrBussIbnrcalcReportClaimAmountVo> atrBussIbnrcalcReportClaimAmountVoHashMapLR = new TreeMap<>();
        /*
            1. 先判断该出险节点是否存在
                如果存在->则找到该条记录，将里面的HashMap对象找出来
               不存在则新建出现节点信息
         */
        atrBussIbnrcalcReportAmountVos.forEach(item -> {
            if (!atrBussIbnrcalcReportClaimAmountVoHashMapCL.containsKey(item.getAccidentNode())) {
                // CL
                AtrBussIbnrcalcReportClaimAmountVo tempCL = new AtrBussIbnrcalcReportClaimAmountVo();
                tempCL.setIbnr(item.getClIbnr());
                tempCL.setOs(item.getOs());
                tempCL.setUnpaidAmount(item.getClUnpaidAmount());
                tempCL.setAccidentNode(item.getAccidentNode());
                tempCL.setSum(item.getClReportedAmount());
                Map<String, BigDecimal> tempHashCL = new TreeMap<>();
                tempHashCL.put(item.getFutureNode(), item.getClReportedAmount());
                tempCL.setFutureDataInfo(tempHashCL);
                atrBussIbnrcalcReportClaimAmountVoHashMapCL.put(item.getAccidentNode(), tempCL);
                // BF
                AtrBussIbnrcalcReportClaimAmountVo tempBF = new AtrBussIbnrcalcReportClaimAmountVo();
                tempBF.setIbnr(item.getBfIbnr());
                tempBF.setOs(item.getOs());
                tempBF.setUnpaidAmount(item.getBfUnpaidAmount());
                tempBF.setAccidentNode(item.getAccidentNode());
                tempBF.setSum(item.getBfReportedAmount());
                Map<String, BigDecimal> tempHashBF = new TreeMap<>();
                tempHashBF.put(item.getFutureNode(), item.getBfReportedAmount());
                tempBF.setFutureDataInfo(tempHashBF);
                atrBussIbnrcalcReportClaimAmountVoHashMapBF.put(item.getAccidentNode(), tempBF);
                //LR
                AtrBussIbnrcalcReportClaimAmountVo tempLR = new AtrBussIbnrcalcReportClaimAmountVo();
                tempLR.setIbnr(item.getLrIbnr());
                tempLR.setOs(item.getOs());
                tempLR.setUnpaidAmount(item.getLrUnpaidAmount());
                tempLR.setAccidentNode(item.getAccidentNode());
                tempLR.setSum(item.getLrReportedAmount());
                Map<String, BigDecimal> tempHashLR = new TreeMap<>();
                tempHashLR.put(item.getFutureNode(), item.getLrReportedAmount());
                tempLR.setFutureDataInfo(tempHashLR);
                atrBussIbnrcalcReportClaimAmountVoHashMapLR.put(item.getAccidentNode(), tempLR);
            } else {
                // CL
                AtrBussIbnrcalcReportClaimAmountVo atrBussIbnrcalcReportClaimAmountVo = atrBussIbnrcalcReportClaimAmountVoHashMapCL.get(item.getAccidentNode());
                atrBussIbnrcalcReportClaimAmountVo.getFutureDataInfo().put(item.getFutureNode(), item.getClReportedAmount());
                atrBussIbnrcalcReportClaimAmountVo.setSum(atrBussIbnrcalcReportClaimAmountVo.getSum().add(item.getClReportedAmount()));
                // BF
                AtrBussIbnrcalcReportClaimAmountVo atrBussIbnrcalcReportClaimAmountVoBF = atrBussIbnrcalcReportClaimAmountVoHashMapBF.get(item.getAccidentNode());
                atrBussIbnrcalcReportClaimAmountVoBF.getFutureDataInfo().put(item.getFutureNode(), item.getBfReportedAmount());
                atrBussIbnrcalcReportClaimAmountVoBF.setSum(atrBussIbnrcalcReportClaimAmountVoBF.getSum().add(item.getBfReportedAmount()));
                // LR
                AtrBussIbnrcalcReportClaimAmountVo atrBussIbnrcalcReportClaimAmountVoLR = atrBussIbnrcalcReportClaimAmountVoHashMapLR.get(item.getAccidentNode());
                atrBussIbnrcalcReportClaimAmountVoLR.getFutureDataInfo().put(item.getFutureNode(), item.getLrReportedAmount());
                atrBussIbnrcalcReportClaimAmountVoLR.setSum(atrBussIbnrcalcReportClaimAmountVoLR.getSum().add(item.getLrReportedAmount()));
            }
        });
        ArrayList<AtrBussIbnrcalcReportClaimAmountVo> atrBussIbnrcalcReportClaimAmountCLVos = new ArrayList<>(atrBussIbnrcalcReportClaimAmountVoHashMapCL.values());
        ArrayList<AtrBussIbnrcalcReportClaimAmountVo> atrBussIbnrcalcReportClaimAmountLRVos = new ArrayList<>(atrBussIbnrcalcReportClaimAmountVoHashMapLR.values());
        ArrayList<AtrBussIbnrcalcReportClaimAmountVo> atrBussIbnrcalcReportClaimAmountBFVos = new ArrayList<>(atrBussIbnrcalcReportClaimAmountVoHashMapBF.values());
        atrBussIbnrcalcStep4DataVoCL.setAtrBussIbnrcalcReportClaimAmountVoList(atrBussIbnrcalcReportClaimAmountCLVos);
        atrBussIbnrcalcStep4DataVoLR.setAtrBussIbnrcalcReportClaimAmountVoList(atrBussIbnrcalcReportClaimAmountLRVos);
        atrBussIbnrcalcStep4DataVoBF.setAtrBussIbnrcalcReportClaimAmountVoList(atrBussIbnrcalcReportClaimAmountBFVos);
        atrBussIbnrcalcReportClaimAmountCLVos.add(getTotalReportClaimAmountVo(atrBussIbnrcalcReportClaimAmountCLVos));
        atrBussIbnrcalcReportClaimAmountLRVos.add(getTotalReportClaimAmountVo(atrBussIbnrcalcReportClaimAmountLRVos));
        atrBussIbnrcalcReportClaimAmountBFVos.add(getTotalReportClaimAmountVo(atrBussIbnrcalcReportClaimAmountBFVos));

        // 获取time_value_discount等信息
        List<AtrBussIbnrcalcTVDInfoVo> atrBussIbnrcalcTVDInfoVosCL = new ArrayList<>();
        List<AtrBussIbnrcalcTVDInfoVo> atrBussIbnrcalcTVDInfoVosLR = new ArrayList<>();
        List<AtrBussIbnrcalcTVDInfoVo> atrBussIbnrcalcTVDInfoVosBF = new ArrayList<>();
        atrBussIbnrcalcStep4DataVoCL.setAtrBussIbnrcalcTVDiscountVos(atrBussIbnrcalcTVDInfoVosCL);
        atrBussIbnrcalcStep4DataVoLR.setAtrBussIbnrcalcTVDiscountVos(atrBussIbnrcalcTVDInfoVosLR);
        atrBussIbnrcalcStep4DataVoBF.setAtrBussIbnrcalcTVDiscountVos(atrBussIbnrcalcTVDInfoVosBF);
        List<AtrBussIbnrcalcTVDiscountVo> atrBussIbnrcalcTVDiscount = atrBussIbnrcalcActionDao.findAtrBussIbnrcalcTVDiscount(actionNo, icpId);
        AtrBussIbnrcalcTVDInfoVo atrBussIbnrcalcTVDInfoVoCLAll = new AtrBussIbnrcalcTVDInfoVo();
        AtrBussIbnrcalcTVDInfoVo atrBussIbnrcalcTVDInfoVoLRAll = new AtrBussIbnrcalcTVDInfoVo();
        AtrBussIbnrcalcTVDInfoVo atrBussIbnrcalcTVDInfoVoBfAll = new AtrBussIbnrcalcTVDInfoVo();

        atrBussIbnrcalcTVDiscount.forEach(item -> {
            // CL
            AtrBussIbnrcalcTVDInfoVo atrBussIbnrcalcTVDInfoVoCL = new AtrBussIbnrcalcTVDInfoVo();
            atrBussIbnrcalcTVDInfoVoCL.setDevNo(item.getDevNo());
            atrBussIbnrcalcTVDInfoVoCL.setSlExpectedReportedRatio(item.getSlExpectedReportedRatio());
            atrBussIbnrcalcTVDInfoVoCL.setTimePeriod(item.getTimePeriod());
            atrBussIbnrcalcTVDInfoVoCL.setInterestRatio(item.getClInterestRatio());
            atrBussIbnrcalcTVDInfoVoCL.setYearMonth(item.getFutureNode());
            atrBussIbnrcalcTVDInfoVoCL.setModify("interestRatio");
            atrBussIbnrcalcTVDInfoVoCL.setDiscountUnpaid(item.getClDisCountedUnpaidAmount());
            atrBussIbnrcalcTVDInfoVosCL.add(atrBussIbnrcalcTVDInfoVoCL);
            // LR
            AtrBussIbnrcalcTVDInfoVo atrBussIbnrcalcTVDInfoVoLR = new AtrBussIbnrcalcTVDInfoVo();
            ClassUtil.copyProperties(atrBussIbnrcalcTVDInfoVoCL, atrBussIbnrcalcTVDInfoVoLR);
            atrBussIbnrcalcTVDInfoVoLR.setInterestRatio(item.getLrInterestRatio());
            atrBussIbnrcalcTVDInfoVoLR.setDiscountUnpaid(item.getLrDisCountedUnpaidAmount());
            atrBussIbnrcalcTVDInfoVosLR.add(atrBussIbnrcalcTVDInfoVoLR);
            // BF
            AtrBussIbnrcalcTVDInfoVo atrBussIbnrcalcTVDInfoVoBF = new AtrBussIbnrcalcTVDInfoVo();
            ClassUtil.copyProperties(atrBussIbnrcalcTVDInfoVoLR, atrBussIbnrcalcTVDInfoVoBF);
            atrBussIbnrcalcTVDInfoVoBF.setInterestRatio(item.getBfInterestRatio());
            atrBussIbnrcalcTVDInfoVoBF.setDiscountUnpaid(item.getBfDisCountedUnpaidAmount());
            atrBussIbnrcalcTVDInfoVosBF.add(atrBussIbnrcalcTVDInfoVoBF);

        });

        AtrBussIbnrcalcTVDiscountInfoVO atrBussIbnrcalcTVDiscountInfoVo = atrBussIbnrcalcActionDao.queryIbnrcalcDiscountInfo(actionNo, icpId);
        if (atrBussIbnrcalcTVDiscountInfoVo != null) {
            // CL
            atrBussIbnrcalcTVDInfoVoCLAll.setDiscountUnpaid(atrBussIbnrcalcTVDiscountInfoVo.getClDisCountedUnpaidAmount());
            atrBussIbnrcalcTVDInfoVoCLAll.setTimeValueDiscount(atrBussIbnrcalcTVDiscountInfoVo.getClTimeValueDiscount());
            // LR
            atrBussIbnrcalcTVDInfoVoLRAll.setDiscountUnpaid(atrBussIbnrcalcTVDiscountInfoVo.getLrDisCountedUnpaidAmount());
            atrBussIbnrcalcTVDInfoVoLRAll.setTimeValueDiscount(atrBussIbnrcalcTVDiscountInfoVo.getLrTimeValueDiscount());
            // BF
            atrBussIbnrcalcTVDInfoVoBfAll.setDiscountUnpaid(atrBussIbnrcalcTVDiscountInfoVo.getBfDisCountedUnpaidAmount());
            atrBussIbnrcalcTVDInfoVoBfAll.setTimeValueDiscount(atrBussIbnrcalcTVDiscountInfoVo.getBfTimeValueDiscount());
        }
        atrBussIbnrcalcTVDInfoVosCL.add(atrBussIbnrcalcTVDInfoVoCLAll);
        atrBussIbnrcalcTVDInfoVosLR.add(atrBussIbnrcalcTVDInfoVoLRAll);
        atrBussIbnrcalcTVDInfoVosBF.add(atrBussIbnrcalcTVDInfoVoBfAll);

        //  查询轨迹表信息
        List<AtrBussIbnrcalcFinalTraceVo> atrBussIbnrcalcFinalTraceVosCL = atrBussIbnrcalcFinalTraceDao.listBussIbnrcalcFinalTraceInfo(icpId, actionNo, "CL");
        List<AtrBussIbnrcalcFinalTraceVo> atrBussIbnrcalcFinalTraceVosLR = atrBussIbnrcalcFinalTraceDao.listBussIbnrcalcFinalTraceInfo(icpId, actionNo, "LR");
        List<AtrBussIbnrcalcFinalTraceVo> atrBussIbnrcalcFinalTraceVosBF = atrBussIbnrcalcFinalTraceDao.listBussIbnrcalcFinalTraceInfo(icpId, actionNo, "BF");
        atrBussIbnrcalcFinalTraceVosCL.addAll(getFinalLastTraceInfo(atrBussIbnrcalcFinalTraceVosCL));
        atrBussIbnrcalcFinalTraceVosLR.addAll(getFinalLastTraceInfo(atrBussIbnrcalcFinalTraceVosLR));
        atrBussIbnrcalcFinalTraceVosBF.addAll(getFinalLastTraceInfo(atrBussIbnrcalcFinalTraceVosBF));
        atrBussIbnrcalcStep4DataVoCL.setAtrBussIbnrcalcFinalTraceVos(atrBussIbnrcalcFinalTraceVosCL);
        atrBussIbnrcalcStep4DataVoLR.setAtrBussIbnrcalcFinalTraceVos(atrBussIbnrcalcFinalTraceVosLR);
        atrBussIbnrcalcStep4DataVoBF.setAtrBussIbnrcalcFinalTraceVos(atrBussIbnrcalcFinalTraceVosBF);
        return result;
    }

    private AtrBussIbnrcalcReportClaimAmountVo getTotalReportClaimAmountVo(ArrayList<AtrBussIbnrcalcReportClaimAmountVo> atrBussIbnrcalcReportClaimAmountVos) {
        AtrBussIbnrcalcReportClaimAmountVo atrBussIbnrcalcReportClaimAmountVo = new AtrBussIbnrcalcReportClaimAmountVo();
        atrBussIbnrcalcReportClaimAmountVo.setAccidentNode("TOTAL");
        atrBussIbnrcalcReportClaimAmountVo.setUnpaidAmount(BigDecimal.ZERO);
        atrBussIbnrcalcReportClaimAmountVo.setOs(BigDecimal.ZERO);
        atrBussIbnrcalcReportClaimAmountVo.setSum(BigDecimal.ZERO);
        atrBussIbnrcalcReportClaimAmountVo.setFutureDataInfo(new TreeMap<String, BigDecimal>());
        atrBussIbnrcalcReportClaimAmountVo.setIbnr(BigDecimal.ZERO);
        for (int i = 0; i < atrBussIbnrcalcReportClaimAmountVos.size(); i++) {
            AtrBussIbnrcalcReportClaimAmountVo item = atrBussIbnrcalcReportClaimAmountVos.get(i);
            atrBussIbnrcalcReportClaimAmountVo.setIbnr(atrBussIbnrcalcReportClaimAmountVo.getIbnr().add(item.getIbnr()));
            atrBussIbnrcalcReportClaimAmountVo.setOs(atrBussIbnrcalcReportClaimAmountVo.getOs().add(item.getOs()));
            atrBussIbnrcalcReportClaimAmountVo.setUnpaidAmount(atrBussIbnrcalcReportClaimAmountVo.getUnpaidAmount().add(item.getUnpaidAmount()));
            atrBussIbnrcalcReportClaimAmountVo.setSum(atrBussIbnrcalcReportClaimAmountVo.getSum().add(item.getSum()));
            Map<String, BigDecimal> futureDataInfo = atrBussIbnrcalcReportClaimAmountVo.getFutureDataInfo();
            for (Map.Entry<String, BigDecimal> decimalEntry : item.getFutureDataInfo().entrySet()) {
                if (!futureDataInfo.containsKey(decimalEntry.getKey())) {
                    futureDataInfo.put(decimalEntry.getKey(), decimalEntry.getValue());
                } else {
                    BigDecimal sourceData = futureDataInfo.get(decimalEntry.getKey());
                    futureDataInfo.put(decimalEntry.getKey(), sourceData.add(decimalEntry.getValue()));
                }
            }
        }
        return atrBussIbnrcalcReportClaimAmountVo;
    }

    // 获取CASE RESERVE的值
    private List<AtrBussIbnrcalcFinalTraceVo> getFinalLastTraceInfo(List<AtrBussIbnrcalcFinalTraceVo> atrBussIbnrcalcFinalTraceVos) {
        ArrayList<AtrBussIbnrcalcFinalTraceVo> result = new ArrayList<>();
        AtrBussIbnrcalcFinalTraceVo atrBussIbnrcalcFinalTraceVoOS = new AtrBussIbnrcalcFinalTraceVo();

        BigDecimal amount = atrBussIbnrcalcFinalTraceVos.get(0).getAmount();
        atrBussIbnrcalcFinalTraceVoOS.setTraceType("Case Reserve");
        atrBussIbnrcalcFinalTraceVoOS.setAmount(amount);
        atrBussIbnrcalcFinalTraceVoOS.setIsModify(false);
        HashMap<String, String> map = new HashMap<>();
        map.put("OS", "Case Reserve");
        map.put("IBNR", "IBNR");
        map.put("ICHE", "Inderect Claim Handling Expense");
        map.put("BSCL", "Best estimate claim liabilities");
        map.put("TVD", "Time value discount");
        map.put("DBCL", "Discounted BE claim liabilities");
        map.put("PAD", "PAD");
        map.put("CL", "Claim Liabilities");
        map.put("IBNR_T", "IBNR TOTAL");
        for (AtrBussIbnrcalcFinalTraceVo atrBussIbnrcalcFinalTraceVo : atrBussIbnrcalcFinalTraceVos) {
            // 缩写映射
            String s = map.get(atrBussIbnrcalcFinalTraceVo.getTraceType());
            atrBussIbnrcalcFinalTraceVo.setTraceType(s);
        }
        result.add(atrBussIbnrcalcFinalTraceVoOS);
        return result;
    }

    private AtrBussIbnrcalcAction findActionPo(String actionNo) {
        AtrBussIbnrcalcAction po = new AtrBussIbnrcalcAction();
        po.setActionNo(actionNo);
        return atrBussIbnrcalcActionDao.findList(po).get(0);
    }

    private interface ReadMethod<T> extends Supplier<T>, Serializable {

    }
}
