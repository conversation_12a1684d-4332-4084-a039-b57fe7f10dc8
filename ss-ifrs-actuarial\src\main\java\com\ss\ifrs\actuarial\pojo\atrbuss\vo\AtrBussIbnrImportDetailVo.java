/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2023-02-06 17:15:22
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ss.library.excel.ExcelValidEnum;
import com.ss.platform.core.annotation.ExcelVaild;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-06 17:15:22<br/>
 * Description: ibne导入明细<br/>
 * Table Name: ATR_BUSS_IBNR_IMPORT_DETAIL<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "ibne导入明细")
public class AtrBussIbnrImportDetailVo implements Serializable {
    /**
     * Database column: ATR_BUSS_IBNR_IMPORT_DETAIL.IBNR_DETAIL_ID
     * Database remarks: ibnr_detail_id|主键
     */
    @ApiModelProperty(value = "ibnr_detail_id|主键", required = true)
    private Long ibnrDetailId;

    /**
     * Database column: ATR_BUSS_IBNR_IMPORT_DETAIL.IBNR_MAIN_ID
     * Database remarks: ibnr_main_id|导入主表主键
     */
    @ApiModelProperty(value = "ibnr_main_id|导入主表主键", required = true)
    private Long ibnrMainId;

    /**
     * Database column: ATR_BUSS_IBNR_IMPORT_DETAIL.PORTFOLIO_NO
     * Database remarks: portfolio_no|合同组合
     */
    @ApiModelProperty(value = "icg_no|合同组合", required = false)
    @ExcelProperty(value = "Group ID")
    private String icgNo;

    /**
     * Database column: ATR_BUSS_IBNR_IMPORT_DETAIL.BUSINESS_MODEL
     * Database remarks: business_model|业务类型
     */
    @ApiModelProperty(value = "business_model|业务类型", required = false)
    @ExcelProperty(value = "业务类型")
    @ExcelVaild(regexType = ExcelValidEnum.NOTNULL)
    private String businessModel;

    /**
     * Database column: ATR_BUSS_IBNR_IMPORT_DETAIL.DOA
     * Database remarks: doa|事故发生月
     */
    @ApiModelProperty(value = "doa|事故发生月", required = false)
    private String doa;

    /**
     * Database column: ATR_BUSS_IBNR_IMPORT_DETAIL.IBNR_AMOUNT
     * Database remarks: ibnr_amount|ibnr金额
     */
    @ApiModelProperty(value = "ibnr_amount|ibnr金额", required = false)
    @ExcelProperty(value = "IBNR金额")
    private BigDecimal ibnrAmount;

    /**
     * Database column: ATR_BUSS_IBNR_IMPORT_DETAIL.OS_AMOUNT
     * Database remarks: os_amount|os金额
     */
    @ApiModelProperty(value = "os_amount|os金额", required = false)
    @ExcelProperty(value = "O/S")
    private BigDecimal osAmount;

    @ApiModelProperty(value = "portfolio_no|合同组合", required = false)
    @ExcelProperty(value = "Portfolio ID")
    private String portfolioNo;


    @ExcelProperty(value = "评估年月")
    private String yearMonth;

    @ExcelProperty(value = "事故季度")
    private String accidentQuarter;

    @ExcelProperty(value = "险类")
    private String riskClassCode;

    @ExcelProperty(value = "核算机构")
    private String centerCode;

    @ExcelProperty(value = "再保业务部")
    private String riDept;

    @ExcelProperty(value = "合约号")
    private String treatyNo;

    @ExcelProperty(value = "合约名称")
    private String treatyName;

    @ApiModelProperty(value = "riIbnrAmount|摊回IBNR金额", required = false)
    @ExcelProperty(value = "摊回IBNR金额")
    private BigDecimal riIbnrAmount;

    @ApiModelProperty(value = "riCaseAmount|摊回CASE金额", required = false)
    @ExcelProperty(value = "摊回CASE金额")
    private BigDecimal riCaseAmount;

    private static final long serialVersionUID = 1L;

    public Long getIbnrDetailId() {
        return ibnrDetailId;
    }

    public void setIbnrDetailId(Long ibnrDetailId) {
        this.ibnrDetailId = ibnrDetailId;
    }

    public Long getIbnrMainId() {
        return ibnrMainId;
    }

    public void setIbnrMainId(Long ibnrMainId) {
        this.ibnrMainId = ibnrMainId;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public String getBusinessModel() {
        return businessModel;
    }

    public void setBusinessModel(String businessModel) {
        this.businessModel = businessModel;
    }

    public String getDoa() {
        return doa;
    }

    public void setDoa(String doa) {
        this.doa = doa;
    }

    public BigDecimal getIbnrAmount() {
        return ibnrAmount;
    }

    public void setIbnrAmount(BigDecimal ibnrAmount) {
        this.ibnrAmount = ibnrAmount;
    }

    public BigDecimal getOsAmount() {
        return osAmount;
    }

    public void setOsAmount(BigDecimal osAmount) {
        this.osAmount = osAmount;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getAccidentQuarter() {
        return accidentQuarter;
    }

    public void setAccidentQuarter(String accidentQuarter) {
        this.accidentQuarter = accidentQuarter;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public String getCenterCode() {
        return centerCode;
    }

    public void setCenterCode(String centerCode) {
        this.centerCode = centerCode;
    }

    public String getRiDept() {
        return riDept;
    }

    public void setRiDept(String riDept) {
        this.riDept = riDept;
    }

    public String getTreatyNo() {
        return treatyNo;
    }

    public void setTreatyNo(String treatyNo) {
        this.treatyNo = treatyNo;
    }

    public String getTreatyName() {
        return treatyName;
    }

    public void setTreatyName(String treatyName) {
        this.treatyName = treatyName;
    }

    public BigDecimal getRiIbnrAmount() {
        return riIbnrAmount;
    }

    public void setRiIbnrAmount(BigDecimal riIbnrAmount) {
        this.riIbnrAmount = riIbnrAmount;
    }

    public BigDecimal getRiCaseAmount() {
        return riCaseAmount;
    }

    public void setRiCaseAmount(BigDecimal riCaseAmount) {
        this.riCaseAmount = riCaseAmount;
    }
}