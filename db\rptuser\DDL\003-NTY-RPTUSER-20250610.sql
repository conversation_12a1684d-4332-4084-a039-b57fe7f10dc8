CREATE TABLE RPTUSER.RPT_BUSS_CHECK_RESULT
(	RESULT_ID int8 NOT NULL,
     ENTITY_ID int8 NOT NULL,
     BOOK_CODE varchar(8) NOT NULL,
     YEAR_MONTH varchar(10) NOT NULL,
     CHECK_RULE_ID int8 NOT NULL,
     RULE_CODE varchar(10),
     CHECK_RESULT varchar(1),
     CREATE_TIME TIMESTAMP (6),
     PRIMARY KEY (RESULT_ID)
);

COMMENT ON COLUMN RPT_BUSS_CHECK_RESULT.CHECK_RULE_ID IS '主键ID';
COMMENT ON COLUMN RPT_BUSS_CHECK_RESULT.ENTITY_ID IS '机构ID';
COMMENT ON COLUMN RPT_BUSS_CHECK_RESULT.BOOK_CODE IS '账套编码';
COMMENT ON COLUMN RPT_BUSS_CHECK_RESULT.YEAR_MONTH IS '业务年月';
COMMENT ON COLUMN RPT_BUSS_CHECK_RESULT.CHECK_RULE_ID IS '规则ID';
COMMENT ON COLUMN RPT_BUSS_CHECK_RESULT.RULE_CODE IS '规则编码';
COMMENT ON COLUMN RPT_BUSS_CHECK_RESULT.CHECK_RESULT IS '检查结果';
COMMENT ON COLUMN RPT_BUSS_CHECK_RESULT.CREATE_TIME IS '检查时间';

CREATE SEQUENCE rptuser.rpt_seq_buss_check_result
    INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1001
CACHE 40;

ALTER SEQUENCE rptuser.rpt_seq_buss_check_result OWNER TO rptuser;