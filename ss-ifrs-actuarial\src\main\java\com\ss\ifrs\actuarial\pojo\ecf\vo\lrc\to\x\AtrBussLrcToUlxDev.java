package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.x;

import com.ss.ifrs.actuarial.util.abp.IgnoreCol;
import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Tab("atr_buss_to_lrc_x_ul_dev")
@Getter
@Setter
public class AtrBussLrcToUlxDev {

    /** 评估期年月 */
    private String yearMonth;

    /** 主表ID */
    private Long mainId;

    /** 发展期序号 */
    private Integer devNo;

    /** 应收保费 */
    private BigDecimal recvPremium;

    /** 已赚保费 */
    private BigDecimal edPremium;

    @IgnoreCol
    private BigDecimal edRate;

}
