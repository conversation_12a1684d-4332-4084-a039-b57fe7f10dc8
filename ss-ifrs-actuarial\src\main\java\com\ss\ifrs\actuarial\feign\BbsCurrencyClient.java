package com.ss.ifrs.actuarial.feign;

import com.ss.platform.pojo.bbs.vo.BbsConfCurrencyVo;
import com.ss.platform.core.conf.FeignAuthConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * @ClassName BbsCurrencyClient
 * @Description 调用BPL币别的接口
 * <AUTHOR>
 * @Date 2022/7/8
 **/
@FeignClient(name = "SS-PLATFORM-COMMON", configuration = { FeignAuthConfig.class })
public interface BbsCurrencyClient {

    /**
     * "根据币别Code查找币别信息"
     *
     * @param currencyCode 币别
     * @return 币别信息
     */
    @RequestMapping(method = RequestMethod.GET, value = "/currency/find_by_code/{currencyCode}")
    BbsConfCurrencyVo findByCode(@PathVariable("currencyCode") String currencyCode);
}
