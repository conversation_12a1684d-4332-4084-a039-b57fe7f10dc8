package com.ss.ifrs.actuarial.api;

import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestBase;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfInterestRateVo;
import com.ss.ifrs.actuarial.service.AtrConfInterestRateService;
import com.ss.ifrs.actuarial.service.AtrExportService;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.annotation.TrackUserBehavioral;
import com.ss.platform.core.annotation.TrackUserBehavioralEnable;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName: AtrConfInterestRateApi
 * @Description: 无风险利率曲线配置Api
 */
@RestController
@RequestMapping("/interest_rate")
@Api(value = "无风险利率曲线配置")
@TrackUserBehavioralEnable
public class AtrConfInterestRateApi extends BaseApi {
    @Autowired
    AtrConfInterestRateService AtrConfInterestRateService;

    @Autowired
    AtrExportService AtrExportService;

    @ApiOperation(value = "查询无风险利率曲线配置列表")
    @TrackUserBehavioral(description = "enquiryInterestRate")
    @RequestMapping(value = "/enquiry", method = RequestMethod.POST)
    public BaseResponse<Map<String, Object>> enquiryInterestRate(@RequestBody AtrConfInterestRateVo AtrConfInterestRateVo, int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrConfInterestRateVo> AtrConfInterestRateVoList = AtrConfInterestRateService.searchPage(AtrConfInterestRateVo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("atrConfInterestRateVoList", AtrConfInterestRateVoList);
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "下载上传模板（国内,国外版本)")
    @TrackUserBehavioral(description = "addImportInterestRate")
    @RequestMapping(value = "/way/downTemplate", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public void downTemplate1(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrConfInterestRateVo AtrConfInterestRateVo) {
        try {
            Long userId = this.loginUserId(request);
            AtrConfInterestRateService.downloadTemplate(request, response, AtrConfInterestRateVo, userId);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
        }
    }


    @ApiOperation(value = "excel导入无风险利率曲线配置信息（国内版本)")
    @TrackUserBehavioral(description = "addImportInterestRate")
    @PermissionRequest(required = false)
    @RequestMapping(value = "/domestic/add_import", method = RequestMethod.POST)
    public BaseResponse<Object> importDataDomestic(HttpServletRequest request, @RequestParam(value = "file", required = false) MultipartFile file,
                                            AtrConfInterestRateVo AtrConfInterestRateVo) {
        try {
            Long userId = this.loginUserId(request);
            AtrConfInterestRateService.importDomesticInterestRate(file, AtrConfInterestRateVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "excel导入无风险利率曲线配置信息(海外版本)")
    @TrackUserBehavioral(description = "addImportInterestRate")
    @RequestMapping(value = "/foreign/add_import", method = RequestMethod.POST)
    public BaseResponse<Object> importDataForeign(HttpServletRequest request, @RequestParam(value = "file", required = false) MultipartFile file,
                                           AtrConfInterestRateVo AtrConfInterestRateVo) {
        try {
            Long userId = this.loginUserId(request);
            AtrConfInterestRateService.importForeignInterestRate(file, AtrConfInterestRateVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "根据id查询无风险利率曲线配置信息")
    @RequestMapping(value = "/find_by_id/{interestRateId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<AtrConfInterestRateVo> findById(@PathVariable("interestRateId") Long interestRateId) {
        AtrConfInterestRateVo AtrConfInterestRateVo = AtrConfInterestRateService.findById(interestRateId);
        return new BaseResponse<AtrConfInterestRateVo>(ResCodeConstant.ResCode.SUCCESS, AtrConfInterestRateVo);
    }

    @ApiOperation(value = "根据id查询无风险利率曲线计算结果信息")
    @RequestMapping(value = "/find_result_by_id/{interestRateId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findResultById(@PathVariable("interestRateId") Long interestRateId) {
        Map<String, Object> map = AtrConfInterestRateService.findBussResultById(interestRateId);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, map);
    }


    @ApiOperation(value = "批量删除无风险利率曲线配置信息")
    @RequestMapping(value = "/delete_by_id/{interestRateId}", method = RequestMethod.GET)
    public BaseResponse<Object> delete(HttpServletRequest request, @PathVariable("interestRateId") Long interestRateId) {
        try {
            Long userId = this.loginUserId(request);
            AtrConfInterestRateService.delete(interestRateId, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "有效状态更新")
    @TrackUserBehavioral(description = "validStatusImportInterestRate")
    @RequestMapping(value = "/valid_status", method = RequestMethod.POST)
    public BaseResponse<Object> validStatus(HttpServletRequest request, @RequestBody AtrConfInterestRateVo dmConfigCheckRuleVo) {
        dmConfigCheckRuleVo.setUpdatorId(this.loginUserId(request));
        Long userId = this.loginUserId(request);
        try {
            AtrConfInterestRateService.updateValid(dmConfigCheckRuleVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, "SUCCESS");
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }


    @ApiOperation(value = "校验无风险利率曲线配置")
    @RequestMapping(value = "/check_pk", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> checkPK(@RequestBody AtrConfInterestRateVo AtrConfInterestRateVo) {
        String result = "0";
        try {
            List<AtrConfInterestRateVo> vos = AtrConfInterestRateService.findList(AtrConfInterestRateVo);
            if(vos.size()>0){
                result = "1";
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, result);
        }
    }

    @ApiOperation(value = "确认版本无风险利率曲线配置")
    @RequestMapping(value = "/confirm", method = RequestMethod.POST)
    public BaseResponse<Object> confirm(@RequestBody AtrConfInterestRateVo AtrConfInterestRateVo, HttpServletRequest request) {
        Long userId = this.loginUserId(request);
        try {
            AtrConfInterestRateService.confirm(AtrConfInterestRateVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "批量审核无风险利率曲线配置")
    @TrackUserBehavioral(description = "batchAuditStatusImportInterestRate")
    @RequestMapping(value = "/batch_audit", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> batchAudit(@RequestBody List<AtrConfInterestRateVo> qtcConfInterestRateVos, HttpServletRequest request, String auditState, String checkedMsg) {
        for (AtrConfInterestRateVo qtcConfInterestRateVo : qtcConfInterestRateVos) {
            qtcConfInterestRateVo.setRemark(checkedMsg);
            qtcConfInterestRateVo.setConfirmId(this.loginUserId(request));
        }
        try {
            AtrConfInterestRateService.auditList(qtcConfInterestRateVos, this.loginUserId(request));
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "导出本无风险利率曲线配置")
    @TrackUserBehavioral(description = "导出本无风险利率曲线配置")
    @RequestMapping(value = "/export_excel", method = RequestMethod.POST)
    public BaseResponse<Map<String, Object>> enquiryInterestRate(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrConfInterestRateVo AtrConfInterestRateVo) {
        Pageable pageParam = new Pageable(0, maxExcelPageSize);
        String language = request.getHeader("ss-Language");
        AtrConfInterestRateVo.setLanguage(language);
        Page<AtrConfInterestRateVo> AtrConfInterestRateVoList = AtrConfInterestRateService.searchPage(AtrConfInterestRateVo, pageParam);
        try {
            Long userId = this.loginUserId(request);
            AtrExportService.exportPage(request, response, AtrConfInterestRateVoList, AtrConfInterestRateVo.class, "df", AtrConfInterestRateVo.getTemplateFileName(), AtrConfInterestRateVo.getTargetRouter(), userId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, null);
    }

    @ApiOperation(value = "导出本无风险利率曲线计算结果")
    @RequestMapping(value = "/download", method = RequestMethod.POST)
    public void downloadInterest(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrConfInterestRateVo AtrConfInterestRateVo) {
        try {
            Long userId = this.loginUserId(request);
            AtrConfInterestRateService.downloadInterest(request, response, AtrConfInterestRateVo, userId);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
        }
    }

    @ApiOperation(value = "查询基础配置数据")
    @GetMapping("/base/list")
    @PermissionRequest(required = false) 
    public BaseResponse<Map<String, Object>> baseList() {
        try {
            List<AtrConfInterestBase> settings = AtrConfInterestRateService.listInterestBase();
            Map<String, Object> result = new HashMap<>();
            result.put("settings", settings);
            return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "更新或保存基础数据")
    @PostMapping("/base/saveOrUpdate")
    @PermissionRequest(required = false)
    public BaseResponse<Map<String, Object>> saveOrUpdate(HttpServletRequest request,@RequestBody List<AtrConfInterestBase> atrConfInterestRateVo) {
        try {
            Long userId = this.loginUserId(request);
            AtrConfInterestRateService.saveOrUpdateInterestBase(atrConfInterestRateVo,userId);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, null);
    }

}
