/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-03-09 10:18:30
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @ClassName: AtrBussLicMainPageReqVo
 * @Description: 计量计算请求参数对象
 * @Author: yinxh.
 * @CreateDate: 2021/3/9 16:14
 * @Version: 1.0
 */
public class AtrBussLicMainPageReqVo implements Serializable {

    private Long licMainId;

    private Long entityId;

    private String yearMonth;

    private String dataSource;

    private String riskClassCode;

    private String currencyCode;

    private String dimension;

    private Short section;

    private Date drawTime;
    
    private String state;

    private String isConfirm;

    
    /**
     * 当前操作人Id
     */
    private Long currentOperId;
    
    /**
     * 当前计算节点
     */
    private String currentCalcNode;
    
    /**
     * 计量计算类型：LIC/CSM
     */
    private String calcType;

    /**
     * Database column: ACC_DATAPENDING.ICG_NO
     * Database remarks: icg_No|合同组编号
     */
    private String icgNo;
    /**
     * 第三步编辑数据
    * */
    private List<AtrBussLicStageVo> atrBussLicStageVoList;

    private static final long serialVersionUID = 1L;

    public Long getLicMainId() {
        return licMainId;
    }

    public void setLicMainId(Long licMainId) {
        this.licMainId = licMainId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }	

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public Short getSection() {
        return section;
    }

    public void setSection(Short section) {
        this.section = section;
    }
    
    public Date getDrawTime() {
        return drawTime;
    }
    
    public void setDrawTime(Date drawTime) {
        this.drawTime = drawTime;
    }
    
    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getIsConfirm() {
        return isConfirm;
    }

    public void setIsConfirm(String isConfirm) {
        this.isConfirm = isConfirm;
    }
    
    public Long getCurrentOperId() {
        return currentOperId;
    }
    
    public void setCurrentOperId(Long currentOperId) {
        this.currentOperId = currentOperId;
    }
    
    public String getCurrentCalcNode() {
        return currentCalcNode;
    }
    
    public void setCurrentCalcNode(String currentCalcNode) {
        this.currentCalcNode = currentCalcNode;
    }
    
    public String getCalcType() {
        return calcType;
    }
    
    public void setCalcType(String calcType) {
        this.calcType = calcType;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public List<AtrBussLicStageVo> getAtrBussLicStageVoList() {
        return atrBussLicStageVoList;
    }

    public void setAtrBussLicStageVoList(List<AtrBussLicStageVo> atrBussLicStageVoList) {
        this.atrBussLicStageVoList = atrBussLicStageVoList;
    }
}