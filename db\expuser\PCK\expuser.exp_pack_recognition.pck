create or replace package exp_pack_recognition is



  PROCEDURE proc_add_recognitionhis(p_entityid   NUMBER,
                               p_bookcode   VARCHAR2,
                               p_yearmonth  VARCHAR2,
                               p_article_id NUMBER,
                               p_userid     NUMBER,
                               p_type       VARCHAR2);

  PROCEDURE proc_identifydept(p_entityid     NUMBER,
                         p_yearmonth    VARCHAR2,
                         p_financial_id NUMBER,
                         p_userid       NUMBER);

  PROCEDURE proc_identifydept_check(p_entityid     NUMBER,
                               p_bookcode     VARCHAR2,
                               p_yearmonth    VARCHAR2,
                               p_financial_id NUMBER,
                               p_userid       NUMBER);

  PROCEDURE proc_identifyfee(p_entityid   NUMBER,
                        p_yearmonth  VARCHAR2,
                        p_article_id NUMBER,
                        p_userid     NUMBER);

  PROCEDURE proc_identifyfee_balance(p_entityid   NUMBER,
                                p_bookcode   VARCHAR2,
                                p_yearmonth  VARCHAR2,
                                p_article_id NUMBER);

  PROCEDURE proc_identifyfee_check(p_entityid     NUMBER,
                              p_bookcode     VARCHAR2,
                              p_yearmonth    VARCHAR2,
                              p_financial_id NUMBER,
                              p_userid       NUMBER);

  PROCEDURE proc_import_checkdatamain(p_userid NUMBER);

  PROCEDURE proc_import_checkimportdata(p_userid NUMBER);

  PROCEDURE proc_import_saveimportdata(p_userid NUMBER);

  PROCEDURE proc_sync_identifydept(p_entityid  NUMBER,
                              p_yearmonth VARCHAR2,
                              p_userid    NUMBER);

end exp_pack_recognition;
/
create or replace package body exp_pack_recognition is


 /***********************************************************************
  NAME :proc_add_recognitionhis
  DESCRIPTION : 指认结果的轨迹保存（'D' 部门指认; 'F': 费用指认）
  DATE :2022-4-28
  AUTHOR :CHENJUNFENG
  ***********************************************************************/
PROCEDURE proc_add_recognitionhis(p_entityid NUMBER, p_bookcode VARCHAR2, p_yearmonth VARCHAR2, p_article_id NUMBER, p_userid NUMBER, p_type VARCHAR2)
 AS
 --p_type 指认类型: 'D' dept; 'F': fee
 BEGIN
    INSERT INTO exp_buss_recognitionrsthis
      (recognitionrsthis_id,
       recognitionrst_id,
       task_id,
       serial_no,
       entity_id,
       book_code,
       year_month,
       account_id,
       benefit_rule_id,
       benefit_state,
       recognition_def_id,
       recognition_state,
       identify_msg,
       create_time,
       creator_id,
       update_time,
       updator_id )
      SELECT exp_seq_rerstdetailhis.nextval,
             recognitionrst_id,
             task_id,
             serial_no,
             entity_id,
             book_code,
             year_month,
             account_id,
             benefit_rule_id,
             benefit_state,
             recognition_def_id,
             recognition_state,
             identify_msg,
             create_time,
             creator_id,
             update_time,
             updator_id
        FROM exp_buss_recognitionrst l
       WHERE l.entity_id = p_entityid
         and l.book_code = p_bookcode
         and l.year_month = p_yearmonth
         and (l.recognitionrst_id = p_article_id or p_article_id is null)
         and ((l.recognition_state != '0' and p_type = 'F') or p_type = 'D')
         and l.benefit_state != '0';  --指认部门为初始状态的数据不需要备份



    INSERT INTO exp_buss_recognitionrstdtlhis
      (REC_RST_DTL_HIS_ID,
       rec_rst_detail_id,
       recognitionrst_id,
       benf_entity_id,
       --serial_no,
       share_amount,
       rule_id,
       rule_factor_id,
       factor_value,
       factor_rate,
       fd_type,
       nf_cf,
       share_fee_type,
       acq_fee_costs,
       mtc_fee_costs,
       loss_fee_costs,
       share_times,
       create_time,
       creator_id,
       update_time,
       updator_id)
      SELECT exp_seq_rerstdetailhis.nextval,
             l.rec_rst_detail_id,
             l.recognitionrst_id,
             l.benf_entity_id,
             --l.serial_no,
             l.share_amount,
             l.rule_id,
             l.rule_factor_id,
             l.factor_value,
             l.factor_rate,
             l.fd_type,
             l.nf_cf,
             l.share_fee_type,
             l.acq_fee_costs,
             l.mtc_fee_costs,
             l.loss_fee_costs,
             l.share_times,
             l.create_time,
             l.creator_id,
             l.update_time,
             l.updator_id
        FROM exp_buss_recognitionrstdetail l
       WHERE l.recognitionrst_id in (
           select recognitionrst_id from exp_buss_recognitionrst t where t.entity_id = p_entityid
            and t.book_code = p_bookcode
            and t.year_month = p_yearmonth
            and (t.recognitionrst_id = p_article_id or p_article_id is null)
            and ((t.recognition_state != '0' and p_type = 'F') or p_type = 'D') --指认费用为初初始状态的不需要备份
          and t.benefit_state != '0'  --指认部门为初始状态的数据不需要备份
          );
       --l.recognitionrst_id = p_article_id;

  EXCEPTION
    WHEN OTHERS THEN
--抛出异常提示信息
    DBMS_OUTPUT.PUT_LINE('**SQLSTATE:' || sqlcode ||' **SQLERRM:'||SQLERRM);
END proc_add_recognitionhis;



/***********************************************************************
  DESCRIPTION : 受益部门指认
  DATE :2022-01-11
  AUTHOR :
***********************************************************************/
PROCEDURE proc_identifydept(p_entityid NUMBER, p_yearmonth VARCHAR2, p_financial_id NUMBER, p_userid NUMBER)
 AS
  -- 声明变量
  v_msg_code  exp_buss_recognitionrst.identify_msg%TYPE;
  v_count        NUMBER(18) := 0; -- 异常指认数据统计
  v_proc_id        NUMBER(18); -- 流程节点ID
  v_parent_proc_id NUMBER(18); -- 流程父节点ID
  v_bookcode       VARCHAR2(32);
  v_task_code      VARCHAR2(32);
  v_assignment_proc_id  NUMBER(18); -- 费用指认节点ID
  v_share_proc_id  NUMBER(18); -- 合同分摊节点ID
	v_start_year_month VARCHAR2(6);
  v_currencyCu varchar(3);
  --cur_record       record;
BEGIN
	v_start_year_month  := substr(p_yearmonth, 1, 4) || '01';
  v_bookcode := bpl_pack_common.func_get_accountSet(p_entityid);
  select currency_code
    into v_currencyCu
    from bpluser.bbs_conf_account_set
   where entity_id = p_entityid
     and book_code = v_bookcode;
  v_msg_code := 'allocation-004';
  if(v_bookcode is null) then
    RETURN;
  end if;

  -- 获取流程节点ID
  SELECT t.proc_id,
         t.parent_proc_id
    INTO v_proc_id,
        v_parent_proc_id
   FROM bpluser.bms_conf_action_procdef t
    WHERE t.proc_code = 'EXP_BENEFIT_DEPART_COSTSHARING';

  --保存轨迹数据
  exp_pack_recognition.proc_add_recognitionhis(p_entityid,v_bookcode, p_yearmonth, p_financial_id, p_userid, 'D');

  -- 清除已保存的分摊数据
  DELETE FROM exp_buss_recognitionrst t
    where t.entity_id = p_entityid
    and t.book_code = v_bookcode
    and t.year_month = p_yearmonth
    and t.benefit_state <> 3
    and (t.recognitionrst_id = p_financial_id or p_financial_id is null)
    and exists(select 1 from exp_dap_financial_data b where t.recognitionrst_id = b.financial_id and dept_type = '0');

  DELETE FROM exp_buss_recognitionrstdetail l WHERE l.recognitionrst_id in (
      select financial_id from exp_dap_financial_data t where t.entity_id = p_entityid
        and t.book_code = v_bookcode
        and t.year_month = p_yearmonth
        and (t.financial_id = p_financial_id or p_financial_id is null)
        and dept_type = '0'
   );

    -- 清除已保存的分摊数据
  update exp_buss_recognitionrst t
  set recognition_def_id = null,  recognition_state='0'
    where t.entity_id = p_entityid
    and t.book_code = v_bookcode
    and t.year_month = p_yearmonth
    and t.benefit_state <> 3
    and (t.recognitionrst_id = p_financial_id or p_financial_id is null)
    and exists(select 1 from exp_dap_financial_data b where t.recognitionrst_id = b.financial_id and dept_type = '1');

  update exp_buss_recognitionrstdetail l set
   nf_cf = null, acq_fee_costs =  null, mtc_fee_costs = null, loss_fee_costs = null
   WHERE l.recognitionrst_id in (
      select financial_id from exp_dap_financial_data t where t.entity_id = p_entityid
          and t.book_code = v_bookcode
        and t.year_month = p_yearmonth
        and (t.financial_id = p_financial_id or p_financial_id is null)
        and dept_type = '1'
   );

  -- 初始化数据处理状态
  v_task_code := bpl_pack_common.func_get_taskcode('EXP','M','DEP');
  INSERT INTO exp_buss_recognitionrst (
      recognitionrst_id,
      task_id,
      serial_no,
      entity_id,
      book_code,
      year_month,
      account_id,
      benefit_state,
      recognition_state,
      CREATOR_ID,-- 创建人
      CREATE_TIME, -- 创建时间
      updator_id,
      update_time
  ) SELECT
      financial_id,
      v_task_code,
      (select COALESCE(max(serial_no),0) +1 FROM exp_buss_recognitionrsthis t1 WHERE t1.recognitionrst_id = t.financial_id),
      entity_id,
      v_bookcode,
      year_month,
      account_id,
      (CASE WHEN amount_cu = 0 THEN 3 ELSE 0 END) AS benefit_state,
      (CASE WHEN amount_cu = 0 THEN 3 ELSE 0 END) AS recognition_state,
      p_userid,-- 创建人
      sysdate,
      p_userid,-- 创建人
      sysdate
  FROM exp_dap_financial_data t
  WHERE t.entity_id = p_entityid
    and t.book_code = v_bookcode
    and t.year_month = p_yearmonth
    and t.dept_type = '0'
    and NOT EXISTS (select financial_id from exp_buss_recognitionrst b WHERE b.recognitionrst_id = t.financial_id)
    and (t.financial_id = p_financial_id or p_financial_id is null);
    
  --分摊前规则校验
  exp_pack_recognition.proc_identifydept_check(p_entityid, v_bookcode, p_yearmonth, p_financial_id, p_userid);


  SELECT COUNT(t.recognitionrst_id) INTO v_count from exp_buss_recognitionrst t
                          where t.entity_id = p_entityid
                            and t.book_code = v_bookcode
                            and t.year_month = p_yearmonth
                            and (t.recognitionrst_id = p_financial_id or p_financial_id is null)
                            and t.benefit_state = '2'
                            and not exists(select 1 from exp_dap_financial_data b where t.recognitionrst_id = b.financial_id and dept_type = '1');
   IF v_count >0 and p_financial_id is not null THEN
     RETURN;
   END IF;

  --对无异常的数据继续分摊流程
  --以下功能不单独捕获异常，任何节点有异常时都退出
    --按部门分摊,结果保存到明细表
  v_msg_code := 'identifyDept_008';
  INSERT INTO exp_buss_recognitionrstdetail
    (rec_rst_detail_id,
     recognitionrst_id,
     benf_entity_id,
     rule_id,
     rule_factor_id,
     factor_value,
     factor_rate,
     share_amount)
    SELECT exp_seq_recognitionrstdetail.nextval,
           t1.recognitionrst_id,
           t3.entity_id benf_entity_id,
           t2.rule_id,
           t4.factor_id rule_factor_id,
           t5.factor_value,
           t4.factor_rate,
           round(b.amount_cu * t4.factor_rate / 100 * (t5.factor_value / (SELECT SUM(c5.factor_value)

                              from exp_conf_benefitruleref c2
                              JOIN exp_conf_benefitrulerefdept c3
                                ON c3.rule_ref_id = c2.rule_ref_id
                              JOIN exp_conf_benefitrulefactref c4
                                ON c4.rule_id = c2.rule_id
                              JOIN exp_conf_benefitfactorvalue c5
                                ON c3.entity_id = c5.entity_id AND c5.factor_id = c4.factor_id
                             WHERE t1.account_id = c2.account_id
                               AND t1.entity_id = c2.entity_id
                               and t4.factor_id = c4.factor_id
                               AND t1.book_code = c2.book_code
                               )), 8) share_amount
    FROM exp_buss_recognitionrst t1
    LEFT JOIN exp_dap_financial_data b
    ON t1.recognitionrst_id = b.financial_id
    JOIN exp_conf_benefitruleref t2
      ON t1.account_id = t2.account_id AND t1.entity_id = t2.entity_id  AND t1.book_code = t2.book_code
    JOIN exp_conf_benefitrulerefdept t3
      ON t3.rule_ref_id = t2.rule_ref_id
    JOIN exp_conf_benefitrulefactref t4
      ON t2.rule_id = t4.rule_id
    JOIN exp_conf_benefitfactorvalue t5
      ON t4.factor_id = t5.factor_id AND t3.entity_id = t5.entity_id
    WHERE t1.entity_id = p_entityid
      and t1.book_code = v_bookcode
      and t1.year_month = p_yearmonth
      and (t1.recognitionrst_id = p_financial_id or p_financial_id is null)
      and t1.benefit_state = '0';


	--更新以保费占比计算部分数据
	UPDATE exp_buss_recognitionrstdetail t
		set	factor_value =nvl((
		select sum(e.amount* (bpluser.bpl_pack_common.func_getexchrate(p_entityid, (last_day(to_date(p_yearmonth||'01', 'yyyy/mm/dd'))) , e.currency_code, v_currencyCu, '1')))
		     from exp_dap_cmunit_data e
					 WHERE  e.dept_id=t.benf_entity_id
					 and e.expenses_type_code = '1'
					 and e.entity_id = p_entityid
					 and e.year_month >= v_start_year_month
					 and e.year_month <= p_yearmonth), 0)
		WHERE exists (select 1 from exp_buss_recognitionrst t1
		where t1.entity_id = p_entityid
			and t1.year_month = p_yearmonth
		  and t1.benefit_state = '0'
			and (t1.recognitionrst_id = p_financial_id or p_financial_id is null)
			and t1.recognitionrst_id=t.recognitionrst_id
		 )
			and t.rule_factor_id in
			(select benefit_factor_id from exp_conf_benefitfactor where valid_is= '1'
			 and audit_state='1' and premium_ratio_flag = '1' and factor_type_enum='R');



  UPDATE exp_buss_recognitionrstdetail t
     set share_amount = round((select b.amount_cu from exp_dap_financial_data b where b.financial_id = t.recognitionrst_id) *
                              t.factor_rate / 100 *
                              (t.factor_value /
                              (SELECT sum(factor_value)
                                  from exp_buss_recognitionrstdetail t1
                                 WHERE t1.rule_factor_id = t.rule_factor_id
                                   and t1.recognitionrst_id =
                                       t.recognitionrst_id)),
                              8)
   WHERE exists (select 1
            from exp_buss_recognitionrst t1
           where t1.entity_id = p_entityid
             and t1.year_month = p_yearmonth
             and t1.benefit_state = '0'
						 and (t1.recognitionrst_id = p_financial_id or p_financial_id is null)
             and t1.recognitionrst_id = t.recognitionrst_id)
     and t.rule_factor_id in
         (select benefit_factor_id
            from exp_conf_benefitfactor
           where valid_is = '1'
             and audit_state = '1'
             and premium_ratio_flag = '1'
             and factor_type_enum = 'R');


  DELETE from exp_buss_recognitionrstdetail T
   WHERE t.recognitionrst_id in
         (select recognitionrst_id
            from exp_buss_recognitionrst t1
           where t1.entity_id = p_entityid
             AND t1.book_code = v_bookcode
             AND t1.year_month = p_yearmonth
             AND (t1.recognitionrst_id = p_financial_id or p_financial_id is null)
             AND t1.benefit_state = '0')
     and share_amount =0
     AND EXISTS (SELECT benefit_factor_id
            FROM exp_conf_benefitfactor cf
           WHERE T.rule_factor_id = cf.benefit_factor_id
             AND valid_is = '1'
             AND audit_state = '1'
             AND premium_ratio_flag = '1'
             AND factor_type_enum = 'R')
     AND NOT EXISTS (SELECT 1
            FROM exp_dap_cmunit_data e
           WHERE e.dept_id = T.benf_entity_id
             AND e.expenses_type_code = '1'
             AND e.entity_id = p_entityid
             AND e.year_month >= v_start_year_month
             AND e.year_month <= p_yearmonth);
		--保费占比计算结束


     v_msg_code := 'identifyDept_010';

		--按部门分摊,差额调整
    UPDATE exp_buss_recognitionrstdetail dl
       SET share_amount = dl.share_amount +
                          (select tf.diff_amount
                             from (SELECT t.rule_factor_id,
                                          t.diff_amount,
                                          (select rdl.rec_rst_detail_id
                                             from exp_buss_recognitionrstdetail rdl
                                            where rdl.recognitionrst_id =
                                                  t.recognitionrst_id
                                              and rdl.share_amount =
                                                  t.max_share_amount
                                              and rownum = 1) rec_rst_detail_id --多条相同时只需要调整一条
                                     FROM (SELECT t1.recognitionrst_id,
                                                  t1.rule_factor_id,
                                                  (b.amount_cu *
                                                  MAX(t1.factor_rate) / 100) -
                                                  SUM(t1.share_amount) diff_amount,
                                                  MAX(t1.share_amount) max_share_amount
                                             FROM exp_buss_recognitionrstdetail t1
                                             LEFT JOIN exp_buss_recognitionrst t2
                                               ON t2.recognitionrst_id =
                                                  t1.recognitionrst_id
                                             LEFT JOIN exp_dap_financial_data b
                                               ON b.financial_id =
                                                  t2.recognitionrst_id
                                            WHERE t2.entity_id = p_entityid
                                              and t2.book_code = v_bookcode
                                              and t2.year_month = p_yearmonth
                                              and (t2.recognitionrst_id =
                                                  p_financial_id or p_financial_id is null)
                                              and t2.benefit_state = '0'
                                            GROUP BY t1.recognitionrst_id,
                                                     t1.rule_factor_id,
                                                     b.amount_cu) t
                                    WHERE t.diff_amount != 0) tf
                            WHERE dl.rec_rst_detail_id = tf.rec_rst_detail_id
                              and dl.rule_factor_id = tf.rule_factor_id)
		  where exists (
                            select 1 from (SELECT t.rule_factor_id,
                                          t.diff_amount,
                                          (select rdl.rec_rst_detail_id
                                             from exp_buss_recognitionrstdetail rdl
                                            where rdl.recognitionrst_id =
                                                  t.recognitionrst_id
                                              and rdl.share_amount =
                                                  t.max_share_amount
                                              and rownum = 1) rec_rst_detail_id --多条相同时只需要调整一条
                                     FROM (SELECT t1.recognitionrst_id,
                                                  t1.rule_factor_id,
                                                  (b.amount_cu *
                                                  MAX(t1.factor_rate) / 100) -
                                                  SUM(t1.share_amount) diff_amount,
                                                  MAX(t1.share_amount) max_share_amount
                                             FROM exp_buss_recognitionrstdetail t1
                                             LEFT JOIN exp_buss_recognitionrst t2
                                               ON t2.recognitionrst_id =
                                                  t1.recognitionrst_id
                                             LEFT JOIN exp_dap_financial_data b
                                               ON b.financial_id =
                                                  t2.recognitionrst_id
                                            WHERE t2.entity_id = p_entityid
                                              and t2.book_code = v_bookcode
                                              and t2.year_month = p_yearmonth
                                              and (t2.recognitionrst_id =
                                                  p_financial_id or p_financial_id is null)
                                              and t2.benefit_state = '0'
                                            GROUP BY t1.recognitionrst_id,
                                                     t1.rule_factor_id,
                                                     b.amount_cu) t
                                    WHERE t.diff_amount != 0) tf
                            WHERE dl.rec_rst_detail_id = tf.rec_rst_detail_id
                              and dl.rule_factor_id = tf.rule_factor_id
			);

    v_msg_code := null;

    -- 更新指认状态
    UPDATE exp_buss_recognitionrst t
       SET benefit_rule_id =
           (select br.rule_ref_id
              from exp_conf_benefitruleref br
             WHERE br.entity_id = t.entity_id
               and br.account_id = t.account_id
               and t.entity_id = p_entityid
               and t.book_code = v_bookcode
               and t.year_month = p_yearmonth
               and (t.recognitionrst_id = p_financial_id or
                   p_financial_id is null)
               and t.benefit_state = '0'),
           benefit_state   = '1', -- 部门已指认
           update_time     = sysdate
     WHERE t.entity_id = p_entityid
       and t.book_code = v_bookcode
       and t.year_month = p_yearmonth
       and (t.recognitionrst_id = p_financial_id or p_financial_id is null)
       and t.benefit_state = '0';


    -- 更新业务日志表
    bpluser.bpl_pack_action_log.proc_save_actionlog(v_task_code,
                                            'EXP',        
                                            p_entityid,
                                            v_bookcode,
                                            p_yearmonth,
                                            v_proc_id,
                                            v_parent_proc_id,
                                            '1', --0-不通过; 1-通过
                                            '2',
                                            p_financial_id,
                                            v_msg_code,
                                            p_userid --创建人员
                                            );

  --校验异常数据写入日志表
  FOR cur_record IN ( select * from exp_buss_recognitionrst t where t.entity_id = p_entityid and t.book_code = v_bookcode and t.year_month = p_yearmonth and (t.recognitionrst_id = p_financial_id or p_financial_id is null) and benefit_state = '2' and rownum=1 ) loop
    -- 更新业务日志表
    bpluser.bpl_pack_action_log.proc_save_actionlog(v_task_code,
                        'EXP',
                        p_entityid,
                        v_bookcode,
                        p_yearmonth,
                        v_proc_id,
                        v_parent_proc_id,
                        '0', --0-不通过; 1-通过
                        '2',
                        cur_record.recognitionrst_id ,
                        cur_record.IDENTIFY_MSG,
                        p_userid --创建人员
                        );
  end loop;

  --获取流程节点ID
  SELECT t.proc_id
    INTO v_assignment_proc_id
    FROM bpluser.bms_conf_action_procdef t
   WHERE t.proc_code = 'EXP_COSTSHARING_ASSIGNMENT';
  SELECT t.proc_id
    INTO v_share_proc_id
    FROM bpluser.bms_conf_action_procdef t
   WHERE t.proc_code = 'EXP_CONTRACT_SHARE';
  --重新部门指认,重置费用指认和分摊流程状态,记录业务日志明细轨迹表
  bpluser.bpl_pack_action_log.proc_add_actionloghis( p_entityid, v_bookcode, p_yearmonth, v_assignment_proc_id );
  bpluser.bpl_pack_action_log.proc_add_actionloghis( p_entityid, v_bookcode, p_yearmonth, v_share_proc_id );
  COMMIT;

  EXCEPTION
    WHEN OTHERS THEN
      DBMS_OUTPUT.PUT_LINE(SQLERRM);

      IF (v_msg_code is null) then
        v_msg_code := 'identifyException';
      END IF;
      -- 更新业务日志表（状态异常）
      bpluser.bpl_pack_action_log.proc_save_actionlog(v_task_code,
                                              'EXP',
                                              p_entityid,
                                              v_bookcode,
                                              p_yearmonth,
                                              v_proc_id,
                                              v_parent_proc_id,
                                              '0', --0-不通过; 1-通过
                                              '2',
                                              p_financial_id,
                                              v_msg_code,
                                              p_userid --创建人员
                                              );
END proc_identifydept;




/***********************************************************************
  DESCRIPTION : 受益部门指认前规则检查
  DATE :2022-01-11
  AUTHOR :
***********************************************************************/
PROCEDURE proc_identifydept_check(p_entityid NUMBER, p_bookcode VARCHAR2, p_yearmonth VARCHAR2, p_financial_id NUMBER, p_userid NUMBER)
 AS
 v_start_year_month VARCHAR2(6);
BEGIN
	 	v_start_year_month  := substr(p_yearmonth, 1, 4) || '01';

    if(p_bookcode is null) then
      -- 待分摊科目及金额是否存在
      UPDATE exp_buss_recognitionrst t
         SET benefit_state = '2', -- 部门指认异常
           identify_msg = 'allocation-004',
           update_time = sysdate
      WHERE t.entity_id = p_entityid
        --and t.book_code = p_bookcode
        and t.year_month = p_yearmonth
        and (t.recognitionrst_id = p_financial_id or p_financial_id is null)
        and t.benefit_state = '0';
    end if;

    -- 待分摊科目及金额是否存在
    UPDATE exp_buss_recognitionrst t
         SET benefit_state = '2', -- 部门指认异常
             identify_msg = 'identifyDept_001',
             update_time = sysdate
       WHERE (t.account_id is null or EXISTS(select financial_id from exp_dap_financial_data b where b.financial_id = t.recognitionrst_id and (b.amount_cu is null or b.amount_cu = 0)))
          and t.entity_id = p_entityid
          and t.book_code = p_bookcode
          and t.year_month = p_yearmonth
          and (t.recognitionrst_id = p_financial_id or p_financial_id is null)
          and t.benefit_state = '0';

    --待分摊科目不存在
    UPDATE exp_buss_recognitionrst t
         SET benefit_state = '2', -- 部门指认异常
             identify_msg = 'identifyDept_002',
             update_time = sysdate
       WHERE not exists(select ac.account_id from exp_conf_account ac where ac.account_id = t.account_id )
          and t.entity_id = p_entityid
          and t.book_code = p_bookcode
          and t.year_month = p_yearmonth
          and (t.recognitionrst_id = p_financial_id or p_financial_id is null)
          and t.benefit_state = '0';

    --待分摊科目状态无效或未审批通过
    UPDATE exp_buss_recognitionrst t
       SET benefit_state = '2', -- 部门指认异常
           identify_msg = 'identifyDept_003',
           update_time = sysdate
     WHERE exists(select ac.account_id from exp_conf_account ac where ac.account_id = t.account_id and ac.valid_is != '1' and  ac.audit_state != '1')
        and t.entity_id = p_entityid
        and t.book_code = p_bookcode
        and t.year_month = p_yearmonth
        and (t.recognitionrst_id = p_financial_id or p_financial_id is null)
        and t.benefit_state = '0';

    --无规则适配
    UPDATE exp_buss_recognitionrst t
       SET benefit_state = '2', -- 部门指认异常
           recognition_state = '0', -- 费用未指认
           identify_msg = 'identifyDept_004',
           update_time = sysdate
     WHERE not exists(select br.account_id from exp_conf_benefitruleref br where br.account_id = t.account_id  and (br.valid_is = '1' and br.audit_state = '1'))
        and t.entity_id = p_entityid
        and t.book_code = p_bookcode
        and t.year_month = p_yearmonth
        and (t.recognitionrst_id = p_financial_id or p_financial_id is null)
        and t.benefit_state = '0';

    --规则适配或状态无效或未审批通过
    UPDATE exp_buss_recognitionrst t
       SET benefit_state = '2', -- 部门指认异常
           recognition_state = '0', -- 费用未指认
           identify_msg = 'identifyDept_005',
           update_time = sysdate
     WHERE exists(select br.account_id from exp_conf_benefitruleref br where br.account_id = t.account_id  and (br.valid_is != '1' or br.audit_state != '1'))
        and t.entity_id = p_entityid
        and t.year_month = p_yearmonth
        and (t.recognitionrst_id = p_financial_id or p_financial_id is null)
        and t.benefit_state = '0';

    -- 科目未关联指认规则
    UPDATE exp_buss_recognitionrst t
       SET benefit_state = '2', -- 部门指认异常
           identify_msg = 'identifyDept_006',
           update_time = sysdate
     WHERE not exists(select br.account_id from exp_conf_benefitruleref br where br.account_id = t.account_id )
        and t.entity_id = p_entityid
        and t.year_month = p_yearmonth
        and (t.recognitionrst_id = p_financial_id or p_financial_id is null)
        and t.benefit_state = '0';

    -- 未指认受益部门
    UPDATE exp_buss_recognitionrst t
       SET benefit_state = '2', -- 部门指认异常
           identify_msg = 'identifyDept_007',
           update_time = sysdate
     WHERE not exists(select br.account_id
                        from exp_conf_benefitruleref br,exp_conf_benefitrulerefdept brd
                       where brd.rule_ref_id = br.rule_ref_id
                         and br.account_id = t.account_id
                         and br.valid_is = '1'
                         and br.audit_state = '1')
        and t.entity_id = p_entityid
        and t.year_month = p_yearmonth
        and (t.recognitionrst_id = p_financial_id or p_financial_id is null)
        and t.benefit_state = '0';

    -- 受益部门无效或非部门
    UPDATE exp_buss_recognitionrst t
       SET benefit_state = '2', -- 部门指认异常
           identify_msg = 'identifyDept_011',
           update_time = sysdate
     WHERE exists(select br.rule_ref_id
                    from exp_conf_benefitruleref br
                    left join exp_conf_benefitrulerefdept brd on brd.rule_ref_id = br.rule_ref_id
                    left join bpluser.bbs_entity dept on dept.entity_id = brd.entity_id --HPLUSER
                   where br.account_id = t.account_id
                     and (dept.valid_is <> '1' OR dept.audit_state <> '1' OR dept.unit_type <> 'D' OR dept.entity_id is null))
        and t.entity_id = p_entityid
        and t.book_code = p_bookcode
        and t.year_month = p_yearmonth
        and (t.recognitionrst_id = p_financial_id or p_financial_id is null)
        and t.benefit_state = '0';


    --分摊因子总数为0
    UPDATE exp_buss_recognitionrst t
       SET benefit_state = '2', -- 部门指认异常
           identify_msg = 'identifyDept_008',
           update_time = sysdate
     WHERE exists(SELECT SUM(t5.factor_value)
                        from exp_conf_benefitruleref t2
                        JOIN exp_conf_benefitrulerefdept t3
                          ON t3.rule_ref_id = t2.rule_ref_id
                        JOIN exp_conf_benefitrulefactref t4
                          ON t4.rule_id = t2.rule_id
                        JOIN exp_conf_benefitfactorvalue t5
                          ON t5.entity_id = t3.entity_id AND t5.factor_id = t4.factor_id
                       WHERE t.account_id = t2.account_id
                         AND t.entity_id = t2.entity_id
                         AND t.account_id = t2.account_id
                         AND t.book_code = t2.book_code
                      HAVING SUM(t5.factor_value) = 0
                       )
        and t.entity_id = p_entityid
        and t.book_code = p_bookcode
        and t.year_month = p_yearmonth
        and (t.recognitionrst_id = p_financial_id or p_financial_id is null)
        and t.benefit_state = '0';

    --分摊因子保费占比，部门保费是否有值
    UPDATE exp_buss_recognitionrst t
      SET benefit_state = '2', -- 部门指认异常
           identify_msg = 'identifyDept_012',
           update_time = sysdate
			where t.entity_id = p_entityid
        and t.book_code = p_bookcode
        and t.year_month = p_yearmonth
        and (t.recognitionrst_id = p_financial_id or p_financial_id is null)
        and t.benefit_state = '0'
			  and account_id in (
					select account_id from (
						select br.account_id,count(e.dept_id) as count
							from exp_conf_benefitruleref br
							left join exp_conf_benefitrulerefdept brd
								on brd.rule_ref_id = br.rule_ref_id
							left join bpluser.bbs_entity dept
								on dept.entity_id = brd.entity_id
							left join exp_conf_benefitrule brl
								on br.RULE_ID = brl.RULE_ID
							left join exp_conf_benefitrulefactref brf
								on brl.RULE_ID = brf.RULE_ID
							left join	(select * from exp_dap_cmunit_data e
											 WHERE e.expenses_type_code = '1'
											 and e.entity_id = p_entityid
											 and e.amount <> 0
											 and e.year_month >= v_start_year_month
											 and e.year_month <= p_yearmonth) e
								  on  e.dept_id = brd.entity_id
						 where (dept.valid_is = '1' and dept.audit_state = '1' and dept.unit_type = 'D' )
							 and brl.valid_is = '1'
							 and brf.FACTOR_ID in
										 (select benefit_factor_id
												from exp_conf_benefitfactor
											 where valid_is = '1'
												 and audit_state = '1'
												 and premium_ratio_flag = '1'
												 and factor_type_enum = 'R')

								group by 	br.account_id
							)t where count=0
			 );

      COMMIT;  
EXCEPTION
  WHEN OTHERS THEN
    ROLLBACK;
    DBMS_OUTPUT.PUT_LINE('部门指认规则验证异常:'|| SQLERRM );

END proc_identifydept_check;




PROCEDURE proc_identifyfee(p_entityid NUMBER, p_yearmonth VARCHAR2, p_article_id NUMBER, p_userid NUMBER)
 AS


    -- 声明变量
    v_msg_code exp_buss_recognitionrst.identify_msg%TYPE;
    v_count        NUMBER(18) := 0; -- 异常指认数据统计
    v_proc_id        NUMBER(18); -- 流程节点ID
    v_parent_proc_id NUMBER(18); -- 流程父节点ID
    v_bookcode       VARCHAR2(32);
    v_task_code      VARCHAR2(32);
    v_share_proc_id  NUMBER(18); -- 合同分摊节点ID
BEGIN
    v_bookcode := bpl_pack_common.func_get_accountSet(p_entityid);
    v_msg_code := 'allocation-004';
    if(v_bookcode is null) then
      RETURN;
    end if;

    --保存轨迹数据
    exp_pack_recognition.proc_add_recognitionhis(p_entityid,v_bookcode, p_yearmonth, p_article_id, p_userid, 'F');

    --初始化指认状态
    UPDATE exp_buss_recognitionrst t
      SET recognition_state = '0', -- 费用未指认
          serial_no = serial_no + 1,
          UPDATE_time = sysdate,
          updator_id = p_userid
    WHERE t.entity_id = p_entityid
      AND t.book_code = v_bookcode
      AND t.year_month = p_yearmonth
      AND (t.recognitionrst_id = p_article_id  OR p_article_id is null)
      AND t.benefit_state = '1';

    --指认前规则校验
    exp_pack_recognition.proc_identifyfee_check(p_entityid,v_bookcode, p_yearmonth, p_article_id, p_userid);

    --查找任务编号
    SELECT t.task_id
      INTO v_task_code
      FROM exp_buss_recognitionrst t
     WHERE t.entity_id = p_entityid
       AND t.year_month = p_yearmonth
       AND (t.recognitionrst_id = p_article_id OR p_article_id is null)
       and rownum = 1;

    IF v_task_code is null THEN
        DBMS_OUTPUT.PUT_LINE('task_code is null');
    END IF;

    --获取流程节点ID
    SELECT t.proc_id,
           t.parent_proc_id
      INTO v_proc_id,
           v_parent_proc_id
      FROM bpluser.bms_conf_action_procdef t
     WHERE t.proc_code = 'EXP_COSTSHARING_ASSIGNMENT';

		SELECT COUNT(t.recognitionrst_id)
			INTO v_count
			FROM exp_buss_recognitionrst t
		 WHERE t.entity_id = p_entityid
			 AND t.book_code = v_bookcode
			 AND t.year_month = p_yearmonth
			 AND (t.recognitionrst_id = p_article_id OR p_article_id is null)
			 AND t.recognition_state = '2';
		IF v_count > 0 AND p_article_id is not null THEN
			RETURN;
		END IF;

    --分摊费用
    UPDATE exp_buss_recognitionrstdetail t1
      SET (nf_cf, acq_fee_costs, mtc_fee_costs, loss_fee_costs) =
          (SELECT ROUND(t1.share_amount * (COALESCE(t3.nfcf_rate, 0) / 100),
                        8),
                  ROUND(t1.share_amount *
                        (COALESCE(t3.acq_fee_rate, 0) / 100),
                        8),
                  ROUND(t1.share_amount *
                        (COALESCE(t3.mtc_fee_rate, 0) / 100),
                        8),
                  ROUND(t1.share_amount *
                        (COALESCE(t3.loss_fee_rate, 0) / 100),
                        8)
             FROM exp_buss_recognitionrst t2
             LEFT JOIN exp_conf_recognitiondef t4
               ON t2.account_id = t4.account_id
             LEFT JOIN exp_conf_recognitiondefdetail t3
               ON t3.recognition_def_id = t4.recognition_def_id
            WHERE t2.recognitionrst_id= t1.recognitionrst_id
						  AND t3.benf_entity_id = t1.benf_entity_id
						)
		where exists (
		  select 1 from exp_buss_recognitionrst t2
		  where t2.entity_id = p_entityid
				AND t2.book_code = v_bookcode
				AND t2.year_month = p_yearmonth
				AND (t2.recognitionrst_id = p_article_id OR p_article_id is null)
				AND t2.recognition_state = '0'
				AND t2.benefit_state = '1'
				AND t1.recognitionrst_id = t2.recognitionrst_id);

    exp_pack_recognition.proc_identifyfee_balance(p_entityid, v_bookcode, p_yearmonth, p_article_id);


    -- 更新指认状态
    UPDATE exp_buss_recognitionrst t
       SET recognition_def_id =
           (select rdf.recognition_def_id
              from exp_conf_recognitiondef rdf
             where rdf.entity_id = t.entity_id
               AND rdf.account_id = t.account_id
               AND t.entity_id = p_entityid
               AND t.book_code = v_bookcode
               AND t.year_month = p_yearmonth
               AND (t.recognitionrst_id = p_article_id OR
                   p_article_id is null)
               AND t.recognition_state = '0'
               AND t.benefit_state = '1'),
           recognition_state  = '1', -- 费用已指认
           UPDATE_time        = sysdate,
           updator_id         = p_userid
		 WHERE t.entity_id = p_entityid
       AND t.book_code = v_bookcode
       AND t.year_month = p_yearmonth
       AND (t.recognitionrst_id = p_article_id  OR p_article_id is null)
       AND t.recognition_state = '0'
       AND t.benefit_state = '1';


    -- 更新业务日志表
   bpluser.bpl_pack_action_log.proc_save_actionlog(v_task_code,
                                            'EXP',
                                            p_entityid,
                                            v_bookcode,
                                            p_yearmonth,
                                            v_proc_id,
                                            v_parent_proc_id,
                                            '1', --0-不通过; 1-通过
                                            '2',
                                            p_article_id,
                                            v_msg_code,
                                            p_userid --创建人员
                                            );

  --更新业务日志表
  FOR cur_record IN ( select * FROM exp_buss_recognitionrst t
                          WHERE t.entity_id = p_entityid
                            AND t.book_code = v_bookcode
                            AND t.year_month = p_yearmonth
                            AND (t.recognitionrst_id = p_article_id  OR p_article_id is null)
                            AND t.recognition_state = '2'
                            and rownum=1 ) loop
    -- 更新业务日志表
    bpluser.bpl_pack_action_log.proc_save_actionlog(v_task_code,
                    'EXP',
                    p_entityid,
                    v_bookcode,
                    p_yearmonth,
                    v_proc_id,
                    v_parent_proc_id,
                    '0', --0-不通过; 1-通过
                    '2',
                    cur_record.recognitionrst_id ,
                    cur_record.IDENTIFY_MSG,
                    p_userid --创建人员
                  );
  END loop;


    SELECT t.proc_id INTO v_share_proc_id
      FROM bpluser.bms_conf_action_procdef t
     WHERE t.proc_code = 'EXP_CONTRACT_SHARE';
  --重新费用指认,分摊流程状态,记录业务日志明细轨迹表
  bpluser.bpl_pack_action_log.proc_add_actionloghis( p_entityid, v_bookcode, p_yearmonth, v_share_proc_id );
  COMMIT;
  EXCEPTION
    WHEN OTHERS THEN
    --ROLLBACK;
      DBMS_OUTPUT.PUT_LINE(':'||SQLERRM);
    -- 费用指认异常
    IF (v_msg_code is null) then
      v_msg_code := 'identifyException';
    END IF;

    -- 更新业务日志表
    bpluser.bpl_pack_action_log.proc_save_actionlog(v_task_code,
                        'EXP',
                        p_entityid,
                        v_bookcode,
                        p_yearmonth,
                        v_proc_id,
                        v_parent_proc_id,
                        '0', --0-不通过; 1-通过
                        '2',
                        p_article_id,
                        v_msg_code,
                        p_userid --创建人员
                        );
END proc_identifyfee;



PROCEDURE proc_identifyfee_balance(p_entityid NUMBER, p_bookcode VARCHAR2, p_yearmonth VARCHAR2, p_article_id NUMBER)
 AS

  /***********************************************************************
  NAME :exp_pack_recognition_proc_identifyfee_balance
  DESCRIPTION : 费用指认尾差处理
  DATE :2022-4-28
  AUTHOR : wyh

  --原方案：尾差统一放入非履约现金流, --尾差统一放入非履约现金流
  --当前方案： 尾差统一放入最大金额的指认费用中
  ***********************************************************************/

BEGIN


  UPDATE exp_buss_recognitionrstdetail t1
   --SET nf_cf = nf_cf + ROUND((t1.share_amount - t1.nf_cf - t1.acq_fee_costs - t1.mtc_fee_costs - loss_fee_costs ),8)
   SET nf_cf =  ROUND((t1.share_amount - t1.acq_fee_costs - t1.mtc_fee_costs - loss_fee_costs ),8)
		where exists (
		  select 1 from exp_buss_recognitionrst t2
		  where t2.entity_id = p_entityid
				AND t2.book_code = p_bookcode
				AND t2.year_month = p_yearmonth
				AND (t2.recognitionrst_id = p_article_id OR p_article_id is null)
				AND t2.recognition_state = '0'
				AND t2.benefit_state = '1'
				AND t1.recognitionrst_id = t2.recognitionrst_id)
	      AND DECODE(t1.nf_cf,0,-99999999999999.99, t1.nf_cf) >= GREATEST(DECODE(t1.acq_fee_costs,0,-99999999999999.99, t1.acq_fee_costs),DECODE(t1.mtc_fee_costs,0,-99999999999999.99, t1.mtc_fee_costs),DECODE(t1.loss_fee_costs,0,-99999999999999.99, t1.loss_fee_costs))
        AND (t1.share_amount - t1.nf_cf - t1.acq_fee_costs - t1.mtc_fee_costs - loss_fee_costs ) <> 0;

   UPDATE exp_buss_recognitionrstdetail t1
       --SET acq_fee_costs =  acq_fee_costs + ROUND((t1.share_amount - t1.nf_cf - t1.acq_fee_costs - t1.mtc_fee_costs - loss_fee_costs ),8)
       SET acq_fee_costs = ROUND((t1.share_amount - t1.nf_cf - t1.mtc_fee_costs - loss_fee_costs ),8)
		where exists (
		  select 1 from exp_buss_recognitionrst t2
		  where t2.entity_id = p_entityid
				AND t2.book_code = p_bookcode
				AND t2.year_month = p_yearmonth
				AND (t2.recognitionrst_id = p_article_id OR p_article_id is null)
				AND t2.recognition_state = '0'
				AND t2.benefit_state = '1'
				AND t1.recognitionrst_id = t2.recognitionrst_id)
        AND DECODE(t1.acq_fee_costs,0,-99999999999999.99, t1.acq_fee_costs) >= GREATEST(DECODE(t1.nf_cf,0,-99999999999999.99, t1.nf_cf),DECODE(t1.mtc_fee_costs,0,-99999999999999.99, t1.mtc_fee_costs),DECODE(t1.loss_fee_costs,0,-99999999999999.99, t1.loss_fee_costs))
        AND (t1.share_amount - t1.nf_cf - t1.acq_fee_costs - t1.mtc_fee_costs - loss_fee_costs ) <> 0;

    UPDATE exp_buss_recognitionrstdetail t1
       --SET mtc_fee_costs =  mtc_fee_costs + ROUND((t1.share_amount - t1.nf_cf - t1.acq_fee_costs - t1.mtc_fee_costs - loss_fee_costs ),8)
       SET mtc_fee_costs =  ROUND((t1.share_amount - t1.nf_cf - t1.acq_fee_costs - loss_fee_costs ),8)
		where exists (
		  select 1 from exp_buss_recognitionrst t2
		  where t2.entity_id = p_entityid
				AND t2.book_code = p_bookcode
				AND t2.year_month = p_yearmonth
				AND (t2.recognitionrst_id = p_article_id OR p_article_id is null)
				AND t2.recognition_state = '0'
				AND t2.benefit_state = '1'
				AND t1.recognitionrst_id = t2.recognitionrst_id)
        AND DECODE(t1.mtc_fee_costs,0,-99999999999999.99, t1.mtc_fee_costs) >= GREATEST(DECODE(t1.nf_cf,0,-99999999999999.99, t1.nf_cf),DECODE(t1.acq_fee_costs,0,-99999999999999.99, t1.acq_fee_costs),DECODE(t1.loss_fee_costs,0,-99999999999999.99, t1.loss_fee_costs))
        AND (t1.share_amount - t1.nf_cf - t1.acq_fee_costs - t1.mtc_fee_costs - loss_fee_costs ) <> 0;


     UPDATE exp_buss_recognitionrstdetail t1
    --SET loss_fee_costs = loss_fee_costs + ROUND((t1.share_amount - t1.nf_cf - t1.acq_fee_costs - t1.mtc_fee_costs - loss_fee_costs ),8)
          SET loss_fee_costs = ROUND((t1.share_amount - t1.nf_cf - t1.acq_fee_costs - t1.mtc_fee_costs ),8)
		where exists (
		  select 1 from exp_buss_recognitionrst t2
		  where t2.entity_id = p_entityid
				AND t2.book_code = p_bookcode
				AND t2.year_month = p_yearmonth
				AND (t2.recognitionrst_id = p_article_id OR p_article_id is null)
				AND t2.recognition_state = '0'
				AND t2.benefit_state = '1'
				AND t1.recognitionrst_id = t2.recognitionrst_id)
        AND DECODE(t1.loss_fee_costs,0,-99999999999999.99, t1.loss_fee_costs) >= GREATEST(DECODE(t1.nf_cf,0,-99999999999999.99, t1.nf_cf),DECODE(t1.acq_fee_costs,0,-99999999999999.99, t1.acq_fee_costs),DECODE(t1.loss_fee_costs,0,-99999999999999.99, t1.loss_fee_costs))
        AND (t1.share_amount - t1.nf_cf - t1.acq_fee_costs - t1.mtc_fee_costs - loss_fee_costs ) <> 0;
      
      COMMIT;      
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
       DBMS_OUTPUT.PUT_LINE('**SQLERRM: '|| SQLERRM ||'【费用指认规则验证异常，请检查】');
END proc_identifyfee_balance;



PROCEDURE proc_identifyfee_check(p_entityid NUMBER, p_bookcode VARCHAR2, p_yearmonth VARCHAR2, p_financial_id NUMBER, p_userid NUMBER)
 AS
BEGIN
    if(p_bookcode is null) then
        UPDATE exp_buss_recognitionrst t
             SET benefit_state = '2', -- 部门指认异常
                 identify_msg = 'allocation-004',
                 update_time = sysdate
           WHERE t.entity_id = p_entityid
              and t.year_month = p_yearmonth
              and (t.recognitionrst_id = p_financial_id or p_financial_id is null)
              and t.benefit_state = '0';
    end if;

    -- 待分摊科目及金额是否存在
    UPDATE exp_buss_recognitionrst t
       SET recognition_state = '2', -- 费用指认异常
           identify_msg = 'identifyFee_001',
           update_time = sysdate
     WHERE (t.account_id is null  or EXISTS(select financial_id from exp_dap_financial_data b where b.financial_id = t.recognitionrst_id and b.amount_cu is null))
        and t.entity_id = p_entityid
        and t.book_code = p_bookcode
        and t.year_month = p_yearmonth
        and (t.recognitionrst_id = p_financial_id or p_financial_id is null)
        and t.recognition_state = '0'
        and t.benefit_state = '1';

    --科目未配置费用指认规则
    UPDATE exp_buss_recognitionrst t
       SET recognition_state = '2', -- 费用指认异常
           identify_msg = 'identifyFee_002',
           update_time = sysdate
    WHERE not exists(select ac.account_id from exp_conf_recognitiondef ac where ac.account_id = t.account_id and ac.fee_config_state = '1')
      and t.entity_id = p_entityid
      and t.book_code = p_bookcode
      and t.year_month = p_yearmonth
      and (t.recognitionrst_id = p_financial_id or p_financial_id is null)
      and t.recognition_state = '0'
      and t.benefit_state = '1';

    --费用指认规则状态无效或未审批通过
    UPDATE exp_buss_recognitionrst t
       SET recognition_state = '2', -- 费用指认异常
           identify_msg = 'identifyFee_003',
           update_time = sysdate
    WHERE not exists(select ac.account_id from exp_conf_recognitiondef ac where ac.account_id = t.account_id and ac.valid_is = '1' and ac.audit_state = '1')
      and t.entity_id = p_entityid
      and t.book_code = p_bookcode
      and t.year_month = p_yearmonth
      and (t.recognitionrst_id = p_financial_id or p_financial_id is null)
      and t.recognition_state = '0'
      and t.benefit_state = '1';


     --费用指认规则未配置费用比例信息
     UPDATE exp_buss_recognitionrst t
        SET recognition_state = '2', -- 费用指认异常
            identify_msg      = 'identifyFee_004',
            update_time       = sysdate
      WHERE not exists
      (SELECT rdf.account_id
               FROM exp_conf_recognitiondef rdf
              inner join exp_conf_recognitiondefdetail rdl
                 on rdl.recognition_def_id = rdf.recognition_def_id
              where rdf.entity_id = t.entity_id
                and rdf.account_id = t.account_id
                and rdf.valid_is = '1'
                and rdf.audit_state = '1')
        and t.entity_id = p_entityid
        and t.book_code = p_bookcode
        and t.year_month = p_yearmonth
        and (t.recognitionrst_id = p_financial_id or p_financial_id is null)
        and t.recognition_state = '0'
        and t.benefit_state = '1';


     --非17 配置部门指认数据，默认指认部门是否配置费用指认
     UPDATE exp_buss_recognitionrst t
         SET recognition_state = '2', -- 费用指认异常
             identify_msg      = 'identifyFee_005',
             update_time       = sysdate
       where t.entity_id = p_entityid
         and t.book_code = p_bookcode
         and t.year_month = p_yearmonth
         and (t.recognitionrst_id = p_financial_id or p_financial_id is null)
         and t.recognition_state = '0'
         and t.benefit_state = '1'
         and EXISTS
         (SELECT B.benf_entity_id
                  FROM EXP_BUSS_RECOGNITIONRSTDETAIL B
                 WHERE T.RECOGNITIONRST_ID = B.RECOGNITIONRST_ID
                   AND NOT EXISTS
                 (SELECT benf_entity_id
                          FROM exp_conf_recognitiondef AC
                          LEFT JOIN exp_conf_recognitiondefdetail T4
                            ON AC.RECOGNITION_DEF_ID = T4.RECOGNITION_DEF_ID
                         WHERE AC.account_id = T.account_id
                           AND AC.FEE_CONFIG_STATE = '1'
                           AND T4.benf_entity_id = B.benf_entity_id)
                );

     COMMIT; 
EXCEPTION
  WHEN OTHERS THEN
    ROLLBACK;
    DBMS_OUTPUT.PUT_LINE('**SQLERRM: '|| SQLERRM ||'【费用指认规则验证异常，请检查】');
END proc_identifyfee_check;



/***********************************************************************
  DESCRIPTION : 费用指认配置-导入数据的校验
  DATE :2022-01-11
  AUTHOR :
***********************************************************************/
PROCEDURE proc_import_checkdatamain(p_userid NUMBER)
 AS
BEGIN
  -- Routine body goes here...
  -- 校验数据
         exp_pack_recognition.proc_import_checkimportdata(p_userid);

         -- 保存通过校验数据
         exp_pack_recognition.proc_import_saveimportdata(p_userid);

      EXCEPTION WHEN OTHERS THEN
      --rollback;
      NULL;
END proc_import_checkdatamain;



PROCEDURE proc_import_checkimportdata(p_userid NUMBER)
 AS

  -- 声明变量
    v_err_msg         VARCHAR2(256); -- 校验信息
    v_flag            VARCHAR2(1);  --  行数据是否校验通过
    v_group_flag      VARCHAR2(1) := '1';  --  组数据是否校验通过
    v_num             NUMBER(8);
  --rec_group         record;
  --rec_row           record;
 BEGIN
  -- 截取编码
  UPDATE EXP_TEMP_RECOGNITION_IMPORT T
     SET ENTITY_CODE      = TRIM(SUBSTR(ENTITY_NAME,
                                        0,
                                        INSTR(ENTITY_NAME, '--', 1) - 1)),
         BOOK_CODE       =
         nvl((SELECT CODE_CODE
            FROM BPLUSER.BPL_V_CONF_CODE
           WHERE (CODE_C_NAME = BOOK_NAME OR
                 CODE_E_NAME = BOOK_NAME OR CODE_L_NAME = BOOK_NAME)
             AND UPPER_CODE_ID =
                 (SELECT CODE_ID
                    FROM BPLUSER.BPL_V_CONF_CODE
                   WHERE CODE_CODE_IDX = 'BookType')), BOOK_NAME),
         ACCOUNT_CODE        = TRIM(SUBSTR(ACCOUNT_NAME,
                                        0,
                                        INSTR(ACCOUNT_NAME, '--', 1) - 1)),
         BENF_ENTITY_CODE = TRIM(SUBSTR(BENF_ENTITY_NAME,
                                        0,
                                        INSTR(BENF_ENTITY_NAME, '--', 1) - 1))
   WHERE T.DATA_KEY = P_USERID;

    --  1，根据 ENTITY_CODE, book_code, ACCOUNT_CODE 进行分组处理
    FOR rec_group IN (select t.ENTITY_CODE, t.book_code, t.ACCOUNT_CODE
                         from EXP_TEMP_RECOGNITION_IMPORT t
                       where t.data_key = p_userId
                         group by t.ENTITY_CODE, t.book_code, t.ACCOUNT_CODE
                      ) LOOP

        -- B. 逐条记录校验是否存在错误数据
        FOR rec_row IN (
                       select t.ENTITY_CODE, t.book_code, t.ACCOUNT_CODE, t.BENF_ENTITY_CODE,
                           t.nfcf_rate, t.acq_fee_rate, t.mtc_fee_rate, t.loss_fee_rate
                         from EXP_TEMP_RECOGNITION_IMPORT t
                       where t.data_key = p_userId
                            and t.ENTITY_CODE = rec_group.ENTITY_CODE
                            and t.book_code = rec_group.book_code
                            and t.ACCOUNT_CODE = rec_group.ACCOUNT_CODE
                      ) LOOP
              v_err_msg := '';
              v_flag := '1';

              -- 1, 总比例 等于 100
              if COALESCE(cast(rec_row.nfcf_rate as NUMBER),0)  + COALESCE(cast(rec_row.acq_fee_rate as NUMBER),0) + COALESCE(cast(rec_row.mtc_fee_rate as NUMBER),0) + COALESCE(cast(rec_row.loss_fee_rate as NUMBER),0) != 100
                then
                 v_err_msg := v_err_msg || 'FIC_001|';
                 v_flag := '0';
                 v_group_flag := '0';
              end if;

              -- 2, 核算单位
              select count(*) into v_num from bpluser.bbs_entity t where t.ENTITY_CODE = rec_row.ENTITY_CODE ;
              if v_num = 0 then
                 v_err_msg := v_err_msg || 'FIC_002|';
                 v_flag := '0';
                 v_group_flag := '0';
              end if;

              -- 3，账套
              select count(*) into v_num from bpl_v_conf_code t where t.code_code_idx = 'BookType/'||rec_row.book_code ;
              if v_num = 0 then
                  v_err_msg := v_err_msg || 'FIC_003|';
                  v_flag := '0';
                  v_group_flag := '0';
              end if;

              -- 4, 科目
              select count(*) into v_num from exp_conf_account t left join bpluser.bbs_account item      --bpluser
             on t.account_id = item.account_id    where item.ACCOUNT_CODE = rec_row.ACCOUNT_CODE ;
              if v_num = 0 then
                  v_err_msg := v_err_msg || 'FIC_004|';
                  v_flag := '0';
                  v_group_flag := '0';
              end if;

              -- 5, 受益部门
              select count(*) into v_num from bpluser.bbs_entity t  where t.ENTITY_CODE = rec_row.BENF_ENTITY_CODE ;
              if v_num = 0 then
                  v_err_msg := v_err_msg || 'FIC_005|';
                  v_flag := '0';
                  v_group_flag := '0';
              end if;

              -- 6, 根据核算单位，账套，科目编码查找是否存在该配置数据
              select count(*) into v_num
                    from exp_conf_account t1,bpluser.bbs_account item, bpluser.bbs_entity t2, exp_conf_recognitiondef t3 --bpluser
                 where 1=1
                     and t1.entity_id = t2.entity_id
                     and t1.account_id = item.account_id
                     and t3.account_id = t1.account_id
                     and t2.ENTITY_CODE = rec_row.ENTITY_CODE
                     and item.ACCOUNT_CODE = rec_row.ACCOUNT_CODE
                     and t1.book_code = rec_row.book_code;

              if v_num = 0 then
                  v_err_msg := v_err_msg || 'FIC_006|';
                  v_flag := '0';
                  v_group_flag := '0';
              end if;

               --  根据 ENTITY_CODE, book_code, ACCOUNT_CODE, BENF_ENTITY_CODE 是否存在重复
              select count(*) into v_num from EXP_TEMP_RECOGNITION_IMPORT t
                 where t.data_key = p_userId
                     and t.ENTITY_CODE = rec_row.ENTITY_CODE
                     and t.book_code = rec_row.book_code
                     and t.ACCOUNT_CODE = rec_row.ACCOUNT_CODE
                     and t.BENF_ENTITY_CODE = rec_row.BENF_ENTITY_CODE
                 group by t.ENTITY_CODE, t.book_code, t.ACCOUNT_CODE, t.BENF_ENTITY_CODE;
              if v_num > 1 then
                v_err_msg := v_err_msg || 'FIC_007|';
                v_flag := '0';
                v_group_flag := '0';
              end if;

              -- 根据item_code, BENF_ENTITY_CODE查询收益部门是否已参与规则适配的部门指认
              SELECT count(*) into v_num
                FROM exp_conf_benefitruleref a
                join bpluser.bbs_account b --bpluser
                  on a.account_id = b.account_id
                left join exp_conf_benefitrulerefdept c
                join bpluser.bbs_entity d
                  on c.entity_id = d.entity_id on a.rule_ref_id = c.rule_ref_id
               WHERE b.ACCOUNT_CODE = rec_row.ACCOUNT_CODE
                 and d.ENTITY_CODE = rec_row.BENF_ENTITY_CODE;
               if v_num = 0 then
                v_err_msg := v_err_msg || 'FIC_008|';
                v_flag := '0';
                v_group_flag := '0';
              end if;

             -- 保存校验结果信息

             update EXP_TEMP_RECOGNITION_IMPORT t
                    set err_msg = v_err_msg ,
                        flag = v_flag
               where t.data_key = p_userId
                    and t.ENTITY_CODE = rec_row.ENTITY_CODE
                    and t.book_code = rec_row.book_code
                    and t.ACCOUNT_CODE = rec_row.ACCOUNT_CODE
                    and t.BENF_ENTITY_CODE = rec_row.BENF_ENTITY_CODE;
        end LOOP;

        -- 更新每组数据校验通过状态
        update EXP_TEMP_RECOGNITION_IMPORT t set flag = v_group_flag
            where t.data_key = p_userId
             and t.ENTITY_CODE = rec_group.ENTITY_CODE
             and t.book_code = rec_group.book_code
             and t.ACCOUNT_CODE = rec_group.ACCOUNT_CODE;

     end LOOP;

  EXCEPTION WHEN OTHERS THEN
   DBMS_OUTPUT.PUT_LINE('**SQLSTATE:'|| sqlcode ||';**SQLERRM:'||SQLERRM);
   null;

END proc_import_checkimportdata;







PROCEDURE proc_import_saveimportdata(p_userid NUMBER)
 AS

  -- 声明变量
    v_def_id                  NUMBER(11);   -- 费用指认配置主表ID
    v_benf_center_id          NUMBER(11);   -- 受益部门ID
    v_update_time             DATE ;
	v_serial_no         	  NUMBER(11);	--版本号
  --rec_group         record;
  --rec_row           record;
 BEGIN
    v_update_time := sysdate ;  -- 修改时间
    --  1，根据 ENTITY_CODE, book_code, ACCOUNT_CODE 进行分组处理
    FOR rec_group IN (
                       select t.ENTITY_CODE, t.book_code, t.ACCOUNT_CODE
                         from EXP_TEMP_RECOGNITION_IMPORT t
                       where t.data_key = p_userId
                          and t.flag = '1'  --  校验通过数据
                         group by t.ENTITY_CODE, t.book_code, t.ACCOUNT_CODE
                      ) LOOP

        -- 1, 根据 ENTITY_CODE, book_code, ACCOUNT_CODE 查找主表ID
        select t3.recognition_def_id into v_def_id
              from exp_conf_account t1,bpluser.bbs_account item, bpluser.bbs_entity t2, exp_conf_recognitiondef t3 --bpluser
           where 1=1
               and t1.entity_id = t2.entity_id
               and t3.account_id = t1.account_id
               and t1.account_id = item.account_id
               and t2.ENTITY_CODE = rec_group.ENTITY_CODE
               and item.ACCOUNT_CODE = rec_group.ACCOUNT_CODE
               and t1.book_code = rec_group.book_code;

        -- 2，删除明显表数据
        delete from exp_conf_recognitiondefdetail where recognition_def_id = v_def_id;

        -- 3，保存明显表数据
        FOR rec_row IN (
                       select t.ENTITY_CODE, t.book_code, t.ACCOUNT_CODE, t.BENF_ENTITY_CODE,
                           t.nfcf_rate, t.acq_fee_rate, t.mtc_fee_rate, t.loss_fee_rate
                         from EXP_TEMP_RECOGNITION_IMPORT t
                       where t.data_key = p_userId
                            and t.ENTITY_CODE = rec_group.ENTITY_CODE
                            and t.book_code = rec_group.book_code
                            and t.ACCOUNT_CODE = rec_group.ACCOUNT_CODE
                      ) LOOP

           --  查找受益部门ID
           select b.entity_id into v_benf_center_id
               from bpluser.bbs_entity b --bpluser
             where b.ENTITY_CODE = rec_row.BENF_ENTITY_CODE;

		  -- 查找版本号
					 select decode(e.serial_no, null, '1', e.serial_no+1) into v_serial_no
						from exp_conf_recognitiondef e
						where e.recognition_def_id = v_def_id;

           -- 保存数据
           insert into exp_conf_recognitiondefdetail(recognition_def_det_id,recognition_def_id,benf_entity_id,nfcf_rate,acq_fee_rate,
 mtc_fee_rate,loss_fee_rate, update_time, updator_id,valid_is,serial_no)
           select  exp_SEQ_RECOGNITIONDEFDETAIL.nextval, v_def_id, v_benf_center_id,COALESCE(cast(rec_row.nfcf_rate as NUMBER),0), COALESCE(cast(rec_row.acq_fee_rate as NUMBER),0), COALESCE(cast(rec_row.mtc_fee_rate as NUMBER),0), COALESCE(cast(rec_row.loss_fee_rate as NUMBER),0), v_update_time, p_userId, '1', v_serial_no from dual;

					 -- 保存子表轨迹数据
           insert into exp_conf_recognitiondefdtlhis(recognition_def_det_his_id,recognition_def_det_id,recognition_def_id,benf_entity_id,nfcf_rate,acq_fee_rate,
 mtc_fee_rate,loss_fee_rate, update_time, updator_id,valid_is,serial_no,oper_id,oper_time,oper_type)
           select  EXP_SEQ_RECOGNITIONDEFDETHIS.nextval,r.recognition_def_det_id,r.recognition_def_id,r.benf_entity_id,r.nfcf_rate,r.acq_fee_rate,
 r.mtc_fee_rate,r.loss_fee_rate, r.update_time, r.updator_id,r.valid_is,r.serial_no,p_userid,v_update_time,'1' from exp_conf_recognitiondefdetail r where r.recognition_def_id = v_def_id and r.benf_entity_id = v_benf_center_id;

        end LOOP;

					-- 保存主表修改状态
					 update exp_conf_recognitiondef t
           set fee_config_state = '1',  -- 已配置
               audit_state = '0',
               valid_is = '1',
               updator_id = p_userId ,  -- 修改人
               update_time = v_update_time,    -- 修改时间
				serial_no = v_serial_no
           where t.recognition_def_id = v_def_id;

					 -- 保存主表轨迹数据
           insert into exp_conf_recognitiondefhis(recognition_def_his_id,recognition_def_id,entity_id,BOOK_CODE,account_id,
 FEE_CONFIG_STATE, update_time, updator_id,valid_is,serial_no,oper_id,oper_time,oper_type)
           select  EXP_SEQ_RECOGNITIONDEFHIS.nextval, f.recognition_def_id, f.entity_id, f.BOOK_CODE, f.account_id,
 f.FEE_CONFIG_STATE, f.update_time, f.updator_id, f.valid_is, f.serial_no, p_userid, v_update_time, '2' from exp_conf_recognitiondef f where f.recognition_def_id = v_def_id;

			end LOOP;


  EXCEPTION WHEN OTHERS THEN
  DBMS_OUTPUT.PUT_LINE( SQLERRM );
      NULL;
END proc_import_saveimportdata;




PROCEDURE proc_sync_identifydept(p_entityid NUMBER, p_yearmonth VARCHAR2, p_userid NUMBER)
 AS
/***********************************************************************
  DESCRIPTION : 待摊数据部门专项 -默认受益部门指认
  DATE :2022-01-11
  AUTHOR :
***********************************************************************/

    -- 声明变量
  v_msg_code  exp_buss_recognitionrst.identify_msg%TYPE;
  v_count        NUMBER(18) := 0; -- 异常指认数据统计
  v_proc_id        NUMBER(18); -- 流程节点ID
  v_parent_proc_id NUMBER(18); -- 流程父节点ID
  v_bookcode       VARCHAR2(32);
  v_task_code      VARCHAR2(32);
  v_assignment_proc_id  NUMBER(18); -- 费用指认节点ID
  v_share_proc_id  NUMBER(18); -- 合同分摊节点ID
  --cur_record       record;
BEGIN
  v_bookcode := bpl_pack_common.func_get_accountSet(p_entityid);
  v_msg_code := 'allocation-004';
  if(v_bookcode is null) then
    RETURN;
  end if;

  -- 获取流程节点ID
  SELECT t.proc_id,
         t.parent_proc_id
    INTO v_proc_id,
         v_parent_proc_id
    FROM bpluser.bms_conf_action_procdef t
   WHERE t.proc_code = 'EXP_BENEFIT_DEPART_COSTSHARING';
  --保存轨迹数据
  exp_pack_recognition.proc_add_recognitionhis(p_entityid,v_bookcode, p_yearmonth, null, p_userid, 'D');


  -- 清除已保存的分摊数据
  DELETE FROM exp_buss_recognitionrst t
   where EXISTS (select 1 from exp_dap_financial_data a where
    t.recognitionrst_id = a.financial_id
    and a.entity_id = p_entityid
    and a.year_month = p_yearmonth
    and a.data_source='2'
    and a.dept_type='1' );

    DELETE FROM exp_buss_recognitionrstdetail l
     WHERE EXISTS (select 1 from exp_dap_financial_data a where
      l.recognitionrst_id = a.financial_id
      and a.entity_id = p_entityid
      and a.year_month = p_yearmonth
      and a.data_source='2'
      and a.dept_type='1' );

  -- 初始化数据处理状态
  v_task_code := bpl_pack_common.func_GET_TASKCODE('EXP','M','DEP'); --bpluser
  INSERT INTO exp_buss_recognitionrst (
      recognitionrst_id,
      task_id,
      serial_no,
      entity_id,
      book_code,
      year_month,
      account_id,
      CREATOR_ID,
      CREATE_TIME,
      benefit_state, -- 部门指认异常
      recognition_state, -- 费用未指认
      identify_msg,
      updator_id,
      update_time
  ) SELECT
      financial_id,
      v_task_code,
      1 as serial_no,
      entity_id,
      v_bookcode,
      year_month,
      account_id,
      p_userid,-- 创建人
      sysdate,
      0,
      0, -- 费用未指认
      null,
      p_userid,
      sysdate
  FROM exp_dap_financial_data t
  WHERE t.entity_id = p_entityid
    and t.book_code = v_bookcode
    and t.year_month = p_yearmonth
    and t.data_source='2'
    and t.dept_type='1'
    and exists(select 1 from exp_dap_financial_data_detail b where t.financial_id = b.financial_id and amount_cu <> 0)
  union all
  SELECT
      financial_id,
      v_task_code,
      1 as serial_no,
      entity_id,
      v_bookcode,
      year_month,
      account_id,
      p_userid,-- 创建人
      sysdate,
      3,-- 金额为0无需指认
      3,
      null,
      p_userid,
      sysdate
  FROM exp_dap_financial_data t
  WHERE t.entity_id = p_entityid
    and t.book_code = v_bookcode
    and t.year_month = p_yearmonth
    and t.amount_cu = 0
    and not exists(select 1 from exp_dap_financial_data_detail b where t.financial_id = b.financial_id and amount_cu <> 0 and t.dept_type='1')  ;


    -- 待分摊科目及金额是否存在
    UPDATE exp_buss_recognitionrst t
         SET benefit_state = '2', -- 部门指认异常
             identify_msg = 'identifyDept_001',
             update_time = sysdate
       WHERE (t.account_id is null or EXISTS (select 1 from exp_dap_financial_data a where
          t.recognitionrst_id = a.financial_id
          and a.data_source='2'
          and a.dept_type='1'
          and amount_cu is null))
          and t.entity_id = p_entityid
          and t.book_code = v_bookcode
          and t.year_month = p_yearmonth
          and t.benefit_state = '0';

    --待分摊科目不存在
    UPDATE exp_buss_recognitionrst t
         SET benefit_state = '2', -- 部门指认异常
             identify_msg = 'identifyDept_002',
             update_time = sysdate
       WHERE not exists(select ac.account_id from exp_conf_account ac where ac.account_id = t.account_id )
          and t.entity_id = p_entityid
          and t.book_code = v_bookcode
          and t.year_month = p_yearmonth
          and EXISTS (select 1 from exp_dap_financial_data a
           where t.recognitionrst_id = a.financial_id
            and a.data_source='2'
            and a.dept_type='1' )
          and t.benefit_state = '0';

    --受益部门不属于机构下
    UPDATE exp_buss_recognitionrst t
       SET benefit_state = '2', -- 部门指认异常
           identify_msg = 'identifyDept_009',
           update_time = sysdate
      WHERE t.entity_id = p_entityid
        and t.book_code = v_bookcode
        and t.year_month = p_yearmonth
        and EXISTS (select 1 from exp_dap_financial_data a ,exp_dap_financial_data_detail d
         where a.financial_id = d.financial_id
          and t.recognitionrst_id = a.financial_id
          and exp_pack_common.func_get_depttype(a.entity_id, null, d.dept_id) ='0'
          and a.data_source='2'
          and a.dept_type='1' )
        and t.benefit_state = '0';

  SELECT COUNT(t.recognitionrst_id) INTO v_count
     from exp_buss_recognitionrst t
    where t.entity_id = p_entityid
      and t.book_code = v_bookcode
      and t.year_month = p_yearmonth
      and EXISTS (select 1 from exp_dap_financial_data a
           where t.recognitionrst_id = a.financial_id
            and a.data_source='2'
            and a.dept_type='1' )
      and t.recognition_state = '2' ;

   IF v_count >0  THEN
      RETURN;
   END IF;

  --对无异常的数据继续分摊流程
  --以下功能不单独捕获异常，任何节点有异常时都退出
    --按部门分摊,结果保存到明细表
  v_msg_code := 'identifyDept_008';
  INSERT INTO exp_buss_recognitionrstdetail
    (rec_rst_detail_id,
     recognitionrst_id,
     benf_entity_id,
     rule_id,
     rule_factor_id,
     factor_value,
     factor_rate,
     share_amount)
    SELECT exp_seq_recognitionrstdetail.nextval,
           t1.recognitionrst_id,
           d.dept_id benf_entity_id,
           null,
           null,
           null,
           null as factor_rate,
           d.amount_cu as share_amount
    FROM exp_buss_recognitionrst t1
    LEFT JOIN exp_dap_financial_data b
    ON t1.recognitionrst_id = b.financial_id
    LEFT JOIN exp_dap_financial_data_detail d
      ON b.financial_id = d.financial_id
    WHERE t1.entity_id = p_entityid
      and t1.book_code = v_bookcode
      and t1.year_month = p_yearmonth
      and t1.benefit_state = '0'
      and b.data_source='2'
      and b.dept_type='1'
      and d.amount_cu <> 0 ;

   v_msg_code := null;



   -- 更新指认状态
       UPDATE exp_buss_recognitionrst t
          SET t.benefit_state = '1', t.update_time = sysdate
        where t.recognitionrst_id = any (select br.financial_id
                 from exp_dap_financial_data br
                where br.data_source = '2'
                  and br.dept_type = '1')
          and t.entity_id = p_entityid
          and t.book_code = v_bookcode
          and t.year_month = p_yearmonth
          and t.benefit_state = '0';


    -- 更新业务日志表
     bpluser.bpl_pack_action_log.proc_save_actionlog(v_task_code,
                                            'EXP',
                                            p_entityid,
                                            v_bookcode,
                                            p_yearmonth,
                                            v_proc_id,
                                            v_parent_proc_id,
                                            '1', --0-不通过; 1-通过
                                            '2',
                                            null,
                                            v_msg_code,
                                            p_userid --创建人员
                                            );


  --校验异常数据写入日志表
  FOR cur_record IN ( select t.recognitionrst_id from exp_buss_recognitionrst t left join exp_dap_financial_data a
  on t.recognitionrst_id = a.financial_id  where t.entity_id = p_entityid and t.book_code = v_bookcode and t.year_month = p_yearmonth and a.data_source='2' and a.dept_type='1' and benefit_state = '2' and rownum=1 ) loop
    -- 更新业务日志表
   bpluser.bpl_pack_action_log.proc_save_actionlog(v_task_code,
                        'EXP',
                        p_entityid,
                        v_bookcode,
                        p_yearmonth,
                        v_proc_id,
                        v_parent_proc_id,
                        '0', --0-不通过; 1-通过
                        '2',
                        cur_record.recognitionrst_id ,
                        v_msg_code,
                        p_userid --创建人员
                        );
  end loop;

  EXCEPTION
    WHEN OTHERS THEN
      IF (v_msg_code is null) then
        v_msg_code := 'identifyException';
      END IF;
      -- 更新业务日志表（状态异常）
      bpluser.bpl_pack_action_log.proc_save_actionlog(v_task_code,
                                              'EXP',
                                              p_entityid,
                                              v_bookcode,
                                              p_yearmonth,
                                              v_proc_id,
                                              v_parent_proc_id,
                                              '0', --0-不通过; 1-通过
                                              '2',
                                              null,
                                              v_msg_code,
                                              p_userid --创建人员
                                              );
      DBMS_OUTPUT.PUT_LINE('**SQLSTATE:'|| sqlcode ||';**SQLERRM:'||SQLERRM||'生成指认数据异常，请检查');

END proc_sync_identifydept;
end exp_pack_recognition;
/
