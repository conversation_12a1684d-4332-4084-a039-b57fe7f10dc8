package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.dao.conf.AtrConfBussPeriodDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfBussPeriodDetailDao;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLicCashFlowVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLrcActionVo;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfBussPeriod;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfBussPeriodDetail;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodBackVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodDetailVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.ifrs.actuarial.pojo.domain.vo.buss.BussPeriodReqVo;
import com.ss.ifrs.actuarial.service.AtrBussLicCashFlowService;
import com.ss.ifrs.actuarial.service.AtrBussLrcCashFlowService;
import com.ss.ifrs.actuarial.service.AtrConfBussPeriodDetailService;
import com.ss.ifrs.actuarial.service.AtrConfBussPeriodService;
import com.ss.ifrs.actuarial.service.puhua.AtrBussIbnrSecAllocService;
import com.ss.ifrs.actuarial.util.Dates;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.CacheUtil;
import com.ss.library.utils.ClassUtil;
import com.ss.library.utils.StringUtil;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.core.constant.SystemConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class AtrConfBussPeriodServiceImpl implements AtrConfBussPeriodService {

    final Logger LOG = LoggerFactory.getLogger(getClass());

    @Autowired
    private AtrConfBussPeriodDao atrConfBussPeriodDao;

    @Autowired
    AtrConfBussPeriodDetailDao atrConfBussPeriodDetailDao;
    
    @Autowired
    AtrConfBussPeriodDetailService atrConfBussPeriodDetailService;

    @Autowired
    private AtrBussLicCashFlowService atrBussLicCashFlowService;

    @Autowired
    private AtrBussLrcCashFlowService atrBussLrcCashFlowService;

    @Autowired
    private AtrBussIbnrSecAllocService atrBussIbnrSecAllocService;

    @Override
    public Page<AtrConfBussPeriodVo> findForDataTables(AtrConfBussPeriodVo vo, Pageable pageParam) {
        return atrConfBussPeriodDao.fuzzySearchPage(vo, pageParam);
    }

    @Override
    public Boolean verifyBussPeriodIsProcessing(Long entityId, String yearMonth, String bizCode) {
        if (StringUtil.isEmpty(entityId) || StringUtil.isEmpty(yearMonth)) {
            return false;
        }
        AtrConfBussPeriodVo po = new AtrConfBussPeriodVo();
        po.setEntityId(entityId);
        po.setYearMonth(yearMonth);
        po.setBizCode(bizCode);// ExpenseConstant.EXP_BUSS_OUTPUT_CONTRACT_ALLOCATION
        AtrConfBussPeriodVo bussPeriodVo = findCurrentPeriod(po);
        return (ObjectUtils.isNotEmpty(bussPeriodVo) && CommonConstant.BussPeriod.PeriodStatus.PROCESSING.equals(bussPeriodVo.getPeriodState()));
    }

    @Override
    public Boolean verifyBussPeriodIsValid(Long entityId, String yearMonth) {
        AtrConfBussPeriodVo vo = new AtrConfBussPeriodVo();
        vo.setEntityId(entityId);
        vo.setYearMonth(yearMonth);
        vo.setValidIs("1");
        AtrConfBussPeriodVo atrConfBussPeriodVo = atrConfBussPeriodDao.findByVo(vo);
        return ObjectUtils.isNotEmpty(atrConfBussPeriodVo);
    }

    @Override
    public AtrConfBussPeriodVo findCurrentPeriod(AtrConfBussPeriodVo vo) {
        String currentPeriodKey = vo.getEntityId() + vo.getBizCode();
        AtrConfBussPeriodVo bussPeriodVo = (AtrConfBussPeriodVo) CacheUtil.get(CommonConstant.BussConf.BUSS_PERIOD_KEY + SystemConstant.AtrIdentity.APP_CODE, currentPeriodKey);
        if (ObjectUtils.isEmpty(bussPeriodVo)) {
            vo.setYearMonth(null);// 确保没有设置业务期间
            vo.setPeriodState(CommonConstant.BussPeriod.PeriodStatus.PROCESSING);
            bussPeriodVo = atrConfBussPeriodDao.findByVo(vo);
            if (ObjectUtils.isEmpty(bussPeriodVo)) {
                vo.setPeriodState(CommonConstant.BussPeriod.PeriodStatus.COMPLETED);
                bussPeriodVo = atrConfBussPeriodDao.findByVo(vo);
                if (ObjectUtils.isEmpty(bussPeriodVo)) {
                    return new AtrConfBussPeriodVo();
                }
            }
            CacheUtil.put(CommonConstant.BussConf.BUSS_PERIOD_KEY + SystemConstant.AtrIdentity.APP_CODE, currentPeriodKey, bussPeriodVo);
        }
        return bussPeriodVo;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public String updateValidIsNext(AtrConfBussPeriodVo atrConfBussPeriodVo, Long userId)
            throws UnexpectedRollbackException {
        try {
            Long bussPeriodId = atrConfBussPeriodVo.getBussPeriodId();
            AtrConfBussPeriod DmConfBussPeriodNew = atrConfBussPeriodDao.findById(bussPeriodId);
            if (DmConfBussPeriodNew != null) {
                // 更新数据
                DmConfBussPeriodNew.setUpdatorId(userId);
                DmConfBussPeriodNew.setUpdateTime(new Date());
                atrConfBussPeriodDao.updateNext(DmConfBussPeriodNew);
            }

            return ResCodeConstant.ResCode.SUCCESS;

        } catch (UnexpectedRollbackException e) {
            LOG.error(e.getLocalizedMessage(), e);
            throw e;
        }
    }

    @Override
    public AtrConfBussPeriodVo findBefore(AtrConfBussPeriodVo atrConfBussPeriodVo) {
        AtrConfBussPeriod atrConfBussPeriodPo = ClassUtil.convert(atrConfBussPeriodVo, AtrConfBussPeriod.class);
        List<AtrConfBussPeriod> periodList = atrConfBussPeriodDao.findBefore(atrConfBussPeriodPo);
        if (CollectionUtils.isNotEmpty(periodList)) {
            AtrConfBussPeriod atrConfBussPeriod = periodList.get(0);

            return ClassUtil.convert(atrConfBussPeriod, AtrConfBussPeriodVo.class);
        } else {
            return null;
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public String updateValidIs(AtrConfBussPeriodVo atrConfBussPeriodVo, Long userId)
            throws UnexpectedRollbackException {
        try {

            Long periodId = atrConfBussPeriodVo.getBussPeriodId();

            AtrConfBussPeriod DmConfBussPeriodNew = atrConfBussPeriodDao.findById(periodId);
            if (DmConfBussPeriodNew != null) {
                // 更新有效状态
                DmConfBussPeriodNew.setValidIs(atrConfBussPeriodVo.getValidIs());

                atrConfBussPeriodDao.updateById(DmConfBussPeriodNew);

            }

            return ResCodeConstant.ResCode.SUCCESS;

        } catch (UnexpectedRollbackException e) {
            LOG.error(e.getLocalizedMessage(), e);
            throw e;
        }
    }

    @Override
    public List<AtrConfBussPeriodVo> findOwActionLog(AtrConfBussPeriodVo vo) {
        if (ObjectUtils.isEmpty(vo) && ObjectUtils.isEmpty(vo.getEntityId())) {
            return Collections.emptyList();
        }
        List<AtrConfBussPeriodVo> atrConfBussPeriodVoList = new ArrayList<>();
        AtrConfBussPeriodVo atrConfBussPeriodVo = getOwBussPeriod(vo);

        if (ObjectUtils.isEmpty(atrConfBussPeriodVo.getYearMonth())) {
            return Collections.emptyList();
        }

        atrConfBussPeriodVoList.add(atrConfBussPeriodVo);
        return atrConfBussPeriodVoList;
    }

    @Override
    public AtrConfBussPeriodVo getOwBussPeriod(AtrConfBussPeriodVo vo) {
        AtrConfBussPeriodVo atrConfBussPeriodVo = this.findCurrentPeriod(vo);
        if (ObjectUtils.isEmpty(atrConfBussPeriodVo.getYearMonth()) || CommonConstant.BussPeriod.PeriodStatus.COMPLETED.equals(atrConfBussPeriodVo.getPeriodState())) {
            vo.setPeriodState(CommonConstant.BussPeriod.PeriodStatus.PREPARED);
            AtrConfBussPeriodVo bussPeriodVo = atrConfBussPeriodDao.findByVo(vo);
            if (ObjectUtils.isNotEmpty(bussPeriodVo)) {
                return bussPeriodVo;
            }
            vo.setPeriodState(CommonConstant.BussPeriod.PeriodStatus.PREPARING);
            bussPeriodVo = atrConfBussPeriodDao.findByVo(vo);
            if (ObjectUtils.isNotEmpty(bussPeriodVo)) {
                return bussPeriodVo;
            }
            return atrConfBussPeriodVo;
        }
        return atrConfBussPeriodVo;
    }
    
    /**
     * 同步当前业务期间的执行状态，并清除业务期间缓存
     */
    @Override
    public void syncPeriodStatus(Long entityId, String periodState, String bizCode) {
        syncPeriodStatus(entityId, periodState);

        // 业务区期间缓存清理：
        if(ObjectUtils.isNotEmpty(bizCode)){
            // 0-输出数据/1-输入数据：清理对应业务模型的业务期间缓存
            deleteFormRedisByHashKey(entityId, bizCode);
        }else {
            // 清除所有业务期间缓存
            deleteFormRedisByHashKey();
        }

    }

    /**
     * 清除所有业务期间缓存
     */
    public void deleteFormRedisByHashKey() {
        // 清除会业务期间下任务缓存
        CacheUtil.remove(CommonConstant.BussConf.BUSS_PERIOD_KEY + SystemConstant.AtrIdentity.APP_CODE);
    }

    /**
     * 清除对应业务模型的业务期间缓存
     * @param entityId
     * @param bizCode
     */
    public void deleteFormRedisByHashKey(Long entityId, String bizCode) {
        // 清除缓存
        String currentPeriodKey = entityId + bizCode;
        CacheUtil.remove(CommonConstant.BussConf.BUSS_PERIOD_KEY + SystemConstant.AtrIdentity.APP_CODE, currentPeriodKey);
    }
    
    /**
     * @description 获取自动任务执行时的业务期间(依据功能任务数据流向决定)
     * @throws
     * <AUTHOR>
     * @date 2022/9/26 20:24
     */
    @Override
    public List<AtrConfBussPeriodVo> findBussPeriodVosForTaskJob(Long entityId, String periodState) {
        
        return atrConfBussPeriodDao.findBussPeriodVoForJob(entityId, periodState);
        
    }
    
    /**
     * @description 同步业务期间状态，跳转下一个业务期间
     * @param  entityId,periodState
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2023/3/17 11:30
     */
    @Override
    public void syncPeriodStatusByEntity(Long entityId, String periodState) {
        /**
         * 切换业务期间逻辑：
         *  场景一：dmConfBussPeriodVo.periodState=0时，切换准备中业务期间状态
         *  场景二：dmConfBussPeriodVo.periodState=2时，切换处理中业务期间状态
         *  场景三：dmConfBussPeriodVo.periodState=null时，包含以上两种情况
         */
        
        if(ObjectUtils.isNotEmpty(periodState)){
            this.syncPeriodStatusForState(entityId, periodState);
        }else {
            // 0-准备中的业务期间，针对输入方向的业务数据处理
            this.syncPeriodStatusForState(entityId, CommonConstant.BussPeriod.PeriodStatus.PREPARING);
            
            // 1-处理中的业务期间，针对输出方向的业务数据处理
            this.syncPeriodStatusForState(entityId, CommonConstant.BussPeriod.PeriodStatus.PROCESSING);
        }
        
        // 清除所有业务期间缓存
        deleteFormRedisByHashKey();
        
    }
    
    /**
     * @description 切换业务期间状态
     * @param  entityId, periodState
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2023/3/17 12:04
     */
    private void syncPeriodStatusForState(Long entityId, String periodState) {
        
        AtrConfBussPeriod confBussPeriod = new AtrConfBussPeriod();
        confBussPeriod.setEntityId(entityId);
        confBussPeriod.setValidIs(CommonConstant.ValidStatus.VALID);
        confBussPeriod.setPeriodState(periodState);
        
        List<AtrConfBussPeriod> confBussPeriodList = atrConfBussPeriodDao.findList(confBussPeriod);
    
        AtrConfBussPeriodDetailVo confBussPeriodDetailVo = null;
        for (AtrConfBussPeriod bussPeriod : confBussPeriodList) {
            confBussPeriodDetailVo = new AtrConfBussPeriodDetailVo();
            confBussPeriodDetailVo.setEntityId(bussPeriod.getEntityId());
            confBussPeriodDetailVo.setBussPeriodId(bussPeriod.getBussPeriodId());
            confBussPeriodDetailVo.setYearMonth(bussPeriod.getYearMonth());
            
            if ("0".equals(bussPeriod.getPeriodState())) {
                // 检查是否存在所有模型已准备，校验1-输入模型
                String inputResultCount = atrConfBussPeriodDetailService.checkInputReadyState(confBussPeriodDetailVo);
                
                // 1-已全部准备
                if ("1".equals(inputResultCount)) {
                    // 同步业务期间 准备中=>已准备 状态
                    this.syncPeriodStatus(entityId, CommonConstant.BussPeriod.PeriodStatus.PREPARED, null);
                }
            } else if ("2".equals(bussPeriod.getPeriodState())) {
                // 检查是否存在所有模型已准备，校验0-输出模型
                String outResultCount = atrConfBussPeriodDetailService.checkOutputReadyState(confBussPeriodDetailVo);
                
                // 1-已全部准备
                if ("1".equals(outResultCount)) {
                    // 同步业务期间 处理中=>已完成 状态
                    this.syncPeriodStatus(entityId, CommonConstant.BussPeriod.PeriodStatus.COMPLETED, null);
                }
            }
            
        }
        
    }


    @Override
    public List<AtrConfBussPeriodVo> findValidPeriodList() {
        AtrConfBussPeriod atrConfBussPeriod = new AtrConfBussPeriod();
        atrConfBussPeriod.setValidIs(CommonConstant.ValidStatus.VALID);
        atrConfBussPeriod.setPeriodState(CommonConstant.BussPeriod.PeriodStatus.PROCESSING);
        List<AtrConfBussPeriod> atrConfBussPeriods = atrConfBussPeriodDao.findList(atrConfBussPeriod);
        return ClassUtil.convert(atrConfBussPeriods, AtrConfBussPeriodVo.class);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor= UnexpectedRollbackException.class)
    public String executionPeriod(Long entityId, String yearMonth, String bizCode) {
             // 修改当前 "处理中" 的业务数据明细状态为 "已准备"
            AtrConfBussPeriodDetailVo detailVo = new AtrConfBussPeriodDetailVo();
            detailVo.setEntityId(entityId);
            detailVo.setYearMonth(yearMonth);
            detailVo.setPeriodState(CommonConstant.BussPeriod.PeriodStatus.PROCESSING);
            detailVo.setReadyState(CommonConstant.BussPeriod.PeriodStatus.PREPARED);
            detailVo.setExecResult("success");
            this.updatePeriodDetail(detailVo);

            //  修改当前处理中的业务年月 为 已完成， 同时修改下个已准备的业务年月为 处理中
            this.syncPeriodStatus(entityId, CommonConstant.BussPeriod.PeriodStatus.COMPLETED, bizCode);
            return "期间" + yearMonth + "存在业务线未计量或者未确认结果";
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void updatePeriodDetail(AtrConfBussPeriodDetailVo atrConfBussPeriodVo) {
        if (StringUtil.isEmpty(atrConfBussPeriodVo.getBussPeriodId())
                || StringUtil.isEmpty(atrConfBussPeriodVo.getYearMonth())) {
            AtrConfBussPeriodVo bussPeriodVo = new AtrConfBussPeriodVo();
            bussPeriodVo.setEntityId(atrConfBussPeriodVo.getEntityId());
            bussPeriodVo.setPeriodState(atrConfBussPeriodVo.getPeriodState());
            bussPeriodVo = atrConfBussPeriodDao.findByVo(bussPeriodVo);
            atrConfBussPeriodVo.setYearMonth(bussPeriodVo.getYearMonth());
            atrConfBussPeriodVo.setBussPeriodId(bussPeriodVo.getBussPeriodId());
        }
        atrConfBussPeriodVo.setDirection("0");
        atrConfBussPeriodDetailDao.updateByDetail(atrConfBussPeriodVo);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public void updatePeriod(AtrConfBussPeriodVo atrConfBussPeriodVo) {
        AtrConfBussPeriodVo bussPeriodVo = new AtrConfBussPeriodVo();
        bussPeriodVo.setEntityId(atrConfBussPeriodVo.getEntityId());
        bussPeriodVo.setYearMonth(atrConfBussPeriodVo.getYearMonth());
        bussPeriodVo = atrConfBussPeriodDao.findByVo(bussPeriodVo);

        if (ObjectUtils.isNotEmpty(bussPeriodVo)) {
            AtrConfBussPeriod bussPeriod = new AtrConfBussPeriod();
            bussPeriod.setBussPeriodId(bussPeriodVo.getBussPeriodId());
            bussPeriod.setPeriodState(atrConfBussPeriodVo.getPeriodState());
            atrConfBussPeriodDao.updateById(bussPeriod);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public Boolean reOpenBussPeriod(AtrConfBussPeriodBackVo confBussPeriodVo) {
        AtrConfBussPeriodVo bussPeriodVo = new AtrConfBussPeriodVo();
        bussPeriodVo.setEntityId(confBussPeriodVo.getEntityId());
        bussPeriodVo.setYearMonth(confBussPeriodVo.getYearMonth());
        bussPeriodVo = atrConfBussPeriodDao.findByVo(bussPeriodVo);
        if (ObjectUtils.isEmpty(bussPeriodVo) || CommonConstant.BussPeriod.PeriodStatus.PROCESSING.equals(bussPeriodVo.getPeriodState())){
            AtrBussLicCashFlowVo bussLicCashFlowVo = new AtrBussLicCashFlowVo();
            bussLicCashFlowVo.setEntityId(confBussPeriodVo.getEntityId());
            bussLicCashFlowVo.setYearMonth(confBussPeriodVo.getYearMonth());
            bussLicCashFlowVo.setBusinessSourceCode(confBussPeriodVo.getBusinessSourceCode());
            atrBussLicCashFlowService.revoke(bussLicCashFlowVo, confBussPeriodVo.getUserId());
            AtrBussLrcActionVo bussLrcCashFlowVo = new AtrBussLrcActionVo();
            bussLrcCashFlowVo.setEntityId(confBussPeriodVo.getEntityId());
            bussLrcCashFlowVo.setYearMonth(confBussPeriodVo.getYearMonth());
            bussLicCashFlowVo.setBusinessSourceCode(confBussPeriodVo.getBusinessSourceCode());
            atrBussLrcCashFlowService.revoke(bussLrcCashFlowVo, confBussPeriodVo.getUserId());

            AtrConfBussPeriodDetailVo detailVo = new AtrConfBussPeriodDetailVo();
            detailVo.setEntityId(confBussPeriodVo.getEntityId());
            detailVo.setYearMonth(confBussPeriodVo.getYearMonth());
            detailVo.setPeriodState(CommonConstant.BussPeriod.PeriodStatus.PROCESSING);
            detailVo.setReadyState(CommonConstant.BussPeriod.DetailBizStatus.PREPARING);
            detailVo.setExecResult("");
            this.updatePeriodDetail(detailVo);
            this.updatePeriod(bussPeriodVo);
        }
        return null;
    }

    @Override
    public Boolean checkYearMonthExecutable(BussPeriodReqVo bussPeriodReqVo) {
        bussPeriodReqVo.setReadyState(CommonConstant.ExecutionState.IN_PROGRESS);
        bussPeriodReqVo.setValidIs(CommonConstant.ValidStatus.VALID);
        AtrConfBussPeriod atrConfBussPeriod = atrConfBussPeriodDao.checkYearMonthExecutable(bussPeriodReqVo);
        return ObjectUtils.isNotEmpty(atrConfBussPeriod);
    }

    @Override
    public void exampleModifyTheCurrentTaskStatus(AtrConfBussPeriodVo atrConfBussPeriodVo) {
        if (ObjectUtils.isNotEmpty(atrConfBussPeriodVo) &&
                ObjectUtils.isNotEmpty(atrConfBussPeriodVo.getYearMonth()) && ObjectUtils.isNotEmpty(atrConfBussPeriodVo.getEntityId()) &&
        ObjectUtils.isNotEmpty(atrConfBussPeriodVo.getBizCode())) {

            AtrConfBussPeriodDetail record = new AtrConfBussPeriodDetail();
            record.setReadyState(CommonConstant.BussPeriod.PeriodStatus.COMPLETED);
            record.setExecResult("success");
            record.setUpdateTime(new Date());

            AtrConfBussPeriodDetail atrConfBussPeriodDetail = ClassUtil.convert(atrConfBussPeriodVo, AtrConfBussPeriodDetail.class);
            atrConfBussPeriodDetailDao.update(record, atrConfBussPeriodDetail);
        }
    }

    @Override
    public void syncPeriodStatus(Long entityId, String state) {
        Date now = new Date();

        if ("0".equals(state)) {
            AtrConfBussPeriod vo = new AtrConfBussPeriod();
            vo.setEntityId(entityId);
            vo.setPeriodState("0");
            List<AtrConfBussPeriod> vos = atrConfBussPeriodDao.findList(vo);

            if (vos.isEmpty()) {
                // 增加一个新的业务期间
                String yearMonth = atrConfBussPeriodDao.getMaxYearMonth(entityId);
                if (yearMonth == null) {
                    yearMonth = Dates.toChar(now, "yyyyMM");
                } else {
                    yearMonth = Dates.getYearMonth(yearMonth, 1);
                }

                vo = new AtrConfBussPeriod();
                vo.setEntityId(entityId);
                vo.setYearMonth(yearMonth);
                vo.setPeriodState("0");
                vo.setValidIs("1");
                vo.setCreatorId(1L);
                vo.setCreateTime(now);
                atrConfBussPeriodDao.save(vo);

                atrConfBussPeriodDao.readyAllDetails(entityId, yearMonth, now);
            }
        } else if ("1".equals(state)) {
            AtrConfBussPeriod vo = new AtrConfBussPeriod();
            vo.setEntityId(entityId);
            vo.setPeriodState("0");
            List<AtrConfBussPeriod> vos = atrConfBussPeriodDao.findList(vo, Sort.by("year_month"));

            if (!vos.isEmpty()) {
                vo = vos.get(0);
                if (checkPeriodDetailAllReady(state, vo.getBussPeriodId())) {
                    // 修改当前业务期间状态为己准备
                    vo.setPeriodState("1");
                    vo.setUpdatorId(1L);
                    vo.setUpdateTime(now);
                    atrConfBussPeriodDao.updateById(vo);

                    // 增加下个准备中业务期间
                    syncPeriodStatus(vo.getEntityId(), "0");
                }
            }

            // 确保有下个处理中业务期间
            syncPeriodStatus(vo.getEntityId(), "2");
        } else if ("2".equals(state)) {
            AtrConfBussPeriod vo = new AtrConfBussPeriod();
            vo.setEntityId(entityId);
            vo.setPeriodState("2");
            List<AtrConfBussPeriod> vos = atrConfBussPeriodDao.findList(vo);

            // 确保无在处理中的业务期间
            if (vos.isEmpty()) {
                vo = new AtrConfBussPeriod();
                vo.setEntityId(entityId);
                vo.setPeriodState("1");
                vos = atrConfBussPeriodDao.findList(vo, Sort.by("year_month"));

                if (!vos.isEmpty()) {
                    vo = vos.get(0);
                    if (checkPeriodDetailAllReady(state, vo.getBussPeriodId())) {
                        // 修改当前业务期间状态为处理中
                        vo.setPeriodState("2");
                        vo.setUpdatorId(1L);
                        vo.setUpdateTime(now);
                        atrConfBussPeriodDao.updateById(vo);
                    }
                }
            }
        } else if ("3".equals(state)) {
            AtrConfBussPeriod vo = new AtrConfBussPeriod();
            vo.setEntityId(entityId);
            vo.setPeriodState("2");
            List<AtrConfBussPeriod> vos = atrConfBussPeriodDao.findList(vo, Sort.by("year_month"));

            if (!vos.isEmpty()) {
                vo = vos.get(0);
                if (checkPeriodDetailAllReady(state, vo.getBussPeriodId())) {
                    // 修改当前业务期间状态为已完成
                    vo.setPeriodState("3");
                    vo.setUpdatorId(1L);
                    vo.setUpdateTime(now);
                    atrConfBussPeriodDao.updateById(vo);

                    String nextYearMonth = Dates.getYearMonth(vo.getYearMonth(), 1);

                    // 检验下一个业务期间是否存在
                    vo = new AtrConfBussPeriod();
                    vo.setEntityId(entityId);
                    vo.setYearMonth(nextYearMonth);
                    vos = atrConfBussPeriodDao.findList(vo);

                    if (vos.isEmpty()) {
                        // 增加下个准备中业务期间
                        syncPeriodStatus(entityId, "0");
                    } else {
                        // 修改下个业务期间状态为处理中
                        syncPeriodStatus(entityId, "2");
                    }
                }
            }
        }

    }


    @Override
    public Map<String, Object> checkReOpenAccountPeriod(AtrConfBussPeriodVo atrConfBussPeriodVo) {
        /**
         * 业务逻辑：
         * 1、重置业务数据：修改审核状态
         * 2、如有处理中状态期间需改为已准备状态
         * 3、将重开的业务期间改为处理中
         */

        AtrConfBussPeriodVo vo = new AtrConfBussPeriodVo();
        Map<String, Object> resultMap = new HashMap<>();
        vo.setEntityId(atrConfBussPeriodVo.getEntityId());
        vo.setPeriodState(CommonConstant.BussPeriod.PeriodStatus.COMPLETED);
        AtrConfBussPeriodVo completeBussPeriodVo = atrConfBussPeriodDao.findByVo(vo);

        resultMap.put("flag1", null == completeBussPeriodVo);
        resultMap.put("completeBussPeriodVo", completeBussPeriodVo);
        return resultMap;
    }


    @Override
    public void reOpenAccountPeriod(AtrConfBussPeriodVo atrConfBussPeriodVo) {
        Map<String, Object> paramMap = new HashMap<>(8);
        paramMap.put("entityId", atrConfBussPeriodVo.getEntityId());
        paramMap.put("bookCode", CommonConstant.AccountSetType.I17);
        paramMap.put("systemCode", SystemConstant.AtrIdentity.APP_CODE);
        paramMap.put("state", "0");
        try {
            AtrConfBussPeriodVo vo = new AtrConfBussPeriodVo();
            vo.setEntityId(atrConfBussPeriodVo.getEntityId());
            vo.setYearMonth(atrConfBussPeriodVo.getYearMonth());
            vo.setPeriodState(CommonConstant.BussPeriod.PeriodStatus.COMPLETED);
            AtrConfBussPeriodVo completeBussPeriodVo = atrConfBussPeriodDao.findByVo(vo);
            if (ObjectUtils.isNotEmpty(completeBussPeriodVo)) {
                atrBussLicCashFlowService.reSetLicCashFlow(completeBussPeriodVo);
                atrBussLrcCashFlowService.reSetLrcCashFlow(completeBussPeriodVo);
                atrBussIbnrSecAllocService.reSetIbnrCashFlow(completeBussPeriodVo);
                //清理重开期间之后的期间数据
                this.reOpenProcessPeriod(completeBussPeriodVo.getEntityId(), completeBussPeriodVo.getYearMonth());
                this.reOpenCompletePeriod(completeBussPeriodVo);
                // 清除缓存
                this.deleteFormRedisByHashKey(completeBussPeriodVo.getEntityId(), null);

                paramMap.put("state", "1");
            }
        } catch (UnexpectedRollbackException e) {
            LOG.error(e.getLocalizedMessage(), e);
            throw e;
        } finally {
        }
    }

    /*
     * 重开期间操作：清理重开之后的期间
     * */
    public void reOpenProcessPeriod(Long entityId, String yearMonth) {
        BussPeriodReqVo bussPeriodReqVo = new BussPeriodReqVo();
        bussPeriodReqVo.setEntityId(entityId);
        bussPeriodReqVo.setYearMonth(yearMonth);
        atrConfBussPeriodDetailDao.clearPeriodDetail(bussPeriodReqVo);
        atrConfBussPeriodDao.clearPeriod(bussPeriodReqVo);
    }


    /*
     * 重开期间操作：期间已完成状态-> 准备中
     * */
    public void reOpenCompletePeriod(AtrConfBussPeriodVo completeBussPeriodVo) {
        AtrConfBussPeriodDetailVo detailVo = new AtrConfBussPeriodDetailVo();
        detailVo.setBussPeriodId(completeBussPeriodVo.getBussPeriodId());
        detailVo.setReadyState(CommonConstant.BussPeriod.PeriodStatus.PREPARING);
        detailVo.setExecResult("");
        atrConfBussPeriodDetailDao.updateByDetail(detailVo);

        AtrConfBussPeriod po = ClassUtil.convert(completeBussPeriodVo, AtrConfBussPeriod.class);
        po.setPeriodState(CommonConstant.BussPeriod.PeriodStatus.PREPARING);
        atrConfBussPeriodDao.updateById(po);
    }

    /**
     * 验证输入明细数据状态是否己准备
     */
    private boolean checkPeriodDetailAllReady(String state, Long bussPeriodId) {
        if ("0".equals(state)) {
            return true;
        }
        String direction = "1".equals(state) || "2".equals(state) ? "1" : "0";
        return atrConfBussPeriodDao.countNotReady(bussPeriodId, direction, Math.random()) == 0;
    }

}
