package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.dao.conf.AtrConfBussPeriodDetailDao;
import com.ss.ifrs.actuarial.feign.BmsActProcFeignClient;
import com.ss.ifrs.actuarial.feign.BbsConfEntityFeignClient;
import com.ss.ifrs.actuarial.feign.BmsScheduleFeignClient;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodDetailVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.ifrs.actuarial.service.AtrConfBussPeriodDetailService;
import com.ss.platform.pojo.com.vo.ActOverviewVo;
import com.ss.platform.pojo.bbs.vo.BbsConfEntityVo;
import com.ss.platform.pojo.bms.job.po.BmsScheduleDetail;
import com.ss.platform.pojo.bms.job.vo.BmsScheduleJobVo;
import com.ss.platform.pojo.bms.qrtz.vo.BmsQrtzConfTaskDetailVo;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@Transactional
public class AtrConfBussBussPeriodDetailServiceImpl implements AtrConfBussPeriodDetailService {

	final Logger LOG = LoggerFactory.getLogger(getClass());

	@Autowired
	private AtrConfBussPeriodDetailDao atrConfPeriodDetailDao;

	@Autowired
	private BmsScheduleFeignClient scheduleFeignClient;

	@Autowired
	private BmsActProcFeignClient actProcFeignClient;

	@Autowired
	private BbsConfEntityFeignClient configCenterFeignClient;

	@Override
	public List<AtrConfBussPeriodDetailVo> findPeriodDtlByEntityId(AtrConfBussPeriodVo atrConfBussPeriodVo) {
		List<AtrConfBussPeriodDetailVo> voList = atrConfPeriodDetailDao.findPeriodDtlByEntityId(atrConfBussPeriodVo);
		if (voList.isEmpty()) {
			voList = new ArrayList<>();
			return voList;
		}
		return voList;
	}

	@Override
	public BmsScheduleJobVo findJobByBizCode(BmsScheduleJobVo scheduleJobVo) {
		Long entityId = scheduleJobVo.getEntityId();
		Long procId = scheduleJobVo.getActProcId();
		String bizCode = scheduleJobVo.getBizCode();

		BmsScheduleDetail jobDetail = scheduleFeignClient.findJobDetailByPeriod(entityId, bizCode);
		if (ObjectUtils.isEmpty(jobDetail)) {
			// 查上级功能组配置定时任务的业务编码
			BmsQrtzConfTaskDetailVo confTaskDtl = scheduleFeignClient.findConfTaskDtlByFuncCode(entityId, bizCode);
			if (ObjectUtils.isEmpty(confTaskDtl)) {
				return scheduleJobVo;
			}
			jobDetail = scheduleFeignClient.findJobDetailByPeriod(entityId, confTaskDtl.getPeriodBussCode());
			if (ObjectUtils.isEmpty(jobDetail)) {
				return scheduleJobVo;
			}
		}
		scheduleJobVo.setJobName(jobDetail.getJobName());
		scheduleJobVo.setJobEName(jobDetail.getJobEName());
		scheduleJobVo.setJobCName(jobDetail.getJobCName());
		scheduleJobVo.setJobLName(jobDetail.getJobLName());
		scheduleJobVo.setJobGroup(jobDetail.getJobGroup());
		scheduleJobVo.setUserId(jobDetail.getCreatorId());
		scheduleJobVo.setUrl(jobDetail.getUrl());
		try {
			BaseResponse<BmsScheduleJobVo> result = scheduleFeignClient.view(scheduleJobVo);
			if (result != null) {
				Object obj = result.getResData();
				return (BmsScheduleJobVo) obj;
			}
		} catch (Exception e) {
			LOG.error(e.getLocalizedMessage(), e);
		}
		if (procId != null) {
			BaseResponse<ActOverviewVo> baseResponse = actProcFeignClient.findById(procId);
			if (ResCodeConstant.ResCode.SUCCESS != baseResponse.getResCode()) {
				ActOverviewVo actOwVo = baseResponse.getResData();
				scheduleJobVo.setActProcVo(actOwVo);
			}
		}
		if (entityId != null) {
			BbsConfEntityVo center = configCenterFeignClient.findByEntityId(entityId);
			if (center != null) {
				scheduleJobVo.setEntityVo(center);
			}
		}
		return scheduleJobVo;
	}

	@Override
	public void immediateExecutionTask(BmsScheduleJobVo scheduleJobVo) throws Exception {
		// 根据业务单位和业务编码查询业务期间详情对象
		scheduleFeignClient.immediateExecutionTask(scheduleJobVo);

	}
	
	@Override
	public String checkInputReadyState(AtrConfBussPeriodDetailVo confBussPeriodDetailVo) {
		
		// 检查最后业务期间详情结果
		//1-输入
		confBussPeriodDetailVo.setDirection("1");
		int count = atrConfPeriodDetailDao.checkReadyState(confBussPeriodDetailVo);
		// 未完全准备
		if (count > 0){
			return "0";
		}
		return "1";
	}
	
	@Override
	public String checkOutputReadyState(AtrConfBussPeriodDetailVo confBussPeriodDetailVo) {
		//0-输出
		confBussPeriodDetailVo.setDirection("0");
		int count = atrConfPeriodDetailDao.checkReadyState(confBussPeriodDetailVo);
		if (count > 0){
			return "0";
		}
		return "1";
	}
}
