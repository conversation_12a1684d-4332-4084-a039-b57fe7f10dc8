CREATE OR REPLACE PACKAGE dm_pack_cmunit_treaty_inward IS

  PROCEDURE proc_cmunit_identify_all(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2);

  PROCEDURE proc_cmunit_identify(p_entity_id  NUMBER,
                                 p_year_month VARCHAR2);

  PROCEDURE proc_majorrisk_test(p_entity_id  NUMBER,
                                p_year_month VARCHAR2);

  PROCEDURE proc_majorrisk_test_nopass(p_entity_id  NUMBER,
                                       p_year_month VARCHAR2);

  PROCEDURE proc_approach_discern(p_entity_id  NUMBER,
                                  p_year_month VARCHAR2);

  PROCEDURE proc_portfolio_discern(p_entity_id  NUMBER,
                                   p_year_month VARCHAR2);

  PROCEDURE proc_profit_loss_discern(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2);

  PROCEDURE proc_icg_discern(p_entity_id  NUMBER,
                             p_year_month VARCHAR2);

  /*PROCEDURE proc_icg_fixed(p_entity_id  NUMBER,
                           p_year_month VARCHAR2);*/

  PROCEDURE proc_icg_group(p_entity_id  NUMBER,
                           p_year_month VARCHAR2);

  PROCEDURE proc_investment_separate(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2);

  PROCEDURE proc_contract_group_confirm(p_entity_id  NUMBER,
                                        p_year_month VARCHAR2);

  PROCEDURE proc_backup_profit_unit_result(p_entity_id  NUMBER,
                                           p_year_month VARCHAR2);

  PROCEDURE proc_buss_cmunit_log(p_entity_id  NUMBER,
                                 p_year_month VARCHAR2,
                                 p_trace_no VARCHAR2,
                                 p_trace_code VARCHAR2,
                                 p_trace_status VARCHAR2,
                                 p_trace_msg VARCHAR2);

END dm_pack_cmunit_treaty_inward;
/
CREATE OR REPLACE PACKAGE BODY dm_pack_cmunit_treaty_inward IS

  FUNCTION func_get_current_year_month(p_entity_id  NUMBER,
                                       p_year_month VARCHAR2) RETURN VARCHAR2 IS
  BEGIN
    RETURN dm_pack_buss_period.func_get_current_year_month(p_entity_id, p_year_month, 'BUSS_CMUNIT_TREATY_IN');

  END func_get_current_year_month;

  FUNCTION func_get_valid_year_month(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2) RETURN VARCHAR2 IS
  BEGIN
    RETURN dm_pack_buss_period.func_get_valid_year_month(p_entity_id, p_year_month, 'BUSS_CMUNIT_TREATY_IN');

  END func_get_valid_year_month;

  PROCEDURE proc_cmunit_identify_all(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2) IS

    cur_yearmonth VARCHAR2(200);
  BEGIN

    IF p_year_month IS NULL OR length(p_year_month) = 0 THEN

      FOR cur_yearmonth IN (SELECT year_month
                              FROM dm_conf_bussperiod
                             WHERE entity_id = p_entity_id
                               AND period_state IN ('0', '1', '2')
                             ORDER BY year_month) LOOP
        -- 合约分入分出 - 生成计量单元
        proc_cmunit_identify(p_entity_id, cur_yearmonth.year_month);

      END LOOP;
    ELSE
      -- 合约分入分出 - 生成计量单元
      proc_cmunit_identify(p_entity_id, p_year_month);

    END IF;

  END proc_cmunit_identify_all;

  PROCEDURE proc_cmunit_identify(p_entity_id  NUMBER,
                                 p_year_month VARCHAR2) IS
    v_proc_id                  NUMBER;
    v_end_date                 DATE;
    v_short_risk_flag_open_is VARCHAR(200);
    v_error_msg               varchar2(2000);
  BEGIN

    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分入-合同分组]计量单元划分-传参不为空：proc_cmunit_identify(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;
    IF func_get_valid_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分入-合同分组]计量单元划分-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    --长短险标识生成是否开启 0-不开启
    SELECT MIN(valid_is)
      INTO v_short_risk_flag_open_is
      FROM bpluser.bpl_conf_code
     WHERE code_code = 'ShortRiskFlagOpenIs'
       AND upper_code_id = 0
       AND valid_is = '1';

    IF v_short_risk_flag_open_is <> '1' THEN
      v_short_risk_flag_open_is := '0';
    END IF;

    v_end_date := add_months(to_date(p_year_month, 'yyyymm'), 1);
    --根据proc_code查询proc_id
    v_proc_id := dm_pack_common.func_get_procid('DM_UNIT_RECOGNIZER');

    -- 再保合约计量单元信息(分入)
    INSERT INTO dm_buss_cmunit_treaty
          (cm_unit_itreaty_id,
           cmunit_no,
           entity_id,
           ri_direction_code,
           effective_date,
           expiry_date,
           issue_date,
           treaty_no,
           treaty_type_code,
           epi_currency_code,
           premium,
           currency_code,
           billing_frequency_code,
           floating_charge_is,
           profit_fee_is,
           min_return_amount,
           related_party, --关联交易方
           short_risk_flag, --长短险标志
           draw_type,
           proc_id,
           buss_year_month, --业务年月
           create_time,
           loa_code,
           business_source_code,
           offshore_is,
           node_state,
           reason_of_failure,
           exception_message
           )
      select dm_seq_buss_cmunit_treaty.nextval cm_unit_itreaty_id,
           dm_pack_cmunit_common.func_get_ri_cmunitno(entity_id, 'T', 'I') cmunit_no,
             -- 计量单元编码： 业务单位 || 合约/临分标志 || 分入/分出标志 || 流水号(000001)
           entity_id,
           ri_direction_code,
           effective_date,
           expiry_date,
           issue_date,
           treaty_no,
           treaty_type_code,
           epi_currency_code,
           premium,
           currency_code,
           billing_frequency_code,
           floating_charge_is,
           profit_fee_is,
           min_return_amount,
           related_party, --关联交易方
           short_risk_flag, --长短险标志
           draw_type,
           proc_id,
           buss_year_month, --业务年月
           create_time,
           loa_code,
           business_source_code,
           offshore_is,
           (CASE WHEN (loa_code) IS NULL THEN '2' ELSE '1' END) node_state,
           (CASE WHEN (loa_code) IS NULL THEN 'DM_loa' ELSE NULL END)reason_of_failure,
           (CASE WHEN (loa_code) IS NULL THEN 'not find loa config' ELSE NULL END)exception_message
      from (SELECT row_number() over(PARTITION BY t1.entity_id, t1.treaty_no ORDER BY 1) rn,--去重
                   t1.entity_id,
                   t1.ri_direction_code,
                   t1.effective_date,
                   t1.expiry_date,
                   t1.issue_date,
                   t1.treaty_no,
                   t1.treaty_type_code,
                   t1.epi_currency_code,
                   t1.premium,
                   t1.currency_code,
                   t1.billing_frequency_code,
                   t1.floating_charge_is,
                   t1.profit_fee_is,
                   t1.min_return_amount,
                   t1.related_party,
                   (CASE
                     WHEN v_short_risk_flag_open_is = '0' THEN
                      '0'
                     ELSE
                      (CASE
                        WHEN add_months(t1.effective_date, 12) > t1.expiry_date THEN
                         '1' -- 短险
                        ELSE
                         '2' -- 长险
                      END)
                   END) AS short_risk_flag, --长短险标志
                   t1.draw_type,
                   v_proc_id as proc_id,
                   '1' node_state,
                   p_year_month AS buss_year_month,
                   localtimestamp create_time,
                   loa.loa_code,
                   'TB' as business_source_code,
                   t1.offshore_is
              FROM dm_reins_treaty t1
              LEFT JOIN bpluser.bbs_conf_loa_detail loat
                ON loat.business_id = t1.base_treaty_id
              LEFT JOIN bpluser.bbs_conf_loa loa
                ON loat.loa_id = loa.loa_id
               AND loa.business_model = 'T'
               AND loa.business_direction = 'I'
               AND loa.valid_is = '1'
               AND loa.audit_state = '1'
               AND loa.entity_id = t1.entity_id
             WHERE t1.entity_id = p_entity_id
               AND t1.task_status = '4'
               AND t1.effective_date < v_end_date
               AND t1.expiry_date >= v_end_date
                  --AND t1.issue_date < V_END_DATE
               AND t1.ri_direction_code = 'I'
               AND t1.treaty_type_code NOT IN ('91', '92')
               AND t1.treaty_type_code NOT IN ('81', '82') --自留合约不进行触发计量单元
               AND NOT EXISTS (
                    --确保不重复生成计量单元
                   SELECT cm_unit_itreaty_id
                      FROM dm_buss_cmunit_treaty cmt
                     WHERE cmt.treaty_no = t1.treaty_no
                       AND cmt.entity_id = t1.entity_id
                       AND cmt.ri_direction_code = t1.ri_direction_code
                    )
      ) g
      WHERE rn = 1;
    COMMIT;

        --只有处理中的才能重新执行
    IF func_get_current_year_month(p_entity_id, p_year_month) IS NOT NULL THEN

      MERGE INTO dm_buss_cmunit_treaty cma
      USING (SELECT cm.cm_unit_itreaty_id,
                    loa.loa_code
               FROM dm_buss_cmunit_treaty cm
               LEFT JOIN bpluser.bbs_conf_treaty ty
                 ON ty.entity_id = cm.entity_id
                AND ty.treaty_no = cm.treaty_no
                AND ty.ri_direction_code = cm.ri_direction_code
                AND ty.treaty_type_code NOT IN ('91', '92')
                AND ty.treaty_type_code NOT IN ('81', '82') --自留合约不进行触发计量单元
                    left join (select loa.entity_id,
                          loa.loa_code,
                          loat.business_id,
                          loa.business_direction
                    from bpluser.bbs_conf_loa loa
                    JOIN bpluser.bbs_conf_loa_detail loat
                     ON loa.loa_id = loat.loa_id
                    where 1=1
                    AND loa.business_model = 'T'
                    AND loa.valid_is = '1'
                    AND loa.audit_state = '1') loa
                     ON loa.business_id = ty.base_treaty_id
                     and loa.entity_id = ty.entity_id
                     AND loa.business_direction = cm.ri_direction_code
              WHERE cm.ri_direction_code = 'I'
                AND cm.year_month IS NULL
                AND cm.buss_year_month <= p_year_month
             ) cmb
      ON (cma.cm_unit_itreaty_id = cmb.cm_unit_itreaty_id)
      WHEN MATCHED THEN
        UPDATE
           SET major_risk           = NULL,
               evaluate_approach    = NULL, --评估方法
               portfolio_no         = NULL,
               pl_judge_rslt        = NULL, --盈亏
               pl_judge_date        = NULL, --盈亏时间
               year_month           = NULL,
               border_date          = NULL,
               icg_no               = NULL,
               invest_rate          = NULL,
               invest_amount        = NULL,
               proc_id              = v_proc_id,
               loa_code             = cmb.loa_code,
               node_state           = (CASE WHEN cmb.loa_code IS NULL THEN '2' ELSE '1' END),
               reason_of_failure    = (CASE WHEN cmb.loa_code IS NULL THEN 'DM_loa' ELSE NULL END),
               reason_of_mr_failure = NULL,
               exception_message    = (CASE WHEN cmb.loa_code IS NULL THEN 'not find loa config' ELSE NULL END);

      COMMIT;

    END IF;

    -- 修改已生成计量单元标识
    UPDATE dm_reins_treaty t
       SET task_status = '5'
     WHERE EXISTS (SELECT 1
              FROM dm_buss_cmunit_treaty g
             WHERE g.entity_id = p_entity_id
               AND g.ri_direction_code = 'I'
               AND g.entity_id = t.entity_id
               AND g.treaty_no = t.treaty_no)
       AND t.entity_id = p_entity_id
       AND t.effective_date < v_end_date
       AND t.expiry_date >= v_end_date
       --AND t.issue_date < v_end_date
       AND t.treaty_type_code NOT IN ('91', '92')
       AND t.task_status = '4'
       AND t.ri_direction_code = 'I';
    COMMIT;

    -- 不符合计量单元生成规则的数据,不再处理
    UPDATE dm_reins_treaty t
       SET task_status = '6'
     WHERE t.entity_id = p_entity_id
       AND t.effective_date < v_end_date
       AND t.expiry_date >= v_end_date
       --AND t.issue_date < v_end_date
       AND t.treaty_type_code IN ('91', '92')
       AND t.task_status = '4'
       AND t.ri_direction_code = 'I';
    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN
      --意外处理
      v_error_msg := '[EXCEPTION][合约分入-合同分组]计量单元划分:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);


  END proc_cmunit_identify;

  PROCEDURE proc_majorrisk_test(p_entity_id  NUMBER,
                                p_year_month VARCHAR2) IS

    v_proc_id        NUMBER;
    v_test_plan_type VARCHAR2(200);
    v_error_msg      varchar2(2000);
    v_count number;
    v_buss_model VARCHAR2(10);
  BEGIN

    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分入-合同分组]重测处理-传参不为空：proc_majorrisk_test(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分入-合同分组]重测处理-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 当前业务年月存在未进行重大风险测试的数据
    -- 根据PROC_CODE查询PROC_ID
    v_proc_id := dm_pack_common.func_get_procid('DM_RISK_TEST');


    v_buss_model := 'TI';

    SELECT count(1)
      INTO v_count
      FROM dm_conf_icg_majorrisk_plan
     WHERE entity_id = p_entity_id
       AND business_model || business_direction = v_buss_model;

    if v_count > 0 then
    --查找合约分入重大风险方案的配置
    SELECT MIN(test_plan_type)
      INTO v_test_plan_type
      FROM dm_conf_icg_majorrisk_plan
     WHERE entity_id = p_entity_id
       AND business_model || business_direction = v_buss_model;
    end if;
    if v_test_plan_type is null then
      v_test_plan_type := 'A';
    end if;

    --dbms_output.put_line(v_test_plan_type);
    --合约分入：
    IF v_test_plan_type IS NOT NULL THEN

      IF v_test_plan_type = 'A' THEN
        --全部通过
        UPDATE dm_buss_cmunit_treaty t
           SET evaluate_approach = NULL, --评估方法
               portfolio_no      = NULL,
               pl_judge_rslt     = NULL, --盈亏
               pl_judge_date     = NULL, --盈亏时间
               year_month        = NULL,
               border_date       = NULL,
               icg_no            = NULL,
               invest_rate       = NULL,
               invest_amount     = NULL,
               proc_id           = v_proc_id,
               node_state        = '1',
               major_risk        = 'Y' --通过
         WHERE t.entity_id = p_entity_id
           AND t.buss_year_month <= p_year_month
           AND t.ri_direction_code = 'I'
           AND t.year_month IS NULL;
        COMMIT;
        --AND (T.MAJOR_RISK IS NULL OR  (T.NODE_STATE = '2' AND T.PROC_ID = V_PROC_ID));

      ELSIF v_test_plan_type = 'C' THEN
        --配置
        MERGE INTO DM_BUSS_CMUNIT_TREATY A
            USING (SELECT CM.CM_UNIT_ITREATY_ID,
                          V_PROC_ID AS PROC_ID,
                          (CASE
                              WHEN RD.TEST_RESULT IS NULL THEN
                               '2'
                              ELSE
                               '1'
                          END) AS NODE_STATE,
                          RD.TEST_RESULT AS MAJOR_RISK,
                          (CASE
                              WHEN RD.TEST_RESULT IS NULL THEN
                               'DM_001'
                              ELSE
                               NULL
                          END) AS REASON_OF_FAILURE,
                          (CASE
                              WHEN RD.TEST_RESULT IS NULL THEN
                               'DM_001'
                              ELSE
                               NULL
                          END) AS REASON_OF_MR_FAILURE,
                          (CASE
                              WHEN RD.TEST_RESULT IS NULL THEN
                               'DM_001'
                              ELSE
                               NULL
                          END) AS EXCEPTION_MESSAGE
                     FROM DM_BUSS_CMUNIT_TREATY CM
                     LEFT JOIN DM_CONF_CONTRACT_RISKDEF RD
                       ON CM.ENTITY_ID = RD.ENTITY_ID
                      AND CM.treaty_no = RD.treaty_no
                      AND RD.VALID_IS = '1'
                      AND RD.AUDIT_STATE = '1'
                      AND RD.BUSINESS_MODEL||RD.BUSINESS_DIRECTION = v_buss_model
                      AND RD.AUDIT_STATE = '1'
                    WHERE CM.ENTITY_ID = p_entity_id
                      AND CM.BUSS_YEAR_MONTH <= p_year_month
                      AND CM.YEAR_MONTH IS NULL
                      AND CM.ri_direction_code = 'I'
                      ) B
            ON (A.CM_UNIT_ITREATY_ID = B.CM_UNIT_ITREATY_ID)
            WHEN MATCHED THEN
                UPDATE
                   SET PROC_ID = B.PROC_ID,
                   NODE_STATE = B.NODE_STATE,
                   MAJOR_RISK = B.MAJOR_RISK,
                   EVALUATE_APPROACH = null,
                   PORTFOLIO_NO = null,
                   PL_JUDGE_RSLT = null,
                   PL_JUDGE_DATE = null,
                   BORDER_DATE = null,
                   ICG_NO = null,
                   INVEST_RATE = null,
                   INVEST_AMOUNT = null,
                   REASON_OF_FAILURE = B.REASON_OF_FAILURE,
                   REASON_OF_MR_FAILURE = B.REASON_OF_MR_FAILURE,
                    EXCEPTION_MESSAGE = B.EXCEPTION_MESSAGE;

        COMMIT;

    ELSIF v_test_plan_type = 'R' THEN
        --规则
        MERGE INTO DM_BUSS_CMUNIT_TREATY A
        USING (SELECT CM_UNIT_ITREATY_ID,
                      V_PROC_ID AS PROC_ID,
                      (CASE
                          WHEN (CMRD.MAJOR_RISK = 'Y' OR CMRD.MAJOR_RISK = 'N') THEN
                           '1'
                          ELSE
                           '2'
                      END) AS NODE_STATE,
                      (CASE
                          WHEN CMRD.MAJOR_RISK LIKE 'E:%' THEN
                           NULL
                          ELSE
                           CMRD.MAJOR_RISK
                      END) AS MAJOR_RISK,
                      (CASE
                          WHEN CMRD.MAJOR_RISK LIKE 'E:%' THEN
                           'DM_001'
                          WHEN CMRD.MAJOR_RISK LIKE 'R:%' THEN
                           'DM_003'
                          ELSE
                           NULL
                      END) AS REASON_OF_FAILURE,
                      (CASE
                          WHEN CMRD.MAJOR_RISK LIKE 'E:%' THEN
                           'DM_001'
                          WHEN CMRD.MAJOR_RISK LIKE 'R:%' THEN
                           'DM_003'
                          ELSE
                           NULL
                      END) AS REASON_OF_MR_FAILURE,
                      CMRD.MAJOR_RISK AS EXCEPTION_MESSAGE
                 FROM (SELECT CM_UNIT_ITREATY_ID,
                              COALESCE(DM_PACK_CMUNIT_COMMON.FUNC_FACTOR_VALUE_CAL(CM_UNIT_ITREATY_ID,ENTITY_ID,treaty_no,NULL,'1',v_buss_model),'R:') AS MAJOR_RISK
                         FROM DM_BUSS_CMUNIT_TREATY
                        WHERE ENTITY_ID = P_ENTITY_ID
                          AND BUSS_YEAR_MONTH <= P_YEAR_MONTH
                          AND YEAR_MONTH IS NULL
                          and RI_DIRECTION_CODE = 'I') CMRD
               ) B
        ON (A.CM_UNIT_ITREATY_ID = B.CM_UNIT_ITREATY_ID)
            WHEN MATCHED THEN
                            UPDATE
                               SET PROC_ID = B.PROC_ID,
                               NODE_STATE = B.NODE_STATE,
                               MAJOR_RISK = B.MAJOR_RISK,
                               EVALUATE_APPROACH = null,
                               PORTFOLIO_NO = null,
                               PL_JUDGE_RSLT = null,
                               PL_JUDGE_DATE = null,
                               BORDER_DATE = null,
                               ICG_NO = null,
                               INVEST_RATE = null,
                               INVEST_AMOUNT = null,
                               REASON_OF_FAILURE = B.REASON_OF_FAILURE,
                               REASON_OF_MR_FAILURE = B.REASON_OF_MR_FAILURE,
                               EXCEPTION_MESSAGE = B.EXCEPTION_MESSAGE;
        commit;
        -- 1，更新未配置规则重测标识为 不通过
        SELECT count(1)
          INTO v_count
          FROM dm_conf_contract_rule_def
         WHERE valid_is = '1'
           AND audit_state = '1'
           AND contract_rule_type = '1'
           AND entity_id = p_entity_id
           AND business_model||business_direction = v_buss_model;

        IF v_count = 0 THEN
          --未配置规则，处理异常
          UPDATE DM_BUSS_CMUNIT_TREATY
             SET node_state           = '2', -- 节点状态
                 proc_id              = v_proc_id,
                 evaluate_approach    = NULL, --评估方法
                 portfolio_no         = NULL,
                 pl_judge_rslt        = NULL, --盈亏
                 pl_judge_date        = NULL, --盈亏时间
                 year_month           = NULL,
                 border_date          = NULL,
                 icg_no               = NULL,
                 invest_rate          = NULL,
                 invest_amount        = NULL,
                 major_risk           = NULL,
                 reason_of_failure    = 'DM_001',
                 reason_of_mr_failure = 'DM_001',
                 exception_message    = NULL
           WHERE entity_id = p_entity_id
             AND buss_year_month <= p_year_month
             AND year_month IS NULL
             and RI_DIRECTION_CODE = 'I';

          COMMIT;
       END IF;
    END IF;

    ELSE

      UPDATE dm_buss_cmunit_treaty t
         SET (proc_id,
              evaluate_approach,
              portfolio_no,
              pl_judge_rslt,
              pl_judge_date,
              year_month,
              border_date,
              icg_no,
              invest_rate,
              invest_amount,
              node_state,
              major_risk) =
             (SELECT v_proc_id AS proc_id,
                     NULL AS evaluate_approach,
                     NULL AS portfolio_no,
                     NULL AS pl_judge_rslt,
                     NULL AS pl_judge_date,
                     NULL AS year_month,
                     NULL AS border_date,
                     NULL AS icg_no,
                     NULL AS invest_rate,
                     NULL AS invest_amount,
                     '1' AS node_state,
                     nvl(CASE
                           WHEN (cc.audit_state = '1' AND cc.valid_is = '1') THEN
                            cc.major_risk
                           ELSE
                            'Y'
                         END, 'Y') AS major_risk
                 FROM dm_buss_cmunit_treaty cm,
                       dm_conf_treaty_major_risk cc
                 WHERE cm.treaty_no = cc.treaty_no
                   AND cm.entity_id = cc.entity_id
                   AND cm.entity_id = p_entity_id
                   AND cm.buss_year_month <= p_year_month
                   AND cm.ri_direction_code = 'I'
                   AND cm.cm_unit_itreaty_id = t.cm_unit_itreaty_id)
       WHERE t.entity_id = p_entity_id
         AND t.buss_year_month <= p_year_month
         AND t.ri_direction_code = 'I'
         AND t.year_month IS NULL;

      COMMIT;
    END IF;

   --如果有不通过的，设置为业务处理通过
     MERGE INTO dm_buss_cmunit_treaty A
          USING (SELECT cm_unit_itreaty_id
                 from dm_buss_cmunit_treaty cm
                  WHERE entity_id = p_entity_id
                         AND buss_year_month <= p_year_month
                         AND year_month IS NULL
                         and RI_DIRECTION_CODE = 'I'
                         and MAJOR_RISK = 'N') B
          ON (A.cm_unit_itreaty_id = B.cm_unit_itreaty_id)
              WHEN MATCHED THEN
                              UPDATE
                                 SET MAJOR_RISK = 'O';
    commit;
    proc_majorrisk_test_nopass(p_entity_id, p_year_month);

  EXCEPTION
    WHEN OTHERS THEN

      --意外处理
      -- 执行异常，记录异常原因
      v_error_msg := '[EXCEPTION][合约分入-合同分组]重测处理:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_majorrisk_test;

  PROCEDURE proc_majorrisk_test_nopass(p_entity_id  NUMBER,
                                       p_year_month VARCHAR2) IS
    v_icg_column         VARCHAR2(4000);
    v_portfolio_column    VARCHAR2(4000);
    v_icg_proc_id        NUMBER;
    v_portflio_proc_id   NUMBER;
    v_count              NUMBER;
    v_reins_direction VARCHAR2(200);
    v_error_msg       varchar2(2000);
  BEGIN
    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分入-合同分组]重测处理不通过-传参不为空：proc_majorrisk_test_nopass(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分入-合同分组]重测处理不通过-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    v_reins_direction := 'I';
    -- 是否存在重测业务不通过及未生成合同组数据

    SELECT count(1)
      INTO v_count
      FROM dm_buss_cmunit_treaty
     WHERE entity_id = p_entity_id
       AND buss_year_month <= p_year_month
       AND major_risk = 'O' -- 重测业务不通过
       AND icg_no IS NULL -- 未生成合同组
       AND ri_direction_code = v_reins_direction
       AND year_month IS NULL
       AND rownum = 1;

    IF v_count = 0 THEN
      return;
    END IF;

    -- 重测业务不通过，生成合同组合
    v_portfolio_column := dm_pack_cmunit_common.func_get_contractcode(p_entity_id, 'G', 'TI');
    IF v_portfolio_column IS NOT NULL THEN
      -- 根据PROC_CODE查询PROC_ID
      v_portflio_proc_id := dm_pack_common.func_get_procid('DM_PORTFOLIO');

      v_icg_column := dm_pack_cmunit_common.func_get_contractcode(p_entity_id, 'C', 'TI');

      IF v_icg_column IS NOT NULL THEN
         v_icg_proc_id := dm_pack_common.func_get_procid('DM_CONTRACTGROUP');
      END IF;
    END IF;

    -- 1,评估方法及盈亏判定设置为 D-不区分
    -- 2,生成合同组合编码
    -- 3,更新合同组编码
    -- 4,《确认日期》为确认不通过的业务期间月末
    EXECUTE IMMEDIATE ('update dm_buss_cmunit_treaty t '
                       ||' set evaluate_approach = ''D'''
                       ||', pl_judge_rslt = ''D'''
                       ||', portfolio_no = ' || v_portfolio_column
                       ||', icg_no = ' || v_icg_column
                       ||', border_date = ' || (CASE WHEN v_icg_column IS NOT NULL THEN 'last_day(to_date('''||p_year_month||'01'',''YYYYMMDD''))' END)
                       ||', proc_id= ' || (CASE WHEN v_portfolio_column IS NULL THEN v_portflio_proc_id ELSE v_icg_proc_id END)
                       ||', node_state= ''' || (CASE WHEN v_portflio_proc_id IS NULL OR v_icg_proc_id IS NULL THEN '2' ELSE '1' END)||''''
                       ||', reason_of_failure = ''' || (CASE WHEN v_portfolio_column IS NULL OR v_icg_column IS NULL THEN 'DM_003' ELSE NULL END) ||''''
                       ||', reason_of_mr_failure = ''' || (CASE WHEN v_portfolio_column IS NULL OR v_icg_column IS NULL THEN 'DM_003' ELSE NULL END) ||''''
                    ||' where entity_id = ' || p_entity_id
                    --||' AND evaluate_approach <> ''D'' '
                    ||' AND major_risk in (''N'',''O'')'
                    --||' AND pl_judge_rslt is not null '
                    --||' AND  portfolio_no is null '
                    ||' AND ri_direction_code = ''I'' '
                    ||' AND year_month is null '
                    ||' AND buss_year_month <= ''' || p_year_month || '''');
    COMMIT;

    /*
    -- 根据PROC_CODE查询PROC_ID
    v_proc_id := dm_pack_common.func_get_procid('DM_INVESTMENT_COST');
    IF v_proc_id IS NULL THEN
      RETURN;
    END IF;

    --合约分入：投成拆分
    UPDATE DM_BUSS_CMUNIT_TREATY CMIN
      SET (INVEST_RATE,
           INVEST_AMOUNT,
           PROC_ID,
           NODE_STATE,
           REASON_OF_FAILURE) =
          (SELECT NVL(TO_NUMBER(BCQ.QUOTA_VALUE, '999.999999'), 0) AS INVEST_RATE,
                  NVL(PREMIUM, 0) *
                  NVL(TO_NUMBER(BCQ.QUOTA_VALUE, '999.999999'), 0) AS INVEST_AMOUNT,
                  V_PROC_ID AS PROC_ID,
                  '1' AS NODE_STATE,
                  NULL AS REASON_OF_FAILURE
             FROM BBS_CONF_QUOTA BCQ
            WHERE BCQ.QUOTA_DEF_ID =
                  (SELECT QUOTA_DEF_ID
                     FROM BBS_CONF_QUOTA_DEF
                    WHERE QUOTA_CODE = 'ReportingPeriodRatio') --取投成拆分字段
              AND BCQ.BUSINESS_MODEL = 'T' --取再保临分的
              AND CMIN.entity_id = BCQ.entity_id
              --AND CMIN.treaty_class_code = BCQ.treaty_class_code
              AND BCQ.VALID_IS = '1'
              AND BCQ.audit_state = '1')
    WHERE CMIN.MAJOR_RISK = 'O'
      AND CMIN.EVALUATE_APPROACH IS NOT NULL
      AND CMIN.PL_JUDGE_RSLT IS NOT NULL
      AND CMIN.BORDER_DATE IS NOT NULL
      AND CMIN.ICG_NO IS NOT NULL
      AND CMIN.INVEST_RATE IS NULL
      AND CMIN.INVEST_AMOUNT IS NULL
      AND CMIN.entity_id = p_entity_id
      AND CMIN.ri_direction_code = V_REINS_DIRECTION
      AND CMIN.YEAR_MONTH IS NULL
      AND CMIN.BUSS_YEAR_MONTH <= P_YEAR_MONTH;

           --合约分入未配置投成拆分规则
             UPDATE DM_BUSS_CMUNIT_TREATY CMIN
             SET PROC_ID = V_PROC_ID,NODE_STATE= '2',REASON_OF_FAILURE = 'DM_001'
             WHERE  CMIN.MAJOR_RISK = 'O'
               AND CMIN.EVALUATE_APPROACH IS NOT NULL
               AND CMIN.PL_JUDGE_RSLT IS NOT NULL
               AND CMIN.BORDER_DATE IS NOT NULL
               AND CMIN.ICG_NO IS NOT NULL
               AND CMIN.ri_direction_code = V_REINS_DIRECTION
               AND CMIN.INVEST_RATE IS NULL
               AND CMIN.INVEST_AMOUNT IS NULL
               AND CMIN.entity_id = p_entity_id
               AND CMIN.YEAR_MONTH IS NULL
               AND CMIN.BUSS_YEAR_MONTH <= P_YEAR_MONTH;*/

  EXCEPTION
    WHEN OTHERS THEN

      -- 执行异常，记录异常原因
      --意外处理
      v_error_msg := '[EXCEPTION][合约分入-合同分组]重测处理:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);
  END proc_majorrisk_test_nopass;

  PROCEDURE proc_approach_discern(p_entity_id  NUMBER,
                                  p_year_month VARCHAR2) IS

    v_proc_id        NUMBER;
    v_error_msg      varchar2(2000);
  BEGIN
    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分入-合同分组]评估方法适配-传参不为空：proc_approach_discern(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分入-合同分组]评估方法适配-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 根据proc_code查询proc_id
    v_proc_id := dm_pack_common.func_get_procid('DM_EVALDEF_CONFIG');

    MERGE INTO dm_buss_cmunit_treaty t
    USING (SELECT t1.cm_unit_itreaty_id,
                  ce.evaluate_approach
             FROM dm_buss_cmunit_treaty t1
             LEFT JOIN bpluser.bbs_conf_loa ce
               ON ce.entity_id = t1.entity_id
              AND ce.loa_code = t1.loa_code
              AND ce.audit_state = '1'
              AND ce.valid_is = '1'
              AND ce.business_model = 'T'
              AND ce.business_direction = t1.ri_direction_code
            WHERE t1.major_risk IN ('Y', 'P')
              AND t1.ri_direction_code = 'I'
              AND t1.year_month IS NULL
              AND t1.entity_id = p_entity_id
              AND t1.buss_year_month <= p_year_month
           ) c
    ON (t.cm_unit_itreaty_id = c.cm_unit_itreaty_id)
    WHEN MATCHED THEN
      UPDATE
         SET t.portfolio_no      = NULL,
             t.pl_judge_rslt     = NULL, --盈亏
             t.pl_judge_date     = NULL, --盈亏时间
             t.year_month        = NULL,
             t.border_date       = NULL,
             t.icg_no            = NULL,
             t.invest_rate       = NULL,
             t.invest_amount     = NULL,
             t.proc_id           = v_proc_id,
             t.evaluate_approach = c.evaluate_approach,
             t.node_state        = (case when c.evaluate_approach is not null then '1' else '2' end),--未配置loa_code提示异常
             t.reason_of_failure = (case when c.evaluate_approach is not null then null else 'DM_001' end)
       WHERE t.major_risk IN ('Y', 'P')
         AND t.ri_direction_code = 'I'
         AND t.year_month IS NULL
         AND t.entity_id = p_entity_id
         AND t.buss_year_month <= p_year_month
      ;
      COMMIT;

  EXCEPTION
    WHEN OTHERS THEN

      -- 执行异常，记录异常原因
      v_error_msg := '[EXCEPTION][合约分入-合同分组]评方法适配:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_approach_discern;


  PROCEDURE proc_portfolio_discern(p_entity_id  NUMBER,
                                   p_year_month VARCHAR2) IS

    v_column      VARCHAR2(4000);
    v_proc_id     NUMBER;
    v_count       NUMBER;
    v_error_msg   varchar2(2000);
  BEGIN
    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分入-合同分组]合同组合划分-传参不为空：proc_portfolio_discern(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

    --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分入-合同分组]合同组合划分-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 当前业务年月存在未进行评估方法的数据
    SELECT count(1)
      INTO v_count
      FROM dm_buss_cmunit_treaty
     WHERE entity_id = p_entity_id
       AND buss_year_month <= p_year_month
       --AND major_risk IN ('Y', 'P')
       AND evaluate_approach IS NULL
       AND ri_direction_code = 'I'
       AND year_month IS NULL
       AND rownum = 1;

    IF v_count > 0 THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分入-合同分组]合同组合划分-当前业务年月存在未进行评估方法的数据';
      raise_application_error(-20002,v_error_msg);

    END IF;


    -- 合约分入
    v_column := dm_pack_cmunit_common.func_get_contractcode(p_entity_id, 'G', 'TI');

    -- 根据PROC_CODE查询PROC_ID(合同组合)
    v_proc_id := dm_pack_common.func_get_procid('DM_PORTFOLIO');

    -- 更新合同组合编码
    --dbms_output.put_line(v_column);
    EXECUTE IMMEDIATE ('update  dm_buss_cmunit_treaty t'
                      ||' set portfolio_no = ' || v_column
                      ||', proc_id = ' || v_proc_id
                      ||', pl_judge_rslt = null'
                      ||', pl_judge_date = null'
                      ||', year_month = null'
                      ||', border_date = null'
                      ||', icg_no = null'
                      ||', invest_rate = null'
                      ||', invest_amount = null'
                      ||', node_state = '''|| (CASE WHEN v_column IS NULL THEN '2' ELSE '1' END)||''' '
                      ||', reason_of_failure = ''' || (CASE WHEN v_column IS NULL THEN 'DM_003' ELSE NULL END) ||''''
                      ||' where entity_id = ' || p_entity_id
                      ||' AND evaluate_approach <> ''D'' '
                      ||' AND ri_direction_code = ''I'' '
                      --||' AND major_risk IN (''Y'', ''P'') '
                      ||' AND  YEAR_MONTH IS NULL '
                      ||' AND buss_year_month <= ''' || p_year_month || ''' ');
    COMMIT;
  EXCEPTION
    WHEN OTHERS THEN

      -- 执行异常，记录异常原因
      v_error_msg := '[EXCEPTION][合约分入-合同分组]合同组合划分:'||SQLERRM;
      --意外处理
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_portfolio_discern;


  PROCEDURE proc_profit_loss_discern(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2) IS

    v_proc_id        NUMBER;
    v_data_key       VARCHAR2(200);
    v_row            NUMBER;
    v_test_plan_type VARCHAR2(10);
    v_error_msg      varchar2(2000);
    v_count          number;
  BEGIN

    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分入-合同分组]盈亏判定-传参不为空：proc_profit_loss_discern(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

       --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分入-合同分组]盈亏判定-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    --如果查询当月没有数据做，直接返回
    select count(1) into v_count
     from dm_buss_cmunit_treaty where entity_id = p_entity_id and buss_year_month <= p_year_month
     and year_month is null;

     if v_count <= 0 then
        return;
     end if;

    /*
    -- 当前业务年月所有计量单元都生成合同组合，才能进行盈亏判定
    SELECT COUNT(1)
      INTO v_row
      FROM dm_buss_cmunit_treaty
     WHERE entity_id = p_entity_id
       AND buss_year_month <= p_year_month
       AND portfolio_no IS NULL
       AND year_month IS NULL
       AND rownum = 1;
    -- 存在合同组合为空的数据
    IF v_row = 1 THEN
      --NULL;
      RETURN;
    END IF;
    */

    -- 根据PROC_CODE查询PROC_ID
    v_proc_id := dm_pack_common.func_get_procid('DM_DETERMINATION');

    v_data_key := p_entity_id || p_year_month;

    -- 合约分入
    UPDATE dm_buss_cmunit_treaty
       SET flag          = v_data_key,
           proc_id       = v_proc_id,
           node_state    = '0',
           pl_judge_date = NULL,
           pl_judge_rslt = NULL,
           year_month    = NULL,
           border_date   = NULL,
           icg_no        = NULL,
           invest_rate   = NULL,
           invest_amount = NULL
     WHERE entity_id = p_entity_id
       AND buss_year_month <= p_year_month
       AND ri_direction_code = 'I' -- 再保方向  分入
       AND portfolio_no IS NOT NULL
       --AND major_risk <> 'O'
       AND evaluate_approach <> 'D'
       AND year_month IS NULL; --未确认的评估期
    -- AND PL_JUDGE_RSLT IS NULL;
    COMMIT;

    SELECT count(1)
      INTO v_count
      FROM dm_conf_icg_profitloss_plan
     WHERE entity_id = p_entity_id
       AND business_model = 'T'
       AND business_direction = 'I';

     if v_count > 0 then
           --按盈亏判定方案配置区分: C-配置 R-规则
      SELECT test_plan_type
        INTO v_test_plan_type
        FROM dm_conf_icg_profitloss_plan
       WHERE entity_id = p_entity_id
         AND business_model = 'T'
         AND business_direction = 'I';
     end if;

   if v_test_plan_type is null then
      v_test_plan_type := 'C';
    end if;


    IF v_test_plan_type = 'C' THEN

      MERGE INTO dm_buss_cmunit_treaty t
      USING (SELECT  dcl.judge_result as judge_result,
                     t1.cm_unit_itreaty_id
               FROM dm_buss_cmunit_treaty t1
               LEFT JOIN dm_conf_contract_lossdef dcl
                 ON dcl.treaty_no = t1.treaty_no
                AND dcl.entity_id = t1.entity_id
                AND dcl.valid_is = '1'
                AND dcl.audit_state = '1'
                AND dcl.business_model = 'T'
                AND dcl.business_direction = 'I'
              WHERE t1.entity_id = p_entity_id
                AND t1.portfolio_no IS NOT NULL
                AND t1.evaluate_approach <> 'D'
                AND ri_direction_code = 'I'
                AND t1.year_month IS NULL
                AND t1.buss_year_month <= p_year_month) c
      ON (t.cm_unit_itreaty_id = c.cm_unit_itreaty_id)
      WHEN MATCHED THEN
        UPDATE
           SET t.pl_judge_rslt     = c.judge_result,
               t.pl_judge_date    =
               (CASE
                 WHEN c.judge_result = 'L' THEN
                  last_day(to_date(p_year_month, 'yyyymm'))
                 ELSE
                  NULL
               END), -- 判定结果为亏损，设置判定时间
               t.node_state        = (case when c.judge_result is not null then '1' else '2' end),
               t.reason_of_failure = (case when c.judge_result is not null then null else 'DM_001' end),
               t.exception_message = (case when c.judge_result is not null then null else 'not find config' end)
         WHERE t.entity_id = p_entity_id
           AND t.portfolio_no IS NOT NULL
           AND t.evaluate_approach <> 'D'
           AND t.ri_direction_code = 'I'
           AND t.year_month IS NULL
           AND t.buss_year_month <= p_year_month;
      COMMIT;

    ELSE
      --  初始化盈亏判断计量单元
      DELETE FROM dm_duct_treaty_profit_unit t
       WHERE EXISTS (SELECT 1
                FROM dm_buss_cmunit_treaty ct
               WHERE
                 --ct.cm_unit_itreaty_id = t.cmunit_id 应该整月都清除
                 --AND
                 ct.entity_id = p_entity_id
                 AND ct.portfolio_no IS NOT NULL -- 已生成合同组合编码
                 AND ct.year_month IS NULL --未确认的评估期
                 AND ct.major_risk <> 'O'
                 AND ct.ri_direction_code = 'I'
                 AND ct.buss_year_month <= p_year_month
                 );
      COMMIT;

      DELETE FROM dm_duct_treaty_profit_param t WHERE t.data_key = v_data_key;
      COMMIT;

      INSERT INTO dm_duct_treaty_profit_unit
        (cmunit_id,
         entity_id,
         year_month,
         data_key,
         set_no,
         portfolio_no,
         evaluate,
         loa_code,
         ri_direction_code,
         treaty_no,
         treaty_type_code,
         issue_date,
         effective_date,
         expiry_date,
         premium,
         fee_amount,
         node_state)
        SELECT cmunit_id,
               entity_id,
               p_year_month year_month,
               v_data_key data_key,
               set_no,
               portfolio_no,
               evaluate,
               loa_code,
               ri_direction_code,
               treaty_no,
               treaty_type_code,
               issue_date,
               effective_date,
               expiry_date,
               premium,
               fee_amount,
               node_state
          FROM (SELECT ct.cm_unit_itreaty_id cmunit_id,
                       ct.entity_id,
                       MAX(ct.portfolio_no) set_no,
                       MAX(ct.portfolio_no) portfolio_no,
                       MAX(ct.evaluate_approach) evaluate,
                       MAX(ct.loa_code) loa_code,
                       MAX(ct.ri_direction_code) ri_direction_code,
                       MAX(ct.treaty_no) treaty_no,
                       MAX(ct.treaty_type_code) treaty_type_code,
                       MAX(ct.issue_date) issue_date,
                       MAX(ct.effective_date) effective_date,
                       MAX(ct.expiry_date) expiry_date,
                       SUM(CASE
                         WHEN rct.treaty_type_code IN (71, 72) --XOL
                          THEN
                          rct.premium
                         ELSE
                          rct.gepi_amount --not XOL
                       END) premium,
                       SUM(CASE
                         WHEN rct.treaty_type_code IN (71, 72) --XOL
                          THEN
                          0
                         ELSE
                          rct.gepi_amount - rct.nepi_amount --not XOL
                       END) fee_amount,
                       '1' node_state
                  FROM dm_buss_cmunit_treaty ct
                  LEFT JOIN dm_reins_treaty rct
                    ON ct.entity_id = rct.entity_id
                   AND ct.treaty_no = rct.treaty_no
                   AND ct.ri_direction_code = rct.ri_direction_code
                 WHERE ct.entity_id = p_entity_id
                   AND ct.portfolio_no IS NOT NULL -- 已生成合同组合编码
                   AND ct.year_month IS NULL --未确认的评估期
                   AND ct.major_risk <> 'O'
                   AND ct.ri_direction_code = 'I'
                      --AND ICG_NO IS NULL  -- 未生成合同组
                      --  AND PL_JUDGE_RSLT IS NULL     -- 盈亏判定标识为空
                   AND ct.buss_year_month <= p_year_month
                 GROUP BY ct.entity_id,ct.cm_unit_itreaty_id--,ct.portfolio_no,rct.treaty_type_code
             );
      COMMIT;

      SELECT COUNT(1)
        INTO v_row
        FROM dm_duct_treaty_profit_unit
       WHERE data_key = v_data_key
         AND rownum = 1;

      -- 满足条件的数据为空, 结束判定
      IF v_row = 0 THEN

         return;

      END IF;


      -- 校验数据:数据完整性 及 配置表数据是否缺失
      --， NODE_STATE - 1 校验通过；  NODE_STATE -2 校验不通过；
      dm_pack_cmunit_common.proc_treaty_profit_check(p_entity_id, p_year_month, v_data_key);


      SELECT COUNT(1)
        INTO v_row
        FROM dm_duct_treaty_profit_unit
       WHERE data_key = v_data_key
         AND node_state = '2'
         AND rownum = 1;

      -- 存在数据不通过校验，更新数据状态，结束处理
      IF v_row > 0 THEN
        -- 更新判定结果及流程状态
        MERGE INTO dm_buss_cmunit_treaty t
        USING (SELECT dpu.cmunit_id,
                     nvl(dpu.node_state, '2') AS node_state,
                     nvl(dpu.fail_code, 'DM_001') AS fail_code
                FROM dm_duct_treaty_profit_unit dpu
               WHERE dpu.data_key = v_data_key
                 AND dpu.node_state = '2'
      ) c
        ON (t.cm_unit_itreaty_id = c.cmunit_id)
        WHEN MATCHED THEN
          UPDATE
             SET t.node_state        = c.node_state,
                 t.reason_of_failure = fail_code
           WHERE t.entity_id = p_entity_id
             AND t.portfolio_no IS NOT NULL
             AND t.year_month IS NULL
             AND t.evaluate_approach <> 'D'
             AND t.ri_direction_code = 'I'
             AND t.buss_year_month <= p_year_month
             AND EXISTS (SELECT 1 FROM dm_duct_treaty_profit_unit dpu
                        WHERE dpu.cmunit_id = t.cm_unit_itreaty_id
                          AND dpu.data_key = v_data_key
                          AND dpu.node_state = '2');
        COMMIT;

        --抛出已自定义异常信息【会中断事务】
        v_error_msg := '[EXCEPTION][合约分入-合同分组]盈亏判定-盈亏数据不通过校验：';
        raise_application_error(-20002,v_error_msg);

      END IF;

      -- 校验通过,处理数据
      -- 假设参数,折现因子
      dm_pack_cmunit_common.proc_treaty_profit_param(p_entity_id, p_year_month, v_data_key, 'QUOTA');

      -- 计量单元 - 现金流
      dm_pack_cmunit_common.proc_treaty_profit_param(p_entity_id, p_year_month, v_data_key, 'UNIT');

      -- 集合现金流
      dm_pack_cmunit_common.proc_treaty_profit_param(p_entity_id, p_year_month, v_data_key, 'SET');

      -- PAA
      dm_pack_cmunit_common.proc_treaty_judge_paa(p_entity_id, p_year_month, v_data_key);

      -- BBA
      dm_pack_cmunit_common.proc_treaty_judge_bba(p_entity_id, p_year_month, v_data_key);

      -- 更新判定结果及流程状态
      MERGE INTO dm_buss_cmunit_treaty t
      USING (SELECT dpu.cmunit_id,
                   nvl(dpu.node_state, '2') AS node_state,
                   judge_rslt,
                   judge_date
              FROM dm_duct_treaty_profit_unit dpu
             WHERE dpu.data_key = v_data_key) c
      ON (t.cm_unit_itreaty_id = c.cmunit_id)
      WHEN MATCHED THEN
        UPDATE
           SET t.pl_judge_rslt     = c.judge_rslt,
               t.pl_judge_date     = c.judge_date,
               t.node_state        = c.node_state,
               t.reason_of_failure = (case when c.node_state = '2' then 'DM_001' else null end)
         WHERE t.entity_id = p_entity_id
           AND t.portfolio_no IS NOT NULL
           AND t.evaluate_approach <> 'D'
           AND t.year_month IS NULL
           AND t.buss_year_month <= p_year_month;
      COMMIT;

    END IF;
    proc_buss_cmunit_log(p_entity_id, p_year_month, v_data_key || '-' || v_test_plan_type, 'proc_profit_loss_discern', '1', NULL);

  EXCEPTION
    WHEN OTHERS THEN

      proc_buss_cmunit_log(p_entity_id, p_year_month, v_data_key || '-' || v_test_plan_type, 'proc_profit_loss_discern', '2', SQLERRM);
       --意外处理
      v_error_msg := '[EXCEPTION][合约分入-合同分组]盈亏判定:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_profit_loss_discern;


  PROCEDURE proc_icg_fixed(p_entity_id  NUMBER,
                           p_year_month VARCHAR2) IS
    v_end_date   TIMESTAMP; -- 当前业务年月的下个月第一天
    v_error_msg  varchar2(2000);
  BEGIN
    /*-- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      RETURN;
    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      RETURN;
    END IF;*/
    -- 根据proc_code查询proc_id
    --v_proc_id := dm_pack_common.func_get_procid('DM_CONTRACTGROUP');

    v_end_date := add_months(to_date(p_year_month, 'yyyymm'), 1);

    --dbms_output.put_line('icg fixed: ' ||v_end_date);

    --业务时间的更新
    MERGE INTO dm_buss_cmunit_treaty cma
    USING (SELECT t.cm_unit_itreaty_id AS cm_unit_itreaty_id,
                  t.d1,
                  t.d2,
                  t.d3,
                  greatest((CASE
                             WHEN t.d2 IS NULL THEN
                              NULL --没有账单数据合同未确认
                             WHEN t.d3 IS NULL THEN
                              t.d2
                             WHEN t.d2 > t.d3 THEN
                              t.d3
                             ELSE
                              t.d2
                           END), t.d1) AS d4 --greatest(least(D2,D3),D1) D4
             FROM (SELECT cmt.cm_unit_itreaty_id,
                          MIN(rt.issue_date) AS d1,
                          MIN(rt.effective_date) AS d3,
                          (CASE
                            WHEN MIN(rb.statement_payment_date) IS NULL THEN
                             NULL --没有账单数据合同未确认
                            WHEN MIN(fin.actual_payment_date) IS NULL THEN
                             MIN(rb.statement_payment_date)
                            WHEN MIN(fin.actual_payment_date) > MIN(rb.statement_payment_date) THEN
                             MIN(rb.statement_payment_date)
                            ELSE
                             MIN(fin.actual_payment_date)
                          END) AS d2
                     FROM dm_buss_cmunit_treaty cmt
                     LEFT JOIN dm_reins_treaty rt --获取D1:reins_treaty.issue_date,D3:reins_treaty.effective_date
                       ON cmt.entity_id = rt.entity_id
                      AND cmt.treaty_no = rt.treaty_no
                      AND cmt.ri_direction_code = rt.ri_direction_code
                     LEFT JOIN dm_reins_bill rb
                       ON rb.entity_id = cmt.entity_id
                      AND rb.treaty_no = cmt.treaty_no
                      AND rb.statement_payment_date < v_end_date
                      AND rb.treaty_no IS NOT NULL
                     LEFT JOIN dm_acc_payment fin
                       ON fin.entity_id = cmt.entity_id
                      AND fin.treaty_no = cmt.treaty_no
                      AND fin.est_payment_seq_no = 1
                      --AND fin.actual_payment_date < v_end_date--不需要看是否存在评估期内，有数据就可以 2023/03/08
                      AND fin.treaty_no IS NOT NULL
                    WHERE cmt.entity_id = p_entity_id
                      AND cmt.pl_judge_rslt IS NOT NULL -- 盈亏判定
                      AND cmt.year_month IS NULL -- 未生成合同组
                      AND cmt.ri_direction_code = 'I'
                      AND cmt.buss_year_month <= p_year_month
                    GROUP BY cmt.cm_unit_itreaty_id--,cmt.treaty_no
                   ) t
     ) cmb
      ON (cma.cm_unit_itreaty_id = cmb.cm_unit_itreaty_id)
    WHEN MATCHED THEN
      UPDATE
         SET cma.d1 = cmb.d1,
             cma.d2 = cmb.d2,
             cma.d3 = cmb.d3,
             cma.d4 = cmb.d4,
             border_date = (CASE
                              WHEN cmb.d4 >= v_end_date THEN
                               NULL --v_end_date下个月第一天
                              ELSE
                               cmb.d4
                            END)
             --proc_id = (CASE WHEN cmb.d4 >= v_end_date THEN NULL ELSE v_proc_id END),
             --node_state = (CASE WHEN cmb.d4 >= v_end_date THEN NULL ELSE '1' END),
             --invest_rate   = NULL,
             --invest_amount = NULL
       WHERE cma.entity_id = p_entity_id
         AND cma.pl_judge_rslt IS NOT NULL -- 盈亏判定
         AND cma.evaluate_approach <> 'D'
         AND cma.year_month IS NULL -- 未生成合同组
         AND cma.ri_direction_code = 'I'
         AND cma.buss_year_month <= p_year_month;

    COMMIT;
      dbms_output.put_line('icg fixed' || 'COMMIT');

  EXCEPTION
    WHEN OTHERS THEN

       --意外处理
      v_error_msg := '[EXCEPTION][合约分入-合同分组]合同分组-获取合同确认日期业务数据:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_icg_fixed;

  PROCEDURE proc_icg_discern(p_entity_id  NUMBER,
                             p_year_month VARCHAR2) IS

    v_column     VARCHAR2(4000);
    v_proc_id    NUMBER;
    v_error_msg  varchar2(2000);
  BEGIN
    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分入-合同分组]合同分组-传参不为空：proc_icg_discern(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分入-合同分组]合同分组-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    --  合同确认
    proc_icg_fixed(p_entity_id, p_year_month);

    -- 根据proc_code查询proc_id
    v_proc_id := dm_pack_common.func_get_procid('DM_CONTRACTGROUP');

    -- 合约分入
    v_column := dm_pack_cmunit_common.func_get_contractcode(p_entity_id, 'C', 'TI');

    -- 根据proc_code查询proc_id
    v_proc_id := dm_pack_common.func_get_procid('DM_CONTRACTGROUP');

    -- 合约分入
    v_column := dm_pack_cmunit_common.func_get_contractcode(p_entity_id, 'C', 'TI');

    -- 更新合同组合编码
    EXECUTE IMMEDIATE ('update dm_buss_cmunit_treaty t'
                      ||' set icg_no = (case when border_date is not null then ' || v_column || ' else null end),'
                      ||' proc_id=' || v_proc_id || ','
                      ||' node_state= '''|| (CASE WHEN v_column IS NULL THEN '2' ELSE '1' END) ||''','
                      ||' reason_of_failure = ''' || (CASE WHEN v_column IS NULL THEN 'DM_003' ELSE NULL END) ||''','
                      ||' invest_rate = null ,'
                      ||' invest_amount = null '
                      ||' where entity_id = ' || p_entity_id
                      ||' AND ri_direction_code = ''I'' '
                      ||' AND evaluate_approach <> ''D'' '
                      ||' AND pl_judge_rslt IS NOT NULL'
                      ||' AND year_month IS NULL '
                      ||' AND buss_year_month <= ''' || p_year_month || ''' ');
    COMMIT;

EXCEPTION
    WHEN OTHERS THEN

       --意外处理
      v_error_msg := '[EXCEPTION][合约分入-合同分组]合同分组:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_icg_discern;

  PROCEDURE proc_icg_group(p_entity_id  NUMBER,
                           p_year_month VARCHAR2) IS

  BEGIN

    --  生成合同组编码
    proc_icg_discern(p_entity_id, p_year_month);

  END proc_icg_group;

  PROCEDURE proc_investment_separate(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2) IS

    v_proc_id     NUMBER;
    v_data_key    VARCHAR2(200);
    v_row         NUMBER;
    v_current_day DATE; -- 当前业务年月第一天
    v_year_month  VARCHAR2(200);
    v_error_msg   varchar2(2000);
  BEGIN

    -- 条件不满足,结束判定
    IF p_entity_id IS NULL
       AND p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分入-合同分组]投成拆分-传参不为空：proc_investment_separate(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 限定只处理 处于处理中的业务年月
    SELECT year_month
      INTO v_year_month
      FROM dm_conf_bussperiod
     WHERE entity_id = p_entity_id
       AND year_month = p_year_month
       AND period_state = '2' -- 处理中
       AND valid_is = '1'
       AND rownum = 1;

    -- 条件不满足,结束判定
    IF v_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分入-合同分组]投成拆分-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 当前业务年月所有计量单元都生成合同分组，才能进行投成拆分
    SELECT 1
      INTO v_row
      FROM dm_buss_cmunit_treaty
     WHERE entity_id = p_entity_id
       AND buss_year_month <= p_year_month
       AND icg_no IS NOT NULL
       AND invest_rate IS NULL
       AND invest_amount IS NULL
       AND rownum = 1;
    -- 存在合同分组为空的数据
    IF v_row != 1 THEN
      RETURN;
    END IF;

    -- 根据PROC_CODE查询PROC_ID
    v_proc_id := dm_pack_common.func_get_procid('DM_INVESTMENT_COST');
    IF v_proc_id IS NULL THEN

       --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分入-合同分组]投成拆分-存在合同分组为空的数据：';
      raise_application_error(-20002,v_error_msg);

    END IF;
    --不需要做
    RETURN;
    /*UPDATE DM_BUSS_CMUNIT_TREATY CMTR
    SET INVEST_RATE = COALESCE(TO_NUMBER(BCQ.QUOTA_VALUE,'999.999999'),0) ,INVEST_AMOUNT = COALESCE(PREMIUM,0)*COALESCE(TO_NUMBER(BCQ.QUOTA_VALUE,'999.999999'),0),
      PROC_ID = V_PROC_ID,NODE_STATE= '1' ,
      REASON_OF_FAILURE = NULL
    FROM
      BPLUSER.BBS_CONF_QUOTA BCQ,DM_REINS_CONF_TREATY RCT
    WHERE
      BCQ.QUOTA_DEF_ID = ( SELECT QUOTA_DEF_ID FROM BPLUSER.BBS_CONF_QUOTA_DEF WHERE QUOTA_CODE = 'QR001' )--取投成拆分字段
      AND BCQ.BUSINESS_MODEL = 'T'--取再保临分的
      AND CMTR.entity_id = BCQ.entity_id
      AND CMTR.entity_id = RCT.entity_id
      AND CMTR.TREATY_NO = RCT.TREATY_NO
      AND RCT.treaty_class_code = BCQ.treaty_class_code
      AND BCQ.VALID_IS = '1'
      AND BCQ.audit_state = '1'
      AND CMTR.MAJOR_RISK IS NOT NULL
      AND CMTR.EVALUATE_APPROACH IS NOT NULL
      AND CMTR.PL_JUDGE_RSLT IS NOT NULL
      AND CMTR.BORDER_DATE IS NOT NULL
      AND CMTR.ICG_NO IS NOT NULL
      AND CMTR.ri_direction_code = 'I'
      --AND CMTR.INVEST_RATE IS NULL
      --AND CMTR.INVEST_AMOUNT IS NULL
      AND CMTR.entity_id = p_entity_id
      AND CMTR.YEAR_MONTH IS NULL
      AND CMTR.BUSS_YEAR_MONTH <= P_YEAR_MONTH;*/

    --WLI未有投成拆分的功能
    IF v_proc_id is NULL
       OR v_proc_id = '' THEN
      RETURN;
    END IF;

  EXCEPTION
    WHEN OTHERS THEN

      --意外处理
      v_error_msg := '[EXCEPTION][合约分入-合同分组]投成拆分:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_investment_separate;

  PROCEDURE proc_contract_group_confirm(p_entity_id  NUMBER,
                                        p_year_month VARCHAR2) IS

  v_error_msg varchar2(2000);

  BEGIN

    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分入-合同分组]确认-传参不为空：proc_contract_group_confirm(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分入-合同分组]确认-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);


    END IF;

    UPDATE dm_buss_cmunit_treaty
       SET year_month = p_year_month
     WHERE entity_id = p_entity_id
       AND buss_year_month <= p_year_month
       AND year_month IS NULL
       AND icg_no IS NOT NULL
       --AND invest_rate IS NOT NULL
       --AND invest_amount IS NOT NULL
       AND ri_direction_code = 'I';
    COMMIT;

    proc_backup_profit_unit_result(p_entity_id, p_year_month);

  EXCEPTION
    WHEN OTHERS THEN

     --意外处理
      v_error_msg := '[EXCEPTION][合约分入-合同分组]确认:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);


  END proc_contract_group_confirm;

  PROCEDURE proc_backup_profit_unit_result(p_entity_id  NUMBER,
                                           p_year_month VARCHAR2) IS
      v_execute VARCHAR2(100);
      v_error_msg varchar2(2000);
  BEGIN

    --按盈亏判定方案配置区分: C-配置 R-规则
    SELECT test_plan_type
      INTO v_execute
      FROM dm_conf_icg_profitloss_plan
     WHERE entity_id = p_entity_id
       AND business_model||business_direction = 'TI';

    IF v_execute = 'R' THEN

      -- backup profit unit result and ref. param value
      dm_pack_cmunit_common.proc_backup_profit_unit_result_tab(p_entity_id,p_year_month,'dm_duct_treaty_profit_unit');
      dm_pack_cmunit_common.proc_backup_profit_unit_result_tab(p_entity_id,p_year_month,'dm_duct_treaty_profit_param');
      dm_pack_cmunit_common.proc_backup_profit_unit_result_tab(p_entity_id,p_year_month,'dm_duct_treaty_profit_amount');

    END IF;

  EXCEPTION
    WHEN OTHERS THEN
         --意外处理
      v_error_msg := '[EXCEPTION][合约分入-合同分组]盈亏轨迹备份:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_backup_profit_unit_result;

  PROCEDURE proc_buss_cmunit_log(p_entity_id  NUMBER,
                                 p_year_month VARCHAR2,
                                 p_trace_no VARCHAR2,
                                 p_trace_code VARCHAR2,
                                 p_trace_status VARCHAR2,
                                 p_trace_msg VARCHAR2) IS
  BEGIN
    INSERT INTO DM_LOG_BUSS_CMUNIT
        (entity_id,
         year_month,
         buss_model,
         trace_no,
         trace_code,
         trace_status,
         trace_msg,
         create_time)
      VALUES
        (p_entity_id,
         p_year_month,
         'TI',
         p_trace_no,
         p_trace_code,
         p_trace_status,
         p_trace_msg,
         SYSDATE);
     COMMIT;

  EXCEPTION
    --意外处理
      WHEN OTHERS THEN
      NULL;
  END proc_buss_cmunit_log;

END dm_pack_cmunit_treaty_inward;
/
