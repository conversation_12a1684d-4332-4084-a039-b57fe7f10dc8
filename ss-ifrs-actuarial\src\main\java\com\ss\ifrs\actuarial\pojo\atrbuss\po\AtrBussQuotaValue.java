/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-08-11 10:56:32
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-08-11 10:56:32<br/>
 * Description: 假设值预存表（配置情况）<br/>
 * Table Name: ATR_BUSS_QUOTA_VALUE<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "假设值预存表（配置情况）")
public class AtrBussQuotaValue implements Serializable {
    /**
     * Database column: ATR_BUSS_QUOTA_VALUE.ID
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: ATR_BUSS_QUOTA_VALUE.ACTION_NO
     * Database remarks: 执行编号
     */
    @ApiModelProperty(value = "执行编号", required = true)
    private String actionNo;

    /**
     * Database column: ATR_BUSS_QUOTA_VALUE.QUOTA_CODE
     * Database remarks: 假设值代码
     */
    @ApiModelProperty(value = "假设值代码", required = true)
    private String quotaCode;

    /**
     * Database column: ATR_BUSS_QUOTA_VALUE.MODEL_DEF_ID
     * Database remarks: 计量模型
     */
    @ApiModelProperty(value = "计量模型", required = true)
    private Long modelDefId;

    /**
     * Database column: ATR_BUSS_QUOTA_VALUE.DIMENSION
     * Database remarks: 维度|G-合同组合、C-合同组、U-保单
     */
    @ApiModelProperty(value = "维度|G-合同组合、C-合同组、U-保单", required = true)
    private String dimension;

    /**
     * Database column: ATR_BUSS_QUOTA_VALUE.DIMENSION_VALUE
     * Database remarks: 维度值
     */
    @ApiModelProperty(value = "维度值", required = false)
    private String dimensionValue;

    /**
     * Database column: ATR_BUSS_QUOTA_VALUE.DEV_NO
     * Database remarks: 发展期
     */
    @ApiModelProperty(value = "发展期", required = false)
    private Long devNo;

    /**
     * Database column: ATR_BUSS_QUOTA_VALUE.QUOTA_VALUE
     * Database remarks: 假设值
     */
    @ApiModelProperty(value = "假设值", required = false)
    private BigDecimal quotaValue;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public String getQuotaCode() {
        return quotaCode;
    }

    public void setQuotaCode(String quotaCode) {
        this.quotaCode = quotaCode;
    }

    public Long getModelDefId() {
        return modelDefId;
    }

    public void setModelDefId(Long modelDefId) {
        this.modelDefId = modelDefId;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getDimensionValue() {
        return dimensionValue;
    }

    public void setDimensionValue(String dimensionValue) {
        this.dimensionValue = dimensionValue;
    }

    public Long getDevNo() {
        return devNo;
    }

    public void setDevNo(Long devNo) {
        this.devNo = devNo;
    }

    public BigDecimal getQuotaValue() {
        return quotaValue;
    }

    public void setQuotaValue(BigDecimal quotaValue) {
        this.quotaValue = quotaValue;
    }
}