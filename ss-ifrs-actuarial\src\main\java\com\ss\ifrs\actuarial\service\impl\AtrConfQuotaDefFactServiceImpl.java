package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.dao.conf.AtrConfQuotaDefFactDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfQuotaDefFactHisDao;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDefFact;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDefFactHis;
import com.ss.ifrs.actuarial.service.AtrConfQuotaDefFactService;
import com.ss.library.utils.ClassUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName AtrConfQuotaDefFactServiceImpl
 * <AUTHOR>
 * @Date 2022/07/04
 **/
@Service("bbsConfQuotaDefFactService")
@Transactional
public class AtrConfQuotaDefFactServiceImpl implements AtrConfQuotaDefFactService {

    @Autowired
    AtrConfQuotaDefFactDao bbsConfQuotaDefFactDao;

    @Autowired
    AtrConfQuotaDefFactHisDao bbsConfQuotaDefFactHisDao;
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, readOnly = false)
    public void save(AtrConfQuotaDefFact bbsConfQuotaDefFact) {
        bbsConfQuotaDefFactDao.save(bbsConfQuotaDefFact);
        AtrConfQuotaDefFactHis bbsConfQuotaDefFactHis = ClassUtil.convert(bbsConfQuotaDefFact, AtrConfQuotaDefFactHis.class);
        bbsConfQuotaDefFactHisDao.save(bbsConfQuotaDefFactHis);
    }

    public void delete(Long quotaDefId) {
        Map<String,Object> map = new HashMap<>();
        map.put("quotaDefId",quotaDefId);
        bbsConfQuotaDefFactDao.deleteByMap(map);
        List<AtrConfQuotaDefFact> list =bbsConfQuotaDefFactDao.findByDefId(quotaDefId);
        for(AtrConfQuotaDefFact po: list) {
            AtrConfQuotaDefFactHis bbsConfQuotaDefFactHis = ClassUtil.convert(po, AtrConfQuotaDefFactHis.class);
            bbsConfQuotaDefFactHis.setSerialNo(ObjectUtils.isEmpty(bbsConfQuotaDefFactHis.getSerialNo()) ? 1 :  bbsConfQuotaDefFactHis.getSerialNo()+1);
            bbsConfQuotaDefFactHisDao.save(bbsConfQuotaDefFactHis);
        }
    }
}
