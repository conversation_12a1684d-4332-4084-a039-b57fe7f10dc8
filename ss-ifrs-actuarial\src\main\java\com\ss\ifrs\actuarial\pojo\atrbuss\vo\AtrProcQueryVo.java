
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import java.io.Serializable;

/**
 *
 * <br/>
 * Remark:LIC过程数据VO
 * <br/>
 */

public class AtrProcQueryVo implements Serializable {
	
	private static final long serialVersionUID = 1L;
	 
    /** 主键 */
    private Long licMainId;

    /** 事故年月 */
    private String yearMonth;
    
    /** 发展期 */
    private String devYearMonth;
    
    /** 发展期 */
    private String icgNo;
    
    /** 数据类型 */
    private String dataType;

    /** 计算节点 */
    private String countNode;

	public Long getLicMainId() {
		return licMainId;
	}

	public void setLicMainId(Long licMainId) {
		this.licMainId = licMainId;
	}

	public String getYearMonth() {
		return yearMonth;
	}

	public void setYearMonth(String yearMonth) {
		this.yearMonth = yearMonth;
	}

	public String getDevYearMonth() {
		return devYearMonth;
	}

	public void setDevYearMonth(String devYearMonth) {
		this.devYearMonth = devYearMonth;
	}

	public String getIcgNo() {
		return icgNo;
	}

	public void setIcgNo(String icgNo) {
		this.icgNo = icgNo;
	}

	public String getDataType() {
		return dataType;
	}

	public void setDataType(String dataType) {
		this.dataType = dataType;
	}

	public String getCountNode() {
		return countNode;
	}

	public void setCountNode(String countNode) {
		this.countNode = countNode;
	}
    
   
}