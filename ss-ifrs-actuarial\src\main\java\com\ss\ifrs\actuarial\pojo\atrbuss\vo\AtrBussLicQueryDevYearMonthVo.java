/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-03-09 10:18:30
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName: AtrBussLicQueryDevYearMonthVo
 * @Description: Lic计量查询数据返回对象-发展年月数据
 * @Author: yinxh.
 * @CreateDate: 2021/3/10 18:32
 * @Version: 1.0
 */
public class AtrBussLicQueryDevYearMonthVo implements Serializable {
    
	 /**
     * 发展期
     */
    private int devYearMonth;
    
    /**
     * 数据类型：R-实际金额；S-预期金额
     */
    private String dataType;
    
    /**
     * 合同组金额
     */
    private BigDecimal amount;
    
    private static final long serialVersionUID = 1L;
    
    public int getDevYearMonth() {
		return devYearMonth;
	}

	public void setDevYearMonth(int devYearMonth) {
		this.devYearMonth = devYearMonth;
	}

	public String getDataType() {
        return dataType;
    }
    
    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
    
    public BigDecimal getAmount() {
        return amount;
    }
    
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
}