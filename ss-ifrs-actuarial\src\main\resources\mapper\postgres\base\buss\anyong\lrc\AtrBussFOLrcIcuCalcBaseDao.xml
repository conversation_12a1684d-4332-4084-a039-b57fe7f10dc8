<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-03-08 10:07:11 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussFOLrcIcuCalcDao">
  <!-- 本文件由GIS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussFOLrcIcuCalc">
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="TASK_CODE" property="taskCode" jdbcType="VARCHAR" />
    <result column="CALC_TYPE" property="calcType" jdbcType="VARCHAR" />
    <result column="DATA_KEY" property="dataKey" jdbcType="DECIMAL" />
    <result column="entity_id" property="entityId" jdbcType="DECIMAL" />
    <result column="DEPT_ID" property="deptId" jdbcType="DECIMAL" />
    <result column="RI_POLICY_NO" property="riPolicyNo" jdbcType="VARCHAR" />
    <result column="ri_endorse_seq_no" property="riEndorseSeqNo" jdbcType="VARCHAR" />
    <result column="correction_seq_no" property="correctionSeqNo" jdbcType="DECIMAL" />
    <result column="CURRENCY_CODE" property="currencyCode" jdbcType="VARCHAR" />
    <result column="YEAR_MONTH" property="yearMonth" jdbcType="VARCHAR" />
    <result column="PORTFOLIO_NO" property="portfolioNo" jdbcType="VARCHAR" />
    <result column="ICG_NO" property="icgNo" jdbcType="VARCHAR" />
    <result column="EVALUATE_APPROACH" property="evaluateApproach" jdbcType="VARCHAR" />
    <result column="MODEL_DEF_ID" property="modelDefId" jdbcType="DECIMAL" />
    <result column="LOA_CODE" property="loaCode" jdbcType="VARCHAR" />
    <result column="CMUNIT_NO" property="cmunitNo" jdbcType="VARCHAR" />
    <result column="PRODUCT_CODE" property="productCode" jdbcType="VARCHAR" />
    <result column="EVALUATE_DATE" property="evaluateDate" jdbcType="TIMESTAMP" />
    <result column="CONTRACT_DATE" property="contractDate" jdbcType="TIMESTAMP" />
    <result column="EFFECTIVE_DATE_IN_DATE" property="effectiveDateInDate" jdbcType="TIMESTAMP" />
    <result column="APPROVAL_DATE_IN_DATE" property="approvalDateInDate" jdbcType="TIMESTAMP" />
    <result column="EXPIRY_DATE_IN_DATE" property="expiryDateInDate" jdbcType="TIMESTAMP" />
    <result column="EFFECTIVE_DATE_BOM" property="effectiveDateBom" jdbcType="TIMESTAMP" />
    <result column="EXPIRY_DATE_EOM" property="expiryDateEom" jdbcType="TIMESTAMP" />
    <result column="payment_frequency_code" property="paymentFrequencyCode" jdbcType="VARCHAR" />
    <result column="payment_frequency_no" property="paymentFrequencyNo" jdbcType="DECIMAL" />
    <result column="GROSS_PREMIUM" property="grossPremium" jdbcType="DECIMAL" />
    <result column="COVERAGE_AMOUNT" property="coverageAmount" jdbcType="DECIMAL" />
    <result column="PASSED_DATES" property="passedDates" jdbcType="DECIMAL" />
    <result column="PASSED_MONTHS" property="passedMonths" jdbcType="DECIMAL" />
    <result column="REMAINING_MONTHS" property="remainingMonths" jdbcType="DECIMAL" />
    <result column="REMAINING_PREM_TERM_PE" property="remainingPremTermPe" jdbcType="DECIMAL" />
    <result column="REMAINING_MONTHS_FUTURE" property="remainingMonthsFuture" jdbcType="DECIMAL" />
    <result column="REMAINING_PREM_TERM_CB" property="remainingPremTermCb" jdbcType="DECIMAL" />
    <result column="PAYMENT_QUARTER" property="paymentQuarter" jdbcType="DECIMAL" />
    <result column="ed_premium_per_coverage_day" property="edPremiumPerCoverageDay" jdbcType="DECIMAL" />
    <result column="ED_PREMIUM" property="edPremium" jdbcType="DECIMAL" />
    <result column="PRI_CUR_END_REMAIN_CSM_RATE" property="priCurEndRemainCsmRate" jdbcType="DECIMAL" />
    <result column="PRI_UNTIL_REPORT_REMAIN_CSM_RATE" property="priUntilReportRemainCsmRate" jdbcType="DECIMAL" />
    <result column="CUMULATIVE_ED_RATE" property="cumulativeEdRate" jdbcType="DECIMAL" />
    <result column="CUR_END_REMAIN_CSM_RATE" property="curEndRemainCsmRate" jdbcType="DECIMAL" />
    <result column="UNTIL_REPORT_REMAIN_CSM_RATE" property="untilReportRemainCsmRate" jdbcType="DECIMAL" />
    <result column="ELR" property="elr" jdbcType="DECIMAL" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    ID, TASK_CODE, CALC_TYPE, DATA_KEY, entity_id, DEPT_ID, RE_POLICY_NO, RE_ENDORSE_SEQ_NO,
    CORRECTED_NUMBER, CURRENCY_CODE, YEAR_MONTH, PORTFOLIO_NO, ICG_NO, EVALUATE_APPROACH,
    MODEL_DEF_ID, LOA_CODE, CMUNIT_NO, PRODUCT_CODE, EVALUATE_DATE, CONTRACT_DATE, EFFECTIVE_DATE_IN_DATE, 
    APPROVAL_DATE_IN_DATE, EXPIRY_DATE_IN_DATE, EFFECTIVE_DATE_BOM, EXPIRY_DATE_EOM, payment_frequency_code,
    payment_frequency_no, GROSS_PREMIUM, COVERAGE_AMOUNT, PASSED_DATES, PASSED_MONTHS, REMAINING_MONTHS,
    REMAINING_PREM_TERM_PE, REMAINING_MONTHS_FUTURE, REMAINING_PREM_TERM_CB, PAYMENT_QUARTER, 
    ed_premium_per_coverage_day, ED_PREMIUM, PRI_CUR_END_REMAIN_CSM_RATE, PRI_UNTIL_REPORT_REMAIN_CSM_RATE,
    CUMULATIVE_ED_RATE, CUR_END_REMAIN_CSM_RATE, UNTIL_REPORT_REMAIN_CSM_RATE, ELR
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="id != null ">
          and ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="taskCode != null and taskCode != ''">
          and TASK_CODE = #{taskCode,jdbcType=VARCHAR}
      </if>
      <if test="calcType != null and calcType != ''">
          and CALC_TYPE = #{calcType,jdbcType=VARCHAR}
      </if>
      <if test="dataKey != null ">
          and DATA_KEY = #{dataKey,jdbcType=DECIMAL}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="deptId != null ">
          and DEPT_ID = #{deptId,jdbcType=DECIMAL}
      </if>
      <if test="rePolicyNo != null and rePolicyNo != ''">
          and RE_POLICY_NO = #{rePolicyNo,jdbcType=VARCHAR}
      </if>
      <if test="reEndorseSeqNo != null and reEndorseSeqNo != ''">
          and RE_ENDORSE_SEQ_NO = #{reEndorseSeqNo,jdbcType=VARCHAR}
      </if>
      <if test="correctedNumber != null ">
          and CORRECTED_NUMBER = #{correctedNumber,jdbcType=DECIMAL}
      </if>
      <if test="currencyCode != null and currencyCode != ''">
          and CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
          and YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="portfolioNo != null and portfolioNo != ''">
          and PORTFOLIO_NO = #{portfolioNo,jdbcType=VARCHAR}
      </if>
      <if test="icgNo != null and icgNo != ''">
          and ICG_NO = #{icgNo,jdbcType=VARCHAR}
      </if>
      <if test="evaluateApproach != null and evaluateApproach != ''">
          and EVALUATE_APPROACH = #{evaluateApproach,jdbcType=VARCHAR}
      </if>
      <if test="modelDefId != null ">
          and MODEL_DEF_ID = #{modelDefId,jdbcType=DECIMAL}
      </if>
      <if test="loaCode != null and loaCode != ''">
          and LOA_CODE = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="cmunitNo != null and cmunitNo != ''">
          and CMUNIT_NO = #{cmunitNo,jdbcType=VARCHAR}
      </if>
      <if test="productCode != null and productCode != ''">
          and PRODUCT_CODE = #{productCode,jdbcType=VARCHAR}
      </if>
      <if test="evaluateDate != null ">
          and EVALUATE_DATE = #{evaluateDate,jdbcType=TIMESTAMP}
      </if>
      <if test="contractDate != null ">
          and CONTRACT_DATE = #{contractDate,jdbcType=TIMESTAMP}
      </if>
      <if test="effectiveDateInDate != null ">
          and EFFECTIVE_DATE_IN_DATE = #{effectiveDateInDate,jdbcType=TIMESTAMP}
      </if>
      <if test="checkDateInDate != null ">
          and APPROVAL_DATE_IN_DATE = #{checkDateInDate,jdbcType=TIMESTAMP}
      </if>
      <if test="expiryDateInDate != null ">
          and EXPIRY_DATE_IN_DATE = #{expiryDateInDate,jdbcType=TIMESTAMP}
      </if>
      <if test="effectiveDateBom != null ">
          and EFFECTIVE_DATE_BOM = #{effectiveDateBom,jdbcType=TIMESTAMP}
      </if>
      <if test="expiryDateEom != null ">
          and EXPIRY_DATE_EOM = #{expiryDateEom,jdbcType=TIMESTAMP}
      </if>
      <if test="premiumFrequency != null and premiumFrequency != ''">
          and payment_frequency_code = #{premiumFrequency,jdbcType=VARCHAR}
      </if>
      <if test="premiumTerm != null ">
          and payment_frequency_no = #{premiumTerm,jdbcType=DECIMAL}
      </if>
      <if test="grossPremium != null ">
          and GROSS_PREMIUM = #{grossPremium,jdbcType=DECIMAL}
      </if>
      <if test="coverageAmount != null ">
          and COVERAGE_AMOUNT = #{coverageAmount,jdbcType=DECIMAL}
      </if>
      <if test="passedDates != null ">
          and PASSED_DATES = #{passedDates,jdbcType=DECIMAL}
      </if>
      <if test="passedMonths != null ">
          and PASSED_MONTHS = #{passedMonths,jdbcType=DECIMAL}
      </if>
      <if test="remainingMonths != null ">
          and REMAINING_MONTHS = #{remainingMonths,jdbcType=DECIMAL}
      </if>
      <if test="remainingPremTermPe != null ">
          and REMAINING_PREM_TERM_PE = #{remainingPremTermPe,jdbcType=DECIMAL}
      </if>
      <if test="remainingMonthsFuture != null ">
          and REMAINING_MONTHS_FUTURE = #{remainingMonthsFuture,jdbcType=DECIMAL}
      </if>
      <if test="remainingPremTermCb != null ">
          and REMAINING_PREM_TERM_CB = #{remainingPremTermCb,jdbcType=DECIMAL}
      </if>
      <if test="paymentQuarter != null ">
          and PAYMENT_QUARTER = #{paymentQuarter,jdbcType=DECIMAL}
      </if>
      <if test="edPremiumPerCoverageDay != null ">
          and ed_premium_per_coverage_day = #{edPremiumPerCoverageDay,jdbcType=DECIMAL}
      </if>
      <if test="edPremium != null ">
          and ED_PREMIUM = #{edPremium,jdbcType=DECIMAL}
      </if>
      <if test="priCurEndRemainCsmRate != null ">
          and PRI_CUR_END_REMAIN_CSM_RATE = #{priCurEndRemainCsmRate,jdbcType=DECIMAL}
      </if>
      <if test="priUntilReportRemainCsmRate != null ">
          and PRI_UNTIL_REPORT_REMAIN_CSM_RATE = #{priUntilReportRemainCsmRate,jdbcType=DECIMAL}
      </if>
      <if test="cumulativeEdRate != null ">
          and CUMULATIVE_ED_RATE = #{cumulativeEdRate,jdbcType=DECIMAL}
      </if>
      <if test="curEndRemainCsmRate != null ">
          and CUR_END_REMAIN_CSM_RATE = #{curEndRemainCsmRate,jdbcType=DECIMAL}
      </if>
      <if test="untilReportRemainCsmRate != null ">
          and UNTIL_REPORT_REMAIN_CSM_RATE = #{untilReportRemainCsmRate,jdbcType=DECIMAL}
      </if>
      <if test="elr != null ">
          and ELR = #{elr,jdbcType=DECIMAL}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.id != null ">
          and ID = #{condition.id,jdbcType=DECIMAL}
      </if>
      <if test="condition.taskCode != null and condition.taskCode != ''">
          and TASK_CODE = #{condition.taskCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.calcType != null and condition.calcType != ''">
          and CALC_TYPE = #{condition.calcType,jdbcType=VARCHAR}
      </if>
      <if test="condition.dataKey != null ">
          and DATA_KEY = #{condition.dataKey,jdbcType=DECIMAL}
      </if>
      <if test="condition.entityId != null ">
          and entity_id = #{condition.entityId,jdbcType=DECIMAL}
      </if>
      <if test="condition.deptId != null ">
          and DEPT_ID = #{condition.deptId,jdbcType=DECIMAL}
      </if>
      <if test="condition.rePolicyNo != null and condition.rePolicyNo != ''">
          and RE_POLICY_NO = #{condition.rePolicyNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.reEndorseSeqNo != null and condition.reEndorseSeqNo != ''">
          and RE_ENDORSE_SEQ_NO = #{condition.reEndorseSeqNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.correctedNumber != null ">
          and CORRECTED_NUMBER = #{condition.correctedNumber,jdbcType=DECIMAL}
      </if>
      <if test="condition.currencyCode != null and condition.currencyCode != ''">
          and CURRENCY_CODE = #{condition.currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.yearMonth != null and condition.yearMonth != ''">
          and YEAR_MONTH = #{condition.yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="condition.portfolioNo != null and condition.portfolioNo != ''">
          and PORTFOLIO_NO = #{condition.portfolioNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.icgNo != null and condition.icgNo != ''">
          and ICG_NO = #{condition.icgNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.evaluateApproach != null and condition.evaluateApproach != ''">
          and EVALUATE_APPROACH = #{condition.evaluateApproach,jdbcType=VARCHAR}
      </if>
      <if test="condition.modelDefId != null ">
          and MODEL_DEF_ID = #{condition.modelDefId,jdbcType=DECIMAL}
      </if>
      <if test="condition.loaCode != null and condition.loaCode != ''">
          and LOA_CODE = #{condition.loaCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.cmunitNo != null and condition.cmunitNo != ''">
          and CMUNIT_NO = #{condition.cmunitNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.productCode != null and condition.productCode != ''">
          and PRODUCT_CODE = #{condition.productCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.evaluateDate != null ">
          and EVALUATE_DATE = #{condition.evaluateDate,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.contractDate != null ">
          and CONTRACT_DATE = #{condition.contractDate,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.effectiveDateInDate != null ">
          and EFFECTIVE_DATE_IN_DATE = #{condition.effectiveDateInDate,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.checkDateInDate != null ">
          and APPROVAL_DATE_IN_DATE = #{condition.checkDateInDate,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.expiryDateInDate != null ">
          and EXPIRY_DATE_IN_DATE = #{condition.expiryDateInDate,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.effectiveDateBom != null ">
          and EFFECTIVE_DATE_BOM = #{condition.effectiveDateBom,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.expiryDateEom != null ">
          and EXPIRY_DATE_EOM = #{condition.expiryDateEom,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.premiumFrequency != null and condition.premiumFrequency != ''">
          and payment_frequency_code = #{condition.premiumFrequency,jdbcType=VARCHAR}
      </if>
      <if test="condition.premiumTerm != null ">
          and payment_frequency_no = #{condition.premiumTerm,jdbcType=DECIMAL}
      </if>
      <if test="condition.grossPremium != null ">
          and GROSS_PREMIUM = #{condition.grossPremium,jdbcType=DECIMAL}
      </if>
      <if test="condition.coverageAmount != null ">
          and COVERAGE_AMOUNT = #{condition.coverageAmount,jdbcType=DECIMAL}
      </if>
      <if test="condition.passedDates != null ">
          and PASSED_DATES = #{condition.passedDates,jdbcType=DECIMAL}
      </if>
      <if test="condition.passedMonths != null ">
          and PASSED_MONTHS = #{condition.passedMonths,jdbcType=DECIMAL}
      </if>
      <if test="condition.remainingMonths != null ">
          and REMAINING_MONTHS = #{condition.remainingMonths,jdbcType=DECIMAL}
      </if>
      <if test="condition.remainingPremTermPe != null ">
          and REMAINING_PREM_TERM_PE = #{condition.remainingPremTermPe,jdbcType=DECIMAL}
      </if>
      <if test="condition.remainingMonthsFuture != null ">
          and REMAINING_MONTHS_FUTURE = #{condition.remainingMonthsFuture,jdbcType=DECIMAL}
      </if>
      <if test="condition.remainingPremTermCb != null ">
          and REMAINING_PREM_TERM_CB = #{condition.remainingPremTermCb,jdbcType=DECIMAL}
      </if>
      <if test="condition.paymentQuarter != null ">
          and PAYMENT_QUARTER = #{condition.paymentQuarter,jdbcType=DECIMAL}
      </if>
      <if test="condition.edPremiumPerCoverageDay != null ">
          and ed_premium_per_coverage_day = #{condition.edPremiumPerCoverageDay,jdbcType=DECIMAL}
      </if>
      <if test="condition.edPremium != null ">
          and ED_PREMIUM = #{condition.edPremium,jdbcType=DECIMAL}
      </if>
      <if test="condition.priCurEndRemainCsmRate != null ">
          and PRI_CUR_END_REMAIN_CSM_RATE = #{condition.priCurEndRemainCsmRate,jdbcType=DECIMAL}
      </if>
      <if test="condition.priUntilReportRemainCsmRate != null ">
          and PRI_UNTIL_REPORT_REMAIN_CSM_RATE = #{condition.priUntilReportRemainCsmRate,jdbcType=DECIMAL}
      </if>
      <if test="condition.cumulativeEdRate != null ">
          and CUMULATIVE_ED_RATE = #{condition.cumulativeEdRate,jdbcType=DECIMAL}
      </if>
      <if test="condition.curEndRemainCsmRate != null ">
          and CUR_END_REMAIN_CSM_RATE = #{condition.curEndRemainCsmRate,jdbcType=DECIMAL}
      </if>
      <if test="condition.untilReportRemainCsmRate != null ">
          and UNTIL_REPORT_REMAIN_CSM_RATE = #{condition.untilReportRemainCsmRate,jdbcType=DECIMAL}
      </if>
      <if test="condition.elr != null ">
          and ELR = #{condition.elr,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="id != null ">
          and ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="taskCode != null and taskCode != ''">
          and TASK_CODE = #{taskCode,jdbcType=VARCHAR}
      </if>
      <if test="calcType != null and calcType != ''">
          and CALC_TYPE = #{calcType,jdbcType=VARCHAR}
      </if>
      <if test="dataKey != null ">
          and DATA_KEY = #{dataKey,jdbcType=DECIMAL}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="deptId != null ">
          and DEPT_ID = #{deptId,jdbcType=DECIMAL}
      </if>
      <if test="rePolicyNo != null and rePolicyNo != ''">
          and RE_POLICY_NO = #{rePolicyNo,jdbcType=VARCHAR}
      </if>
      <if test="reEndorseSeqNo != null and reEndorseSeqNo != ''">
          and RE_ENDORSE_SEQ_NO = #{reEndorseSeqNo,jdbcType=VARCHAR}
      </if>
      <if test="correctedNumber != null ">
          and CORRECTED_NUMBER = #{correctedNumber,jdbcType=DECIMAL}
      </if>
      <if test="currencyCode != null and currencyCode != ''">
          and CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
          and YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="portfolioNo != null and portfolioNo != ''">
          and PORTFOLIO_NO = #{portfolioNo,jdbcType=VARCHAR}
      </if>
      <if test="icgNo != null and icgNo != ''">
          and ICG_NO = #{icgNo,jdbcType=VARCHAR}
      </if>
      <if test="evaluateApproach != null and evaluateApproach != ''">
          and EVALUATE_APPROACH = #{evaluateApproach,jdbcType=VARCHAR}
      </if>
      <if test="modelDefId != null ">
          and MODEL_DEF_ID = #{modelDefId,jdbcType=DECIMAL}
      </if>
      <if test="loaCode != null and loaCode != ''">
          and LOA_CODE = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="cmunitNo != null and cmunitNo != ''">
          and CMUNIT_NO = #{cmunitNo,jdbcType=VARCHAR}
      </if>
      <if test="productCode != null and productCode != ''">
          and PRODUCT_CODE = #{productCode,jdbcType=VARCHAR}
      </if>
      <if test="evaluateDate != null ">
          and EVALUATE_DATE = #{evaluateDate,jdbcType=TIMESTAMP}
      </if>
      <if test="contractDate != null ">
          and CONTRACT_DATE = #{contractDate,jdbcType=TIMESTAMP}
      </if>
      <if test="effectiveDateInDate != null ">
          and EFFECTIVE_DATE_IN_DATE = #{effectiveDateInDate,jdbcType=TIMESTAMP}
      </if>
      <if test="checkDateInDate != null ">
          and APPROVAL_DATE_IN_DATE = #{checkDateInDate,jdbcType=TIMESTAMP}
      </if>
      <if test="expiryDateInDate != null ">
          and EXPIRY_DATE_IN_DATE = #{expiryDateInDate,jdbcType=TIMESTAMP}
      </if>
      <if test="effectiveDateBom != null ">
          and EFFECTIVE_DATE_BOM = #{effectiveDateBom,jdbcType=TIMESTAMP}
      </if>
      <if test="expiryDateEom != null ">
          and EXPIRY_DATE_EOM = #{expiryDateEom,jdbcType=TIMESTAMP}
      </if>
      <if test="premiumFrequency != null and premiumFrequency != ''">
          and payment_frequency_code = #{premiumFrequency,jdbcType=VARCHAR}
      </if>
      <if test="premiumTerm != null ">
          and payment_frequency_no = #{premiumTerm,jdbcType=DECIMAL}
      </if>
      <if test="grossPremium != null ">
          and GROSS_PREMIUM = #{grossPremium,jdbcType=DECIMAL}
      </if>
      <if test="coverageAmount != null ">
          and COVERAGE_AMOUNT = #{coverageAmount,jdbcType=DECIMAL}
      </if>
      <if test="passedDates != null ">
          and PASSED_DATES = #{passedDates,jdbcType=DECIMAL}
      </if>
      <if test="passedMonths != null ">
          and PASSED_MONTHS = #{passedMonths,jdbcType=DECIMAL}
      </if>
      <if test="remainingMonths != null ">
          and REMAINING_MONTHS = #{remainingMonths,jdbcType=DECIMAL}
      </if>
      <if test="remainingPremTermPe != null ">
          and REMAINING_PREM_TERM_PE = #{remainingPremTermPe,jdbcType=DECIMAL}
      </if>
      <if test="remainingMonthsFuture != null ">
          and REMAINING_MONTHS_FUTURE = #{remainingMonthsFuture,jdbcType=DECIMAL}
      </if>
      <if test="remainingPremTermCb != null ">
          and REMAINING_PREM_TERM_CB = #{remainingPremTermCb,jdbcType=DECIMAL}
      </if>
      <if test="paymentQuarter != null ">
          and PAYMENT_QUARTER = #{paymentQuarter,jdbcType=DECIMAL}
      </if>
      <if test="edPremiumPerCoverageDay != null ">
          and ed_premium_per_coverage_day = #{edPremiumPerCoverageDay,jdbcType=DECIMAL}
      </if>
      <if test="edPremium != null ">
          and ED_PREMIUM = #{edPremium,jdbcType=DECIMAL}
      </if>
      <if test="priCurEndRemainCsmRate != null ">
          and PRI_CUR_END_REMAIN_CSM_RATE = #{priCurEndRemainCsmRate,jdbcType=DECIMAL}
      </if>
      <if test="priUntilReportRemainCsmRate != null ">
          and PRI_UNTIL_REPORT_REMAIN_CSM_RATE = #{priUntilReportRemainCsmRate,jdbcType=DECIMAL}
      </if>
      <if test="cumulativeEdRate != null ">
          and CUMULATIVE_ED_RATE = #{cumulativeEdRate,jdbcType=DECIMAL}
      </if>
      <if test="curEndRemainCsmRate != null ">
          and CUR_END_REMAIN_CSM_RATE = #{curEndRemainCsmRate,jdbcType=DECIMAL}
      </if>
      <if test="untilReportRemainCsmRate != null ">
          and UNTIL_REPORT_REMAIN_CSM_RATE = #{untilReportRemainCsmRate,jdbcType=DECIMAL}
      </if>
      <if test="elr != null ">
          and ELR = #{elr,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_FO_LRC_ICU_CALC
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_FO_LRC_ICU_CALC
    where ID in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_FO_LRC_ICU_CALC
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussFOLrcIcuCalc">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_FO_LRC_ICU_CALC
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_FO_LRC_ICU_CALC
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="ID" keyProperty="id" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussFOLrcIcuCalc">
    insert into ATR_BUSS_FO_LRC_ICU_CALC
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="taskCode != null">
        TASK_CODE,
      </if>
      <if test="calcType != null">
        CALC_TYPE,
      </if>
      <if test="dataKey != null">
        DATA_KEY,
      </if>
      <if test="entityId != null">
        entity_id,
      </if>
      <if test="deptId != null">
        DEPT_ID,
      </if>
      <if test="rePolicyNo != null">
        RE_POLICY_NO,
      </if>
      <if test="reEndorseSeqNo != null">
        RE_ENDORSE_SEQ_NO,
      </if>
      <if test="correctedNumber != null">
        CORRECTED_NUMBER,
      </if>
      <if test="currencyCode != null">
        CURRENCY_CODE,
      </if>
      <if test="yearMonth != null">
        YEAR_MONTH,
      </if>
      <if test="portfolioNo != null">
        PORTFOLIO_NO,
      </if>
      <if test="icgNo != null">
        ICG_NO,
      </if>
      <if test="evaluateApproach != null">
        EVALUATE_APPROACH,
      </if>
      <if test="modelDefId != null">
        MODEL_DEF_ID,
      </if>
      <if test="loaCode != null">
        LOA_CODE,
      </if>
      <if test="cmunitNo != null">
        CMUNIT_NO,
      </if>
      <if test="productCode != null">
        PRODUCT_CODE,
      </if>
      <if test="evaluateDate != null">
        EVALUATE_DATE,
      </if>
      <if test="contractDate != null">
        CONTRACT_DATE,
      </if>
      <if test="effectiveDateInDate != null">
        EFFECTIVE_DATE_IN_DATE,
      </if>
      <if test="checkDateInDate != null">
        APPROVAL_DATE_IN_DATE,
      </if>
      <if test="expiryDateInDate != null">
        EXPIRY_DATE_IN_DATE,
      </if>
      <if test="effectiveDateBom != null">
        EFFECTIVE_DATE_BOM,
      </if>
      <if test="expiryDateEom != null">
        EXPIRY_DATE_EOM,
      </if>
      <if test="premiumFrequency != null">
        payment_frequency_code,
      </if>
      <if test="premiumTerm != null">
        payment_frequency_no,
      </if>
      <if test="grossPremium != null">
        GROSS_PREMIUM,
      </if>
      <if test="coverageAmount != null">
        COVERAGE_AMOUNT,
      </if>
      <if test="passedDates != null">
        PASSED_DATES,
      </if>
      <if test="passedMonths != null">
        PASSED_MONTHS,
      </if>
      <if test="remainingMonths != null">
        REMAINING_MONTHS,
      </if>
      <if test="remainingPremTermPe != null">
        REMAINING_PREM_TERM_PE,
      </if>
      <if test="remainingMonthsFuture != null">
        REMAINING_MONTHS_FUTURE,
      </if>
      <if test="remainingPremTermCb != null">
        REMAINING_PREM_TERM_CB,
      </if>
      <if test="paymentQuarter != null">
        PAYMENT_QUARTER,
      </if>
      <if test="edPremiumPerCoverageDay != null">
        ed_premium_per_coverage_day,
      </if>
      <if test="edPremium != null">
        ED_PREMIUM,
      </if>
      <if test="priCurEndRemainCsmRate != null">
        PRI_CUR_END_REMAIN_CSM_RATE,
      </if>
      <if test="priUntilReportRemainCsmRate != null">
        PRI_UNTIL_REPORT_REMAIN_CSM_RATE,
      </if>
      <if test="cumulativeEdRate != null">
        CUMULATIVE_ED_RATE,
      </if>
      <if test="curEndRemainCsmRate != null">
        CUR_END_REMAIN_CSM_RATE,
      </if>
      <if test="untilReportRemainCsmRate != null">
        UNTIL_REPORT_REMAIN_CSM_RATE,
      </if>
      <if test="elr != null">
        ELR,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="taskCode != null">
        #{taskCode,jdbcType=VARCHAR},
      </if>
      <if test="calcType != null">
        #{calcType,jdbcType=VARCHAR},
      </if>
      <if test="dataKey != null">
        #{dataKey,jdbcType=DECIMAL},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=DECIMAL},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=DECIMAL},
      </if>
      <if test="rePolicyNo != null">
        #{rePolicyNo,jdbcType=VARCHAR},
      </if>
      <if test="reEndorseSeqNo != null">
        #{reEndorseSeqNo,jdbcType=VARCHAR},
      </if>
      <if test="correctedNumber != null">
        #{correctedNumber,jdbcType=DECIMAL},
      </if>
      <if test="currencyCode != null">
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="yearMonth != null">
        #{yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="portfolioNo != null">
        #{portfolioNo,jdbcType=VARCHAR},
      </if>
      <if test="icgNo != null">
        #{icgNo,jdbcType=VARCHAR},
      </if>
      <if test="evaluateApproach != null">
        #{evaluateApproach,jdbcType=VARCHAR},
      </if>
      <if test="modelDefId != null">
        #{modelDefId,jdbcType=DECIMAL},
      </if>
      <if test="loaCode != null">
        #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="cmunitNo != null">
        #{cmunitNo,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null">
        #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="evaluateDate != null">
        #{evaluateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="contractDate != null">
        #{contractDate,jdbcType=TIMESTAMP},
      </if>
      <if test="effectiveDateInDate != null">
        #{effectiveDateInDate,jdbcType=TIMESTAMP},
      </if>
      <if test="checkDateInDate != null">
        #{checkDateInDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expiryDateInDate != null">
        #{expiryDateInDate,jdbcType=TIMESTAMP},
      </if>
      <if test="effectiveDateBom != null">
        #{effectiveDateBom,jdbcType=TIMESTAMP},
      </if>
      <if test="expiryDateEom != null">
        #{expiryDateEom,jdbcType=TIMESTAMP},
      </if>
      <if test="premiumFrequency != null">
        #{premiumFrequency,jdbcType=VARCHAR},
      </if>
      <if test="premiumTerm != null">
        #{premiumTerm,jdbcType=DECIMAL},
      </if>
      <if test="grossPremium != null">
        #{grossPremium,jdbcType=DECIMAL},
      </if>
      <if test="coverageAmount != null">
        #{coverageAmount,jdbcType=DECIMAL},
      </if>
      <if test="passedDates != null">
        #{passedDates,jdbcType=DECIMAL},
      </if>
      <if test="passedMonths != null">
        #{passedMonths,jdbcType=DECIMAL},
      </if>
      <if test="remainingMonths != null">
        #{remainingMonths,jdbcType=DECIMAL},
      </if>
      <if test="remainingPremTermPe != null">
        #{remainingPremTermPe,jdbcType=DECIMAL},
      </if>
      <if test="remainingMonthsFuture != null">
        #{remainingMonthsFuture,jdbcType=DECIMAL},
      </if>
      <if test="remainingPremTermCb != null">
        #{remainingPremTermCb,jdbcType=DECIMAL},
      </if>
      <if test="paymentQuarter != null">
        #{paymentQuarter,jdbcType=DECIMAL},
      </if>
      <if test="edPremiumPerCoverageDay != null">
        #{edPremiumPerCoverageDay,jdbcType=DECIMAL},
      </if>
      <if test="edPremium != null">
        #{edPremium,jdbcType=DECIMAL},
      </if>
      <if test="priCurEndRemainCsmRate != null">
        #{priCurEndRemainCsmRate,jdbcType=DECIMAL},
      </if>
      <if test="priUntilReportRemainCsmRate != null">
        #{priUntilReportRemainCsmRate,jdbcType=DECIMAL},
      </if>
      <if test="cumulativeEdRate != null">
        #{cumulativeEdRate,jdbcType=DECIMAL},
      </if>
      <if test="curEndRemainCsmRate != null">
        #{curEndRemainCsmRate,jdbcType=DECIMAL},
      </if>
      <if test="untilReportRemainCsmRate != null">
        #{untilReportRemainCsmRate,jdbcType=DECIMAL},
      </if>
      <if test="elr != null">
        #{elr,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all 
    <foreach collection="list" item="item" index="index">
      into ATR_BUSS_FO_LRC_ICU_CALC values 
       (#{item.id,jdbcType=DECIMAL}, #{item.taskCode,jdbcType=VARCHAR}, 
        #{item.calcType,jdbcType=VARCHAR}, #{item.dataKey,jdbcType=DECIMAL}, #{item.entityId,jdbcType=DECIMAL},
        #{item.deptId,jdbcType=DECIMAL}, #{item.rePolicyNo,jdbcType=VARCHAR}, #{item.reEndorseSeqNo,jdbcType=VARCHAR}, 
        #{item.correctedNumber,jdbcType=DECIMAL}, #{item.currencyCode,jdbcType=VARCHAR}, #{item.yearMonth,jdbcType=VARCHAR},
        #{item.portfolioNo,jdbcType=VARCHAR}, #{item.icgNo,jdbcType=VARCHAR}, #{item.evaluateApproach,jdbcType=VARCHAR}, 
        #{item.modelDefId,jdbcType=DECIMAL}, #{item.loaCode,jdbcType=VARCHAR}, #{item.cmunitNo,jdbcType=VARCHAR}, 
        #{item.productCode,jdbcType=VARCHAR}, #{item.evaluateDate,jdbcType=TIMESTAMP}, 
        #{item.contractDate,jdbcType=TIMESTAMP}, #{item.effectiveDateInDate,jdbcType=TIMESTAMP}, 
        #{item.checkDateInDate,jdbcType=TIMESTAMP}, #{item.expiryDateInDate,jdbcType=TIMESTAMP}, 
        #{item.effectiveDateBom,jdbcType=TIMESTAMP}, #{item.expiryDateEom,jdbcType=TIMESTAMP}, 
        #{item.premiumFrequency,jdbcType=VARCHAR}, #{item.premiumTerm,jdbcType=DECIMAL}, 
        #{item.grossPremium,jdbcType=DECIMAL}, #{item.coverageAmount,jdbcType=DECIMAL}, 
        #{item.passedDates,jdbcType=DECIMAL}, #{item.passedMonths,jdbcType=DECIMAL}, #{item.remainingMonths,jdbcType=DECIMAL}, 
        #{item.remainingPremTermPe,jdbcType=DECIMAL}, #{item.remainingMonthsFuture,jdbcType=DECIMAL}, 
        #{item.remainingPremTermCb,jdbcType=DECIMAL}, #{item.paymentQuarter,jdbcType=DECIMAL}, 
        #{item.edPremiumPerCoverageDay,jdbcType=DECIMAL}, #{item.edPremium,jdbcType=DECIMAL},
        #{item.priCurEndRemainCsmRate,jdbcType=DECIMAL}, #{item.priUntilReportRemainCsmRate,jdbcType=DECIMAL}, 
        #{item.cumulativeEdRate,jdbcType=DECIMAL}, #{item.curEndRemainCsmRate,jdbcType=DECIMAL}, 
        #{item.untilReportRemainCsmRate,jdbcType=DECIMAL}, #{item.elr,jdbcType=DECIMAL}
        )
    </foreach>
    select 1 
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussFOLrcIcuCalc">
    update ATR_BUSS_FO_LRC_ICU_CALC
    <set>
      <if test="taskCode != null">
        TASK_CODE = #{taskCode,jdbcType=VARCHAR},
      </if>
      <if test="calcType != null">
        CALC_TYPE = #{calcType,jdbcType=VARCHAR},
      </if>
      <if test="dataKey != null">
        DATA_KEY = #{dataKey,jdbcType=DECIMAL},
      </if>
      <if test="entityId != null">
        entity_id = #{entityId,jdbcType=DECIMAL},
      </if>
      <if test="deptId != null">
        DEPT_ID = #{deptId,jdbcType=DECIMAL},
      </if>
      <if test="rePolicyNo != null">
        RE_POLICY_NO = #{rePolicyNo,jdbcType=VARCHAR},
      </if>
      <if test="reEndorseSeqNo != null">
        RE_ENDORSE_SEQ_NO = #{reEndorseSeqNo,jdbcType=VARCHAR},
      </if>
      <if test="correctedNumber != null">
        CORRECTED_NUMBER = #{correctedNumber,jdbcType=DECIMAL},
      </if>
      <if test="currencyCode != null">
        CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="yearMonth != null">
        YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="portfolioNo != null">
        PORTFOLIO_NO = #{portfolioNo,jdbcType=VARCHAR},
      </if>
      <if test="icgNo != null">
        ICG_NO = #{icgNo,jdbcType=VARCHAR},
      </if>
      <if test="evaluateApproach != null">
        EVALUATE_APPROACH = #{evaluateApproach,jdbcType=VARCHAR},
      </if>
      <if test="modelDefId != null">
        MODEL_DEF_ID = #{modelDefId,jdbcType=DECIMAL},
      </if>
      <if test="loaCode != null">
        LOA_CODE = #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="cmunitNo != null">
        CMUNIT_NO = #{cmunitNo,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null">
        PRODUCT_CODE = #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="evaluateDate != null">
        EVALUATE_DATE = #{evaluateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="contractDate != null">
        CONTRACT_DATE = #{contractDate,jdbcType=TIMESTAMP},
      </if>
      <if test="effectiveDateInDate != null">
        EFFECTIVE_DATE_IN_DATE = #{effectiveDateInDate,jdbcType=TIMESTAMP},
      </if>
      <if test="checkDateInDate != null">
        APPROVAL_DATE_IN_DATE = #{checkDateInDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expiryDateInDate != null">
        EXPIRY_DATE_IN_DATE = #{expiryDateInDate,jdbcType=TIMESTAMP},
      </if>
      <if test="effectiveDateBom != null">
        EFFECTIVE_DATE_BOM = #{effectiveDateBom,jdbcType=TIMESTAMP},
      </if>
      <if test="expiryDateEom != null">
        EXPIRY_DATE_EOM = #{expiryDateEom,jdbcType=TIMESTAMP},
      </if>
      <if test="premiumFrequency != null">
        payment_frequency_code = #{premiumFrequency,jdbcType=VARCHAR},
      </if>
      <if test="premiumTerm != null">
        payment_frequency_no = #{premiumTerm,jdbcType=DECIMAL},
      </if>
      <if test="grossPremium != null">
        GROSS_PREMIUM = #{grossPremium,jdbcType=DECIMAL},
      </if>
      <if test="coverageAmount != null">
        COVERAGE_AMOUNT = #{coverageAmount,jdbcType=DECIMAL},
      </if>
      <if test="passedDates != null">
        PASSED_DATES = #{passedDates,jdbcType=DECIMAL},
      </if>
      <if test="passedMonths != null">
        PASSED_MONTHS = #{passedMonths,jdbcType=DECIMAL},
      </if>
      <if test="remainingMonths != null">
        REMAINING_MONTHS = #{remainingMonths,jdbcType=DECIMAL},
      </if>
      <if test="remainingPremTermPe != null">
        REMAINING_PREM_TERM_PE = #{remainingPremTermPe,jdbcType=DECIMAL},
      </if>
      <if test="remainingMonthsFuture != null">
        REMAINING_MONTHS_FUTURE = #{remainingMonthsFuture,jdbcType=DECIMAL},
      </if>
      <if test="remainingPremTermCb != null">
        REMAINING_PREM_TERM_CB = #{remainingPremTermCb,jdbcType=DECIMAL},
      </if>
      <if test="paymentQuarter != null">
        PAYMENT_QUARTER = #{paymentQuarter,jdbcType=DECIMAL},
      </if>
      <if test="edPremiumPerCoverageDay != null">
        ed_premium_per_coverage_day = #{edPremiumPerCoverageDay,jdbcType=DECIMAL},
      </if>
      <if test="edPremium != null">
        ED_PREMIUM = #{edPremium,jdbcType=DECIMAL},
      </if>
      <if test="priCurEndRemainCsmRate != null">
        PRI_CUR_END_REMAIN_CSM_RATE = #{priCurEndRemainCsmRate,jdbcType=DECIMAL},
      </if>
      <if test="priUntilReportRemainCsmRate != null">
        PRI_UNTIL_REPORT_REMAIN_CSM_RATE = #{priUntilReportRemainCsmRate,jdbcType=DECIMAL},
      </if>
      <if test="cumulativeEdRate != null">
        CUMULATIVE_ED_RATE = #{cumulativeEdRate,jdbcType=DECIMAL},
      </if>
      <if test="curEndRemainCsmRate != null">
        CUR_END_REMAIN_CSM_RATE = #{curEndRemainCsmRate,jdbcType=DECIMAL},
      </if>
      <if test="untilReportRemainCsmRate != null">
        UNTIL_REPORT_REMAIN_CSM_RATE = #{untilReportRemainCsmRate,jdbcType=DECIMAL},
      </if>
      <if test="elr != null">
        ELR = #{elr,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussFOLrcIcuCalc">
    update ATR_BUSS_FO_LRC_ICU_CALC
    <set>
      <if test="record.taskCode != null">
        TASK_CODE = #{record.taskCode,jdbcType=VARCHAR},
      </if>
      <if test="record.calcType != null">
        CALC_TYPE = #{record.calcType,jdbcType=VARCHAR},
      </if>
      <if test="record.dataKey != null">
        DATA_KEY = #{record.dataKey,jdbcType=DECIMAL},
      </if>
      <if test="record.entityId != null">
        entity_id = #{record.entityId,jdbcType=DECIMAL},
      </if>
      <if test="record.deptId != null">
        DEPT_ID = #{record.deptId,jdbcType=DECIMAL},
      </if>
      <if test="record.rePolicyNo != null">
        RE_POLICY_NO = #{record.rePolicyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reEndorseSeqNo != null">
        RE_ENDORSE_SEQ_NO = #{record.reEndorseSeqNo,jdbcType=VARCHAR},
      </if>
      <if test="record.correctedNumber != null">
        CORRECTED_NUMBER = #{record.correctedNumber,jdbcType=DECIMAL},
      </if>
      <if test="record.currencyCode != null">
        CURRENCY_CODE = #{record.currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.yearMonth != null">
        YEAR_MONTH = #{record.yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="record.portfolioNo != null">
        PORTFOLIO_NO = #{record.portfolioNo,jdbcType=VARCHAR},
      </if>
      <if test="record.icgNo != null">
        ICG_NO = #{record.icgNo,jdbcType=VARCHAR},
      </if>
      <if test="record.evaluateApproach != null">
        EVALUATE_APPROACH = #{record.evaluateApproach,jdbcType=VARCHAR},
      </if>
      <if test="record.modelDefId != null">
        MODEL_DEF_ID = #{record.modelDefId,jdbcType=DECIMAL},
      </if>
      <if test="record.loaCode != null">
        LOA_CODE = #{record.loaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cmunitNo != null">
        CMUNIT_NO = #{record.cmunitNo,jdbcType=VARCHAR},
      </if>
      <if test="record.productCode != null">
        PRODUCT_CODE = #{record.productCode,jdbcType=VARCHAR},
      </if>
      <if test="record.evaluateDate != null">
        EVALUATE_DATE = #{record.evaluateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.contractDate != null">
        CONTRACT_DATE = #{record.contractDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.effectiveDateInDate != null">
        EFFECTIVE_DATE_IN_DATE = #{record.effectiveDateInDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.checkDateInDate != null">
        APPROVAL_DATE_IN_DATE = #{record.checkDateInDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.expiryDateInDate != null">
        EXPIRY_DATE_IN_DATE = #{record.expiryDateInDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.effectiveDateBom != null">
        EFFECTIVE_DATE_BOM = #{record.effectiveDateBom,jdbcType=TIMESTAMP},
      </if>
      <if test="record.expiryDateEom != null">
        EXPIRY_DATE_EOM = #{record.expiryDateEom,jdbcType=TIMESTAMP},
      </if>
      <if test="record.premiumFrequency != null">
        payment_frequency_code = #{record.premiumFrequency,jdbcType=VARCHAR},
      </if>
      <if test="record.premiumTerm != null">
        payment_frequency_no = #{record.premiumTerm,jdbcType=DECIMAL},
      </if>
      <if test="record.grossPremium != null">
        GROSS_PREMIUM = #{record.grossPremium,jdbcType=DECIMAL},
      </if>
      <if test="record.coverageAmount != null">
        COVERAGE_AMOUNT = #{record.coverageAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.passedDates != null">
        PASSED_DATES = #{record.passedDates,jdbcType=DECIMAL},
      </if>
      <if test="record.passedMonths != null">
        PASSED_MONTHS = #{record.passedMonths,jdbcType=DECIMAL},
      </if>
      <if test="record.remainingMonths != null">
        REMAINING_MONTHS = #{record.remainingMonths,jdbcType=DECIMAL},
      </if>
      <if test="record.remainingPremTermPe != null">
        REMAINING_PREM_TERM_PE = #{record.remainingPremTermPe,jdbcType=DECIMAL},
      </if>
      <if test="record.remainingMonthsFuture != null">
        REMAINING_MONTHS_FUTURE = #{record.remainingMonthsFuture,jdbcType=DECIMAL},
      </if>
      <if test="record.remainingPremTermCb != null">
        REMAINING_PREM_TERM_CB = #{record.remainingPremTermCb,jdbcType=DECIMAL},
      </if>
      <if test="record.paymentQuarter != null">
        PAYMENT_QUARTER = #{record.paymentQuarter,jdbcType=DECIMAL},
      </if>
      <if test="record.edPremiumPerCoverageDay != null">
        ed_premium_per_coverage_day = #{record.edPremiumPerCoverageDay,jdbcType=DECIMAL},
      </if>
      <if test="record.edPremium != null">
        ED_PREMIUM = #{record.edPremium,jdbcType=DECIMAL},
      </if>
      <if test="record.priCurEndRemainCsmRate != null">
        PRI_CUR_END_REMAIN_CSM_RATE = #{record.priCurEndRemainCsmRate,jdbcType=DECIMAL},
      </if>
      <if test="record.priUntilReportRemainCsmRate != null">
        PRI_UNTIL_REPORT_REMAIN_CSM_RATE = #{record.priUntilReportRemainCsmRate,jdbcType=DECIMAL},
      </if>
      <if test="record.cumulativeEdRate != null">
        CUMULATIVE_ED_RATE = #{record.cumulativeEdRate,jdbcType=DECIMAL},
      </if>
      <if test="record.curEndRemainCsmRate != null">
        CUR_END_REMAIN_CSM_RATE = #{record.curEndRemainCsmRate,jdbcType=DECIMAL},
      </if>
      <if test="record.untilReportRemainCsmRate != null">
        UNTIL_REPORT_REMAIN_CSM_RATE = #{record.untilReportRemainCsmRate,jdbcType=DECIMAL},
      </if>
      <if test="record.elr != null">
        ELR = #{record.elr,jdbcType=DECIMAL},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from ATR_BUSS_FO_LRC_ICU_CALC
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from ATR_BUSS_FO_LRC_ICU_CALC
    where ID in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from ATR_BUSS_FO_LRC_ICU_CALC
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussFOLrcIcuCalc">
    select count(1) from ATR_BUSS_FO_LRC_ICU_CALC
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>