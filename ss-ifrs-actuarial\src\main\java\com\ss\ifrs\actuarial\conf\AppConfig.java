/**
 * Project Name:gis-service-webui
 * File Name:AppConfig.java
 * Package Name:com.cnGIS .fcs.webui.conf
 * Date:2017年12月13日下午2:54:09
 *
 */

package com.ss.ifrs.actuarial.conf;


import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * ClassName:AppConfig <br/>
 * Description: TODO ADD Description. <br/>
 * Date: 2017年12月13日 下午2:54:09 <br/>
 *
 * <AUTHOR>
@Configuration
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "app.config")
public class AppConfig {



    private String financeApiUrl;

    private String excelDirectory;

    private String dbSchema;

    private String exportService;

    private String fsApiUrl;

    private String searchEngineUrl;

    private String apiKey;

    private String apiSecret;

    private String basePath;

    private String etlBasePath;

    private String uploadBasePath;

    private String kettlePath;

    private String fileExchangePath;

    private String fileTemplate;

    private String uploadPath;

    private String parseXml;

    private List<String> excludedUrls;

    private String exportExcelTemplate;

    private String exportExcelOutTemplate;



    public String getExcelDirectory() {
        return excelDirectory;
    }

    public void setExcelDirectory(String excelDirectory) {
        this.excelDirectory = excelDirectory;
    }

    public String getFinanceApiUrl() {
        return financeApiUrl;
    }

    public void setFinanceApiUrl(String financeApiUrl) {
        this.financeApiUrl = financeApiUrl;
    }

    public String getDbSchema() {
        return dbSchema;
    }

    public void setDbSchema(String dbSchema) {
        this.dbSchema = dbSchema;
    }

    public String getExportService() {
        return exportService;
    }

    public void setExportService(String exportService) {
        this.exportService = exportService;
    }

    public String getFsApiUrl() {
        return fsApiUrl;
    }

    public void setFsApiUrl(String fsApiUrl) {
        this.fsApiUrl = fsApiUrl;
    }

    public String getSearchEngineUrl() {
        return searchEngineUrl;
    }

    public void setSearchEngineUrl(String searchEngineUrl) {
        this.searchEngineUrl = searchEngineUrl;
    }

/*
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
*/

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getApiSecret() {
        return apiSecret;
    }

    public void setApiSecret(String apiSecret) {
        this.apiSecret = apiSecret;
    }

    public String getBasePath() {
        return basePath;
    }

    public void setBasePath(String basePath) {
        this.basePath = basePath;
    }

    public String getEtlBasePath() {
        return etlBasePath;
    }

    public void setEtlBasePath(String etlBasePath) {
        this.etlBasePath = etlBasePath;
    }

    public String getUploadBasePath() {
        return uploadBasePath;
    }

    public void setUploadBasePath(String uploadBasePath) {
        this.uploadBasePath = uploadBasePath;
    }

    public String getKettlePath() {
        return kettlePath;
    }

    public void setKettlePath(String kettlePath) {
        this.kettlePath = kettlePath;
    }

    public String getFileExchangePath() {
        return fileExchangePath;
    }

    public void setFileExchangePath(String fileExchangePath) {
        this.fileExchangePath = fileExchangePath;
    }

    public String getFileTemplate() {
        return fileTemplate;
    }

    public void setFileTemplate(String fileTemplate) {
        this.fileTemplate = fileTemplate;
    }

    public String getUploadPath() {
        return uploadPath;
    }

    public void setUploadPath(String uploadPath) {
        this.uploadPath = uploadPath;
    }

    public String getParseXml() {
        return parseXml;
    }

    public void setParseXml(String parseXml) {
        this.parseXml = parseXml;
    }

    public List<String> getExcludedUrls() {
        return excludedUrls;
    }

    public void setExcludedUrls(List<String> excludedUrls) {
        this.excludedUrls = excludedUrls;
    }

    public String getExportExcelTemplate() {
        return exportExcelTemplate;
    }

    public void setExportExcelTemplate(String exportExcelTemplate) {
        this.exportExcelTemplate = exportExcelTemplate;
    }

    public String getExportExcelOutTemplate() {
        return exportExcelOutTemplate;
    }

    public void setExportExcelOutTemplate(String exportExcelOutTemplate) {
        this.exportExcelOutTemplate = exportExcelOutTemplate;
    }
}
