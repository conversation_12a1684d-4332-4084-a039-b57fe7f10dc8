package com.ss.ifrs.actuarial.dao;

import com.ss.ifrs.actuarial.pojo.ecf.vo.syncqtc.AtrLogSyncQtc;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

@Mapper
public interface AtrBussEcfSyncQtcDao {

    void insertLog(AtrLogSyncQtc vo);

    long getMaxLogId();

    void syncEcfFrIr(Map<String, Object> paramMap);

    void syncEcfWaIr(Map<String, Object> paramMap);

    void syncEcfIcg(Map<String, Object> paramMap);

    void syncEcfIcgCf(Map<String, Object> paramMap);

    void syncEcfLicPv(Map<String, Object> paramMap);

    void syncEcfLicPvUDD(Map<String, Object> paramMap);
    void syncEcfLicPvUFO(Map<String, Object> paramMap);
    void syncEcfLicPvUTI(Map<String, Object> paramMap);
    void syncEcfLicPvUTO(Map<String, Object> paramMap);

    void syncExfLrcDDUCf(Map<String, Object> paramMap);
    void syncExfLrcFOUCf(Map<String, Object> paramMap);
    void syncExfLrcTIUCf(Map<String, Object> paramMap);
    void syncExfLrcTOUCf(Map<String, Object> paramMap);




    void syncEcfRecoveredFO(Map<String, Object> paramMap);

    void syncEcfRecoveredTO(Map<String, Object> paramMap);
    void syncEcfRecoveredTX(Map<String, Object> paramMap);

    void syncEcfDDEdPremium(Map<String, Object> paramMap);

    String getLrcDdActionNo(@Param("yearMonth") String yearMonth);

    void truncateTempQtcRecoveredPolicy();

}
