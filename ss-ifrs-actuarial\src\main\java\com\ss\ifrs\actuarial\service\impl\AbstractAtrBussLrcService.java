package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.dao.AtrBussLrcDdDao;
import com.ss.ifrs.actuarial.dao.AtrBussLrcFoDao;
import com.ss.ifrs.actuarial.dao.AtrBussLrcTiDao;
import com.ss.ifrs.actuarial.dao.AtrBussLrcToDao;
import com.ss.ifrs.actuarial.pojo.ecf.vo.AtrBussQuota;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.AtrLogLrc;
import com.ss.ifrs.actuarial.util.Dates;
import com.ss.ifrs.actuarial.util.EcfUtil;
import com.ss.ifrs.actuarial.util.abp.AsyncBatchProcessor;
import org.joda.time.DateTime;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 预期保费现金流的基础 service
 * <AUTHOR>
 */
public abstract class AbstractAtrBussLrcService extends AbstractAtrBussEcfService {

    @Resource
    protected AtrBussLrcDdDao atrBussLrcDdDao;

    @Resource
    protected AtrBussLrcFoDao atrBussLrcFoDao;

    @Resource
    protected AtrBussLrcTiDao atrBussLrcTiDao;

    @Resource
    protected AtrBussLrcToDao atrBussLrcToDao;

    protected AtomicLong icuMainIdGen;

    protected AtomicLong icgMainIdGen;


    protected int parts;

    protected void initEnvParams(String actionNo, Long entityId, String yearMonth, String businessSourceCode) {
        super.initEnvParams(actionNo, entityId, yearMonth, businessSourceCode,"LRC");
        this.logIdGen = new AtomicLong(atrBussLrcDao.getMaxLogId());
        this.parts = EcfUtil.getLrcConfParts(atrBussLrcDao, yearMonth, businessSourceCode);
        commonParamMap.put("parts", parts);
        commonParamMap.put("module", "LRC");
        logEnv();
    }

    @Override
    protected String getLastActionNo() {
        String lastYearMonth = Dates.preYearMonth(yearMonth);
        return atrBussLrcDao.getActionNo(lastYearMonth, businessSourceCode);
    }

    protected void initAbp(AsyncBatchProcessor abp) {
        this.abp = abp;
    }

    protected Date devDate(int devNo) {
        return Dates.lastDay(new DateTime(evDateBom).plusMonths(devNo));
    }


    protected int getMaxClmPatternDevNo(String riskClassCode, String icgNo) {
        String quotaCode = EcfUtil.Q_LRC_CLAIM_SETTLED_PATTERN;
        List<AtrBussQuota> quotas = quotaIndex.list(Arrays.asList(quotaCode, riskClassCode, icgNo));
        int maxDevNo = -1;
        if (!quotas.isEmpty()) {
            for (AtrBussQuota quota : quotas) {
                if (quota.getValue() != null && quota.getValue() != 0) {
                    if (maxDevNo < quota.getDevNo()) {
                        maxDevNo = quota.getDevNo();
                    }
                }
            }
        }
        return Math.max(maxDevNo, 0);
    }

    protected void logEnv() {
        AtrLogLrc vo = new AtrLogLrc();
        vo.setMark("env");
        vo.setBusinessInfo("T_DB=" + EcfUtil.THREADS_DB + ", " + "T_CALC=" + EcfUtil.THREADS_CALC);
        super.logDebug(vo);
    }

    protected void logDebug(String mark) {
        AtrLogLrc vo = new AtrLogLrc();
        vo.setMark(mark);
        super.logDebug(vo);
    }

    protected void logDebug(String mark, String busiInfo) {
        AtrLogLrc vo = new AtrLogLrc();
        vo.setMark(mark);
        vo.setBusinessInfo(busiInfo);
        super.logDebug(vo);
    }

    protected void logError(Exception e) {
        AtrLogLrc vo = new AtrLogLrc();
        vo.setMark("error");
        super.logError(vo, e);
    }


    // 具有舍入功能的BigDecimal操作
    protected BigDecimal round(BigDecimal value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        return value.setScale(8, RoundingMode.HALF_UP);
    }

    // 用于利率计算的舍入方法，保留更多小数位
    protected BigDecimal roundR(BigDecimal value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        return value.setScale(15, RoundingMode.HALF_UP);
    }

    // 适配Map<Integer, BigDecimal>的放入方法
    protected void putDevValue(Map<Integer, BigDecimal> map, Integer key, BigDecimal value) {
        if (map.containsKey(key)) {
            map.put(key, map.get(key).add(nvl(value)));
        } else {
            map.put(key, nvl(value));
        }
    }

    // 安全地将Double转换为BigDecimal，避免空指针异常
    protected BigDecimal safeValueOf(Double value) {
        return value == null ? BigDecimal.ZERO : BigDecimal.valueOf(value);
    }

    // 安全地获取BigDecimal值，避免空指针异常
    protected BigDecimal nvl(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value;
    }

    // 安全地获取Integer值，避免空指针异常
    protected Integer nvl(Integer value, Integer defaultValue) {
        return value == null ? defaultValue : value;
    }

}
