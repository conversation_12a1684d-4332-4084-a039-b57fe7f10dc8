package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.x;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AtrDuctLrcToxPaid {

    private String treatyNo;

    private String yearMonth;

    private BigDecimal mdp;

    private BigDecimal adjustFee;

    private BigDecimal netFee;
    
    /** 一级机构 */
    private String companyCode1;
    
    /** 二级机构 */
    private String companyCode2;
    
    /** 三级机构 */
    private String companyCode3;
    
    /** 四级机构 */
    private String companyCode4;
    
    /** 财务渠道 */
    private String finAccChannel;
    
    /** 合约名称 */
    private String treatyName;

}
