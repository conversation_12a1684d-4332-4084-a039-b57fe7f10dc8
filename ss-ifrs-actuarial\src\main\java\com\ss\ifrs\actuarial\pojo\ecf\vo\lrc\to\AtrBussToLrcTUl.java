package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to; // 指定的包名

import java.math.BigDecimal;
import java.util.Date;

import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Data;

@Tab("atr_buss_to_lrc_t_ul")
@Data
public class AtrBussToLrcTUl {

    /**
     * ID (bigint, not null)
     * 数据库字段: id
     */
    private Long id;

    /**
     * 执行编号 (varchar(32), not null) - 注意图片中是 varchar2(32)
     * 数据库字段: action_no
     */
    private String actionNo;

    /**
     * 业务发生年月
     * 对应数据库字段: year_month (VARCHAR(6))
     */
    private String bussYearMonth;

    /**
     * 业务发生年月 (varchar(6), not null)
     * 数据库字段: year_month
     */
    private String yearMonth;

    /**
     * 业务单位ID (bigint, not null)
     * 数据库字段: entity_id
     */
    private Long entityId;

    /**
     * 合约号 (varchar(60), not null)
     * 数据库字段: treaty_no
     */
    private String treatyNo;

    /**
     * 分保单号 (varchar(60), not null)
     * 数据库字段: ri_policy_no
     */
    private String riPolicyNo;

    /**
     * 分批单序号 (varchar(10), not null)
     * 数据库字段: ri_endorse_seq_no
     */
    private String riEndorseSeqNo;

    /**
     * 保单号 (varchar(60), not null)
     * 数据库字段: policy_no
     */
    private String policyNo;

    /**
     * 批单序号 (varchar(10), not null)
     * 数据库字段: endorse_seq_no
     */
    private String endorseSeqNo;

    /**
     * 险类代码 (varchar(10), not null)
     * 数据库字段: risk_class_code
     */
    private String riskClassCode;

    /**
     * 险种代码 (varchar(10), not null)
     * 数据库字段: risk_code
     */
    private String riskCode;

    /**
     * 险别代码 (varchar(30), not null)
     * 数据库字段: kind_code
     */
    private String kindCode;

    /*
     * 底单合同确认日期
     * */
    private Date policyContractDate;

    /*
    * 合同组合
    * */
    private String portfolioNo;

    private String icgName;

    /**
     * 标的合同组 (varchar(60), not null)
     * 数据库字段: icg_no
     */
    private String icgNo;

    /**
     * 计量单元编号 (varchar(60), not null)
     * 数据库字段: cmunit_no
     */
    private String cmunitNo;

    /**
     * 标的保单责任起始日期 (date, not null)
     * 数据库字段: effective_date
     */
    private Date effectiveDate;

    /**
     * 标的保单责任终止日期 (date, not null)
     * 数据库字段: expiry_date
     */
    private Date expiryDate;

    /**
     * 再保合约责任起始日期 (date, not null)
     * 数据库字段: ri_effective_date
     */
    private Date riEffectiveDate;

    /**
     * 再保合约责任终止日期 (date, not null)
     * 数据库字段: ri_expiry_date
     */
    private Date riExpiryDate;

    /**
     * 分保的核保通过日期 (date, not null)
     * 数据库字段: approval_date
     */
    private Date approvalDate;

    /**
     * 合同确认日期 (date, not null)
     * 数据库字段: contract_date
     */
    private Date contractDate;

    /**
     * 合同签发日 (date, not null)
     * 数据库字段: issue_date
     */
    private Date issueDate;

    /**
     * 当期实收保费 (decimal(21,4), not null)
     * 备注: 费用类型:C01, C10, D16
     * 数据库字段: cur_premium
     */
    private BigDecimal curPremium;

    /**
     * 当期净额结算手续费缴费 (decimal(21,4), not null)
     * 备注: 当期支付的分保费对应的收到的预付手续费或固定手续费, 费用类型:D01
     * 数据库字段: cur_net_fee
     */
    private BigDecimal curNetFee;

    /**
     * 上期累计实收保费 (decimal(21,4), not null) - 新增字段
     * 数据库字段: pre_cuml_paid_premium
     */
    private BigDecimal preCumlPaidPremium;

    /**
     * 上期累计实收手续费 (decimal(21,4), not null) - 新增字段
     * 数据库字段: pre_cuml_paid_net_fee
     */
    private BigDecimal preCumlPaidNetFee;

    /**
     * 一级机构 (varchar(30), not null)
     * 数据库字段: company_code1
     */
    private String companyCode1;

    /**
     * 二级机构 (varchar(30), not null)
     * 数据库字段: company_code2
     */
    private String companyCode2;

    /**
     * 三级机构 (varchar(30), not null)
     * 数据库字段: company_code3
     */
    private String companyCode3;

    /**
     * 四级机构 (varchar(30), not null)
     * 数据库字段: company_code4
     */
    private String companyCode4;

    /**
     * 净额结算手续费率 (decimal(19,6), not null)
     * 数据库字段: fee_rate
     */
    private BigDecimal feeRate;

    /**
     * 当期已赚保费 (decimal(21,4), not null)
     * 数据库字段: cur_ed_premium
     */
    private BigDecimal curEdPremium;

    /**
     * 当期已赚手续费 (decimal(21,4), not null)
     * 数据库字段: cur_ed_net_fee
     */
    private BigDecimal curEdNetFee;

    /**
     * 上期已赚保费 (decimal(21,4), not null)
     * 数据库字段: pre_ed_premium
     */
    private BigDecimal preCumlEdPremium;

    /**
     * 上期已赚手续费 (decimal(21,4), not null)
     * 数据库字段: pre_ed_net_fee
     */
    private BigDecimal preCumlEdNetFee;

    /**
     * 财务渠道 (fin_acc_channel)
     */
    private String finAccChannel;

    /**
     * 财务产品代码 (fin_product_code)
     */
    private String finProductCode;

    /**
     * 财务明细代码 (fin_detail_code)
     */
    private String finDetailCode;

    /**
     * 财务子产品代码 (fin_sub_product_code)
     */
    private String finSubProductCode;

    /**
     * 盈亏判定结果 (pl_judge_rslt)
     */
    private String plJudgeRslt;
    
    /**
     * 合约名称 (treaty_name)
     */
    private String treatyName;
    
    /**
     * 标的保单签发日期 (policy_issue_date)
     */
    private Date policyIssueDate;
    
    /**
     * 标的单承保认可日期 (policy_confirm_date)
     */
    private Date policyConfirmDate;
    
    /**
     * 标的保单核保日期 (policy_approval_date)
     */
    private Date policyApprovalDate;
    
    /**
     * 标的保单保费 (policy_premium)
     */
    private BigDecimal policyPremium;
    
    /**
     * 标的保单净额结算手续费 (policy_netfee)
     */
    private BigDecimal policyNetfee;

    /**
     * 部门段
     * 对应数据库字段: dept_id (VARCHAR(60))
     * 对应acepayment的 article5
     */
    private String deptId;
    
    /**
     * 渠道段
     * 对应数据库字段: channel_id (VARCHAR(60))
     * 对应acepayment的 article7
     */
    private String channelId;
    
    /**
     * 核算机构
     * 对应数据库字段: center_code (VARCHAR(60))
     * 对应acepayment的 centercode
     */
    private String centerCode;

    /*投资成分*/
    private BigDecimal invAmount;
    private BigDecimal premium;
    private BigDecimal netFee;
}