package com.ss.ifrs.actuarial.service.impl;

import com.alibaba.excel.enums.WriteDirectionEnum;
import com.ss.ifrs.actuarial.constant.ActuarialConstant;
import com.ss.ifrs.actuarial.dao.AtrBussDDLicIcgCalcDao;
import com.ss.ifrs.actuarial.dao.AtrBussDDLicIcgCalcDetailDao;
import com.ss.ifrs.actuarial.dao.AtrBussDDLicIcpCalcAccidentYearYmDetailDao;
import com.ss.ifrs.actuarial.dao.AtrBussFOLicIcgCalcDao;
import com.ss.ifrs.actuarial.dao.AtrBussFOLicIcgCalcDetailDao;
import com.ss.ifrs.actuarial.dao.AtrBussFOLicIcpCalcAccidentYmDetailDao;
import com.ss.ifrs.actuarial.dao.AtrBussLicCashFlowDao;
import com.ss.ifrs.actuarial.dao.AtrBussLicIcgCalcBusinessTypeDao;
import com.ss.ifrs.actuarial.dao.AtrBussTILicIcgCalcDao;
import com.ss.ifrs.actuarial.dao.AtrBussTILicIcgCalcDetailDao;
import com.ss.ifrs.actuarial.dao.AtrBussTILicIcpCalcAccidentYmDetailDao;
import com.ss.ifrs.actuarial.dao.AtrBussTOLicIcgCalcDao;
import com.ss.ifrs.actuarial.dao.AtrBussTOLicIcgCalcDetailDao;
import com.ss.ifrs.actuarial.dao.AtrBussTOLicIcpCalcAccidentYmDetailDao;
import com.ss.ifrs.actuarial.feign.BbsConfAccountSetFeignClient;
import com.ss.ifrs.actuarial.feign.BmsConfCodeFeignClient;
import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussFOLicIcgCalcDetail;
import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLicCashFlow;
import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussTILicIcgCalcDetail;
import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussTOLicIcgCalcDetail;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussFOLicIcgCalcDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussFOLicIcgCalcVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLicCashFlowVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLicIcgAmountVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussTILicIcgCalcDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussTILicIcgCalcVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussTOLicIcgCalcDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussTOLicIcgCalcVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfCodeVo;
import com.ss.ifrs.actuarial.pojo.atrdap.po.AtrBussDDLicIcgCalcDetail;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrBussDDLicIcgCalcDetailVo;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrBussDDLicIcgCalcVo;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo;
import com.ss.ifrs.actuarial.service.AtrBussLicCashFlowService;
import com.ss.ifrs.actuarial.service.AtrConfCodeService;
import com.ss.ifrs.actuarial.service.AtrConfModelDefService;
import com.ss.ifrs.actuarial.service.AtrExportService;
import com.ss.library.excel.ExcelSheet;
import com.ss.library.excel.ExcelSheetData;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ClassUtil;
import com.ss.library.utils.FilterUtil;
import com.ss.library.utils.SpringContextUtil;
import com.ss.library.utils.StringUtil;
import com.ss.platform.core.constant.CommonConstant;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName: AtrBussLicCashFlowServiceImpl
 * @Description: 计量预期赔付现金流接口实现类
 * @Author: xiaomj.
 * @CreateDate: 2023/2/7 19:54
 * @Version: 1.0
 */
@Service(value = "atrBussLicCashFlowService")
public class AtrBussLicCashFlowServiceImpl implements AtrBussLicCashFlowService {

	@Autowired
    BmsConfCodeFeignClient bmsConfCodeFeignClient;

	@Autowired(required = false)
	BbsConfAccountSetFeignClient accAccountPeriodFeignClient;

	@Autowired
	AtrConfModelDefService atrConfModelDefService;

	// 日志管理
	final Logger LOG  = LoggerFactory.getLogger(getClass());

	@Autowired
	private AtrBussLicCashFlowDao atrBussLicCashFlowDao;

	@Autowired
	private AtrBussDDLicIcpCalcAccidentYearYmDetailDao atrBussDDLicIcpCalcAccidentYearYmDetailDao;

	@Autowired
	private AtrBussFOLicIcpCalcAccidentYmDetailDao atrBussFOLicIcpCalcAccidentYmDetailDao;

	@Autowired
	private AtrBussTILicIcpCalcAccidentYmDetailDao atrBussTILicIcpCalcAccidentYmDetailDao;

	@Autowired
	private AtrBussTOLicIcpCalcAccidentYmDetailDao atrBussTOLicIcpCalcAccidentYmDetailDao;

	@Autowired
	private AtrBussDDLicIcgCalcDao atrBussDDLicIcgCalcDao;

	@Autowired
	private AtrBussDDLicIcgCalcDetailDao atrBussDDLicIcgCalcDetailDao;

	@Autowired
	private AtrBussFOLicIcgCalcDao atrBussFOLicIcgCalcDao;

	@Autowired
	private AtrBussFOLicIcgCalcDetailDao atrBussFOLicIcgCalcDetailDao;

	@Autowired
	private AtrBussTILicIcgCalcDao atrBussTILicIcgCalcDao;

	@Autowired
	private AtrBussTILicIcgCalcDetailDao atrBussTILicIcgCalcDetailDao;

	@Autowired
	private AtrBussTOLicIcgCalcDao atrBussTOLicIcgCalcDao;

	@Autowired
	private AtrBussTOLicIcgCalcDetailDao atrBussTOLicIcgCalcDetailDao;

	@Autowired
	private AtrBussLicIcgCalcBusinessTypeDao atrBussLicIcgCalcBusinessTypeDao;

	@Autowired
    AtrConfCodeService atrConfCodeService;

	@Autowired
	AtrExportService atrExportService;

	/*
	* 方法返回MAP类型值，key公用值
	* */
	private final static String MAP_RETURN_PUBLIC_KEY = "resultList";

	@Override
	public Page<AtrBussLicCashFlowVo> findForDataTables(AtrBussLicCashFlowVo atrBussLicCashFlowVo, Pageable pageParam) {
		return atrBussLicCashFlowDao.fuzzySearchPage(atrBussLicCashFlowVo, pageParam);
	}

	@Override
	@Async("ruleThreadPool")
	@Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = RuntimeException.class)
	public void save(AtrBussLicCashFlowVo atrBussLicCashFlowVo, Long userId) {
		this.procCalc(atrBussLicCashFlowVo);
	}

	@Override
	public AtrBussLicCashFlowVo findById(Long id) {
		return atrBussLicCashFlowDao.findByid(id);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
	public void delete(Long id, Long userId) {
		atrBussLicCashFlowDao.deleteById(id);
	}

	@Override
	public Map<String, Object> findClaimPaid(AtrDapDrawVo atrDapDrawVo) {
		if (atrDapDrawVo == null || atrDapDrawVo.getEntityId() == null
			 ) {
			return new HashMap<>();
		}
		Map<String, Object> map = new HashMap<String, Object>();
		switch (atrDapDrawVo.getBusinessSourceCode()){
			case "DD":
				List<AtrBussDDLicIcgCalcVo> resultListDD = atrBussDDLicIcgCalcDao.findAccidentClaimPaid(atrDapDrawVo);
				map.put(MAP_RETURN_PUBLIC_KEY, resultListDD);
				break;
			case "FO":
				List<AtrBussFOLicIcgCalcVo> resultListFO = atrBussFOLicIcgCalcDao.findAccidentClaimPaid(atrDapDrawVo);
				map.put(MAP_RETURN_PUBLIC_KEY, resultListFO);
				break;
			case "TI":
				List<AtrBussTILicIcgCalcVo> resultListTI = atrBussTILicIcgCalcDao.findAccidentClaimPaid(atrDapDrawVo);
				map.put(MAP_RETURN_PUBLIC_KEY, resultListTI);
				break;
			case "TO":
				List<AtrBussTOLicIcgCalcVo> resultListTO = atrBussTOLicIcgCalcDao.findAccidentClaimPaid(atrDapDrawVo);
				map.put(MAP_RETURN_PUBLIC_KEY, resultListTO);
				break;
			default:
				break;
		}
		return map;
	}

	@Override
	public List<AtrDapDrawVo> findPaidMode(AtrDapDrawVo atrDapDrawVo) {
		if (atrDapDrawVo == null || atrDapDrawVo.getMainId() == null) {
			return new ArrayList<AtrDapDrawVo>();
		}
		List<AtrDapDrawVo> resultList = new ArrayList<>();
		switch (atrDapDrawVo.getBusinessSourceCode()){
			case "DD":
				// 查找事故年月
				resultList = atrBussDDLicIcpCalcAccidentYearYmDetailDao.findYearMonth(atrDapDrawVo.getMainId());
				for (AtrDapDrawVo vo : resultList) {
					vo.setMainId(atrDapDrawVo.getMainId());
					List<AtrDapDrawVo> devYearMonthList = atrBussDDLicIcpCalcAccidentYearYmDetailDao.findPaidMode(vo);
					vo.setDevYearMonthList(devYearMonthList);
				}
				break;
			case "FO":
				// 查找事故年月
				resultList = atrBussFOLicIcpCalcAccidentYmDetailDao.findYearMonth(atrDapDrawVo.getMainId());
				for (AtrDapDrawVo vo : resultList) {
					vo.setMainId(atrDapDrawVo.getMainId());
					List<AtrDapDrawVo> devYearMonthList = atrBussFOLicIcpCalcAccidentYmDetailDao.findPaidMode(vo);
					vo.setDevYearMonthList(devYearMonthList);
				}
				break;
			case "TI":
				// 查找事故年月
				resultList = atrBussTILicIcpCalcAccidentYmDetailDao.findYearMonth(atrDapDrawVo.getMainId());
				for (AtrDapDrawVo vo : resultList) {
					vo.setMainId(atrDapDrawVo.getMainId());
					List<AtrDapDrawVo> devYearMonthList = atrBussTILicIcpCalcAccidentYmDetailDao.findPaidMode(vo);
					vo.setDevYearMonthList(devYearMonthList);
				}
				break;
			case "TO":
				// 查找事故年月
				resultList = atrBussTOLicIcpCalcAccidentYmDetailDao.findYearMonth(atrDapDrawVo.getMainId());
				for (AtrDapDrawVo vo : resultList) {
					vo.setMainId(atrDapDrawVo.getMainId());
					List<AtrDapDrawVo> devYearMonthList = atrBussTOLicIcpCalcAccidentYmDetailDao.findPaidMode(vo);
					vo.setDevYearMonthList(devYearMonthList);
				}
				break;
			default:
				break;
		}

		return resultList;
	}

	@Override
	public Map<String, Object> findUlt(AtrDapDrawVo atrDapDrawVo) {
		if (atrDapDrawVo == null || atrDapDrawVo.getMainId() == null) {
			return new HashMap<>();
		}
		Map<String, Object> map = new HashMap<String, Object>();
		switch (atrDapDrawVo.getBusinessSourceCode()){
			case "DD":
				AtrBussDDLicIcgCalcDetail poDD = new AtrBussDDLicIcgCalcDetail();
				poDD.setMainId(atrDapDrawVo.getMainId());
				List<AtrBussDDLicIcgCalcDetail> detailDD = atrBussDDLicIcgCalcDetailDao.findList(poDD);
				map.put(MAP_RETURN_PUBLIC_KEY, detailDD);
				break;
			case "FO":
				AtrBussFOLicIcgCalcDetail poFO = new AtrBussFOLicIcgCalcDetail();
				poFO.setMainId(atrDapDrawVo.getMainId());
				List<AtrBussFOLicIcgCalcDetail> detailFO = atrBussFOLicIcgCalcDetailDao.findList(poFO);
				map.put(MAP_RETURN_PUBLIC_KEY, detailFO);
				break;
			case "TI":
				AtrBussTILicIcgCalcDetail poTI = new AtrBussTILicIcgCalcDetail();
				poTI.setMainId(atrDapDrawVo.getMainId());
				List<AtrBussTILicIcgCalcDetail> detailTI = atrBussTILicIcgCalcDetailDao.findList(poTI);
				map.put(MAP_RETURN_PUBLIC_KEY, detailTI);
				break;
			case "TO":
				AtrBussTOLicIcgCalcDetail poTO = new AtrBussTOLicIcgCalcDetail();
				poTO.setMainId(atrDapDrawVo.getMainId());
				List<AtrBussTOLicIcgCalcDetail> detailTO = atrBussTOLicIcgCalcDetailDao.findList(poTO);
				map.put(MAP_RETURN_PUBLIC_KEY, detailTO);
				break;
			default:
				break;
		}

		return map;
	}

	@Override
	public AtrDapDrawVo findId(AtrDapDrawVo vo) {
		if (vo == null || vo.getEntityId() == null
				  || vo.getYearMonth() == null
				|| vo.getPortfolioNo() == null || vo.getActionNo() == null) {
			return null;
		}
		AtrDapDrawVo drawVo = new AtrDapDrawVo();
		switch (vo.getBusinessSourceCode()){
			case "DD":
				AtrBussDDLicIcgCalcVo calcVoDD =  atrBussDDLicIcgCalcDao.findId(vo);
				if (ObjectUtils.isNotEmpty(calcVoDD)&&ObjectUtils.isNotEmpty(calcVoDD.getId())) {
					drawVo.setMainId(calcVoDD.getId());}
				break;
			case "FO":
				AtrBussFOLicIcgCalcVo calcVoFO =  atrBussFOLicIcgCalcDao.findId(vo);
				if (ObjectUtils.isNotEmpty(calcVoFO)&&ObjectUtils.isNotEmpty(calcVoFO.getId())) {
					drawVo.setMainId(calcVoFO.getId());}
				break;
			case "TI":
				AtrBussTILicIcgCalcVo calcVoTI =  atrBussTILicIcgCalcDao.findId(vo);
				if (ObjectUtils.isNotEmpty(calcVoTI)&&ObjectUtils.isNotEmpty(calcVoTI.getId())) {
					drawVo.setMainId(calcVoTI.getId());}
				break;
			case "TO":
				AtrBussTOLicIcgCalcVo calcVoTO =  atrBussTOLicIcgCalcDao.findId(vo);
				if (ObjectUtils.isNotEmpty(calcVoTO)&&ObjectUtils.isNotEmpty(calcVoTO.getId())) {
					drawVo.setMainId(calcVoTO.getId());}
				break;
			default:
				break;
		}

		return drawVo;
	}

	@Override
	public Map<String, Object> showExpectedClaim(AtrDapDrawVo atrDapDrawVo) {
		if (atrDapDrawVo == null || atrDapDrawVo.getMainId() == null) {
			return new HashMap<>();
		}
		Map<String, Object> map = new HashMap<String, Object>();
		switch (atrDapDrawVo.getBusinessSourceCode()){
			case "DD":
				List<AtrBussDDLicIcgCalcDetailVo> detailDD = atrBussDDLicIcgCalcDetailDao.findByMainId(atrDapDrawVo.getMainId());
				map.put(MAP_RETURN_PUBLIC_KEY, detailDD);
				break;
			case "FO":
				List<AtrBussFOLicIcgCalcDetailVo> detailFO = atrBussFOLicIcgCalcDetailDao.findByMainId(atrDapDrawVo.getMainId());
				map.put(MAP_RETURN_PUBLIC_KEY, detailFO);
				break;
			case "TI":
				List<AtrBussTILicIcgCalcDetailVo> detailTI = atrBussTILicIcgCalcDetailDao.findByMainId(atrDapDrawVo.getMainId());
				map.put(MAP_RETURN_PUBLIC_KEY, detailTI);
				break;
			case "TO":
				List<AtrBussTOLicIcgCalcDetailVo> detailTO = atrBussTOLicIcgCalcDetailDao.findByMainId(atrDapDrawVo.getMainId());
				map.put(MAP_RETURN_PUBLIC_KEY, detailTO);
				break;
			default:
				break;
		}

		return map;
	}

	@Override
	public Map<String, Object> findCalcClaimPaid(AtrDapDrawVo atrDapDrawVo) {
		if (atrDapDrawVo == null  || atrDapDrawVo.getActionNo() == null) {
			return new HashMap<>();
		}
		Map<String, Object> map = new HashMap<String, Object>();
		switch (atrDapDrawVo.getBusinessSourceCode()){
			case "DD":
				List<AtrBussDDLicIcgCalcVo> detailDD = atrBussDDLicIcgCalcDao.findClaimPaid(atrDapDrawVo);
				for (AtrBussDDLicIcgCalcVo vo : detailDD) {
					atrDapDrawVo.setIcgNo(vo.getIcgNo());
					atrDapDrawVo.setAccidentYearMonth(vo.getAccidentYearMonth());
					List<AtrBussDDLicIcgCalcDetailVo> detailList = atrBussDDLicIcgCalcDetailDao.findDevByVo(atrDapDrawVo);
					vo.setDetailList(detailList);
				}
				map.put(MAP_RETURN_PUBLIC_KEY, detailDD);
				break;
			case "FO":
				List<AtrBussFOLicIcgCalcVo> detailFO = atrBussFOLicIcgCalcDao.findClaimPaid(atrDapDrawVo);
				for (AtrBussFOLicIcgCalcVo vo : detailFO) {
					atrDapDrawVo.setIcgNo(vo.getIcgNo());
					atrDapDrawVo.setAccidentYearMonth(vo.getAccidentYearMonth());
					List<AtrBussFOLicIcgCalcDetailVo> detailList = atrBussFOLicIcgCalcDetailDao.findByVo(atrDapDrawVo);
					vo.setDetailList(detailList);
				}
				map.put(MAP_RETURN_PUBLIC_KEY, detailFO);
				break;
			case "TI":
				List<AtrBussTILicIcgCalcVo> detailTI = atrBussTILicIcgCalcDao.findClaimPaid(atrDapDrawVo);
				for (AtrBussTILicIcgCalcVo vo : detailTI) {
					atrDapDrawVo.setIcgNo(vo.getIcgNo());
					atrDapDrawVo.setAccidentYearMonth(vo.getAccidentYearMonth());
					List<AtrBussTILicIcgCalcDetailVo> detailList = atrBussTILicIcgCalcDetailDao.findByVo(atrDapDrawVo);
					vo.setDetailList(detailList);
				}
				map.put(MAP_RETURN_PUBLIC_KEY, detailTI);
				break;
			case "TO":
				List<AtrBussTOLicIcgCalcVo> detailTO = atrBussTOLicIcgCalcDao.findClaimPaid(atrDapDrawVo);
				for (AtrBussTOLicIcgCalcVo vo : detailTO) {
					atrDapDrawVo.setIcgNo(vo.getIcgNo());
					atrDapDrawVo.setAccidentYearMonth(vo.getAccidentYearMonth());
					List<AtrBussTOLicIcgCalcDetailVo> detailList = atrBussTOLicIcgCalcDetailDao.findByVo(atrDapDrawVo);
					vo.setDetailList(detailList);
				}
				map.put(MAP_RETURN_PUBLIC_KEY, detailTO);
				break;
			default:
				break;
		}

		return map;
	}

	private void procCalc(AtrBussLicCashFlowVo vo) {
		Long entityId = vo.getEntityId();
		String yearMonth = vo.getYearMonth();
		String businessSourceCode = vo.getBusinessSourceCode();
		SpringContextUtil.getBean(AtrBussLicService.class).entry(entityId, yearMonth, businessSourceCode);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = UnexpectedRollbackException.class)
	public Boolean confirm(AtrBussLicCashFlowVo bussLicCashFlowVo, Long userId)  throws UnexpectedRollbackException {
		Boolean confirmFlag = true;
		AtrBussLicCashFlow atrBussLicCashFlow = new AtrBussLicCashFlow();
		atrBussLicCashFlow.setEntityId(bussLicCashFlowVo.getEntityId());
		atrBussLicCashFlow.setYearMonth(bussLicCashFlowVo.getYearMonth());
		atrBussLicCashFlow.setBusinessSourceCode(bussLicCashFlowVo.getBusinessSourceCode());
		atrBussLicCashFlow.setConfirmIs(CommonConstant.VersionStatus.CONFIRMED);
		Long confirmCount = atrBussLicCashFlowDao.count(atrBussLicCashFlow);
		if (ObjectUtils.isNotEmpty(confirmCount) && confirmCount==0) {
			try {
				AtrBussLicCashFlow po = ClassUtil.convert(bussLicCashFlowVo, AtrBussLicCashFlow.class);

				Date date = new Date();
				po.setConfirmIs("1");
				po.setConfirmUser(userId);
				po.setConfirmTime(date);
				po.setUpdatorId(userId);
				po.setUpdateTime(date);
				atrBussLicCashFlowDao.updateById(po);

				AtrConfBussPeriodVo atrConfBussPeriodVo = new AtrConfBussPeriodVo();
				atrConfBussPeriodVo.setEntityId(bussLicCashFlowVo.getEntityId());
				atrConfBussPeriodVo.setYearMonth(bussLicCashFlowVo.getYearMonth());
				atrConfBussPeriodVo.setProcCode(ActuarialConstant.ProcCode.EXPECTED_CF_LIC);
				atrConfModelDefService.confirmBeCFProc(atrConfBussPeriodVo, userId);
			} catch (UnexpectedRollbackException e) {
				LOG.error(e.getLocalizedMessage(), e);
				throw e;
			}
		} else {
			confirmFlag = false;
		}
		return confirmFlag;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
	public void revoke(AtrBussLicCashFlowVo bussLicCashFlowVo, Long userId) {
		AtrBussLicCashFlow atrBussLicCashFlow = new AtrBussLicCashFlow();
		atrBussLicCashFlow.setEntityId(bussLicCashFlowVo.getEntityId());
		atrBussLicCashFlow.setYearMonth(bussLicCashFlowVo.getYearMonth());
		atrBussLicCashFlow.setBusinessSourceCode(bussLicCashFlowVo.getBusinessSourceCode());
		atrBussLicCashFlow.setConfirmIs(CommonConstant.VersionStatus.CONFIRMED);
		Long confirmCount = atrBussLicCashFlowDao.count(atrBussLicCashFlow);
		if (ObjectUtils.isNotEmpty(confirmCount) && confirmCount > 0) {
			Date date = new Date();
			atrBussLicCashFlow.setConfirmIs(CommonConstant.VersionStatus.PENDING);
			atrBussLicCashFlow.setConfirmUser(userId);
			atrBussLicCashFlow.setConfirmTime(date);
			atrBussLicCashFlow.setUpdatorId(userId);
			atrBussLicCashFlow.setUpdateTime(date);
			atrBussLicCashFlowDao.revoke(atrBussLicCashFlow);
		}
	}

	@Override
	//@TrackActuarialProcess
	public void calculateAll(AtrBussLicCashFlowVo licCashFlowVo, Long userId) {
		Long entityId = licCashFlowVo.getEntityId();
		String yearMonth = licCashFlowVo.getYearMonth();
		SpringContextUtil.getBean(AtrBussLicService.class).entry(entityId, yearMonth, "DD");
		SpringContextUtil.getBean(AtrBussLicService.class).entry(entityId, yearMonth, "FO");
		SpringContextUtil.getBean(AtrBussLicService.class).entry(entityId, yearMonth, "TI");
		SpringContextUtil.getBean(AtrBussLicService.class).entry(entityId, yearMonth, "TO");
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
	public String confirmLicCfVersion(AtrBussLicCashFlowVo licCashFlowVo, Long userId) {
		List<String> businessModelList = bmsConfCodeFeignClient.findByCodeIdx("BusinessModel/Base");
		if (ObjectUtils.isEmpty(businessModelList)) {
			return "not business model";
		}
 		//po.setCurrencyCode(licCashFlowVo.getCurrencyCode());
		businessModelList.forEach(businessModel-> {
			AtrBussLicCashFlow po = new AtrBussLicCashFlow();
			po.setEntityId(licCashFlowVo.getEntityId());
			po.setYearMonth(licCashFlowVo.getYearMonth());
			po.setBusinessSourceCode(businessModel);
			po.setConfirmIs(CommonConstant.VersionStatus.CONFIRMED);
			Long confirmCount = atrBussLicCashFlowDao.count(po);
			po.setConfirmIs("0");
			Long unConfirmCount = atrBussLicCashFlowDao.count(po);
			if (ObjectUtils.isNotEmpty(confirmCount) && confirmCount == 0 && ObjectUtils.isNotEmpty(unConfirmCount) && unConfirmCount > 0) {
 				Date date = new Date();
				po.setConfirmIs(CommonConstant.VersionStatus.CONFIRMED);
				po.setConfirmUser(userId);
				po.setConfirmTime(date);
				atrBussLicCashFlowDao.updateConfirm(po);
			}
		});
		AtrConfBussPeriodVo atrConfBussPeriodVo = new AtrConfBussPeriodVo();
		atrConfBussPeriodVo.setEntityId(licCashFlowVo.getEntityId());
		atrConfBussPeriodVo.setYearMonth(licCashFlowVo.getYearMonth());
		atrConfBussPeriodVo.setProcCode(ActuarialConstant.ProcCode.EXPECTED_CF_LIC);
		atrConfModelDefService.confirmBeCFProc(atrConfBussPeriodVo, userId);
		return null;
	}

	@Override
	public Page<AtrDapDrawVo> findPortfolioData(AtrDapDrawVo atrDapDrawVo, Pageable pageParam) {
		if (atrDapDrawVo == null || atrDapDrawVo.getId() == null) {
			return new Page<AtrDapDrawVo>();
		}
		atrDapDrawVo.setPortfolioNo(FilterUtil.transitionSearch(atrDapDrawVo.getPortfolioNo()));
		atrDapDrawVo.setIcgNo(FilterUtil.transitionSearch(atrDapDrawVo.getIcgNo()));
		AtrBussLicCashFlowVo atrBussLicCashFlowVo = atrBussLicCashFlowDao.findByid(atrDapDrawVo.getId());
		Page<AtrDapDrawVo> result = new Page<>();
		if(ObjectUtils.isNotEmpty(atrBussLicCashFlowVo)&&ObjectUtils.isNotEmpty(atrBussLicCashFlowVo.getEntityId())
				&&ObjectUtils.isNotEmpty(atrBussLicCashFlowVo.getBusinessSourceCode())&&ObjectUtils.isNotEmpty(atrBussLicCashFlowVo.getActionNo())
				 &&ObjectUtils.isNotEmpty(atrBussLicCashFlowVo.getYearMonth())){
			atrDapDrawVo.setBusinessSourceCode(atrBussLicCashFlowVo.getBusinessSourceCode());
			atrDapDrawVo.setEntityId(atrBussLicCashFlowVo.getEntityId());
			atrDapDrawVo.setCurrencyCode(atrBussLicCashFlowVo.getCurrencyCode());
			atrDapDrawVo.setActionNo(atrBussLicCashFlowVo.getActionNo());
			atrDapDrawVo.setYearMonth(atrBussLicCashFlowVo.getYearMonth());
			result = atrBussLicCashFlowDao.findPortfolioData(atrDapDrawVo, pageParam);
		}
		return result;
	}

	@Override
	public void exportLicResultExcel(HttpServletRequest request, HttpServletResponse response, AtrBussLicCashFlowVo atrBussLicCashFlowVo, Long userId) throws Exception {
		AtrBussLicCashFlow atrBussLicCashFlow = atrBussLicCashFlowDao.findById(atrBussLicCashFlowVo.getId());
		if (ObjectUtils.isEmpty(atrBussLicCashFlow)) {
			return;
		}
		if (ObjectUtils.isEmpty(atrBussLicCashFlowVo.getTemplateFileName())) {
			throw new RuntimeException("Excel Template is null");
		}
		//获取语言
		String language = request.getHeader("ss-Language");
		if(StringUtil.isEmpty(language)){
			language = "en";
		}
		atrBussLicCashFlowVo.setEntityId(atrBussLicCashFlow.getEntityId());
		atrBussLicCashFlowVo.setCurrencyCode(atrBussLicCashFlow.getCurrencyCode());
		atrBussLicCashFlowVo.setBusinessSourceCode(atrBussLicCashFlow.getBusinessSourceCode());
		atrBussLicCashFlowVo.setActionNo(atrBussLicCashFlow.getActionNo());
		atrBussLicCashFlowVo.setLanguage(language);
		List<AtrDapDrawVo> accidentDevNoList = atrBussLicIcgCalcBusinessTypeDao.findDevNo(atrBussLicCashFlowVo);
		List<AtrDapDrawVo> icgCalcDetailDevNoList = atrBussLicIcgCalcBusinessTypeDao.findAccidentDevNo(atrBussLicCashFlowVo);
		String templateExcelFilePath = generateTemplateExcel(request, icgCalcDetailDevNoList, accidentDevNoList, atrBussLicCashFlowVo);

		atrBussLicCashFlowVo.setCreatorId(userId);
		atrBussLicCashFlowVo.setAccidentDevNoList(accidentDevNoList.stream().map(AtrDapDrawVo::getDevNo).collect(Collectors.toList()));
		atrBussLicCashFlowVo.setIcgDetailDevNoList(icgCalcDetailDevNoList.stream().map(AtrDapDrawVo::getDevNo).mapToInt(i -> i).boxed().collect(Collectors.toList()));
		this.fillLicDataToExcel(request, response, atrBussLicCashFlowVo, templateExcelFilePath);
	}

	@Override
	public void exportLicAioiExcel(HttpServletRequest request, HttpServletResponse response, AtrBussLicCashFlowVo atrBussLicCashFlowVo, Long userId) throws Exception {

	}

	@Override
	public List<AtrBussLicIcgAmountVo> showExpectedClaimTotal(AtrDapDrawVo atrDapDrawVo) {
		List<AtrBussLicIcgAmountVo>  atrBussLicIcgAmountVos = atrBussLicIcgCalcBusinessTypeDao.showExpectedClaimTotal(atrDapDrawVo);
		return atrBussLicIcgAmountVos;
	}

	@Override
	public void reSetLicCashFlow(AtrConfBussPeriodVo atrConfBussPeriodVo) {
		atrBussLicCashFlowDao.reSetLicCashFlow(atrConfBussPeriodVo);
	}


	/**
	 * @Description: 生成LIC填充数据Excel模板
	 * @Author: wyh.
	 * @CreateDate: 2024/2/27
	 */
	private String generateTemplateExcel(HttpServletRequest request, List<AtrDapDrawVo> icgCalcDetailDevNoList, List<AtrDapDrawVo> accidentDevNoList, AtrBussLicCashFlowVo atrBussLicCashFlowVo) throws Exception{
		List<ExcelSheet> sheetList = new ArrayList<>();
		List<ExcelSheetData> ultSheetDataList = new ArrayList<>();
		ExcelSheetData icgWeightSheetData = new ExcelSheetData("dp", icgCalcDetailDevNoList, WriteDirectionEnum.HORIZONTAL );
		ultSheetDataList.add(icgWeightSheetData);
		ExcelSheet ultSheet = new ExcelSheet(1, ultSheetDataList);

		List<ExcelSheetData> claimPatternSheetDataList = new ArrayList<>();
		ExcelSheetData claimPatternSheetData = new ExcelSheetData("dp", accidentDevNoList, WriteDirectionEnum.HORIZONTAL);
		claimPatternSheetDataList.add(claimPatternSheetData);
		ExcelSheet claimPatternSheet = new ExcelSheet(2, claimPatternSheetDataList);

		List<ExcelSheetData> licSheetDataList = new ArrayList<>();
		ExcelSheetData licSheetData = new ExcelSheetData("dp", icgCalcDetailDevNoList, WriteDirectionEnum.HORIZONTAL);
		licSheetDataList.add(licSheetData);
		ExcelSheet licSheet = new ExcelSheet(3, claimPatternSheetDataList);

		sheetList.add(ultSheet);
		sheetList.add(claimPatternSheet);
		sheetList.add(licSheet);
		return atrExportService.generateExcelBySheetList(request, sheetList, atrBussLicCashFlowVo.getTemplateFileName());
	}

	/**
	 * @Description: 填充LIC结果数据到Excel上
	 * @Author: wyh.
	 * @CreateDate: 2024/2/27
	 */
	private void fillLicDataToExcel(HttpServletRequest request, HttpServletResponse response, AtrBussLicCashFlowVo atrBussLicCashFlowVo,String templateFile) throws Exception{
		//1.查询实付赔款
		AtrDapDrawVo atrDapDrawVo = ClassUtil.convert(atrBussLicCashFlowVo, AtrDapDrawVo.class);
		Map<String, Object> calcClaimPaidMap = this.findClaimPaid(atrDapDrawVo);
		List<AtrBussDDLicIcgCalcVo> licIcgCalcVoList = (List<AtrBussDDLicIcgCalcVo>)calcClaimPaidMap.get(MAP_RETURN_PUBLIC_KEY);

		List<ExcelSheet> sheetList = new ArrayList<>();
		List<ExcelSheetData> claimPaidDataList = new ArrayList<>();
		ExcelSheetData claimPaidDataListSheetData = new ExcelSheetData("df", licIcgCalcVoList);
		claimPaidDataList.add(claimPaidDataListSheetData);
		ExcelSheet claimPaidSheet = new ExcelSheet(0, claimPaidDataList);
		sheetList.add(claimPaidSheet);

		//2.查询ULT
		List<Map<String, Object>> ultMap = findClaimUlt(atrBussLicCashFlowVo);
		List<ExcelSheetData> ultDataList = new ArrayList<>();
		ExcelSheetData ultDataListSheetData = new ExcelSheetData("df", ultMap);
		ultDataList.add(ultDataListSheetData);
		ExcelSheet ultSheet = new ExcelSheet(1, ultDataList);
		sheetList.add(ultSheet);

		//3.查询Mode
		List<Map<String, Object>> claimPatternMap = findClaimPattern(atrBussLicCashFlowVo);
		List<ExcelSheetData> claimPatternDataList = new ArrayList<>();
		ExcelSheetData claimPatternDataListSheetData = new ExcelSheetData("df", claimPatternMap);
		claimPatternDataList.add(claimPatternDataListSheetData);
		ExcelSheet claimPatternSheet = new ExcelSheet(2, claimPatternDataList);
		sheetList.add(claimPatternSheet);

		//4.查询LIC
		List<Map<String, Object>> licMap = findLic(atrBussLicCashFlowVo);
		List<ExcelSheetData> licDataList = new ArrayList<>();
		ExcelSheetData licDataListSheetData = new ExcelSheetData("df", licMap);
		licDataList.add(licDataListSheetData);
		ExcelSheet licSheet = new ExcelSheet(3, licDataList);
		sheetList.add(licSheet);

		atrExportService.exportSheetList(request,response, sheetList, templateFile,
				atrBussLicCashFlowVo.getTemplateFileName(),atrBussLicCashFlowVo.getLogFileName(), atrBussLicCashFlowVo.getTargetRouter(), atrBussLicCashFlowVo.getCreatorId());

	}

	/**
	 * @Description: 查询ult比例结果,按合同组号分组查询
	 * @Author: wyh.
	 * @CreateDate: 2024/2/27
	 */
 	private List<Map<String, Object>> findClaimUlt(AtrBussLicCashFlowVo atrBussLicCashFlowVo) {
		List<Map<String, Object>> ultResultListMap = atrBussLicIcgCalcBusinessTypeDao.findUltDev(atrBussLicCashFlowVo);
		return ultResultListMap;
	}

	/**
	 * @Description: 查询赔付模式结果,按合同组号分组查询
	 * @Author: wyh.
	 * @CreateDate: 2024/2/27
	 */
	private List<Map<String, Object>> findClaimPattern(AtrBussLicCashFlowVo atrBussLicCashFlowVo) {
		List<Map<String, Object>> ultResultListMap = atrBussLicIcgCalcBusinessTypeDao.findClaimPattern(atrBussLicCashFlowVo);
		return ultResultListMap;
	}

	/**
	 * @Description: 查询LIC预期赔付结果,按合同组号分组查询
	 * @Author: wyh.
	 * @CreateDate: 2024/2/27
	 */
	private List<Map<String, Object>> findLic(AtrBussLicCashFlowVo atrBussLicCashFlowVo) {
		AtrConfCodeVo sysCodeVo = new AtrConfCodeVo();
		sysCodeVo.setCodeCodeIdx("ExpectedClaimType");
		List<AtrConfCodeVo> confCodeVoList = atrConfCodeService.findCodeByCodeIdx(sysCodeVo);
		List<Map<String, Object>> licResultListMap = atrBussLicIcgCalcBusinessTypeDao.findLicDev(confCodeVoList, atrBussLicCashFlowVo);
		return licResultListMap;
	}

}
