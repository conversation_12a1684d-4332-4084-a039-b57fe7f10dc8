<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-06-05 14:31:46 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.reserve.AtrBussReserveEarnedDetailDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveEarnedDetail">
    <id column="EARNED_DETAIL_ID" property="earnedDetailId" jdbcType="DECIMAL" />
    <result column="RESERVE_EARNED_ID" property="reserveEarnedId" jdbcType="DECIMAL" />
    <result column="risk_class_code" property="riskClassCode" jdbcType="VARCHAR" />
    <result column="RISK_CODE" property="riskCode" jdbcType="VARCHAR" />
    <result column="CMUNIT_NO" property="cmunitNo" jdbcType="VARCHAR" />
    <result column="POLICY_NO" property="policyNo" jdbcType="VARCHAR" />
    <result column="ENDORSE_SEQ_NO" property="endorseSeqNo" jdbcType="VARCHAR" />
    <result column="ENDORSE_NO" property="endorseNo" jdbcType="VARCHAR" />
    <result column="ICG_NO" property="icgNo" jdbcType="VARCHAR" />
    <result column="ICP_NO" property="icpNo" jdbcType="VARCHAR" />
    <result column="business_source_code" property="businessSourceCode" jdbcType="VARCHAR" />
    <result column="REINS_TYPE" property="reinsType" jdbcType="VARCHAR" />
    <result column="START_DATE" property="startDate" jdbcType="TIMESTAMP" />
    <result column="END_DATE" property="endDate" jdbcType="TIMESTAMP" />
    <result column="CHECK_DATE" property="checkDate" jdbcType="TIMESTAMP" />
    <result column="ATR_METHOD" property="atrMethod" jdbcType="VARCHAR" />
    <result column="LOA_CODE" property="loaCode" jdbcType="VARCHAR" />
    <result column="CURRENCY_CODE" property="currencyCode" jdbcType="VARCHAR" />
    <result column="PREMIUM" property="premium" jdbcType="DECIMAL" />
    <result column="COMM" property="Comm" jdbcType="DECIMAL" />
    <result column="DRAW_PREMIUM_START" property="drawPremiumStart" jdbcType="DECIMAL" />
    <result column="DRAW_PREMIUM_END" property="drawPremiumEnd" jdbcType="DECIMAL" />
    <result column="DRAW_COMM_START" property="drawCommStart" jdbcType="DECIMAL" />
    <result column="DRAW_COMM_END" property="drawCommEnd" jdbcType="DECIMAL" />
    <result column="DRAW_PREM_EARNED" property="drawPremEarned" jdbcType="DECIMAL" />
    <result column="DRAW_COMM_EARNED" property="drawCommEarned" jdbcType="DECIMAL" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    EARNED_DETAIL_ID, RESERVE_EARNED_ID, risk_class_code, RISK_CODE, CMUNIT_NO, POLICY_NO,
    ENDORSE_SEQ_NO, ENDORSE_NO, ICG_NO, ICP_NO, business_source_code, REINS_TYPE, START_DATE,
    END_DATE, CHECK_DATE, ATR_METHOD, LOA_CODE, CURRENCY_CODE, PREMIUM, COMM, DRAW_PREMIUM_START,
    DRAW_PREMIUM_END, DRAW_COMM_START, DRAW_COMM_END, DRAW_PREM_EARNED, DRAW_COMM_EARNED
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="earnedDetailId != null ">
          and EARNED_DETAIL_ID = #{earnedDetailId,jdbcType=DECIMAL}
      </if>
      <if test="reserveEarnedId != null ">
          and RESERVE_EARNED_ID = #{reserveEarnedId,jdbcType=DECIMAL}
      </if>
      <if test="riskClassCode != null and riskClassCode != ''">
          and risk_class_code = #{riskClassCode,jdbcType=VARCHAR}
      </if>
      <if test="riskCode != null and riskCode != ''">
          and RISK_CODE = #{riskCode,jdbcType=VARCHAR}
      </if>
      <if test="cmunitNo != null and cmunitNo != ''">
          and CMUNIT_NO = #{cmunitNo,jdbcType=VARCHAR}
      </if>
      <if test="policyNo != null and policyNo != ''">
          and POLICY_NO = #{policyNo,jdbcType=VARCHAR}
      </if>
      <if test="endorseSeqNo != null and endorseSeqNo != ''">
          and ENDORSE_SEQ_NO = #{endorseSeqNo,jdbcType=VARCHAR}
      </if>
      <if test="endorseNo != null and endorseNo != ''">
          and ENDORSE_NO = #{endorseNo,jdbcType=VARCHAR}
      </if>
      <if test="icgNo != null and icgNo != ''">
          and ICG_NO = #{icgNo,jdbcType=VARCHAR}
      </if>
      <if test="icpNo != null and icpNo != ''">
          and ICP_NO = #{icpNo,jdbcType=VARCHAR}
      </if>
      <if test="businessSourceCode != null and businessSourceCode != ''">
          and business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="reinsType != null and reinsType != ''">
          and REINS_TYPE = #{reinsType,jdbcType=VARCHAR}
      </if>
      <if test="startDate != null ">
          and START_DATE = #{startDate,jdbcType=TIMESTAMP}
      </if>
      <if test="endDate != null ">
          and END_DATE = #{endDate,jdbcType=TIMESTAMP}
      </if>
      <if test="checkDate != null ">
          and CHECK_DATE = #{checkDate,jdbcType=TIMESTAMP}
      </if>
      <if test="atrMethod != null and atrMethod != ''">
          and ATR_METHOD = #{atrMethod,jdbcType=VARCHAR}
      </if>
      <if test="loaCode != null and loaCode != ''">
          and LOA_CODE = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="currencyCode != null and currencyCode != ''">
          and CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="premium != null ">
          and PREMIUM = #{premium,jdbcType=DECIMAL}
      </if>
      <if test="Comm != null ">
          and COMM = #{Comm,jdbcType=DECIMAL}
      </if>
      <if test="drawPremiumStart != null ">
          and DRAW_PREMIUM_START = #{drawPremiumStart,jdbcType=DECIMAL}
      </if>
      <if test="drawPremiumEnd != null ">
          and DRAW_PREMIUM_END = #{drawPremiumEnd,jdbcType=DECIMAL}
      </if>
      <if test="drawCommStart != null ">
          and DRAW_COMM_START = #{drawCommStart,jdbcType=DECIMAL}
      </if>
      <if test="drawCommEnd != null ">
          and DRAW_COMM_END = #{drawCommEnd,jdbcType=DECIMAL}
      </if>
      <if test="drawPremEarned != null ">
          and DRAW_PREM_EARNED = #{drawPremEarned,jdbcType=DECIMAL}
      </if>
      <if test="drawCommEarned != null ">
          and DRAW_COMM_EARNED = #{drawCommEarned,jdbcType=DECIMAL}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.earnedDetailId != null ">
          and EARNED_DETAIL_ID = #{condition.earnedDetailId,jdbcType=DECIMAL}
      </if>
      <if test="condition.reserveEarnedId != null ">
          and RESERVE_EARNED_ID = #{condition.reserveEarnedId,jdbcType=DECIMAL}
      </if>
      <if test="condition.riskClassCode != null and condition.riskClassCode != ''">
          and risk_class_code = #{condition.riskClassCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.riskCode != null and condition.riskCode != ''">
          and RISK_CODE = #{condition.riskCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.cmunitNo != null and condition.cmunitNo != ''">
          and CMUNIT_NO = #{condition.cmunitNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.policyNo != null and condition.policyNo != ''">
          and POLICY_NO = #{condition.policyNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.endorseSeqNo != null and condition.endorseSeqNo != ''">
          and ENDORSE_SEQ_NO = #{condition.endorseSeqNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.endorseNo != null and condition.endorseNo != ''">
          and ENDORSE_NO = #{condition.endorseNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.icgNo != null and condition.icgNo != ''">
          and ICG_NO = #{condition.icgNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.icpNo != null and condition.icpNo != ''">
          and ICP_NO = #{condition.icpNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.businessSourceCode != null and condition.businessSourceCode != ''">
          and business_source_code = #{condition.businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.reinsType != null and condition.reinsType != ''">
          and REINS_TYPE = #{condition.reinsType,jdbcType=VARCHAR}
      </if>
      <if test="condition.startDate != null ">
          and START_DATE = #{condition.startDate,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.endDate != null ">
          and END_DATE = #{condition.endDate,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.checkDate != null ">
          and CHECK_DATE = #{condition.checkDate,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.atrMethod != null and condition.atrMethod != ''">
          and ATR_METHOD = #{condition.atrMethod,jdbcType=VARCHAR}
      </if>
      <if test="condition.loaCode != null and condition.loaCode != ''">
          and LOA_CODE = #{condition.loaCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.currencyCode != null and condition.currencyCode != ''">
          and CURRENCY_CODE = #{condition.currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.premium != null ">
          and PREMIUM = #{condition.premium,jdbcType=DECIMAL}
      </if>
      <if test="condition.Comm != null ">
          and COMM = #{condition.Comm,jdbcType=DECIMAL}
      </if>
      <if test="condition.drawPremiumStart != null ">
          and DRAW_PREMIUM_START = #{condition.drawPremiumStart,jdbcType=DECIMAL}
      </if>
      <if test="condition.drawPremiumEnd != null ">
          and DRAW_PREMIUM_END = #{condition.drawPremiumEnd,jdbcType=DECIMAL}
      </if>
      <if test="condition.drawCommStart != null ">
          and DRAW_COMM_START = #{condition.drawCommStart,jdbcType=DECIMAL}
      </if>
      <if test="condition.drawCommEnd != null ">
          and DRAW_COMM_END = #{condition.drawCommEnd,jdbcType=DECIMAL}
      </if>
      <if test="condition.drawPremEarned != null ">
          and DRAW_PREM_EARNED = #{condition.drawPremEarned,jdbcType=DECIMAL}
      </if>
      <if test="condition.drawCommEarned != null ">
          and DRAW_COMM_EARNED = #{condition.drawCommEarned,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="earnedDetailId != null ">
          and EARNED_DETAIL_ID = #{earnedDetailId,jdbcType=DECIMAL}
      </if>
      <if test="reserveEarnedId != null ">
          and RESERVE_EARNED_ID = #{reserveEarnedId,jdbcType=DECIMAL}
      </if>
      <if test="riskClassCode != null and riskClassCode != ''">
          and risk_class_code = #{riskClassCode,jdbcType=VARCHAR}
      </if>
      <if test="riskCode != null and riskCode != ''">
          and RISK_CODE = #{riskCode,jdbcType=VARCHAR}
      </if>
      <if test="cmunitNo != null and cmunitNo != ''">
          and CMUNIT_NO = #{cmunitNo,jdbcType=VARCHAR}
      </if>
      <if test="policyNo != null and policyNo != ''">
          and POLICY_NO = #{policyNo,jdbcType=VARCHAR}
      </if>
      <if test="endorseSeqNo != null and endorseSeqNo != ''">
          and ENDORSE_SEQ_NO = #{endorseSeqNo,jdbcType=VARCHAR}
      </if>
      <if test="endorseNo != null and endorseNo != ''">
          and ENDORSE_NO = #{endorseNo,jdbcType=VARCHAR}
      </if>
      <if test="icgNo != null and icgNo != ''">
          and ICG_NO = #{icgNo,jdbcType=VARCHAR}
      </if>
      <if test="icpNo != null and icpNo != ''">
          and ICP_NO = #{icpNo,jdbcType=VARCHAR}
      </if>
      <if test="businessSourceCode != null and businessSourceCode != ''">
          and business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="reinsType != null and reinsType != ''">
          and REINS_TYPE = #{reinsType,jdbcType=VARCHAR}
      </if>
      <if test="startDate != null ">
          and START_DATE = #{startDate,jdbcType=TIMESTAMP}
      </if>
      <if test="endDate != null ">
          and END_DATE = #{endDate,jdbcType=TIMESTAMP}
      </if>
      <if test="checkDate != null ">
          and CHECK_DATE = #{checkDate,jdbcType=TIMESTAMP}
      </if>
      <if test="atrMethod != null and atrMethod != ''">
          and ATR_METHOD = #{atrMethod,jdbcType=VARCHAR}
      </if>
      <if test="loaCode != null and loaCode != ''">
          and LOA_CODE = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="currencyCode != null and currencyCode != ''">
          and CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="premium != null ">
          and PREMIUM = #{premium,jdbcType=DECIMAL}
      </if>
      <if test="Comm != null ">
          and COMM = #{Comm,jdbcType=DECIMAL}
      </if>
      <if test="drawPremiumStart != null ">
          and DRAW_PREMIUM_START = #{drawPremiumStart,jdbcType=DECIMAL}
      </if>
      <if test="drawPremiumEnd != null ">
          and DRAW_PREMIUM_END = #{drawPremiumEnd,jdbcType=DECIMAL}
      </if>
      <if test="drawCommStart != null ">
          and DRAW_COMM_START = #{drawCommStart,jdbcType=DECIMAL}
      </if>
      <if test="drawCommEnd != null ">
          and DRAW_COMM_END = #{drawCommEnd,jdbcType=DECIMAL}
      </if>
      <if test="drawPremEarned != null ">
          and DRAW_PREM_EARNED = #{drawPremEarned,jdbcType=DECIMAL}
      </if>
      <if test="drawCommEarned != null ">
          and DRAW_COMM_EARNED = #{drawCommEarned,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_RESERVE_EARNED_DETAIL
    where EARNED_DETAIL_ID = #{earnedDetailId,jdbcType=DECIMAL}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_RESERVE_EARNED_DETAIL
    where EARNED_DETAIL_ID in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_RESERVE_EARNED_DETAIL
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveEarnedDetail">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_RESERVE_EARNED_DETAIL
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_RESERVE_EARNED_DETAIL
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="EARNED_DETAIL_ID" keyProperty="earnedDetailId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveEarnedDetail">
    <selectKey resultType="long" keyProperty="earnedDetailId" order="BEFORE">
      select nextval('atr_seq_buss_reserve_earned_detail') as sequenceNo
    </selectKey>
    insert into ATR_BUSS_RESERVE_EARNED_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="earnedDetailId != null">
        EARNED_DETAIL_ID,
      </if>
      <if test="reserveEarnedId != null">
        RESERVE_EARNED_ID,
      </if>
      <if test="riskClassCode != null">
        risk_class_code,
      </if>
      <if test="riskCode != null">
        RISK_CODE,
      </if>
      <if test="cmunitNo != null">
        CMUNIT_NO,
      </if>
      <if test="policyNo != null">
        POLICY_NO,
      </if>
      <if test="endorseSeqNo != null">
        ENDORSE_SEQ_NO,
      </if>
      <if test="endorseNo != null">
        ENDORSE_NO,
      </if>
      <if test="icgNo != null">
        ICG_NO,
      </if>
      <if test="icpNo != null">
        ICP_NO,
      </if>
      <if test="businessSourceCode != null">
        business_source_code,
      </if>
      <if test="reinsType != null">
        REINS_TYPE,
      </if>
      <if test="startDate != null">
        START_DATE,
      </if>
      <if test="endDate != null">
        END_DATE,
      </if>
      <if test="checkDate != null">
        CHECK_DATE,
      </if>
      <if test="atrMethod != null">
        ATR_METHOD,
      </if>
      <if test="loaCode != null">
        LOA_CODE,
      </if>
      <if test="currencyCode != null">
        CURRENCY_CODE,
      </if>
      <if test="premium != null">
        PREMIUM,
      </if>
      <if test="Comm != null">
        COMM,
      </if>
      <if test="drawPremiumStart != null">
        DRAW_PREMIUM_START,
      </if>
      <if test="drawPremiumEnd != null">
        DRAW_PREMIUM_END,
      </if>
      <if test="drawCommStart != null">
        DRAW_COMM_START,
      </if>
      <if test="drawCommEnd != null">
        DRAW_COMM_END,
      </if>
      <if test="drawPremEarned != null">
        DRAW_PREM_EARNED,
      </if>
      <if test="drawCommEarned != null">
        DRAW_COMM_EARNED,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="earnedDetailId != null">
        #{earnedDetailId,jdbcType=DECIMAL},
      </if>
      <if test="reserveEarnedId != null">
        #{reserveEarnedId,jdbcType=DECIMAL},
      </if>
      <if test="riskClassCode != null">
        #{riskClassCode,jdbcType=VARCHAR},
      </if>
      <if test="riskCode != null">
        #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="cmunitNo != null">
        #{cmunitNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="endorseSeqNo != null">
        #{endorseSeqNo,jdbcType=VARCHAR},
      </if>
      <if test="endorseNo != null">
        #{endorseNo,jdbcType=VARCHAR},
      </if>
      <if test="icgNo != null">
        #{icgNo,jdbcType=VARCHAR},
      </if>
      <if test="icpNo != null">
        #{icpNo,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceCode != null">
        #{businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="reinsType != null">
        #{reinsType,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="checkDate != null">
        #{checkDate,jdbcType=TIMESTAMP},
      </if>
      <if test="atrMethod != null">
        #{atrMethod,jdbcType=VARCHAR},
      </if>
      <if test="loaCode != null">
        #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="currencyCode != null">
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="premium != null">
        #{premium,jdbcType=DECIMAL},
      </if>
      <if test="Comm != null">
        #{Comm,jdbcType=DECIMAL},
      </if>
      <if test="drawPremiumStart != null">
        #{drawPremiumStart,jdbcType=DECIMAL},
      </if>
      <if test="drawPremiumEnd != null">
        #{drawPremiumEnd,jdbcType=DECIMAL},
      </if>
      <if test="drawCommStart != null">
        #{drawCommStart,jdbcType=DECIMAL},
      </if>
      <if test="drawCommEnd != null">
        #{drawCommEnd,jdbcType=DECIMAL},
      </if>
      <if test="drawPremEarned != null">
        #{drawPremEarned,jdbcType=DECIMAL},
      </if>
      <if test="drawCommEarned != null">
        #{drawCommEarned,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all 
    <foreach collection="list" item="item" index="index">
      into ATR_BUSS_RESERVE_EARNED_DETAIL values 
       (#{item.earnedDetailId,jdbcType=DECIMAL}, 
        #{item.reserveEarnedId,jdbcType=DECIMAL}, #{item.riskClassCode,jdbcType=VARCHAR}, #{item.riskCode,jdbcType=VARCHAR},
        #{item.cmunitNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, #{item.endorseSeqNo,jdbcType=VARCHAR}, 
        #{item.endorseNo,jdbcType=VARCHAR}, #{item.icgNo,jdbcType=VARCHAR}, #{item.icpNo,jdbcType=VARCHAR}, 
        #{item.businessSourceCode,jdbcType=VARCHAR}, #{item.reinsType,jdbcType=VARCHAR}, #{item.startDate,jdbcType=TIMESTAMP},
        #{item.endDate,jdbcType=TIMESTAMP}, #{item.checkDate,jdbcType=TIMESTAMP}, #{item.atrMethod,jdbcType=VARCHAR}, 
        #{item.loaCode,jdbcType=VARCHAR}, #{item.currencyCode,jdbcType=VARCHAR}, #{item.premium,jdbcType=DECIMAL},
        #{item.Comm,jdbcType=DECIMAL}, #{item.drawPremiumStart,jdbcType=DECIMAL}, #{item.drawPremiumEnd,jdbcType=DECIMAL}, 
        #{item.drawCommStart,jdbcType=DECIMAL}, #{item.drawCommEnd,jdbcType=DECIMAL}, #{item.drawPremEarned,jdbcType=DECIMAL}, 
        #{item.drawCommEarned,jdbcType=DECIMAL})
    </foreach>
    select 1 from dual
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveEarnedDetail">
    update ATR_BUSS_RESERVE_EARNED_DETAIL
    <set>
      <if test="reserveEarnedId != null">
        RESERVE_EARNED_ID = #{reserveEarnedId,jdbcType=DECIMAL},
      </if>
      <if test="riskClassCode != null">
        risk_class_code = #{riskClassCode,jdbcType=VARCHAR},
      </if>
      <if test="riskCode != null">
        RISK_CODE = #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="cmunitNo != null">
        CMUNIT_NO = #{cmunitNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        POLICY_NO = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="endorseSeqNo != null">
        ENDORSE_SEQ_NO = #{endorseSeqNo,jdbcType=VARCHAR},
      </if>
      <if test="endorseNo != null">
        ENDORSE_NO = #{endorseNo,jdbcType=VARCHAR},
      </if>
      <if test="icgNo != null">
        ICG_NO = #{icgNo,jdbcType=VARCHAR},
      </if>
      <if test="icpNo != null">
        ICP_NO = #{icpNo,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceCode != null">
        business_source_code = #{businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="reinsType != null">
        REINS_TYPE = #{reinsType,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        START_DATE = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        END_DATE = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="checkDate != null">
        CHECK_DATE = #{checkDate,jdbcType=TIMESTAMP},
      </if>
      <if test="atrMethod != null">
        ATR_METHOD = #{atrMethod,jdbcType=VARCHAR},
      </if>
      <if test="loaCode != null">
        LOA_CODE = #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="currencyCode != null">
        CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="premium != null">
        PREMIUM = #{premium,jdbcType=DECIMAL},
      </if>
      <if test="Comm != null">
        COMM = #{Comm,jdbcType=DECIMAL},
      </if>
      <if test="drawPremiumStart != null">
        DRAW_PREMIUM_START = #{drawPremiumStart,jdbcType=DECIMAL},
      </if>
      <if test="drawPremiumEnd != null">
        DRAW_PREMIUM_END = #{drawPremiumEnd,jdbcType=DECIMAL},
      </if>
      <if test="drawCommStart != null">
        DRAW_COMM_START = #{drawCommStart,jdbcType=DECIMAL},
      </if>
      <if test="drawCommEnd != null">
        DRAW_COMM_END = #{drawCommEnd,jdbcType=DECIMAL},
      </if>
      <if test="drawPremEarned != null">
        DRAW_PREM_EARNED = #{drawPremEarned,jdbcType=DECIMAL},
      </if>
      <if test="drawCommEarned != null">
        DRAW_COMM_EARNED = #{drawCommEarned,jdbcType=DECIMAL},
      </if>
    </set>
    where EARNED_DETAIL_ID = #{earnedDetailId,jdbcType=DECIMAL}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveEarnedDetail">
    update ATR_BUSS_RESERVE_EARNED_DETAIL
    <set>
      <if test="record.reserveEarnedId != null">
        RESERVE_EARNED_ID = #{record.reserveEarnedId,jdbcType=DECIMAL},
      </if>
      <if test="record.riskClassCode != null">
        risk_class_code = #{record.riskClassCode,jdbcType=VARCHAR},
      </if>
      <if test="record.riskCode != null">
        RISK_CODE = #{record.riskCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cmunitNo != null">
        CMUNIT_NO = #{record.cmunitNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        POLICY_NO = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.endorseSeqNo != null">
        ENDORSE_SEQ_NO = #{record.endorseSeqNo,jdbcType=VARCHAR},
      </if>
      <if test="record.endorseNo != null">
        ENDORSE_NO = #{record.endorseNo,jdbcType=VARCHAR},
      </if>
      <if test="record.icgNo != null">
        ICG_NO = #{record.icgNo,jdbcType=VARCHAR},
      </if>
      <if test="record.icpNo != null">
        ICP_NO = #{record.icpNo,jdbcType=VARCHAR},
      </if>
      <if test="record.businessSourceCode != null">
        business_source_code = #{record.businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.reinsType != null">
        REINS_TYPE = #{record.reinsType,jdbcType=VARCHAR},
      </if>
      <if test="record.startDate != null">
        START_DATE = #{record.startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endDate != null">
        END_DATE = #{record.endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.checkDate != null">
        CHECK_DATE = #{record.checkDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.atrMethod != null">
        ATR_METHOD = #{record.atrMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.loaCode != null">
        LOA_CODE = #{record.loaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.currencyCode != null">
        CURRENCY_CODE = #{record.currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.premium != null">
        PREMIUM = #{record.premium,jdbcType=DECIMAL},
      </if>
      <if test="record.Comm != null">
        COMM = #{record.Comm,jdbcType=DECIMAL},
      </if>
      <if test="record.drawPremiumStart != null">
        DRAW_PREMIUM_START = #{record.drawPremiumStart,jdbcType=DECIMAL},
      </if>
      <if test="record.drawPremiumEnd != null">
        DRAW_PREMIUM_END = #{record.drawPremiumEnd,jdbcType=DECIMAL},
      </if>
      <if test="record.drawCommStart != null">
        DRAW_COMM_START = #{record.drawCommStart,jdbcType=DECIMAL},
      </if>
      <if test="record.drawCommEnd != null">
        DRAW_COMM_END = #{record.drawCommEnd,jdbcType=DECIMAL},
      </if>
      <if test="record.drawPremEarned != null">
        DRAW_PREM_EARNED = #{record.drawPremEarned,jdbcType=DECIMAL},
      </if>
      <if test="record.drawCommEarned != null">
        DRAW_COMM_EARNED = #{record.drawCommEarned,jdbcType=DECIMAL},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from ATR_BUSS_RESERVE_EARNED_DETAIL
    where EARNED_DETAIL_ID = #{earnedDetailId,jdbcType=DECIMAL}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from ATR_BUSS_RESERVE_EARNED_DETAIL
    where EARNED_DETAIL_ID in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from ATR_BUSS_RESERVE_EARNED_DETAIL
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveEarnedDetail">
    select count(1) from ATR_BUSS_RESERVE_EARNED_DETAIL
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>