package com.ss.ifrs.actuarial.service.impl.puhua;

import com.ss.ifrs.actuarial.constant.ActuarialConstant;
import com.ss.ifrs.actuarial.constant.AtrExceptionEnum;
import com.ss.ifrs.actuarial.dao.buss.alloc.*;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussDiffIbnrAllocVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrAllocAction;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussClaimImportMainVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrAllocActionVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrAllocResultVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussIbnrImportMainVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.ifrs.actuarial.service.AtrBussIbnrClaimImportService;
import com.ss.ifrs.actuarial.service.AtrBussIbnrImportService;
import com.ss.ifrs.actuarial.service.puhua.AtrBussIbnrSecAllocService;
import com.ss.ifrs.actuarial.service.AtrExportService;
import com.ss.ifrs.actuarial.util.EcfUtil;
import com.ss.library.excel.ExcelSheet;
import com.ss.library.excel.ExcelSheetData;
import com.ss.library.mybatis.handler.CommonResultHandler;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.thread.ThreadUtils;
import com.ss.library.utils.ClassUtil;
import com.ss.library.utils.ExcelDownloadUtil;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.ThreadConstant;
import com.ss.platform.core.exception.BusinessException;
import com.ss.platform.util.ExcelExportUtil;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Future;

@Service
public class AtrBussIbnrSecAllocServiceImpl implements AtrBussIbnrSecAllocService {
    final Logger LOG = LoggerFactory.getLogger(getClass());

    @Autowired
    private AtrBussIbnrImportService atrBussIbnrImportService;

    @Autowired
    private AtrBussIbnrClaimImportService atrBussIbnrClaimImportService;

    @Autowired
    private AtrBussIbnrAllocActionDao atrBussIbnrAllocActionDao;

    @Autowired
    private AtrBussIbnrAllocDdResultDao bussIbnrAllocDdResultDao;

    @Autowired
    private AtrBussIbnrAllocTiResultDao bussIbnrAllocTiResultDao;

    @Autowired
    private AtrBussIbnrAllocFoResultDao bussIbnrAllocFoResultDao;

    @Autowired
    private AtrBussIbnrAllocToResultDao bussIbnrAllocToResultDao;

    @Autowired
    private AtrBussIbnrAllocOutResultDao bussIbnrAllocOutResultDao;

    @Autowired
    private AtrBussIbnrAllocToResultXDao bussIbnrAllocToResultXDao;

    @Autowired
    AtrExportService atrExportService;

    @Override
    public Page<AtrBussIbnrAllocActionVo> findForDataTables(AtrBussIbnrAllocActionVo atrBussIbnrAllocActionVo, Pageable pageParam) {
        Page<AtrBussIbnrAllocActionVo> list = atrBussIbnrAllocActionDao.fuzzySearchPage(atrBussIbnrAllocActionVo, pageParam);
        return list;
    }

    @Override
    public void delete(Long id, Long userId) {
        atrBussIbnrAllocActionDao.deleteById(id);
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = RuntimeException.class)
    public Boolean confirm(AtrBussIbnrAllocActionVo bussLicCashFlowVo, Long userId) {
        Boolean confirmFlag = true;
        AtrBussIbnrAllocAction atrBussLrcAction = new AtrBussIbnrAllocAction();
        atrBussLrcAction.setEntityId(bussLicCashFlowVo.getEntityId());
        atrBussLrcAction.setYearMonth(bussLicCashFlowVo.getYearMonth());
        atrBussLrcAction.setBusinessSourceCode(bussLicCashFlowVo.getBusinessSourceCode());
        atrBussLrcAction.setConfirmIs(CommonConstant.VersionStatus.CONFIRMED);
        Long confirmCount = atrBussIbnrAllocActionDao.count(atrBussLrcAction);
        if (ObjectUtils.isNotEmpty(confirmCount) && confirmCount==0) {
            AtrBussIbnrAllocAction po = new AtrBussIbnrAllocAction();
            Date date = new Date();
            po.setConfirmIs(CommonConstant.VersionStatus.CONFIRMED);
            po.setConfirmUser(userId);
            po.setConfirmTime(date);
            po.setUpdatorId(userId);
            po.setUpdateTime(date);
            if (ObjectUtils.isNotEmpty(bussLicCashFlowVo.getId())) {
                po.setId(bussLicCashFlowVo.getId());
                atrBussIbnrAllocActionDao.updateById(po);
            } else{
                po.setEntityId(bussLicCashFlowVo.getEntityId());
                po.setYearMonth(bussLicCashFlowVo.getYearMonth());
                po.setBusinessSourceCode(bussLicCashFlowVo.getBusinessSourceCode());
                atrBussIbnrAllocActionDao.updateConfirm(po);
            }
            this.pullIbnrDataToLic(bussLicCashFlowVo);



            AtrConfBussPeriodVo atrConfBussPeriodVo = new AtrConfBussPeriodVo();
            atrConfBussPeriodVo.setEntityId(bussLicCashFlowVo.getEntityId());
            atrConfBussPeriodVo.setYearMonth(bussLicCashFlowVo.getYearMonth());
            atrConfBussPeriodVo.setProcCode(ActuarialConstant.ProcCode.EXPECTED_CF_LRC);
            //atrConfModelDefService.confirmBeCFProc(atrConfBussPeriodVo, userId);
        } else {
            confirmFlag = false;
        }
        return confirmFlag;
    }

    @Override
    public void calculateAll(AtrBussIbnrAllocActionVo ibnrAllocActionVo, Long userId) {
        AtrBussIbnrImportMainVo ibnrImportMainVo = ClassUtil.convert(ibnrAllocActionVo, AtrBussIbnrImportMainVo.class);
        if (hasIbnrCalc(ibnrAllocActionVo)) {
            LOG.info("有执行中的计算任务：" + atrBussIbnrImportService.hasIbnrData(ibnrImportMainVo));
            return;
        }
        LOG.info("结果：" + atrBussIbnrImportService.hasIbnrData(ibnrImportMainVo));
        if(atrBussIbnrImportService.hasIbnrData(ibnrImportMainVo)){
            AtrBussIbnrAllocAction po = ClassUtil.convert(ibnrAllocActionVo, AtrBussIbnrAllocAction.class);
            po.setActionNo(EcfUtil.createActionNo());
            po.setCreatorId(userId);
            po.setCreateTime(new Date());
            po.setStatus("R");
            po.setConfirmIs(CommonConstant.VersionStatus.PENDING);

            atrBussIbnrAllocActionDao.save(po);
            try{
                this.ibnrSecAllocStart(po);
                po.setStatus("S");
                atrBussIbnrAllocActionDao.updateById(po);
            } catch (Exception e) {
                LOG.error(e.getLocalizedMessage());
                po.setStatus("E");
                atrBussIbnrAllocActionDao.updateById(po);
            }
        }
    }

    @Override
    public Page<AtrBussIbnrAllocResultVo> findAllocList(AtrBussIbnrAllocActionVo allocActionVo, Pageable pageParam) {
        Page<AtrBussIbnrAllocResultVo> ibnrAllocActionVos = new Page<>();
        switch (allocActionVo.getBusinessSourceCode()) {
            case "DD" :
                ibnrAllocActionVos = bussIbnrAllocDdResultDao.findAllocList(allocActionVo, pageParam);
                break;
            case "TI" :
                ibnrAllocActionVos = bussIbnrAllocTiResultDao.findAllocList(allocActionVo, pageParam);
                break;
            case "FO":
                ibnrAllocActionVos = bussIbnrAllocFoResultDao.findAllocList(allocActionVo, pageParam);
                break;
            case "TO":
                ibnrAllocActionVos = bussIbnrAllocToResultDao.findAllocList(allocActionVo, pageParam);
                break;
            case "OUT":
                ibnrAllocActionVos = bussIbnrAllocOutResultDao.findAllocList(allocActionVo, pageParam);
                break;
            default:
                break;
        }
        return ibnrAllocActionVos;
    }

    @Override
    public Page<AtrBussIbnrAllocResultVo> findXAllocList(AtrBussIbnrAllocActionVo allocActionVo, Pageable pageParam) {
        return bussIbnrAllocToResultXDao.findAllocList(allocActionVo, pageParam);
    }

    @Override
    public void exportAllocData(HttpServletRequest request, HttpServletResponse response, AtrBussIbnrAllocActionVo allocActionVo, Long userId) throws Exception {
        String fileName = allocActionVo.getTemplateFileName();
        if (ObjectUtils.isEmpty(fileName)) {
            throw new Exception("Excel Name is null");
        }
        // 准备导出的sheet数据列表
        List<ExcelSheet> sheetList = new ArrayList<>();
        List<AtrBussIbnrAllocResultVo> ddIbnrAllocActionVos = bussIbnrAllocDdResultDao.findAllocList(allocActionVo);
        ExcelSheet ddExcelSheet = new ExcelSheet(0,  Arrays.asList(new ExcelSheetData("df", ddIbnrAllocActionVos)), false);
        sheetList.add(ddExcelSheet);
        List<AtrBussIbnrAllocResultVo> tiIbnrAllocActionVos = bussIbnrAllocTiResultDao.findAllocList(allocActionVo);
        ExcelSheet tiExcelSheet = new ExcelSheet(1,  Arrays.asList(new ExcelSheetData("df", tiIbnrAllocActionVos)), false);
        sheetList.add(tiExcelSheet);
        List<AtrBussIbnrAllocResultVo> foIbnrAllocActionVos = bussIbnrAllocFoResultDao.findAllocList(allocActionVo);
        ExcelSheet foExcelSheet = new ExcelSheet(2,  Arrays.asList(new ExcelSheetData("df", foIbnrAllocActionVos)), false);
        sheetList.add(foExcelSheet);

        List<AtrBussIbnrAllocResultVo> toIbnrAllocActionVos = bussIbnrAllocToResultDao.findAllocList(allocActionVo);
        ExcelSheet toExcelSheet = new ExcelSheet(3,  Arrays.asList(new ExcelSheetData("df", toIbnrAllocActionVos)), false);
        sheetList.add(toExcelSheet);

        List<AtrBussIbnrAllocResultVo> toXIbnrAllocActionVos = bussIbnrAllocToResultXDao.findAllocList(allocActionVo);
        ExcelSheet toXExcelSheet = new ExcelSheet(4,  Arrays.asList(new ExcelSheetData("df", toXIbnrAllocActionVos)), false);
        sheetList.add(toXExcelSheet);
        atrExportService.exportExcelSheetList(request, response, sheetList,
                fileName, "", allocActionVo.getTargetRouter(), userId);
    }

    @Override
    public Boolean hasIbnrCalc(AtrBussIbnrAllocActionVo allocActionVo) {
        AtrBussIbnrAllocAction atrBussIbnrImportMain = new AtrBussIbnrAllocAction();
        atrBussIbnrImportMain.setEntityId(allocActionVo.getEntityId());
        atrBussIbnrImportMain.setYearMonth(allocActionVo.getYearMonth());
        atrBussIbnrImportMain.setBusinessSourceCode(allocActionVo.getBusinessSourceCode());
        atrBussIbnrImportMain.setStatus("R");
        Long count = atrBussIbnrAllocActionDao.count(atrBussIbnrImportMain);
        return ObjectUtils.isNotEmpty(count) && count >0;
    }

    @Override
    public Boolean checkIbnrAllocResult(AtrBussIbnrAllocAction po) {
        Long count = null;
        switch (po.getBusinessSourceCode()) {
            case "DD" :
                count = atrBussIbnrAllocActionDao.countDDIbnrAllocImBalance(po);
                break;
            case "TI" :
                count = atrBussIbnrAllocActionDao.countTIIbnrAllocImBalance(po);
                break;
            case "OUT":
                count = atrBussIbnrAllocActionDao.countOutIbnrAllocImBalance(po);
                //count = count + atrBussIbnrAllocActionDao.countCaseAllocImBalance(po);
                break;
            default:
                break;
        }
         return ObjectUtils.isEmpty(count) || count == 0;
    }

    @Override
    public void downloadAllocData(HttpServletRequest request, HttpServletResponse response, AtrBussIbnrAllocActionVo allocActionVo, Long userId) throws Exception {
        switch (allocActionVo.getBusinessSourceCode()) {
            case "DD" :
                this.exportDDAllocData(request, response, allocActionVo, userId);
                break;
            case "TI" :
                this.exportTIAllocData(request, response, allocActionVo, userId);
                break;
            case "FO":
                this.exportFOAllocData(request, response, allocActionVo, userId);
                break;
            case "TO":
                this.exportTOAllocData(request, response, allocActionVo, userId);
            case "OUT":
                this.exportTOAllocData(request, response, allocActionVo, userId);
            default:
                break;
        }
    }

    @Override
    public void reSetIbnrCashFlow(AtrConfBussPeriodVo atrConfBussPeriodVo) {
        atrBussIbnrAllocActionDao.reSetIbnrCashFlow(atrConfBussPeriodVo);
    }


    @Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = RuntimeException.class)
    void ibnrSecAllocStart(AtrBussIbnrAllocAction bussIbnrAllocAction) {
        if (ObjectUtils.isNotEmpty(bussIbnrAllocAction) && ObjectUtils.isNotEmpty(bussIbnrAllocAction.getBusinessSourceCode())) {
            AtrBussIbnrImportMainVo ibnrImportMainVo = ClassUtil.convert(bussIbnrAllocAction, AtrBussIbnrImportMainVo.class);
            AtrBussIbnrImportMainVo bussIbnrImportMainVo = atrBussIbnrImportService.findByVo(ibnrImportMainVo);
            if (ObjectUtils.isNotEmpty(bussIbnrImportMainVo)) {
                  this.ibnrSecAllocDuct(bussIbnrAllocAction, bussIbnrImportMainVo);
            }
        } else {
            throw new BusinessException(AtrExceptionEnum.IBNR_NOT_BUSINESS);
        }
    }

    private void ibnrSecAllocDuct(AtrBussIbnrAllocAction bussIbnrAllocAction, AtrBussIbnrImportMainVo ibnrImportMainVo) {
/*        atrBussIbnrAllocActionDao.cleanDuctIbnrEpData();
        atrBussIbnrAllocActionDao.cleanDuctIbnrEpDataOut();
        atrBussIbnrAllocActionDao.cleanDuctIbnrEpDataOutX();
        atrBussIbnrAllocActionDao.cleanDuctIbnrImportData();*/
        switch (bussIbnrAllocAction.getBusinessSourceCode()) {
            case "DD" :
                this.ibnrSecAllocDuctDD(bussIbnrAllocAction, ibnrImportMainVo);
                this.allocDDBalance(bussIbnrAllocAction);
                break;
            case "TI" :
                this.ibnrSecAllocDuctTi(bussIbnrAllocAction, ibnrImportMainVo);
                break;
            case "OUT":
                AtrBussClaimImportMainVo claimImportMainVo1 = ClassUtil.convert(bussIbnrAllocAction, AtrBussClaimImportMainVo.class);
                AtrBussClaimImportMainVo atrBussClaimImportMainVo1 = atrBussIbnrClaimImportService.findByVo(claimImportMainVo1);

                this.ibnrSecAllocDuctOut(bussIbnrAllocAction, ibnrImportMainVo);
                this.ibnrSecAllocDuctOutX(bussIbnrAllocAction, atrBussClaimImportMainVo1, ibnrImportMainVo);

                this.allocOUTBalance(bussIbnrAllocAction);
            default:
                break;
        }
        if(!this.checkIbnrAllocResult(bussIbnrAllocAction)) {
            throw new BusinessException(AtrExceptionEnum.IBNR_NO_BALANCE);
        }
    }


    protected void allocDDBalance(AtrBussIbnrAllocAction bussIbnrAllocActionVo) {
        List<AtrBussDiffIbnrAllocVo> tailDiffList = atrBussIbnrAllocActionDao.findDDTailDiffList(bussIbnrAllocActionVo);
        List<Future<?>> futureList = new ArrayList<>();
        tailDiffList.forEach(tailDiffPo -> {
            Future<?> future = ThreadUtils.execAsync(() -> {
                if (ObjectUtils.isNotEmpty(tailDiffPo.getIbnrDetailId())) {
                    atrBussIbnrAllocActionDao.allocDDBalance(tailDiffPo);
                }
            }, ThreadConstant.DATA_SLICE_THREAD_POOL);
            futureList.add(future);

        });
        ThreadUtils.waitAllAndThrows(futureList);
    }

    protected void allocOUTBalance(AtrBussIbnrAllocAction bussIbnrAllocActionVo) {
        List<AtrBussDiffIbnrAllocVo> tailDiffList = atrBussIbnrAllocActionDao.findOutTailDiffList(bussIbnrAllocActionVo);
        List<Future<?>> futureList = new ArrayList<>();
        tailDiffList.forEach(tailDiffPo -> {
            Future<?> future = ThreadUtils.execAsync(() -> {
                if (ObjectUtils.isNotEmpty(tailDiffPo.getIbnrDetailId())) {
                    atrBussIbnrAllocActionDao.allocOutBalance(tailDiffPo);
                }
            }, ThreadConstant.DATA_SLICE_THREAD_POOL);
            futureList.add(future);

        });
        ThreadUtils.waitAllAndThrows(futureList);
    }

    /*
    * 1.
    * */
    private void ibnrSecAllocDuctDD(AtrBussIbnrAllocAction bussIbnrAllocActionVo, AtrBussIbnrImportMainVo ibnrImportMainVo) {
        switch (ibnrImportMainVo.getIbnrRange()) {
            case "1" :
                bussIbnrAllocDdResultDao.saveDuctAllocEp(bussIbnrAllocActionVo, ibnrImportMainVo);
                bussIbnrAllocDdResultDao.saveDuctAllocEpSum(bussIbnrAllocActionVo);

                bussIbnrAllocDdResultDao.saveDuctImportSum(bussIbnrAllocActionVo, ibnrImportMainVo);
                bussIbnrAllocDdResultDao.saveBussAllocData(bussIbnrAllocActionVo, ibnrImportMainVo);
                break;
            case "2" :
                bussIbnrAllocDdResultDao.saveBussAllocDataTrans(bussIbnrAllocActionVo, ibnrImportMainVo);
                break;
        }
    }

    private void ibnrSecAllocDuctTi(AtrBussIbnrAllocAction bussIbnrAllocActionVo, AtrBussIbnrImportMainVo ibnrImportMainVo) {
        bussIbnrAllocTiResultDao.saveIbnrAllocTiResult(bussIbnrAllocActionVo, ibnrImportMainVo);
    }

    private void ibnrSecAllocDuctFo(AtrBussIbnrAllocAction bussIbnrAllocActionVo, AtrBussIbnrImportMainVo ibnrImportMainVo) {
        switch (ibnrImportMainVo.getIbnrRange()) {
            case "1" :
                bussIbnrAllocFoResultDao.saveDuctAllocEp(bussIbnrAllocActionVo, ibnrImportMainVo);
                bussIbnrAllocFoResultDao.saveDuctAllocEpSum(bussIbnrAllocActionVo);
                bussIbnrAllocFoResultDao.saveDuctImportSum(bussIbnrAllocActionVo, ibnrImportMainVo);
                bussIbnrAllocFoResultDao.saveBussAllocData(bussIbnrAllocActionVo);
                break;
            case "2" :
                bussIbnrAllocFoResultDao.saveBussAllocDataTrans(bussIbnrAllocActionVo, ibnrImportMainVo);
                break;
        }
    }

    private void ibnrSecAllocDuctTo(AtrBussIbnrAllocAction bussIbnrAllocActionVo, AtrBussIbnrImportMainVo ibnrImportMainVo) {
        switch (ibnrImportMainVo.getIbnrRange()) {
            case "1" :
                bussIbnrAllocToResultDao.saveDuctAllocEp(bussIbnrAllocActionVo, ibnrImportMainVo);
                bussIbnrAllocToResultDao.saveDuctAllocEpSum(bussIbnrAllocActionVo);
                bussIbnrAllocToResultDao.saveDuctImportSum(bussIbnrAllocActionVo, ibnrImportMainVo);
                bussIbnrAllocToResultDao.saveBussAllocData(bussIbnrAllocActionVo);
                break;
            case "2" :
                bussIbnrAllocToResultDao.saveBussAllocDataTrans(bussIbnrAllocActionVo, ibnrImportMainVo);
                break;
        }
    }

    private void ibnrSecAllocDuctOut(AtrBussIbnrAllocAction bussIbnrAllocActionVo, AtrBussIbnrImportMainVo ibnrImportMainVo) {
        switch (ibnrImportMainVo.getIbnrRange()) {
            case "1" :
                bussIbnrAllocOutResultDao.saveDuctAllocFoEp(bussIbnrAllocActionVo, ibnrImportMainVo);
                bussIbnrAllocOutResultDao.saveDuctAllocToEp(bussIbnrAllocActionVo, ibnrImportMainVo);
                bussIbnrAllocOutResultDao.saveDuctAllocEpSum(bussIbnrAllocActionVo);
                bussIbnrAllocOutResultDao.saveDuctImportSum(bussIbnrAllocActionVo, ibnrImportMainVo);
                bussIbnrAllocOutResultDao.saveBussAllocData(bussIbnrAllocActionVo);
                break;
            case "2" :
                bussIbnrAllocOutResultDao.saveBussAllocDataTrans(bussIbnrAllocActionVo, ibnrImportMainVo);
                break;
        }
    }


    private void ibnrSecAllocDuctOutX(AtrBussIbnrAllocAction bussIbnrAllocActionVo, AtrBussClaimImportMainVo claimImportMainVo, AtrBussIbnrImportMainVo ibnrImportMainVo) {
        bussIbnrAllocToResultXDao.saveDuctAllocEp(bussIbnrAllocActionVo, claimImportMainVo);
        bussIbnrAllocToResultXDao.saveDuctAllocEpSum(bussIbnrAllocActionVo);
        bussIbnrAllocToResultXDao.saveIbnrAllocOutXData(bussIbnrAllocActionVo, ibnrImportMainVo);
    }


    public Page<AtrBussIbnrAllocResultVo> pullIbnrDataToLic(AtrBussIbnrAllocActionVo allocActionVo) {
        Page<AtrBussIbnrAllocResultVo> ibnrAllocActionVos = new Page<>();
        switch (allocActionVo.getBusinessSourceCode()) {
            case "DD" :
                bussIbnrAllocDdResultDao.deleteDapIbnr(allocActionVo);
                bussIbnrAllocDdResultDao.confirm(allocActionVo);
                break;
            case "TI" :
                bussIbnrAllocTiResultDao.deleteDapIbnr(allocActionVo);
                bussIbnrAllocTiResultDao.confirm(allocActionVo);
                break;
            case "FO":
                bussIbnrAllocFoResultDao.deleteDapIbnr(allocActionVo);
                bussIbnrAllocFoResultDao.confirm(allocActionVo);
                break;
            case "TO":
                bussIbnrAllocToResultDao.deleteDapIbnr(allocActionVo);
                bussIbnrAllocToResultXDao.deleteDapIbnr(allocActionVo);

                bussIbnrAllocToResultDao.confirm(allocActionVo);
                bussIbnrAllocToResultXDao.confirm(allocActionVo);
            case "OUT":
                bussIbnrAllocOutResultDao.deleteDapFoIbnr(allocActionVo);
                bussIbnrAllocOutResultDao.deleteDapToIbnr(allocActionVo);
                bussIbnrAllocToResultXDao.deleteDapIbnr(allocActionVo);

                bussIbnrAllocOutResultDao.confirmFoData(allocActionVo);
                bussIbnrAllocOutResultDao.confirmToData(allocActionVo);
                bussIbnrAllocToResultXDao.confirm(allocActionVo);
            default:
                break;
        }
        return ibnrAllocActionVos;
    }


    private void exportDDAllocData(HttpServletRequest request, HttpServletResponse response, AtrBussIbnrAllocActionVo allocActionVo, Long userId) {
        Date outPutTime = new Date();
        SimpleDateFormat formatterTime = new SimpleDateFormat("HHmmss");
        String formatterTimeCode = formatterTime.format(outPutTime);
        String fileName = allocActionVo.getTemplateFileName() + formatterTimeCode + ".xlsx";
        String logPath = atrExportService.getOutPutPathSave() + fileName;
        Long logId = atrExportService.saveExportTrackLog(fileName, allocActionVo.getTargetRouter(), logPath, userId, false);
        boolean taskStatus = true;
        String errorMsg = null;
        try {
            exportHandlerDDAllocData(request, response, allocActionVo.getTemplateFileName(), allocActionVo,
                    "zh", formatterTimeCode);
        } catch (Exception e) {
            taskStatus = false;
            errorMsg = ExceptionUtils.getStackTrace(e);
            if (errorMsg.length() >= 3900) {
                errorMsg = errorMsg.substring(1, 3900);
            }
            LOG.error("现金流导出失败！", e);
        } finally {
            atrExportService.updateExportTrackLog(logId,
                    taskStatus ? CommonConstant.ExportLogTaskStatus.SUCCESSFUL : CommonConstant.ExportLogTaskStatus.FAIL
                    , userId, errorMsg);
        }
    }

    private String exportHandlerDDAllocData(HttpServletRequest request, HttpServletResponse response, String fileName,
                                           AtrBussIbnrAllocActionVo accBussVoucherVo, String language,
                                           String formatterTimeCode) throws IOException {
        String fileSavePath = atrExportService.getOutPutPath();
        String tempFilePath = atrExportService.getTemplatePath() + fileName + ".xlsx";
        tempFilePath = ExcelExportUtil.dealFilePath(tempFilePath, language);

        Long maxRow = bussIbnrAllocDdResultDao.countAllocList(accBussVoucherVo);
        CommonResultHandler<AtrBussIbnrAllocResultVo> resultHandler =
                new CommonResultHandler<AtrBussIbnrAllocResultVo>(fileSavePath
                        , fileName + formatterTimeCode, tempFilePath, "df", false, maxRow) {
                    @Override
                    public Object processing(AtrBussIbnrAllocResultVo obj) {
                        return obj;
                    }
                };
        bussIbnrAllocDdResultDao.findAllocList(resultHandler, accBussVoucherVo);
        resultHandler.getWriter().finish();
        ExcelDownloadUtil.downloadExcel(request, response, fileSavePath, fileName, formatterTimeCode);
        return fileSavePath + fileName + ".xlsx";
    }

    private void exportTIAllocData(HttpServletRequest request, HttpServletResponse response, AtrBussIbnrAllocActionVo allocActionVo, Long userId) throws Exception {
        String fileName = allocActionVo.getTemplateFileName();
        if (ObjectUtils.isEmpty(fileName)) {
            throw new Exception("Excel Name is null");
        }
        // 准备导出的sheet数据列表
        List<AtrBussIbnrAllocResultVo> tiIbnrAllocActionVos = bussIbnrAllocTiResultDao.findAllocList(allocActionVo);
        atrExportService.exportList(request, response, tiIbnrAllocActionVos, AtrBussIbnrAllocResultVo.class,
                "df", fileName, allocActionVo.getTargetRouter(), userId);
    }


    private void exportFOAllocData(HttpServletRequest request, HttpServletResponse response, AtrBussIbnrAllocActionVo allocActionVo, Long userId) throws Exception {
        String fileName = allocActionVo.getTemplateFileName();
        if (ObjectUtils.isEmpty(fileName)) {
            throw new Exception("Excel Name is null");
        }
        // 准备导出的sheet数据列表
        List<AtrBussIbnrAllocResultVo> foIbnrAllocActionVos = bussIbnrAllocFoResultDao.findAllocList(allocActionVo);
        atrExportService.exportList(request, response, foIbnrAllocActionVos, AtrBussIbnrAllocResultVo.class,
                "df", fileName, allocActionVo.getTargetRouter(), userId);
    }


    private void exportTOAllocData(HttpServletRequest request, HttpServletResponse response, AtrBussIbnrAllocActionVo allocActionVo, Long userId) throws Exception {
        String fileName = allocActionVo.getTemplateFileName();
        if (ObjectUtils.isEmpty(fileName)) {
            throw new Exception("Excel Name is null");
        }
        // 准备导出的sheet数据列表
        List<ExcelSheet> sheetList = new ArrayList<>();
        List<AtrBussIbnrAllocResultVo> toIbnrAllocActionVos = bussIbnrAllocOutResultDao.findAllocList(allocActionVo);
        ExcelSheet toExcelSheet = new ExcelSheet(0,  Arrays.asList(new ExcelSheetData("df", toIbnrAllocActionVos)), false);
        sheetList.add(toExcelSheet);

        List<AtrBussIbnrAllocResultVo> toXIbnrAllocActionVos = bussIbnrAllocToResultXDao.findAllocList(allocActionVo);
        ExcelSheet toXExcelSheet = new ExcelSheet(1,  Arrays.asList(new ExcelSheetData("df", toXIbnrAllocActionVos)), false);
        sheetList.add(toXExcelSheet);
        atrExportService.exportExcelSheetList(request, response, sheetList,
                fileName, "", allocActionVo.getTargetRouter(), userId);
    }

}
