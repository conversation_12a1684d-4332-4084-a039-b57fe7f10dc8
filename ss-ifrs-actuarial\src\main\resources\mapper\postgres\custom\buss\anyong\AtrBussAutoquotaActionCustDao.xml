<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-08-28 15:11:17 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussAutoquotaActionDao">
  <!-- 本配置文件由SS MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**IDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->

  <resultMap id="CustomResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaActionVo">
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="action_no" property="actionNo" jdbcType="VARCHAR" />
    <result column="entity_id" property="entityId" jdbcType="BIGINT" />
    <result column="business_source_code" property="businessSourceCode" jdbcType="VARCHAR" />
    <result column="loa_code" property="loaCode" jdbcType="VARCHAR" />
    <result column="deadline" property="deadline" jdbcType="DATE" />
    <result column="draw_interval" property="drawInterval" jdbcType="VARCHAR" />
    <result column="draw_interval_quantity" property="drawIntervalQuantity" jdbcType="INTEGER" />
    <result column="appli_ev_yearmonths" property="appliEvYearmonths" jdbcType="VARCHAR" />
    <result column="appli_quota_codes" property="appliQuotaCodes" jdbcType="VARCHAR" />
    <result column="oper_id" property="operId" jdbcType="BIGINT" />
    <result column="oper_time" property="operTime" jdbcType="TIMESTAMP" />
    <result column="draw_state" property="drawState" jdbcType="VARCHAR" />
    <result column="draw_start_time" property="drawStartTime" jdbcType="TIMESTAMP" />
    <result column="draw_end_time" property="drawEndTime" jdbcType="TIMESTAMP" />
    <result column="approval_state" property="approvalState" jdbcType="VARCHAR" />
    <result column="approval_id" property="approvalId" jdbcType="BIGINT" />
    <result column="approval_time" property="approvalTime" jdbcType="TIMESTAMP" />
    <result column="loa_name" property="loaName" jdbcType="VARCHAR" />
    <result column="entity_code" property="entityCode" jdbcType="VARCHAR" />
    <result column="entity_name" property="entityName" jdbcType="VARCHAR" />
  </resultMap>

  <resultMap id="UnitResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaUnitResultVo">
    <result column="entity_id" property="entityId" jdbcType="VARCHAR" />
    <result column="entity_code" property="entityCode" jdbcType="VARCHAR" />
    <result column="entity_name" property="entityName" jdbcType="VARCHAR" />
    <result column="business_source_code" property="businessSourceCode" jdbcType="VARCHAR" />
    <result column="loa_code" property="loaCode" jdbcType="VARCHAR" />
    <result column="buss_year" property="bussYear" jdbcType="VARCHAR" />
    <result column="gross_premium" property="grossPremium" jdbcType="NUMERIC" />
    <result column="net_premium" property="netPremium" jdbcType="NUMERIC" />
    <result column="settled_claim" property="settledClaim" jdbcType="NUMERIC" />
    <result column="settled_expense" property="settledExpense" jdbcType="NUMERIC" />
    <result column="os_claim" property="osClaim" jdbcType="NUMERIC" />
    <result column="os_expense" property="osExpense" jdbcType="NUMERIC" />
    <result column="non_acq_expense" property="nonAcqExpense" jdbcType="NUMERIC" />
    <result column="maintenance_expense" property="maintenanceExpense" jdbcType="NUMERIC" />
  </resultMap>

  <resultMap id="AccountResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaAccountResultVo">
    <result column="entity_id" property="entityId" jdbcType="VARCHAR" />
    <result column="entity_code" property="entityCode" jdbcType="VARCHAR" />
    <result column="entity_name" property="entityName" jdbcType="VARCHAR" />
    <result column="business_source_code" property="businessSourceCode" jdbcType="VARCHAR" />
    <result column="loa_code" property="loaCode" jdbcType="VARCHAR" />
    <result column="year_month" property="yearMonth" jdbcType="VARCHAR" />
    <result column="account_code" property="accountCode" jdbcType="VARCHAR" />
    <result column="amount" property="amount" jdbcType="NUMERIC" />
  </resultMap>

  <select id="doAction" parameterType="java.lang.String" statementType="CALLABLE">
    call atr_pack_autoquota_proc_do_action( #{actionNo,mode=IN,jdbcType=VARCHAR})
  </select>

  <select id="approveAction" parameterType="java.lang.String" statementType="CALLABLE">
    call atr_pack_autoquota_proc_approve_action( #{actionNo,mode=IN,jdbcType=VARCHAR})
  </select>

  <select id="deleteAction" parameterType="java.lang.Long" statementType="CALLABLE">
    call atr_pack_autoquota_proc_delete_action( #{actionNo,mode=IN,jdbcType=NUMERIC})
  </select>

  <select id="fuzzySearchPage" flushCache="false" useCache="true" resultMap="CustomResultMap"
          parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaActionVo">
    select
    <choose>
      <when test="language != null and language != '' ">
        <choose>
          <when test='language == "zh"'>
            c1.loa_c_name as loa_name,
            c2.entity_c_name as entity_name,
          </when>
          <when test='language == "tn"'>
            c1.loa_l_name as loa_name,
            c2.entity_l_name as entity_name,
          </when>
          <otherwise>
            c1.loa_e_name as loa_name,
            c2.entity_e_name as entity_name,
          </otherwise>
        </choose>
      </when>
    </choose>
    c2.entity_code,
    t.*
    from atr_buss_autoquota_action t
    left join bpluser.bbs_conf_loa c1
    on t.loa_code = c1.loa_code
    left join bpluser.bbs_conf_entity c2
    on t.entity_id = c2.entity_id
    <include refid="Custom_Select_By_Vo_Where" />
    order by t.id desc
  </select>

  <select id="queryUnitPage" flushCache="false" useCache="true" resultMap="UnitResultMap"
          parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaGroupQueryVo">
    select ${groupColumns},
    max(e.entity_code) as entity_code,
    <choose>
      <when test="language='zh'">
        max(e.entity_c_name)
      </when>
      <when test="language='tn'">
        max(e.entity_l_name)
      </when>
      <otherwise>
        max(e.entity_e_name)
      </otherwise>
    </choose> as entity_name,
    coalesce(sum(t.gross_premium), 0) as gross_premium,
    coalesce(sum(t.net_premium), 0) as net_premium,
    coalesce(sum(t.settled_claim), 0) as settled_claim,
    coalesce(sum(t.settled_expense), 0) as settled_expense,
    coalesce(sum(t.os_claim), 0) as os_claim,
    coalesce(sum(t.os_expense), 0) as os_expense,
    coalesce(sum(t.non_acq_expense), 0) as non_acq_expense,
    coalesce(sum(t.maintenance_expense), 0) as maintenance_expense
    from atr_buss_autoquota_buss_value t, atr_buss_autoquota_action a
    left join bpluser.bbs_conf_entity e
    on a.entity_id = e.entity_id
    where t.action_no = #{actionNo,jdbcType=VARCHAR}
    and t.action_no = a.action_no
    group by ${groupColumns}
    order by ${groupColumns}
  </select>

  <select id="queryAccountPage" flushCache="false" useCache="true" resultMap="AccountResultMap"
          parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaGroupQueryVo">
    select ${groupColumns},
    <if test="groupColumns.contains('entity_id')">
      max(e.entity_code) as entity_code,
      <choose>
        <when test="language='zh'">
          max(e.entity_c_name)
        </when>
        <when test="language='tn'">
          max(e.entity_l_name)
        </when>
        <otherwise>
          max(e.entity_e_name)
        </otherwise>
      </choose> as entity_name,
    </if>
    coalesce(sum(t.amount), 0) as amount
    from atr_dap_autoquota_account t,
    atr_buss_autoquota_action a
    left join bpluser.bbs_conf_entity e
    on a.entity_id = e.entity_id
    where t.action_no = #{actionNo,jdbcType=VARCHAR}
    and t.action_no = a.action_no
    group by ${groupColumns}
    order by ${groupColumns}
  </select>

  <select id="queryQuotaValuePage" flushCache="false" useCache="true"
          resultType="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaValueResultVo"
          parameterType="java.lang.String">
    select
    e.entity_id as entityId,
    e.entity_code as entityCode,
    <choose>
      <when test="language='zh'">
        e.entity_c_name
      </when>
      <when test="language='tn'">
        e.entity_l_name
      </when>
      <otherwise>
        e.entity_e_name
      </otherwise>
    </choose> as entityName,
    t.business_source_code as businessSourceCode,
    t.loa_code             as loaCode,
    t.quota_code           as quotaCode,
    <choose>
      <when test="language == 'zh'">
        d.quota_c_name
      </when>
      <when test="language == 'tn'">
        d.quota_l_name
      </when>
      <otherwise>
        d.quota_e_name
      </otherwise>
    </choose>        as quotaName,
    t.quota_value          as quotaValue
    from atr_buss_autoquota_value t
    inner join atr_buss_autoquota_action a
    on a.action_no = t.action_no
    left join atr_conf_quota_def d
    on t.quota_code = d.quota_code
    left join bpluser.bbs_conf_entity e
    on a.entity_id = e.entity_id
    where t.action_no = #{actionNo,jdbcType=VARCHAR}
    order by a.entity_id, t.business_source_code, t.loa_code, t.quota_code
  </select>

  <select id="queryLoaPage" flushCache="false" useCache="true"
          resultType="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaLoaVo"
          parameterType="com.ss.platform.pojo.bbs.vo.BbsConfLoaVo">
    select t.loa_code as loaCode,
    <choose>
      <when test='language == "zh"'>
        t.loa_c_name
      </when>
      <when test='language == "tn"'>
        t.loa_l_name
      </when>
      <otherwise>
        t.loa_e_name
      </otherwise>
    </choose>
    as loaName,
    t.business_model || t.business_model as businessSourceCode
    from bpluser.bbs_conf_loa t
    where t.valid_is = '1'
    and t.business_model != 'F'
    and t.audit_state = '1'
    <if test='businessModel != null and businessModel != ""'>
      and t.business_model = #{businessModel,jdbcType=VARCHAR}
    </if>
    <if test='businessDirection != null and businessDirection != ""'>
      and t.business_direction = #{businessDirection,jdbcType=VARCHAR}
    </if>
    <if test='loaCode != null and loaCode != ""'>
      and t.loa_code = #{loaCode,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="queryConfWeightByBuss" resultType="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaWeightDetailVo"
          parameterType="com.ss.platform.pojo.bbs.vo.BbsConfLoaVo">

    select x.business_model || x.business_direction as                           businessSourceCode,
    x.loa_code                               as                           loaCode,
    string_agg(x.weight_value::varchar, ',' order by x.offset_years desc) weightValuesAgg
    from (select distinct lo.business_model,
    lo.business_direction,
    lo.loa_code,
    t.offset_years,
    t.weight_value
    from bpluser.bbs_conf_loa lo left join atr_conf_autoquota_weight t
    on lo.loa_code = t.loa_code
    where lo.valid_is = '1'
    and lo.audit_state = '1'
    and lo.business_model != 'F'
    <if test='businessModel != null and businessModel != ""'>
      and lo.business_model = #{businessModel,jdbcType=VARCHAR}
    </if>
    <if test='businessDirection != null and businessDirection != ""'>
      and lo.business_direction = #{businessDirection,jdbcType=VARCHAR}
    </if>
    <if test='loaCode != null and loaCode != ""'>
      and lo.loa_code = #{loaCode,jdbcType=VARCHAR}
    </if>
    ) x
    group by x.business_model, x.business_direction, x.loa_code
    order by x.business_model, x.business_direction, x.loa_code

  </select>

  <select id="queryConfWeight" resultType="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaWeightDetailVo">
    select coalesce(t.loa_code, lo.loa_code)                                                   as loaCode,
           string_agg(coalesce(t.weight_value::varchar, ''), ',' order by t.offset_years desc) as weightValuesAgg
    from atr_conf_autoquota_weight t
           full join (select distinct loa_code
                      from bpluser.bbs_conf_loa
                      where valid_is = '1'
                        and audit_state = '1'
                        and business_model != 'F') lo
                     on t.loa_code = lo.loa_code
    group by coalesce(t.loa_code, lo.loa_code)
  </select>

  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Custom_Select_By_Vo_Where">
    <where>
      <if test='id != null'>
        and t.id = #{id,jdbcType=DECIMAL}
      </if>
      <if test='actionNo != null and actionNo != ""'>
        and t.action_no = #{actionNo,jdbcType=VARCHAR}
      </if>
      <if test='entityId != null'>
        and t.entity_id = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test='businessSourceCode != null and businessSourceCode != ""'>
        and t.business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test='loaCode != null and loaCode != ""'>
        and t.loa_code = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test='deadline != null'>
        and t.deadline = #{deadline,jdbcType=DATE}
      </if>
      <if test='drawInterval != null and drawInterval != ""'>
        and t.draw_interval = #{drawInterval,jdbcType=VARCHAR}
      </if>
      <if test='drawIntervalQuantity != null'>
        and t.draw_interval_quantity = #{drawIntervalQuantity,jdbcType=DECIMAL}
      </if>
      <if test='appliEvYearmonths != null and appliEvYearmonths != ""'>
        and t.appli_ev_yearmonths = #{appliEvYearmonths,jdbcType=VARCHAR}
      </if>
      <if test='appliQuotaCodes != null and appliQuotaCodes != ""'>
        and t.appli_quota_codes = #{appliQuotaCodes,jdbcType=VARCHAR}
      </if>
      <if test='operId != null'>
        and t.oper_id = #{operId,jdbcType=DECIMAL}
      </if>
      <if test='operTime != null'>
        and t.oper_time = #{operTime,jdbcType=TIMESTAMP}
      </if>
      <if test='drawState != null and drawState != ""'>
        and t.draw_state = #{drawState,jdbcType=VARCHAR}
      </if>
      <if test='drawStartTime != null'>
        and t.draw_start_time = #{drawStartTime,jdbcType=TIMESTAMP}
      </if>
      <if test='drawEndTime != null'>
        and t.draw_end_time = #{drawEndTime,jdbcType=TIMESTAMP}
      </if>
      <if test='approvalState != null and approvalState != ""'>
        and t.approval_state = #{approvalState,jdbcType=VARCHAR}
      </if>
      <if test='approvalId != null'>
        and t.approval_id = #{approvalId,jdbcType=DECIMAL}
      </if>
      <if test='approvalTime != null'>
        and t.approval_time = #{approvalTime,jdbcType=TIMESTAMP}
      </if>
      <if test='deadlineBegin != null'>
        and t.deadline &gt;= #{deadlineBegin,jdbcType=TIMESTAMP}
      </if>
      <if test='deadlineEnd != null'>
        and t.deadline &lt;= #{deadlineEnd,jdbcType=TIMESTAMP}
      </if>
      <if test='operTimeBegin != null'>
        and t.oper_time &gt;= #{operTimeBegin,jdbcType=TIMESTAMP}
      </if>
      <if test='operTimeEnd != null'>
        and t.oper_time &lt;= #{operTimeEnd,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>
</mapper>