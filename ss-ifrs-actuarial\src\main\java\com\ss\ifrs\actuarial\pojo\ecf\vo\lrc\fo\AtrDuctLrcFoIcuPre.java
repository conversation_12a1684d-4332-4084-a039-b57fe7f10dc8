package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.fo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 上期ICU数据存储类 - 临分分出业务
 */
@Data
public class AtrDuctLrcFoIcuPre {

    /** 合约号 */
    private String treatyNo;

    /** 保单号 */
    private String policyNo;

    /** 批单序号 */
    private String endorseSeqNo;

    /** 险别代码 */
    private String kindCode;

    /** 上期累计已赚保费 */
    private BigDecimal preCumlEdPremium;

    /** 上期累计已赚净额结算手续费 */
    private BigDecimal preCumlEdNetFee;

    /** 当期已赚保费 */
    private BigDecimal curEdPremium;

    /** 当期已赚净额结算手续费 */
    private BigDecimal curEdNetFee;
}
