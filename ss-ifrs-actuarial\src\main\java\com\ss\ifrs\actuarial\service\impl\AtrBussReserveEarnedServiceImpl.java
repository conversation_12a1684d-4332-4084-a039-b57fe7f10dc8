package com.ss.ifrs.actuarial.service.impl;


import com.ss.ifrs.actuarial.dao.buss.reserve.AtrBussReserveEarnedDao;
import com.ss.ifrs.actuarial.dao.buss.reserve.AtrBussReserveEarnedDetailDao;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveEarned;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveEarnedDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveEarnedVo;
import com.ss.ifrs.actuarial.service.AtrBussReserveEarnedService;
import com.ss.ifrs.actuarial.util.AtrExcelHead;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ClassUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class AtrBussReserveEarnedServiceImpl implements AtrBussReserveEarnedService {

    @Autowired
    AtrBussReserveEarnedDao atrBussReserveEarnedDao;
    @Autowired
    AtrBussReserveEarnedDetailDao atrBussReserveEarnedDetailDao;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor= UnexpectedRollbackException.class)
    public void reserveEarnedExtract(AtrBussReserveEarnedVo atrBussReserveEarnedVo, Long userId) {
        AtrBussReserveEarned atrBussReserveEarned = ClassUtil.convert(atrBussReserveEarnedVo, AtrBussReserveEarned.class);
        SimpleDateFormat dateFormatVersion = new SimpleDateFormat("yyyyMMddHHmmss");
        atrBussReserveEarned.setCreatorId(userId);
        atrBussReserveEarned.setCreateTime(new Date());
        atrBussReserveEarned.setDataSource("0");
        atrBussReserveEarned.setConfirmIs("0");
        atrBussReserveEarned.setVersionNo(dateFormatVersion.format(new Date()));
        atrBussReserveEarned.setStartDate(atrBussReserveEarnedVo.getExtractStartDate());
        atrBussReserveEarned.setEndDate(atrBussReserveEarnedVo.getExtractEndDate());
        atrBussReserveEarnedDao.save(atrBussReserveEarned);

        Map<String, Object> param = new HashMap<>(2);
        param.put("reserveEarnedId", atrBussReserveEarned.getReserveEarnedId());
        atrBussReserveEarnedDao.reserveEarnedExtract(param);
    }

    @Override
    public Page<AtrBussReserveEarnedVo> searchPage(AtrBussReserveEarnedVo atrBussReserveEarnedVo, Pageable pageParam) {
        Page<AtrBussReserveEarnedVo> atrBussReserveEarnedVoPage = atrBussReserveEarnedDao.fuzzySearchPage(atrBussReserveEarnedVo, pageParam);
        return  atrBussReserveEarnedVoPage;
    }

    @Override
    public Page<AtrBussReserveEarnedDetailVo> findForDataTables(AtrBussReserveEarnedVo atrBussReserveEarnedVo, Pageable pageParam) {
        if (ObjectUtils.isNotEmpty(atrBussReserveEarnedVo.getColumnList())) {
            String columnSql = String.join(", d.", atrBussReserveEarnedVo.getColumnList());
            atrBussReserveEarnedVo.setColumnSql("d." + columnSql + ",");
        }

        Page<AtrBussReserveEarnedDetailVo> atrBussReserveEarnedVoPage = atrBussReserveEarnedDetailDao.fuzzySearchPage(atrBussReserveEarnedVo, pageParam);
        return atrBussReserveEarnedVoPage;
    }

    @Override
    public void confirm(AtrBussReserveEarnedVo atrBussReserveEarnedVo, Long userId) {
        if(ObjectUtils.isEmpty(atrBussReserveEarnedVo.getReserveEarnedId()) && checkIsConfirm(atrBussReserveEarnedVo)) {
            return;
        }
        AtrBussReserveEarned po =  atrBussReserveEarnedDao.findById(atrBussReserveEarnedVo.getReserveEarnedId());
        po.setConfirmIs("1");
        po.setConfirmId(userId);
        po.setConfirmTime(new Date());
        po.setUpdatorId(userId);
        po.setUpdateTime(new Date());
        atrBussReserveEarnedDao.updateById(po);
    }

//    @Override
//    public void downloadDataFile(HttpServletResponse response, AtrBussReserveEarnedVo atrBussReserveEarnedVo) throws IOException {
//
//        List<AtrBussReserveExcelVo> atrBussReserveEarnedVoPage = atrBussReserveEarnedDetailDao.searchListById(atrBussReserveEarnedVo);
//        String excelName = atrBussReserveEarnedVo.getAtrExcel().getFileName();
//        for (AtrExcelSheet sheet : atrBussReserveEarnedVo.getAtrExcel().getAtrSheetList()) {
//            EasyExcelUtil.setResponse(response, excelName+  ".xlsx");
//            EasyExcelUtil.export(response, head(sheet.getHeadList()), atrBussReserveEarnedVoPage);
//        }
//    }

    @Override
    public List<AtrBussReserveEarnedDetailVo> findDownloadList(AtrBussReserveEarnedVo atrBussReserveEarnedVo) {
        if (ObjectUtils.isNotEmpty(atrBussReserveEarnedVo.getColumnList())) {
            String columnSql = String.join(",", atrBussReserveEarnedVo.getColumnList());
            atrBussReserveEarnedVo.setColumnSql(columnSql + ",");
        }
        return atrBussReserveEarnedDetailDao.fuzzySearchPage(atrBussReserveEarnedVo);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor= UnexpectedRollbackException.class)
    public void delete(Long reserveEarnedId, Long userId) {
        AtrBussReserveEarned po =  atrBussReserveEarnedDao.findById(reserveEarnedId);
        if(ObjectUtils.isNotEmpty(po) && CommonConstant.VersionStatus.PENDING.equals(po.getConfirmIs())) {
            atrBussReserveEarnedDetailDao.deleteByMainId(reserveEarnedId);
            atrBussReserveEarnedDao.deleteById(reserveEarnedId);
        }
    }

    @Override
    public Boolean checkIsConfirm(AtrBussReserveEarnedVo atrBussReserveEarnedVo) {
        Map<String, Object> param = new HashMap<>();
        param.put("entityId", atrBussReserveEarnedVo.getEntityId());
        param.put("startDate", atrBussReserveEarnedVo.getStartDate());
        param.put("endDate", atrBussReserveEarnedVo.getEndDate());
        Long count = atrBussReserveEarnedDao.reserveEarnedConfirm(param);
        if(ObjectUtils.isNotEmpty(count) && count >0) {
            return true;
        }
        return false;
    }



    public static List<List<String>> head(List<AtrExcelHead> headList){
        List<List<String>> list = new ArrayList<List<String>>();
        headList.forEach(e -> {
            if(e.getChildHeadList()!= null && e.getChildHeadList().size() > 0) {
                e.getChildHeadList().forEach(c -> {
                    List<String> head = new ArrayList<>();
                    head.add(e.getLabel());
                    head.add(c.getLabel());
                    list.add(head);
                });
            } else {
                List<String> head = new ArrayList<>();
                head.add(e.getLabel());
                list.add(head);
            }
        });
        return list;
    }
}
