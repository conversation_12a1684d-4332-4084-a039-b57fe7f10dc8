/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2022-08-19 14:51:44
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2022-08-19 14:51:44<br/>
 * Description: Excel表字段数据定义表<br/>
 * Table Name: atr_conf_excel_table_column<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "Excel表字段数据定义表")
public class AtrConfExcelTableColumnVo implements Serializable {
    /**
     * Database column: atr_conf_excel_table_column.col_id
     * Database remarks: Col_Id|主键
     */
    @ApiModelProperty(value = "Col_Id|主键", required = true)
    private Long colId;

    /**
     * Database column: atr_conf_excel_table_column.col_code
     * Database remarks: Col_Code|字段编码
     */
    @ApiModelProperty(value = "Col_Code|字段编码", required = false)
    private String colCode;

    /**
     * Database column: atr_conf_excel_table_column.biz_type_id
     * Database remarks: Col_Type_Code|规则类型编码
     */
    @ApiModelProperty(value = "Col_Type_Code|规则类型编码", required = false)
    private Long bizTypeId;

    /**
     * Database column: atr_conf_excel_table_column.col_e_name
     * Database remarks: Col_E_Name|字段英文名
     */
    @ApiModelProperty(value = "Col_E_Name|字段英文名", required = false)
    private String colEName;

    /**
     * Database column: atr_conf_excel_table_column.col_t_name
     * Database remarks: Col_T_Name|字段繁体名
     */
    @ApiModelProperty(value = "Col_T_Name|字段繁体名", required = false)
    private String colTName;

    /**
     * Database column: atr_conf_excel_table_column.col_c_name
     * Database remarks: Col_C_Name|字段中文名
     */
    @ApiModelProperty(value = "Col_C_Name|字段中文名", required = false)
    private String colCName;

    /**
     * Database column: atr_conf_excel_table_column.col_type
     * Database remarks: Col_Type|字段类型
     */
    @ApiModelProperty(value = "Col_Type|字段类型", required = false)
    private String colType;

    /**
     * Database column: atr_conf_excel_table_column.col_length
     * Database remarks: Col_Length|字段长度
     */
    @ApiModelProperty(value = "Col_Length|字段长度", required = false)
    private String colLength;

    /**
     * Database column: atr_conf_excel_table_column.col_desc
     * Database remarks: Col_Desc|字段描述
     */
    @ApiModelProperty(value = "Col_Desc|字段描述", required = false)
    private String colDesc;

    /**
     * Database column: atr_conf_excel_table_column.original_is
     * Database remarks: Original_Is|是否原字段，0-是，1-否
     */
    @ApiModelProperty(value = "Original_Is|是否原字段，0-是，1-否", required = false)
    private String originalIs;

    /**
     * Database column: atr_conf_excel_table_column.valid_is
     * Database remarks: Valind_is|有效标志
     */
    @ApiModelProperty(value = "Valind_is|有效标志", required = false)
    private String validIs;

    /**
     * Database column: atr_conf_excel_table_column.create_time
     * Database remarks: Create_Time|创建时间
     */
    @ApiModelProperty(value = "Create_Time|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: atr_conf_excel_table_column.creator_id
     * Database remarks: Creator_code|创建人
     */
    @ApiModelProperty(value = "Creator_code|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: atr_conf_excel_table_column.update_time
     * Database remarks: Update_time|最后变更时间
     */
    @ApiModelProperty(value = "Update_time|最后变更时间", required = false)
    private Date updateTime;

    /**
     * Database column: atr_conf_excel_table_column.updator_id
     * Database remarks: Updator_Code|最后变更经手人
     */
    @ApiModelProperty(value = "Updator_Code|最后变更经手人", required = false)
    private Long updatorId;

    /**
     * Database column: atr_conf_excel_table_column.display_no
     * Database remarks: Display_No|排序
     */
    @ApiModelProperty(value = "Display_No|排序", required = false)
    private BigDecimal displayNo;

    /**
     * Database column: atr_conf_excel_table_column.need_is
     * Database remarks: Need_Is|是否必录，0-否，1-是
     */
    @ApiModelProperty(value = "Need_Is|是否必录，0-否，1-是", required = false)
    private String needIs;

    /**
     * Database column: atr_conf_excel_table_column.enumeration_is
     * Database remarks: Enumeration_Is|是否枚举，0-否，1-是
     */
    @ApiModelProperty(value = "Enumeration_Is|是否枚举，0-否，1-是", required = false)
    private String enumerationIs;

    /**
     * Database column: atr_conf_excel_table_column.template_display_is
     * Database remarks: Template_Display_Is|是否模板显示，0-否，1-是
     */
    @ApiModelProperty(value = "Template_Display_Is|是否模板显示，0-否，1-是", required = false)
    private String templateDisplayIs;

    /**
     * Database column: atr_conf_excel_table_column.code_type
     * Database remarks: Code_Type|枚举值类型
     */
    @ApiModelProperty(value = "Code_Type|枚举值类型", required = false)
    private String codeType;

    /**
     * Database column: atr_conf_excel_table_column.need_is_rule_id
     * Database remarks: 是否必录规则id
     */
    @ApiModelProperty(value = "是否必录规则id", required = false)
    private Long needIsRuleId;

    /**
     * Database column: atr_conf_excel_table_column.enumeration_is_rule_id
     * Database remarks: 是否枚举值规则id
     */
    @ApiModelProperty(value = "是否枚举值规则id", required = false)
    private Long enumerationIsRuleId;

    /**
     * Database column: atr_conf_excel_table_column.constraint_type
     * Database remarks: constraint_type|null或0-普通字段,1-作为主键
     */
    @ApiModelProperty(value = "constraint_type|null或0-普通字段,1-作为主键", required = false)
    private String constraintType;

    private static final long serialVersionUID = 1L;

    private String ruleCodeType;
    private String codeCName;
    private String codeLName;
    private String codeEName;

    public Long getColId() {
        return colId;
    }

    public void setColId(Long colId) {
        this.colId = colId;
    }

    public String getColCode() {
        return colCode;
    }

    public void setColCode(String colCode) {
        this.colCode = colCode;
    }

    public Long getBizTypeId() {
        return bizTypeId;
    }

    public void setBizTypeId(Long bizTypeId) {
        this.bizTypeId = bizTypeId;
    }

    public String getColEName() {
        return colEName;
    }

    public void setColEName(String colEName) {
        this.colEName = colEName;
    }

    public String getColTName() {
        return colTName;
    }

    public void setColTName(String colTName) {
        this.colTName = colTName;
    }

    public String getColCName() {
        return colCName;
    }

    public void setColCName(String colCName) {
        this.colCName = colCName;
    }

    public String getColType() {
        return colType;
    }

    public void setColType(String colType) {
        this.colType = colType;
    }

    public String getColLength() {
        return colLength;
    }

    public void setColLength(String colLength) {
        this.colLength = colLength;
    }

    public String getColDesc() {
        return colDesc;
    }

    public void setColDesc(String colDesc) {
        this.colDesc = colDesc;
    }

    public String getOriginalIs() {
        return originalIs;
    }

    public void setOriginalIs(String originalIs) {
        this.originalIs = originalIs;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public BigDecimal getDisplayNo() {
        return displayNo;
    }

    public void setDisplayNo(BigDecimal displayNo) {
        this.displayNo = displayNo;
    }

    public String getNeedIs() {
        return needIs;
    }

    public void setNeedIs(String needIs) {
        this.needIs = needIs;
    }

    public String getEnumerationIs() {
        return enumerationIs;
    }

    public void setEnumerationIs(String enumerationIs) {
        this.enumerationIs = enumerationIs;
    }

    public String getTemplateDisplayIs() {
        return templateDisplayIs;
    }

    public void setTemplateDisplayIs(String templateDisplayIs) {
        this.templateDisplayIs = templateDisplayIs;
    }

    public String getCodeType() {
        return codeType;
    }

    public void setCodeType(String codeType) {
        this.codeType = codeType;
    }

    public Long getNeedIsRuleId() {
        return needIsRuleId;
    }

    public void setNeedIsRuleId(Long needIsRuleId) {
        this.needIsRuleId = needIsRuleId;
    }

    public Long getEnumerationIsRuleId() {
        return enumerationIsRuleId;
    }

    public void setEnumerationIsRuleId(Long enumerationIsRuleId) {
        this.enumerationIsRuleId = enumerationIsRuleId;
    }

    public String getConstraintType() {
        return constraintType;
    }

    public void setConstraintType(String constraintType) {
        this.constraintType = constraintType;
    }

    public String getRuleCodeType() {
        return ruleCodeType;
    }

    public void setRuleCodeType(String ruleCodeType) {
        this.ruleCodeType = ruleCodeType;
    }

    public String getCodeCName() {
        return codeCName;
    }

    public void setCodeCName(String codeCName) {
        this.codeCName = codeCName;
    }

    public String getCodeLName() {
        return codeLName;
    }

    public void setCodeLName(String codeLName) {
        this.codeLName = codeLName;
    }

    public String getCodeEName() {
        return codeEName;
    }

    public void setCodeEName(String codeEName) {
        this.codeEName = codeEName;
    }
}