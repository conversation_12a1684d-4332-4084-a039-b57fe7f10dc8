create or replace package dm_pack_duct_stat is

    PROCEDURE proc_update_bussprd_dtl_all(p_entity_id   number,
                                          p_year_month  varchar,
                                          p_biz_type_id number);

    PROCEDURE proc_update_bussprd_dtl(p_entity_id   number,
                                      p_year_month  varchar,
                                      p_biz_type_id number);

    function func_check_stat_count(p_entity_id   number,
                                   p_year_month  varchar,
                                   p_biz_type_id number) return varchar2;

    function func_paring_stat_sql(p_result_sql varchar2) return number;

    PROCEDURE proc_paring_stat(p_entity_id   NUMBER,
                               p_task_code   varchar2,
                               p_biz_type_id NUMBER);

    PROCEDURE proc_paring_stat_etl(p_entity_id   number,
                                   p_task_code   varchar2,
                                   p_biz_type_id number);

    PROCEDURE proc_paring_stat_reset(p_biz_type_id number);

    PROCEDURE proc_paring_stat_add_col(p_biz_type_id number,
                                       p_col_name    varchar2,
                                       p_stat_type   varchar2);
    PROCEDURE proc_paring_stat_del_col(p_biz_type_id number,
                                       p_col_name    varchar2);

    PROCEDURE proc_paring_stat_all;

end dm_pack_duct_stat;
/
create or replace package body dm_pack_duct_stat is

    PROCEDURE proc_update_bussprd_dtl_all(p_entity_id   number,
                                          p_year_month  varchar,
                                          p_biz_type_id number) is
    begin
        if p_biz_type_id is null then
            for cur_table in (select biz_type_id from dm_conf_table) loop
                    proc_update_bussprd_dtl(p_entity_id,
                                            p_year_month,
                                            cur_table.biz_type_id);
                end loop;
        else
            proc_update_bussprd_dtl(p_entity_id, p_year_month, p_biz_type_id);
        end if;

    end proc_update_bussprd_dtl_all;

    PROCEDURE proc_update_bussprd_dtl(p_entity_id   number,
                                      p_year_month  varchar,
                                      p_biz_type_id number) is
        v_check_status   varchar2(2);
        v_count          number;
        v_biz_code       varchar2(50);
        v_task_code      varchar2(200);
        v_ready_state    varchar2(1);
        v_BUSS_PERIOD_ID number;
        v_error_msg      VARCHAR2(2000);
    begin

        if p_biz_type_id is not null then

            v_check_status := func_check_stat_count(p_entity_id,
                                                    p_year_month,
                                                    p_biz_type_id);

            if v_check_status = '0' then
                return;
            end if;
            --查找信号表任意一条task_code为按月还是按日，按月可更新，按日最后一天才做更新
            select count(1)
            into v_count
            from dm_conf_table
            where biz_type_id = p_biz_type_id;

            if v_count < 1 then

                --意外处理
                v_error_msg := '[EXCEPTION][数据统计]统计数量-proc_update_bussprd_dtl: 无效模型';
                --捕获异常，往外抛
                raise_application_error(-20004, v_error_msg);

            end if;

            select biz_code
            into v_biz_code
            from dm_conf_table
            where biz_type_id = p_biz_type_id;

            if v_check_status = '1' then

                select count(1)
                into v_count
                from ods_data_push_signal
                where year_month = p_year_month
                  and push_model = v_biz_code
                  and TASK_STATUS = '3';

                if v_count < 1 then

                    --意外处理
                    v_error_msg := '[EXCEPTION][数据统计]统计数量-proc_update_bussprd_dtl: 无效信号表数据';
                    --捕获异常，往外抛
                    raise_application_error(-20004, v_error_msg);

                end if;

                select min(task_code)
                into v_task_code
                from ods_data_push_signal
                where year_month = p_year_month
                  and push_model = v_biz_code
                  and TASK_STATUS = '3';
                v_ready_state := '1';

                /*IF substr(v_task_code, 14, 2) != '00' and
                   to_char(SYSDATE, 'yyyyMMdd') !=
                   to_char(last_day(SYSDATE), 'yyyyMMdd') THEN
                    v_ready_state := '0';
                end if;*/

                select count(1)
                into v_count
                from dm_conf_bussperiod
                where entity_id = p_entity_id
                  and year_month = p_year_month;

                if v_count < 0 then

                    --意外处理
                    v_error_msg := '[EXCEPTION][数据统计]统计数量-proc_update_bussprd_dtl: 无效月份-' ||
                                   p_year_month;
                    --捕获异常，往外抛
                    raise_application_error(-20004, v_error_msg);

                end if;

                select BUSS_PERIOD_ID
                into v_BUSS_PERIOD_ID
                from dm_conf_bussperiod
                where entity_id = p_entity_id
                  and year_month = p_year_month;

                if v_ready_state = '1' then
                    update dm_conf_bussperiod_detail
                    set READY_STATE = '1', EXEC_RESULT = 'success'
                    where BUSS_PERIOD_ID = v_BUSS_PERIOD_ID
                      and biz_type_id = p_biz_type_id
                      and DIRECTION = '1';

                end if;

            end if;

            if v_check_status = '2' then
                --重新统计
                for cur_push_model in (select task_code
                                       from ods_data_push_signal
                                       where year_month = p_year_month
                                         and push_model = v_biz_code
                                         and TASK_STATUS <> '0') loop
                        proc_paring_stat(p_entity_id,
                                         cur_push_model.task_code,
                                         p_biz_type_id);
                    end loop;

               --proc_update_bussprd_dtl(p_entity_id, p_year_month, p_biz_type_id);

            end if;

        end if;

    end proc_update_bussprd_dtl;

    /**
    ** func name : func_check_stat_count
    ** 检查当前准备中的业务期间，etl的数据统计数量所对应模型是否正确
    **
    ** return 0为参数不正确，1为正确，,2为需要重新统计
    **
    **/
    function func_check_stat_count(p_entity_id   number,
                                   p_year_month  varchar,
                                   p_biz_type_id number) return varchar2 is
        v_count             number;
        v_biz_code          varchar2(50);
        v_src_table         varchar2(50);
        v_entity_code       varchar2(100);
        v_check_sql         varchar2(2000);
        v_count_entity_code number;
        v_check_count       number;
        v_stat_table        varchar2(200);
    begin

        select count(1)
        into v_count
        from bpluser.bbs_entity
        where entity_id = p_entity_id;
        if v_count < 1 then
            --检查业务单位
            return '0';
        end if;

        select entity_code
        into v_entity_code
        from bpluser.bbs_entity
        where entity_id = p_entity_id;

        --检查当前业务期间是否是准备中的
        select count(1)
        into v_count
        from dm_conf_bussperiod
        where entity_id = p_entity_id
          and year_month = p_year_month
          and period_state = '0';
        if v_count < 1 then
            --当前业务期间不在准备中
            return '0';
        end if;

        if p_biz_type_id is null then
            return '0';
        end if;

        select count(1)
        into v_count
        from dm_conf_table
        where biz_type_id = p_biz_type_id;

        if v_count < 1 then
            --检查当前的业务模型是否存在
            return '0';
        end if;

        select biz_code, 'ODS_' || biz_code, 'DM_STAT_' || biz_code
        into v_biz_code, v_src_table, v_stat_table
        from dm_conf_table
        where biz_type_id = p_biz_type_id;

        --查找信号表的数据

        select count(1)
        into v_count
        from ods_data_push_signal
        where year_month = p_year_month
          and push_model = v_biz_code
          and TASK_STATUS = '3';

        if v_count < 1 then
            --检查信号表的数据是否存在
            return '0';
        end if;

        --查询当前模型是否存在entity_code字段
        select count(1)
        into v_count_entity_code
        from user_tab_columns
        where table_name = upper(v_stat_table)
          and column_name = upper('entity_code');

        for cur_push_signal in (select task_code
                                from ods_data_push_signal
                                where year_month = p_year_month
                                  and push_model = v_biz_code
                                  and TASK_STATUS = '3') loop
                --检查src表的数量是否跟统计表的数据一致
                if v_count_entity_code > 0 then
                    v_check_sql := 'select count(1)
            from ' || v_stat_table ||
                                   ' stat
                        join (select entity_code,task_code,to_char(DRAW_TIME,''yyyy-MM-DD'') as DRAW_TIME ,TASK_STATUS,count(1) as sum_count
                        from ' || v_src_table ||
                                   ' where task_code = ''' || cur_push_signal.task_code ||
                                   ''' and entity_code = ''' || v_entity_code || '''
            group by entity_code,task_code,to_char(DRAW_TIME,''yyyy-MM-DD''),TASK_STATUS) t
            on stat.entity_code = t.entity_code and stat.task_code = t.task_code
            and to_char(stat.draw_time,''yyyy-MM-DD'') =  t.DRAW_TIME and stat.TASK_STATUS = t.TASK_STATUS
            and stat.quantity <> t.sum_count';
                else
                    v_check_sql := 'select count(1)
            from ' || v_stat_table ||
                                   ' stat
                        join (select task_code,to_char(DRAW_TIME,''yyyy-MM-DD'') as DRAW_TIME ,TASK_STATUS,count(1) as sum_count
                        from ' || v_src_table ||
                                   ' where task_code = ''' || cur_push_signal.task_code || '''
            group by task_code,to_char(DRAW_TIME,''yyyy-MM-DD''),TASK_STATUS) t
            on  stat.task_code = t.task_code
            and to_char(stat.draw_time,''yyyy-MM-DD'') =  t.DRAW_TIME and stat.TASK_STATUS = t.TASK_STATUS
            and stat.quantity <> t.sum_count';
                end if;

                execute immediate v_check_sql
                    into v_check_count;

                if v_check_count > 0 then
                    --大于0则存在不相同的
                    return '2';
                end if;

            end loop;

        return '1';

    end func_check_stat_count;

    function func_paring_stat_sql(p_result_sql varchar2) return number is
        v_result number;
    begin

        if p_result_sql is null or trim(p_result_sql) = '' then
            return null;
        end if;

        execute immediate p_result_sql
            into v_result;

        return v_result;

    EXCEPTION
        WHEN OTHERS THEN
            dbms_output.put_line(SQLERRM);

    end func_paring_stat_sql;

    PROCEDURE proc_paring_stat(p_entity_id   number,
                               p_task_code   varchar2,
                               p_biz_type_id number) is
        v_count            number;
        v_table            varchar2(200);
        v_biz_code         varchar2(100);
        v_entity_code      varchar2(100);
        v_src_table        varchar2(200);
        v_sequence         varchar2(200);
        v_sql              varchar2(4000);
        v_stat_columns     varchar2(4000);
        v_group_columns    varchar2(4000);
        v_order_columns    varchar2(4000);
        v_sum_columns      varchar2(4000);
        v_ori_stat_columns varchar2(4000);
        v_ori_sum_columns  varchar2(4000);
        -- 是否只用于统计数量
        v_is_only_count    varchar2(2) := '0';
        v_have_entity_code number;
        v_error_msg        VARCHAR2(2000);
    begin

        -- 查询是否为需要统计金额的
        select max(biz_code),
               sum(case
                       when sum_is = '0' then
                           0
                       else
                           1
                   end)
        into v_biz_code, v_count
        from dm_conf_table t
                 left join dm_conf_table_column c
                           on c.biz_type_id = t.biz_type_id
        where t.valid_is = '1'
          and t.biz_type_id = p_biz_type_id
        group by biz_code;
        --dbms_output.put_line(v_count);

        -- 上面查询为空则只统计数量
        if v_count = 0 then
            v_is_only_count := '1';
        end if;

        -- 都为空，直接返回
        if v_biz_code is null then
            return;
        end if;

        v_table     := 'DM_STAT_' || v_biz_code;
        v_src_table := 'ODS_' || v_biz_code;
        v_sequence  := 'DM_SEQ_STAT_' || v_biz_code;
        --dbms_output.put_line(v_table);


        -- 查询entityCode
        if p_entity_id is not null then
            select max(entity_code)
            into v_entity_code
            from bpluser.bbs_entity
            where entity_id = p_entity_id;
        end if;

        --查询当前模型是否存在entity_code字段
        select count(1)
        into v_have_entity_code
        from user_tab_columns
        where table_name = upper(v_table)
          and column_name = upper('entity_code');

        -- 当存在entity_code 但查不到返回
        if v_entity_code is null and v_have_entity_code <> 0 then
            return;
        end if;

        -- clean old data
        v_sql := 'delete from $$table where task_code = ''' || p_task_code || '''';
        if v_have_entity_code > 0 and v_biz_code <> 'BASE_ENTITY' then
            v_sql := v_sql || ' and entity_code = ''' || v_entity_code || '''';
        end if;
        v_sql := replace(v_sql, '$$table', v_table);
        execute immediate v_sql;

        -- 同时统计金额和数量
        if v_is_only_count = '0' then
            -- 获取统计、group by、order by 字段
            select listagg((case
                                when upper(col.COL_TYPE) in ('DATE', 'TIMESTAMP') then
                                        'to_char(' || col.col_code || ',''yyyy/MM/dd'') as ' ||
                                        col.col_code
                                else
                                    col.col_code
                end),
                           ',') WITHIN group(order by col.col_code),
                   listagg((case
                                when upper(col.COL_TYPE) in ('DATE', 'TIMESTAMP') then
                                        'to_char(' || col.col_code || ',''yyyy/MM/dd'')'
                                else
                                    col.col_code
                       end),
                           ',') WITHIN group(order by col.col_code) v_statistical_group,
                   listagg(col.col_code, ',') WITHIN group(order by col.col_code),
                   listagg(col.col_code, ',') WITHIN group(order by col.col_code)
            into v_stat_columns,
                v_group_columns,
                v_order_columns,
                v_ori_stat_columns
            from dm_conf_table_column col
            where col.biz_type_id = p_biz_type_id
              and col.statistical_is = '1'
              and col.valid_is = '1'
              and ((col.col_code = upper('entity_code') and COL.BIZ_TYPE_ID not in (select BIZ_TYPE_ID from dm_conf_table where biz_code = upper('BASE_ENTITY')))
       or col.col_code <> upper('entity_code'));

            -- 获取汇总字段
            select listagg('sum(' || col.col_code || ') as ' || col.col_code, ',') WITHIN group(order by col.col_code),
                   listagg(col.col_code, ',') WITHIN group(order by col.col_code)
            into v_sum_columns, v_ori_sum_columns
            from dm_conf_table_column col
            where col.biz_type_id = p_biz_type_id
              and col.sum_is = '1'
              and col.valid_is = '1';

            -- create insert sql
            v_sql := '
          insert into $$table (
               id,
               task_code,
               draw_time,
               task_status,
               quantity
               $$ori_stat_columns
               $$ori_sum_columns
             )
             select
                 $$sequence.nextval,
                 task_code,
                 draw_time,
                 task_status,
                 quantity
                 $$ori_stat_columns
                 $$ori_sum_columns
               from
                 (select
                         task_code,
                         trunc(draw_time) draw_time,
                         (case
                           when task_status not in (''6'', ''0'') then
                            ''2''
                           else
                            task_status
                         end) task_status,
                         count(1) as quantity
                         $$stat_columns
                         $$sum_columns
                    from $$src_table
                   where task_code = :1 $$condition_entity
                   group by
                            task_code,
                            draw_time,
                            (case
                             when task_status not in (''6'', ''0'') then
                              ''2''
                             else
                              task_status
                           end)
                            $$group_columns
                    order by
                            task_code,
                            draw_time,
                            TASK_STATUS
                            $$order_columns
                        )
                    ';

            v_sql := replace(v_sql, '$$table', v_table);
            v_sql := replace(v_sql, '$$src_table', v_src_table);
            v_sql := replace(v_sql, '$$sequence', v_sequence);

                        if v_ori_stat_columns is null or v_ori_stat_columns = '' then
                v_sql := replace(v_sql, '$$ori_stat_columns', '');
            else
                v_sql := replace(v_sql, '$$ori_stat_columns', ','||v_ori_stat_columns);
            end if;

            if v_ori_sum_columns is null or v_ori_sum_columns = '' then
                v_sql := replace(v_sql, '$$ori_sum_columns', '');
            else
                v_sql := replace(v_sql, '$$ori_sum_columns', ','||v_ori_sum_columns);
            end if;

            if v_stat_columns is null or v_stat_columns = '' then
                 v_sql := replace(v_sql, '$$stat_columns', '');
            else
                 v_sql := replace(v_sql, '$$stat_columns', ','||v_stat_columns);
            end if;

            if v_sum_columns is null or v_sum_columns = '' then
                 v_sql := replace(v_sql, '$$sum_columns', '');
            else
                v_sql := replace(v_sql, '$$sum_columns', ','||v_sum_columns);
            end if;

           if v_group_columns is null or v_group_columns = '' then
              v_sql := replace(v_sql, '$$group_columns', '');
           else
              v_sql := replace(v_sql, '$$group_columns', ','||v_group_columns);
           end if;

           if v_order_columns is null or v_order_columns = '' then
              v_sql := replace(v_sql, '$$order_columns', '');
           else
             v_sql := replace(v_sql, '$$order_columns', ','||v_order_columns);
           end if;

            if v_have_entity_code > 0 and v_biz_code <> 'BASE_ENTITY' then
               v_sql := replace(v_sql, '$$condition_entity', ' and entity_code = :2 ');
                 -- insert data
               execute immediate v_sql
                 using p_task_code, v_entity_code;
            else
                -- insert data
              execute immediate v_sql
                using p_task_code;
            end if;


            commit;
            -- 只处理数量
        else
            select count(1)
            into v_count
            from bpluser.bbs_entity
            where entity_id = p_entity_id;
            if v_count > 0 then
                select entity_code
                into v_entity_code
                from bpluser.bbs_entity
                where entity_id = p_entity_id;
            end if;

            -- 对于没有entity_code 和base_entity 来说，需要去除entity_code条件
            if v_have_entity_code < 1 or v_biz_code = 'BASE_ENTITY' then
                v_sql := 'insert into ' || v_table || '(
            id,draw_time,TASK_CODE,task_status,quantity)
            select ' || v_sequence || '.nextval,to_date(t.DRAW_TIME,''yyyy-MM-DD''),t.task_code,t.task_status,t.quantity
             from
            (select to_char(DRAW_TIME,''yyyy-MM-DD'') as DRAW_TIME,task_code,TASK_STATUS,count(1) as quantity
            from ' || v_src_table ||
                         ' group by to_char(DRAW_TIME,''yyyy-MM-DD''),TASK_STATUS,task_code) t
                            where task_code = ''' || p_task_code || '''';
            else

                v_sql := 'insert into ' || v_table || '(
            id,draw_time,entity_code,TASK_CODE,task_status,quantity)
            select ' || v_sequence || '.nextval,to_date(t.DRAW_TIME,''yyyy-MM-DD''),t.entity_code,t.task_code,t.task_status,t.quantity
             from
            (select to_char(DRAW_TIME,''yyyy-MM-DD'') as DRAW_TIME,entity_code,task_code,TASK_STATUS,count(1) as quantity
            from ' || v_src_table ||
                         ' group by to_char(DRAW_TIME,''yyyy-MM-DD''),TASK_STATUS,task_code,entity_code) t
                            where task_code = ''' || p_task_code ||
                         ''' and entity_code = ''' || v_entity_code || '''';
            end if;
            execute immediate v_sql;

            commit;
        end if;

    end proc_paring_stat;

    /**
    ** proc name :  proc_paring_stat_amount_etl
    ** param :
    **      p_entity_id 业务单位id
    ** describe ： 根据模型统计数量表，进行未处理的模型进行重新统计金额
    **/
    PROCEDURE proc_paring_stat_etl(p_entity_id   number,
                                   p_task_code   varchar2,
                                   p_biz_type_id number) is
        v_count      number;
        v_stat_table varchar2(100);
        v_error_msg  VARCHAR2(2000);
    begin

        if p_entity_id is null then

            --意外处理
            v_error_msg := '[EXCEPTION][数据统计]统计金额-proc_paring_stat_etl:
                      无效参数(p_entity_id:' || p_entity_id ||
                           ',p_task_code:' || p_task_code || ',p_biz_type_id:' ||
                           p_biz_type_id || ')';
            --捕获异常，往外抛
            raise_application_error(-20004, v_error_msg);

        end if;

        if p_task_code is null and p_biz_type_id is null then
            -- 只有entity_id 则对该id下所有模型进行重新处理
            for cur_ps in (select ps.task_code   as task_code,
                                  ct.biz_type_id as biz_type_id
                           from ods_data_push_signal ps
                                    join dm_conf_table ct
                                         on ps.push_model = ct.biz_code
                           where ps.TASK_STATUS = '0'
                           order by ps.year_month) loop
                    proc_paring_stat(p_entity_id, cur_ps.task_code, cur_ps.biz_type_id);
                end loop;

        elsif p_task_code is not null and p_biz_type_id is null then
            -- 指定任务批次和业务单位进行处理
            for cur_ps in (select ps.task_code   as task_code,
                                  ct.biz_type_id as biz_type_id
                           from ods_data_push_signal ps
                                    join dm_conf_table ct
                                         on ps.push_model = ct.biz_code
                           where ps.TASK_STATUS = '0'
                             and ps.task_code = p_task_code
                           order by ps.year_month) loop
                    proc_paring_stat(p_entity_id, cur_ps.task_code, cur_ps.biz_type_id);
                end loop;

        elsif p_task_code is null and p_biz_type_id is not null then
            -- 指定任务批次和业务单位进行处理
            for cur_ps in (select ps.task_code   as task_code,
                                  ct.biz_type_id as biz_type_id
                           from ods_data_push_signal ps
                                    join dm_conf_table ct
                                         on ps.push_model = ct.biz_code
                           where ps.TASK_STATUS = '0'
                             and ct.biz_type_id = p_biz_type_id
                           order by ps.year_month) loop
                    proc_paring_stat(p_entity_id, cur_ps.task_code, cur_ps.biz_type_id);
                end loop;
        else
            -- 参数完全，则只处理指定的模型
            proc_paring_stat(p_entity_id, p_task_code, p_biz_type_id);
        end if;
    end proc_paring_stat_etl;

    /**
    ** proc name :  proc_paring_stat_by_biz_type
    ** param :
    **      p_biz_type_id 模型id
    ** describe ： 统计对应模型
    **/
    PROCEDURE proc_paring_stat_reset(p_biz_type_id number) is
        v_table            varchar2(200);
        v_src_table        varchar2(200);
        v_sequence         varchar2(200);
        v_sql              varchar2(4000);
        v_stat_columns     varchar2(4000);
        v_group_columns    varchar2(4000);
        v_order_columns    varchar2(4000);
        v_sum_columns      varchar2(4000);
        v_ori_stat_columns varchar2(4000);
        v_ori_sum_columns  varchar2(4000);
        v_is_only_count    varchar2(2);
        v_biz_code         varchar2(200);
        v_count            number;
    begin

        select biz_code
        into v_biz_code
        from dm_conf_table t
        where valid_is = '1'
          and biz_type_id = p_biz_type_id;

        if v_biz_code is null then
            return;
        end if;

        v_table     := 'DM_STAT_' || v_biz_code;
        v_src_table := 'ODS_' || v_biz_code;
        v_sequence  := 'DM_SEQ_STAT_' || v_biz_code;

        --dbms_output.put_line(v_table);

        -- clean
        -- 这里不使用 truncate ，  避免无法回滚
        v_sql := 'delete from ' || v_table;
        execute immediate v_sql;
        -- 重置序列
        v_sql := 'alter sequence ' || v_sequence || ' increment by ' || 1 ||
                 ' minvalue 0';
        execute immediate v_sql;

        -- 判断是否只需要处理数量
        select count(1)
        into v_count
        from dm_conf_table_column
        where BIZ_TYPE_ID = p_biz_type_id
          and STATISTICAL_IS = '1';

        if v_count > 0 then
            v_is_only_count := '0';
        else
            v_is_only_count := '1';
        end if;

        if v_is_only_count = '0' then
            -- 同时处理统计金额和数量

            -- 获取统计、group by、order by 字段
            select listagg((case
                                when upper(col.COL_TYPE) in ('DATE', 'TIMESTAMP') then
                                        'to_char(' || col.col_code || ',''yyyy/MM/dd'') as ' ||
                                        col.col_code
                                else
                                    col.col_code
                end),
                           ',') WITHIN group(order by col.col_code),
                   listagg((case
                                when upper(col.COL_TYPE) in ('DATE', 'TIMESTAMP') then
                                        'to_char(' || col.col_code || ',''yyyy/MM/dd'')'
                                else
                                    col.col_code
                       end),
                           ',') WITHIN group(order by col.col_code) v_statistical_group,
                   listagg(col.col_code, ',') WITHIN group(order by col.col_code),
                   listagg(col.col_code, ',') WITHIN group(order by col.col_code)
            into v_stat_columns,
                v_group_columns,
                v_order_columns,
                v_ori_stat_columns
            from dm_conf_table_column col
            where col.biz_type_id = p_biz_type_id
              and col.statistical_is = '1'
              and col.valid_is = '1'
                and ((col.col_code = upper('entity_code') and COL.BIZ_TYPE_ID not in (select BIZ_TYPE_ID from dm_conf_table where biz_code = upper('BASE_ENTITY')))
       or col.col_code <> upper('entity_code'));

            -- 获取汇总字段
            select listagg('sum(' || col.col_code || ') as ' || col.col_code, ',') WITHIN group(order by col.col_code),
                   listagg(col.col_code, ',') WITHIN group(order by col.col_code)
            into v_sum_columns, v_ori_sum_columns
            from dm_conf_table_column col
            where col.biz_type_id = p_biz_type_id
              and col.sum_is = '1'
              and col.valid_is = '1';

            -- create insert sql
            v_sql := '
          insert into $$table (
               id,
               task_code,
               draw_time,
               task_status,
               quantity
               $$ori_stat_columns
               $$ori_sum_columns
             )
             select
                 $$sequence.nextval,
                 task_code,
                 draw_time,
                 task_status,
                 quantity
                 $$ori_stat_columns
                 $$ori_sum_columns
               from
                 (select
                         task_code,
                         trunc(draw_time) draw_time,
                         (case
                           when task_status not in (''6'', ''0'') then
                            ''2''
                           else
                            task_status
                         end) task_status,
                         count(1) as quantity
                         $$stat_columns
                         $$sum_columns
                    from $$src_table
                   group by
                            task_code,
                            draw_time,
                            (case
                             when task_status not in (''6'', ''0'') then
                              ''2''
                             else
                              task_status
                           end)
                            $$group_columns
                    order by
                            task_code,
                            draw_time,
                            TASK_STATUS
                            $$order_columns
                        )
                    ';

            v_sql := replace(v_sql, '$$table', v_table);
            v_sql := replace(v_sql, '$$src_table', v_src_table);
            v_sql := replace(v_sql, '$$sequence', v_sequence);

            if v_ori_stat_columns is null or v_ori_stat_columns = '' then
                v_sql := replace(v_sql, '$$ori_stat_columns', '');
            else
                v_sql := replace(v_sql, '$$ori_stat_columns', ','||v_ori_stat_columns);
            end if;

            if v_ori_sum_columns is null or v_ori_sum_columns = '' then
                v_sql := replace(v_sql, '$$ori_sum_columns', '');
            else
                v_sql := replace(v_sql, '$$ori_sum_columns', ','||v_ori_sum_columns);
            end if;

            if v_stat_columns is null or v_stat_columns = '' then
                 v_sql := replace(v_sql, '$$stat_columns', '');
            else
                 v_sql := replace(v_sql, '$$stat_columns', ','||v_stat_columns);
            end if;

            if v_sum_columns is null or v_sum_columns = '' then
                 v_sql := replace(v_sql, '$$sum_columns', '');
            else
                v_sql := replace(v_sql, '$$sum_columns', ','||v_sum_columns);
            end if;

           if v_group_columns is null or v_group_columns = '' then
              v_sql := replace(v_sql, '$$group_columns', '');
           else
              v_sql := replace(v_sql, '$$group_columns', ','||v_group_columns);
           end if;

           if v_order_columns is null or v_order_columns = '' then
              v_sql := replace(v_sql, '$$order_columns', '');
           else
             v_sql := replace(v_sql, '$$order_columns', ','||v_order_columns);
           end if;

            -- insert data
            execute immediate v_sql;

        else
            -- 只统计金额
            --查询当前模型是否存在entity_code字段
            select count(1)
            into v_count
            from user_tab_columns
            where table_name = upper(v_table)
              and column_name = upper('entity_code');

            -- 对于没有entity_code 和base_entity 来说，需要去除entity_code条件
            if v_count < 1 or v_biz_code = 'BASE_ENTITY' then
                v_sql := 'insert into ' || v_table || '(
            id,draw_time,TASK_CODE,task_status,quantity)
            select ' || v_sequence || '.nextval,to_date(t.DRAW_TIME,''yyyy-MM-DD''),t.task_code,t.task_status,t.quantity
             from
            (select to_char(DRAW_TIME,''yyyy-MM-DD'') as DRAW_TIME,task_code,TASK_STATUS,count(1) as quantity
            from ' || v_src_table ||
                         ' group by to_char(DRAW_TIME,''yyyy-MM-DD''),TASK_STATUS,task_code) t ';
            else

                v_sql := 'insert into ' || v_table || '(
            id,draw_time,entity_code,TASK_CODE,task_status,quantity)
            select ' || v_sequence || '.nextval,to_date(t.DRAW_TIME,''yyyy-MM-DD''),t.entity_code,t.task_code,t.task_status,t.quantity
             from
            (select to_char(DRAW_TIME,''yyyy-MM-DD'') as DRAW_TIME,entity_code,task_code,TASK_STATUS,count(1) as quantity
            from ' || v_src_table ||
                         ' group by to_char(DRAW_TIME,''yyyy-MM-DD''),TASK_STATUS,task_code,entity_code) t';
            end if;
            execute immediate v_sql;

            commit;
        end if;
        commit;
    end proc_paring_stat_reset;

    /**
    ** proc name :  proc_paring_stat_amount_add_col
    ** param :
    **      p_biz_type_id 模型id
    **      p_col_name    列名
    **      p_stat_type  统计类型：1为统计字段，2为汇总字段
    ** describe ： 对应的模型统计金额表，新增加统计字段或汇总字段
    **/
    PROCEDURE proc_paring_stat_add_col(p_biz_type_id number,
                                       p_col_name    varchar2,
                                       p_stat_type   varchar2) is
        v_count       number;
        v_stat_table  varchar2(100);
        v_column_type varchar2(100);
        v_error_msg   VARCHAR2(2000);
    begin

        if p_col_name is null or p_stat_type is null then

            --意外处理
            v_error_msg := '[EXCEPTION][数据统计]统计金额-proc_paring_stat_add_col:
                      无效参数(p_col_name:' || p_col_name ||
                           ',p_stat_type:' || p_stat_type || ')';
            --捕获异常，往外抛
            raise_application_error(-20004, v_error_msg);

        end if;

        select count(*)
        into v_count
        from dm_conf_table
        where biz_type_id = p_biz_type_id;

        if v_count < 1 then

            --意外处理
            v_error_msg := '[EXCEPTION][数据统计]统计金额-proc_paring_stat_add_col:无效模型';
            --捕获异常，往外抛
            raise_application_error(-20004, v_error_msg);

        end if;

        select 'DM_STAT_' || biz_code
        into v_stat_table
        from dm_conf_table
        where biz_type_id = p_biz_type_id;

        if p_stat_type = '1' then
            v_column_type := 'VARCHAR2(1000)';
        elsif p_stat_type = '2' then
            v_column_type := 'NUMBER(16,2)';
        else
            v_column_type := '';
        end if;

        dm_pack_commonutils.ADD_TABLE_COLUMN(v_stat_table,
                                             p_col_name,
                                             v_column_type,
                                             null);

    end proc_paring_stat_add_col;

    /**
    ** proc name :  proc_paring_stat_amount_del_col
    ** param :
    **      p_biz_type_id 模型id
    **      p_col_name    列名
    ** describe ： 对应的模型统计金额表，删除统计字段或汇总字段
    **/
    PROCEDURE proc_paring_stat_del_col(p_biz_type_id number,
                                       p_col_name    varchar2) is
        v_stat_table varchar2(100);
        v_count      number;
        v_error_msg  VARCHAR2(2000);
    begin
        if p_biz_type_id is null or p_col_name is null then

            --意外处理
            v_error_msg := '[EXCEPTION][数据统计]统计金额-proc_paring_stat_del_col:
                      无效参数(p_biz_type_id:' ||
                           p_biz_type_id || ',p_col_name:' || p_col_name || ')';
            --捕获异常，往外抛
            raise_application_error(-20004, v_error_msg);

        end if;

        select count(*)
        into v_count
        from dm_conf_table
        where biz_type_id = p_biz_type_id;

        if v_count < 1 then

            --意外处理
            v_error_msg := '[EXCEPTION][数据统计]统计金额-proc_paring_stat_del_col:无效模型';
            --捕获异常，往外抛
            raise_application_error(-20004, v_error_msg);

        end if;

        select 'DM_STAT_' || biz_code
        into v_stat_table
        from dm_conf_table
        where biz_type_id = p_biz_type_id;

        dm_pack_commonutils.DROP_TABLE_COLUMN(v_stat_table, p_col_name);

    end proc_paring_stat_del_col;

    PROCEDURE proc_paring_stat_all is
    begin

        for rec in (select biz_type_id from dm_conf_table t where valid_is = '1') loop
                proc_paring_stat_reset(rec.biz_type_id);
            end loop;
    end proc_paring_stat_all;

end dm_pack_duct_stat;
/
