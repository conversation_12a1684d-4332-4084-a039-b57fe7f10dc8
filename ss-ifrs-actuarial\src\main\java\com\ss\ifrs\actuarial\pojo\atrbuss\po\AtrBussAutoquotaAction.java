/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-08-28 15:11:17
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-08-28 15:11:17<br/>
 * Description: 假设值自动计算配置控制表<br/>
 * Table Name: atr_buss_autoquota_action<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "假设值自动计算配置控制表")
public class AtrBussAutoquotaAction implements Serializable {
    /**
     * Database column: atr_buss_autoquota_action.id
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: atr_buss_autoquota_action.action_no
     * Database remarks: 执行编号
     */
    @ApiModelProperty(value = "执行编号", required = true)
    private String actionNo;

    /**
     * Database column: atr_buss_autoquota_action.entity_id
     * Database remarks: 核算单位
     */
    @ApiModelProperty(value = "核算单位", required = true)
    private Long entityId;

    /**
     * Database column: atr_buss_autoquota_action.business_source_code
     * Database remarks: 业务类型|DD-直接业务&临分分入业务，TI-合约分入，FO-临分分出，TO-合约分出
     */
    @ApiModelProperty(value = "业务类型|DD-直接业务&临分分入业务，TI-合约分入，FO-临分分出，TO-合约分出", required = false)
    private String businessSourceCode;

    /**
     * Database column: atr_buss_autoquota_action.loa_code
     * Database remarks: 业务线
     */
    @ApiModelProperty(value = "业务线", required = false)
    private String loaCode;

    /**
     * Database column: atr_buss_autoquota_action.deadline
     * Database remarks: 提取截止日期
     */
    @ApiModelProperty(value = "提取截止日期", required = true)
    private Date deadline;

    /**
     * Database column: atr_buss_autoquota_action.draw_interval
     * Database remarks: 提取区间
     */
    @ApiModelProperty(value = "提取区间", required = true)
    private String drawInterval;

    /**
     * Database column: atr_buss_autoquota_action.draw_interval_quantity
     * Database remarks: 提取区间数
     */
    @ApiModelProperty(value = "提取区间数", required = true)
    private Integer drawIntervalQuantity;

    /**
     * Database column: atr_buss_autoquota_action.appli_ev_yearmonths
     * Database remarks: 适配评估期|多个时用英文逗号分隔
     */
    @ApiModelProperty(value = "适配评估期|多个时用英文逗号分隔", required = false)
    private String appliEvYearmonths;

    /**
     * Database column: atr_buss_autoquota_action.appli_quota_codes
     * Database remarks: 适配假设值|多个时用英文逗号分隔
     */
    @ApiModelProperty(value = "适配假设值|多个时用英文逗号分隔", required = true)
    private String appliQuotaCodes;

    /**
     * Database column: atr_buss_autoquota_action.oper_id
     * Database remarks: 操作人
     */
    @ApiModelProperty(value = "操作人", required = true)
    private Long operId;

    /**
     * Database column: atr_buss_autoquota_action.oper_time
     * Database remarks: 操作时间
     */
    @ApiModelProperty(value = "操作时间", required = true)
    private Date operTime;

    /**
     * Database column: atr_buss_autoquota_action.draw_state
     * Database remarks: 0-待执行、1-执行中、2-成功、3-失败、4-不执行； 码表： TaskStatus
     */
    @ApiModelProperty(value = "提取状态|0-未开始、1-已完成、2-异常； 码表： IdentifyState", required = true)
    private String drawState;

    /**
     * Database column: atr_buss_autoquota_action.draw_start_time
     * Database remarks: 提取开始时间
     */
    @ApiModelProperty(value = "提取开始时间", required = false)
    private Date drawStartTime;

    /**
     * Database column: atr_buss_autoquota_action.draw_end_time
     * Database remarks: 提取结束时间
     */
    @ApiModelProperty(value = "提取结束时间", required = false)
    private Date drawEndTime;

    /**
     * Database column: atr_buss_autoquota_action.approval_state
     * Database remarks: 审核状态|0-待审核、1-审核通过、2-审核不通过； 码表： AuditStatus/View
     */
    @ApiModelProperty(value = "审核状态|0-待审核、1-审核通过、2-审核不通过； 码表： AuditStatus/View", required = true)
    private String approvalState;

    /**
     * Database column: atr_buss_autoquota_action.approval_id
     * Database remarks: 审核人
     */
    @ApiModelProperty(value = "审核人", required = false)
    private Long approvalId;

    /**
     * Database column: atr_buss_autoquota_action.approval_time
     * Database remarks: 审核通过时间
     */
    @ApiModelProperty(value = "审核通过时间", required = false)
    private Date approvalTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public Date getDeadline() {
        return deadline;
    }

    public void setDeadline(Date deadline) {
        this.deadline = deadline;
    }

    public String getDrawInterval() {
        return drawInterval;
    }

    public void setDrawInterval(String drawInterval) {
        this.drawInterval = drawInterval;
    }

    public Integer getDrawIntervalQuantity() {
        return drawIntervalQuantity;
    }

    public void setDrawIntervalQuantity(Integer drawIntervalQuantity) {
        this.drawIntervalQuantity = drawIntervalQuantity;
    }

    public String getAppliEvYearmonths() {
        return appliEvYearmonths;
    }

    public void setAppliEvYearmonths(String appliEvYearmonths) {
        this.appliEvYearmonths = appliEvYearmonths;
    }

    public String getAppliQuotaCodes() {
        return appliQuotaCodes;
    }

    public void setAppliQuotaCodes(String appliQuotaCodes) {
        this.appliQuotaCodes = appliQuotaCodes;
    }

    public Long getOperId() {
        return operId;
    }

    public void setOperId(Long operId) {
        this.operId = operId;
    }

    public Date getOperTime() {
        return operTime;
    }

    public void setOperTime(Date operTime) {
        this.operTime = operTime;
    }

    public String getDrawState() {
        return drawState;
    }

    public void setDrawState(String drawState) {
        this.drawState = drawState;
    }

    public Date getDrawStartTime() {
        return drawStartTime;
    }

    public void setDrawStartTime(Date drawStartTime) {
        this.drawStartTime = drawStartTime;
    }

    public Date getDrawEndTime() {
        return drawEndTime;
    }

    public void setDrawEndTime(Date drawEndTime) {
        this.drawEndTime = drawEndTime;
    }

    public String getApprovalState() {
        return approvalState;
    }

    public void setApprovalState(String approvalState) {
        this.approvalState = approvalState;
    }

    public Long getApprovalId() {
        return approvalId;
    }

    public void setApprovalId(Long approvalId) {
        this.approvalId = approvalId;
    }

    public Date getApprovalTime() {
        return approvalTime;
    }

    public void setApprovalTime(Date approvalTime) {
        this.approvalTime = approvalTime;
    }
}