/**
 * 
 * This file was generated by Taiping MyBatis Generator(v1.2.12).
 * Date: 2024-05-08 10:55:27
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by Taiping MyBatis Generator(v1.2.12).
 * Create Date: 2024-05-08 10:55:27<br/>
 * Description: ibnr计算-最终轨迹<br/>
 * Table Name: atr_buss_ibnrcalc_final_trace<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "ibnr计算-最终轨迹")
public class AtrBussIbnrcalcFinalTrace implements Serializable {
    /**
     * Database column: atr_buss_ibnrcalc_final_trace.id
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: atr_buss_ibnrcalc_final_trace.action_no
     * Database remarks: action编号
     */
    @ApiModelProperty(value = "action编号", required = true)
    private String actionNo;

    /**
     * Database column: atr_buss_ibnrcalc_final_trace.icp_id
     * Database remarks: 合同组合层ID
     */
    @ApiModelProperty(value = "合同组合层ID", required = true)
    private Long icpId;

    /**
     * Database column: atr_buss_ibnrcalc_final_trace.method_type
     * Database remarks: 算法类型|LR/CL/BF
     */
    @ApiModelProperty(value = "算法类型|LR/CL/BF", required = false)
    private String methodType;

    /**
     * Database column: atr_buss_ibnrcalc_final_trace.display_no
     * Database remarks: 排列序号
     */
    @ApiModelProperty(value = "排列序号", required = false)
    private Integer displayNo;

    /**
     * Database column: atr_buss_ibnrcalc_final_trace.trace_type
     * Database remarks: 轨迹类型|OS - Case Reserve
IBNR - IBNR
ICHE_ORI - Inderect Claim Handling Expense
ICHE - Inderect Claim Handling Expense (用户手工调整)
BSCL - Best estimate claim liabilities
TVD_ORI - Time value discount
TVD - Time value discount  (用户手工调整)
DBCL - Discounted BE claim liabilities
PAD_ORI - PAD
PAD - PAD   (用户手工调整)
CL - Claim Liabilities
IBNR_T - IBNR TOTAL
     */
    @ApiModelProperty(value = "轨迹类型|OS - Case Reserve", required = false)
    private String traceType;

    /**
     * Database column: atr_buss_ibnrcalc_final_trace.factor
     * Database remarks: Factor
     */
    @ApiModelProperty(value = "Factor", required = false)
    private BigDecimal factor;

    /**
     * Database column: atr_buss_ibnrcalc_final_trace.amount
     * Database remarks: Amount
     */
    @ApiModelProperty(value = "Amount", required = false)
    private BigDecimal amount;

    /**
     * Database column: atr_buss_ibnrcalc_final_trace.ibnr
     * Database remarks: IBNR
     */
    @ApiModelProperty(value = "IBNR", required = false)
    private BigDecimal ibnr;

    /**
     * Database column: atr_buss_ibnrcalc_final_trace.os
     * Database remarks: Case Reserve
     */
    @ApiModelProperty(value = "Case Reserve", required = false)
    private BigDecimal os;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public Long getIcpId() {
        return icpId;
    }

    public void setIcpId(Long icpId) {
        this.icpId = icpId;
    }

    public String getMethodType() {
        return methodType;
    }

    public void setMethodType(String methodType) {
        this.methodType = methodType;
    }

    public Integer getDisplayNo() {
        return displayNo;
    }

    public void setDisplayNo(Integer displayNo) {
        this.displayNo = displayNo;
    }

    public String getTraceType() {
        return traceType;
    }

    public void setTraceType(String traceType) {
        this.traceType = traceType;
    }

    public BigDecimal getFactor() {
        return factor;
    }

    public void setFactor(BigDecimal factor) {
        this.factor = factor;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getIbnr() {
        return ibnr;
    }

    public void setIbnr(BigDecimal ibnr) {
        this.ibnr = ibnr;
    }

    public BigDecimal getOs() {
        return os;
    }

    public void setOs(BigDecimal os) {
        this.os = os;
    }
}