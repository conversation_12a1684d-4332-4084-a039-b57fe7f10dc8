package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to;

import com.ss.ifrs.actuarial.util.abp.IgnoreCol;
import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Data;

import java.math.BigDecimal;

/**
 * LRC 比例合约分出底层保单发展期表
 * 对应数据库表: atr_buss_to_lrc_t_ul_dev
 */
@Tab("atr_buss_to_lrc_t_ul_r_dev")
@Data
public class AtrBussToLrcTUlRDev {

    /**
     * 主表ID
     * 对应数据库字段: main_id (BIGINT)
     */
    private Long mainId;

    /**
     * 发展期序号
     * 对应数据库字段: dev_no (INTEGER)
     */
    private Integer devNo;

    /**
     * 已写入已赚保费
     * 对应数据库字段: paid_ed_premium (DECIMAL(21, 4))
     */
    private BigDecimal paidEdPremium;

    /**
     * 已赚保费
     * 对应数据库字段: ed_premium (DECIMAL(21, 4))
     */
    private BigDecimal edPremium;

    /**
     * 分出保费现金流
     * 对应数据库字段: recv_premium (DECIMAL(21, 4))
     */
    private BigDecimal recvPremium;

    /**
     * 已写入已赚手续费
     * 对应数据库字段: paid_net_fee (DECIMAL(21, 8))
     */
    private BigDecimal paidNetFee;

    /**
     * 已赚手续费
     * 对应数据库字段: ed_net_fee (DECIMAL(21, 4))
     */
    private BigDecimal edNetFee;

    /**
     * 分出净额结算手续费现金流
     * 对应数据库字段: net_fee (DECIMAL(21, 4))
     */
    private BigDecimal netFee;


    private BigDecimal edRate;

}