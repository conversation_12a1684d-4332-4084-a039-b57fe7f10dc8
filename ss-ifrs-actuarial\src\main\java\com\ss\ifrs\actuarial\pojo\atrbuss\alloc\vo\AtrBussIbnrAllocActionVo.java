/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2025-03-24 19:27:36
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2025-03-24 19:27:36<br/>
 * Description: 分摊操作表<br/>
 * Table Name: atr_buss_ibnr_alloc_action<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "分摊操作表")
public class AtrBussIbnrAllocActionVo implements Serializable {
    /**
     * Database column: atr_buss_ibnr_alloc_action.id
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: atr_buss_ibnr_alloc_action.action_no
     * Database remarks: 执行编号
     */
    @ApiModelProperty(value = "执行编号", required = true)
    private String actionNo;

    /**
     * Database column: atr_buss_ibnr_alloc_action.entity_id
     * Database remarks: 业务单位ID
     */
    @ApiModelProperty(value = "业务单位ID", required = true)
    private Long entityId;

    /**
     * Database column: atr_buss_ibnr_alloc_action.year_month
     * Database remarks: 业务年月|评估期
     */
    @ApiModelProperty(value = "业务年月|评估期", required = true)
    private String yearMonth;

    /**
     * Database column: atr_buss_ibnr_alloc_action.business_source_code
     * Database remarks: 业务类型|DD-直保&临分分入；FO-临分分出；TI-合约分入；TO-合约分出
     */
    @ApiModelProperty(value = "业务类型|DD-直保&临分分入；FO-临分分出；TI-合约分入；TO-合约分出", required = true)
    private String businessSourceCode;

    /**
     * Database column: atr_buss_ibnr_alloc_action.status
     * Database remarks: 执行状态|R-执行中；E-执行异常；S-执行成功
     */
    @ApiModelProperty(value = "执行状态|R-执行中；E-执行异常；S-执行成功", required = true)
    private String status;

    /**
     * Database column: atr_buss_ibnr_alloc_action.confirm_is
     * Database remarks: 是否确认|1-是、0-否
     */
    @ApiModelProperty(value = "是否确认|1-是、0-否", required = true)
    private String confirmIs;

    /**
     * Database column: atr_buss_ibnr_alloc_action.confirm_user
     * Database remarks: 确认人
     */
    @ApiModelProperty(value = "确认人", required = false)
    private Long confirmUser;

    /**
     * Database column: atr_buss_ibnr_alloc_action.confirm_time
     * Database remarks: 确认时间
     */
    @ApiModelProperty(value = "确认时间", required = false)
    private Date confirmTime;

    /**
     * Database column: atr_buss_ibnr_alloc_action.creator_id
     * Database remarks: 创建人
     */
    @ApiModelProperty(value = "创建人", required = false)
    private Long creatorId;

    /**
     * Database column: atr_buss_ibnr_alloc_action.create_time
     * Database remarks: 创建时间
     */
    @ApiModelProperty(value = "创建时间", required = false)
    private Date createTime;

    /**
     * Database column: atr_buss_ibnr_alloc_action.updator_id
     * Database remarks: 最后修改人
     */
    @ApiModelProperty(value = "最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: atr_buss_ibnr_alloc_action.update_time
     * Database remarks: 最后修改时间
     */
    @ApiModelProperty(value = "最后修改时间", required = false)
    private Date updateTime;

    private String yearMonthStart;
    private String yearMonthEnd;

    private String entityCode;
    private String entityCName;
    private String entityLName;
    private String entityEName;

    private String creatorName;

    private String confirmName;

    private String language;

    private String templateFileName;

    private String logFileName;

    private String targetRouter;

    private String zipName;
    /**
     * 自动任务传输字段：任务模式：A-自动
     */
    private String taskMode;

    private String taskCode;
    /**
     * 自动任务传输字段：重试次数
     */
    private Long retryOrder;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getConfirmIs() {
        return confirmIs;
    }

    public void setConfirmIs(String confirmIs) {
        this.confirmIs = confirmIs;
    }

    public Long getConfirmUser() {
        return confirmUser;
    }

    public void setConfirmUser(Long confirmUser) {
        this.confirmUser = confirmUser;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getEntityCode() {
        return entityCode;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public String getEntityCName() {
        return entityCName;
    }

    public void setEntityCName(String entityCName) {
        this.entityCName = entityCName;
    }

    public String getEntityLName() {
        return entityLName;
    }

    public void setEntityLName(String entityLName) {
        this.entityLName = entityLName;
    }

    public String getEntityEName() {
        return entityEName;
    }

    public void setEntityEName(String entityEName) {
        this.entityEName = entityEName;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getTemplateFileName() {
        return templateFileName;
    }

    public void setTemplateFileName(String templateFileName) {
        this.templateFileName = templateFileName;
    }

    public String getLogFileName() {
        return logFileName;
    }

    public void setLogFileName(String logFileName) {
        this.logFileName = logFileName;
    }

    public String getTargetRouter() {
        return targetRouter;
    }

    public void setTargetRouter(String targetRouter) {
        this.targetRouter = targetRouter;
    }

    public String getZipName() {
        return zipName;
    }

    public void setZipName(String zipName) {
        this.zipName = zipName;
    }

    public String getTaskMode() {
        return taskMode;
    }

    public void setTaskMode(String taskMode) {
        this.taskMode = taskMode;
    }

    public Long getRetryOrder() {
        return retryOrder;
    }

    public void setRetryOrder(Long retryOrder) {
        this.retryOrder = retryOrder;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getYearMonthStart() {
        return yearMonthStart;
    }

    public void setYearMonthStart(String yearMonthStart) {
        this.yearMonthStart = yearMonthStart;
    }

    public String getYearMonthEnd() {
        return yearMonthEnd;
    }

    public void setYearMonthEnd(String yearMonthEnd) {
        this.yearMonthEnd = yearMonthEnd;
    }

    public String getConfirmName() {
        return confirmName;
    }

    public void setConfirmName(String confirmName) {
        this.confirmName = confirmName;
    }
}