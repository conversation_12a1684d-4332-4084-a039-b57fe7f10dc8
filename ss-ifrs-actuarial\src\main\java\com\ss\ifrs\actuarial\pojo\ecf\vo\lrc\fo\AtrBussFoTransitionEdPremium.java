package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.fo;

import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Data;

import java.math.BigDecimal;

/**
 * FO过渡期已赚保费结果表
 * 
 * <AUTHOR>
 */
@Data
@Tab("atr_buss_fo_transition_ed_premium")
public class AtrBussFoTransitionEdPremium {
    
    /**
     * 机构ID
     */
    private Long entityId;
    
    /**
     * 保单号
     */
    private String policyNo;
    
    /**
     * 批单序号
     */
    private String endorseSeqNo;
    
    /**
     * 险别代码
     */
    private String kindCode;
    
    /**
     * 年月
     */
    private String yearMonth;
    
    /**
     * 已赚保费
     */
    private BigDecimal edPremium;
} 