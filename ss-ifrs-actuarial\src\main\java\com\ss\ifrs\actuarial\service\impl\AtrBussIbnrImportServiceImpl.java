package com.ss.ifrs.actuarial.service.impl;

import com.alibaba.excel.exception.ExcelAnalysisException;
import com.ss.ifrs.actuarial.dao.*;
import com.ss.ifrs.actuarial.dao.buss.alloc.AtrBussClaimImportMainDao;
import com.ss.ifrs.actuarial.dao.buss.alloc.AtrBussIbnrImportClaimDao;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussClaimImportMain;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrImportClaim;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussClaimImportMainVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrImportClaimVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.po.*;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussIbnrImportDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussIbnrImportMainVo;
import com.ss.ifrs.actuarial.service.AtrBussIbnrImportService;
import com.ss.ifrs.actuarial.service.AtrImportService;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ClassUtil;
import com.ss.library.utils.DateUtil;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.util.ExcelExportUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AtrBussIbnrImportServiceImpl implements AtrBussIbnrImportService {

    @Autowired
    private AtrBussIbnrImportMainDao atrBussIbnrMainDao;

    @Autowired
    private AtrBussIbnrImportDetailDao atrBussIbnrDetailDao;

    @Autowired
    private AtrBussIbnrImportClaimDao atrBussIbnrImportClaimDao;

    @Autowired
    private AtrBussClaimImportMainDao atrBussClaimImportMainDao;

    @Autowired
    private AtrImportService atrImportService;

    @Override
    public Page<AtrBussIbnrImportMainVo> searchLicPage(AtrBussIbnrImportMainVo atrBussBecfMainVo, Pageable pageParam) {
        Page<AtrBussIbnrImportMainVo> atrBussBecfMainVoPage = atrBussIbnrMainDao.fuzzySearchPage(atrBussBecfMainVo, pageParam);
        return atrBussBecfMainVoPage;
    }

    @Override
    public Page<AtrBussClaimImportMainVo> searchXoClaimPage(AtrBussClaimImportMainVo ibnrImportMainVo, Pageable pageParam) {
        Page<AtrBussClaimImportMainVo> atrBussBecfMainVoPage = atrBussClaimImportMainDao.fuzzySearchPage(ibnrImportMainVo, pageParam);
        return atrBussBecfMainVoPage;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, readOnly = false)
    public String licExcelImport(MultipartFile file, AtrBussIbnrImportMainVo atrBussBecfMainVo, Long userId) throws Exception {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtil.YMD);
        List<AtrBussIbnrImportDetailVo> list = new ArrayList<>();
        try {
            list= ExcelExportUtil.read(file.getInputStream(), AtrBussIbnrImportDetailVo.class);
            if (ObjectUtils.isEmpty(list) || list.size() > atrBussBecfMainVo.getMaxImportLine()) {
                return "2";
            }
        } catch (ExcelAnalysisException e) {
            throw e;
        }

        atrBussBecfMainVo.setCreatorId(userId);
        Map<String, List<AtrBussIbnrImportDetailVo>> ibnrImportListMap = list.stream()
                .collect(Collectors.groupingBy( e-> fetchGroupKy(e), LinkedHashMap::new, Collectors.toList()));
        ibnrImportListMap.forEach((key, value) -> {
            atrBussBecfMainVo.setCreatorId(userId);
            atrBussBecfMainVo.setCreateTime(new Date());
           // atrBussBecfMainVo.setValuationDate(value.get(0).getAssessmentDate());
            atrBussBecfMainVo.setYearMonth(key);
            atrBussBecfMainVo.setConfirmIs("0");
            atrBussBecfMainVo.setVersionNo(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            AtrBussIbnrImportMain po = ClassUtil.convert(atrBussBecfMainVo, AtrBussIbnrImportMain.class);
            atrBussIbnrMainDao.save(po);
            this.saveIbnrDetailData(po.getIbnrMainId(), ClassUtil.convert(value,AtrBussIbnrImportDetail.class));
        });
        atrImportService.importFile(file,atrBussBecfMainVo.getTargetRouter(),userId);
        return "1";
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, readOnly = false)
    public Boolean licDataConfirm(AtrBussIbnrImportMainVo atrBussBecfMainVo, Long userId) {
        AtrBussIbnrImportMain po = atrBussIbnrMainDao.findById(atrBussBecfMainVo.getIbnrMainId());
        if (ObjectUtils.isNotEmpty(po)) {
            Integer count = atrBussIbnrMainDao.countConfirm(po);
            if (count > 0 ) {
                return false;
            }
            po.setConfirmIs("1");
            po.setConfirmId(userId);
            po.setConfirmTime(new Date());
            po.setUpdatorId(userId);
            po.setUpdateTime(new Date());
            atrBussIbnrMainDao.updateById(po);
        }
        return true;
    }

    @Override
    public List<AtrBussIbnrImportMainVo> findIbnrList(AtrBussIbnrImportMainVo atrBussBecfMainVo) {
        List<AtrBussIbnrImportMain> atrBussBecfMainList = atrBussIbnrMainDao.findList(ClassUtil.convert(atrBussBecfMainVo, AtrBussIbnrImportMain.class));
        return ClassUtil.convert(atrBussBecfMainList, AtrBussIbnrImportMainVo.class);
    }

    @Override
    public AtrBussIbnrImportMainVo findById(Long ibnrMainId) {
        AtrBussIbnrImportMainVo atrBussBecfMainVo = atrBussIbnrMainDao.findByMainId(ibnrMainId);
        if (ObjectUtils.isNotEmpty(atrBussBecfMainVo)) {
            AtrBussIbnrImportDetail atrBussBecfDetail = new AtrBussIbnrImportDetail();
            atrBussBecfDetail.setIbnrMainId(ibnrMainId);
            atrBussBecfDetail.setBusinessModel("TI");
            List<AtrBussIbnrImportDetail> atrTIBussBecfDetailList = atrBussIbnrDetailDao.findList(atrBussBecfDetail);
            atrBussBecfMainVo.setIbnrTiImportDetailVoList(ClassUtil.convert(atrTIBussBecfDetailList, AtrBussIbnrImportDetailVo.class));

            List<AtrBussIbnrImportDetail> atrBussBecfDetailList = atrBussIbnrDetailDao.findIbnrDetailList(ibnrMainId);
            atrBussBecfMainVo.setIbnrImportDetailVoList(ClassUtil.convert(atrBussBecfDetailList, AtrBussIbnrImportDetailVo.class));

            AtrBussIbnrImportClaim atrBussIbnrImportCalim = new AtrBussIbnrImportClaim();
            atrBussIbnrImportCalim.setClaimMainId(ibnrMainId);
            List<AtrBussIbnrImportClaim> atrBussIbnrImportCalimList = atrBussIbnrImportClaimDao.findList(atrBussIbnrImportCalim);
            atrBussBecfMainVo.setIbnrImportCalimVoList(ClassUtil.convert(atrBussIbnrImportCalimList, AtrBussIbnrImportClaimVo.class));
        }
        return atrBussBecfMainVo;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, readOnly = false)
    public AtrBussIbnrImportMainVo deleteIbnrByBecfId(Long ibnrMainId) {
        AtrBussIbnrImportMainVo atrBussBecfMainVo = atrBussIbnrMainDao.findByMainId(ibnrMainId);
        if (ObjectUtils.isEmpty(atrBussBecfMainVo) || "1".equals(atrBussBecfMainVo.getConfirmIs())) {
            return atrBussBecfMainVo;
        }
        HashMap map = new HashMap();
        map.put("ibnrMainId", ibnrMainId);
        atrBussIbnrDetailDao.deleteByMap(map);
        atrBussIbnrMainDao.deleteById(ibnrMainId);
        return atrBussBecfMainVo;
    }

    @Override
    public void ibnrExcelImport(MultipartFile file, AtrBussIbnrImportMainVo ibnrImportMainVo, Long userId) throws Exception {
        List<AtrBussIbnrImportDetailVo> ddBussIbnrImportDetailList  = new ArrayList<>();
        List<AtrBussIbnrImportDetailVo> tiBussIbnrImportDetailList  = new ArrayList<>();
        List<AtrBussIbnrImportDetailVo> outwardBussIbnrImportDetailList  = new ArrayList<>();
        List<AtrBussIbnrImportDetailVo> outwardXBussIbnrImportDetailList  = new ArrayList<>();

        try {
            ddBussIbnrImportDetailList= ExcelExportUtil.read(file.getInputStream(), AtrBussIbnrImportDetailVo.class,1);
            tiBussIbnrImportDetailList= ExcelExportUtil.read(file.getInputStream(), AtrBussIbnrImportDetailVo.class, 2);
            outwardBussIbnrImportDetailList= ExcelExportUtil.read(file.getInputStream(), AtrBussIbnrImportDetailVo.class, 3 );
            outwardXBussIbnrImportDetailList= ExcelExportUtil.read(file.getInputStream(), AtrBussIbnrImportDetailVo.class, 4);

        } catch (ExcelAnalysisException e) {
            throw e;
        }
        ibnrImportMainVo.setCreatorId(userId);
        ibnrImportMainVo.setCreateTime(new Date());
        ibnrImportMainVo.setConfirmIs("0");
        ibnrImportMainVo.setVersionNo(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        AtrBussIbnrImportMain po = ClassUtil.convert(ibnrImportMainVo, AtrBussIbnrImportMain.class);
        atrBussIbnrMainDao.save(po);
        this.saveIbnrDetailData(po.getIbnrMainId(), ClassUtil.convert(ddBussIbnrImportDetailList, AtrBussIbnrImportDetail.class));
        this.saveIbnrDetailData(po.getIbnrMainId(), ClassUtil.convert(tiBussIbnrImportDetailList, AtrBussIbnrImportDetail.class));
        this.saveIbnrDetailData(po.getIbnrMainId(), ClassUtil.convert(outwardBussIbnrImportDetailList, AtrBussIbnrImportDetail.class));
        this.saveIbnrDetailData(po.getIbnrMainId(), ClassUtil.convert(outwardXBussIbnrImportDetailList, AtrBussIbnrImportDetail.class));
    }

    @Override
    public AtrBussIbnrImportMainVo findByVo(AtrBussIbnrImportMainVo ibnrImportMainVo) {
        return atrBussIbnrMainDao.findByVo(ibnrImportMainVo);
    }

    @Override
    public void claimExcelImport(MultipartFile file, AtrBussIbnrImportMainVo ibnrImportMainVo, Long userId) throws Exception {
        List<AtrBussIbnrImportClaim> outwardXClaimList;
        try {
            outwardXClaimList= ExcelExportUtil.read(file.getInputStream(), AtrBussIbnrImportClaim.class, 1);
        } catch (ExcelAnalysisException e) {
            throw e;
        }
        ibnrImportMainVo.setCreatorId(userId);
        ibnrImportMainVo.setCreateTime(new Date());
        ibnrImportMainVo.setConfirmIs("0");
        ibnrImportMainVo.setVersionNo(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        AtrBussClaimImportMain po = ClassUtil.convert(ibnrImportMainVo, AtrBussClaimImportMain.class);
        atrBussClaimImportMainDao.save(po);
        this.saveIbnrClaimData(po.getClaimMainId(), outwardXClaimList);
    }

    @Override
    public Boolean hasIbnrData(AtrBussIbnrImportMainVo ibnrImportMainVo) {
        AtrBussIbnrImportMain atrBussIbnrImportMain = new AtrBussIbnrImportMain();
        atrBussIbnrImportMain.setEntityId(ibnrImportMainVo.getEntityId());
        atrBussIbnrImportMain.setYearMonth(ibnrImportMainVo.getYearMonth());
        atrBussIbnrImportMain.setConfirmIs(CommonConstant.VersionStatus.CONFIRMED);
        Long count = atrBussIbnrMainDao.count(atrBussIbnrImportMain);
        return ObjectUtils.isNotEmpty(count) && count >0;
    }

    private String fetchGroupKy(AtrBussIbnrImportDetailVo bussIbnrImportDetailVo) {
        return null;
    }

    @Transactional(propagation = Propagation.REQUIRED, readOnly = false)
    public void saveIbnrDetailData(Long ibnrMainId, List<AtrBussIbnrImportDetail> ibnrImportDetailList) {
        if (CollectionUtils.isEmpty(ibnrImportDetailList)) {
            return;
        }
        int spitLen = 500;
        int queueSize = ibnrImportDetailList.size() <= spitLen ? 1 : (int) Math.ceil(ibnrImportDetailList.size() / spitLen) + 1;
        List handleList = null;
        for (int i = 0; i < queueSize; i++) {
            if ((i + 1) == queueSize) {
                int startIndex = i * spitLen;
                int endIndex = ibnrImportDetailList.size();
                handleList = ibnrImportDetailList.subList(startIndex, endIndex);
            } else {
                int startIndex = i * spitLen;
                int endIndex = (i + 1) * spitLen;
                handleList = ibnrImportDetailList.subList(startIndex, endIndex);
            }
            //确保 保存的handleList中有数据
            if (CollectionUtils.isNotEmpty(handleList)) {
                atrBussIbnrDetailDao.saveIbnrDetailList(ibnrMainId, handleList);
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, readOnly = false)
    public void saveIbnrClaimData(Long ibnrMainId, List<AtrBussIbnrImportClaim> ibnrImportCalims) {
        if (CollectionUtils.isEmpty(ibnrImportCalims)) {
            return;
        }
        int spitLen = 500;
        int queueSize = ibnrImportCalims.size() <= spitLen ? 1 : (int) Math.ceil(ibnrImportCalims.size() / spitLen) + 1;
        List handleList = null;
        for (int i = 0; i < queueSize; i++) {
            if ((i + 1) == queueSize) {
                int startIndex = i * spitLen;
                int endIndex = ibnrImportCalims.size();
                handleList = ibnrImportCalims.subList(startIndex, endIndex);
            } else {
                int startIndex = i * spitLen;
                int endIndex = (i + 1) * spitLen;
                handleList = ibnrImportCalims.subList(startIndex, endIndex);
            }
            //确保 保存的handleList中有数据
            if (CollectionUtils.isNotEmpty(handleList)) {
                atrBussIbnrImportClaimDao.saveIbnrDetailList(ibnrMainId, handleList);
            }
        }
    }
}
