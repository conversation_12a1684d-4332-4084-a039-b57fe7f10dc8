package com.ss.ifrs.actuarial.feign;

import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.conf.FeignAuthConfig;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.pojo.bbs.vo.BbsConfLoaVo;
import com.ss.platform.pojo.bbs.vo.BbsConfRegularRuleVo;
import com.ss.platform.pojo.com.po.ConfCode;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @ClassName BmsConfCodeFeignClient
 * @Description TODO 调用基础数据信息的接口
 * <AUTHOR>
 * @Date 2022/03/17
 **/
@FeignClient(name = "SS-PLATFORM-COMMON", configuration = { FeignAuthConfig.class })
public interface BmsConfCodeFeignClient {

    @RequestMapping(method = RequestMethod.GET, value = "/gg_code/find_code/by_code_type/{upperCodeCode}")
    @PermissionRequest(required = false)
    List<ConfCode> findCodeListByCodeType(@PathVariable("upperCodeCode") String upperCodeCode);

    @RequestMapping(method = RequestMethod.GET, value = "/regular_rule/find_by_pk/{ruleId}")
    BaseResponse<BbsConfRegularRuleVo> findRegularRuleById(@PathVariable("ruleId") Long ruleId);

    @RequestMapping(method = RequestMethod.POST, value = "/conf_loa/find_loa_vo")
    @PermissionRequest(required = false)
    BbsConfLoaVo findLoaVo(@RequestBody BbsConfLoaVo bbsConfLoaVo);

    @RequestMapping(value = "/gg_code/find_code/by_code_type", method = RequestMethod.GET)
    List<ConfCode> findCodeByCodeType(@RequestParam("codeType") String codeType);

    @RequestMapping(value = "/gg_code/find_code_by_codeIdx", method = RequestMethod.GET)
    List<String> findByCodeIdx(@RequestParam("codeCodeIdx") String codeCodeIdx);

    @RequestMapping(method = RequestMethod.POST, value = "/gg_code/find_taskcode")
    BaseResponse<Map<String, Object>> findTaskCode(@RequestBody Map<String, Object> paramMap);

}
