create or replace package exp_pack_buss_period is

  -- Author  : ZOPT
  -- Created : 2022/5/9 18:33:01
  -- Purpose :


FUNCTION func_period_detail(p_entity_id NUMBER, p_year_month VARCHAR2, p_state VARCHAR2)
  RETURN BOOLEAN;

PROCEDURE proc_period_execution(p_entity_id NUMBER, p_state VARCHAR2);

end exp_pack_buss_period;
/
create or replace package body exp_pack_buss_period is


FUNCTION func_period_detail(p_entity_id NUMBER, p_year_month VARCHAR2, p_state VARCHAR2)
  RETURN BOOLEAN IS

  v_count NUMBER;
BEGIN

  --新增业务期间明细
  IF p_state = '0' THEN

    INSERT INTO exp_conf_bussperiod_detail
      (period_detail_id,
       buss_period_id,
       biz_type_id,
       system_code,
       direction,
       ready_state,
       creator_id,
       create_time)
    SELECT exp_seq_conf_bussperiod_detail.NEXTVAL,
           bp.buss_period_id,
           ct.biz_type_id,
           system_code,
           direction, --输入
           '0' ready_state,
           bp.creator_id,
           sysdate create_time
    FROM exp_conf_table ct
    LEFT JOIN exp_conf_bussperiod bp
      ON bp.entity_id = p_entity_id
     AND bp.year_month = p_year_month
    WHERE ct.valid_is = '1'
    ;
    RETURN true;

  --业务期间己准备，所有输入数据己准备
  ELSIF p_state = '1' THEN
    SELECT COUNT(period_detail_id) INTO v_count
      FROM exp_conf_bussperiod_detail bpd
      LEFT JOIN exp_conf_bussperiod bp
        ON bp.buss_period_id = bpd.buss_period_id
     WHERE bpd.direction = '1'
       AND bpd.ready_state = '0'
       AND bp.entity_id = p_entity_id
       AND bp.year_month = p_year_month;

    --无待准备数据
    IF v_count = 0 THEN
      RETURN true;
    ELSE
      RETURN false;
    END IF;

  --业务期间处理中，所有输入数据己准备
  ELSIF p_state = '2' THEN
    SELECT COUNT(period_detail_id) INTO v_count
      FROM exp_conf_bussperiod_detail bpd
      LEFT JOIN exp_conf_bussperiod bp
        ON bp.buss_period_id = bpd.buss_period_id
     WHERE bpd.direction = '1'
       AND bpd.ready_state = '0'
       AND bp.entity_id = p_entity_id
       AND bp.year_month = p_year_month;

    --无待准备数据
    IF v_count = 0 THEN
      RETURN true;
    ELSE
      RETURN false;
    END IF;

  --业务期间已完成，所有输出数据己准备
  ELSIF p_state = '3' THEN
    SELECT COUNT(period_detail_id) INTO v_count
      FROM exp_conf_bussperiod_detail bpd
      LEFT JOIN exp_conf_bussperiod bp
        ON bp.buss_period_id = bpd.buss_period_id
     WHERE bpd.direction = '0'
       AND bpd.ready_state = '0'
       AND bp.entity_id = p_entity_id
       AND bp.year_month = p_year_month;

    --无待准备数据
    IF v_count = 0 THEN
      RETURN true;
    ELSE
      RETURN false;
    END IF;
  END IF;

  RETURN false;

EXCEPTION
  WHEN others THEN
DBMS_OUTPUT.PUT_LINE('(' || SQLERRM||')');

  RETURN false;
END func_period_detail;




PROCEDURE proc_period_execution(p_entity_id NUMBER, p_state VARCHAR2)
 IS

  v_year_month VARCHAR2(20);
  v_next_year_month VARCHAR2(20);
  v_detail_reday_state BOOLEAN;
 BEGIN

  --新增业务期间
  IF p_state = '0' THEN
    --检验是否存在准备中的业务期间(有且仅有一个在准备中的业务期间)
    SELECT min(year_month) INTO v_year_month
      FROM exp_conf_bussperiod
     WHERE entity_id = p_entity_id
       AND execution_state = '0';

    IF v_year_month IS NULL THEN
      --增加一个新的业务期间
      SELECT  TO_CHAR(ADD_MONTHS(to_date( year_month, 'YYYYMM' ),+1),'YYYYMM') INTO v_next_year_month
        FROM exp_conf_bussperiod
       WHERE entity_id = p_entity_id
       and rownum=1
       ORDER BY year_month DESC
      ;

      --初始数据按当前时间处理
      IF v_next_year_month IS NULL THEN
        v_next_year_month := to_char(sysdate,'YYYYMM');
      END IF;

      INSERT INTO exp_conf_bussperiod
        (buss_period_id,
         entity_id,
         year_month,
         execution_state,
         valid_is,
         creator_id,
         create_time
        )
      VALUES
       (exp_seq_conf_bussperiod.NEXTVAL,
        p_entity_id,
        v_next_year_month,
        '0',
        '1',
        '1',
        sysdate
        );

      --TODO 增加准备中的输入输出明细数据
      v_detail_reday_state := exp_pack_buss_period.func_period_detail(p_entity_id, v_next_year_month, p_state);

    END IF;
  --业务期间己准备
  ELSIF p_state = '1' THEN
    --检验是否存在准备中业务期间
    SELECT min(year_month) INTO v_year_month
      FROM exp_conf_bussperiod
     WHERE entity_id = p_entity_id
       AND execution_state = '0'
     ORDER BY year_month;

    IF v_year_month IS NOT NULL THEN

      --验证输入明细数据状态是否己准备
      v_detail_reday_state := func_period_detail(p_entity_id, v_year_month, p_state);
      IF v_detail_reday_state THEN
        --修改当前业务期间状态为己准备
        UPDATE exp_conf_bussperiod
          SET execution_state = '1',
              updator_id = 1,
              update_time = sysdate
        WHERE entity_id = p_entity_id
          AND year_month = v_year_month;

        --增加下个准备中业务期间
        exp_pack_buss_period.proc_period_execution(p_entity_id, '0');

      END IF;
    END IF;

    --确保有下个处理中业务期间
     exp_pack_buss_period.proc_period_execution(p_entity_id, '2');

  --业务期间处理中
  ELSIF p_state = '2' THEN
    --检查在处理中的业务期间
    SELECT min(year_month) INTO v_year_month
    FROM exp_conf_bussperiod
    WHERE entity_id = p_entity_id
      AND execution_state = '2'
    ORDER BY year_month;

    --确保无在处理中的业务期间
    IF v_year_month IS NULL THEN
      SELECT year_month
        INTO v_next_year_month from (select *
                                  FROM exp_conf_bussperiod
                                 WHERE entity_id = p_entity_id
                                   AND execution_state = '1'
                                 ORDER BY year_month asc)
       where rownum = 1;

      --存在下一个己准备的业务期间,开始处理
      IF v_next_year_month IS NOT NULL THEN

        --验证输出明细数据状态是否己准备完成，验证成功后执行以下逻辑
        v_detail_reday_state := func_period_detail(p_entity_id, v_next_year_month, p_state);
        IF v_detail_reday_state THEN

          --修改当前业务期间状态为处理中
          UPDATE exp_conf_bussperiod
            SET execution_state = '2',
                updator_id = 1,
                update_time = sysdate
          WHERE entity_id = p_entity_id
            AND year_month = v_next_year_month;
        END IF;
      END IF;
    END IF;

  --业务期间已完成
  ELSIF p_state = '3' THEN
    SELECT min(year_month) INTO v_year_month
    FROM exp_conf_bussperiod
    WHERE entity_id = p_entity_id
      AND execution_state = '2'
      and rownum=1
    ORDER BY year_month ;

    IF v_year_month IS NOT NULL THEN

      --验证输出明细数据状态是否己完成，验证成功后执行以下逻辑
      v_detail_reday_state := func_period_detail(p_entity_id, v_year_month, p_state);
      IF v_detail_reday_state THEN

        --修改当前业务期间状态为已完成
        UPDATE exp_conf_bussperiod
          SET execution_state = '3',
              updator_id = 1,
              update_time = sysdate
        WHERE entity_id = p_entity_id
          AND year_month = v_year_month;

        --下一个业务期间
        v_next_year_month := TO_CHAR(ADD_MONTHS(to_date( v_year_month, 'YYYYMM' ),+1),'YYYYMM');


        --检验下一个业务期间是否存在
        SELECT MIN(year_month) INTO v_year_month
        FROM exp_conf_bussperiod
        WHERE entity_id = p_entity_id
          AND year_month = v_next_year_month;

        IF v_year_month IS NULL THEN
          --增加下个准备中业务期间
          exp_pack_buss_period.proc_period_execution(p_entity_id, '0');
        ELSE
          --修改下个业务期间状态为处理中
          exp_pack_buss_period.proc_period_execution(p_entity_id, '2');
        END IF;
      END IF;
    END IF;
  END IF;

EXCEPTION
  WHEN others THEN
  --ROLLBACK;
  DBMS_OUTPUT.PUT_LINE('(' || SQLERRM||')');

END proc_period_execution;


end exp_pack_buss_period;
/
