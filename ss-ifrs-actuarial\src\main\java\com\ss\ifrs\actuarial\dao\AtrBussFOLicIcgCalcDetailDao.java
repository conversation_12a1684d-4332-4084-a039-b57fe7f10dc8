/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-03-02 18:17:47
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao;


import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussFOLicIcgCalcDetail;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussFOLicIcgCalcDetailVo;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-03-02 18:17:47<br/>
 * Description: LIC 计算结果明细(合同组维度，临分分出) Dao类<br/>
 * Related Table Name: ATR_BUSS_FO_LIC_ICG_CALC_DETAIL<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrBussFOLicIcgCalcDetailDao extends IDao<AtrBussFOLicIcgCalcDetail, Long> {

    List<AtrBussFOLicIcgCalcDetailVo> findByVo(AtrDapDrawVo atrDapDrawVo);

    List<AtrBussFOLicIcgCalcDetailVo> findByMainId(Long mainId);
}