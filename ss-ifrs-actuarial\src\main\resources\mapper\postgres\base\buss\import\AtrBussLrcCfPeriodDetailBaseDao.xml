<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-01-11 16:41:01 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussLrcCfPeriodDetailDao">
  <!-- 本文件由GIS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfPeriodDetail">
    <id column="LRC_CF_PERIOD_DETAIL_ID" property="lrcCfPeriodDetailId" jdbcType="DECIMAL" />
    <result column="LRC_CF_PERIOD_ID" property="lrcCfPeriodId" jdbcType="DECIMAL" />
    <result column="LRC_CF_FEE_TYPE" property="lrcCfFeeType" jdbcType="VARCHAR" />
    <result column="DEV_PERIOD" property="devPeriod" jdbcType="DECIMAL" />
    <result column="LRC_CF_AMOUNT" property="lrcCfAmount" jdbcType="DECIMAL" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    LRC_CF_PERIOD_DETAIL_ID, LRC_CF_PERIOD_ID, LRC_CF_FEE_TYPE, DEV_PERIOD, LRC_CF_AMOUNT
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="lrcCfPeriodDetailId != null ">
          and LRC_CF_PERIOD_DETAIL_ID = #{lrcCfPeriodDetailId,jdbcType=DECIMAL}
      </if>
      <if test="lrcCfPeriodId != null ">
          and LRC_CF_PERIOD_ID = #{lrcCfPeriodId,jdbcType=DECIMAL}
      </if>
      <if test="lrcCfFeeType != null and lrcCfFeeType != ''">
          and LRC_CF_FEE_TYPE = #{lrcCfFeeType,jdbcType=VARCHAR}
      </if>
      <if test="devPeriod != null ">
          and DEV_PERIOD = #{devPeriod,jdbcType=DECIMAL}
      </if>
      <if test="lrcCfAmount != null ">
          and LRC_CF_AMOUNT = #{lrcCfAmount,jdbcType=DECIMAL}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.lrcCfPeriodDetailId != null ">
          and LRC_CF_PERIOD_DETAIL_ID = #{condition.lrcCfPeriodDetailId,jdbcType=DECIMAL}
      </if>
      <if test="condition.lrcCfPeriodId != null ">
          and LRC_CF_PERIOD_ID = #{condition.lrcCfPeriodId,jdbcType=DECIMAL}
      </if>
      <if test="condition.lrcCfFeeType != null and condition.lrcCfFeeType != ''">
          and LRC_CF_FEE_TYPE = #{condition.lrcCfFeeType,jdbcType=VARCHAR}
      </if>
      <if test="condition.devPeriod != null ">
          and DEV_PERIOD = #{condition.devPeriod,jdbcType=DECIMAL}
      </if>
      <if test="condition.lrcCfAmount != null ">
          and LRC_CF_AMOUNT = #{condition.lrcCfAmount,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="lrcCfPeriodDetailId != null ">
          and LRC_CF_PERIOD_DETAIL_ID = #{lrcCfPeriodDetailId,jdbcType=DECIMAL}
      </if>
      <if test="lrcCfPeriodId != null ">
          and LRC_CF_PERIOD_ID = #{lrcCfPeriodId,jdbcType=DECIMAL}
      </if>
      <if test="lrcCfFeeType != null and lrcCfFeeType != ''">
          and LRC_CF_FEE_TYPE = #{lrcCfFeeType,jdbcType=VARCHAR}
      </if>
      <if test="devPeriod != null ">
          and DEV_PERIOD = #{devPeriod,jdbcType=DECIMAL}
      </if>
      <if test="lrcCfAmount != null ">
          and LRC_CF_AMOUNT = #{lrcCfAmount,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_LRC_CF_PERIOD_DETAIL
    where LRC_CF_PERIOD_DETAIL_ID = #{lrcCfPeriodDetailId,jdbcType=DECIMAL}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_LRC_CF_PERIOD_DETAIL
    where LRC_CF_PERIOD_DETAIL_ID in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_LRC_CF_PERIOD_DETAIL
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfPeriodDetail">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_LRC_CF_PERIOD_DETAIL
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_LRC_CF_PERIOD_DETAIL
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="LRC_CF_PERIOD_DETAIL_ID" keyProperty="lrcCfPeriodDetailId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfPeriodDetail">
    <selectKey resultType="long" keyProperty="lrcCfPeriodDetailId" order="BEFORE">
      select nextval('ATR_SEQ_BUSS_LRC_CF_PERIOD_DTL') as sequenceNo 
    </selectKey>
    insert into ATR_BUSS_LRC_CF_PERIOD_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="lrcCfPeriodDetailId != null">
        LRC_CF_PERIOD_DETAIL_ID,
      </if>
      <if test="lrcCfPeriodId != null">
        LRC_CF_PERIOD_ID,
      </if>
      <if test="lrcCfFeeType != null">
        LRC_CF_FEE_TYPE,
      </if>
      <if test="devPeriod != null">
        DEV_PERIOD,
      </if>
      <if test="lrcCfAmount != null">
        LRC_CF_AMOUNT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="lrcCfPeriodDetailId != null">
        #{lrcCfPeriodDetailId,jdbcType=DECIMAL},
      </if>
      <if test="lrcCfPeriodId != null">
        #{lrcCfPeriodId,jdbcType=DECIMAL},
      </if>
      <if test="lrcCfFeeType != null">
        #{lrcCfFeeType,jdbcType=VARCHAR},
      </if>
      <if test="devPeriod != null">
        #{devPeriod,jdbcType=DECIMAL},
      </if>
      <if test="lrcCfAmount != null">
        #{lrcCfAmount,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all 
    <foreach collection="list" item="item" index="index">
      into ATR_BUSS_LRC_CF_PERIOD_DETAIL values 
       (#{item.lrcCfPeriodDetailId,jdbcType=DECIMAL}, 
        #{item.lrcCfPeriodId,jdbcType=DECIMAL}, #{item.lrcCfFeeType,jdbcType=VARCHAR}, 
        #{item.devPeriod,jdbcType=DECIMAL}, #{item.lrcCfAmount,jdbcType=DECIMAL})
    </foreach>
    select 1 
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfPeriodDetail">
    update ATR_BUSS_LRC_CF_PERIOD_DETAIL
    <set>
      <if test="lrcCfPeriodId != null">
        LRC_CF_PERIOD_ID = #{lrcCfPeriodId,jdbcType=DECIMAL},
      </if>
      <if test="lrcCfFeeType != null">
        LRC_CF_FEE_TYPE = #{lrcCfFeeType,jdbcType=VARCHAR},
      </if>
      <if test="devPeriod != null">
        DEV_PERIOD = #{devPeriod,jdbcType=DECIMAL},
      </if>
      <if test="lrcCfAmount != null">
        LRC_CF_AMOUNT = #{lrcCfAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where LRC_CF_PERIOD_DETAIL_ID = #{lrcCfPeriodDetailId,jdbcType=DECIMAL}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfPeriodDetail">
    update ATR_BUSS_LRC_CF_PERIOD_DETAIL
    <set>
      <if test="record.lrcCfPeriodId != null">
        LRC_CF_PERIOD_ID = #{record.lrcCfPeriodId,jdbcType=DECIMAL},
      </if>
      <if test="record.lrcCfFeeType != null">
        LRC_CF_FEE_TYPE = #{record.lrcCfFeeType,jdbcType=VARCHAR},
      </if>
      <if test="record.devPeriod != null">
        DEV_PERIOD = #{record.devPeriod,jdbcType=DECIMAL},
      </if>
      <if test="record.lrcCfAmount != null">
        LRC_CF_AMOUNT = #{record.lrcCfAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from ATR_BUSS_LRC_CF_PERIOD_DETAIL
    where LRC_CF_PERIOD_DETAIL_ID = #{lrcCfPeriodDetailId,jdbcType=DECIMAL}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from ATR_BUSS_LRC_CF_PERIOD_DETAIL
    where LRC_CF_PERIOD_DETAIL_ID in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from ATR_BUSS_LRC_CF_PERIOD_DETAIL
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfPeriodDetail">
    select count(1) from ATR_BUSS_LRC_CF_PERIOD_DETAIL
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>