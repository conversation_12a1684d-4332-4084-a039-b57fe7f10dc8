package com.ss.ifrs.actuarial.api;

import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussBecfDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussBecfMainVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLrcCfMainVo;
import com.ss.ifrs.actuarial.service.AtrBussDataImportService;
import com.ss.ifrs.actuarial.service.AtrExportService;
import com.ss.platform.core.annotation.TrackUserBehavioral;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ExceptionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

@RestController
@RequestMapping("/import")
@Api(value = "导入接口")
public class AtrBussDataImportApi extends BaseApi {

    @Autowired
    AtrBussDataImportService atrBussBecfService;

    @Autowired
    AtrExportService atrExportService;


    @ApiOperation(value = "下载赔付现金流上传模板")
    @RequestMapping(value = "/becf/downTemplate", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public void downBeCfLicTemplate(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrBussBecfMainVo atrBussBecfMainVo) {
        try {
            Long userId = this.loginUserId(request);
            atrExportService.downloadTemplateExcel(request, response, atrBussBecfMainVo.getTemplateFileName(), null, userId);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
        }
    }

    /**
     *  becf模块
     * */
    @ApiOperation(value = "赔付现金流查询")
    @TrackUserBehavioral(description = "enquiry CashFlow")
    @RequestMapping(value = "/becf/enquiry", method = RequestMethod.POST)
    public BaseResponse<Map<String, Object>> enquiryBecfPage(@RequestBody AtrBussBecfMainVo atrBussBecfMainVo, int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrBussBecfMainVo> atrConfInterestRateVoList = atrBussBecfService.searchLicPage(atrBussBecfMainVo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("bussBecfMainVoList", atrConfInterestRateVoList);
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "导出现金流模板")
    @RequestMapping(value = "/becf/export_template", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public void exporttLicTemplate(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrBussBecfMainVo atrBussBecfMainVo) throws Exception  {
        Long userId = this.loginUserId(request);
        atrExportService.exportList(request, response,  new ArrayList<>(), AtrBussBecfDetailVo.class,"df", atrBussBecfMainVo.getTemplateFileName(), atrBussBecfMainVo.getTargetRouter(), userId);
    }

    @ApiOperation(value = "赔付现金流导入")
    @RequestMapping(value = "/becf/import", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> becfImport(HttpServletRequest request, @RequestParam(value = "file", required = false) MultipartFile file,
                                             AtrBussBecfMainVo atrBussEvaluateMainVo)  {
        Long userId = this.loginUserId(request);
        String result = "0";
        String exceptionMsg = null;
        try{
            atrBussBecfService.licExcelImport(file, atrBussEvaluateMainVo, userId);
            result = "1";
        } catch (Exception e) {
            exceptionMsg = ExceptionUtil.getMessage(e);
            logger.error(e.getLocalizedMessage(), e);
        }
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, exceptionMsg, result);
    }

    @ApiOperation(value = "赔付现金流确认")
    @RequestMapping(value = "/becf/confirm", method = RequestMethod.POST)
    public BaseResponse<Object> confirm(@RequestBody AtrBussBecfMainVo atrBussEvaluateMainVo, HttpServletRequest request) {
        Long userId = this.loginUserId(request);
        try {
            atrBussBecfService.licDataConfirm(atrBussEvaluateMainVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "赔付现金流是否已确认")
    @RequestMapping(value = "/becf/check_confirm", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> checkPK(@RequestBody AtrBussBecfMainVo atrBussBecfMainVo) {
        String result = "0";
        try {
            List<AtrBussBecfMainVo> vos = atrBussBecfService.findLicList(atrBussBecfMainVo);
            if(vos.size()>0){
                result = "1";
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, result);
        }
    }

    @ApiOperation(value = "根据id查询赔付现金流信息")
    @RequestMapping(value = "/becf/find_by_id/{becfMainId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<AtrBussBecfMainVo> findById(@PathVariable("becfMainId") Long becfMainId) {
        AtrBussBecfMainVo atrBussBecfMainVo = atrBussBecfService.findLicById(becfMainId);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, atrBussBecfMainVo);
    }

    @ApiOperation(value = "根据id删除赔付现金流信息")
    @RequestMapping(value = "/becf/delete_by_pk/{becfMainId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<Object> deleteById(@PathVariable("becfMainId") Long becfMainId) {
        AtrBussBecfMainVo atrBussBecfMainVo = atrBussBecfService.deleteLicByBecfId(becfMainId);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, atrBussBecfMainVo);
    }



    /*
    *LRC保费
    * */
    @ApiOperation(value = "下载保费现金流上传模板")
    @RequestMapping(value = "/lrc/downTemplate", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public void downBeCfLrcTemplate(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrBussBecfMainVo atrBussBecfMainVo) {
        try {
            Long userId = this.loginUserId(request);
            atrExportService.downloadTemplateExcel(request, response, atrBussBecfMainVo.getTemplateFileName(), null, userId);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
        }
    }

    @ApiOperation(value = "Lrc保费现金流查询")
    @TrackUserBehavioral(description = "enquiry CashFlow")
    @RequestMapping(value = "/lrc/enquiry", method = RequestMethod.POST)
    public BaseResponse<Map<String, Object>> enquiryLrcCashPage(@RequestBody AtrBussLrcCfMainVo atrBussLrcCfMainVo, int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrBussLrcCfMainVo> atrBussLrcCfMainVoPage = atrBussBecfService.searchLrcPage(atrBussLrcCfMainVo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("bussLrcCfMainVoList", atrBussLrcCfMainVoPage);
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "保费现金流导入")
    @RequestMapping(value = "/lrc/import", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> becfImport(HttpServletRequest request, @RequestParam(value = "file", required = false) MultipartFile file,
                                           AtrBussLrcCfMainVo atrBussLrcCfMainVo)  {
        Long userId = this.loginUserId(request);
        String result = "0";
        String exceptionMsg = null;
        try{
            atrBussBecfService.lrcExcelImport(file, atrBussLrcCfMainVo, userId);
            result = "1";
        } catch (Exception e) {
            exceptionMsg = ExceptionUtil.getMessage(e);
            logger.error(e.getLocalizedMessage(), e);
        }
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, exceptionMsg, result);
    }

    @ApiOperation(value = "赔付现金流确认")
    @RequestMapping(value = "/lrc/confirm", method = RequestMethod.POST)
    public BaseResponse<Object> confirm(@RequestBody AtrBussLrcCfMainVo atrBussLrcCfMainVo, HttpServletRequest request) {
        Long userId = this.loginUserId(request);
        try {
            atrBussBecfService.lrcDataConfirm(atrBussLrcCfMainVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "保费现金流是否已确认")
    @RequestMapping(value = "/lrc/check_confirm", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> checkPK(@RequestBody AtrBussLrcCfMainVo atrBussLrcCfMainVo) {
        String result = "0";
        try {
            List<AtrBussLrcCfMainVo> vos = atrBussBecfService.findLrcList(atrBussLrcCfMainVo);
            if(vos.size()>0){
                result = "1";
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, result);
        }
    }

    @ApiOperation(value = "根据id查询保费现金流信息")
    @RequestMapping(value = "/lrc/find_by_id/{lrcCfMainId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<AtrBussLrcCfMainVo> findLrcDataById(@PathVariable("lrcCfMainId") Long lrcCfMainId) {
       AtrBussLrcCfMainVo atrBussLrcCfMainVo = atrBussBecfService.findLrcById(lrcCfMainId);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, atrBussLrcCfMainVo);
    }


    @ApiOperation(value = "查询评估结果结果合同组明细数据接口")
    @RequestMapping(value = "/lrc/find_periodHeader_by_id/{lrcCfMainId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<Map<String, Object>> findPeriodHeaderDataById(@PathVariable("lrcCfMainId") Long lrcCfMainId) throws IllegalAccessException {
        LinkedHashMap<String,Object> resultMap = new LinkedHashMap<>();
        try {
            resultMap  = atrBussBecfService.findPeriodHeaderDataById(lrcCfMainId);
        } catch (Exception ex) {
            logger.error(ex.getLocalizedMessage(), ex);
        }
        return new BaseResponse(ResCodeConstant.ResCode.SUCCESS, resultMap);
    }

    @ApiOperation(value = "查询评估结果结果合同组明细数据接口")
    @RequestMapping(value = "/lrc/find_period_by_id/{lrcCfMainId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<Map<String, Object>> findPeriodDataById(@PathVariable("lrcCfMainId") Long lrcCfMainId) throws IllegalAccessException {
        LinkedHashMap<String,Object> resultMap = new LinkedHashMap<>();
        try {
            resultMap = atrBussBecfService.findPeriodDataById(lrcCfMainId);
        } catch (Exception ex) {
            logger.error(ex.getLocalizedMessage(), ex);
        }
        return new BaseResponse(ResCodeConstant.ResCode.SUCCESS, resultMap);
    }



    @ApiOperation(value = "根据id删除保费现金流信息")
    @RequestMapping(value = "/lrc/delete_by_pk/{lrcCfMainId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<Object> deleteLrcCfDateById(@PathVariable("lrcCfMainId") Long lrcCfMainId) {
        AtrBussLrcCfMainVo atrBussLrcCfMainVo = atrBussBecfService.deleteLrcByLrcCfMainId(lrcCfMainId);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, atrBussLrcCfMainVo);
    }
}
