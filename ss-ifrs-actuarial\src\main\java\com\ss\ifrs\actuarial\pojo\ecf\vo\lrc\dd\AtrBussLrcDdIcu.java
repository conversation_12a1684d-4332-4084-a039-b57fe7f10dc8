package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.dd;

import com.ss.ifrs.actuarial.util.abp.IgnoreCol; // 假设这个 import 是需要的
import com.ss.ifrs.actuarial.util.abp.Tab;     // 假设这个 import 是需要的
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;       // 引入 Date

/**
 * LRC 单元信息 (直保&临分分入) - 对应表 atr_buss_dd_lrc_u
 * 注意：此类根据新表结构更新了字段
 */
@Data
@Tab("atr_buss_dd_lrc_u")
public class AtrBussLrcDdIcu {

    /** ID (id) */
    private Long id;

    /** 执行编号 (action_no) */
    private String actionNo;

    /** 业务单位ID (entity_id) */
    private Long entityId;

    /** 评估期年月 (year_month) */
    private String yearMonth;

    /** 业务来源代码 (business_type) - 备注: DB-直保、FB-分入 */
    private String businessType;

    /** 险类代码 (risk_class_code) */
    private String riskClassCode;

    /** 保单号 (policy_no) */
    private String policyNo;

    /** 批单序号 (endorse_seq_no) */
    private String endorseSeqNo;

    /** 险种代码 (risk_code) */
    private String riskCode;

    /** 险别代码 (kind_code) */
    private String kindCode;

    /** 合同组合编号 (portfolio_no) */
    private String portfolioNo;

    /** 合同组号码 (icg_no) */
    private String icgNo;
    
    /** 合同组名称 (icg_name) */
    private String icgName;

    /** 计量单元编号 (cmunit_no) */
    private String cmunitNo;

    /** 一级机构 (company_code1) */
    private String companyCode1;

    /** 二级机构 (company_code2) */
    private String companyCode2;

    /** 三级机构 (company_code3) */
    private String companyCode3;

    /** 四级机构 (company_code4) */
    private String companyCode4;
    
    /** 核算机构 (center_code) */
    private String centerCode;

    /** 起保日期 (effective_date) */
    private Date effectiveDate; // date -> Date

    /** 终保日期 (expiry_date) */
    private Date expiryDate; // date -> Date

    /** 批改生效日期 (endorse_effective_date) */
    private Date endorseEffectiveDate; // date -> Date

    /** 核保通过日期 (approval_date) */
    private Date approvalDate; // date -> Date

    /** 合同确认日期 (contract_date) */
    private Date contractDate; // date -> Date
    
    /** 承保确认日期 (comfirm_date) */
    private Date comfirmDate;

    /** 业务发生年月 (dap_year_month) */
    private String dapYearMonth;

    /** 保费 (premium) */
    private BigDecimal premium; // Double -> BigDecimal

    /** 跟单获取费用 (iacf) */
    private BigDecimal iacf; // Double -> BigDecimal

    /** 非跟单获取费用-对内 (iaehc_in) */
    private BigDecimal iaehcIn; // Double -> BigDecimal

    /** 非跟单获取费用-对外 (iaehc_out) */
    private BigDecimal iaehcOut; // Double -> BigDecimal

    /** 净额结算手续费率 (net_fee_rate) */
    private BigDecimal netFeeRate; // Double -> BigDecimal

    /** 净额结算手续费 (net_fee) */
    private BigDecimal netFee; // Double -> BigDecimal

    /** 上期累计实收保费 (pre_cuml_paid_premium) */
    private BigDecimal preCumlPaidPremium; // Double -> BigDecimal

    /** 上期累计实收净额结算手续费 (pre_cuml_paid_net_fee) */
    private BigDecimal preCumlPaidNetFee; // Double -> BigDecimal

    /** 上期累计实收跟单获取费用 (pre_cuml_paid_iacf) */
    private BigDecimal preCumlPaidIacf; // Double -> BigDecimal

    /** 上期累计已赚保费 (pre_cuml_ed_premium) */
    private BigDecimal preCumlEdPremium; // Double -> BigDecimal

    /** 上期累计已赚净额结算手续费 (pre_cuml_ed_net_fee) */
    private BigDecimal preCumlEdNetFee; // Double -> BigDecimal

    /** 上期累计已赚跟单获取费用 (pre_cuml_ed_iacf) */
    private BigDecimal preCumlEdIacf; // Double -> BigDecimal

    /** 上期累计已赚非跟单获取费用-对内 (pre_cuml_ed_iaehc_in) */
    private BigDecimal preCumlEdIaehcIn; // Double -> BigDecimal

    /** 上期累计已赚非跟单获取费用-对外 (pre_cuml_ed_iaehc_out) */
    private BigDecimal preCumlEdIaehcOut; // Double -> BigDecimal

    /** 当期已赚保费 (cur_ed_premium) */
    private BigDecimal curEdPremium; // Double -> BigDecimal

    /** 当期已赚净额结算手续费 (cur_ed_net_fee) */
    private BigDecimal curEdNetFee; // Double -> BigDecimal

    /** 当期已赚跟单获取费用 (cur_ed_iacf) */
    private BigDecimal curEdIacf; // Double -> BigDecimal

    /** 当期已赚非跟单获取费用-对内 (cur_ed_iaehc_in) */
    private BigDecimal curEdIaehcIn; // Double -> BigDecimal

    /** 当期已赚非跟单获取费用-对外 (cur_ed_iaehc_out) */
    private BigDecimal curEdIaehcOut; // Double -> BigDecimal


    private Date issueDate;
    
    /** 财务渠道 (fin_acc_channel) */
    private String finAccChannel;
    
    /** 财务产品代码 (fin_product_code) */
    private String finProductCode;
    
    /** 财务明细代码 (fin_detail_code) */
    private String finDetailCode;
    
    /** 财务子产品代码 (fin_sub_product_code) */
    private String finSubProductCode;
    
    /** 盈亏判定结果 (pl_judge_rslt) */
    private String plJudgeRslt;

    /** 部门段 (dept_id) - 对应acepayment的article5 */
    private String deptId;
    
    /** 渠道段 (channel_id) - 对应acepayment的article7 */
    private String channelId;

    private BigDecimal preIaehcIn;

    private BigDecimal preIaehcOut;

    /** 当期减值 */
    private BigDecimal curBadDebt;

    /** 当期实收保费  */
    private BigDecimal curPaidPremium;

    /** 当期跟单获取费用 */
    private BigDecimal curIacf;

    /** 当期实收净额结算手续费 */
    private BigDecimal curPaidNetFee;

    /** 批改类型代码 (endorse_type_code) */
    private String endorseTypeCode;

    /** 特殊处理类型 (special_process_type) - 0:正常处理, 1:只计算第0期, 2:不计算发展期 */
    @IgnoreCol
    private Integer specialProcessType;

    /** 剩余月数 */
    @IgnoreCol
    private Integer remainingMonths; // 保留 @IgnoreCol 字段

    @IgnoreCol
    private BigDecimal totalBadDebt;

}