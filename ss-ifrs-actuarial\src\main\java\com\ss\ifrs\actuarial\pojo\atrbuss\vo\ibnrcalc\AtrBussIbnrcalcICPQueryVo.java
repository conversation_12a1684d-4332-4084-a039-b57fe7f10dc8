/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-12-04 15:35:00
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter @Getter
public class AtrBussIbnrcalcICPQueryVo {

    @ApiModelProperty(value = "actionNo", required = true)
    private String actionNo;

    @ApiModelProperty(value = "loa")
    private String loaCode;

    @ApiModelProperty(value = "合同组合")
    private String portfolioNo;

}