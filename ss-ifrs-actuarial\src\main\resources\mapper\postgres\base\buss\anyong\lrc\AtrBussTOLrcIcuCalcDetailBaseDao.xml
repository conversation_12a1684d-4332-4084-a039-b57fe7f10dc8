<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-03-08 10:07:11 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussTOLrcIcuCalcDetailDao">
  <!-- 本文件由GIS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussTOLrcIcuCalcDetailVo">
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="MAIN_ID" property="mainId" jdbcType="DECIMAL" />
    <result column="DEV_NO" property="devNo" jdbcType="DECIMAL" />
    <result column="RIF_RUN_OFF_PATTERN" property="rifRunOffPattern" jdbcType="DECIMAL" />
    <result column="AFTER_PREMIUM_IMPAIRMENT_RATE" property="afterPremiumImpairmentRate" jdbcType="DECIMAL" />
    <result column="RECV_PREMIUM" property="recvPremium" jdbcType="DECIMAL" />
    <result column="ADJ_COMMISSION" property="adjCommission" jdbcType="DECIMAL" />
    <result column="BROKERAGE_FEE" property="brokerageFee" jdbcType="DECIMAL" />
    <result column="IACF_FEE" property="iacfFee" jdbcType="DECIMAL" />
    <result column="ED_PREMIUM" property="edPremium" jdbcType="DECIMAL" />
    <result column="MAINTENANCE_FEE" property="maintenanceFee" jdbcType="DECIMAL" />
    <result column="UE_PREMIUM" property="uePremium" jdbcType="DECIMAL" />
    <result column="GEP_UWQ" property="gepUwq" jdbcType="DECIMAL" />
    <result column="ULTIMATE_LOSS" property="ultimateLoss" jdbcType="DECIMAL" />
    <result column="RIF_RUN_OFF_PATTERN_RATE" property="rifRunOffPatternRate" jdbcType="DECIMAL" />
    <result column="ULTIMATE_LOSS_MORTGAGE" property="ultimateLossMortgage" jdbcType="DECIMAL" />
    <result column="COVERAGE_AMOUNT" property="coverageAmount" jdbcType="DECIMAL" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    ID, MAIN_ID, DEV_NO, RIF_RUN_OFF_PATTERN, AFTER_PREMIUM_IMPAIRMENT_RATE, RECV_PREMIUM, 
    ADJ_COMMISSION, BROKERAGE_FEE, IACF_FEE, ED_PREMIUM, MAINTENANCE_FEE, UE_PREMIUM, 
    GEP_UWQ, ULTIMATE_LOSS, RIF_RUN_OFF_PATTERN_RATE, ULTIMATE_LOSS_MORTGAGE, COVERAGE_AMOUNT
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="id != null ">
          and ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="mainId != null ">
          and MAIN_ID = #{mainId,jdbcType=DECIMAL}
      </if>
      <if test="devNo != null ">
          and DEV_NO = #{devNo,jdbcType=DECIMAL}
      </if>
      <if test="rifRunOffPattern != null ">
          and RIF_RUN_OFF_PATTERN = #{rifRunOffPattern,jdbcType=DECIMAL}
      </if>
      <if test="afterPremiumImpairmentRate != null ">
          and AFTER_PREMIUM_IMPAIRMENT_RATE = #{afterPremiumImpairmentRate,jdbcType=DECIMAL}
      </if>
      <if test="recvPremium != null ">
          and RECV_PREMIUM = #{recvPremium,jdbcType=DECIMAL}
      </if>
      <if test="adjCommission != null ">
          and ADJ_COMMISSION = #{adjCommission,jdbcType=DECIMAL}
      </if>
      <if test="brokerageFee != null ">
          and BROKERAGE_FEE = #{brokerageFee,jdbcType=DECIMAL}
      </if>
      <if test="iacfFee != null ">
          and IACF_FEE = #{iacfFee,jdbcType=DECIMAL}
      </if>
      <if test="edPremium != null ">
          and ED_PREMIUM = #{edPremium,jdbcType=DECIMAL}
      </if>
      <if test="maintenanceFee != null ">
          and MAINTENANCE_FEE = #{maintenanceFee,jdbcType=DECIMAL}
      </if>
      <if test="uePremium != null ">
          and UE_PREMIUM = #{uePremium,jdbcType=DECIMAL}
      </if>
      <if test="gepUwq != null ">
          and GEP_UWQ = #{gepUwq,jdbcType=DECIMAL}
      </if>
      <if test="ultimateLoss != null ">
          and ULTIMATE_LOSS = #{ultimateLoss,jdbcType=DECIMAL}
      </if>
      <if test="rifRunOffPatternRate != null ">
          and RIF_RUN_OFF_PATTERN_RATE = #{rifRunOffPatternRate,jdbcType=DECIMAL}
      </if>
      <if test="ultimateLossMortgage != null ">
          and ULTIMATE_LOSS_MORTGAGE = #{ultimateLossMortgage,jdbcType=DECIMAL}
      </if>
      <if test="coverageAmount != null ">
          and COVERAGE_AMOUNT = #{coverageAmount,jdbcType=DECIMAL}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.id != null ">
          and ID = #{condition.id,jdbcType=DECIMAL}
      </if>
      <if test="condition.mainId != null ">
          and MAIN_ID = #{condition.mainId,jdbcType=DECIMAL}
      </if>
      <if test="condition.devNo != null ">
          and DEV_NO = #{condition.devNo,jdbcType=DECIMAL}
      </if>
      <if test="condition.rifRunOffPattern != null ">
          and RIF_RUN_OFF_PATTERN = #{condition.rifRunOffPattern,jdbcType=DECIMAL}
      </if>
      <if test="condition.afterPremiumImpairmentRate != null ">
          and AFTER_PREMIUM_IMPAIRMENT_RATE = #{condition.afterPremiumImpairmentRate,jdbcType=DECIMAL}
      </if>
      <if test="condition.recvPremium != null ">
          and RECV_PREMIUM = #{condition.recvPremium,jdbcType=DECIMAL}
      </if>
      <if test="condition.adjCommission != null ">
          and ADJ_COMMISSION = #{condition.adjCommission,jdbcType=DECIMAL}
      </if>
      <if test="condition.brokerageFee != null ">
          and BROKERAGE_FEE = #{condition.brokerageFee,jdbcType=DECIMAL}
      </if>
      <if test="condition.iacfFee != null ">
          and IACF_FEE = #{condition.iacfFee,jdbcType=DECIMAL}
      </if>
      <if test="condition.edPremium != null ">
          and ED_PREMIUM = #{condition.edPremium,jdbcType=DECIMAL}
      </if>
      <if test="condition.maintenanceFee != null ">
          and MAINTENANCE_FEE = #{condition.maintenanceFee,jdbcType=DECIMAL}
      </if>
      <if test="condition.uePremium != null ">
          and UE_PREMIUM = #{condition.uePremium,jdbcType=DECIMAL}
      </if>
      <if test="condition.gepUwq != null ">
          and GEP_UWQ = #{condition.gepUwq,jdbcType=DECIMAL}
      </if>
      <if test="condition.ultimateLoss != null ">
          and ULTIMATE_LOSS = #{condition.ultimateLoss,jdbcType=DECIMAL}
      </if>
      <if test="condition.rifRunOffPatternRate != null ">
          and RIF_RUN_OFF_PATTERN_RATE = #{condition.rifRunOffPatternRate,jdbcType=DECIMAL}
      </if>
      <if test="condition.ultimateLossMortgage != null ">
          and ULTIMATE_LOSS_MORTGAGE = #{condition.ultimateLossMortgage,jdbcType=DECIMAL}
      </if>
      <if test="condition.coverageAmount != null ">
          and COVERAGE_AMOUNT = #{condition.coverageAmount,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="id != null ">
          and ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="mainId != null ">
          and MAIN_ID = #{mainId,jdbcType=DECIMAL}
      </if>
      <if test="devNo != null ">
          and DEV_NO = #{devNo,jdbcType=DECIMAL}
      </if>
      <if test="rifRunOffPattern != null ">
          and RIF_RUN_OFF_PATTERN = #{rifRunOffPattern,jdbcType=DECIMAL}
      </if>
      <if test="afterPremiumImpairmentRate != null ">
          and AFTER_PREMIUM_IMPAIRMENT_RATE = #{afterPremiumImpairmentRate,jdbcType=DECIMAL}
      </if>
      <if test="recvPremium != null ">
          and RECV_PREMIUM = #{recvPremium,jdbcType=DECIMAL}
      </if>
      <if test="adjCommission != null ">
          and ADJ_COMMISSION = #{adjCommission,jdbcType=DECIMAL}
      </if>
      <if test="brokerageFee != null ">
          and BROKERAGE_FEE = #{brokerageFee,jdbcType=DECIMAL}
      </if>
      <if test="iacfFee != null ">
          and IACF_FEE = #{iacfFee,jdbcType=DECIMAL}
      </if>
      <if test="edPremium != null ">
          and ED_PREMIUM = #{edPremium,jdbcType=DECIMAL}
      </if>
      <if test="maintenanceFee != null ">
          and MAINTENANCE_FEE = #{maintenanceFee,jdbcType=DECIMAL}
      </if>
      <if test="uePremium != null ">
          and UE_PREMIUM = #{uePremium,jdbcType=DECIMAL}
      </if>
      <if test="gepUwq != null ">
          and GEP_UWQ = #{gepUwq,jdbcType=DECIMAL}
      </if>
      <if test="ultimateLoss != null ">
          and ULTIMATE_LOSS = #{ultimateLoss,jdbcType=DECIMAL}
      </if>
      <if test="rifRunOffPatternRate != null ">
          and RIF_RUN_OFF_PATTERN_RATE = #{rifRunOffPatternRate,jdbcType=DECIMAL}
      </if>
      <if test="ultimateLossMortgage != null ">
          and ULTIMATE_LOSS_MORTGAGE = #{ultimateLossMortgage,jdbcType=DECIMAL}
      </if>
      <if test="coverageAmount != null ">
          and COVERAGE_AMOUNT = #{coverageAmount,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_TO_LRC_ICU_CALC_DETAIL
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_TO_LRC_ICU_CALC_DETAIL
    where ID in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_TO_LRC_ICU_CALC_DETAIL
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussTOLrcIcuCalcDetail">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_TO_LRC_ICU_CALC_DETAIL
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_TO_LRC_ICU_CALC_DETAIL
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="ID" keyProperty="id" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussTOLrcIcuCalcDetail">
    insert into ATR_BUSS_TO_LRC_ICU_CALC_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="mainId != null">
        MAIN_ID,
      </if>
      <if test="devNo != null">
        DEV_NO,
      </if>
      <if test="rifRunOffPattern != null">
        RIF_RUN_OFF_PATTERN,
      </if>
      <if test="afterPremiumImpairmentRate != null">
        AFTER_PREMIUM_IMPAIRMENT_RATE,
      </if>
      <if test="recvPremium != null">
        RECV_PREMIUM,
      </if>
      <if test="adjCommission != null">
        ADJ_COMMISSION,
      </if>
      <if test="brokerageFee != null">
        BROKERAGE_FEE,
      </if>
      <if test="iacfFee != null">
        IACF_FEE,
      </if>
      <if test="edPremium != null">
        ED_PREMIUM,
      </if>
      <if test="maintenanceFee != null">
        MAINTENANCE_FEE,
      </if>
      <if test="uePremium != null">
        UE_PREMIUM,
      </if>
      <if test="gepUwq != null">
        GEP_UWQ,
      </if>
      <if test="ultimateLoss != null">
        ULTIMATE_LOSS,
      </if>
      <if test="rifRunOffPatternRate != null">
        RIF_RUN_OFF_PATTERN_RATE,
      </if>
      <if test="ultimateLossMortgage != null">
        ULTIMATE_LOSS_MORTGAGE,
      </if>
      <if test="coverageAmount != null">
        COVERAGE_AMOUNT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="mainId != null">
        #{mainId,jdbcType=DECIMAL},
      </if>
      <if test="devNo != null">
        #{devNo,jdbcType=DECIMAL},
      </if>
      <if test="rifRunOffPattern != null">
        #{rifRunOffPattern,jdbcType=DECIMAL},
      </if>
      <if test="afterPremiumImpairmentRate != null">
        #{afterPremiumImpairmentRate,jdbcType=DECIMAL},
      </if>
      <if test="recvPremium != null">
        #{recvPremium,jdbcType=DECIMAL},
      </if>
      <if test="adjCommission != null">
        #{adjCommission,jdbcType=DECIMAL},
      </if>
      <if test="brokerageFee != null">
        #{brokerageFee,jdbcType=DECIMAL},
      </if>
      <if test="iacfFee != null">
        #{iacfFee,jdbcType=DECIMAL},
      </if>
      <if test="edPremium != null">
        #{edPremium,jdbcType=DECIMAL},
      </if>
      <if test="maintenanceFee != null">
        #{maintenanceFee,jdbcType=DECIMAL},
      </if>
      <if test="uePremium != null">
        #{uePremium,jdbcType=DECIMAL},
      </if>
      <if test="gepUwq != null">
        #{gepUwq,jdbcType=DECIMAL},
      </if>
      <if test="ultimateLoss != null">
        #{ultimateLoss,jdbcType=DECIMAL},
      </if>
      <if test="rifRunOffPatternRate != null">
        #{rifRunOffPatternRate,jdbcType=DECIMAL},
      </if>
      <if test="ultimateLossMortgage != null">
        #{ultimateLossMortgage,jdbcType=DECIMAL},
      </if>
      <if test="coverageAmount != null">
        #{coverageAmount,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all 
    <foreach collection="list" item="item" index="index">
      into ATR_BUSS_TO_LRC_ICU_CALC_DETAIL values 
       (#{item.id,jdbcType=DECIMAL}, 
        #{item.mainId,jdbcType=DECIMAL}, #{item.devNo,jdbcType=DECIMAL}, #{item.rifRunOffPattern,jdbcType=DECIMAL}, 
        #{item.afterPremiumImpairmentRate,jdbcType=DECIMAL}, #{item.recvPremium,jdbcType=DECIMAL}, 
        #{item.adjCommission,jdbcType=DECIMAL}, #{item.brokerageFee,jdbcType=DECIMAL}, 
        #{item.iacfFee,jdbcType=DECIMAL}, #{item.edPremium,jdbcType=DECIMAL}, #{item.maintenanceFee,jdbcType=DECIMAL}, 
        #{item.uePremium,jdbcType=DECIMAL}, #{item.gepUwq,jdbcType=DECIMAL}, #{item.ultimateLoss,jdbcType=DECIMAL}, 
        #{item.rifRunOffPatternRate,jdbcType=DECIMAL}, #{item.ultimateLossMortgage,jdbcType=DECIMAL}, 
        #{item.coverageAmount,jdbcType=DECIMAL})
    </foreach>
    select 1 
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussTOLrcIcuCalcDetail">
    update ATR_BUSS_TO_LRC_ICU_CALC_DETAIL
    <set>
      <if test="mainId != null">
        MAIN_ID = #{mainId,jdbcType=DECIMAL},
      </if>
      <if test="devNo != null">
        DEV_NO = #{devNo,jdbcType=DECIMAL},
      </if>
      <if test="rifRunOffPattern != null">
        RIF_RUN_OFF_PATTERN = #{rifRunOffPattern,jdbcType=DECIMAL},
      </if>
      <if test="afterPremiumImpairmentRate != null">
        AFTER_PREMIUM_IMPAIRMENT_RATE = #{afterPremiumImpairmentRate,jdbcType=DECIMAL},
      </if>
      <if test="recvPremium != null">
        RECV_PREMIUM = #{recvPremium,jdbcType=DECIMAL},
      </if>
      <if test="adjCommission != null">
        ADJ_COMMISSION = #{adjCommission,jdbcType=DECIMAL},
      </if>
      <if test="brokerageFee != null">
        BROKERAGE_FEE = #{brokerageFee,jdbcType=DECIMAL},
      </if>
      <if test="iacfFee != null">
        IACF_FEE = #{iacfFee,jdbcType=DECIMAL},
      </if>
      <if test="edPremium != null">
        ED_PREMIUM = #{edPremium,jdbcType=DECIMAL},
      </if>
      <if test="maintenanceFee != null">
        MAINTENANCE_FEE = #{maintenanceFee,jdbcType=DECIMAL},
      </if>
      <if test="uePremium != null">
        UE_PREMIUM = #{uePremium,jdbcType=DECIMAL},
      </if>
      <if test="gepUwq != null">
        GEP_UWQ = #{gepUwq,jdbcType=DECIMAL},
      </if>
      <if test="ultimateLoss != null">
        ULTIMATE_LOSS = #{ultimateLoss,jdbcType=DECIMAL},
      </if>
      <if test="rifRunOffPatternRate != null">
        RIF_RUN_OFF_PATTERN_RATE = #{rifRunOffPatternRate,jdbcType=DECIMAL},
      </if>
      <if test="ultimateLossMortgage != null">
        ULTIMATE_LOSS_MORTGAGE = #{ultimateLossMortgage,jdbcType=DECIMAL},
      </if>
      <if test="coverageAmount != null">
        COVERAGE_AMOUNT = #{coverageAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussTOLrcIcuCalcDetail">
    update ATR_BUSS_TO_LRC_ICU_CALC_DETAIL
    <set>
      <if test="record.mainId != null">
        MAIN_ID = #{record.mainId,jdbcType=DECIMAL},
      </if>
      <if test="record.devNo != null">
        DEV_NO = #{record.devNo,jdbcType=DECIMAL},
      </if>
      <if test="record.rifRunOffPattern != null">
        RIF_RUN_OFF_PATTERN = #{record.rifRunOffPattern,jdbcType=DECIMAL},
      </if>
      <if test="record.afterPremiumImpairmentRate != null">
        AFTER_PREMIUM_IMPAIRMENT_RATE = #{record.afterPremiumImpairmentRate,jdbcType=DECIMAL},
      </if>
      <if test="record.recvPremium != null">
        RECV_PREMIUM = #{record.recvPremium,jdbcType=DECIMAL},
      </if>
      <if test="record.adjCommission != null">
        ADJ_COMMISSION = #{record.adjCommission,jdbcType=DECIMAL},
      </if>
      <if test="record.brokerageFee != null">
        BROKERAGE_FEE = #{record.brokerageFee,jdbcType=DECIMAL},
      </if>
      <if test="record.iacfFee != null">
        IACF_FEE = #{record.iacfFee,jdbcType=DECIMAL},
      </if>
      <if test="record.edPremium != null">
        ED_PREMIUM = #{record.edPremium,jdbcType=DECIMAL},
      </if>
      <if test="record.maintenanceFee != null">
        MAINTENANCE_FEE = #{record.maintenanceFee,jdbcType=DECIMAL},
      </if>
      <if test="record.uePremium != null">
        UE_PREMIUM = #{record.uePremium,jdbcType=DECIMAL},
      </if>
      <if test="record.gepUwq != null">
        GEP_UWQ = #{record.gepUwq,jdbcType=DECIMAL},
      </if>
      <if test="record.ultimateLoss != null">
        ULTIMATE_LOSS = #{record.ultimateLoss,jdbcType=DECIMAL},
      </if>
      <if test="record.rifRunOffPatternRate != null">
        RIF_RUN_OFF_PATTERN_RATE = #{record.rifRunOffPatternRate,jdbcType=DECIMAL},
      </if>
      <if test="record.ultimateLossMortgage != null">
        ULTIMATE_LOSS_MORTGAGE = #{record.ultimateLossMortgage,jdbcType=DECIMAL},
      </if>
      <if test="record.coverageAmount != null">
        COVERAGE_AMOUNT = #{record.coverageAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from ATR_BUSS_TO_LRC_ICU_CALC_DETAIL
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from ATR_BUSS_TO_LRC_ICU_CALC_DETAIL
    where ID in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from ATR_BUSS_TO_LRC_ICU_CALC_DETAIL
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussTOLrcIcuCalcDetail">
    select count(1) from ATR_BUSS_TO_LRC_ICU_CALC_DETAIL
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>