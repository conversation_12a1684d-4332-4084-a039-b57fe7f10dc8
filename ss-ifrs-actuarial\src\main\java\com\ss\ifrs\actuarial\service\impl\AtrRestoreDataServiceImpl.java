package com.ss.ifrs.actuarial.service.impl;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import com.ss.ifrs.actuarial.service.AtrRestoreDataService;

@Service
public class AtrRestoreDataServiceImpl implements AtrRestoreDataService {

	final Logger LOG = LoggerFactory.getLogger(getClass());

	@Autowired
	private JdbcTemplate jdbcTemplate;

	@Autowired
	private TransactionTemplate transactionTemplate;

	@Override
	public void executeRestoreSql() throws Exception {
		transactionTemplate.execute((TransactionCallback<Object>) transactionStatus -> {
			String querySql = "SELECT 'delete from '|| replace(c.relname , '_bak','') as deletesql,"
					+ "'insert into '|| replace(c.relname , '_bak','') ||'(' || string_agg(a.attname,', ') ||' )SELECT ' || string_agg(a.attname,', ') ||' FROM '|| c.relname  as insertsql "
					+ "FROM pg_class as c,pg_attribute as a " + "where c.relname like 'atr%_bak' and "
					+ "a.attrelid = c.oid and a.attnum>0 " + "group by c.relname";
			List<Map<String, Object>> sqlList = jdbcTemplate.queryForList(querySql);
			for (Map<String, Object> res : sqlList) {
				String deleteSql = (String) res.get("deletesql");
				String insertSql = (String) res.get("insertsql");
				try {
					jdbcTemplate.update(deleteSql);
					jdbcTemplate.update(insertSql);
				} catch (Exception e) {
					transactionStatus.setRollbackOnly();
					LOG.error(e.getLocalizedMessage(), e);
				}
			}
			return transactionStatus;
		});
	}
}
