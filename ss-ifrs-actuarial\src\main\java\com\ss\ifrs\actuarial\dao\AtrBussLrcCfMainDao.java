/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2022-12-07 10:49:05
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao;

import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfMain;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLrcCfMainVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2022-12-07 10:49:05<br/>
 * Description: BECF外部接入主表 Dao类<br/>
 * Related Table Name: ATR_BUSS_LRC_CF_MAIN<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrBussLrcCfMainDao extends IDao<AtrBussLrcCfMain, Long> {

    Page<AtrBussLrcCfMainVo> fuzzySearchPage(AtrBussLrcCfMainVo atrBussLrcCfMainVo, Pageable pageParam);

    AtrBussLrcCfMainVo findByMainId(Long lrcCfMainId);

    Integer countConfirm(AtrBussLrcCfMain atrBussLrcCfMain);
}