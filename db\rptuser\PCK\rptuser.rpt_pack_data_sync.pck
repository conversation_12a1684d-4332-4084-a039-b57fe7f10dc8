CREATE OR REPLACE PACKAGE rpt_pack_data_sync IS

    --同步科目余额数据
    PROCEDURE proc_ledger_balance(p_entity_id  NUMBER,
                                  p_year_month VARCHAR2,
                                  p_book_code  VARCHAR2,
                                  p_oper_id    NUMBER);

    --同步科目专项数据
    PROCEDURE proc_article_balance(p_entity_id  NUMBER,
                                   p_year_month VARCHAR2,
                                   p_book_code  VARCHAR2,
                                   p_oper_id    NUMBER);
    --同步计量输出数据
    PROCEDURE proc_atr_evaluate_result(p_entity_id        NUMBER,
                                       p_year_month_start VARCHAR2,
                                       p_year_month_end   VARCHAR2,
                                       p_oper_id          NUMBER);

END rpt_pack_data_sync;
/
CREATE OR REPLACE PACKAGE BODY rpt_pack_data_sync IS

    PROCEDURE proc_ledger_balance(p_entity_id  NUMBER,
                                  p_year_month VARCHAR2,
                                  p_book_code  VARCHAR2,
                                  p_oper_id    NUMBER) AS

        v_serialno_src       NUMBER;
        v_serialno_tgt       NUMBER;
        v_period_detail_id   NUMBER;
        v_serialno_src_count NUMBER;
        v_serialno_tgt_count NUMBER;

        v_log_msg   VARCHAR2(4000); --日志信息
        v_error_msg VARCHAR2(200); --异常信息

    BEGIN
        BEGIN
            --先比较源表轨迹表的版本号和目标表版本号

            --查看源数据表是否有数据
            SELECT COUNT(1)
            INTO v_serialno_src_count
            FROM accuser.acc_buss_ledger_balance bh
            WHERE bh.entity_id = p_entity_id
              AND bh.book_code = p_book_code
              AND bh.year_month = p_year_month
            ORDER BY bh.ledger_balance_id DESC;

            dbms_output.put_line('科目余额源数据表数据量为:' || v_serialno_src_count);

            IF v_serialno_src_count = 0 THEN
                --源数据表无数据，不同步
                --抛出自定义异常信息 【会中断事务】
                v_error_msg := '科目余额源数据表数据量为:' || v_serialno_src_count ||
                               '，不执行同步：[' || p_entity_id || ']-[' || p_book_code ||
                               ']-[' || p_year_month || ']';

                --往外层抛出异常信息
                raise_application_error(-20002, v_error_msg);

            ELSE

                --获取源数据表版本号
                SELECT r.serial_no
                INTO v_serialno_src
                FROM (SELECT bh.serial_no
                      FROM accuser.acc_buss_ledger_balance bh
                      WHERE bh.entity_id = p_entity_id
                        AND bh.book_code = p_book_code
                        AND bh.year_month = p_year_month
                      ORDER BY bh.ledger_balance_id DESC) r
                WHERE rownum = 1;

                --查看目标表是否有数据
                SELECT COUNT(1)
                INTO v_serialno_tgt_count
                FROM rpt_dap_ledger_balance lb
                WHERE lb.entity_id = p_entity_id
                  AND lb.book_code = p_book_code
                  AND lb.year_month = p_year_month
                ORDER BY lb.ledger_blance_id DESC;

                IF v_serialno_tgt_count = 0 THEN
                    --目标表无数据，可同步
                    v_serialno_tgt := 0;
                ELSE
                    -- 目标表有数据，则获取目标表最新版本号
                    SELECT r.serial_no
                    INTO v_serialno_tgt
                    FROM (SELECT lb.serial_no
                          FROM rpt_dap_ledger_balance lb
                          WHERE lb.entity_id = p_entity_id
                            AND lb.book_code = p_book_code
                            AND lb.year_month = p_year_month
                          ORDER BY lb.ledger_blance_id DESC) r
                    WHERE rownum = 1;
                END IF;

                --若版本号取最新，则不同步数据
                IF v_serialno_tgt = coalesce(v_serialno_src, 0) THEN
                    --抛出自定义异常信息 【会中断事务】
                    v_error_msg := '科目余额目标表数据版本号已最新，不同步：[' || p_entity_id || ']-[' ||
                                   p_book_code || ']-[' || p_year_month || ']';

                    --不抛出异常信息
                    dbms_output.put_line(v_error_msg);

                ELSE

                    --备份到轨迹表
                    INSERT INTO rpt_dap_ledger_balancehis
                    (ledger_blance_his_id,
                     ledger_blance_id,
                     entity_id,
                     book_code,
                     year_month,
                     root_account_id,
                     account_id,
                     currency_code,
                     currency_cu_code,
                     debit_amount,
                     debit_amount_cu,
                     credit_amount,
                     credit_amount_cu,
                     debit_amount_quarter,
                     debit_amount_quarter_cu,
                     credit_amount_quarter,
                     credit_amount_quarter_cu,
                     debit_amount_year,
                     debit_amount_year_cu,
                     credit_amount_year,
                     credit_amount_year_cu,
                     opening_balance_cu,
                     closing_balance_cu,
                     opening_balance,
                     closing_balance,
                     create_time,
                     creator_id,
                     update_time,
                     updator_id,
                     serial_no)
                    SELECT rpt_seq_dap_led_blc_his.nextval,
                           ledger_blance_id,
                           entity_id,
                           book_code,
                           year_month,
                           root_account_id,
                           account_id,
                           currency_code,
                           currency_cu_code,
                           debit_amount,
                           debit_amount_cu,
                           credit_amount,
                           credit_amount_cu,
                           debit_amount_quarter,
                           debit_amount_quarter_cu,
                           credit_amount_quarter,
                           credit_amount_quarter_cu,
                           debit_amount_year,
                           debit_amount_year_cu,
                           credit_amount_year,
                           credit_amount_year_cu,
                           opening_balance_cu,
                           closing_balance_cu,
                           opening_balance,
                           closing_balance,
                           create_time,
                           creator_id,
                           update_time,
                           updator_id,
                           serial_no
                    FROM rpt_dap_ledger_balance b
                    WHERE b.entity_id = p_entity_id
                      AND b.book_code = p_book_code
                      AND b.year_month = p_year_month;

                    --清除旧数据
                    DELETE FROM rpt_dap_ledger_balance b
                    WHERE b.entity_id = p_entity_id
                      AND b.book_code = p_book_code
                      AND b.year_month = p_year_month;

                    --同步新数据
                    INSERT INTO rpt_dap_ledger_balance
                    (ledger_blance_id,
                     entity_id,
                     book_code,
                     year_month,
                     root_account_id,
                     account_id,
                     currency_code,
                     currency_cu_code,
                     debit_amount,
                     debit_amount_cu,
                     credit_amount,
                     credit_amount_cu,
                     debit_amount_quarter,
                     debit_amount_quarter_cu,
                     credit_amount_quarter,
                     credit_amount_quarter_cu,
                     debit_amount_year,
                     debit_amount_year_cu,
                     credit_amount_year,
                     credit_amount_year_cu,
                     opening_balance_cu,
                     closing_balance_cu,
                     opening_balance,
                     closing_balance,
                     create_time,
                     creator_id,
                     update_time,
                     updator_id,
                     serial_no)
                    SELECT rpt_seq_dap_ledger_blc.nextval,
                           entity_id,
                           book_code,
                           year_month,
                           root_account_id,
                           account_id,
                           currency_code,
                           currency_cu_code,
                           debit_amount,
                           debit_amount_cu,
                           credit_amount,
                           credit_amount_cu,
                           debit_amount_quarter,
                           debit_amount_quarter_cu,
                           credit_amount_quarter,
                           credit_amount_quarter_cu,
                           debit_amount_year,
                           debit_amount_year_cu,
                           credit_amount_year,
                           credit_amount_year_cu,
                           opening_balance_cu,
                           closing_balance_cu,
                           opening_balance,
                           closing_balance,
                           SYSDATE,
                           p_oper_id,
                           NULL,
                           NULL,
                           coalesce(v_serialno_src, 0) + 1 --目标表版本号 = 源数据版本号 + 1
                    FROM accuser.acc_buss_ledger_balance b
                    WHERE b.entity_id = p_entity_id
                      AND b.book_code = p_book_code
                      AND b.year_month = p_year_month
                      AND b.root_account_id IS NOT NULL;

                END IF;

            END IF;

            --查询当前业务期间详情id
            SELECT bpd.period_detail_id
            INTO v_period_detail_id
            FROM rpt_conf_bussperiod_detail bpd
                     LEFT JOIN rpt_conf_bussperiod p
                               ON bpd.buss_period_id = p.buss_period_id
                     LEFT JOIN rpt_conf_table t
                               ON bpd.biz_type_id = t.biz_type_id
            WHERE p.entity_id = p_entity_id
              AND p.year_month = p_year_month
              AND t.biz_code = 'DAP_ACC_LEDGER_BALANCE';

            --修改rpt_conf_bussperiod_detail为已准备状态
            UPDATE rpt_conf_bussperiod_detail
            SET ready_state = '1',
                exec_result = 'success',
                task_time   = SYSDATE
            WHERE period_detail_id = v_period_detail_id;

            --同步业务期间
            rpt_pack_buss_period.proc_period_execution(p_entity_id, '1');

            --提交事务
            COMMIT;
        EXCEPTION
            WHEN OTHERS THEN

                --修改rpt_conf_bussperiod_detail为失败状态
                UPDATE rpt_conf_bussperiod_detail
                SET ready_state = '0',
                    exec_result = 'failed',
                    task_time   = SYSDATE
                WHERE period_detail_id = v_period_detail_id;

                --提交事务
                COMMIT;

                --抛出自定义异常信息 【会中断事务】
                v_log_msg := substr('报表同步科目余额表发生异常，请检查！：' || v_error_msg ||
                                    '；**SQLERRM: ' || SQLERRM || '**Error line: ' ||
                                    dbms_utility.format_error_backtrace() || '',
                                    1,
                                    4000);

                --往外层抛出异常信息
                raise_application_error(-20003, v_log_msg);

        END;

    END proc_ledger_balance;

    PROCEDURE proc_article_balance(p_entity_id  NUMBER,
                                   p_year_month VARCHAR2,
                                   p_book_code  VARCHAR2,
                                   p_oper_id    NUMBER) AS
        v_serialno_src       NUMBER;
        v_serialno_tgt       NUMBER;
        v_period_detail_id   NUMBER;
        v_serialno_src_count NUMBER;
        v_serialno_tgt_count NUMBER;

        v_log_msg   VARCHAR2(4000); --日志信息
        v_error_msg VARCHAR2(200); --异常信息

    BEGIN
        BEGIN
            --先比较源表的版本号和目标表版本号

            --查看源数据表是否有数据
            SELECT COUNT(1)
            INTO v_serialno_src_count
            FROM accuser.acc_buss_article_balance bh
            WHERE bh.entity_id = p_entity_id
              AND bh.book_code = p_book_code
              AND bh.year_month = p_year_month
            ORDER BY bh.article_id DESC;

            dbms_output.put_line('专项余额源数据表数据量为:' || v_serialno_src_count);

            IF v_serialno_src_count = 0 THEN
                --源数据表无数据，不同步
                --抛出自定义异常信息 【会中断事务】
                v_error_msg := '专项余额源数据表数据量为:' || v_serialno_src_count ||
                               '，不执行同步：[' || p_entity_id || ']-[' || p_book_code ||
                               ']-[' || p_year_month || ']';

                --暂不往外层抛出异常信息【因专项不一定有数据，暂时不抛异常终止，且更新业务期间详情状态为成功】

            ELSE

                --获取源数据表版本号
                SELECT r.serial_no
                INTO v_serialno_src
                FROM (SELECT serial_no
                      FROM accuser.acc_buss_article_balance bh
                      WHERE bh.entity_id = p_entity_id
                        AND bh.book_code = p_book_code
                        AND bh.year_month = p_year_month
                      ORDER BY bh.article_id DESC) r
                WHERE rownum = 1;

                --查看目标数据表是否有数据
                SELECT COUNT(1)
                INTO v_serialno_tgt_count
                FROM rpt_dap_article_balance ab
                WHERE ab.entity_id = p_entity_id
                  AND ab.book_code = p_book_code
                  AND ab.year_month = p_year_month
                ORDER BY ab.art_blance_id DESC;

                IF v_serialno_tgt_count = 0 THEN
                    --目标表无数据，可同步
                    v_serialno_tgt := 0;
                ELSE
                    -- 获取目标表最新版本号
                    SELECT r.serial_no
                    INTO v_serialno_tgt
                    FROM (SELECT ab.serial_no
                          FROM rpt_dap_article_balance ab
                          WHERE ab.entity_id = p_entity_id
                            AND ab.book_code = p_book_code
                            AND ab.year_month = p_year_month
                          ORDER BY ab.art_blance_id DESC) r
                    WHERE rownum = 1;
                END IF;

                --若版本号取最新，则不同步数据
                IF v_serialno_tgt = coalesce(v_serialno_src, 0) + 1 THEN

                    --抛出自定义异常信息 【会中断事务】
                    v_error_msg := '专项余额目标表数据版本号已最新，不同步：[' || p_entity_id || ']-[' ||
                                   p_book_code || ']-[' || p_year_month || ']';

                    --不抛出异常信息
                    dbms_output.put_line(v_error_msg);

                ELSE

                    --备份到轨迹表
                    INSERT INTO rpt_dap_article_bal_his
                    (art_blance_his_id,
                     art_blance_id,
                     entity_id,
                     book_code,
                     year_month,
                     root_account_id,
                     account_id,
                     article1,
                     article2,
                     article3,
                     article4,
                     article5,
                     article6,
                     article7,
                     article8,
                     article9,
                     article10,
                     article11,
                     article12,
                     article13,
                     article14,
                     article15,
                     article16,
                     article17,
                     currency_code,
                     currency_cu_code,
                     debit_amount,
                     debit_amount_cu,
                     credit_amount,
                     credit_amount_cu,
                     debit_amount_quarter,
                     debit_amount_quarter_cu,
                     credit_amount_quarter,
                     credit_amount_quarter_cu,
                     debit_amount_year,
                     debit_amount_year_cu,
                     credit_amount_year,
                     credit_amount_year_cu,
                     opening_balance_cu,
                     closing_balance_cu,
                     opening_balance,
                     closing_balance,
                     create_time,
                     creator_id,
                     update_time,
                     updator_id,
                     serial_no)
                    SELECT rpt_seq_dap_atc_blc_his.nextval,
                           art_blance_id,
                           entity_id,
                           book_code,
                           year_month,
                           root_account_id,
                           account_id,
                           article1,
                           article2,
                           article3,
                           article4,
                           article5,
                           article6,
                           article7,
                           article8,
                           article9,
                           article10,
                           article11,
                           article12,
                           article13,
                           article14,
                           article15,
                           article16,
                           article17,
                           currency_code,
                           currency_cu_code,
                           debit_amount,
                           debit_amount_cu,
                           credit_amount,
                           credit_amount_cu,
                           debit_amount_quarter,
                           debit_amount_quarter_cu,
                           credit_amount_quarter,
                           credit_amount_quarter_cu,
                           debit_amount_year,
                           debit_amount_year_cu,
                           credit_amount_year,
                           credit_amount_year_cu,
                           opening_balance_cu,
                           closing_balance_cu,
                           opening_balance,
                           closing_balance,
                           create_time,
                           creator_id,
                           update_time,
                           updator_id,
                           serial_no
                    FROM rpt_dap_article_balance b
                    WHERE b.entity_id = p_entity_id
                      AND b.book_code = p_book_code
                      AND b.year_month = p_year_month;

                    --清除旧数据
                    DELETE FROM rpt_dap_article_balance b
                    WHERE b.entity_id = p_entity_id
                      AND b.book_code = p_book_code
                      AND b.year_month = p_year_month;

                    -- 同步新数据
                    INSERT INTO rpt_dap_article_balance
                    (art_blance_id,
                     entity_id,
                     book_code,
                     year_month,
                     root_account_id,
                     account_id,
                     article1,
                     article2,
                     article3,
                     article4,
                     article5,
                     article6,
                     article7,
                     article8,
                     article9,
                     article10,
                     article11,
                     article12,
                     article13,
                     article14,
                     article15,
                     article16,
                     article17,
                     currency_code,
                     currency_cu_code,
                     debit_amount,
                     debit_amount_cu,
                     credit_amount,
                     credit_amount_cu,
                     debit_amount_quarter,
                     debit_amount_quarter_cu,
                     credit_amount_quarter,
                     credit_amount_quarter_cu,
                     debit_amount_year,
                     debit_amount_year_cu,
                     credit_amount_year,
                     credit_amount_year_cu,
                     opening_balance_cu,
                     closing_balance_cu,
                     opening_balance,
                     closing_balance,
                     create_time,
                     creator_id,
                     update_time,
                     updator_id,
                     serial_no)
                    SELECT rpt_seq_dap_article_blc.nextval,
                           entity_id,
                           book_code,
                           year_month,
                           root_account_id,
                           account_id,
                           article1,
                           article2,
                           article3,
                           article4,
                           article5,
                           article6,
                           article7,
                           article8,
                           article9,
                           article10,
                           article11,
                           article12,
                           article13,
                           article14,
                           article15,
                           article16,
                           article17,
                           currency_code,
                           currency_cu_code,
                           debit_amount,
                           debit_amount_cu,
                           credit_amount,
                           credit_amount_cu,
                           debit_amount_quarter,
                           debit_amount_quarter_cu,
                           credit_amount_quarter,
                           credit_amount_quarter_cu,
                           debit_amount_year,
                           debit_amount_year_cu,
                           credit_amount_year,
                           credit_amount_year_cu,
                           opening_balance_cu,
                           closing_balance_cu,
                           opening_balance,
                           closing_balance,
                           SYSDATE,
                           p_oper_id,
                           NULL,
                           NULL,
                           coalesce(v_serialno_src, 0) + 1 --目标表版本号 = 源数据版本号 + 1
                    FROM accuser.acc_buss_article_balance b
                    WHERE b.entity_id = p_entity_id
                      AND b.book_code = p_book_code
                      AND b.year_month = p_year_month
                      AND b.root_account_id IS NOT NULL;

                END IF;

            END IF;

            --查询当前业务期间详情id
            SELECT bpd.period_detail_id
            INTO v_period_detail_id
            FROM rpt_conf_bussperiod_detail bpd
                     LEFT JOIN rpt_conf_bussperiod p
                               ON bpd.buss_period_id = p.buss_period_id
                     LEFT JOIN rpt_conf_table t
                               ON bpd.biz_type_id = t.biz_type_id
            WHERE p.entity_id = p_entity_id
              AND p.year_month = p_year_month
              AND t.biz_code = 'DAP_ACC_ARTICLE_BALANCE';

            --修改rpt_conf_bussperiod_detail为已准备状态
            UPDATE rpt_conf_bussperiod_detail
            SET ready_state = '1',
                exec_result = 'success',
                task_time   = SYSDATE
            WHERE period_detail_id = v_period_detail_id;

            --同步业务期间
            rpt_pack_buss_period.proc_period_execution(p_entity_id, '1');

            --提交事务
            COMMIT;

        EXCEPTION
            WHEN OTHERS THEN

                --修改rpt_conf_bussperiod_detail为失败状态
                UPDATE rpt_conf_bussperiod_detail
                SET ready_state = '0',
                    exec_result = 'failed',
                    task_time   = SYSDATE
                WHERE period_detail_id = v_period_detail_id;

                --提交事务
                COMMIT;

                --抛出自定义异常信息 【会中断事务】
                v_log_msg := substr('报表同步科目专项表发生异常，请检查！：' || v_error_msg ||
                                    '；**SQLERRM: ' || SQLERRM || '**Error line: ' ||
                                    dbms_utility.format_error_backtrace() || '',
                                    1,
                                    4000);

                --往外层抛出异常信息
                raise_application_error(-20003, v_log_msg);

        END;
    END proc_article_balance;

    --同步计量输出数据
    PROCEDURE proc_atr_evaluate_result(p_entity_id  NUMBER,
                                       p_year_month_start VARCHAR2,
                                       p_year_month_end   VARCHAR2,
                                       p_oper_id    NUMBER) as
        v_serialno_src_count NUMBER;
        v_log_msg            VARCHAR2(4000); --日志信息
        v_error_msg          VARCHAR2(200); --异常信息
    begin

        --查看源数据表是否有数据,只要EXECUTION_STATE = S 的数据，代表已完成
        SELECT COUNT(1)
        INTO v_serialno_src_count
        FROM QTCUSER.qtc_buss_evaluate_main t
        WHERE t.entity_id = p_entity_id
          AND t.year_month between p_year_month_start and p_year_month_end
          and t.EXECUTION_STATE = 'S';

        dbms_output.put_line('计量输出源数据表数据量为:' || v_serialno_src_count);

        IF v_serialno_src_count = 0 THEN
            --源数据表无数据，不同步
            --抛出自定义异常信息 【会中断事务】
            v_error_msg := '计量输出源数据表数据量为:' || v_serialno_src_count || '，不执行同步：[' ||
                           p_entity_id || ']-[' || p_year_month_start || ']-[' || p_year_month_end || ']';
            --往外层抛出异常信息
            raise_application_error(-20002, v_error_msg);
        END IF;

        -- 备份目标表数据
        insert into rpt_dap_qtc_evaluatehis
        (dap_his_id,
         dap_id,
         evaluate_main_id,
         entity_id,
         model_def_id,
         currency_code,
         evaluate_date,
         year_month,
         version_no,
         confirm_is,
         confirm_id,
         confirm_time,
         business_model,
         business_direction,
         loa_code,
         portfolio_no,
         icg_no,
         unit_no,
         dev_period,
         var1,
         var2,
         var3,
         var4,
         var5,
         num1,
         num2,
         num3,
         num4,
         num5,
         num6,
         num7,
         num8,
         num9,
         num10,
         num11,
         num12,
         num13,
         num14,
         num15,
         num16,
         num17,
         num18,
         num19,
         num20,
         num21,
         num22,
         num23,
         num24,
         num25,
         num26,
         num27,
         num28,
         num29,
         num30,
         num31,
         num32,
         num33,
         num34,
         num35,
         num36,
         num37,
         num38,
         num39,
         num40,
         num41,
         num42,
         num43,
         num44,
         num45,
         num46,
         num47,
         num48,
         num49,
         num50,
         num51,
         num52,
         num53,
         num54,
         num55,
         num56,
         num57,
         num58,
         num59,
         num60,
         num61,
         num62,
         num63,
         num64,
         num65,
         num66,
         num67,
         num68,
         num69,
         num70,
         num71,
         num72,
         num73)
        select RPT_SEQ_DAP_QTC_EVALUATEHIS.nextval,
               t.dap_id,
               t.evaluate_main_id,
               t.entity_id,
               t.model_def_id,
               t.currency_code,
               t.evaluate_date,
               t.year_month,
               t.version_no,
               t.confirm_is,
               t.confirm_id,
               t.confirm_time,
               t.business_model,
               t.business_direction,
               t.loa_code,
               t.portfolio_no,
               t.icg_no,
               t.unit_no,
               t.dev_period,
               t.var1,
               t.var2,
               t.var3,
               t.var4,
               t.var5,
               t.num1,
               t.num2,
               t.num3,
               t.num4,
               t.num5,
               t.num6,
               t.num7,
               t.num8,
               t.num9,
               t.num10,
               t.num11,
               t.num12,
               t.num13,
               t.num14,
               t.num15,
               t.num16,
               t.num17,
               t.num18,
               t.num19,
               t.num20,
               t.num21,
               t.num22,
               t.num23,
               t.num24,
               t.num25,
               t.num26,
               t.num27,
               t.num28,
               t.num29,
               t.num30,
               t.num31,
               t.num32,
               t.num33,
               t.num34,
               t.num35,
               t.num36,
               t.num37,
               t.num38,
               t.num39,
               t.num40,
               t.num41,
               t.num42,
               t.num43,
               t.num44,
               t.num45,
               t.num46,
               t.num47,
               t.num48,
               t.num49,
               t.num50,
               t.num51,
               t.num52,
               t.num53,
               t.num54,
               t.num55,
               t.num56,
               t.num57,
               t.num58,
               t.num59,
               t.num60,
               t.num61,
               t.num62,
               t.num63,
               t.num64,
               t.num65,
               t.num66,
               t.num67,
               t.num68,
               t.num69,
               t.num70,
               t.num71,
               t.num72,
               t.num73
        from rpt_dap_qtc_evaluate t
        where t.ENTITY_ID = p_entity_id
          and t.year_month between p_year_month_start and p_year_month_end;

        -- 删除原表数据
        delete from rpt_dap_qtc_evaluate t
        where t.ENTITY_ID = p_entity_id
          and t.year_month between p_year_month_start and p_year_month_end;
        commit;
        -- 同步计量输出表数据
        insert into rpt_dap_qtc_evaluate
        (dap_id,
         evaluate_main_id,
         entity_id,
         model_def_id,
         currency_code,
         evaluate_date,
         year_month,
         version_no,
         confirm_is,
         confirm_id,
         confirm_time,
         business_model,
         business_direction,
         loa_code,
         portfolio_no,
         icg_no,
         unit_no,
         dev_period,
         var1,
         var2,
         var3,
         var4,
         var5,
         num1,
         num2,
         num3,
         num4,
         num5,
         num6,
         num7,
         num8,
         num9,
         num10,
         num11,
         num12,
         num13,
         num14,
         num15,
         num16,
         num17,
         num18,
         num19,
         num20,
         num21,
         num22,
         num23,
         num24,
         num25,
         num26,
         num27,
         num28,
         num29,
         num30,
         num31,
         num32,
         num33,
         num34,
         num35,
         num36,
         num37,
         num38,
         num39,
         num40,
         num41,
         num42,
         num43,
         num44,
         num45,
         num46,
         num47,
         num48,
         num49,
         num50,
         num51,
         num52,
         num53,
         num54,
         num55,
         num56,
         num57,
         num58,
         num59,
         num60,
         num61,
         num62,
         num63,
         num64,
         num65,
         num66,
         num67,
         num68,
         num69,
         num70,
         num71,
         num72,
         num73)
        select rpt_seq_dap_qtc_evaluate.nextval,
               t.evaluate_main_id,
               t.entity_id,
               t.model_def_id,
               t.currency_code,
               t.evaluate_date,
               t.year_month,
               t.version_no,
               t.confirm_is,
               t.confirm_id,
               t.confirm_time,
               t.business_model,
               t.business_direction,
               t.loa_code,
               r.portfolio_no,
               r.icg_no,
               r.unit_no,
               r.dev_period,
               r.var1,
               r.var2,
               r.var3,
               r.var4,
               r.var5,
               r.num1,
               r.num2,
               r.num3,
               r.num4,
               r.num5,
               r.num6,
               r.num7,
               r.num8,
               r.num9,
               r.num10,
               r.num11,
               r.num12,
               r.num13,
               r.num14,
               r.num15,
               r.num16,
               r.num17,
               r.num18,
               r.num19,
               r.num20,
               r.num21,
               r.num22,
               r.num23,
               r.num24,
               r.num25,
               r.num26,
               r.num27,
               r.num28,
               r.num29,
               r.num30,
               r.num31,
               r.num32,
               r.num33,
               r.num34,
               r.num35,
               r.num36,
               r.num37,
               r.num38,
               r.num39,
               r.num40,
               r.num41,
               r.num42,
               r.num43,
               r.num44,
               r.num45,
               r.num46,
               r.num47,
               r.num48,
               r.num49,
               r.num50,
               r.num51,
               r.num52,
               r.num53,
               r.num54,
               r.num55,
               r.num56,
               r.num57,
               r.num58,
               r.num59,
               r.num60,
               r.num61,
               r.num62,
               r.num63,
               r.num64,
               r.num65,
               r.num66,
               r.num67,
               r.num68,
               r.num69,
               r.num70,
               r.num71,
               r.num72,
               r.num73
        from QTCUSER.qtc_buss_evaluate_main t
                 left join QTCUSER.qtc_buss_evaluate_result r
                           on t.EVALUATE_MAIN_ID = r.EVALUATE_MAIN_ID
        where t.ENTITY_ID = p_entity_id
          and t.year_month between p_year_month_start and p_year_month_end
          and t.EXECUTION_STATE = 'S';
        --提交事务
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            --抛出自定义异常信息 【会中断事务】
            v_log_msg := substr('报表同步计量输出表发生异常，请检查！：' || v_error_msg ||
                                '；**SQLERRM: ' || SQLERRM || '**Error line: ' ||
                                dbms_utility.format_error_backtrace() || '',
                                1,
                                4000);
            --往外层抛出异常信息
            raise_application_error(-20003, v_log_msg);
    end proc_atr_evaluate_result;
END rpt_pack_data_sync;
/
