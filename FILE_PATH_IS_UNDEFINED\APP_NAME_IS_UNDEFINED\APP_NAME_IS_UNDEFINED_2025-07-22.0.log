[2m2025-07-22 15:10:09.406[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 15:15:09.500[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 15:20:09.504[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 15:25:09.506[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 15:30:09.508[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 15:35:09.516[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 15:35:54.150[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mo.m.s.mapper.ClassPathMapperScanner     [0;39m [2m:[0;39m No MyBatis mapper was found in '[com.ss.*.**.dao]' package. Please check your configuration.
[2m2025-07-22 15:35:54.165[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-07-22 15:35:54.199[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 32ms. Found 0 Redis repository interfaces.
[2m2025-07-22 15:35:55.987[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,f5a0227db1711f44,5859317ab09a72da,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-22 15:35:55.988[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,f5a0227db1711f44,5859317ab09a72da,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lrc.AtrBussPHLrcActionDao.fuzzySearchPage
[2m2025-07-22 15:35:55.988[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,f5a0227db1711f44,5859317ab09a72da,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrBussLrcActionVo":{"businessSourceCode":"DD","confirmIs":"","currencyCode":"","entityCode":"01 -- 海峡金桥财产保险股份有限公司","entityId":1,"loaCode":"","portfolioNo":"","riskClassCode":"","yearMonth":"202501"},"pageParam":{"countable":true,"offset":0,"page":0,"pageNumber":0,"pageSize":10,"paged":true,"size":10,"unpaged":false},"param1":{"$ref":"$.atrBussLrcActionVo"},"param2":{"$ref":"$.pageParam"}}
[2m2025-07-22 15:35:55.988[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,f5a0227db1711f44,5859317ab09a72da,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter2: {"businessSourceCode":"DD","confirmIs":"","currencyCode":"","entityCode":"01 -- 海峡金桥财产保险股份有限公司","entityId":1,"loaCode":"","portfolioNo":"","riskClassCode":"","yearMonth":"202501"}
[2m2025-07-22 15:35:55.989[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,f5a0227db1711f44,5859317ab09a72da,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-22 15:35:55.989[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,f5a0227db1711f44,5859317ab09a72da,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-22 15:35:55.989[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,f5a0227db1711f44,5859317ab09a72da,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql begin.
[2m2025-07-22 15:35:55.989[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,f5a0227db1711f44,5859317ab09a72da,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL CacheKey: Cache:-70957514
[2m2025-07-22 15:35:56.015[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,f5a0227db1711f44,5859317ab09a72da,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m CountSql: select count(1) FROM ('SQL') tmp_count
[2m2025-07-22 15:35:56.048[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,f5a0227db1711f44,5859317ab09a72da,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql end. Time cost: 59ms, total num： 3.
[2m2025-07-22 15:35:56.049[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,f5a0227db1711f44,5859317ab09a72da,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m ==>  Preparing: select a.ID,a.ACTION_NO, a.entity_id, a.YEAR_MONTH,a.business_source_code,a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name, u.user_name as creatorName FROM ATR_BUSS_LRC_ACTION a left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id left JOIN bpluser.bpl_saa_user u on a.creator_id = u.user_id WHERE a.entity_id = ? and a.YEAR_MONTH = ? and a.business_source_code = ? ORDER BY ID desc limit 10 
[2m2025-07-22 15:35:56.049[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,f5a0227db1711f44,5859317ab09a72da,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m ==> Parameters: 1(Long), 202501(String), DD(String)
[2m2025-07-22 15:35:56.116[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,f5a0227db1711f44,5859317ab09a72da,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-22 15:35:56.116[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,f5a0227db1711f44,5859317ab09a72da,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-1][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 128
[2m2025-07-22 15:40:09.528[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 15:45:09.530[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 15:50:09.535[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 15:55:09.543[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 16:00:09.556[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 16:05:09.557[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 16:10:09.559[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 16:15:09.561[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 16:20:09.563[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 16:25:10.580[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 16:30:10.594[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 16:33:54.657[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,03367a29fd79e6c5,5aae327a1b0d128e,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-22 16:33:54.657[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,03367a29fd79e6c5,5aae327a1b0d128e,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lrc.AtrBussPHLrcActionDao.fuzzySearchPage
[2m2025-07-22 16:33:54.660[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,03367a29fd79e6c5,5aae327a1b0d128e,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrBussLrcActionVo":{"businessSourceCode":"DD","confirmIs":"","currencyCode":"","entityCode":"01 -- 海峡金桥财产保险股份有限公司","entityId":1,"loaCode":"","portfolioNo":"","riskClassCode":"","yearMonth":"202501"},"pageParam":{"countable":true,"offset":0,"page":0,"pageNumber":0,"pageSize":10,"paged":true,"size":10,"unpaged":false},"param1":{"$ref":"$.atrBussLrcActionVo"},"param2":{"$ref":"$.pageParam"}}
[2m2025-07-22 16:33:54.660[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,03367a29fd79e6c5,5aae327a1b0d128e,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter2: {"businessSourceCode":"DD","confirmIs":"","currencyCode":"","entityCode":"01 -- 海峡金桥财产保险股份有限公司","entityId":1,"loaCode":"","portfolioNo":"","riskClassCode":"","yearMonth":"202501"}
[2m2025-07-22 16:33:54.663[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,03367a29fd79e6c5,5aae327a1b0d128e,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-22 16:33:54.663[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,03367a29fd79e6c5,5aae327a1b0d128e,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-22 16:33:54.663[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,03367a29fd79e6c5,5aae327a1b0d128e,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql begin.
[2m2025-07-22 16:33:54.664[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,03367a29fd79e6c5,5aae327a1b0d128e,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL CacheKey: Cache:-70957514
[2m2025-07-22 16:33:54.688[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,03367a29fd79e6c5,5aae327a1b0d128e,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m CountSql: select count(1) FROM ('SQL') tmp_count
[2m2025-07-22 16:33:54.726[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,03367a29fd79e6c5,5aae327a1b0d128e,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql end. Time cost: 63ms, total num： 3.
[2m2025-07-22 16:33:54.727[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,03367a29fd79e6c5,5aae327a1b0d128e,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m ==>  Preparing: select a.ID,a.ACTION_NO, a.entity_id, a.YEAR_MONTH,a.business_source_code,a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name, u.user_name as creatorName FROM ATR_BUSS_LRC_ACTION a left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id left JOIN bpluser.bpl_saa_user u on a.creator_id = u.user_id WHERE a.entity_id = ? and a.YEAR_MONTH = ? and a.business_source_code = ? ORDER BY ID desc limit 10 
[2m2025-07-22 16:33:54.728[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,03367a29fd79e6c5,5aae327a1b0d128e,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m ==> Parameters: 1(Long), 202501(String), DD(String)
[2m2025-07-22 16:33:54.762[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,03367a29fd79e6c5,5aae327a1b0d128e,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-22 16:33:54.763[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,03367a29fd79e6c5,5aae327a1b0d128e,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-4][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 103
[2m2025-07-22 16:34:28.661[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,474f6a01d62654dd,78ac394235925173,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-22 16:34:28.662[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,474f6a01d62654dd,78ac394235925173,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lrc.AtrBussPHLrcActionDao.fuzzySearchPage
[2m2025-07-22 16:34:28.662[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,474f6a01d62654dd,78ac394235925173,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrBussLrcActionVo":{"businessSourceCode":"DD","confirmIs":"","currencyCode":"","entityCode":"01 -- 海峡金桥财产保险股份有限公司","entityId":1,"loaCode":"","portfolioNo":"","riskClassCode":"","yearMonth":"202501"},"pageParam":{"countable":true,"offset":0,"page":0,"pageNumber":0,"pageSize":10,"paged":true,"size":10,"unpaged":false},"param1":{"$ref":"$.atrBussLrcActionVo"},"param2":{"$ref":"$.pageParam"}}
[2m2025-07-22 16:34:28.662[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,474f6a01d62654dd,78ac394235925173,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter2: {"businessSourceCode":"DD","confirmIs":"","currencyCode":"","entityCode":"01 -- 海峡金桥财产保险股份有限公司","entityId":1,"loaCode":"","portfolioNo":"","riskClassCode":"","yearMonth":"202501"}
[2m2025-07-22 16:34:28.663[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,474f6a01d62654dd,78ac394235925173,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-22 16:34:28.663[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,474f6a01d62654dd,78ac394235925173,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-22 16:34:28.663[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,474f6a01d62654dd,78ac394235925173,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql begin.
[2m2025-07-22 16:34:28.663[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,474f6a01d62654dd,78ac394235925173,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL CacheKey: Cache:-70957514
[2m2025-07-22 16:34:28.691[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,474f6a01d62654dd,78ac394235925173,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m CountSql: select count(1) FROM ('SQL') tmp_count
[2m2025-07-22 16:34:28.715[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,474f6a01d62654dd,78ac394235925173,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql end. Time cost: 52ms, total num： 3.
[2m2025-07-22 16:34:28.716[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,474f6a01d62654dd,78ac394235925173,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m ==>  Preparing: select a.ID,a.ACTION_NO, a.entity_id, a.YEAR_MONTH,a.business_source_code,a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name, u.user_name as creatorName FROM ATR_BUSS_LRC_ACTION a left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id left JOIN bpluser.bpl_saa_user u on a.creator_id = u.user_id WHERE a.entity_id = ? and a.YEAR_MONTH = ? and a.business_source_code = ? ORDER BY ID desc limit 10 
[2m2025-07-22 16:34:28.716[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,474f6a01d62654dd,78ac394235925173,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m ==> Parameters: 1(Long), 202501(String), DD(String)
[2m2025-07-22 16:34:28.747[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,474f6a01d62654dd,78ac394235925173,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-22 16:34:28.748[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,474f6a01d62654dd,78ac394235925173,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 86
[2m2025-07-22 16:34:35.410[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,5ee4c5e3e8ced9a8,c8090779318e1bc0,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-22 16:34:35.410[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,5ee4c5e3e8ced9a8,c8090779318e1bc0,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lrc.AtrBussPHLrcActionDao.fuzzySearchPage
[2m2025-07-22 16:34:35.410[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,5ee4c5e3e8ced9a8,c8090779318e1bc0,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrBussLrcActionVo":{"businessSourceCode":"DD","confirmIs":"","currencyCode":"","entityCode":"01 -- 海峡金桥财产保险股份有限公司","entityId":1,"loaCode":"","portfolioNo":"","riskClassCode":"","yearMonth":"202501"},"pageParam":{"countable":true,"offset":0,"page":0,"pageNumber":0,"pageSize":10,"paged":true,"size":10,"unpaged":false},"param1":{"$ref":"$.atrBussLrcActionVo"},"param2":{"$ref":"$.pageParam"}}
[2m2025-07-22 16:34:35.411[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,5ee4c5e3e8ced9a8,c8090779318e1bc0,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter2: {"businessSourceCode":"DD","confirmIs":"","currencyCode":"","entityCode":"01 -- 海峡金桥财产保险股份有限公司","entityId":1,"loaCode":"","portfolioNo":"","riskClassCode":"","yearMonth":"202501"}
[2m2025-07-22 16:34:35.412[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,5ee4c5e3e8ced9a8,c8090779318e1bc0,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-22 16:34:35.412[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,5ee4c5e3e8ced9a8,c8090779318e1bc0,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-22 16:34:35.412[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,5ee4c5e3e8ced9a8,c8090779318e1bc0,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql begin.
[2m2025-07-22 16:34:35.412[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,5ee4c5e3e8ced9a8,c8090779318e1bc0,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL CacheKey: Cache:-70957514
[2m2025-07-22 16:34:35.442[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,5ee4c5e3e8ced9a8,c8090779318e1bc0,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m CountSql: select count(1) FROM ('SQL') tmp_count
[2m2025-07-22 16:34:35.468[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,5ee4c5e3e8ced9a8,c8090779318e1bc0,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql end. Time cost: 56ms, total num： 3.
[2m2025-07-22 16:34:35.468[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,5ee4c5e3e8ced9a8,c8090779318e1bc0,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m ==>  Preparing: select a.ID,a.ACTION_NO, a.entity_id, a.YEAR_MONTH,a.business_source_code,a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name, u.user_name as creatorName FROM ATR_BUSS_LRC_ACTION a left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id left JOIN bpluser.bpl_saa_user u on a.creator_id = u.user_id WHERE a.entity_id = ? and a.YEAR_MONTH = ? and a.business_source_code = ? ORDER BY ID desc limit 10 
[2m2025-07-22 16:34:35.468[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,5ee4c5e3e8ced9a8,c8090779318e1bc0,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m ==> Parameters: 1(Long), 202501(String), DD(String)
[2m2025-07-22 16:34:35.501[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,5ee4c5e3e8ced9a8,c8090779318e1bc0,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-22 16:34:35.501[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,5ee4c5e3e8ced9a8,c8090779318e1bc0,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-9][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 91
[2m2025-07-22 16:34:39.893[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-22 16:34:39.893[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lrc.AtrBussPHLrcActionDao.findByid
[2m2025-07-22 16:34:39.893[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: 11
[2m2025-07-22 16:34:39.893[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-22 16:34:39.893[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-22 16:34:39.915[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m ==>  Preparing: select a.ID, a.ACTION_NO, a.entity_id, a.YEAR_MONTH, a.business_source_code,a.STATUS, a.CONFIRM_IS,a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name FROM ATR_BUSS_LRC_ACTION a left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id WHERE a.ID = ? 
[2m2025-07-22 16:34:39.915[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m ==> Parameters: 11(Long)
[2m2025-07-22 16:34:39.937[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.b.p.l.A.findByid              [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-22 16:34:39.938[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 45
[2m2025-07-22 16:34:39.948[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-22 16:34:39.948[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lrc.AtrBussPHLrcActionDao.findPortfolioData
[2m2025-07-22 16:34:39.949[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrBussLrcActionVo":{"actionNo":"20250722_145716_TRBUH","businessSourceCode":"DD","confirmIs":"0","createTime":1753167436000,"entityCName":"海峡金桥财产保险股份有限公司","entityCode":"01","entityEName":"","entityId":1,"entityLName":"","icgNo":"","id":11,"portfolioNo":"","riskClassCode":"","status":"S","updateTime":1753167757000,"yearMonth":"202501"},"pageParam":{"countable":true,"offset":0,"page":0,"pageNumber":0,"pageSize":10,"paged":true,"size":10,"unpaged":false},"param1":{"$ref":"$.atrBussLrcActionVo"},"param2":{"$ref":"$.pageParam"}}
[2m2025-07-22 16:34:39.949[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter2: {"actionNo":"20250722_145716_TRBUH","businessSourceCode":"DD","confirmIs":"0","createTime":1753167436000,"entityCName":"海峡金桥财产保险股份有限公司","entityCode":"01","entityEName":"","entityId":1,"entityLName":"","icgNo":"","id":11,"portfolioNo":"","riskClassCode":"","status":"S","updateTime":1753167757000,"yearMonth":"202501"}
[2m2025-07-22 16:34:39.963[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-22 16:34:39.963[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-22 16:34:39.963[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql begin.
[2m2025-07-22 16:34:39.963[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL CacheKey: Cache:1958497940
[2m2025-07-22 16:34:39.963[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m CountSql: select count(1) FROM ('SQL') tmp_count
[2m2025-07-22 16:34:39.992[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql end. Time cost: 29ms, total num： 11.
[2m2025-07-22 16:34:39.992[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.b.p.l.A.findPortfolioData     [0;39m [2m:[0;39m ==>  Preparing: select a.ID, a.ACTION_NO, a.entity_id, a.YEAR_MONTH, a.business_source_code, a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name, u.user_name as creatorName, b.PORTFOLIO_NO as PORTFOLIO_NO, b.ICG_NO as ICG_NO , b.RISK_CLASS_CODE as RISK_CLASS_CODE FROM ATR_BUSS_DD_LRC_G b left JOIN ATR_BUSS_LRC_ACTION a on a.ACTION_NO = b.ACTION_NO left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id left JOIN bpluser.bpl_saa_user u on a.creator_id = u.user_id WHERE a.action_no = ? ORDER BY b.ID desc limit 10 
[2m2025-07-22 16:34:39.994[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.b.p.l.A.findPortfolioData     [0;39m [2m:[0;39m ==> Parameters: 20250722_145716_TRBUH(String)
[2m2025-07-22 16:34:40.023[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.b.p.l.A.findPortfolioData     [0;39m [2m:[0;39m <==      Total: 10
[2m2025-07-22 16:34:40.024[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,08407d0b30659a69,a24f2064dc57ef7b,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 75
[2m2025-07-22 16:35:10.595[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 16:40:10.608[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 16:45:10.619[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 16:50:10.630[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 16:55:10.639[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 17:00:10.650[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 17:05:10.651[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 17:10:10.652[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 17:15:10.654[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 17:20:10.655[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 17:25:10.656[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 17:30:10.657[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 17:35:10.658[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 17:40:10.668[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 17:45:10.680[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 17:50:10.682[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 17:55:10.688[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 18:00:10.703[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 18:05:10.704[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 18:10:10.709[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 18:14:38.505[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9570c51967536768,4b94cd5a9bdc623f,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-22 18:14:38.505[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9570c51967536768,4b94cd5a9bdc623f,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lrc.AtrBussPHLrcActionDao.fuzzySearchPage
[2m2025-07-22 18:14:38.506[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9570c51967536768,4b94cd5a9bdc623f,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrBussLrcActionVo":{"businessSourceCode":"DD","confirmIs":"","currencyCode":"","entityCode":"01 -- 海峡金桥财产保险股份有限公司","entityId":1,"loaCode":"","portfolioNo":"","riskClassCode":"","yearMonth":"202501"},"pageParam":{"countable":true,"offset":0,"page":0,"pageNumber":0,"pageSize":10,"paged":true,"size":10,"unpaged":false},"param1":{"$ref":"$.atrBussLrcActionVo"},"param2":{"$ref":"$.pageParam"}}
[2m2025-07-22 18:14:38.506[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9570c51967536768,4b94cd5a9bdc623f,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter2: {"businessSourceCode":"DD","confirmIs":"","currencyCode":"","entityCode":"01 -- 海峡金桥财产保险股份有限公司","entityId":1,"loaCode":"","portfolioNo":"","riskClassCode":"","yearMonth":"202501"}
[2m2025-07-22 18:14:38.509[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9570c51967536768,4b94cd5a9bdc623f,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-22 18:14:38.509[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9570c51967536768,4b94cd5a9bdc623f,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-22 18:14:38.510[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9570c51967536768,4b94cd5a9bdc623f,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql begin.
[2m2025-07-22 18:14:38.510[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9570c51967536768,4b94cd5a9bdc623f,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL CacheKey: Cache:-70957514
[2m2025-07-22 18:14:38.533[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9570c51967536768,4b94cd5a9bdc623f,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m CountSql: select count(1) FROM ('SQL') tmp_count
[2m2025-07-22 18:14:38.564[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9570c51967536768,4b94cd5a9bdc623f,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql end. Time cost: 54ms, total num： 3.
[2m2025-07-22 18:14:38.565[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9570c51967536768,4b94cd5a9bdc623f,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m ==>  Preparing: select a.ID,a.ACTION_NO, a.entity_id, a.YEAR_MONTH,a.business_source_code,a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name, u.user_name as creatorName FROM ATR_BUSS_LRC_ACTION a left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id left JOIN bpluser.bpl_saa_user u on a.creator_id = u.user_id WHERE a.entity_id = ? and a.YEAR_MONTH = ? and a.business_source_code = ? ORDER BY ID desc limit 10 
[2m2025-07-22 18:14:38.565[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9570c51967536768,4b94cd5a9bdc623f,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m ==> Parameters: 1(Long), 202501(String), DD(String)
[2m2025-07-22 18:14:38.600[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9570c51967536768,4b94cd5a9bdc623f,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-22 18:14:38.600[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,9570c51967536768,4b94cd5a9bdc623f,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[io-7608-exec-10][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 94
[2m2025-07-22 18:15:10.721[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 18:19:28.709[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c97f1467862512b2,d7d83f5a9491de77,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-22 18:19:28.710[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c97f1467862512b2,d7d83f5a9491de77,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lrc.AtrBussPHLrcActionDao.fuzzySearchPage
[2m2025-07-22 18:19:28.710[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c97f1467862512b2,d7d83f5a9491de77,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrBussLrcActionVo":{"businessSourceCode":"DD","confirmIs":"","currencyCode":"","entityCode":"01 -- 海峡金桥财产保险股份有限公司","entityId":1,"loaCode":"","portfolioNo":"","riskClassCode":"","yearMonth":"202501"},"pageParam":{"countable":true,"offset":0,"page":0,"pageNumber":0,"pageSize":10,"paged":true,"size":10,"unpaged":false},"param1":{"$ref":"$.atrBussLrcActionVo"},"param2":{"$ref":"$.pageParam"}}
[2m2025-07-22 18:19:28.710[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c97f1467862512b2,d7d83f5a9491de77,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter2: {"businessSourceCode":"DD","confirmIs":"","currencyCode":"","entityCode":"01 -- 海峡金桥财产保险股份有限公司","entityId":1,"loaCode":"","portfolioNo":"","riskClassCode":"","yearMonth":"202501"}
[2m2025-07-22 18:19:28.710[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c97f1467862512b2,d7d83f5a9491de77,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-22 18:19:28.710[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c97f1467862512b2,d7d83f5a9491de77,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-22 18:19:28.710[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c97f1467862512b2,d7d83f5a9491de77,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql begin.
[2m2025-07-22 18:19:28.710[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c97f1467862512b2,d7d83f5a9491de77,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL CacheKey: Cache:-70957514
[2m2025-07-22 18:19:28.732[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c97f1467862512b2,d7d83f5a9491de77,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m CountSql: select count(1) FROM ('SQL') tmp_count
[2m2025-07-22 18:19:28.763[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c97f1467862512b2,d7d83f5a9491de77,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql end. Time cost: 53ms, total num： 3.
[2m2025-07-22 18:19:28.763[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c97f1467862512b2,d7d83f5a9491de77,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m ==>  Preparing: select a.ID,a.ACTION_NO, a.entity_id, a.YEAR_MONTH,a.business_source_code,a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name, u.user_name as creatorName FROM ATR_BUSS_LRC_ACTION a left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id left JOIN bpluser.bpl_saa_user u on a.creator_id = u.user_id WHERE a.entity_id = ? and a.YEAR_MONTH = ? and a.business_source_code = ? ORDER BY ID desc limit 10 
[2m2025-07-22 18:19:28.764[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c97f1467862512b2,d7d83f5a9491de77,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m ==> Parameters: 1(Long), 202501(String), DD(String)
[2m2025-07-22 18:19:28.790[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c97f1467862512b2,d7d83f5a9491de77,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-22 18:19:28.790[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,c97f1467862512b2,d7d83f5a9491de77,true][0;39m [35m29508[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 80
[2m2025-07-22 18:20:10.723[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 18:25:10.727[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 18:30:10.733[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 18:35:10.744[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 18:40:10.756[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-22 18:42:47.146[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[     Thread-164][0;39m [36mc.n.l.PollingServerListUpdater          [0;39m [2m:[0;39m Shutting down the Executor Pool for PollingServerListUpdater
[2m2025-07-22 18:42:47.347[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application SS-IFRS-ACTUARIAL with eureka with status DOWN
[2m2025-07-22 18:42:47.347[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Saw local status change event StatusChangeEvent [timestamp=1753180967347, current=DOWN, previous=UP]
[2m2025-07-22 18:42:47.348[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[nfoReplicator-0][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m DiscoveryClient_SS-IFRS-ACTUARIAL/localhost:ss-ifrs-actuarial:7608: registering service...
[2m2025-07-22 18:42:47.394[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[nfoReplicator-0][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m DiscoveryClient_SS-IFRS-ACTUARIAL/localhost:ss-ifrs-actuarial:7608 - registration status: 204
[2m2025-07-22 18:42:48.084[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.s.l.thread.SSThreadPoolTaskExecutor   [0;39m [2m:[0;39m Shutting down ExecutorService 'dataSliceThreadPool'
[2m2025-07-22 18:42:49.009[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.s.l.thread.SSThreadPoolTaskExecutor   [0;39m [2m:[0;39m Shutting down ExecutorService 'ruleThreadPool'
[2m2025-07-22 18:42:49.039[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.u.concurrent.ShutdownEnabledTimer   [0;39m [2m:[0;39m Shutdown hook removed for: NFLoadBalancer-PingTimer-SS-PLATFORM-COMMON
[2m2025-07-22 18:42:49.327[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.u.concurrent.ShutdownEnabledTimer   [0;39m [2m:[0;39m Exception caught (might be ok if at shutdown)

java.lang.IllegalStateException: Shutdown in progress
	at java.lang.ApplicationShutdownHooks.remove(ApplicationShutdownHooks.java:82)
	at java.lang.Runtime.removeShutdownHook(Runtime.java:239)
	at com.netflix.util.concurrent.ShutdownEnabledTimer.cancel(ShutdownEnabledTimer.java:70)
	at com.netflix.loadbalancer.BaseLoadBalancer.cancelPingTask(BaseLoadBalancer.java:632)
	at com.netflix.loadbalancer.BaseLoadBalancer.shutdown(BaseLoadBalancer.java:883)
	at com.netflix.loadbalancer.DynamicServerListLoadBalancer.shutdown(DynamicServerListLoadBalancer.java:285)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.invokeCustomDestroyMethod(DisposableBeanAdapter.java:339)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:273)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:978)
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:92)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:948)

[2m2025-07-22 18:42:49.364[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.u.concurrent.ShutdownEnabledTimer   [0;39m [2m:[0;39m Shutdown hook removed for: NFLoadBalancer-PingTimer-SS-PLATFORM-SCHEDULE
[2m2025-07-22 18:42:49.365[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.u.concurrent.ShutdownEnabledTimer   [0;39m [2m:[0;39m Exception caught (might be ok if at shutdown)

java.lang.IllegalStateException: Shutdown in progress
	at java.lang.ApplicationShutdownHooks.remove(ApplicationShutdownHooks.java:82)
	at java.lang.Runtime.removeShutdownHook(Runtime.java:239)
	at com.netflix.util.concurrent.ShutdownEnabledTimer.cancel(ShutdownEnabledTimer.java:70)
	at com.netflix.loadbalancer.BaseLoadBalancer.cancelPingTask(BaseLoadBalancer.java:632)
	at com.netflix.loadbalancer.BaseLoadBalancer.shutdown(BaseLoadBalancer.java:883)
	at com.netflix.loadbalancer.DynamicServerListLoadBalancer.shutdown(DynamicServerListLoadBalancer.java:285)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.invokeCustomDestroyMethod(DisposableBeanAdapter.java:339)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:273)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:978)
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:92)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:948)

[2m2025-07-22 18:42:49.375[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m IFRSHikariCPForATR - Shutdown initiated...
[2m2025-07-22 18:42:49.384[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m IFRSHikariCPForATR - Shutdown completed.
[2m2025-07-22 18:42:49.388[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Shutting down DiscoveryClient ...
[2m2025-07-22 18:42:52.396[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[scoveryClient-1][0;39m [36mc.netflix.discovery.TimedSupervisorTask [0;39m [2m:[0;39m task supervisor shutting down, can't accept the task
[2m2025-07-22 18:42:52.397[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[scoveryClient-0][0;39m [36mc.netflix.discovery.TimedSupervisorTask [0;39m [2m:[0;39m task supervisor shutting down, can't accept the task
[2m2025-07-22 18:42:52.405[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Unregistering ...
[2m2025-07-22 18:42:53.683[0;39m [APP_ENV_IS_UNDEFINED] [31mERROR [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[tbeatExecutor-0][0;39m [36mc.n.d.s.t.d.RedirectingEurekaHttpClient [0;39m [2m:[0;39m Request execution error. endpoint=DefaultEndpoint{ serviceUrl='**********************************************/eureka/}

com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused: connect
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.put(WebResource.java:529)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.sendHeartBeat(AbstractJerseyEurekaHttpClient.java:103)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.DiscoveryClient.renew(DiscoveryClient.java:874)
	at com.netflix.discovery.DiscoveryClient$HeartbeatThread.run(DiscoveryClient.java:1443)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 26 common frames omitted

[2m2025-07-22 18:42:53.683[0;39m [APP_ENV_IS_UNDEFINED] [31mERROR [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[freshExecutor-0][0;39m [36mc.n.d.s.t.d.RedirectingEurekaHttpClient [0;39m [2m:[0;39m Request execution error. endpoint=DefaultEndpoint{ serviceUrl='**********************************************/eureka/}

com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused: connect
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.get(WebResource.java:509)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.getApplicationsInternal(AbstractJerseyEurekaHttpClient.java:196)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.getDelta(AbstractJerseyEurekaHttpClient.java:172)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.DiscoveryClient.getAndUpdateDelta(DiscoveryClient.java:1115)
	at com.netflix.discovery.DiscoveryClient.fetchRegistry(DiscoveryClient.java:997)
	at com.netflix.discovery.DiscoveryClient.refreshRegistry(DiscoveryClient.java:1517)
	at com.netflix.discovery.DiscoveryClient$CacheRefreshThread.run(DiscoveryClient.java:1484)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 29 common frames omitted

[2m2025-07-22 18:42:53.684[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[tbeatExecutor-0][0;39m [36mc.n.d.s.t.d.RetryableEurekaHttpClient   [0;39m [2m:[0;39m Request execution failed with message: java.net.ConnectException: Connection refused: connect
[2m2025-07-22 18:42:53.684[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[freshExecutor-0][0;39m [36mc.n.d.s.t.d.RetryableEurekaHttpClient   [0;39m [2m:[0;39m Request execution failed with message: java.net.ConnectException: Connection refused: connect
[2m2025-07-22 18:42:54.530[0;39m [APP_ENV_IS_UNDEFINED] [31mERROR [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.d.s.t.d.RedirectingEurekaHttpClient [0;39m [2m:[0;39m Request execution error. endpoint=DefaultEndpoint{ serviceUrl='**********************************************/eureka/}

com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused: connect
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:953)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:929)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:948)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 42 common frames omitted

[2m2025-07-22 18:42:54.530[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.d.s.t.d.RetryableEurekaHttpClient   [0;39m [2m:[0;39m Request execution failed with message: java.net.ConnectException: Connection refused: connect
[2m2025-07-22 18:42:55.735[0;39m [APP_ENV_IS_UNDEFINED] [31mERROR [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[tbeatExecutor-0][0;39m [36mc.n.d.s.t.d.RedirectingEurekaHttpClient [0;39m [2m:[0;39m Request execution error. endpoint=DefaultEndpoint{ serviceUrl='**********************************************/eureka/}

com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused: connect
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.put(WebResource.java:529)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.sendHeartBeat(AbstractJerseyEurekaHttpClient.java:103)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:118)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:79)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.DiscoveryClient.renew(DiscoveryClient.java:874)
	at com.netflix.discovery.DiscoveryClient$HeartbeatThread.run(DiscoveryClient.java:1443)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 27 common frames omitted

[2m2025-07-22 18:42:55.735[0;39m [APP_ENV_IS_UNDEFINED] [31mERROR [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[freshExecutor-0][0;39m [36mc.n.d.s.t.d.RedirectingEurekaHttpClient [0;39m [2m:[0;39m Request execution error. endpoint=DefaultEndpoint{ serviceUrl='**********************************************/eureka/}

com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused: connect
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.get(WebResource.java:509)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.getApplicationsInternal(AbstractJerseyEurekaHttpClient.java:196)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.getDelta(AbstractJerseyEurekaHttpClient.java:172)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:118)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:79)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.DiscoveryClient.getAndUpdateDelta(DiscoveryClient.java:1115)
	at com.netflix.discovery.DiscoveryClient.fetchRegistry(DiscoveryClient.java:997)
	at com.netflix.discovery.DiscoveryClient.refreshRegistry(DiscoveryClient.java:1517)
	at com.netflix.discovery.DiscoveryClient$CacheRefreshThread.run(DiscoveryClient.java:1484)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 30 common frames omitted

[2m2025-07-22 18:42:55.735[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[tbeatExecutor-0][0;39m [36mc.n.d.s.t.d.RetryableEurekaHttpClient   [0;39m [2m:[0;39m Request execution failed with message: java.net.ConnectException: Connection refused: connect
[2m2025-07-22 18:42:55.735[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[freshExecutor-0][0;39m [36mc.n.d.s.t.d.RetryableEurekaHttpClient   [0;39m [2m:[0;39m Request execution failed with message: java.net.ConnectException: Connection refused: connect
[2m2025-07-22 18:42:55.746[0;39m [APP_ENV_IS_UNDEFINED] [31mERROR [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[tbeatExecutor-0][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m DiscoveryClient_SS-IFRS-ACTUARIAL/localhost:ss-ifrs-actuarial:7608 - was unable to send heartbeat!

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.DiscoveryClient.renew(DiscoveryClient.java:874)
	at com.netflix.discovery.DiscoveryClient$HeartbeatThread.run(DiscoveryClient.java:1443)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

[2m2025-07-22 18:42:55.746[0;39m [APP_ENV_IS_UNDEFINED] [31mERROR [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[freshExecutor-0][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m DiscoveryClient_SS-IFRS-ACTUARIAL/localhost:ss-ifrs-actuarial:7608 - was unable to refresh its cache! status = Cannot execute request on any known server

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$7.execute(EurekaHttpClientDecorator.java:152)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.getDelta(EurekaHttpClientDecorator.java:149)
	at com.netflix.discovery.DiscoveryClient.getAndUpdateDelta(DiscoveryClient.java:1115)
	at com.netflix.discovery.DiscoveryClient.fetchRegistry(DiscoveryClient.java:997)
	at com.netflix.discovery.DiscoveryClient.refreshRegistry(DiscoveryClient.java:1517)
	at com.netflix.discovery.DiscoveryClient$CacheRefreshThread.run(DiscoveryClient.java:1484)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

[2m2025-07-22 18:42:56.589[0;39m [APP_ENV_IS_UNDEFINED] [31mERROR [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.d.s.t.d.RedirectingEurekaHttpClient [0;39m [2m:[0;39m Request execution error. endpoint=DefaultEndpoint{ serviceUrl='**********************************************/eureka/}

com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused: connect
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:118)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:79)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:953)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:929)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:948)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 43 common frames omitted

[2m2025-07-22 18:42:56.590[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.d.s.t.d.RetryableEurekaHttpClient   [0;39m [2m:[0;39m Request execution failed with message: java.net.ConnectException: Connection refused: connect
[2m2025-07-22 18:42:56.590[0;39m [APP_ENV_IS_UNDEFINED] [31mERROR [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m DiscoveryClient_SS-IFRS-ACTUARIAL/localhost:ss-ifrs-actuarial:7608 - de-registration failedCannot execute request on any known server

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:953)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:929)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:948)

[2m2025-07-22 18:42:56.613[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m29508[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Completed shut down of DiscoveryClient
