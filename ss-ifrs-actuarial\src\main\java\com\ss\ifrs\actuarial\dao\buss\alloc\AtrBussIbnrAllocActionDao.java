/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2025-03-24 19:27:36
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao.buss.alloc;


import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussDiffIbnrAllocVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrAllocAction;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrAllocActionVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2025-03-24 19:27:36<br/>
 * Description: 分摊操作表 Dao类<br/>
 * Related Table Name: atr_buss_ibnr_alloc_action<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrBussIbnrAllocActionDao extends IDao<AtrBussIbnrAllocAction, Long> {

    Page<AtrBussIbnrAllocActionVo> fuzzySearchPage(AtrBussIbnrAllocActionVo atrBussIbnrAllocActionVo, Pageable pageParam);

    void updateConfirm(AtrBussIbnrAllocAction atrBussIbnrAllocAction);

    void cleanDuctIbnrEpData();

    void cleanDuctIbnrEpDataOut();

    void cleanDuctIbnrEpDataOutX();

    void cleanDuctIbnrImportData();

    Long countDDIbnrAllocImBalance(AtrBussIbnrAllocAction po);

    Long countOutIbnrAllocImBalance(AtrBussIbnrAllocAction po);

    Long countTIIbnrAllocImBalance(AtrBussIbnrAllocAction po);

    Long countCaseAllocImBalance(AtrBussIbnrAllocAction po);

    void reSetIbnrCashFlow(AtrConfBussPeriodVo atrConfBussPeriodVo);

    List<AtrBussDiffIbnrAllocVo> findDDTailDiffList(AtrBussIbnrAllocAction expAllocProcVo);

    void allocDDBalance(AtrBussDiffIbnrAllocVo expConfBussPeriodVo);

    List<AtrBussDiffIbnrAllocVo> findOutTailDiffList(AtrBussIbnrAllocAction expAllocProcVo);

    void allocOutBalance(AtrBussDiffIbnrAllocVo expConfBussPeriodVo);

}