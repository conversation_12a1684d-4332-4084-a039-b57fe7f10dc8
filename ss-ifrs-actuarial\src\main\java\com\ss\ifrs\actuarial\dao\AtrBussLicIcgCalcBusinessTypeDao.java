/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-02-06 14:38:40
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao;

import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLicCashFlowVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLicIcgAmountVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfCodeVo;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-06 14:38:40<br/>
 * Description: LIC计算结果明细(不同业务类型数据查询)Dao类<br/>
 * Related Table Name: ATR_BUSS_DD_LIC_ICG_CALC_DETAIL<br/>
 * <br/>
 */
@Mapper
public interface AtrBussLicIcgCalcBusinessTypeDao {

    List<AtrDapDrawVo> findDevNo(AtrBussLicCashFlowVo atrBussLicCashFlowVo);

    List<Map<String, Object>> findUltDev(AtrBussLicCashFlowVo atrBussLicCashFlowVo);

    List<Map<String, Object>> findLicDev(@Param("feeTypeList") List<AtrConfCodeVo> confCodeVoList, @Param("LicCashFlow") AtrBussLicCashFlowVo atrBussLicCashFlowVo);

    List<AtrDapDrawVo> findAccidentDevNo(AtrBussLicCashFlowVo atrBussLicCashFlowVo);

    List<Map<String, Object>> findClaimPattern(AtrBussLicCashFlowVo atrBussLicCashFlowVo);

    List<AtrBussLicIcgAmountVo> showExpectedClaimTotal(AtrDapDrawVo atrDapDrawVo);
}