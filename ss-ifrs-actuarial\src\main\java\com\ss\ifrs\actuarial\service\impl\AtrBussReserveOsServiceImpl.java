package com.ss.ifrs.actuarial.service.impl;

//import com.alibaba.excel.EasyExcel;
//import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSONObject;
import com.ss.ifrs.actuarial.dao.buss.reserve.AtrBussReserveOsDao;
import com.ss.ifrs.actuarial.dao.buss.reserve.AtrBussReserveOsDetailDao;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveOs;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveOsDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveOsExcelVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveOsVo;
import com.ss.ifrs.actuarial.service.AtrBussReserveOsService;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ClassUtil;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName AtrBussReserveOsServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/2/11
 **/
@Service
public class AtrBussReserveOsServiceImpl implements AtrBussReserveOsService {

    @Autowired
    AtrBussReserveOsDao atrBussReserveOsDao;
    @Autowired
    AtrBussReserveOsDetailDao atrBussReserveOsDetailDao;

    @Override
    @Async("ruleThreadPool")
    @Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = RuntimeException.class)
    public void reserveOsExtract(AtrBussReserveOsVo atrBussReserveOsVo, Long userId){
        atrBussReserveOsVo.setCreatorId(userId);
        if(null != atrBussReserveOsVo && null != atrBussReserveOsVo.getEntityId()
                && null != atrBussReserveOsVo.getDrawStartDate() && null != atrBussReserveOsVo.getDrawEndDate()){
            AtrBussReserveOs po = atrBussReserveOsDao.findByVo(atrBussReserveOsVo);
            if(null != po){
                atrBussReserveOsDetailDao.deleteByReserveOsId(po.getReserveOsId());
                atrBussReserveOsDao.deleteById(po.getReserveOsId());
            }

            AtrBussReserveOs atrBussReserveOs = ClassUtil.convert(atrBussReserveOsVo, AtrBussReserveOs.class);
            SimpleDateFormat dateFormatVersion = new SimpleDateFormat("yyyyMMddHHmmss");
            SimpleDateFormat dateFormatMonth= new SimpleDateFormat("yyyyMM");
            atrBussReserveOs.setCreatorId(userId);
            atrBussReserveOs.setCreateTime(new Date());
            atrBussReserveOs.setDataSource("0");
            atrBussReserveOs.setConfirmIs("0");
            atrBussReserveOs.setVersionNo(dateFormatVersion.format(new Date()));
            atrBussReserveOs.setYearMonth(dateFormatMonth.format(atrBussReserveOsVo.getDrawEndDate()));
            atrBussReserveOsDao.save(atrBussReserveOs);

            Map<String, Object> param = new HashMap<>(2);
            /*param.put("entityId", atrBussReserveOsVo.getEntityId());
            param.put("riskClass", atrBussReserveOsVo.getRiskClassCode());
            param.put("drawStartDate", atrBussReserveOsVo.getDrawStartDate());
            param.put("drawEndDate", atrBussReserveOsVo.getDrawEndDate());
            param.put("userId", userId);*/

            param.put("reserveOsId", atrBussReserveOs.getReserveOsId());
            atrBussReserveOsDao.reserveOsExtract(param);
        }
    }

    @Override
    public Page<AtrBussReserveOsVo> searchPage(AtrBussReserveOsVo atrBussReserveOsVo, Pageable pageParam) {
        Page<AtrBussReserveOsVo> atrBussReserveOsVoPage = atrBussReserveOsDao.fuzzySearchPage(atrBussReserveOsVo, pageParam);
        return  atrBussReserveOsVoPage;
    }

    @Override
    public Page<AtrBussReserveOsDetailVo> findForDataTables(AtrBussReserveOsVo atrBussReserveOsVo, Pageable pageParam) {
        if (ObjectUtils.isNotEmpty(atrBussReserveOsVo.getColumnList())) {
            String columnSql = String.join(",", atrBussReserveOsVo.getColumnList());
            atrBussReserveOsVo.setColumnSql(columnSql + ",");
        }

        Page<AtrBussReserveOsDetailVo> atrBussReserveOsVoPage = atrBussReserveOsDetailDao.fuzzySearchPage(atrBussReserveOsVo, pageParam);
        return atrBussReserveOsVoPage;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor= UnexpectedRollbackException.class)
    public void confirm(AtrBussReserveOsVo atrBussReserveOsVo, Long userId) {
        if(ObjectUtils.isEmpty(atrBussReserveOsVo.getReserveOsId()) && checkIsConfirm(atrBussReserveOsVo)) {
            return;
        }
        AtrBussReserveOs po =  atrBussReserveOsDao.findById(atrBussReserveOsVo.getReserveOsId());
        po.setConfirmIs("1");
        po.setConfirmId(userId);
        po.setConfirmTime(new Date());
        po.setUpdatorId(userId);
        po.setUpdateTime(new Date());
        atrBussReserveOsDao.updateById(po);
    }

    @Override
    public void downloadDataFile(HttpServletResponse response, AtrBussReserveOsVo atrBussReserveOsVo) throws IOException {

        List<AtrBussReserveOsExcelVo> target = new ArrayList<AtrBussReserveOsExcelVo>();
        AtrBussReserveOsExcelVo userEntity = new AtrBussReserveOsExcelVo();
        userEntity.setName("小鱼儿");
        userEntity.setAccount("*************");
        userEntity.setAddress("深圳");
        userEntity.setAge(18);
        userEntity.setOccupation("码农");
        target.add(userEntity);

        String fileName = "temp";
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
//        EasyExcel.write(response.getOutputStream(), AtrBussReserveOsExcelVo.class).excelType(ExcelTypeEnum.XLS).autoCloseStream(Boolean.TRUE)
//                .sheet("test_report").doWrite(target);
    }

    @Override
    public List<AtrBussReserveOsVo> findOsDownloadList(AtrBussReserveOsVo atrBussReserveOsVo){
        if (ObjectUtils.isNotEmpty(atrBussReserveOsVo.getColumnList())) {
            String columnSql = String.join(",", atrBussReserveOsVo.getColumnList());
            atrBussReserveOsVo.setColumnSql(columnSql + ",");
        }
        return atrBussReserveOsDetailDao.findOsDetailList(atrBussReserveOsVo);
    }

    @Override
    public Boolean checkIsConfirm(AtrBussReserveOsVo atrBussReserveOsVo) {
        Map<String, Object> param = new HashMap<>();
        param.put("entityId", atrBussReserveOsVo.getEntityId());
        param.put("yearMonth", atrBussReserveOsVo.getYearMonth());
        Long count = atrBussReserveOsDao.reserveOsConfirm(param);
        if(ObjectUtils.isNotEmpty(count) && count >0) {
            return true;
        }
        return false;
    }

    @Override
    public Map<String, Object> getDownloadAllStaffWorkers(JSONObject jsonParam) throws Exception {
        //导出数据
        HSSFWorkbook workbook = new HSSFWorkbook();
        //声明一个表格
        HSSFSheet sheet = workbook.createSheet("员工信息表");
        //设置表格的默认宽度
        sheet.setDefaultColumnWidth((short) 30);
        //需要导出的文件名字
        String fileName = "os_reserve" + ".xls";
        int rowNum = 1;
        //导出Excel 表格头部
        String [] headers = {"姓名","年龄","职业","账户","地址"};
        HSSFRow row = sheet.createRow(0);
        //在Excel中添加表头
        for (int i = 0;i<headers.length;i++){
            HSSFCell cell = row.createCell(i);
            HSSFRichTextString text = new HSSFRichTextString(headers[i]);
            cell.setCellValue(text);
        }
        HSSFRow row1 = null;
        List<AtrBussReserveOsExcelVo> osExcelVoList = new ArrayList<AtrBussReserveOsExcelVo>();
        for(AtrBussReserveOsExcelVo atrBussReserveOsExcelVo : osExcelVoList){
            row1 = sheet.createRow(rowNum);
            row1.createCell(0).setCellValue(atrBussReserveOsExcelVo.getName());
            row1.createCell(1).setCellValue(atrBussReserveOsExcelVo.getAge());
            row1.createCell(2).setCellValue(atrBussReserveOsExcelVo.getOccupation());
            row1.createCell(3).setCellValue(atrBussReserveOsExcelVo.getAccount());
            row1.createCell(3).setCellValue(atrBussReserveOsExcelVo.getAddress());
        }

        /*byte[] fileByte = workbook.getBytes();*/
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        workbook.write(os);
        byte[] fileByte = os.toByteArray();
        /*ByteArrayOutputStream os = new ByteArrayOutputStream();
        workbook.write(os);
        os.toByteArray();

        workbook.close();
        String httpUrl = null;
        String filePath = null;
        String url = null;
        try {
            url = FastDFSUtils.getDoUpload(sessionToken,fileName,os.toByteArray());
        }catch (Exception e){
            errLog("getDownloadAllStaffWorkers",ErrorCodeMsg.UNKNOWN_ERROR.getMessage(),"获取文件服务下载地址为空");
            throw new AttemptException(ErrorCodeMsg.UNKNOWN_ERROR,"获取文件服务下载地址为空");
        }
        if (StringUtils.isNotEmpty(url)) {
            JSONObject jsonObject = JSONObject.parseObject(url);
            String data = jsonObject.getString("data");
            JSONObject dataObject = JSONObject.parseObject(data);
            httpUrl = dataObject.getString("httpUrl");
            filePath = dataObject.getString("filePath");
            if (StringUtils.isEmpty(filePath)) {
                errLog("getDownloadTrainStuEnroll", ErrorCodeMsg.UNKNOWN_ERROR.getMessage(), "获取文件服务下载地址为空2");
                throw new AttemptException(ErrorCodeMsg.UNKNOWN_ERROR, "获取文件服务下载地址为空2");
            }
        }
        System.out.println(url);
        Map<String,Object> map = new HashMap<>();
        map.put("url",fileServer + "/" + filePath);
        map.put("attname",fileName);*/

        Map<String,Object> map = new HashMap<>();
        map.put("osFileByte", fileByte);
        return map;
    }

    @Override
    public HSSFWorkbook excelDownload(HttpServletResponse response) throws IOException{
        //表头数据
        //String[] header = {"姓名", "工号", "性别", "出生日期", "身份证号码", "婚姻状况","民族","籍贯","政治面貌","电子邮件","电话号码","联系地址","所属部门","职位","职称","聘用形式","入职日期","转正日期","合同起始日期","合同截止日期","合同期限","最高学历"};
        //String[] header = {"姓名", "年龄", "职业", "账户", "地址"};
        String[] header = {"Name", "Age", "Occupation", "Account", "Address"};

        //声明一个工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();
        //生成一个表格，设置表格名称为"temp"
        HSSFSheet sheet = workbook.createSheet("temp");
        //设置表格列宽度为10个字节
        sheet.setDefaultColumnWidth(10);
        //创建标题的显示样式
        HSSFCellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setFillForegroundColor(IndexedColors.YELLOW.index);
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        //创建第一行表头
        HSSFRow headRow = sheet.createRow(0);

        //遍历添加表头(下面模拟遍历学生，也是同样的操作过程)
        for (int i = 0; i < header.length; i++) {
            //创建一个单元格
            HSSFCell cell = headRow.createCell(i);
            //创建一个内容对象
            HSSFRichTextString text = new HSSFRichTextString(header[i]);
            //将内容对象的文字内容写入到单元格中
            cell.setCellValue(text);
            cell.setCellStyle(headerStyle);
        }

        //获取所有的信息
        List<AtrBussReserveOsExcelVo> list = new ArrayList<AtrBussReserveOsExcelVo>();
        AtrBussReserveOsExcelVo osExcelVo = new AtrBussReserveOsExcelVo();
        osExcelVo.setName("small");
        osExcelVo.setAccount("*************");
        osExcelVo.setAddress("shenzhen");
        osExcelVo.setAge(18);
        osExcelVo.setOccupation("coder");
        list.add(osExcelVo);

        for(int i=0;i<list.size();i++){
            //创建一行
            HSSFRow row1 = sheet.createRow(i+1);
            row1.createCell(0).setCellValue(new HSSFRichTextString(list.get(i).getName()));
            row1.createCell(1).setCellValue(new HSSFRichTextString(list.get(i).getAge().toString()));
            row1.createCell(2).setCellValue(new HSSFRichTextString(list.get(i).getOccupation().toString()));
            row1.createCell(3).setCellValue(new HSSFRichTextString(list.get(i).getAccount().toString()));
            row1.createCell(4).setCellValue(new HSSFRichTextString(list.get(i).getAddress().toString()));

            /*//第一列创建并赋值
            row1.createCell(1).setCellValue(new HSSFRichTextString(list.get(i).getId().toString()));
            //第4列创建并赋值 date类型
            if(emps.get(i).getBirthday() != null){
                row1.createCell(3).setCellValue(new HSSFRichTextString(dateFormat.format(emps.get(i).getBirthday())));
            }
            //第15列创建并赋值
            if(emps.get(i).getEngageForm() != null){
                row1.createCell(15).setCellValue(new HSSFRichTextString(emps.get(i).getEngageForm()));
            }*/

        }

        return workbook;
    }

    @Override
    public void delete(Long reserveOsId, Long userId) {
        AtrBussReserveOs po =  atrBussReserveOsDao.findById(reserveOsId);
        if(ObjectUtils.isNotEmpty(po) && CommonConstant.VersionStatus.PENDING.equals(po.getConfirmIs())) {
            atrBussReserveOsDetailDao.deleteByReserveOsId(reserveOsId);
            atrBussReserveOsDao.deleteById(reserveOsId);
        }
    }

}
