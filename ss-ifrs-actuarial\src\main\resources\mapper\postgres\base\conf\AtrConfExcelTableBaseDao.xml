<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2022-08-19 14:51:44 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.conf.AtrConfExcelTableDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfExcelTable">
    <id column="biz_type_id" property="bizTypeId" jdbcType="NUMERIC" />
    <result column="biz_code" property="bizCode" jdbcType="VARCHAR" />
    <result column="type_e_name" property="typeEName" jdbcType="VARCHAR" />
    <result column="type_l_name" property="typeLName" jdbcType="VARCHAR" />
    <result column="type_c_name" property="typeCName" jdbcType="VARCHAR" />
    <result column="sys_id" property="sysId" jdbcType="NUMERIC" />
    <result column="type_group" property="typeGroup" jdbcType="VARCHAR" />
    <result column="redirect_to_url" property="redirectToUrl" jdbcType="VARCHAR" />
    <result column="valid_is" property="validIs" jdbcType="VARCHAR" />
    <result column="display_no" property="displayNo" jdbcType="NUMERIC" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="creator_id" property="creatorId" jdbcType="NUMERIC" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="updator_id" property="updatorId" jdbcType="NUMERIC" />
    <result column="original_is" property="originalIs" jdbcType="VARCHAR" />
    <result column="upload_is" property="uploadIs" jdbcType="VARCHAR" />
    <result column="seq_name" property="seqName" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    biz_type_id, biz_code, type_e_name, type_l_name, type_c_name, sys_id, type_group,
    redirect_to_url, valid_is, display_no, create_time, creator_id, update_time, updator_id, 
    original_is, upload_is, seq_name
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="bizTypeId != null ">
          and biz_type_id = #{bizTypeId,jdbcType=NUMERIC}
      </if>
      <if test="bizCode != null and bizCode != ''">
          and biz_code = #{bizCode,jdbcType=VARCHAR}
      </if>
      <if test="typeEName != null and typeEName != ''">
          and type_e_name = #{typeEName,jdbcType=VARCHAR}
      </if>
      <if test="typeLName != null and typeLName != ''">
          and type_l_name = #{typeLName,jdbcType=VARCHAR}
      </if>
      <if test="typeCName != null and typeCName != ''">
          and type_c_name = #{typeCName,jdbcType=VARCHAR}
      </if>
      <if test="sysId != null ">
          and sys_id = #{sysId,jdbcType=NUMERIC}
      </if>
      <if test="typeGroup != null and typeGroup != ''">
          and type_group = #{typeGroup,jdbcType=VARCHAR}
      </if>
      <if test="redirectToUrl != null and redirectToUrl != ''">
          and redirect_to_url = #{redirectToUrl,jdbcType=VARCHAR}
      </if>
      <if test="validIs != null and validIs != ''">
          and valid_is = #{validIs,jdbcType=VARCHAR}
      </if>
      <if test="displayNo != null ">
          and display_no = #{displayNo,jdbcType=NUMERIC}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creatorId != null ">
          and creator_id = #{creatorId,jdbcType=NUMERIC}
      </if>
      <if test="updateTime != null ">
          and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and updator_id = #{updatorId,jdbcType=NUMERIC}
      </if>
      <if test="originalIs != null and originalIs != ''">
          and original_is = #{originalIs,jdbcType=VARCHAR}
      </if>
      <if test="uploadIs != null and uploadIs != ''">
          and upload_is = #{uploadIs,jdbcType=VARCHAR}
      </if>
      <if test="seqName != null and seqName != ''">
          and seq_name = #{seqName,jdbcType=VARCHAR}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.bizTypeId != null ">
          and biz_type_id = #{condition.bizTypeId,jdbcType=NUMERIC}
      </if>
      <if test="condition.bizCode != null and condition.bizCode != ''">
          and biz_code = #{condition.bizCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.typeEName != null and condition.typeEName != ''">
          and type_e_name = #{condition.typeEName,jdbcType=VARCHAR}
      </if>
      <if test="condition.typeLName != null and condition.typeLName != ''">
          and type_l_name = #{condition.typeLName,jdbcType=VARCHAR}
      </if>
      <if test="condition.typeCName != null and condition.typeCName != ''">
          and type_c_name = #{condition.typeCName,jdbcType=VARCHAR}
      </if>
      <if test="condition.sysId != null ">
          and sys_id = #{condition.sysId,jdbcType=NUMERIC}
      </if>
      <if test="condition.typeGroup != null and condition.typeGroup != ''">
          and type_group = #{condition.typeGroup,jdbcType=VARCHAR}
      </if>
      <if test="condition.redirectToUrl != null and condition.redirectToUrl != ''">
          and redirect_to_url = #{condition.redirectToUrl,jdbcType=VARCHAR}
      </if>
      <if test="condition.validIs != null and condition.validIs != ''">
          and valid_is = #{condition.validIs,jdbcType=VARCHAR}
      </if>
      <if test="condition.displayNo != null ">
          and display_no = #{condition.displayNo,jdbcType=NUMERIC}
      </if>
      <if test="condition.createTime != null ">
          and create_time = #{condition.createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.creatorId != null ">
          and creator_id = #{condition.creatorId,jdbcType=NUMERIC}
      </if>
      <if test="condition.updateTime != null ">
          and update_time = #{condition.updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.updatorId != null ">
          and updator_id = #{condition.updatorId,jdbcType=NUMERIC}
      </if>
      <if test="condition.originalIs != null and condition.originalIs != ''">
          and original_is = #{condition.originalIs,jdbcType=VARCHAR}
      </if>
      <if test="condition.uploadIs != null and condition.uploadIs != ''">
          and upload_is = #{condition.uploadIs,jdbcType=VARCHAR}
      </if>
      <if test="condition.seqName != null and condition.seqName != ''">
          and seq_name = #{condition.seqName,jdbcType=VARCHAR}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="bizTypeId != null ">
          and biz_type_id = #{bizTypeId,jdbcType=NUMERIC}
      </if>
      <if test="bizCode != null and bizCode != ''">
          and biz_code = #{bizCode,jdbcType=VARCHAR}
      </if>
      <if test="typeEName != null and typeEName != ''">
          and type_e_name = #{typeEName,jdbcType=VARCHAR}
      </if>
      <if test="typeLName != null and typeLName != ''">
          and type_l_name = #{typeLName,jdbcType=VARCHAR}
      </if>
      <if test="typeCName != null and typeCName != ''">
          and type_c_name = #{typeCName,jdbcType=VARCHAR}
      </if>
      <if test="sysId != null ">
          and sys_id = #{sysId,jdbcType=NUMERIC}
      </if>
      <if test="typeGroup != null and typeGroup != ''">
          and type_group = #{typeGroup,jdbcType=VARCHAR}
      </if>
      <if test="redirectToUrl != null and redirectToUrl != ''">
          and redirect_to_url = #{redirectToUrl,jdbcType=VARCHAR}
      </if>
      <if test="validIs != null and validIs != ''">
          and valid_is = #{validIs,jdbcType=VARCHAR}
      </if>
      <if test="displayNo != null ">
          and display_no = #{displayNo,jdbcType=NUMERIC}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creatorId != null ">
          and creator_id = #{creatorId,jdbcType=NUMERIC}
      </if>
      <if test="updateTime != null ">
          and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and updator_id = #{updatorId,jdbcType=NUMERIC}
      </if>
      <if test="originalIs != null and originalIs != ''">
          and original_is = #{originalIs,jdbcType=VARCHAR}
      </if>
      <if test="uploadIs != null and uploadIs != ''">
          and upload_is = #{uploadIs,jdbcType=VARCHAR}
      </if>
      <if test="seqName != null and seqName != ''">
          and seq_name = #{seqName,jdbcType=VARCHAR}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_excel_table
    where biz_type_id = #{bizTypeId,jdbcType=NUMERIC}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_excel_table
    where biz_type_id in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_excel_table
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfExcelTable">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_excel_table
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_excel_table
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="biz_type_id" keyProperty="bizTypeId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfExcelTable">
    insert into atr_conf_excel_table
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizTypeId != null">
        biz_type_id,
      </if>
      <if test="bizCode != null">
        biz_code,
      </if>
      <if test="typeEName != null">
        type_e_name,
      </if>
      <if test="typeLName != null">
        type_l_name,
      </if>
      <if test="typeCName != null">
        type_c_name,
      </if>
      <if test="sysId != null">
        sys_id,
      </if>
      <if test="typeGroup != null">
        type_group,
      </if>
      <if test="redirectToUrl != null">
        redirect_to_url,
      </if>
      <if test="validIs != null">
        valid_is,
      </if>
      <if test="displayNo != null">
        display_no,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updatorId != null">
        updator_id,
      </if>
      <if test="originalIs != null">
        original_is,
      </if>
      <if test="uploadIs != null">
        upload_is,
      </if>
      <if test="seqName != null">
        seq_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizTypeId != null">
        #{bizTypeId,jdbcType=NUMERIC},
      </if>
      <if test="bizCode != null">
        #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="typeEName != null">
        #{typeEName,jdbcType=VARCHAR},
      </if>
      <if test="typeLName != null">
        #{typeLName,jdbcType=VARCHAR},
      </if>
      <if test="typeCName != null">
        #{typeCName,jdbcType=VARCHAR},
      </if>
      <if test="sysId != null">
        #{sysId,jdbcType=NUMERIC},
      </if>
      <if test="typeGroup != null">
        #{typeGroup,jdbcType=VARCHAR},
      </if>
      <if test="redirectToUrl != null">
        #{redirectToUrl,jdbcType=VARCHAR},
      </if>
      <if test="validIs != null">
        #{validIs,jdbcType=VARCHAR},
      </if>
      <if test="displayNo != null">
        #{displayNo,jdbcType=NUMERIC},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=NUMERIC},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        #{updatorId,jdbcType=NUMERIC},
      </if>
      <if test="originalIs != null">
        #{originalIs,jdbcType=VARCHAR},
      </if>
      <if test="uploadIs != null">
        #{uploadIs,jdbcType=VARCHAR},
      </if>
      <if test="seqName != null">
        #{seqName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert into atr_conf_excel_table
     (biz_type_id, biz_code, type_e_name, 
      type_l_name, type_c_name, sys_id,
      type_group, redirect_to_url, valid_is, 
      display_no, create_time, creator_id, 
      update_time, updator_id, original_is, 
      upload_is, seq_name)
     values 
    <foreach collection="list" item="item" index="index" separator=",">
       (#{item.bizTypeId,jdbcType=NUMERIC}, #{item.bizCode,jdbcType=VARCHAR}, #{item.typeEName,jdbcType=VARCHAR}, 
        #{item.typeLName,jdbcType=VARCHAR}, #{item.typeCName,jdbcType=VARCHAR}, #{item.sysId,jdbcType=NUMERIC},
        #{item.typeGroup,jdbcType=VARCHAR}, #{item.redirectToUrl,jdbcType=VARCHAR}, #{item.validIs,jdbcType=VARCHAR}, 
        #{item.displayNo,jdbcType=NUMERIC}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.creatorId,jdbcType=NUMERIC}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updatorId,jdbcType=NUMERIC}, #{item.originalIs,jdbcType=VARCHAR}, 
        #{item.uploadIs,jdbcType=VARCHAR}, #{item.seqName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfExcelTable">
    update atr_conf_excel_table
    <set>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="typeEName != null">
        type_e_name = #{typeEName,jdbcType=VARCHAR},
      </if>
      <if test="typeLName != null">
        type_l_name = #{typeLName,jdbcType=VARCHAR},
      </if>
      <if test="typeCName != null">
        type_c_name = #{typeCName,jdbcType=VARCHAR},
      </if>
      <if test="sysId != null">
        sys_id = #{sysId,jdbcType=NUMERIC},
      </if>
      <if test="typeGroup != null">
        type_group = #{typeGroup,jdbcType=VARCHAR},
      </if>
      <if test="redirectToUrl != null">
        redirect_to_url = #{redirectToUrl,jdbcType=VARCHAR},
      </if>
      <if test="validIs != null">
        valid_is = #{validIs,jdbcType=VARCHAR},
      </if>
      <if test="displayNo != null">
        display_no = #{displayNo,jdbcType=NUMERIC},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=NUMERIC},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        updator_id = #{updatorId,jdbcType=NUMERIC},
      </if>
      <if test="originalIs != null">
        original_is = #{originalIs,jdbcType=VARCHAR},
      </if>
      <if test="uploadIs != null">
        upload_is = #{uploadIs,jdbcType=VARCHAR},
      </if>
      <if test="seqName != null">
        seq_name = #{seqName,jdbcType=VARCHAR},
      </if>
    </set>
    where biz_type_id = #{bizTypeId,jdbcType=NUMERIC}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfExcelTable">
    update atr_conf_excel_table
    <set>
      <if test="record.bizCode != null">
        biz_code = #{record.bizCode,jdbcType=VARCHAR},
      </if>
      <if test="record.typeEName != null">
        type_e_name = #{record.typeEName,jdbcType=VARCHAR},
      </if>
      <if test="record.typeLName != null">
        type_l_name = #{record.typeLName,jdbcType=VARCHAR},
      </if>
      <if test="record.typeCName != null">
        type_c_name = #{record.typeCName,jdbcType=VARCHAR},
      </if>
      <if test="record.sysId != null">
        sys_id = #{record.sysId,jdbcType=NUMERIC},
      </if>
      <if test="record.typeGroup != null">
        type_group = #{record.typeGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.redirectToUrl != null">
        redirect_to_url = #{record.redirectToUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.validIs != null">
        valid_is = #{record.validIs,jdbcType=VARCHAR},
      </if>
      <if test="record.displayNo != null">
        display_no = #{record.displayNo,jdbcType=NUMERIC},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=NUMERIC},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatorId != null">
        updator_id = #{record.updatorId,jdbcType=NUMERIC},
      </if>
      <if test="record.originalIs != null">
        original_is = #{record.originalIs,jdbcType=VARCHAR},
      </if>
      <if test="record.uploadIs != null">
        upload_is = #{record.uploadIs,jdbcType=VARCHAR},
      </if>
      <if test="record.seqName != null">
        seq_name = #{record.seqName,jdbcType=VARCHAR},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from atr_conf_excel_table
    where biz_type_id = #{bizTypeId,jdbcType=NUMERIC}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from atr_conf_excel_table
    where biz_type_id in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from atr_conf_excel_table
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfExcelTable">
    select count(1) from atr_conf_excel_table
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>