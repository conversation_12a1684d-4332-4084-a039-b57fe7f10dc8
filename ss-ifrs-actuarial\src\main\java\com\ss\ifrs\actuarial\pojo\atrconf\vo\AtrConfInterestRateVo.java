/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2022-06-29 10:33:37
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import com.ss.platform.core.annotation.SsTranslateCode;
import com.ss.platform.core.constant.SystemConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2022-06-29 10:33:37<br/>
 * Description: 无风险利率曲线配置<br/>
 * Table Name: atr_conf_interest_rate<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "无风险利率曲线配置")
public class AtrConfInterestRateVo implements Serializable {
    /**
     * Database column: atr_conf_interest_rate.interest_rate_id
     * Database remarks: interest_rate_id|主键
     */
    @ApiModelProperty(value = "interest_rate_id|主键", required = true)
    private Long interestRateId;

    /**
     * Database column: ATR_CONF_INTEREST_RATE.CENTER_ID
     * Database remarks: Center_Id|核算单位ID
     */
    @ApiModelProperty(value = "Center_Id|核算单位ID", required = false)
    @NotNull(message = "The Center Id can't be null|业务单位不能为空|業務單位不能為空")
    //@DecimalMax(value = "2048", message = "Center Id must be less than 2048|业务单位必须小于2048|業務單位必須小於2048")
    private Long entityId;

    /**
     * Database column: ATR_CONF_INTEREST_RATE.CURRENCY
     * Database remarks: currency|币种
     */
    @ApiModelProperty(value = "currency|币种", required = false)
    @NotBlank(message = "The currency can't be null|币种不能为空|幣種不能為空")
    @Size(max = 3, message = "The currency's length is too long|币种过长|幣種過長")
    private String currencyCode;

    /**
     * Database column: ATR_CONF_INTEREST_RATE.YEAR_MONTH
     * Database remarks: year_month|适用期
     */
    @ApiModelProperty(value = "year_month|适用期", required = false)
    @NotBlank(message = "The year month can't be null|适用期不能为空|適用期不能為空")
    @Size(max = 6, message = "The year month's length is too long|适用期过长|適用期過長")
    private String yearMonth;

    /**
     * Database column: atr_conf_interest_rate.version_no
     * Database remarks: null
     */
    private String versionNo;

    /**
     * Database column: atr_conf_interest_rate.confirm_is
     * Database remarks: null
     */
    @SsTranslateCode(context = SystemConstant.QtcIdentity.APP_CONTEXT,codeCodeIdx = "LicConfirm")
    private String confirmIs;

    /**
     * Database column: atr_conf_interest_rate.confirm_id
     * Database remarks: null
     */
    private Long confirmId;

    /**
     * Database column: atr_conf_interest_rate.confirm_time
     * Database remarks: null
     */
    private Date confirmTime;

    /**
     * Database column: atr_conf_interest_rate.valid_is
     * Database remarks: Valid_Is|是否有效
     */
    @ApiModelProperty(value = "Valid_Is|是否有效", required = false)
    private String validIs;

    /**
     * Database column: atr_conf_interest_rate.remark
     * Database remarks: remark|备注
     */
    @ApiModelProperty(value = "remark|备注", required = false)
    private String remark;

    /**
     * Database column: atr_conf_interest_rate.creator_id
     * Database remarks: Creator_Id|创建人
     */
    @ApiModelProperty(value = "Creator_Id|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: atr_conf_interest_rate.create_time
     * Database remarks: Create_Time|创建时间
     */
    @ApiModelProperty(value = "Create_Time|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: atr_conf_interest_rate.updator_id
     * Database remarks: Updator_Id|最后修改人
     */
    @ApiModelProperty(value = "Updator_Id|最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: atr_conf_interest_rate.update_time
     * Database remarks: Update_Time|最后修改时间
     */
    @ApiModelProperty(value = "Update_Time|最后修改时间", required = false)
    private Date updateTime;

    private Integer configYear;

    private String entityCode;

    private String entityEName;

    private String entityCName;

    private String entityLName;

    private String currencyEName;

    private String currencyCName;

    private String currencyLName;

    private String creatorName;

    private String updatorName;

    private String confirmName;

    private String templateFileName;

    private String logFileName;

    private String targetRouter;

    private String language;

    private static final long serialVersionUID = 1L;

    List<AtrConfInterestRateDetailVo> atrConfInterestRateDetailVoList;

    public Long getInterestRateId() {
        return interestRateId;
    }

    public void setInterestRateId(Long interestRateId) {
        this.interestRateId = interestRateId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }	

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

    public String getConfirmIs() {
        return confirmIs;
    }

    public void setConfirmIs(String confirmIs) {
        this.confirmIs = confirmIs;
    }

    public Long getConfirmId() {
        return confirmId;
    }

    public void setConfirmId(Long confirmId) {
        this.confirmId = confirmId;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getEntityCode() {
        return entityCode;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public String getEntityEName() {
        return entityEName;
    }

    public void setEntityEName(String entityEName) {
        this.entityEName = entityEName;
    }

    public String getEntityCName() {
        return entityCName;
    }

    public void setEntityCName(String entityCName) {
        this.entityCName = entityCName;
    }

    public String getEntityLName() {
        return entityLName;
    }

    public void setEntityLName(String entityLName) {
        this.entityLName = entityLName;
    }

    public String getCurrencyEName() {
        return currencyEName;
    }

    public void setCurrencyEName(String currencyEName) {
        this.currencyEName = currencyEName;
    }

    public String getCurrencyCName() {
        return currencyCName;
    }

    public void setCurrencyCName(String currencyCName) {
        this.currencyCName = currencyCName;
    }

    public String getCurrencyLName() {
        return currencyLName;
    }

    public void setCurrencyLName(String currencyLName) {
        this.currencyLName = currencyLName;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }

    public String getConfirmName() {
        return confirmName;
    }

    public void setConfirmName(String confirmName) {
        this.confirmName = confirmName;
    }

    public String getTemplateFileName() {
        return templateFileName;
    }

    public void setTemplateFileName(String templateFileName) {
        this.templateFileName = templateFileName;
    }


    public String getTargetRouter() {
        return targetRouter;
    }

    public void setTargetRouter(String targetRouter) {
        this.targetRouter = targetRouter;
    }

    public Integer getConfigYear() {
        return configYear;
    }

    public void setConfigYear(Integer configYear) {
        this.configYear = configYear;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getLogFileName() {
        return logFileName;
    }

    public void setLogFileName(String logFileName) {
        this.logFileName = logFileName;
    }

    public List<AtrConfInterestRateDetailVo> getAtrConfInterestRateDetailVoList() {
        return atrConfInterestRateDetailVoList;
    }

    public void setAtrConfInterestRateDetailVoList(List<AtrConfInterestRateDetailVo> atrConfInterestRateDetailVoList) {
        this.atrConfInterestRateDetailVoList = atrConfInterestRateDetailVoList;
    }
}