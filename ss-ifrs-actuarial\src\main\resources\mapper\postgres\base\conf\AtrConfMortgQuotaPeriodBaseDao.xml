<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-02-02 14:32:29 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.conf.AtrConfMortgQuotaPeriodDao">
  <!-- 本文件由GIS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfMortgQuotaPeriod">
    <id column="MORTG_QUOTA_PERIOD_ID" property="mortgQuotaPeriodId" jdbcType="DECIMAL" />
    <result column="MORTG_QUOTA_ID" property="mortgQuotaId" jdbcType="DECIMAL" />
    <result column="QUOTA_PERIOD" property="quotaPeriod" jdbcType="DECIMAL" />
    <result column="QUOTA_VALUE" property="quotaValue" jdbcType="DECIMAL" />
    <result column="SERIAL_NO" property="serialNo" jdbcType="DECIMAL" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    MORTG_QUOTA_PERIOD_ID, MORTG_QUOTA_ID, QUOTA_PERIOD, QUOTA_VALUE, SERIAL_NO
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="mortgQuotaPeriodId != null ">
          and MORTG_QUOTA_PERIOD_ID = #{mortgQuotaPeriodId,jdbcType=DECIMAL}
      </if>
      <if test="mortgQuotaId != null ">
          and MORTG_QUOTA_ID = #{mortgQuotaId,jdbcType=DECIMAL}
      </if>
      <if test="quotaPeriod != null ">
          and QUOTA_PERIOD = #{quotaPeriod,jdbcType=DECIMAL}
      </if>
      <if test="quotaValue != null ">
          and QUOTA_VALUE = #{quotaValue,jdbcType=DECIMAL}
      </if>
      <if test="serialNo != null ">
          and SERIAL_NO = #{serialNo,jdbcType=DECIMAL}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.mortgQuotaPeriodId != null ">
          and MORTG_QUOTA_PERIOD_ID = #{condition.mortgQuotaPeriodId,jdbcType=DECIMAL}
      </if>
      <if test="condition.mortgQuotaId != null ">
          and MORTG_QUOTA_ID = #{condition.mortgQuotaId,jdbcType=DECIMAL}
      </if>
      <if test="condition.quotaPeriod != null ">
          and QUOTA_PERIOD = #{condition.quotaPeriod,jdbcType=DECIMAL}
      </if>
      <if test="condition.quotaValue != null ">
          and QUOTA_VALUE = #{condition.quotaValue,jdbcType=DECIMAL}
      </if>
      <if test="condition.serialNo != null ">
          and SERIAL_NO = #{condition.serialNo,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="mortgQuotaPeriodId != null ">
          and MORTG_QUOTA_PERIOD_ID = #{mortgQuotaPeriodId,jdbcType=DECIMAL}
      </if>
      <if test="mortgQuotaId != null ">
          and MORTG_QUOTA_ID = #{mortgQuotaId,jdbcType=DECIMAL}
      </if>
      <if test="quotaPeriod != null ">
          and QUOTA_PERIOD = #{quotaPeriod,jdbcType=DECIMAL}
      </if>
      <if test="quotaValue != null ">
          and QUOTA_VALUE = #{quotaValue,jdbcType=DECIMAL}
      </if>
      <if test="serialNo != null ">
          and SERIAL_NO = #{serialNo,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from ATR_CONF_MORTG_QUOTA_PERIOD
    where MORTG_QUOTA_PERIOD_ID = #{mortgQuotaPeriodId,jdbcType=DECIMAL}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from ATR_CONF_MORTG_QUOTA_PERIOD
    where MORTG_QUOTA_PERIOD_ID in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ATR_CONF_MORTG_QUOTA_PERIOD
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfMortgQuotaPeriod">
    select 
    <include refid="Base_Column_List" />
    from ATR_CONF_MORTG_QUOTA_PERIOD
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from ATR_CONF_MORTG_QUOTA_PERIOD
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="MORTG_QUOTA_PERIOD_ID" keyProperty="mortgQuotaPeriodId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfMortgQuotaPeriod">
    <selectKey resultType="long" keyProperty="mortgQuotaPeriodId" order="BEFORE">
      select nextval('atr_seq_conf_mortg_quota_p') as sequenceNo 
    </selectKey>
    insert into ATR_CONF_MORTG_QUOTA_PERIOD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mortgQuotaPeriodId != null">
        MORTG_QUOTA_PERIOD_ID,
      </if>
      <if test="mortgQuotaId != null">
        MORTG_QUOTA_ID,
      </if>
      <if test="quotaPeriod != null">
        QUOTA_PERIOD,
      </if>
      <if test="quotaValue != null">
        QUOTA_VALUE,
      </if>
      <if test="serialNo != null">
        SERIAL_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mortgQuotaPeriodId != null">
        #{mortgQuotaPeriodId,jdbcType=DECIMAL},
      </if>
      <if test="mortgQuotaId != null">
        #{mortgQuotaId,jdbcType=DECIMAL},
      </if>
      <if test="quotaPeriod != null">
        #{quotaPeriod,jdbcType=DECIMAL},
      </if>
      <if test="quotaValue != null">
        #{quotaValue,jdbcType=DECIMAL},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all 
    <foreach collection="list" item="item" index="index">
      into ATR_CONF_MORTG_QUOTA_PERIOD values 
       (#{item.mortgQuotaPeriodId,jdbcType=DECIMAL}, 
        #{item.mortgQuotaId,jdbcType=DECIMAL}, #{item.quotaPeriod,jdbcType=DECIMAL}, #{item.quotaValue,jdbcType=DECIMAL}, 
        #{item.serialNo,jdbcType=DECIMAL})
    </foreach>
    select 1 
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfMortgQuotaPeriod">
    update ATR_CONF_MORTG_QUOTA_PERIOD
    <set>
      <if test="mortgQuotaId != null">
        MORTG_QUOTA_ID = #{mortgQuotaId,jdbcType=DECIMAL},
      </if>
      <if test="quotaPeriod != null">
        QUOTA_PERIOD = #{quotaPeriod,jdbcType=DECIMAL},
      </if>
      <if test="quotaValue != null">
        QUOTA_VALUE = #{quotaValue,jdbcType=DECIMAL},
      </if>
      <if test="serialNo != null">
        SERIAL_NO = #{serialNo,jdbcType=DECIMAL},
      </if>
    </set>
    where MORTG_QUOTA_PERIOD_ID = #{mortgQuotaPeriodId,jdbcType=DECIMAL}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfMortgQuotaPeriod">
    update ATR_CONF_MORTG_QUOTA_PERIOD
    <set>
      <if test="record.mortgQuotaId != null">
        MORTG_QUOTA_ID = #{record.mortgQuotaId,jdbcType=DECIMAL},
      </if>
      <if test="record.quotaPeriod != null">
        QUOTA_PERIOD = #{record.quotaPeriod,jdbcType=DECIMAL},
      </if>
      <if test="record.quotaValue != null">
        QUOTA_VALUE = #{record.quotaValue,jdbcType=DECIMAL},
      </if>
      <if test="record.serialNo != null">
        SERIAL_NO = #{record.serialNo,jdbcType=DECIMAL},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from ATR_CONF_MORTG_QUOTA_PERIOD
    where MORTG_QUOTA_PERIOD_ID = #{mortgQuotaPeriodId,jdbcType=DECIMAL}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from ATR_CONF_MORTG_QUOTA_PERIOD
    where MORTG_QUOTA_PERIOD_ID in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from ATR_CONF_MORTG_QUOTA_PERIOD
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfMortgQuotaPeriod">
    select count(1) from ATR_CONF_MORTG_QUOTA_PERIOD
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>