package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class AtrBussSyncQtcVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long entityId;

    private String yearMonth;

    /**
     * 自动任务传输字段：任务模式：A-自动
     */
    private String taskMode;

    /**
     * Database column: ATR_BUSS_LIC_ACTION.TASK_CODE
     * Database remarks: 版本号
     */
    @ApiModelProperty(value = "版本号", required = true)
    private String taskCode;

    /**
     * 自动任务传输字段：重试次数
     */
    private Long retryOrder;

}
