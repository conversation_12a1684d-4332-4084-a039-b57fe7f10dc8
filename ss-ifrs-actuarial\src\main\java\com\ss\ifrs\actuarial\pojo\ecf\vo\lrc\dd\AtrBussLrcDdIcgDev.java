package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.dd; // 假设包名与之前一致

import com.ss.ifrs.actuarial.util.abp.Tab; // 假设这个 import 是需要的
import lombok.Data;

import java.math.BigDecimal;

/**
 * LRC 合同组发展期 (直保&临分分入) - 对应表 atr_buss_dd_lrc_g_dev
 */
@Data
@Tab("atr_buss_dd_lrc_g_dev")
public class AtrBussLrcDdIcgDev {

    /** 主表ID (main_id) */
    private Long mainId;

    /** 发展期序号 (dev_no) */
    private Integer devNo; // smallint -> Integer

    /** 业务年月 (year_month) - 备注: 评估期的年月 */
    private String yearMonth;

    /** 退保费 (lapse) */
    private BigDecimal lapse; // decimal -> BigDecimal

    /** 预期维持费用 (mt_fee) */
    private BigDecimal mtFee; // decimal -> BigDecimal

    /** 预期赔付 (claim) */
    private BigDecimal claim; // decimal -> BigDecimal

    /** 预期间接理赔费用 (ulae) */
    private BigDecimal ulae; // decimal -> BigDecimal

    /** 预期非金融风险调整 (ra) */
    private BigDecimal ra; // decimal -> BigDecimal

    /** 已赚比例 (ed_rate) */
    private BigDecimal edRate; // decimal(16, 15) -> BigDecimal

    /** 已赚保费 (ed_premium) */
    private BigDecimal edPremium; // decimal -> BigDecimal

    /** 已赚净额结算 (ed_net_fee) */
    private BigDecimal edNetFee; // decimal(21, 8) -> BigDecimal

    /** 已赚跟单获取费用 (ed_iacf) */
    private BigDecimal edIacf; // decimal(21, 8) -> BigDecimal

    /** 已赚非跟单获取费用-对内 (ed_iaehc_in) */
    private BigDecimal edIaehcIn; // decimal(21, 8) -> BigDecimal

    /** 已赚非跟单获取费用-对外 (ed_iaehc_out) */
    private BigDecimal edIaehcOut; // decimal(21, 8) -> BigDecimal

    /** 应收保费 (recv_premium) */
    private BigDecimal recvPremium; // decimal -> BigDecimal

    /** 净额结算手续费 (net_fee) */
    private BigDecimal netFee; // decimal -> BigDecimal

    /** 跟单获取费用 (iacf) */
    private BigDecimal iacf; // decimal -> BigDecimal

    /** 非跟单获取费用-对内 (iaehc_in) */
    private BigDecimal iaehcIn; // decimal -> BigDecimal

    /** 非跟单获取费用-对外 (iaehc_out) */
    private BigDecimal iaehcOut; // decimal -> BigDecimal

    /** 减值 (bad_debt) */
    private BigDecimal badDebt; // decimal -> BigDecimal

}