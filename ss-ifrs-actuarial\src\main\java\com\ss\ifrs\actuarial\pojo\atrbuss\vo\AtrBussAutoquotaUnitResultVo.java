package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
public class AtrBussAutoquotaUnitResultVo {

    @ApiModelProperty(value = "核算单位ID")
    private String entityId;

    @ApiModelProperty(value = "核算单位代码")
    private String entityCode;

    @ApiModelProperty(value = "核算单位名称")
    private String entityName;

    @ApiModelProperty(value = "业务类型|DD-直接业务&临分分入业务，TI-合约分入，FO-临分分出，TO-合约分出")
    private String businessSourceCode;

    @ApiModelProperty(value = "业务线")
    private String loaCode;

    @ApiModelProperty(value = "合同组合号码")
    private String portfolioNo;

    @ApiModelProperty(value = "业务年")
    private String bussYear;

    @ApiModelProperty(value = "毛保费")
    private BigDecimal grossPremium;

    @ApiModelProperty(value = "净保费")
    private BigDecimal netPremium;

    @ApiModelProperty(value = "已决赔款")
    private BigDecimal settledClaim;

    @ApiModelProperty(value = "已决费用")
    private BigDecimal settledExpense;

    @ApiModelProperty(value = "未决赔款")
    private BigDecimal osClaim;

    @ApiModelProperty(value = "未决费用")
    private BigDecimal osExpense;

    @ApiModelProperty(value = "非跟单获取费用")
    private BigDecimal nonAcqExpense;

    @ApiModelProperty(value = "维持费用")
    private BigDecimal maintenanceExpense;

}
