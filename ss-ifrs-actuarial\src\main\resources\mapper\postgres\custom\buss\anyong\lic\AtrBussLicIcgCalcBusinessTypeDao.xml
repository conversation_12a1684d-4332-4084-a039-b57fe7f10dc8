<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-02-06 14:38:40 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussLicIcgCalcBusinessTypeDao">
  <!-- 本配置文件由GIS MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**IDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->
  <resultMap id="CustResultMap" type="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="MAIN_ID" property="mainId" jdbcType="DECIMAL" />
    <result column="DEV_NO" property="devNo" jdbcType="DECIMAL" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Cust_Column_List">
    a.ID, a.MAIN_ID, a.DEV_NO, a.IBNR_CUR, a.IBNR_PRE, a.OS_CUR, a.OS_PRE, a.ULAE_CUR, a.ULAE_PRE
  </sql>
    
    <select id="findDevNo" flushCache="false" useCache="true" resultMap="CustResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLicCashFlowVo">
        SELECT LICD.Dev_No
        FROM ATR_BUSS_LIC_ACTION BLA
        RIGHT JOIN ATR_BUSS_${businessSourceCode}_LIC_ICG_CALC BDLIC
        ON BLA.ACTION_NO = BDLIC.ACTION_NO
        RIGHT JOIN ATR_BUSS_${businessSourceCode}_LIC_ICG_CALC_DETAIL LICD
        ON BDLIC.ID = LICD.MAIN_ID
        WHERE bla.id= #{id,jdbcType=NUMERIC}
        <if test="icgNo != null and icgNo != ''">
            and BDLIC.Icg_No = #{icgNo,jdbcType=VARCHAR}
        </if>
        GROUP BY Dev_No
        ORDER BY Dev_No
    </select>

    <select id="findUltDev" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLicCashFlowVo">
        SELECT BDLIC.Portfolio_No as "portfolioNo",
               BDLIC.Icg_No as "icgNo"
            <foreach collection="icgDetailDevNoList" item="item" index="index" open=","  separator=",">
                MAX(CASE LICD.DEV_NO WHEN ${item} THEN LICD.ULT_ORI ELSE NULL END) AS "${item}"
            </foreach>
        FROM ATR_BUSS_LIC_ACTION BLA
        RIGHT JOIN ATR_BUSS_${businessSourceCode}_LIC_ICG_CALC BDLIC
        ON BLA.ACTION_NO = BDLIC.ACTION_NO
        RIGHT JOIN ATR_BUSS_${businessSourceCode}_LIC_ICG_CALC_DETAIL LICD
        ON BDLIC.ID = LICD.MAIN_ID
        WHERE bla.id= #{id,jdbcType=NUMERIC}
        <if test="icgNo != null and icgNo != ''">
            and BDLIC.Icg_No = #{icgNo,jdbcType=VARCHAR}
        </if>
        GROUP BY BDLIC.Portfolio_No,BDLIC.Icg_No
        order by BDLIC.Portfolio_No,BDLIC.Icg_No
    </select>


    <select id="findLicDev" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap" parameterType="java.util.List">
        <foreach collection="feeTypeList" item="feeType" index="index" open=""  separator=" union all ">
            SELECT BDLIC.Portfolio_No as "portfolioNo",
            BDLIC.Icg_No as "icgNo"
            <choose>
                <when test="LicCashFlow.language != null and LicCashFlow.language != '' ">
                    <choose>
                        <when test='LicCashFlow.language == "zh"'>
                            ,#{feeType.codeCName} as "expectedType"
                        </when>
                        <when test='LicCashFlow.language == "tn"'>
                            ,#{feeType.codeLName} as "expectedType"
                        </when>
                        <when test='LicCashFlow.language == "en"'>
                            ,#{feeType.codeEName} as "expectedType"
                        </when>
                        <otherwise>
                            ,#{feeType.codeEName} as "expectedType"
                        </otherwise>
                    </choose>
                </when>
            </choose>
            <foreach collection="LicCashFlow.icgDetailDevNoList" item="item" index="index" open=","  separator=",">
                MAX(CASE LICD.DEV_NO WHEN ${item} THEN LICD.${feeType.codeCode} ELSE NULL END) AS "${item}"
            </foreach>
            FROM ATR_BUSS_LIC_ACTION BLA
            RIGHT JOIN ATR_BUSS_${LicCashFlow.businessSourceCode}_LIC_ICG_CALC BDLIC
            ON BLA.ACTION_NO = BDLIC.ACTION_NO
            RIGHT JOIN ATR_BUSS_${LicCashFlow.businessSourceCode}_LIC_ICG_CALC_DETAIL LICD
            ON BDLIC.ID = LICD.MAIN_ID
            WHERE bla.id= #{LicCashFlow.id,jdbcType=NUMERIC}
            <if test="LicCashFlow.icgNo != null and LicCashFlow.icgNo != ''">
                and BDLIC.Icg_No = #{LicCashFlow.icgNo,jdbcType=VARCHAR}
            </if>
            GROUP BY BDLIC.Portfolio_No,BDLIC.Icg_No
        </foreach>
    </select>

    <select id="findAccidentDevNo" flushCache="false" useCache="true" resultMap="CustResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLicCashFlowVo">
        SELECT LICAYD.Dev_No
        FROM ATR_BUSS_LIC_ACTION BLA
        RIGHT JOIN ATR_BUSS_${businessSourceCode}_LIC_ICG_CALC BDLIC
        ON BLA.ACTION_NO = BDLIC.ACTION_NO
        RIGHT JOIN ATR_BUSS_${businessSourceCode}_LIC_ICG_CALC_ACCIDENT_YM LICAY
        ON BDLIC.ID = LICAY.MAIN_ID
        RIGHT JOIN ATR_BUSS_${businessSourceCode}_LIC_ICG_CALC_ACCIDENT_YM_DETAIL LICAYD
        ON LICAY.MAIN_ID = LICAYD.MAIN_ID
        AND LICAY.ACCIDENT_YEAR_MONTH = LICAYD.ACCIDENT_YEAR_MONTH
        WHERE bla.id= #{id,jdbcType=NUMERIC}
        <if test="icgNo != null and icgNo != ''">
            and BDLIC.Icg_No = #{icgNo,jdbcType=VARCHAR}
        </if>
        GROUP BY Dev_No
        ORDER BY Dev_No
    </select>

    <select id="findClaimPattern" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLicCashFlowVo">
        SELECT BDLIC.Portfolio_No as "portfolioNo",
        BDLIC.Icg_No as "icgNo",
        LICAYD.ACCIDENT_YEAR_MONTH as "accidentYearMonth"
        <foreach collection="accidentDevNoList" item="item" index="index" open=","  separator=",">
            MAX(CASE LICAYD.DEV_NO WHEN ${item} THEN LICAYD.PAID_MODE ELSE NULL END) AS "${item}"
        </foreach>
        FROM ATR_BUSS_LIC_ACTION BLA
        RIGHT JOIN ATR_BUSS_${businessSourceCode}_LIC_ICG_CALC BDLIC
        ON BLA.ACTION_NO = BDLIC.ACTION_NO
        RIGHT JOIN ATR_BUSS_${businessSourceCode}_LIC_ICG_CALC_ACCIDENT_YM LICAY
        ON BDLIC.ID = LICAY.MAIN_ID
        RIGHT JOIN ATR_BUSS_${businessSourceCode}_LIC_ICG_CALC_ACCIDENT_YM_DETAIL LICAYD
        ON LICAY.MAIN_ID = LICAYD.MAIN_ID
        AND LICAY.ACCIDENT_YEAR_MONTH = LICAYD.ACCIDENT_YEAR_MONTH
        WHERE bla.id= #{id,jdbcType=NUMERIC}
        <if test="icgNo != null and icgNo != ''">
            and BDLIC.Icg_No = #{icgNo,jdbcType=VARCHAR}
        </if>
        GROUP BY BDLIC.Portfolio_No,BDLIC.Icg_No,LICAYD.ACCIDENT_YEAR_MONTH
        order by BDLIC.Portfolio_No,BDLIC.Icg_No,LICAYD.ACCIDENT_YEAR_MONTH
    </select>
</mapper>