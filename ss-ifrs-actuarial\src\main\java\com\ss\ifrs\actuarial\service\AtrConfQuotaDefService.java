package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDef;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaDefVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface AtrConfQuotaDefService {

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 模糊查询
     * @Return Page
     */
    Page<?> findAtrConfQuotaDefList(AtrConfQuotaDefVo bbsConfQuotaDefVo, Pageable pageable);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 新增或更新指标定义配置
     * @Return
     */
    void addOrUpdateVo(AtrConfQuotaDefVo bbsConfQuotaDefVo, Long userId);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 根据主键id查询指标定义
     * @Return
     */
    AtrConfQuotaDefVo findByPk(Long quotaDefId);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 删除指标定义配置
     * @Return 1可删除；0不可删除
     */
    String deleteByPk(Long confQuotaId, Long userId);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 指标定义配置审核
     * @Return
     */
    void updateAudit(AtrConfQuotaDefVo bbsConfQuotaDefVo, Long userId);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 指标定义配置状态调整
     * @Return
     */
    String disableValid(AtrConfQuotaDefVo bbsConfQuotaDefVo, Long userId);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 批量审核
     * @Return
     */
    String batchAudit(ArrayList<AtrConfQuotaDefVo> auditDealList, Long userId);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/13
     * @Description 根据指标配置查询指标定义对象列表
     */
    List<AtrConfQuotaDef> findVoByQuota(AtrConfQuotaVo bbsConfQuotaVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/15
     * @Description 根据指标配置查询非发展期定义对象列表
     */
    List<AtrConfQuotaDefVo> findDefListByVo(AtrConfQuotaVo bbsConfQuotaVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/22
     * @Description 根据指标配置查询非发展期定义对象列表
     */
    List<AtrConfQuotaDefVo> findDefListByPeriod(AtrConfQuotaVo bbsConfQuotaVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/15
     * @Description 查找指标编码的有效性
     */
    String findValidAtrQuotaCode(String quotaCode);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/23
     * @Description 根据指标编码查找对象
     */
    AtrConfQuotaDef findByQuotaCode(String quotaCode);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/12/29
     * @Description 查找指标定义对象列表
     */
    List<AtrConfQuotaDef> findList(AtrConfQuotaDef bbsConfQuotaDef);

    AtrConfQuotaDefVo findVoByPk(Long quotaDefId);

    AtrConfQuotaDefVo findHisVoByPk(Long quotaDefId);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2023/02/14 查找指标配置结果列的表头
     */
    Map<String, Object> findQuotaDefByGroup(AtrConfQuotaDefVo confQuotaDefVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2023/02/14 查找指标配置结果列的表头
     */
    List<AtrConfQuotaDefVo> findQuotaDefList(AtrConfQuotaDefVo confQuotaDefVo);

    List<AtrConfQuotaDefVo> findConfQuotaCodeType();
}

