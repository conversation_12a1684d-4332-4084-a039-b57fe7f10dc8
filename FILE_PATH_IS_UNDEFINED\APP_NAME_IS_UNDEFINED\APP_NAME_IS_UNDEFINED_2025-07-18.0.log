[2m2025-07-18 14:51:45.784[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m17788[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-18 14:56:45.797[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m17788[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-18 15:01:45.801[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m17788[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-18 15:06:45.808[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m17788[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-18 15:11:45.814[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m17788[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-18 15:16:45.820[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m17788[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-18 15:21:45.826[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m17788[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-18 15:26:45.840[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m17788[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-18 15:31:45.850[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m17788[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-18 15:36:45.859[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m17788[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-18 15:41:45.874[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m17788[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-18 15:46:45.889[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m17788[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-18 15:51:45.900[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m17788[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-18 15:56:45.901[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m17788[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-18 16:01:45.915[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m17788[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-18 16:04:35.115[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m17788[0;39m [2m---[0;39m [2m[     Thread-222][0;39m [36mc.n.l.PollingServerListUpdater          [0;39m [2m:[0;39m Shutting down the Executor Pool for PollingServerListUpdater
[2m2025-07-18 16:04:35.144[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m17788[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application SS-IFRS-ACTUARIAL with eureka with status DOWN
[2m2025-07-18 16:04:35.148[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m17788[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Saw local status change event StatusChangeEvent [timestamp=1752825875148, current=DOWN, previous=UP]
[2m2025-07-18 16:04:35.149[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m17788[0;39m [2m---[0;39m [2m[nfoReplicator-0][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m DiscoveryClient_SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608: registering service...
[2m2025-07-18 16:04:35.169[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m17788[0;39m [2m---[0;39m [2m[nfoReplicator-0][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m DiscoveryClient_SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 - registration status: 204
