CREATE OR REPLACE PACKAGE exp_pack_data_sync IS

  -- Author  : ZOPT
  -- Created : 2022/5/10 9:27:02
  -- Purpose :

  PROCEDURE proc_add_financial_data(p_entityid  NUMBER,
                                    p_yearmonth VARCHAR2,
                                    p_userid    NUMBER);
                                    
  PROCEDURE proc_add_financial_data_by_ledger(p_entityid  NUMBER,
                                                p_yearmonth VARCHAR2,
                                                p_userid    NUMBER);
                                    
  PROCEDURE proc_add_financial_data_by_article(p_entityid  NUMBER,
                                                p_yearmonth VARCHAR2,
                                                p_userid    NUMBER);
                                    
                                    
  PROCEDURE proc_add_metering_unit(p_entityid  NUMBER,
                                   p_yearmonth VARCHAR2,
                                   p_userid    NUMBER);



  FUNCTION func_transition_period_processing(p_entityid  NUMBER,
                                   p_yearmonth VARCHAR2,
                                   p_userid    NUMBER) RETURN NUMBER;
                                   
  PROCEDURE proc_sync_exp_account(p_entityid  NUMBER);
END exp_pack_data_sync;
/
CREATE OR REPLACE PACKAGE BODY exp_pack_data_sync IS

  PROCEDURE proc_add_financial_data(p_entityid  NUMBER,
                                    p_yearmonth VARCHAR2,
                                    p_userid    NUMBER) AS
   
    v_log_msg   VARCHAR2(4000); --日志信息
    v_error_msg VARCHAR2(4000); --异常信息
    v_data_count   NUMBER(11);
    v_sync_plan_type VARCHAR2(1) :='L';
    v_count    NUMBER(1);
  BEGIN   
    SELECT COUNT(1) INTO v_count FROM EXP_CONF_SYNC_ALLOC_DATA_PLAN t WHERE entity_id = p_entityid; 
      
    IF v_count > 0 THEN
      SELECT t.sync_plan_type INTO v_sync_plan_type FROM EXP_CONF_SYNC_ALLOC_DATA_PLAN t WHERE entity_id = p_entityid; 
    END IF;
    --待摊数据来源区分
    IF v_sync_plan_type = 'L' THEN 
       proc_add_financial_data_by_ledger(p_entityid,p_yearmonth,p_userid);
    ELSIF v_sync_plan_type = 'A' THEN
       proc_add_financial_data_by_article(p_entityid,p_yearmonth,p_userid);   
    END IF;  
  EXCEPTION
    WHEN OTHERS THEN
      --抛出自定义异常信息 【会中断事务】
      v_error_msg := substr(SQLERRM, 1, 4000);
      --往外层抛出异常信息
      raise_application_error(-20003, v_error_msg);  
  END proc_add_financial_data;                                              
                                    
  PROCEDURE proc_add_financial_data_by_ledger(p_entityid  NUMBER,
                                                p_yearmonth VARCHAR2,
                                                p_userid    NUMBER) AS
    /***********************************************************************
    NAME : proc_add_financial_data_by_ledger
    DESCRIPTION :  待摊数据同步来源与科目余额
    ***********************************************************************/                                    
    v_data_status          VARCHAR2(32);                                  
    v_log_msg   VARCHAR2(4000); --日志信息
    v_error_msg VARCHAR2(4000); --异常信息
    v_data_count   NUMBER(11);
    v_period_detail_id  NUMBER(11);
    v_period_detail_status VARCHAR2(32);
   BEGIN      
     
   --验证源数据是否已准备
    SELECT period_state
      INTO v_data_status
      FROM dmuser.dm_conf_bussperiod -- dmuser
     WHERE entity_id = p_entityid
       AND year_month = p_yearmonth
       AND valid_is = '1';
       
      --查询当前业务期间详情id, 验证当前数据是否为准备中
      SELECT bpd.period_detail_id,
             bpd.ready_state
        INTO v_period_detail_id,
             v_period_detail_status
        FROM exp_conf_bussperiod_detail bpd
        LEFT JOIN exp_conf_bussperiod p
          ON bpd.buss_period_id = p.buss_period_id
        LEFT JOIN exp_conf_table t
          ON bpd.biz_type_id = t.biz_type_id
       WHERE p.entity_id = p_entityid
         AND p.year_month = p_yearmonth
         AND t.biz_code = 'DAP_DM_FINANCIAL_DATA';
    
    IF v_data_status >= '3' AND v_period_detail_status = '0' THEN    
      IF func_transition_period_processing(p_entityid,p_yearmonth,1) = 1 THEN
         RETURN;
      END IF;    
      
      --先清空当前业务年月数据
      DELETE FROM exp_dap_financial_data_detail b
       WHERE b.financial_id IN (SELECT financial_id
                                  FROM exp_dap_financial_data b
                                 WHERE b.entity_id = p_entityid
                                   AND b.year_month = p_yearmonth);

      DELETE FROM exp_dap_financial_data b
       WHERE b.entity_id = p_entityid
         AND b.year_month = p_yearmonth;
                             
      --情况二、科目余额不存在专项数据，生成一条数据
      INSERT INTO exp_dap_financial_data
        (financial_id, -- financial_id|主键
         entity_id, -- entity_id|核算单位
         book_code, -- book_code|账套编号
         year_month, -- year_Month|会计期间
         account_id, -- account_id|科目
         currency_cu_code, -- currency_cu_code|本位币
         debit_amount_cu, -- Debit_Amount_Cu|本期借方发生额(本位币)
         credit_amount_cu, -- Credit_Amount _Cu|本期贷方发生额(本位币)
         amount_cu, -- amount_cu|本月发生额(本位币)
         dept_type, -- dept_type|部门类型(是否待摊部门 0-否,1-是)
         data_source, -- data_source|数据来源(1-科目余额, 2-专项余额)
         creator_id, -- creator_id|创建人
         create_time, -- create_time|创建时间
         task_code -- task_code|任务ID
         )
        SELECT exp_seq_dap_financial_data.nextval,
               bb.entity_id, -- 业务单位
               bb.book_code, -- 账套
               bb.year_month, -- 会计期间
               bb.account_id,
               bb.currency_cu_code,
               debit_amount_year_cu,
               credit_amount_year_cu,
               amount_cu,
               '0', -- dept_type
               '1', -- data_source
               p_userid, --creator_id
               SYSDATE, -- 创建时间
               task_code
          FROM (SELECT b.entity_id, -- 业务单位
                       b.book_code, -- 账套
                       b.year_month, -- 会计期间
                       b.account_id, -- 科目
                       b.currency_cu_code, -- 币别(本位币)
                       SUM(b.debit_amount_year_cu) debit_amount_year_cu, -- 本月发生额(本位币)（借方）
                       SUM(b.credit_amount_year_cu) credit_amount_year_cu, -- 本月发生额(本位币)（贷方）
                       SUM(b.debit_amount_year_cu - b.credit_amount_year_cu) amount_cu, -- amount_cu|本月发生额(本位币)
                       MAX(b.task_code) task_code --task_code
                  FROM dmuser.dm_fin_ledger_balance b
                 WHERE b.entity_id = p_entityid
                   AND b.year_month = p_yearmonth
                   AND NOT EXISTS (SELECT 1
                          FROM exp_dap_financial_data t
                         WHERE b.entity_id = t.entity_id
                           AND b.book_code = t.book_code
                           AND b.year_month = t.year_month
                           AND b.account_id = t.account_id)
                   AND EXISTS (SELECT 1
                          FROM exp_conf_account c
                         WHERE b.account_id = c.account_id
                           AND c.valid_is = '1'
                           AND c.audit_state = '1')
                 GROUP BY b.entity_id,
                          b.book_code,
                          b.year_month,
                          b.account_id,
                          b.currency_cu_code,
                          p_userid) bb;

      -- 2、写入待分摊数据明细表
      INSERT INTO exp_dap_financial_data_detail
        (financial_detail_id, -- financial_id|主键
         financial_id, -- financial_id|主键
         currency_code,
         currency_cu_code,
         dept_id,
         data_source, -- data_source|数据来源(1-科目余额, 2-专项余额)
         debit_amount, -- Debit_Amount|本期借方发生额(原币)
         debit_amount_cu, -- Debit_Amount_Cu|本期借方发生额(本位币)
         credit_amount, -- Credit_Amount|本期贷方发生额(原币)
         credit_amount_cu, -- Credit_Amount _Cu|本期贷方发生额(本位币)
         amount, -- amount|本月发生额(原币)
         amount_cu -- amount_cu|本月发生额(本位币)
         )
        SELECT exp_seq_dap_financial_data_dtl.nextval,
               bb.financial_id,
               bb.currency_code,
               bb.currency_cu_code,
               NULL,
               '1',
               debit_amount_year,
               debit_amount_year_cu,
               credit_amount_year,
               credit_amount_year_cu,
               amount,
               amount_cu
          FROM (SELECT fd.financial_id,
                       b.currency_code,
                       b.currency_cu_code,
                       SUM(b.debit_amount_year) debit_amount_year, -- 本月发生额（借方）
                       SUM(b.debit_amount_year_cu) debit_amount_year_cu, -- 本月发生额(本位币)（借方）
                       SUM(b.credit_amount_year) credit_amount_year, -- 本月发生额（贷方）
                       SUM(b.credit_amount_year_cu) credit_amount_year_cu, -- 本月发生额(本位币)（贷方）
                       SUM(b.debit_amount_year - b.credit_amount_year) amount, -- amount|本月发生额(原币)
                       SUM(b.debit_amount_year_cu - b.credit_amount_year_cu) amount_cu -- amount_cu|本月发生额(本位币)
                  FROM exp_dap_financial_data fd
                  LEFT JOIN dmuser.dm_fin_ledger_balance b
                    ON b.entity_id = fd.entity_id
                   AND b.year_month = fd.year_month
                   AND b.account_id = fd.account_id
                 WHERE b.entity_id = p_entityid
                   AND b.year_month = p_yearmonth
                   AND fd.data_source = '1'
                 GROUP BY fd.financial_id,
                          b.currency_code,
                          b.currency_cu_code) bb;
      COMMIT;                                       
      --修改exp_conf_bussperiod_detail为已准备状态
      UPDATE exp_conf_bussperiod_detail
         SET ready_state = '1',
             exec_result = 'success',
             task_time   = SYSDATE
       WHERE period_detail_id = v_period_detail_id;
       COMMIT;
      --同步业务期间执行状态
      exp_pack_buss_period.proc_period_execution(p_entityid, '1');              
    END IF;                      
  EXCEPTION
    WHEN OTHERS THEN
      dbms_output.put_line('**出错行数: '|| dbms_utility.format_error_backtrace());
      IF v_period_detail_id IS NOT NULL THEN
        --修改exp_conf_bussperiod_detail为失败状态
        UPDATE exp_conf_bussperiod_detail
           SET ready_state = '0',
               exec_result = 'failed',
               task_time   = localtimestamp
         WHERE period_detail_id = v_period_detail_id;
          --提交事务
        COMMIT;
      END IF;
      --抛出自定义异常信息 【会中断事务】
      v_error_msg := substr('待摊数据同步发生异常，请检查！：' ||  '**SQLERRM: ' || SQLERRM || '**Error line: ' || dbms_utility.format_error_backtrace() || '', 1, 4000);
      --往外层抛出异常信息
      raise_application_error(-20003, v_error_msg);                          
                                                
  END proc_add_financial_data_by_ledger;   
  
 
                                                   
  PROCEDURE proc_add_financial_data_by_article(p_entityid  NUMBER,
                                    p_yearmonth VARCHAR2,
                                    p_userid    NUMBER) AS
    /***********************************************************************
    NAME : proc_add_financial_data_by_ledger
    DESCRIPTION :  待摊数据同步来源与科目余额和专项余额
    ***********************************************************************/  

    v_data_status          VARCHAR2(32);
    v_period_detail_id     NUMBER(11);
    v_period_detail_status VARCHAR2(32);
    v_log_msg   VARCHAR2(4000); --日志信息
    v_error_msg VARCHAR2(4000); --异常信息
    v_data_count   NUMBER(11);
  BEGIN
    
   --验证源数据是否已准备
    SELECT period_state
      INTO v_data_status
      FROM dmuser.dm_conf_bussperiod -- dmuser
     WHERE entity_id = p_entityid
       AND year_month = p_yearmonth
       AND valid_is = '1';

    --查询当前业务期间详情id, 验证当前数据是否为准备中
    SELECT bpd.period_detail_id,
           bpd.ready_state
      INTO v_period_detail_id,
           v_period_detail_status
      FROM exp_conf_bussperiod_detail bpd
      LEFT JOIN exp_conf_bussperiod p
        ON bpd.buss_period_id = p.buss_period_id
      LEFT JOIN exp_conf_table t
        ON bpd.biz_type_id = t.biz_type_id
     WHERE p.entity_id = p_entityid
       AND p.year_month = p_yearmonth
       AND t.biz_code = 'DAP_DM_FINANCIAL_DATA';

    dbms_output.put_line('源数据准备状态:' || v_data_status || ',业务期间详情id:' || v_period_detail_id || ',当前数据准备状态' || v_period_detail_status);

    IF v_data_status != '3' THEN
      v_error_msg := '数据平台期间状态不是已完成为:' || '，待摊数据不执行同步：[' || p_entityid || ']-['|| p_yearmonth || ']';
      --往外层抛出异常信息
      raise_application_error(-20002, v_error_msg);
    END IF;

    IF v_data_status >= '3' AND v_period_detail_status = '0' THEN

        IF func_transition_period_processing(p_entityid,p_yearmonth,1) = 1  THEN
           RETURN;
        END IF;

      --先清空当前业务年月数据
      DELETE FROM exp_dap_financial_data_detail b
       WHERE b.financial_id IN (SELECT financial_id
                                  FROM exp_dap_financial_data b
                                 WHERE b.entity_id = p_entityid
                                   AND b.year_month = p_yearmonth);

      DELETE FROM exp_dap_financial_data b
       WHERE b.entity_id = p_entityid
         AND b.year_month = p_yearmonth;
         

      --情况一、科目只存在专项数据取所有数据(按部门分组写主表和明细)，有部门专项和无部门会各自生成一条
      INSERT INTO exp_dap_financial_data
        (financial_id, -- financial_id|主键
         entity_id, -- entity_id|核算单位
         book_code, -- book_code|账套编号
         year_month, -- year_Month|会计期间
         account_id, -- account_id|科目
         currency_cu_code, -- currency_cu_code|本位币
         debit_amount_cu, -- Debit_Amount_Cu|本期借方发生额(本位币)
         credit_amount_cu, -- Credit_Amount _Cu|本期贷方发生额(本位币)
         amount_cu, -- amount_cu|本月发生额(本位币)
         dept_type, -- dept_type|部门类型(是否待摊部门 0-否,1-是)
         data_source, -- data_source|数据来源(1-科目余额, 2-专项余额)
         creator_id, -- creator_id|创建人
         create_time, -- create_time|创建时间
         task_code -- task_code|任务ID
         )
        SELECT exp_seq_dap_financial_data.nextval,
               bb.entity_id, -- 业务单位
               bb.book_code, -- 账套
               bb.year_month, -- 会计期间
               bb.account_id, -- 科目
               bb.currency_cu_code,
               debit_amount_year_cu,
               credit_amount_year_cu,
               amount_cu,
               dept_type,
               '2' AS data_source,
               p_userid, --creator_id
               SYSDATE, -- 创建时间
               bb.task_code
          FROM (SELECT b.entity_id, -- 业务单位
                       b.book_code, -- 账套
                       b.year_month, -- 会计期间
                       b.account_id, -- 科目
                       b.currency_cu_code, -- 币别 (本位币)
                       SUM(b.debit_amount_year_cu) debit_amount_year_cu, -- 本月发生额(本位币)（借方）
                       SUM(b.credit_amount_year_cu) credit_amount_year_cu, -- 本月发生额(本位币)（贷方）
                       SUM(b.debit_amount_year_cu - b.credit_amount_year_cu) amount_cu, -- amount_cu|本月发生额(本位币)
                       exp_pack_common.func_get_depttype(NULL, substr(b.article1, 3, length(b.article1)-1), NULL) AS dept_type,
                       MAX(b.task_code) task_code --task_code
                  FROM dmuser.dm_fin_article_balance b
                  LEFT JOIN bpluser.bpl_company d --bpluser
                    ON d.COMPANY_CODE = substr(b.article1, 3, length(b.article1)-1)
                 WHERE b.entity_id = p_entityid
                   AND b.year_month = p_yearmonth
                   AND EXISTS (SELECT 1
                          FROM exp_conf_account c
                         WHERE b.account_id = c.account_id
                           AND c.valid_is = '1'
                           AND audit_state = '1')
                 GROUP BY b.entity_id,
                          b.book_code,
                          b.year_month,
                          b.account_id,
                          b.currency_cu_code,
                          exp_pack_common.func_get_depttype(NULL, substr(b.article1, 3, length(b.article1)-1), NULL),
                          p_userid) bb;


      --写入待分摊数据明细表
      INSERT INTO exp_dap_financial_data_detail
        (financial_detail_id, -- financial_id|主键
         financial_id, -- financial_id|主键
         currency_code,
         currency_cu_code,
         dept_id,
         debit_amount, -- Debit_Amount|本期借方发生额(原币)
         debit_amount_cu, -- Debit_Amount_Cu|本期借方发生额(本位币)
         credit_amount, -- Credit_Amount|本期贷方发生额(原币)
         credit_amount_cu, -- Credit_Amount _Cu|本期贷方发生额(本位币)
         amount, -- amount|本月发生额(原币)
         amount_cu -- amount_cu|本月发生额(本位币)
         )
        SELECT exp_seq_dap_financial_data_dtl.nextval,
               bb.financial_id, -- financial_id|主键
               bb.currency_code, -- 原币
               bb.currency_cu_code,
               bb.company_id, -- entity_id|核算单位
               debit_amount_year,
               debit_amount_year_cu,
               credit_amount_year_cu,
               credit_amount_year,
               amount,
               amount_cu
          FROM (SELECT fd.financial_id,
                       b.currency_code,
                       b.currency_cu_code,
                       d.company_id,
                       SUM(b.debit_amount_year) debit_amount_year, -- 本月发生额（借方）
                       SUM(b.debit_amount_year_cu) debit_amount_year_cu, -- 本月发生额(本位币)（借方）
                       SUM(b.credit_amount_year) credit_amount_year, -- 本月发生额（贷方）
                       SUM(b.credit_amount_year_cu) credit_amount_year_cu, -- 本月发生额(本位币)（贷方）
                       SUM(b.debit_amount_year - b.credit_amount_year) amount, -- amount|本月发生额(原币)
                       SUM(b.debit_amount_year_cu - b.credit_amount_year_cu) amount_cu -- amount_cu|本月发生额(本位币)
                  FROM exp_dap_financial_data fd
                  LEFT JOIN dmuser.dm_fin_article_balance b
                    ON b.entity_id = fd.entity_id
                   AND b.year_month = fd.year_month
                   AND b.account_id = fd.account_id
                   AND exp_pack_common.func_get_depttype(NULL, substr(b.article1, 3, length(b.article1)-1), NULL) = fd.dept_type --expuser
                  LEFT JOIN bpluser.bpl_company d
                    ON d.COMPANY_CODE = substr(b.article1, 3, length(b.article1)-1)
                 WHERE fd.entity_id = p_entityid
                   AND fd.year_month = p_yearmonth
                   AND fd.data_source = '2'
                 GROUP BY fd.financial_id,
                          b.currency_code,
                          b.currency_cu_code,
                          d.company_id) bb;


      --情况二、科目余额不存在专项数据，生成一条数据
      INSERT INTO exp_dap_financial_data
        (financial_id, -- financial_id|主键
         entity_id, -- entity_id|核算单位
         book_code, -- book_code|账套编号
         year_month, -- year_Month|会计期间
         account_id, -- account_id|科目
         currency_cu_code, -- currency_cu_code|本位币
         debit_amount_cu, -- Debit_Amount_Cu|本期借方发生额(本位币)
         credit_amount_cu, -- Credit_Amount _Cu|本期贷方发生额(本位币)
         amount_cu, -- amount_cu|本月发生额(本位币)
         dept_type, -- dept_type|部门类型(是否待摊部门 0-否,1-是)
         data_source, -- data_source|数据来源(1-科目余额, 2-专项余额)
         creator_id, -- creator_id|创建人
         create_time, -- create_time|创建时间
         task_code -- task_code|任务ID
         )
        SELECT exp_seq_dap_financial_data.nextval,
               bb.entity_id, -- 业务单位
               bb.book_code, -- 账套
               bb.year_month, -- 会计期间
               bb.account_id,
               bb.currency_cu_code,
               debit_amount_year_cu,
               credit_amount_year_cu,
               amount_cu,
               '0', -- dept_type
               '1', -- data_source
               p_userid, --creator_id
               SYSDATE, -- 创建时间
               task_code
          FROM (SELECT b.entity_id, -- 业务单位
                       b.book_code, -- 账套
                       b.year_month, -- 会计期间
                       b.account_id, -- 科目
                       b.currency_cu_code, -- 币别(本位币)
                       SUM(b.debit_amount_year_cu) debit_amount_year_cu, -- 本月发生额(本位币)（借方）
                       SUM(b.credit_amount_year_cu) credit_amount_year_cu, -- 本月发生额(本位币)（贷方）
                       SUM(b.debit_amount_year_cu - b.credit_amount_year_cu) amount_cu, -- amount_cu|本月发生额(本位币)
                       MAX(b.task_code) task_code --task_code
                  FROM dmuser.dm_fin_ledger_balance b
                 WHERE b.entity_id = p_entityid
                   AND b.year_month = p_yearmonth
                   AND NOT EXISTS (SELECT 1
                          FROM exp_dap_financial_data t
                         WHERE b.entity_id = t.entity_id
                           AND b.book_code = t.book_code
                           AND b.year_month = t.year_month
                           AND b.account_id = t.account_id)
                   AND EXISTS (SELECT 1
                          FROM exp_conf_account c
                         WHERE b.account_id = c.account_id
                           AND c.valid_is = '1'
                           AND c.audit_state = '1')
                 GROUP BY b.entity_id,
                          b.book_code,
                          b.year_month,
                          b.account_id,
                          b.currency_cu_code,
                          p_userid) bb;

      -- 2、写入待分摊数据明细表
      INSERT INTO exp_dap_financial_data_detail
        (financial_detail_id, -- financial_id|主键
         financial_id, -- financial_id|主键
         currency_code,
         currency_cu_code,
         dept_id,
         data_source, -- data_source|数据来源(1-科目余额, 2-专项余额)
         debit_amount, -- Debit_Amount|本期借方发生额(原币)
         debit_amount_cu, -- Debit_Amount_Cu|本期借方发生额(本位币)
         credit_amount, -- Credit_Amount|本期贷方发生额(原币)
         credit_amount_cu, -- Credit_Amount _Cu|本期贷方发生额(本位币)
         amount, -- amount|本月发生额(原币)
         amount_cu -- amount_cu|本月发生额(本位币)
         )
        SELECT exp_seq_dap_financial_data_dtl.nextval,
               bb.financial_id,
               bb.currency_code,
               bb.currency_cu_code,
               NULL,
               '1',
               debit_amount_year,
               debit_amount_year_cu,
               credit_amount_year,
               credit_amount_year_cu,
               amount,
               amount_cu
          FROM (SELECT fd.financial_id,
                       b.currency_code,
                       b.currency_cu_code,
                       SUM(b.debit_amount_year) debit_amount_year, -- 本月发生额（借方）
                       SUM(b.debit_amount_year_cu) debit_amount_year_cu, -- 本月发生额(本位币)（借方）
                       SUM(b.credit_amount_year) credit_amount_year, -- 本月发生额（贷方）
                       SUM(b.credit_amount_year_cu) credit_amount_year_cu, -- 本月发生额(本位币)（贷方）
                       SUM(b.debit_amount_year - b.credit_amount_year) amount, -- amount|本月发生额(原币)
                       SUM(b.debit_amount_year_cu - b.credit_amount_year_cu) amount_cu -- amount_cu|本月发生额(本位币)
                  FROM exp_dap_financial_data fd
                  LEFT JOIN dmuser.dm_fin_ledger_balance b
                    ON b.entity_id = fd.entity_id
                   AND b.year_month = fd.year_month
                   AND b.account_id = fd.account_id
                 WHERE b.entity_id = p_entityid
                   AND b.year_month = p_yearmonth
                   AND fd.data_source = '1'
                 GROUP BY fd.financial_id,
                          b.currency_code,
                          b.currency_cu_code) bb;




      -- 情况三、科目余额存在专项数据，且专项数据不存在【无部门】数据时，会新增一条新的主表数据
      INSERT INTO exp_dap_financial_data
        (financial_id, -- financial_id|主键
         entity_id, -- entity_id|核算单位
         book_code, -- book_code|账套编号
         year_month, -- year_Month|会计期间
         account_id, -- account_id|科目
         currency_cu_code, -- currency_cu_code|本位币
         debit_amount_cu, -- Debit_Amount_Cu|本期借方发生额(本位币)
         credit_amount_cu, -- Credit_Amount _Cu|本期贷方发生额(本位币)
         amount_cu, -- amount_cu|本月发生额(本位币)
         dept_type, -- dept_type|部门类型(是否待摊部门 0-否,1-是)
         data_source, -- data_source|数据来源(1-科目余额, 2-专项余额)
         creator_id, -- creator_id|创建人
         create_time, -- create_time|创建时间
         task_code -- task_code|任务ID
         )
        SELECT exp_seq_dap_financial_data.nextval,
               bb.entity_id, -- 业务单位
               bb.book_code, -- 账套
               bb.year_month, -- 会计期间
               bb.account_id,
               bb.currency_cu_code,
               debit_cu,
               amount,
               amount_cu,
               '0', -- dept_type
               '1', -- data_source
               p_userid, --creator_id
               SYSDATE, -- 创建时间
               task_code
          FROM (SELECT b.entity_id, -- 业务单位
                       b.book_code, -- 账套
                       b.year_month, -- 会计期间
                       b.account_id, -- 科目
                       b.currency_cu_code, -- 币别(本位币)
                       SUM(b.debit_amount_year_cu - fd.debit_amount_cu) debit_cu, -- 本月发生额(本位币)（借方）
                       SUM(b.credit_amount_year_cu - fd.credit_amount_cu) amount, -- 本月发生额(本位币)（贷方）
                       SUM(b.amount_cu - fd.amount_cu) amount_cu, -- amount_cu|本月发生额(本位币)
                       '0', -- dept_type
                       '1', -- data_source
                       p_userid, --creator_id
                       SYSDATE, -- 创建时间
                       MAX(b.task_code) task_code --task_code
                  FROM (SELECT b.entity_id, -- 业务单位
                               b.book_code, -- 账套
                               b.year_month, -- 会计期间
                               b.account_id, -- 科目
                               b.currency_cu_code, -- 币别(本位币)
                               b.task_code,
                               SUM(b.debit_amount_year_cu) AS debit_amount_year_cu, -- 本月发生额(本位币)（借方）
                               SUM(b.credit_amount_year_cu) AS credit_amount_year_cu, -- 本月发生额(本位币)（贷方）
                               SUM(b.debit_amount_year_cu - b.credit_amount_year_cu) AS amount_cu
                          FROM dmuser.dm_fin_ledger_balance b
                         GROUP BY b.entity_id,
                                  b.book_code,
                                  b.year_month,
                                  b.account_id,
                                  b.currency_cu_code,
                                  b.task_code) b
                  LEFT JOIN (SELECT f.financial_id,
                                   f.account_id,
                                   SUM(fd.debit_amount_cu) AS debit_amount_cu,
                                   SUM(fd.credit_amount_cu) AS credit_amount_cu,
                                   SUM(fd.amount_cu) AS amount_cu
                              FROM exp_dap_financial_data        f,
                                   exp_dap_financial_data_detail fd
                             WHERE f.financial_id = fd.financial_id
                               AND f.entity_id = p_entityid
                               AND f.year_month = p_yearmonth
                               AND f.data_source = '2'
                             GROUP BY f.financial_id,
                                      f.account_id) fd
                    ON b.account_id = fd.account_id
                 WHERE b.entity_id = p_entityid
                   AND b.year_month = p_yearmonth
                   AND (b.debit_amount_year_cu - b.credit_amount_year_cu - fd.amount_cu) <> 0
                   AND EXISTS (SELECT 1
                          FROM exp_dap_financial_data t
                         WHERE b.entity_id = t.entity_id
                           AND b.book_code = t.book_code
                           AND b.year_month = t.year_month
                           AND b.account_id = t.account_id
                           AND data_source = '2')
                   AND NOT EXISTS (SELECT 1
                          FROM exp_dap_financial_data t
                         WHERE b.entity_id = t.entity_id
                           AND b.book_code = t.book_code
                           AND b.year_month = t.year_month
                           AND b.account_id = t.account_id
                           AND data_source = '2'
                           AND dept_type = '0')
                   AND EXISTS (SELECT 1
                          FROM exp_conf_account c
                         WHERE b.account_id = c.account_id
                           AND c.valid_is = '1'
                           AND c.audit_state = '1')
                 GROUP BY b.entity_id,
                          b.book_code,
                          b.year_month,
                          b.account_id,
                          b.currency_cu_code,
                          p_userid) bb;




      INSERT INTO exp_dap_financial_data_detail
        (financial_detail_id, -- financial_id|主键
         financial_id, -- financial_id|主键
         currency_code,
         currency_cu_code,
         dept_id,
         data_source,
         debit_amount, -- Debit_Amount|本期借方发生额(原币)
         debit_amount_cu, -- Debit_Amount_Cu|本期借方发生额(本位币)
         credit_amount, -- Credit_Amount|本期贷方发生额(原币)
         credit_amount_cu, -- Credit_Amount _Cu|本期贷方发生额(本位币)
         amount, -- amount|本月发生额(原币)
         amount_cu -- amount_cu|本月发生额(本位币)
         )
        SELECT exp_seq_dap_financial_data_dtl.nextval,
               (SELECT financial_id
                  FROM exp_dap_financial_data f
                 WHERE b.entity_id = f.entity_id
                   AND b.year_month = f.year_month
                   AND b.account_id = f.account_id
                   AND data_source = '1'),
               b.currency_code,
               b.currency_code,
               NULL,
               '1',
               (b.debit_amount_year - fd.debit_amount), -- 本月发生额（借方）
               (b.debit_amount_year_cu - fd.debit_amount_cu), -- 本月发生额(本位币)（借方）
               (b.credit_amount_year - fd.credit_amount), -- 本月发生额（贷方）
               (b.credit_amount_year_cu - fd.credit_amount_cu), -- 本月发生额(本位币)（贷方）
               (b.debit_amount_year - b.credit_amount_year - fd.amount), -- amount|本月发生额(原币)
               (b.debit_amount_year_cu - b.credit_amount_year_cu - fd.amount_cu) -- amount_cu|本月发生额(本位币)
          FROM dmuser.dm_fin_ledger_balance b --dmuser
          LEFT JOIN (SELECT f.financial_id,
                            f.account_id,
                            fd.currency_code,
                            SUM(fd.debit_amount) AS debit_amount,
                            SUM(fd.debit_amount_cu) AS debit_amount_cu,
                            SUM(fd.credit_amount) AS credit_amount,
                            SUM(fd.credit_amount_cu) AS credit_amount_cu,
                            SUM(fd.amount) AS amount,
                            SUM(fd.amount_cu) AS amount_cu
                       FROM exp_dap_financial_data        f,
                            exp_dap_financial_data_detail fd
                      WHERE f.financial_id = fd.financial_id
                        AND f.entity_id = p_entityid
                        AND f.year_month = p_yearmonth
                      GROUP BY f.financial_id,
                               f.account_id,
                               fd.currency_code) fd
            ON b.account_id = fd.account_id
           AND b.currency_code = fd.currency_code
         WHERE b.entity_id = p_entityid
           AND b.year_month = p_yearmonth
           AND (b.debit_amount_year_cu - b.credit_amount_year_cu - fd.amount_cu) <> 0
           AND EXISTS (SELECT 1
                  FROM exp_dap_financial_data t
                 WHERE b.entity_id = t.entity_id
                   AND b.book_code = t.book_code
                   AND b.year_month = t.year_month
                   AND b.account_id = t.account_id
                   AND data_source = '2')
           AND NOT EXISTS (SELECT 1
                  FROM exp_dap_financial_data t
                 WHERE b.entity_id = t.entity_id
                   AND b.book_code = t.book_code
                   AND b.year_month = t.year_month
                   AND b.account_id = t.account_id
                   AND data_source = '2'
                   AND dept_type = '0')
           AND EXISTS (SELECT 1
                  FROM exp_conf_account c
                 WHERE b.account_id = c.account_id
                   AND c.valid_is = '1'
                   AND c.audit_state = '1');


      -- 情况四、科目余额存在专项数据，且专项数不存在【无部门】数据时
      INSERT INTO exp_dap_financial_data_detail
        (financial_detail_id, -- financial_id|主键
         financial_id, -- financial_id|主键
         currency_code,
         currency_cu_code,
         dept_id,
         data_source,
         debit_amount, -- Debit_Amount|本期借方发生额(原币)
         debit_amount_cu, -- Debit_Amount_Cu|本期借方发生额(本位币)
         credit_amount, -- Credit_Amount|本期贷方发生额(原币)
         credit_amount_cu, -- Credit_Amount _Cu|本期贷方发生额(本位币)
         amount, -- amount|本月发生额(原币)
         amount_cu -- amount_cu|本月发生额(本位币)
         )
        SELECT exp_seq_dap_financial_data_dtl.nextval,
               (SELECT t.financial_id
                  FROM exp_dap_financial_data t
                 WHERE b.entity_id = t.entity_id
                   AND b.book_code = t.book_code
                   AND b.year_month = t.year_month
                   AND b.account_id = t.account_id
                   AND data_source = '2'
                   AND dept_type = '0') AS financial_id,
               b.currency_code,
               b.currency_cu_code,
               NULL,
               '1',
               (b.debit_amount_year - fd.debit_amount), -- 本月发生额（借方）
               (b.debit_amount_year_cu - fd.debit_amount_cu), -- 本月发生额(本位币)（借方）
               (b.credit_amount_year - fd.credit_amount), -- 本月发生额（贷方）
               (b.credit_amount_year_cu - fd.credit_amount_cu), -- 本月发生额(本位币)（贷方）
               (b.debit_amount_year - b.credit_amount_year - fd.amount), -- amount|本月发生额(原币)
               (b.debit_amount_year_cu - b.credit_amount_year_cu - fd.amount_cu) -- amount_cu|本月发生额(本位币)
          FROM dmuser.dm_fin_ledger_balance b
          LEFT JOIN (SELECT f.account_id,
                            fd.currency_code,
                            SUM(fd.debit_amount) AS debit_amount,
                            SUM(fd.debit_amount_cu) AS debit_amount_cu,
                            SUM(fd.credit_amount) AS credit_amount,
                            SUM(fd.credit_amount_cu) AS credit_amount_cu,
                            SUM(fd.amount) AS amount,
                            SUM(fd.amount_cu) AS amount_cu
                       FROM exp_dap_financial_data        f,
                            exp_dap_financial_data_detail fd
                      WHERE f.financial_id = fd.financial_id
                        AND f.entity_id = p_entityid
                        AND f.year_month = p_yearmonth
                      GROUP BY f.account_id,
                               fd.currency_code) fd
            ON b.account_id = fd.account_id
           AND b.currency_code = fd.currency_code
         WHERE b.entity_id = p_entityid
           AND b.year_month = p_yearmonth
           AND (b.debit_amount_year_cu - b.credit_amount_year_cu - fd.amount_cu) <> 0
           AND EXISTS (SELECT 1
                  FROM exp_dap_financial_data t
                 WHERE b.entity_id = t.entity_id
                   AND b.book_code = t.book_code
                   AND b.year_month = t.year_month
                   AND b.account_id = t.account_id
                   AND data_source = '2')
           AND EXISTS (SELECT 1
                  FROM exp_dap_financial_data t
                 WHERE b.entity_id = t.entity_id
                   AND b.book_code = t.book_code
                   AND b.year_month = t.year_month
                   AND b.account_id = t.account_id
                   AND data_source = '2'
                   AND dept_type = '0')
           AND EXISTS (SELECT 1
                  FROM exp_conf_account c
                 WHERE b.account_id = c.account_id
                   AND c.valid_is = '1'
                   AND c.audit_state = '1');


      UPDATE exp_dap_financial_data t
         SET debit_amount_cu =
             (SELECT SUM(debit_amount_cu) FROM exp_dap_financial_data_detail fd WHERE fd.financial_id = t.financial_id),
             credit_amount_cu =
             (SELECT SUM(credit_amount_cu) FROM exp_dap_financial_data_detail fd WHERE fd.financial_id = t.financial_id),
             amount_cu       =
             (SELECT SUM(amount_cu) FROM exp_dap_financial_data_detail fd WHERE fd.financial_id = t.financial_id)
       WHERE t.entity_id = p_entityid
         AND t.year_month = p_yearmonth
         AND t.data_source = '2'
         AND t.dept_type = '0';

      COMMIT;
      --对有部门专项的待摊数据进行受益部门指认
      exp_pack_recognition.proc_sync_identifydept(p_entityid, p_yearmonth, p_userid);

      --修改exp_conf_bussperiod_detail为已准备状态
      UPDATE exp_conf_bussperiod_detail
         SET ready_state = '1',
             exec_result = 'success',
             task_time   = SYSDATE
       WHERE period_detail_id = v_period_detail_id;
       COMMIT;
      --同步业务期间执行状态
      exp_pack_buss_period.proc_period_execution(p_entityid, '1');
    END IF;
  EXCEPTION
    WHEN OTHERS THEN
      dbms_output.put_line('**出错行数: '|| dbms_utility.format_error_backtrace());
      IF v_period_detail_id IS NOT NULL THEN
        --修改exp_conf_bussperiod_detail为失败状态
        UPDATE exp_conf_bussperiod_detail
           SET ready_state = '0',
               exec_result = 'failed',
               task_time   = localtimestamp
         WHERE period_detail_id = v_period_detail_id;
          --提交事务
        COMMIT;
      END IF;
      --抛出自定义异常信息 【会中断事务】
      v_error_msg := substr('待摊数据同步发生异常，请检查！：' ||  '**SQLERRM: ' || SQLERRM || '**Error line: ' || dbms_utility.format_error_backtrace() || '', 1, 4000);
      --往外层抛出异常信息
      raise_application_error(-20003, v_error_msg);
  END proc_add_financial_data_by_article;



  PROCEDURE proc_add_metering_unit(p_entityid  NUMBER,
                                   p_yearmonth VARCHAR2,
                                   p_userid    NUMBER) AS
    /***********************************************************************
    NAME : proc_add_metering_unit
    DESCRIPTION : 插入计量数据表
    DATE :2021-1-30
    AUTHOR :sry
    -------
    MODIFY LOG
    UPDATE DATE : 2022-2-10
    UPDATE BY   : cjf
    UPDATE DESC : 直接保费提取规则调整，增加临分、合约分入保费，直保已决，再保分入已决提取
  ***********************************************************************/

    v_data_status          VARCHAR2(10):= 3;
    v_period_detail_id     NUMBER(11);
    v_period_detail_status VARCHAR2(10);
    v_log_msg   VARCHAR2(4000); --日志信息
    v_error_msg VARCHAR2(4000); --异常信息
  BEGIN
    --验证源数据是否已准备
    SELECT period_state
      INTO v_data_status
      FROM dmuser.dm_conf_bussperiod
     WHERE entity_id = p_entityid
       AND year_month = p_yearmonth
       AND valid_is = '1';
    --查询当前业务期间详情id, 验证当前数据是否为准备中
    SELECT bpd.period_detail_id,
           bpd.ready_state
      INTO v_period_detail_id,
           v_period_detail_status
      FROM exp_conf_bussperiod_detail bpd
      LEFT JOIN exp_conf_bussperiod p
        ON bpd.buss_period_id = p.buss_period_id
      LEFT JOIN exp_conf_table t
        ON bpd.biz_type_id = t.biz_type_id
     WHERE p.entity_id = p_entityid
       AND p.year_month = p_yearmonth
       AND t.biz_code = 'DAP_DM_CMUNIT_DATA';


    dbms_output.put_line('源数据准备状态:' || v_data_status || ',业务期间详情id:' || v_period_detail_id || ',当前数据准备状态:' || v_period_detail_status);

    IF v_data_status != '3' THEN
      v_error_msg := '数据平台期间状态不是已完成为:' || '，计量数据不执行同步：[' || p_entityid || ']-[' || p_yearmonth || ']';
      --往外层抛出异常信息
      raise_application_error(-20002, v_error_msg);
    END IF;


    IF v_data_status >= '3'
       AND v_period_detail_status = '0' THEN
      --先清空当前业务年月数据
      DELETE FROM exp_dap_cmunit_data b
       WHERE b.entity_id = p_entityid
         AND b.year_month = p_yearmonth;

      --1 写入直接业务计量单元保费数据
      INSERT /*+APPEND */ INTO exp_dap_cmunit_data
        (cmunit_data_id, --主键
         entity_id, --财务机构
         year_month, --业务年月
         business_source_code, --业务类型
         expenses_type_code, --费用类型   1为保费，2 为已决赔款
         risk_code, --险种代码
         amount, --直保金额（原币）: expenses_type_code = 0为 保费金额，expenses_type_code = 1为已决赔款金额
         currency_code, --币别
         cmunit_no, --计量单元编号
         icg_no, --合同组编号
         portfolio_no, --合同组合编号
         dept_id, --部门组ID
         endor_signature_date, --批单签发日
         inception_date, --合同确认日
         curevaluate_date, --当前评估日期
         endorse_no, --批单号/赔案号
         effective_date, --生效日期
         expiry_date, --终止日期
         endorse_effective_date, --批改生效日期
         evaluate_approach,
         policy_no,
         task_code)
        SELECT exp_seq_dap_cmunit_data.nextval AS cmunit_data_id, --主键
               p_entityid AS entity_id, --财务机构
               p_yearmonth AS year_month, --业务年月
               business_source_code, --业务类型
               expenses_type_code, --费用类型   1为保费，2为已决赔款
               risk_code, --险种代码
               amount, --直保金额（原币）: expenses_type_code = 1为 保费金额，expenses_type_code = 2为已决赔款金额
               currency_code, --币别
               cmunit_no, --计量单元编号
               contract_group_no, --合同组编号
               portfolio_no, --合同组合编号
               dept_id, --部门组ID
               to_char(endor_signature_date,'YYYY-MM-DD HH24:MI:SS'), --批单签发日
               to_char(inception_date, 'YYYY-MM-DD HH24:MI:SS'), --合同确认日
               to_char(curevaluate_date, 'YYYY-MM-DD HH24:MI:SS'), --当前评估日期
               endor_seq_no, --批单号/赔案号
               to_char(effective_date, 'YYYY-MM-DD HH24:MI:SS'), --生效日期
               to_char(expiry_date, 'YYYY-MM-DD HH24:MI:SS'), --终止日期
               to_char(endorse_effective_date, 'YYYY-MM-DD HH24:MI:SS'), --批改生效日期
               evaluate_approach,
               policy_no,
               '' AS task_code --TASK_code|任务ID
          FROM (SELECT b.business_source_code AS business_source_code, -- 业务类型  --bussiness_type 应该是业务来源 DB-直接業務，FB-臨汾業務，TB-合約業務
                       '1' AS expenses_type_code, -- 费用类型
                       b.risk_code AS risk_code, -- 险种代码
                       SUM(t.premium) AS amount, -- 直保金额
                       t.currency_code AS currency_code, -- 币别
                       b.cmunit_no AS cmunit_no, --计量单元编号
                       b.icg_no AS contract_group_no, --合同组编号
                       b.portfolio_no AS portfolio_no, --合同组合编号
                       b.dept_id AS dept_id, --部门组ID
                       m.issue_date AS endor_signature_date, --批单签发日
                       b.border_date AS inception_date, --合同确认日 --没有该字段。只有合同确认年月year_month
                       last_day(to_date(p_yearmonth || '01', 'YYYYMMDD')) AS curevaluate_date, --当前评估日期
                       b.evaluate_approach AS evaluate_approach,
                       m.policy_no,
                       t.endorse_no AS endor_seq_no, --批单号/ 赔案号
                       t.effective_date, --生效日期
                       t.expiry_date,
                       m.endorse_effective_date --批改生效日期
                  FROM dmuser.dm_buss_cmunit_direct        b,
                       dmuser.dm_policy_main    m,
                       dmuser.dm_policy_premium t
                 WHERE b.entity_id = p_entityid
                   AND b.policy_no = m.policy_no
                   AND m.policy_no = t.policy_no
                   AND m.endorse_seq_no = t.endorse_seq_no
                   AND (((SELECT adapt_value FROM bpluser.bms_conf_broad_adapter WHERE adapt_code = 'DD_LOA') = 'product_code') OR
                       ((SELECT adapt_value FROM bpluser.bms_conf_broad_adapter WHERE adapt_code = 'DD_LOA') = 'risk_code' AND b.loa_code = t.risk_code) OR
                       ((SELECT adapt_value FROM bpluser.bms_conf_broad_adapter WHERE adapt_code = 'DD_LOA') = 'risk_class' AND b.loa_code = t.risk_class_code))
                   AND greatest(m.approval_date, nvl(m.endorse_effective_date, m.approval_date)) <= last_day(to_date(p_yearmonth || '01', 'YYYYMMDD'))
                   AND greatest(m.approval_date, nvl(m.endorse_effective_date, m.approval_date)) >= (to_date(p_yearmonth || '01', 'YYYYMMDD'))
                   AND b.year_month IS NOT NULL
                 GROUP BY b.business_source_code,
                          b.risk_code,
                          t.currency_code,
                          b.cmunit_no,
                          b.icg_no,
                          b.portfolio_no,
                          b.dept_id,
                          m.issue_date,
                          b.border_date,
                          b.evaluate_approach,
                          m.policy_no,
                          t.endorse_no,
                          t.effective_date,
                          t.expiry_date,
                          m.endorse_effective_date
              ) gg;
      COMMIT;
      --2 写入合约计量单元保费数据
      INSERT /*+APPEND */ INTO exp_dap_cmunit_data
        (cmunit_data_id, --主键
         entity_id, --财务机构
         year_month, --业务年月
         business_source_code, --业务类型
         expenses_type_code, --费用类型   1为保费，2 为已决赔款
         risk_code, --险种代码
         amount, --直保金额（原币）: expenses_type_code = 0为 保费金额，expenses_type_code = 1为已决赔款金额
         currency_code, --币别
         cmunit_no, --计量单元编号
         icg_no, --合同组编号
         portfolio_no, --合同组合编号
         dept_id, --部门组ID
         endor_signature_date, --批单签发日
         inception_date, --合同确认日
         curevaluate_date, --当前评估日期
         endorse_no, --批单号/赔案号
         effective_date, --生效日期
         expiry_date, --终止日期
         endorse_effective_date, --批改生效日期
         evaluate_approach,
         policy_no,
         task_code)
        SELECT exp_seq_dap_cmunit_data.nextval AS cmunit_data_id, --主键
               p_entityid AS entity_id, --财务机构
               p_yearmonth AS year_month, --业务年月
               business_source_code, --业务类型
               expenses_type_code, --费用类型   1为保费，2为已决赔款
               risk_code, --险种代码
               amount, --直保金额（原币）: expenses_type_code = 1为 保费金额，expenses_type_code = 2为已决赔款金额
               currency_code, --币别
               cmunit_no, --计量单元编号
               contract_group_no, --合同组编号
               portfolio_no, --合同组合编号
               dept_id, --部门组ID
               to_char(endor_signature_date, 'YYYY-MM-DD HH24:MI:SS'), --批单签发日
               to_char(inception_date, 'YYYY-MM-DD HH24:MI:SS'), --合同确认日
               to_char(curevaluate_date, 'YYYY-MM-DD HH24:MI:SS'), --当前评估日期
               endor_seq_no, --批单号/赔案号
               to_char(effective_date, 'YYYY-MM-DD HH24:MI:SS'), --生效日期
               to_char(expiry_date, 'YYYY-MM-DD HH24:MI:SS'), --终止日期
               to_char(endorse_effective_date, 'YYYY-MM-DD HH24:MI:SS'), --批改生效日期
               evaluate_approach,
               policy_no,
               '' AS task_code --TASK_code|任务ID
          FROM (SELECT 'TB' AS business_source_code,
                       '1' AS expenses_type_code, -- 费用类型
                       rbd.risk_code, -- 险种代码
                       SUM(m.premium) AS amount, -- 直保金额
                       b.currency_code AS currency_code, -- 币别
                       b.cmunit_no, --计量单元编号
                       b.icg_no AS contract_group_no, --合同组编号
                       b.portfolio_no, --合同组合编号
                       rbd.dept_id AS dept_id, --b.dept_id,  --部门组ID --没有部门字段？？？？
                       NULL AS endor_signature_date, --批单签发日
                       b.border_date AS inception_date, --合同确认日 --没有该字段。只有合同确认年月year_month
                       last_day(to_date(p_yearmonth || '01', 'YYYYMMDD')) AS curevaluate_date, --当前评估日期
                       b.evaluate_approach AS evaluate_approach,
                       '' AS policy_no,
                       '' AS endor_seq_no, --批单号/赔案号
                       b.effective_date, --生效日期
                       b.expiry_date, --终止日期
                       NULL AS endorse_effective_date --批改生效日期
                  FROM dmuser.dm_buss_cmunit_treaty      b,
                       dmuser.dm_reins_treaty        m,
                       dmuser.dm_reins_bill          a,
                       dmuser.dm_reins_bill_detail rbd,
                       bpluser.bbs_conf_fee_type_mapping fee
                 WHERE b.entity_id = p_entityid
                      --and b.year_month = p_yearmonth
                   AND b.ri_direction_code = 'I'
                   AND b.treaty_no = m.treaty_no
                   AND b.treaty_no = a.treaty_no
                   AND b.ri_direction_code = m.ri_direction_code
                   AND b.ri_direction_code = a.ri_direction_code
                   AND a.entity_id = rbd.entity_id AND a.ri_statement_no = rbd.ri_statement_no
                   AND rbd.expenses_type_code = fee.expenses_type_code
                   AND fee.business_source_code = 'TB'
                   AND fee.fee_class = 'Premium'
                   AND a.statement_approval_date <= last_day(to_date(p_yearmonth || '01', 'YYYYMMDD'))
                   AND a.statement_approval_date >= (to_date(p_yearmonth || '01', 'YYYYMMDD'))
                   AND b.year_month IS NOT NULL
                 GROUP BY rbd.risk_code,
                          b.currency_code,
                          b.cmunit_no,
                          b.icg_no,
                          b.portfolio_no,
                          rbd.dept_id,
                          b.border_date,
                          b.evaluate_approach,
                          b.effective_date,
                          b.expiry_date
              ) gg;
      COMMIT;
      --3 写入直保计量单元赔款数据
      INSERT /*+APPEND */ INTO exp_dap_cmunit_data
        (cmunit_data_id, --主键
         entity_id, --财务机构
         year_month, --业务年月
         business_source_code, --业务类型
         expenses_type_code, --费用类型   1为保费，2 为已决赔款
         risk_code, --险种代码
         amount, --直保金额（原币）: expenses_type_code = 0为 保费金额，expenses_type_code = 1为已决赔款金额
         currency_code, --币别
         cmunit_no, --计量单元编号
         icg_no, --合同组编号
         portfolio_no, --合同组合编号
         dept_id, --部门组ID
         endor_signature_date, --批单签发日
         inception_date, --合同确认日
         curevaluate_date, --当前评估日期
         endorse_no, --批单号/赔案号
         effective_date, --生效日期
         expiry_date, --终止日期
         endorse_effective_date, --批改生效日期
         evaluate_approach,
         policy_no,
         task_code)
        SELECT exp_seq_dap_cmunit_data.nextval AS cmunit_data_id, --主键
               p_entityid AS entity_id, --财务机构
               p_yearmonth AS year_month, --业务年月
               business_source_code, --业务类型
               expenses_type_code, --费用类型   1为保费，2为已决赔款
               risk_code, --险种代码
               amount, --直保金额（原币）: expenses_type_code = 1为 保费金额，expenses_type_code = 2为已决赔款金额
               currency_code, --币别
               cmunit_no, --计量单元编号
               contract_group_no, --合同组编号
               portfolio_no, --合同组合编号
               dept_id, --部门组ID
               to_char(endor_signature_date, 'YYYY-MM-DD HH24:MI:SS'), --批单签发日
               to_char(inception_date, 'YYYY-MM-DD HH24:MI:SS'), --合同确认日
               to_char(curevaluate_date, 'YYYY-MM-DD HH24:MI:SS'), --当前评估日期
               endor_seq_no, --批单号/赔案号
               to_char(effective_date, 'YYYY-MM-DD HH24:MI:SS'), --生效日期
               to_char(expiry_date, 'YYYY-MM-DD HH24:MI:SS'), --终止日期
               to_char(endorse_effective_date, 'YYYY-MM-DD HH24:MI:SS'), --批改生效日期
               evaluate_approach,
               policy_no,
               '' AS task_code --TASK_code|任务ID
          FROM (SELECT b.business_source_code AS business_source_code,
                       '2' AS expenses_type_code, -- 费用类型
                       b.risk_code, -- 险种代码
                       SUM(c.amount) AS amount, -- 直保金额
                       c.currency_code AS currency_code, -- 币别
                       b.cmunit_no, --计量单元编号
                       b.icg_no AS contract_group_no, --合同组编号
                       b.portfolio_no, --合同组合编号
                       b.dept_id AS dept_id, --部门组ID
                       m.issue_date AS endor_signature_date, --批单签发日
                       b.border_date AS inception_date, --合同确认日 --没有该字段。只有合同确认年月year_month
                       last_day(to_date(p_yearmonth || '01', 'YYYYMMDD')) AS curevaluate_date, --当前评估日期
                       b.evaluate_approach AS evaluate_approach, --评估方法
                       b.policy_no, --保单号
                       c.claim_loss_seq_no  AS endor_seq_no, --c.claim_no as endor_seq_no,  --批单号/赔案号
                       m.effective_date, --生效日期
                       m.expiry_date, --终止日期
                       m.endorse_effective_date --批改生效日期
                  FROM dmuser.dm_buss_cmunit_direct        b,
                       dmuser.dm_policy_main    m,
                       dmuser.dm_claim_loss     c
                 WHERE c.entity_id = p_entityid
                   AND to_char(c.gl_posting_date, 'YYYYMM') = p_yearmonth
                   AND b.entity_id = m.entity_id
                   AND m.entity_id = c.entity_id
                   AND b.policy_no = m.policy_no
                   AND m.policy_no = c.policy_no
                   AND m.endorse_seq_no = (SELECT endorse_seq_no FROM dmuser.dm_claim_main a WHERE a.claim_no=c.claim_no AND ROWNUM = 1)
                   AND b.year_month IS NOT NULL
                   GROUP BY
                       b.business_source_code,
                       b.risk_code,
                       c.currency_code,
                       b.cmunit_no,
                       b.icg_no,
                       b.portfolio_no,
                       b.dept_id,
                       m.issue_date ,
                       b.border_date,
                       b.evaluate_approach,
                       b.policy_no,
                       c.claim_loss_seq_no ,
                       m.effective_date,
                       m.expiry_date,
                       m.endorse_effective_date
              ) gg;
      COMMIT;

      --4 写入合约计量单元赔款数据
      INSERT /*+APPEND */ INTO exp_dap_cmunit_data
        (cmunit_data_id, --主键
         entity_id, --财务机构
         year_month, --业务年月
         business_source_code, --业务类型
         expenses_type_code, --费用类型   1为保费，2 为已决赔款
         risk_code, --险种代码
         amount, --直保金额（原币）: expenses_type_code = 0为 保费金额，expenses_type_code = 1为已决赔款金额
         currency_code, --币别
         cmunit_no, --计量单元编号
         icg_no, --合同组编号
         portfolio_no, --合同组合编号
         dept_id, --部门组ID
         endor_signature_date, --批单签发日
         inception_date, --合同确认日
         curevaluate_date, --当前评估日期
         endorse_no, --批单号/赔案号
         effective_date, --生效日期
         expiry_date, --终止日期
         endorse_effective_date, --批改生效日期
         evaluate_approach,
         policy_no,
         task_code)
        SELECT exp_seq_dap_cmunit_data.nextval AS cmunit_data_id, --主键
               p_entityid AS entity_id, --财务机构
               p_yearmonth AS year_month, --业务年月
               business_source_code, --业务类型
               expenses_type_code, --费用类型   1为保费，2为已决赔款
               risk_code, --险种代码
               amount, --直保金额（原币）: expenses_type_code = 1为 保费金额，expenses_type_code = 2为已决赔款金额
               currency_code, --币别
               cmunit_no, --计量单元编号
               contract_group_no, --合同组编号
               portfolio_no, --合同组合编号
               dept_id, --部门组ID
               to_char(endor_signature_date, 'YYYY-MM-DD HH24:MI:SS'), --批单签发日
               to_char(inception_date, 'YYYY-MM-DD HH24:MI:SS'), --合同确认日
               to_char(curevaluate_date, 'YYYY-MM-DD HH24:MI:SS'), --当前评估日期
               endor_seq_no, --批单号/赔案号
               to_char(effective_date, 'YYYY-MM-DD HH24:MI:SS'), --生效日期
               to_char(expiry_date, 'YYYY-MM-DD HH24:MI:SS'), --终止日期
               to_char(endorse_effective_date, 'YYYY-MM-DD HH24:MI:SS'), --批改生效日期
               evaluate_approach,
               policy_no,
               '' AS task_code
          FROM (SELECT 'TB' AS business_source_code,
                       '2' AS expenses_type_code, -- 费用类型
                       rbd.risk_code, -- 险种代码
                       SUM(rbd.amount) AS amount, -- 直保金额
                       b.currency_code AS currency_code, -- 币别
                       b.cmunit_no, --计量单元编号
                       b.icg_no AS contract_group_no, --合同组编号
                       b.portfolio_no, --合同组合编号
                       rbd.dept_id AS dept_id, --a.dept_id,  --部门组ID --没有部门字段？？？？
                       b.issue_date AS endor_signature_date, --批单签发日
                       b.border_date AS inception_date, --合同确认日 --没有该字段。只有合同确认年月year_month
                       last_day(to_date(p_yearmonth || '01', 'YYYYMMDD')) AS curevaluate_date, --当前评估日期
                       b.evaluate_approach AS evaluate_approach,
                       '' AS policy_no,
                       '' AS endor_seq_no, --批单号/赔案号
                       b.effective_date, --生效日期
                       b.expiry_date, --终止日期
                       NULL endorse_effective_date --批改生效日期
                  FROM dmuser.dm_buss_cmunit_treaty b,
                       dmuser.dm_reins_bill     a,
                      dmuser.dm_reins_bill_detail rbd
                 WHERE b.entity_id = p_entityid
                   AND b.entity_id = a.entity_id
                   AND b.ri_direction_code = 'I'
                   AND b.ri_direction_code = a.ri_direction_code
                   AND to_char(a.statement_approval_date, 'YYYYMM') = p_yearmonth
                   AND b.treaty_no = a.treaty_no
                   AND a.entity_id = rbd.entity_id AND a.ri_statement_no = rbd.ri_statement_no
                   AND rbd.expenses_type_code IN ('D03', 'D17')
                   AND b.year_month IS NOT NULL
                 GROUP BY a.entity_id,
                          rbd.risk_code,
                          b.currency_code,
                          b.cmunit_no,
                          b.icg_no,
                          b.portfolio_no,
                          b.issue_date,
                          b.border_date,
                          b.evaluate_approach,
                          b.effective_date,
                          b.expiry_date,
                          rbd.dept_id
              ) gg;
      COMMIT;

      --修改exp_conf_bussperiod_detail为已准备状态
      UPDATE exp_conf_bussperiod_detail
         SET ready_state = '1',
             exec_result = 'success',
             task_time   = localtimestamp
       WHERE period_detail_id = v_period_detail_id;
      COMMIT;

      --同步业务期间执行状态
      expuser.exp_pack_buss_period.proc_period_execution(p_entityid, '1');
    END IF;
  EXCEPTION
    WHEN OTHERS THEN
      IF v_period_detail_id IS NOT NULL THEN
        --修改exp_conf_bussperiod_detail为失败状态
        UPDATE exp_conf_bussperiod_detail
           SET ready_state = '0',
               exec_result = 'failed',
               task_time   = localtimestamp
         WHERE period_detail_id = v_period_detail_id;
          --提交事务
        COMMIT;
      END IF;
      --抛出自定义异常信息 【会中断事务】
      v_error_msg := substr('计量同步发生异常，请检查！：' ||  '**SQLERRM: ' || SQLERRM || '**Error line: ' || dbms_utility.format_error_backtrace() || '', 1, 4000);
      --往外层抛出异常信息
      raise_application_error(-20003, v_error_msg);

  END proc_add_metering_unit;

  FUNCTION func_transition_period_processing(p_entityid  NUMBER,
                                 p_yearmonth VARCHAR2,
                                 p_userid    NUMBER) RETURN NUMBER AS
    /***********************************************************************
    NAME : proc_transition_period_processing
    DESCRIPTION : 过渡期处理，2022年以前不走费用分摊
    ***********************************************************************/
  v_msg_code       varchar(200):='过度期';
  v_bookcode       varchar(60);
  v_task_code      VARCHAR2(64);
  v_error_msg      VARCHAR2(4000); --异常信息
  v_period_detail_count NUMBER;
  v_trans_period_count  NUMBER;
  v_transition_Period   varchar(6);
  BEGIN

    SELECT COUNT(T.code_code) INTO v_trans_period_count
      FROM EXP_V_CONF_CODE T
     WHERE UPPER_CODE_ID =
           (SELECT T.CODE_ID
              FROM EXP_V_CONF_CODE T
             WHERE CODE_CODE = 'TransitionPeriod');
     IF v_trans_period_count IS NULL OR v_trans_period_count = 0 THEN
        RETURN 0;
     END IF;

    SELECT T.code_code INTO v_transition_Period
      FROM EXP_V_CONF_CODE T
     WHERE UPPER_CODE_ID =
           (SELECT T.CODE_ID
              FROM EXP_V_CONF_CODE T
             WHERE CODE_CODE = 'TransitionPeriod')
     ORDER BY CODE_CODE DESC
     FETCH NEXT 1 rows ONLY;


    IF p_yearmonth<= v_transition_Period THEN
      dbms_output.put_line('进入过渡期' || p_yearmonth );
      v_bookcode := bpl_pack_common.func_get_accountSet(p_entityid);
      v_task_code := bpl_pack_common.func_get_taskcode('EXP','M','DEP');

       IF v_bookcode IS NULL OR v_task_code IS NULL THEN
        --往外层抛出异常信息
        raise_application_error(-20002, '现行账套不存或任务号获取不成功');
       END IF;

      SELECT COUNT(bpd.period_detail_id)
      INTO v_period_detail_count
      FROM exp_conf_bussperiod_detail bpd
      LEFT JOIN exp_conf_bussperiod p
        ON bpd.buss_period_id = p.buss_period_id
      LEFT JOIN exp_conf_table t
        ON bpd.biz_type_id = t.biz_type_id
     WHERE p.entity_id = p_entityid
       AND p.year_month = p_yearmonth;

       IF v_period_detail_count IS NULL OR v_period_detail_count=0  THEN
        --往外层抛出异常信息
        raise_application_error(-20002, '期间不存在');
       END IF;

      --修改exp_conf_bussperiod_detail为已准备状态
      UPDATE EXP_CONF_BUSSPERIOD_DETAIL
         SET READY_STATE = '1',
             EXEC_RESULT = 'success',
             TASK_TIME   = LOCALTIMESTAMP
       WHERE BUSS_PERIOD_ID =
             (SELECT BUSS_PERIOD_ID
                FROM EXP_CONF_BUSSPERIOD
               WHERE entity_id = p_entityid
                 AND YEAR_MONTH = p_yearmonth)
         AND DIRECTION = '1';
      --同步业务期间执行状态
      expuser.exp_pack_buss_period.proc_period_execution(p_entityid, '1');
      COMMIT;

      FOR rec_proc IN(
        SELECT T.*
          FROM bpluser.bms_conf_action_procdef T
         WHERE SYSTEM_CODE = 'EXP'
           AND LEVEL = 3
           AND SUBSTR(T.SCENERIO_CODE, 1, 1) = '1'
         START WITH T.PROC_ID = (SELECT T.PROC_ID
                                   FROM bpluser.bms_conf_action_procdef T
                                  WHERE T.PROC_CODE = 'EXPROOT')
        CONNECT BY PRIOR T.PROC_ID = T.PARENT_PROC_ID
       ) LOOP
          -- 更新业务日志表
          bpluser.bpl_pack_action_log.proc_save_actionlog(v_task_code,
                                                  'EXP',
                                                  p_entityid,
                                                  v_bookcode,
                                                  p_yearmonth,
                                                  rec_proc.proc_id,
                                                  rec_proc.parent_proc_id,
                                                  '1', --0-不通过; 1-通过
                                                  '2',
                                                  '',
                                                  v_msg_code,
                                                  p_userid --创建人员
                                             );

        END loop;

        --修改exp_conf_bussperiod_detail为已准备状态
        UPDATE EXP_CONF_BUSSPERIOD_DETAIL
           SET READY_STATE = '1',
               EXEC_RESULT = 'success',
               TASK_TIME   = LOCALTIMESTAMP
         WHERE BUSS_PERIOD_ID =
               (SELECT BUSS_PERIOD_ID
                  FROM EXP_CONF_BUSSPERIOD
                 WHERE entity_id = p_entityid
                   AND YEAR_MONTH = p_yearmonth)
           AND DIRECTION = '0';
        --同步业务期间执行状态
        --expuser.exp_pack_buss_period.proc_period_execution(p_entityid, '3');
        COMMIT;
        RETURN 1;
      ELSE
        RETURN 0;
      END IF;
  EXCEPTION
    WHEN OTHERS THEN
      UPDATE exp_conf_bussperiod_detail
         SET ready_state = '0',
             exec_result = 'failed',
             task_time   = localtimestamp
       WHERE BUSS_PERIOD_ID =
             (SELECT BUSS_PERIOD_ID
                FROM EXP_CONF_BUSSPERIOD
               WHERE entity_id = p_entityid
                 AND YEAR_MONTH = p_yearmonth);
      --抛出自定义异常信息 【会中断事务】
      v_error_msg := substr('过渡期发生异常，请检查！：' ||  '**SQLERRM: ' || SQLERRM || '**Error line: ' || dbms_utility.format_error_backtrace() || '', 1, 4000);
      --往外层抛出异常信息
      raise_application_error(-20003, v_error_msg);
      RETURN 1;
  END func_transition_period_processing;
  
  PROCEDURE proc_sync_exp_account(p_entityid  NUMBER) AS
    v_error_msg VARCHAR2(4000); --异常信息
    v_data_count   NUMBER(11);
  BEGIN  
 
    UPDATE exp_conf_recognitiondef T
       SET ACCOUNT_ID =
           (SELECT AC.ACCOUNT_ID
              FROM BPLUSER.BBS_ACCOUNT AC
             WHERE AC.ACCOUNT_CODE =
                   (SELECT T2.REMARK
                      FROM BPLUSER.BPL_CONF_CODE T2
                     WHERE T2.CODE_CODE LIKE '%OE%'
                       AND T2.Upper_Code_Id =
                           (SELECT c.Code_Id
                              FROM BPLUSER.BPL_V_CONF_CODE c
                             WHERE c.code_code_idx = 'FeeType')
                       AND T2.CODE_CODE =
                           (SELECT T1.EXPENSES_TYPE_CODE
                              FROM EXP_CONF_ACCOUNT T1
                             WHERE T1.ACCOUNT_ID = t.account_id)))
     WHERE t.entity_id=p_entityid AND 1 = 1
       AND EXISTS
     (SELECT AC.ACCOUNT_ID
              FROM BPLUSER.BBS_ACCOUNT AC
             WHERE AC.ACCOUNT_CODE =
                   (SELECT T2.REMARK
                      FROM BPLUSER.BPL_CONF_CODE T2
                     WHERE T2.CODE_CODE LIKE '%OE%'
                       AND T2.Upper_Code_Id =
                           (SELECT c.Code_Id
                              FROM BPLUSER.BPL_V_CONF_CODE c
                             WHERE c.code_code_idx = 'FeeType')
                       AND T2.CODE_CODE =
                           (SELECT T1.EXPENSES_TYPE_CODE
                              FROM EXP_CONF_ACCOUNT T1
                             WHERE T1.ACCOUNT_ID = t.account_id)));
                                    
                                    
     
    UPDATE exp_conf_benefitruleref T SET ACCOUNT_ID= (SELECT AC.ACCOUNT_ID
      FROM BPLUSER.BBS_ACCOUNT AC
     WHERE AC.ACCOUNT_CODE =
           (SELECT T2.REMARK
              FROM BPLUSER.BPL_CONF_CODE T2
             WHERE T2.CODE_CODE LIKE '%OE%' AND T2.Upper_Code_Id = (SELECT c.Code_Id FROM BPLUSER.BPL_V_CONF_CODE c WHERE c.code_code_idx='FeeType' )
               AND T2.CODE_CODE = (SELECT T1.EXPENSES_TYPE_CODE
                                     FROM EXP_CONF_ACCOUNT T1
                                    WHERE T1.ACCOUNT_ID = t.account_id)))  
      WHERE t.entity_id = p_entityid AND 1=1 AND EXISTS (SELECT AC.ACCOUNT_ID
      FROM BPLUSER.BBS_ACCOUNT AC
     WHERE AC.ACCOUNT_CODE =
           (SELECT T2.REMARK
              FROM BPLUSER.BPL_CONF_CODE T2
             WHERE T2.CODE_CODE LIKE '%OE%' AND T2.Upper_Code_Id = (SELECT c.Code_Id FROM BPLUSER.BPL_V_CONF_CODE c WHERE c.code_code_idx='FeeType' )
               AND T2.CODE_CODE = (SELECT T1.EXPENSES_TYPE_CODE
                                     FROM EXP_CONF_ACCOUNT T1
                                    WHERE T1.ACCOUNT_ID = t.account_id)));
                                    
                                    
     

    UPDATE EXP_CONF_ACCOUNT T
       SET ACCOUNT_ID =
           (SELECT AC.ACCOUNT_ID
              FROM BPLUSER.BBS_ACCOUNT AC
             WHERE AC.ACCOUNT_CODE = (SELECT T1.REMARK
                                        FROM BPLUSER.BPL_CONF_CODE T1
                                       WHERE T1.CODE_CODE LIKE '%OE%'
                                         AND T1.UPPER_CODE_ID =
                                             (SELECT C.CODE_ID
                                                FROM BPLUSER.BPL_V_CONF_CODE C
                                               WHERE C.CODE_CODE_IDX = 'FeeType')
                                         AND T1.CODE_CODE = T.EXPENSES_TYPE_CODE
                                      
                                      ))
     WHERE T.ENTITY_ID = p_entityid
       AND 1 = 1
       AND EXISTS
     (SELECT 1
              FROM BPLUSER.BBS_ACCOUNT AC
             WHERE AC.ACCOUNT_CODE = (SELECT T1.REMARK
                                        FROM BPLUSER.BPL_CONF_CODE T1
                                       WHERE T1.CODE_CODE LIKE '%OE%'
                                         AND T1.UPPER_CODE_ID =
                                             (SELECT C.CODE_ID
                                                FROM BPLUSER.BPL_V_CONF_CODE C
                                               WHERE C.CODE_CODE_IDX = 'FeeType')
                                         AND T1.CODE_CODE = T.EXPENSES_TYPE_CODE
                                      
                                      ));
    commit;
    
    
    
    --1 待摊科目配置
    INSERT INTO expuser.exp_conf_account
      (account_id,
       entity_id,
       book_code,
       valid_is,
       audit_state,
       creator_id,
       create_time,
       SERIAL_NO,
       account_code)
      SELECT item.account_id,
             item.entity_id,
             item.book_code,
             '1' as valid_is,
             item.audit_state,
             item.creator_id,
             item.create_time,
             1 AS SERIAL_NO,
             item.account_code
        FROM bpluser.bbs_account item
       WHERE item.entity_id = 1
         AND item.final_level_is = '1'
         AND item.valid_is = '1'
         AND item.book_code = 'BookI4'
         AND account_code in ( 
            '6402/03/',
            '6411/02/01/02/',
            '6421/01/01/04/',
            '6421/01/01/06/',
            '6421/01/01/07/01/',
            '6421/01/01/09/01/',
            '6421/01/99/',
            '6421/02/01/',
            '6542/01/05/01/',
            '6542/01/05/02/',
            '6542/03/01/',
            '6711/02/',
            '6711/04/01/',
            '6711/04/02/',
            '6711/05/01/'
         )
         AND NOT EXISTS (SELECT 1
                FROM expuser.exp_conf_account code
               WHERE item.account_id = code.account_id)
       ORDER BY item.account_code;

    --2 科目适配指认规则
    INSERT INTO expuser.exp_conf_benefitruleref
      (rule_ref_id,
       entity_id,
       book_code,
       account_id,
       rule_id,
       design_is,
       valid_is,
       audit_state,
       creator_id,
       create_time)
      SELECT expuser.exp_seq_benefitruleref.nextval,
             entity_id,
             book_code,
             account_id,
             (SELECT rule_id
                FROM expuser.exp_conf_benefitrule
               WHERE valid_is = '1'
                 AND audit_state = '1'
                 and rule_code = 'Number001'
                 and rownum = 1),
             '0' AS design_is,
             valid_is,
             audit_state,
             creator_id,
             create_time
        FROM expuser.exp_conf_account A
       WHERE A.valid_is = '1'
         AND A.audit_state = '1'
         AND A.book_code = 'BookI4'
         AND NOT EXISTS
       (SELECT 1 FROM exp_conf_benefitruleref REF WHERE A.account_id = REF.account_id);

    --3 科目适配指认部门
    INSERT INTO expuser.exp_conf_benefitrulerefdept
      (rule_ref_dept_id, rule_ref_id, entity_id, creator_id, create_time)
      SELECT expuser.exp_seq_benefitrulerefdept.nextval,
             REF.rule_ref_id,
             ac.entity_id AS entity_id,
             REF.creator_id,
             REF.create_time
        FROM expuser.exp_conf_account A
        LEFT JOIN expuser.exp_conf_benefitruleref REF
          ON A.account_id = REF.account_id
        LEFT JOIN bpluser.bbs_entity ac
          ON 1 = 1
       WHERE A.valid_is = '1'
         AND A.audit_state = '1'
         AND A.book_code = 'BookI4'
         AND ac.entity_code in ('0102', '0104', '0107', '010928', '0130')
         AND NOT EXISTS (SELECT 1
                FROM exp_CONF_benefitrulerefdept r
               WHERE r.rule_ref_id = REF.rule_ref_id
                 AND r.entity_id = ac.entity_id);
                 
                 
                 
    --4、配置费用指认配置
    INSERT INTO EXP_CONF_RECOGNITIONDEF
      (RECOGNITION_DEF_ID,
       entity_id,
       BOOK_CODE,
       account_id,
       FEE_CONFIG_STATE,
       VALID_IS,
       creator_id,
       CREATE_TIME,
       AUDIT_STATE)
      SELECT expuser.exp_seq_RECOGNITIONDEF.nextval,
             entity_id,
             book_code,
             account_id,
             '0' AS FEE_CONFIG_STATE,
             valid_is,
             creator_id,
             create_time,
             audit_state
        FROM expuser.exp_conf_account A
       WHERE a.valid_is = '1'
         AND a.audit_state = '1'
         AND book_code = 'BookI4'
         AND NOT EXISTS
       (SELECT 1 FROM EXP_CONF_RECOGNITIONDEF REF WHERE A.account_id = REF.account_id);
       COMMIT;
 
 
    --插入业务期间详情任务配置
 
      --1、循环xxx_conf_period
        FOR cur_table IN (
          SELECT account_id FROM bpluser.bbs_account t WHERE book_code ='BookI4' AND account_code IN (  
            '6421/01/01/04/',
            '6421/01/01/06/',
            '6421/01/01/07/01/',
            '6421/01/01/09/01/',
            '6421/01/99/',
            '6421/02/01/',
            '6542/01/05/01/',
            '6542/01/05/02/',
            '6542/03/01/',
			'6421/01/01/01/',
			'6421/01/01/10/')
          ) LOOP
       
            insert into expuser.exp_conf_Recognitiondefdetail
              (RECOGNITION_DEF_DET_ID,
               RECOGNITION_DEF_ID,
               BENF_entity_id,
               NFCF_RATE,
               ACQ_FEE_RATE,
               MTC_FEE_RATE,
               LOSS_FEE_RATE,
               VALID_IS,
               CREATOR_ID,
               CREATE_TIME)            
            select 
               expuser.exp_seq_recognitiondefdetail.nextval as RECOGNITION_DEF_DET_ID,
               a.RECOGNITION_DEF_ID,
               ac.entity_id AS BENF_entity_id,
               0 AS NFCF_RATE,
               100 as ACQ_FEE_RATE,
               0 as MTC_FEE_RATE,
               0 as LOSS_FEE_RATE,
               a.VALID_IS,
               a.CREATOR_ID,
               a.CREATE_TIME
            from EXP_CONF_RECOGNITIONDEF a
               LEFT JOIN bpluser.bbs_entity ac
                  ON 1 = 1
               WHERE A.valid_is = '1'
                 AND A.audit_state = '1'
                 and a.FEE_CONFIG_STATE='0'
                 AND A.book_code = 'BookI4'
                 AND a.account_id=cur_table.account_id
                 AND ac.entity_code in ('0102', '0104', '0107', '010928', '0130')
                 AND NOT EXISTS (SELECT 1
                        FROM Exp_CONF_Recognitiondefdetail r
                       WHERE r.RECOGNITION_DEF_ID = a.RECOGNITION_DEF_ID
                         AND r.benf_entity_id = ac.entity_id);         
              --提交事务
            COMMIT;
        END LOOP;
        
        
      --2、循环xxx_conf_period
        FOR cur_acq IN (
          SELECT account_id FROM bpluser.bbs_account t WHERE book_code ='BookI4' AND account_code IN ( 
            '6402/03/',
            '6411/02/01/02/',
            '6711/02/',
            '6711/04/01/',
            '6711/04/02/',
            '6711/05/01/')
          ) LOOP
       
            insert into expuser.exp_conf_Recognitiondefdetail
              (RECOGNITION_DEF_DET_ID,
               RECOGNITION_DEF_ID,
               BENF_entity_id,
               NFCF_RATE,
               ACQ_FEE_RATE,
               MTC_FEE_RATE,
               LOSS_FEE_RATE,
               VALID_IS,
               CREATOR_ID,
               CREATE_TIME)            
            select 
               expuser.exp_seq_recognitiondefdetail.nextval as RECOGNITION_DEF_DET_ID,
               a.RECOGNITION_DEF_ID,
               ac.entity_id AS BENF_entity_id,
               100 AS NFCF_RATE,
               0 as ACQ_FEE_RATE,
               0 as MTC_FEE_RATE,
               0 as LOSS_FEE_RATE,
               a.VALID_IS,
               a.CREATOR_ID,
               a.CREATE_TIME
            from EXP_CONF_RECOGNITIONDEF a
               LEFT JOIN bpluser.bbs_entity ac
                  ON 1 = 1
               WHERE A.valid_is = '1'
                 AND A.audit_state = '1'
                 and a.FEE_CONFIG_STATE='0'
                 AND A.book_code = 'BookI4'
                 AND a.account_id=cur_acq.account_id
                 AND ac.entity_code in ('0102', '0104', '0107', '010928', '0130')
                 AND NOT EXISTS (SELECT 1
                        FROM Exp_CONF_Recognitiondefdetail r
                       WHERE r.RECOGNITION_DEF_ID = a.RECOGNITION_DEF_ID
                         AND r.benf_entity_id = ac.entity_id);         
              --提交事务
            COMMIT;
        END LOOP; 
 

    update EXP_CONF_RECOGNITIONDEF a
       set FEE_CONFIG_STATE = '1'
     where FEE_CONFIG_STATE = '0'
       and exists
     (select 1
              from Exp_CONF_Recognitiondefdetail r
             where r.RECOGNITION_DEF_ID = a.RECOGNITION_DEF_ID);
    COMMIT;  

    --费用小类更新 
    UPDATE exp_conf_account SET EXPENSES_TYPE_CODE = 'OE126' WHERE ACCOUNT_ID = (SELECT T.ACCOUNT_ID FROM bpluser.BBS_ACCOUNT T WHERE ACCOUNT_CODE='6402/03/');
    UPDATE exp_conf_account SET EXPENSES_TYPE_CODE = 'OE127' WHERE ACCOUNT_ID = (SELECT T.ACCOUNT_ID FROM bpluser.BBS_ACCOUNT T WHERE ACCOUNT_CODE='6411/02/01/02/');
    UPDATE exp_conf_account SET EXPENSES_TYPE_CODE = 'OE128' WHERE ACCOUNT_ID = (SELECT T.ACCOUNT_ID FROM bpluser.BBS_ACCOUNT T WHERE ACCOUNT_CODE='6421/01/01/04/');
    UPDATE exp_conf_account SET EXPENSES_TYPE_CODE = 'OE129' WHERE ACCOUNT_ID = (SELECT T.ACCOUNT_ID FROM bpluser.BBS_ACCOUNT T WHERE ACCOUNT_CODE='6421/01/01/06/');
    UPDATE exp_conf_account SET EXPENSES_TYPE_CODE = 'OE130' WHERE ACCOUNT_ID = (SELECT T.ACCOUNT_ID FROM bpluser.BBS_ACCOUNT T WHERE ACCOUNT_CODE='6421/01/01/07/01/');
    UPDATE exp_conf_account SET EXPENSES_TYPE_CODE = 'OE131' WHERE ACCOUNT_ID = (SELECT T.ACCOUNT_ID FROM bpluser.BBS_ACCOUNT T WHERE ACCOUNT_CODE='6421/01/01/09/01/');
    UPDATE exp_conf_account SET EXPENSES_TYPE_CODE = 'OE132' WHERE ACCOUNT_ID = (SELECT T.ACCOUNT_ID FROM bpluser.BBS_ACCOUNT T WHERE ACCOUNT_CODE='6421/01/99/');
    UPDATE exp_conf_account SET EXPENSES_TYPE_CODE = 'OE133' WHERE ACCOUNT_ID = (SELECT T.ACCOUNT_ID FROM bpluser.BBS_ACCOUNT T WHERE ACCOUNT_CODE='6421/02/01/');
    UPDATE exp_conf_account SET EXPENSES_TYPE_CODE = 'OE134' WHERE ACCOUNT_ID = (SELECT T.ACCOUNT_ID FROM bpluser.BBS_ACCOUNT T WHERE ACCOUNT_CODE='6542/01/05/01/');
    UPDATE exp_conf_account SET EXPENSES_TYPE_CODE = 'OE135' WHERE ACCOUNT_ID = (SELECT T.ACCOUNT_ID FROM bpluser.BBS_ACCOUNT T WHERE ACCOUNT_CODE='6542/01/05/02/');
    UPDATE exp_conf_account SET EXPENSES_TYPE_CODE = 'OE136' WHERE ACCOUNT_ID = (SELECT T.ACCOUNT_ID FROM bpluser.BBS_ACCOUNT T WHERE ACCOUNT_CODE='6542/03/01/');
    UPDATE exp_conf_account SET EXPENSES_TYPE_CODE = 'OE137' WHERE ACCOUNT_ID = (SELECT T.ACCOUNT_ID FROM bpluser.BBS_ACCOUNT T WHERE ACCOUNT_CODE='6711/02/');
    UPDATE exp_conf_account SET EXPENSES_TYPE_CODE = 'OE138' WHERE ACCOUNT_ID = (SELECT T.ACCOUNT_ID FROM bpluser.BBS_ACCOUNT T WHERE ACCOUNT_CODE='6711/04/01/');
    UPDATE exp_conf_account SET EXPENSES_TYPE_CODE = 'OE139' WHERE ACCOUNT_ID = (SELECT T.ACCOUNT_ID FROM bpluser.BBS_ACCOUNT T WHERE ACCOUNT_CODE='6711/04/02/');
    UPDATE exp_conf_account SET EXPENSES_TYPE_CODE = 'OE140' WHERE ACCOUNT_ID = (SELECT T.ACCOUNT_ID FROM bpluser.BBS_ACCOUNT T WHERE ACCOUNT_CODE='6711/05/01/');
    UPDATE exp_conf_account SET EXPENSES_TYPE_CODE = 'OE141' WHERE ACCOUNT_ID = (SELECT T.ACCOUNT_ID FROM bpluser.BBS_ACCOUNT T WHERE ACCOUNT_CODE='6421/01/01/01/');
    UPDATE exp_conf_account SET EXPENSES_TYPE_CODE = 'OE142' WHERE ACCOUNT_ID = (SELECT T.ACCOUNT_ID FROM bpluser.BBS_ACCOUNT T WHERE ACCOUNT_CODE='6421/01/01/10/');
    COMMIT;

    
  EXCEPTION
    WHEN OTHERS THEN
      --抛出自定义异常信息 【会中断事务】
      v_error_msg := substr('计量同步发生异常，请检查！：' ||  '**SQLERRM: ' || SQLERRM || '**Error line: ' || dbms_utility.format_error_backtrace() || '', 1, 4000);
      --往外层抛出异常信息
      raise_application_error(-20003, v_error_msg);
  END proc_sync_exp_account;
  
END exp_pack_data_sync;
/
