[2m2025-07-21 10:22:02.445[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m The following profiles are active: dev
[2m2025-07-21 10:22:02.895[0;39m [dev] [33m WARN[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-21 10:22:02.987[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=ff1a2d2c-d19d-3931-a591-81b4348e9e38
[2m2025-07-21 10:22:03.236[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 7602 (http)
[2m2025-07-21 10:22:03.285[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 828 ms
[2m2025-07-21 10:22:03.328[0;39m [dev] [33m WARN[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-21 10:22:03.328[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-21 10:22:03.335[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.netflix.config.DynamicPropertyFactory [0;39m [2m:[0;39m DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@36c2b646
[2m2025-07-21 10:22:03.933[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-21 10:22:03.934[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-21 10:22:04.035[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-21 10:22:04.037[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-21 10:22:04.453[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.web.DefaultSecurityFilterChain    [0;39m [2m:[0;39m Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@50dc49e1, org.springframework.security.web.context.SecurityContextPersistenceFilter@a457c2b, org.springframework.security.web.header.HeaderWriterFilter@7aae1170, org.springframework.security.web.authentication.logout.LogoutFilter@3af36922, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@7196a8f1, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5773d271, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@22da200e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@650a1aff, org.springframework.security.web.session.SessionManagementFilter@46d0f89c, org.springframework.security.web.access.ExceptionTranslationFilter@58601e7a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4d43a1b7]
[2m2025-07-21 10:22:04.464[0;39m [dev] [33m WARN[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-21 10:22:04.464[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-21 10:22:04.560[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService 'applicationTaskExecutor'
[2m2025-07-21 10:22:05.225[0;39m [dev] [33m WARN[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mockingLoadBalancerClientRibbonWarnLogger[0;39m [2m:[0;39m You already have RibbonLoadBalancerClient on your classpath. It will be used by default. As Spring Cloud Ribbon is in maintenance mode. We recommend switching to BlockingLoadBalancerClient instead. In order to use it, set the value of `spring.cloud.loadbalancer.ribbon.enabled` to `false` or remove spring-cloud-starter-netflix-ribbon from your project.
[2m2025-07-21 10:22:05.311[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-21 10:22:05.329[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Initializing Eureka in region us-east-1
[2m2025-07-21 10:22:05.329[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Client configured to neither register nor query for data.
[2m2025-07-21 10:22:05.334[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Discovery Client initialized at timestamp 1753064525334 with initial instances count: 0
[2m2025-07-21 10:22:05.366[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initializing ...
[2m2025-07-21 10:22:05.368[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Adding new peer nodes [**********************************************/eureka/]
[2m2025-07-21 10:22:05.741[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-21 10:22:05.742[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-21 10:22:05.742[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-21 10:22:05.742[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-21 10:22:05.837[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Replica node URL:  **********************************************/eureka/
[2m2025-07-21 10:22:05.846[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Finished initializing remote region registries. All known remote regions: []
[2m2025-07-21 10:22:05.847[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initialized
[2m2025-07-21 10:22:05.857[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 2 endpoint(s) beneath base path '/actuator'
[2m2025-07-21 10:22:05.941[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application SS-PLATFORM-EUREKA with eureka with status UP
[2m2025-07-21 10:22:05.944[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Setting the eureka configuration..
[2m2025-07-21 10:22:05.944[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka data center value eureka.datacenter is not set, defaulting to default
[2m2025-07-21 10:22:05.945[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka environment value eureka.environment is not set, defaulting to test
[2m2025-07-21 10:22:05.958[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m isAws returned false
[2m2025-07-21 10:22:05.959[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Initialized server context
[2m2025-07-21 10:22:05.959[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Got 1 instances from neighboring DS node
[2m2025-07-21 10:22:05.959[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Renew threshold is: 1
[2m2025-07-21 10:22:05.959[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Changing status to UP
[2m2025-07-21 10:22:05.969[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36me.s.EurekaServerInitializerConfiguration[0;39m [2m:[0;39m Started Eureka Server
[2m2025-07-21 10:22:05.985[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 7602 (http) with context path ''
[2m2025-07-21 10:22:05.986[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 7602
[2m2025-07-21 10:22:06.378[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m Started EurekaServerApplication in 5.163 seconds (JVM running for 6.512)
[2m2025-07-21 10:22:06.498[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[2)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-21 10:22:06.505[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[2)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 7 ms
[2m2025-07-21 10:22:32.826[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=false)
[2m2025-07-21 10:22:33.544[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[nio-7602-exec-7][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=true)
[2m2025-07-21 10:22:38.783[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[nio-7602-exec-9][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=false)
[2m2025-07-21 10:22:39.312[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=true)
[2m2025-07-21 10:22:41.029[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=false)
[2m2025-07-21 10:22:41.550[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[nio-7602-exec-6][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=true)
[2m2025-07-21 10:22:41.617[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=false)
[2m2025-07-21 10:22:42.139[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=true)
[2m2025-07-21 10:23:05.975[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 10:23:58.144[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-21 10:23:58.667[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-21 10:24:05.977[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 10:25:05.988[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 10:26:05.996[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 10:27:06.006[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 10:28:06.017[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 10:29:06.028[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 10:30:06.042[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 10:31:06.052[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 10:32:06.055[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 10:33:06.058[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 10:34:06.063[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 10:35:06.074[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 10:36:06.078[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 10:37:05.849[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 10:37:06.083[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 10:38:06.088[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 10:39:06.103[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 10:40:06.145[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 41ms
[2m2025-07-21 10:41:06.155[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 10:42:06.163[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 10:43:06.166[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 10:44:06.176[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 10:45:06.188[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 10:46:06.202[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 10:47:06.202[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 10:48:06.203[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 10:49:06.214[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 10:50:06.227[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 10:51:06.229[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 10:52:05.860[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 10:52:06.229[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 10:53:06.237[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 10:54:06.246[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 10:54:13.909[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[nio-7602-exec-7][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status DOWN (replication=false)
[2m2025-07-21 10:54:13.914[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[nio-7602-exec-6][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status DOWN (replication=false)
[2m2025-07-21 10:54:13.917[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status DOWN (replication=false)
[2m2025-07-21 10:54:13.932[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application SS-PLATFORM-EUREKA with eureka with status DOWN
[2m2025-07-21 10:54:13.937[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shutting down ...
[2m2025-07-21 10:54:13.941[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.batchSize.Will start computing stats again.
[2m2025-07-21 10:54:13.945[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.executionTime.Will start computing stats again.
[2m2025-07-21 10:54:13.947[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.executionTime.Will start computing stats again.
[2m2025-07-21 10:54:13.948[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.batchSize.Will start computing stats again.
[2m2025-07-21 10:54:13.961[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shut down
[2m2025-07-21 10:54:13.969[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Shutting down ExecutorService 'applicationTaskExecutor'
[2m2025-07-21 10:54:13.975[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Shutting down DiscoveryClient ...
[2m2025-07-21 10:54:13.977[0;39m [dev] [32m INFO[0;39m [35m33212[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Completed shut down of DiscoveryClient
[2m2025-07-21 10:54:21.176[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m The following profiles are active: dev
[2m2025-07-21 10:54:21.627[0;39m [dev] [33m WARN[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-21 10:54:21.724[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=ff1a2d2c-d19d-3931-a591-81b4348e9e38
[2m2025-07-21 10:54:21.987[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 7602 (http)
[2m2025-07-21 10:54:22.049[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 862 ms
[2m2025-07-21 10:54:22.094[0;39m [dev] [33m WARN[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-21 10:54:22.094[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-21 10:54:22.100[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.netflix.config.DynamicPropertyFactory [0;39m [2m:[0;39m DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@45acdd11
[2m2025-07-21 10:54:22.938[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-21 10:54:22.940[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-21 10:54:23.111[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-21 10:54:23.111[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-21 10:54:23.591[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.web.DefaultSecurityFilterChain    [0;39m [2m:[0;39m Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@70d3cdbf, org.springframework.security.web.context.SecurityContextPersistenceFilter@4ff66917, org.springframework.security.web.header.HeaderWriterFilter@51d8f2f2, org.springframework.security.web.authentication.logout.LogoutFilter@43e3a390, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@60dcf9ec, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@647fb583, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@19705650, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@72a7aa4f, org.springframework.security.web.session.SessionManagementFilter@7d8b66d9, org.springframework.security.web.access.ExceptionTranslationFilter@3b3056a6, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@d70e9a]
[2m2025-07-21 10:54:23.601[0;39m [dev] [33m WARN[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-21 10:54:23.601[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-21 10:54:23.733[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService 'applicationTaskExecutor'
[2m2025-07-21 10:54:24.378[0;39m [dev] [33m WARN[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mockingLoadBalancerClientRibbonWarnLogger[0;39m [2m:[0;39m You already have RibbonLoadBalancerClient on your classpath. It will be used by default. As Spring Cloud Ribbon is in maintenance mode. We recommend switching to BlockingLoadBalancerClient instead. In order to use it, set the value of `spring.cloud.loadbalancer.ribbon.enabled` to `false` or remove spring-cloud-starter-netflix-ribbon from your project.
[2m2025-07-21 10:54:24.464[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-21 10:54:24.496[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Initializing Eureka in region us-east-1
[2m2025-07-21 10:54:24.496[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Client configured to neither register nor query for data.
[2m2025-07-21 10:54:24.505[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Discovery Client initialized at timestamp 1753066464504 with initial instances count: 0
[2m2025-07-21 10:54:24.538[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initializing ...
[2m2025-07-21 10:54:24.540[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Adding new peer nodes [**********************************************/eureka/]
[2m2025-07-21 10:54:24.837[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-21 10:54:24.837[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-21 10:54:24.837[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-21 10:54:24.837[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-21 10:54:24.904[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Replica node URL:  **********************************************/eureka/
[2m2025-07-21 10:54:24.913[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Finished initializing remote region registries. All known remote regions: []
[2m2025-07-21 10:54:24.913[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initialized
[2m2025-07-21 10:54:24.922[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 2 endpoint(s) beneath base path '/actuator'
[2m2025-07-21 10:54:24.984[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application SS-PLATFORM-EUREKA with eureka with status UP
[2m2025-07-21 10:54:24.987[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Setting the eureka configuration..
[2m2025-07-21 10:54:24.987[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka data center value eureka.datacenter is not set, defaulting to default
[2m2025-07-21 10:54:24.988[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka environment value eureka.environment is not set, defaulting to test
[2m2025-07-21 10:54:25.000[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m isAws returned false
[2m2025-07-21 10:54:25.000[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Initialized server context
[2m2025-07-21 10:54:25.000[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Got 1 instances from neighboring DS node
[2m2025-07-21 10:54:25.000[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Renew threshold is: 1
[2m2025-07-21 10:54:25.001[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Changing status to UP
[2m2025-07-21 10:54:25.010[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36me.s.EurekaServerInitializerConfiguration[0;39m [2m:[0;39m Started Eureka Server
[2m2025-07-21 10:54:25.030[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 7602 (http) with context path ''
[2m2025-07-21 10:54:25.031[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 7602
[2m2025-07-21 10:54:25.378[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m Started EurekaServerApplication in 5.497 seconds (JVM running for 6.654)
[2m2025-07-21 10:54:26.593[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[5)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-21 10:54:26.599[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[5)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 6 ms
[2m2025-07-21 10:54:43.075[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=false)
[2m2025-07-21 10:54:43.773[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=true)
[2m2025-07-21 10:54:50.050[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[nio-7602-exec-7][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=false)
[2m2025-07-21 10:54:50.571[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=true)
[2m2025-07-21 10:54:51.074[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=false)
[2m2025-07-21 10:54:51.599[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=true)
[2m2025-07-21 10:54:53.260[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=false)
[2m2025-07-21 10:54:53.785[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=true)
[2m2025-07-21 10:55:25.017[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 10:55:45.434[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-21 10:55:45.960[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[io-7602-exec-10][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-21 10:56:25.021[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 10:57:25.034[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 10:58:25.045[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 10:59:25.047[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 11:00:25.054[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 11:01:25.069[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-21 11:02:25.073[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 11:03:25.074[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 11:04:25.084[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 11:05:25.088[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 11:06:25.096[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 11:07:25.108[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 11:08:25.109[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 11:09:24.924[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 11:09:25.111[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 11:10:25.115[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 11:11:25.118[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 11:12:25.123[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 11:13:25.130[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 11:14:25.142[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 11:15:25.150[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 11:16:25.159[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 11:17:25.161[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 11:18:25.171[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 11:19:25.180[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 11:20:25.183[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 11:21:25.190[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 11:22:25.204[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 11:23:25.206[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 11:24:24.925[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 11:24:25.221[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-21 11:25:25.233[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 11:26:25.247[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 11:27:25.256[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 11:28:25.257[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 11:29:25.267[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 11:30:25.272[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 11:31:25.276[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 11:32:25.280[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 11:33:25.295[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-21 11:34:25.299[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 11:35:25.306[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 11:36:25.313[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 11:37:25.322[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 11:38:25.332[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 11:39:24.933[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 11:39:25.335[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 11:40:25.346[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 11:41:25.348[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 11:42:25.362[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 11:43:25.364[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 11:44:25.378[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 11:45:25.379[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 11:46:25.380[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 11:47:25.392[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 11:48:25.401[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 11:49:25.411[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 11:50:25.418[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 11:51:25.433[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 11:52:25.442[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 11:53:25.448[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 11:54:24.947[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 11:54:25.463[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 11:55:25.476[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 11:56:25.556[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 80ms
[2m2025-07-21 11:56:27.123[0;39m [dev] [31mERROR[0;39m [35m20392[0;39m [2m---[0;39m [2m[get_localhost-1][0;39m [36mc.n.e.cluster.ReplicationTaskProcessor  [0;39m [2m:[0;39m It seems to be a socket read timeout exception, it will retry later. if it continues to happen and some eureka node occupied all the cpu time, you should set property 'eureka.server.peer-node-read-timeout-ms' to a bigger value

com.sun.jersey.api.client.ClientHandlerException: java.net.SocketTimeoutException: Read timed out
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.netflix.eureka.cluster.DynamicGZIPContentEncodingFilter.handle(DynamicGZIPContentEncodingFilter.java:48)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.post(WebResource.java:570)
	at com.netflix.eureka.transport.JerseyReplicationClient.submitBatchUpdates(JerseyReplicationClient.java:117)
	at com.netflix.eureka.cluster.ReplicationTaskProcessor.process(ReplicationTaskProcessor.java:80)
	at com.netflix.eureka.util.batcher.TaskExecutors$BatchWorkerRunnable.run(TaskExecutors.java:190)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at org.apache.http.impl.io.AbstractSessionInputBuffer.fillBuffer(AbstractSessionInputBuffer.java:161)
	at org.apache.http.impl.io.SocketInputBuffer.fillBuffer(SocketInputBuffer.java:82)
	at org.apache.http.impl.io.AbstractSessionInputBuffer.readLine(AbstractSessionInputBuffer.java:276)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:138)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:56)
	at org.apache.http.impl.io.AbstractMessageParser.parse(AbstractMessageParser.java:259)
	at org.apache.http.impl.AbstractHttpClientConnection.receiveResponseHeader(AbstractHttpClientConnection.java:294)
	at org.apache.http.impl.conn.DefaultClientConnection.receiveResponseHeader(DefaultClientConnection.java:257)
	at org.apache.http.impl.conn.AbstractClientConnAdapter.receiveResponseHeader(AbstractClientConnAdapter.java:230)
	at org.apache.http.protocol.HttpRequestExecutor.doReceiveResponse(HttpRequestExecutor.java:273)
	at org.apache.http.protocol.HttpRequestExecutor.execute(HttpRequestExecutor.java:125)
	at org.apache.http.impl.client.DefaultRequestDirector.tryExecute(DefaultRequestDirector.java:679)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:481)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 10 common frames omitted

[2m2025-07-21 11:57:25.563[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 11:58:25.572[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 11:59:25.582[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 12:00:25.590[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 12:01:25.592[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 12:02:25.601[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 12:03:25.612[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 12:04:25.628[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-21 12:05:25.643[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-21 12:06:25.658[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-21 12:07:25.659[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 12:08:25.673[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 12:09:24.957[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 12:09:25.686[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 12:10:25.687[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 12:11:25.700[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 12:12:25.709[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 12:13:25.722[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 12:14:25.727[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 12:15:25.742[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-21 12:16:25.753[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 12:17:25.761[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 12:18:25.766[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 12:19:25.771[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 12:20:25.784[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 12:21:25.790[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 12:22:25.799[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 12:23:25.812[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 12:24:24.971[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 12:24:25.826[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 12:25:25.840[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 12:26:25.843[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 12:27:25.844[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 12:28:25.848[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 12:29:25.863[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 12:30:25.866[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 12:31:25.881[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-21 12:32:25.890[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 12:33:25.898[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 12:34:25.904[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 12:35:25.907[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 12:36:25.911[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 12:37:25.917[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 12:38:25.928[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 12:39:24.972[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 12:39:25.935[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 12:40:25.936[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 12:41:25.945[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 12:42:25.955[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 12:43:25.961[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 12:44:25.964[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 12:45:25.972[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 12:46:25.983[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 12:47:25.995[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 12:48:26.006[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 12:49:26.010[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 12:50:26.018[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 12:51:26.029[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 12:52:26.032[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 12:53:26.034[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 12:54:24.986[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 12:54:26.048[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 12:55:26.055[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 12:56:26.065[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 12:57:26.080[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-21 12:58:26.095[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-21 12:59:26.106[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 13:00:26.106[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 13:01:26.109[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 13:02:26.121[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 13:03:26.136[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 13:04:26.140[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 13:05:26.153[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 13:06:26.162[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 13:07:26.176[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 13:08:26.192[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-21 13:09:24.999[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 13:09:26.194[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 13:10:26.201[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 13:11:26.210[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 13:12:26.223[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 13:13:26.227[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 13:14:26.241[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 13:15:26.244[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 13:16:26.257[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 13:17:26.269[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 13:18:26.279[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 13:19:26.289[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 13:20:26.297[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 13:21:26.298[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 13:22:26.298[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 13:23:26.305[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 13:24:25.016[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 13:24:26.320[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 13:25:26.334[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 13:26:26.341[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 13:27:26.353[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 13:28:26.355[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 13:29:26.360[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 13:30:26.372[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 13:31:26.382[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 13:32:26.396[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 13:33:26.399[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 13:34:26.401[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 13:35:26.402[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 13:36:26.411[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 13:37:26.422[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 13:38:26.423[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 13:39:25.023[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 13:39:26.433[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 13:40:26.438[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 13:41:26.441[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 13:42:26.443[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 13:43:26.456[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 13:44:26.459[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 13:45:26.464[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 13:46:26.464[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 13:47:26.477[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 13:48:26.482[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 13:49:26.496[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 13:50:26.498[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 13:51:26.503[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 13:52:26.509[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 13:53:26.515[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 13:54:25.029[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 13:54:26.522[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 13:55:26.529[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 13:56:26.541[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 13:57:26.548[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 13:58:26.561[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 13:59:26.566[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 14:00:26.576[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 14:01:26.586[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 14:02:26.596[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 14:03:26.605[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 14:04:26.615[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 14:05:26.618[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 14:06:26.628[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 14:07:26.629[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 14:08:26.631[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 14:09:25.045[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 14:09:26.644[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 14:10:26.651[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 14:11:26.662[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 14:12:26.670[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 14:13:26.680[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 14:14:26.690[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 14:15:26.701[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 14:16:26.708[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 14:17:26.713[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 14:18:26.718[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 14:19:26.728[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 14:20:26.736[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 14:21:26.744[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 14:22:26.756[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 14:23:26.766[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 14:24:25.049[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 14:24:26.773[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 14:25:26.785[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 14:26:26.785[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 14:27:26.789[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 14:28:26.795[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 14:29:26.796[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 14:29:43.436[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status DOWN (replication=false)
[2m2025-07-21 14:29:43.457[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application SS-PLATFORM-EUREKA with eureka with status DOWN
[2m2025-07-21 14:29:43.467[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status DOWN (replication=false)
[2m2025-07-21 14:29:43.521[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[nio-7602-exec-9][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status DOWN (replication=false)
[2m2025-07-21 14:29:43.523[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[io-7602-exec-10][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status DOWN (replication=false)
[2m2025-07-21 14:29:43.565[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shutting down ...
[2m2025-07-21 14:29:43.571[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.batchSize.Will start computing stats again.
[2m2025-07-21 14:29:43.579[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.executionTime.Will start computing stats again.
[2m2025-07-21 14:29:43.579[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.executionTime.Will start computing stats again.
[2m2025-07-21 14:29:43.580[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.batchSize.Will start computing stats again.
[2m2025-07-21 14:29:43.707[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shut down
[2m2025-07-21 14:29:43.746[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Shutting down ExecutorService 'applicationTaskExecutor'
[2m2025-07-21 14:29:43.820[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Shutting down DiscoveryClient ...
[2m2025-07-21 14:29:43.826[0;39m [dev] [32m INFO[0;39m [35m20392[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Completed shut down of DiscoveryClient
[2m2025-07-21 14:57:48.790[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m The following profiles are active: dev
[2m2025-07-21 14:57:49.519[0;39m [dev] [33m WARN[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-21 14:57:49.607[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=ff1a2d2c-d19d-3931-a591-81b4348e9e38
[2m2025-07-21 14:57:49.849[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 7602 (http)
[2m2025-07-21 14:57:49.902[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 1089 ms
[2m2025-07-21 14:57:49.942[0;39m [dev] [33m WARN[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-21 14:57:49.942[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-21 14:57:49.950[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.netflix.config.DynamicPropertyFactory [0;39m [2m:[0;39m DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@1981d861
[2m2025-07-21 14:57:50.621[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-21 14:57:50.622[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-21 14:57:50.732[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-21 14:57:50.733[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-21 14:57:51.139[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.web.DefaultSecurityFilterChain    [0;39m [2m:[0;39m Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@ad0bb4e, org.springframework.security.web.context.SecurityContextPersistenceFilter@43e8f1c, org.springframework.security.web.header.HeaderWriterFilter@635ff2a5, org.springframework.security.web.authentication.logout.LogoutFilter@4e50ae56, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@fe8aaeb, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@675ec28b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7871d261, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7196a8f1, org.springframework.security.web.session.SessionManagementFilter@6eede35e, org.springframework.security.web.access.ExceptionTranslationFilter@1e6bd263, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@647fb583]
[2m2025-07-21 14:57:51.146[0;39m [dev] [33m WARN[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-21 14:57:51.146[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-21 14:57:51.230[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService 'applicationTaskExecutor'
[2m2025-07-21 14:57:51.935[0;39m [dev] [33m WARN[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mockingLoadBalancerClientRibbonWarnLogger[0;39m [2m:[0;39m You already have RibbonLoadBalancerClient on your classpath. It will be used by default. As Spring Cloud Ribbon is in maintenance mode. We recommend switching to BlockingLoadBalancerClient instead. In order to use it, set the value of `spring.cloud.loadbalancer.ribbon.enabled` to `false` or remove spring-cloud-starter-netflix-ribbon from your project.
[2m2025-07-21 14:57:52.045[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-21 14:57:52.071[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Initializing Eureka in region us-east-1
[2m2025-07-21 14:57:52.071[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Client configured to neither register nor query for data.
[2m2025-07-21 14:57:52.078[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Discovery Client initialized at timestamp 1753081072077 with initial instances count: 0
[2m2025-07-21 14:57:52.112[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initializing ...
[2m2025-07-21 14:57:52.115[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Adding new peer nodes [**********************************************/eureka/]
[2m2025-07-21 14:57:52.474[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-21 14:57:52.474[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-21 14:57:52.474[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-21 14:57:52.474[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-21 14:57:52.539[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Replica node URL:  **********************************************/eureka/
[2m2025-07-21 14:57:52.546[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Finished initializing remote region registries. All known remote regions: []
[2m2025-07-21 14:57:52.547[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initialized
[2m2025-07-21 14:57:52.556[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 2 endpoint(s) beneath base path '/actuator'
[2m2025-07-21 14:57:52.616[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application SS-PLATFORM-EUREKA with eureka with status UP
[2m2025-07-21 14:57:52.617[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Setting the eureka configuration..
[2m2025-07-21 14:57:52.617[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka data center value eureka.datacenter is not set, defaulting to default
[2m2025-07-21 14:57:52.617[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka environment value eureka.environment is not set, defaulting to test
[2m2025-07-21 14:57:52.625[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m isAws returned false
[2m2025-07-21 14:57:52.625[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Initialized server context
[2m2025-07-21 14:57:52.625[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Got 1 instances from neighboring DS node
[2m2025-07-21 14:57:52.625[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Renew threshold is: 1
[2m2025-07-21 14:57:52.625[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Changing status to UP
[2m2025-07-21 14:57:52.632[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36me.s.EurekaServerInitializerConfiguration[0;39m [2m:[0;39m Started Eureka Server
[2m2025-07-21 14:57:52.648[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 7602 (http) with context path ''
[2m2025-07-21 14:57:52.649[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 7602
[2m2025-07-21 14:57:52.975[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m Started EurekaServerApplication in 5.611 seconds (JVM running for 6.861)
[2m2025-07-21 14:57:53.068[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[1)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-21 14:57:53.073[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[1)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 5 ms
[2m2025-07-21 14:58:18.969[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=false)
[2m2025-07-21 14:58:19.651[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=true)
[2m2025-07-21 14:58:29.751[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=false)
[2m2025-07-21 14:58:30.282[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[nio-7602-exec-6][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=true)
[2m2025-07-21 14:58:31.282[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=false)
[2m2025-07-21 14:58:31.706[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[nio-7602-exec-9][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=false)
[2m2025-07-21 14:58:31.804[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=true)
[2m2025-07-21 14:58:31.804[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=true)
[2m2025-07-21 14:58:52.634[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 14:59:11.186[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-21 14:59:11.715[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-21 14:59:52.635[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 15:00:52.645[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 15:01:52.657[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 15:02:52.659[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 15:03:52.671[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 15:04:52.679[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 15:05:52.686[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 15:06:52.686[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 15:07:52.695[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 15:08:52.700[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 15:09:52.700[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 15:10:52.703[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 15:11:52.704[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 15:12:52.549[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 15:12:52.715[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 15:13:52.727[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 15:14:52.732[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 15:15:52.733[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 15:16:52.734[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 15:17:52.749[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-21 15:18:52.762[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 15:19:52.766[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 15:20:25.873[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status DOWN (replication=false)
[2m2025-07-21 15:20:25.884[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[nio-7602-exec-6][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status DOWN (replication=false)
[2m2025-07-21 15:20:25.948[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status DOWN (replication=false)
[2m2025-07-21 15:20:25.974[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application SS-PLATFORM-EUREKA with eureka with status DOWN
[2m2025-07-21 15:20:25.974[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[nio-7602-exec-7][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status DOWN (replication=false)
[2m2025-07-21 15:20:26.004[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shutting down ...
[2m2025-07-21 15:20:26.008[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.batchSize.Will start computing stats again.
[2m2025-07-21 15:20:26.010[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.executionTime.Will start computing stats again.
[2m2025-07-21 15:20:26.010[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.executionTime.Will start computing stats again.
[2m2025-07-21 15:20:26.011[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.batchSize.Will start computing stats again.
[2m2025-07-21 15:20:26.027[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shut down
[2m2025-07-21 15:20:26.038[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Shutting down ExecutorService 'applicationTaskExecutor'
[2m2025-07-21 15:20:26.045[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Shutting down DiscoveryClient ...
[2m2025-07-21 15:20:26.046[0;39m [dev] [32m INFO[0;39m [35m31300[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Completed shut down of DiscoveryClient
[2m2025-07-21 15:21:07.099[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m The following profiles are active: dev
[2m2025-07-21 15:21:07.706[0;39m [dev] [33m WARN[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-21 15:21:07.845[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=ff1a2d2c-d19d-3931-a591-81b4348e9e38
[2m2025-07-21 15:21:08.174[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 7602 (http)
[2m2025-07-21 15:21:08.228[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 1113 ms
[2m2025-07-21 15:21:08.270[0;39m [dev] [33m WARN[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-21 15:21:08.270[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-21 15:21:08.280[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.netflix.config.DynamicPropertyFactory [0;39m [2m:[0;39m DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@56dfab87
[2m2025-07-21 15:21:08.948[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-21 15:21:08.948[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-21 15:21:09.034[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-21 15:21:09.034[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-21 15:21:09.514[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.web.DefaultSecurityFilterChain    [0;39m [2m:[0;39m Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5e98032e, org.springframework.security.web.context.SecurityContextPersistenceFilter@19b9f903, org.springframework.security.web.header.HeaderWriterFilter@53bb71e5, org.springframework.security.web.authentication.logout.LogoutFilter@1182413a, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@49038f97, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@28cb86b2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4f235e8e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4fe8f2ae, org.springframework.security.web.session.SessionManagementFilter@51ed2f68, org.springframework.security.web.access.ExceptionTranslationFilter@6704df84, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@70c491b8]
[2m2025-07-21 15:21:09.523[0;39m [dev] [33m WARN[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-21 15:21:09.524[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-21 15:21:09.620[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService 'applicationTaskExecutor'
[2m2025-07-21 15:21:10.257[0;39m [dev] [33m WARN[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mockingLoadBalancerClientRibbonWarnLogger[0;39m [2m:[0;39m You already have RibbonLoadBalancerClient on your classpath. It will be used by default. As Spring Cloud Ribbon is in maintenance mode. We recommend switching to BlockingLoadBalancerClient instead. In order to use it, set the value of `spring.cloud.loadbalancer.ribbon.enabled` to `false` or remove spring-cloud-starter-netflix-ribbon from your project.
[2m2025-07-21 15:21:10.366[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-21 15:21:10.389[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Initializing Eureka in region us-east-1
[2m2025-07-21 15:21:10.389[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Client configured to neither register nor query for data.
[2m2025-07-21 15:21:10.396[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Discovery Client initialized at timestamp 1753082470395 with initial instances count: 0
[2m2025-07-21 15:21:10.431[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initializing ...
[2m2025-07-21 15:21:10.435[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Adding new peer nodes [**********************************************/eureka/]
[2m2025-07-21 15:21:10.857[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-21 15:21:10.857[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-21 15:21:10.857[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-21 15:21:10.857[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-21 15:21:10.964[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Replica node URL:  **********************************************/eureka/
[2m2025-07-21 15:21:10.975[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Finished initializing remote region registries. All known remote regions: []
[2m2025-07-21 15:21:10.976[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initialized
[2m2025-07-21 15:21:10.986[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 2 endpoint(s) beneath base path '/actuator'
[2m2025-07-21 15:21:11.077[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application SS-PLATFORM-EUREKA with eureka with status UP
[2m2025-07-21 15:21:11.110[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Setting the eureka configuration..
[2m2025-07-21 15:21:11.110[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka data center value eureka.datacenter is not set, defaulting to default
[2m2025-07-21 15:21:11.111[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka environment value eureka.environment is not set, defaulting to test
[2m2025-07-21 15:21:11.155[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m isAws returned false
[2m2025-07-21 15:21:11.157[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Initialized server context
[2m2025-07-21 15:21:11.157[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Got 1 instances from neighboring DS node
[2m2025-07-21 15:21:11.157[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Renew threshold is: 1
[2m2025-07-21 15:21:11.158[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Changing status to UP
[2m2025-07-21 15:21:11.160[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 7602 (http) with context path ''
[2m2025-07-21 15:21:11.164[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 7602
[2m2025-07-21 15:21:11.192[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[      Thread-21][0;39m [36me.s.EurekaServerInitializerConfiguration[0;39m [2m:[0;39m Started Eureka Server
[2m2025-07-21 15:21:11.534[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m Started EurekaServerApplication in 5.684 seconds (JVM running for 6.413)
[2m2025-07-21 15:21:11.858[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[1)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-21 15:21:11.864[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[1)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 5 ms
[2m2025-07-21 15:21:36.703[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=false)
[2m2025-07-21 15:21:37.337[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=true)
[2m2025-07-21 15:21:42.054[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[nio-7602-exec-9][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=false)
[2m2025-07-21 15:21:42.581[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[io-7602-exec-10][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=true)
[2m2025-07-21 15:21:47.739[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[nio-7602-exec-6][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=false)
[2m2025-07-21 15:21:48.259[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=true)
[2m2025-07-21 15:21:50.760[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=false)
[2m2025-07-21 15:21:51.280[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=true)
[2m2025-07-21 15:22:11.163[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 15:22:30.431[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[io-7602-exec-10][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-21 15:22:30.954[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[nio-7602-exec-7][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-21 15:23:11.165[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 15:24:11.165[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 15:25:11.165[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 15:26:11.172[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 15:27:11.180[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 15:28:11.193[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 15:29:11.195[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 15:30:11.210[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 15:31:11.217[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 15:32:11.221[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 15:33:11.235[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 15:34:11.250[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 15:35:11.259[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 15:36:10.977[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 15:36:11.272[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 15:37:11.286[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 15:37:11.287[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Evicting 1 items (expired=1, evictionLimit=1)
[2m2025-07-21 15:37:11.287[0;39m [dev] [33m WARN[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m DS: Registry: expired lease for SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608
[2m2025-07-21 15:37:11.288[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Cancelled instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 (replication=false)
[2m2025-07-21 15:38:11.298[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 15:39:11.307[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 15:40:11.314[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 15:41:11.323[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 15:42:11.326[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 15:43:12.970[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 15:44:12.985[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 15:45:12.995[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 15:45:44.032[0;39m [dev] [33m WARN[0;39m [35m4488[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m DS: Registry: lease doesn't exist, registering resource: SS-IFRS-ACTUARIAL - DESKTOP-658MVB3:ss-ifrs-actuarial:7608
[2m2025-07-21 15:45:44.032[0;39m [dev] [33m WARN[0;39m [35m4488[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.eureka.resources.InstanceResource   [0;39m [2m:[0;39m Not Found (Renew): SS-IFRS-ACTUARIAL - DESKTOP-658MVB3:ss-ifrs-actuarial:7608
[2m2025-07-21 15:45:44.140[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-21 15:45:44.658[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-21 15:46:13.007[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 15:47:13.007[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 15:48:13.015[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 15:49:13.021[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 15:50:13.024[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 15:51:12.611[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 15:51:13.032[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 15:52:13.040[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 15:53:13.043[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 15:54:13.044[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 15:55:13.052[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 15:56:13.058[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 15:57:13.073[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 15:58:13.074[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 15:59:13.076[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 16:00:13.090[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 16:01:13.098[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 16:02:13.108[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 16:03:13.111[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 16:04:13.116[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 16:05:13.129[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 16:06:12.619[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 16:06:13.141[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 16:07:13.148[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 16:08:13.148[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 16:09:13.152[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 16:10:13.167[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 16:11:13.177[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 16:12:13.183[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 16:13:13.196[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 16:14:13.206[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 16:15:13.220[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 16:16:13.226[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 16:17:13.235[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 16:18:13.236[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 16:19:13.247[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 16:20:13.252[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 16:21:12.627[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 16:21:13.355[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 103ms
[2m2025-07-21 16:22:13.362[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 16:23:13.372[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 16:24:13.375[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 16:25:13.385[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 16:26:13.394[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 16:27:13.406[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 16:28:13.418[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 16:29:13.431[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 16:30:13.439[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 16:31:13.440[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 16:32:13.449[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 16:33:13.460[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 16:34:13.471[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 16:35:13.472[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 16:36:12.637[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 16:36:13.474[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 16:37:13.478[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 16:38:13.483[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 16:39:13.495[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 16:40:13.499[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 16:41:13.508[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 16:42:13.518[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 16:43:13.529[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 16:44:13.543[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 16:45:13.550[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 16:46:13.565[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 16:47:13.571[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 16:48:13.577[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 16:49:13.590[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 16:50:13.604[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 16:51:12.640[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 16:51:13.615[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 16:52:13.622[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 16:53:13.631[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 16:54:13.633[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 16:55:13.636[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 16:56:13.640[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 16:57:13.653[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 16:58:13.667[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 16:59:13.672[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 17:00:13.682[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 17:01:13.693[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 17:02:13.704[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 17:03:13.704[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 17:04:13.707[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 17:05:13.713[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 17:06:12.655[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 17:06:13.725[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 17:07:13.728[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 17:08:13.735[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 17:09:13.749[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 17:10:13.759[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 17:11:13.761[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 17:12:13.764[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 17:13:13.776[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 17:14:13.783[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 17:15:13.783[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 17:16:13.787[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 17:17:13.792[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 17:18:13.792[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 17:19:13.807[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-21 17:20:13.814[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 17:21:12.664[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 17:21:13.826[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 17:22:13.832[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 17:23:13.840[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 17:24:13.851[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 17:25:13.865[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 17:26:13.880[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-21 17:27:13.884[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 17:28:13.886[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 17:29:13.891[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 17:30:13.893[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 17:31:13.909[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-21 17:32:13.913[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 17:33:13.928[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 17:34:13.938[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 17:35:13.943[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 17:36:12.676[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 17:36:13.951[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 17:37:13.953[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 17:38:13.958[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 17:39:13.968[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 17:40:13.981[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 17:41:13.984[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 17:42:13.986[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 17:43:13.993[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 17:44:14.004[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 17:45:14.005[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 17:46:14.012[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 17:47:14.012[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 17:48:14.021[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 17:49:14.033[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 17:50:14.046[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 17:51:12.690[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 17:51:14.052[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 17:52:14.066[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 17:53:14.075[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 17:54:14.084[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 17:55:14.085[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 17:56:14.093[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 17:57:14.096[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 17:58:14.100[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 17:59:14.103[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 18:00:14.104[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 18:01:14.116[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 18:02:14.117[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 18:03:14.117[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 18:04:14.122[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 18:05:14.137[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 18:06:12.690[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 18:06:14.147[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 18:07:14.153[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 18:08:14.162[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 18:09:14.175[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 18:10:14.179[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 18:11:14.183[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 18:12:14.184[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 18:13:14.186[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-21 18:14:14.200[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-21 18:15:14.205[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 18:16:14.214[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 18:17:14.215[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 18:18:14.219[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 18:19:14.219[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 18:20:14.229[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-21 18:21:12.699[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 18:21:14.242[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 18:22:14.252[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 18:23:14.260[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 18:24:14.260[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 18:25:14.268[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-21 18:26:14.270[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 18:27:14.277[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-21 18:28:14.284[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 18:29:14.289[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 18:30:14.301[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 18:31:14.313[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 18:32:14.320[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 18:33:14.325[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 18:34:14.332[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-21 18:35:14.332[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-21 18:36:12.711[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-21 18:36:14.337[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-21 18:37:14.339[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-21 18:38:14.342[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-21 18:39:14.356[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-21 18:40:14.367[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-21 18:41:14.372[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-21 18:42:14.382[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 18:43:14.394[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-21 18:44:14.405[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-21 18:44:39.021[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application SS-PLATFORM-EUREKA with eureka with status DOWN
[2m2025-07-21 18:44:39.049[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status DOWN (replication=false)
[2m2025-07-21 18:44:39.052[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[io-7602-exec-10][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status DOWN (replication=false)
[2m2025-07-21 18:44:39.052[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[nio-7602-exec-6][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status DOWN (replication=false)
[2m2025-07-21 18:44:39.070[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shutting down ...
[2m2025-07-21 18:44:39.079[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.batchSize.Will start computing stats again.
[2m2025-07-21 18:44:39.079[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status DOWN (replication=false)
[2m2025-07-21 18:44:39.084[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.executionTime.Will start computing stats again.
[2m2025-07-21 18:44:39.084[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.executionTime.Will start computing stats again.
[2m2025-07-21 18:44:39.085[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.batchSize.Will start computing stats again.
[2m2025-07-21 18:44:39.093[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status DOWN (replication=false)
[2m2025-07-21 18:44:39.105[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shut down
[2m2025-07-21 18:44:39.112[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Shutting down ExecutorService 'applicationTaskExecutor'
[2m2025-07-21 18:44:39.126[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Shutting down DiscoveryClient ...
[2m2025-07-21 18:44:39.128[0;39m [dev] [32m INFO[0;39m [35m4488[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Completed shut down of DiscoveryClient
