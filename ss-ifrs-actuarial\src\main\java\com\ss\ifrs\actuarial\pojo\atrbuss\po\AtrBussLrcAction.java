/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-08-11 10:56:32
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-08-11 10:56:32<br/>
 * Description: LRC 预期现金流操作表<br/>
 * Table Name: ATR_BUSS_LRC_ACTION<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "LRC 预期现金流操作表")
public class AtrBussLrcAction implements Serializable {
    /**
     * Database column: ATR_BUSS_LRC_ACTION.ID
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.ACTION_NO
     * Database remarks: 执行编号|一次操作的全局唯一编号， 一次操作的维度是模块（LIC或LRC）、业务类型、业务单位、业务年月、币别
     */
    @ApiModelProperty(value = "执行编号|一次操作的全局唯一编号， 一次操作的维度是模块（LIC或LRC）、业务类型、业务单位、业务年月、币别", required = true)
    private String actionNo;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.TASK_CODE
     * Database remarks: 版本号
     */
    @ApiModelProperty(value = "版本号", required = true)
    private String taskCode;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.ENTITY_ID
     * Database remarks: 业务单位ID
     */
    @ApiModelProperty(value = "业务单位ID", required = true)
    private Long entityId;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.YEAR_MONTH
     * Database remarks: 业务年月|评估期
     */
    @ApiModelProperty(value = "业务年月|评估期", required = true)
    private String yearMonth;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.CURRENCY_CODE
     * Database remarks: 币别
     */
    @ApiModelProperty(value = "币别", required = true)
    private String currencyCode;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.BUSINESS_SOURCE_CODE
     * Database remarks: 业务类型|DD-直保&临分分入；FO-临分分出；TI-合约分入；TO-合约分出
     */
    @ApiModelProperty(value = "业务类型|DD-直保&临分分入；FO-临分分出；TI-合约分入；TO-合约分出", required = true)
    private String businessSourceCode;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.STATUS
     * Database remarks: 执行状态|R-执行中；E-执行异常；S-执行成功
     */
    @ApiModelProperty(value = "执行状态|R-执行中；E-执行异常；S-执行成功", required = true)
    private String status;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.CONFIRM_IS
     * Database remarks: 是否确认|1-是、0-否
     */
    @ApiModelProperty(value = "是否确认|1-是、0-否", required = true)
    private String confirmIs;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.CONFIRM_USER
     * Database remarks: 确认人
     */
    @ApiModelProperty(value = "确认人", required = false)
    private Long confirmUser;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.CONFIRM_TIME
     * Database remarks: 确认时间
     */
    @ApiModelProperty(value = "确认时间", required = false)
    private Date confirmTime;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.CREATOR_ID
     * Database remarks: 创建人
     */
    @ApiModelProperty(value = "创建人", required = false)
    private Long creatorId;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.CREATE_TIME
     * Database remarks: 创建时间
     */
    @ApiModelProperty(value = "创建时间", required = false)
    private Date createTime;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.UPDATOR_ID
     * Database remarks: 最后修改人
     */
    @ApiModelProperty(value = "最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.UPDATE_TIME
     * Database remarks: 最后修改时间
     */
    @ApiModelProperty(value = "最后修改时间", required = false)
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getConfirmIs() {
        return confirmIs;
    }

    public void setConfirmIs(String confirmIs) {
        this.confirmIs = confirmIs;
    }

    public Long getConfirmUser() {
        return confirmUser;
    }

    public void setConfirmUser(Long confirmUser) {
        this.confirmUser = confirmUser;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}