package com.ss.ifrs.actuarial.dao.conf;

import com.ss.ifrs.actuarial.pojo.atrcode.po.AtrConfCodeAdapter;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;


/**
 * This code was generated by SS MyBatis Generator. Create Date: 2018-02-07
 * 16:18:36<br>
 * Description: SysCodeConfig|双击域和下拉列表查询配置表 Dao类<br>
 * Related Table: syscodeconfig<br>
 * <br>
 * Reminder:
 * 至mybatis-generator-1.0.13版本起，Dao层父类修改为{@link IDao}<br>
 * 涉及到变动如下：<br>
 * 1、使用save方法替换原insert与insertSelective方法<br>
 * 2、使用deleteById方法替换原deleteByPrimaryKey方法<br>
 * 3、使用deleteByIds方法替换原deleteBatchByPrimaryKeys方法<br>
 * 4、使用updateById与update方法替换原updateByPrimaryKey与updateSelectiveByPrimaryKey方法<br>
 * 5、使用findById方法替换原selectByPrimaryKey方法<br>
 * 6、删除原selectBatchByPrimaryKeys、selectPage与findDataTables方法<br>
 * <br>
 */
@Mapper
public interface AtrConfCodeAdapterDao extends IDao<AtrConfCodeAdapter, String> {

    AtrConfCodeAdapter findByCode(String code);
}