<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2021-07-08 11:25:49 -->
<!-- Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.conf.AtrConfBussPeriodDao">
  <!-- 本文件由GIS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfBussPeriod">
    <id column="buss_period_id" property="bussPeriodId" jdbcType="NUMERIC" />
    <result column="entity_id" property="entityId" jdbcType="NUMERIC" />
    <result column="year_month" property="yearMonth" jdbcType="VARCHAR" />
    <result column="period_state" property="periodState" jdbcType="VARCHAR" />
    <result column="valid_is" property="validIs" jdbcType="VARCHAR" />
    <result column="creator_id" property="creatorId" jdbcType="NUMERIC" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="updator_id" property="updatorId" jdbcType="NUMERIC" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    buss_period_id, entity_id, year_month, period_state, valid_is, creator_id, create_time,
    updator_id, update_time
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="bussPeriodId != null ">
          and buss_period_id = #{bussPeriodId,jdbcType=NUMERIC}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=NUMERIC}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
          and year_month = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="periodState != null and periodState != ''">
          and period_state = #{periodState,jdbcType=VARCHAR}
      </if>
      <if test="validIs != null and validIs != ''">
          and valid_is = #{validIs,jdbcType=VARCHAR}
      </if>
      <if test="creatorId != null ">
          and creator_id = #{creatorId,jdbcType=NUMERIC}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and updator_id = #{updatorId,jdbcType=NUMERIC}
      </if>
      <if test="updateTime != null ">
          and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.bussPeriodId != null ">
          and buss_period_id = #{condition.bussPeriodId,jdbcType=NUMERIC}
      </if>
      <if test="condition.entityId != null ">
          and entity_id = #{condition.entityId,jdbcType=NUMERIC}
      </if>
      <if test="condition.yearMonth != null and condition.yearMonth != ''">
          and year_month = #{condition.yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="condition.periodState != null and condition.periodState != ''">
          and period_state = #{condition.periodState,jdbcType=VARCHAR}
      </if>
      <if test="condition.validIs != null and condition.validIs != ''">
          and valid_is = #{condition.validIs,jdbcType=VARCHAR}
      </if>
      <if test="condition.creatorId != null ">
          and creator_id = #{condition.creatorId,jdbcType=NUMERIC}
      </if>
      <if test="condition.createTime != null ">
          and create_time = #{condition.createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.updatorId != null ">
          and updator_id = #{condition.updatorId,jdbcType=NUMERIC}
      </if>
      <if test="condition.updateTime != null ">
          and update_time = #{condition.updateTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="bussPeriodId != null ">
          and buss_period_id = #{bussPeriodId,jdbcType=NUMERIC}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=NUMERIC}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
          and year_month = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="periodState != null and periodState != ''">
          and period_state = #{periodState,jdbcType=VARCHAR}
      </if>
      <if test="validIs != null and validIs != ''">
          and valid_is = #{validIs,jdbcType=VARCHAR}
      </if>
      <if test="creatorId != null ">
          and creator_id = #{creatorId,jdbcType=NUMERIC}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and updator_id = #{updatorId,jdbcType=NUMERIC}
      </if>
      <if test="updateTime != null ">
          and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_bussperiod
    where buss_period_id = #{bussPeriodId,jdbcType=NUMERIC}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_bussperiod
    where buss_period_id in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_bussperiod
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfBussPeriod">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_bussperiod
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_bussperiod
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="buss_period_id" keyProperty="bussPeriodId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfBussPeriod">
    <selectKey resultType="long" keyProperty="bussPeriodId" order="BEFORE">
      select nextval('atr_seq_conf_bussperiod')
    </selectKey>
    insert into atr_conf_bussperiod
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bussPeriodId != null">
        buss_period_id,
      </if>
      <if test="entityId != null">
        entity_id,
      </if>
      <if test="yearMonth != null">
        year_month,
      </if>
      <if test="periodState != null">
        period_state,
      </if>
      <if test="validIs != null">
        valid_is,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updatorId != null">
        updator_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bussPeriodId != null">
        #{bussPeriodId,jdbcType=NUMERIC},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=NUMERIC},
      </if>
      <if test="yearMonth != null">
        #{yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="periodState != null">
        #{periodState,jdbcType=VARCHAR},
      </if>
      <if test="validIs != null">
        #{validIs,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=NUMERIC},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        #{updatorId,jdbcType=NUMERIC},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert into atr_conf_bussperiod
     (buss_period_id, entity_id, year_month,
      period_state, valid_is, creator_id, 
      create_time, updator_id, update_time
      )
     values 
    <foreach collection="list" item="item" index="index" separator=",">
       (#{item.bussPeriodId,jdbcType=NUMERIC}, #{item.entityId,jdbcType=NUMERIC}, #{item.yearMonth,jdbcType=VARCHAR},
        #{item.periodState,jdbcType=VARCHAR}, #{item.validIs,jdbcType=VARCHAR}, #{item.creatorId,jdbcType=NUMERIC}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updatorId,jdbcType=NUMERIC}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfBussPeriod">
    update atr_conf_bussperiod
    <set>
      <if test="entityId != null">
        entity_id = #{entityId,jdbcType=NUMERIC},
      </if>
      <if test="yearMonth != null">
        year_month = #{yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="periodState != null">
        period_state = #{periodState,jdbcType=VARCHAR},
      </if>
      <if test="validIs != null">
        valid_is = #{validIs,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=NUMERIC},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        updator_id = #{updatorId,jdbcType=NUMERIC},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where buss_period_id = #{bussPeriodId,jdbcType=NUMERIC}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfBussPeriod">
    update atr_conf_bussperiod
    <set>
      <if test="record.entityId != null">
        entity_id = #{record.entityId,jdbcType=NUMERIC},
      </if>
      <if test="record.yearMonth != null">
        year_month = #{record.yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="record.periodState != null">
        period_state = #{record.periodState,jdbcType=VARCHAR},
      </if>
      <if test="record.validIs != null">
        valid_is = #{record.validIs,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=NUMERIC},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatorId != null">
        updator_id = #{record.updatorId,jdbcType=NUMERIC},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from atr_conf_bussperiod
    where buss_period_id = #{bussPeriodId,jdbcType=NUMERIC}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from atr_conf_bussperiod
    where buss_period_id in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from atr_conf_bussperiod
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfBussPeriod">
    select count(1) from atr_conf_bussperiod
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>