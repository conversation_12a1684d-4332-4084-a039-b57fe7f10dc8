/**
 * 
 * This file was generated by  MyBatis Generator(v1.2.12).
 * Date: 2021-11-11 16:15:28
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027,   All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.other.vo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * This code was generated by  MyBatis Generator(v1.2.12).
 * Create Date: 2021-11-11 16:15:28<br/>
 * Description: 计量评估结果表<br/>
 * Table Name: atr_duct_evaluate_result<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */

public class AtrDuctEvaluatePeriodVo implements Serializable {

    private String quotaCode;
    private String quotaEName;
    private String quotaCName;
    private String quotaLName;
    private List<Object> valueList = new ArrayList();



    public String getQuotaCode() {
        return quotaCode;
    }

    public void setQuotaCode(String quotaCode) {
        this.quotaCode = quotaCode;
    }

    public String getQuotaEName() {
        return quotaEName;
    }

    public void setQuotaEName(String quotaEName) {
        this.quotaEName = quotaEName;
    }

    public String getQuotaCName() {
        return quotaCName;
    }

    public void setQuotaCName(String quotaCName) {
        this.quotaCName = quotaCName;
    }

    public String getQuotaLName() {
        return quotaLName;
    }

    public void setQuotaLName(String quotaLName) {
        this.quotaLName = quotaLName;
    }

    public List<Object> getValueList() {
        return valueList;
    }

    public void setValueList(List<Object> valueList) {
        this.valueList = valueList;
    }
}