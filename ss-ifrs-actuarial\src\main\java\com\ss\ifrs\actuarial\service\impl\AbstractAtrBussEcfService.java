package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.dao.AtrBussEcfDao;
import com.ss.ifrs.actuarial.dao.AtrBussLicDao;
import com.ss.ifrs.actuarial.dao.AtrBussLrcDao;
import com.ss.ifrs.actuarial.pojo.ecf.vo.AtrBussQuota;
import com.ss.ifrs.actuarial.pojo.ecf.vo.AtrLogEcf;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lic.AtrLogLic;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.AtrLogLrc;
import com.ss.ifrs.actuarial.util.Dates;
import com.ss.ifrs.actuarial.util.EcfUtil;
import com.ss.ifrs.actuarial.util.IndexFactory;
import com.ss.ifrs.actuarial.util.abp.AsyncBatchProcessor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;

/**
 * 预期现金流的基础 service
 * <AUTHOR>
 */
public abstract class AbstractAtrBussEcfService {

    @Resource
    protected JdbcTemplate jdbcTemplate;
    @Resource
    protected AtrBussEcfDao atrBussEcfDao;
    @Resource
    protected AtrBussLrcDao atrBussLrcDao;
    @Resource
    protected AtrBussLicDao atrBussLicDao;

    protected AtomicLong logIdGen;

    protected AsyncBatchProcessor abp;

    protected final Map<String, Object> commonParamMap = new HashMap<>();

    protected String actionNo;

    protected Long entityId;

    protected String yearMonth;

    protected String businessSourceCode;

    protected Date evDate;

    protected Date evDateBom;

    protected String baseCurrencyCode;

    protected String lastActionNo;

    protected String lastYearMonth;

    protected int parts;

    protected final IndexFactory<AtrBussQuota> quotaIndex = new IndexFactory<>();

    protected AsyncBatchProcessor createAsyncBatchProcessor() {
        return new AsyncBatchProcessor(actionNo, jdbcTemplate, EcfUtil.THREADS_DB);
    }

    protected void initEnvParams(String actionNo, Long entityId, String yearMonth, String businessSourceCode, String mode) {
        this.baseCurrencyCode = atrBussEcfDao.getBaseCurrency(entityId);
        this.businessSourceCode = businessSourceCode;
        this.entityId = entityId;
        this.yearMonth = yearMonth;
        this.evDate = Dates.lastDay(yearMonth);
        this.evDateBom = Dates.toDate(yearMonth);
        this.actionNo = actionNo;
        this.lastYearMonth = Dates.preYearMonth(yearMonth);
        this.lastActionNo = getLastActionNo();
        Integer confParts = atrBussEcfDao.getConfParts(yearMonth, businessSourceCode, mode);
        this.parts = confParts != null ? confParts : 20;
        initCommonParamMap();
    }

    private void initCommonParamMap() {
        commonParamMap.put("entityId", entityId);
        commonParamMap.put("yearMonth", yearMonth);
        commonParamMap.put("evDate", evDate);
        commonParamMap.put("evDateBom", evDateBom);
        commonParamMap.put("actionNo", actionNo);
        commonParamMap.put("businessSourceCode", businessSourceCode);
        commonParamMap.put("lastYearMonth", lastYearMonth);
        commonParamMap.put("lastActionNo", lastActionNo);
        commonParamMap.put("baseCurrencyCode", baseCurrencyCode);
        commonParamMap.put("parts", parts);
    }

    protected String getLastActionNo() {
        return atrBussLicDao.getLastActionNo(entityId, lastYearMonth, businessSourceCode);
    }

    protected void collectQuotaDef() {
        Class<?> clazz = getClass();
        Field[] fields = clazz.getDeclaredFields();
        Set<String> quotaCodes = new HashSet<>();
        try {
            for (Field field : fields) {
                if (field.getName().startsWith("Q_") && field.getType().equals(String.class)) {
                    String quotaCode = FieldUtils
                            .readDeclaredStaticField(clazz, field.getName(), true)
                            .toString();
                    quotaCodes.add(quotaCode);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if (!quotaCodes.isEmpty()) {
            atrBussEcfDao.collectQuotaDef(actionNo, quotaCodes);
        }
    }

    protected void collectQuota() {
        atrBussEcfDao.collectQuotaForICG(commonParamMap);
        atrBussEcfDao.collectQuotaForICGDev(commonParamMap);
        List<AtrBussQuota> quotas = atrBussEcfDao.findQuota(actionNo);
        initQuotaIndex(quotas);
    }


    protected void initQuotaIndex(List<AtrBussQuota> quotas) {
        quotas.sort(Comparator.comparing(AtrBussQuota::getDimValue)
                .thenComparingInt(AtrBussQuota::getDevNo));
        for (AtrBussQuota quota : quotas) {
            if ( EcfUtil.Q_LIC_RD_RATIO.equals(quota.getQuotaCode())){
                List<?> key = Arrays.asList(quota.getQuotaCode(), quota.getDimValue());
                quotaIndex.add(key, quota);
            }else{
                List<?> key = Arrays.asList(quota.getQuotaCode(), quota.getRiskClassCode(), quota.getDimValue());
                quotaIndex.add(key, quota);
                key = Arrays.asList(quota.getQuotaCode(), quota.getRiskClassCode(), quota.getDimValue(), quota.getDevNo());
                quotaIndex.add(key, quota);
            }

        }
    }

    protected double getQuota(String quotaCode, String riskClassCode, String icgNo) {
        return getQuota(quotaCode, riskClassCode, icgNo, -1);
    }

    protected double getQuota(String quotaCode,String icgNo){
        return getQuota(quotaCode,null,icgNo,-1);
    }

    protected double getQuota(String quotaCode, String riskClassCode, String icgNo, int devNo) {
        Double quota = getQuotaOrNull(quotaCode, riskClassCode,  icgNo, devNo);
        return quota == null ? 0 : quota;
    }

    protected Double getQuotaOrNull(String quotaCode, String riskClassCode, String icgNo, int devNo) {
        List<AtrBussQuota> quotas;
        if ( EcfUtil.Q_LIC_RD_RATIO.equals(quotaCode)){
            quotas = quotaIndex.list(Arrays.asList(quotaCode,icgNo));
        }else{
            if ( devNo == -1 ){
                quotas = quotaIndex.list(Arrays.asList(quotaCode, riskClassCode, icgNo));
            }else{
                quotas = quotaIndex.list(Arrays.asList(quotaCode, riskClassCode, icgNo, devNo));
            }
        }

        return CollectionUtils.isEmpty(quotas) ? null : quotas.get(0).getValue();
    }

    protected double nvl(Double value) {
        return value == null ? 0D : value;
    }

    protected BigDecimal nvl(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value;
    }

    /**
     * 如果 value 为 null 或 0， 将返回 defaultValue， 否则返回 value 自己
     */
    protected double nvl0(Double value, double defaultValue) {
        return value == null || value == 0 ? defaultValue : value;
    }

    protected Double nullif(Double value) {
        if (value == null) {
            return null;
        }
        return value == 0D ? null : value;
    }

    protected double round(double value) {
        return EcfUtil.round(value, EcfUtil.SCALE_MONEY);
    }

    protected void putDevValue(Map<Integer, Double> map, Integer devNo, Double value) {
        if (map.containsKey(devNo)) {
            map.put(devNo, map.get(devNo) + nvl(value));
        } else {
            map.put(devNo, nvl(value));
        }
    }

    protected void putDevValueB(Map<Integer, BigDecimal> map, Integer devNo, Double value) {
        if (map.containsKey(devNo)) {
            map.put(devNo, map.get(devNo).add(BigDecimal.valueOf(nvl(value))));
        } else {
            map.put(devNo, BigDecimal.valueOf(nvl(value)));
        }
    }

    protected double roundR(double value) {
        return EcfUtil.round(value, EcfUtil.SCALE_RATIO);
    }

    protected double get(List<Double> list, int index) {
        if (list == null) {
            return 0;
        }
        return index < list.size() ? nvl(list.get(index)) : 0;
    }

    protected <T> double sum(List<T> list, Function<T, Double> f) {
        if (list == null || list.isEmpty()) {
            return 0;
        }
        return list.stream().filter(t -> f.apply(t) != null).map(f).reduce(0D, Double::sum);
    }

    protected void logDebug(AtrLogEcf vo) {
        vo.setId(logIdGen.incrementAndGet());
        vo.setActionNo(actionNo);
        vo.setLogType("DEBUG");
        vo.setCreateTime(new Date());
        insertLog(vo);
    }

    protected void logError(AtrLogEcf vo, Exception e) {
        String msg = null;
        if (e != null) {
            Throwable t = ExceptionUtils.getRootCause(e);
            String trace = ExceptionUtils.getStackTrace(t);
            msg = StringUtils.substring(trace, 0, 1000);
        }
        vo.setId(logIdGen.incrementAndGet());
        vo.setLogType("ERROR");
        vo.setActionNo(actionNo);
        vo.setMsg(msg);
        vo.setCreateTime(new Date());
        insertLog(vo);
    }

    private void insertLog(AtrLogEcf vo) {
        if (vo instanceof AtrLogLrc) {
            atrBussLrcDao.insertLog((AtrLogLrc) vo);
        } else if (vo instanceof AtrLogLic) {
            atrBussLicDao.insertLog((AtrLogLic) vo);
        }
    }

}
