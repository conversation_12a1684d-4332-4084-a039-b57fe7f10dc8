/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2023-02-03 18:32:02
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-03 18:32:02<br/>
 * Description: Mortgage假设配置<br/>
 * Table Name: ATR_CONF_MORTG_QUOTAHIS<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "Mortgage假设配置")
public class AtrConfMortgQuotaHisVo implements Serializable {
    /**
     * Database column: ATR_CONF_MORTG_QUOTAHIS.MORTG_QUOTA_HIS_ID
     * Database remarks: null
     */
    private Long mortgQuotaHisId;

    /**
     * Database column: ATR_CONF_MORTG_QUOTAHIS.MORTG_QUOTA_ID
     * Database remarks: mortg Quota Id|主键
     */
    @ApiModelProperty(value = "mortg Quota Id|主键", required = true)
    private Long mortgQuotaId;

    private Long entityId;

    @ApiModelProperty(value = "year_month|评估期", required = true)
    private String yearMonth;

    /**
     * Database column: ATR_CONF_MORTG_QUOTAHIS.POLICY_NO
     * Database remarks: policy No|保单号
     */
    @ApiModelProperty(value = "policy No|保单号", required = false)
    private String policyNo;

    /**
     * Database column: ATR_CONF_MORTG_QUOTAHIS.INSURED_NAME
     * Database remarks: insured Name|被保险人
     */
    @ApiModelProperty(value = "insured Name|被保险人", required = false)
    private String insuredName;

    /**
     * Database column: ATR_CONF_MORTG_QUOTAHIS.UW_YEAR
     * Database remarks: uw Year|核保年份
     */
    @ApiModelProperty(value = "uw Year|核保年份", required = false)
    private String uwYear;

    /**
     * Database column: ATR_CONF_MORTG_QUOTAHIS.DELINQUENCY_RATE
     * Database remarks: delinquency Rate|拖欠比率
     */
    @ApiModelProperty(value = "delinquency Rate|拖欠比率", required = false)
    private BigDecimal delinquencyRate;

    /**
     * Database column: ATR_CONF_MORTG_QUOTAHIS.DEFAULT_RATE
     * Database remarks: default Rate|默认比率
     */
    @ApiModelProperty(value = "default Rate|默认比率", required = false)
    private BigDecimal defaultRate;

    /**
     * Database column: ATR_CONF_MORTG_QUOTAHIS.SERIAL_NO
     * Database remarks: serialNo|版本号
     */
    @ApiModelProperty(value = "serialNo|版本号", required = true)
    private Long serialNo;

    /**
     * Database column: ATR_CONF_MORTG_QUOTAHIS.VALID_IS
     * Database remarks: validIs|是否有效
     */
    @ApiModelProperty(value = "validIs|是否有效", required = true)
    private String validIs;

    /**
     * Database column: ATR_CONF_MORTG_QUOTAHIS.AUDIT_STATE
     * Database remarks: audit_State|审核状态
     */
    @ApiModelProperty(value = "audit_State|审核状态", required = false)
    private String auditState;

    /**
     * Database column: ATR_CONF_MORTG_QUOTAHIS.CHECKED_MSG
     * Database remarks: checked_msg|审核意见
     */
    @ApiModelProperty(value = "checked_msg|审核意见", required = false)
    private String checkedMsg;

    /**
     * Database column: ATR_CONF_MORTG_QUOTAHIS.OPER_TYPE
     * Database remarks: null
     */
    private String operType;

    /**
     * Database column: ATR_CONF_MORTG_QUOTAHIS.OPER_ID
     * Database remarks: null
     */
    private Long operId;

    /**
     * Database column: ATR_CONF_MORTG_QUOTAHIS.OPER_TIME
     * Database remarks: null
     */
    private Date operTime;

    /**
     * Database column: ATR_CONF_MORTG_QUOTAHIS.CHECKED_ID
     * Database remarks: checked_id|审核人
     */
    @ApiModelProperty(value = "checked_id|审核人", required = false)
    private Long checkedId;

    /**
     * Database column: ATR_CONF_MORTG_QUOTAHIS.CHECKED_TIME
     * Database remarks: checked_time|审核时间
     */
    @ApiModelProperty(value = "checked_time|审核时间", required = false)
    private Date checkedTime;

    /**
     * Database column: ATR_CONF_MORTG_QUOTAHIS.CREATOR_ID
     * Database remarks: creator_id|创建人
     */
    @ApiModelProperty(value = "creator_id|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: ATR_CONF_MORTG_QUOTAHIS.CREATE_TIME
     * Database remarks: create_time|创建时间
     */
    @ApiModelProperty(value = "create_time|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: ATR_CONF_MORTG_QUOTAHIS.UPDATOR_ID
     * Database remarks: updator_id|最后修改人
     */
    @ApiModelProperty(value = "updator_id|最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: ATR_CONF_MORTG_QUOTAHIS.UPDATE_TIME
     * Database remarks: update_time|最后修改时间
     */
    @ApiModelProperty(value = "update_time|最后修改时间", required = false)
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public Long getMortgQuotaHisId() {
        return mortgQuotaHisId;
    }

    public void setMortgQuotaHisId(Long mortgQuotaHisId) {
        this.mortgQuotaHisId = mortgQuotaHisId;
    }

    public Long getMortgQuotaId() {
        return mortgQuotaId;
    }

    public void setMortgQuotaId(Long mortgQuotaId) {
        this.mortgQuotaId = mortgQuotaId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }	

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getInsuredName() {
        return insuredName;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }

    public String getUwYear() {
        return uwYear;
    }

    public void setUwYear(String uwYear) {
        this.uwYear = uwYear;
    }

    public BigDecimal getDelinquencyRate() {
        return delinquencyRate;
    }

    public void setDelinquencyRate(BigDecimal delinquencyRate) {
        this.delinquencyRate = delinquencyRate;
    }

    public BigDecimal getDefaultRate() {
        return defaultRate;
    }

    public void setDefaultRate(BigDecimal defaultRate) {
        this.defaultRate = defaultRate;
    }

    public Long getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Long serialNo) {
        this.serialNo = serialNo;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    public String getCheckedMsg() {
        return checkedMsg;
    }

    public void setCheckedMsg(String checkedMsg) {
        this.checkedMsg = checkedMsg;
    }

    public String getOperType() {
        return operType;
    }

    public void setOperType(String operType) {
        this.operType = operType;
    }

    public Long getOperId() {
        return operId;
    }

    public void setOperId(Long operId) {
        this.operId = operId;
    }

    public Date getOperTime() {
        return operTime;
    }

    public void setOperTime(Date operTime) {
        this.operTime = operTime;
    }

    public Long getCheckedId() {
        return checkedId;
    }

    public void setCheckedId(Long checkedId) {
        this.checkedId = checkedId;
    }

    public Date getCheckedTime() {
        return checkedTime;
    }

    public void setCheckedTime(Date checkedTime) {
        this.checkedTime = checkedTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}