<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-02-10 17:55:58 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussDDLrcIcgCalcDetailDao">
  <!-- 本配置文件由GIS MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**IDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->
  <resultMap id="CustResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussDDLrcIcgCalcDetailVo">
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="MAIN_ID" property="mainId" jdbcType="DECIMAL" />
    <result column="DEV_NO" property="devNo" jdbcType="DECIMAL" />
    <result column="COVERAGE_AMOUNT" property="coverageAmount" jdbcType="DECIMAL" />
    <result column="CSM_RATE" property="csmRate" jdbcType="DECIMAL" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Cust_Column_List">
    ID, MAIN_ID, DEV_NO, COVERAGE_AMOUNT, CSM_RATE
  </sql>

  <select id="findByMainId" flushCache="false" useCache="true" resultMap="CustResultMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussDDLrcIcgCalcDetailVo">
    select
    <include refid="Cust_Column_List" />
    from ATR_BUSS_DD_LRC_ICG_CALC_DETAIL
    where MAIN_ID = #{mainId,jdbcType=DECIMAL}
  </select>

  <select id="findDateByMainId" flushCache="false" useCache="true" resultMap="CustResultMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
    select
    <include refid="Cust_Column_List" />
    from ATR_BUSS_DD_LRC_ICG_CALC_DETAIL b
      where exists(
      select 1 from atr_BUSS_DD_LRC_ICG_CALC a
      where b.MAIN_ID = a.id and a.action_no = #{actionNo,jdbcType=VARCHAR}
      )
    ORDER BY DEV_NO desc
  </select>

  <select id="findByVo" flushCache="false" useCache="true" resultType="Integer"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
    SELECT distinct DEV_NO from ATR_BUSS_DD_LRC_ICG_CALC_DETAIL where MAIN_ID in
    (select id from ATR_BUSS_DD_LRC_ICG_CALC where action_no = #{actionNo,jdbcType=VARCHAR}
    <if test="portfolioNo != null and portfolioNo != ''">
      and portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
    </if>
    <if test="icgNo != null and icgNo != ''">
      and icg_no = #{icgNo,jdbcType=VARCHAR}
    </if>)
    <if test="feeType != null and feeType != ''">
      <choose>
        <when test='feeType=="icgCov"'>
          and COVERAGE_AMOUNT is not null
        </when>
        <when test='feeType=="csm"'>
          and CSM_RATE is not null
        </when>
        <otherwise>
          and COVERAGE_AMOUNT is not null
        </otherwise>
      </choose>
    </if>
    ORDER BY DEV_NO
  </select>
</mapper>
