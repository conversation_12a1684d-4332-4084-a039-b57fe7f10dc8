package com.ss.ifrs.actuarial.service.impl;

import com.alibaba.excel.exception.ExcelAnalysisException;
import com.ss.ifrs.actuarial.dao.buss.alloc.AtrBussClaimImportMainDao;
import com.ss.ifrs.actuarial.dao.buss.alloc.AtrBussIbnrImportClaimDao;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussClaimImportMain;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrImportClaim;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussClaimImportMainVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrImportClaimVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussIbnrImportMainVo;
import com.ss.ifrs.actuarial.service.AtrBussIbnrClaimImportService;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ClassUtil;
import com.ss.platform.util.ExcelExportUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class AtrBussIbnrClaimImportServiceImpl implements AtrBussIbnrClaimImportService {

    @Autowired
    private AtrBussIbnrImportClaimDao atrBussIbnrImportClaimDao;

    @Autowired
    private AtrBussClaimImportMainDao atrBussClaimImportMainDao;

    @Override
    public Page<AtrBussClaimImportMainVo> searchXoClaimPage(AtrBussClaimImportMainVo ibnrImportMainVo, Pageable pageParam) {
        Page<AtrBussClaimImportMainVo> atrBussBecfMainVoPage = atrBussClaimImportMainDao.fuzzySearchPage(ibnrImportMainVo, pageParam);
        return atrBussBecfMainVoPage;
    }
    
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, readOnly = false)
    public void ibnrDataConfirm(AtrBussClaimImportMainVo atrBussBecfMainVo, Long userId) {
        AtrBussClaimImportMain po = atrBussClaimImportMainDao.findById(atrBussBecfMainVo.getClaimMainId());
        if (ObjectUtils.isNotEmpty(po)) {
            Integer count = atrBussClaimImportMainDao.countConfirm(po);
            if (count > 0 ) {
                return;
            }
            po.setConfirmIs("1");
            po.setConfirmId(userId);
            po.setConfirmTime(new Date());
            po.setUpdatorId(userId);
            po.setUpdateTime(new Date());
            atrBussClaimImportMainDao.updateById(po);
        }
    }

    @Override
    public List<AtrBussClaimImportMainVo> findCLList(AtrBussClaimImportMainVo atrBussBecfMainVo) {
        List<AtrBussClaimImportMain> atrBussBecfMainList = atrBussClaimImportMainDao.findList(ClassUtil.convert(atrBussBecfMainVo, AtrBussClaimImportMain.class));
        return ClassUtil.convert(atrBussBecfMainList, AtrBussClaimImportMainVo.class);
    }

    @Override
    public AtrBussClaimImportMainVo findById(Long claimMainId) {
        AtrBussClaimImportMainVo atrBussBecfMainVo = atrBussClaimImportMainDao.findByMainId(claimMainId);

        AtrBussIbnrImportClaim atrBussIbnrImportCalim = new AtrBussIbnrImportClaim();
        atrBussIbnrImportCalim.setClaimMainId(claimMainId);
        List<AtrBussIbnrImportClaim> atrBussIbnrImportCalimList = atrBussIbnrImportClaimDao.findList(atrBussIbnrImportCalim);
        atrBussBecfMainVo.setIbnrImportCalimVoList(ClassUtil.convert(atrBussIbnrImportCalimList, AtrBussIbnrImportClaimVo.class));
        return atrBussBecfMainVo;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, readOnly = false)
    public AtrBussClaimImportMainVo deleteCLByBecfId(Long claimMainId) {
        AtrBussClaimImportMainVo atrBussBecfMainVo = atrBussClaimImportMainDao.findByMainId(claimMainId);
        if (ObjectUtils.isEmpty(atrBussBecfMainVo) || "1".equals(atrBussBecfMainVo.getConfirmIs())) {
            return atrBussBecfMainVo;
        }
        HashMap map = new HashMap();
        map.put("claimMainId", claimMainId);
        atrBussIbnrImportClaimDao.deleteByMap(map);
        atrBussClaimImportMainDao.deleteById(claimMainId);
        return atrBussBecfMainVo;
    }

    @Override
    public AtrBussClaimImportMainVo findByVo(AtrBussClaimImportMainVo ibnrImportMainVo) {
        return atrBussClaimImportMainDao.findByVo(ibnrImportMainVo);
    }

    @Override
    public void claimExcelImport(MultipartFile file, AtrBussClaimImportMainVo ibnrImportMainVo, Long userId) throws Exception {
        List<AtrBussIbnrImportClaim> outwardXClaimList;
        try {
            outwardXClaimList= ExcelExportUtil.read(file.getInputStream(), AtrBussIbnrImportClaim.class, 5);
        } catch (ExcelAnalysisException e) {
            throw e;
        }
        ibnrImportMainVo.setCreatorId(userId);
        ibnrImportMainVo.setCreateTime(new Date());
        ibnrImportMainVo.setConfirmIs("0");
        ibnrImportMainVo.setVersionNo(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        AtrBussClaimImportMain po = ClassUtil.convert(ibnrImportMainVo, AtrBussClaimImportMain.class);
        atrBussClaimImportMainDao.save(po);
        this.saveIbnrClaimData(po.getClaimMainId(), outwardXClaimList);
    }


    @Transactional(propagation = Propagation.REQUIRED, readOnly = false)
    public void saveIbnrClaimData(Long claimMainId, List<AtrBussIbnrImportClaim> ibnrImportCalims) {
        if (CollectionUtils.isEmpty(ibnrImportCalims)) {
            return;
        }
        int spitLen = 500;
        int queueSize = ibnrImportCalims.size() <= spitLen ? 1 : (int) Math.ceil(ibnrImportCalims.size() / spitLen) + 1;
        List handleList = null;
        for (int i = 0; i < queueSize; i++) {
            if ((i + 1) == queueSize) {
                int startIndex = i * spitLen;
                int endIndex = ibnrImportCalims.size();
                handleList = ibnrImportCalims.subList(startIndex, endIndex);
            } else {
                int startIndex = i * spitLen;
                int endIndex = (i + 1) * spitLen;
                handleList = ibnrImportCalims.subList(startIndex, endIndex);
            }
            //确保 保存的handleList中有数据
            if (CollectionUtils.isNotEmpty(handleList)) {
                atrBussIbnrImportClaimDao.saveIbnrDetailList(claimMainId, handleList);
            }
        }
    }
}
