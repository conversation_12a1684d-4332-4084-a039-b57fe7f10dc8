package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.dd;

import com.ss.ifrs.actuarial.util.abp.IgnoreCol;
import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * LRC 合同组 (直保&临分分入) - 对应表 atr_buss_dd_lrc_g
 *
 */
@Data
@Tab("atr_buss_dd_lrc_g")
public class AtrBussLrcDdIcg {

    /** ID (id) */
    private Long id;

    /** 执行编号 (action_no) */
    private String actionNo;

    /** 业务单位ID (entity_id) */
    private Long entityId;

    /** 评估期年月 (year_month) */
    private String yearMonth;

    /** 业务来源代码 (business_type) - 备注: DB-直保、FB-分入 */
    private String businessType;

    /** 险类代码 (risk_class_code) */
    private String riskClassCode;

    /** 合同组合编号 (portfolio_no) */
    private String portfolioNo;

    /** 总保费 (total_premium) */
    private BigDecimal totalPremium;

    /** 总净额结算手续费 (total_net_fee) */
    private BigDecimal totalNetFee;

    /** 跟单IACF (iacf) */
    private BigDecimal iacf;

    /** 非跟单获取费用(对内) (iaehc_in) */
    private BigDecimal iaehcIn;

    /** 非跟单获取费用(对外) (iaehc_out) */
    private BigDecimal iaehcOut;

    /** 减值 (bad_debt) */
    private BigDecimal badDebt;

    /** 合同组号码 (icg_no) */
    private String icgNo; // 注意：这个字段在之前的代码里有，图片里也有
    
    /** 合同组名称 (icg_name) */
    private String icgName;

    /** 退保率 (lapse_rate) */
    private BigDecimal lapseRate;

    /** 维持费用率 (mt_rate) */
    private BigDecimal mtRate;

    /** 赔付率 (claim_rate) */
    private BigDecimal claimRate;

    /** 未到期间接理赔费用率 (ulae_rate) */
    private BigDecimal ulaeRate;

    /** 未到期非金融风险调整率 (ra_rate) */
    private BigDecimal raRate;
    
    /** 盈亏判定结果 (pl_judge_rslt) */
    private String plJudgeRslt;


    // --- 保留 @IgnoreCol 字段 ---
    @IgnoreCol
    private Integer remainingMonths;

    @IgnoreCol
    private Integer maxClmQuotaDevNo;

    /** 发展期已赚保费 */
    @IgnoreCol
    private Map<Integer, BigDecimal> devEdPremiumMap = new HashMap<>();

    @IgnoreCol
    private Map<Integer, BigDecimal> devRecvPremiumMap = new HashMap<>();

    @IgnoreCol
    private Map<Integer, BigDecimal> devIacfMap = new HashMap<>();

    @IgnoreCol
    private Map<Integer, BigDecimal> devIaehcInMap = new HashMap<>();

    @IgnoreCol
    private Map<Integer, BigDecimal> devIaehcOutMap = new HashMap<>();

    @IgnoreCol
    private Map<Integer, BigDecimal> devNetFeeMap = new HashMap<>();

    @IgnoreCol
    private Map<Integer, BigDecimal> devBadDebtMap = new HashMap<>();
    
    /** 发展期已赚净额结算手续费 */
    @IgnoreCol
    private Map<Integer, BigDecimal> devEdNetFeeMap = new HashMap<>();
    
    /** 发展期已赚跟单获取费用 */
    @IgnoreCol
    private Map<Integer, BigDecimal> devEdIacfMap = new HashMap<>();
    
    /** 发展期已赚非跟单获取费用(对内) */
    @IgnoreCol
    private Map<Integer, BigDecimal> devEdIaehcInMap = new HashMap<>();
    
    /** 发展期已赚非跟单获取费用(对外) */
    @IgnoreCol
    private Map<Integer, BigDecimal> devEdIaehcOutMap = new HashMap<>();
}