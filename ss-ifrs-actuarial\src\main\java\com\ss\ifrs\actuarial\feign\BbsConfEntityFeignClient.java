package com.ss.ifrs.actuarial.feign;

import com.ss.platform.pojo.bbs.vo.BbsConfEntityVo;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.conf.FeignAuthConfig;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * 调用BPL业务单位的接口
 *
 * <AUTHOR>
 */
@FeignClient(name = "SS-PLATFORM-COMMON", configuration = {FeignAuthConfig.class})
public interface BbsConfEntityFeignClient {
    /**
     * "根据业务单位ID编码查找业务单位信息"
     *
     * @param entityId 业务单位ID
     * @return 业务单位信息
     */
    @RequestMapping(method = RequestMethod.GET, value = "/basic_entity/find_by_id/{entityId}")
    BbsConfEntityVo findByEntityId(@PathVariable("entityId") Long entityId);
    
	/**
	 * @description: 根据业务单位编码查找业务单位信息
	 * @param : [entityCode]
	 * @return: com.ss.ifrs.datamgr.vo.BbsConfEntityVo
	 * @throws:
	 * @author: yinxh.
	 * @createTime: 2021/5/8 18:45
	 */
	@RequestMapping(value = "/basic_entity/find_by_Code/{entityCode}", method = RequestMethod.GET)
	@PermissionRequest(required = false)
    BbsConfEntityVo findByEntityCode(@PathVariable("entityCode") String entityCode);

}
