package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.ti;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class AtrDuctLrcTiIcuPre {

    /** 险类代码 */
    private String riskClassCode;

    /** 合约号 */
    private String treatyNo;

    private String deptId;

    private String channelId;

    private String centerCode;
    private String finProductCode;
    private String finDetailCode;
    private String finSubProductCode;

    /** 上期累计已赚保费 */
    private BigDecimal preCumlEdPremium;

    /** 上期累计已赚净额结算手续费 */
    private BigDecimal preCumlEdNetFee;

    /** 上期累计已赚跟单获取费用 */
    private BigDecimal preCumlEdIacf;

    /** 上期累计已赚非跟单获取费用 */
    private BigDecimal preCumlEdIaehcIn;
    private BigDecimal preCumlEdIaehcOut;

    /** 当期已赚保费 */
    private BigDecimal curEdPremium;

    /** 当期已赚净额结算手续费 */
    private BigDecimal curEdNetFee;

    /** 当期已赚跟单获取费用 */
    private BigDecimal curEdIacf;

    /** 当期已赚非跟单获取费用 */
    private BigDecimal curEdIaehcIn;
    private BigDecimal curEdIaehcOut;

}
