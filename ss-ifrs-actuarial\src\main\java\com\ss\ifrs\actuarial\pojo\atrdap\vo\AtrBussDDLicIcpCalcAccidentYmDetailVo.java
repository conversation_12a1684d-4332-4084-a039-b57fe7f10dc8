/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-02-04 11:41:51
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrdap.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-04 11:41:51<br/>
 * Description: LIC 计算结果事故年月明细(合同组合维度，直保&临分分入)<br/>
 * Table Name: ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "LIC 计算结果事故年月明细(合同组合维度，直保&临分分入)")
public class AtrBussDDLicIcpCalcAccidentYmDetailVo implements Serializable {
    /**
     * Database column: ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL.ID
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL.MAIN_ID
     * Database remarks: 结果表的ID
     */
    @ApiModelProperty(value = "结果表的ID", required = true)
    private Long mainId;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL.DAMAGE_YEAR_MONTH
     * Database remarks: 事故年月
     */
    @ApiModelProperty(value = "事故年月", required = true)
    private String accidentYearMonth;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL.DEV_NO
     * Database remarks: 发展期
     */
    @ApiModelProperty(value = "发展期", required = false)
    private Short devNo;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL.PAID_MODE
     * Database remarks: 赔付模式
     */
    @ApiModelProperty(value = "赔付模式", required = false)
    private BigDecimal paidMode;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL.IBNR
     * Database remarks: IBNR
     */
    @ApiModelProperty(value = "IBNR", required = false)
    private BigDecimal ibnr;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL.OS
     * Database remarks: O/S
     */
    @ApiModelProperty(value = "O/S", required = false)
    private BigDecimal os;

    /**
     * Database column: ATR_BUSS_DD_LIC_ICP_CALC_ACCIDENT_YM_DETAIL.ULAE
     * Database remarks: 间接理赔费用
     */
    @ApiModelProperty(value = "间接理赔费用", required = false)
    private BigDecimal ulae;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMainId() {
        return mainId;
    }

    public void setMainId(Long mainId) {
        this.mainId = mainId;
    }

    public String getAccidentYearMonth() {
        return accidentYearMonth;
    }

    public void setAccidentYearMonth(String accidentYearMonth) {
        this.accidentYearMonth = accidentYearMonth;
    }

    public Short getDevNo() {
        return devNo;
    }

    public void setDevNo(Short devNo) {
        this.devNo = devNo;
    }

    public BigDecimal getPaidMode() {
        return paidMode;
    }

    public void setPaidMode(BigDecimal paidMode) {
        this.paidMode = paidMode;
    }

    public BigDecimal getIbnr() {
        return ibnr;
    }

    public void setIbnr(BigDecimal ibnr) {
        this.ibnr = ibnr;
    }

    public BigDecimal getOs() {
        return os;
    }

    public void setOs(BigDecimal os) {
        this.os = os;
    }

    public BigDecimal getUlae() {
        return ulae;
    }

    public void setUlae(BigDecimal ulae) {
        this.ulae = ulae;
    }
}