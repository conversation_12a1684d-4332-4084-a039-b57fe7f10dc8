/**
 * 
 * This file was generated by Taiping MyBatis Generator(v1.2.12).
 * Date: 2025-05-28 17:57:11
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao.buss.alloc;

import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussClaimImportMain;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussClaimImportMainVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrImportMain;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussIbnrImportMainVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;

/**
 * This code was generated by Taiping MyBatis Generator(v1.2.12).
 * Create Date: 2025-05-28 17:57:11<br/>
 * Description: null Dao类<br/>
 * Related Table Name: atr_buss_calim_import_main<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrBussClaimImportMainDao extends IDao<AtrBussClaimImportMain, Long> {

    Page<AtrBussClaimImportMainVo> fuzzySearchPage(AtrBussClaimImportMainVo atrBussBecfMainVo, Pageable pageParam);

    AtrBussClaimImportMainVo findByMainId(Long becfMainId);

    Integer countConfirm(AtrBussClaimImportMain atrBussBecfMain);

     AtrBussClaimImportMainVo findByVo(AtrBussClaimImportMainVo ibnrImportMainVo);
}