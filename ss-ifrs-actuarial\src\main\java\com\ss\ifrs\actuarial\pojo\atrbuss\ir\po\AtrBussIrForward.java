/**
 * 
 * This file was generated by Taiping MyBatis Generator(v1.2.12).
 * Date: 2024-08-21 18:15:20
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.ir.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by Taiping MyBatis Generator(v1.2.12).
 * Create Date: 2024-08-21 18:15:20<br/>
 * Description: 当期远期利率主表<br/>
 * Table Name: ATR_BUSS_IR_FORWARD<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "当期远期利率主表")
public class AtrBussIrForward implements Serializable {
    /**
     * Database column: ATR_BUSS_IR_FORWARD.ID
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: ATR_BUSS_IR_FORWARD.UPLOAD_RATE_ID
     * Database remarks: 上传数据的ID
     */
    @ApiModelProperty(value = "上传数据的ID", required = true)
    private Long uploadRateId;

    /**
     * Database column: ATR_BUSS_IR_FORWARD.ENTITY_ID
     * Database remarks: 机构ID
     */
    @ApiModelProperty(value = "机构ID", required = true)
    private Long entityId;

    /**
     * Database column: ATR_BUSS_IR_FORWARD.YEAR_MONTH
     * Database remarks: 评估年月
     */
    @ApiModelProperty(value = "评估年月", required = true)
    private String yearMonth;

    /**
     * Database column: ATR_BUSS_IR_FORWARD.CURRENCY_CODE
     * Database remarks: 币别
     */
    @ApiModelProperty(value = "币别", required = true)
    private String currencyCode;

    /**
     * Database column: ATR_BUSS_IR_FORWARD.CONFIRM_IS
     * Database remarks: 是否确认|0-否、1-是
     */
    @ApiModelProperty(value = "是否确认|0-否、1-是", required = true)
    private String confirmIs;

    /**
     * Database column: ATR_BUSS_IR_FORWARD.CONFIRM_TIME
     * Database remarks: 确认时间
     */
    @ApiModelProperty(value = "确认时间", required = false)
    private Date confirmTime;

    /**
     * Database column: ATR_BUSS_IR_FORWARD.CREATE_TIME
     * Database remarks: 创建时间
     */
    @ApiModelProperty(value = "创建时间", required = false)
    private Date createTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUploadRateId() {
        return uploadRateId;
    }

    public void setUploadRateId(Long uploadRateId) {
        this.uploadRateId = uploadRateId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getConfirmIs() {
        return confirmIs;
    }

    public void setConfirmIs(String confirmIs) {
        this.confirmIs = confirmIs;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}