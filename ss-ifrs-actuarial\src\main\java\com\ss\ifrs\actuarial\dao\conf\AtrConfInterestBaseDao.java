package com.ss.ifrs.actuarial.dao.conf;

import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestBase;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface AtrConfInterestBaseDao extends IDao<AtrConfInterestBase,String> {
    /**
     * 当code相同时更新，否则插入
     * @param atrConfInterestBase 基础参数数据
     */
    void saveOrUpdateByCode(AtrConfInterestBase atrConfInterestBase);
}
