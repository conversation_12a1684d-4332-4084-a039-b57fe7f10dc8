package com.ss.ifrs.actuarial.dao.conf;

import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveIbnrDetailVo;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfCode;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfCodeVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12). Create Date:
 * 2022-03-28 17:44:07<br/>
 * Description: atrConfCode|基础代码表，用于定义基础代码的明细内容，包括代码类型、代码值、代码名称 Dao类<br/>
 * Related Table Name: atr_conf_code<br/>
 * <br/>
 * Remark: 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrConfCodeDao extends IDao<AtrConfCode, Long> {
	Page<Long> fuzzyCodeIdPage(AtrConfCodeVo confCodeVo, Pageable pageParam);

	List<AtrConfCodeVo> findCodeVoListByIds(AtrConfCodeVo confCodeVo);

	AtrConfCodeVo findByCodeId(@Param("codeId") Long codeId);

	List<AtrConfCodeVo> findByUpperCodeId(@Param("upperCodeId") Long upperCodeId);

	AtrConfCodeVo findByCodeCode(@Param("codeCode") String codeCode);

	AtrConfCodeVo findUpperCode(@Param("codeCodeIdx") String upperCode);

	AtrConfCodeVo findCodeVoByCodeIdx(@Param("codeCodeIdx") String codeCodeIdx);

	List<AtrConfCode> findListOrderByDisplayNo(AtrConfCode bplCode);

	Page<AtrConfCodeVo> fuzzySearchPage(AtrConfCodeVo BplCode, Pageable pageParam);

	ArrayList<AtrConfCodeVo> findBycodeArrs(ArrayList<String> codeCodesList);

	Page<AtrConfCodeVo> findListValid(AtrConfCodeVo po, Pageable pageParam);

	Page<?> dynamicSql(Map<String, String> map, Pageable pageParam);

	List<?> dynamicSql(Map map);

	Page<AtrConfCodeVo> findListForAutoComplete(AtrConfCodeVo po, Pageable pageParam);

	List findTreeData(@Param(value = "tableName") String tableName, @Param(value = "param") String... param);

	AtrConfCodeVo findCodeVoByCodeCode(AtrConfCodeVo atrConfCodeVo);

	List<AtrConfCodeVo> selectCodeByUpperCode(AtrConfCodeVo atrConfCodeVo);

	List<AtrBussReserveIbnrDetailVo> findCodeByUpperCode(String codeCodeInx);

	AtrConfCode findByCodeAndIdx(AtrConfCodeVo atrConfCodeVo);

	List<AtrConfCodeVo> findListVoByUpperCode(String codeCodeIdx);

	List<String> findByCodeIdx(String codeCodeIdx);
}