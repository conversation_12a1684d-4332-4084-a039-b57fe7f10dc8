package com.ss.ifrs.actuarial.job;

import com.alibaba.fastjson.JSONObject;
import com.ss.ifrs.actuarial.constant.ActuarialConstant;
import com.ss.ifrs.actuarial.feign.BmsConfCodeFeignClient;
import com.ss.ifrs.actuarial.feign.BmsScheduleFeignClient;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConvertJsonVo;
import com.ss.ifrs.actuarial.service.AtrBussConfigCheckService;
import com.ss.ifrs.actuarial.service.AtrConfBussPeriodService;
import com.ss.library.utils.ClassUtil;
import com.ss.library.utils.DataUtil;
import com.ss.library.utils.FastJsonUtil;
import com.ss.library.utils.TaskExecuteUtils;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.pojo.bms.job.po.BmsScheduleDetail;
import com.ss.platform.pojo.bms.job.vo.BmsScheduleJobVo;
import com.ss.platform.pojo.bms.log.po.BmsLogScheduleJob;
import com.ss.platform.pojo.bms.log.vo.BmsLogScheduleJobVo;
import com.ss.platform.pojo.bms.qrtz.vo.BmsQrtzConfTaskDetailVo;
import com.ss.platform.pojo.bms.qrtz.vo.BmsQrtzConfTaskVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.sql.Date;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

;

/**
 * @ClassName: AtrJobBussApi
 * @Description: 定时任务业务Api
 * @Author: sry
 * @Version: 1.1
 */
@RestController
@RequestMapping(value = "/job")
@Api(value = "计量平台定时任务业务Api")
public class AtrJobBussApi extends BaseApi {
	// 日志管理
	final Logger LOG = LoggerFactory.getLogger(getClass());

	@Autowired
	BmsScheduleFeignClient bmsScheduleFeignClient;

	@Autowired
	AtrConfBussPeriodService atrConfBussPeriodService;

	@Autowired
	AtrBussConfigCheckService atrBussConfigCheckService;

    @Autowired
    BmsConfCodeFeignClient bmsConfCodeFeignClient;

	@Autowired
	JdbcTemplate jdbcTemplate;

	@ApiOperation(value = "计量平台配置检查接口")
	@RequestMapping(value = "/job_check_config", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	@Async("ruleThreadPool")
	BaseResponse<String> jobCheckConfig(@RequestBody JSONObject jsonParam) {
		AtrConfBussPeriodVo atrConfBussPeriodVo = new AtrConfBussPeriodVo();
		atrConfBussPeriodVo.setEntityId(Long.parseLong(jsonParam.getString("entityId")));
		try {
			AtrConfBussPeriodVo checkBussPeriodVo = atrConfBussPeriodService.getOwBussPeriod(atrConfBussPeriodVo);
			if (ObjectUtils.isNotEmpty(checkBussPeriodVo)) {
				atrBussConfigCheckService.configCheck(checkBussPeriodVo, 1L);
			}
			return new BaseResponse<String>(ResCodeConstant.ResCode.SUCCESS, ResCodeConstant.ResCode.SUCCESS);
		} catch (UnexpectedRollbackException e) {
			logger.error(e.getLocalizedMessage(), e);
			return new BaseResponse<String>(ResCodeConstant.ResCode.OTHER_ERROR, null);
		}
	}

	@ApiOperation("同步业务单位下的业务期间状态")
	@RequestMapping(value = "/sync_period_status", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<Object> syncPeriodStatusByEntity(HttpServletRequest request,
														 @RequestBody JSONObject jsonParam) {
		// 获取准备中的业务年月
		List<AtrConfBussPeriodVo> bussPeriodVoList = atrConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PREPARING);
		// 此处查询准备中的业务期间只是做个引子作用，里面实际根据业务单位去更新状态
		if(ObjectUtils.isEmpty(bussPeriodVoList)){
			bussPeriodVoList = atrConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PROCESSING);
		}

		// 统一业务处理接口
		return this.dealFunctionForJob(request, jsonParam, bussPeriodVoList);

	}

	@ApiOperation(value = "ibnr计算入口")
	@RequestMapping(value = "/ibnr_calc", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<Object> ibnrCalc(HttpServletRequest request, @RequestBody JSONObject jsonParam) {

		// 获取准备中的业务年月
		List<AtrConfBussPeriodVo> bussPeriodVoList = atrConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PREPARING);

		// 统一业务处理接口
		return this.dealFunctionForJob(request, jsonParam, bussPeriodVoList);
	}

	/**
	 * @description 提取数据平台数据到计量平台调度任务请求API【自动执行任务/手动触发任务】
	 * @param  request, jsonParam
	 * @return com.ss.platform.pojo.base.po.BaseResponse<java.lang.Object>
	 * @throws
	 * <AUTHOR>
	 * @date 2022/9/16 17:16
	 */
	@ApiOperation(value = "同步数据到精算平台入口")
	@RequestMapping(value = "/sync/pck/atrData", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<Object> drawAtrData(HttpServletRequest request, @RequestBody JSONObject jsonParam) {
		List<AtrConfBussPeriodVo> bussPeriodVoList;
		Long entityId = Long.parseLong(jsonParam.getString("entityId"));
		String status = CommonConstant.BussPeriod.PeriodStatus.PREPARING;
		// 获取准备中的业务年月
		bussPeriodVoList = atrConfBussPeriodService.findBussPeriodVosForTaskJob(entityId, status);
		// 统一业务处理接口
		return this.dealFunctionForJob(request, jsonParam, bussPeriodVoList);
	}

	@ApiOperation("计量")
	@RequestMapping(value = "/actuarial", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<Object> autoTaskQuantification(HttpServletRequest request, @RequestBody JSONObject jsonParam) {
		// 获取处理中的业务年月
		List<AtrConfBussPeriodVo> bussPeriodVoList = atrConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PROCESSING);

		// 统一业务处理接口
		return this.dealFunctionForJob(request, jsonParam, bussPeriodVoList);
	}

	@ApiOperation(value = "IBNR二次分摊数据")
	@RequestMapping(value = "/ibnr_secondary_allocation", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<Object> jobSyncSecondaryAllocation(HttpServletRequest request, @RequestBody JSONObject jsonParam) {
		// 获取处理中的业务年月
		List<AtrConfBussPeriodVo> bussPeriodVoList = atrConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PROCESSING);

		// 统一业务处理接口
		return this.dealFunctionForJob(request, jsonParam, bussPeriodVoList);
	}

	@ApiOperation(value = "计算过渡期已赚保费")
	@RequestMapping(value = "/calculateTransitionEarnedPremium")
	@PermissionRequest(required = false)
	public BaseResponse<Object> calculateTransitionEarnedPremium(HttpServletRequest request, @RequestBody JSONObject jsonParam) {
		// 获取处理中的业务年月
		List<AtrConfBussPeriodVo> bussPeriodVoList = atrConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PROCESSING);
		if ( bussPeriodVoList == null || bussPeriodVoList.isEmpty() ) {
			bussPeriodVoList = atrConfBussPeriodService.findBussPeriodVosForTaskJob(Long.parseLong(jsonParam.getString("entityId")), CommonConstant.BussPeriod.PeriodStatus.PREPARING);
		}
		return dealFunctionForJob(request, jsonParam, bussPeriodVoList);
	}

	/**
	 * @param request, jsonParam, bussPeriodVo
	 * @return com.ss.platform.pojo.base.po.BaseResponse<java.lang.Object>
	 * @throws
	 * @description 处理业务功能自动任务【公共，并初始化功能日志】
	 * <AUTHOR>
	 * @date 2022/9/27 16:16
	 */
	private BaseResponse<Object> dealFunctionForJob(HttpServletRequest request, JSONObject jsonParam, List<AtrConfBussPeriodVo> bussPeriodVoList) {
		Long userId = ObjectUtils.isEmpty(this.loginUserId(request)) ? 1L : this.loginUserId(request);
		// 根据业务单位和功能组ID获取功能组清单【按优先级排序】
		List<BmsQrtzConfTaskDetailVo> confTaskDtlList = bmsScheduleFeignClient.findConfTaskDtlListByConfTaskId(Long.parseLong(jsonParam.getString("entityId")), Long.valueOf(jsonParam.getString("confTaskId")));

		// 检查功能配置是否为空
		if(ObjectUtils.isEmpty(confTaskDtlList)){
			// 无功能任务日志插入，说明未找到合适的业务期间或功能执行，应不做处理
			return new BaseResponse<>(ResCodeConstant.ResCode.RULE_UNMATCH, "No matching The Function found. please check before executing!");
		}

		// 有功能任务日志插入，继续调用JobApi
		if (ObjectUtils.isNotEmpty(bussPeriodVoList)) {

			// 是否执行DB操作发生异常，默认false
			boolean hasErrorCaused = false;

			// 功能任务执行DB异常信息
			String taskDtlErrorMsg = "";

			StringBuilder strBud = new StringBuilder();

			for (AtrConfBussPeriodVo bussPeriodVo : bussPeriodVoList) {
				// 循环功能组
				for (BmsQrtzConfTaskDetailVo confTaskDtlVo : confTaskDtlList) {

					// 捕获功能执行异常，若发生异常不影响其它非依赖功能的执行，有依赖功能关系的更新状态不执行处理
					try {
						// 按功能类型处理
						switch (confTaskDtlVo.getTaskType()) {
							case CommonConstant.ScheduleTaskType.METHOD:
								// 写入功能清单方法参数表，合同组功能链方法参数仅业务单元和业务年月
								AtrConvertJsonVo convertJsonVo = new AtrConvertJsonVo();
								convertJsonVo.setEntityId(Long.parseLong(jsonParam.getString("entityId")));
								convertJsonVo.setYearMonth(bussPeriodVo.getYearMonth());
								// 无账套
								convertJsonVo.setTaskCode(jsonParam.getString("taskCode"));
								convertJsonVo.setTaskMode(CommonConstant.ScheduleTaskMode.AUTO);
								convertJsonVo.setRetryOrder(Long.valueOf(jsonParam.getString("retryOrder")));

								// 各业务平台特殊处理
								convertJsonVo.setEvaluateType(ActuarialConstant.EvaluateType.ALL);

								// 转换为json字符串数据
								String jsonData = FastJsonUtil.toJson(convertJsonVo);

								/* // 查询功能清单方法参数表
								BmsQrtzConfMethodParamVo bplQrtzConfMethodParamVo = new BmsQrtzConfMethodParamVo();
								bplQrtzConfMethodParamVo.setTaskDetailId(confTaskDtlVo.getTaskDetailId());
								bplQrtzConfMethodParamVo.setEntityId(Long.parseLong(jsonParam.getString("entityId")));
								bplQrtzConfMethodParamVo.setYearMonth(bussPeriodVo.getYearMonth());
								bplQrtzConfMethodParamVo.setBookCode(null);
								bplQrtzConfMethodParamVo.setValidIs(CommonConstant.ValidStatus.VALID);

								// 查询参数表
								BmsQrtzConfMethodParamVo taskDetailMethodParam = bmsScheduleFeignClient.findTaskDetailMethodParam(bplQrtzConfMethodParamVo);

								if (ObjectUtils.isNotEmpty(taskDetailMethodParam)) {
									bplQrtzConfMethodParamVo.setParamId(taskDetailMethodParam.getParamId());
								}
								// 刷新参数表
								bplQrtzConfMethodParamVo.setJsonParam(jsonData);
								bmsScheduleFeignClient.addMethodParam(bplQrtzConfMethodParamVo); */

								// 获取方法的参数序列化对象
								Class[] argsClass = ClassUtil.convertClassName(confTaskDtlVo.getSerializedParam());
								Object[] args = ClassUtil.convertArgsObject(argsClass, request, jsonData);
								logger.debug("Class[]: " + Arrays.toString(argsClass));
								logger.debug("Object[]: " + Arrays.toString(args));
								// 执行方法调用，切面内更新功能组日志
								TaskExecuteUtils.invokeMethod(confTaskDtlVo.getMethod(), argsClass, args, confTaskDtlVo.getAsyncIs());

								break;
							case CommonConstant.ScheduleTaskType.PROCEDURE:
								// 获取指定业务期间所在月份的最后一天
								LocalDate yearMonthDate = LocalDate.parse(bussPeriodVo.getYearMonth() + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
								LocalDate yearMonthLastDay = yearMonthDate.with(TemporalAdjusters.lastDayOfMonth());
								// 计量提数入口pck出参定义
								String actionNo = "p_action_no";
								// 执行存储过程，参数个数及数据类型要一致
								TaskExecuteUtils.callProcedureForInOutParam(this.jdbcTemplate,
										confTaskDtlVo.getProcName(),
										confTaskDtlVo.getParam(),
										new Object[]{
												Long.parseLong(jsonParam.getString("entityId")),
												Date.valueOf(yearMonthLastDay)},
										new Object[]{
												actionNo},
										confTaskDtlVo.getAsyncIs());

								// 执行成功后，更新功能组日志
								this.updateScheduleJobLogForCallProcedure(Long.parseLong(jsonParam.getString("entityId")), confTaskDtlVo, bussPeriodVo.getYearMonth(), null, userId);

								// 同步完数据后需要刷新业务期间缓存
								atrConfBussPeriodService.syncPeriodStatusByEntity(Long.parseLong(jsonParam.getString("entityId")), null);
								break;
							default:
								logger.info("Task type not support");
								break;
						}

					} catch (Exception e) {
						// 执行API调度发生异常处理
						logger.error(e.getMessage(), e);

						Map<String, Object> paramMap = new HashMap<>();
						// 更新日志参数1
						paramMap.put("entityId", Long.parseLong(jsonParam.getString("entityId")));
						// 更新日志参数2
						paramMap.put("taskCode", jsonParam.getString("taskCode"));
						// 更新日志参数3
						paramMap.put("taskMode", CommonConstant.ScheduleTaskMode.AUTO);
						// 更新日志参数4
						paramMap.put("yearMonth", bussPeriodVo.getYearMonth());
						// 更新日志参数5
						paramMap.put("bookCode", null);
						// 更新日志参数6
						paramMap.put("funcCode", confTaskDtlVo.getFuncCode());
						// 更新日志参数7
						paramMap.put("taskStatus", CommonConstant.ScheduleLogStatus.FAILED);

						// 功能执行DB发生异常，更新功能任务日志执行状态为：3-失败
						bmsScheduleFeignClient.updateTaskLogStatus(paramMap);

						// 回写每个功能执行异常信息，方便跟踪
						taskDtlErrorMsg = strBud.append(taskDtlErrorMsg).append("; ").append(confTaskDtlVo.getFuncCode()).append(": ").append(DataUtil.getMessageWithMaxLength(ExceptionUtils.getStackTrace(e))).toString();

						// 日志返回DB异常
						hasErrorCaused = true;

					}

				} // 功能任务循环结束
			} // 业务期间循环结束

			// 功能组存在某个功能执行发生异常后返回处理
			if (hasErrorCaused) {
				//记录异常信息
				if (taskDtlErrorMsg.length() > 3500) {
					taskDtlErrorMsg = taskDtlErrorMsg.substring(0, 3500);
				}
				return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, taskDtlErrorMsg);
			}


			// 功能组任务执行正常返回处理
			return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "success");

		} else {
			// 无功能任务日志插入，说明未找到合适的业务期间或功能执行，应不做处理
			return new BaseResponse<>(ResCodeConstant.ResCode.RULE_UNMATCH, "No matching business period found，please check!");
		}

	}

	/**
	 * @description 执行存储过程成功后更新功能组日志状态，并触发下一个依赖它的自动任务
	 * @param  entityId, confTaskDtlVo, yearMonth, bookCode, userId
	 * @return void
	 * @throws
	 * <AUTHOR>
	 * @date 2023/4/4 17:08
	 */
	private void updateScheduleJobLogForCallProcedure(Long entityId, BmsQrtzConfTaskDetailVo confTaskDtlVo, String yearMonth, String bookCode, Long userId) throws Exception{
		/**
		 * 功能组日志处理：
		 *  1、自动/手动模式，在切面执行功能后更新完功能日志，同步更新功能组日志，如果功能执行失败，
		 *      更新功能组日志状态为失败，记录信息；
		 *  2、自动/手动模式，如果当前功能是最后一个功能，且执行成功，则更新功能组日志状态为2-成功。否则为1-处理中
		 */

		BaseResponse<BmsLogScheduleJobVo> jobLogByConfTaskId = bmsScheduleFeignClient.findScheduleJobLogInProgressByConfTaskId(confTaskDtlVo.getConfTaskId(),entityId);

		if(ObjectUtils.isNotEmpty(jobLogByConfTaskId) && ObjectUtils.isNotEmpty(jobLogByConfTaskId.getResData())) {
			BmsLogScheduleJobVo scheduleJobLogVo = jobLogByConfTaskId.getResData();

			/**
			 * 功能组日志处理：
			 *      1、如果当前功能是最后一个功能，则设置功能组日志状态为2-已完成
			 *      2、然后触发下一个依赖它的自动任务执行
			 */

			BmsQrtzConfTaskDetailVo lastTaskDtlVo = bmsScheduleFeignClient.findLastTaskDtlByConfTaskId(entityId, confTaskDtlVo.getConfTaskId());
			// 最后一个功能完成后，更新功能组日志状态为2-已完成
			if (ObjectUtils.isNotEmpty(lastTaskDtlVo) && confTaskDtlVo.getFuncCode().equals(lastTaskDtlVo.getFuncCode())) {
				// 成功
				scheduleJobLogVo.setTaskStatus(CommonConstant.ScheduleLogStatus.SUCCESS);
				// 清除旧信息
				scheduleJobLogVo.setResCode(null);
				scheduleJobLogVo.setResData(null);
				// 最近一次成功结束时间
				scheduleJobLogVo.setLastEndDate(new java.util.Date());
				scheduleJobLogVo.setUpdatorId(userId);
				scheduleJobLogVo.setUpdateTime(new java.util.Date());

				// 更新功能组日志
				if (ObjectUtils.isNotEmpty(yearMonth)) {
					scheduleJobLogVo.setYearMonth(yearMonth);
				}
				if (ObjectUtils.isNotEmpty(bookCode)) {
					scheduleJobLogVo.setBookCode(bookCode);
				}
				BmsLogScheduleJob bmsLogScheduleJob = ClassUtil.convert(scheduleJobLogVo, BmsLogScheduleJob.class);
				bmsScheduleFeignClient.updateScheduleJobLog(bmsLogScheduleJob);


				// 查询该功能组作为被依赖的对象的任务【可能多个】，然后触发下一个依赖它的自动任务执行
				List<BmsQrtzConfTaskVo> taskVoRelationTaskList = bmsScheduleFeignClient.findConfTaskVoByEntityIdAndRelationTaskId(entityId, confTaskDtlVo.getConfTaskId());

				// 获取被依赖的功能组定时任务信息
				if(ObjectUtils.isNotEmpty(taskVoRelationTaskList)){
					for(BmsQrtzConfTaskVo taskVoRelationTaskVo: taskVoRelationTaskList){
						BmsScheduleDetail scheduleDetail = bmsScheduleFeignClient.findJobDetailsByEntityIdAndConfTaskId(entityId, taskVoRelationTaskVo.getConfTaskId());
						if(ObjectUtils.isNotEmpty(scheduleDetail)){
							BmsScheduleJobVo bmsScheduleJobVo = new BmsScheduleJobVo();
							bmsScheduleJobVo.setJobGroup(scheduleDetail.getJobGroup());
							bmsScheduleJobVo.setJobName(scheduleDetail.getJobName());
							// 立即触发依赖任务执行
							bmsScheduleFeignClient.immediateExecutionTask(bmsScheduleJobVo);
						}
					}
				}
			}
		}

		// 若存在待执行的功能组任务日志，则更新日志状态为4-不执行
		BaseResponse<BmsLogScheduleJobVo> jobLogToExecByConfTaskId = bmsScheduleFeignClient.findScheduleJobLogToExecByConfTaskId(confTaskDtlVo.getConfTaskId(),entityId);
		if(ObjectUtils.isNotEmpty(jobLogToExecByConfTaskId) && ObjectUtils.isNotEmpty(jobLogToExecByConfTaskId.getResData())) {
			BmsLogScheduleJobVo scheduleJobLogVo = jobLogToExecByConfTaskId.getResData();

			// 更新功能组日志
			if (ObjectUtils.isNotEmpty(yearMonth)) {
				scheduleJobLogVo.setYearMonth(yearMonth);
			}
			if (ObjectUtils.isNotEmpty(bookCode)) {
				scheduleJobLogVo.setBookCode(bookCode);
			}
			// 已经执行过后，更新为其他待执行的状态为4-不执行
			scheduleJobLogVo.setTaskStatus(CommonConstant.ScheduleLogStatus.NONEXEC);
			// 清除旧信息
			scheduleJobLogVo.setResData("The Job task has been successfully executed by other tasks ");
			BmsLogScheduleJob bmsLogScheduleJob = ClassUtil.convert(scheduleJobLogVo, BmsLogScheduleJob.class);
			bmsScheduleFeignClient.updateScheduleJobLog(bmsLogScheduleJob);
		}
	}

}
