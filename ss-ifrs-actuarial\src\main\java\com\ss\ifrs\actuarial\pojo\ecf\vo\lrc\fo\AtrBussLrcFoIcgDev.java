package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.fo;

import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Tab("atr_buss_fo_lrc_g_dev")
public class AtrBussLrcFoIcgDev {

    /** 主表ID */
    private Long mainId;

    /** 发展期序号 */
    private Integer devNo;

    /** 业务年月 */
    private String yearMonth;

    /** 已赚比例 */
    private BigDecimal edRate;

    /** 已赚保费 */
    private BigDecimal edPremium;

    /** 已赚净额结算 */
    private BigDecimal edNetFee;

    /** 应收保费 */
    private BigDecimal recvPremium;

    /** 净额结算手续费 */
    private BigDecimal netFee;

}
