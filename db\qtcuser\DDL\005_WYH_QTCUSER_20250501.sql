delete from qtc_conf_code where 1=1;
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (12, 0, 'Approved', '版本审批', '版本審批', 'Version Approval', '1', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (85, 12, '0', '未批准', '未批准', 'Rejected', '1', 1, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (86, 12, '1', '批准', '批准', 'Approved', '1', 2, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (510, 0, 'CaseFlowClass', '现金流分类', '現金流分類', 'Cash Flow Class', '1', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (511, 510, '1', '外部-赔付现金流(已发生已报告)', '外部-賠付現金流(已發生已報告)', 'External - Claim Cash Flow(CASE)', '1', 1, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (512, 510, '2', '外部-赔付现金流(已发生未报告)', '外部-賠付現金流(已發生未報告)', 'External - Claim Cash Flow(IBNR)', '1', 2, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (513, 510, '3', '外部-保费现金流', '外部-保費現金流', 'External - Premium Cash Flow', '1', 3, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (514, 510, '4', '系统计算-赔付现金流', '系統計算-賠付現金流', 'System Calculation - Claim Cash Flow', '1', 4, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (515, 510, '5', '系统计算-保费现金流', '系統計算-保費現金流', 'System Calculation - Premium Cash Flow', '1', 5, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (516, 0, 'CaseFlowSource', '现金流数据来源', '現金流數據來源', 'Cash Flow Data Source', '1', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (518, 516, 'EI', '外部导入', '外部導入', 'External Import', '1', 2, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (517, 516, 'SC', '系统计算', '系統計算', 'System Calculation', '1', 1, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (7, 0, 'ConfInterestYear', '曲线利率控制导入配置', '曲线利率控制导入配置', 'Import Interest Curve Configuration', '1', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (125, 7, '5', '曲线利率控制导入配置年份', '曲線利率控制導入配置年份', 'Import Interest Curve Configuration Year', '1', 6, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (21, 0, 'FactorType', '因子类型', '因子类型', 'Factor Type', '1', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (123, 21, 'Lic', 'LIC', 'LIC', 'LIC', '1', 2, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (124, 21, 'Lrc', 'LRC', 'LRC', 'LRC', '1', 1, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (423, 0, 'InterestRateType', '利率类型', '利率類型', 'Interest Rate Type', '1', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (424, 423, '1', '期末/远期利率', '期末/遠期利率', 'Closing/forward interest rate', '1', 3, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (425, 423, '2', '初始确认利率', '初始確認利率', 'Initial recognition interest rate', '1', 2, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (426, 423, '3', '加权初始利率', '加權初始利率', 'Weighted initial interest rate', '1', 1, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (283, 0, 'LOA_ALL', '全量业务线', '全量业务线', 'All Business Lines', '0', NULL, '0', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (284, 283, 'ALL', '全量', '全量', 'All', '0', NULL, '0', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (523, 0, 'QuantificationStatus', '计量执行状态', '計量執行狀態', 'Quantification Execution Status', '1', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (525, 523, 'E', '执行异常', '執行異常', 'Execution Exception', '1', 2, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (524, 523, 'R', '执行中', '執行中', 'Under Execution', '1', 1, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (526, 523, 'S', '执行成功', '執行成功', 'Execution Succeeded', '1', 3, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (603, 0, 'QuotaClass', '假设值归类', '假設值歸類', 'QuotaClassification', '1', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (604, 603, 'A', '精算假设', '精算假設', 'Actuarial assumptions', '1', 2, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (609, 603, 'C', '合同組', '合同組', 'Groups of Insurance Contract', '0', 3, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (605, 603, 'E', '预期现金流假设', '預期現金流假設', 'Expected cash flow assumptions', '1', 3, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (608, 603, 'G', '合同组合', '合同組合', 'Portfolio of Insurance Contract', '1', 2, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (606, 603, 'Q', '计量模型假设', '計量模型假設', 'Quantification model assumptions', '1', 4, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (403, 0, 'QuotaDimension', '假设维度', '假设维度', 'Quota Dimension', '1', NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (404, 403, 'Base', '基础数据', '基礎數據', 'Basic Data', '1', 1, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (407, 404, 'C', '合同組', '合同組', 'Groups of Insurance Contract', '1', 3, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (406, 404, 'G', '合同组合', '合同組合', 'Portfolio of Insurance Contract', '1', 2, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (408, 404, 'U', '保单', '保單', 'Policy', '1', 4, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (405, 403, 'view', '查看', '查看', 'view ', '1', 2, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (409, 405, 'G', '合同组合', '合同組合', 'Portfolio', '1', 1, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (263, 0, 'interestRateTime', '无风险利率曲线时点类型', '無風險利率曲線時點類型', 'Risk-free Interest Curve Time Slot Type', '1', 15, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (265, 263, '0.5', '期中计息/折现到评估时点', '期中計息/折現到評估時點', 'Interim interest / discount to the evaluation time point
', '1', 2, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (264, 263, '0', '期初计息/折现到评估时点', '期初計息/折現到評估時點', 'Accrued Interest in The Beginning of The Period/Discounted to Assessment Time Slot', '1', 1, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (266, 263, '1', '期末计息/折现到评估时点', '期末計息/折現到評估時點', 'Interest at the end of the period / discount to the evaluation time point
', '1', 3, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (22, 0, 'FactorDataCategory', '计量因子分类', '计量因子分类', 'Factor Data Category', NULL, NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (23, 22, 'R1', '保费', '保费', '保费', NULL, 1, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (24, 22, 'R2', '净额结算手续费', '净额结算手续费', '净额结算手续费', NULL, 4, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (25, 22, 'R3', '获取费用', '获取费用', '获取费用', NULL, 2, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (26, 22, 'R4', '非跟单IACF', '非跟单IACF', '非跟单IACF', NULL, 2, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (27, 22, 'R5', '保费坏账（减值）', '保费坏账（减值）', '保费坏账（减值）', NULL, 3, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (28, 22, 'R6', '亏损部分', '亏损部分', '亏损部分', NULL, 6, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (29, 22, 'R7', '投资成分', '投资成分', '投资成分', NULL, 6, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (30, 22, 'I1', '已发生已报告科目', '已发生已报告科目', '已发生已报告科目', NULL, 1, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (31, 22, 'I2', '已发生未报告科目', '已发生未报告科目', '已发生未报告科目', NULL, 2, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (32, 22, 'I3', '间接理赔费用科目', '间接理赔费用科目', '间接理赔费用科目', NULL, 3, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (33, 22, 'I4', '非金融风险调整科目', '非金融风险调整科目', '非金融风险调整科目', NULL, 4, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (34, 22, 'I5', '再保人不履约风险科目', '再保人不履约风险科目', '再保人不履约风险科目', NULL, 5, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (41, 0, 'RunNode', '数据检查节点', '数据检查节点', 'Data Check Node', NULL, NULL, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (42, 41, 'A', '计量前', '计量前', '计量前', NULL, 1, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (43, 41, 'B', '计量检查', '计量检查', '计量检查', NULL, 4, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (44, 41, 'C', '分摊检查', '分摊检查', '分摊检查', NULL, 4, '1', NULL, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (61, 0, 'RptQtcDataDetailConf', '计量明细配置', '计量明细配置', '计量明细配置', '', NULL, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (63, 61, 'PAA_MBR', 'PAA再保前模板', 'PAA再保前模板', 'PAA再保前模板', '', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (64, 61, 'PAA_MO', 'PAA再保前模板', 'PAA再保前模板', 'PAA再保前模板', '', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (661, 521, 'bxfwfy', '保险服务费用', '保险服务费用', '保险服务费用', '{"sign":1,"lrcExclLc":2,"lrcLc":1,"licExclLc":2,"licLc":2,"codeType":"1"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (66, 63, 'qddc1', '保费', '保费', '保费', '{"conf":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (67, 66, 'M3_OA_01', '期初余额', '期初余额', '期初余额', '{"conf":3,"codeType":"2"}', 3001, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (68, 63, 'qddc2', '净额结算手续费', '净额结算手续费', '净额结算手续费', '{"conf":2,"codeType":"1"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (69, 63, 'qddc3', '跟单IACF', '跟单IACF', '跟单IACF', '{"conf":2,"codeType":"1"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (70, 63, 'qddc4', '非跟单IACF', '非跟单IACF', '非跟单IACF', '{"conf":2,"codeType":"1"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (71, 63, 'qddc5', '减值', '减值', '减值', '{"conf":2,"codeType":"1"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (72, 63, 'qddc6', '亏损部分', '亏损部分', '亏损部分', '{"conf":2,"codeType":"1"}', 6, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (73, 63, 'qddc7', '投资成分', '投资成分', '投资成分', '{"conf":2,"codeType":"1"}', 7, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (74, 63, 'qddc8', 'CASE', 'CASE', 'CASE', '{"conf":2,"codeType":"1"}', 8, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (75, 63, 'qddc9', 'IBNR', 'IBNR', 'IBNR', '{"conf":2,"codeType":"1"}', 9, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (76, 63, 'qddc10', '间接理赔费用准备金', '间接理赔费用准备金', '间接理赔费用准备金', '{"conf":2,"codeType":"1"}', 10, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (77, 63, 'qddc11', 'RA', 'RA', 'RA', '{"conf":2,"codeType":"1"}', 11, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (78, 64, 'qddc1', '保费', '保费', '保费', '{"conf":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (79, 64, 'qddc2', '摊回净额结算手续费', '摊回净额结算手续费', '摊回净额结算手续费', '{"conf":2,"codeType":"1"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (80, 64, 'qddc6', '亏损部分', '亏损部分', '亏损部分', '{"conf":2,"codeType":"1"}', 6, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (81, 64, 'qddc7', '投资成分', '投资成分', '投资成分', '{"conf":2,"codeType":"1"}', 7, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (82, 64, 'qddc8', 'CASE', 'CASE', 'CASE', '{"conf":2,"codeType":"1"}', 8, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (83, 64, 'qddc9', 'IBNR', 'IBNR', 'IBNR', '{"conf":2,"codeType":"1"}', 9, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (84, 64, 'qddc10', '间接理赔费用准备金', '间接理赔费用准备金', '间接理赔费用准备金', '{"conf":2,"codeType":"1"}', 10, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1062, 721, 'xgzhsybdhj', '相关综合收益变动合计', '相关综合收益变动合计', '相关综合收益变动合计', '{"sign":1,"lrcExclLc":5,"lrcLc":5,"licExclLc":5,"licLc":5,"codeType":"1","sumList":["fczbxhtbxsy","fczbxhtjrbde","qtsxbd","qtzhsyqtbd"]}', 10, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (663, 662, 'M3_OI_02', 'ISE-CASE-已发生赔款负债提转差 ', 'ISE-CASE-已发生赔款负债提转差', 'ISE-CASE-已发生赔款负债提转差', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (664, 662, 'M3_OJ_02', 'ISE-CASE-已发生赔款负债提转差 ', 'ISE-CASE-已发生赔款负债提转差', 'ISE-CASE-已发生赔款负债提转差', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (184, 141, 'tzcf', '投资成分', '投資成分', 'Investment components', '{"sign":1,"lrcExclLc":2,"lrcLc":4,"lic":2,"codeType":"1"}', 7, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (141, 0, 'QtcDisclosure103BBADataDetailConf', '直保分入披露表M1', '直保分入披露表M1', '直保分入披露表M1', '', NULL, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (665, 662, 'M3_OK_02', 'ISE-CASE-已发生赔款负债提转差 ', 'ISE-CASE-已发生赔款负债提转差', 'ISE-CASE-已发生赔款负债提转差', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (142, 141, 'nczc', '年初资产', '年初資產', 'Opening insurance contract assets', '{"sign":1,"lrcExclLc":1,"lrcLc":1,"lic":1,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (143, 141, 'ncfz', '年初负债', '年初負債', 'Opening insurance contract liabilities', '{"sign":1,"lrcExclLc":1,"lrcLc":1,"lic":1,"codeType":"1"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (144, 141, 'ncye', '年初余额', '年初餘額', 'Net opening balance', '{"sign":1,"lrcExclLc":1,"lrcLc":1,"lic":1,"codeType":"1"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (145, 141, 'zhsybd', '综合收益变动', '綜合收益變動', 'Changes in the statement of profit or loss and other comprehensive income', '{"sign":1,"lrcExclLc":1,"lrcLc":1,"lic":1,"codeType":"1"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (146, 141, 'bxhtsr', '保险合同收入', '綜合收益變動', 'Changes in the statement of profit or loss and other comprehensive income', '{"sign":1,"lrcExclLc":2,"lrcLc":1,"lic":1,"codeType":"1"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (147, 146, 'jxgdzsfxdht', '经修改的追溯法下的合同', '經修改的追溯法下的合同', 'Contracts under the modified retrospective transition approach', '{"sign":1,"lrcExclLc":1,"lrcLc":1,"lic":1,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (148, 146, 'gyjzfxdht', '公允价值法下的合同', '公允價值法下的合同', 'Contracts under the fair value transition approach', '{"sign":1,"lrcExclLc":1,"lrcLc":1,"lic":1,"codeType":"1"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (149, 146, 'qtht', '其他合同', '其他合同', 'Other contracts', '{"sign":1,"lrcExclLc":2,"lrcLc":1,"lic":1,"codeType":"1"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (150, 149, 'M01_EP_0052', '保险合同收入_预期赔付与费用_非亏损部分', '保險合同收入_預期賠付與費用_非虧損部分', 'Insurance revenue_Expected payment and expense_Excl LC', '{"sign":-1,"lrcExclLc":3,"lrcLc":4,"lic":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (151, 149, 'M01_EP_0053', '保险合同收入_预期释放的非金融风险调整_非亏损部分', '保險合同收入_預期釋放的非金融風險調整_非虧損部分', 'Insurance revenue_Expected release of RA_Excl LC', '{"sign":-1,"lrcExclLc":3,"lrcLc":4,"lic":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (152, 149, 'M01_EP_0005', '保险合同收入_摊销的CSM', '保險合同收入_攤銷的CSM', 'Insurance revenue_CSM Amortization', '{"sign":-1,"lrcExclLc":3,"lrcLc":4,"lic":4,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (153, 149, 'M01_EP_0006', '保险合同收入_摊销的IACF', '保險合同收入_攤銷的IACF', 'Insurance revenue_IACF Amortization', '{"sign":-1,"lrcExclLc":3,"lrcLc":4,"lic":4,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (154, 149, 'M01_EP_0007', '保险合同收入_与过去服务相关的经验调整', '保險合同收入_與過去服務相關的經驗調整', 'Insurance revenue_Adjustment of past service', '{"sign":-1,"lrcExclLc":3,"lrcLc":4,"lic":4,"codeType":"2"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (155, 149, 'M01_EP_0008', '保险合同收入_分解的投资成分', '保險合同收入_分解的投資成分', 'Insurance revenue_Investment component recognized', '{"sign":-1,"lrcExclLc":3,"lrcLc":4,"lic":4,"codeType":"2"}', 6, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (156, 141, 'bxfwzc', '保险服务支出', '保險服務支出', 'Insurance service expenses', '{"sign":1,"lrcExclLc":2,"lrcLc":2,"lic":2,"codeType":"1"}', 6, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (157, 156, 'yfspkjqtbxfwzc', '已发生赔款及其他保险服务支出', '已發生賠款及其他保險服務支出', 'Incurred claims and other insurance service expenses', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"lic":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (158, 157, 'M05_EP_0029', '现金流_支付的赔付与费用_当年新增', '現金流_支付的賠付與費用_當年新增', 'Cash Flow_ Paid Claim$Expense_New Added in Current Year', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"lic":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (159, 157, 'M05_EP_0022', '赔付与费用_已发生已报告未决赔款负债提转差_当年新增_预期现金流', '賠付與費用_已發生已報告未決賠款負債提轉差_當年新增_預期現金流', 'Claims and expense_change in case reserves_New Added in Current Year_Expected cashflows', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"lic":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (160, 157, 'M05_EP_0026', '赔付与费用_已发生已报告未决赔款负债提转差_当年新增_非金融风险调整', '賠付與費用_已發生已報告未決賠款負債提轉差_當年新增_非金融風險調整', 'Claims and expense_change in case reserves_New Added in Current Year_RA', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"lic":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (227, 68, 'M3_OB_03', '净额结算手续费计息', '净额结算手续费计息', '净额结算手续费计息', '{"conf":3,"codeType":"2"}', 3008, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (161, 157, 'M05_EP_0023', '赔付与费用_已发生未报告未决赔款负债提转差_当年新增_预期现金流', '賠付與費用_已發生未報告未決賠款負債提轉差_當年新增_預期現金流', 'Claims and expense_change in IBNR_New Added in Current Year_Expected cashflows', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"lic":3,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (162, 157, 'M05_EP_0027', '赔付与费用_已发生未报告未决赔款负债提转差_当年新增_非金融风险调整', '賠付與費用_已發生未報告未決賠款負債提轉差_當年新增_非金融風險調整', 'Claims and expense_change in IBNR_New Added in Current Year_RA', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"lic":3,"codeType":"2"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (163, 157, 'M05_EP_0024', '赔付与费用_间接理赔费用提转差_当年新增_预期现金流', '賠付與費用_間接理賠費用提轉差_當年新增_預期現金流', 'Claims and expense_change in ULAE_New Added in Current Year_Expected cashflows', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"lic":3,"codeType":"2"}', 6, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (164, 157, 'M05_EP_0028', '赔付与费用_间接理赔费用提转差_当年新增_非金融风险调整', '賠付與費用_間接理賠費用提轉差_當年新增_非金融風險調整', 'Claims and expense_change in ULAE_New Added in Current Year_RA', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"lic":3,"codeType":"2"}', 7, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (165, 157, 'M05_EP_0025', '赔付与费用_调整手续费提转差_当年新增_预期现金流', '賠付與費用_調整手續費提轉差_當年新增_預期現金流', 'Claims and expense_change in adjustment of  provisional commission_New Added in Current Year_RA', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"lic":3,"codeType":"2"}', 8, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (166, 156, 'bxhqxjldtx', '保险获取现金流的摊销', '保險獲取現金流的攤銷', 'Amortisation of insurance acquisition cash flows', '{"sign":1,"lrcExclLc":2,"lrcLc":4,"lic":4,"codeType":"1"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (167, 166, 'M01_EP_0011', '赔付与费用_摊销的IACF', '賠付與費用_攤銷的IACF', 'Claims and expense_IACF Amortization', '{"sign":-1,"lrcExclLc":3,"lrcLc":4,"lic":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (168, 156, 'kshtzdssjgdssdzh', '亏损合同组的损失及该等损失的转回', '虧損合同組的損失及該等損失的轉回', 'Losses and reversal of losses on onerous contracts', '{"sign":1,"lrcExclLc":4,"lrcLc":2,"lic":4,"codeType":"1"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (169, 168, 'M01_EP_0009', '赔付与费用_亏损分摊_预期现金流', '賠付與費用_虧損分攤_預期現金流', 'Claims and expense_Release in LC_Expected cashflows', '{"sign":-1,"lrcExclLc":4,"lrcLc":3,"lic":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (170, 168, 'M01_EP_0010', '赔付与费用_亏损分摊_非金融风险调整', '賠付與費用_虧損分攤_非金融風險調整', 'Claims and expense_Release in LC_RA', '{"sign":-1,"lrcExclLc":4,"lrcLc":3,"lic":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (171, 168, 'M01_EP_0013', '亏损合同损益_新增合同预期现金流_赔付与费用现金流_亏损', '虧損合同損益_新增合同預期現金流_賠付與費用現金流_虧損', 'Change in LC_Expected cashflows of New contract_Claims and expense_LC', '{"sign":-1,"lrcExclLc":4,"lrcLc":3,"lic":4,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (172, 168, 'M01_EP_0014', '亏损合同损益_新增合同非金融风险调整_亏损', '虧損合同損益_新增合同非金融風險調整_虧損', 'Change in LC_RA of New contract_Claims and expense_LC', '{"sign":-1,"lrcExclLc":4,"lrcLc":3,"lic":4,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (173, 168, 'M01_EP_0015', '亏损合同损益_不调整CSM的预期现金流变动', '虧損合同損益_不調整CSM的預期現金流變動', 'Change in LC_Expected cashflows_not adjusting CSM', '{"sign":-1,"lrcExclLc":4,"lrcLc":3,"lic":4,"codeType":"2"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (174, 168, 'M01_EP_0016', '亏损合同损益_不调整CSM的非金融风险调整变动', '虧損合同損益_不調整CSM的非金融風險調整變動', 'Change in LC_RA_not adjusting CSM', '{"sign":-1,"lrcExclLc":4,"lrcLc":3,"lic":4,"codeType":"2"}', 6, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (175, 156, 'yfspkfzdtz', '已发生赔款负债的调整', '已發生賠款負債的調整', 'Changes to liabilities for incurred claims', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"lic":2,"codeType":"1"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (176, 175, 'M05_EP_0037', '现金流_支付的赔付与费用_往年变动', '現金流_支付的賠付與費用_往年變動', 'Cash Flow_ Paid Claim$Expense_Changed in Previous Year', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"lic":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (177, 175, 'M05_EP_0030', '赔付与费用_已发生已报告未决赔款负债提转差_往年变动_预期现金流', '賠付與費用_已發生已報告未決賠款負債提轉差_往年變動_預期現金流', 'Claims and expense_change in case reserves_Changed in Previous Year_Expected cashflows', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"lic":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (178, 175, 'M05_EP_0034', '赔付与费用_已发生已报告未决赔款负债提转差_往年变动_非金融风险调整', '賠付與費用_已發生已報告未決賠款負債提轉差_往年變動_非金融風險調整', 'Claims and expense_change in case reserves_Changed in Previous Year_RA', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"lic":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (179, 175, 'M05_EP_0031', '赔付与费用_已发生未报告未决赔款负债提转差_往年变动_预期现金流', '賠付與費用_已發生未報告未決賠款負債提轉差_往年變動_預期現金流', 'Claims and expense_change in IBNR_Changed in Previous Year_Expected cashflows', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"lic":3,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (180, 175, 'M05_EP_0035', '赔付与费用_已发生未报告未决赔款负债提转差_往年变动_非金融风险调整', '賠付與費用_已發生未報告未決賠款負債提轉差_往年變動_非金融風險調整', 'Claims and expense_change in IBNR_Changed in Previous Year_RA', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"lic":3,"codeType":"2"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (181, 175, 'M05_EP_0032', '赔付与费用_间接理赔费用提转差_往年变动_预期现金流', '賠付與費用_間接理賠費用提轉差_往年變動_預期現金流', 'Claims and expense_change in ULAE_Changed in Previous Year_Expected cashflows', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"lic":3,"codeType":"2"}', 6, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (182, 175, 'M05_EP_0036', '赔付与费用_间接理赔费用提转差_往年变动_非金融风险调整', '賠付與費用_間接理賠費用提轉差_往年變動_非金融風險調整', 'Claims and expense_change in ULAE_Changed in Previous Year_RA', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"lic":3,"codeType":"2"}', 7, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (183, 175, 'M05_EP_0033', '赔付与费用_调整手续费提转差_往年变动_预期现金流', '賠付與費用_調整手續費提轉差_往年變動_預期現金流', 'Claims and expense_change in adjustment of  provisional commission_Changed in Previous Year_RA', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"lic":3,"codeType":"2"}', 8, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (185, 184, 'M01_EP_0012', '赔付与费用_分解的投资成分', '賠付與費用_分解的投資成分', 'Claims and expense_Investment component recognized', '{"sign":1,"lrcExclLc":6,"lrcLc":4,"lic":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (186, 141, 'bxfwyj', '保险服务业绩', '保險服務業績', 'Insurance service results', '{"sign":1,"lrcExclLc":5,"lrcLc":5,"lic":5,"codeType":"1","sumList":["bxhtsr","bxfwzc","tzcf"]}', 8, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (187, 186, 'bxhtjcwsy', '保险合同净财务损益', '保險合同淨財務損益', 'Insurance finance income / expenses', '{"sign":1,"lrcExclLc":2,"lrcLc":2,"lic":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (188, 187, 'M01_EP_0017', 'IFIE_未到期_预期现金流_非亏损', 'IFIE_未到期_預期現金流_非虧損', 'IFIE_LRC_Expected cashflows_Excl LC', '{"sign":-1,"lrcExclLc":3,"lrcLc":4,"lic":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (189, 187, 'M01_EP_0019', 'IFIE_未到期_非金融风险调整_非亏损', 'IFIE_未到期_非金融風險調整_非虧損', 'IFIE_LRC_RA_Excl LC', '{"sign":-1,"lrcExclLc":3,"lrcLc":4,"lic":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (190, 187, 'M01_EP_0021', 'IFIE_未到期_CSM', 'IFIE_未到期_CSM', 'IFIE_LRC_CSM', '{"sign":-1,"lrcExclLc":3,"lrcLc":4,"lic":4,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (192, 187, 'M01_EP_0022', 'OCI_未到期_预期现金流_非亏损', 'OCI_未到期_預期現金流_非虧損', 'OCI_LRC_Expected cashflows_Excl LC', '{"sign":-1,"lrcExclLc":3,"lrcLc":4,"lic":4,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (193, 187, 'M01_EP_0024', 'OCI_未到期_非金融风险调整_非亏损', 'OCI_未到期_非金融風險調整_非虧損', 'OCI_LRC_RA_Excl LC', '{"sign":-1,"lrcExclLc":3,"lrcLc":4,"lic":4,"codeType":"2"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (194, 187, 'M01_EP_0018', 'IFIE_未到期_预期现金流_亏损', 'IFIE_未到期_預期現金流_虧損', 'IFIE_LRC_Expected cashflows_LC', '{"sign":-1,"lrcExclLc":4,"lrcLc":3,"lic":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (195, 187, 'M01_EP_0020', 'IFIE_未到期_非金融风险调整_亏损', 'IFIE_未到期_非金融風險調整_虧損', 'IFIE_LRC_RA_LC', '{"sign":-1,"lrcExclLc":4,"lrcLc":3,"lic":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (196, 187, 'M01_EP_0023', 'OCI_未到期_预期现金流_亏损', 'OCI_未到期_預期現金流_虧損', 'OCI_LRC_Expected cashflows_LC', '{"sign":-1,"lrcExclLc":4,"lrcLc":3,"lic":4,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (197, 187, 'M01_EP_0025', 'OCI_未到期_非金融风险调整_亏损', 'OCI_未到期_非金融風險調整_虧損', 'OCI_LRC_RA_LC', '{"sign":-1,"lrcExclLc":4,"lrcLc":3,"lic":4,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (198, 187, 'M05_EP_0009', 'IFIE_已发生_预期现金流', 'IFIE_已發生_預期現金流', 'IFIE_LIC_Expected cashflows', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"lic":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (199, 187, 'M05_EP_0019', 'IFIE_已发生_非金融风险调整', 'IFIE_已發生_非金融風險調整', 'IFIE_LIC_RA', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"lic":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (200, 187, 'M05_EP_0010', 'OCI_已发生_预期现金流', 'OCI_已發生_預期現金流', 'OCI_LIC_Expected cashflows', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"lic":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (201, 187, 'M05_EP_0020', 'OCI_已发生_非金融风险调整', 'OCI_已發生_非金融風險調整', 'OCI_LIC_RA', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"lic":3,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (202, 141, 'hlbddyx', '汇率变动的影响', '匯率變動的影響', 'Effect of movements in exchange rates', '{"sign":1,"lrcExclLc":1,"lrcLc":1,"lic":1,"codeType":"1"}', 9, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (203, 141, 'zhsybdhj', '综合收益变动合计', '綜合收益變動合計', 'Total changes in the statement of profit or loss and other comprehensive income', '{"sign":1,"lrcExclLc":5,"lrcLc":5,"lic":5,"codeType":"1","sumList":["bxfwyj","bxhtjcwsy"]}', 10, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (204, 141, 'xjl', '现金流', '現金流', 'Cash flows', '{"sign":1,"lrcExclLc":1,"lrcLc":1,"lic":1,"codeType":"1"}', 11, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (205, 204, 'sddbf', '收到的保费', '現金流', 'Cash flows', '{"sign":1,"lrcExclLc":2,"lrcLc":4,"lic":4,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (206, 205, 'M01_EP_0055', '现金流_收到的保费', '現金流_收到的保費', 'Cashflows_Premium received', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"lic":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (207, 204, 'zfdpkjqtbxfwzcbktzcf', '支付的赔款及其他保险服务支出，包括投资成分', '支付的賠款及其他保險服務支出，包括投資成分', 'Claims and other insurance service expenses paid, including investment components', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"lic":2,"codeType":"1"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (208, 207, 'M05_EP_0012', '现金流_支付的赔付与费用', '現金流_支付的賠付與費用', 'Cash Flow_ Paid Claim$Expense_Occured_Expected', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"lic":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (209, 204, 'bxhqxjl', '保险获取现金流', '保險獲取現金流', 'Insurance acquisition cash flows paid', '{"sign":1,"lrcExclLc":2,"lrcLc":4,"lic":4,"codeType":"1"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (210, 209, 'M01_EP_0056', '现金流_支付的获取费用', '現金流_支付的獲取費用', 'Cashflows_IACF paid', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"lic":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (211, 141, 'xjlhj', '现金流合计', '現金流合計', 'Cash flows', '{"sign":1,"lrcExclLc":5,"lrcLc":5,"lic":5,"codeType":"1","sumList":["sddbf","zfdpkjqtbxfwzcbktzcf","bxhqxjl"]}', 12, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (212, 211, 'qttz', '其他调整', '其他調整', 'Other adjustments', '{"sign":1,"lrcExclLc":1,"lrcLc":1,"lic":1,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (213, 141, 'nmye', '年末余额', '年末餘額', 'Net closing balance', '{"sign":1,"lrcExclLc":5,"lrcLc":5,"lic":5,"codeType":"1","sumList":["ncye","zhsybdhj","xjlhj","qttz"]}', 13, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (221, 66, 'M3_OA_02', '当期确认的保险收入 （未扣除投资成分）', '当期确认的保险收入 （未扣除投资成分）', '当期确认的保险收入 （未扣除投资成分）', '{"conf":3,"codeType":"2"}', 3002, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (222, 66, 'M3_OA_03', '保费现金流计息', '保费现金流计息', '保费现金流计息', '{"conf":3,"codeType":"2"}', 3003, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (223, 66, 'M3_OA_04', '实际保费现金流', '实际保费现金流', '实际保费现金流', '{"conf":3,"codeType":"2"}', 3004, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (224, 66, 'M3_OA_05', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 3005, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (225, 68, 'M3_OB_01', '期初余额', '期初余额', '期初余额', '{"conf":3,"codeType":"2"}', 3006, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (226, 68, 'M3_OB_02', '当期摊销的净额结算手续费', '当期摊销的净额结算手续费', '当期摊销的净额结算手续费', '{"conf":3,"codeType":"2"}', 3007, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (228, 68, 'M3_OB_04', '实际净额结算手续费现金流', '实际净额结算手续费现金流', '实际净额结算手续费现金流', '{"conf":3,"codeType":"2"}', 3009, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (229, 68, 'M3_OB_05', '期初余额', '期初余额', '期初余额', '{"conf":3,"codeType":"2"}', 3010, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (230, 69, 'M3_OC_01', '期初余额', '期初余额', '期初余额', '{"conf":3,"codeType":"2"}', 3011, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (231, 69, 'M3_OC_02', '当期摊销的跟单获取费用现金流', '当期摊销的跟单获取费用现金流', '当期摊销的跟单获取费用现金流', '{"conf":3,"codeType":"2"}', 3012, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (232, 69, 'M3_OC_03', '跟单获取费用现金流计息', '跟单获取费用现金流计息', '跟单获取费用现金流计息', '{"conf":3,"codeType":"2"}', 3013, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (233, 69, 'M3_OC_04', '实际跟单获取费用现金流', '实际跟单获取费用现金流', '实际跟单获取费用现金流', '{"conf":3,"codeType":"2"}', 3017, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (234, 69, 'M3_OC_05', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 3018, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (235, 70, 'M3_OD_01', '期初余额', '期初余额', '期初余额', '{"conf":3,"codeType":"2"}', 3019, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (236, 70, 'M3_OD_02', '当期摊销的非跟单获取费用现金流', '当期摊销的非跟单获取费用现金流', '当期摊销的非跟单获取费用现金流', '{"conf":3,"codeType":"2"}', 3020, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (237, 70, 'M3_OD_03', '非跟单获取费用现金流计息', '非跟单获取费用现金流计息', '非跟单获取费用现金流计息', '{"conf":3,"codeType":"2"}', 3021, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (238, 70, 'M3_OD_04', '实际非跟单获取费用现金流', '实际非跟单获取费用现金流', '实际非跟单获取费用现金流', '{"conf":3,"codeType":"2"}', 3022, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (239, 70, 'M3_OD_05', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 3023, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (240, 71, 'M3_OE_01', '期初余额', '期初余额', '期初余额', '{"conf":3,"codeType":"2"}', 3024, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (241, 71, 'M3_OE_02', '当期摊销的减值', '当期摊销的减值', '当期摊销的减值', '{"conf":3,"codeType":"2"}', 3025, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (242, 71, 'M3_OE_03', '减值现金流计息', '减值现金流计息', '减值现金流计息', '{"conf":3,"codeType":"2"}', 3026, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (243, 71, 'M3_OE_04', '实际计提减值', '实际计提减值', '实际计提减值', '{"conf":3,"codeType":"2"}', 3027, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (244, 71, 'M3_OE_05', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 3028, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (245, 72, 'M3_OF_01', '期初余额', '期初余额', '期初余额', '{"conf":3,"codeType":"2"}', 3071, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (246, 72, 'M3_OF_02', '亏损部分当期的变动金额', '亏损部分当期的变动金额', '亏损部分当期的变动金额', '{"conf":3,"codeType":"2"}', 3029, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (247, 72, 'M3_OF_03', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 3030, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (666, 662, 'M3_OG_01', 'ISE-CASE-已发生赔款负债提转差 ', 'ISE-CASE-已发生赔款负债提转差', 'ISE-CASE-已发生赔款负债提转差', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (324, 323, 'wdqzrzc', '其中：未到期责任资产', '其中：未到期责任资产', '其中：未到期责任资产', '{"conf":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (325, 323, 'yfspkzc', ' 已发生赔款资产', ' 已发生赔款资产', ' 已发生赔款资产', '{"conf":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (327, 326, 'wdqzrfz', '未到期责任负债', '未到期责任负债', '未到期责任负债', '{"conf":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (328, 326, 'yfspkfz', ' 已发生赔款负债', ' 已发生赔款负债', ' 已发生赔款负债', '{"conf":2,"codeType":"1"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (281, 0, 'QtcComprehensiveIncomeTableConf', '直保分入报表', '直保分入报表', '直保分入报表', '', NULL, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (341, 331, 'dqbxhqxjl', '当期摊销的保险获取现金流', '当期摊销的保险获取现金流', '当期摊销的保险获取现金流', '{"conf":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (342, 331, 'yfspkjfy', '已发生赔款及费用', '已发生赔款及费用', '已发生赔款及费用', '{"conf":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (343, 331, 'dqbxhqxjlbd', '与过去服务相关的变动，即与已发生赔款负债相关的履约现金流变动', '与过去服务相关的变动，即与已发生赔款负债相关的履约现金流变动', '与过去服务相关的变动，即与已发生赔款负债相关的履约现金流变动', '{"conf":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (344, 331, 'ksbfdqrjzh', '亏损部分的确认及转回', '亏损部分的确认及转回', '亏损部分的确认及转回', '{"conf":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (345, 331, 'tzch', '投资成分', '投资成分', '投资成分', '{"conf":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (326, 281, 'bxhtfz', '保险合同负债', '保险合同负债', '保险合同负债', '{"conf":2,"codeType":"1"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (249, 74, 'M3_OI_01', '期初余额', '期初余额', '期初余额', '{"conf":3,"codeType":"2"}', 3032, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (323, 281, 'bxhtzc', '保险合同资产', '保险合同资产', '保险合同资产', '{"conf":2,"codeType":"1"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (330, 281, 'bxhtsr', '保险服务收入', '保险服务收入', 'Insurance revenue', '{"conf":2,"codeType":"1"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (331, 281, 'bxfwfy', '保险服务费用', '保险服务费用', '保险服务费用', '{"conf":2,"codeType":"1"}', 6, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (301, 281, 'hbzj', '货币资金', '货币资金', '货币资金', '{"conf":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (667, 662, 'M3_OL_02', 'ISE-CASE-已发生赔款负债提转差 ', 'ISE-CASE-已发生赔款负债提转差', 'ISE-CASE-已发生赔款负债提转差', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (248, 73, 'M3_OG_01', '当期确认的投资成分', '当期确认的投资成分', '当期确认的投资成分', '{"conf":3,"codeType":"2"}', 3031, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (361, 74, 'M3_OI_01', '期初余额', '期初余额', '期初余额', '{"conf":3,"codeType":"2"}', 3032, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (362, 74, 'M3_OI_02', 'ISE-CASE-已发生赔款负债提转差 (与当前服务相关)', 'ISE-CASE-已发生赔款负债提转差 (与当前服务相关)', 'ISE-CASE-已发生赔款负债提转差 (与当前服务相关)', '{"conf":3,"codeType":"2"}', 3033, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (363, 74, 'M3_OI_03', 'ISE-CASE-已发生赔款负债提转差 (与过去服务相关)', 'ISE-CASE-已发生赔款负债提转差 (与过去服务相关)', 'ISE-CASE-已发生赔款负债提转差 (与过去服务相关)', '{"conf":3,"codeType":"2"}', 3034, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (364, 74, 'M3_OI_04', 'IFIE-CASE-计提利息的金额', 'IFIE-CASE-计提利息的金额', 'IFIE-CASE-计提利息的金额', '{"conf":3,"codeType":"2"}', 3035, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (365, 74, 'M3_OI_05', 'OCI-CASE-利率变动的金额', 'OCI-CASE-利率变动的金额', 'OCI-CASE-利率变动的金额', '{"conf":3,"codeType":"2"}', 3036, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (366, 74, 'M3_OI_06', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 3037, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (367, 75, 'M3_OJ_01', '期初余额', '期初余额', '期初余额', '{"conf":3,"codeType":"2"}', 3038, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (368, 75, 'M3_OJ_02', 'ISE-IBNR-已发生赔款负债提转差 (与当前服务相关)', 'ISE-IBNR-已发生赔款负债提转差 (与当前服务相关)', 'ISE-IBNR-已发生赔款负债提转差 (与当前服务相关)', '{"conf":3,"codeType":"2"}', 3039, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (369, 75, 'M3_OJ_03', 'ISE-IBNR-已发生赔款负债提转差 (与过去服务相关)', 'ISE-IBNR-已发生赔款负债提转差 (与过去服务相关)', 'ISE-IBNR-已发生赔款负债提转差 (与过去服务相关)', '{"conf":3,"codeType":"2"}', 3040, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (370, 75, 'M3_OJ_04', 'IFIE-IBNR-计提利息的金额', 'IFIE-IBNR-计提利息的金额', 'IFIE-IBNR-计提利息的金额', '{"conf":3,"codeType":"2"}', 3041, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (371, 75, 'M3_OJ_05', 'OCI-IBNR-利率变动的金额', 'OCI-IBNR-利率变动的金额', 'OCI-IBNR-利率变动的金额', '{"conf":3,"codeType":"2"}', 3042, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (372, 75, 'M3_OJ_06', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 3043, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (373, 76, 'M3_OK_01', '期初余额', '期初余额', '期初余额', '{"conf":3,"codeType":"2"}', 3044, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (374, 76, 'M3_OK_02', 'ISE-ULAE-已发生赔款负债提转差 (与当前服务相关)', 'ISE-ULAE-已发生赔款负债提转差 (与当前服务相关)', 'ISE-ULAE-已发生赔款负债提转差 (与当前服务相关)', '{"conf":3,"codeType":"2"}', 3045, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (375, 76, 'M3_OK_03', 'ISE-ULAE-已发生赔款负债提转差 (与过去服务相关)', 'ISE-ULAE-已发生赔款负债提转差 (与过去服务相关)', 'ISE-ULAE-已发生赔款负债提转差 (与过去服务相关)', '{"conf":3,"codeType":"2"}', 3046, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (376, 76, 'M3_OK_04', 'IFIE-ULAE-计提利息的金额', 'IFIE-ULAE-计提利息的金额', 'IFIE-ULAE-计提利息的金额', '{"conf":3,"codeType":"2"}', 3047, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (377, 76, 'M3_OK_05', 'OCI-ULAE-利率变动的金额', 'OCI-ULAE-利率变动的金额', 'OCI-ULAE-利率变动的金额', '{"conf":3,"codeType":"2"}', 3048, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (378, 76, 'M3_OK_06', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 3049, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (379, 77, 'M3_OL_01', '期初余额', '期初余额', '期初余额', '{"conf":3,"codeType":"2"}', 3050, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (380, 77, 'M3_OL_02', 'ISE-RA-已发生赔款负债提转差 (与当前服务相关)', 'ISE-RA-已发生赔款负债提转差 (与当前服务相关)', 'ISE-RA-已发生赔款负债提转差 (与当前服务相关)', '{"conf":3,"codeType":"2"}', 3051, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (381, 77, 'M3_OL_03', 'ISE-RA-已发生赔款负债提转差 (与过去服务相关)', 'ISE-RA-已发生赔款负债提转差 (与过去服务相关)', 'ISE-RA-已发生赔款负债提转差 (与过去服务相关)', '{"conf":3,"codeType":"2"}', 3052, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (382, 77, 'M3_OL_04', 'IFIE-RA-计提利息的金额', 'IFIE-RA-计提利息的金额', 'IFIE-RA-计提利息的金额', '{"conf":3,"codeType":"2"}', 3053, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (383, 77, 'M3_OL_05', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', '{"conf":3,"codeType":"2"}', 3054, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (384, 77, 'M3_OL_06', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 3055, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (907, 328, 'M3_OJ_06', 'IBNR期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (352, 281, 'qtzhsy', '其他综合收益', '其他综合收益', '其他综合收益', '{"conf":2,"codeType":"1"}', 10, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (669, 668, 'M3_OC_02', '当期摊销的跟单获取费用现金流', '当期摊销的跟单获取费用现金流', '当期摊销的跟单获取费用现金流', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (670, 668, 'M3_OD_02', '当期摊销的非跟单获取费用现金流', '当期摊销的非跟单获取费用现金流', '当期摊销的非跟单获取费用现金流', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (672, 671, 'M3_OF_02', '亏损部分当期的变动金额', '亏损部分当期的变动金额', '亏损部分当期的变动金额', '{"sign":1,"lrcExclLc":4,"lrcLc":3,"licExclLc":4,"licLc":4,"codeType":"2"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (674, 673, 'M3_OI_03', '亏损部分当期的变动金额', '亏损部分当期的变动金额', '亏损部分当期的变动金额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (675, 673, 'M3_OJ_03', '亏损部分当期的变动金额', '亏损部分当期的变动金额', '亏损部分当期的变动金额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (676, 673, 'M3_OK_03', '亏损部分当期的变动金额', '亏损部分当期的变动金额', '亏损部分当期的变动金额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (677, 673, 'M3_OL_03', '亏损部分当期的变动金额', '亏损部分当期的变动金额', '亏损部分当期的变动金额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (679, 528, 'M3_OA_01', '保费期初余额', '保费期初余额', '保费期初余额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (680, 528, 'M3_OB_01', '手续费期初余额', '手续费期初余额', '手续费期初余额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (681, 528, 'M3_OC_01', '跟单IACF期初余额', '手续费期初余额', '手续费期初余额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (682, 528, 'M3_OD_01', '非跟单IACF期初余额', '手续费期初余额', '手续费期初余额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (683, 528, 'M3_OE_01', '减值期初余额', '减值期初余额', '减值期初余额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (684, 528, 'M3_OF_01', '亏损部分期初余额', '亏损部分期初余额', '亏损部分期初余额', '{"sign":1,"lrcExclLc":4,"lrcLc":3,"licExclLc":4,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (685, 528, 'M3_OI_01', '亏损部分期初余额', '亏损部分期初余额', '亏损部分期初余额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (686, 528, 'M3_OJ_01', '亏损部分期初余额', '亏损部分期初余额', '亏损部分期初余额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (687, 528, 'M3_OK_01', '亏损部分期初余额', '亏损部分期初余额', '亏损部分期初余额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (688, 528, 'M3_OL_01', 'RA期初余额', 'RA期初余额', 'RA期初余额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (522, 521, 'qcncfz', '期初/年初的保险合同负债', '期初/年初的保险合同负债', '期初/年初的保险合同负债', '{"sign":1,"lrcExclLc":5,"lrcLc":5,"licExclLc":5,"licLc":5,"codeType":"1","sumList":["qcncfzzc"],"if":"<"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (671, 661, 'ksbfdqrjzh', '亏损部分的确认及转回', '亏损部分的确认及转回', '亏损部分的确认及转回', '{"sign":1,"lrcExclLc":4,"lrcLc":2,"licExclLc":4,"licLc":4,"codeType":"1"}', 43, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (521, 0, 'QtcDisclosure103PAADataDetailConf', '直保分入披露表M1', '直保分入披露表M1', '直保分入披露表M1', '', NULL, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (821, 0, 'QtcRiComprehensiveIncomeTableConf', '分出报表', '分出报表', '分出报表', '', NULL, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (460, 78, 'M4_OA_01', '期初余额', '期初余额', '期初余额', '{"conf":3,"codeType":"2"}', 4001, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (461, 78, 'M4_OA_02', '当期确认的保险收入 （未扣除投资成分）', '当期确认的保险收入 （未扣除投资成分）', '当期确认的保险收入 （未扣除投资成分）', '{"conf":3,"codeType":"2"}', 4002, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (462, 78, 'M4_OA_03', '分出保费现金流计息', '分出保费现金流计息', '分出保费现金流计息', '{"conf":3,"codeType":"2"}', 4003, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (463, 78, 'M4_OA_04', '实际分出保费现金流', '实际分出保费现金流', '实际分出保费现金流', '{"conf":3,"codeType":"2"}', 4004, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (464, 78, 'M4_OA_05', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 4005, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (465, 79, 'M4_OB_01', '期初余额', '期初余额', '期初余额', '{"conf":3,"codeType":"2"}', 4014, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (466, 79, 'M4_OB_02', '当期摊销的摊回净额结算手续费', '当期摊销的摊回净额结算手续费', '当期摊销的摊回净额结算手续费', '{"conf":3,"codeType":"2"}', 4015, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (467, 79, 'M4_OB_03', '摊回净额结算手续费计息', '摊回净额结算手续费计息', '摊回净额结算手续费计息', '{"conf":3,"codeType":"2"}', 4016, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (468, 79, 'M4_OB_04', '实际摊回净额结算手续费现金流', '实际摊回净额结算手续费现金流', '实际摊回净额结算手续费现金流', '{"conf":3,"codeType":"2"}', 4017, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (469, 79, 'M4_OB_05', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 4018, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (470, 80, 'M4_OC_01', '期初余额', '期初余额', '期初余额', '{"conf":3,"codeType":"2"}', 4049, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (471, 80, 'M4_OC_02', '亏损摊回当期的变动金额', '亏损摊回当期的变动金额', '亏损摊回当期的变动金额', '{"conf":3,"codeType":"2"}', 4050, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (472, 80, 'M4_OC_03', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 4051, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (473, 81, 'M4_OD_01', '当期确认的投资成分', '当期确认的投资成分', '当期确认的投资成分', '{"conf":3,"codeType":"2"}', 4052, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (474, 82, 'M4_OI_01', '期初余额', '期初余额', '期初余额', '{"conf":3,"codeType":"2"}', 4068, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (475, 82, 'M4_OI_02', 'ISE-CASE-已发生赔款负债提转差 (与当前服务相关)', 'ISE-CASE-已发生赔款负债提转差 (与当前服务相关)', 'ISE-CASE-已发生赔款负债提转差 (与当前服务相关)', '{"conf":3,"codeType":"2"}', 4069, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (476, 82, 'M4_OI_03', 'ISE-CASE-已发生赔款负债提转差 (与过去服务相关)', 'ISE-CASE-已发生赔款负债提转差 (与过去服务相关)', 'ISE-CASE-已发生赔款负债提转差 (与过去服务相关)', '{"conf":3,"codeType":"2"}', 4070, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (477, 82, 'M4_OI_04', 'IFIE-CASE-计提利息的金额', 'IFIE-CASE-计提利息的金额', 'IFIE-CASE-计提利息的金额', '{"conf":3,"codeType":"2"}', 4101, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (478, 82, 'M4_OI_05', 'OCI-CASE-利率变动的金额', 'OCI-CASE-利率变动的金额', 'OCI-CASE-利率变动的金额', '{"conf":3,"codeType":"2"}', 4102, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (479, 82, 'M4_OI_06', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 4103, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (480, 83, 'M4_OJ_01', '期初余额', '期初余额', '期初余额', '{"conf":3,"codeType":"2"}', 4104, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (481, 83, 'M4_OJ_02', 'ISE-IBNR-已发生赔款负债提转差 (与当前服务相关)', 'ISE-IBNR-已发生赔款负债提转差 (与当前服务相关)', 'ISE-IBNR-已发生赔款负债提转差 (与当前服务相关)', '{"conf":3,"codeType":"2"}', 4105, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (482, 83, 'M4_OJ_03', 'ISE-IBNR-已发生赔款负债提转差 (与过去服务相关)', 'ISE-IBNR-已发生赔款负债提转差 (与过去服务相关)', 'ISE-IBNR-已发生赔款负债提转差 (与过去服务相关)', '{"conf":3,"codeType":"2"}', 4106, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (483, 83, 'M4_OJ_04', 'IFIE-IBNR-计提利息的金额', 'IFIE-IBNR-计提利息的金额', 'IFIE-IBNR-计提利息的金额', '{"conf":3,"codeType":"2"}', 4107, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (484, 83, 'M4_OJ_05', 'OCI-IBNR-利率变动的金额', 'OCI-IBNR-利率变动的金额', 'OCI-IBNR-利率变动的金额', '{"conf":3,"codeType":"2"}', 4108, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (485, 83, 'M4_OJ_06', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 4109, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (486, 84, 'M4_OL_01', '期初余额', '期初余额', '期初余额', '{"conf":3,"codeType":"2"}', 4116, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (487, 84, 'M4_OL_02', 'ISE-RA-已发生赔款负债提转差 (与当前服务相关)', 'ISE-RA-已发生赔款负债提转差 (与当前服务相关)', 'ISE-RA-已发生赔款负债提转差 (与当前服务相关)', '{"conf":3,"codeType":"2"}', 4117, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (488, 84, 'M4_OL_03', 'ISE-RA-已发生赔款负债提转差 (与过去服务相关)', 'ISE-RA-已发生赔款负债提转差 (与过去服务相关)', 'ISE-RA-已发生赔款负债提转差 (与过去服务相关)', '{"conf":3,"codeType":"2"}', 4118, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (489, 84, 'M4_OL_04', 'IFIE-RA-计提利息的金额', 'IFIE-RA-计提利息的金额', 'IFIE-RA-计提利息的金额', '{"conf":3,"codeType":"2"}', 4119, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (490, 84, 'M4_OL_05', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', '{"conf":3,"codeType":"2"}', 4120, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (491, 84, 'M4_OL_06', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 4121, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (823, 822, 'M4_OA_04', '实际保费现金流', '实际保费现金流', '实际保费现金流', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (824, 822, 'M4_OB_04', '实际净额结算手续费现金流', '实际净额结算手续费现金流', '实际净额结算手续费现金流', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (825, 821, 'fczbxhtzc', '分出再保险合同资产', '分出再保险合同资产', '分出再保险合同资产', '{"conf":2,"codeType":"1"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (880, 821, 'qtzhsy', '其他综合收益', '其他综合收益', '其他综合收益', '{"conf":2,"codeType":"1"}', 10, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (881, 880, 'M4_OI_05', 'OCI-CASE-利率变动的金额', 'OCI-CASE-利率变动的金额', 'OCI-CASE-利率变动的金额', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (882, 880, 'M4_OJ_05', 'OCI-IBNR-利率变动的金额', 'OCI-IBNR-利率变动的金额', 'OCI-IBNR-利率变动的金额', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (883, 880, 'M4_OL_05', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', '{"conf":3,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (908, 328, 'M3_OL_06', 'RA期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (909, 328, 'M3_OM_03', 'RA期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (910, 327, 'M3_OA_05', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (911, 327, 'M3_OB_05', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (912, 327, 'M3_OC_05', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (913, 327, 'M3_OC_05', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (914, 327, 'M3_OE_05', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (915, 327, 'M3_OF_03', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 6, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (617, 281, 'bxfwyj', '保险服务业绩', '保险服务业绩', '保险服务业绩', '{"conf":5,"codeType":"1","sumList":["bxhtsr","bxfwfy"]}', 7, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (601, 281, 'jlyks', '净利润/(亏损)', '净利润/(亏损)', '净利润/(亏损)', '{"conf":5,"codeType":"1","sumList":["bxfwyj","cbcwss"]}', 9, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (916, 352, 'M3_OI_05', 'OCI-CASE-利率变动的金额', 'OCI-CASE-利率变动的金额', 'OCI-CASE-利率变动的金额', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (917, 352, 'M3_OJ_05', 'OCI-IBNR-利率变动的金额', 'OCI-IBNR-利率变动的金额', 'OCI-IBNR-利率变动的金额', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (918, 352, 'M3_OK_05', 'OCI-ULAE-利率变动的金额', 'OCI-ULAE-利率变动的金额', 'OCI-ULAE-利率变动的金额', '{"conf":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (919, 352, 'M3_OL_05', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', '{"conf":3,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (920, 281, 'cbcwss', '承保财务损失', '承保财务损失', '承保财务损失', '{"conf":2,"codeType":"1"}', 8, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (921, 920, 'wdqzrfztz', '未到期责任负债融资成分调整', '未到期责任负债融资成分调整', '未到期责任负债融资成分调整', '{"conf":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (922, 921, 'M3_OA_03', '保费现金流计息', '保费现金流计息', '保费现金流计息', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (563, 301, 'M3_OC_04', '实际跟单获取费用现金流', '实际跟单获取费用现金流', '实际跟单获取费用现金流', '{"conf":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (565, 301, 'M3_OD_04', '实际非跟单获取费用现金流', '实际非跟单获取费用现金流', '实际非跟单获取费用现金流', '{"conf":3,"codeType":"2"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (545, 521, 'xgzhsybdhj', '相关综合收益变动合计', '相关综合收益变动合计', '相关综合收益变动合计', '{"sign":1,"lrcExclLc":5,"lrcLc":5,"licExclLc":5,"licLc":5,"codeType":"1","sumList":["bxfwyj","bxhtjrbde","qtsxbd","qtzhsyqtbd"]}', 10, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (562, 301, 'M3_OB_04', '实际净额结算手续费现金流', '实际净额结算手续费现金流', '实际净额结算手续费现金流', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (822, 821, 'hbzj', '货币资金', '货币资金', '货币资金', '{"conf":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (546, 521, 'tzcf', '投资成分', '投资成分', '投资成分', '{"sign":1,"lrcExclLc":2,"lrcLc":4,"licExclLc":2,"licLc":4,"codeType":"1"}', 11, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (721, 0, 'QtcRiDisclosure103PAADataDetailConf', '分出披露表M1', '分出披露表M1', '分出披露表M1', '', 123, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (561, 301, 'M3_OA_04', '实际保费现金流', '实际保费现金流', '实际保费现金流', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (564, 301, 'M3_OD_04', '实际非跟单获取费用现金流', '实际非跟单获取费用现金流', '实际非跟单获取费用现金流', '{"conf":3,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (566, 301, 'M3_OE_04', '当期计提减值', '当期计提减值', '当期计提减值', '{"conf":3,"codeType":"2"}', 6, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (567, 324, 'M3_OA_05', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (568, 324, 'M3_OB_05', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (569, 324, 'M3_OC_05', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (570, 324, 'M3_OC_05', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (571, 324, 'M3_OE_05', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (572, 324, 'M3_OF_03', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 6, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (577, 330, 'M3_OA_02', '当期确认的保险收入 （未扣除投资成分）', '当期确认的保险收入 （未扣除投资成分）', '当期确认的保险收入 （未扣除投资成分）', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (578, 330, 'M3_OB_02', '当期摊销的净额结算手续费', '当期摊销的净额结算手续费', '当期摊销的净额结算手续费', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (579, 330, 'M3_OE_02', '当期摊销的减值', '当期摊销的净额结算手续费', '当期摊销的净额结算手续费', '{"conf":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (580, 330, 'M3_OE_02', '当期摊销的减值', '当期摊销的净额结算手续费', '当期摊销的净额结算手续费', '{"conf":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (581, 330, 'M3_OG_01', '当期确认的投资成分', '当期摊销的净额结算手续费', '当期摊销的净额结算手续费', '{"conf":3,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (582, 341, 'M3_OC_02', '当期摊销的跟单获取费用现金流', '当期摊销的跟单获取费用现金流', '当期摊销的跟单获取费用现金流', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (583, 342, 'M3_OJ_02', '当期摊销的跟单获取费用现金流', '当期摊销的跟单获取费用现金流', '当期摊销的跟单获取费用现金流', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (584, 342, 'M3_OK_02', '当期摊销的跟单获取费用现金流', '当期摊销的跟单获取费用现金流', '当期摊销的跟单获取费用现金流', '{"conf":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (585, 342, 'M3_OL_02', 'ISE-RA-已发生赔款负债提转差 (与当前服务相关)', '当期摊销的跟单获取费用现金流', '当期摊销的跟单获取费用现金流', '{"conf":3,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (586, 343, 'M3_OI_03', 'ISE-CASE-已发生赔款负债提转差 (与过去服务相关)', '当期摊销的跟单获取费用现金流', '当期摊销的跟单获取费用现金流', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (587, 343, 'M3_OJ_03', 'ISE-IBNR-已发生赔款负债提转差 (与过去服务相关)', '当期摊销的跟单获取费用现金流', '当期摊销的跟单获取费用现金流', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (588, 343, 'M3_OK_03', 'ISE-IBNR-已发生赔款负债提转差 (与过去服务相关)', '当期摊销的跟单获取费用现金流', '当期摊销的跟单获取费用现金流', '{"conf":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (589, 343, 'M3_OL_03', 'ISE-RA-已发生赔款负债提转差 (与过去服务相关)', '当期摊销的跟单获取费用现金流', '当期摊销的跟单获取费用现金流', '{"conf":3,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (590, 344, 'M3_OF_02', '亏损部分当期的变动金额', '亏损部分当期的变动金额', '亏损部分当期的变动金额', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (591, 345, 'M3_OG_01', '当期确认的投资成分', '当期确认的投资成分', '当期确认的投资成分', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (901, 325, 'M3_OI_06', 'Case期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (902, 325, 'M3_OJ_06', 'IBNR期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (903, 325, 'M3_OL_06', 'RA期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (904, 325, 'M3_OM_03', 'RA期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (906, 328, 'M3_OI_06', 'Case期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (722, 721, 'qcncfchtzc', '期初/年初的分出再保险合同资产', '期初/年初的分出再保险合同资产', '期初/年初的分出再保险合同资产', '{"sign":1,"lrcExclLc":5,"lrcLc":5,"licExclLc":5,"licLc":5,"codeType":"1","sumList":["qcncfchtzcfz"],"if":"<"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (723, 721, 'qcncfchtfz', '期初/年初的分出再保险合同负债', '期初/年初的分出再保险合同负债', '期初/年初的分出再保险合同负债', '{"sign":1,"lrcExclLc":5,"lrcLc":5,"licExclLc":5,"licLc":5,"codeType":"1","sumList":["qcncfchtzcfz"],"if":">"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (724, 721, 'qcncfchtzcfz', '期初/年初的分出再保险合同净资产/负债', '期初/年初的分出再保险合同净资产/负债', '期初/年初的分出再保险合同净资产/负债', '{"sign":1,"lrcExclLc":2,"lrcLc":2,"licExclLc":2,"licLc":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (725, 724, 'M4_OA_01', '减值期初余额', '减值期初余额', '减值期初余额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (726, 724, 'M4_OB_01', '减值期初余额', '减值期初余额', '减值期初余额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (727, 724, 'M4_OC_01', '减值期初余额', '减值期初余额', '减值期初余额', '{"sign":1,"lrcExclLc":4,"lrcLc":3,"licExclLc":4,"licLc":4,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (728, 724, 'M4_OI_01', '减值期初余额', '减值期初余额', '减值期初余额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (729, 724, 'M4_OJ_01', '减值期初余额', '减值期初余额', '减值期初余额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (730, 724, 'M4_OM_01', '减值期初余额', '减值期初余额', '减值期初余额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (731, 724, 'M4_OK_01', '减值期初余额', '减值期初余额', '减值期初余额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (733, 732, 'M4_OA_02', '减值期初余额', '减值期初余额', '减值期初余额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (734, 732, 'M4_OB_02', '减值期初余额', '减值期初余额', '减值期初余额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (735, 732, 'M4_OC_02', '减值期初余额', '减值期初余额', '减值期初余额', '{"sign":-1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (738, 737, 'M4_OI_02', '减值期初余额', '减值期初余额', '减值期初余额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (739, 737, 'M4_OJ_02', '减值期初余额', '减值期初余额', '减值期初余额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (740, 737, 'M4_OD_01', '减值期初余额', '减值期初余额', '减值期初余额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (741, 737, 'M4_OL_02', 'ISE-RA-已发生赔款负债提转差 (与当前服务相关)', 'ISE-RA-已发生赔款负债提转差 (与当前服务相关)', 'ISE-RA-已发生赔款负债提转差 (与当前服务相关)', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (743, 742, 'M4_OC_02', ' 亏损摊回当期的变动金额', '亏损摊回当期的变动金额', '亏损摊回当期的变动金额 (与当前服务相关)', '{"sign":1,"lrcExclLc":4,"lrcLc":3,"licExclLc":4,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (745, 742, 'M4_OI_03', ' ISE-CASE-已发生赔款负债提转差 (与过去服务相关)', 'ISE-CASE-已发生赔款负债提转差 (与过去服务相关)', '亏损摊回当期的变动金额 (与当前服务相关)', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (746, 742, 'M4_OJ_03', ' ISE-IBNR-已发生赔款负债提转差 (与过去服务相关)', 'ISE-IBNR-已发生赔款负债提转差 (与过去服务相关)', 'ISE-IBNR-已发生赔款负债提转差 (与过去服务相关)', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (826, 825, 'wdqzrzc', '其中：未到期责任资产', '其中：未到期责任资产', '其中：未到期责任资产', '{"conf":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (646, 541, 'M3_OL_04', '保费现金流计息', '保费现金流计息', '保费现金流计息', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (647, 542, 'M3_OI_05', '保费现金流计息', '保费现金流计息', '保费现金流计息', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (648, 542, 'M3_OJ_05', '保费现金流计息', '保费现金流计息', '保费现金流计息', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (649, 542, 'M3_OK_05', '保费现金流计息', '保费现金流计息', '保费现金流计息', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (732, 721, 'fcbfft', '分出保费的分摊', '分出保费的分摊', '分出保费的分摊', '{"sign":1,"lrcExclLc":2,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"1"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (736, 721, 'thbxfwfy', '摊回保险服务费用', '摊回保险服务费用', '摊回保险服务费用', '{"sign":1,"lrcExclLc":2,"lrcLc":2,"licExclLc":2,"licLc":2,"codeType":"1"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (737, 736, 'thdqfspkqtfy', '摊回当期发生赔款及其他相关费用', '摊回当期发生赔款及其他相关费用', '摊回当期发生赔款及其他相关费用', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":2,"licLc":2,"codeType":"1"}', 51, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (742, 736, 'ksbfdqrjzh', '亏损摊回部分的确认及转回', '亏损摊回部分的确认及转回', '亏损摊回部分的确认及转回', '{"sign":1,"lrcExclLc":4,"lrcLc":2,"licExclLc":4,"licLc":4,"codeType":"1"}', 52, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (744, 736, 'fbthyfspkzclyxjlbd', '分保摊回已发生赔款资产相关履约现金流量变动', '分保摊回已发生赔款资产相关履约现金流量变动', '分保摊回已发生赔款资产相关履约现金流量变动', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":2,"licLc":2,"codeType":"1"}', 53, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (650, 542, 'M3_OL_05', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (651, 546, 'M3_OG_01', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (652, 546, 'M3_OG_01', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (749, 742, 'M4_OM_02', ' ISE-再保人不履约风险调整变动', 'ISE-再保人不履约风险调整变动', 'ISE-再保人不履约风险调整变动', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (747, 742, 'M4_OL_03', ' ISE-RA-已发生赔款负债提转差 (与过去服务相关)', 'ISE-RA-已发生赔款负债提转差 (与过去服务相关)', 'ISE-RA-已发生赔款负债提转差 (与过去服务相关)', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (748, 736, 'zbxfrrblyfxbde', '再保险分入人不履约风险变动额', '再保险分入人不履约风险变动额', '再保险分入人不履约风险变动额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":2,"licLc":4,"codeType":"1"}', 54, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (754, 753, 'M4_OA_03', '分出保费现金流计息', '分出保费现金流计息', '分出保费现金流计息', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (755, 753, 'M4_OB_03', '分出保费现金流计息', '分出保费现金流计息', '分出保费现金流计息', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (756, 753, 'M4_OI_04', '分出保费现金流计息', '分出保费现金流计息', '分出保费现金流计息', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (757, 753, 'M4_OJ_04', '分出保费现金流计息', '分出保费现金流计息', '分出保费现金流计息', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (758, 753, 'M4_OL_04', '分出保费现金流计息', '分出保费现金流计息', '分出保费现金流计息', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":3,"codeType":"2"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (760, 759, 'M4_OI_05', '分出保费现金流计息', '分出保费现金流计息', '分出保费现金流计息', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (761, 759, 'M4_OJ_05', '分出保费现金流计息', '分出保费现金流计息', '分出保费现金流计息', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (762, 759, 'M4_OL_05', '分出保费现金流计息', '分出保费现金流计息', '分出保费现金流计息', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":3,"codeType":"2"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (540, 521, 'bxhtjrbde', '保险合同金融变动额', '保险合同金融变动额', '保险合同金融变动额', '{"sign":1,"lrcExclLc":2,"lrcLc":2,"licExclLc":2,"licLc":2,"codeType":"1"}', 7, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (528, 521, 'qcncfzzc', '期初/年初的保险合同净负债/资产', '期初/年初的保险合同净负债/资产', '期初/年初的保险合同净负债/资产', '{"sign":1,"lrcExclLc":2,"lrcLc":2,"licExclLc":2,"licLc":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (750, 736, 'qtthfy', '其他摊回费用', '其他摊回费用', '其他摊回费用', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"1"}', 55, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (751, 721, 'fczbxhtbxsy', '分出再保险合同的保险损益', '分出再保险合同的保险损益', '分出再保险合同的保险损益', '{"sign":1,"lrcExclLc":5,"lrcLc":5,"licExclLc":5,"licLc":5,"codeType":"1","sumList":["fcbfft","thbxfwfy"]}', 6, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (752, 721, 'fczbxhtjrbde', '分出再保险合同的保险合同金融变动额', '分出再保险合同的保险合同金融变动额', '分出再保险合同的保险合同金融变动额', '{"sign":1,"lrcExclLc":2,"lrcLc":2,"licExclLc":2,"licLc":2,"codeType":"1"}', 7, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (753, 752, 'jrsydfczbxhtjrbde', '其中：计入损益的分出再保险合同的保险合同金融变动额', '其中：计入损益的分出再保险合同的保险合同金融变动额', '其中：计入损益的分出再保险合同的保险合同金融变动额', '{"sign":1,"lrcExclLc":2,"lrcLc":4,"licExclLc":2,"licLc":2,"codeType":"1"}', 71, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (759, 752, 'jrqtzhsyfczbxjrbde', '计入其他综合收益的分出再保险合同的保险合同金融变动额', '计入其他综合收益的分出再保险合同的保险合同金融变动额', '计入其他综合收益的分出再保险合同的保险合同金融变动额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":2,"licLc":2,"codeType":"1"}', 72, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (765, 721, 'qtsxbd', '其他损益变动', '其他损益变动', '其他损益变动', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"1"}', 8, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (766, 721, 'qtzhsyqtbd', '其他综合收益其他变动', '其他综合收益其他变动', '其他综合收益其他变动', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"1"}', 9, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (768, 721, 'tzcf', '投资成分', '投资成分', '投资成分', '{"sign":1,"lrcExclLc":2,"lrcLc":4,"licExclLc":2,"licLc":4,"codeType":"1"}', 11, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (638, 541, 'M3_OA_03', '保费现金流计息', '保费现金流计息', '保费现金流计息', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (639, 541, 'M3_OB_03', '保费现金流计息', '保费现金流计息', '保费现金流计息', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (640, 541, 'M3_OC_03', '保费现金流计息', '保费现金流计息', '保费现金流计息', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (641, 541, 'M3_OD_03', '保费现金流计息', '保费现金流计息', '保费现金流计息', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (642, 541, 'M3_OE_03', '保费现金流计息', '保费现金流计息', '保费现金流计息', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (643, 541, 'M3_OI_04', '保费现金流计息', '保费现金流计息', '保费现金流计息', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (644, 541, 'M3_OJ_04', '保费现金流计息', '保费现金流计息', '保费现金流计息', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (645, 541, 'M3_OK_04', '保费现金流计息', '保费现金流计息', '保费现金流计息', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (827, 826, 'M4_OA_05', '保费期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (828, 826, 'M4_OB_05', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (829, 826, 'M4_OC_03', '期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (830, 825, 'yfspkzc', ' 已发生赔款资产', ' 已发生赔款资产', ' 已发生赔款资产', '{"conf":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (831, 830, 'M4_OI_06', 'Case期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (832, 830, 'M4_OJ_06', 'IBNR期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (833, 830, 'M4_OL_06', 'RA期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (527, 521, 'qcnczc', '期初/年初的保险合同资产', '期初/年初的保险合同资产', '期初/年初的保险合同资产', '{"sign":1,"lrcExclLc":5,"lrcLc":5,"licExclLc":5,"licLc":5,"codeType":"1","sumList":["qcncfzzc"],"if":">"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (834, 830, 'M4_OM_03', 'RA期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (835, 821, 'fczbxhtfz', '分出再保险合同负债', '分出再保险合同负债', '分出再保险合同负债', '{"conf":2,"codeType":"1"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (836, 835, 'wdqzrfz', '未到期责任负债', '未到期责任负债', '未到期责任负债', '{"conf":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (838, 836, 'M4_OA_05', 'RA期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (839, 836, 'M4_OB_05', 'RA期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (840, 836, 'M4_OC_03', 'RA期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (843, 835, 'yfspkfz', ' 已发生赔款负债', ' 已发生赔款负债', ' 已发生赔款负债', '{"conf":2,"codeType":"1"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (844, 843, 'M4_OI_06', 'Case期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (845, 843, 'M4_OJ_06', 'IBNR期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (846, 843, 'M4_OL_06', 'RA期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (847, 843, 'M4_OM_03', 'RA期末余额', '期末余额', '期末余额', '{"conf":3,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (850, 821, 'fcbfft', '分出保费的分摊', '分出保费的分摊', '分出保费的分摊', '{"conf":2,"codeType":"1"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (851, 850, 'M4_OA_02', '当期确认的保险收入 （未扣除投资成分）', '当期确认的保险收入 （未扣除投资成分）', '当期确认的保险收入 （未扣除投资成分）', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (852, 850, 'M4_OB_02', '当期摊销的净额结算手续费', '当期摊销的净额结算手续费', '当期摊销的净额结算手续费', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (853, 850, 'M4_OD_01', '当期确认的投资成分', '当期摊销的净额结算手续费', '当期摊销的净额结算手续费', '{"sign":-1,"conf":3,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (554, 521, 'qmnmbxhtjfz', '期末/年末的保险合同负债', '期末/年末的保险合同负债', '期末/年末的保险合同负债', '{"sign":1,"lrcExclLc":5,"lrcLc":5,"licExclLc":5,"licLc":5,"codeType":"1","sumList":["qmnmbxhfzzc"],"if":"<"}', 16, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (848, 821, 'syzqyhj', '所有者权益合计', '所有者权益合计', '所有者权益合计', '{"conf":5,"codeType":"1", "sumList":["hbzj","fczbxhtzc","fczbxhtfz"]}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (553, 521, 'qmnmbxhtzc', '期末/年末的保险合同资产', '期末/年末的保险合同资产', '期末/年末的保险合同资产', '{"sign":1,"lrcExclLc":5,"lrcLc":5,"licExclLc":5,"licLc":5,"codeType":"1","sumList":["qmnmbxhfzzc"],"if":">"}', 15, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (849, 821, 'check1', 'check', 'check', 'check', '{"conf":5,"codeType":"1","sumList":["syzqyhj","-jlyks","-qtzhsy"]}', 50, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (769, 768, 'M4_OD_01', '当期确认的投资成分', '当期确认的投资成分', '当期确认的投资成分', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (770, 768, 'M4_OD_01', '当期确认的投资成分', '当期确认的投资成分', '当期确认的投资成分', '{"sign":-1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (772, 771, 'M4_OA_04', '当期确认的投资成分', '当期确认的投资成分', '当期确认的投资成分', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (773, 771, 'M4_OB_04', '当期确认的投资成分', '当期确认的投资成分', '当期确认的投资成分', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (782, 781, 'M4_OA_05', '期末余额', '期末余额', '期末余额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (783, 781, 'M4_OB_05', '期末余额', '期末余额', '期末余额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (784, 781, 'M4_OC_05', '期末余额', '期末余额', '期末余额', '{"sign":1,"lrcExclLc":4,"lrcLc":3,"licExclLc":4,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (785, 781, 'M4_OI_06', '期末余额', '期末余额', '期末余额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (786, 781, 'M4_OJ_06', '期末余额', '期末余额', '期末余额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 6, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (787, 781, 'M4_OM_03', '期末余额', '期末余额', '期末余额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 7, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (788, 781, 'M4_OL_06', '期末余额', '期末余额', '期末余额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":3,"codeType":"2"}', 8, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (780, 721, 'qmnmfczbxhtjfz', '期末/年末的分出再保险合同负债', '期末/年末的分出再保险合同负债', '期末/年末的分出再保险合同负债', '{"sign":1,"lrcExclLc":5,"lrcLc":5,"licExclLc":5,"licLc":5,"codeType":"1","sumList":["qmnmfczbxhfzzc"],"if":"<"}', 16, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (779, 721, 'qmnmfczbxhtzc', '期末/年末的分出再保险合同资产', '期末/年末的分出再保险合同资产', '期末/年末的分出再保险合同资产', '{"sign":1,"lrcExclLc":5,"lrcLc":5,"licExclLc":5,"licLc":5,"codeType":"1","sumList":["qmnmfczbxhfzzc"],"if":">"}', 15, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (776, 721, 'xjllhj', '现金流量合计', '现金流量合计', '现金流量合计', '{"sign":1,"lrcExclLc":2,"lrcLc":2,"licExclLc":2,"licLc":2,"codeType":"1"}', 12, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (777, 721, 'qtbd', '其他变动', '其他变动', '其他变动', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"1"}', 13, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (775, 776, 'qtxjll', '其他现金流量', '其他现金流量', '其他现金流量', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"1"}', 113, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (774, 776, 'zfdthpkjqtxgfyl', '收到的摊回赔款及其他相关费用（含投资成分）', '收到的摊回赔款及其他相关费用（含投资成分）', '收到的摊回赔款及其他相关费用（含投资成分）', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"1"}', 112, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (771, 776, 'zcfcbf', '支付的分出保费', '支付的分出保费', '支付的分出保费', '{"sign":1,"lrcExclLc":2,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"1"}', 111, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (781, 721, 'qmnmfczbxhtjfzzc', '期末/年末分出再保险合同净资产/负债', '期末/年末分出再保险合同净资产/负债', '期末/年末分出再保险合同净资产/负债', '{"sign":1,"lrcExclLc":2,"lrcLc":2,"licExclLc":2,"licLc":2,"codeType":"1"}', 17, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (778, 721, 'qmnmfczbxhfzzc', '期末/年末的分出再保险合同净资产/负债', '期末/年末的分出再保险合同净资产/负债', '期末/年末的分出再保险合同净资产/负债', '{"sign":1,"lrcExclLc":5,"lrcLc":5,"licExclLc":5,"licLc":5,"codeType":"1","sumList":["qcncfchtzcfz","xgzhsybdhj","tzcf","xjllhj"]}', 14, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (923, 921, 'M3_OB_03', '净额结算手续费计息', '净额结算手续费计息', '净额结算手续费计息', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (924, 921, 'M3_OC_03', '跟单获取费用现金流计息', '跟单获取费用现金流计息', '跟单获取费用现金流计息', '{"conf":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (925, 921, 'M3_OD_03', '非跟单获取费用现金流计息', '非跟单获取费用现金流计息', '非跟单获取费用现金流计息', '{"conf":3,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (926, 921, 'M3_OE_03', '减值现金流计息', '减值现金流计息', '减值现金流计息', '{"conf":3,"codeType":"2"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (927, 920, 'yfspkfzrztz', '已发生赔款负债融资成分调整', '已发生赔款负债融资成分调整', '已发生赔款负债融资成分调整', '{"conf":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (928, 927, 'M3_OI_04', 'IFIE-CASE-计提利息的金额', 'IFIE-CASE-计提利息的金额', 'IFIE-CASE-计提利息的金额', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (929, 927, 'M3_OJ_04', 'IFIE-IBNR-计提利息的金额', 'IFIE-IBNR-计提利息的金额', 'IFIE-IBNR-计提利息的金额', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (930, 927, 'M3_OK_04', 'IFIE-ULAE-计提利息的金额', 'IFIE-ULAE-计提利息的金额', 'IFIE-ULAE-计提利息的金额', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (931, 927, 'M3_OL_04', 'IFIE-RA-计提利息的金额', 'IFIE-RA-计提利息的金额', 'IFIE-RA-计提利息的金额', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (932, 920, 'hlbd', '汇率变动', '汇率变动', '汇率变动', '{"conf":2,"codeType":"1"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (933, 281, 'syzqyhj', '所有者权益合计', '所有者权益合计', '所有者权益合计', '{"conf":5,"codeType":"1","sumList":["hbzj","bxhtzc","bxhtfz"]}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (937, 281, 'check1', 'check', 'check', 'check', '{"conf":5,"codeType":"1","sumList":["syzqyhj","-jlyks","-qtzhsy"]}', 50, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (944, 555, 'M3_OA_05', '期末余额', '期末余额', '期末余额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (945, 555, 'M3_OB_05', '期末余额', '期末余额', '期末余额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (946, 555, 'M3_OC_05', '期末余额', '期末余额', '期末余额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (947, 555, 'M3_OD_05', '期末余额', '期末余额', '期末余额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (948, 555, 'M3_OE_05', '期末余额', '期末余额', '期末余额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (949, 555, 'M3_OF_03', '期末余额', '期末余额', '期末余额', '{"sign":1,"lrcExclLc":4,"lrcLc":3,"licExclLc":4,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (950, 555, 'M3_OI_06', '期末余额', '期末余额', '期末余额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (951, 555, 'M3_OJ_06', '期末余额', '期末余额', '期末余额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (952, 555, 'M3_OK_06', '期末余额', '期末余额', '期末余额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":3,"licLc":4,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (953, 555, 'M3_OL_06', '期末余额', '期末余额', '期末余额', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (555, 521, 'qmnmbxhtjfzzc', '期末/年末保险合同净负债/资产', '期末/年末保险合同净负债/资产', '期末/年末保险合同净负债/资产', '{"sign":1,"lrcExclLc":2,"lrcLc":2,"licExclLc":2,"licLc":2,"codeType":"1"}', 17, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (954, 521, 'qmnmbxhfzzc', '期末/年末的保险合同净负债/资产', '期末/年末的保险合同净负债/资产', '期末/年末的保险合同净负债/资产', '{"sign":1,"lrcExclLc":5,"lrcLc":5,"licExclLc":5,"licLc":5,"codeType":"1","sumList":["xgzhsybdhj","xjllhj","tzcf"]}', 14, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (958, 521, 'bxfwyj', '保险服务业绩', '保险服务业绩', '保险服务业绩', '{"sign":1,"lrcExclLc":5,"lrcLc":5,"licExclLc":5,"licLc":5,"codeType":"1","sumList":["bxhtsr","bxfwzc","tzcf"]}', 6, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (678, 661, 'qtfy', '其他费用', '其他费用', '其他费用', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"1"}', 45, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (541, 540, 'jrsydbxhtjrbde', '其中：计入损益的保险合同金融变动额', '其中：计入损益的保险合同金融变动额', '其中：计入损益的保险合同金融变动额', '{"sign":1,"lrcExclLc":2,"lrcLc":2,"licExclLc":2,"licLc":2,"codeType":"1"}', 71, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (542, 540, 'jrqtzhsybxjrbde', '计入其他综合收益的保险合同金融变动额', '计入其他综合收益的保险合同金融变动额', '计入其他综合收益的保险合同金融变动额', '{"sign":1,"lrcExclLc":2,"lrcLc":2,"licExclLc":2,"licLc":2,"codeType":"1"}', 72, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (662, 661, 'dqfspkjqtxgfy', '当期发生赔款及其他相关费用（保险获取现金流量除外）', '当期发生赔款及其他相关费用（保险获取现金流量除外）', '当期发生赔款及其他相关费用（保险获取现金流量除外）', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":2,"licLc":2,"codeType":"1"}', 41, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (668, 661, 'bxhqxjllldtx', '保险获取现金流量的摊销', '保险获取现金流量的摊销', '保险获取现金流量的摊销', '{"sign":1,"lrcExclLc":2,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"1"}', 42, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (673, 661, 'yfspkfzxglyxjlbd', '已发生赔款负债相关履约现金流量变动', '已发生赔款负债相关履约现金流量变动', '已发生赔款负债相关履约现金流量变动', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":2,"licLc":2,"codeType":"1"}', 44, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (962, 521, 'qtzhsyqtbd', '其他综合收益其他变动', '其他综合收益其他变动', '其他综合收益其他变动', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"1"}', 9, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (963, 521, 'qtsxbd', '其他损益变动', '其他损益变动', '其他损益变动', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"1"}', 8, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (965, 521, 'qtbd', '其他变动', '其他变动', '其他变动', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"1"}', 13, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (966, 521, 'xjllhj', '现金流量合计', '现金流量合计', '现金流量合计', '{"sign":1,"lrcExclLc":2,"lrcLc":2,"licExclLc":2,"licLc":2,"codeType":"1"}', 12, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (968, 967, 'M3_OA_04', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (969, 967, 'M3_OB_04', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (970, 967, 'M3_OE_04', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (972, 971, 'M3_OC_04', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (973, 971, 'M3_OD_04', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', 'OCI-RA-利率变动的金额', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (967, 966, 'sddbf', '收到的保费', '收到的保费', '收到的保费', '{"sign":1,"lrcExclLc":2,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"1"}', 111, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (971, 966, 'zfdbxhqxjll', '支付的保险获取现金流量', '支付的保险获取现金流量', '支付的保险获取现金流量', '{"sign":1,"lrcExclLc":2,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"1"}', 112, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (974, 966, 'zfdpkjqtxgfyl', '支付的赔款及其他相关费用（含投资成分）', '支付的赔款及其他相关费用（含投资成分）', '支付的赔款及其他相关费用（含投资成分）', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"1"}', 113, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (975, 966, 'qtxjll', '其他现金流量', '其他现金流量', '其他现金流量', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"1"}', 114, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (977, 976, 'gdqcyxzzstzfdht', '过渡日采用修正追溯调整法的合同', '过渡日采用修正追溯调整法的合同', '过渡日采用修正追溯调整法的合同', '{"sign":1,"lrcExclLc":2,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (978, 976, 'gdqcygyjzfdht', '过渡日采用公允价值法的合同', '过渡日采用公允价值法的合同', '过渡日采用公允价值法的合同', '{"sign":1,"lrcExclLc":4,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"1"}', 32, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (980, 979, 'M3_OA_02', '当期确认的保险收入 （未扣除投资成分）', '当期确认的保险收入 （未扣除投资成分）', '当期确认的保险收入 （未扣除投资成分）', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (981, 979, 'M3_OB_02', '当期确认的保险收入 （未扣除投资成分）', '当期确认的保险收入 （未扣除投资成分）', '当期确认的保险收入 （未扣除投资成分）', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (982, 979, 'M3_OC_02', '当期确认的保险收入 （未扣除投资成分）', '当期确认的保险收入 （未扣除投资成分）', '当期确认的保险收入 （未扣除投资成分）', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (983, 979, 'M3_OD_02', '当期确认的保险收入 （未扣除投资成分）', '当期确认的保险收入 （未扣除投资成分）', '当期确认的保险收入 （未扣除投资成分）', '{"sign":1,"lrcExclLc":3,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (976, 521, 'bxfwsr', '保险服务收入', '保险服务收入', '保险服务收入', '{"sign":1,"lrcExclLc":2,"lrcLc":2,"licExclLc":2,"licLc":2,"codeType":"1"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (979, 976, 'qtht', '其余合同', '其余合同', '其余合同', '{"sign":1,"lrcExclLc":2,"lrcLc":4,"licExclLc":4,"licLc":4,"codeType":"1"}', 33, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1001, 521, 'dd103Check', 'Check', 'Check', 'Check', '{"sign":1,"lrcExclLc":5,"lrcLc":5,"licExclLc":5,"licLc":5,"codeType":"1", "sumList":["qmnmbxhfzzc","-qmnmbxhtjfzzc"]}', 18, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1021, 821, 'cyzbxjcb', '持有的再保险的净成本', '持有的再保险的净成本', '持有的再保险的净成本', '{"conf":5,"codeType":"1","sumList":["fcbfft","thbxfwfy"]}', 7, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1022, 821, 'thbxfwfy', '摊回保险服务费用', '摊回保险服务费用', '摊回保险服务费用', '{"conf":2,"codeType":"1"}', 6, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1023, 1022, 'thyfspkfy', '摊回已发生赔款及费用', '摊回已发生赔款及费用', '摊回已发生赔款及费用', '{"conf":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1024, 1023, 'M4_OI_02', '当期摊销的跟单获取费用现金流', '当期摊销的跟单获取费用现金流', '当期摊销的跟单获取费用现金流', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1025, 1023, 'M4_OJ_02', '当期摊销的非跟单获取费用现金流', '当期摊销的非跟单获取费用现金流', '当期摊销的非跟单获取费用现金流', '{"conf":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1026, 1022, 'zbrblyfxbdyx', '再保人不履约风险变动的影响', '再保人不履约风险变动的影响', '再保人不履约风险变动的影响', '{"conf":2,"codeType":"1"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1027, 1026, 'M4_OM_02', 'ISE-CASE-已发生赔款负债提转差 (与当前服务相关)', '当期摊销的跟单获取费用现金流', '当期摊销的跟单获取费用现金流', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1028, 1022, 'thdqbxhqxjlbd', '与过去服务相关的变动，即与摊回已发生赔款负债相关的履约现金流变动', '与过去服务相关的变动，即与摊回已发生赔款负债相关的履约现金流变动', '与过去服务相关的变动，即与摊回已发生赔款负债相关的履约现金流变动', '{"conf":2,"codeType":"1"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1029, 1028, 'M4_OI_03', 'ISE-CASE-已发生赔款负债提转差 (与过去服务相关)', '当期摊销的跟单获取费用现金流', '当期摊销的跟单获取费用现金流', '{"conf":3,"codeType":"2"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1030, 1028, 'M4_OJ_03', 'ISE-IBNR-已发生赔款负债提转差 (与过去服务相关)', '当期摊销的跟单获取费用现金流', '当期摊销的跟单获取费用现金流', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1031, 1028, 'M4_OL_03', 'ISE-RA-已发生赔款负债提转差 (与过去服务相关)', '当期摊销的跟单获取费用现金流', '当期摊销的跟单获取费用现金流', '{"conf":3,"codeType":"2"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1032, 1022, 'ksbfdqrjzh', '亏损部分的确认及转回', '亏损部分的确认及转回', '亏损部分的确认及转回', '{"conf":2,"codeType":"1"}', 4, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1033, 1032, 'M4_OC_02', '亏损部分当期的变动金额', '亏损部分当期的变动金额', '亏损部分当期的变动金额', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1034, 1022, 'tzch', '投资成分', '投资成分', '投资成分', '{"conf":2,"codeType":"1"}', 5, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1035, 1034, 'M4_OD_01', '当期确认的投资成分', '当期确认的投资成分', '当期确认的投资成分', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1036, 821, 'fczbxcwsy', '分出再保险财务收益', '分出再保险财务收益', '分出再保险财务收益', '{"conf":2,"codeType":"1"}', 8, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1037, 1036, 'wdqzrfztz', '未到期责任负债融资成分调整', '未到期责任负债融资成分调整', '未到期责任负债融资成分调整', '{"conf":2,"codeType":"1"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1038, 1037, 'M4_OA_03', '保费现金流计息', '保费现金流计息', '保费现金流计息', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1039, 1037, 'M4_OB_03', '净额结算手续费计息', '净额结算手续费计息', '净额结算手续费计息', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1040, 1036, 'yfspkfzrztz', '已发生赔款负债融资成分调整', '已发生赔款负债融资成分调整', '已发生赔款负债融资成分调整', '{"conf":2,"codeType":"1"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1041, 1040, 'M4_OI_04', 'IFIE-CASE-计提利息的金额', 'IFIE-CASE-计提利息的金额', 'IFIE-CASE-计提利息的金额', '{"conf":3,"codeType":"2"}', 1, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1042, 1040, 'M4_OJ_04', 'IFIE-IBNR-计提利息的金额', 'IFIE-IBNR-计提利息的金额', 'IFIE-IBNR-计提利息的金额', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1043, 1040, 'M4_OL_04', 'IFIE-RA-计提利息的金额', 'IFIE-RA-计提利息的金额', 'IFIE-RA-计提利息的金额', '{"conf":3,"codeType":"2"}', 2, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1044, 1036, 'hlbd', '汇率变动', '汇率变动', '汇率变动', '{"conf":2,"codeType":"4"}', 3, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1045, 821, 'jlyks', '净利润/(亏损)', '净利润/(亏损)', '净利润/(亏损)', '{"conf":5,"codeType":"1","sumList":["cyzbxjcb","fczbxcwsy"]}', 9, '1', 1, NULL, NULL, NULL);
INSERT INTO qtcuser.qtc_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES (1061, 721, 'Out103Check', 'Check', 'Check', 'Check', '{"sign":1,"lrcExclLc":5,"lrcLc":5,"licExclLc":5,"licLc":5,"codeType":"1", "sumList":["qmnmfczbxhfzzc","-qmnmfczbxhtjfzzc"]}', 18, '1', 1, NULL, NULL, NULL);
