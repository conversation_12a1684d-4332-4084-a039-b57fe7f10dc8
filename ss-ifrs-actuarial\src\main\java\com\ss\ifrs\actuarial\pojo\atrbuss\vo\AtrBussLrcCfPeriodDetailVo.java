/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2023-01-11 16:41:00
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2023-01-11 16:41:00<br/>
 * Description: LRC输出现金流发展期明细表<br/>
 * Table Name: ATR_BUSS_LRC_CF_PERIOD_DETAIL<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "LRC输出现金流发展期明细表")
public class AtrBussLrcCfPeriodDetailVo implements Serializable {
    /**
     * Database column: ATR_BUSS_LRC_CF_PERIOD_DETAIL.LRC_CF_PERIOD_DETAIL_ID
     * Database remarks: lrc_cf_period_detail_id|主键
     */
    @ApiModelProperty(value = "lrc_cf_period_detail_id|主键", required = true)
    private Long lrcCfPeriodDetailId;

    /**
     * Database column: ATR_BUSS_LRC_CF_PERIOD_DETAIL.LRC_CF_PERIOD_ID
     * Database remarks: lrc_cf_period_id|发展期主表主键
     */
    @ApiModelProperty(value = "lrc_cf_period_id|发展期主表主键", required = true)
    private Long lrcCfPeriodId;

    /**
     * Database column: ATR_BUSS_LRC_CF_PERIOD_DETAIL.LRC_CF_FEE_TYPE
     * Database remarks: lrc_cf_fee_type|lrc现金流费用类型
     */
    @ApiModelProperty(value = "lrc_cf_fee_type|lrc现金流费用类型", required = false)
    private String lrcCfFeeType;

    /**
     * Database column: ATR_BUSS_LRC_CF_PERIOD_DETAIL.DEV_PERIOD
     * Database remarks: dev_period|发展期
     */
    @ApiModelProperty(value = "dev_period|发展期", required = false)
    private Integer devPeriod;

    /**
     * Database column: ATR_BUSS_LRC_CF_PERIOD_DETAIL.LRC_CF_AMOUNT
     * Database remarks: lrc_cf_amount|现金流费用金额
     */
    @ApiModelProperty(value = "lrc_cf_amount|现金流费用金额", required = false)
    private BigDecimal lrcCfAmount;

    private static final long serialVersionUID = 1L;

    public Long getLrcCfPeriodDetailId() {
        return lrcCfPeriodDetailId;
    }

    public void setLrcCfPeriodDetailId(Long lrcCfPeriodDetailId) {
        this.lrcCfPeriodDetailId = lrcCfPeriodDetailId;
    }

    public Long getLrcCfPeriodId() {
        return lrcCfPeriodId;
    }

    public void setLrcCfPeriodId(Long lrcCfPeriodId) {
        this.lrcCfPeriodId = lrcCfPeriodId;
    }

    public String getLrcCfFeeType() {
        return lrcCfFeeType;
    }

    public void setLrcCfFeeType(String lrcCfFeeType) {
        this.lrcCfFeeType = lrcCfFeeType;
    }

    public Integer getDevPeriod() {
        return devPeriod;
    }

    public void setDevPeriod(Integer devPeriod) {
        this.devPeriod = devPeriod;
    }

    public BigDecimal getLrcCfAmount() {
        return lrcCfAmount;
    }

    public void setLrcCfAmount(BigDecimal lrcCfAmount) {
        this.lrcCfAmount = lrcCfAmount;
    }
}