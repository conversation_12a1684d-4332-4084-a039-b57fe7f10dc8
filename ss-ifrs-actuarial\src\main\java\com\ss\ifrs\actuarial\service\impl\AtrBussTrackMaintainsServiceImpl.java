package com.ss.ifrs.actuarial.service.impl;


import com.ss.ifrs.actuarial.dao.AtrBussTrackMaintainsDao;
import com.ss.ifrs.actuarial.service.AtrBussTrackMaintainsService;
import com.ss.platform.pojo.bms.track.vo.TrackMaintainsReqVo;
import com.ss.platform.pojo.bms.track.vo.TrackMaintainsVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.StringUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class AtrBussTrackMaintainsServiceImpl implements AtrBussTrackMaintainsService {
    @Autowired
    AtrBussTrackMaintainsDao atrBussTrackMaintainsDao;

    public Page<TrackMaintainsReqVo> fuzzySearchPage(TrackMaintainsVo vo, Pageable pageParam) throws Exception {
        if(ObjectUtils.isEmpty(vo) || ObjectUtils.isEmpty(vo.getRedirectApp())|| ObjectUtils.isEmpty(vo.getRedirectUrl())
                || ObjectUtils.isEmpty(vo.getRedirectParam())|| ObjectUtils.isEmpty(vo.getModelHisId())) {
            return null;
        }
        if (hasIllegalSqlStr(vo.getRedirectApp())|| hasIllegalSqlStr(vo.getRedirectUrl())
                || hasIllegalSqlStr(vo.getRedirectParam())|| hasIllegalSqlStr(vo.getModelHisId())) {
            throw new Exception("This text contains illegal characters!");
        }
        return atrBussTrackMaintainsDao.fuzzySearchPage(vo, pageParam);
    }

    /**
     * 分析sql是否为DML类型
     * <param name="Str">传入用户提交数据</param>
     * <returns>返回是否含有SQL注入式攻击代码</returns>
     **/
    private boolean hasIllegalSqlStr(String str) {
        String sqlStr = "exec|truncate|create|alter|drop|grant|revoke";
        if (StringUtil.isNotEmpty(str) && str.length() > 0)
        {
            String[] anySqlStr = sqlStr.split("\\|", -1);
            for (String ss : anySqlStr)
            {
                if (str.toLowerCase().indexOf(ss) >= 0)
                {
                    return true;
                }
            }
        }
        return false;
    }

}
