package com.ss.ifrs.actuarial.dao;

import com.ss.ifrs.actuarial.pojo.ecf.vo.AtrSD7;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lic.AtrBussLicAction;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lic.AtrBussLicGDevAmount;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lic.AtrBussLicU;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lic.AtrBussLicUAmount;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lic.AtrDuctLicLastCurCf;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lic.AtrLogLic;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface AtrBussLicDao {

    void insertLog(AtrLogLic vo);

    long getMaxLogId();


    void createAction(AtrBussLicAction vo);

    void updateActionStatus(AtrBussLicAction vo);

    long getMaxMainId();
    long getMaxMainGId();

    void truncateAccTemp();

    String getLastActionNo(@Param("entityId") Long entityId, @Param("lastYearMonth") String lastYearMonth,
                           @Param("businessSourceCode") String businessSourceCode);


    List<AtrBussLicU> findAccTempList();

    List<AtrBussLicUAmount> insertAmountData(Map<String, Object> commonParamMap);

    List<AtrDuctLicLastCurCf> collectLastCurCF(Map<String, Object> commonParamMap);

    List<AtrSD7> collectInitIr(Map<String, Object> commonParamMap);
    
    /**
     * 获取上一期的合同组现金流数据
     * @param commonParamMap 通用参数
     * @return 上一期合同组发展期数据列表
     */
    List<AtrBussLicGDevAmount> collectPreviousGroupCF(Map<String, Object> commonParamMap);

    /**
     * 获取上期的折现数据
     * @param commonParamMap 通用参数
     * @return 折现数据列表
     */
    List<AtrSD7> collectPrevDiscounts(Map<String, Object> commonParamMap);
    
    /**
     * 获取汇总后的发展期数据
     * @param mainId 主表ID
     * @param cfType 现金流类型
     * @param amountType 金额类型
     * @return 发展期数据列表
     */
    List<AtrBussLicGDevAmount> findGDevAmounts(@Param("mainId") Long mainId, 
                                              @Param("cfType") String cfType,
                                              @Param("amountType") String amountType);

    void deleteTempData();

    void initTempData(Map<String, Object> commonParamMap);

    /**
     * 获取临时表中的数据
     * @param paramMap 参数Map，包含分区参数pn
     * @return 当前分区的临时数据列表
     */
    List<AtrBussLicU> getTempData(Map<String, Object> paramMap);


}
