create or replace package qtc_pack_evaluate is

    procedure proc_evaluate_main(p_year_month varchar, p_version varchar);

    procedure proc_evaluate_interest(p_interest_id NUMBER);

    procedure proc_evl_run(p_version varchar2, p_model_id number);

end qtc_pack_evaluate;
/

create or replace package body qtc_pack_evaluate is

    procedure proc_evl_log_run(p_main_id number, p_version varchar, p_node varchar, p_state varchar) is

    begin


        if p_state = 'B' then
            insert into qtc_LOG_EVL_RUN(evl_run_id, evl_main_id, version_no, node_code, run_state, start_time, end_time,
                                        run_cost)
            values (qtc_seq_log_evl_run.nextval, p_main_id, p_version, p_node, p_state, localtimestamp, null, null);

        else

            UPDATE qtc_LOG_EVL_RUN
            SET run_state = p_state,
                end_time  = localtimestamp,
                run_cost  = (extract(DAY FROM localtimestamp - START_TIME) * 24 * 60 * 60) +
                            (extract(HOUR FROM localtimestamp - START_TIME) * 60 * 60) +
                            (extract(MINUTE FROM localtimestamp - START_TIME) * 60) +
                            extract(SECOND FROM localtimestamp - START_TIME)
            WHERE version_no = p_version
              and node_code = p_node;

        end if;

        commit;

    END proc_evl_log_run;

    procedure proc_evl_log_run_dt(p_main_id number, p_version varchar, p_sup_node varchar,
                                  p_node varchar, p_err varchar, p_type varchar) is

    begin

        if p_type = 'B' then
            insert into qtc_LOG_EVL_RUN_DETAIL(evl_run_detail_id, evl_main_id, version_no,
                                               super_node, node_code, start_time, ERR_MSG)
            values (qtc_seq_log_evl_run_detail.nextval, p_main_id, p_version, p_sup_node,
                    p_node, localtimestamp, substr(p_err, 1, 4000));

        else

            UPDATE qtc_LOG_EVL_RUN_DETAIL
            SET err_msg  = substr(p_err, 1, 4000),
                end_time = localtimestamp,
                run_cost = (extract(DAY FROM localtimestamp - START_TIME) * 24 * 60 * 60) +
                           (extract(HOUR FROM localtimestamp - START_TIME) * 60 * 60) +
                           (extract(MINUTE FROM localtimestamp - START_TIME) * 60) +
                           extract(SECOND FROM localtimestamp - START_TIME)
            WHERE version_no = p_version
              and SUPER_NODE = p_sup_node
              and node_code = p_node;

        end if;

        commit;

    END proc_evl_log_run_dt;

    procedure proc_evaluate_interest(p_interest_id NUMBER) is


        v_year_month    varchar2(6);
        v_currency      varchar2(6) ;
        v_center        NUMBER ;
        v_main_id       NUMBER ;
        v_int_rate_l    NUMBER ;
        v_profit        NUMBER ;
        v_profit_flow_l NUMBER ;
        v_rate_l        NUMBER;
        v_rate_2_f      NUMBER ;
        v_min_mh        integer;
        v_num           integer ;
        v_fl            integer ;

    BEGIN

        select year_month, CURRENCY_code, ENTITY_ID
        into v_year_month,v_currency, v_center
        from qtc_conf_interest_rate
        where interest_rate_id = p_interest_id;

        select count(*) into v_num
        from qtc_conf_interest_rate t1,
                 qtc_buss_interest_main t2
            where t1.CONFIRM_IS = '1'
              and t1.YEAR_MONTH = to_char(add_months(to_date(v_year_month, 'yyyymm'), -1), 'yyyymm')
              and t1.ENTITY_ID = v_center
              and t1.CURRENCY_CODE = v_currency
              and t2.INTEREST_RATE_ID = t1.INTEREST_RATE_ID
              and t2.INTEREST_TYPE = 1;

        if v_num = 1 then

            select t2.INTEREST_MAIN_ID
            into v_int_rate_l
            from qtc_conf_interest_rate t1,
                 qtc_buss_interest_main t2
            where t1.CONFIRM_IS = '1'
              and t1.YEAR_MONTH = to_char(add_months(to_date(v_year_month, 'yyyymm'), -1), 'yyyymm')
              and t1.ENTITY_ID = v_center
              and t1.CURRENCY_CODE = v_currency
              and t2.INTEREST_RATE_ID = t1.INTEREST_RATE_ID
              and t2.INTEREST_TYPE = 1;

        end if;

        select min(norm_month)
        into v_min_mh
        from qtc_conf_interest_rate_detail
        where interest_rate_id = p_interest_id;

        v_main_id := qtc_seq_buss_interest_main.nextval;
        insert into qtc_buss_interest_main(interest_main_id, interest_rate_id, ENTITY_ID, CURRENCY_code, year_month,
                                           interest_type)
        values (v_main_id, p_interest_id, v_center, v_currency, v_year_month, '1');

        v_profit_flow_l := 1.0;
        for cur_mon in (select (norm_month - v_min_mh + 1) norm_month,
                               mon_far_profit_flow / 100   mon_far_profit_flow
                        from qtc_conf_interest_rate_detail
                        where interest_rate_id = p_interest_id
                        order by norm_month)
            loop

                if v_rate_l is null then
                    v_rate_l := 1.0;
                end if;

                insert into qtc_buss_interest_detail(interest_detail_id, interest_main_id,
                                                     date_type, pay_time, dev_period, interest_rate)
                values (qtc_SEQ_buss_INTEREST_DETAIL.nextval, v_main_id,
                        'B', '2', cur_mon.norm_month, cur_mon.mon_far_profit_flow);

                insert into qtc_buss_interest_detail(interest_detail_id, interest_main_id, date_type, pay_time,
                                                     dev_period, interest_rate)
                values (qtc_SEQ_buss_INTEREST_DETAIL.nextval, v_main_id, 'J', '0', cur_mon.norm_month,
                        v_rate_l);

                insert into qtc_buss_interest_detail(interest_detail_id, interest_main_id, date_type,
                                                     pay_time, dev_period, interest_rate)
                values (qtc_SEQ_buss_INTEREST_DETAIL.nextval, v_main_id, 'J',
                        '0.5', cur_mon.norm_month, v_rate_l * POWER((cur_mon.mon_far_profit_flow + 1), -0.5));

                v_profit_flow_l := v_profit_flow_l * (cur_mon.mon_far_profit_flow + 1);

                insert into qtc_buss_interest_detail(interest_detail_id, interest_main_id, date_type,
                                                     pay_time, dev_period, interest_rate)
                values (qtc_SEQ_buss_INTEREST_DETAIL.nextval, v_main_id, 'J',
                        '1', cur_mon.norm_month, POWER(v_profit_flow_l, -1));

                v_rate_l := POWER(v_profit_flow_l, -1);


            end loop;

        v_main_id := qtc_seq_buss_interest_main.nextval;
        insert into qtc_buss_interest_main(interest_main_id, interest_rate_id, ENTITY_ID, CURRENCY_code, year_month,
                                           interest_type)
        values (v_main_id, p_interest_id, v_center, v_currency, v_year_month, '2');


        v_profit_flow_l := 0.0;
        v_rate_l := null;
        for cur_mon in (select ttt.dev_no , ttt.b_rate
                            from (select (norm_month - v_min_mh+1)          dev_no,
                                         mon_far_profit_flow / 100 b_rate
                                  from qtc_conf_interest_rate_detail
                                  where interest_rate_id = p_interest_id
                                  union
                                  select 0 , 0 from dual
                                  ) ttt
                            order by ttt.dev_no)
            loop

                if cur_mon.dev_no = 0   then

                    if v_int_rate_l is not null then
                        select INTEREST_RATE into v_profit
                        from qtc_buss_interest_detail
                        where INTEREST_MAIN_ID = v_int_rate_l
                         and DATE_TYPE = 'B'
                         and DEV_PERIOD = 1;

                    end if;

                    v_fl := 1 ;

                else
                    v_profit := cur_mon.b_rate ;
                     v_fl := -1 ;

                end if;

                if cur_mon.dev_no = 0 then
                    v_rate_2_f := 1 + nvl(v_profit,0);
                    v_rate_l := 1;
                    v_profit_flow_l := 1;
                else
                    v_rate_2_f := v_rate_l;
                    v_profit_flow_l := v_profit_flow_l * (v_profit + 1);
                end if;


                insert into qtc_buss_interest_detail(interest_detail_id, interest_main_id,
                                                     date_type, pay_time, dev_period, interest_rate)
                values (qtc_SEQ_buss_INTEREST_DETAIL.nextval, v_main_id,
                        'B', '2', cur_mon.dev_no, nvl(v_profit,0));

                insert into qtc_buss_interest_detail(interest_detail_id, interest_main_id, date_type,
                                                     pay_time, dev_period, interest_rate)
                values (qtc_SEQ_buss_INTEREST_DETAIL.nextval, v_main_id, 'J',
                        '0', cur_mon.dev_no, v_rate_2_f);

                insert into qtc_buss_interest_detail(interest_detail_id, interest_main_id, date_type,
                                                     pay_time, dev_period, interest_rate)
                values (qtc_SEQ_buss_INTEREST_DETAIL.nextval, v_main_id, 'J',
                        '0.5', cur_mon.dev_no, v_rate_l * POWER((nvl(v_profit,0) + 1), v_fl*0.5));


                insert into qtc_buss_interest_detail(interest_detail_id, interest_main_id, date_type,
                                                     pay_time, dev_period, interest_rate)
                values (qtc_SEQ_buss_INTEREST_DETAIL.nextval, v_main_id, 'J',
                        '1', cur_mon.dev_no, POWER(v_profit_flow_l, v_fl));

                v_rate_l := POWER(v_profit_flow_l, -1);

            end loop;


    EXCEPTION
        WHEN OTHERS THEN
            raise_application_error(-20002, SQLERRM);
    END proc_evaluate_interest;

    procedure proc_evl_version_save(p_main_id number, P_TYPE VARCHAR2, P_SOURCE VARCHAR2, P_NO VARCHAR2, P_ID NUMBER) is

    begin

        if p_main_id || p_type || p_id is null then
            return ;
        end if;

        INSERT INTO qtc_buss_evaluate_cf_ref(CF_NO_ID, EVALUATE_MAIN_ID, CF_TYPE, CF_SOURCE, VERSION_NO, VERSION_ID)
        VALUES (qtc_SEQ_BUSS_EVALUATE_CF_REF.nextval, P_MAIN_ID, P_TYPE, P_SOURCE, P_NO, P_ID);

    EXCEPTION
        WHEN OTHERS THEN
            raise_application_error(-20002, sqlerrm) ;
    end proc_evl_version_save;

    procedure proc_evl_bm_dd_cf(p_version varchar2) is

        v_node    varchar(32);
        v_node_dt varchar(32);

    BEGIN

        v_node := 'RLCF_DD';
        proc_evl_log_run(null, p_version, v_node, 'B');

        v_node_dt := 'RLCF_DD_001';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_bm_dd_unit
            (unit_ID,
             VERSION_NO,
             model_def_id,
             YEAR_MONTH,
             EVALUATE_MAIN_ID,
             policy_no,
             endorse_seq_no,
             loa_code,
             portfolio_no,
             icg_no,
             evaluate_approach,
             effective_date,
             expiry_date,
             contract_date,
             new_ym,
             new_year,
             premium,
             CUR_END_REMAIN_UN_RATE,
             LP_CUR_END_REMAIN_UN_RATE,
             ACCUMULATED_EARNED_RATE,
             RPT_PER_REMAIN_UN_RATE)
            select qtc_seq_buss_evl_bm_dd_unit.nextval,
                   mt.VERSION_NO,
                   mt.MODEL_DEF_ID,
                   mt.YEAR_MONTH,
                   mt.EVALUATE_MAIN_ID,
                   t2.policy_no,
                   t2.endorse_seq_no,
                   t2.loa_code,
                   t2.portfolio_no,
                   t2.icg_no,
                   t2.evaluate_approach,
                   t2.EFFECTIVE_DATE,
                   t2.EXPIRY_DATE,
                   t2.contract_date,
                   to_char(t2.contract_date, 'yyyymm'),
                   to_char(t2.contract_date, 'yyyy'),
                   t2.premium,
                   T2.CUR_END_REMAIN_UN_RATE,
                   t2.LP_CUR_END_REMAIN_UN_RATE,
                   T2.ACCUMULATED_EARNED_RATE,
                   t2.RPT_PER_REMAIN_UN_RATE
            from qtc_buss_evaluate_main mt ,
                 QTC_DAP_ECF_DD_ICU t2
            where  mt.VERSION_NO = p_version
              and mt.BUSINESS_MODEL = 'D'
              and mt.BUSINESS_DIRECTION = 'D'
              and t2.currency_code = mt.currency_code
              and t2.entity_id = mt.entity_id
              and t2.YEAR_MONTH = mt.YEAR_MONTH
              and t2.LOA_CODE = mt.LOA_CODE ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_DD_002';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_BUSS_EVL_ICG_MSG(ICG_MSG_ID, version_no, batch_no, BUS_DIR ,
                                         evaluate_approach, model_def_id, evl_ym, evl_main_id,
                                         loa_code, icg_no, cal_type, CR_PREMIUM, PREMIUM,  unit_num,
                                         rpt_start, rpt_end, rpt_bwt, rpt_num)
        select qtc_SEQ_BUSS_EVL_ICG_MSG.nextval,
               pt.VERSION_NO,
               1,
               buss_dir ,
               PT.EVALUATE_APPROACH,
               pt.MODEL_DEF_ID,
               pt.YEAR_MONTH,
               pt.EVALUATE_MAIN_ID,
               pt.LOA_CODE,
               PT.ICG_NO,
               'Lrc' ,
               pt.NEW_PREMIUM,
               pt.PREMIUM ,
               pt.unit_num,
               TO_CHAR(TRUNC(TO_DATE(pt.YEAR_MONTH, 'YYYYMM'), 'YEAR'), 'YYYYMM'),
               TO_CHAR(ADD_MONTHS(TRUNC(TO_DATE(pt.YEAR_MONTH, 'YYYYMM'), 'YEAR'), 11), 'YYYYMM'),
               MONTHS_BETWEEN(TO_DATE(pt.YEAR_MONTH, 'YYYYMM'), TRUNC(TO_DATE(pt.YEAR_MONTH, 'YYYYMM'), 'YEAR')) +
               1,
               12
        from (SELECT mt.VERSION_NO,
                     mt.MODEL_DEF_ID,
                     mt.BUSINESS_MODEL||mt.BUSINESS_DIRECTION buss_dir ,
                     mt.YEAR_MONTH,
                     mt.LOA_CODE,
                     mt.EVALUATE_MAIN_ID,
                     UT.EVALUATE_APPROACH,
                     UT.ICG_NO,
                     sum(case when ut.NEW_YM = mt.YEAR_MONTH then ut.PREMIUM end) NEW_PREMIUM,
                     sum(ut.PREMIUM)                                              PREMIUM,
                     count(ut.UNIT_ID)                                            unit_num
              FROM qtc_BUSS_EVALUATE_MAIN mt,
                   qtc_buss_evl_bm_dd_unit UT
              WHERE mt.VERSION_NO = p_version
                and mt.BUSINESS_MODEL = 'D'
                and mt.BUSINESS_DIRECTION = 'D'
                and UT.EVALUATE_MAIN_ID = mt.EVALUATE_MAIN_ID
              GROUP BY mt.VERSION_NO,
                       mt.MODEL_DEF_ID,
                       mt.BUSINESS_MODEL||mt.BUSINESS_DIRECTION,
                       mt.YEAR_MONTH,
                       mt.LOA_CODE,
                       mt.EVALUATE_MAIN_ID,
                       UT.EVALUATE_APPROACH,
                       UT.ICG_NO) pt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_DD_003';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');
        --
        merge into qtc_buss_evl_bm_dd_unit tg
        using (select ms.VERSION_NO ,
                      ms.MODEL_DEF_ID,
                      ms.ICG_NO,
                      t1.policy_no,
                      t1.ENDORSE_SEQ_NO,
                      max(T1.ENDORSE_NO)                 ENDNO,
                      max(case
                              when t1.POLICY_TYPE_CODE = 'E' then t1.VALID_DATE
                              else t1.CONTRACT_DATE end) CON_DATE,
                      sum(T1.COMMISSION)                 iacf
               from atruser.atr_DAP_DD_PREMIUM t1,
                    qtc_BUSS_EVL_ICG_MSG ms
               where ms.VERSION_NO = p_version
                 and ms.CAL_TYPE = 'Lrc'
                 and t1.LOA_CODE = ms.LOA_CODE
                 and t1.ICG_NO = ms.ICG_NO
               group by ms.VERSION_NO, ms.MODEL_DEF_ID, ms.ICG_NO, t1.policy_no, t1.ENDORSE_SEQ_NO) sc
        on (tg.VERSION_NO = sc.VERSION_NO  and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID  and tg.ICG_NO = sc.ICG_NO
            and tg.POLICY_NO = sc.policy_no and tg.ENDORSE_SEQ_NO = sc.ENDORSE_SEQ_NO)
        when matched then
            update
            set iacf_fee      = sc.iacf * -1,
                ENDORSE_NO    = SC.ENDNO,
                CONTRACT_DATE = sc.CON_DATE,
                new_ym        = to_char(sc.CON_DATE, 'yyyymm'),
                new_year      = to_char(sc.CON_DATE, 'yyyy');

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_DD_004';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');
        --
        merge into qtc_buss_evl_bm_dd_unit tg
        using (select t2.VERSION_NO,
                      t2.MODEL_DEF_ID,
                      t1.ICG_NO,
                      t1.policy_no,
                      t1.ENDORSE_SEQ_NO,
                      sum(t1.PAID_PREMIUM) premium,
                      sum(T1.COMMISSION)   iacf
               from qtc_DAP_DD_PREMIUM_PAID t1,
                    qtc_buss_evaluate_main t2
               where T1.entity_id = t2.entity_id
                 and T1.YEAR_MONTH = t2.YEAR_MONTH
                 and t1.LOA_CODE = t2.LOA_CODE
                 and t2.BUSINESS_MODEL = 'D'
                 and t2.BUSINESS_DIRECTION = 'D'
                 and t2.VERSION_NO = p_version
               group by t2.VERSION_NO, t2.MODEL_DEF_ID, t1.ICG_NO, t1.policy_no, t1.ENDORSE_SEQ_NO) sc
        on (tg.VERSION_NO = sc.VERSION_NO  and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO
            and tg.POLICY_NO = sc.policy_no and tg.ENDORSE_SEQ_NO = sc.ENDORSE_SEQ_NO)
        when matched then
            update
            set cur_iacf    = sc.iacf * -1,
                cur_premium = sc.premium;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_DD_005';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');
        --
        insert into qtc_BUSS_EVL_BM_DD_UNIT_CF(unit_cf_id, version_no, model_def_id,
                                                   evaluate_approach, evaluate_main_id, icg_no, policy_no,
                                                   endorse_seq_no, NEW_YM, NEW_YEAR,
                                                   year_month, exp_ym, dev_period, ed_premium, ue_premium,
                                                   recv_premium, iacf_fee, maintenance_fee, ultimate_loss,
                                                   brokerage_fee, adj_commission)
            select qtc_seq_buss_evl_bm_dd_unit_cf.nextval,
                   mt.VERSION_NO,
                   mt.MODEL_DEF_ID,
                   ut.EVALUATE_APPROACH,
                   mt.evl_main_id,
                   ut.icg_no,
                   ut.POLICY_NO,
                   ut.ENDORSE_SEQ_NO,
                   ut.NEW_YM,
                   ut.NEW_YEAR,
                   mt.evl_ym,
                   to_char(add_months(to_date(mt.evl_ym, 'yyyymm'), t3.DEV_PERIOD), 'yyyymm'),
                   t3.DEV_PERIOD,
                   CASE
                       WHEN T3.DEV_PERIOD = 0 THEN ut.PREMIUM * ut.LP_CUR_END_REMAIN_UN_RATE - T3.UE_PREMIUM
                       ELSE T3.ED_PREMIUM END,
                   t3.UE_PREMIUM,
                   t3.RECV_PREMIUM,
                   (nvl(t3.IACF_FEE,0)+nvl(t3.IACF_FEE_NON_POLICY,0)+nvl(T3.BROKERAGE_FEE,0)) * -1,
                   T3.MAINTENANCE_FEE * -1,
                   T3.ULTIMATE_LOSS * -1,
                   T3.BROKERAGE_FEE * -1,
                   t3.ADJ_COMMISSION * -1
            from qtc_BUSS_EVL_ICG_MSG mt,
                 qtc_buss_evl_bm_dd_unit ut,
                 QTC_DAP_ECF_DD_ICU_CF t3
            where mt.VERSION_NO = p_version
              and ut.EVALUATE_MAIN_ID = mt.EVL_MAIN_ID
              AND ut.icg_no = mt.icg_no
              and t3.YEAR_MONTH = MT.EVL_YM
              AND T3.ICG_NO = MT.ICG_NO
              AND T3.POLICY_NO = UT.POLICY_NO
              AND T3.ENDORSE_SEQ_NO = UT.ENDORSE_SEQ_NO;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_DD_006';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_BUSS_EVL_BM_ICG_RATE(icg_rate_id, version_no, evl_main_id, loa_code,
                                             icg_no, dev_period, flt1)
        select qtc_SEQ_BUSS_EVL_BM_ICG_RATE.nextval,
               MT.VERSION_NO,
               MT.EVALUATE_MAIN_ID,
               MT.LOA_CODE,
               t2.ICG_NO,
               t3.DEV_NO,
               T3.CSM_RATE
        from atruser.atr_BUSS_LRC_ACTION t1,
             atruser.atr_BUSS_DD_LRC_ICG_CALC t2,
             atruser.atr_BUSS_DD_LRC_ICG_CALC_DETAIL t3,
             qtc_buss_evaluate_main mt
        where t1.action_no = t2.action_no
          and t2.id = t3.MAIN_ID
          and t1.currency_code = mt.currency_code
          and t1.entity_id = mt.entity_id
          and t1.CONFIRM_IS = '1'
          and t1.YEAR_MONTH = mt.YEAR_MONTH
          and t2.LOA_CODE = mt.LOA_CODE
          and mt.VERSION_NO = p_version
          and mt.BUSINESS_DIRECTION = 'D'
          AND MT.BUSINESS_MODEL = 'D';

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_DD_008';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_BUSS_EVL_ICG_CUR(icg_cur_id, version_no, evl_ym, buss_model ,
                                         model_def_id, loa_code, evl_main_id, icg_no, cr_premium, CR_IACF)
        select qtc_seq_BUSS_EVL_ICG_CUR.nextval,
               ttt.VERSION_NO,
               ttt.YEAR_MONTH,
               'DD' ,
               ttt.MODEL_DEF_ID,
               ttt.LOA_CODE,
               ttt.EVALUATE_MAIN_ID,
               ttt.ICG_NO,
               ttt.premium,
               ttt.iacf
        from (select t2.MODEL_DEF_ID,
                     t2.YEAR_MONTH,
                     t2.LOA_CODE,
                     t2.EVALUATE_MAIN_ID,
                     t2.VERSION_NO,
                     t1.ICG_NO,
                     sum(t1.PAID_PREMIUM)    premium,
                     sum(T1.COMMISSION * -1) iacf
              from qtc_DAP_DD_PREMIUM_PAID t1,
                   qtc_buss_evaluate_main t2
              where T1.entity_id = t2.entity_id
                and T1.YEAR_MONTH = t2.YEAR_MONTH
                and t1.LOA_CODE = t2.LOA_CODE
                and t2.BUSINESS_MODEL = 'D'
                and t2.BUSINESS_DIRECTION = 'D'
                and t2.VERSION_NO = p_version
              group by t2.MODEL_DEF_ID,
                       t2.YEAR_MONTH,
                       t2.LOA_CODE,
                       t2.EVALUATE_MAIN_ID,
                       t2.VERSION_NO,
                       t1.ICG_NO) ttt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_DD_009';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        merge into qtc_BUSS_EVL_ICG_MSG tg
        using (SELECT mt.VERSION_NO,
                      mt.MODEL_DEF_ID,
                      UT.ICG_NO,
                      sum(ut.PREMIUM) NEW_PREMIUM
               FROM qtc_BUSS_EVALUATE_MAIN mt,
                    qtc_buss_evl_bm_dd_unit UT
               WHERE mt.VERSION_NO = p_version
                 and mt.BUSINESS_MODEL = 'D'
                 and mt.BUSINESS_DIRECTION = 'D'
                 and UT.EVALUATE_MAIN_ID = mt.EVALUATE_MAIN_ID
                 and ut.NEW_YM = mt.YEAR_MONTH
               GROUP BY mt.VERSION_NO, mt.MODEL_DEF_ID, UT.ICG_NO) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO)
        when matched then
            update set tg.CR_PREMIUM = sc.NEW_PREMIUM;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        proc_evl_log_run(null, p_version, v_node, 'S');

    EXCEPTION
        WHEN OTHERS THEN
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, sqlerrm, 'S');
            proc_evl_log_run(null, p_version, v_node, 'E');

            raise_application_error(-20002, SQLERRM);
    END proc_evl_bm_dd_cf;

    procedure proc_evl_bm_ti_cf(p_version varchar2) is

        v_node    varchar(32);
        v_node_dt varchar(32);

    BEGIN

        v_node := 'RLCF_TI';
        proc_evl_log_run(null, p_version, v_node, 'B');

        v_node_dt := 'RLCF_TI_001';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_bm_ti_unit
            (unit_ID,
             VERSION_NO,
             model_def_id,
             YEAR_MONTH,
             EVALUATE_MAIN_ID,
             treaty_no,
             loa_code,
             portfolio_no,
             icg_no,
             evaluate_approach,
             effective_date,
             expiry_date,
             contract_date,
             new_ym,
             new_year,
             premium,
             CUR_END_REMAIN_UN_RATE,
             LP_CUR_END_REMAIN_UN_RATE,
             ACCUMULATED_EARNED_RATE,
             RPT_PER_REMAIN_UN_RATE)
            select qtc_seq_buss_evl_bm_ti_unit.nextval,
                   mt.VERSION_NO,
                   mt.MODEL_DEF_ID,
                   mt.YEAR_MONTH,
                   mt.EVALUATE_MAIN_ID,
                   t2.treaty_no,
                   t2.loa_code,
                   t2.portfolio_no,
                   t2.icg_no,
                   t2.evaluate_approach,
                   t2.EFFECTIVE_DATE,
                   t2.EXPIRY_DATE,
                   t2.contract_date,
                   to_char(t2.contract_date, 'yyyymm'),
                   to_char(t2.contract_date, 'yyyy'),
                   t2.premium,
                   T2.CUR_END_REMAIN_UN_RATE,
                   t2.LP_CUR_END_REMAIN_UN_RATE,
                   T2.ACCUMULATED_EARNED_RATE,
                   t2.RPT_PER_REMAIN_UN_RATE
            from qtc_buss_evaluate_main mt ,
                 QTC_DAP_ECF_TI_ICU t2
            where  mt.VERSION_NO = p_version
              and mt.BUSINESS_MODEL = 'T'
              and mt.BUSINESS_DIRECTION = 'I'
              and t2.currency_code = mt.currency_code
              and t2.entity_id = mt.entity_id
              and t2.YEAR_MONTH = mt.YEAR_MONTH
              and t2.LOA_CODE = mt.LOA_CODE ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_TI_001';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_BUSS_EVL_ICG_MSG(ICG_MSG_ID, version_no, batch_no, BUS_DIR ,
                                         evaluate_approach, model_def_id, evl_ym, evl_main_id,
                                         loa_code, icg_no, CAL_TYPE, CR_PREMIUM, PREMIUM, unit_num,
                                         rpt_start, rpt_end, rpt_bwt, rpt_num)
        select qtc_SEQ_BUSS_EVL_ICG_MSG.nextval,
               pt.VERSION_NO,
               1,
               buss_dir ,
               PT.EVALUATE_APPROACH,
               pt.MODEL_DEF_ID,
               pt.YEAR_MONTH,
               pt.EVALUATE_MAIN_ID,
               pt.LOA_CODE,
               PT.ICG_NO,
               'Lrc' ,
               pt.NEW_PREMIUM,
               pt.PREMIUM ,
               pt.unit_num,
               TO_CHAR(TRUNC(TO_DATE(pt.YEAR_MONTH, 'YYYYMM'), 'YEAR'), 'YYYYMM'),
               TO_CHAR(ADD_MONTHS(TRUNC(TO_DATE(pt.YEAR_MONTH, 'YYYYMM'), 'YEAR'), 11), 'YYYYMM'),
               MONTHS_BETWEEN(TO_DATE(pt.YEAR_MONTH, 'YYYYMM'), TRUNC(TO_DATE(pt.YEAR_MONTH, 'YYYYMM'), 'YEAR')) +
               1,
               12
        from (SELECT mt.VERSION_NO,
                     mt.MODEL_DEF_ID,
                     mt.BUSINESS_MODEL||mt.BUSINESS_DIRECTION buss_dir,
                     mt.YEAR_MONTH,
                     mt.LOA_CODE,
                     mt.EVALUATE_MAIN_ID,
                     UT.EVALUATE_APPROACH,
                     UT.ICG_NO,
                     sum(case when ut.NEW_YM = mt.YEAR_MONTH then ut.PREMIUM end) NEW_PREMIUM,
                     sum(ut.PREMIUM)                                              PREMIUM,
                     count(ut.UNIT_ID)                                            unit_num
              FROM qtc_BUSS_EVALUATE_MAIN mt,
                   qtc_buss_evl_bm_ti_unit UT
              WHERE mt.VERSION_NO = p_version
                and mt.BUSINESS_MODEL = 'T'
                and mt.BUSINESS_DIRECTION = 'I'
                and UT.EVALUATE_MAIN_ID = mt.EVALUATE_MAIN_ID
              GROUP BY mt.VERSION_NO,
                       mt.MODEL_DEF_ID,
                       mt.BUSINESS_MODEL||mt.BUSINESS_DIRECTION ,
                       mt.YEAR_MONTH,
                       mt.LOA_CODE,
                       mt.EVALUATE_MAIN_ID,
                       UT.EVALUATE_APPROACH,
                       UT.ICG_NO) pt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_TI_002';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');
        --
        merge into qtc_buss_evl_bm_ti_unit bp
        using (select MS.evl_main_id,
                      MS.ICG_NO,
                      t1.TREATY_NO,
                      sum(T1.COMMISSION) iacf
               from atruser.atr_DAP_TI_PREMIUM t1,
                    qtc_BUSS_EVL_ICG_MSG ms
               where ms.VERSION_NO = p_version
                 and t1.LOA_CODE = ms.LOA_CODE
                 and t1.ICG_NO = ms.ICG_NO
               group by ms.evl_main_id, MS.ICG_NO, t1.TREATY_NO) sc
        on (bp.evaluate_main_id = sc.evl_main_id and bp.ICG_NO = sc.ICG_NO and bp.TREATY_NO = sc.TREATY_NO)
        when matched then
            update
            set iacf_fee = sc.iacf * -1;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_TI_003';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');
        --
        merge into qtc_buss_evl_bm_ti_unit bp
        using (select t2.VERSION_NO,
                      t1.ICG_NO,
                      t1.TREATY_NO,
                      sum(t1.PAID_PREMIUM) premium,
                      sum(T1.COMMISSION)   iacf
               from qtc_DAP_ti_PREMIUM_PAID t1,
                    qtc_buss_evaluate_main t2
               where T1.entity_id = t2.entity_id
                 and T1.YEAR_MONTH = t2.YEAR_MONTH
                 and t1.LOA_CODE = t2.LOA_CODE
                 and t2.BUSINESS_MODEL = 'T'
                 and t2.BUSINESS_DIRECTION = 'I'
                 and t2.VERSION_NO = p_version
               group by t2.VERSION_NO, t1.ICG_NO, t1.TREATY_NO) sc
        on (bp.VERSION_NO = sc.VERSION_NO and bp.ICG_NO = sc.ICG_NO
            and bp.TREATY_NO = sc.TREATY_NO)
        when matched then
            update
            set cur_iacf    = sc.iacf * -1,
                cur_premium = sc.premium;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_TI_004';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_bm_ti_unit_cf(unit_cf_id, version_no, model_def_id,
                                                   evaluate_approach, evaluate_main_id, icg_no, TREATY_NO, NEW_YEAR,
                                                   NEW_YM,
                                                   year_month, exp_ym, dev_period, ed_premium, ue_premium,
                                                   recv_premium, iacf_fee, maintenance_fee, ultimate_loss,
                                                   brokerage_fee, adj_commission)
            select qtc_seq_buss_evl_bm_ti_unit_cf.nextval,
                   mt.VERSION_NO,
                   mt.MODEL_DEF_ID,
                   ut.EVALUATE_APPROACH,
                   mt.EVALUATE_MAIN_ID,
                   ut.icg_no,
                   ut.TREATY_NO,
                   ut.NEW_YEAR,
                   ut.NEW_YM,
                   mt.YEAR_MONTH,
                   to_char(add_months(to_date(mt.YEAR_MONTH, 'yyyymm'), t3.DEV_PERIOD), 'yyyymm'),
                   t3.DEV_PERIOD,
                   CASE
                       WHEN T3.DEV_PERIOD = 0 THEN ut.PREMIUM * ut.LP_CUR_END_REMAIN_UN_RATE - T3.UE_PREMIUM
                       ELSE T3.ED_PREMIUM END,
                   t3.UE_PREMIUM,
                   t3.RECV_PREMIUM,
                   (nvl(t3.IACF_FEE,0)+nvl(t3.IACF_FEE_NON_POLICY,0)+nvl(T3.BROKERAGE_FEE,0)) *-1,
                   T3.MAINTENANCE_FEE*-1,
                   T3.ULTIMATE_LOSS*-1,
                   T3.BROKERAGE_FEE*-1,
                   t3.ADJ_COMMISSION*-1
            from qtc_buss_evaluate_main mt,
                 qtc_buss_evl_bm_ti_unit ut,
                 QTC_DAP_ECF_TI_ICU_CF t3
            where mt.VERSION_NO = p_version
              and mt.BUSINESS_DIRECTION = 'I'
              AND MT.BUSINESS_MODEL = 'T'
              and ut.EVALUATE_MAIN_ID = mt.EVALUATE_MAIN_ID
              and t3.ENTITY_ID = mt.ENTITY_ID
              and t3.LOA_CODE = mt.LOA_CODE
              and t3.TREATY_NO = ut.TREATY_NO;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_TO_005';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        merge into qtc_buss_evl_bm_ti_unit_cf tg
            using ( select t1.VERSION_NO , t1.MODEL_DEF_ID, t3.ICG_NO, t3.TREATY_NO, t3.DEV_PERIOD, t3.ED_PREMIUM, t3.ED_PREMIUM_CUM
                    from  qtc_buss_evaluate_main t1 ,
                          qtc_buss_evaluate_main t2 ,
                          qtc_buss_evl_bm_ti_unit_cf t3
                    where t1.VERSION_NO = p_version
                    and t1.BUSINESS_MODEL = 'T'
                    and t1.BUSINESS_DIRECTION = 'I'
                    and t2.YEAR_MONTH = to_char(add_months(to_date(t1.YEAR_MONTH, 'yyyymm') , -1), 'yyyymm')
                    and t2.CONFIRM_IS = '1'
                    and t2.MODEL_DEF_ID = t1.MODEL_DEF_ID
                    and t2.LOA_CODE = t1.LOA_CODE
                    and t3.EVALUATE_MAIN_ID = t2.EVALUATE_MAIN_ID
                    and t3.DEV_PERIOD = 0 ) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO
               and tg.TREATY_NO = sc.TREATY_NO and tg.DEV_PERIOD = sc.DEV_PERIOD)
        when matched then
            update set ED_PREMIUM_CUM = nvl(sc.ED_PREMIUM,0) + nvl(sc.ED_PREMIUM_CUM,0),
                       ED_PREMIUM = nvl(tg.ED_PREMIUM,0) - (nvl(sc.ED_PREMIUM,0) + nvl(sc.ED_PREMIUM_CUM,0)) ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');


        v_node_dt := 'RLCF_TI_006';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_BUSS_EVL_BM_ICG_RATE(icg_rate_id, version_no, evl_main_id, loa_code,
                                             icg_no, dev_period, flt1)
        select qtc_SEQ_BUSS_EVL_BM_ICG_RATE.nextval,
               MT.VERSION_NO,
               MT.EVALUATE_MAIN_ID,
               MT.LOA_CODE,
               t2.ICG_NO,
               t3.DEV_NO,
               T3.CSM_RATE
        from atruser.atr_BUSS_LRC_ACTION t1,
             atruser.atr_BUSS_TI_LRC_ICG_CALC t2,
             atruser.atr_BUSS_TI_LRC_ICG_CALC_DETAIL t3,
             qtc_buss_evaluate_main mt
        where t1.action_no = t2.action_no
          and t2.id = t3.MAIN_ID
          and t1.currency_code = mt.currency_code
          and t1.entity_id = mt.entity_id
          and t1.CONFIRM_IS = '1'
          and t1.YEAR_MONTH = mt.YEAR_MONTH
          and t2.LOA_CODE = mt.LOA_CODE
          and mt.VERSION_NO = p_version
          and mt.BUSINESS_DIRECTION = 'I'
          AND MT.BUSINESS_MODEL = 'T';

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_TI_007';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_BUSS_EVL_ICG_CUR(icg_cur_id, version_no, evl_ym, buss_model ,
                                         model_def_id, loa_code, evl_main_id, icg_no, cr_premium, CR_IACF)

        select qtc_seq_BUSS_EVL_ICG_CUR.nextval,
               ttt.VERSION_NO,
               ttt.YEAR_MONTH,
               'TI' ,
               ttt.MODEL_DEF_ID,
               ttt.LOA_CODE,
               ttt.EVALUATE_MAIN_ID,
               ttt.ICG_NO,
               ttt.premium,
               ttt.iacf
        from (select t2.MODEL_DEF_ID,
                     t2.YEAR_MONTH,
                     t2.LOA_CODE,
                     t2.EVALUATE_MAIN_ID,
                     t2.VERSION_NO,
                     t1.ICG_NO,
                     sum(t1.PAID_PREMIUM)    premium,
                     sum(T1.COMMISSION * -1) iacf
              from qtc_DAP_TI_PREMIUM_PAID t1,
                   qtc_buss_evaluate_main t2
              where T1.entity_id = t2.entity_id
                and T1.YEAR_MONTH = t2.YEAR_MONTH
                and t1.LOA_CODE = t2.LOA_CODE
                and t2.BUSINESS_MODEL = 'T'
                and t2.BUSINESS_DIRECTION = 'I'
                and t2.VERSION_NO = p_version
              group by t2.MODEL_DEF_ID,
                       t2.YEAR_MONTH,
                       t2.LOA_CODE,
                       t2.EVALUATE_MAIN_ID,
                       t2.VERSION_NO,
                       t1.ICG_NO) ttt;


        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        proc_evl_log_run(null, p_version, v_node, 'S');

    EXCEPTION
        WHEN OTHERS THEN
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, sqlerrm, 'S');
            proc_evl_log_run(null, p_version, v_node, 'E');

            raise_application_error(-20002, SQLERRM);
    END proc_evl_bm_ti_cf;

    procedure proc_evl_bm_fo_cf(p_version varchar2) is

        v_node    varchar(32);
        v_node_dt varchar(32);

    BEGIN

        v_node := 'RLCF_FO';
        proc_evl_log_run(null, p_version, v_node, 'B');

        v_node_dt := 'RLCF_FO_001';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_bm_fo_unit
            (unit_ID,
             VERSION_NO,
             model_def_id,
             YEAR_MONTH,
             EVALUATE_MAIN_ID,
             ri_policy_no,
             ri_endorse_seq_no,
             RI_STATEMENT_NO,
             loa_code,
             portfolio_no,
             icg_no,
             POLICY_NO,
             ENDORSE_SEQ_NO,
             evaluate_approach,
             effective_date,
             expiry_date,
             contract_date,
             new_ym,
             new_year,
             premium,
             CUR_END_REMAIN_UN_RATE,
             LP_CUR_END_REMAIN_UN_RATE,
             ACCUMULATED_EARNED_RATE,
             RPT_PER_REMAIN_UN_RATE)
            select qtc_seq_buss_evl_bm_FO_unit.nextval,
                   mt.VERSION_NO,
                   mt.MODEL_DEF_ID,
                   mt.YEAR_MONTH,
                   mt.EVALUATE_MAIN_ID,
                   t2.ri_policy_no ,
                   t2.ri_endorse_seq_no,
                   T2.RI_STATEMENT_NO ,
                   t2.loa_code,
                   t2.portfolio_no,
                   t2.icg_no,
                   t2.POLICY_NO,
                   t2.ENDORSE_SEQ_NO,
                   t2.evaluate_approach,
                   t2.EFFECTIVE_DATE,
                   t2.EXPIRY_DATE,
                   t2.contract_date,
                   to_char(t2.contract_date, 'yyyymm'),
                   to_char(t2.contract_date, 'yyyy'),
                   t2.premium,
                   T2.CUR_END_REMAIN_UN_RATE,
                   t2.LP_CUR_END_REMAIN_UN_RATE,
                   T2.ACCUMULATED_EARNED_RATE,
                   t2.RPT_PER_REMAIN_UN_RATE
            from qtc_buss_evaluate_main mt ,
                 QTC_DAP_ECF_FO_ICU t2
            where  mt.VERSION_NO = p_version
              and mt.BUSINESS_MODEL = 'F'
              and mt.BUSINESS_DIRECTION = 'O'
              and t2.currency_code = mt.currency_code
              and t2.entity_id = mt.entity_id
              and t2.YEAR_MONTH = mt.YEAR_MONTH
              and t2.LOA_CODE = mt.LOA_CODE ;


        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_FO_002';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_BUSS_EVL_ICG_MSG(ICG_MSG_ID, version_no, batch_no, BUS_DIR ,
                                         evaluate_approach, model_def_id, evl_ym, evl_main_id,
                                         loa_code, icg_no, CAL_TYPE, CR_PREMIUM, PREMIUM, unit_num,
                                         rpt_start, rpt_end, rpt_bwt, rpt_num)
        select qtc_SEQ_BUSS_EVL_ICG_MSG.nextval,
               pt.VERSION_NO,
               1,
               pt.buss_dir ,
               PT.EVALUATE_APPROACH,
               pt.MODEL_DEF_ID,
               pt.YEAR_MONTH,
               pt.EVALUATE_MAIN_ID,
               pt.LOA_CODE,
               PT.ICG_NO,
               'Lrc' ,
               pt.NEW_PREMIUM,
               pt.PREMIUM ,
               pt.unit_num,
               TO_CHAR(TRUNC(TO_DATE(pt.YEAR_MONTH, 'YYYYMM'), 'YEAR'), 'YYYYMM'),
               TO_CHAR(ADD_MONTHS(TRUNC(TO_DATE(pt.YEAR_MONTH, 'YYYYMM'), 'YEAR'), 11), 'YYYYMM'),
               MONTHS_BETWEEN(TO_DATE(pt.YEAR_MONTH, 'YYYYMM'), TRUNC(TO_DATE(pt.YEAR_MONTH, 'YYYYMM'), 'YEAR')) +
               1,
               12
        from (SELECT mt.VERSION_NO,
                     mt.MODEL_DEF_ID,
                     mt.BUSINESS_MODEL||mt.BUSINESS_DIRECTION buss_dir,
                     mt.YEAR_MONTH,
                     mt.LOA_CODE,
                     mt.EVALUATE_MAIN_ID,
                     UT.EVALUATE_APPROACH,
                     UT.ICG_NO,
                     sum(case when ut.NEW_YM = mt.YEAR_MONTH then ut.PREMIUM end) NEW_PREMIUM,
                     sum(ut.PREMIUM)                                              PREMIUM,
                     count(ut.UNIT_ID)                                            unit_num
              FROM qtc_BUSS_EVALUATE_MAIN mt,
                   qtc_buss_evl_bm_fo_unit UT
              WHERE mt.VERSION_NO = p_version
                and mt.BUSINESS_MODEL = 'F'
                and mt.BUSINESS_DIRECTION = 'O'
                and UT.EVALUATE_MAIN_ID = mt.EVALUATE_MAIN_ID
              GROUP BY mt.VERSION_NO,
                       mt.MODEL_DEF_ID,
                       mt.BUSINESS_MODEL||mt.BUSINESS_DIRECTION ,
                       mt.YEAR_MONTH,
                       mt.LOA_CODE,
                       mt.EVALUATE_MAIN_ID,
                       UT.EVALUATE_APPROACH,
                       UT.ICG_NO) pt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_FO_003';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');
        --
        merge into qtc_buss_evl_bm_fo_unit tg
        using (select t2.EVALUATE_MAIN_ID,
                      t1.ICG_NO,
                      t1.RI_POLICY_NO,
                      t1.RI_ENDORSE_SEQ_NO,
                      t1.RI_STATEMENT_NO,
                      sum(t1.PAID_PREMIUM * -1) PAID,
                      sum(t1.COMMISSION) iacf
               from qtc_buss_evaluate_main t2,
                    qtc_DAP_FO_PREMIUM_PAID t1
               where t2.VERSION_NO = p_version
                 and t2.BUSINESS_MODEL = 'F'
                 and t2.BUSINESS_DIRECTION = 'O'
                 and T1.entity_id = t2.entity_id
                 and T1.YEAR_MONTH = t2.YEAR_MONTH
                 and t1.LOA_CODE = t2.LOA_CODE
               group by t2.EVALUATE_MAIN_ID,
                        t1.ICG_NO,
                        t1.RI_POLICY_NO,
                        t1.RI_ENDORSE_SEQ_NO,
                        t1.RI_STATEMENT_NO) sc
        on (tg.EVALUATE_MAIN_ID = sc.EVALUATE_MAIN_ID and tg.ICG_NO = sc.ICG_NO
            and tg.RI_POLICY_NO = sc.RI_POLICY_NO and tg.RI_ENDORSE_SEQ_NO = sc.RI_ENDORSE_SEQ_NO
            and tg.RI_STATEMENT_NO = sc.RI_STATEMENT_NO)
        when matched then
            update
            set cur_premium = sc.PAID,
                cur_iacf = sc.iacf;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_FO_004';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

       insert into qtc_buss_evl_bm_fo_unit_cf(unit_cf_id, version_no, model_def_id,
                                                   evaluate_approach, evaluate_main_id, icg_no, RI_POLICY_NO,
                                                   RI_ENDORSE_SEQ_NO, RI_STATEMENT_NO, NEW_YM, NEW_YEAR,
                                                   year_month, exp_ym, dev_period, ed_premium, ue_premium,
                                                   recv_premium, maintenance_fee, ultimate_loss,
                                                   brokerage_fee, adj_commission)
            select qtc_seq_buss_evl_bm_fo_unit_cf.nextval,
                   mt.VERSION_NO,
                   mt.MODEL_DEF_ID,
                   ut.EVALUATE_APPROACH,
                   mt.EVALUATE_MAIN_ID,
                   ut.icg_no,
                   ut.RI_POLICY_NO,
                   ut.RI_ENDORSE_SEQ_NO,
                   ut.RI_STATEMENT_NO,
                   ut.NEW_YM,
                   ut.NEW_YEAR,
                   mt.YEAR_MONTH,
                   to_char(add_months(to_date(mt.YEAR_MONTH, 'yyyymm'), t3.DEV_PERIOD), 'yyyymm'),
                   t3.DEV_PERIOD,
                   CASE
                       WHEN T3.DEV_PERIOD = 0 THEN ut.PREMIUM * ut.LP_CUR_END_REMAIN_UN_RATE - T3.UE_PREMIUM
                       ELSE T3.ED_PREMIUM END,
                   t3.UE_PREMIUM,
                   t3.RECV_PREMIUM,
                   T3.MAINTENANCE_FEE*-1,
                   T3.ULTIMATE_LOSS*-1,
                   T3.BROKERAGE_FEE*-1,
                   t3.ADJ_COMMISSION*-1
            from qtc_buss_evaluate_main mt,
                 qtc_buss_evl_bm_fo_unit ut,
                 QTC_DAP_ECF_FO_ICU_CF t3
            where mt.VERSION_NO = p_version
              and mt.BUSINESS_DIRECTION = 'O'
              AND MT.BUSINESS_MODEL = 'F'
              and ut.EVALUATE_MAIN_ID = mt.EVALUATE_MAIN_ID
              and t3.ENTITY_ID = mt.ENTITY_ID
              and t3.YEAR_MONTH = mt.YEAR_MONTH
              and t3.RI_POLICY_NO = ut.RI_POLICY_NO
              and t3.RI_ENDORSE_SEQ_NO = ut.RI_ENDORSE_SEQ_NO;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_FO_005';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_BUSS_EVL_BM_ICG_RATE(icg_rate_id, version_no, evl_main_id, loa_code,
                                             icg_no, dev_period, flt1)
        select qtc_SEQ_BUSS_EVL_BM_ICG_RATE.nextval,
               MT.VERSION_NO,
               MT.EVALUATE_MAIN_ID,
               MT.LOA_CODE,
               t2.ICG_NO,
               t3.DEV_NO,
               T3.CSM_RATE
        from atruser.atr_BUSS_LRC_ACTION t1,
             atruser.atr_BUSS_FO_LRC_ICG_CALC t2,
             atruser.atr_BUSS_FO_LRC_ICG_CALC_DETAIL t3,
             qtc_buss_evaluate_main mt
        where t1.action_no = t2.action_no
          and t2.id = t3.MAIN_ID
          and t1.currency_code = mt.currency_code
          and t1.entity_id = mt.entity_id
          and t1.CONFIRM_IS = '1'
          and t1.YEAR_MONTH = mt.YEAR_MONTH
          and t2.LOA_CODE = mt.LOA_CODE
          and mt.VERSION_NO = p_version
          and mt.BUSINESS_DIRECTION = 'O'
          AND MT.BUSINESS_MODEL = 'F';

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_FO_007';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_BUSS_EVL_ICG_CUR(icg_cur_id, version_no, evl_ym, buss_model,
                                         model_def_id, loa_code, evl_main_id, icg_no, cr_premium, CR_IACF)

        select qtc_seq_BUSS_EVL_ICG_CUR.nextval,
               ttt.VERSION_NO,
               ttt.YEAR_MONTH,
               'FO' ,
               ttt.MODEL_DEF_ID,
               ttt.LOA_CODE,
               ttt.EVALUATE_MAIN_ID,
               ttt.ICG_NO,
               ttt.premium * -1,
               ttt.iacf
        from (select t2.MODEL_DEF_ID,
                     t2.YEAR_MONTH,
                     t2.LOA_CODE,
                     t2.EVALUATE_MAIN_ID,
                     t2.VERSION_NO,
                     t1.ICG_NO,
                     sum(t1.PAID_PREMIUM) premium,
                     sum(T1.COMMISSION)   iacf
              from qtc_DAP_FO_PREMIUM_PAID t1,
                   qtc_buss_evaluate_main t2
              where T1.entity_id = t2.entity_id
                and T1.YEAR_MONTH = t2.YEAR_MONTH
                and t1.LOA_CODE = t2.LOA_CODE
                and t2.BUSINESS_MODEL = 'F'
                and t2.BUSINESS_DIRECTION = 'O'
                and t2.VERSION_NO = p_version
              group by t2.MODEL_DEF_ID,
                       t2.YEAR_MONTH,
                       t2.LOA_CODE,
                       t2.EVALUATE_MAIN_ID,
                       t2.VERSION_NO,
                       t1.ICG_NO) ttt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        proc_evl_log_run(null, p_version, v_node, 'S');

    EXCEPTION
        WHEN OTHERS THEN
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, sqlerrm, 'S');
            proc_evl_log_run(null, p_version, v_node, 'E');

            raise_application_error(-20002, SQLERRM);
    END proc_evl_bm_fo_cf;

    procedure proc_evl_bm_to_cf(p_version varchar2) is

        v_node    varchar(32);
        v_node_dt varchar(32);

    BEGIN

        v_node := 'RLCF_TO';
        proc_evl_log_run(null, p_version, v_node, 'B');

        v_node_dt := 'RLCF_TO_001';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_bm_to_unit
            (unit_ID,
             VERSION_NO,
             model_def_id,
             YEAR_MONTH,
             EVALUATE_MAIN_ID,
             treaty_no,
             loa_code,
             portfolio_no,
             icg_no,
             evaluate_approach,
             effective_date,
             expiry_date,
             contract_date,
             new_ym,
             new_year,
             premium,
             CUR_END_REMAIN_UN_RATE,
             LP_CUR_END_REMAIN_UN_RATE,
             ACCUMULATED_EARNED_RATE,
             RPT_PER_REMAIN_UN_RATE)
            select qtc_seq_buss_evl_bm_ti_unit.nextval,
                   mt.VERSION_NO,
                   mt.MODEL_DEF_ID,
                   mt.YEAR_MONTH,
                   mt.EVALUATE_MAIN_ID,
                   t2.treaty_no,
                   t2.loa_code,
                   t2.portfolio_no,
                   t2.icg_no,
                   t2.evaluate_approach,
                   t2.EFFECTIVE_DATE,
                   t2.EXPIRY_DATE,
                   t2.contract_date,
                   to_char(t2.contract_date, 'yyyymm'),
                   to_char(t2.contract_date, 'yyyy'),
                   t2.premium,
                   T2.CUR_END_REMAIN_UN_RATE,
                   t2.LP_CUR_END_REMAIN_UN_RATE,
                   T2.ACCUMULATED_EARNED_RATE,
                   t2.RPT_PER_REMAIN_UN_RATE
            from qtc_buss_evaluate_main mt ,
                 QTC_DAP_ECF_TO_ICU t2
            where  mt.VERSION_NO = p_version
              and mt.BUSINESS_MODEL = 'T'
              and mt.BUSINESS_DIRECTION = 'O'
              and t2.currency_code = mt.currency_code
              and t2.entity_id = mt.entity_id
              and t2.YEAR_MONTH = mt.YEAR_MONTH
              and t2.LOA_CODE = mt.LOA_CODE ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_TO_002';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_BUSS_EVL_ICG_MSG(ICG_MSG_ID, version_no, batch_no, BUS_DIR,
                                         evaluate_approach, model_def_id, evl_ym, evl_main_id,
                                         loa_code, icg_no, CAL_TYPE, CR_PREMIUM, PREMIUM, unit_num,
                                         rpt_start, rpt_end, rpt_bwt, rpt_num)
        select qtc_SEQ_BUSS_EVL_ICG_MSG.nextval,
               pt.VERSION_NO,
               1,
               pt.buss_dir ,
               PT.EVALUATE_APPROACH,
               pt.MODEL_DEF_ID,
               pt.YEAR_MONTH,
               pt.EVALUATE_MAIN_ID,
               pt.LOA_CODE,
               PT.ICG_NO,
               'Lrc' ,
               pt.NEW_PREMIUM,
               pt.PREMIUM ,
               pt.unit_num,
               TO_CHAR(TRUNC(TO_DATE(pt.YEAR_MONTH, 'YYYYMM'), 'YEAR'), 'YYYYMM'),
               TO_CHAR(ADD_MONTHS(TRUNC(TO_DATE(pt.YEAR_MONTH, 'YYYYMM'), 'YEAR'), 11), 'YYYYMM'),
               MONTHS_BETWEEN(TO_DATE(pt.YEAR_MONTH, 'YYYYMM'), TRUNC(TO_DATE(pt.YEAR_MONTH, 'YYYYMM'), 'YEAR')) +
               1,
               12
        from (SELECT mt.VERSION_NO,
                     mt.MODEL_DEF_ID,
                     mt.BUSINESS_MODEL||mt.BUSINESS_DIRECTION buss_dir,
                     mt.YEAR_MONTH,
                     mt.LOA_CODE,
                     mt.EVALUATE_MAIN_ID,
                     UT.EVALUATE_APPROACH,
                     UT.ICG_NO,
                     sum(case when ut.NEW_YM = mt.YEAR_MONTH then ut.PREMIUM end) NEW_PREMIUM,
                     sum(ut.PREMIUM)                                              PREMIUM,
                     count(ut.UNIT_ID)                                            unit_num
              FROM qtc_BUSS_EVALUATE_MAIN mt,
                   qtc_buss_evl_bm_to_unit UT
              WHERE mt.VERSION_NO = p_version
                and mt.BUSINESS_MODEL = 'T'
                and mt.BUSINESS_DIRECTION = 'O'
                and UT.EVALUATE_MAIN_ID = mt.EVALUATE_MAIN_ID
              GROUP BY mt.VERSION_NO,
                       mt.MODEL_DEF_ID,
                       mt.BUSINESS_MODEL||mt.BUSINESS_DIRECTION ,
                       mt.YEAR_MONTH,
                       mt.LOA_CODE,
                       mt.EVALUATE_MAIN_ID,
                       UT.EVALUATE_APPROACH,
                       UT.ICG_NO) pt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_TO_003';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');
        --
        merge into qtc_buss_evl_bm_to_unit bp
        using (select t2.VERSION_NO,
                      t2.MODEL_DEF_ID,
                      t1.ICG_NO,
                      t1.TREATY_NO,
                      sum(t1.PAID_PREMIUM * -1) premium,
                      sum(t1.COMMISSION) iacf
               from qtc_DAP_TO_PREMIUM_PAID t1,
                    qtc_buss_evaluate_main t2
               where T1.entity_id = t2.entity_id
                 and T1.YEAR_MONTH = t2.YEAR_MONTH
                 and t1.LOA_CODE = t2.LOA_CODE
                 and t2.BUSINESS_MODEL = 'T'
                 and t2.BUSINESS_DIRECTION = 'O'
                 and t2.VERSION_NO = p_version
               group by t2.VERSION_NO, t2.MODEL_DEF_ID, t1.ICG_NO, t1.TREATY_NO) sc
        on (bp.VERSION_NO = sc.VERSION_NO and bp.MODEL_DEF_ID = sc.MODEL_DEF_ID and bp.ICG_NO = sc.ICG_NO
            and bp.TREATY_NO = sc.TREATY_NO)
        when matched then
            update
            set cur_premium = sc.premium,
                cur_iacf = sc.iacf;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_TO_004';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_bm_to_unit_cf(unit_cf_id, version_no, model_def_id,
                                                   evaluate_approach, evaluate_main_id, icg_no, TREATY_NO, NEW_YEAR,
                                                   NEW_YM,
                                                   year_month, exp_ym, dev_period, ed_premium, ue_premium,
                                                   recv_premium, maintenance_fee, ultimate_loss,
                                                   brokerage_fee, adj_commission)
            select qtc_seq_buss_evl_bm_to_unit_cf.nextval,
                   mt.VERSION_NO,
                   mt.MODEL_DEF_ID,
                   ut.EVALUATE_APPROACH,
                   mt.EVALUATE_MAIN_ID,
                   ut.icg_no,
                   ut.TREATY_NO,
                   ut.NEW_YEAR,
                   ut.NEW_YM,
                   mt.YEAR_MONTH,
                   to_char(add_months(to_date(mt.YEAR_MONTH, 'yyyymm'), t3.DEV_PERIOD), 'yyyymm'),
                   t3.DEV_PERIOD,
                   CASE
                       WHEN T3.DEV_PERIOD = 0 THEN ut.PREMIUM * ut.LP_CUR_END_REMAIN_UN_RATE - T3.UE_PREMIUM
                       ELSE T3.ED_PREMIUM END,
                   t3.UE_PREMIUM,
                   t3.RECV_PREMIUM,
                   T3.MAINTENANCE_FEE*-1,
                   T3.ULTIMATE_LOSS*-1,
                   T3.BROKERAGE_FEE*-1,
                   t3.ADJ_COMMISSION*-1
            from qtc_buss_evaluate_main mt,
                 qtc_buss_evl_bm_to_unit ut,
                 QTC_DAP_ECF_TO_ICU_CF t3
            where mt.VERSION_NO = p_version
              and mt.BUSINESS_DIRECTION = 'O'
              AND MT.BUSINESS_MODEL = 'T'
              and ut.EVALUATE_MAIN_ID = mt.EVALUATE_MAIN_ID
              and t3.ENTITY_ID = mt.ENTITY_ID
              and t3.YEAR_MONTH = mt.YEAR_MONTH
              and t3.TREATY_NO = ut.TREATY_NO;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_TO_005';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        merge into qtc_buss_evl_bm_to_unit_cf tg
            using ( select t1.VERSION_NO , t1.MODEL_DEF_ID, t3.ICG_NO, t3.TREATY_NO, t3.DEV_PERIOD, t3.ED_PREMIUM, t3.ED_PREMIUM_CUM
                    from  qtc_buss_evaluate_main t1 ,
                          qtc_buss_evaluate_main t2 ,
                          qtc_buss_evl_bm_to_unit_cf t3
                    where t1.VERSION_NO = p_version
                    and t1.BUSINESS_MODEL = 'T'
                    and t1.BUSINESS_DIRECTION = 'O'
                    and t2.YEAR_MONTH = to_char(add_months(to_date(t1.YEAR_MONTH, 'yyyymm') , -1), 'yyyymm')
                    and t2.CONFIRM_IS = '1'
                    and t2.MODEL_DEF_ID = t1.MODEL_DEF_ID
                    and t2.LOA_CODE = t1.LOA_CODE
                    and t3.EVALUATE_MAIN_ID = t2.EVALUATE_MAIN_ID
                    and t3.DEV_PERIOD = 0 ) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO
               and tg.TREATY_NO = sc.TREATY_NO and tg.DEV_PERIOD = sc.DEV_PERIOD)
        when matched then
            update set ED_PREMIUM_CUM = nvl(sc.ED_PREMIUM,0) + nvl(sc.ED_PREMIUM_CUM,0),
                       ED_PREMIUM = nvl(tg.ED_PREMIUM,0) - (nvl(sc.ED_PREMIUM,0) + nvl(sc.ED_PREMIUM_CUM,0)) ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_TO_006';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_BUSS_EVL_BM_ICG_RATE(icg_rate_id, version_no, evl_main_id, loa_code,
                                             icg_no, dev_period, flt1)
        select qtc_SEQ_BUSS_EVL_BM_ICG_RATE.nextval,
               MT.VERSION_NO,
               MT.EVALUATE_MAIN_ID,
               MT.LOA_CODE,
               t2.ICG_NO,
               t3.DEV_NO,
               T3.CSM_RATE
        from atruser.atr_BUSS_LRC_ACTION t1,
             atruser.atr_BUSS_TO_LRC_ICG_CALC t2,
             atruser.atr_BUSS_TO_LRC_ICG_CALC_DETAIL t3,
             qtc_buss_evaluate_main mt
        where t1.action_no = t2.action_no
          and t2.id = t3.MAIN_ID
          and t1.currency_code = mt.currency_code
          and t1.entity_id = mt.entity_id
          and t1.CONFIRM_IS = '1'
          and t1.YEAR_MONTH = mt.YEAR_MONTH
          and t2.LOA_CODE = mt.LOA_CODE
          and mt.VERSION_NO = p_version
          and mt.BUSINESS_DIRECTION = 'O'
          AND MT.BUSINESS_MODEL = 'T';

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'RLCF_TO_007';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_BUSS_EVL_ICG_CUR(icg_cur_id, version_no, evl_ym, buss_model ,
                                         model_def_id, loa_code, evl_main_id, icg_no, cr_premium, CR_IACF)

        select qtc_seq_BUSS_EVL_ICG_CUR.nextval,
               ttt.VERSION_NO,
               ttt.YEAR_MONTH,
               'TO' ,
               ttt.MODEL_DEF_ID,
               ttt.LOA_CODE,
               ttt.EVALUATE_MAIN_ID,
               ttt.ICG_NO,
               ttt.premium,
               ttt.iacf
        from (select t2.MODEL_DEF_ID,
                     t2.YEAR_MONTH,
                     t2.LOA_CODE,
                     t2.EVALUATE_MAIN_ID,
                     t2.VERSION_NO,
                     t1.ICG_NO,
                     sum(t1.PAID_PREMIUM * -1) premium,
                     sum(T1.COMMISSION)        iacf
              from qtc_DAP_TO_PREMIUM_PAID t1,
                   qtc_buss_evaluate_main t2
              where T1.entity_id = t2.entity_id
                and T1.YEAR_MONTH = t2.YEAR_MONTH
                and t1.LOA_CODE = t2.LOA_CODE
                and t2.BUSINESS_MODEL = 'T'
                and t2.BUSINESS_DIRECTION = 'O'
                and t2.VERSION_NO = p_version
              group by t2.MODEL_DEF_ID,
                       t2.YEAR_MONTH,
                       t2.LOA_CODE,
                       t2.EVALUATE_MAIN_ID,
                       t2.VERSION_NO,
                       t1.ICG_NO) ttt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        proc_evl_log_run(null, p_version, v_node, 'S');

    EXCEPTION
        WHEN OTHERS THEN
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, sqlerrm, 'S');
            proc_evl_log_run(null, p_version, v_node, 'E');

            raise_application_error(-20002, SQLERRM);
    END proc_evl_bm_to_cf;

    procedure proc_evl_bm_lic_cf(p_version varchar2) is

        v_node    varchar(32);
        v_node_dt varchar(32);

    BEGIN

        v_node := 'LIC_CF';
        proc_evl_log_run(null, p_version, v_node, 'B');

        v_node_dt := 'LIC_CF_001';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_lic_icg_cf(icg_cf_id, version_no, model_def_id, EVALUATE_APPROACH, evaluate_main_id,
                                            loa_code, icg_no, year_month, exp_ym, dev_period,
                                            num1, num2, num3, num4, num5, num6)
        select qtc_seq_buss_evl_lic_icg_cf.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVALUATE_APPROACH,
               EVALUATE_MAIN_ID,
               LOA_CODE,
               ICG_NO,
               YEAR_MONTH,
               to_char(add_months(to_date(YEAR_MONTH, 'yyyymm'), DEV_NO), 'yyyymm'),
               DEV_NO,
               OS_PRE,
               OS_CUR,
               IBNR_PRE,
               IBNR_CUR,
               ULAE_PRE,
               ULAE_CUR
        from (select mt.VERSION_NO,
                     mt.MODEL_DEF_ID,
                     t2.EVALUATE_APPROACH,
                     MT.EVALUATE_MAIN_ID,
                     MT.LOA_CODE,
                     T2.ICG_NO,
                     mt.YEAR_MONTH,
                     t3.DEV_NO,
                     SUM(T3.OS_CUR)   OS_CUR,
                     SUM(T3.OS_PRE)   OS_PRE,
                     SUM(T3.ULAE_CUR) ULAE_CUR,
                     SUM(T3.ULAE_PRE) ULAE_PRE,
                     SUM(T3.IBNR_CUR) IBNR_CUR,
                     SUM(T3.IBNR_PRE) IBNR_PRE
              from atruser.atr_BUSS_LIC_ACTION t1,
                   atruser.atr_BUSS_DD_LIC_ICG_CALC t2,
                   atruser.atr_BUSS_DD_LIC_ICG_CALC_DETAIL t3,
                   qtc_buss_evaluate_main mt
              where mt.VERSION_NO = p_version
                and mt.BUSINESS_MODEL = 'D'
                AND MT.BUSINESS_DIRECTION = 'D'
                and t1.currency_code = mt.currency_code
                and t1.entity_id = MT.ENTITY_ID
                and t1.CONFIRM_IS = '1'
                and t1.YEAR_MONTH = mt.YEAR_MONTH
                AND T1.BUSINESS_SOURCE_CODE = 'DD'
                and t1.action_no = t2.action_no
                and t2.LOA_CODE = mt.LOA_CODE
                and t2.id = t3.MAIN_ID
              GROUP BY mt.VERSION_NO,
                       mt.MODEL_DEF_ID,
                       t2.EVALUATE_APPROACH,
                       MT.EVALUATE_MAIN_ID,
                       MT.LOA_CODE,
                       mt.YEAR_MONTH,
                       T2.ICG_NO,
                       t3.DEV_NO) TTT;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'LIC_CF_002';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_lic_icg_cf(icg_cf_id, version_no, model_def_id, EVALUATE_APPROACH, evaluate_main_id,
                                            loa_code, icg_no, year_month, exp_ym, dev_period,
                                            num1, num2, num3, num4, num5, num6)
        select qtc_seq_buss_evl_lic_icg_cf.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVALUATE_APPROACH,
               EVALUATE_MAIN_ID,
               LOA_CODE,
               ICG_NO,
               YEAR_MONTH,
               to_char(add_months(to_date(YEAR_MONTH, 'yyyymm'), DEV_NO), 'yyyymm'),
               DEV_NO,
               OS_PRE,
               OS_CUR,
               IBNR_PRE,
               IBNR_CUR,
               ULAE_PRE,
               ULAE_CUR
        from (select mt.VERSION_NO,
                     mt.MODEL_DEF_ID,
                     MT.EVALUATE_MAIN_ID,
                     MT.LOA_CODE,
                     T2.ICG_NO,
                     mt.YEAR_MONTH,
                     t2.EVALUATE_APPROACH,
                     t3.DEV_NO,
                     SUM(T3.OS_CUR)   OS_CUR,
                     SUM(T3.OS_PRE)   OS_PRE,
                     SUM(T3.ULAE_CUR) ULAE_CUR,
                     SUM(T3.ULAE_PRE) ULAE_PRE,
                     SUM(T3.IBNR_CUR) IBNR_CUR,
                     SUM(T3.IBNR_PRE) IBNR_PRE
              from atruser.atr_BUSS_LIC_ACTION t1,
                   atruser.atr_BUSS_TI_LIC_ICG_CALC t2,
                   atruser.atr_BUSS_TI_LIC_ICG_CALC_DETAIL t3,
                   qtc_buss_evaluate_main mt
              where t1.action_no = t2.action_no
                and t2.id = t3.MAIN_ID
                and t1.currency_code = mt.currency_code
                and t1.entity_id = MT.ENTITY_ID
                and t1.CONFIRM_IS = '1'
                AND T1.BUSINESS_SOURCE_CODE = 'TI'
                and t1.YEAR_MONTH = mt.YEAR_MONTH
                and t2.LOA_CODE = mt.LOA_CODE
                and mt.VERSION_NO = p_version
                and mt.BUSINESS_MODEL = 'T'
                AND MT.BUSINESS_DIRECTION = 'I'
              GROUP BY mt.VERSION_NO,
                       mt.MODEL_DEF_ID,
                       MT.EVALUATE_MAIN_ID,
                       MT.LOA_CODE,
                       mt.YEAR_MONTH,
                       t2.EVALUATE_APPROACH,
                       T2.ICG_NO,
                       t3.DEV_NO) TTT;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'LIC_CF_003';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_lic_icg_cf(icg_cf_id, version_no, model_def_id, EVALUATE_APPROACH, evaluate_main_id,
                                            loa_code, icg_no, year_month, exp_ym, dev_period,
                                            num1, num2, num3, num4, num5, num6)
        select qtc_seq_buss_evl_lic_icg_cf.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVALUATE_APPROACH,
               EVALUATE_MAIN_ID,
               LOA_CODE,
               ICG_NO,
               YEAR_MONTH,
               to_char(add_months(to_date(YEAR_MONTH, 'yyyymm'), DEV_NO), 'yyyymm'),
               DEV_NO,
               OS_PRE,
               OS_CUR,
               IBNR_PRE,
               IBNR_CUR,
               ULAE_PRE,
               ULAE_CUR
        from (select mt.VERSION_NO,
                     mt.MODEL_DEF_ID,
                     MT.EVALUATE_MAIN_ID,
                     MT.LOA_CODE,
                     T2.ICG_NO,
                     mt.YEAR_MONTH,
                     t2.EVALUATE_APPROACH,
                     t3.DEV_NO,
                     SUM(T3.OS_CUR)   OS_CUR,
                     SUM(T3.OS_PRE)   OS_PRE,
                     SUM(T3.ULAE_CUR) ULAE_CUR,
                     SUM(T3.ULAE_PRE) ULAE_PRE,
                     SUM(T3.IBNR_CUR) IBNR_CUR,
                     SUM(T3.IBNR_PRE) IBNR_PRE
              from atruser.atr_BUSS_LIC_ACTION t1,
                   atruser.atr_BUSS_FO_LIC_ICG_CALC t2,
                   atruser.atr_BUSS_FO_LIC_ICG_CALC_DETAIL t3,
                   qtc_buss_evaluate_main mt
              where t1.action_no = t2.action_no
                and t2.id = t3.MAIN_ID
                and t1.currency_code = mt.currency_code
                and t1.entity_id = MT.ENTITY_ID
                and t1.CONFIRM_IS = '1'
                AND T1.BUSINESS_SOURCE_CODE = 'FO'
                and t1.YEAR_MONTH = mt.YEAR_MONTH
                and t2.LOA_CODE = mt.LOA_CODE
                and mt.VERSION_NO = p_version
                and mt.BUSINESS_MODEL = 'F'
                AND MT.BUSINESS_DIRECTION = 'O'
              GROUP BY mt.VERSION_NO,
                       mt.MODEL_DEF_ID,
                       MT.EVALUATE_MAIN_ID,
                       MT.LOA_CODE,
                       mt.YEAR_MONTH,
                       t2.EVALUATE_APPROACH,
                       T2.ICG_NO,
                       t3.DEV_NO) TTT;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'LIC_CF_004';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_lic_icg_cf(icg_cf_id, version_no, model_def_id, EVALUATE_APPROACH, evaluate_main_id,
                                            loa_code, icg_no, year_month, exp_ym, dev_period,
                                            num1, num2, num3, num4, num5, num6)
        select qtc_seq_buss_evl_lic_icg_cf.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVALUATE_APPROACH,
               EVALUATE_MAIN_ID,
               LOA_CODE,
               ICG_NO,
               YEAR_MONTH,
               to_char(add_months(to_date(YEAR_MONTH, 'yyyymm'), DEV_NO), 'yyyymm'),
               DEV_NO,
               OS_PRE,
               OS_CUR,
               IBNR_PRE,
               IBNR_CUR,
               ULAE_PRE,
               ULAE_CUR
        from (select mt.VERSION_NO,
                     mt.MODEL_DEF_ID,
                     MT.EVALUATE_MAIN_ID,
                     MT.LOA_CODE,
                     T2.ICG_NO,
                     mt.YEAR_MONTH,
                     t2.EVALUATE_APPROACH,
                     t3.DEV_NO,
                     SUM(T3.OS_CUR)   OS_CUR,
                     SUM(T3.OS_PRE)   OS_PRE,
                     SUM(T3.ULAE_CUR) ULAE_CUR,
                     SUM(T3.ULAE_PRE) ULAE_PRE,
                     SUM(T3.IBNR_CUR) IBNR_CUR,
                     SUM(T3.IBNR_PRE) IBNR_PRE
              from atruser.atr_BUSS_LIC_ACTION t1,
                   atruser.atr_BUSS_TO_LIC_ICG_CALC t2,
                   atruser.atr_BUSS_TO_LIC_ICG_CALC_DETAIL t3,
                   qtc_buss_evaluate_main mt
              where t1.action_no = t2.action_no
                and t2.id = t3.MAIN_ID
                and t1.currency_code = mt.currency_code
                and t1.entity_id = MT.ENTITY_ID
                and t1.CONFIRM_IS = '1'
                AND T1.BUSINESS_SOURCE_CODE = 'TO'
                and t1.YEAR_MONTH = mt.YEAR_MONTH
                and t2.LOA_CODE = mt.LOA_CODE
                and mt.VERSION_NO = p_version
                and mt.BUSINESS_MODEL = 'T'
                AND MT.BUSINESS_DIRECTION = 'O'
              GROUP BY mt.VERSION_NO,
                       mt.MODEL_DEF_ID,
                       MT.EVALUATE_MAIN_ID,
                       MT.LOA_CODE,
                       mt.YEAR_MONTH,
                       t2.EVALUATE_APPROACH,
                       T2.ICG_NO,
                       t3.DEV_NO) TTT;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'LIC_CF_005';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_BUSS_EVL_ICG_MSG(ICG_MSG_ID, version_no, batch_no, BUS_DIR,
                                         evaluate_approach, model_def_id, evl_ym, evl_main_id,
                                         loa_code, icg_no, cal_type ,
                                         rpt_start, rpt_end, rpt_bwt, rpt_num, state)
        select qtc_SEQ_BUSS_EVL_ICG_MSG.nextval,
               pt.VERSION_NO,
               1,
               buss_dir ,
               PT.EVALUATE_APPROACH,
               pt.MODEL_DEF_ID,
               pt.YEAR_MONTH,
               pt.EVALUATE_MAIN_ID,
               pt.LOA_CODE,
               PT.ICG_NO,
               'Lic',
               TO_CHAR(TRUNC(TO_DATE(pt.YEAR_MONTH, 'YYYYMM'), 'YEAR'), 'YYYYMM'),
               TO_CHAR(ADD_MONTHS(TRUNC(TO_DATE(pt.YEAR_MONTH, 'YYYYMM'), 'YEAR'), 11), 'YYYYMM'),
               MONTHS_BETWEEN(TO_DATE(pt.YEAR_MONTH, 'YYYYMM'), TRUNC(TO_DATE(pt.YEAR_MONTH, 'YYYYMM'), 'YEAR')) +
               1,
               12,
               1
        from (SELECT mt.VERSION_NO,
                     mt.MODEL_DEF_ID,
                      mt.BUSINESS_MODEL||mt.BUSINESS_DIRECTION buss_dir,
                     mt.YEAR_MONTH,
                     mt.LOA_CODE,
                     mt.EVALUATE_MAIN_ID,
                     UT.EVALUATE_APPROACH,
                     UT.ICG_NO
              FROM qtc_BUSS_EVALUATE_MAIN mt,
                   qtc_buss_evl_lic_icg_cf UT
              WHERE mt.VERSION_NO = p_version
                and UT.EVALUATE_MAIN_ID = mt.EVALUATE_MAIN_ID
              GROUP BY mt.VERSION_NO,
                       mt.MODEL_DEF_ID,
                       mt.BUSINESS_MODEL||mt.BUSINESS_DIRECTION,
                       mt.YEAR_MONTH,
                       mt.LOA_CODE,
                       mt.EVALUATE_MAIN_ID,
                       UT.EVALUATE_APPROACH,
                       UT.ICG_NO) pt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        proc_evl_log_run(null, p_version, v_node, 'S');

    EXCEPTION
        WHEN OTHERS THEN
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, sqlerrm, 'S');
            proc_evl_log_run(null, p_version, v_node, 'E');

            raise_application_error(-20002, SQLERRM);
    END proc_evl_bm_lic_cf;

    procedure proc_evl_lrc_gmm_cf(p_version varchar2, p_model_id number) is

        v_node      varchar(32);
        v_node_dt   varchar(32);
        v_ym        integer ;
        v_rpt_start integer ;
        v_rpt_end   integer ;
        v_fir       integer ;
    begin

        select evl_ym,
               RPT_START,
               RPT_END,
               case when evl_ym = RPT_START then 1 else 0 end
        into v_ym , v_rpt_start, v_rpt_end , v_fir
        from qtc_BUSS_EVL_ICG_MSG
        where VERSION_NO = p_version
          and ROWNUM = 1;

        v_node := 'GMM_CF';
        proc_evl_log_run(null, p_version, v_node, 'B');


        v_node_dt := 'GMM_CF_008';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A1 DD
        insert into qtc_buss_evl_gmm_icg_cf(icg_cf_id, version_no, model_def_id, evaluate_main_id, icg_no,
                                            CF_TYPE, year_month, exp_ym, dev_period,
                                            num1, num2, num3, num4)
        select qtc_seq_buss_evl_gmm_icg_cf.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVALUATE_MAIN_ID,
               ICG_NO,
               'A1',
               YEAR_MONTH,
               EXP_YM,
               DEV_PERIOD,
               INVEST_FEE,
               ED_PREMIUM,
               RECV_PREMIUM,
               IACF_FEE
        from (select uf.VERSION_NO,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.ICG_NO,
                     uf.YEAR_MONTH,
                     uf.EXP_YM,
                     uf.DEV_PERIOD,
                     SUM(uf.INVEST_FEE)   INVEST_FEE,
                     SUM(uf.ED_PREMIUM)   ED_PREMIUM,
                     SUM(uf.RECV_PREMIUM) RECV_PREMIUM,
                     SUM(uf.IACF_FEE)     IACF_FEE
              from qtc_BUSS_EVL_BM_DD_UNIT_CF uf
              where uf.VERSION_NO = p_version
                and uf.MODEL_DEF_ID = p_model_id
                and uf.NEW_YM = to_number(uf.YEAR_MONTH)
              GROUP BY uf.VERSION_NO,
                       uf.MODEL_DEF_ID,
                       uf.EVALUATE_MAIN_ID,
                       uf.ICG_NO,
                       uf.YEAR_MONTH,
                       uf.EXP_YM,
                       uf.DEV_PERIOD);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_009';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A1 FO
        insert into qtc_buss_evl_gmm_icg_cf(icg_cf_id, version_no, model_def_id, evaluate_main_id, icg_no,
                                            CF_TYPE, year_month, exp_ym, dev_period,
                                            num1, num2, num3, num4)
        select qtc_seq_buss_evl_gmm_icg_cf.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVALUATE_MAIN_ID,
               ICG_NO,
               'A1',
               YEAR_MONTH,
               EXP_YM,
               DEV_PERIOD,
               INVEST_FEE,
               ED_PREMIUM,
               RECV_PREMIUM,
               IACF_FEE
        from (select uf.VERSION_NO,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.ICG_NO,
                     uf.YEAR_MONTH,
                     uf.EXP_YM,
                     uf.DEV_PERIOD,
                     SUM(uf.INVEST_FEE)   INVEST_FEE,
                     SUM(uf.ED_PREMIUM)   ED_PREMIUM,
                     SUM(uf.RECV_PREMIUM) RECV_PREMIUM,
                     SUM(uf.IACF_FEE)     IACF_FEE
              from qtc_BUSS_EVL_BM_FO_UNIT_CF uf
              where uf.VERSION_NO = p_version
                and uf.MODEL_DEF_ID = p_model_id
                and uf.NEW_YM = to_number(uf.YEAR_MONTH)
              GROUP BY uf.VERSION_NO,
                       uf.MODEL_DEF_ID,
                       uf.EVALUATE_MAIN_ID,
                       uf.ICG_NO,
                       uf.YEAR_MONTH,
                       uf.EXP_YM,
                       uf.DEV_PERIOD);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_010';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A1 TI
        insert into qtc_buss_evl_gmm_icg_cf(icg_cf_id, version_no, model_def_id, evaluate_main_id, icg_no,
                                            CF_TYPE, year_month, exp_ym, dev_period,
                                            num1, num2, num3, num4)
        select qtc_seq_buss_evl_gmm_icg_cf.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVALUATE_MAIN_ID,
               ICG_NO,
               'A1',
               YEAR_MONTH,
               EXP_YM,
               DEV_PERIOD,
               INVEST_FEE,
               ED_PREMIUM,
               RECV_PREMIUM,
               IACF_FEE
        from (select uf.VERSION_NO,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.ICG_NO,
                     uf.YEAR_MONTH,
                     uf.EXP_YM,
                     uf.DEV_PERIOD,
                     SUM(uf.INVEST_FEE)   INVEST_FEE,
                     SUM(uf.ED_PREMIUM)   ED_PREMIUM,
                     SUM(uf.RECV_PREMIUM) RECV_PREMIUM,
                     SUM(uf.IACF_FEE)     IACF_FEE
              from qtc_BUSS_EVL_BM_TI_UNIT_CF uf
              where uf.VERSION_NO = p_version
                and uf.MODEL_DEF_ID = p_model_id
                and uf.NEW_YM = to_number(uf.YEAR_MONTH)
              GROUP BY uf.VERSION_NO,
                       uf.MODEL_DEF_ID,
                       uf.EVALUATE_MAIN_ID,
                       uf.ICG_NO,
                       uf.YEAR_MONTH,
                       uf.EXP_YM,
                       uf.DEV_PERIOD);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_011';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A1 TO
        insert into qtc_buss_evl_gmm_icg_cf(icg_cf_id, version_no, model_def_id, evaluate_main_id, icg_no,
                                            CF_TYPE, year_month, exp_ym, dev_period,
                                            num1, num2, num3, num4)
        select qtc_seq_buss_evl_gmm_icg_cf.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVALUATE_MAIN_ID,
               ICG_NO,
               'A1',
               YEAR_MONTH,
               EXP_YM,
               DEV_PERIOD,
               INVEST_FEE,
               ED_PREMIUM,
               RECV_PREMIUM,
               IACF_FEE
        from (select uf.VERSION_NO,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.ICG_NO,
                     uf.YEAR_MONTH,
                     uf.EXP_YM,
                     uf.DEV_PERIOD,
                     SUM(uf.INVEST_FEE)   INVEST_FEE,
                     SUM(uf.ED_PREMIUM)   ED_PREMIUM,
                     SUM(uf.RECV_PREMIUM) RECV_PREMIUM,
                     SUM(uf.IACF_FEE)     IACF_FEE
              from qtc_BUSS_EVL_BM_TO_UNIT_CF uf
              where uf.VERSION_NO = p_version
                and uf.MODEL_DEF_ID = p_model_id
                and uf.NEW_YM = to_number(uf.YEAR_MONTH)
              GROUP BY uf.VERSION_NO,
                       uf.MODEL_DEF_ID,
                       uf.EVALUATE_MAIN_ID,
                       uf.ICG_NO,
                       uf.YEAR_MONTH,
                       uf.EXP_YM,
                       uf.DEV_PERIOD);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_012';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A1
        insert into qtc_BUSS_EVL_EXP_LOOS(exp_loos_id, VERSION_NO, MODEL_DEF_ID, YEAR_MONTH,
                                          EVALUATE_MAIN_ID, ICG_NO, cf_type, EXP_YM, LOOS_YM,
                                          exp_dev, loos_dev, num1, num2, num4)
        select /*+ leading(pt, ptd) */
               qtc_SEQ_BUSS_EVL_EXP_LOOS.nextval,
               gcf.VERSION_NO,
               gcf.MODEL_DEF_ID,
               GCF.YEAR_MONTH,
               GCF.EVALUATE_MAIN_ID,
               GCF.ICG_NO,
               'A1',
               to_char(add_months(to_date(gcf.YEAR_MONTH, 'yyyymm'), gcf.DEV_PERIOD), 'yyyymm'),
               to_char(add_months(to_date(gcf.YEAR_MONTH, 'yyyymm'), (gcf.DEV_PERIOD + ptd.DEV_PERIOD)), 'yyyymm'),
               gcf.DEV_PERIOD,
               ptd.DEV_PERIOD,
               gcf.NUM2 * (1 - NVL(pt.NUM11, 0)) * pt.NUM10 * ptd.FLT1 * -1,
               case when ptd.DEV_PERIOD = 0 then gcf.num2 * pt.num2 * -1 end,
               case
                   when ptd.DEV_PERIOD = 0 then
                           ((gcf.NUM2 * (1 - NVL(pt.NUM11, 0)) * pt.NUM10 * ptd.FLT1 * -1) +
                            (gcf.num2 * pt.num2 * -1)) * pt.num3
                   else (gcf.NUM2 * (1 - NVL(pt.NUM11, 0)) * pt.NUM10 * ptd.FLT1 * -1) * pt.num3 end
        from qtc_BUSS_EVL_CFG_PT pt,
             qtc_BUSS_EVL_CFG_PT_DEV ptd,
             qtc_buss_evl_gmm_icg_cf gcf
        where pt.VERSION_NO = p_version
          and pt.MODEL_DEF_ID = p_model_id
          and ptd.VERSION_NO = pt.VERSION_NO
          and ptd.MODEL_DEF_ID = pt.MODEL_DEF_ID
          and ptd.ICG_NO = pt.ICG_NO
          and gcf.VERSION_NO = pt.VERSION_NO
          and gcf.MODEL_DEF_ID = pt.MODEL_DEF_ID
          and gcf.ICG_NO = pt.ICG_NO
          and gcf.CF_TYPE = 'A1';

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_013';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A1
        merge into qtc_buss_evl_gmm_icg_cf tg
        using (select LS.VERSION_NO,
                      LS.MODEL_DEF_ID,
                      LS.EVALUATE_MAIN_ID,
                      LS.ICG_NO,
                      LS.YEAR_MONTH,
                      ls.CF_TYPE,
                      ls.LOOS_YM,
                      (ls.exp_dev + ls.loos_dev) dev_row,
                      SUM(ls.NUM1)               LOSS,
                      sum(ls.num2)               m1,
                      sum(ls.num4)               ra
               from qtc_BUSS_EVL_EXP_LOOS LS
               where LS.VERSION_NO = p_version
                 and LS.MODEL_DEF_ID = p_model_id
                 and ls.CF_TYPE = 'A1'
               GROUP BY LS.VERSION_NO,
                        LS.MODEL_DEF_ID,
                        LS.EVALUATE_MAIN_ID,
                        LS.ICG_NO,
                        LS.YEAR_MONTH,
                        ls.CF_TYPE,
                        ls.LOOS_YM,
                        (LS.exp_dev + ls.loos_dev)) sc
        on (tg.VERSION_NO = SC.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.CF_TYPE = sc.CF_TYPE
            AND TG.ICG_NO = SC.ICG_NO AND TG.DEV_PERIOD = SC.dev_row)
        WHEN MATCHED THEN
            UPDATE
            SET NUM5 = SC.LOSS,
                num6 = sc.m1,
                num8 = sc.ra
        when not matched then
            insert (icg_cf_id, version_no, model_def_id, evaluate_main_id,
                    icg_no, CF_TYPE, year_month, exp_ym, dev_period, num5, num6, num8)
            values (qtc_seq_buss_evl_gmm_icg_cf.nextval, sc.VERSION_NO, sc.MODEL_DEF_ID,
                    sc.EVALUATE_MAIN_ID, sc.ICG_NO, SC.CF_TYPE, sc.YEAR_MONTH, sc.LOOS_YM, sc.dev_row, sc.LOSS, sc.m1,
                    sc.ra);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');
        -- A2
        if v_fir = 1 then
             v_node_dt := 'GMM_CF_014';
             proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

            insert into qtc_buss_evl_gmm_icg_cf(icg_cf_id, version_no, batch_no, model_def_id,
                                                evaluate_main_id, LOA_CODE, icg_no, CF_TYPE, year_month, exp_ym, dev_period,
                                                num1, num2, num3, num4, num5, num6, num7, num8)
            select qtc_seq_buss_evl_gmm_icg_cf.nextval,
                   version_no,
                   batch_no,
                   model_def_id,
                   evaluate_main_id,
                   LOA_CODE,
                   icg_no,
                   'A2',
                   year_month,
                   exp_ym,
                   dev_period,
                   num1,
                   num2,
                   num3,
                   num4,
                   num5,
                   num6,
                   num7,
                   num8
            from qtc_buss_evl_gmm_icg_cf gcf
            where gcf.VERSION_NO = p_version
              and gcf.MODEL_DEF_ID = p_model_id
              and gcf.CF_TYPE = 'A1';

           proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

            v_node_dt := 'GMM_CF_0141';
             proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

              insert into qtc_BUSS_EVL_EXP_LOOS(exp_loos_id, VERSION_NO, MODEL_DEF_ID, YEAR_MONTH,
                                          EVALUATE_MAIN_ID, ICG_NO, cf_type, EXP_YM, LOOS_YM,
                                          exp_dev, loos_dev, num1, num2, num4)
              select qtc_SEQ_BUSS_EVL_EXP_LOOS.nextval,
                     VERSION_NO,
                     MODEL_DEF_ID,
                     YEAR_MONTH,
                     EVALUATE_MAIN_ID,
                     ICG_NO,
                     'A2',
                     EXP_YM,
                     LOOS_YM,
                     exp_dev,
                     loos_dev,
                     num1,
                     num2,
                     num4
              from qtc_BUSS_EVL_EXP_LOOS ls
               where ls.VERSION_NO = p_version
               and ls.MODEL_DEF_ID = p_model_id
               and ls.CF_TYPE = 'A1' ;

            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');


        else
             v_node_dt := 'GMM_CF_015';
             proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

             insert into qtc_buss_evl_gmm_icg_cf(icg_cf_id, version_no, model_def_id, evaluate_main_id, LOA_CODE,
                                                 year_month, exp_ym,dev_period, icg_no, CF_TYPE,
                                                 num1, num2, num3, num4, num5, num6, num7, num8)
             select qtc_seq_buss_evl_gmm_icg_cf.nextval, ttt.*
             from (select rs.VERSION_NO,
                          rs.MODEL_DEF_ID,
                          rs.EVL_MAIN_ID,
                          rs.LOA_CODE,
                          rs.EVL_YM,
                          gcf.EXP_YM,
                          MONTHS_BETWEEN(TO_DATE(gcf.EXP_YM, 'YYYYMM'), TO_DATE(rs.EVL_YM, 'YYYYMM')),
                          gcf.ICG_NO,
                          'A2',
                          sum(num1) v1,
                          sum(num2) v2,
                          sum(num3) v3,
                          sum(num4) v4,
                          sum(num5) v5,
                          sum(num6) v6,
                          sum(num7) v7,
                          sum(num8) v8
                   from qtc_BUSS_EVL_ICG_MSG rs,
                        qtc_buss_evl_gmm_icg_cf gcf
                   where rs.VERSION_NO = p_version
                     and rs.MODEL_DEF_ID = p_model_id
                     and rs.CAL_TYPE = 'Lrc'
                     and ((gcf.VERSION_NO = RS.VERSION_NO and gcf.CF_TYPE = 'A1')
                       OR (gcf.VERSION_NO = RS.VERSION_PRE and gcf.CF_TYPE = 'A2'))
                     and gcf.MODEL_DEF_ID = rs.MODEL_DEF_ID
                     and gcf.ICG_NO = rs.ICG_NO
                     and gcf.EXP_YM >= rs.EVL_YM
                   GROUP BY rs.VERSION_NO,
                            rs.MODEL_DEF_ID,
                            rs.EVL_MAIN_ID,
                            rs.LOA_CODE,
                            rs.EVL_YM,
                            gcf.EXP_YM,
                            gcf.ICG_NO) ttt;

             proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');


             v_node_dt := 'GMM_CF_0151';
             proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

              insert into qtc_BUSS_EVL_EXP_LOOS(exp_loos_id, VERSION_NO, MODEL_DEF_ID, YEAR_MONTH,
                                          EVALUATE_MAIN_ID, ICG_NO, cf_type, EXP_YM, LOOS_YM,
                                         EXP_DEV, LOOS_DEV,  num1, num2, num4)
              select qtc_SEQ_BUSS_EVL_EXP_LOOS.nextval, ttt.*
              from (select  /*+ leading(rs, ls) */
                     rs.VERSION_NO,
                     rs.MODEL_DEF_ID,
                     rs.EVL_YM,
                     rs.EVL_MAIN_ID,
                     rs.ICG_NO,
                     'A2',
                     ls.EXP_YM,
                     ls.LOOS_YM,
                     MONTHS_BETWEEN(TO_DATE(ls.EXP_YM, 'YYYYMM'), TO_DATE(rs.EVL_YM, 'YYYYMM')),
                      MONTHS_BETWEEN(TO_DATE(ls.LOOS_YM, 'YYYYMM'), TO_DATE(rs.EVL_YM, 'YYYYMM')),
                     sum(num1),
                     sum(num2),
                     sum(num4)
              from qtc_BUSS_EVL_ICG_MSG rs,
                   qtc_BUSS_EVL_EXP_LOOS ls
               where rs.VERSION_NO = p_version
                 and rs.MODEL_DEF_ID = p_model_id
               and rs.CAL_TYPE = 'Lrc'
               and ((ls.VERSION_NO = rs.VERSION_NO and ls.CF_TYPE = 'A1') or (ls.VERSION_NO = rs.VERSION_PRE and ls.CF_TYPE = 'A2') )
               and ls.MODEL_DEF_ID = rs.MODEL_DEF_ID
               and ls.ICG_NO = rs.ICG_NO
               and ls.EXP_YM >= rs.EVL_YM
               and ls.LOOS_YM >= rs.EVL_YM
               group by rs.VERSION_NO,
                     rs.MODEL_DEF_ID,
                     rs.EVL_YM,
                     rs.EVL_MAIN_ID,
                     rs.ICG_NO,
                     'A2',
                     EXP_YM,
                     LOOS_YM) ttt ;

            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');


        end if ;

        v_node_dt := 'GMM_CF_016';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A3
        insert into qtc_buss_evl_gmm_icg_cf(icg_cf_id, version_no, model_def_id,
                                            evaluate_main_id, icg_no, CF_TYPE, year_month, exp_ym, dev_period,
                                            num1, num2, num3, num4)
        select qtc_seq_buss_evl_gmm_icg_cf.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVALUATE_MAIN_ID,
               ICG_NO,
               'A3',
               YEAR_MONTH,
               EXP_YM,
               DEV_PERIOD,
               INVEST_FEE,
               ED_PREMIUM,
               RECV_PREMIUM,
               IACF_FEE
        from (select uf.VERSION_NO,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.ICG_NO,
                     uf.YEAR_MONTH,
                     uf.EXP_YM,
                     uf.DEV_PERIOD,
                     SUM(uf.INVEST_FEE)   INVEST_FEE,
                     SUM(uf.ED_PREMIUM)   ED_PREMIUM,
                     SUM(uf.RECV_PREMIUM) RECV_PREMIUM,
                     SUM(uf.IACF_FEE)     IACF_FEE
              from qtc_BUSS_EVL_BM_DD_UNIT_CF uf
              where uf.VERSION_NO = p_version
                and uf.MODEL_DEF_ID = p_model_id
                and uf.NEW_YM >= v_rpt_start
              GROUP BY uf.VERSION_NO,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.ICG_NO,
                     uf.YEAR_MONTH,
                     uf.EXP_YM,
                     uf.DEV_PERIOD);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_017';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A3
        insert into qtc_buss_evl_gmm_icg_cf(icg_cf_id, version_no, model_def_id,
                                            evaluate_main_id, icg_no, CF_TYPE, year_month, exp_ym, dev_period,
                                            num1, num2, num3, num4)
        select qtc_seq_buss_evl_gmm_icg_cf.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVALUATE_MAIN_ID,
               ICG_NO,
               'A3',
               YEAR_MONTH,
               EXP_YM,
               DEV_PERIOD,
               INVEST_FEE,
               ED_PREMIUM,
               RECV_PREMIUM,
               IACF_FEE
        from (select uf.VERSION_NO,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.ICG_NO,
                     uf.YEAR_MONTH,
                     uf.EXP_YM,
                     uf.DEV_PERIOD,
                     SUM(uf.INVEST_FEE)   INVEST_FEE,
                     SUM(uf.ED_PREMIUM)   ED_PREMIUM,
                     SUM(uf.RECV_PREMIUM) RECV_PREMIUM,
                     SUM(uf.IACF_FEE)     IACF_FEE
              from qtc_BUSS_EVL_BM_FO_UNIT_CF uf
              where uf.VERSION_NO = p_version
                and uf.MODEL_DEF_ID = p_model_id
                and uf.NEW_YM >= v_rpt_start
              GROUP BY uf.VERSION_NO,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.ICG_NO,
                     uf.YEAR_MONTH,
                     uf.EXP_YM,
                     uf.DEV_PERIOD);
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_018';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A3
        insert into qtc_buss_evl_gmm_icg_cf(icg_cf_id, version_no, model_def_id,
                                            evaluate_main_id, icg_no, CF_TYPE, year_month, exp_ym, dev_period,
                                            num1, num2, num3, num4)
        select qtc_seq_buss_evl_gmm_icg_cf.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVALUATE_MAIN_ID,
               ICG_NO,
               'A3',
               YEAR_MONTH,
               EXP_YM,
               DEV_PERIOD,
               INVEST_FEE,
               ED_PREMIUM,
               RECV_PREMIUM,
               IACF_FEE
        from (select uf.VERSION_NO,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.ICG_NO,
                     uf.YEAR_MONTH,
                     uf.EXP_YM,
                     uf.DEV_PERIOD,
                     SUM(uf.INVEST_FEE)   INVEST_FEE,
                     SUM(uf.ED_PREMIUM)   ED_PREMIUM,
                     SUM(uf.RECV_PREMIUM) RECV_PREMIUM,
                     SUM(uf.IACF_FEE)     IACF_FEE
              from qtc_BUSS_EVL_BM_TI_UNIT_CF uf
              where uf.VERSION_NO = p_version
                and uf.MODEL_DEF_ID = p_model_id
                and uf.NEW_YM >= v_rpt_start
              GROUP BY uf.VERSION_NO,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.ICG_NO,
                     uf.YEAR_MONTH,
                     uf.EXP_YM,
                     uf.DEV_PERIOD);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_019';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A3
        insert into qtc_buss_evl_gmm_icg_cf(icg_cf_id, version_no, model_def_id,
                                            evaluate_main_id, icg_no, CF_TYPE, year_month, exp_ym, dev_period,
                                            num1, num2, num3, num4)
        select qtc_seq_buss_evl_gmm_icg_cf.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVALUATE_MAIN_ID,
               ICG_NO,
               'A3',
               YEAR_MONTH,
               EXP_YM,
               DEV_PERIOD,
               INVEST_FEE,
               ED_PREMIUM,
               RECV_PREMIUM,
               IACF_FEE
        from (select uf.VERSION_NO,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.ICG_NO,
                     uf.YEAR_MONTH,
                     uf.EXP_YM,
                     uf.DEV_PERIOD,
                     SUM(uf.INVEST_FEE)   INVEST_FEE,
                     SUM(uf.ED_PREMIUM)   ED_PREMIUM,
                     SUM(uf.RECV_PREMIUM) RECV_PREMIUM,
                     SUM(uf.IACF_FEE)     IACF_FEE
              from qtc_BUSS_EVL_BM_TO_UNIT_CF uf
              where uf.VERSION_NO = p_version
                and uf.MODEL_DEF_ID = p_model_id
                and uf.NEW_YM >= v_rpt_start
              GROUP BY uf.VERSION_NO,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.ICG_NO,
                     uf.YEAR_MONTH,
                     uf.EXP_YM,
                     uf.DEV_PERIOD);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_0201';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_BUSS_EVL_EXP_LOOS(exp_loos_id, VERSION_NO, MODEL_DEF_ID, YEAR_MONTH,
                                          EVALUATE_MAIN_ID, ICG_NO, cf_type, EXP_YM, LOOS_YM,
                                          exp_dev, loos_dev, num1, num2, num4)
        select /*+ leading(pt, ptd) */
               qtc_SEQ_BUSS_EVL_EXP_LOOS.nextval,
               gcf.VERSION_NO,
               gcf.MODEL_DEF_ID,
               gcf.YEAR_MONTH,
               GCF.EVALUATE_MAIN_ID,
               GCF.ICG_NO,
               'A3',
               to_char(add_months(to_date(gcf.YEAR_MONTH, 'yyyymm'), gcf.DEV_PERIOD), 'yyyymm'),
               to_char(add_months(to_date(gcf.YEAR_MONTH, 'yyyymm'), (gcf.DEV_PERIOD + ptd.DEV_PERIOD)), 'yyyymm'),
               gcf.DEV_PERIOD,
               ptd.DEV_PERIOD,
               gcf.NUM2 * (1 - NVL(pt.NUM11, 0)) * pt.NUM10 * ptd.FLT1 * -1,
               case when ptd.DEV_PERIOD = 0 then gcf.num2 * pt.num2 * -1 end,
               case
                   when ptd.DEV_PERIOD = 0 then
                           ((gcf.NUM2 * (1 - NVL(pt.NUM11, 0)) * pt.NUM10 * ptd.FLT1 * -1) +
                            (gcf.num2 * pt.num2 * -1)) * pt.num3
                   else (gcf.NUM2 * (1 - NVL(pt.NUM11, 0)) * pt.NUM10 * ptd.FLT1 * -1) * pt.num3 end
        from qtc_BUSS_EVL_CFG_PT pt,
             qtc_BUSS_EVL_CFG_PT_DEV ptd,
             qtc_buss_evl_gmm_icg_cf gcf
        where pt.VERSION_NO = p_version
          and pt.MODEL_DEF_ID = p_model_id
          and ptd.VERSION_NO = pt.VERSION_NO
          and ptd.MODEL_DEF_ID = pt.MODEL_DEF_ID
          and ptd.ICG_NO = pt.ICG_NO
          and gcf.VERSION_NO = pt.VERSION_NO
          and gcf.MODEL_DEF_ID = pt.MODEL_DEF_ID
          and gcf.ICG_NO = pt.ICG_NO
          and gcf.CF_TYPE = 'A3'
          and gcf.DEV_PERIOD > 0 ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_0202';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_evl_gmm_icg_cf tg
        using (select LS.VERSION_NO,
                      LS.MODEL_DEF_ID,
                      LS.EVALUATE_MAIN_ID,
                      LS.ICG_NO,
                      LS.YEAR_MONTH,
                      ls.CF_TYPE,
                      ls.LOOS_YM,
                      (ls.exp_dev + ls.loos_dev) dev_row,
                      SUM(ls.NUM1)               LOSS,
                      sum(ls.num2)               m1,
                      sum(ls.num4)               ra
               from qtc_BUSS_EVL_EXP_LOOS LS
               where LS.VERSION_NO = p_version
                 and LS.MODEL_DEF_ID = p_model_id
                 and ls.CF_TYPE = 'A3'
               GROUP BY LS.VERSION_NO,
                        LS.MODEL_DEF_ID,
                        LS.EVALUATE_MAIN_ID,
                        LS.ICG_NO,
                        LS.YEAR_MONTH,
                        ls.CF_TYPE,
                        ls.LOOS_YM,
                        (LS.exp_dev + ls.loos_dev)) sc
        on (tg.VERSION_NO = SC.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.CF_TYPE = sc.CF_TYPE
            AND TG.ICG_NO = SC.ICG_NO AND TG.DEV_PERIOD = SC.dev_row)
        WHEN MATCHED THEN
            UPDATE
            SET NUM5 = SC.LOSS,
                num6 = sc.m1,
                num8 = sc.ra
        when not matched then
            insert (icg_cf_id, version_no, model_def_id, evaluate_main_id,
                    icg_no, CF_TYPE, year_month, exp_ym, dev_period, num5, num6, num8)
            values (qtc_seq_buss_evl_gmm_icg_cf.nextval, sc.VERSION_NO, sc.MODEL_DEF_ID,
                    sc.EVALUATE_MAIN_ID, sc.ICG_NO, SC.CF_TYPE, sc.YEAR_MONTH, sc.LOOS_YM, sc.dev_row, sc.LOSS, sc.m1,
                    sc.ra);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');


        -- A4
        if v_fir = 1 then

            v_node_dt := 'GMM_CF_024';
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

            insert into qtc_buss_evl_gmm_icg_cf(icg_cf_id, version_no, model_def_id,
                                                evaluate_main_id, icg_no, CF_TYPE, year_month, exp_ym, dev_period,
                                                num1, num2, num3, num4)
            select qtc_seq_buss_evl_gmm_icg_cf.nextval, ttt.*
            from (select MG.VERSION_NO,
                         MG.MODEL_DEF_ID,
                         MG.EVL_MAIN_ID,
                         MG.ICG_NO,
                         'A4',
                         MG.EVL_YM,
                         cf.EXP_YM,
                         MONTHS_BETWEEN(TO_DATE(cf.EXP_YM, 'YYYYMM'), TO_DATE(MG.EVL_YM, 'YYYYMM')),
                         SUM(CF.NUM1),
                         SUM(CF.NUM2),
                         SUM(CF.NUM3),
                         SUM(CF.NUM4)
                  from qtc_BUSS_EVL_ICG_MSG MG,
                       qtc_BUSS_EVL_GMM_ICG_CF cf
                  where mg.VERSION_NO = p_version
                    and mg.MODEL_DEF_ID = p_model_id
                    and mg.CAL_TYPE = 'Lrc'
                    and cf.VERSION_NO = mg.VERSION_PRE
                    and cf.MODEL_DEF_ID = mg.MODEL_DEF_ID
                    and cf.ICG_NO = mg.ICG_NO
                    and cf.EXP_YM >= mg.EVL_YM
                    and cf.CF_TYPE in ('A3', 'A5')
                  GROUP BY MG.VERSION_NO,
                           MG.MODEL_DEF_ID,
                           MG.EVL_MAIN_ID,
                           MG.ICG_NO,
                           MG.EVL_YM,
                           cf.EXP_YM) ttt;

            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

            v_node_dt := 'GMM_CF_025';
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
            -- A4
            insert into qtc_BUSS_EVL_EXP_LOOS(exp_loos_id, VERSION_NO, MODEL_DEF_ID, YEAR_MONTH, EVALUATE_MAIN_ID,
                                              ICG_NO, cf_type,
                                              EXP_YM, LOOS_YM,
                                              exp_dev, loos_dev, num1, num2, num4)
            select qtc_SEQ_BUSS_EVL_EXP_LOOS.nextval,
                   version_no,
                   MODEL_DEF_ID,
                   EVL_YM,
                   evl_main_id,
                   icg_no,
                   'A4',
                   exp_ym,
                   loos_ym,
                   exp_dev - 1,
                   loos_dev,
                   v1,
                   v2,
                   v3
            FROM (SELECT MS.VERSION_NO,
                         MS.MODEL_DEF_ID,
                         MS.EVL_YM,
                         MS.EVL_MAIN_ID,
                         MS.ICG_NO,
                         LS.EXP_YM,
                         LS.LOOS_YM,
                         LS.exp_dev,
                         LS.loos_dev,
                         sum(num1) v1,
                         sum(num2) v2,
                         sum(num4) v3
                  from qtc_BUSS_EVL_ICG_MSG ms,
                       qtc_BUSS_EVL_EXP_LOOS ls
                  where ms.VERSION_NO = p_version
                    and ms.MODEL_DEF_ID = p_model_id
                    and ms.CAL_TYPE = 'Lrc'
                    and ls.VERSION_NO = ms.VERSION_PRE
                    and ls.MODEL_DEF_ID = ms.MODEL_DEF_ID
                    and ls.ICG_NO = ms.ICG_NO
                    and ls.CF_TYPE in ('A3', 'A5')
                    and ls.EXP_YM >= ms.EVL_YM
                  group by MS.VERSION_NO,
                           MS.MODEL_DEF_ID,
                           MS.EVL_YM,
                           MS.EVL_MAIN_ID,
                           MS.ICG_NO,
                           LS.EXP_YM,
                           LS.LOOS_YM,
                           LS.exp_dev,
                           LS.loos_dev) ttt;

            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        else

            v_node_dt := 'GMM_CF_027';
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
            insert into qtc_buss_evl_gmm_icg_cf(icg_cf_id, version_no, batch_no, model_def_id,
                                                evaluate_main_id, LOA_CODE, icg_no, CF_TYPE, year_month, exp_ym,
                                                dev_period, num1, num2, num3, num4)
            select qtc_seq_buss_evl_gmm_icg_cf.nextval,
                   ms.version_no,
                   ms.batch_no,
                   ms.model_def_id,
                   ms.EVL_MAIN_ID,
                   ms.LOA_CODE,
                   ms.icg_no,
                   cf.CF_TYPE,
                   ms.EVL_YM,
                   cf.exp_ym,
                   cf.dev_period - 1,
                   num1,
                   num2,
                   num3,
                   num4
            from qtc_BUSS_EVL_ICG_MSG ms,
                 qtc_buss_evl_gmm_icg_cf cf
            where ms.VERSION_NO = p_version
              and ms.MODEL_DEF_ID = p_model_id
              and ms.CAL_TYPE = 'Lrc'
              and cf.VERSION_NO = ms.VERSION_PRE
              and cf.MODEL_DEF_ID = ms.MODEL_DEF_ID
              and cf.ICG_NO = ms.ICG_NO
              and cf.CF_TYPE = 'A4'
              and cf.Exp_Ym >= ms.EVL_YM;

            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

            v_node_dt := 'GMM_CF_0271';
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

            insert into qtc_BUSS_EVL_EXP_LOOS(exp_loos_id, VERSION_NO, MODEL_DEF_ID, YEAR_MONTH, EVALUATE_MAIN_ID, ICG_NO, cf_type,
                                              EXP_YM, LOOS_YM, exp_dev, loos_dev, num1, num2, num4)
            select qtc_SEQ_BUSS_EVL_EXP_LOOS.nextval,
                   ms.VERSION_NO,
                   ms.MODEL_DEF_ID,
                   MS.EVL_YM,
                   ms.EVL_MAIN_ID,
                   ms.ICG_NO,
                   cf_type,
                   EXP_YM,
                   LOOS_YM,
                   exp_dev-1,
                   loos_dev-1,
                   num1,
                   num2,
                   num4
            from qtc_BUSS_EVL_ICG_MSG ms,
                 qtc_BUSS_EVL_EXP_LOOS ls
              where ms.VERSION_NO = p_version
                and ms.MODEL_DEF_ID = p_model_id
                and ms.CAL_TYPE = 'Lrc'
                and ls.VERSION_NO = ms.VERSION_PRE
                and ls.MODEL_DEF_ID = ms.MODEL_DEF_ID
                and ls.ICG_NO = ms.ICG_NO
                and ls.CF_TYPE = 'A4'
                and ls.EXP_YM >= ms.EVL_YM
                AND ls.LOOS_YM >= ms.EVL_YM;

            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        end if;

        v_node_dt := 'GMM_CF_026';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A4
        merge into qtc_buss_evl_gmm_icg_cf tg
        using (select LS.VERSION_NO,
                      LS.MODEL_DEF_ID,
                      LS.EVALUATE_MAIN_ID,
                      LS.ICG_NO,
                      ls.CF_TYPE,
                      LS.YEAR_MONTH,
                      ls.LOOS_YM,
                      (ls.exp_dev + ls.loos_dev) dev_row,
                      SUM(ls.NUM1)               LOSS,
                      sum(ls.num2)               m1,
                      sum(ls.num4)               ra
               from qtc_BUSS_EVL_EXP_LOOS LS
               where LS.VERSION_NO = p_version
                 and LS.MODEL_DEF_ID = p_model_id
                 and ls.CF_TYPE = 'A4'
               GROUP BY LS.VERSION_NO,
                        LS.MODEL_DEF_ID,
                        LS.EVALUATE_MAIN_ID,
                        LS.ICG_NO,
                        ls.CF_TYPE,
                        LS.YEAR_MONTH,
                        ls.LOOS_YM,
                        (LS.exp_dev + ls.loos_dev)) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.CF_TYPE = sc.CF_TYPE
            AND TG.ICG_NO = SC.ICG_NO AND TG.DEV_PERIOD = SC.dev_row)
        WHEN MATCHED THEN
            UPDATE
            SET NUM5 = SC.LOSS,
                num6 = sc.m1,
                num8 = sc.ra
        when not matched then
            insert (icg_cf_id, version_no, model_def_id, evaluate_main_id,
                    icg_no, CF_TYPE, year_month, exp_ym, dev_period, num5, num6, num8)
            values (qtc_seq_buss_evl_gmm_icg_cf.nextval, sc.VERSION_NO, sc.MODEL_DEF_ID,
                    sc.EVALUATE_MAIN_ID, sc.ICG_NO, sc.CF_TYPE, sc.YEAR_MONTH, sc.LOOS_YM, sc.dev_row, sc.LOSS,
                    sc.m1,
                    sc.ra);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_0272';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        MERGE INTO qtc_buss_evl_gmm_icg_cf TG
        USING (SELECT pt.VERSION_NO, pt.MODEL_DEF_ID, pt.ICG_NO, 'A4' cf_type, pt.num3
               from qtc_BUSS_EVL_CFG_PT pt
               where pt.VERSION_NO = p_version
                 and pt.model_def_id = p_model_id) SC
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID
            and tg.ICG_NO = sc.ICG_NO AND TG.CF_TYPE = sc.cf_type)
        when matched then
            update
            set num8 = (nvl(tg.num5, 0) + nvl(tg.num6, 0)) * sc.NUM3;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_029';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A5 DD
        insert into qtc_buss_evl_gmm_icg_cf(icg_cf_id, version_no, model_def_id,
                                            evaluate_main_id, icg_no, CF_TYPE, year_month, exp_ym, dev_period,
                                            num1, num2, num3, num4)
        select qtc_seq_buss_evl_gmm_icg_cf.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVALUATE_MAIN_ID,
               ICG_NO,
               'A5',
               YEAR_MONTH,
               EXP_YM,
               DEV_PERIOD,
               INVEST_FEE,
               ED_PREMIUM,
               RECV_PREMIUM,
               IACF_FEE
        from (select uf.VERSION_NO,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.ICG_NO,
                     uf.YEAR_MONTH,
                     uf.EXP_YM,
                     uf.DEV_PERIOD,
                     SUM(uf.INVEST_FEE)   INVEST_FEE,
                     SUM(uf.ED_PREMIUM)   ED_PREMIUM,
                     SUM(uf.RECV_PREMIUM) RECV_PREMIUM,
                     SUM(uf.IACF_FEE)     IACF_FEE
              from qtc_BUSS_EVL_BM_DD_UNIT_CF uf
              where uf.VERSION_NO = p_version
                and uf.MODEL_DEF_ID = p_model_id
                and uf.NEW_YM < v_rpt_start
              GROUP BY uf.VERSION_NO,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.ICG_NO,
                     uf.YEAR_MONTH,
                     uf.EXP_YM,
                     uf.DEV_PERIOD);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_030';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A5 FO
        insert into qtc_buss_evl_gmm_icg_cf(icg_cf_id, version_no, model_def_id,
                                            evaluate_main_id, icg_no, CF_TYPE, year_month, exp_ym, dev_period,
                                            num1, num2, num3, num4)
        select qtc_seq_buss_evl_gmm_icg_cf.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVALUATE_MAIN_ID,
               ICG_NO,
               'A5',
               YEAR_MONTH,
               EXP_YM,
               DEV_PERIOD,
               INVEST_FEE,
               ED_PREMIUM,
               RECV_PREMIUM,
               IACF_FEE
        from (select uf.VERSION_NO,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.ICG_NO,
                     uf.YEAR_MONTH,
                     uf.EXP_YM,
                     uf.DEV_PERIOD,
                     SUM(uf.INVEST_FEE)   INVEST_FEE,
                     SUM(uf.ED_PREMIUM)   ED_PREMIUM,
                     SUM(uf.RECV_PREMIUM) RECV_PREMIUM,
                     SUM(uf.IACF_FEE)     IACF_FEE
              from qtc_BUSS_EVL_BM_FO_UNIT_CF uf
              where uf.VERSION_NO = p_version
                and uf.MODEL_DEF_ID = p_model_id
                and uf.NEW_YM < v_rpt_start
              GROUP BY uf.VERSION_NO,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.ICG_NO,
                     uf.YEAR_MONTH,
                     uf.EXP_YM,
                     uf.DEV_PERIOD);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_031';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A5 TI
        insert into qtc_buss_evl_gmm_icg_cf(icg_cf_id, version_no, model_def_id,
                                            evaluate_main_id, icg_no, CF_TYPE, year_month, exp_ym, dev_period,
                                            num1, num2, num3, num4)
        select qtc_seq_buss_evl_gmm_icg_cf.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVALUATE_MAIN_ID,
               ICG_NO,
               'A5',
               YEAR_MONTH,
               EXP_YM,
               DEV_PERIOD,
               INVEST_FEE,
               ED_PREMIUM,
               RECV_PREMIUM,
               IACF_FEE
        from (select uf.VERSION_NO,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.ICG_NO,
                     uf.YEAR_MONTH,
                     uf.EXP_YM,
                     uf.DEV_PERIOD,
                     SUM(uf.INVEST_FEE)   INVEST_FEE,
                     SUM(uf.ED_PREMIUM)   ED_PREMIUM,
                     SUM(uf.RECV_PREMIUM) RECV_PREMIUM,
                     SUM(uf.IACF_FEE)     IACF_FEE
              from qtc_BUSS_EVL_BM_TI_UNIT_CF uf
              where uf.VERSION_NO = p_version
                and uf.MODEL_DEF_ID = p_model_id
                and uf.NEW_YM < v_rpt_start
              GROUP BY uf.VERSION_NO,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.ICG_NO,
                     uf.YEAR_MONTH,
                     uf.EXP_YM,
                     uf.DEV_PERIOD);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_031';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A5 TO
        insert into qtc_buss_evl_gmm_icg_cf(icg_cf_id, version_no, model_def_id,
                                            evaluate_main_id, icg_no, CF_TYPE, year_month, exp_ym, dev_period,
                                            num1, num2, num3, num4)
        select qtc_seq_buss_evl_gmm_icg_cf.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVALUATE_MAIN_ID,
               ICG_NO,
               'A5',
               YEAR_MONTH,
               EXP_YM,
               DEV_PERIOD,
               INVEST_FEE,
               ED_PREMIUM,
               RECV_PREMIUM,
               IACF_FEE
        from (select uf.VERSION_NO,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.ICG_NO,
                     uf.YEAR_MONTH,
                     uf.EXP_YM,
                     uf.DEV_PERIOD,
                     SUM(uf.INVEST_FEE)   INVEST_FEE,
                     SUM(uf.ED_PREMIUM)   ED_PREMIUM,
                     SUM(uf.RECV_PREMIUM) RECV_PREMIUM,
                     SUM(uf.IACF_FEE)     IACF_FEE
              from qtc_BUSS_EVL_BM_TO_UNIT_CF uf
              where uf.VERSION_NO = p_version
                and uf.MODEL_DEF_ID = p_model_id
                and uf.NEW_YM < v_rpt_start
              GROUP BY uf.VERSION_NO,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.ICG_NO,
                     uf.YEAR_MONTH,
                     uf.EXP_YM,
                     uf.DEV_PERIOD);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_032';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_BUSS_EVL_EXP_LOOS(exp_loos_id, VERSION_NO, MODEL_DEF_ID, YEAR_MONTH,
                                          EVALUATE_MAIN_ID, ICG_NO, cf_type, EXP_YM, LOOS_YM,
                                          exp_dev, loos_dev, num1, num2, num4)
        select /*+ leading(pt, ptd) */
               qtc_SEQ_BUSS_EVL_EXP_LOOS.nextval,
               gcf.VERSION_NO,
               gcf.MODEL_DEF_ID,
               gcf.YEAR_MONTH,
               GCF.EVALUATE_MAIN_ID,
               GCF.ICG_NO,
               'A5',
               to_char(add_months(to_date(gcf.YEAR_MONTH, 'yyyymm'), gcf.DEV_PERIOD), 'yyyymm'),
               to_char(add_months(to_date(gcf.YEAR_MONTH, 'yyyymm'), (gcf.DEV_PERIOD + ptd.DEV_PERIOD)), 'yyyymm'),
               gcf.DEV_PERIOD,
               ptd.DEV_PERIOD,
               gcf.NUM2 * (1 - NVL(pt.NUM11, 0)) * pt.NUM10 * ptd.FLT1 * -1,
               case when ptd.DEV_PERIOD = 0 then gcf.num2 * pt.num2 * -1 end,
               case
                   when ptd.DEV_PERIOD = 0 then
                           ((gcf.NUM2 * (1 - NVL(pt.NUM11, 0)) * pt.NUM10 * ptd.FLT1 * -1) +
                            (gcf.num2 * pt.num2 * -1)) * pt.num3
                   else (gcf.NUM2 * (1 - NVL(pt.NUM11, 0)) * pt.NUM10 * ptd.FLT1 * -1) * pt.num3 end
        from qtc_BUSS_EVL_CFG_PT pt,
             qtc_BUSS_EVL_CFG_PT_DEV ptd,
             qtc_buss_evl_gmm_icg_cf gcf
        where pt.VERSION_NO = p_version
          and pt.MODEL_DEF_ID = p_model_id
          and ptd.VERSION_NO = pt.VERSION_NO
          and ptd.MODEL_DEF_ID = pt.MODEL_DEF_ID
          and ptd.ICG_NO = pt.ICG_NO
          and gcf.VERSION_NO = pt.VERSION_NO
          and gcf.MODEL_DEF_ID = pt.MODEL_DEF_ID
          and gcf.ICG_NO = pt.ICG_NO
          and gcf.CF_TYPE = 'A5'
          and gcf.DEV_PERIOD > 0;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_032';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_evl_gmm_icg_cf tg
        using (select LS.VERSION_NO,
                      LS.MODEL_DEF_ID,
                      LS.EVALUATE_MAIN_ID,
                      LS.ICG_NO,
                      ls.CF_TYPE,
                      LS.YEAR_MONTH,
                      ls.LOOS_YM,
                      (ls.exp_dev + ls.loos_dev) dev_row,
                      SUM(ls.NUM1)               LOSS,
                      sum(ls.num2)               m1,
                      sum(ls.num4)               ra
               from qtc_BUSS_EVL_EXP_LOOS LS
               where LS.VERSION_NO = p_version
                 and LS.MODEL_DEF_ID = p_model_id
                 and ls.CF_TYPE = 'A5'
               GROUP BY LS.VERSION_NO,
                        LS.MODEL_DEF_ID,
                        LS.EVALUATE_MAIN_ID,
                        LS.ICG_NO,
                        ls.CF_TYPE,
                        LS.YEAR_MONTH,
                        ls.LOOS_YM,
                        (LS.exp_dev + ls.loos_dev)) sc
        on (tg.VERSION_NO = p_version and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.CF_TYPE = sc.CF_TYPE
            AND TG.ICG_NO = SC.ICG_NO AND TG.DEV_PERIOD = SC.dev_row)
        WHEN MATCHED THEN
            UPDATE
            SET NUM5 = SC.LOSS,
                num6 = sc.m1,
                num8 = sc.ra
        when not matched then
            insert (icg_cf_id, version_no, model_def_id, evaluate_main_id,
                    icg_no, CF_TYPE, year_month, exp_ym, dev_period, num5, num6, num8)
            values (qtc_seq_buss_evl_gmm_icg_cf.nextval, sc.VERSION_NO, sc.MODEL_DEF_ID,
                    sc.EVALUATE_MAIN_ID, sc.ICG_NO, sc.CF_TYPE, sc.year_month, sc.LOOS_YM, sc.dev_row, sc.LOSS, sc.m1,
                    sc.ra);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_033';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_BUSS_EVL_ICG_CUR tg
        using (select ut.VERSION_NO,
                      ut.MODEL_DEF_ID,
                      ut.EVALUATE_MAIN_ID,
                      ut.ICG_NO,
                      sum(case
                              when (ut.NEW_YM = ut.YEAR_MONTH) or (v_rpt_start = ut.YEAR_MONTH)
                                  then ut.IACF_FEE * ut.RPT_PER_REMAIN_UN_RATE end)                 v1,
                      sum(ut.IACF_FEE * (ut.LP_CUR_END_REMAIN_UN_RATE - ut.CUR_END_REMAIN_UN_RATE)) v2,
                      sum(case
                              when (ut.NEW_YM = ut.YEAR_MONTH) or (v_rpt_start = ut.YEAR_MONTH)
                                  then ut.PREMIUM * ut.RPT_PER_REMAIN_UN_RATE end)                  v3,
                      sum(ut.PREMIUM * (ut.LP_CUR_END_REMAIN_UN_RATE - ut.CUR_END_REMAIN_UN_RATE))  v4
               from qtc_BUSS_EVL_BM_DD_UNIT ut
               where ut.VERSION_NO = p_version
                 and ut.MODEL_DEF_ID = p_model_id
               group by ut.VERSION_NO,
                        ut.MODEL_DEF_ID,
                        ut.EVALUATE_MAIN_ID,
                        ut.ICG_NO) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO)
        when matched then
            update
            set IACF       = NVL(TG.IACF, 0) + NVL(v1, 0),
                ED_IACF    = NVL(TG.ED_IACF, 0) + NVL(v2, 0),
                PREMIUM    = NVL(TG.PREMIUM, 0) + NVL(v3, 0),
                ED_PREMIUM = NVL(TG.ED_PREMIUM, 0) + NVL(v4, 0)
        when not matched then
            insert (ICG_CUR_ID, VERSION_NO, MODEL_DEF_ID, BUSS_MODEL, EVL_MAIN_ID, ICG_NO, IACF, ED_IACF, PREMIUM, ED_PREMIUM)
            values (qtc_seq_BUSS_EVL_ICG_CUR.nextval, p_version, sc.MODEL_DEF_ID, 'DD' ,
                    sc.EVALUATE_MAIN_ID, sc.ICG_NO, sc.v1, sc.v2, sc.v3, sc.v4);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_034';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_BUSS_EVL_ICG_CUR tg
        using (select ut.VERSION_NO,
                      ut.MODEL_DEF_ID,
                      ut.EVALUATE_MAIN_ID,
                      ut.ICG_NO,
                      sum(case
                              when (ut.NEW_YM = ut.YEAR_MONTH) or (v_rpt_start = ut.YEAR_MONTH)
                                  then ut.IACF_FEE * ut.RPT_PER_REMAIN_UN_RATE end)                 v1,
                      sum(ut.IACF_FEE * (ut.LP_CUR_END_REMAIN_UN_RATE - ut.CUR_END_REMAIN_UN_RATE)) v2,
                      sum(case
                              when (ut.NEW_YM = ut.YEAR_MONTH) or (v_rpt_start = ut.YEAR_MONTH)
                                  then ut.PREMIUM * ut.RPT_PER_REMAIN_UN_RATE end)                  v3,
                      sum(ut.PREMIUM * (ut.LP_CUR_END_REMAIN_UN_RATE - ut.CUR_END_REMAIN_UN_RATE))  v4
               from qtc_BUSS_EVL_BM_TI_UNIT ut
               where ut.VERSION_NO = p_version
                 and ut.MODEL_DEF_ID = p_model_id
               group by ut.VERSION_NO,
                        ut.MODEL_DEF_ID,
                        ut.EVALUATE_MAIN_ID,
                        ut.ICG_NO) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO)
        when matched then
            update
            set IACF       = NVL(TG.IACF, 0) + NVL(v1, 0),
                ED_IACF    = NVL(TG.ED_IACF, 0) + NVL(v2, 0),
                PREMIUM    = NVL(TG.PREMIUM, 0) + NVL(v3, 0),
                ED_PREMIUM = NVL(TG.ED_PREMIUM, 0) + NVL(v4, 0)
        when not matched then
            insert (ICG_CUR_ID, VERSION_NO, MODEL_DEF_ID, BUSS_MODEL, EVL_MAIN_ID, ICG_NO, IACF, ED_IACF, PREMIUM, ED_PREMIUM)
            values (qtc_seq_BUSS_EVL_ICG_CUR.nextval, p_version, sc.MODEL_DEF_ID, 'TI' ,
                    sc.EVALUATE_MAIN_ID, sc.ICG_NO, sc.v1, sc.v2, sc.v3, sc.v4);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_035';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_BUSS_EVL_ICG_CUR tg
        using (select ut.VERSION_NO,
                      ut.MODEL_DEF_ID,
                      ut.EVALUATE_MAIN_ID,
                      ut.ICG_NO,
                      sum(case
                              when (ut.NEW_YM = ut.YEAR_MONTH) or (v_rpt_start = ut.YEAR_MONTH)
                                  then ut.PREMIUM * ut.RPT_PER_REMAIN_UN_RATE end)                  v3,
                      sum(ut.PREMIUM * (ut.LP_CUR_END_REMAIN_UN_RATE - ut.CUR_END_REMAIN_UN_RATE))  v4
               from qtc_BUSS_EVL_BM_FO_UNIT ut
               where ut.VERSION_NO = p_version
                 and ut.MODEL_DEF_ID = p_model_id
               group by ut.VERSION_NO,
                        ut.MODEL_DEF_ID,
                        ut.EVALUATE_MAIN_ID,
                        ut.ICG_NO) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO)
        when matched then
            update
            set
                PREMIUM    = NVL(TG.PREMIUM, 0) + NVL(v3, 0),
                ED_PREMIUM = NVL(TG.ED_PREMIUM, 0) + NVL(v4, 0)
        when not matched then
            insert (ICG_CUR_ID, VERSION_NO, MODEL_DEF_ID, BUSS_MODEL, EVL_MAIN_ID, ICG_NO, PREMIUM, ED_PREMIUM)
            values (qtc_seq_BUSS_EVL_ICG_CUR.nextval, p_version, sc.MODEL_DEF_ID, 'FO' ,
                    sc.EVALUATE_MAIN_ID, sc.ICG_NO, sc.v3, sc.v4);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_036';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_BUSS_EVL_ICG_CUR tg
        using (select ut.VERSION_NO,
                      ut.MODEL_DEF_ID,
                      ut.EVALUATE_MAIN_ID,
                      ut.YEAR_MONTH ,
                      ut.ICG_NO,
                      sum(ut.PREMIUM) init_premium
               from qtc_BUSS_EVL_BM_TO_UNIT ut
               where ut.VERSION_NO = p_version
                 and ut.MODEL_DEF_ID = p_model_id
                 and ut.NEW_YM = ut.YEAR_MONTH
               group by ut.VERSION_NO,
                        ut.MODEL_DEF_ID,
                        ut.EVALUATE_MAIN_ID,
                        ut.YEAR_MONTH ,
                        ut.ICG_NO) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO)
        when matched then
            update
            set INIT_PREMIUM    = NVL(SC.init_premium, 0),
                INIT_UE_PREMIUM = NVL(SC.init_premium, 0)
        when not matched then
            insert (ICG_CUR_ID, VERSION_NO, MODEL_DEF_ID, BUSS_MODEL, EVL_YM,  EVL_MAIN_ID, ICG_NO, INIT_PREMIUM,
                    INIT_UE_PREMIUM)
            values (qtc_seq_BUSS_EVL_ICG_CUR.nextval, p_version, sc.MODEL_DEF_ID, 'TO', sc.YEAR_MONTH ,
                    sc.EVALUATE_MAIN_ID, sc.ICG_NO, sc.init_premium, sc.init_premium);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_037';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        if v_fir = 1 then

            merge into qtc_BUSS_EVL_ICG_CUR tg
            using (select mg.VERSION_NO,
                          mg.MODEL_DEF_ID,
                          mg.EVL_YM,
                          mg.EVL_MAIN_ID,
                          mg.ICG_NO,
                          sum(ut.PREMIUM) init_premium
                   from qtc_BUSS_EVL_ICG_MSG mg,
                        qtc_BUSS_EVL_BM_TO_UNIT ut
                   where mg.VERSION_NO = p_version
                     and mg.MODEL_DEF_ID = p_model_id
                     and mg.CAL_TYPE = 'Lrc'
                     and ut.VERSION_NO = mg.VERSION_PRE
                     and ut.MODEL_DEF_ID = mg.MODEL_DEF_ID
                     and ut.ICG_NO = mg.ICG_NO
                   group by mg.VERSION_NO,
                            mg.MODEL_DEF_ID,
                            mg.EVL_YM,
                            mg.EVL_MAIN_ID,
                            mg.ICG_NO) sc
            on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO)
            when matched then
                update
                set INIT_PREMIUM = NVL(SC.init_premium, 0)
            when not matched then
                insert (ICG_CUR_ID, VERSION_NO, MODEL_DEF_ID, EVL_YM, BUSS_MODEL, EVL_MAIN_ID, ICG_NO, INIT_PREMIUM)
                values (qtc_seq_BUSS_EVL_ICG_CUR.nextval, sc.VERSION_NO, sc.MODEL_DEF_ID, sc.EVL_YM, 'TO',
                        sc.EVL_MAIN_ID, sc.ICG_NO, sc.init_premium);

        end if;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_038';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

         if v_fir = 1 then

            merge into qtc_BUSS_EVL_ICG_CUR tg
            using (select mg.VERSION_NO,
                          mg.MODEL_DEF_ID,
                          mg.EVL_YM,
                          mg.EVL_MAIN_ID,
                          mg.ICG_NO,
                          sum(ut.UE_PREMIUM) init_ue_premium
                   from qtc_BUSS_EVL_ICG_MSG mg ,
                       qtc_BUSS_EVL_BM_TO_UNIT_CF ut
                   where mg.VERSION_NO = p_version
                     and mg.MODEL_DEF_ID = p_model_id
                     and mg.CAL_TYPE = 'Lrc'
                     and ut.VERSION_NO = mg.VERSION_PRE
                     and ut.MODEL_DEF_ID = mg.MODEL_DEF_ID
                     and ut.ICG_NO = mg.ICG_NO
                     and ut.YEAR_MONTH = ut.EXP_YM
                   group by mg.VERSION_NO,
                          mg.MODEL_DEF_ID,
                          mg.EVL_YM,
                          mg.EVL_MAIN_ID,
                          mg.ICG_NO) sc
            on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO)
            when matched then
                update
                set
                    INIT_UE_PREMIUM  = NVL(SC.init_ue_premium, 0);

        end if ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_039';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        if v_fir = 0 then

            merge into qtc_BUSS_EVL_ICG_CUR tg
            using (select ms.VERSION_NO,
                          ms.MODEL_DEF_ID,
                          ms.EVL_YM ,
                          ms.EVL_MAIN_ID ,
                          ms.ICG_NO,
                          cr.IACF,
                          cr.ED_IACF,
                          CR.INIT_PREMIUM,
                          CR.INIT_UE_PREMIUM
                   from qtc_buss_evl_icg_msg ms,
                        qtc_BUSS_EVL_ICG_CUR cr
                   where ms.VERSION_NO = p_version
                     and ms.MODEL_DEF_ID = p_model_id
                     and ms.CAL_TYPE = 'Lrc'
                     and cr.VERSION_NO = ms.VERSION_PRE
                     and cr.MODEL_DEF_ID = ms.MODEL_DEF_ID
                     and cr.ICG_NO = ms.ICG_NO
                     and cr.BUSS_MODEL = 'TO') sc
            on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO)
            when matched then
                update
                set INIT_PREMIUM    = nvl(sc.INIT_PREMIUM, 0),
                    INIT_UE_PREMIUM = nvl(sc.INIT_UE_PREMIUM, 0)
            when not matched then
                insert (ICG_CUR_ID, VERSION_NO, MODEL_DEF_ID, evl_ym, BUSS_MODEL, EVL_MAIN_ID, ICG_NO, INIT_PREMIUM, INIT_UE_PREMIUM)
                values (qtc_seq_BUSS_EVL_ICG_CUR.nextval, p_version, sc.MODEL_DEF_ID, sc.EVL_YM, 'TO' ,
                        sc.EVL_MAIN_ID, sc.ICG_NO, sc.INIT_PREMIUM, sc.INIT_UE_PREMIUM);

        end if;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_040';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_BUSS_EVL_ICG_CUR tg
        using (select ut.VERSION_NO,
                      ut.MODEL_DEF_ID,
                      ut.EVALUATE_MAIN_ID,
                      ut.ICG_NO,
                      sum(ut.PREMIUM) premium
               from qtc_BUSS_EVL_BM_TO_UNIT ut
               where ut.VERSION_NO = p_version
                 and ut.MODEL_DEF_ID = p_model_id
               group by ut.VERSION_NO,
                        ut.MODEL_DEF_ID,
                        ut.EVALUATE_MAIN_ID,
                        ut.ICG_NO) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO)
        when matched then
            update
            set PREMIUM = tg.init_ue_premium + (sc.premium - tg.init_premium);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_041';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_BUSS_EVL_ICG_CUR tg
        using (select ut.VERSION_NO,
                      ut.MODEL_DEF_ID,
                      ut.EVALUATE_MAIN_ID,
                      ut.ICG_NO,
                      sum(ut.UE_PREMIUM) UE_PREMIUM
               from qtc_BUSS_EVL_BM_TO_UNIT_CF ut
               where ut.VERSION_NO = p_version
                 and ut.MODEL_DEF_ID = p_model_id
                 and ut.YEAR_MONTH = ut.EXP_YM
               group by ut.VERSION_NO,
                        ut.MODEL_DEF_ID,
                        ut.EVALUATE_MAIN_ID,
                        ut.ICG_NO) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO)
        when matched then
            update
            set ED_PREMIUM = (tg.PREMIUM - sc.UE_PREMIUM);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_042';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        if v_fir = 0 then

            merge into qtc_BUSS_EVL_ICG_CUR tg
            using (select ms.VERSION_NO, ms.MODEL_DEF_ID, ms.ICG_NO, cr.IACF, cr.ED_IACF, CR.PREMIUM, CR.ED_PREMIUM
                   from qtc_buss_evl_icg_msg ms,
                        qtc_BUSS_EVL_ICG_CUR cr
                   where ms.VERSION_NO = p_version
                     and ms.MODEL_DEF_ID = p_model_id
                     and ms.CAL_TYPE = 'Lrc'
                     and cr.VERSION_NO = ms.VERSION_PRE
                     and cr.MODEL_DEF_ID = ms.MODEL_DEF_ID
                     and cr.ICG_NO = ms.ICG_NO
                     and cr.BUSS_MODEL in ('DD','TI','FO')) sc
            on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO)
            when matched then
                update
                set IACF       = nvl(tg.IACF, 0) + nvl(sc.IACF, 0),
                    ED_IACF    = nvl(tg.ED_IACF, 0) + nvl(sc.ED_IACF, 0),
                    PREMIUM    = nvl(tg.PREMIUM, 0) + nvl(sc.PREMIUM, 0),
                    ED_PREMIUM = nvl(tg.ED_PREMIUM, 0) + nvl(sc.ED_PREMIUM, 0);

        end if;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_043';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_BUSS_EVL_CSM_RATE(icg_rate_id, version_no,  model_def_id, evl_main_id,
                                          icg_no, dev_period, CSM_RATE)
        SELECT qtc_SEQ_BUSS_EVL_CSM_RATE.nextval,
               CR.VERSION_NO,
               CR.model_def_id,
               CR.EVL_MAIN_ID,
               CR.ICG_NO,
               0,
               CASE
                   when abs(nvl(mg.PREMIUM,0)) < 1 then 1
                   WHEN abs(nvl(cr.PREMIUM,0)) < 1 THEN 1
                   when ABS(CR.ED_PREMIUM) > ABS(CR.PREMIUM) then 1
                   ELSE CR.ED_PREMIUM / CR.PREMIUM END
        FROM  qtc_buss_evl_icg_msg mg ,
              qtc_BUSS_EVL_ICG_CUR CR
        WHERE mg.VERSION_NO = p_version
          and mg.MODEL_DEF_ID = p_model_id
          and mg.CAL_TYPE = 'Lrc'
          and CR.VERSION_NO = mg.VERSION_NO
          and CR.MODEL_DEF_ID = mg.MODEL_DEF_ID
          and cr.ICG_NO = mg.ICG_NO;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_044';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_BUSS_EVL_CSM_RATE(icg_rate_id, version_no, model_def_id, evl_main_id,
                                          icg_no, dev_period, CSM_RATE)
        SELECT qtc_SEQ_BUSS_EVL_CSM_RATE.nextval,
               MS.VERSION_NO,
               ms.model_def_id,
               MS.EVL_MAIN_ID,
               MS.ICG_NO,
               RT.DEV_PERIOD,
               RT.FLT1 * (1 - MS.CSM_RATE)
        FROM qtc_BUSS_EVL_CSM_RATE MS,
             qtc_BUSS_EVL_BM_ICG_RATE RT
        WHERE MS.VERSION_NO = p_version
          and ms.MODEL_DEF_ID = p_model_id
          AND MS.DEV_PERIOD = 0
          AND RT.EVL_MAIN_ID = MS.EVL_MAIN_ID
          AND RT.ICG_NO = MS.ICG_NO
          AND RT.DEV_PERIOD > 0;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_045';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        --
        merge into qtc_BUSS_EVL_CSM_RATE TG
        using (SELECT t.VERSION_NO, t.MODEL_DEF_ID, t.icg_no, max(t.dev_period) dev_no, SUM(CSM_RATE) V1
               FROM qtc_BUSS_EVL_CSM_RATE T
               WHERE T.VERSION_NO = p_version
                 and t.MODEL_DEF_ID = p_model_id
               group by t.VERSION_NO, t.MODEL_DEF_ID, t.icg_no) SC
        on (TG.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and TG.ICG_NO = sc.ICG_NO
            and TG.DEV_PERIOD = sc.DEV_NO)
        when matched then
            update set CSM_RATE = NVL(TG.CSM_RATE, 0) + NVL(1 - V1, 0);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_0421';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A6
        merge into qtc_buss_evl_gmm_icg_cf tg
        using (select mg.VERSION_NO,
                      mg.MODEL_DEF_ID,
                      mg.EVL_MAIN_ID,
                      mg.ICG_NO,
                      'A6'                               cf_type,
                      mg.EVL_YM,
                      cf.EXP_YM,
                      mg.CR_PREMIUM                      V2,
                      mg.CR_PREMIUM - sum(cf.UE_PREMIUM) V1
               from qtc_BUSS_EVL_ICG_MSG mg,
                    qtc_BUSS_EVL_BM_DD_UNIT_CF cf
               where mg.VERSION_NO = p_version
                 and mg.MODEL_DEF_ID = p_model_id
                 and mg.CAL_TYPE = 'Lrc'
                 and cf.VERSION_NO = mg.VERSION_NO
                 and cf.MODEL_DEF_ID = mg.MODEL_DEF_ID
                 and cf.ICG_NO = mg.ICG_NO
                 and cf.EXP_YM <= mg.RPT_END
                 and cf.NEW_YM = MG.EVL_YM
               GROUP BY mg.VERSION_NO,
                        mg.MODEL_DEF_ID,
                        mg.EVL_MAIN_ID,
                        mg.ICG_NO,
                        mg.EVL_YM,
                        cf.EXP_YM,
                        mg.CR_PREMIUM) sc
        ON (TG.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and TG.ICG_NO = sc.ICG_NO
            and TG.EXP_YM = sc.EXP_YM and tg.CF_TYPE = sc.cf_type)
        WHEN MATCHED THEN
            UPDATE
            SET num1 = nvl(tg.num1, 0) + nvl(sc.v1, 0),
                num2 = nvl(tg.num2, 0) + nvl(sc.v2, 0)
        when not matched then
            insert (icg_cf_id, version_no, model_def_id, evaluate_main_id, icg_no, CF_TYPE, year_month, exp_ym, num1,
                    num2)
            values (qtc_seq_buss_evl_gmm_icg_cf.nextval, sc.VERSION_NO, sc.MODEL_DEF_ID, sc.EVL_MAIN_ID,
                    sc.ICG_NO, sc.cf_type, sc.EVL_YM, sc.EXP_YM, sc.v1, sc.v2);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_0422';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A6
        merge into qtc_buss_evl_gmm_icg_cf tg
        using (select mg.VERSION_NO,
                      mg.MODEL_DEF_ID,
                      mg.EVL_MAIN_ID,
                      mg.ICG_NO,
                      'A6'                               cf_type,
                      mg.EVL_YM,
                      cf.EXP_YM,
                      mg.CR_PREMIUM                      V2,
                      mg.CR_PREMIUM - sum(cf.UE_PREMIUM) V1
               from qtc_BUSS_EVL_ICG_MSG mg,
                    qtc_BUSS_EVL_BM_TI_UNIT_CF cf
               where mg.VERSION_NO = p_version
                 and mg.MODEL_DEF_ID = p_model_id
                 and mg.CAL_TYPE = 'Lrc'
                 and cf.VERSION_NO = mg.VERSION_NO
                 and cf.MODEL_DEF_ID = mg.MODEL_DEF_ID
                 and cf.ICG_NO = mg.ICG_NO
                 and cf.EXP_YM <= mg.RPT_END
                 and cf.NEW_YM = MG.EVL_YM
               GROUP BY mg.VERSION_NO,
                        mg.MODEL_DEF_ID,
                        mg.EVL_MAIN_ID,
                        mg.ICG_NO,
                        mg.EVL_YM,
                        cf.EXP_YM,
                        mg.CR_PREMIUM) sc
        ON (TG.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and TG.ICG_NO = sc.ICG_NO
            and TG.EXP_YM = sc.EXP_YM and tg.CF_TYPE = sc.cf_type)
        WHEN MATCHED THEN
            UPDATE
            SET num1 = nvl(tg.num1, 0) + nvl(sc.v1, 0),
                num2 = nvl(tg.num2, 0) + nvl(sc.v2, 0)
        when not matched then
            insert (icg_cf_id, version_no, model_def_id, evaluate_main_id, icg_no, CF_TYPE, year_month, exp_ym, num1,
                    num2)
            values (qtc_seq_buss_evl_gmm_icg_cf.nextval, sc.VERSION_NO, sc.MODEL_DEF_ID, sc.EVL_MAIN_ID,
                    sc.ICG_NO, sc.cf_type, sc.EVL_YM, sc.EXP_YM, sc.v1, sc.v2);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_0423';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A6
        merge into qtc_buss_evl_gmm_icg_cf tg
        using (select mg.VERSION_NO,
                      mg.MODEL_DEF_ID,
                      mg.EVL_MAIN_ID,
                      mg.ICG_NO,
                      'A6'                               cf_type,
                      mg.EVL_YM,
                      cf.EXP_YM,
                      mg.CR_PREMIUM                      V2,
                      mg.CR_PREMIUM - sum(cf.UE_PREMIUM) V1
               from qtc_BUSS_EVL_ICG_MSG mg,
                    qtc_BUSS_EVL_BM_FO_UNIT_CF cf
               where mg.VERSION_NO = p_version
                 and mg.MODEL_DEF_ID = p_model_id
                 and mg.CAL_TYPE = 'Lrc'
                 and cf.VERSION_NO = mg.VERSION_NO
                 and cf.MODEL_DEF_ID = mg.MODEL_DEF_ID
                 and cf.ICG_NO = mg.ICG_NO
                 and cf.EXP_YM <= mg.RPT_END
                 and cf.NEW_YM = MG.EVL_YM
               GROUP BY mg.VERSION_NO,
                        mg.MODEL_DEF_ID,
                        mg.EVL_MAIN_ID,
                        mg.ICG_NO,
                        mg.EVL_YM,
                        cf.EXP_YM,
                        mg.CR_PREMIUM) sc
        ON (TG.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and TG.ICG_NO = sc.ICG_NO
            and TG.EXP_YM = sc.EXP_YM and tg.CF_TYPE = sc.cf_type)
        WHEN MATCHED THEN
            UPDATE
            SET num1 = nvl(tg.num1, 0) + nvl(sc.v1, 0),
                num2 = nvl(tg.num2, 0) + nvl(sc.v2, 0)
        when not matched then
            insert (icg_cf_id, version_no, model_def_id, evaluate_main_id, icg_no, CF_TYPE, year_month, exp_ym, num1,
                    num2)
            values (qtc_seq_buss_evl_gmm_icg_cf.nextval, sc.VERSION_NO, sc.MODEL_DEF_ID, sc.EVL_MAIN_ID,
                    sc.ICG_NO, sc.cf_type, sc.EVL_YM, sc.EXP_YM, sc.v1, sc.v2);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_0424';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A6
        merge into qtc_buss_evl_gmm_icg_cf tg
        using (select mg.VERSION_NO,
                      mg.MODEL_DEF_ID,
                      mg.EVL_MAIN_ID,
                      mg.ICG_NO,
                      'A6'                               cf_type,
                      mg.EVL_YM,
                      cf.EXP_YM,
                      mg.CR_PREMIUM                      V2,
                      mg.CR_PREMIUM - sum(cf.UE_PREMIUM) V1
               from qtc_BUSS_EVL_ICG_MSG mg,
                    qtc_BUSS_EVL_BM_TO_UNIT_CF cf
               where mg.VERSION_NO = p_version
                 and mg.MODEL_DEF_ID = p_model_id
                 and mg.CAL_TYPE = 'Lrc'
                 and cf.VERSION_NO = mg.VERSION_NO
                 and cf.MODEL_DEF_ID = mg.MODEL_DEF_ID
                 and cf.ICG_NO = mg.ICG_NO
                 and cf.EXP_YM <= mg.RPT_END
                 and cf.NEW_YM = MG.EVL_YM
               GROUP BY mg.VERSION_NO,
                        mg.MODEL_DEF_ID,
                        mg.EVL_MAIN_ID,
                        mg.ICG_NO,
                        mg.EVL_YM,
                        cf.EXP_YM,
                        mg.CR_PREMIUM) sc
        ON (TG.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and TG.ICG_NO = sc.ICG_NO
            and TG.EXP_YM = sc.EXP_YM and tg.CF_TYPE = sc.cf_type)
        WHEN MATCHED THEN
            UPDATE
            SET num1 = nvl(tg.num1, 0) + nvl(sc.v1, 0),
                num2 = nvl(tg.num2, 0) + nvl(sc.v2, 0)
        when not matched then
            insert (icg_cf_id, version_no, model_def_id, evaluate_main_id, icg_no, CF_TYPE, year_month, exp_ym, num1,
                    num2)
            values (qtc_seq_buss_evl_gmm_icg_cf.nextval, sc.VERSION_NO, sc.MODEL_DEF_ID, sc.EVL_MAIN_ID,
                    sc.ICG_NO, sc.cf_type, sc.EVL_YM, sc.EXP_YM, sc.v1, sc.v2);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        if v_fir = 1 then

            v_node_dt := 'GMM_CF_0491';
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

            MERGE INTO qtc_buss_evl_gmm_icg_cf TG
            USING (select /*+ leading(mg, ut) */
                       mg.VERSION_NO,
                       mg.MODEL_DEF_ID,
                       mg.EVL_MAIN_ID,
                       mg.ICG_NO,
                       'A6'            cf_type,
                       nvl(ut.num3, 0) V1,
                       nvl(ut.num4, 0) V2
                   from qtc_BUSS_EVL_ICG_MSG mg,
                        qtc_buss_evl_gmm_icg_cf ut
                   where mg.VERSION_NO = p_version
                     and mg.MODEL_DEF_ID = p_model_id
                     and mg.CAL_TYPE = 'Lrc'
                     and ut.VERSION_NO = mg.VERSION_PRE
                     and ut.MODEL_DEF_ID = mg.MODEL_DEF_ID
                     and ut.ICG_NO = mg.ICG_NO
                     and ut.CF_TYPE = 'A6') SC
            ON (TG.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and TG.ICG_NO = sc.ICG_NO
                and tg.CF_TYPE = sc.cf_type)
            WHEN MATCHED THEN
                UPDATE
                SET num5 = sc.v1,
                    num6 = sc.v2;

            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        end if;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_043';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        --
        merge into qtc_buss_evl_gmm_icg_cf TG
        using (SELECT mg.VERSION_NO, mg.MODEL_DEF_ID, mg.EVL_MAIN_ID, mg.icg_no, mg.EVL_YM, mg.PREMIUM, 'A6' cf_type
               FROM qtc_BUSS_EVL_ICG_MSG mg
               WHERE mg.VERSION_NO = p_version
                 and mg.MODEL_DEF_ID = p_model_id
                 and mg.CAL_TYPE = 'Lrc') SC
        on (TG.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and TG.ICG_NO = sc.ICG_NO
            and TG.EXP_YM = sc.EVL_YM and tg.CF_TYPE = sc.cf_type)
        when matched then
            update set num4 = sc.PREMIUM
        when not matched then
           insert (icg_cf_id, version_no, model_def_id,evaluate_main_id, icg_no, CF_TYPE, year_month, exp_ym, num4)
           values (qtc_seq_buss_evl_gmm_icg_cf.nextval, sc.VERSION_NO, sc.MODEL_DEF_ID, sc.EVL_MAIN_ID,
                   sc.ICG_NO, sc.cf_type, sc.EVL_YM, sc.EVL_YM, sc.PREMIUM) ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_0441';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_evl_gmm_icg_cf TG
        using (select cf.VERSION_NO, cf.MODEL_DEF_ID, cf.EVALUATE_MAIN_ID, cf.icg_no, cf.YEAR_MONTH,  'A6' cf_type , sum(cf.UE_PREMIUM) v2
               FROM qtc_BUSS_EVL_BM_DD_UNIT_CF cf
               WHERE cf.VERSION_NO = p_version
                 and cf.MODEL_DEF_ID = p_model_id
                 and cf.EXP_YM = cf.YEAR_MONTH
                 group by cf.VERSION_NO, cf.MODEL_DEF_ID, cf.EVALUATE_MAIN_ID, cf.icg_no, cf.YEAR_MONTH ) SC
        on (TG.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and TG.ICG_NO = sc.ICG_NO
            and TG.EXP_YM = sc.YEAR_MONTH and tg.CF_TYPE = sc.cf_type)
        when matched then
            update set num3 = tg.num4 - sc.v2 ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_0442';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_evl_gmm_icg_cf TG
        using (select cf.VERSION_NO, cf.MODEL_DEF_ID, cf.EVALUATE_MAIN_ID, cf.icg_no, cf.YEAR_MONTH,  'A6' cf_type , sum(cf.UE_PREMIUM) v2
               FROM qtc_BUSS_EVL_BM_FO_UNIT_CF cf
               WHERE cf.VERSION_NO = p_version
                 and cf.MODEL_DEF_ID = p_model_id
                 and cf.EXP_YM = cf.YEAR_MONTH
                 group by cf.VERSION_NO, cf.MODEL_DEF_ID, cf.EVALUATE_MAIN_ID, cf.icg_no, cf.YEAR_MONTH ) SC
        on (TG.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and TG.ICG_NO = sc.ICG_NO
            and TG.EXP_YM = sc.YEAR_MONTH and tg.CF_TYPE = sc.cf_type)
        when matched then
            update set num3 = tg.num4 - sc.v2 ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_0443';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_evl_gmm_icg_cf TG
        using (select cf.VERSION_NO, cf.MODEL_DEF_ID, cf.EVALUATE_MAIN_ID, cf.icg_no, cf.YEAR_MONTH,  'A6' cf_type , sum(cf.UE_PREMIUM) v2
               FROM qtc_BUSS_EVL_BM_TI_UNIT_CF cf
               WHERE cf.VERSION_NO = p_version
                 and cf.MODEL_DEF_ID = p_model_id
                 and cf.EXP_YM = cf.YEAR_MONTH
                 group by cf.VERSION_NO, cf.MODEL_DEF_ID, cf.EVALUATE_MAIN_ID, cf.icg_no, cf.YEAR_MONTH ) SC
        on (TG.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and TG.ICG_NO = sc.ICG_NO
            and TG.EXP_YM = sc.YEAR_MONTH and tg.CF_TYPE = sc.cf_type)
        when matched then
            update set num3 = tg.num4 - sc.v2 ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_0444';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_evl_gmm_icg_cf TG
        using (select cf.VERSION_NO, cf.MODEL_DEF_ID, cf.EVALUATE_MAIN_ID, cf.icg_no, cf.YEAR_MONTH,  'A6' cf_type , sum(cf.UE_PREMIUM) v2
               FROM qtc_BUSS_EVL_BM_TO_UNIT_CF cf
               WHERE cf.VERSION_NO = p_version
                 and cf.MODEL_DEF_ID = p_model_id
                 and cf.EXP_YM = cf.YEAR_MONTH
                 group by cf.VERSION_NO, cf.MODEL_DEF_ID, cf.EVALUATE_MAIN_ID, cf.icg_no, cf.YEAR_MONTH ) SC
        on (TG.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and TG.ICG_NO = sc.ICG_NO
            and TG.EXP_YM = sc.YEAR_MONTH and tg.CF_TYPE = sc.cf_type)
        when matched then
            update set num3 = tg.num4 - sc.v2 ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_045';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        if v_fir = 0 then

            merge into qtc_buss_evl_gmm_icg_cf TG
            using (select mg.VERSION_NO,
                          mg.MODEL_DEF_ID,
                          mg.EVL_MAIN_ID,
                          mg.EVL_YM,
                          cf.icg_no,
                          cf.EXP_YM,
                          cf.CF_TYPE,
                          num1 v1,
                          num2 v2,
                          num5 v3,
                          num6 v4
                   FROM qtc_BUSS_EVL_ICG_MSG mg,
                        qtc_buss_evl_gmm_icg_cf cf
                   WHERE mg.VERSION_NO = p_version
                     and mg.MODEL_DEF_ID = p_model_id
                     and mg.CAL_TYPE = 'Lrc'
                     and cf.VERSION_NO = mg.VERSION_PRE
                     and cf.MODEL_DEF_ID = mg.MODEL_DEF_ID
                     and cf.ICG_NO = mg.ICG_NO
                     and cf.CF_TYPE = 'A6'
                     and cf.EXP_YM >= mg.EVL_YM) SC
            on (TG.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and TG.ICG_NO = sc.ICG_NO
                and TG.EXP_YM = sc.EXP_YM and tg.CF_TYPE = sc.cf_type)
            when matched then
                update
                set num1 = nvl(tg.num1, 0) + nvl(sc.v1, 0),
                    num2 = nvl(tg.num2, 0) + nvl(sc.v2, 0),
                    num5 = nvl(tg.num5, 0) + nvl(sc.v3, 0),
                    num6 = nvl(tg.num6, 0) + nvl(sc.v4, 0)
            when not matched then
               insert (icg_cf_id, version_no, model_def_id,evaluate_main_id, icg_no, CF_TYPE, year_month, exp_ym, num1,num2,num5,num6)
               values (qtc_seq_buss_evl_gmm_icg_cf.nextval, sc.VERSION_NO, sc.MODEL_DEF_ID, sc.EVL_MAIN_ID,
                       sc.ICG_NO, sc.cf_type, sc.EVL_YM, sc.EXP_YM, sc.v1,sc.v2,sc.v3,sc.v4) ;

        end if ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_CF_046';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_evl_gmm_icg_cf TG
        using (select mg.VERSION_NO,
                      mg.MODEL_DEF_ID,
                      mg.EVL_MAIN_ID,
                      mg.EVL_YM,
                      cf.icg_no,
                      cf.CF_TYPE,
                      num3 v3,
                      num4 v4
               FROM qtc_BUSS_EVL_ICG_MSG mg,
                    qtc_buss_evl_gmm_icg_cf cf
               WHERE mg.VERSION_NO = p_version
                 and mg.MODEL_DEF_ID = p_model_id
                 and mg.CAL_TYPE = 'Lrc'
                 and cf.VERSION_NO = mg.VERSION_PRE
                 and cf.MODEL_DEF_ID = mg.MODEL_DEF_ID
                 and cf.ICG_NO = mg.ICG_NO
                 and cf.CF_TYPE = 'A6'
                 and cf.EXP_YM < mg.EVL_YM) SC
        on (TG.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and TG.ICG_NO = sc.ICG_NO
            and TG.EXP_YM = sc.EVL_YM and tg.CF_TYPE = sc.cf_type)
        when matched then
            update
            set num3 = nvl(tg.num3, 0) + nvl(sc.v3, 0);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        proc_evl_log_run(null, p_version, v_node, 'S');

    EXCEPTION
        WHEN OTHERS THEN
            proc_evl_log_run(null, p_version, v_node, 'E');
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, sqlerrm, 'S');

            raise_application_error(-20002, SQLERRM);

    end proc_evl_lrc_gmm_cf;

    procedure proc_evl_lrc_gmm_cf_pv(p_version varchar2, p_model_id number) is

        v_node      varchar(32);
        v_node_dt   varchar(32);
        v_ym        integer ;
        v_rpt_start integer ;
        v_rpt_end   integer ;
        v_fir       integer ;

    begin

        select evl_ym,
               RPT_START,
               RPT_END,
               case when evl_ym = RPT_START then 1 else 0 end
        into v_ym , v_rpt_start, v_rpt_end , v_fir
        from qtc_BUSS_EVL_ICG_MSG
        where VERSION_NO = p_version
          and ROWNUM = 1;

        v_node := 'GMM_PV';
        proc_evl_log_run(null, p_version, v_node, 'B');

        v_node_dt := 'GMM_PV_001';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A1
        insert into qtc_buss_evl_gmm_icg_pv(icg_pv_id, version_no, batch_no, model_def_id,
                                            evaluate_main_id, icg_no, year_month, cf_type,
                                            num1, num3, num4, num5, num6, num7)
        select qtc_seq_buss_evl_gmm_icg_pv.nextval,
               version_no,
               batch_no,
               model_def_id,
               evaluate_main_id,
               icg_no,
               year_month,
               'A12',
               num1,
               num3,
               num4,
               num5,
               num6,
               num7
        from (select gcf.version_no,
                     gcf.batch_no,
                     gcf.model_def_id,
                     gcf.evaluate_main_id,
                     gcf.icg_no,
                     gcf.year_month,
                     SUM(GCF.NUM1)           NUM1,
                     SUM(GCF.NUM3 * IR.FLT5) NUM3,
                     SUM(GCF.NUM4 * IR.FLT6) NUM4,
                     SUM(GCF.NUM5 * IR.FLT8) NUM5,
                     SUM(GCF.NUM6 * IR.FLT7) NUM6,
                     SUM(GCF.NUM7 * IR.FLT8) NUM7
              from qtc_buss_evl_gmm_icg_cf gcf,
                   qtc_BUSS_EVL_CFG_IR ir
              where ir.VERSION_NO = p_version
                and ir.MODEL_DEF_ID = p_model_id
                and gcf.VERSION_NO = ir.VERSION_NO
                and gcf.MODEL_DEF_ID = ir.MODEL_DEF_ID
                and gcf.CF_TYPE = 'A1'
                AND gcf.ICG_NO = ir.ICG_NO
                and gcf.DEV_PERIOD = ir.DEV_PERIOD
              GROUP BY gcf.version_no,
                       gcf.batch_no,
                       gcf.model_def_id,
                       gcf.evaluate_main_id,
                       gcf.icg_no,
                       gcf.year_month);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PV_002';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        MERGE INTO qtc_buss_evl_gmm_icg_pv TG
        USING (SELECT pt.VERSION_NO,
                      PT.MODEL_DEF_ID,
                      pt.ICG_NO,
                      'A12'                                                 CF_TYPE,
                      case
                          when pt.num9 = '0' then power(1 + ir.FLT1, -1)
                          when pt.num9 = '0.5' then power(1 + ir.FLT1, -0.5)
                          when pt.num9 = '1' then power(1 + ir.FLT1, 0) end ivt
               from qtc_BUSS_EVL_CFG_IR ir,
                    qtc_BUSS_EVL_CFG_PT pt
               where pt.VERSION_NO = p_version
                 and pt.model_def_id = p_model_id
                 and ir.VERSION_NO = PT.VERSION_NO
                 and ir.MODEL_DEF_ID = pt.MODEL_DEF_ID
                 and ir.ICG_NO = PT.ICG_NO
                 and ir.DEV_PERIOD = 0) SC
        on (tg.VERSION_NO = sc.VERSION_NO AND TG.MODEL_DEF_ID = SC.MODEL_DEF_ID
                    and tg.ICG_NO = sc.ICG_NO AND TG.CF_TYPE = sc.CF_TYPE)
        when matched then
            update
            set num1 = tg.num1 * sc.ivt,
                num3 = tg.num3 * sc.ivt,
                num4 = tg.num4 * sc.ivt,
                num5 = tg.num5 * sc.ivt,
                num6 = tg.num6 * sc.ivt,
                num7 = tg.num7 * sc.ivt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PV_003';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        MERGE INTO qtc_buss_evl_gmm_icg_pv TG
        USING (SELECT pt.EVALUATE_MAIN_ID, 'A12' CF_TYPE, pt.ICG_NO, pt.num3, pt.num14
               from qtc_BUSS_EVL_CFG_PT pt
               where pt.VERSION_NO = p_version
                 and pt.model_def_id = p_model_id) SC
        on (tg.EVALUATE_MAIN_ID = sc.EVALUATE_MAIN_ID and tg.ICG_NO = sc.ICG_NO AND TG.CF_TYPE = sc.CF_TYPE)
        when matched then
            update
            set num8 = (nvl(tg.num5, 0) + nvl(tg.num6, 0)) * sc.NUM3;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PV_004';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_gmm_free(free_id, version_no, model_def_id, evaluate_main_id, year_month,
                                          icg_no, exp_ym, dev_period, num1, num2, num5)
        select qtc_seq_buss_evl_gmm_free.nextval,
               VERSION_NO,
               model_def_id,
               EVALUATE_MAIN_ID,
               YEAR_MONTH,
               ICG_NO,
               EXP_YM,
               EXP_DEV,
               loss,
               m1,
               v1
        from (select ir.VERSION_NO,
                     ir.model_def_id,
                     ir.EVALUATE_MAIN_ID,
                     ls.YEAR_MONTH,
                     ls.ICG_NO,
                     ls.EXP_YM,
                     ls.EXP_DEV,
                     sum(case when ls.LOOS_DEV > 0 then ls.num1 * ir.FLT8 end) loss,
                     sum(ls.num2)                                              m1,
                     sum(case when ls.LOOS_DEV = 0 then ls.num1 end)           v1
              from qtc_BUSS_EVL_EXP_LOOS LS,
                   qtc_BUSS_EVL_CFG_IR ir
              where ir.VERSION_NO = P_VERSION
                and ir.MODEL_DEF_ID = p_model_id
                AND LS.VERSION_NO = ir.VERSION_NO
                and ls.MODEL_DEF_ID = ir.MODEL_DEF_ID
                and ls.ICG_NO = ir.ICG_NO
                and ls.EXP_YM <= v_rpt_end
                and ls.CF_TYPE = 'A1'
                and ir.EXP_YM = ls.LOOS_YM
              group by ir.VERSION_NO,
                       ir.model_def_id,
                       ir.EVALUATE_MAIN_ID,
                       ls.YEAR_MONTH,
                       ls.ICG_NO,
                       ls.EXP_YM,
                       ls.EXP_DEV);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PV_005';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_evl_gmm_free tg
        using (select ms.VERSION_NO,
                      ms.MODEL_DEF_ID,
                      ms.ICG_NO,
                      CASE
                          WHEN MS.EVL_YM = MS.RPT_START THEN
                              ir.DEV_PERIOD
                          ELSE DEV_PERIOD END                                                                   DEV_NO,
                      ROUND(EXP(SUM(LN(ir.flt1 + 1)) OVER (PARTITION BY ir.ICG_NO ORDER BY ir.DEV_PERIOD)), 16) r1
               from qtc_BUSS_EVL_ICG_MSG ms,
                    qtc_BUSS_EVL_CFG_IR ir
               where ms.VERSION_NO = p_version
                 and ms.MODEL_DEF_ID = p_model_id
                 and ms.CAL_TYPE = 'Lrc'
                 and ir.VERSION_NO = ms.VERSION_NO
                 and ir.MODEL_DEF_ID = ms.MODEL_DEF_ID
                 and ir.ICG_NO = ms.ICG_NO
                 and ir.DEV_PERIOD < ms.RPT_NUM
                 and ir.DEV_PERIOD > 0) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.icg_no = sc.icg_no and tg.DEV_PERIOD = sc.DEV_NO)
        when matched then
            update set num1 = nvl(tg.num1, 0) * sc.r1;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PV_006';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        update qtc_buss_evl_gmm_free fr
        set num1 = nvl(fr.num1, 0) + nvl(fr.num5, 0)
        where fr.version_no = p_version
          and fr.model_def_id = p_model_id;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PV_007';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        MERGE INTO qtc_buss_evl_gmm_free TG
        USING (SELECT pt.EVALUATE_MAIN_ID, pt.ICG_NO, pt.num3
               from qtc_BUSS_EVL_CFG_PT pt
               where pt.VERSION_NO = p_version
                 and pt.model_def_id = p_model_id) SC
        on (tg.EVALUATE_MAIN_ID = sc.EVALUATE_MAIN_ID and tg.icg_no = sc.icg_no)
        when matched then
            update set num4 = (nvl(tg.num1, 0) + nvl(tg.num2, 0)) * sc.num3;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');


        v_node_dt := 'GMM_PV_0071';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        IF v_fir = 0 then

            merge into qtc_buss_evl_gmm_free tg
            using (select ms.VERSION_NO,
                          ms.MODEL_DEF_ID,
                          ms.EVL_MAIN_ID,
                          ms.EVL_YM,
                          ms.ICG_NO,
                          fr.EXP_YM,
                          fr.DEV_PERIOD - 1 dev_no,
                          fr.num1,
                          fr.num2,
                          fr.num4
                   from qtc_BUSS_EVL_ICG_MSG ms,
                        qtc_buss_evl_gmm_free fr
                   where ms.VERSION_NO = p_version
                     and ms.MODEL_DEF_ID = p_model_id
                     and ms.CAL_TYPE = 'Lrc'
                     and fr.VERSION_NO = ms.VERSION_PRE
                     and fr.MODEL_DEF_ID = ms.MODEL_DEF_ID
                     and fr.ICG_NO = ms.ICG_NO
                     and fr.EXP_YM >= ms.EVL_YM) sc
            on (tg.VERSION_NO = sc.VERSION_NO  and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.icg_no = sc.icg_no
                    and tg.EXP_YM = sc.EXP_YM)
            when matched then
                update set num1 = nvl(tg.num1, 0) + nvl(sc.num1, 0),
                           num2 = nvl(tg.num2, 0) + nvl(sc.num2, 0),
                           num4 = nvl(tg.num4, 0) + nvl(sc.num4, 0)
            when not matched then
               insert (free_id, version_no, model_def_id, evaluate_main_id, year_month,
                                              icg_no, exp_ym, dev_period, num1, num2, num4)
               values (qtc_seq_buss_evl_gmm_free.nextval, sc.VERSION_NO, sc.MODEL_DEF_ID, sc.EVL_MAIN_ID,
                        sc.EVL_YM, sc.ICG_NO, sc.EXP_YM, sc.dev_no, sc.num1, sc.num2, sc.num4);

         end if ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PV_0072';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
         /*IF v_fir = 0 then

            merge into qtc_buss_evl_gmm_free tg
            using (select ms.VERSION_NO,
                          ms.MODEL_DEF_ID,
                          ms.EVL_MAIN_ID,
                          ms.EVL_YM,
                          ms.ICG_NO,
                          fr.num1,
                          fr.num2,
                          fr.num4
                   from qtc_BUSS_EVL_ICG_MSG ms,
                        qtc_buss_evl_gmm_free fr
                   where ms.VERSION_NO = p_version
                     and ms.MODEL_DEF_ID = p_model_id
                     and ms.CAL_TYPE = 'Lrc'
                     and fr.VERSION_NO = ms.VERSION_PRE
                     and fr.MODEL_DEF_ID = ms.MODEL_DEF_ID
                     and fr.ICG_NO = ms.ICG_NO
                     and fr.EXP_YM < ms.EVL_YM) sc
            on (tg.VERSION_NO = sc.VERSION_NO  and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.icg_no = sc.icg_no
                    and tg.EXP_YM = sc.EVL_YM)
            when matched then
                update set num1 = nvl(tg.num1, 0) + nvl(sc.num1, 0),
                           num2 = nvl(tg.num2, 0) + nvl(sc.num2, 0),
                           num4 = nvl(tg.num4, 0) + nvl(sc.num4, 0) ;

         end if ;*/

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PV_008';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A2
        insert into qtc_buss_evl_gmm_icg_pv(icg_pv_id, version_no, batch_no, model_def_id,
                                            evaluate_main_id, icg_no, year_month, cf_type,
                                            num1, num3, num4)
        select qtc_seq_buss_evl_gmm_icg_pv.nextval,
               version_no,
               batch_no,
               model_def_id,
               evaluate_main_id,
               icg_no,
               year_month,
               'A23',
               num1,
               num3,
               num4
        from (select gcf.version_no,
                     gcf.batch_no,
                     gcf.model_def_id,
                     gcf.evaluate_main_id,
                     gcf.icg_no,
                     gcf.year_month,
                     sum(gcf.num1)           num1,
                     sum(gcf.num3 * ir.FLT5) num3,
                     sum(gcf.num4 * ir.FLT6) num4
              from qtc_buss_evl_gmm_icg_cf gcf,
                   qtc_BUSS_EVL_CFG_WA_IR ir
              where gcf.VERSION_NO = p_version
                and gcf.model_def_id = p_model_id
                and gcf.DEV_PERIOD > 0
                and gcf.CF_TYPE = 'A2'
                and ir.EVALUATE_MAIN_ID = gcf.EVALUATE_MAIN_ID
                and ir.ICG_NO = gcf.ICG_NO
                and ir.DEV_PERIOD = gcf.DEV_PERIOD
              group by gcf.version_no, gcf.batch_no, gcf.model_def_id,
                       gcf.evaluate_main_id, gcf.icg_no, gcf.year_month);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PV_009';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        MERGE INTO qtc_buss_evl_gmm_icg_pv TG
        using (select /*+ leading(IR, LS) */
                      IR.VERSION_NO,
                      ir.MODEL_DEF_ID ,
                      IR.ICG_NO,
                      'A23'                  CF_TYPE,
                      SUM(LS.NUM1 * ir.FLT8) LOSS,
                      SUM(LS.NUM2 * ir.FLT7) M1,
                      SUM(LS.NUM4 * ir.FLT8) ra
               from qtc_BUSS_EVL_EXP_LOOS LS,
                    qtc_BUSS_EVL_CFG_WA_IR IR
               where IR.VERSION_NO = P_VERSION
                 and IR.MODEL_DEF_ID = p_model_id
                 and LS.VERSION_NO = ir.VERSION_NO
                 and ls.MODEL_DEF_ID = ir.MODEL_DEF_ID
                 and ls.ICG_NO = ir.ICG_NO
                 and ls.EXP_YM > to_number(ls.YEAR_MONTH)
                 and ls.LOOS_YM > to_number(ls.YEAR_MONTH)
                 and ls.CF_TYPE = 'A2'
                 and ir.EXP_YM = ls.LOOS_YM
               GROUP BY IR.VERSION_NO,
                      ir.MODEL_DEF_ID ,
                      IR.ICG_NO) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO AND TG.CF_TYPE = sc.CF_TYPE)
        when matched then
            update
            set num5 = NVL(TG.num5, 0) + NVL(sc.LOSS, 0),
                num6 = NVL(TG.num6, 0) + NVL(SC.M1, 0),
                num8 = NVL(TG.num8, 0) + NVL(sc.ra, 0);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');


        v_node_dt := 'GMM_PV_011';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A3
        insert into qtc_buss_evl_gmm_icg_pv(icg_pv_id, version_no,  model_def_id,
                                            evaluate_main_id, icg_no, year_month, cf_type,
                                            num1, num3, num4, num5, num6, num7, num8)
        select qtc_seq_buss_evl_gmm_icg_pv.nextval,
               version_no,
               model_def_id,
               evaluate_main_id,
               icg_no,
               year_month,
               'A31',
               num1,
               num3,
               num4,
               num5,
               num6,
               num7,
               num8
        from (select gcf.version_no,
                     gcf.model_def_id,
                     gcf.evaluate_main_id,
                     gcf.icg_no,
                     gcf.year_month,
                     SUM(gcf.num1)           num1,
                     SUM(GCF.NUM3 * IR.FLT5) NUM3,
                     SUM(GCF.NUM4 * IR.FLT6) NUM4,
                     SUM(GCF.NUM5 * IR.FLT8) NUM5,
                     SUM(GCF.NUM6 * IR.FLT7) NUM6,
                     SUM(GCF.NUM7 * IR.FLT8) NUM7,
                     SUM(GCF.NUM8 * IR.FLT8) NUM8
              from qtc_buss_evl_gmm_icg_cf gcf,
                   qtc_BUSS_EVL_CFG_FR_IR ir
              where ir.VERSION_NO = p_version
                and ir.model_def_id = p_model_id
                and gcf.VERSION_NO = ir.VERSION_NO
                and gcf.MODEL_DEF_ID = ir.MODEL_DEF_ID
                and gcf.ICG_NO = ir.ICG_NO
                and gcf.DEV_PERIOD = ir.DEV_PERIOD
                and gcf.CF_TYPE = 'A3'
                and gcf.DEV_PERIOD > 0
              group by gcf.version_no, gcf.batch_no, gcf.model_def_id,
                       gcf.evaluate_main_id, gcf.icg_no, gcf.year_month);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PV_012';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_gmm_icg_pv(icg_pv_id, version_no, batch_no, model_def_id,
                                            evaluate_main_id, icg_no, year_month, cf_type,
                                            num1, num3, num4, num5, num6, num7, num8)
        select qtc_seq_buss_evl_gmm_icg_pv.nextval,
               version_no,
               batch_no,
               model_def_id,
               evaluate_main_id,
               icg_no,
               year_month,
               'A33',
               num1,
               num3,
               num4,
               num5,
               num6,
               num7,
               num8
        from (select gcf.version_no,
                     gcf.batch_no,
                     gcf.model_def_id,
                     gcf.evaluate_main_id,
                     gcf.icg_no,
                     gcf.year_month,
                     sum(gcf.num1)           num1,
                     sum(gcf.num3 * ir.FLT5) num3,
                     sum(gcf.num4 * ir.FLT6) num4,
                     sum(gcf.num5 * ir.FLT8) num5,
                     sum(gcf.num6 * ir.FLT7) num6,
                     sum(gcf.num7 * ir.FLT8) num7,
                     sum(gcf.num8 * ir.FLT8) num8
              from qtc_buss_evl_gmm_icg_cf gcf,
                   qtc_BUSS_EVL_CFG_WA_IR ir
              where ir.VERSION_NO = p_version
                and ir.model_def_id = p_model_id
                and gcf.VERSION_NO = ir.VERSION_NO
                and gcf.MODEL_DEF_ID = ir.MODEL_DEF_ID
                and gcf.ICG_NO = ir.ICG_NO
                and gcf.DEV_PERIOD = ir.DEV_PERIOD
                and gcf.CF_TYPE = 'A3'
                and gcf.DEV_PERIOD > 0
              group by gcf.version_no, gcf.batch_no, gcf.model_def_id,
                       gcf.evaluate_main_id, gcf.icg_no, gcf.year_month);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PV_013';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A4
        insert into qtc_buss_evl_gmm_icg_pv(icg_pv_id, version_no, batch_no, model_def_id,
                                            evaluate_main_id, icg_no, year_month, cf_type,
                                            num1, num3, num4)
        select qtc_seq_buss_evl_gmm_icg_pv.nextval,
               version_no,
               batch_no,
               model_def_id,
               evaluate_main_id,
               icg_no,
               year_month,
               'A43',
               num1,
               num3,
               num4
        from (select gcf.version_no,
                     gcf.batch_no,
                     gcf.model_def_id,
                     gcf.evaluate_main_id,
                     gcf.icg_no,
                     gcf.year_month,
                     sum(gcf.num1)           num1,
                     sum(gcf.num3 * ir.FLT5) num3,
                     sum(gcf.num4 * ir.FLT6) num4
              from qtc_buss_evl_gmm_icg_cf gcf,
                   qtc_BUSS_EVL_CFG_WA_IR ir
              where ir.VERSION_NO = p_version
                and ir.model_def_id = p_model_id
                and gcf.VERSION_NO = ir.VERSION_NO
                and gcf.MODEL_DEF_ID = ir.MODEL_DEF_ID
                and gcf.ICG_NO = ir.ICG_NO
                and gcf.DEV_PERIOD = ir.DEV_PERIOD
                and gcf.CF_TYPE = 'A4'
                and gcf.DEV_PERIOD > 0
              group by gcf.version_no, gcf.batch_no, gcf.model_def_id,
                       gcf.evaluate_main_id, gcf.icg_no, gcf.year_month);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PV_014';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        MERGE INTO qtc_buss_evl_gmm_icg_pv TG
        using (select /*+ leading(IR, LS) */
                      ls.VERSION_NO,
                      ls.MODEL_DEF_ID,
                      ls.ICG_NO,
                      'A43'                  CF_TYPE,
                      SUM(LS.NUM1 * ir.FLT8) LOSS,
                      SUM(LS.NUM2 * ir.FLT7) M1,
                      SUM(LS.NUM4 * ir.FLT8) ra
               from qtc_BUSS_EVL_EXP_LOOS LS,
                    qtc_BUSS_EVL_CFG_WA_IR IR
               where IR.VERSION_NO = p_version
                 and IR.MODEL_DEF_ID = p_model_id
                 and ls.VERSION_NO = ir.VERSION_NO
                 and ls.MODEL_DEF_ID = ir.MODEL_DEF_ID
                 and ls.ICG_NO = ir.ICG_NO
                 and ls.EXP_YM > to_number(ls.YEAR_MONTH)
                 and ls.LOOS_YM > to_number(ls.YEAR_MONTH)
                 and ls.CF_TYPE = 'A4'
                 and ir.EXP_YM = ls.LOOS_YM
               GROUP BY ls.VERSION_NO,
                        ls.MODEL_DEF_ID,
                        ls.ICG_NO) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID
            and tg.ICG_NO = sc.ICG_NO AND TG.CF_TYPE = sc.CF_TYPE)
        when matched then
            update
            set num5 = sc.LOSS,
                num6 = SC.M1,
                num8 = sc.ra;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PV_016';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_evl_gmm_icg_pv tg
        using (select pt.VERSION_NO, pt.MODEL_DEF_ID, 'A43' CF_TYPE,  pt.ICG_NO, PT.NUM3
               from qtc_BUSS_EVL_CFG_PT pt
               where pt.VERSION_NO = p_version
                 and pt.MODEL_DEF_ID = p_model_id) sc
        on (tg.VERSION_NO = SC.VERSION_NO AND TG.MODEL_DEF_ID = SC.MODEL_DEF_ID and tg.CF_TYPE = SC.CF_TYPE AND TG.ICG_NO = SC.ICG_NO)
        WHEN MATCHED THEN
            UPDATE SET NUM8 = (NVL(TG.NUM5, 0) + NVL(TG.NUM6, 0)) * SC.NUM3;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PV_017';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- A5
        insert into qtc_buss_evl_gmm_icg_pv(icg_pv_id, version_no,  model_def_id,
                                            evaluate_main_id, icg_no, year_month, cf_type,
                                            num1, num3, num4, num5, num6, num7, num8)
        select qtc_seq_buss_evl_gmm_icg_pv.nextval,
               version_no,
               model_def_id,
               evaluate_main_id,
               icg_no,
               year_month,
               'A51',
               num1,
               num3,
               num4,
               num5,
               num6,
               num7,
               num8
        from (select gcf.version_no,
                     gcf.model_def_id,
                     gcf.evaluate_main_id,
                     gcf.icg_no,
                     gcf.year_month,
                     sum(gcf.num1)           num1,
                     sum(gcf.num3 * ir.FLT5) num3,
                     sum(gcf.num4 * ir.FLT6) num4,
                     sum(gcf.num5 * ir.FLT8) num5,
                     sum(gcf.num6 * ir.FLT7) num6,
                     sum(gcf.num7 * ir.FLT8) num7,
                     sum(gcf.num8 * ir.FLT8) num8
              from qtc_buss_evl_gmm_icg_cf gcf,
                   qtc_BUSS_EVL_CFG_FR_IR ir
              where gcf.VERSION_NO = p_version
                and gcf.model_def_id = p_model_id
                and gcf.CF_TYPE = 'A5'
                and ir.EVALUATE_MAIN_ID = gcf.EVALUATE_MAIN_ID
                and ir.ICG_NO = gcf.ICG_NO
                and ir.DEV_PERIOD = gcf.DEV_PERIOD
                and gcf.DEV_PERIOD > 0
              group by gcf.version_no, gcf.model_def_id,
                       gcf.evaluate_main_id, gcf.icg_no, gcf.year_month);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PV_018';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_gmm_icg_pv(icg_pv_id, version_no, model_def_id,
                                            evaluate_main_id, icg_no, year_month, cf_type,
                                            num1, num3, num4, num5, num6, num7, num8)
        select qtc_seq_buss_evl_gmm_icg_pv.nextval,
               version_no,
               model_def_id,
               evaluate_main_id,
               icg_no,
               year_month,
               'A53',
               num1,
               num3,
               num4,
               num5,
               num6,
               num7,
               num8
        from (select gcf.version_no,
                     gcf.model_def_id,
                     gcf.evaluate_main_id,
                     gcf.icg_no,
                     gcf.year_month,
                     sum(gcf.num1)           num1,
                     sum(gcf.num3 * ir.FLT5) num3,
                     sum(gcf.num4 * ir.FLT6) num4,
                     sum(gcf.num5 * ir.FLT8) num5,
                     sum(gcf.num6 * ir.FLT7) num6,
                     sum(gcf.num7 * ir.FLT8) num7,
                     sum(gcf.num8 * ir.FLT8) num8
              from qtc_buss_evl_gmm_icg_cf gcf,
                   qtc_BUSS_EVL_CFG_WA_IR ir
              where gcf.VERSION_NO = p_version
                and gcf.model_def_id = p_model_id
                and gcf.CF_TYPE = 'A5'
                and gcf.DEV_PERIOD > 0
                and ir.evaluate_main_id = gcf.evaluate_main_id
                and ir.ICG_NO = gcf.ICG_NO
                and ir.DEV_PERIOD = gcf.DEV_PERIOD
              group by gcf.version_no, gcf.model_def_id,
                       gcf.evaluate_main_id, gcf.icg_no, gcf.year_month);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PV_019';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        MERGE INTO qtc_buss_evl_gmm_icg_pv TG
        USING (SELECT  PT.VERSION_NO, PT.MODEL_DEF_ID , pt.ICG_NO, pt.num14
               from qtc_BUSS_EVL_CFG_PT pt
               where pt.VERSION_NO = p_version
                AND PT.MODEL_DEF_ID = P_MODEL_ID) SC
        on (tg.VERSION_NO = SC.VERSION_NO AND TG.MODEL_DEF_ID = SC.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO AND
            TG.CF_TYPE IN ('A12', 'A31', 'A51'))
        when matched then
            update
            set num9  = case
                            when (nvl(tg.num3, 0) + nvl(tg.num5, 0) + nvl(tg.num6, 0) + nvl(tg.num7, 0) +
                                  nvl(tg.num8, 0)) > 0 then
                                    (nvl(tg.num3, 0) + nvl(tg.num5, 0) + nvl(tg.num6, 0) + nvl(tg.num7, 0)) *
                                    sc.NUM14 * -1 end,
                num10 = case
                            when (nvl(tg.num3, 0) + nvl(tg.num5, 0) + nvl(tg.num6, 0) + nvl(tg.num7, 0) +
                                  nvl(tg.num8, 0)) > 0 then
                                nvl(tg.num8, 0) * sc.num14 * -1 end;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        proc_evl_log_run(null, p_version, v_node, 'S');

    EXCEPTION
        WHEN OTHERS THEN
            proc_evl_log_run(null, p_version, v_node, 'E');
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, sqlerrm, 'S');

            raise_application_error(-20002, SQLERRM);

    end proc_evl_lrc_gmm_cf_pv;

    procedure proc_evl_lrc_gmm_pre(p_version varchar2, p_model_id number) is

        v_node      varchar(32);
        v_node_dt   varchar(32);
        v_ym        integer ;
        v_rpt_start integer ;
        v_rpt_end   integer ;
        v_fir       integer ;
    begin

        v_node := 'GMM_PRE';
        proc_evl_log_run(null, p_version, v_node, 'B');

        select evl_ym,
               RPT_START,
               RPT_END,
               case when RPT_START = evl_ym then 1 else 0 end
        into v_ym , v_rpt_start, v_rpt_end, v_fir
        from qtc_BUSS_EVL_ICG_MSG
        where VERSION_NO = p_version
          and model_def_id = p_model_id
          and ROWNUM = 1;

        v_node_dt := 'GMM_PRE_001';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        IF v_fir = 0 THEN

            insert into qtc_BUSS_EVL_ICG_MSG(ICG_MSG_ID, version_no, VERSION_PRE ,
                                             evaluate_approach, model_def_id, evl_ym, evl_main_id,
                                             loa_code, icg_no, cal_type,
                                             rpt_start, rpt_end, rpt_bwt, rpt_num)
            select qtc_SEQ_BUSS_EVL_ICG_MSG.nextval,
                   mt.VERSION_NO,
                   lmt.VERSION_NO ,
                   MS.EVALUATE_APPROACH,
                   mt.MODEL_DEF_ID,
                   mt.YEAR_MONTH,
                   mt.EVALUATE_MAIN_ID,
                   mt.LOA_CODE,
                   ms.ICG_NO,
                   ms.CAL_TYPE,
                   ms.rpt_start,
                   ms.rpt_end,
                   ms.rpt_bwt + 1,
                   ms.rpt_num
            from qtc_buss_evaluate_main mt,
                 qtc_buss_evaluate_main lmt,
                 qtc_BUSS_EVL_ICG_MSG ms
            where mt.VERSION_NO = p_version
              and mt.MODEL_DEF_ID = p_model_id
              and lmt.CONFIRM_IS = '1'
              and lmt.MODEL_DEF_ID = mt.MODEL_DEF_ID
              and lmt.YEAR_MONTH = to_char(add_months(to_date(mt.YEAR_MONTH, 'yyyymm'), -1), 'yyyymm')
              and lmt.LOA_CODE = mt.LOA_CODE
              and ms.VERSION_NO = lmt.VERSION_NO
              and ms.MODEL_DEF_ID = lmt.MODEL_DEF_ID
              and ms.LOA_CODE = lmt.LOA_CODE
              and ms.EVALUATE_APPROACH = 'BBA'
              and ms.CAL_TYPE = 'Lrc'
              and not exists (select 1
                                    from qtc_BUSS_EVL_ICG_MSG mg
                                    where mg.VERSION_NO = mt.VERSION_NO
                                      and mg.MODEL_DEF_ID = mt.MODEL_DEF_ID
                                      and mg.EVALUATE_APPROACH = 'BBA'
                                      and mg.CAL_TYPE = 'Lrc'
                                      and mg.ICG_NO = ms.ICG_NO);

        END IF;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

       v_node_dt := 'GMM_PRE_002';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_gmm_icg_rate(ICG_RATE_ID, version_no, MODEL_DEF_ID, evaluate_main_id, icg_no, type_no,
                                              FLT1, FLT2, FLT3, FLT4, FLT6, FLT7, FLT8)
        select qtc_seq_buss_evl_gmm_icg_rate.nextval,
               pt.VERSION_NO,
               pt.MODEL_DEF_ID,
               pt.EVALUATE_MAIN_ID,
               pt.ICG_NO,
               'R1',
               pt.num3,
               null,
               (1 - pt.num9),
               power(1 + ir.flt1, pt.num9 - 1),
               pt.num15,
               pt.num16,
               v_fir
        from qtc_BUSS_EVL_CFG_PT PT,
             qtc_buss_evl_cfg_ir ir
        where PT.VERSION_NO = P_VERSION
          AND PT.MODEL_DEF_ID = p_model_id
          and IR.VERSION_NO = PT.VERSION_NO
          and ir.MODEL_DEF_ID = PT.MODEL_DEF_ID
          and IR.ICG_NO = PT.ICG_NO
          and ir.DEV_PERIOD = 0;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_0021';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        merge into qtc_buss_evl_gmm_icg_rate tg
            using (select VERSION_NO, MODEL_DEF_ID, icg_no , 'R1' ty, flt1 v1
                       from qtc_buss_evl_cfg_wa_ir  wr
                    where VERSION_NO = p_version
                      and MODEL_DEF_ID = p_model_id
                      and DEV_PERIOD = 0) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO
             and tg.TYPE_NO = sc.ty)
        when matched then
            update set tg.FLT5 = sc.v1 ;
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_003';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_gmm_icg_rate(ICG_RATE_ID, VERSION_NO, MODEL_DEF_ID, EVALUATE_MAIN_ID, ICG_NO, TYPE_NO,
                                              flt1, FLT3)
        select qtc_seq_buss_evl_gmm_icg_rate.nextval,
               CR.VERSION_NO,
               cr.MODEL_DEF_ID,
               CR.EVL_MAIN_ID,
               CR.ICG_NO,
               'R2',
               CASE
                   WHEN abs(nvl(cf.NUM2, 0)+nvl(cf.NUM6, 0) - nvl(cf.NUM4, 0)) < 1
                       THEN 0
                   WHEN abs((nvl(cf.NUM1, 0)+nvl(cf.NUM5, 0) - nvl(cf.NUM3, 0))) >= abs((nvl(cf.NUM2, 0)+nvl(cf.NUM6, 0) - nvl(cf.NUM4, 0)))
                       THEN 1
                   ELSE (nvl(cf.NUM1, 0)+nvl(cf.NUM5, 0) - nvl(cf.NUM3, 0)) / (nvl(cf.NUM2, 0)+nvl(cf.NUM6, 0) - nvl(cf.NUM4, 0)) END,
               CASE
                   WHEN NVL(CR.Iacf, 0) = 0 THEN 1
                   ELSE CR.ED_Iacf / CR.Iacf END RATE
        FROM qtc_BUSS_EVL_ICG_CUR CR,
             qtc_buss_evl_gmm_icg_cf cf
        WHERE CR.VERSION_NO = P_VERSION
          AND CR.MODEL_DEF_ID = p_model_id
          and cf.VERSION_NO = cr.VERSION_NO
          and cf.MODEL_DEF_ID = cr.MODEL_DEF_ID
          and cr.ICG_NO = cf.ICG_NO
          and cf.CF_TYPE = 'A6'
          and cf.EXP_YM = cf.YEAR_MONTH;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_004';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_evl_gmm_icg_rate tg
            using (select mg.VERSION_NO, mg.MODEL_DEF_ID, mg.EVL_MAIN_ID, mg.ICG_NO, 'R2' ty
                       from qtc_BUSS_EVL_ICG_MSG  mg
                    where mg.VERSION_NO = p_version
                      and mg.MODEL_DEF_ID = p_model_id
                      and mg.CAL_TYPE = 'Lrc') sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO
             and tg.TYPE_NO = sc.ty)
        when not matched then
            insert (ICG_RATE_ID, VERSION_NO, MODEL_DEF_ID, EVALUATE_MAIN_ID, ICG_NO, TYPE_NO,  FLT3)
            values (qtc_seq_buss_evl_gmm_icg_rate.nextval, sc.VERSION_NO, sc.MODEL_DEF_ID, sc.EVL_MAIN_ID, sc.ICG_NO, sc.ty, 1);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_005';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_gmm_icg_rate(ICG_RATE_ID, VERSION_NO, model_def_id, EVALUATE_MAIN_ID, ICG_NO, TYPE_NO,
                                              flt1, flt2, FLT3, FLT4, FLT5, FLT6, FLT7, FLT8)
        select qtc_seq_buss_evl_gmm_icg_rate.nextval,
               VERSION_NO,
               model_def_id,
               EVL_MAIN_ID,
               ICG_NO,
               'R3',
               ttt.f0,
               case when ttt.f0 = 1 then 0 else (nvl(ttt.f1, 1) - nvl(ttt.f0, 1)) / (1 - ttt.f0) end,
               case when ttt.f0 = 1 then 0 else (nvl(ttt.f2, 1) - nvl(ttt.f1, 1)) / (1 - ttt.f0) end,
               case
                   when ttt.f0 = 1 then 0
                   else (nvl(ttt.f3, 1) - nvl(ttt.f2, 1)) / (1 - ttt.f0) end,
               case when ttt.f0 = 1 then 0 else (nvl(ttt.f4, 1) - nvl(ttt.f3, 1)) / (1 - ttt.f0) end,
               case when ttt.f0 = 1 then 0 else (nvl(ttt.f5, 1) - nvl(ttt.f4, 1)) / (1 - ttt.f0) end,
               0,
               0
        from  (select version_no,
                     model_def_id,
                     EVL_MAIN_ID,
                     ICG_NO,
                     sum(case when dev_period = 0 then csm_rate end)   f0,
                     sum(case when dev_period <= 12 then csm_rate end) f1,
                     sum(case when dev_period <= 24 then csm_rate end) f2,
                     sum(case when dev_period <= 36 then csm_rate end) f3,
                     sum(case when dev_period <= 48 then csm_rate end) f4,
                     sum(case when dev_period <= 60 then csm_rate end) f5
              from qtc_BUSS_EVL_CSM_RATE
              where VERSION_NO = p_version
                AND MODEL_DEF_ID = p_model_id
              group by version_no, model_def_id, EVL_MAIN_ID, ICG_NO) TTT ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_006';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_evl_gmm_icg_rate tg
            using (select mg.VERSION_NO, mg.MODEL_DEF_ID, mg.EVL_MAIN_ID, mg.ICG_NO, 'R3' ty
                       from qtc_BUSS_EVL_ICG_MSG  mg
                    where mg.VERSION_NO = p_version
                      and mg.MODEL_DEF_ID = p_model_id
                      and mg.CAL_TYPE = 'Lrc') sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO
             and tg.TYPE_NO = sc.ty)
        when not matched then
            insert (ICG_RATE_ID, VERSION_NO, MODEL_DEF_ID, EVALUATE_MAIN_ID, ICG_NO, TYPE_NO,  FLT1)
            values (qtc_seq_buss_evl_gmm_icg_rate.nextval, sc.VERSION_NO, sc.MODEL_DEF_ID, sc.EVL_MAIN_ID, sc.ICG_NO, sc.ty, 1);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_007';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_gmm_icg_rate(ICG_RATE_ID, VERSION_NO, MODEL_DEF_ID, EVALUATE_MAIN_ID, ICG_NO, TYPE_NO,
                                              flt1, flt2, FLT3, FLT4)
        SELECT qtc_seq_buss_evl_gmm_icg_rate.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVL_MAIN_ID,
               ICG_NO,
               'R4',
               flt1,
               flt2,
               flt3,
               flt4
        from (select MS.VERSION_NO,
                     ms.MODEL_DEF_ID,
                     MS.EVL_MAIN_ID,
                     MS.ICG_NO,
                     sum(case when t2.TYPE_NO = 'R3' THEN T2.FLT1 END) flt1,
                     sum(case when t2.TYPE_NO = 'R2' THEN T2.FLT3 END) flt2,
                     sum(case when t2.TYPE_NO = 'R2' THEN T2.FLT4 END) flt3,
                     sum(case when t2.TYPE_NO = 'R2' THEN T2.FLT5 END) flt4
              FROM qtc_BUSS_EVL_ICG_MSG MS,
                   qtc_buss_evl_gmm_icg_rate t2
              where MS.VERSION_NO = p_version
                and ms.MODEL_DEF_ID = p_model_id
                and ms.CAL_TYPE = 'Lrc'
                and t2.VERSION_NO = ms.VERSION_PRE
                and t2.MODEL_DEF_ID = ms.MODEL_DEF_ID
                and t2.ICG_NO = ms.ICG_NO
              group by MS.VERSION_NO,
                       ms.MODEL_DEF_ID,
                       MS.EVL_MAIN_ID,
                       MS.ICG_NO);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_008';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        if v_fir = 1 then
        insert into qtc_buss_evl_gmm_icg_pre(icg_pre_id, version_no,
                                             model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                                             num1, num2, num3, num4, num5, num6, num7, num8, num9, num10)
        select qtc_seq_buss_evl_gmm_icg_pre.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVL_MAIN_ID,
               ICG_NO,
               EVL_YM,
               'L1',
               m1,
               m2,
               m3,
               m4,
               m5,
               m6,
               m7,
               nvl(m7, 0) - nvl(m8, 0),
               m8,
               m9
        from (select sc.VERSION_NO,
                     sc.MODEL_DEF_ID,
                     sc.EVL_MAIN_ID,
                     sc.ICG_NO,
                     sc.EVL_YM,
                     sum(case when cg.TYPE_NO = 'A1' then cg.num8 end)  m1,
                     sum(case when cg.TYPE_NO = 'C3' then cg.num9 end)  m2,
                     sum(case when cg.TYPE_NO = 'C4' then cg.num9 end)  m3,
                     sum(case when cg.TYPE_NO = 'C5' then cg.num8 end)  m4,
                     sum(case when cg.TYPE_NO = 'C6' then cg.num8 end)  m5,
                     sum(case when cg.TYPE_NO = 'D1' then cg.num13 end) m6,
                     sum(case when cg.TYPE_NO = 'C3' then cg.num10 end) m7,
                     sum(case when cg.TYPE_NO = 'C2' then cg.num6 end)  m8,
                     sum(case when cg.TYPE_NO = 'C3' then cg.num10 end)  m9
              from qtc_BUSS_EVL_ICG_MSG sc,
                   qtc_buss_evl_gmm_cal_icg cg
              where sc.VERSION_NO = p_version
                AND SC.MODEL_DEF_ID = p_model_id
                and sc.CAL_TYPE = 'Lrc'
                and cg.VERSION_NO = sc.VERSION_PRE
                and cg.ICG_NO = sc.ICG_NO
              group by sc.VERSION_NO,
                       sc.BATCH_NO,
                       sc.MODEL_DEF_ID,
                       sc.EVL_MAIN_ID,
                       sc.ICG_NO,
                       sc.EVL_YM);
        end if ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_009';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
         if v_fir = 1 then
        insert into qtc_buss_evl_gmm_icg_pre(icg_pre_id, version_no,
                                             model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                                             num1, num2, num3, num4, num5, num6, num7, num8)
        select qtc_seq_buss_evl_gmm_icg_pre.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVL_MAIN_ID,
               ICG_NO,
               EVL_YM,
               'L2',
               v3,
               v4,
               v5,
               v6,
               v7,
               nvl(v3, 0) + nvl(v4, 0) + nvl(v5, 0) + nvl(v6, 0) + nvl(v7, 0),
               v8,
               nvl(v5, 0) + nvl(v6, 0) + nvl(v7, 0)
        from (select ms.VERSION_NO,
                     ms.MODEL_DEF_ID,
                     ms.EVL_MAIN_ID,
                     ms.EVL_YM,
                     ms.ICG_NO,
                     sum(pv.num3) v3,
                     sum(pv.num4) v4,
                     sum(pv.num5) v5,
                     sum(pv.num6) v6,
                     sum(pv.num7) v7,
                     sum(pv.num8) v8
              from qtc_BUSS_EVL_ICG_MSG ms,
                   qtc_buss_evl_gmm_icg_pv pv
              where ms.VERSION_NO = p_version
                AND ms.MODEL_DEF_ID = p_model_id
                and ms.CAL_TYPE = 'Lrc'
                and pv.VERSION_NO = ms.VERSION_PRE
                and pv.MODEL_DEF_ID = ms.MODEL_DEF_ID
                and pv.ICG_NO = ms.ICG_NO
                and pv.CF_TYPE IN ('A33', 'A53')
              group by ms.VERSION_NO,
                       ms.BATCH_NO,
                       ms.MODEL_DEF_ID,
                       ms.EVL_MAIN_ID,
                       ms.EVL_YM,
                       ms.ICG_NO) ttt;
        end if ;
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_010';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
         if v_fir = 1 then
        insert into qtc_buss_evl_gmm_icg_pre(icg_pre_id, version_no,
                                             model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                                             num1, num2, num3, num4, num5, num6, num7, num8, num9, num10)
        select qtc_seq_buss_evl_gmm_icg_pre.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVL_MAIN_ID,
               ICG_NO,
               EVL_YM,
               'L3',
               v3,
               v4,
               v5,
               v6,
               v7,
               nvl(v3, 0) + nvl(v4, 0) + nvl(v5, 0) + nvl(v6, 0) + nvl(v7, 0),
               v8,
               nvl(v5, 0) + nvl(v6, 0) + nvl(v7, 0),
               v9,
               v10
        from (select ms.VERSION_NO,
                     ms.MODEL_DEF_ID,
                     ms.EVL_MAIN_ID,
                     ms.EVL_YM,
                     ms.ICG_NO,
                     sum(pv.num3)  v3,
                     sum(pv.num4)  v4,
                     sum(pv.num5)  v5,
                     sum(pv.num6)  v6,
                     sum(pv.num7)  v7,
                     sum(pv.num8)  v8,
                     sum(pv.num9)  v9,
                     sum(pv.num10) v10
              from qtc_BUSS_EVL_ICG_MSG ms,
                   qtc_buss_evl_gmm_icg_pv pv
              where ms.VERSION_NO = p_version
                AND ms.MODEL_DEF_ID = p_model_id
                and ms.CAL_TYPE = 'Lrc'
                and pv.VERSION_NO = ms.VERSION_PRE
                and pv.MODEL_DEF_ID = ms.MODEL_DEF_ID
                and pv.ICG_NO = ms.ICG_NO
                and pv.CF_TYPE IN ('A31', 'A51')
              group by ms.VERSION_NO,
                       ms.MODEL_DEF_ID,
                       ms.EVL_MAIN_ID,
                       ms.EVL_YM,
                       ms.ICG_NO) ttt;
        end if ;
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_011';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
         if v_fir = 0 then
        insert into qtc_buss_evl_gmm_icg_pre(icg_pre_id, version_no,
                                             model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                                             num1, num2, num3, num4, num5, num6, num7, num8, num9, num10)
        select qtc_seq_buss_evl_gmm_icg_pre.nextval,
               sc.VERSION_NO,
               sc.MODEL_DEF_ID,
               sc.EVL_MAIN_ID,
               sc.ICG_NO,
               sc.EVL_YM,
               pe.TYPE_NO,
               pe.num1,
               pe.num2,
               pe.num3,
               pe.num4,
               pe.num5,
               pe.num6,
               pe.num7,
               pe.num8,
               pe.num9,
               pe.num10
        from qtc_buss_evl_gmm_icg_pre pe,
             qtc_BUSS_EVL_ICG_MSG sc
        where sc.VERSION_NO = p_version
          AND SC.MODEL_DEF_ID = p_model_id
          and sc.CAL_TYPE = 'Lrc'
          and sc.EVL_YM > sc.RPT_START
          and pe.VERSION_NO = sc.VERSION_PRE
          and pe.MODEL_DEF_ID = sc.MODEL_DEF_ID
          and pe.ICG_NO = sc.ICG_NO
          and pe.TYPE_NO in ('L1', 'L2', 'L3');

        end if ;
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_012';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_gmm_icg_pre(icg_pre_id, version_no,
                                             model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                                             num1, num2, num3, num4, num5, num6, num7, num8, num9, NUM10)
        select qtc_seq_buss_evl_gmm_icg_pre.nextval,
               gpv.VERSION_NO,
               gpv.MODEL_DEF_ID,
               gpv.EVALUATE_MAIN_ID,
               gpv.ICG_NO,
               gpv.YEAR_MONTH,
               case
                   when nvl(gpv.num3, 0) + nvl(gpv.num4, 0) + nvl(gpv.num5, 0) + nvl(gpv.num6, 0) +
                        nvl(gpv.num7, 0) + nvl(gpv.num8, 0) + nvl(gpv.num9, 0) + nvl(gpv.num10, 0) >= 0
                       then 'L5'
                   else 'L4' end,
               gpv.num1,
               gpv.num3,
               gpv.num4,
               gpv.num5,
               gpv.num6,
               gpv.num7,
               gpv.num8,
               nvl(gpv.num3, 0) + nvl(gpv.num4, 0) + nvl(gpv.num5, 0) +
               nvl(gpv.num6, 0) + nvl(gpv.num7, 0) + nvl(gpv.num8, 0),
               gpv.num9,
               gpv.num10
        from qtc_buss_evl_gmm_icg_pv gpv
        where gpv.VERSION_NO = p_version
          AND gpv.MODEL_DEF_ID = p_model_id
          and gpv.CF_TYPE = 'A12';

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_013';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        if v_fir = 0 then
        merge into qtc_buss_evl_gmm_icg_pre tg
        using (select ms.model_def_id,
                      ms.EVL_MAIN_ID,
                      ms.EVL_YM,
                      pe.TYPE_NO,
                      pe.ICG_NO,
                      pe.num1,
                      pe.num2,
                      pe.num3,
                      pe.num4,
                      pe.num5,
                      pe.num6,
                      pe.num7,
                      pe.num8,
                      pe.num9,
                      pe.NUM10
               from qtc_BUSS_EVL_ICG_MSG ms,
                    qtc_buss_evl_gmm_icg_pre pe
               where ms.VERSION_NO = p_version
                 AND MS.MODEL_DEF_ID = p_model_id
                 and ms.CAL_TYPE = 'Lrc'
                 and pe.VERSION_NO = ms.VERSION_PRE
                 and pe.MODEL_DEF_ID = ms.MODEL_DEF_ID
                 and pe.ICG_NO = ms.ICG_NO
                 and pe.TYPE_NO in ('L5', 'L4')) sc
        on (tg.VERSION_NO = p_version
            and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO and tg.TYPE_NO = sc.TYPE_NO)
        when matched then
            update
            set num1  = nvl(tg.num1, 0) + nvl(sc.num1, 0),
                num2  = nvl(tg.num2, 0) + nvl(sc.num2, 0),
                num3  = nvl(tg.num3, 0) + nvl(sc.num3, 0),
                num4  = nvl(tg.num4, 0) + nvl(sc.num4, 0),
                num5  = nvl(tg.num5, 0) + nvl(sc.num5, 0),
                num6  = nvl(tg.num6, 0) + nvl(sc.num6, 0),
                num7  = nvl(tg.num7, 0) + nvl(sc.num7, 0),
                num8  = nvl(tg.num8, 0) + nvl(sc.num8, 0),
                num9  = nvl(tg.num9, 0) + nvl(sc.num9, 0),
                num10 = nvl(tg.num10, 0) + nvl(sc.num10, 0)
        when not matched then
            insert (icg_pre_id, version_no,
                    model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                    num1, num2, num3, num4, num5, num6, num7, num8, num9, num10)
            values (qtc_seq_buss_evl_gmm_icg_pre.nextval, p_version,
                    sc.MODEL_DEF_ID, sc.EVL_MAIN_ID, sc.ICG_NO, sc.EVL_YM, sc.TYPE_NO,
                    sc.num1, sc.num2, sc.num3, sc.num4, sc.num5, sc.num6, sc.num7, sc.num8, sc.num9, sc.num10);
        end if ;
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_014';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_gmm_icg_pre(icg_pre_id, version_no,
                                             model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                                             num1, num2, num3, num4, num5, num6, num7, num8, num9, num10)
        select qtc_seq_buss_evl_gmm_icg_pre.nextval,
               gpv.VERSION_NO,
               gpv.MODEL_DEF_ID,
               gpv.evaluate_main_id,
               gpv.ICG_NO,
               gpv.YEAR_MONTH,
               'L6',
               gpv.num1,
               gpv.num3,
               gpv.num4,
               gpv.num5,
               gpv.num6,
               gpv.num7,
               gpv.num8,
               nvl(gpv.num3, 0) + nvl(gpv.num4, 0) + nvl(gpv.num5, 0) +
               nvl(gpv.num6, 0) + nvl(gpv.num7, 0) + nvl(gpv.num8, 0),
               gpv.num9,
               gpv.num10
        from qtc_buss_evl_gmm_icg_pv gpv
        where gpv.VERSION_NO = p_version
          AND gpv.MODEL_DEF_ID = p_model_id
          and gpv.CF_TYPE = 'A12';

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_015';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_gmm_icg_pre(icg_pre_id, version_no,
                                             model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                                             num2, num3, num4, num5, num6, num7, num8, NUM9)
        select qtc_seq_buss_evl_gmm_icg_pre.nextval,
               pe.VERSION_NO,
               pe.model_def_id,
               pe.EVALUATE_MAIN_ID,
               pe.ICG_NO,
               pe.YEAR_MONTH,
               'L7',
               pe.num1,
               pe.num2,
               pe.num3,
               pe.num4,
               pe.num5,
               pe.num6,
               pe.num7,
               nvl(pe.num6, 0) + nvl(pe.num7, 0)
        from qtc_buss_evl_gmm_icg_pre pe
        where pe.VERSION_NO = p_version
          and pe.MODEL_DEF_ID = p_model_id
          and pe.TYPE_NO = 'L2';

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_016';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_gmm_icg_pre(icg_pre_id, version_no,
                                             model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                                             num1, num2, num3, num4, num5, num6, num7, num8, num9, num10, num11)
        select qtc_seq_buss_evl_gmm_icg_pre.nextval,
               VERSION_NO,
               model_def_id,
               EVALUATE_MAIN_ID,
               ICG_NO,
               YEAR_MONTH,
               'L8',
               num1,
               num2,
               num3,
               num4,
               num5,
               num6,
               nvl(num8, 0) - nvl(num7, 0),
               num7,
               num8,
               num9,
               num10
        from (select pe.VERSION_NO,
                     pe.model_def_id,
                     pe.EVALUATE_MAIN_ID,
                     pe.ICG_NO,
                     pe.YEAR_MONTH,
                     sum(pe.num1)  num1,
                     sum(pe.num2)  num2,
                     sum(pe.num3)  num3,
                     sum(pe.num4)  num4,
                     sum(pe.num5)  num5,
                     sum(pe.num6)  num6,
                     sum(pe.num7)  num7,
                     sum(pe.num8)  num8,
                     sum(pe.num9)  num9,
                     sum(pe.num10) num10
              from qtc_buss_evl_gmm_icg_pre pe
              where pe.VERSION_NO = p_version
                and pe.MODEL_DEF_ID = p_model_id
                and pe.TYPE_NO in ('L4', 'L5')
              group by pe.VERSION_NO,
                       pe.BATCH_NO,
                       pe.model_def_id,
                       pe.EVALUATE_MAIN_ID,
                       pe.ICG_NO,
                       pe.YEAR_MONTH);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_017';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_gmm_icg_pre(icg_pre_id, version_no,
                                             model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                                             num2, num3, num4, num5, num6, num7, num8, num9, num10, num11)
        select qtc_seq_buss_evl_gmm_icg_pre.nextval,
               pe.VERSION_NO,
               pe.model_def_id,
               pe.EVALUATE_MAIN_ID,
               pe.ICG_NO,
               pe.YEAR_MONTH,
               'L9',
               pe.num1,
               pe.num2,
               pe.num3,
               pe.num4,
               pe.num5,
               pe.num6,
               pe.num7,
               nvl(pe.num6, 0) + nvl(pe.num7, 0),
               pe.num9,
               pe.num10
        from qtc_buss_evl_gmm_icg_pre pe
        where pe.VERSION_NO = p_version
          and pe.MODEL_DEF_ID = p_model_id
          and pe.TYPE_NO = 'L3';

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_018';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_gmm_icg_pre(icg_pre_id, version_no,
                                             model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                                             num2, num3, num4, num5, num6, num7, num8, num9, num10, num11)
        select qtc_seq_buss_evl_gmm_icg_pre.nextval,
               GPV.VERSION_NO,
               GPV.model_def_id,
               GPV.evaluate_main_id,
               GPV.ICG_NO,
               GPV.year_month,
               case
                   when gpv.CF_TYPE = 'A43' then 'L10'
                   when gpv.CF_TYPE = 'A23' then 'L11'
                   when gpv.CF_TYPE = 'A53' then 'L12'
                   when gpv.CF_TYPE = 'A33' then 'L13'
                   when gpv.CF_TYPE = 'A51' then 'L14'
                   when gpv.CF_TYPE = 'A31' then 'L15' end,
               GPV.num3,
               GPV.num4,
               GPV.num5,
               GPV.num6,
               GPV.num7,
               nvl(GPV.num3, 0) + nvl(GPV.num4, 0) + nvl(GPV.num5, 0) + nvl(GPV.num6, 0) + nvl(GPV.num7, 0),
               GPV.NUM8,
               nvl(GPV.num3, 0) + nvl(GPV.num4, 0) + nvl(GPV.num5, 0) + nvl(GPV.num6, 0) + nvl(GPV.num7, 0) +
               nvl(GPV.num8, 0) + nvl(GPV.num9, 0) + nvl(GPV.num10, 0),
               GPV.num9,
               GPV.num10
        from qtc_buss_evl_gmm_icg_pv GPV
        where GPV.VERSION_NO = p_version
          AND GPV.MODEL_DEF_ID = p_model_id
          and GPV.CF_TYPE in ('A43', 'A23', 'A53', 'A33', 'A51', 'A31');

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_019';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_gmm_icg_pre(icg_pre_id, version_no,
                                             model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                                             num1, num2, num3, num4)
        select qtc_seq_buss_evl_gmm_icg_pre.nextval,
               VERSION_NO,
               model_def_id,
               evaluate_main_id,
               ICG_NO,
               year_month,
               'L16',
               V1,
               V2,
               V3,
               V4
        from (select gc.VERSION_NO,
                     gc.model_def_id,
                     gc.evaluate_main_id,
                     gc.ICG_NO,
                     gc.year_month,
                     sum(case when gc.CF_TYPE = 'A4' THEN gc.num4 END) V1,
                     sum(case when gc.CF_TYPE = 'A2' THEN gc.num4 END) V2,
                     sum(case when gc.CF_TYPE = 'A5' THEN gc.num4 END) V3,
                     sum(case when gc.CF_TYPE = 'A3' THEN gc.num4 END) V4
              from qtc_buss_evl_gmm_icg_cf gc
              where gc.VERSION_NO = p_version
                AND gc.MODEL_DEF_ID = p_model_id
                and gc.CF_TYPE IN ('A2', 'A3', 'A4', 'A5')
                and gc.dev_period > 0
              group by gc.VERSION_NO,
                       gc.model_def_id,
                       gc.evaluate_main_id,
                       gc.ICG_NO,
                       gc.year_month) tt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_020';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_gmm_icg_pre(icg_pre_id, version_no, EVALUATE_MAIN_ID,
                                             model_def_id, icg_no, year_month, type_no,
                                             num1, num2, num3, num4, num5, num6, num7, num8)
        select qtc_seq_buss_evl_gmm_icg_pre.nextval,
               VERSION_NO,
               EVALUATE_MAIN_ID,
               model_def_id,
               ICG_NO,
               year_month,
               'L17',
               V1,
               V2,
               V3,
               V4,
               v5,
               v6,
               v7,
               v8
        from (select gc.VERSION_NO,
                     gc.EVALUATE_MAIN_ID,
                     gc.model_def_id,
                     gc.ICG_NO,
                     gc.year_month,
                     sum(case when gc.CF_TYPE = 'A4' THEN gc.num3 END) V1,
                     sum(case when gc.CF_TYPE = 'A2' THEN gc.num3 END) V2,
                     sum(case when gc.CF_TYPE = 'A5' THEN gc.num3 END) V3,
                     sum(case when gc.CF_TYPE = 'A3' THEN gc.num3 END) V4,
                     sum(case when gc.CF_TYPE = 'A4' THEN gc.num4 END) V5,
                     sum(case when gc.CF_TYPE = 'A2' THEN gc.num4 END) V6,
                     sum(case when gc.CF_TYPE = 'A5' THEN gc.num4 END) V7,
                     sum(case when gc.CF_TYPE = 'A3' THEN gc.num4 END) V8
              from qtc_buss_evl_gmm_icg_cf gc
              where gc.VERSION_NO = P_VERSION
                AND gc.MODEL_DEF_ID = p_model_id
                and gc.DEV_PERIOD = 0
                and gc.CF_TYPE IN ('A2', 'A3', 'A4', 'A5')
              group by gc.VERSION_NO,
                       gc.EVALUATE_MAIN_ID,
                       gc.model_def_id,
                       gc.ICG_NO,
                       gc.year_month) tt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_021';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        if v_fir = 0 then
            merge into qtc_buss_evl_gmm_icg_pre tg
            using (select SC.VERSION_NO,
                          SC.BATCH_NO,
                          SC.model_def_id,
                          SC.EVL_MAIN_ID,
                          SC.ICG_NO,
                          SC.EVL_YM,
                          gc.TYPE_NO,
                          gc.num1 v1,
                          gc.num2 v2,
                          gc.num3 v3,
                          gc.num4 v4,
                          gc.num5 v5,
                          gc.num6 v6,
                          gc.num7 v7,
                          gc.num8 v8
                   from qtc_BUSS_EVL_ICG_MSG SC,
                        qtc_buss_evl_gmm_icg_pre gc
                   where SC.VERSION_NO = p_version
                     AND SC.MODEL_DEF_ID = p_model_id
                     and sc.CAL_TYPE = 'Lrc'
                     and gc.VERSION_NO = sc.VERSION_PRE
                     and gc.MODEL_DEF_ID = sc.MODEL_DEF_ID
                     and gc.ICG_NO = sc.ICG_NO
                     and gc.TYPE_NO = 'L17') sc
            on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID
                and tg.ICG_NO = sc.ICG_NO and tg.TYPE_NO = sc.TYPE_NO)
            when matched then
                update
                set num1 = nvl(sc.v1, 0) + nvl(tg.num1, 0),
                    num2 = nvl(sc.v2, 0) + nvl(tg.num2, 0),
                    num3 = nvl(sc.v3, 0) + nvl(tg.num3, 0),
                    num4 = nvl(sc.v4, 0) + nvl(tg.num4, 0),
                    num5 = nvl(sc.v5, 0) + nvl(tg.num5, 0),
                    num6 = nvl(sc.v6, 0) + nvl(tg.num6, 0),
                    num7 = nvl(sc.v7, 0) + nvl(tg.num7, 0),
                    num8 = nvl(sc.v8, 0) + nvl(tg.num8, 0)
            when not matched then
                insert (icg_pre_id, version_no, batch_no, model_def_id, evaluate_main_id,
                        icg_no, year_month, type_no, num1, num2, num3, num4, num5, num6, num7, num8)
                values (qtc_seq_buss_evl_gmm_icg_pre.nextval, sc.VERSION_NO, sc.BATCH_NO, sc.MODEL_DEF_ID,
                        sc.EVL_MAIN_ID, sc.ICG_NO, sc.EVL_YM, sc.TYPE_NO, sc.v1, sc.v2, sc.v3, sc.v4, sc.v5, sc.v6,
                        sc.v7,
                        sc.v8);
        end if;
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_022';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_gmm_icg_pre(icg_pre_id, version_no, EVALUATE_MAIN_ID,
                                                 model_def_id, icg_no, year_month, type_no,
                                                 num1, num2, num3)
            select qtc_seq_buss_evl_gmm_icg_pre.nextval,
                   VERSION_NO,
                   EVALUATE_MAIN_ID,
                   model_def_id,
                   ICG_NO,
                   YEAR_MONTH,
                   'L18',
                   V1,
                   V2,
                   V3
            from (select /*+ leading(ir, ls) */
                         ls.VERSION_NO,
                         ls.EVALUATE_MAIN_ID,
                         ls.model_def_id,
                         ls.ICG_NO,
                         ls.YEAR_MONTH,
                         sum(case when ls.LOOS_DEV = 0 then ls.num1 else ls.num1 * ir.flt8 end) v1,
                         sum(case when ls.LOOS_DEV = 0 then ls.num2 else ls.num2 * ir.flt8 end) v2,
                         sum(case when ls.LOOS_DEV = 0 then ls.num3 else ls.num3 * ir.flt8 end) v3
                  from qtc_BUSS_EVL_EXP_LOOS ls,
                       qtc_BUSS_EVL_CFG_WA_IR ir
                  where ir.VERSION_NO = p_version
                    and ir.MODEL_DEF_ID = p_model_id
                    and ls.VERSION_NO = ir.VERSION_NO
                    and ls.MODEL_DEF_ID = ir.MODEL_DEF_ID
                    and ls.ICG_NO = ir.ICG_NO
                    and ls.exp_ym = ls.YEAR_MONTH
                    and ls.CF_TYPE = 'A4'
                    and ls.LOOS_YM = ir.EXP_YM
                  group by ls.VERSION_NO,
                           ls.EVALUATE_MAIN_ID,
                           ls.model_def_id,
                           ls.ICG_NO,
                           ls.YEAR_MONTH) tt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_023';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        if v_fir = 0 then

            merge into qtc_buss_evl_gmm_icg_pre tg
            using (select ms.VERSION_NO,
                          ms.MODEL_DEF_ID,
                          ms.EVL_MAIN_ID,
                          ms.EVL_YM,
                          ms.ICG_NO,
                          pe.TYPE_NO,
                          pe.num1,
                          pe.num2,
                          pe.num3,
                          pe.num4
                   from qtc_BUSS_EVL_ICG_MSG ms,
                        qtc_buss_evl_gmm_icg_pre pe
                   where ms.VERSION_NO = p_version
                     and ms.MODEL_DEF_ID = p_model_id
                     and ms.CAL_TYPE = 'Lrc'
                     and pe.VERSION_NO = ms.VERSION_PRE
                     and pe.MODEL_DEF_ID = ms.MODEL_DEF_ID
                     and pe.ICG_NO = ms.ICG_NO
                     and pe.type_no = 'L18') sc
            on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.TYPE_NO = sc.TYPE_NO and
                tg.ICG_NO = sc.ICG_NO)
            when matched then
                update
                set num1 = nvl(tg.num1, 0) + nvl(sc.num1, 0),
                    num2 = nvl(tg.num2, 0) + nvl(sc.num2, 0),
                    num3 = nvl(tg.num3, 0) + nvl(sc.num3, 0),
                    num4 = nvl(tg.num4, 0) + nvl(sc.num4, 0)
            when not matched then
                insert (icg_pre_id, version_no, model_def_id, evaluate_main_id,
                        icg_no, year_month, type_no, num1, num2, num3, num4)
                values (qtc_seq_buss_evl_gmm_icg_pre.nextval, sc.VERSION_NO, sc.MODEL_DEF_ID,
                        sc.EVL_MAIN_ID, sc.ICG_NO, sc.EVL_YM, sc.TYPE_NO, sc.num1, sc.num2, sc.num3, sc.num4);

        end if;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_024';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_evl_gmm_icg_pre tg
        using (select pt.VERSION_NO, pt.MODEL_DEF_ID,  pt.ICG_NO, pt.num3
               from qtc_BUSS_EVL_CFG_PT pt
               where pt.VERSION_NO = p_version
                and pt.MODEL_DEF_ID = p_model_id) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO and tg.TYPE_NO = 'L18')
        when matched then
            update set num4 = (nvl(tg.num1, 0) + nvl(tg.num2, 0)) * sc.NUM3;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_025';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_gmm_icg_pre(icg_pre_id, version_no,  model_def_id, EVALUATE_MAIN_ID,
                                             year_month, icg_no, type_no, num1, num2, num3, num4)
        select qtc_seq_buss_evl_gmm_icg_pre.nextval,
               fr.VERSION_NO,
               fr.model_def_id,
               fr.EVALUATE_MAIN_ID,
               fr.year_month,
               fr.ICG_NO,
               'L19',
               fr.num1,
               fr.num2,
               fr.num3,
               fr.num4
        from qtc_buss_evl_gmm_free fr
        where fr.VERSION_NO = p_version
          and fr.MODEL_DEF_ID = p_model_id
          and fr.exp_ym = fr.YEAR_MONTH;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_026';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        if v_fir = 0 then

            merge into qtc_buss_evl_gmm_icg_pre tg
            using (select ms.VERSION_NO,
                          ms.MODEL_DEF_ID,
                          ms.EVL_MAIN_ID,
                          ms.ICG_NO,
                          pe.TYPE_NO,
                          pe.num1 v1,
                          pe.num2 v2,
                          pe.num3 v3,
                          pe.num4 v4
                   from qtc_BUSS_EVL_ICG_MSG ms,
                        qtc_buss_evl_gmm_icg_pre pe
                   where ms.VERSION_NO = p_version
                     and ms.MODEL_DEF_ID = p_model_id
                     and ms.CAL_TYPE = 'Lrc'
                     and pe.VERSION_NO = ms.VERSION_PRE
                     and pe.MODEL_DEF_ID = ms.MODEL_DEF_ID
                     and pe.ICG_NO = ms.ICG_NO
                     and pe.TYPE_NO = 'L19') sc
            on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO and
                tg.TYPE_NO = sc.TYPE_NO)
            when matched then
                update
                set num1 = nvl(sc.v1, 0) + nvl(tg.num1, 0),
                    num2 = nvl(sc.v2, 0) + nvl(tg.num2, 0),
                    num3 = nvl(sc.v3, 0) + nvl(tg.num3, 0),
                    num4 = nvl(sc.v4, 0) + nvl(tg.num4, 0)
            when not matched then
                insert (icg_pre_id, version_no, EVALUATE_MAIN_ID, model_def_id, icg_no, type_no, num1, num2, num3, num4)
                values (qtc_seq_buss_evl_gmm_icg_pre.nextval, sc.VERSION_NO, sc.EVL_MAIN_ID,
                        sc.MODEL_DEF_ID, sc.ICG_NO, sc.TYPE_NO, sc.v1, sc.v2, sc.v3, sc.v4);

        end if;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_027';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        if v_fir = 0 then

        insert into qtc_buss_evl_gmm_icg_pre(icg_pre_id, version_no,  model_def_id, evaluate_main_id,
                                             icg_no, year_month, type_no, num1, num2, num3, num4, num5, num6)
        select qtc_seq_buss_evl_gmm_icg_pre.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVL_MAIN_ID,
               ICG_NO,
               EVL_YM,
               'L20',
               m1,
               m2,
               m3,
               m4,
               m5,
               m6
        from (select sc.VERSION_NO,
                     sc.MODEL_DEF_ID,
                     sc.EVL_MAIN_ID,
                     sc.ICG_NO,
                     sc.EVL_YM,
                     sum(case when cg.TYPE_NO = 'A1' then cg.num1 end) m1,
                     sum(case when cg.TYPE_NO = 'A1' then cg.num5 end) m2,
                     sum(case when cg.TYPE_NO = 'C1' then cg.num2 end) m3,
                     sum(case when cg.TYPE_NO = 'C1' then cg.num5 end) m4,
                     sum(case when cg.TYPE_NO = 'D1' then cg.num2 end) m5,
                     sum(case when cg.TYPE_NO = 'D1' then cg.num7 end) m6
              from qtc_BUSS_EVL_ICG_MSG sc,
                   qtc_buss_evl_gmm_cal_icg cg
              where sc.VERSION_NO = p_version
                and sc.MODEL_DEF_ID = p_model_id
                and sc.CAL_TYPE = 'Lrc'
                and cg.VERSION_NO = sc.VERSION_PRE
                and sc.ICG_NO = cg.ICG_NO
              group by sc.VERSION_NO,
                       sc.MODEL_DEF_ID,
                       sc.EVL_MAIN_ID,
                       sc.ICG_NO,
                       sc.EVL_YM);

        end if ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_028';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_gmm_icg_pre(icg_pre_id, version_no, loa_code,
                                             model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                                             num1, num2)
        select qtc_seq_buss_evl_gmm_icg_pre.nextval,
               cr.VERSION_NO,
               CR.LOA_CODE,
               CR.MODEL_DEF_ID,
               CR.EVL_MAIN_ID,
               CR.ICG_NO,
               CR.EVL_YM,
               'L21',
               CR.CR_PREMIUM,
               CR.CR_IACF
        from qtc_BUSS_EVL_ICG_CUR CR
        where CR.VERSION_NO = p_version
          and cr.model_def_id = p_model_id;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_029';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        if v_fir = 0 then

            merge into qtc_buss_evl_gmm_icg_pre tg
            using (select mt.VERSION_NO,
                          mt.MODEL_DEF_ID,
                          mt.LOA_CODE,
                          mt.EVALUATE_MAIN_ID,
                          pe.ICG_NO,
                          pe.TYPE_NO,
                          pe.num1 v1,
                          pe.num2 v2
                   from qtc_buss_evaluate_main mt,
                        qtc_buss_evaluate_main lmt,
                        qtc_buss_evl_gmm_icg_pre pe
                   where mt.VERSION_NO = p_version
                     and mt.MODEL_DEF_ID = p_model_id
                     and lmt.CONFIRM_IS = '1'
                     and lmt.MODEL_DEF_ID = mt.MODEL_DEF_ID
                     and lmt.YEAR_MONTH = to_char(add_months(to_date(mt.YEAR_MONTH, 'yyyymm'), -1), 'yyyymm')
                     and lmt.LOA_CODE = mt.LOA_CODE
                     and pe.VERSION_NO = lmt.VERSION_NO
                     and pe.MODEL_DEF_ID = lmt.MODEL_DEF_ID
                     and pe.EVALUATE_MAIN_ID = lmt.EVALUATE_MAIN_ID
                     and pe.TYPE_NO = 'L21') sc
            on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO and
                tg.TYPE_NO = sc.TYPE_NO)
            when matched then
                update
                set num1 = nvl(sc.v1, 0) + nvl(tg.num1, 0),
                    num2 = nvl(sc.v2, 0) + nvl(tg.num2, 0)
            when not matched then
                insert (icg_pre_id, version_no, LOA_CODE, EVALUATE_MAIN_ID, model_def_id, icg_no, type_no, num1, num2)
                values (qtc_seq_buss_evl_gmm_icg_pre.nextval, sc.VERSION_NO, sc.LOA_CODE, sc.EVALUATE_MAIN_ID,
                        sc.MODEL_DEF_ID, sc.ICG_NO, sc.TYPE_NO, sc.v1, sc.v2);

        end if;
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_PRE_030';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_gmm_icg_pre(icg_pre_id, version_no, loa_code,
                                             model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                                             num3, num4)
        select qtc_seq_buss_evl_gmm_icg_pre.nextval,
               p_version,
               th.LOA_CODE,
               p_model_id,
               th.EVALUATE_MAIN_ID,
               th.ri_icg,
               th.YEAR_MONTH,
               'L22',
               rl.num27 * th.RATE,
               rl.num18 * th.RATE
        from qtc_buss_evaluate_main mt,
             qtc_buss_evaluate_result rl,
             (select t1.LOA_CODE,
                     t1.EVALUATE_MAIN_ID,
                     t1.YEAR_MONTH,
                     t1.ICG_NO                         ri_icg,
                     t2.ICG_NO                         bf_icg,
                     sum(t1.PREMIUM) / sum(t2.PREMIUM) RATE
              from qtc_buss_evl_bm_fo_unit t1,
                   QTC_DAP_ECF_DD_ICU t2
              where t1.VERSION_NO = p_version
                and t1.MODEL_DEF_ID = p_model_id
                and t1.POLICY_NO = t2.POLICY_NO
                and t1.ENDORSE_SEQ_NO = t2.ENDORSE_SEQ_NO
              group by t1.LOA_CODE, t1.EVALUATE_MAIN_ID, t1.YEAR_MONTH, t1.ICG_NO, t2.ICG_NO) th
        where mt.YEAR_MONTH = th.YEAR_MONTH
          and mt.CONFIRM_IS = '1'
          and mt.MODEL_DEF_ID = 1
          and rl.EVALUATE_MAIN_ID = mt.EVALUATE_MAIN_ID
          and rl.VAR1 = 'Lrc'
          and rl.ICG_NO = th.bf_icg;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');


        proc_evl_log_run(null, p_version, v_node, 'S');
    EXCEPTION
        WHEN OTHERS THEN
            proc_evl_log_run(null, p_version, v_node, 'E');
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, sqlerrm, 'S');

            raise_application_error(-20002, SQLERRM);

    end proc_evl_lrc_gmm_pre;

    procedure proc_evl_lrc_gmm_special(p_version varchar2, p_model_id number) is

        v_node      varchar(32);
        v_node_dt   varchar(32);

    begin

        v_node := 'GMM_SPEC';
        proc_evl_log_run(null, p_version, v_node, 'B');

        v_node_dt := 'GMM_SPEC_001';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_BUSS_EVALUATE_RESULT tg
        using (select t1.EVALUATE_MAIN_ID,
                      t2.ICG_NO,
                      case
                          when t2.PL_JUDGE_RSLT = 'L' then 2
                          when t2.PL_JUDGE_RSLT in ('O', 'P') then 1 end v1
               from qtc_BUSS_EVALUATE_MAIN t1,
                    atruser.atr_DAP_DD_PREMIUM t2
               where t1.VERSION_NO = p_version
                 and t1.MODEL_DEF_ID = p_model_id
                 and t1.BUSINESS_MODEL = 'D'
                 and t1.BUSINESS_DIRECTION = 'D'
                 and t2.LOA_CODE = t1.LOA_CODE
               group by t1.EVALUATE_MAIN_ID, t2.ICG_NO, t2.PL_JUDGE_RSLT) sc
        on (tg.EVALUATE_MAIN_ID = sc.EVALUATE_MAIN_ID and tg.ICG_NO = sc.ICG_NO and tg.VAR1 = 'Lrc')
        when matched then
            update set num54 = sc.v1;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'GMM_SPEC_002';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_BUSS_EVALUATE_RESULT tg
        using (select t1.EVALUATE_MAIN_ID,
                      t2.ICG_NO,
                      case
                          when t2.PL_JUDGE_RSLT = 'L' then 2
                          when t2.PL_JUDGE_RSLT in ('O', 'P') then 1 end v1
               from qtc_BUSS_EVALUATE_MAIN t1,
                    atruser.atr_DAP_TI_PREMIUM t2
               where t1.VERSION_NO = p_version
                 and t1.MODEL_DEF_ID = p_model_id
                 and t1.BUSINESS_MODEL = 'T'
                 and t1.BUSINESS_DIRECTION = 'I'
                 and t2.LOA_CODE = t1.LOA_CODE
               group by t1.EVALUATE_MAIN_ID, t2.ICG_NO, t2.PL_JUDGE_RSLT) sc
        on (tg.EVALUATE_MAIN_ID = sc.EVALUATE_MAIN_ID and tg.ICG_NO = sc.ICG_NO and tg.VAR1 = 'Lrc')
        when matched then
            update set num54 = sc.v1;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        proc_evl_log_run(null, p_version, v_node, 'S');


    EXCEPTION
        WHEN OTHERS THEN
            proc_evl_log_run(null, p_version, v_node, 'E');
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, sqlerrm, 'S');

            raise_application_error(-20002, SQLERRM);

    end proc_evl_lrc_gmm_special;

    procedure proc_evl_lrc_paa_cf(p_version varchar2, p_model_id number) is
        v_node    varchar(32);
        v_node_dt varchar(32);
    begin

        v_node := 'PAA_CF';
        proc_evl_log_run(null, p_version, v_node, 'B');

        v_node_dt := 'PAA_CF_001';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_paa_icg_cf(ICG_CF_ID, version_no, model_def_id, evaluate_main_id,
                                            icg_no, exp_ym, dev_period,
                                            num1, num2, num3, num4, num5)
        select qtc_seq_buss_evl_paa_unit_cf.nextval, TTT.*
        from (select uf.version_no,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.icg_no,
                     uf.EXP_YM,
                     uf.DEV_PERIOD,
                     sum(uf.RECV_PREMIUM),
                     sum(uf.iacf_fee),
                     sum(uf.MAINTENANCE_FEE),
                     sum(NVL(uf.ULTIMATE_LOSS, 0) + NVL(uf.ADJ_COMMISSION, 0)),
                     sum((NVL(uf.ULTIMATE_LOSS, 0) + NVL(uf.ADJ_COMMISSION, 0) + NVL(uf.MAINTENANCE_FEE, 0)))
              from qtc_BUSS_EVL_BM_DD_UNIT_CF uf
              where uf.VERSION_NO = p_version
                and uf.MODEL_DEF_ID = p_model_id
              group by uf.version_no,
                       uf.MODEL_DEF_ID,
                       uf.EVALUATE_MAIN_ID,
                       uf.icg_no,
                       uf.EXP_YM,
                       uf.DEV_PERIOD) ttt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'PAA_CF_002';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');
        insert into qtc_buss_evl_paa_icg_cf(ICG_CF_ID, version_no, model_def_id, evaluate_main_id,
                                            icg_no, exp_ym, dev_period,
                                            num1, num2, num3, num4, num5)
        select qtc_seq_buss_evl_paa_unit_cf.nextval, TTT.*
        from (select uf.version_no,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.icg_no,
                     uf.EXP_YM,
                     uf.DEV_PERIOD,
                     sum(uf.RECV_PREMIUM),
                     sum(uf.iacf_fee),
                     sum(uf.MAINTENANCE_FEE),
                     sum(NVL(uf.ULTIMATE_LOSS, 0) + NVL(uf.ADJ_COMMISSION, 0)),
                     sum((NVL(uf.ULTIMATE_LOSS, 0) + NVL(uf.ADJ_COMMISSION, 0) + NVL(uf.MAINTENANCE_FEE, 0)))
              from qtc_BUSS_EVL_BM_TI_UNIT_CF uf
              where uf.VERSION_NO = p_version
                and uf.MODEL_DEF_ID = p_model_id
              group by uf.version_no,
                       uf.MODEL_DEF_ID,
                       uf.EVALUATE_MAIN_ID,
                       uf.icg_no,
                       uf.EXP_YM,
                       uf.DEV_PERIOD) ttt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'PAA_CF_003';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');
        insert into qtc_buss_evl_paa_icg_cf(ICG_CF_ID, version_no, model_def_id, evaluate_main_id,
                                            icg_no, exp_ym, dev_period,
                                            num1, num2, num3, num4, num5)
        select qtc_seq_buss_evl_paa_unit_cf.nextval, TTT.*
        from (select uf.version_no,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.icg_no,
                     uf.EXP_YM,
                     uf.DEV_PERIOD,
                     sum(uf.RECV_PREMIUM),
                     sum(uf.iacf_fee),
                     sum(uf.MAINTENANCE_FEE),
                     sum(NVL(uf.ULTIMATE_LOSS, 0) + NVL(uf.ADJ_COMMISSION, 0)),
                     sum((NVL(uf.ULTIMATE_LOSS, 0) + NVL(uf.ADJ_COMMISSION, 0) + NVL(uf.MAINTENANCE_FEE, 0)))
              from qtc_BUSS_EVL_BM_FO_UNIT_CF uf
              where uf.VERSION_NO = p_version
                and uf.MODEL_DEF_ID = p_model_id
              group by uf.version_no,
                       uf.MODEL_DEF_ID,
                       uf.EVALUATE_MAIN_ID,
                       uf.icg_no,
                       uf.EXP_YM,
                       uf.DEV_PERIOD) ttt;
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'PAA_CF_004';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');
        insert into qtc_buss_evl_paa_icg_cf(ICG_CF_ID, version_no, model_def_id, evaluate_main_id,
                                            icg_no, exp_ym, dev_period,
                                            num1, num2, num3, num4, num5)
        select qtc_seq_buss_evl_paa_unit_cf.nextval, TTT.*
        from (select uf.version_no,
                     uf.MODEL_DEF_ID,
                     uf.EVALUATE_MAIN_ID,
                     uf.icg_no,
                     uf.EXP_YM,
                     uf.DEV_PERIOD,
                     sum(uf.RECV_PREMIUM),
                     sum(uf.iacf_fee),
                     sum(uf.MAINTENANCE_FEE),
                     sum(NVL(uf.ULTIMATE_LOSS, 0) + NVL(uf.ADJ_COMMISSION, 0)),
                     sum((NVL(uf.ULTIMATE_LOSS, 0) + NVL(uf.ADJ_COMMISSION, 0) + NVL(uf.MAINTENANCE_FEE, 0)))
              from qtc_BUSS_EVL_BM_TO_UNIT_CF uf
              where uf.VERSION_NO = p_version
                and uf.MODEL_DEF_ID = p_model_id
              group by uf.version_no,
                       uf.MODEL_DEF_ID,
                       uf.EVALUATE_MAIN_ID,
                       uf.icg_no,
                       uf.EXP_YM,
                       uf.DEV_PERIOD) ttt;
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'PAA_CF_005';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        MERGE INTO qtc_buss_evl_paa_icg_cf TG
        USING (SELECT PT.VERSION_NO, pt.MODEL_DEF_ID, PT.ICG_NO, PT.NUM3
               FROM qtc_BUSS_EVL_CFG_PT PT
               WHERE pt.VERSION_NO = p_version
                 and pt.MODEL_DEF_ID = p_model_id) SC
        ON (TG.VERSION_NO = SC.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID AND TG.ICG_NO = SC.ICG_NO)
        WHEN MATCHED THEN
            UPDATE SET NUM5 = TG.NUM5 * SC.NUM3;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');


        proc_evl_log_run(null, p_version, v_node, 'S');
    EXCEPTION
        WHEN OTHERS THEN
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, sqlerrm, 'E');
            proc_evl_log_run(null, p_version, v_node, 'E');

            raise_application_error(-20002, SQLERRM);
    end proc_evl_lrc_paa_cf ;

    procedure proc_evl_lrc_paa_cf_pv(p_version varchar2, p_model_id number) is
        v_node    varchar(32);
        v_node_dt varchar(32);
    begin

        v_node := 'PAA_PV';
        proc_evl_log_run(null, p_version, v_node, 'B');

        v_node_dt := 'PAA_PV_001';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_paa_icg_cf_pv(icg_cf_id, version_no, model_def_id,
                                               evaluate_main_id, icg_no, num1, num2, num3, num4, num5, num6, num7, num8)
        select qtc_seq_buss_evl_paa_icg_cf_pv.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVALUATE_MAIN_ID,
               ICG_NO,
               num1,
               num2,
               num3,
               num4,
               num5,
               coalesce(num1, 0) + coalesce(num2, 0) + coalesce(num3, 0) + coalesce(num4, 0) + coalesce(num5, 0),
               case
                   when coalesce(num1, 0) + coalesce(num3, 0) + coalesce(num4, 0) + coalesce(num5, 0) > 0 then
                       (coalesce(num1, 0) + coalesce(num3, 0) + coalesce(num4, 0)) * -1 end,
               case
                   when coalesce(num1, 0) + coalesce(num3, 0) + coalesce(num4, 0) + coalesce(num5, 0) > 0 then
                       coalesce(num5, 0) * -1 end
        from (select uf.VERSION_NO,
                     uf.EVALUATE_MAIN_ID,
                     UF.MODEL_DEF_ID,
                     uf.ICG_NO,
                     sum(uf.num1 * ir.flt5)                                     num1,
                     sum(uf.num2 * ir.flt6)                                     num2,
                     sum(uf.num3 * ir.flt7)                                     num3,
                     sum(uf.num4 * ir.flt8)                                     num4,
                     sum(uf.num5 * ir.flt8)                                     num5
              from qtc_buss_evl_paa_icg_cf uf,
                   qtc_BUSS_EVL_CFG_FR_IR ir
              where ir.VERSION_NO = p_version
                and ir.MODEL_DEF_ID = p_model_id
                and uf.VERSION_NO = ir.VERSION_NO
                and uf.MODEL_DEF_ID = ir.MODEL_DEF_ID
                and ir.ICG_NO = uf.ICG_NO
                and uf.DEV_PERIOD = ir.DEV_PERIOD
              group by uf.VERSION_NO,
                       uf.EVALUATE_MAIN_ID,
                       UF.MODEL_DEF_ID,
                       uf.ICG_NO) tt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'PAA_PV_002';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        MERGE INTO qtc_buss_evl_paa_icg_cf_pv TG
        USING (SELECT PT.VERSION_NO, pt.MODEL_DEF_ID, PT.ICG_NO, PT.NUM14
               FROM qtc_BUSS_EVL_CFG_PT PT
               WHERE PT.VERSION_NO = p_version
                 AND PT.MODEL_DEF_ID = p_model_id
                 ) SC
        ON (TG.VERSION_NO = SC.VERSION_NO AND tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and TG.ICG_NO = SC.ICG_NO)
        WHEN MATCHED THEN
            UPDATE SET NUM7 = TG.NUM7 * SC.NUM14, NUM8 = TG.NUM8 * SC.NUM14;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        proc_evl_log_run(null, p_version, v_node, 'S');
    EXCEPTION
        WHEN OTHERS THEN
            proc_evl_log_run(null, p_version, v_node, 'E');
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, sqlerrm, 'S');

            raise_application_error(-20002, SQLERRM);

    end proc_evl_lrc_paa_cf_pv ;

    procedure proc_evl_lrc_paa_pre(p_version varchar2, p_model_id number) is

        v_node      varchar(32);
        v_node_dt   varchar(32);
        v_ym        varchar2(6);
        v_rpt_start integer ;
        v_rpt_end   integer ;
        v_fir       integer ;
    begin

        select evl_ym,
               RPT_START,
               RPT_END,
               case when RPT_START = evl_ym then 1 else 0 end
        into v_ym, v_rpt_start, v_rpt_end, v_fir
        from qtc_BUSS_EVL_ICG_MSG
        where VERSION_NO = p_version
          and model_def_id = p_model_id
          and ROWNUM = 1;

        v_node := 'PAA_PRE';
        proc_evl_log_run(null, p_version, v_node, 'B');

        v_node_dt := 'PAA_PRE_03';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_paa_pre(paa_pre_id, version_no, model_def_id, evaluate_main_id,
                                         icg_no, buss_key, type_no, num1, num2, num4, num5)
        select qtc_seq_buss_evl_paa_pre.nextval,
               ttt.VERSION_NO,
               ttt.MODEL_DEF_ID,
               ttt.EVALUATE_MAIN_ID,
               ttt.ICG_NO,
               ttt.BUSS_KEY,
               'L3',
               ttt.v1,
               ttt.v2,
               ttt.v3,
               ttt.v4
        from (select /*+ leading(PT, WR, UF) */
                  PT.VERSION_NO,
                  PT.MODEL_DEF_ID,
                  PT.EVALUATE_MAIN_ID,
                  PT.ICG_NO,
                  uf.policy_no || '-' || uf.ENDORSE_SEQ_NO                            BUSS_KEY,
                  SUM(case when UF.DEV_PERIOD = 0 then UF.RECV_PREMIUM * wr.flt5 end) v1,
                  SUM(case when UF.DEV_PERIOD > 0 then UF.RECV_PREMIUM * wr.flt5 end) v2,
                  SUM(case when UF.DEV_PERIOD = 0 then UF.iacf_fee * wr.flt5 end)     v3,
                  SUM(case when UF.DEV_PERIOD > 0 then UF.iacf_fee * wr.flt5 end)     v4
              from qtc_BUSS_EVL_CFG_PT PT,
                   qtc_BUSS_EVL_CFG_WA_IR WR,
                   qtc_BUSS_EVL_BM_DD_UNIT_CF UF
              where PT.version_no = p_version
                and PT.MODEL_DEF_ID = p_model_id
                and pt.num20 = 1
                and WR.VERSION_NO = PT.VERSION_NO
                and wr.MODEL_DEF_ID = pt.MODEL_DEF_ID
                and WR.icg_no = PT.icg_no
                and UF.VERSION_NO = PT.VERSION_NO
                and uf.MODEL_DEF_ID = pt.MODEL_DEF_ID
                and UF.ICG_NO = PT.ICG_NO
                and uf.DEV_PERIOD = wr.DEV_PERIOD
              group by PT.VERSION_NO,
                       PT.MODEL_DEF_ID,
                       PT.EVALUATE_MAIN_ID,
                       PT.ICG_NO,
                       uf.policy_no || '-' || uf.ENDORSE_SEQ_NO) ttt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_031';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_paa_pre(paa_pre_id, version_no, model_def_id, evaluate_main_id,
                                         icg_no, buss_key, type_no, num1, num2, num4, num5)
        select qtc_seq_buss_evl_paa_pre.nextval,
               ttt.VERSION_NO,
               ttt.MODEL_DEF_ID,
               ttt.EVALUATE_MAIN_ID,
               ttt.ICG_NO,
               ttt.BUSS_KEY,
               'L3',
               ttt.v1,
               ttt.v2,
               ttt.v3,
               ttt.v4
        from (select /*+ leading(PT, UF) */
                  PT.VERSION_NO,
                  PT.MODEL_DEF_ID,
                  PT.EVALUATE_MAIN_ID,
                  PT.ICG_NO,
                  uf.policy_no || '-' || uf.ENDORSE_SEQ_NO                  BUSS_KEY,
                  SUM(case when UF.DEV_PERIOD = 0 then UF.RECV_PREMIUM end) v1,
                  SUM(case when UF.DEV_PERIOD > 0 then UF.RECV_PREMIUM end) v2,
                  SUM(case when UF.DEV_PERIOD = 0 then UF.iacf_fee end)     v3,
                  SUM(case when UF.DEV_PERIOD > 0 then UF.iacf_fee end)     v4
              from qtc_BUSS_EVL_CFG_PT PT,
                   qtc_BUSS_EVL_BM_DD_UNIT_CF UF
              where PT.version_no = p_version
                and PT.MODEL_DEF_ID = p_model_id
                and pt.num20 = 0
                and UF.VERSION_NO = PT.VERSION_NO
                and uf.MODEL_DEF_ID = pt.MODEL_DEF_ID
                and UF.ICG_NO = PT.ICG_NO
              group by PT.VERSION_NO,
                       PT.MODEL_DEF_ID,
                       PT.EVALUATE_MAIN_ID,
                       PT.ICG_NO,
                       uf.policy_no || '-' || uf.ENDORSE_SEQ_NO) ttt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_04';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_paa_pre(paa_pre_id, version_no, batch_no, model_def_id, evaluate_main_id,
                                         icg_no, buss_key, type_no, num1, num2, num4, num5)
        select qtc_seq_buss_evl_paa_pre.nextval,
               ttt.VERSION_NO,
               ttt.BATCH_NO,
               ttt.MODEL_DEF_ID,
               ttt.EVALUATE_MAIN_ID,
               ttt.ICG_NO,
               ttt.BUSS_KEY,
               'L3',
               ttt.v1,
               ttt.v2,
               ttt.v3,
               ttt.v4
        from (select /*+ leading(PT, WR, UF) */
                  PT.VERSION_NO,
                  PT.BATCH_NO,
                  PT.MODEL_DEF_ID,
                  PT.EVALUATE_MAIN_ID,
                  PT.ICG_NO,
                  uf.TREATY_NO                                                        BUSS_KEY,
                  SUM(case when UF.DEV_PERIOD = 0 then UF.RECV_PREMIUM * wr.flt5 end) v1,
                  SUM(case when UF.DEV_PERIOD > 0 then UF.RECV_PREMIUM * wr.flt5 end) v2,
                  SUM(case when UF.DEV_PERIOD = 0 then UF.iacf_fee * wr.flt5 end)     v3,
                  SUM(case when UF.DEV_PERIOD > 0 then UF.iacf_fee * wr.flt5 end)     v4
              from qtc_BUSS_EVL_CFG_PT PT,
                   qtc_BUSS_EVL_CFG_WA_IR WR,
                   qtc_BUSS_EVL_BM_TI_UNIT_CF UF
              where PT.version_no = p_version
                and PT.MODEL_DEF_ID = p_model_id
                and pt.num20 = 1
                and WR.VERSION_NO = PT.VERSION_NO
                and wr.MODEL_DEF_ID = pt.MODEL_DEF_ID
                and WR.icg_no = PT.icg_no
                and UF.VERSION_NO = PT.VERSION_NO
                and uf.MODEL_DEF_ID = pt.MODEL_DEF_ID
                and UF.ICG_NO = PT.ICG_NO
                and uf.DEV_PERIOD = wr.DEV_PERIOD
              group by PT.VERSION_NO,
                       PT.BATCH_NO,
                       PT.MODEL_DEF_ID,
                       PT.EVALUATE_MAIN_ID,
                       PT.ICG_NO,
                       uf.TREATY_NO) ttt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_041';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_paa_pre(paa_pre_id, version_no, model_def_id, evaluate_main_id,
                                         icg_no, buss_key, type_no, num1, num2, num4, num5)
        select qtc_seq_buss_evl_paa_pre.nextval,
               ttt.VERSION_NO,
               ttt.MODEL_DEF_ID,
               ttt.EVALUATE_MAIN_ID,
               ttt.ICG_NO,
               ttt.BUSS_KEY,
               'L3',
               ttt.v1,
               ttt.v2,
               ttt.v3,
               ttt.v4
        from (select PT.VERSION_NO,
                     PT.MODEL_DEF_ID,
                     PT.EVALUATE_MAIN_ID,
                     PT.ICG_NO,
                     uf.TREATY_NO                                              BUSS_KEY,
                     SUM(case when UF.DEV_PERIOD = 0 then UF.RECV_PREMIUM end) v1,
                     SUM(case when UF.DEV_PERIOD > 0 then UF.RECV_PREMIUM end) v2,
                     SUM(case when UF.DEV_PERIOD = 0 then UF.iacf_fee end)     v3,
                     SUM(case when UF.DEV_PERIOD > 0 then UF.iacf_fee end)     v4
              from qtc_BUSS_EVL_CFG_PT PT,
                   qtc_BUSS_EVL_BM_TI_UNIT_CF UF
              where PT.version_no = p_version
                and PT.MODEL_DEF_ID = p_model_id
                and pt.num20 = 0
                and UF.VERSION_NO = PT.VERSION_NO
                and uf.MODEL_DEF_ID = pt.MODEL_DEF_ID
                and UF.ICG_NO = PT.ICG_NO
              group by PT.VERSION_NO,
                       PT.MODEL_DEF_ID,
                       PT.EVALUATE_MAIN_ID,
                       PT.ICG_NO,
                       uf.TREATY_NO) ttt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_05';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_paa_pre(paa_pre_id, version_no, batch_no, model_def_id, evaluate_main_id,
                                         icg_no, buss_key, type_no, num1, num2, num4, num5)
        select qtc_seq_buss_evl_paa_pre.nextval,
               ttt.VERSION_NO,
               ttt.BATCH_NO,
               ttt.MODEL_DEF_ID,
               ttt.EVALUATE_MAIN_ID,
               ttt.ICG_NO,
               ttt.BUSS_KEY,
               'L3',
               ttt.v1,
               ttt.v2,
               ttt.v3,
               ttt.v4
        from (select /*+ leading(PT, WR, UF) */
                  PT.VERSION_NO,
                  PT.BATCH_NO,
                  PT.MODEL_DEF_ID,
                  PT.EVALUATE_MAIN_ID,
                  PT.ICG_NO,
                  uf.RI_policy_no || '-' || uf.RI_ENDORSE_SEQ_NO || UF.RI_STATEMENT_NO BUSS_KEY,
                  SUM(case when UF.DEV_PERIOD = 0 then UF.RECV_PREMIUM * wr.flt5 end)  v1,
                  SUM(case when UF.DEV_PERIOD > 0 then UF.RECV_PREMIUM * wr.flt5 end)  v2,
                  SUM(case when UF.DEV_PERIOD = 0 then UF.iacf_fee * wr.flt5 end)      v3,
                  SUM(case when UF.DEV_PERIOD > 0 then UF.iacf_fee * wr.flt5 end)      v4
              from qtc_BUSS_EVL_CFG_PT PT,
                   qtc_BUSS_EVL_CFG_WA_IR WR,
                   qtc_BUSS_EVL_BM_FO_UNIT_CF UF
              where PT.version_no = p_version
                and PT.MODEL_DEF_ID = p_model_id
                and pt.num20 = 1
                and WR.VERSION_NO = PT.VERSION_NO
                and wr.MODEL_DEF_ID = pt.MODEL_DEF_ID
                and WR.icg_no = PT.icg_no
                and UF.VERSION_NO = PT.VERSION_NO
                and uf.MODEL_DEF_ID = pt.MODEL_DEF_ID
                and UF.ICG_NO = PT.ICG_NO
                and uf.DEV_PERIOD = wr.DEV_PERIOD
              group by PT.VERSION_NO,
                       PT.BATCH_NO,
                       PT.MODEL_DEF_ID,
                       PT.EVALUATE_MAIN_ID,
                       PT.ICG_NO,
                       uf.RI_policy_no || '-' || uf.RI_ENDORSE_SEQ_NO || UF.RI_STATEMENT_NO) ttt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_05';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_paa_pre(paa_pre_id, version_no, batch_no, model_def_id, evaluate_main_id,
                                         icg_no, buss_key, type_no, num1, num2, num4, num5)
        select qtc_seq_buss_evl_paa_pre.nextval,
               ttt.VERSION_NO,
               ttt.BATCH_NO,
               ttt.MODEL_DEF_ID,
               ttt.EVALUATE_MAIN_ID,
               ttt.ICG_NO,
               ttt.BUSS_KEY,
               'L3',
               ttt.v1,
               ttt.v2,
               ttt.v3,
               ttt.v4
        from (select /*+ leading(PT, UF) */
                  PT.VERSION_NO,
                  PT.BATCH_NO,
                  PT.MODEL_DEF_ID,
                  PT.EVALUATE_MAIN_ID,
                  PT.ICG_NO,
                  uf.RI_policy_no || '-' || uf.RI_ENDORSE_SEQ_NO || UF.RI_STATEMENT_NO BUSS_KEY,
                  SUM(case when UF.DEV_PERIOD = 0 then UF.RECV_PREMIUM end)            v1,
                  SUM(case when UF.DEV_PERIOD > 0 then UF.RECV_PREMIUM end)            v2,
                  SUM(case when UF.DEV_PERIOD = 0 then UF.iacf_fee end)                v3,
                  SUM(case when UF.DEV_PERIOD > 0 then UF.iacf_fee end)                v4
              from qtc_BUSS_EVL_CFG_PT PT,
                   qtc_BUSS_EVL_BM_FO_UNIT_CF UF
              where PT.version_no = p_version
                and PT.MODEL_DEF_ID = p_model_id
                and pt.num20 = 1
                and UF.VERSION_NO = PT.VERSION_NO
                and uf.MODEL_DEF_ID = pt.MODEL_DEF_ID
                and UF.ICG_NO = PT.ICG_NO
              group by PT.VERSION_NO,
                       PT.BATCH_NO,
                       PT.MODEL_DEF_ID,
                       PT.EVALUATE_MAIN_ID,
                       PT.ICG_NO,
                       uf.RI_policy_no || '-' || uf.RI_ENDORSE_SEQ_NO || UF.RI_STATEMENT_NO) ttt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_06';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_paa_pre(paa_pre_id, version_no, batch_no, model_def_id, evaluate_main_id,
                                         icg_no, buss_key, type_no, num1, num2, num4, num5)
        select qtc_seq_buss_evl_paa_pre.nextval,
               ttt.VERSION_NO,
               ttt.BATCH_NO,
               ttt.MODEL_DEF_ID,
               ttt.EVALUATE_MAIN_ID,
               ttt.ICG_NO,
               ttt.BUSS_KEY,
               'L3',
               ttt.v1,
               ttt.v2,
               ttt.v3,
               ttt.v4
        from (select /*+ leading(PT, WR, UF) */
                  PT.VERSION_NO,
                  PT.BATCH_NO,
                  PT.MODEL_DEF_ID,
                  PT.EVALUATE_MAIN_ID,
                  PT.ICG_NO,
                  uf.TREATY_NO                                                        BUSS_KEY,
                  SUM(case when UF.DEV_PERIOD = 0 then UF.RECV_PREMIUM * wr.flt5 end) v1,
                  SUM(case when UF.DEV_PERIOD > 0 then UF.RECV_PREMIUM * wr.flt5 end) v2,
                  SUM(case when UF.DEV_PERIOD = 0 then UF.iacf_fee * wr.flt5 end)     v3,
                  SUM(case when UF.DEV_PERIOD > 0 then UF.iacf_fee * wr.flt5 end)     v4
              from qtc_BUSS_EVL_CFG_PT PT,
                   qtc_BUSS_EVL_CFG_WA_IR WR,
                   qtc_BUSS_EVL_BM_TO_UNIT_CF UF
              where PT.version_no = p_version
                and PT.MODEL_DEF_ID = p_model_id
                and pt.num20 = 1
                and WR.VERSION_NO = PT.VERSION_NO
                and wr.MODEL_DEF_ID = pt.MODEL_DEF_ID
                and WR.icg_no = PT.icg_no
                and UF.VERSION_NO = PT.VERSION_NO
                and uf.MODEL_DEF_ID = pt.MODEL_DEF_ID
                and UF.ICG_NO = PT.ICG_NO
                and uf.DEV_PERIOD = wr.DEV_PERIOD
              group by PT.VERSION_NO,
                       PT.BATCH_NO,
                       PT.MODEL_DEF_ID,
                       PT.EVALUATE_MAIN_ID,
                       PT.ICG_NO,
                       uf.TREATY_NO) ttt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_06';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_paa_pre(paa_pre_id, version_no, batch_no, model_def_id, evaluate_main_id,
                                         icg_no, buss_key, type_no, num1, num2, num4, num5)
        select qtc_seq_buss_evl_paa_pre.nextval,
               ttt.VERSION_NO,
               ttt.BATCH_NO,
               ttt.MODEL_DEF_ID,
               ttt.EVALUATE_MAIN_ID,
               ttt.ICG_NO,
               ttt.BUSS_KEY,
               'L3',
               ttt.v1,
               ttt.v2,
               ttt.v3,
               ttt.v4
        from (select /*+ leading(PT, UF) */
                  PT.VERSION_NO,
                  PT.BATCH_NO,
                  PT.MODEL_DEF_ID,
                  PT.EVALUATE_MAIN_ID,
                  PT.ICG_NO,
                  uf.TREATY_NO                                              BUSS_KEY,
                  SUM(case when UF.DEV_PERIOD = 0 then UF.RECV_PREMIUM end) v1,
                  SUM(case when UF.DEV_PERIOD > 0 then UF.RECV_PREMIUM end) v2,
                  SUM(case when UF.DEV_PERIOD = 0 then UF.iacf_fee end)     v3,
                  SUM(case when UF.DEV_PERIOD > 0 then UF.iacf_fee end)     v4
              from qtc_BUSS_EVL_CFG_PT PT,
                   qtc_BUSS_EVL_BM_TO_UNIT_CF UF
              where PT.version_no = p_version
                and PT.MODEL_DEF_ID = p_model_id
                and pt.num20 = 0
                and UF.VERSION_NO = PT.VERSION_NO
                and uf.MODEL_DEF_ID = pt.MODEL_DEF_ID
                and UF.ICG_NO = PT.ICG_NO
              group by PT.VERSION_NO,
                       PT.BATCH_NO,
                       PT.MODEL_DEF_ID,
                       PT.EVALUATE_MAIN_ID,
                       PT.ICG_NO,
                       uf.TREATY_NO) ttt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_07';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_paa_pre(paa_pre_id, version_no, batch_no, model_def_id, evaluate_main_id,
                                         year_month, icg_no, buss_key, type_no,
                                         num1, num2, num3, num4, num5)
        select qtc_seq_buss_evl_paa_pre.nextval,
               sc.VERSION_NO,
               sc.BATCH_NO,
               sc.MODEL_DEF_ID,
               sc.EVL_MAIN_ID,
               sc.EVL_YM,
               sc.ICG_NO,
               ut.policy_no || '-' || ut.endorse_seq_no,
               'L4',
               case when sc.RPT_START = sc.EVL_YM then 1 else 0 end,
               ut.cur_end_remain_un_rate,
               case
                   when ut.rpt_per_remain_un_rate = 0 then 0
                   else ut.cur_end_remain_un_rate / ut.rpt_per_remain_un_rate end,
               ut.lp_cur_end_remain_un_rate - ut.cur_end_remain_un_rate,
               ut.PREMIUM
        from qtc_BUSS_EVL_ICG_MSG sc,
             qtc_buss_evl_bm_dd_unit ut
        where sc.version_no = p_version
          and sc.MODEL_DEF_ID = p_model_id
          and sc.CAL_TYPE = 'Lrc'
          and ut.evaluate_main_id = sc.evl_main_id
          and ut.icg_no = sc.icg_no;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_08';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_paa_pre(paa_pre_id, version_no, batch_no, model_def_id, evaluate_main_id,
                                         year_month, icg_no, buss_key, type_no,
                                         num1, num2, num3, num4, num5)
        select qtc_seq_buss_evl_paa_pre.nextval,
               sc.VERSION_NO,
               sc.BATCH_NO,
               sc.MODEL_DEF_ID,
               sc.EVL_MAIN_ID,
               sc.EVL_YM,
               sc.ICG_NO,
               ut.ri_policy_no || '-' || ut.ri_endorse_seq_no || UT.RI_STATEMENT_NO,
               'L4',
               case when sc.RPT_START = sc.EVL_YM then 1 else 0 end,
               ut.cur_end_remain_un_rate,
               case
                   when ut.rpt_per_remain_un_rate = 0 then 0
                   else ut.cur_end_remain_un_rate / ut.rpt_per_remain_un_rate end,
               ut.lp_cur_end_remain_un_rate - ut.cur_end_remain_un_rate,
               ut.premium
        from qtc_BUSS_EVL_ICG_MSG sc,
             qtc_buss_evl_bm_fo_unit ut
        where sc.version_no = p_version
          and sc.MODEL_DEF_ID = p_model_id
          and sc.CAL_TYPE = 'Lrc'
          and ut.evaluate_main_id = sc.evl_main_id
          and ut.icg_no = sc.icg_no;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_09';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_paa_pre(paa_pre_id, version_no, batch_no, model_def_id, evaluate_main_id,
                                         year_month, icg_no, buss_key, type_no,
                                         num1, num2, num3, num4, num5)
        select qtc_seq_buss_evl_paa_pre.nextval,
               sc.VERSION_NO,
               sc.BATCH_NO,
               sc.MODEL_DEF_ID,
               sc.EVL_MAIN_ID,
               sc.EVL_YM,
               sc.ICG_NO,
               ut.TREATY_NO,
               'L4',
               case when sc.RPT_START = sc.EVL_YM then 1 else 0 end,
               ut.cur_end_remain_un_rate,
               case
                   when ut.rpt_per_remain_un_rate = 0 then 0
                   else ut.cur_end_remain_un_rate / ut.rpt_per_remain_un_rate end,
               ut.lp_cur_end_remain_un_rate - ut.cur_end_remain_un_rate,
               ut.premium
        from qtc_BUSS_EVL_ICG_MSG sc,
             qtc_buss_evl_bm_ti_unit ut
        where sc.version_no = p_version
          and sc.MODEL_DEF_ID = p_model_id
          and sc.CAL_TYPE = 'Lrc'
          and ut.evaluate_main_id = sc.evl_main_id
          and ut.icg_no = sc.icg_no;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_10';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_paa_pre(paa_pre_id, version_no, batch_no, model_def_id, evaluate_main_id,
                                         year_month, icg_no, buss_key, type_no,
                                         num1, num2, num3, num4, num5)
        select qtc_seq_buss_evl_paa_pre.nextval,
               sc.VERSION_NO,
               sc.BATCH_NO,
               sc.MODEL_DEF_ID,
               sc.EVL_MAIN_ID,
               sc.EVL_YM,
               sc.ICG_NO,
               ut.TREATY_NO,
               'L4',
               case when sc.RPT_START = sc.EVL_YM then 1 else 0 end,
               ut.cur_end_remain_un_rate,
               case
                   when ut.rpt_per_remain_un_rate = 0 then 0
                   else ut.cur_end_remain_un_rate / ut.rpt_per_remain_un_rate end,
               ut.lp_cur_end_remain_un_rate - ut.cur_end_remain_un_rate,
               ut.premium
        from qtc_BUSS_EVL_ICG_MSG sc,
             qtc_buss_evl_bm_to_unit ut
        where sc.version_no = p_version
          and sc.MODEL_DEF_ID = p_model_id
          and sc.CAL_TYPE = 'Lrc'
          and ut.evaluate_main_id = sc.evl_main_id
          and ut.icg_no = sc.icg_no;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_0101';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_paa_pre(paa_pre_id, version_no, model_def_id, evaluate_main_id,
                                         year_month, icg_no, buss_key, type_no,
                                         num1, num2, num3, num4, num5, num6, num7)
        select qtc_seq_buss_evl_paa_pre.nextval,
               sc.VERSION_NO,
               sc.MODEL_DEF_ID,
               sc.EVL_MAIN_ID,
               sc.EVL_YM,
               sc.ICG_NO,
               ut.BUSS_KEY,
               'L2',
               ut.num1,
               ut.num3,
               ut.num4,
               ut.num5,
               ut.num7,
               ut.num9,
               ut.num10
        from qtc_BUSS_EVL_ICG_MSG sc,
             qtc_BUSS_EVL_PAA_CAL_UNIT UT,
             qtc_buss_evl_paa_pre pe
        where sc.VERSION_NO = p_version
          and sc.MODEL_DEF_ID = p_model_id
          and sc.CAL_TYPE = 'Lrc'
          and UT.VERSION_NO = sc.VERSION_PRE
          and UT.icg_no = sc.icg_no
          and ut.TYPE_NO = 'A1'
          and pe.VERSION_NO = sc.VERSION_NO
          and pe.ICG_NO = sc.ICG_NO
          and pe.BUSS_KEY = ut.BUSS_KEY
          and pe.TYPE_NO = 'L4';

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_02';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        merge into qtc_buss_evl_paa_pre tg
        using (select /*+ leading(PT, WR) */
                   wr.VERSION_NO, wr.MODEL_DEF_ID, wr.ICG_NO, wr.flt2 v1
               from qtc_BUSS_EVL_CFG_PT PT,
                    qtc_BUSS_EVL_CFG_WA_IR WR
               where PT.version_no = p_version
                 and PT.MODEL_DEF_ID = p_model_id
                 and wr.VERSION_NO = pt.VERSION_NO
                 and wr.MODEL_DEF_ID = pt.MODEL_DEF_ID
                 and wr.icg_no = pt.icg_no
                 and pt.num20 = 1
                 and wr.DEV_PERIOD = 0) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO and
            tg.TYPE_NO = 'L2')
        when matched then
            update
            set num1 = tg.num1 * sc.v1,
                num2 = tg.num2 * sc.v1,
                num3 = tg.num3 * sc.v1,
                num4 = tg.num4 * sc.v1,
                num5 = tg.num5 * sc.v1,
                num6 = tg.num6 * sc.v1,
                num7 = tg.num7 * sc.v1;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_11';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_paa_pre(paa_pre_id, version_no, model_def_id, evaluate_main_id,
                                         icg_no, year_month, type_no,
                                         num4, num5, num9)
        select qtc_seq_buss_evl_paa_pre.nextval, ttt.*
        from (select ut.VERSION_NO,
                     ut.MODEL_DEF_ID,
                     ut.EVALUATE_MAIN_ID,
                     ut.ICG_NO,
                     ut.YEAR_MONTH,
                     'L1',
                     sum(ut.CUR_PREMIUM),
                     sum(ut.CUR_IACF),
                     sum(ut.INVEST_AMOUNT * (ut.LP_CUR_END_REMAIN_UN_RATE - ut.CUR_END_REMAIN_UN_RATE))
              from qtc_buss_evl_bm_dd_unit ut
              where ut.VERSION_NO = p_version
                and ut.MODEL_DEF_ID = p_model_id
              group by ut.VERSION_NO,
                       ut.MODEL_DEF_ID,
                       ut.EVALUATE_MAIN_ID,
                       ut.ICG_NO,
                       ut.YEAR_MONTH) ttt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_12';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_paa_pre(paa_pre_id, version_no, model_def_id, evaluate_main_id,
                                         icg_no, year_month, type_no,
                                         num4,num5, num9)
        select qtc_seq_buss_evl_paa_pre.nextval, ttt.*
        from (select ut.VERSION_NO,
                     ut.MODEL_DEF_ID,
                     ut.EVALUATE_MAIN_ID,
                     ut.ICG_NO,
                     ut.YEAR_MONTH,
                     'L1',
                     sum(ut.CUR_PREMIUM),
                      sum(ut.CUR_IACF),
                     sum(ut.INVEST_AMOUNT * (ut.LP_CUR_END_REMAIN_UN_RATE - ut.CUR_END_REMAIN_UN_RATE))
              from qtc_buss_evl_bm_fo_unit ut
              where ut.VERSION_NO = p_version
                and ut.MODEL_DEF_ID = p_model_id
              group by ut.VERSION_NO,
                       ut.MODEL_DEF_ID,
                       ut.EVALUATE_MAIN_ID,
                       ut.ICG_NO,
                       ut.YEAR_MONTH) ttt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_13';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_paa_pre(paa_pre_id, version_no, model_def_id, evaluate_main_id,
                                         icg_no, year_month, type_no,
                                         num4, num5, num9)
        select qtc_seq_buss_evl_paa_pre.nextval, ttt.*
        from (select ut.VERSION_NO,
                     ut.MODEL_DEF_ID,
                     ut.EVALUATE_MAIN_ID,
                     ut.ICG_NO,
                     ut.YEAR_MONTH,
                     'L1',
                     sum(ut.CUR_PREMIUM),
                     sum(ut.CUR_IACF),
                     sum(ut.INVEST_AMOUNT * (ut.LP_CUR_END_REMAIN_UN_RATE - ut.CUR_END_REMAIN_UN_RATE))
              from qtc_buss_evl_bm_ti_unit ut
              where ut.VERSION_NO = p_version
                and ut.MODEL_DEF_ID = p_model_id
              group by ut.VERSION_NO,
                       ut.MODEL_DEF_ID,
                       ut.EVALUATE_MAIN_ID,
                       ut.ICG_NO,
                       ut.YEAR_MONTH) ttt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_14';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_paa_pre(paa_pre_id, version_no, model_def_id, evaluate_main_id,
                                         icg_no, year_month, type_no,
                                         num4, num5, num9)
        select qtc_seq_buss_evl_paa_pre.nextval, ttt.*
        from (select ut.VERSION_NO,
                     ut.MODEL_DEF_ID,
                     ut.EVALUATE_MAIN_ID,
                     ut.ICG_NO,
                     ut.YEAR_MONTH,
                     'L1',
                     sum(ut.CUR_PREMIUM),
                     sum(ut.CUR_IACF),
                     sum(ut.INVEST_AMOUNT * (ut.LP_CUR_END_REMAIN_UN_RATE - ut.CUR_END_REMAIN_UN_RATE))
              from qtc_buss_evl_bm_to_unit ut
              where ut.VERSION_NO = p_version
                and ut.MODEL_DEF_ID = p_model_id
              group by ut.VERSION_NO,
                       ut.MODEL_DEF_ID,
                       ut.EVALUATE_MAIN_ID,
                       ut.ICG_NO,
                       ut.YEAR_MONTH) ttt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_15';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_16';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');
        merge into qtc_buss_evl_paa_pre tg
        using (select pt.VERSION_NO,
                      pt.MODEL_DEF_ID,
                      pt.ICG_NO,
                      pt.num20 v1,
                      wa.flt1  v2,
                      pt.num5  v3
               from qtc_BUSS_EVL_CFG_WA_IR wa,
                    qtc_BUSS_EVL_CFG_pt pt
               where pt.VERSION_NO = p_version
                 and pt.MODEL_DEF_ID = p_model_id
                 and wa.VERSION_NO = pt.VERSION_NO
                 and wa.MODEL_DEF_ID = pt.MODEL_DEF_ID
                 and wa.ICG_NO = pt.ICG_NO
                 and wa.DEV_PERIOD = 0) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO and
            tg.TYPE_NO = 'L1')
        when matched then
            update set num1 = sc.v1, num2 = sc.v2, num3 = sc.v3;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_17';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        merge into qtc_buss_evl_paa_pre tg
        using (select mg.VERSION_NO, mg.MODEL_DEF_ID, mg.ICG_NO, rl.num5, rl.num6, rl.num8, rl.num11
               from qtc_BUSS_EVL_ICG_MSG mg,
                    qtc_BUSS_EVALUATE_MAIN mt,
                    qtc_buss_evaluate_result rl
               where mg.VERSION_NO = p_version
                 and mg.MODEL_DEF_ID = p_model_id
                 and mg.CAL_TYPE = 'Lrc'
                 and mt.VERSION_NO = mg.VERSION_PRE
                 and mt.MODEL_DEF_ID = mg.MODEL_DEF_ID
                 and mt.LOA_CODE = mg.LOA_CODE
                 and rl.EVALUATE_MAIN_ID = mt.EVALUATE_MAIN_ID
                 and rl.ICG_NO = mg.ICG_NO
                 and rl.VAR1 = 'Lrc') sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO and
            tg.TYPE_NO = 'L1')
        when matched then
            update set num7 = sc.num5, num8 = sc.num6, num11 = sc.NUM8, num12 = sc.num11;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_17';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_paa_pre(paa_pre_id, version_no, model_def_id, evaluate_main_id,
                                         icg_no, year_month, type_no,
                                         num7, num8, num14)
        select qtc_seq_buss_evl_paa_pre.nextval,
               mt.VERSION_NO,
               lmt.MODEL_DEF_ID,
               mt.EVALUATE_MAIN_ID,
               rl.ICG_NO,
               mt.YEAR_MONTH,
               'L1',
               rl.num5,
               rl.num6,
               1
        from (select t.VERSION_NO, t.YEAR_MONTH, t.LOA_CODE, t.EVALUATE_MAIN_ID
              from qtc_buss_evaluate_main t
              where t.version_no = p_version
                and t.MODEL_DEF_ID = p_model_id) mt,
             qtc_buss_evaluate_main lmt,
             qtc_buss_evaluate_result rl
        where lmt.CONFIRM_IS = '1'
          and lmt.MODEL_DEF_ID = p_model_id
          and lmt.BUSINESS_DIRECTION in ('D', 'I')
          and lmt.YEAR_MONTH = to_char(add_months(to_date(mt.YEAR_MONTH, 'yyyymm'), -1), 'yyyymm')
          and lmt.LOA_CODE = mt.LOA_CODE
          and rl.EVALUATE_MAIN_ID = lmt.EVALUATE_MAIN_ID
          and rl.VAR1 = 'Lrc'
          and abs(rl.num5) >= 1
          and not exists (select 1
                          from qtc_buss_evl_paa_pre p
                          where p.VERSION_NO = p_version
                            and p.MODEL_DEF_ID = p_model_id
                            and p.ICG_NO = rl.ICG_NO);


        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_18';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        merge into qtc_buss_evl_paa_pre tg
        using (select pv.VERSION_NO, pv.MODEL_DEF_ID, pv.ICG_NO, pv.num6 v1
               from qtc_buss_evl_paa_icg_cf_pv pv
               where pv.VERSION_NO = p_version
                 and pv.MODEL_DEF_ID = p_model_id) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO and
            tg.TYPE_NO = 'L1')
        when matched then
            update set num6 = sc.v1;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_19';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        insert into qtc_buss_evl_paa_pre(paa_pre_id, version_no, model_def_id, evaluate_main_id,
                                         icg_no, year_month, type_no,
                                         num1, num2, num3)
        select qtc_seq_buss_evl_paa_pre.nextval,
               pv.VERSION_NO,
               pv.MODEL_DEF_ID,
               pv.EVALUATE_MAIN_ID,
               pv.ICG_NO,
               v_ym,
               'L5',
               pv.num8,
               pv.num7,
               v_fir
        from qtc_buss_evl_paa_icg_cf_pv pv
        where pv.VERSION_NO = p_version
          and pv.MODEL_DEF_ID = p_model_id;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_20';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        merge into qtc_buss_evl_paa_pre tg
        using (select th.EVALUATE_MAIN_ID, th.N1, rl.num6 * th.RATE num5
               from qtc_buss_evaluate_main mt,
                    qtc_buss_evaluate_result rl,
                    (select t1.EVALUATE_MAIN_ID,
                            t1.YEAR_MONTH,
                            t1.ICG_NO                         N1,
                            t2.ICG_NO                         N2,
                            sum(t1.PREMIUM) / sum(t2.PREMIUM) RATE
                     from qtc_buss_evl_bm_fo_unit t1,
                          QTC_DAP_ECF_DD_ICU t2
                     where t1.VERSION_NO = p_version
                       and t1.MODEL_DEF_ID = p_model_id
                       and t1.POLICY_NO = t2.POLICY_NO
                       and t1.ENDORSE_SEQ_NO = t2.ENDORSE_SEQ_NO
                     group by t1.EVALUATE_MAIN_ID, t1.YEAR_MONTH, t1.ICG_NO, t2.ICG_NO) th
               where mt.YEAR_MONTH = th.YEAR_MONTH
                 and mt.CONFIRM_IS = '1'
                 and mt.MODEL_DEF_ID = 3
                 and rl.EVALUATE_MAIN_ID = mt.EVALUATE_MAIN_ID
                 and rl.VAR1 = 'Lrc'
                 and rl.ICG_NO = th.N2) sc
        on (tg.EVALUATE_MAIN_ID = sc.EVALUATE_MAIN_ID and tg.ICG_NO = sc.N1 and tg.TYPE_NO = 'L5')
        when matched then
            update set num5 = sc.num5;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_PRE_25';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

        merge into qtc_buss_evl_paa_pre tg
        using (select cr.EVL_MAIN_ID, cr.ICG_NO, cr.CR_PREMIUM, cr.CR_IACF
               from qtc_BUSS_EVALUATE_MAIN mt,
                    qtc_BUSS_EVL_ICG_CUR cr
               where mt.VERSION_NO = p_version
                 and mt.MODEL_DEF_ID = p_model_id
                 and cr.evl_main_id = mt.EVALUATE_MAIN_ID) sc
        on (tg.EVALUATE_MAIN_ID = sc.EVL_MAIN_ID and tg.ICG_NO = sc.ICG_NO and tg.TYPE_NO = 'L1')
        when matched then
            update set num4 = sc.CR_PREMIUM, num5 = sc.CR_IACF;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        proc_evl_log_run(null, p_version, v_node, 'S');
    EXCEPTION
        WHEN OTHERS THEN
            proc_evl_log_run(null, p_version, v_node, 'E');
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, sqlerrm, 'S');

            raise_application_error(-20002, SQLERRM);

    end proc_evl_lrc_paa_pre;

    procedure proc_evl_lrc_paa_special(p_version varchar2, p_model_id number) is

        v_node      varchar(32);
        v_node_dt   varchar(32);

    begin

        v_node := 'PAA_SPEC';
        proc_evl_log_run(null, p_version, v_node, 'B');

        v_node_dt := 'PAA_SPEC_001';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_BUSS_EVALUATE_RESULT tg
        using (select t1.EVALUATE_MAIN_ID,
                      t2.ICG_NO,
                      case
                          when t2.PL_JUDGE_RSLT = 'L' then 2
                          when t2.PL_JUDGE_RSLT in ('O', 'P') then 1 end v1
               from qtc_BUSS_EVALUATE_MAIN t1,
                    atruser.atr_DAP_DD_PREMIUM t2
               where t1.VERSION_NO = p_version
                 and t1.MODEL_DEF_ID = p_model_id
                 and t1.BUSINESS_MODEL = 'D'
                 and t1.BUSINESS_DIRECTION = 'D'
                 and t2.LOA_CODE = t1.LOA_CODE
               group by t1.EVALUATE_MAIN_ID, t2.ICG_NO, t2.PL_JUDGE_RSLT) sc
        on (tg.EVALUATE_MAIN_ID = sc.EVALUATE_MAIN_ID and tg.ICG_NO = sc.ICG_NO and tg.VAR1 = 'Lrc')
        when matched then
            update set num12 = sc.v1;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PAA_SPEC_002';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_BUSS_EVALUATE_RESULT tg
        using (select t1.EVALUATE_MAIN_ID,
                      t2.ICG_NO,
                      case
                          when t2.PL_JUDGE_RSLT = 'L' then 2
                          when t2.PL_JUDGE_RSLT in ('O', 'P') then 1 end v1
               from qtc_BUSS_EVALUATE_MAIN t1,
                    atruser.atr_DAP_TI_PREMIUM t2
               where t1.VERSION_NO = p_version
                 and t1.MODEL_DEF_ID = p_model_id
                 and t1.BUSINESS_MODEL = 'T'
                 and t1.BUSINESS_DIRECTION = 'I'
                 and t2.LOA_CODE = t1.LOA_CODE
               group by t1.EVALUATE_MAIN_ID, t2.ICG_NO, t2.PL_JUDGE_RSLT) sc
        on (tg.EVALUATE_MAIN_ID = sc.EVALUATE_MAIN_ID and tg.ICG_NO = sc.ICG_NO and tg.VAR1 = 'Lrc')
        when matched then
            update set num12 = sc.v1;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        proc_evl_log_run(null, p_version, v_node, 'S');


    EXCEPTION
        WHEN OTHERS THEN
            proc_evl_log_run(null, p_version, v_node, 'E');
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, sqlerrm, 'S');

            raise_application_error(-20002, SQLERRM);

    end proc_evl_lrc_paa_special;

    procedure proc_evl_lic_cf_pv(p_version varchar2) is
        v_node    varchar(32);
        v_node_dt varchar(32);

    BEGIN

        v_node := 'LIC_PV';
        proc_evl_log_run(null, p_version, v_node, 'B');

        v_node_dt := 'LIC_PV_01';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        insert into qtc_BUSS_EVL_LIC_ICG_PV(icg_cf_id, version_no,
                                            model_def_id, evaluate_main_id, loa_code, icg_no, year_month,
                                            cf_type, num1, num2, num3, num4, num5, num6, num7, num8)
        select qtc_seq_buss_evl_lic_icg_pv.nextval,
               version_no,
               model_def_id,
               evaluate_main_id,
               loa_code,
               icg_no,
               year_month,
               'C11',
               num1,
               NUM2,
               num3,
               num4,
               num5,
               num6,
               num7,
               num8
        from (select gcf.version_no,
                     gcf.LOA_CODE,
                     gcf.model_def_id,
                     gcf.evaluate_main_id,
                     gcf.icg_no,
                     gcf.year_month,
                     SUM(GCF.NUM1 * IR.FLT8) NUM1,
                     SUM(GCF.NUM2 * IR.FLT8) NUM2,
                     SUM(GCF.NUM3 * IR.FLT8) NUM3,
                     SUM(GCF.NUM4 * IR.FLT8) NUM4,
                     SUM(GCF.NUM5 * IR.FLT8) NUM5,
                     SUM(GCF.NUM6 * IR.FLT8) NUM6,
                     SUM(GCF.NUM7 * IR.FLT8) NUM7,
                     SUM(GCF.NUM8 * IR.FLT8) NUM8
              from qtc_BUSS_EVL_LIC_ICG_CF gcf,
                   qtc_BUSS_EVL_CFG_FR_IR ir
              where ir.VERSION_NO = p_version
                and gcf.VERSION_NO = ir.VERSION_NO
                and gcf.MODEL_DEF_ID = ir.MODEL_DEF_ID
                AND gcf.ICG_NO = ir.ICG_NO
                and gcf.DEV_PERIOD = ir.DEV_PERIOD
                and gcf.DEV_PERIOD > 0
              GROUP BY gcf.version_no,
                       gcf.LOA_CODE,
                       gcf.model_def_id,
                       gcf.evaluate_main_id,
                       gcf.icg_no,
                       gcf.year_month) TT;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'LIC_PV_02';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        insert into qtc_BUSS_EVL_LIC_ICG_PV(icg_cf_id, version_no,
                                            model_def_id, evaluate_main_id, loa_code, icg_no, year_month,
                                            cf_type, num1, num2, num3, num4, num5, num6, num7, num8)
        select qtc_seq_buss_evl_lic_icg_pv.nextval,
               version_no,
               model_def_id,
               evaluate_main_id,
               loa_code,
               icg_no,
               year_month,
               'C13',
               num1,
               NUM2,
               num3,
               num4,
               num5,
               num6,
               num7,
               num8
        from (select gcf.version_no,
                     gcf.LOA_CODE,
                     gcf.model_def_id,
                     gcf.evaluate_main_id,
                     gcf.icg_no,
                     gcf.year_month,
                     SUM(GCF.NUM1 * IR.FLT8) NUM1,
                     SUM(GCF.NUM2 * IR.FLT8) NUM2,
                     SUM(GCF.NUM3 * IR.FLT8) NUM3,
                     SUM(GCF.NUM4 * IR.FLT8) NUM4,
                     SUM(GCF.NUM5 * IR.FLT8) NUM5,
                     SUM(GCF.NUM6 * IR.FLT8) NUM6,
                     SUM(GCF.NUM7 * IR.FLT8) NUM7,
                     SUM(GCF.NUM8 * IR.FLT8) NUM8
              from qtc_BUSS_EVL_LIC_ICG_CF gcf,
                   qtc_BUSS_EVL_CFG_WA_IR ir
               where ir.VERSION_NO = p_version
                and gcf.VERSION_NO = ir.VERSION_NO
                and gcf.MODEL_DEF_ID = ir.MODEL_DEF_ID
                AND gcf.ICG_NO = ir.ICG_NO
                and gcf.DEV_PERIOD = ir.DEV_PERIOD
                and gcf.DEV_PERIOD > 0
              GROUP BY gcf.version_no,
                       gcf.LOA_CODE,
                       gcf.model_def_id,
                       gcf.evaluate_main_id,
                       gcf.icg_no,
                       gcf.year_month) TT;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        proc_evl_log_run(null, p_version, v_node, 's');
    EXCEPTION
        WHEN OTHERS THEN
            proc_evl_log_run(null, p_version, v_node, 'E');
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, sqlerrm, 'E');

            raise_application_error(-20002, SQLERRM);
    END proc_evl_lic_cf_pv;

    procedure proc_evl_lic_pre(p_version varchar2) is
        v_node    varchar(32);
        v_node_dt varchar(32);
         v_ym      varchar2(6);
        v_rpt_start  integer ;
        v_rpt_end   integer ;
        v_fir       integer ;

    begin

        v_node := 'PRE_LIC';
        proc_evl_log_run(null, p_version, v_node, 'B');

        select evl_ym,
               RPT_START,
               RPT_END,
               case when RPT_START = evl_ym then 1 else 0 end
        into v_ym,  v_rpt_start, v_rpt_end, v_fir
        from qtc_BUSS_EVL_ICG_MSG
        where VERSION_NO = p_version
          and ROWNUM = 1;

        v_node_dt := 'PRE_LIC_01';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        IF v_fir = 0 THEN
            insert into qtc_BUSS_EVL_ICG_MSG(ICG_MSG_ID, version_no, VERSION_PRE,
                                             evaluate_approach, model_def_id, evl_ym, evl_main_id,
                                             loa_code, icg_no, cal_type,
                                             rpt_start, rpt_end, rpt_bwt, rpt_num, STATE)
            select qtc_SEQ_BUSS_EVL_ICG_MSG.nextval,
                   mt.VERSION_NO,
                   lmt.VERSION_NO,
                   MS.EVALUATE_APPROACH,
                   mt.MODEL_DEF_ID,
                   mt.YEAR_MONTH,
                   mt.EVALUATE_MAIN_ID,
                   mt.LOA_CODE,
                   ms.ICG_NO,
                   ms.CAL_TYPE,
                   ms.rpt_start,
                   ms.rpt_end,
                   ms.rpt_bwt + 1,
                   ms.rpt_num,
                   9
            from qtc_buss_evaluate_main mt,
                 qtc_buss_evaluate_main lmt,
                 qtc_BUSS_EVL_ICG_MSG ms
            where mt.VERSION_NO = p_version
              and lmt.CONFIRM_IS = '1'
              and lmt.MODEL_DEF_ID = mt.MODEL_DEF_ID
              and lmt.YEAR_MONTH = to_char(add_months(to_date(mt.YEAR_MONTH, 'yyyymm'), -1), 'yyyymm')
              and lmt.LOA_CODE = mt.LOA_CODE
              and ms.VERSION_NO = lmt.VERSION_NO
              and ms.MODEL_DEF_ID = lmt.MODEL_DEF_ID
              and ms.LOA_CODE = lmt.LOA_CODE
              and ms.CAL_TYPE = 'Lic'
              and not exists (select 1
                              from qtc_BUSS_EVL_ICG_MSG mg
                              where mg.VERSION_NO = mt.VERSION_NO
                                and mg.MODEL_DEF_ID = mt.MODEL_DEF_ID
                                and mg.CAL_TYPE = ms.CAL_TYPE
                                and mg.ICG_NO = ms.ICG_NO);
        END IF;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_02';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_BUSS_EVL_LIC_PRE(ICG_PRE_ID, version_no,  model_def_id, evaluate_main_id,
                                         icg_no, type_no,
                                         NUM1, NUM2, NUM3, NUM4, NUM5)
        select qtc_seq_buss_evl_lic_pre.nextval,
               SC.VERSION_NO,
               SC.MODEL_DEF_ID,
               SC.EVL_MAIN_ID,
               SC.ICG_NO,
               'Q11',
               pt.num17,
               PE.num17,
               case when RPT_START = evl_ym then 1 else 0 end,
               pt.num15,
               PT.NUM18
        from qtc_BUSS_EVL_ICG_MSG SC
                 join qtc_BUSS_EVL_CFG_PT PT on PT.VERSION_NO = SC.VERSION_NO
            AND PT.MODEL_DEF_ID = SC.MODEL_DEF_ID
            AND PT.ICG_NO = SC.ICG_NO
                 left join qtc_BUSS_EVL_CFG_PT PE on PE.VERSION_NO = SC.VERSION_PRE
            AND PE.MODEL_DEF_ID = SC.MODEL_DEF_ID
            AND PE.ICG_NO = SC.ICG_NO
        where SC.VERSION_NO = p_version
          and sc.CAL_TYPE = 'Lic'
          and sc.STATE = 1;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_03';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_BUSS_EVL_LIC_PRE(ICG_PRE_ID, version_no,  model_def_id, evaluate_main_id,
                                         icg_no, type_no, NUM1, NUM2)
        select qtc_seq_buss_evl_lic_pre.nextval,
               SC.VERSION_NO,
               SC.MODEL_DEF_ID,
               SC.EVL_MAIN_ID,
               SC.ICG_NO,
               'Q11',
               NUM1,
               NUM2
        from qtc_BUSS_EVL_ICG_MSG SC
                 join qtc_BUSS_EVL_LIC_PRE PT on PT.VERSION_NO = SC.VERSION_PRE
            AND PT.MODEL_DEF_ID = SC.MODEL_DEF_ID
            AND PT.ICG_NO = SC.ICG_NO
            and pt.TYPE_NO = 'Q11'
        where SC.VERSION_NO = p_version
          and sc.CAL_TYPE = 'Lic'
          and sc.STATE = 9;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_04';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        if v_fir = 0 then

            insert into qtc_buss_evl_lic_pre(icg_pre_id, version_no,
                                             model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                                             num1, num2, num3, num4, num5, num6, num7, num8, num9)
            select qtc_seq_buss_evl_lic_pre.nextval,
                   mt.VERSION_NO,
                   mt.MODEL_DEF_ID,
                   mt.EVALUATE_MAIN_ID,
                   rl.ICG_NO,
                   mt.YEAR_MONTH,
                   'Q1',
                   rl.num5,
                   rl.num6,
                   rl.num7,
                   rl.num8,
                   rl.num9,
                   rl.num10,
                   rl.num11,
                   rl.num12,
                   rl.num4
            from qtc_buss_evaluate_main mt ,
                 qtc_buss_evaluate_main lmt ,
                 qtc_BUSS_EVALUATE_RESULT rl
            where mt.VERSION_NO = p_version
              and lmt.MODEL_DEF_ID = mt.MODEL_DEF_ID
              and lmt.LOA_CODE = mt.LOA_CODE
              and lmt.CONFIRM_IS = '1'
              and lmt.YEAR_MONTH = to_char(add_months(to_date(mt.YEAR_MONTH,'yyyymm'), -1) , 'yyyymm')
              and rl.EVALUATE_MAIN_ID = lmt.EVALUATE_MAIN_ID
              and rl.var1 = 'Lic' ;

        end if ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_05';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        if v_fir = 0 then

            insert into qtc_buss_evl_lic_pre(icg_pre_id, version_no,
                                             model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                                             num1, num2, num3, num4, num5, num6, num7, num8)
            select qtc_seq_buss_evl_lic_pre.nextval,
                   mt.VERSION_NO,
                   mt.MODEL_DEF_ID,
                   mt.EVALUATE_MAIN_ID,
                   rl.ICG_NO,
                   mt.YEAR_MONTH,
                   'Q2',
                   rl.num16,
                   rl.num17,
                   rl.num18,
                   rl.num19,
                   rl.num20,
                   rl.num21,
                   rl.num14,
                   rl.num15
            from qtc_buss_evaluate_main mt ,
                 qtc_buss_evaluate_main lmt ,
                 qtc_BUSS_EVALUATE_RESULT rl
            where mt.VERSION_NO = p_version
              and lmt.MODEL_DEF_ID = mt.MODEL_DEF_ID
              and lmt.LOA_CODE = mt.LOA_CODE
              and lmt.CONFIRM_IS = '1'
              and lmt.YEAR_MONTH = to_char(add_months(to_date(mt.YEAR_MONTH,'yyyymm'), -1) , 'yyyymm')
              and rl.EVALUATE_MAIN_ID = lmt.EVALUATE_MAIN_ID
              and rl.var1 = 'Lic' ;

        end if ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_06';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        if v_fir = 0 then

            insert into qtc_buss_evl_lic_pre(icg_pre_id, version_no,
                                             model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                                             num1, num2, num3, num4, num5, num6, num7, num8, num9, num10)
            select qtc_seq_buss_evl_lic_pre.nextval,
                   mt.VERSION_NO,
                   mt.MODEL_DEF_ID,
                   mt.EVALUATE_MAIN_ID,
                   rl.ICG_NO,
                   mt.YEAR_MONTH,
                   'Q3',
                   rl.num22,
                   rl.num23,
                   rl.num24,
                   rl.num25,
                   rl.num26,
                   rl.num27,
                   rl.num28,
                   rl.num29,
                   rl.num20,
                   rl.num21
            from qtc_buss_evaluate_main mt ,
                 qtc_buss_evaluate_main lmt ,
                 qtc_BUSS_EVALUATE_RESULT rl
            where mt.VERSION_NO = p_version
              and lmt.MODEL_DEF_ID = mt.MODEL_DEF_ID
              and lmt.LOA_CODE = mt.LOA_CODE
              and lmt.CONFIRM_IS = '1'
              and lmt.YEAR_MONTH = to_char(add_months(to_date(mt.YEAR_MONTH,'yyyymm'), -1) , 'yyyymm')
              and rl.EVALUATE_MAIN_ID = lmt.EVALUATE_MAIN_ID
              and rl.var1 = 'Lic' ;

        end if ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_07';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        if v_fir = 0 then

            insert into qtc_buss_evl_lic_pre(icg_pre_id, version_no,
                                             model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                                             num1, num2, num3, num4, num5, num6, num7, num8, num9, num10, num11, num12)
            select qtc_seq_buss_evl_lic_pre.nextval,
                   mt.VERSION_NO,
                   mt.MODEL_DEF_ID,
                   mt.EVALUATE_MAIN_ID,
                   rl.ICG_NO,
                   mt.YEAR_MONTH,
                  'Q4',
                   rl.num30,
                   rl.num31,
                   rl.num32,
                   rl.num33,
                   rl.num34,
                   rl.num35,
                   rl.num36,
                   rl.num37,
                   rl.num26,
                   rl.num27,
                   rl.num28,
                   rl.num29
            from qtc_buss_evaluate_main mt ,
                 qtc_buss_evaluate_main lmt ,
                 qtc_BUSS_EVALUATE_RESULT rl
            where mt.VERSION_NO = p_version
              and lmt.MODEL_DEF_ID = mt.MODEL_DEF_ID
              and lmt.LOA_CODE = mt.LOA_CODE
              and lmt.CONFIRM_IS = '1'
              and lmt.YEAR_MONTH = to_char(add_months(to_date(mt.YEAR_MONTH,'yyyymm'), -1) , 'yyyymm')
              and rl.EVALUATE_MAIN_ID = lmt.EVALUATE_MAIN_ID
              and rl.var1 = 'Lic' ;

        end if ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_08';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_lic_pre(icg_pre_id, version_no,
                                         model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                                         num1, num2, num3, num4, num5, num6, num7, num8,
                                         num9, num10, num11, num12, num13)
        select qtc_seq_buss_evl_lic_pre.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVL_MAIN_ID,
               ICG_NO,
               EVL_YM,
               'Q5',
               m1,
               m2,
               m3,
               m4,
               m5,
               m6,
               m7,
               m8,
               m9,
               m10,
               m11,
               m12,
               m13
        from (select sc.VERSION_NO,
                     sc.MODEL_DEF_ID,
                     sc.EVL_MAIN_ID,
                     sc.ICG_NO,
                     sc.EVL_YM,
                     sum(case when cg.TYPE_NO = 'A1' then cg.num1 end)  m1,
                     sum(case when cg.TYPE_NO = 'A1' then cg.num2 end)  m2,
                     sum(case when cg.TYPE_NO = 'B1' then cg.num1 end)  m3,
                     sum(case when cg.TYPE_NO = 'B1' then cg.num2 end)  m4,
                     sum(case when cg.TYPE_NO = 'C1' then cg.num1 end)  m5,
                     sum(case when cg.TYPE_NO = 'C1' then cg.num2 end)  m6,
                     sum(case when cg.TYPE_NO = 'D1' then cg.num1 end)  m7,
                     sum(case when cg.TYPE_NO = 'D1' then cg.num2 end)  m8,
                     sum(case when cg.TYPE_NO = 'A1' then cg.num12 end) m9,
                     sum(case when cg.TYPE_NO = 'B1' then cg.num12 end) m10,
                     sum(case when cg.TYPE_NO = 'D1' then cg.num12 end) m11,
                     sum(case when cg.TYPE_NO = 'A2' then cg.num12 end) m12,
                     sum(case when cg.TYPE_NO = 'B2' then cg.num12 end) m13
              from qtc_BUSS_EVL_ICG_MSG sc,
                   qtc_buss_evl_lic_cal cg
              where sc.VERSION_NO = p_version
                and sc.CAL_TYPE = 'Lic'
                and cg.VERSION_NO = sc.VERSION_PRE
                and cg.ICG_NO = sc.ICG_NO
              group by sc.VERSION_NO,
                       sc.MODEL_DEF_ID,
                       sc.EVL_MAIN_ID,
                       sc.ICG_NO,
                       sc.EVL_YM) tt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_09';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_lic_pre(icg_pre_id, version_no,
                                         model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                                         num1, num2, num3, num4, num5, num6, num7, num8)
        select qtc_seq_buss_evl_lic_pre.nextval,
               pv.VERSION_NO,
               pv.MODEL_DEF_ID,
               pv.EVALUATE_MAIN_ID,
               pv.ICG_NO,
               pv.YEAR_MONTH,
               'Q7',
               pv.num1,
               pv.num2,
               pv.num3,
               pv.num4,
               pv.num5,
               pv.num6,
               pv.num7,
               pv.num8
        from qtc_BUSS_EVL_ICG_MSG sc,
             qtc_BUSS_EVL_LIC_ICG_PV pv
        where sc.VERSION_NO = p_version
          and sc.CAL_TYPE = 'Lic'
          and pv.VERSION_NO = sc.VERSION_NO
          and pv.MODEL_DEF_ID = sc.MODEL_DEF_ID
          and pv.ICG_NO = sc.ICG_NO
          and pv.CF_TYPE = 'C13';

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_10';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_lic_pre(icg_pre_id, version_no,
                                         model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                                         num1, num2, num3, num4, num5, num6, num7, num8)
        select qtc_seq_buss_evl_lic_pre.nextval,
               pv.VERSION_NO,
               pv.MODEL_DEF_ID,
               pv.EVALUATE_MAIN_ID,
               pv.ICG_NO,
               pv.YEAR_MONTH,
               'Q8',
               pv.num1,
               pv.num2,
               pv.num3,
               pv.num4,
               pv.num5,
               pv.num6,
               pv.num7,
               pv.num8
        from qtc_BUSS_EVL_ICG_MSG sc,
             qtc_BUSS_EVL_LIC_ICG_PV pv
        where sc.VERSION_NO = p_version
          and sc.CAL_TYPE = 'Lic'
          and pv.VERSION_NO = sc.VERSION_NO
          and pv.MODEL_DEF_ID = sc.MODEL_DEF_ID
          and pv.ICG_NO = sc.ICG_NO
          and pv.CF_TYPE = 'C11';

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_11';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_lic_pre(icg_pre_id, version_no, model_def_id, evaluate_main_id,
                                          icg_no, year_month, type_no,
                                         num1, num2, num3, num4, num5, num6, num7, num8)
        select qtc_seq_buss_evl_lic_pre.nextval,
               version_no,
               model_def_id,
               evl_main_id,
               icg_no,
               evl_ym,
               'Q9',
               num1,
               NUM2,
               num3,
               num4,
               num5,
               num6,
               num7,
               num8
        from (select sc.version_no,
                     sc.model_def_id,
                     sc.evl_main_id,
                     sc.icg_no,
                     sc.evl_ym,
                     sum(CASE WHEN gcf.EXP_YM = sc.EVL_YM THEN gcf.NUM1 ELSE gcf.num1 * IR.FLT8 END) num1,
                     sum(CASE WHEN gcf.EXP_YM = sc.EVL_YM THEN gcf.NUM2 ELSE gcf.num2 * IR.FLT8 END) num2,
                     sum(CASE WHEN gcf.EXP_YM = sc.EVL_YM THEN gcf.NUM3 ELSE gcf.num3 * IR.FLT8 END) num3,
                     sum(CASE WHEN gcf.EXP_YM = sc.EVL_YM THEN gcf.NUM4 ELSE gcf.num4 * IR.FLT8 END) num4,
                     sum(CASE WHEN gcf.EXP_YM = sc.EVL_YM THEN gcf.NUM5 ELSE gcf.num5 * IR.FLT8 END) num5,
                     sum(CASE WHEN gcf.EXP_YM = sc.EVL_YM THEN gcf.NUM6 ELSE gcf.num6 * IR.FLT8 END) num6,
                     sum(CASE WHEN gcf.EXP_YM = sc.EVL_YM THEN gcf.NUM7 ELSE gcf.num7 * IR.FLT8 END) num7,
                     sum(CASE WHEN gcf.EXP_YM = sc.EVL_YM THEN gcf.NUM8 ELSE gcf.num8 * IR.FLT8 END) num8
              from qtc_BUSS_EVL_ICG_MSG sc,
                   qtc_BUSS_EVL_CFG_WA_IR ir,
                   qtc_BUSS_EVL_LIC_ICG_CF gcf
              where sc.VERSION_NO = p_version
                and sc.CAL_TYPE = 'Lic'
                and sc.STATE = 1
                and ir.VERSION_NO = sc.VERSION_NO
                and ir.MODEL_DEF_ID = sc.MODEL_DEF_ID
                and ir.ICG_NO = sc.ICG_NO
                and gcf.VERSION_NO = sc.VERSION_PRE
                and gcf.MODEL_DEF_ID = sc.MODEL_DEF_ID
                and gcf.ICG_NO = sc.ICG_NO
                and gcf.EXP_YM >= sc.EVL_YM
                and ir.EXP_YM = gcf.EXP_YM
              GROUP BY sc.version_no,
                       sc.model_def_id,
                       sc.evl_main_id,
                       sc.icg_no,
                       sc.evl_ym) TT;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_12';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_lic_pre(icg_pre_id, version_no, model_def_id, evaluate_main_id,
                                          icg_no, year_month, type_no,
                                         num1, num2, num3, num4, num5, num6, num7, num8)
        select qtc_seq_buss_evl_lic_pre.nextval,
               version_no,
               model_def_id,
               evl_main_id,
               icg_no,
               evl_ym,
               'Q9',
               num1,
               NUM2,
               num3,
               num4,
               num5,
               num6,
               num7,
               num8
        from (select sc.version_no,
                     sc.model_def_id,
                     sc.evl_main_id,
                     sc.icg_no,
                     sc.evl_ym,
                     sum(CASE WHEN gcf.EXP_YM = sc.EVL_YM THEN gcf.NUM1 ELSE gcf.num1 * IR.FLT8 END) num1,
                     sum(CASE WHEN gcf.EXP_YM = sc.EVL_YM THEN gcf.NUM2 ELSE gcf.num2 * IR.FLT8 END) num2,
                     sum(CASE WHEN gcf.EXP_YM = sc.EVL_YM THEN gcf.NUM3 ELSE gcf.num3 * IR.FLT8 END) num3,
                     sum(CASE WHEN gcf.EXP_YM = sc.EVL_YM THEN gcf.NUM4 ELSE gcf.num4 * IR.FLT8 END) num4,
                     sum(CASE WHEN gcf.EXP_YM = sc.EVL_YM THEN gcf.NUM5 ELSE gcf.num5 * IR.FLT8 END) num5,
                     sum(CASE WHEN gcf.EXP_YM = sc.EVL_YM THEN gcf.NUM6 ELSE gcf.num6 * IR.FLT8 END) num6,
                     sum(CASE WHEN gcf.EXP_YM = sc.EVL_YM THEN gcf.NUM7 ELSE gcf.num7 * IR.FLT8 END) num7,
                     sum(CASE WHEN gcf.EXP_YM = sc.EVL_YM THEN gcf.NUM8 ELSE gcf.num8 * IR.FLT8 END) num8
              from qtc_BUSS_EVL_ICG_MSG sc,
                   qtc_BUSS_EVL_CFG_WA_IR ir,
                   qtc_BUSS_EVL_LIC_ICG_CF gcf
              where sc.VERSION_NO = p_version
                and sc.CAL_TYPE = 'Lic'
                and sc.STATE = 9
                and ir.VERSION_NO = sc.VERSION_PRE
                and ir.MODEL_DEF_ID = sc.MODEL_DEF_ID
                and ir.ICG_NO = sc.ICG_NO
                and gcf.VERSION_NO = sc.VERSION_PRE
                and gcf.MODEL_DEF_ID = sc.MODEL_DEF_ID
                and gcf.ICG_NO = sc.ICG_NO
                and gcf.EXP_YM >= sc.EVL_YM
                and ir.EXP_YM = gcf.EXP_YM
              GROUP BY sc.version_no,
                       sc.model_def_id,
                       sc.evl_main_id,
                       sc.icg_no,
                       sc.evl_ym) TT;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_13';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_lic_pre(icg_pre_id, version_no,
                                         model_def_id, evaluate_main_id, icg_no, year_month, type_no,
                                         num1, num2, num3, num4, num5, num6, num7, num8)
        select qtc_seq_buss_evl_lic_pre.nextval,
               sc.VERSION_NO,
               sc.MODEL_DEF_ID,
               sc.EVL_MAIN_ID,
               sc.ICG_NO,
               sc.EVL_YM,
               'Q10',
               pe.num1,
               pe.num2,
               pe.num3,
               pe.num4,
               pe.num5,
               pe.num6,
               pe.num7,
               pe.num8
         from qtc_BUSS_EVL_ICG_MSG sc,
             qtc_buss_evl_lic_pre pe
        where sc.VERSION_NO = p_version
          and sc.CAL_TYPE = 'Lic'
          and pe.VERSION_NO = sc.VERSION_PRE
          and pe.MODEL_DEF_ID = sc.MODEL_DEF_ID
          and pe.ICG_NO = sc.ICG_NO
          and pe.TYPE_NO = 'Q7';

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_14';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_lic_pre (icg_pre_id, version_no, model_def_id, evaluate_main_id, icg_no,
                                          year_month, type_no, num1, num2, num3, num4)
        select qtc_seq_buss_evl_lic_pre.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVALUATE_MAIN_ID,
               ICG_NO,
               YEAR_MONTH,
               'Q6',
               m2,
               m1,
               0,
               0
        from (select mt.VERSION_NO,
                     mt.MODEL_DEF_ID,
                     mt.EVALUATE_MAIN_ID,
                     mt.YEAR_MONTH,
                     mt.ICG_NO,
                     SUM(case
                             when to_char(PD.ACCIDENT_DATE_TIME, 'yyyy') =
                                  substr(pd.YEAR_MONTH, 1, 4) then
                                 PAID_AMOUNT
                         end) m1,
                     SUM(case
                             when to_char(PD.ACCIDENT_DATE_TIME, 'yyyy') <
                                  substr(pd.YEAR_MONTH, 1, 4) then
                                 PAID_AMOUNT
                         end) m2
              FROM (SELECT pe.VERSION_NO, pe.MODEL_DEF_ID, pe.YEAR_MONTH, pe.EVALUATE_MAIN_ID, pe.ICG_NO
                    FROM qtc_buss_evl_lic_pre PE
                    WHERE PE.VERSION_NO = P_VERSION
                    group by pe.VERSION_NO, pe.MODEL_DEF_ID, pe.YEAR_MONTH, pe.EVALUATE_MAIN_ID, pe.ICG_NO) mt,
                   qtc_DAP_DD_CLAIM_PAID PD
              WHERE PD.ICG_NO = MT.ICG_NO
                and PD.YEAR_MONTH = mt.YEAR_MONTH
              GROUP BY mt.VERSION_NO,
                       mt.MODEL_DEF_ID,
                       mt.EVALUATE_MAIN_ID,
                       mt.YEAR_MONTH,
                       mt.ICG_NO) tt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_15';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_lic_pre (icg_pre_id, version_no, model_def_id, evaluate_main_id, icg_no,
                                          year_month, type_no, num1, num2, num3, num4)
        select qtc_seq_buss_evl_lic_pre.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVALUATE_MAIN_ID,
               ICG_NO,
               YEAR_MONTH,
               'Q6',
               m2,
               m1,
               0,
               0
        from (select mt.VERSION_NO,
                     mt.MODEL_DEF_ID,
                     mt.EVALUATE_MAIN_ID,
                     mt.YEAR_MONTH,
                     mt.ICG_NO,
                     SUM(case
                             when to_char(PD.ACCIDENT_DATE_TIME, 'yyyy') =
                                  substr(pd.YEAR_MONTH, 1, 4) then
                                 PAID_AMOUNT
                         end) m1,
                     SUM(case
                             when to_char(PD.ACCIDENT_DATE_TIME, 'yyyy') <
                                  substr(pd.YEAR_MONTH, 1, 4) then
                                 PAID_AMOUNT
                         end) m2
              FROM (SELECT pe.VERSION_NO, pe.MODEL_DEF_ID, pe.YEAR_MONTH, pe.EVALUATE_MAIN_ID, pe.ICG_NO
                    FROM qtc_buss_evl_lic_pre PE
                    WHERE PE.VERSION_NO = P_VERSION
                    group by pe.VERSION_NO, pe.MODEL_DEF_ID, pe.YEAR_MONTH, pe.EVALUATE_MAIN_ID, pe.ICG_NO) mt,
                   qtc_DAP_FO_CLAIM_PAID PD
              WHERE PD.ICG_NO = MT.ICG_NO
                and PD.YEAR_MONTH = mt.YEAR_MONTH
              GROUP BY mt.VERSION_NO,
                       mt.MODEL_DEF_ID,
                       mt.EVALUATE_MAIN_ID,
                       mt.YEAR_MONTH,
                       mt.ICG_NO) tt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_16';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_lic_pre (icg_pre_id, version_no, model_def_id, evaluate_main_id, icg_no,
                                          year_month, type_no, num1, num2, num3, num4)
        select qtc_seq_buss_evl_lic_pre.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVALUATE_MAIN_ID,
               ICG_NO,
               YEAR_MONTH,
               'Q6',
               m2,
               m1,
               0,
               0
        from (select mt.VERSION_NO,
                     mt.MODEL_DEF_ID,
                     mt.EVALUATE_MAIN_ID,
                     mt.YEAR_MONTH,
                     mt.ICG_NO,
                     SUM(case
                             when to_char(PD.ACCIDENT_DATE_TIME, 'yyyy') =
                                  substr(pd.YEAR_MONTH, 1, 4) then
                                 PAID_AMOUNT
                         end) m1,
                     SUM(case
                             when to_char(PD.ACCIDENT_DATE_TIME, 'yyyy') <
                                  substr(pd.YEAR_MONTH, 1, 4) then
                                 PAID_AMOUNT
                         end) m2
              FROM (SELECT pe.VERSION_NO, pe.MODEL_DEF_ID, pe.YEAR_MONTH, pe.EVALUATE_MAIN_ID, pe.ICG_NO
                    FROM qtc_buss_evl_lic_pre PE
                    WHERE PE.VERSION_NO = P_VERSION
                    group by pe.VERSION_NO, pe.MODEL_DEF_ID, pe.YEAR_MONTH, pe.EVALUATE_MAIN_ID, pe.ICG_NO) mt,
                   qtc_DAP_TI_CLAIM_PAID PD
              WHERE PD.ICG_NO = MT.ICG_NO
                and PD.YEAR_MONTH = mt.YEAR_MONTH
              GROUP BY mt.VERSION_NO,
                       mt.MODEL_DEF_ID,
                       mt.EVALUATE_MAIN_ID,
                       mt.YEAR_MONTH,
                       mt.ICG_NO) tt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_17';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_lic_pre (icg_pre_id, version_no, model_def_id, evaluate_main_id, icg_no,
                                          year_month, type_no, num1, num2, num3, num4)
        select qtc_seq_buss_evl_lic_pre.nextval,
               VERSION_NO,
               MODEL_DEF_ID,
               EVALUATE_MAIN_ID,
               ICG_NO,
               YEAR_MONTH,
               'Q6',
               m2,
               m1,
               0,
               0
        from (select mt.VERSION_NO,
                     mt.MODEL_DEF_ID,
                     mt.EVALUATE_MAIN_ID,
                     mt.YEAR_MONTH,
                     mt.ICG_NO,
                     SUM(case
                             when to_char(PD.ACCIDENT_DATE_TIME, 'yyyy') =
                                  substr(pd.YEAR_MONTH, 1, 4) then
                                 PAID_AMOUNT
                         end) m1,
                     SUM(case
                             when to_char(PD.ACCIDENT_DATE_TIME, 'yyyy') <
                                  substr(pd.YEAR_MONTH, 1, 4) then
                                 PAID_AMOUNT
                         end) m2
              FROM (SELECT pe.VERSION_NO, pe.MODEL_DEF_ID, pe.YEAR_MONTH, pe.EVALUATE_MAIN_ID, pe.ICG_NO
                    FROM qtc_buss_evl_lic_pre PE
                    WHERE PE.VERSION_NO = P_VERSION
                    group by pe.VERSION_NO, pe.MODEL_DEF_ID, pe.YEAR_MONTH, pe.EVALUATE_MAIN_ID, pe.ICG_NO) mt,
                   qtc_DAP_TO_CLAIM_PAID PD
              WHERE PD.ICG_NO = MT.ICG_NO
                and PD.YEAR_MONTH = mt.YEAR_MONTH
              GROUP BY mt.VERSION_NO,
                       mt.MODEL_DEF_ID,
                       mt.EVALUATE_MAIN_ID,
                       mt.YEAR_MONTH,
                       mt.ICG_NO) tt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_18';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_evl_lic_pre tg
        using (select MT.VERSION_NO,
                      MT.MODEL_DEF_ID,
                      MT.EVALUATE_MAIN_ID,
                      MT.YEAR_MONTH,
                      SUM(case
                              when to_char(PD.ACCIDENT_DATE_TIME, 'yyyy') =
                                   substr(pd.YEAR_MONTH, 1, 4) then
                                  PAID_AMOUNT
                          end) m1,
                      SUM(case
                              when to_char(PD.ACCIDENT_DATE_TIME, 'yyyy') <
                                   substr(pd.YEAR_MONTH, 1, 4) then
                                  PAID_AMOUNT
                          end) m2
               FROM qtc_BUSS_EVALUATE_MAIN mt,
                    qtc_DAP_DD_CLAIM_PAID PD
               WHERE mt.VERSION_NO = p_version
                 and mt.BUSINESS_MODEL = 'D'
                 and mt.BUSINESS_DIRECTION = 'D'
                 and PD.LOA_CODE = MT.LOA_CODE
                 and PD.YEAR_MONTH = mt.YEAR_MONTH
                 and pd.ENTITY_ID = mt.ENTITY_ID
                 and not exists (select 1
                                 from qtc_BUSS_EVL_ICG_MSG ms
                                 where ms.VERSION_NO = mt.VERSION_NO
                                   and ms.MODEL_DEF_ID = mt.MODEL_DEF_ID
                                   and ms.CAL_TYPE = 'Lic'
                                   and ms.ICG_NO = pd.ICG_NO)
               GROUP BY MT.VERSION_NO,
                        MT.MODEL_DEF_ID,
                        MT.EVALUATE_MAIN_ID,
                        MT.YEAR_MONTH) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.TYPE_NO = 'Q6' and
            tg.ICG_NO = 'Dummy')
        when matched then
            update set num1 = nvl(tg.num1, 0) + nvl(sc.m1, 0), num2 = nvl(tg.num2, 0) + nvl(sc.m2, 0)
        when not matched then
            insert (icg_pre_id, version_no, model_def_id, evaluate_main_id, icg_no,
                    year_month, type_no, num1, num2)
            values (qtc_seq_buss_evl_lic_pre.nextval, sc.VERSION_NO, sc.MODEL_DEF_ID, sc.EVALUATE_MAIN_ID, 'Dummy',
                    sc.YEAR_MONTH, 'Q6', sc.m1, sc.m2);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_19';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_evl_lic_pre tg
        using (select MT.VERSION_NO,
                      MT.MODEL_DEF_ID,
                      MT.EVALUATE_MAIN_ID,
                      MT.YEAR_MONTH,
                      SUM(case
                              when to_char(PD.ACCIDENT_DATE_TIME, 'yyyy') =
                                   substr(pd.YEAR_MONTH, 1, 4) then
                                  PAID_AMOUNT
                          end) m1,
                      SUM(case
                              when to_char(PD.ACCIDENT_DATE_TIME, 'yyyy') <
                                   substr(pd.YEAR_MONTH, 1, 4) then
                                  PAID_AMOUNT
                          end) m2
               FROM qtc_BUSS_EVALUATE_MAIN mt,
                    qtc_DAP_FO_CLAIM_PAID PD
               WHERE mt.VERSION_NO = p_version
                 and mt.BUSINESS_MODEL = 'F'
                 and mt.BUSINESS_DIRECTION = 'O'
                 and PD.LOA_CODE = MT.LOA_CODE
                 and PD.YEAR_MONTH = mt.YEAR_MONTH
                 and pd.ENTITY_ID = mt.ENTITY_ID
                 and not exists (select 1
                                 from qtc_BUSS_EVL_ICG_MSG ms
                                 where ms.VERSION_NO = mt.VERSION_NO
                                   and ms.MODEL_DEF_ID = mt.MODEL_DEF_ID
                                   and ms.CAL_TYPE = 'Lic'
                                   and ms.ICG_NO = pd.ICG_NO)
               GROUP BY MT.VERSION_NO,
                        MT.MODEL_DEF_ID,
                        MT.EVALUATE_MAIN_ID,
                        MT.YEAR_MONTH) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.TYPE_NO = 'Q6' and
            tg.ICG_NO = 'Dummy')
        when matched then
            update set num1 = nvl(tg.num1, 0) + nvl(sc.m1, 0), num2 = nvl(tg.num2, 0) + nvl(sc.m2, 0)
        when not matched then
            insert (icg_pre_id, version_no, model_def_id, evaluate_main_id, icg_no,
                    year_month, type_no, num1, num2)
            values (qtc_seq_buss_evl_lic_pre.nextval, sc.VERSION_NO, sc.MODEL_DEF_ID, sc.EVALUATE_MAIN_ID, 'Dummy',
                    sc.YEAR_MONTH, 'Q6', sc.m1, sc.m2);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_20';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_evl_lic_pre tg
        using (select MT.VERSION_NO,
                      MT.MODEL_DEF_ID,
                      MT.EVALUATE_MAIN_ID,
                      MT.YEAR_MONTH,
                      SUM(case
                              when to_char(PD.ACCIDENT_DATE_TIME, 'yyyy') =
                                   substr(pd.YEAR_MONTH, 1, 4) then
                                  PAID_AMOUNT
                          end) m1,
                      SUM(case
                              when to_char(PD.ACCIDENT_DATE_TIME, 'yyyy') <
                                   substr(pd.YEAR_MONTH, 1, 4) then
                                  PAID_AMOUNT
                          end) m2
               FROM qtc_BUSS_EVALUATE_MAIN mt,
                    qtc_DAP_TI_CLAIM_PAID PD
               WHERE mt.VERSION_NO = p_version
                 and mt.BUSINESS_MODEL = 'T'
                 and mt.BUSINESS_DIRECTION = 'I'
                 and PD.LOA_CODE = MT.LOA_CODE
                 and PD.YEAR_MONTH = mt.YEAR_MONTH
                 and pd.ENTITY_ID = mt.ENTITY_ID
                 and not exists (select 1
                                 from qtc_BUSS_EVL_ICG_MSG ms
                                 where ms.VERSION_NO = mt.VERSION_NO
                                   and ms.MODEL_DEF_ID = mt.MODEL_DEF_ID
                                   and ms.CAL_TYPE = 'Lic'
                                   and ms.ICG_NO = pd.ICG_NO)
               GROUP BY MT.VERSION_NO,
                        MT.MODEL_DEF_ID,
                        MT.EVALUATE_MAIN_ID,
                        MT.YEAR_MONTH) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.TYPE_NO = 'Q6' and
            tg.ICG_NO = 'Dummy')
        when matched then
            update set num1 = nvl(tg.num1, 0) + nvl(sc.m1, 0), num2 = nvl(tg.num2, 0) + nvl(sc.m2, 0)
        when not matched then
            insert (icg_pre_id, version_no, model_def_id, evaluate_main_id, icg_no,
                    year_month, type_no, num1, num2)
            values (qtc_seq_buss_evl_lic_pre.nextval, sc.VERSION_NO, sc.MODEL_DEF_ID, sc.EVALUATE_MAIN_ID, 'Dummy',
                    sc.YEAR_MONTH, 'Q6', sc.m1, sc.m2);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_21';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_evl_lic_pre tg
        using (select MT.VERSION_NO,
                      MT.MODEL_DEF_ID,
                      MT.EVALUATE_MAIN_ID,
                      MT.YEAR_MONTH,
                      SUM(case
                              when to_char(PD.ACCIDENT_DATE_TIME, 'yyyy') =
                                   substr(pd.YEAR_MONTH, 1, 4) then
                                  PAID_AMOUNT
                          end) m1,
                      SUM(case
                              when to_char(PD.ACCIDENT_DATE_TIME, 'yyyy') <
                                   substr(pd.YEAR_MONTH, 1, 4) then
                                  PAID_AMOUNT
                          end) m2
               FROM qtc_BUSS_EVALUATE_MAIN mt,
                    qtc_DAP_TO_CLAIM_PAID PD
               WHERE mt.VERSION_NO = p_version
                 and mt.BUSINESS_MODEL = 'T'
                 and mt.BUSINESS_DIRECTION = 'O'
                 and PD.LOA_CODE = MT.LOA_CODE
                 and PD.YEAR_MONTH = mt.YEAR_MONTH
                 and pd.ENTITY_ID = mt.ENTITY_ID
                 and not exists (select 1
                                 from qtc_BUSS_EVL_ICG_MSG ms
                                 where ms.VERSION_NO = mt.VERSION_NO
                                   and ms.MODEL_DEF_ID = mt.MODEL_DEF_ID
                                   and ms.CAL_TYPE = 'Lic'
                                   and ms.ICG_NO = pd.ICG_NO)
               GROUP BY MT.VERSION_NO,
                        MT.MODEL_DEF_ID,
                        MT.EVALUATE_MAIN_ID,
                        MT.YEAR_MONTH) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.TYPE_NO = 'Q6' and
            tg.ICG_NO = 'Dummy')
        when matched then
            update set num1 = nvl(tg.num1, 0) + nvl(sc.m1, 0), num2 = nvl(tg.num2, 0) + nvl(sc.m2, 0)
        when not matched then
            insert (icg_pre_id, version_no, model_def_id, evaluate_main_id, icg_no,
                    year_month, type_no, num1, num2)
            values (qtc_seq_buss_evl_lic_pre.nextval, sc.VERSION_NO, sc.MODEL_DEF_ID, sc.EVALUATE_MAIN_ID, 'Dummy',
                    sc.YEAR_MONTH, 'Q6', sc.m1, sc.m2);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_22';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_lic_pre (icg_pre_id, version_no, model_def_id, evaluate_main_id, icg_no, TREATY_NO,
                                          type_no, num1, num2)
        select qtc_seq_buss_evl_lic_pre.nextval, ttt.*
        from (select pt.VERSION_NO,
                     pt.MODEL_DEF_ID,
                     pt.EVALUATE_MAIN_ID,
                     pt.ICG_NO,
                     tm.TREATY_NO,
                     'Q12',
                     ceil(months_between(to_date(v_ym, 'yyyymm'), TRUNC(min(tm.CONTRACT_DATE)))) - PT.NUM2 v1,
                     sum(tm.PREMIUM)
              from qtc_buss_evl_cfg_pt pt,
                   atruser.ATR_DAP_TO_PREMIUM tm
              where pt.VERSION_NO = p_version
                and pt.ICG_NO = tm.ICG_NO
              GROUP BY pt.VERSION_NO,
                       pt.MODEL_DEF_ID,
                       pt.EVALUATE_MAIN_ID,
                       pt.ICG_NO,
                       tm.TREATY_NO,
                       PT.NUM2) ttt
        where ttt.v1 >= 0;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_23';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_evl_lic_pre tg
        using (select pe.EVALUATE_MAIN_ID,
                      pe.TYPE_NO,
                      pe.ICG_NO,
                      pe.TREATY_NO,
                      sum(pd.PAID_AMOUNT) v1
               from qtc_buss_evl_lic_pre pe,
                    atruser.ATR_DAP_TO_CLAIM_PAID pd
               where pe.VERSION_NO = p_version
                 and pe.TYPE_NO = 'Q12'
                 and pe.ICG_NO = pd.ICG_NO
                 and pe.TREATY_NO = pd.TREATY_NO
               GROUP BY pe.VERSION_NO,
                        pe.MODEL_DEF_ID,
                        pe.EVALUATE_MAIN_ID,
                        pe.ICG_NO,
                        pe.TREATY_NO) sc
        on (tg.EVALUATE_MAIN_ID = sc.EVALUATE_MAIN_ID and tg.TYPE_NO = sc.TYPE_NO
            and tg.ICG_NO = sc.ICG_NO and tg.treaty_no = sc.TREATY_NO)
        when matched then
            update set num3 = sc.v1, num4 = case when tg.num2 = 0 then 0 else (sc.v1 / tg.num2) * -1 end;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_24';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_evl_lic_pre tg
        using (select pe.EVALUATE_MAIN_ID,
                      pe.TYPE_NO,
                      pe.ICG_NO,
                      pe.TREATY_NO,
                      f1.floating_commission_rate v1
               from qtc_buss_evl_lic_pre pe,
                    bpluser.bbs_conf_reins_float_charge f1
               where pe.VERSION_NO = p_version
                 and pe.TYPE_NO = 'Q12'
                 and pe.TREATY_NO = f1.TREATY_NO
                 and f1.min_loss_rate > pe.num4
                 and f1.max_loss_rate <= pe.num4 ) sc
        on (tg.EVALUATE_MAIN_ID = sc.EVALUATE_MAIN_ID and tg.TYPE_NO = sc.TYPE_NO
            and tg.ICG_NO = sc.ICG_NO and tg.treaty_no = sc.TREATY_NO)
        when matched then
            update set num5 = sc.v1, num6 = 0;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');


         v_node_dt := 'PRE_LIC_25';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_evl_lic_pre tg
        using (select mg.EVL_MAIN_ID,
                      pe.TYPE_NO,
                      pe.ICG_NO,
                      pe.TREATY_NO,
                      pe.num5
               from qtc_buss_evl_lic_pre pe,
                    qtc_buss_evl_icg_msg mg
               where mg.VERSION_NO = p_version
                 and mg.CAL_TYPE = 'Lic'
                 and pe.VERSION_NO = mg.VERSION_PRE
                 and pe.MODEL_DEF_ID = mg.MODEL_DEF_ID
                 and pe.TYPE_NO = 'Q12'
                 and pe.ICG_NO = mg.ICG_NO) sc
        on (tg.EVALUATE_MAIN_ID = sc.EVL_MAIN_ID and tg.TYPE_NO = sc.TYPE_NO
            and tg.ICG_NO = sc.ICG_NO and tg.treaty_no = sc.TREATY_NO)
        when matched then
            update set num6 = sc.num5;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'PRE_LIC_26';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_evl_lic_pre (icg_pre_id, version_no, model_def_id, evaluate_main_id, icg_no,
                                          type_no, num1)
        select qtc_seq_buss_evl_lic_pre.nextval, ttt.*
        from (select pe.VERSION_NO,
                     pe.MODEL_DEF_ID,
                     pe.EVALUATE_MAIN_ID,
                     pe.ICG_NO,
                     'Q13',
                     sum(pe.num2 * (pe.num5 - pe.num6))
              from qtc_buss_evl_lic_pre pe
              where pe.VERSION_NO = p_version
                and pe.TYPE_NO = 'Q12'
              GROUP BY pe.VERSION_NO,
                       pe.MODEL_DEF_ID,
                       pe.EVALUATE_MAIN_ID,
                       pe.ICG_NO) ttt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        proc_evl_log_run(null, p_version, v_node, 'S');




    EXCEPTION
        WHEN OTHERS THEN
            proc_evl_log_run(null, p_version, v_node, 'E');
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, sqlerrm, 'B');

            raise_application_error(-20002, SQLERRM);
    END proc_evl_lic_pre;

    procedure proc_evl_run(p_version varchar2, p_model_id number) is
        --
        v_node    varchar(32);
        v_node_dt varchar(32);

    BEGIN

        for cur_node in (select st.STEP_CODE, st.seq_no
                         from qtc_conf_model_step st
                         where st.model_def_id = p_model_id
                           and st.seq_no > 0
                         group by st.STEP_CODE, st.SEQ_NO
                         order by st.seq_no)
            loop
                begin
                    v_node := 'RL' || '_' || cur_node.STEP_CODE;
                    proc_evl_log_run(null, p_version, v_node, 'B');

                    for cur_rule in (select t2.sql_c, t2.factor_code
                                     from qtc_conf_model_factor_ref t1,
                                          qtc_temp_duct_clac_spe t2
                                     where t1.DATA_TYPE = cur_node.STEP_CODE
                                       and t1.factor_code = t2.factor_code
                                       and t2.SQL_C is not null
                                     order by t2.seq_no)
                        loop
                            v_node_dt := v_node || '_' || cur_rule.Factor_Code;
                            EXECUTE IMMEDIATE cur_rule.sql_c using p_version;

                        end loop;

                    commit;
                    proc_evl_log_run(null, p_version, v_node, 'S');
                EXCEPTION
                    WHEN OTHERS THEN
                        proc_evl_log_run(null, p_version, v_node, 'E');
                        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, sqlerrm, 'B');

                        raise_application_error(-20002, SQLERRM);
                end;

            end loop;

    EXCEPTION
        WHEN OTHERS THEN
            raise_application_error(-20002, SQLERRM);
    END proc_evl_run;

    procedure proc_evl_paa(p_version varchar2, p_model number) is

    begin

        proc_evl_lrc_paa_cf(p_version, p_model);
        proc_evl_lrc_paa_cf_pv(p_version, p_model);
        proc_evl_lrc_paa_pre(p_version, p_model);
        proc_evl_run(p_version, p_model);
        proc_evl_lrc_paa_special(p_version,p_model);

    end proc_evl_paa ;

    procedure proc_evl_gmm(p_version varchar2, p_model number) is

    begin

        proc_evl_lrc_gmm_cf(p_version, p_model);
        proc_evl_lrc_gmm_cf_pv(p_version, p_model);
        proc_evl_lrc_gmm_pre(p_version, p_model);
        proc_evl_run(p_version, p_model);
        proc_evl_lrc_gmm_special(p_version,p_model);

    end proc_evl_gmm ;

    procedure proc_evl_lic(p_version varchar2, p_lic_id number) is

    begin

        proc_evl_lic_cf_pv(p_version);
        proc_evl_lic_pre(p_version);
        proc_evl_run(p_version, p_lic_id);

    end proc_evl_lic ;

    procedure proc_evl_calculate(p_version varchar2) is

        v_num integer;

    begin

        v_num := 1;
        for ap in (select t1.VERSION_NO,
                          t1.EVALUATE_APPROACH,
                          t1.MODEL_DEF_ID,
                          case
                              when t1.MODEL_DEF_ID in (1, 3) then
                                  5
                              else
                                  6
                              end lic_id
                   from qtc_BUSS_EVL_ICG_MSG t1
                   where t1.VERSION_NO = p_version
                   group by t1.VERSION_NO,
                            t1.EVALUATE_APPROACH,
                            t1.MODEL_DEF_ID)
            loop

                if ap.EVALUATE_APPROACH = 'BBA' then

                    proc_evl_gmm(ap.version_no, ap.model_def_id);

                end if;

                if ap.EVALUATE_APPROACH = 'PAA' then

                    proc_evl_paa(ap.version_no, ap.model_def_id);

                end if;

                if v_num = 1 then

                    v_num := 2;

                    proc_evl_lic(ap.version_no,ap.lic_id);

                end if;

            end loop;

    end proc_evl_calculate;

    procedure proc_evl_interest(p_version varchar2) is

        v_interest_id NUMBER(11);
        v_avg_id      NUMBER(11);
        v_num         integer;
        v_ym          varchar(6) ;
        v_entity      number(11);
        v_currency    varchar2(16);
        v_node        varchar2(16);
        v_node_dt     varchar2(16);
    begin

        v_node := 'ITR';
        proc_evl_log_run(null, p_version, v_node, 'B');

         v_node_dt := 'ITR_001';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        select mt.entity_id, mt.year_month, mt.currency_code
        into v_entity , v_ym, v_currency
        from qtc_BUSS_EVALUATE_MAIN mt
        where mt.VERSION_NO = p_version
        group by mt.entity_id, mt.year_month, mt.currency_code;

        select count(*)
        into v_num
        from qtc_conf_interest_rate rt
        where rt.YEAR_MONTH = v_ym
          and rt.CURRENCY_code = v_currency
          and rt.ENTITY_ID = v_entity
          and rt.CONFIRM_IS = '1';

        if v_num != 1 then
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');
            proc_evl_log_run(NULL, p_version, v_node, 'S');
            return;
        end if;

        select rt.interest_rate_id
        into v_interest_id
        from qtc_conf_interest_rate rt
        where rt.YEAR_MONTH = v_ym
          and rt.CURRENCY_code = v_currency
          and rt.ENTITY_ID = v_entity
          and rt.CONFIRM_IS = '1';

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'ITR_002';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        -- 当期确认版本加权利率
        insert into qtc_buss_interest_main(interest_main_id, interest_rate_id,
                                           ENTITY_ID, CURRENCY_code, year_month, interest_type)
        select qtc_seq_buss_interest_main.nextval,
               rt.INTEREST_RATE_ID,
               rt.ENTITY_ID,
               rt.CURRENCY_CODE,
               rt.YEAR_MONTH,
               '3'
        from qtc_CONF_INTEREST_RATE rt
        where rt.INTEREST_RATE_ID = v_interest_id
          and not exists(select tn.INTEREST_RATE_ID
                         from qtc_buss_interest_main tn
                         where tn.INTEREST_RATE_ID = rt.INTEREST_RATE_ID
                           and tn.INTEREST_TYPE = '3');

        select t.INTEREST_MAIN_ID
        into v_avg_id
        from qtc_buss_interest_main t
        where t.INTEREST_RATE_ID = v_interest_id
          and t.INTEREST_TYPE = '3';

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'ITR_003';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        delete
        from qtc_buss_interest_detail
        where VERSION_NO = p_version
          and INTEREST_MAIN_ID = v_avg_id
          and ICG_NO in (select mg.ICG_NO from qtc_BUSS_EVL_ICG_MSG mg where mg.VERSION_NO = p_version);

        delete
        from qtc_buss_interest_weight
        where VERSION_NO = p_version
          and INTEREST_MAIN_ID = v_avg_id
          and ICG_NO in (select mg.ICG_NO from qtc_BUSS_EVL_ICG_MSG mg where mg.VERSION_NO = p_version);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'ITR_004';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_interest_weight tg
        using (select mg.VERSION_NO, mg.ICG_NO, sum(mg.CR_PREMIUM) CR_PREMIUM
                   from qtc_BUSS_EVL_ICG_MSG mg
                   where mg.VERSION_NO = p_version
                   group by mg.VERSION_NO, mg.ICG_NO) sc
        on (tg.INTEREST_MAIN_ID = v_avg_id and tg.ICG_NO = sc.ICG_NO)
        when matched then
            update set tg.MONTH_POLICY = sc.CR_PREMIUM
        when not matched then
            insert (interest_weight_id, interest_main_id, version_no, icg_no, month_policy)
            values (qtc_seq_buss_interest_weight.nextval, v_avg_id, sc.VERSION_NO,
                    sc.ICG_NO, sc.CR_PREMIUM);

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'ITR_005';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_interest_weight tg
        using (select mg.VERSION_NO, wt.ICG_NO, nvl(wt.NON_MONTH_POLICY, 0) + nvl(wt.MONTH_POLICY, 0) vt
               from (select VERSION_NO, EVL_YM, ICG_NO
                   from qtc_BUSS_EVL_ICG_MSG
                   where VERSION_NO = p_version
                   group by VERSION_NO, EVL_YM , ICG_NO) mg,
                    qtc_conf_interest_rate rt,
                    qtc_buss_interest_main tn,
                    qtc_buss_interest_weight wt
               where  rt.CONFIRM_IS = '1'
                 and rt.YEAR_MONTH = to_char(add_months(to_date(mg.EVL_YM, 'yyyymm'), -1), 'yyyymm')
                 and rt.ENTITY_ID = v_entity
                 and rt.CURRENCY_CODE = v_currency
                 and tn.INTEREST_RATE_ID = rt.INTEREST_RATE_ID
                 and tn.INTEREST_TYPE = '3'
                 and wt.INTEREST_MAIN_ID = tn.INTEREST_MAIN_ID
                 and wt.ICG_NO = mg.ICG_NO) sc
        on (tg.INTEREST_MAIN_ID = v_avg_id and tg.VERSION_NO = sc.VERSION_NO and tg.ICG_NO = sc.ICG_NO)
        when matched then
            update set tg.NON_MONTH_POLICY = sc.vt;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'ITR_006';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_interest_detail(interest_detail_id, interest_main_id, version_no, exp_ym, icg_no,
                                             date_type, PAY_TIME, dev_period, interest_rate)
        select qtc_SEQ_buss_INTEREST_DETAIL.nextval,
               wt.INTEREST_MAIN_ID,
               wt.VERSION_NO,
               t3.exp_ym,
               t3.ICG_NO,
               t3.DATE_TYPE,
               t3.PAY_TIME,
               MONTHS_BETWEEN(TO_DATE(t3.exp_ym, 'YYYYMM'), TO_DATE(v_ym, 'YYYYMM')),
               case
                   when nvl(wt.MONTH_POLICY, 0) = 0 or (nvl(wt.MONTH_POLICY, 0) + nvl(wt.NON_MONTH_POLICY, 0)) = 0
                       then t3.INTEREST_RATE
                   else (1- least(abs(nvl(wt.MONTH_POLICY, 0)) / abs((nvl(wt.MONTH_POLICY, 0) + nvl(wt.NON_MONTH_POLICY, 0))),1)) *
                        t3.INTEREST_RATE end
        from qtc_CONF_INTEREST_RATE t1,
             qtc_BUSS_INTEREST_MAIN t2,
             qtc_BUSS_INTEREST_DETAIL t3,
             qtc_buss_interest_weight wt
        where t1.CONFIRM_IS = '1'
          and t1.YEAR_MONTH =
              (select max(t1.YEAR_MONTH)
               from qtc_CONF_INTEREST_RATE t1,
                    qtc_BUSS_INTEREST_MAIN t2
               where t1.CONFIRM_IS = '1'
                 and t1.ENTITY_ID = v_entity
                 and t1.CURRENCY_CODE = v_currency
                 and t1.YEAR_MONTH < v_ym
                 and t2.INTEREST_RATE_ID = t1.INTEREST_RATE_ID
                 and t2.INTEREST_TYPE = '3')
          and t1.ENTITY_ID = v_entity
          and t1.CURRENCY_CODE = v_currency
          and t2.INTEREST_RATE_ID = t1.INTEREST_RATE_ID
          and t2.INTEREST_TYPE = '3'
          and t3.INTEREST_MAIN_ID = t2.INTEREST_MAIN_ID
          and t3.DATE_TYPE = 'B'
          and wt.VERSION_NO = p_version
          and wt.ICG_NO = t3.ICG_NO
          and t3.exp_ym >= v_ym ;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'ITR_0071';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        merge into qtc_buss_interest_detail tg
        using (select wt.INTEREST_MAIN_ID,
                      wt.VERSION_NO,
                      wt.ICG_NO,
                      to_char(add_months(to_date(v_ym, 'yyyymm'), t3.DEV_PERIOD), 'yyyymm')  exp_ym,
                      t3.DATE_TYPE,
                      t3.PAY_TIME,
                      t3.DEV_PERIOD,
                      t3.INTEREST_RATE,
                      case
                          when nvl(wt.MONTH_POLICY, 0) = 0 or
                               (nvl(wt.MONTH_POLICY, 0) + nvl(wt.NON_MONTH_POLICY, 0)) = 0
                              then 0
                          else
                            least(abs(nvl(wt.MONTH_POLICY, 0)) / abs((nvl(wt.MONTH_POLICY, 0) + nvl(wt.NON_MONTH_POLICY, 0))),1) *
                               t3.INTEREST_RATE end wtr
               from qtc_CONF_INTEREST_RATE t1,
                    qtc_BUSS_INTEREST_MAIN t2,
                    qtc_BUSS_INTEREST_DETAIL t3,
                    qtc_buss_interest_weight wt
               where t1.CONFIRM_IS = '1'
                 and t1.YEAR_MONTH = v_ym
                 and t1.ENTITY_ID = v_entity
                 and t1.CURRENCY_CODE = v_currency
                 and t2.INTEREST_RATE_ID = t1.INTEREST_RATE_ID
                 and t2.INTEREST_TYPE = '2'
                 and t3.version_no is null
                 and t3.INTEREST_MAIN_ID = t2.INTEREST_MAIN_ID
                 and t3.pay_time = '2'
                 and wt.VERSION_NO = p_version) sc
        on (tg.VERSION_NO = sc.VERSION_NO and tg.INTEREST_MAIN_ID = sc.INTEREST_MAIN_ID and tg.ICG_NO = sc.ICG_NO
            and tg.PAY_TIME = sc.PAY_TIME  and tg.DEV_PERIOD = sc.DEV_PERIOD)
        when matched then
            update set interest_rate = nvl(tg.INTEREST_RATE, 0) + nvl(sc.wtr, 0)
        when not matched then
            insert (interest_detail_id, interest_main_id, version_no, exp_ym, icg_no, date_type, PAY_TIME, dev_period,
                    interest_rate)
            values (qtc_SEQ_buss_INTEREST_DETAIL.nextval, sc.INTEREST_MAIN_ID, sc.VERSION_NO, sc.exp_ym, sc.ICG_NO, sc.DATE_TYPE,
                    sc.PAY_TIME, sc.DEV_PERIOD, sc.INTEREST_RATE);
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'ITR_0072';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_interest_detail(interest_detail_id, interest_main_id, version_no, exp_ym, icg_no,
                                             date_type, PAY_TIME, dev_period, interest_rate)
        SELECT qtc_SEQ_buss_INTEREST_DETAIL.nextval,
               DL.INTEREST_MAIN_ID,
               DL.VERSION_NO,
               dl.EXP_YM,
               DL.ICG_NO,
               'J',
               '1',
               DL.DEV_PERIOD,
               1
        FROM qtc_BUSS_INTEREST_DETAIL DL
        WHERE DL.VERSION_NO = p_version
          AND DL.PAY_TIME = 2
          and DL.DEV_PERIOD = 0;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'ITR_0073';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_interest_detail(interest_detail_id, interest_main_id, version_no, exp_ym, icg_no,
                                             date_type, PAY_TIME, dev_period, interest_rate)
        SELECT qtc_SEQ_buss_INTEREST_DETAIL.nextval,
               DL.INTEREST_MAIN_ID,
               DL.VERSION_NO,
               DL.EXP_YM,
               DL.ICG_NO,
               'J',
               '1',
               DL.DEV_PERIOD,
               power(EXP(SUM(LN(dl.INTEREST_RATE + 1)) OVER (partition by DL.ICG_NO order by dl.DEV_PERIOD)), -1)
        FROM qtc_BUSS_INTEREST_DETAIL DL
        WHERE DL.VERSION_NO = p_version
          AND DL.PAY_TIME = 2
          and dl.DEV_PERIOD > 0;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'ITR_0074';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_interest_detail(interest_detail_id, interest_main_id, version_no, exp_ym, icg_no,
                                             date_type, PAY_TIME, dev_period, interest_rate)
        SELECT qtc_SEQ_buss_INTEREST_DETAIL.nextval,
               DL.INTEREST_MAIN_ID,
               DL.VERSION_NO,
               dl.EXP_YM,
               DL.ICG_NO,
               'J',
               '0',
               DL.DEV_PERIOD,
               1+DL.INTEREST_RATE
        FROM qtc_BUSS_INTEREST_DETAIL DL
        WHERE DL.VERSION_NO = p_version
          AND DL.PAY_TIME = 2
          and dl.DEV_PERIOD = 0;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

       v_node_dt := 'ITR_0075';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_interest_detail(interest_detail_id, interest_main_id, version_no, exp_ym, icg_no,
                                             date_type, PAY_TIME, dev_period, interest_rate)
        SELECT qtc_SEQ_buss_INTEREST_DETAIL.nextval,
               DL.INTEREST_MAIN_ID,
               DL.VERSION_NO,
               dl.EXP_YM,
               DL.ICG_NO,
               'J',
               '0',
               DL.DEV_PERIOD+1,
               DL.INTEREST_RATE
        FROM qtc_BUSS_INTEREST_DETAIL DL
        WHERE DL.VERSION_NO = p_version
          AND DL.PAY_TIME = 1;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'ITR_0076';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_interest_detail(interest_detail_id, interest_main_id, version_no, exp_ym, icg_no,
                                             date_type, PAY_TIME, dev_period, interest_rate)
        SELECT qtc_SEQ_buss_INTEREST_DETAIL.nextval,
               DL.INTEREST_MAIN_ID,
               DL.VERSION_NO,
               dl.EXP_YM,
               DL.ICG_NO,
               'J',
               '0.5',
               DL.DEV_PERIOD,
               CASE WHEN DL.DEV_PERIOD = 0 THEN POWER(DL.INTEREST_RATE +1 , 0.5) ELSE POWER(DL.INTEREST_RATE +1 , -0.5) END
        FROM qtc_BUSS_INTEREST_DETAIL DL
        WHERE DL.VERSION_NO = p_version
          AND DL.PAY_TIME = 2
          and DL.DEV_PERIOD <= 1;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'ITR_0077';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');

        insert into qtc_buss_interest_detail(interest_detail_id, interest_main_id, version_no, exp_ym, icg_no,
                                             date_type, PAY_TIME, dev_period, interest_rate)
        SELECT qtc_SEQ_buss_INTEREST_DETAIL.nextval,
               DL1.INTEREST_MAIN_ID,
               DL1.VERSION_NO,
               dl1.EXP_YM,
               DL1.ICG_NO,
               'J',
               '0.5',
               DL1.DEV_PERIOD,
               POWER(DL1.INTEREST_RATE + 1, -0.5) * dl2.interest_rate
        FROM qtc_BUSS_INTEREST_DETAIL DL1,
             qtc_BUSS_INTEREST_DETAIL DL2
        WHERE DL1.VERSION_NO = p_version
          AND DL1.PAY_TIME = '2'
          and DL1.DEV_PERIOD >= 2
          and dl2.version_no = dl1.version_no
          and dl2.interest_main_id = dl1.interest_main_id
          and dl2.icg_no = dl1.icg_no
          and dl2.pay_time = '1'
          and dl1.dev_period = dl2.dev_period + 1;

        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'ITR_0079';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        insert into qtc_BUSS_EVL_CFG_IR(IR_ID,
                                        VERSION_NO,
                                        BATCH_NO,
                                        MODEL_DEF_ID,
                                        evaluate_main_id,
                                        loa_code,
                                        icg_no,
                                        exp_ym,
                                        dev_period,
                                        flt1,
                                        flt2,
                                        flt3,
                                        flt4,
                                        flt5,
                                        flt6,
                                        flt7,
                                        flt8)
        select qtc_SEQ_BUSS_EVL_CFG_IR.nextval,
               IP.VERSION_NO,
               ip.BATCH_NO,
               ip.MODEL_DEF_ID,
               ip.EVALUATE_MAIN_ID,
               ip.loa_code,
               ip.ICG_NO,
               to_char(add_months(to_date(v_ym, 'yyyymm'), ir.DEV_PERIOD), 'yyyymm'),
               ir.DEV_PERIOD,                         --
               ir.C1,                                 --
               ir.C2,                                 --
               ir.C3,                                 --
               ir.C4,                                 --
               CASE
                   WHEN IP.NUM5 = '0' THEN ir.C2
                   WHEN IP.NUM5 = '0.5' THEN ir.C3
                   WHEN IP.NUM5 = '1' THEN ir.C4 END, --
               CASE
                   WHEN IP.NUM6 = '0' THEN C2
                   WHEN IP.NUM6 = '0.5' THEN C3
                   WHEN IP.NUM6 = '1' THEN C4 END,    --
               CASE
                   WHEN IP.NUM7 = '0' THEN C2
                   WHEN IP.NUM7 = '0.5' THEN C3
                   WHEN IP.NUM7 = '1' THEN C4 END,    --
               CASE
                   WHEN IP.NUM8 = '0' THEN C2
                   WHEN IP.NUM8 = '0.5' THEN C3
                   WHEN IP.NUM8 = '1' THEN C4 END     --
        from qtc_BUSS_EVL_CFG_PT IP,
             (select T2.INTEREST_TYPE,
                     t3.dev_period,
                     SUM(CASE WHEN T3.DATE_TYPE = 'B' THEN t3.interest_rate END)  C1,
                     SUM(CASE WHEN T3.pay_time = '0' THEN t3.interest_rate END)   C2,
                     SUM(CASE WHEN T3.pay_time = '0.5' THEN t3.interest_rate END) C3,
                     SUM(CASE WHEN T3.pay_time = '1' THEN t3.interest_rate END)   C4
              from qtc_conf_interest_rate t1,
                   qtc_buss_interest_main t2,
                   qtc_buss_interest_detail t3
              where t1.interest_rate_id = t2.interest_rate_id
                and t1.confirm_is = '1'
                and t1.currency_code = v_currency
                and t1.year_month = V_YM
                and t2.INTEREST_TYPE = '2'
                and t2.interest_main_id = t3.interest_main_id
              GROUP BY T2.INTEREST_TYPE, t3.dev_period) ir
        where IP.VERSION_NO = p_version
        and ir.DEV_PERIOD <= ip.num25;
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'ITR_008';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        insert into qtc_BUSS_EVL_CFG_FR_IR(FR_IR_ID,
                                           VERSION_NO,
                                           BATCH_NO,
                                           MODEL_DEF_ID,
                                           evaluate_main_id,
                                           loa_code,
                                           icg_no,
                                           exp_ym,
                                           dev_period,
                                           flt1,
                                           flt2,
                                           flt3,
                                           flt4,
                                           flt5,
                                           flt6,
                                           flt7,
                                           flt8)
        select qtc_SEQ_BUSS_EVL_CFG_FR_IR.nextval,
               IP.VERSION_NO,
               ip.BATCH_NO,
               ip.MODEL_DEF_ID,
               ip.EVALUATE_MAIN_ID,
               ip.loa_code,
               ip.ICG_NO,
               to_char(add_months(to_date(v_ym, 'yyyymm'), ir.DEV_PERIOD), 'yyyymm'),
               ir.DEV_PERIOD,                         --
               ir.C1,                                 --
               ir.C2,                                 --
               ir.C3,                                 --
               ir.C4,                                 --
               CASE
                   WHEN IP.NUM5 = '0' THEN ir.C2
                   WHEN IP.NUM5 = '0.5' THEN ir.C3
                   WHEN IP.NUM5 = '1' THEN ir.C4 END, --
               CASE
                   WHEN IP.NUM6 = '0' THEN C2
                   WHEN IP.NUM6 = '0.5' THEN C3
                   WHEN IP.NUM6 = '1' THEN C4 END,    --
               CASE
                   WHEN IP.NUM7 = '0' THEN C2
                   WHEN IP.NUM7 = '0.5' THEN C3
                   WHEN IP.NUM7 = '1' THEN C4 END,    --
               CASE
                   WHEN IP.NUM8 = '0' THEN C2
                   WHEN IP.NUM8 = '0.5' THEN C3
                   WHEN IP.NUM8 = '1' THEN C4 END     --
        from qtc_BUSS_EVL_CFG_PT IP,
             (select T2.INTEREST_TYPE,
                     t3.dev_period,
                     SUM(CASE WHEN T3.DATE_TYPE = 'B' THEN t3.interest_rate END)  C1,
                     SUM(CASE WHEN T3.pay_time = '0' THEN t3.interest_rate END)   C2,
                     SUM(CASE WHEN T3.pay_time = '0.5' THEN t3.interest_rate END) C3,
                     SUM(CASE WHEN T3.pay_time = '1' THEN t3.interest_rate END)   C4
              from qtc_conf_interest_rate t1,
                   qtc_buss_interest_main t2,
                   qtc_buss_interest_detail t3
              where t1.interest_rate_id = t2.interest_rate_id
                and t1.confirm_is = '1'
                and t1.currency_code = v_currency
                and t1.year_month = V_YM
                and t2.INTEREST_TYPE = '1'
                and t2.interest_main_id = t3.interest_main_id
              GROUP BY T2.INTEREST_TYPE, t3.dev_period) ir
        where IP.VERSION_NO = p_version
        and ir.DEV_PERIOD <= ip.num25;
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'ITR_009';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        insert into qtc_BUSS_EVL_CFG_WA_IR(WA_IR_ID,
                                           VERSION_NO,
                                           BATCH_NO,
                                           MODEL_DEF_ID,
                                           evaluate_main_id,
                                           loa_code,
                                           icg_no,
                                           exp_ym,
                                           dev_period,
                                           flt1,
                                           flt2,
                                           flt3,
                                           flt4,
                                           flt5,
                                           flt6,
                                           flt7,
                                           flt8)
        select qtc_SEQ_BUSS_EVL_CFG_WA_IR.nextval,
               IP.VERSION_NO,
               ip.BATCH_NO,
               ip.MODEL_DEF_ID,
               ip.EVALUATE_MAIN_ID,
               ip.loa_code,
               ip.ICG_NO,
               to_char(add_months(to_date(v_ym, 'yyyymm'), ir.DEV_PERIOD), 'yyyymm'),
               ir.DEV_PERIOD,                         --
               ir.C1,                                 --
               ir.C2,                                 --
               ir.C3,                                 --
               ir.C4,                                 --
               CASE
                   WHEN IP.NUM5 = '0' THEN ir.C2
                   WHEN IP.NUM5 = '0.5' THEN ir.C3
                   WHEN IP.NUM5 = '1' THEN ir.C4 END, --
               CASE
                   WHEN IP.NUM6 = '0' THEN C2
                   WHEN IP.NUM6 = '0.5' THEN C3
                   WHEN IP.NUM6 = '1' THEN C4 END,    --
               CASE
                   WHEN IP.NUM7 = '0' THEN C2
                   WHEN IP.NUM7 = '0.5' THEN C3
                   WHEN IP.NUM7 = '1' THEN C4 END,    --
               CASE
                   WHEN IP.NUM8 = '0' THEN C2
                   WHEN IP.NUM8 = '0.5' THEN C3
                   WHEN IP.NUM8 = '1' THEN C4 END     --
        from qtc_BUSS_EVL_CFG_PT IP,
             (select t3.ICG_NO,
                     T2.INTEREST_TYPE,
                     t3.dev_period,
                     SUM(CASE WHEN T3.DATE_TYPE = 'B' THEN t3.interest_rate END)  C1,
                     SUM(CASE WHEN T3.pay_time = '0' THEN t3.interest_rate END)   C2,
                     SUM(CASE WHEN T3.pay_time = '0.5' THEN t3.interest_rate END) C3,
                     SUM(CASE WHEN T3.pay_time = '1' THEN t3.interest_rate END)   C4
              from qtc_conf_interest_rate t1,
                   qtc_buss_interest_main t2,
                   qtc_buss_interest_detail t3
              where t1.interest_rate_id = t2.interest_rate_id
                and t1.confirm_is = '1'
                and t1.currency_code = v_currency
                and t1.year_month = V_YM
                and t2.INTEREST_TYPE = '3'
                and t2.interest_main_id = t3.interest_main_id
              GROUP BY t3.ICG_NO, T2.INTEREST_TYPE, t3.dev_period) ir
        where IP.VERSION_NO = p_version
          AND IP.ICG_NO = IR.ICG_NO
          and ir.DEV_PERIOD <= ip.num25;
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        proc_evl_log_run(null, p_version, v_node, 'S');
    EXCEPTION
        WHEN OTHERS THEN
            proc_evl_log_run(null, p_version, v_node, 'E');
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, sqlerrm, 'S');

            raise_application_error(-20002, SQLERRM);
    END proc_evl_interest;

    procedure proc_evl_suppose(p_version varchar2) is

        v_ym            varchar2(6);
        v_l_ym          varchar2(6);
        v_node          varchar(32);
        v_node_dt       varchar(32);
    begin

        v_node := 'SPIT';
        proc_evl_log_run(null, p_version, v_node, 'B');

        v_node_dt := 'SPIT_001';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');
         select mt.year_month , to_char(add_months(to_date(year_month, 'yyyymm'), -1), 'yyyymm')
          into  v_ym  , v_l_ym
          from qtc_BUSS_EVALUATE_MAIN mt
          where mt.VERSION_NO = p_version
           and rownum = 1;
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'S');

        v_node_dt := 'SPIT_002';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'B');
        merge into qtc_BUSS_EVL_ICG_MSG tg
        using (select mt.VERSION_NO, mt.MODEL_DEF_ID, mt.BUSINESS_MODEL||mt.BUSINESS_DIRECTION buss_dir, mt.LOA_CODE
               from qtc_buss_evaluate_main mt
               where mt.CONFIRM_IS = '1'
                 and mt.YEAR_MONTH = v_l_ym) sc
        on (tg.VERSION_NO = p_version and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID
                 and tg.BUS_DIR = sc.buss_dir and tg.LOA_CODE = sc.LOA_CODE)
        when matched then
            update
            set version_pre = sc.VERSION_NO;
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'SPIT_003';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');
        insert into qtc_BUSS_EVL_CFG_PT(CFG_PT_id,
                                        VERSION_NO,
                                        BATCH_NO,
                                        model_def_id,
                                        EVALUATE_APPROACH,
                                        evaluate_main_id,
                                        loa_code,
                                        icg_no,
                                        num1,
                                        num2,
                                        num3,
                                        num4,
                                        num5,
                                        num6,
                                        num7,
                                        num8,
                                        num9,
                                        num10,
                                        num11,
                                        num12,
                                        num13,
                                        NUM14,
                                        num15,
                                        num16,
                                        NUM17,
                                        NUM18,
                                        num19,
                                        num20,
                                        num21)
        select qtc_SEQ_BUSS_EVL_CFG_PT.nextval,
               RS.VERSION_NO,
               RS.BATCH_NO,
               rs.model_def_id,
               RS.EVALUATE_APPROACH,
               RS.EVL_MAIN_ID,
               RS.LOA_CODE,
               RS.ICG_NO,
               COALESCE(T_001, P_001), -- ????????????????????
               COALESCE(T_002, P_002), -- ??????????????
               COALESCE(T_003, P_003), -- ????????????????????
               COALESCE(T_004, P_004), -- ????????????????
               COALESCE(T_005, P_005), -- ????????????
               COALESCE(T_006, P_006), -- ????????????????
               COALESCE(T_007, P_007), -- ????????????????
               COALESCE(T_008, P_008), -- ????????????????????????
               COALESCE(T_009, P_009), -- ????????????
               COALESCE(T_010, P_010), -- ????????????????
               COALESCE(T_011, P_011), -- ??????????
               COALESCE(T_012, P_012), -- ????????????
               COALESCE(T_013, P_013), -- ??????????????????
               COALESCE(T_014, P_014), -- ??????????????????????
               COALESCE(T_015, P_015), -- OCI??????
               COALESCE(T_016, P_016), -- IACF????????????????????
               COALESCE(T_017, P_017), -- ??????????????????????
               COALESCE(T_018, P_018), -- ????????????????????????
               COALESCE(T_019, P_019), -- ??????????
               COALESCE(T_020, P_020),  --  ????????????????????
               COALESCE(T_021, P_021)  --  ????????????????????
        from (SELECT MS.VERSION_NO,
                     MS.BATCH_NO,
                     MS.model_def_id,
                     MS.EVALUATE_APPROACH,
                     MS.EVL_MAIN_ID,
                     MS.LOA_CODE,
                     MS.ICG_NO
                  FROM qtc_BUSS_EVL_ICG_MSG MS
                WHERE MS.VERSION_NO = p_version
                  group by MS.VERSION_NO,
                     MS.BATCH_NO,
                     MS.model_def_id,
                     MS.EVALUATE_APPROACH,
                     MS.EVL_MAIN_ID,
                     MS.LOA_CODE,
                     MS.ICG_NO) RS
                 LEFT JOIN (select DIMENSION_VALUE                                                                   ICG_NO,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QT008' then t1.QUOTA_VALUE end) as number) T_001,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QR003' then t1.QUOTA_VALUE end) as number) T_002,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QR008' then t1.QUOTA_VALUE end) as number) T_003,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QT008' then t1.QUOTA_VALUE end) as number) T_004,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QT002' then t1.QUOTA_VALUE end) as number) T_005,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QT003' then t1.QUOTA_VALUE end) as number) T_006,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QT004' then t1.QUOTA_VALUE end) as number) T_007,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QT006' then t1.QUOTA_VALUE end) as number) T_008,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QT007' then t1.QUOTA_VALUE end) as number) T_009,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QR004' then t1.QUOTA_VALUE end) as number) T_010,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QR006' then t1.QUOTA_VALUE end) as number) T_011,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QR001' then t1.QUOTA_VALUE end) as number) T_012,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QT009' then t1.QUOTA_VALUE end) as number) T_013,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QA004' then t1.QUOTA_VALUE end) as number) T_014,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QA002' then t1.QUOTA_VALUE end) as number) T_015,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QA005' then t1.QUOTA_VALUE end) as number) T_016,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QR009' then t1.QUOTA_VALUE end) as number) T_017,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QA007' then t1.QUOTA_VALUE end) as number) T_018,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QA001' then t1.QUOTA_VALUE end) as number) T_019,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QA008' then t1.QUOTA_VALUE end) as number) T_020,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QT011' then t1.QUOTA_VALUE end) as number) T_021
                            from qtc_BUSS_QUOTA t1,
                                 qtc_CONF_QUOTA_DEF t2,
                                 qtc_buss_evaluate_main mt
                            where t1.QUOTA_DEF_ID = t2.QUOTA_DEF_ID
                              and t1.DIMENSION = 'C'
                              and t1.EVALUATE_MAIN_ID = mt.EVALUATE_MAIN_ID
                              and mt.VERSION_NO = p_version
                            GROUP BY DIMENSION_VALUE) T_ICG ON T_ICG.ICG_NO = RS.ICG_NO
                 LEFT JOIN (select DIMENSION_VALUE                                                                   LOA_CODE,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QT008' then t1.QUOTA_VALUE end) as number) P_001,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QR003' then t1.QUOTA_VALUE end) as number) P_002,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QR008' then t1.QUOTA_VALUE end) as number) P_003,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QT008' then t1.QUOTA_VALUE end) as number) P_004,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QT002' then t1.QUOTA_VALUE end) as number) P_005,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QT003' then t1.QUOTA_VALUE end) as number) P_006,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QT004' then t1.QUOTA_VALUE end) as number) P_007,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QT006' then t1.QUOTA_VALUE end) as number) P_008,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QT007' then t1.QUOTA_VALUE end) as number) P_009,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QR004' then t1.QUOTA_VALUE end) as number) P_010,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QR006' then t1.QUOTA_VALUE end) as number) P_011,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QR001' then t1.QUOTA_VALUE end) as number) P_012,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QT009' then t1.QUOTA_VALUE end) as number) P_013,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QA004' then t1.QUOTA_VALUE end) as number) P_014,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QA002' then t1.QUOTA_VALUE end) as number) P_015,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QA005' then t1.QUOTA_VALUE end) as number) P_016,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QR009' then t1.QUOTA_VALUE end) as number) P_017,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QA007' then t1.QUOTA_VALUE end) as number) P_018,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QA001' then t1.QUOTA_VALUE end) as number) P_019,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QA008' then t1.QUOTA_VALUE end) as number) P_020,
                                   cast(
                                           sum(case when t2.QUOTA_CODE = 'QT011' then t1.QUOTA_VALUE end) as number) P_021
                            from qtc_BUSS_QUOTA t1,
                                 qtc_CONF_QUOTA_DEF t2,
                                 qtc_buss_evaluate_main mt
                            where t1.QUOTA_DEF_ID = t2.QUOTA_DEF_ID
                              and t1.DIMENSION = 'G'
                              and t1.EVALUATE_MAIN_ID = MT.EVALUATE_MAIN_ID
                              AND MT.VERSION_NO = P_VERSION
                            GROUP BY DIMENSION_VALUE) P_ICG ON P_ICG.LOA_CODE = RS.LOA_CODE;
        proc_evl_log_run_dt(NULL, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'SPIT_004';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');
        INSERT INTO qtc_BUSS_EVL_CFG_PT_DEV(PT_DEV_ID,
                                            VERSION_NO,
                                            MODEL_DEF_ID,
                                            evaluate_main_id,
                                            loa_code,
                                            icg_no,
                                            DEV_PERIOD,
                                            FLT1)
        SELECT qtc_SEQ_BUSS_EVL_CFG_PT_DEV.NEXTVAL,
               SH.VERSION_NO,
               SH.MODEL_DEF_ID,
               SH.EVL_MAIN_ID,
               SH.LOA_CODE,
               SH.ICG_NO,
               T3.QUOTA_PERIOD - 1,
               T3.QUOTA_VALUE
        FROM qtc_CONF_QUOTA_DEF T1,
             qtc_BUSS_QUOTA T2,
             qtc_BUSS_QUOTA_DETAIL T3,
             (select VERSION_NO, MODEL_DEF_ID, EVL_MAIN_ID, LOA_CODE, ICG_NO
                  from qtc_BUSS_EVL_ICG_MSG
                  where VERSION_NO = P_VERSION
                 group by  VERSION_NO, MODEL_DEF_ID,EVL_MAIN_ID, LOA_CODE, ICG_NO ) SH
        WHERE SH.EVL_MAIN_ID = T2.EVALUATE_MAIN_ID
          AND T1.QUOTA_CODE = 'QP001'
          AND T1.QUOTA_DEF_ID = T2.QUOTA_DEF_ID
          AND T2.DIMENSION = 'G'
          and t2.DIMENSION_VALUE = sh.LOA_CODE
          AND T3.BUSS_QUOTA_ID = T2.BUSS_QUOTA_ID
          AND T3.QUOTA_PERIOD > 0;
        proc_evl_log_run_dt(NULL, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'SPIT_005';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

          merge into qtc_BUSS_EVL_CFG_PT tg
              using (select version_no , model_def_id , icg_no , count(icg_no) v1
                      from qtc_BUSS_EVL_BM_DD_UNIT_CF
                      where version_no = p_version
                      group by version_no , model_def_id , icg_no) sc
          on (tg.version_no = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO)
              when matched then
                update set num25 = sc.v1 ;

        proc_evl_log_run_dt(NULL, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'SPIT_006';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

          merge into qtc_BUSS_EVL_CFG_PT tg
              using (select version_no , model_def_id , icg_no , count(icg_no) v1
                      from qtc_BUSS_EVL_BM_FO_UNIT_CF
                      where version_no = p_version
                      group by version_no , model_def_id , icg_no) sc
          on (tg.version_no = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO)
              when matched then
                update set num25 = sc.v1 ;

        proc_evl_log_run_dt(NULL, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'SPIT_007';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

          merge into qtc_BUSS_EVL_CFG_PT tg
              using (select version_no , model_def_id , icg_no , count(icg_no) v1
                      from qtc_BUSS_EVL_BM_TI_UNIT_CF
                      where version_no = p_version
                      group by version_no , model_def_id , icg_no) sc
          on (tg.version_no = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO)
              when matched then
                update set num25 = sc.v1 ;

        proc_evl_log_run_dt(NULL, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'SPIT_008';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

          merge into qtc_BUSS_EVL_CFG_PT tg
              using (select version_no , model_def_id , icg_no , count(icg_no) v1
                      from qtc_BUSS_EVL_BM_TO_UNIT_CF
                      where version_no = p_version
                      group by version_no , model_def_id , icg_no) sc
          on (tg.version_no = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO)
              when matched then
                update set num25 = sc.v1 ;

        proc_evl_log_run_dt(NULL, p_version, v_node, v_node_dt, NULL, 'S');


        v_node_dt := 'SPIT_009';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

          merge into qtc_BUSS_EVL_CFG_PT tg
              using (select version_no , model_def_id , icg_no , count(icg_no) v1
                      from qtc_BUSS_EVL_CFG_PT_DEV
                      where version_no = p_version
                      group by version_no , model_def_id , icg_no) sc
          on (tg.version_no = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO)
              when matched then
                update set num25 =  NVL(TG.NUM25,0) + NVL(sc.v1,0) ;

        proc_evl_log_run_dt(NULL, p_version, v_node, v_node_dt, NULL, 'S');

        v_node_dt := 'SPIT_010';
        proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, null, 'B');

          merge into qtc_BUSS_EVL_CFG_PT tg
              using (select version_no , model_def_id , icg_no , count(ICG_CF_ID) v1
                      from qtc_BUSS_EVL_LIC_ICG_CF
                      where version_no = p_version
                      group by version_no , model_def_id , icg_no) sc
          on (tg.version_no = sc.VERSION_NO and tg.MODEL_DEF_ID = sc.MODEL_DEF_ID and tg.ICG_NO = sc.ICG_NO)
              when matched then
                update set num25 = greatest(tg.num25 , sc.v1) ;

        proc_evl_log_run_dt(NULL, p_version, v_node, v_node_dt, NULL, 'S');

        proc_evl_log_run(null, p_version, v_node, 'S');
    EXCEPTION
        WHEN OTHERS THEN
            proc_evl_log_run(null, p_version, v_node, 'E');
            proc_evl_log_run_dt(null, p_version, v_node, v_node_dt, sqlerrm, 'S');

            raise_application_error(-20002, SQLERRM);
    END proc_evl_suppose;

    procedure proc_evl_bm_cf(p_version varchar2) is

    BEGIN

        FOR CF IN (SELECT T1.BUSINESS_MODEL || T1.BUSINESS_DIRECTION BUSS_DIR
                   FROM qtc_BUSS_EVALUATE_MAIN T1
                   WHERE T1.VERSION_NO = p_version
                   GROUP BY T1.BUSINESS_MODEL || T1.BUSINESS_DIRECTION)
            LOOP

                IF CF.BUSS_DIR = 'DD' THEN
                    proc_evl_bm_dd_cf(P_VERSION => P_VERSION);
                end if;

                IF CF.BUSS_DIR = 'TI' THEN
                    proc_evl_bm_ti_cf(P_VERSION => P_VERSION);
                end if;

                IF CF.BUSS_DIR = 'FO' THEN
                    proc_evl_bm_fo_cf(P_VERSION => P_VERSION);
                end if;

                IF CF.BUSS_DIR = 'TO' THEN
                    proc_evl_bm_to_cf(P_VERSION => P_VERSION);
                end if;

            end loop;

        proc_evl_bm_lic_cf(p_version);

    END proc_evl_bm_cf;

    procedure proc_evaluate_main(p_year_month varchar, p_version varchar) is

    BEGIN

        update qtc_buss_evaluate_main
        set execution_state = 'R'
        WHERE version_no = p_version
          and year_month = p_year_month;
        COMMIT;

        proc_evl_bm_cf(p_version);

        proc_evl_suppose(p_version);

        proc_evl_interest(p_version);

        proc_evl_calculate(p_version);

        update qtc_buss_evaluate_main
        set execution_state = 'S',
            update_time     = localtimestamp
        WHERE version_no = p_version
          and year_month = p_year_month;
        COMMIT;

    EXCEPTION
        WHEN OTHERS THEN
            update qtc_buss_evaluate_main
            set execution_state = 'E',
                update_time     = localtimestamp
            WHERE version_no = p_version;
            COMMIT;

            raise_application_error(-20002, SQLERRM);

    END proc_evaluate_main;

end qtc_pack_evaluate;
/

