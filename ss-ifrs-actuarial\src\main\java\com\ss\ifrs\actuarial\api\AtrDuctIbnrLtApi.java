package com.ss.ifrs.actuarial.api;

import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveIbnrDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveIbnrVo;
import com.ss.ifrs.actuarial.service.AtrBussReserveIbnrDetailService;
import com.ss.ifrs.actuarial.service.AtrBussReserveIbnrService;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.library.constant.RestfulCodeConstant;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.platform.util.CheckParamsUtil;
import com.ss.library.utils.ExceptionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/4/09
 */

@RestController
@RequestMapping("/duct_ibnr_lt")
@Api(value = "LRC计算对外Api接口")
public class AtrDuctIbnrLtApi extends BaseApi{

    // 日志管理
    final Logger LOG  = LoggerFactory.getLogger(getClass());

    @Autowired
    private AtrBussReserveIbnrDetailService atrBussReserveIbnrDetailService;

    @Autowired
    private AtrBussReserveIbnrService atrBussReserveIbnrService;


    @RequestMapping(value = "/enquiry", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<Map<String, Object>> enquiryPage(@RequestBody AtrBussReserveIbnrVo atrBussReserveIbnrVo,
                                                                    int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        atrBussReserveIbnrVo.setExtractionMethod("2");
        Page<AtrBussReserveIbnrVo> atrBussReserveIbnrVoList = atrBussReserveIbnrService.searchPage(atrBussReserveIbnrVo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("atrBussReserveIbnrVoList", atrBussReserveIbnrVoList);
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @RequestMapping(value = "/find_by_reserveid/{reserveIbnrId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<AtrBussReserveIbnrVo> findById(@PathVariable("reserveIbnrId") Long reserveIbnrId) {
        AtrBussReserveIbnrVo atrBussReserveIbnrVo = atrBussReserveIbnrService.findById(reserveIbnrId);
        return new BaseResponse<AtrBussReserveIbnrVo>(ResCodeConstant.ResCode.SUCCESS, atrBussReserveIbnrVo);
    }

    @ApiOperation("累计赔款查询")
    @RequestMapping(value = "/detail/showCumulativeCurrent", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> showDataForCumulativeCurrent(@RequestBody AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo) {
        List<AtrBussReserveIbnrDetailVo> atrBussReserveIbnrDetailVos = atrBussReserveIbnrDetailService.findCumulative(atrBussReserveIbnrDetailVo);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, atrBussReserveIbnrDetailVos);
    }

    //第二步
    @RequestMapping(value = "/detail/showDataForCDFCurrent", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> showDataForCDFCurrent(@RequestBody AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo) {
        List<AtrBussReserveIbnrDetailVo> atrBussReserveIbnrDetailVos = atrBussReserveIbnrDetailService.findReparations(atrBussReserveIbnrDetailVo);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, atrBussReserveIbnrDetailVos);
    }

    //第三步
    @RequestMapping(value = "/detail/showLtIbnrData", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> showLtIbnrData(@RequestBody AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo) {
        List<AtrBussReserveIbnrDetailVo> atrBussReserveIbnrDetailVos = atrBussReserveIbnrDetailService.showLtIbnrData(atrBussReserveIbnrDetailVo);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, atrBussReserveIbnrDetailVos);
    }

    //cdf计算
    @RequestMapping(value = "/detail/cdfCalculate", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<Object> updateValueAndCDFCalculate(HttpServletRequest request,
                                              @RequestBody AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo) {
        try {
            atrBussReserveIbnrDetailService.updateValueAndCDFCalculate(atrBussReserveIbnrDetailVo);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (UnexpectedRollbackException e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.OTHER_ERROR, null);
        }
    }

    //OS计算
    @RequestMapping(value = "/detail/osCalculate", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<Object> updateValueAndOSCalculate(HttpServletRequest request,
                                                              @RequestBody AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo) {
        try {
            atrBussReserveIbnrDetailService.updateValueAndOSCalculate(atrBussReserveIbnrDetailVo);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (UnexpectedRollbackException e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.OTHER_ERROR, null);
        }
    }



    @ApiOperation(value = "增加主表信息")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public BaseResponse<Object> add(@RequestBody @Validated
                                            AtrBussReserveIbnrVo atrBussReserveIbnrVo, HttpServletRequest request, BindingResult br) {

        if (br.hasErrors()) {
            // 该状态码为“其它格式转换错误”
            StringBuffer errorMessages = new StringBuffer();
            for (int i = 0; i < br.getErrorCount(); i++) {
                errorMessages.append(br.getAllErrors().get(i).getDefaultMessage() + ";");
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.FORMAT_TRANSFORM_ERROR,new CheckParamsUtil().getMessages(errorMessages.toString()));
        }
        atrBussReserveIbnrVo.setCreatorId(this.loginUserId(request));
        atrBussReserveIbnrVo.setCreateTime(new Date());
        atrBussReserveIbnrVo.setExtractionMethod("2");
        try {
            Long userId = this.loginUserId(request);
            atrBussReserveIbnrService.save(atrBussReserveIbnrVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            LOG.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "校验配置")
    @RequestMapping(value = "/check_pk", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> checkPK(@RequestBody AtrBussReserveIbnrVo atrBussReserveIbnrVo) {
        String result = "0";
        try {
            List<AtrBussReserveIbnrVo> vos = atrBussReserveIbnrService.findList(atrBussReserveIbnrVo);
            if(vos.size()>0){
                result = "1";
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, result);
        }
    }

    @ApiOperation(value = "删除配置信息")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public BaseResponse<Object> delete(HttpServletRequest request, @RequestBody AtrBussReserveIbnrVo atrBussReserveIbnrVo) {
        try {
            Long userId = this.loginUserId(request);
            atrBussReserveIbnrService.delete(atrBussReserveIbnrVo);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "确认状态修改")
    @RequestMapping(value = "/confirm", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> reserveOsConfirm(HttpServletRequest request, @RequestBody AtrBussReserveIbnrVo atrBussReserveIbnrVo) {
        Long userId = this.loginUserId(request);
        atrBussReserveIbnrService.confirm(atrBussReserveIbnrVo, userId);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
    }

    @ApiOperation(value = "Excel导入")
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> excelImport(@RequestParam(value = "file", required = false) MultipartFile file,
                                            HttpServletRequest request, AtrBussReserveIbnrVo atrBussReserveIbnrVo) throws Exception {
        Long userId = this.loginUserId(request);
        String userCode = this.loginUserCode(request);

        String fileName = request.getParameter("fileName");
        AtrBussReserveIbnrVo ibnrVo = new AtrBussReserveIbnrVo();
        ibnrVo.setFileName(fileName);
        ibnrVo.setCreatorId(userId);
        ibnrVo.setUpdatorId(userId);
        ibnrVo.setCreateTime(new Date());
        ibnrVo.setUpdateTime(new Date());
        ibnrVo.setCreatorCode(userCode);

        String exceptionMsg = null;
        try {
            atrBussReserveIbnrService.excelImport(file, ibnrVo, userId);
        } catch (Exception ex) {
            ibnrVo.setDrawStatus("0");
            exceptionMsg = ExceptionUtil.getMessage(ex);
            LOG.error(ex.getLocalizedMessage(), ex);
        }

        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, exceptionMsg);
    }

    @ApiOperation(value = "Excel导出")
    @RequestMapping(value = "/generate_excel", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<Object> generateTemplate(HttpServletResponse response, AtrBussReserveIbnrVo atrBussReserveIbnrVo) {
        try {
            Date date = new Date();
            System.out.println(date);
            atrBussReserveIbnrService.excelExport(response, atrBussReserveIbnrVo);
            Date date2 = new Date();
            System.out.println(date2);
            System.out.println(date2.getTime() - date.getTime());
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, RestfulCodeConstant.RestfulCode.SUCCESS);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, RestfulCodeConstant.RestfulCode.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation("查询一个版本下的合同组合数据")
    @RequestMapping(value = "/find_ibnr_portfolio", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findPortfolioData(@RequestBody AtrBussReserveIbnrVo atrBussReserveIbnrVo, int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrBussReserveIbnrDetailVo> page = atrBussReserveIbnrService.findPortfolioData(atrBussReserveIbnrVo, pageParam);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("resultList",page);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
    }
    /*@ApiOperation(value = "Excel导出")
    @RequestMapping(value = "/export", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public void download(HttpServletResponse response, AtrBussReserveIbnrVo atrBussReserveIbnrVo) throws Exception {
        try {
            atrBussReserveIbnrService.export(response, atrBussReserveIbnrVo);
        } catch (Exception ex) {
            LOG.error(ex.getLocalizedMessage(), ex);
            throw ex;
        }
    }*/

}
