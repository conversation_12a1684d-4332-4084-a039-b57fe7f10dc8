/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-11-29 14:13:36
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao.conf;

import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInflationFactor;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfInflationFactorConditionVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfInflationFactorPageVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrInflationRatioRangeVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrInflationRatioVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-11-29 14:13:36<br/>
 * Description: 通货膨胀系数 Dao类<br/>
 * Related Table Name: atr_conf_inflation_factor<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrConfInflationFactorDao extends IDao<AtrConfInflationFactor, Long> {

    Page<AtrConfInflationFactorPageVo> searchPage(AtrConfInflationFactorConditionVo vo, Pageable page);

    /**
     * 计算通货膨胀比例
     */
    void calcRatio();

    AtrInflationRatioRangeVo queryRatioRange();

    List<AtrInflationRatioVo> queryAllRatio();

}