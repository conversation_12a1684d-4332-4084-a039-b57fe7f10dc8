<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-11-29 14:13:36 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.conf.AtrConfInflationFactorDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**BaseDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInflationFactor">
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="buss_year" property="bussYear" jdbcType="VARCHAR" />
    <result column="inf_factor" property="infFactor" jdbcType="NUMERIC" />
    <result column="confirm_is" property="confirmIs" jdbcType="VARCHAR" />
    <result column="confirm_id" property="confirmId" jdbcType="BIGINT" />
    <result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    id, buss_year, inf_factor, confirm_is, confirm_id, confirm_time, create_time, update_time
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="id != null ">
          and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="bussYear != null and bussYear != ''">
          and buss_year = #{bussYear,jdbcType=VARCHAR}
      </if>
      <if test="infFactor != null ">
          and inf_factor = #{infFactor,jdbcType=NUMERIC}
      </if>
      <if test="confirmIs != null and confirmIs != ''">
          and confirm_is = #{confirmIs,jdbcType=VARCHAR}
      </if>
      <if test="confirmId != null ">
          and confirm_id = #{confirmId,jdbcType=BIGINT}
      </if>
      <if test="confirmTime != null ">
          and confirm_time = #{confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null ">
          and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.id != null ">
          and id = #{condition.id,jdbcType=BIGINT}
      </if>
      <if test="condition.bussYear != null and condition.bussYear != ''">
          and buss_year = #{condition.bussYear,jdbcType=VARCHAR}
      </if>
      <if test="condition.infFactor != null ">
          and inf_factor = #{condition.infFactor,jdbcType=NUMERIC}
      </if>
      <if test="condition.confirmIs != null and condition.confirmIs != ''">
          and confirm_is = #{condition.confirmIs,jdbcType=VARCHAR}
      </if>
      <if test="condition.confirmId != null ">
          and confirm_id = #{condition.confirmId,jdbcType=BIGINT}
      </if>
      <if test="condition.confirmTime != null ">
          and confirm_time = #{condition.confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.createTime != null ">
          and create_time = #{condition.createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.updateTime != null ">
          and update_time = #{condition.updateTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="id != null ">
          and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="bussYear != null and bussYear != ''">
          and buss_year = #{bussYear,jdbcType=VARCHAR}
      </if>
      <if test="infFactor != null ">
          and inf_factor = #{infFactor,jdbcType=NUMERIC}
      </if>
      <if test="confirmIs != null and confirmIs != ''">
          and confirm_is = #{confirmIs,jdbcType=VARCHAR}
      </if>
      <if test="confirmId != null ">
          and confirm_id = #{confirmId,jdbcType=BIGINT}
      </if>
      <if test="confirmTime != null ">
          and confirm_time = #{confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null ">
          and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from "atr_conf_inflation_factor"
    where id = #{id,jdbcType=BIGINT}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from "atr_conf_inflation_factor"
    where id in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "atr_conf_inflation_factor"
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInflationFactor">
    select 
    <include refid="Base_Column_List" />
    from "atr_conf_inflation_factor"
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from "atr_conf_inflation_factor"
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="id" keyProperty="id" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInflationFactor">
    insert into "atr_conf_inflation_factor"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="bussYear != null">
        buss_year,
      </if>
      <if test="infFactor != null">
        inf_factor,
      </if>
      <if test="confirmIs != null">
        confirm_is,
      </if>
      <if test="confirmId != null">
        confirm_id,
      </if>
      <if test="confirmTime != null">
        confirm_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="bussYear != null">
        #{bussYear,jdbcType=VARCHAR},
      </if>
      <if test="infFactor != null">
        #{infFactor,jdbcType=NUMERIC},
      </if>
      <if test="confirmIs != null">
        #{confirmIs,jdbcType=VARCHAR},
      </if>
      <if test="confirmId != null">
        #{confirmId,jdbcType=BIGINT},
      </if>
      <if test="confirmTime != null">
        #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert into "atr_conf_inflation_factor"
     (id, buss_year, inf_factor, 
      confirm_is, confirm_id, confirm_time, 
      create_time, update_time)
     values 
    <foreach collection="list" item="item" index="index" separator=",">
       (#{item.id,jdbcType=BIGINT}, #{item.bussYear,jdbcType=VARCHAR}, #{item.infFactor,jdbcType=NUMERIC}, 
        #{item.confirmIs,jdbcType=VARCHAR}, #{item.confirmId,jdbcType=BIGINT}, #{item.confirmTime,jdbcType=TIMESTAMP}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInflationFactor">
    update "atr_conf_inflation_factor"
    <set>
      <if test="bussYear != null">
        buss_year = #{bussYear,jdbcType=VARCHAR},
      </if>
      <if test="infFactor != null">
        inf_factor = #{infFactor,jdbcType=NUMERIC},
      </if>
      <if test="confirmIs != null">
        confirm_is = #{confirmIs,jdbcType=VARCHAR},
      </if>
      <if test="confirmId != null">
        confirm_id = #{confirmId,jdbcType=BIGINT},
      </if>
      <if test="confirmTime != null">
        confirm_time = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInflationFactor">
    update "atr_conf_inflation_factor"
    <set>
      <if test="record.bussYear != null">
        buss_year = #{record.bussYear,jdbcType=VARCHAR},
      </if>
      <if test="record.infFactor != null">
        inf_factor = #{record.infFactor,jdbcType=NUMERIC},
      </if>
      <if test="record.confirmIs != null">
        confirm_is = #{record.confirmIs,jdbcType=VARCHAR},
      </if>
      <if test="record.confirmId != null">
        confirm_id = #{record.confirmId,jdbcType=BIGINT},
      </if>
      <if test="record.confirmTime != null">
        confirm_time = #{record.confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from "atr_conf_inflation_factor"
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from "atr_conf_inflation_factor"
    where id in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from "atr_conf_inflation_factor"
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInflationFactor">
    select count(1) from "atr_conf_inflation_factor"
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>