call RPT_PACK_COMMONUTILS_ADD_TABLE_COLUMN('RPT_CONF_REPORT_TASK_CONDITION', 'search_code', 'varchar(64)', '查询编码');

call RPT_PACK_COMMONUTILS_ADD_TABLE_COLUMN('RPT_CONF_REPORT_TASK_CONDITION', 'search_type', 'varchar(2)',
                                           '查询类型：1-码表查询，2-输入框');


truncate table RPT_CONF_REPORT_TASK_CONDITION;

INSERT INTO RPTUSER.RPT_CONF_REPORT_TASK_CONDITION (ID, CONDITION_FIELD, CO<PERSON><PERSON>ION_SQL_FIELD, CONDITION_C_NAME,
                                                    CONDITION_E_NAME, CONDITION_L_NAME, SEARCH_CODE, SEARCH_TYPE)
VALUES ((select coalesce(max(id),0) + 1 from RPT_CONF_REPORT_TASK_CONDITION), 'ARTICLE9', 'ARTICLE9', '合同组', 'Group ID',
        '合同組', null, '2');


INSERT INTO RPTUSER.RPT_CONF_REPORT_TASK_CONDITION (ID, CONDITION_FIELD, CONDITION_SQL_FIELD, CONDITION_C_NAME,
                                                    CONDITION_E_NAME, CONDITION_L_NAME, SEARCH_CODE, SEARCH_TYPE)
VALUES ((select coalesce(max(id),0) + 1 from RPT_CONF_REPORT_TASK_CONDITION), 'ARTICLE8', 'ARTICLE8', '合同组合', 'Portfolio ID',
        '合同組合', null, '2');

INSERT INTO RPTUSER.RPT_CONF_REPORT_TASK_CONDITION (ID, CONDITION_FIELD, CONDITION_SQL_FIELD, CONDITION_C_NAME,
                                                    CONDITION_E_NAME, CONDITION_L_NAME, SEARCH_CODE, SEARCH_TYPE)
VALUES ((select coalesce(max(id),0) + 1 from RPT_CONF_REPORT_TASK_CONDITION), 'ARTICLE7', 'ARTICLE7', '现金流专项', 'Cashflow Article',
        '現金流專項', null, '2');

INSERT INTO RPTUSER.RPT_CONF_REPORT_TASK_CONDITION (ID, CONDITION_FIELD, CONDITION_SQL_FIELD, CONDITION_C_NAME,
                                                    CONDITION_E_NAME, CONDITION_L_NAME, SEARCH_CODE, SEARCH_TYPE)
VALUES ((select coalesce(max(id),0) + 1 from RPT_CONF_REPORT_TASK_CONDITION), 'ARTICLE18', 'ARTICLE18', '渠道', 'Channel Code',
        '渠道', null, '2');


INSERT INTO RPTUSER.RPT_CONF_REPORT_TASK_CONDITION (ID, CONDITION_FIELD, CONDITION_SQL_FIELD, CONDITION_C_NAME,
                                                    CONDITION_E_NAME, CONDITION_L_NAME, SEARCH_CODE, SEARCH_TYPE)
VALUES ((select coalesce(max(id),0) + 1 from RPT_CONF_REPORT_TASK_CONDITION), 'ARTICLE21', 'ARTICLE21', '补充产品段', 'Sur Product Id',
        '補充產品段', null, '2');

INSERT INTO RPTUSER.RPT_CONF_REPORT_TASK_CONDITION (ID, CONDITION_FIELD, CONDITION_SQL_FIELD, CONDITION_C_NAME,
                                                    CONDITION_E_NAME, CONDITION_L_NAME, SEARCH_CODE, SEARCH_TYPE)
VALUES ((select coalesce(max(id),0) + 1 from RPT_CONF_REPORT_TASK_CONDITION), 'ARTICLE20', 'ARTICLE20', '产品段', 'Product Id',
        '產品段', null, '2');


INSERT INTO RPTUSER.RPT_CONF_REPORT_TASK_CONDITION (ID, CONDITION_FIELD, CONDITION_SQL_FIELD, CONDITION_C_NAME,
                                                    CONDITION_E_NAME, CONDITION_L_NAME, SEARCH_CODE, SEARCH_TYPE)
VALUES ((select coalesce(max(id),0) + 1 from RPT_CONF_REPORT_TASK_CONDITION), 'ARTICLE19', 'ARTICLE19', '明细段', 'Detail Id',
        '明細段', null, '2');

INSERT INTO RPTUSER.RPT_CONF_REPORT_TASK_CONDITION (ID, CONDITION_FIELD, CONDITION_SQL_FIELD, CONDITION_C_NAME,
                                                    CONDITION_E_NAME, CONDITION_L_NAME, SEARCH_CODE, SEARCH_TYPE)
VALUES ((select coalesce(max(id),0) + 1 from RPT_CONF_REPORT_TASK_CONDITION), 'ARTICLE2', 'ARTICLE2', '部门专项', 'Dept Article',
        '部門專項', null, '2');

