/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2022-02-10 15:13:31
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo;

//import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2022-02-10 15:13:31<br/>
 * Description: upr结果明细表<br/>
 * Table Name: atr_buss_reserve_upr_detail<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "结果明细表")
public class AtrBussReserveExcelVo implements Serializable {

    //@ExcelProperty(index = 0, value = "Accounting Unit")
    private String entityCode;

    //@ExcelProperty(index = 1, value = "Year Month")
    private String yearMonth;

    //@ExcelProperty(index = 2, value = "Product Risk Categories")
    private String riskClassCode;

    //@ExcelProperty(index = 3, value = "Risk")
    private String riskCode;

    //@ExcelProperty(index = 4, value = "Quantification Unit")
    private String cmunitNo;

    //@ExcelProperty(index = 5, value = "Policy No.")
    private String policyNo;

   // @ExcelProperty(index = 6, value = "Endorse No.")
    private String endorseSeqNo;

    //@ExcelProperty(index = 7, value = "Endorse No.")
    private String endorseNo;

    //@ExcelProperty(index = 8, value = "Start Date")
    private Date startDate;

    //@ExcelProperty(index = 9, value = "End Date")
    private Date endDate;

    //@ExcelProperty(index = 10, value = "Business Type")
    private String businessSourceCode;

    //@ExcelProperty(index = 11, value = "Business Type")
    private String currencyCode;

    //@ExcelProperty(index = 12, value = {"Premium","Before R/I"})
    private BigDecimal beforePremium;
    //@ExcelProperty(index = 13, value = {"Unearned Premium","Before R/I"})
    private BigDecimal unearnedBeforePremium;
   // @ExcelProperty(index = 14, value = {"Premium","outward"})
    private BigDecimal outwardPremium;
    //@ExcelProperty(index = 15, value = {"Unearned Premium","outward"})
    private BigDecimal unearnedOutwardPremium;
    //@ExcelProperty(index = 16, value = {"Premium","After R/I"})
    private BigDecimal afterPremium;
    //@ExcelProperty(index = 17, value = {"Unearned Premium", "After R/I"})
    private BigDecimal unearnedAfterPremium;

    private static final long serialVersionUID = 1L;

    public String getEntityCode() {
        return entityCode;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public String getRiskCode() {
        return riskCode;
    }

    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    public String getCmunitNo() {
        return cmunitNo;
    }

    public void setCmunitNo(String cmunitNo) {
        this.cmunitNo = cmunitNo;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getEndorseSeqNo() {
        return endorseSeqNo;
    }

    public void setEndorseSeqNo(String endorseSeqNo) {
        this.endorseSeqNo = endorseSeqNo;
    }

    public String getEndorseNo() {
        return endorseNo;
    }

    public void setEndorseNo(String endorseNo) {
        this.endorseNo = endorseNo;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public BigDecimal getBeforePremium() {
        return beforePremium;
    }

    public void setBeforePremium(BigDecimal beforePremium) {
        this.beforePremium = beforePremium;
    }

    public BigDecimal getUnearnedBeforePremium() {
        return unearnedBeforePremium;
    }

    public void setUnearnedBeforePremium(BigDecimal unearnedBeforePremium) {
        this.unearnedBeforePremium = unearnedBeforePremium;
    }

    public BigDecimal getOutwardPremium() {
        return outwardPremium;
    }

    public void setOutwardPremium(BigDecimal outwardPremium) {
        this.outwardPremium = outwardPremium;
    }

    public BigDecimal getUnearnedOutwardPremium() {
        return unearnedOutwardPremium;
    }

    public void setUnearnedOutwardPremium(BigDecimal unearnedOutwardPremium) {
        this.unearnedOutwardPremium = unearnedOutwardPremium;
    }

    public BigDecimal getAfterPremium() {
        return afterPremium;
    }

    public void setAfterPremium(BigDecimal afterPremium) {
        this.afterPremium = afterPremium;
    }

    public BigDecimal getUnearnedAfterPremium() {
        return unearnedAfterPremium;
    }

    public void setUnearnedAfterPremium(BigDecimal unearnedAfterPremium) {
        this.unearnedAfterPremium = unearnedAfterPremium;
    }
}