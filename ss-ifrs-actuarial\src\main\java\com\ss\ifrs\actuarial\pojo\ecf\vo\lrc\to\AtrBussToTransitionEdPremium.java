package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to;

import com.ss.ifrs.actuarial.util.abp.Col;
import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Data;

import java.math.BigDecimal;

/**
 * TO业务过渡期已赚保费
 * 
 * <AUTHOR> generated
 */
@Data
@Tab("atr_buss_to_transition_ed_premium")
public class AtrBussToTransitionEdPremium {

    /**
     * 机构ID
     */
    @Col("entity_id")
    private Long entityId;

    /**
     * 业务年月
     */
    @Col("year_month")
    private String yearMonth;

    /**
     * 合约号
     */
    @Col("treaty_no")
    private String treatyNo;

    /**
     * 保单号
     */
    @Col("policy_no")
    private String policyNo;

    /**
     * 批单序号
     */
    @Col("endorse_seq_no")
    private String endorseSeqNo;

    /**
     * 险别代码
     */
    @Col("kind_code")
    private String kindCode;

    /**
     * 已赚保费
     */
    @Col("ed_premium")
    private BigDecimal edPremium;

} 