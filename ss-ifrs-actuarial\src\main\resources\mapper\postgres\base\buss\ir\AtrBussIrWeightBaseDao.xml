<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by Taiping MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2024-08-21 18:15:20 -->
<!-- Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.AtrBussIrWeightDao">
  <!-- 本文件由Taiping MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**BaseDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.ir.po.AtrBussIrWeight">
    <result column="ID" property="id" jdbcType="DECIMAL" />
    <result column="ENTITY_ID" property="entityId" jdbcType="DECIMAL" />
    <result column="YEAR_MONTH" property="yearMonth" jdbcType="VARCHAR" />
    <result column="CURRENCY_CODE" property="currencyCode" jdbcType="VARCHAR" />
    <result column="ICG_NO" property="icgNo" jdbcType="VARCHAR" />
    <result column="PREMIUM" property="premium" jdbcType="DECIMAL" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    ID, ENTITY_ID, YEAR_MONTH, CURRENCY_CODE, ICG_NO, PREMIUM, CREATE_TIME
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="id != null ">
          and ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="entityId != null ">
          and ENTITY_ID = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
          and YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="currencyCode != null and currencyCode != ''">
          and CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="icgNo != null and icgNo != ''">
          and ICG_NO = #{icgNo,jdbcType=VARCHAR}
      </if>
      <if test="premium != null ">
          and PREMIUM = #{premium,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null ">
          and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.id != null ">
          and ID = #{condition.id,jdbcType=DECIMAL}
      </if>
      <if test="condition.entityId != null ">
          and ENTITY_ID = #{condition.entityId,jdbcType=DECIMAL}
      </if>
      <if test="condition.yearMonth != null and condition.yearMonth != ''">
          and YEAR_MONTH = #{condition.yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="condition.currencyCode != null and condition.currencyCode != ''">
          and CURRENCY_CODE = #{condition.currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.icgNo != null and condition.icgNo != ''">
          and ICG_NO = #{condition.icgNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.premium != null ">
          and PREMIUM = #{condition.premium,jdbcType=DECIMAL}
      </if>
      <if test="condition.createTime != null ">
          and CREATE_TIME = #{condition.createTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="id != null ">
          and ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="entityId != null ">
          and ENTITY_ID = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
          and YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="currencyCode != null and currencyCode != ''">
          and CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="icgNo != null and icgNo != ''">
          and ICG_NO = #{icgNo,jdbcType=VARCHAR}
      </if>
      <if test="premium != null ">
          and PREMIUM = #{premium,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null ">
          and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_IR_WEIGHT
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.ir.po.AtrBussIrWeight">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_IR_WEIGHT
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_IR_WEIGHT
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.ir.po.AtrBussIrWeight">
    insert into ATR_BUSS_IR_WEIGHT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="entityId != null">
        ENTITY_ID,
      </if>
      <if test="yearMonth != null">
        YEAR_MONTH,
      </if>
      <if test="currencyCode != null">
        CURRENCY_CODE,
      </if>
      <if test="icgNo != null">
        ICG_NO,
      </if>
      <if test="premium != null">
        PREMIUM,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=DECIMAL},
      </if>
      <if test="yearMonth != null">
        #{yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="currencyCode != null">
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="icgNo != null">
        #{icgNo,jdbcType=VARCHAR},
      </if>
      <if test="premium != null">
        #{premium,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all 
    <foreach collection="list" item="item" index="index">
      into ATR_BUSS_IR_WEIGHT values 
       (#{item.id,jdbcType=DECIMAL}, #{item.entityId,jdbcType=DECIMAL}, 
        #{item.yearMonth,jdbcType=VARCHAR}, #{item.currencyCode,jdbcType=VARCHAR}, #{item.icgNo,jdbcType=VARCHAR}, 
        #{item.premium,jdbcType=DECIMAL}, #{item.createTime,jdbcType=TIMESTAMP}
        )
    </foreach>
    select 1 from dual
  </insert>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.ir.po.AtrBussIrWeight">
    update ATR_BUSS_IR_WEIGHT
    <set>
      <if test="record.id != null">
        ID = #{record.id,jdbcType=DECIMAL},
      </if>
      <if test="record.entityId != null">
        ENTITY_ID = #{record.entityId,jdbcType=DECIMAL},
      </if>
      <if test="record.yearMonth != null">
        YEAR_MONTH = #{record.yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="record.currencyCode != null">
        CURRENCY_CODE = #{record.currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.icgNo != null">
        ICG_NO = #{record.icgNo,jdbcType=VARCHAR},
      </if>
      <if test="record.premium != null">
        PREMIUM = #{record.premium,jdbcType=DECIMAL},
      </if>
      <if test="record.createTime != null">
        CREATE_TIME = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from ATR_BUSS_IR_WEIGHT
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.ir.po.AtrBussIrWeight">
    select count(1) from ATR_BUSS_IR_WEIGHT
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>