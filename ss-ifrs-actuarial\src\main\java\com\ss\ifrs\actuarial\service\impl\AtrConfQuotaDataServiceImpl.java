package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.conf.AppConfig;
import com.ss.ifrs.actuarial.constant.ActuarialConstant;
import com.ss.ifrs.actuarial.dao.conf.*;
import com.ss.ifrs.actuarial.feign.BmsConfCodeFeignClient;
import com.ss.ifrs.actuarial.pojo.atrconf.po.*;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.*;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrDapIcgRiskClassVo;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrExcelCellVo;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrExcelParseVo;
import com.ss.ifrs.actuarial.service.*;
import com.ss.ifrs.actuarial.util.AtrConfQuotaImportUtil;
import com.ss.ifrs.actuarial.util.AtrExcelGenerateUtils;
import com.ss.ifrs.actuarial.util.ExcelImportUtils;
import com.ss.ifrs.actuarial.util.ExcelUtil;
import com.ss.library.constant.ExcelConstant;
import com.ss.library.excel.ExcelSheet;
import com.ss.library.excel.ExcelSheetData;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ClassUtil;
import com.ss.library.utils.DataUtil;
import com.ss.library.utils.StringUtil;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.pojo.bbs.vo.BbsConfLoaVo;
import com.ss.platform.pojo.bbs.vo.BbsConfRegularRuleVo;
import com.ss.platform.pojo.com.po.ConfCode;
import com.ss.platform.util.ExcelExportUtil;
import com.ss.platform.util.LanguageUtil;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.el.parser.ParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.mail.IllegalWriteException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/11/10
 * @Param param
 * @return return
 **/
@Service("atrConfQuotaDataService")
public class AtrConfQuotaDataServiceImpl implements AtrConfQuotaDataService {

    @Autowired
    AtrConfQuotaDao atrConfQuotaDao;

    @Autowired
    AtrConfQuotaHisDao atrConfQuotaHisDao;

    @Autowired
    AtrConfQuotaDetailDao atrConfQuotaDetailDao;

    @Autowired
    AtrConfQuotaDetailHisDao atrConfQuotaDetailHisDao;

    @Autowired
    AtrConfQuotaDefService atrConfQuotaDefService;
    
    @Autowired
    AtrConfQuotaDefDao atrConfQuotaDefDao;

    @Autowired
    AtrConfExcelService atrConfExcelService;

    @Autowired
    AppConfig appConfig;

    @Autowired
    AtrConfCodeService atrConfCodeService;

    @Autowired
    private AtrImportService atrImportService;

    @Autowired
    AtrExportService atrExportService;

    @Autowired
    AtrDapTreatyService atrDapTreatyService;

    @Autowired(required = false)
    private BmsConfCodeFeignClient bmsConfCodeFeignClient;

    @Autowired
    AtrConfQuotaImportUtil atrConfQuotaImportUtil;

    final Logger LOG = LoggerFactory.getLogger(getClass());

    @Override
    public Page<Map<String,Object>> findAtrConfQuotaList(AtrConfQuotaVo atrConfQuotaVo, Pageable pageable){
        List<AtrConfQuotaVo> quotaVoList = new ArrayList<AtrConfQuotaVo>();
        Page<Map<String,Object>> pages = new Page<Map<String,Object>>();
        pages = atrConfQuotaDao.fuzzySearchPage(atrConfQuotaVo, pageable);
        if(ObjectUtils.isNotEmpty(pages) && ObjectUtils.isNotEmpty(pages.getContent())){
            for (Map<String,Object> map : pages.getContent()) {
                //业务维度主键查询具体假设
                atrConfQuotaVo.setEntityId(Long.valueOf(map.get("entityId").toString()));
                atrConfQuotaVo.setBusinessSourceCode(map.get("businessSourceCode").toString());
                atrConfQuotaVo.setQuotaClass(map.get("quotaClass").toString());
                atrConfQuotaVo.setYearMonth(map.get("yearMonth").toString());
                atrConfQuotaVo.setDimension(map.get("dimension").toString());
                atrConfQuotaVo.setDimensionValue(map.get("dimensionValue").toString());
                if (ObjectUtils.isNotEmpty(map.get("riskClassCode"))){
                    atrConfQuotaVo.setRiskClassCode(map.get("riskClassCode").toString());
                } else {
                    atrConfQuotaVo.setRiskClassCode(null);
                }

                quotaVoList = atrConfQuotaDao.selectQuotaValue(atrConfQuotaVo);
                //查询出具体指标及值，然后放进map。把为百分比的数值乘以100
                quotaVoList.forEach(quotaVo ->{
                    map.put(quotaVo.getQuotaCode(), this.convertPercentByValue(quotaVo.getQuotaValueType(), quotaVo.getQuotaValue()));
                });
            }
        }
        return pages;
    }

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/12/27 17:01
     * @Description 把为百分比的数值乘以100
     */
    private String convertPercentByValue(String quotaValueType, String quotaValue){
        BigDecimal percent = new BigDecimal(100);
        // 指标数据类型为4-百分比，指标值不为空并且符合BigDecimal
        if(ActuarialConstant.QuotaType.VALUE_TYPE_PERCENTAGES.equals(quotaValueType)
            && null != quotaValue && DataUtil.isBigDecimal(quotaValue)){
            //multiply乘法
            quotaValue = new BigDecimal(quotaValue).multiply(percent).stripTrailingZeros().toPlainString();
        }
        return quotaValue;
    }

    @Override
    public List<AtrConfQuotaVo> findList(AtrConfQuotaVo atrConfQuotaVo){
        List<AtrConfQuotaVo> voList = atrConfQuotaDao.findListByVo(atrConfQuotaVo);
        return voList;
    }



    @Override
    public AtrConfQuotaVo findHisByPk(Long quotaHisId) {
        AtrConfQuotaVo vo = atrConfQuotaHisDao.findQuotaHisById(quotaHisId);
        return vo;
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public void deleteByPk(Long quotaId, Long userId){
        try{
            //写入轨迹表
            AtrConfQuota atrConfQuota = atrConfQuotaDao.findById(quotaId);
            this.dealSaveHis(atrConfQuota, userId, CommonConstant.OperType.DELETE);
            atrConfQuotaDao.deleteById(quotaId);
        }catch (UnexpectedRollbackException e){
            LOG.error(e.getLocalizedMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void updateAudit(AtrConfQuotaVo atrConfQuotaVo, Long userId){
        try{
            atrConfQuotaVo.setCheckedTime(new Date());
            atrConfQuotaVo.setCheckedId(userId);
            if (ObjectUtils.isNotEmpty(atrConfQuotaVo) && ObjectUtils.isNotEmpty(atrConfQuotaVo.getEntityId()) && ObjectUtils.isNotEmpty(atrConfQuotaVo.getDimensionValue()))
            {
                Date currentDate = new Date();
                AtrConfQuota atrConfQuota;
                String auditState = atrConfQuotaVo.getAuditState();
                atrConfQuotaVo.setAuditState(null);
                List<AtrConfQuotaVo> voList = atrConfQuotaDao.findVoBy(atrConfQuotaVo);
                Integer serialNo =  atrConfQuotaDao.findMaxSerialNo(atrConfQuotaVo);
                for(AtrConfQuotaVo confQuotaVo : voList){
                    confQuotaVo.setAuditState(auditState);
                    confQuotaVo.setCheckedMsg(atrConfQuotaVo.getCheckedMsg());
                    confQuotaVo.setCheckedTime(currentDate);
                    confQuotaVo.setCheckedId(userId);
                    confQuotaVo.setSerialNo(ObjectUtils.isEmpty(serialNo) ? 1 : serialNo +1);
                    atrConfQuotaDao.updateAudit(confQuotaVo);
                    atrConfQuota = ClassUtil.convert(confQuotaVo, AtrConfQuota.class);
                    atrConfQuota.setAuditState(auditState);
                    atrConfQuota.setValidIs(confQuotaVo.getValidIs());
                    this.dealSaveHis(atrConfQuota, userId, CommonConstant.OperType.AUDIT);
                }
                this.saveQuotaDetailHis(atrConfQuotaVo, serialNo, userId, CommonConstant.OperType.AUDIT);
            }
        }catch (UnexpectedRollbackException e){
            LOG.error(e.getLocalizedMessage(), e);
            throw new RuntimeException();
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public String disableValid(AtrConfQuotaVo atrConfQuotaVo, Long userId){
        String validIs = atrConfQuotaVo.getValidIs();
        atrConfQuotaVo.setValidIs(null);
        if(!verifyQuotaVo(atrConfQuotaVo)) {
            return validIs;
        }
        List<AtrConfQuotaVo> voList = atrConfQuotaDao.findVoBy(atrConfQuotaVo);
        Integer serialNo =  atrConfQuotaDao.findMaxSerialNo(atrConfQuotaVo);
        if (null != voList && voList.size() > 0) {
            try {
                AtrConfQuota atrConfQuota;
                Date currentDate = new Date();
                for(AtrConfQuotaVo confQuotaVo : voList){
                    confQuotaVo.setValidIs(validIs);
                    confQuotaVo.setAuditState("0");
                    confQuotaVo.setCheckedMsg("");
                    confQuotaVo.setUpdateTime(currentDate);
                    confQuotaVo.setSerialNo(ObjectUtils.isEmpty(serialNo) ? 1 : serialNo +1);
                    // 1、一般业务模型businessModel为D-直保/再保临汾时，只有风险大类riskClass，treatyClass为空；
                    //先使用LOA业务线
                    if(StringUtil.isNotEmpty(confQuotaVo.getLoaCode()))
                    {
                        atrConfQuotaDao.updateValid(confQuotaVo);
                    }
                    atrConfQuota = ClassUtil.convert(confQuotaVo, AtrConfQuota.class);
                    //写入轨迹表
                    this.dealSaveHis(atrConfQuota, userId, CommonConstant.OperType.MODIFY);
                }
                this.saveQuotaDetailHis(atrConfQuotaVo, serialNo, userId, CommonConstant.OperType.MODIFY);
                return atrConfQuotaVo.getValidIs();
            } catch (UnexpectedRollbackException e) {
                LOG.error(e.getLocalizedMessage(), e);
                throw e;
            }
        } else {
            return atrConfQuotaVo.getValidIs();
        }
    }


    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public String batchAudit(ArrayList<AtrConfQuotaVo> auditDealList, Long userId){
        /**
         * 业务逻辑：
         *  1、审核状态变为待审核状态 0-待审核；
         */
        try {
            List<AtrConfQuota> atrConfQuotaList = ClassUtil.convert(auditDealList, AtrConfQuota.class);
            atrConfQuotaList.stream().forEach(atrConfQuota -> {
                // 调用单个审核
                AtrConfQuotaVo atrConfQuotaVo = ClassUtil.convert(atrConfQuota, AtrConfQuotaVo.class);
                this.updateAudit(atrConfQuotaVo, userId);
            });
            return ResCodeConstant.ResCode.SUCCESS;
        } catch (UnexpectedRollbackException e) {
            LOG.error(e.getLocalizedMessage(),e);
            throw e;
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deleteByVo(AtrConfQuotaVo atrConfQuotaVo, Long userId){
        if(!verifyQuotaVo(atrConfQuotaVo)) {
            return;
        }
        List<AtrConfQuota> list = atrConfQuotaDao.findList(ClassUtil.convert(atrConfQuotaVo, AtrConfQuota.class));
        Integer serialNo =  atrConfQuotaDao.findMaxSerialNo(atrConfQuotaVo);
        if(!list.isEmpty()){
            Date currentDate = new Date();
            for(AtrConfQuota atrConfQuota : list){
                atrConfQuotaDao.deleteById(atrConfQuota.getQuotaId());
                atrConfQuota.setUpdateTime(currentDate);
                atrConfQuota.setUpdatorId(userId);
                atrConfQuota.setSerialNo(ObjectUtils.isEmpty(serialNo) ? 1 : serialNo +1);
                this.dealSaveHis(atrConfQuota, userId, CommonConstant.OperType.DELETE);
            }
            this.saveQuotaDetailHis(atrConfQuotaVo, serialNo, userId, CommonConstant.OperType.DELETE);
        }
    }

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 保存轨迹信息
     * @Return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void dealSaveHis(AtrConfQuota po, Long userId, String operType) {
        AtrConfQuotaHis atrConfQuotaHis = new AtrConfQuotaHis();
        ClassUtil.copyProperties(po, atrConfQuotaHis);
        atrConfQuotaHis.setOperId(userId);
        atrConfQuotaHis.setOperTime(new Date());
        atrConfQuotaHis.setOperType(operType);
        atrConfQuotaHisDao.save(atrConfQuotaHis);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void dealSaveListHis(List<AtrConfQuota> poList, Long userId, String operType) {
        List<AtrConfQuotaHis> atrConfQuotaHisList = ClassUtil.convert(poList, AtrConfQuotaHis.class);
        atrConfQuotaHisList.forEach(atrConfQuotaHis -> {
            atrConfQuotaHis.setOperId(userId);
            atrConfQuotaHis.setOperTime(new Date());
            atrConfQuotaHis.setOperType(operType);
        });
        atrConfQuotaHisDao.saveList(atrConfQuotaHisList);
    }

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/17
     * @Description 保存明细轨迹信息
     * @Return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void saveQuotaDetailHis(AtrConfQuotaVo confQuotaVo,Integer serialNo, Long userId, String operType) {
        List<AtrConfQuotaDetail> quotaDetailList = atrConfQuotaDetailDao.findListByQuotaVo(confQuotaVo);
        quotaDetailList.forEach(atrConfQuotaDetail -> {
            if(CommonConstant.OperType.DELETE.equals(operType)) {
                atrConfQuotaDetailDao.deleteById(atrConfQuotaDetail.getQuotaDetailId());
            }
            atrConfQuotaDetail.setSerialNo(ObjectUtils.isEmpty(serialNo) ? 1 : serialNo +1);
            this.dealSaveDetailHis(atrConfQuotaDetail, userId, operType);
        });
    }

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/17
     * @Description 保存明细轨迹信息
     * @Return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void dealSaveDetailHis(AtrConfQuotaDetail po, Long userId, String operType) {
        AtrConfQuotaDetailHis atrConfQuotaDetailHis = new AtrConfQuotaDetailHis();
        ClassUtil.copyProperties(po, atrConfQuotaDetailHis);
        atrConfQuotaDetailHis.setOperId(userId);
        atrConfQuotaDetailHis.setOperTime(new Date());
        atrConfQuotaDetailHis.setOperType(operType);
        atrConfQuotaDetailHisDao.save(atrConfQuotaDetailHis);
    }


    @Override
    public AtrConfQuotaVo findAtrConfQuotaVo(AtrConfQuotaVo atrConfQuotaVo){
        AtrConfQuotaVo vo = atrConfQuotaDao.findAuditInformation(atrConfQuotaVo);
        return vo;
    }


    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/12/28 10:12
     * @Description 查找发展期Map
     */
    @Override
    public Map<String, Object> findPeriodMap(AtrConfQuotaVo atrConfQuotaVo){
        Map<String, Object> resultMap = new LinkedHashMap<>();
        Map<String, Object> dataMap = new LinkedHashMap<>();
        //DevelopPeriod 发展期
        atrConfQuotaVo.setQuotaType(ActuarialConstant.QuotaType.DEVELOP_PERIOD);
        List<AtrConfQuotaDefVo> atrConfQuotaDefVoList;
        List<AtrConfQuotaPeriodVo> period = null;
        List<AtrConfQuotaPeriodVo> periodTemp = null;

        // 新增
        if("add".equals(atrConfQuotaVo.getType())){
            periodTemp = atrConfQuotaDao.findConfQuotaPeriodByAdd();
        }else {
            periodTemp = atrConfQuotaDao.findConfQuotaPeriod(atrConfQuotaVo);
        }

        period = periodTemp;
        atrConfQuotaVo.setValidIs("1");
        atrConfQuotaVo.setAuditState(CommonConstant.AuditStatus.APPROVED);
        atrConfQuotaDefVoList = atrConfQuotaDefService.findDefListByPeriod(atrConfQuotaVo);
        atrConfQuotaDefVoList = atrConfQuotaDefVoList.stream().filter(quotDef -> !ActuarialConstant.QuotaGroup.EXCLUDE_GROUP.contains(quotDef.getQuotaGroup())).collect(Collectors.toList());
        for(AtrConfQuotaDefVo atrConfQuotaDefVo : atrConfQuotaDefVoList) {
            atrConfQuotaVo.setQuotaId(atrConfQuotaDefVo.getQuotaId());
            List<String> valueList = atrConfQuotaDao.findConfQuotaPeriodValue(atrConfQuotaVo);
            // 新增时
            if("add".equals(atrConfQuotaVo.getType())){
                valueList = new ArrayList<>(Arrays.asList(""));
            }
            if(null != valueList && valueList.size() > 0 && !"".equals(valueList.get(0))){
                // 非新增时，正常下有已录入发展期期次数据
                valueList = this.convertPerNumberToPeriodList(valueList, atrConfQuotaDefVo);
            }else {
                // 非新增时，中途修改指标因子为发展期情况
                valueList = atrConfQuotaDao.findConfQuotaPeriodValueByAdd();
            }
            atrConfQuotaDefVo.setValueList(valueList);
            dataMap.put(atrConfQuotaDefVo.getQuotaCode(), atrConfQuotaDefVo);
        }
        if(period.isEmpty()){
            AtrConfQuotaPeriodVo p = new AtrConfQuotaPeriodVo();
            p.setQuotaPeriod(new Long(0));
            period.add(p);
        }
        resultMap.put("data", dataMap);
        resultMap.put("period", period);
        return resultMap;
    }

    @Override
    public void addOrUpdate(AtrConfQuotaMainVo atrConfQuotaMainVo, Long userId){
        if (ObjectUtils.isNotEmpty(atrConfQuotaMainVo) && ObjectUtils.isNotEmpty(atrConfQuotaMainVo.getAtrConfQuotaVoList()))
        {
            // 百分比转换
            AtrConfQuotaMainVo confQuotaMainSaveVo = this.convertPercentagesToNumber(atrConfQuotaMainVo);
            saveAtrConfQuotaList(confQuotaMainSaveVo, userId);
        }
    }


    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/12/15
     * @Description 校验发展期明细数据2
     * @Return
     */
    private AtrConfQuotaResultVo checkQuotaDetail(List<AtrConfQuotaDetailVo> atrConfQuotaDetailVoList, Long quotaDefId) {
        AtrConfQuotaResultVo resultVo = new AtrConfQuotaResultVo();
        AtrConfQuotaDefVo atrConfQuotaDefVo = atrConfQuotaDefService.findByPk(quotaDefId);
        // 对比参照的总值
        BigDecimal baseSum = new BigDecimal(1);
        // 计算总值
        BigDecimal sumValue = new BigDecimal(0);
        String flag = "success";
        //String pattern = "/^(0(\\.\\d{1,2})?|1(\\.0{1,2})?)$/";
        String pattern = CommonConstant.PatternRule.NUMBER;

        for(AtrConfQuotaDetailVo detailVo : atrConfQuotaDetailVoList) {
            // ldq: 正则判断是否为数值字符串
            if(null !=  detailVo.getQuotaValue() && !"".equals(detailVo.getQuotaValue())
                && Pattern.matches(pattern, detailVo.getQuotaValue())){
                sumValue = sumValue.add(new BigDecimal(detailVo.getQuotaValue()));
            }
        }

        if(null != atrConfQuotaDefVo.getQuotaCode()
                && "1".equals(atrConfQuotaDefVo.getPercentHundredIs())){
            // compareTo等于0则相等，否则不相等
            if(sumValue.compareTo(baseSum) == 0){
                //resultVo.setFlag("success");
            }else {
                // 一般为统计总和不等于100%时
                resultVo = ClassUtil.convert(atrConfQuotaDefVo, AtrConfQuotaResultVo.class);
                //resultVo.setFlag("error_add_percent");
                flag = "error_add_percent";
            }
        }
        resultVo.setFlag(flag);
        return resultVo;
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void saveAtrConfQuotaList(AtrConfQuotaMainVo atrConfQuotaMainVo, Long userId) {
        List<AtrConfQuotaVo> atrConfQuotaVoList = atrConfQuotaMainVo.getAtrConfQuotaVoList();
        AtrConfQuota atrConfQuota = new AtrConfQuota();
        AtrConfQuotaVo atrConfQuotaVo;
        AtrConfQuotaDef atrConfQuotaDef;
        Date currentDate = new Date();
        String loaCode = atrConfQuotaMainVo.getLoaCode();
        Long entityId = atrConfQuotaMainVo.getEntityId();
        String businessSourceCode = atrConfQuotaMainVo.getBusinessSourceCode();
        String yearMonth = atrConfQuotaMainVo.getYearMonth();
        String dimension = atrConfQuotaMainVo.getDimension();
        String dimensionValue = atrConfQuotaMainVo.getDimensionValue();
        String quotaClass = atrConfQuotaMainVo.getQuotaClass();
        String riskClassCode = atrConfQuotaMainVo.getRiskClassCode();

        //默认状态
        List<AtrConfQuotaDef> defList = atrConfQuotaDefService.findList(new AtrConfQuotaDef());
        // 查找指标定义的所有code
        List<String> quotaCodeList = defList.stream().map(AtrConfQuotaDef :: getQuotaCode).collect(Collectors.toList());
        AtrConfQuotaVo confQuotaVo =  atrConfQuotaVoList.get(0);
        confQuotaVo.setEntityId(entityId);
        confQuotaVo.setBusinessSourceCode(businessSourceCode);
        confQuotaVo.setYearMonth(yearMonth);
        confQuotaVo.setDimension(dimension);
        confQuotaVo.setDimensionValue(dimensionValue);
        confQuotaVo.setQuotaClass(quotaClass);
        this.deleteQuotaByOtherModel(ClassUtil.convert(confQuotaVo, AtrConfQuota.class), userId);

        Integer serialNo =  atrConfQuotaDao.findMaxSerialNo(confQuotaVo);
        if(null != entityId && StringUtil.isNotEmpty(atrConfQuotaVoList)){
            for(AtrConfQuotaVo vo : atrConfQuotaVoList) {
                vo.setEntityId(entityId);
                vo.setBusinessSourceCode(businessSourceCode);
                vo.setYearMonth(yearMonth);
                vo.setDimension(dimension);
                vo.setDimensionValue(dimensionValue);
                vo.setQuotaClass(quotaClass);
                vo.setRiskClassCode(riskClassCode);
                // 1、新增
                if("add".equals(atrConfQuotaMainVo.getType()) && quotaCodeList.contains(vo.getQuotaCode())){
                    //vo.setEntityId(quotaDetailVo.getEntityId());
                    //atrConfQuotaDef = atrConfQuotaDefService.findByQuotaCode(vo.getQuotaCode());
                    atrConfQuotaDef = defList.stream().filter(quotaDef -> quotaDef.getQuotaCode().equals(vo.getQuotaCode())).findFirst().get();
                    vo.setQuotaDefId(atrConfQuotaDef.getQuotaDefId());
                    atrConfQuota = ClassUtil.convert(vo, AtrConfQuota.class);
                    atrConfQuota.setQuotaDefId(atrConfQuotaDef.getQuotaDefId());
                    atrConfQuota.setEntityId(entityId);
                    atrConfQuota.setLoaCode(loaCode);
                    confQuotaVo.setDimension(dimension);
                    confQuotaVo.setDimensionValue(dimensionValue);
                    atrConfQuota.setQuotaDefId(atrConfQuotaDef.getQuotaDefId());
                    atrConfQuota.setCreateTime(currentDate);
                    atrConfQuota.setCreatorId(userId);
                    atrConfQuota.setAuditState(CommonConstant.AuditStatus.TO_BE_AUDITED);
                    atrConfQuota.setValidIs("1");
                    atrConfQuota.setSerialNo(ObjectUtils.isEmpty(serialNo) ? 1 :  serialNo +1);
                    vo.setLoaCode(loaCode);
                    vo.setBusinessSourceCode(businessSourceCode);

                    atrConfQuotaDao.save(atrConfQuota);
                    this.dealSaveHis(atrConfQuota, userId, CommonConstant.OperType.ADD);
                    if(ObjectUtils.isNotEmpty(vo.getAtrConfQuotaDetailVoList())
                            && ActuarialConstant.QuotaType.DEVELOP_PERIOD.equals(vo.getQuotaType())) {
                        atrConfQuotaVo = ClassUtil.convert(atrConfQuota, AtrConfQuotaVo.class);
                        // 保存指标发展期数据
                        this.saveQuotaDetail(vo.getAtrConfQuotaDetailVoList(), atrConfQuotaVo, userId, currentDate);

                    }
                }
                // 2、修改
                if(!"add".equals(atrConfQuotaMainVo.getType()) && quotaCodeList.contains(vo.getQuotaCode())){
                    atrConfQuota = atrConfQuotaDao.findByUnique(vo);
                    if(null != atrConfQuota){
                        vo.setQuotaId(atrConfQuota.getQuotaId());
                        if(StringUtil.isNotEmpty(vo.getQuotaValue())){
                            atrConfQuota.setQuotaValue(vo.getQuotaValue());
                        }

                        atrConfQuota.setUpdateTime(currentDate);
                        atrConfQuota.setUpdatorId(userId);
                        atrConfQuota.setAuditState(CommonConstant.AuditStatus.TO_BE_AUDITED);
                        atrConfQuota.setLoaCode(loaCode);
                        atrConfQuota.setBusinessSourceCode(businessSourceCode);
                    }

                    vo.setAuditState(CommonConstant.AuditStatus.TO_BE_AUDITED);
                    vo.setUpdatorId(userId);
                    vo.setUpdateTime(currentDate);
                    vo.setSerialNo(ObjectUtils.isEmpty(serialNo) ? 1 : serialNo +1 );
                    if(null != atrConfQuota && null != atrConfQuota.getQuotaId()){
                        atrConfQuotaDao.updateValue(vo);
                        atrConfQuota = atrConfQuotaDao.findById(vo.getQuotaId());
                    }else {
                        // 指标定义新增指标时新增
                        atrConfQuota = ClassUtil.convert(vo, AtrConfQuota.class);
                        atrConfQuota.setCreatorId(userId);
                        atrConfQuota.setCreateTime(currentDate);
                        atrConfQuota.setValidIs(CommonConstant.ValidStatus.VALID);
                        atrConfQuotaDao.save(atrConfQuota);
                    }
                    this.dealSaveHis(atrConfQuota, userId, CommonConstant.OperType.MODIFY);

                    if(ObjectUtils.isNotEmpty(vo.getAtrConfQuotaDetailVoList())
                            && ActuarialConstant.QuotaType.DEVELOP_PERIOD.equals(vo.getQuotaType())) {
                        atrConfQuotaVo = ClassUtil.convert(atrConfQuota, AtrConfQuotaVo.class);
                        //atrConfQuota.setSerialNo(ObjectUtils.isEmpty(serialNo) ? 1 : serialNo +1 );
                        // 保存指标发展期数据
                        this.saveQuotaDetail(vo.getAtrConfQuotaDetailVoList(), atrConfQuotaVo, userId, currentDate);
                    }
                }
            }
        }
    }

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/12/30 10:19
     * @Description  保存发展期数据
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void saveQuotaDetail(List<AtrConfQuotaDetailVo> detailList, AtrConfQuotaVo atrConfQuotaVo, Long userId, Date currentDate){
        AtrConfQuotaDetail detailPo;
        AtrConfQuotaDefVo atrConfQuotaDefVo = atrConfQuotaDefService.findByPk(atrConfQuotaVo.getQuotaDefId());
        String quotaValue;
        BigDecimal percent = new BigDecimal(100);

        int i = 0;
        atrConfQuotaDetailDao.deleteByQuotaId(atrConfQuotaVo.getQuotaId());
        for(AtrConfQuotaDetailVo quotaDetailVo : detailList) {
            // 先删除后插入
            detailPo = ClassUtil.convert(quotaDetailVo, AtrConfQuotaDetail.class);
            detailPo.setQuotaPeriod(new Long(++i));
            detailPo.setQuotaId(atrConfQuotaVo.getQuotaId());
            detailPo.setCreateTime(currentDate);
            detailPo.setCreatorId(userId);
            detailPo.setSerialNo(atrConfQuotaVo.getSerialNo());
            if(ActuarialConstant.QuotaType.VALUE_TYPE_PERCENTAGES.equals(atrConfQuotaDefVo.getQuotaValueType())
                && StringUtil.isNotEmpty(detailPo.getQuotaValue())){
                quotaValue = new BigDecimal(detailPo.getQuotaValue().trim()).divide(percent).toString();
                detailPo.setQuotaValue(quotaValue);
            }
            atrConfQuotaDetailDao.save(detailPo);
            this.dealSaveDetailHis(detailPo, userId, CommonConstant.OperType.ADD);
        }
    }

    private void saveQuotaByNonPeriod(List<AtrConfQuota> quotaList, AtrConfQuotaVo atrConfQuotaVo, Long userId){
        Date currentDate = new Date();
        String[] classArr;
        for(AtrConfQuota quota : quotaList){
            quota.setEntityId(atrConfQuotaVo.getEntityId());
            quota.setBusinessSourceCode(atrConfQuotaVo.getBusinessSourceCode());
            quota.setValidIs("1");
            quota.setAuditState(CommonConstant.AuditStatus.TO_BE_AUDITED);
            quota.setQuotaValue("");
            quota.setCreatorId(userId);
            quota.setCreateTime(currentDate);
            // 风险大类

            atrConfQuotaDao.save(quota);
            this.dealSaveHis(quota, userId, CommonConstant.OperType.ADD);
        }
    }

    private void saveQuotaPeriodList(List<AtrConfQuota> quotaList, Long userId){
        Date currentDate = new Date();
        List<AtrConfQuotaDetail> detailList = new ArrayList<AtrConfQuotaDetail>();
        detailList.addAll(ClassUtil.convert(quotaList, AtrConfQuotaDetail.class));
        for(AtrConfQuotaDetail detail : detailList){
            detail.setQuotaValue("");
            detail.setQuotaPeriod(new Long(0));
            detail.setCreatorId(userId);
            detail.setCreateTime(currentDate);
            atrConfQuotaDetailDao.save(detail);
            this.dealSaveDetailHis(detail, userId, CommonConstant.OperType.ADD);
        }
    }

    private void saveQuotaPeriod(Long quotaId, List<AtrConfQuotaDetailVo> quotaDetailVoList) {
        if (ObjectUtils.isEmpty(quotaId)) {
            LOG.error("保存发展期数据失败：quotaId为空");
            throw new RuntimeException("保存发展期数据失败：quotaId为空");
        }
        if (CollectionUtils.isEmpty(quotaDetailVoList)) {
            LOG.info("没有发展期数据需要保存，quotaId: {}", quotaId);
            return;
        }
        LOG.info("开始保存发展期数据，quotaId: {}, 数据条数: {}", quotaId, quotaDetailVoList.size());
        try {
            // 转换为PO对象
            List<AtrConfQuotaDetail> detailList = ClassUtil.convert(quotaDetailVoList, AtrConfQuotaDetail.class);
            // 为每个明细设置quotaId
            for (AtrConfQuotaDetail detail : detailList) {
                detail.setQuotaId(quotaId);
                // 确保发展期不为空
                if (detail.getQuotaPeriod() == null) {
                    LOG.warn("发展期为空，设置为默认值1，quotaId: {}", quotaId);
                    detail.setQuotaPeriod(1L);
                }
                // 确保值不为空
                if (StringUtil.isEmpty(detail.getQuotaValue())) {
                    LOG.warn("发展期 {} 的值为空，设置为默认值'0'，quotaId: {}", detail.getQuotaPeriod(), quotaId);
                    detail.setQuotaValue("0");
                }
            }
            // 保存到明细表
            int savedCount = atrConfQuotaDetailDao.saveQuotaDetailList(quotaId, detailList);
            LOG.info("成功保存发展期数据 {} 条，quotaId: {}", savedCount, quotaId);
        } catch (Exception e) {
            LOG.error("保存发展期数据失败，quotaId: {}, 错误: {}", quotaId, e.getMessage(), e);
            throw new RuntimeException("保存发展期数据失败: " + e.getMessage());
        }
    }

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/12/28 10:12
     * @Description 查找指标假设设置Map
     */
    @Override
    public List<AtrConfQuotaGroupDefVo> findConfQuotaDef(AtrConfQuotaVo atrConfQuotaVo){
        List<AtrConfQuotaGroupDefVo> atrBussQuotaDefVoList = new ArrayList<>();
        AtrConfQuotaGroupDefVo atrBussQuotaDefVo = null;
        BbsConfLoaVo loaVo = ClassUtil.convert(atrConfQuotaVo, BbsConfLoaVo.class);
        // 根据业务线唯一约束（业务单位、业务模型、业务方向、loa代码）查找对象信息
        BbsConfLoaVo atrConfLoaVo = bmsConfCodeFeignClient.findLoaVo(loaVo);

        String type = atrConfQuotaVo.getType();
        atrConfQuotaVo.setType(null);
        atrConfQuotaVo.setValidIs(CommonConstant.ValidStatus.VALID);
        atrConfQuotaVo.setAuditState(CommonConstant.AuditStatus.APPROVED);
        atrConfQuotaVo.setQuotaType(ActuarialConstant.QuotaType.NON_DEVELOP_PERIOD);

        List<AtrConfQuotaDefVo> atrConfQuotaDefVoList = new ArrayList<>();
        // 新增时
        if("add".equals(type)){
            atrConfQuotaVo.setQuotaType(ActuarialConstant.QuotaType.NON_DEVELOP_PERIOD);
            atrConfQuotaDefVoList = atrConfQuotaDao.findQuotaByAdd(atrConfQuotaVo);
        }else {
            atrConfQuotaDefVoList = this.atrConfQuotaDao.findConfQuotaVo(atrConfQuotaVo);
        }

        if(null != atrConfQuotaDefVoList && atrConfQuotaDefVoList.size() > 0){
            //转换为百分比的显示
            atrConfQuotaDefVoList = this.convertNumberToPerList(atrConfQuotaDefVoList);
            if(null != atrConfLoaVo){
                for(AtrConfQuotaDefVo atrConfQuotaDefVo : atrConfQuotaDefVoList){
                    atrConfQuotaDefVo.setLoaCName(atrConfLoaVo.getLoaCName());
                    atrConfQuotaDefVo.setLoaLName(atrConfLoaVo.getLoaLName());
                    atrConfQuotaDefVo.setLoaEName(atrConfLoaVo.getLoaEName());
                }
            }
        }
        Map<String, List<AtrConfQuotaDefVo>> confQuotaDetailMap = atrConfQuotaDefVoList.stream()
                .collect(Collectors.groupingBy(AtrConfQuotaDefVo::getQuotaGroup,LinkedHashMap::new, Collectors.toList()));
        for (Map.Entry<String, List<AtrConfQuotaDefVo>> entry : confQuotaDetailMap.entrySet()){
            if (ActuarialConstant.QuotaGroup.EXCLUDE_GROUP.contains(entry.getKey())) {
                continue;
            }
            atrBussQuotaDefVo = new AtrConfQuotaGroupDefVo();
            atrBussQuotaDefVo.setQuotaGroup(entry.getKey());
            atrBussQuotaDefVo.setConfQuotaDefList(entry.getValue());
            atrBussQuotaDefVoList.add(atrBussQuotaDefVo);
        }
        return atrBussQuotaDefVoList;
    }

    @Override
    public Map<String,Object> findPrePeriod(AtrConfQuotaVo atrConfQuotaVo) {
        Map<String,Object> map = new HashMap<String, Object>();
        List<AtrConfQuotaGroupDefVo> atrBussQuotaDefVoList = new ArrayList<>();
        Map<String, Object> quotaDetailMap = new HashMap<>();
        String preYearMonth = atrConfQuotaDao.selectPreviousPeriod(ClassUtil.convert(atrConfQuotaVo, AtrConfQuota.class));
        if (ObjectUtils.isEmpty(preYearMonth)) {
            map.put("quota", atrBussQuotaDefVoList);
            map.put("quotaDetail", quotaDetailMap);
            return map;
        }

        //普通假设值上期数据
        AtrConfQuotaGroupDefVo atrBussQuotaDefVo ;
        atrConfQuotaVo.setValidIs(CommonConstant.ValidStatus.VALID);
        atrConfQuotaVo.setAuditState(CommonConstant.AuditStatus.APPROVED);
        atrConfQuotaVo.setQuotaType(ActuarialConstant.QuotaType.NON_DEVELOP_PERIOD);
        atrConfQuotaVo.setYearMonth(preYearMonth);
        List<AtrConfQuotaDefVo> atrConfQuotaDefVoList = new ArrayList<>();
        atrConfQuotaDefVoList = this.atrConfQuotaDao.findConfQuotaVo(atrConfQuotaVo);
        Map<String, List<AtrConfQuotaDefVo>> confQuotaDetailMap = atrConfQuotaDefVoList.stream()
                .collect(Collectors.groupingBy(AtrConfQuotaDefVo::getQuotaGroup,LinkedHashMap::new, Collectors.toList()));
        for (Map.Entry<String, List<AtrConfQuotaDefVo>> entry : confQuotaDetailMap.entrySet()){
            if (ActuarialConstant.QuotaGroup.EXCLUDE_GROUP.contains(entry.getKey())) {
                continue;
            }
            atrBussQuotaDefVo = new AtrConfQuotaGroupDefVo();
            atrBussQuotaDefVo.setQuotaGroup(entry.getKey());
            atrBussQuotaDefVo.setConfQuotaDefList(entry.getValue());
            atrBussQuotaDefVoList.add(atrBussQuotaDefVo);
        }

        //发展期假设值上期数据
        atrConfQuotaVo.setQuotaType(ActuarialConstant.QuotaType.DEVELOP_PERIOD);
        atrConfQuotaDefVoList = atrConfQuotaDefService.findDefListByPeriod(atrConfQuotaVo);
        List<AtrConfQuotaDetail> quotaDetailList = atrConfQuotaDetailDao.findConfQuotaPeriodValue(atrConfQuotaVo);
        atrConfQuotaDefVoList = atrConfQuotaDefVoList.stream().filter(quotDef -> !ActuarialConstant.QuotaGroup.EXCLUDE_GROUP.contains(quotDef.getQuotaGroup())).collect(Collectors.toList());
        List<AtrConfQuotaPeriodVo> periodTemp = atrConfQuotaDao.findConfQuotaPeriod(atrConfQuotaVo);
        for(AtrConfQuotaDefVo atrConfQuotaDefVo : atrConfQuotaDefVoList) {
            if(ObjectUtils.isNotEmpty(atrConfQuotaDefVo.getQuotaId())) {
                List<String> valueList = quotaDetailList.stream()
                        .filter(detail-> atrConfQuotaDefVo.getQuotaId().equals(detail.getQuotaId()))
                        .map(AtrConfQuotaDetail :: getQuotaValue).collect(Collectors.toList());
                if(null != valueList && valueList.size() > 0 && !"".equals(valueList.get(0))){
                    // 非新增时，正常下有已录入发展期期次数据
                    valueList = this.convertPerNumberToPeriodList(valueList, atrConfQuotaDefVo);
                }
                atrConfQuotaDefVo.setValueList(valueList);
            }
            quotaDetailMap.put(atrConfQuotaDefVo.getQuotaCode(), atrConfQuotaDefVo);
        }
        map.put("quota", atrBussQuotaDefVoList);
        map.put("quotaDetail", quotaDetailMap);
        map.put("quotaDetailPeriod", periodTemp);
        return map;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Map<String,Object> loadPrePeriodQuota(AtrConfQuotaVo atrConfQuotaVo) {
        Map<String,Object> map = new HashMap<String, Object>();
        String preYearMonth = atrConfQuotaDao.selectPreviousPeriod(ClassUtil.convert(atrConfQuotaVo, AtrConfQuota.class));
        if (ObjectUtils.isEmpty(preYearMonth)) {
            return map;
        }
        atrConfQuotaVo.setPerYearMonth(preYearMonth);
        atrConfQuotaDao.loadPrePeriodQuota(atrConfQuotaVo);
        atrConfQuotaDetailDao.loadPrePeriodQuotaDetail(atrConfQuotaVo);
        return map;
    }

    @Override
    public void syncQuotaClass(AtrConfQuotaDefVo confQuotaDefVo) {
        atrConfQuotaDao.syncQuotaClassByQuotaDef(confQuotaDefVo);
    }

    @Override
    public String findValidateCode(AtrConfQuotaVo atrConfQuotaVo){
        // validateFlag：0表示不存在相关数据，1表示存在。默认不存在
        String validateFlag = "0";
        if(ObjectUtils.isNotEmpty(atrConfQuotaVo)
                && (StringUtil.isNotEmpty(atrConfQuotaVo.getDimensionValue()) || "A".equals(atrConfQuotaVo.getDimension()))){
            // 查询存在有相同的业务主键，则返回存在相关数据
            List<AtrConfQuota> atrConfQuotas = atrConfQuotaDao.findListByVoAndType(atrConfQuotaVo);
            if(null != atrConfQuotas && atrConfQuotas.size() > 0){
                validateFlag = "1";
            }
        }
        return validateFlag;
    }


    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/12/16
     * @Description 数据保存前，百分比的数值转换为实际数值，但无需精算小数位
     */
    private AtrConfQuotaMainVo convertPercentagesToNumber(AtrConfQuotaMainVo atrConfQuotaMainVo){
        String quotaValue;
        AtrConfQuotaDef atrConfQuotaDef = new AtrConfQuotaDef();
        List<AtrConfQuotaVo> list = atrConfQuotaMainVo.getAtrConfQuotaVoList();
        List<AtrConfQuotaDetailVo> detailVoList;
        List<AtrConfQuotaDef> defList = atrConfQuotaDefService.findList(new AtrConfQuotaDef());
        // 查找指标定义的所有code
        List<String> quotaCodeList = defList.stream().map(AtrConfQuotaDef :: getQuotaCode).collect(Collectors.toList());
        BigDecimal percent = new BigDecimal(100);
        for(AtrConfQuotaVo atrConfQuotaVo : list){
            if(StringUtil.isNotEmpty(atrConfQuotaVo.getQuotaCode())
                    && quotaCodeList.contains(atrConfQuotaVo.getQuotaCode())){
                if(null == atrConfQuotaVo.getQuotaId()){
                    atrConfQuotaDef = atrConfQuotaDefService.findByQuotaCode(atrConfQuotaVo.getQuotaCode());
                    if(null != atrConfQuotaDef){
                        atrConfQuotaVo.setQuotaDefId(atrConfQuotaDef.getQuotaDefId());
                        atrConfQuotaVo.setQuotaType(atrConfQuotaDef.getQuotaType());
                    }
                }

                // 如果是百分比的话，值就乘以100
                // BigDecimal的加减乘除分别为add、subtract、multiply、divide
                if(null != atrConfQuotaDef
                        && null != atrConfQuotaDef.getQuotaValueType()
                        && null != atrConfQuotaDef.getQuotaType() ){

                    if(ActuarialConstant.QuotaType.VALUE_TYPE_PERCENTAGES.equals(atrConfQuotaDef.getQuotaValueType())
                            && null != atrConfQuotaVo.getQuotaValue() && !"".equals(atrConfQuotaVo.getQuotaValue())){
                        // BigDecimal t1 = percent.multiply(BigDecimal);
                        if(ActuarialConstant.QuotaType.DEVELOP_PERIOD.equals(atrConfQuotaDef.getQuotaType())){
                            // 发展期数据
                            detailVoList = atrConfQuotaVo.getAtrConfQuotaDetailVoList();
                            if(null != detailVoList && detailVoList.size() > 0){
                                for(AtrConfQuotaDetailVo detailVo : detailVoList){
                                    quotaValue = new BigDecimal(detailVo.getQuotaValue()).divide(percent).toString();
                                    detailVo.setQuotaValue(quotaValue);
                                }
                                atrConfQuotaVo.setAtrConfQuotaDetailVoList(detailVoList);
                            }
                        }else {
                            // 指标数据
                            quotaValue = new BigDecimal(atrConfQuotaVo.getQuotaValue()).divide(percent).toString();
                            atrConfQuotaVo.setQuotaValue(quotaValue);
                        }
                    }
                }
            }
        }
        atrConfQuotaMainVo.setAtrConfQuotaVoList(list);
        return atrConfQuotaMainVo;
    }


    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/12/16
     * @Description 显示保存前，百分比的实际数值转换为百分比数值, 指标设置部分
     */
    private List<AtrConfQuotaDefVo> convertNumberToPerList(List<AtrConfQuotaDefVo> quotaDefVoList) {
        BigDecimal percent = new BigDecimal(100);
        BbsConfRegularRuleVo atrConfRegularRuleVo = new BbsConfRegularRuleVo();
        String quotaValue = null;
        if(null != quotaDefVoList && quotaDefVoList.size() > 0){
            for(AtrConfQuotaDefVo atrConfQuotaDefVo : quotaDefVoList){
                if(null != atrConfQuotaDefVo && !"".equals(atrConfQuotaDefVo.getValue())){
                    // 百分比，也需保留两位小数
                    if(ActuarialConstant.QuotaType.VALUE_TYPE_PERCENTAGES.equals(atrConfQuotaDefVo.getQuotaValueType())
                        && StringUtil.isNotEmpty(atrConfQuotaDefVo.getValue())){
                        //multiply乘法
                        quotaValue = new BigDecimal(atrConfQuotaDefVo.getValue()).multiply(percent).stripTrailingZeros().toPlainString();
                    }else {
                        quotaValue = atrConfQuotaDefVo.getValue();
                        // 其他含两位小数的规则，需保留两位小数
                        if(null != atrConfQuotaDefVo.getRuleCode() && null != atrConfQuotaDefVo.getValue()){
                            if(CommonConstant.PatternRuleConf.KEEP_2_DECIMAL_HUNDRED.equals(atrConfQuotaDefVo.getRuleCode())
                                    || CommonConstant.PatternRuleConf.KEEP_2_DECIMAL_NUMBER.equals(atrConfQuotaDefVo.getRuleCode())){
                                quotaValue = DataUtil.convert2StringByStr(quotaValue);
                            }
                        }
                    }
                    atrConfQuotaDefVo.setValue(quotaValue);
                }
            }
        }
        return quotaDefVoList;
    }

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/12/17
     * @Description 显示保存前，百分比的实际数值转换为百分比数值, 发展期假设部分
     */
    private List<String> convertPerNumberToPeriodList(List<String> valueList, AtrConfQuotaDefVo atrConfQuotaDefVo) {
        List<String> resultList = new ArrayList<>();
        String quotaValueType = atrConfQuotaDefVo.getQuotaValueType();
        // quotaValueType为4百分比的，需要把数值乘以100。
        if(ActuarialConstant.QuotaType.VALUE_TYPE_PERCENTAGES.equals(atrConfQuotaDefVo.getQuotaValueType())){
            BigDecimal percent = new BigDecimal(100);
            String quotaValue;
            String pattern = CommonConstant.PatternRule.NUMBER;
            //  && Pattern.matches(pattern, quotaValue)
            for(int i=0; i<valueList.size(); i++){
                quotaValue = valueList.get(i);
                if(null != quotaValue && !"".equals(quotaValue)){
                    quotaValue = new BigDecimal(quotaValue).multiply(percent).stripTrailingZeros().toPlainString();
                    quotaValue = DataUtil.convert2String(quotaValue);
                }
                resultList.add(quotaValue);
            }
        } else {
            resultList = valueList;
        }
        return resultList;
    }

    @Override
    public Map<String, Object> findQuotaDefHeader(AtrConfQuotaDefVo confQuotaDefVo) {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        Map<String, Object> dataMap;
        Map<String, Object> groupMap = new LinkedHashMap<>();
        //假设分组码值
        List<ConfCode> bplCodeList = bmsConfCodeFeignClient.findCodeListByCodeType("QuotaGroup");
        if(StringUtil.isNotEmpty(bplCodeList)){
            for(ConfCode bplCodePo : bplCodeList) {
                if (ActuarialConstant.QuotaGroup.EXCLUDE_GROUP.contains(bplCodePo.getCodeCode())) {
                    continue;
                }
                dataMap = new LinkedHashMap<>();
                AtrConfQuotaDef atrConfQuotaDef = new AtrConfQuotaDef();
                atrConfQuotaDef.setQuotaGroup(bplCodePo.getCodeCode());
                atrConfQuotaDef.setValidIs("1");
                atrConfQuotaDef.setAuditState(CommonConstant.AuditStatus.APPROVED);
                atrConfQuotaDef.setBusinessSourceCode(confQuotaDefVo.getBusinessSourceCode());
                atrConfQuotaDef.setDimension(confQuotaDefVo.getDimension());
                atrConfQuotaDef.setQuotaClass(confQuotaDefVo.getQuotaClass());
                List<AtrConfQuotaDef> quotaDefList = atrConfQuotaDefDao.findByPo(atrConfQuotaDef);

                if(ObjectUtils.isEmpty(quotaDefList)) {
                    continue;
                }
                List<AtrConfQuotaDef> quotaDefListSort =  quotaDefList.stream().sorted(Comparator.comparing(AtrConfQuotaDef::getDisplayNo)).collect(Collectors.toList());
                for(AtrConfQuotaDef def : quotaDefListSort) {
                    dataMap.put(def.getQuotaCode(), def);
                }
                AtrConfQuotaDefVo groupQuotaDef = new AtrConfQuotaDefVo();
                groupQuotaDef.setQuotaCode(bplCodePo.getCodeCode());
                groupQuotaDef.setQuotaEName(bplCodePo.getCodeEName());
                groupQuotaDef.setQuotaCName(bplCodePo.getCodeCName());
                groupQuotaDef.setQuotaLName(bplCodePo.getCodeLName());
                groupQuotaDef.setChildQuota((LinkedHashMap<String, Object>) dataMap);
                groupMap.put(groupQuotaDef.getQuotaCode(), groupQuotaDef);
            }
        }

        resultMap.put("data", groupMap);
        return resultMap;
    }

    @Override
    public Map<String, Object> findQuotaDetailByVo(AtrConfQuotaVo atrConfQuotaVo){
        Map<String, Object> resultMap = new LinkedHashMap<>();
        List<AtrConfQuotaDetailVo> periodList = atrConfQuotaDao.findQuotaDetailByVo(atrConfQuotaVo);
        AtrConfQuotaDef atrConfQuotaDef = new AtrConfQuotaDef();
        if(null != atrConfQuotaVo && StringUtil.isNotEmpty(atrConfQuotaVo.getQuotaCode())){
            atrConfQuotaDef = atrConfQuotaDefService.findByQuotaCode(atrConfQuotaVo.getQuotaCode());
        }

        BigDecimal percent = new BigDecimal(100);
        String quotaValue;
        // 乘法multiply
        for(AtrConfQuotaDetailVo vo : periodList) {
            if(null != atrConfQuotaDef && ActuarialConstant.QuotaType.VALUE_TYPE_PERCENTAGES.equals(atrConfQuotaDef.getQuotaValueType())){
                quotaValue = new BigDecimal(vo.getQuotaValue()).multiply(percent).stripTrailingZeros().toPlainString();
                quotaValue = DataUtil.convert2String(quotaValue);
                vo.setQuotaValue(quotaValue);
            }
            resultMap.put(vo.getQuotaPeriod() + "", vo);

        }
        return resultMap;
    }

    @Override
    public List<AtrConfQuotaGroupDefVo> findConfQuotaHisDef(AtrConfQuotaVo atrConfQuotaVo) {
        List<AtrConfQuotaGroupDefVo> atrBussQuotaDefVoList = new ArrayList<>();
        AtrConfQuotaGroupDefVo atrBussQuotaDefVo = null;

        BbsConfLoaVo loaVo = ClassUtil.convert(atrConfQuotaVo, BbsConfLoaVo.class);
        // 根据业务线唯一约束（业务单位、业务模型、业务方向、loa代码）查找对象信息
        BbsConfLoaVo atrConfLoaVo = bmsConfCodeFeignClient.findLoaVo(loaVo);

        atrConfQuotaVo.setValidIs("1");
        atrConfQuotaVo.setAuditState(CommonConstant.AuditStatus.APPROVED);
        atrConfQuotaVo.setQuotaType(ActuarialConstant.QuotaType.NON_DEVELOP_PERIOD);

        List<AtrConfQuotaDefVo> atrConfQuotaDefVoList = new ArrayList<>();

        // 新增时
        atrConfQuotaDefVoList = atrConfQuotaHisDao.findConfQuotaHisVo(atrConfQuotaVo);

        if(null != atrConfQuotaDefVoList && atrConfQuotaDefVoList.size() > 0){
            //转换为百分比的显示
            atrConfQuotaDefVoList = this.convertNumberToPerList(atrConfQuotaDefVoList);
            if(null != atrConfLoaVo){
                for(AtrConfQuotaDefVo atrConfQuotaDefVo : atrConfQuotaDefVoList){
                    atrConfQuotaDefVo.setLoaCName(atrConfLoaVo.getLoaCName());
                    atrConfQuotaDefVo.setLoaLName(atrConfLoaVo.getLoaLName());
                    atrConfQuotaDefVo.setLoaEName(atrConfLoaVo.getLoaEName());
                }
            }
        }
        Map<String, List<AtrConfQuotaDefVo>> confQuotaDetailMap = atrConfQuotaDefVoList.stream()
                .collect(Collectors.groupingBy(AtrConfQuotaDefVo::getQuotaGroup,LinkedHashMap::new, Collectors.toList()));
        for (Map.Entry<String, List<AtrConfQuotaDefVo>> entry : confQuotaDetailMap.entrySet()){
            atrBussQuotaDefVo = new AtrConfQuotaGroupDefVo();
            atrBussQuotaDefVo.setQuotaGroup(entry.getKey());
            atrBussQuotaDefVo.setConfQuotaDefList(entry.getValue());
            atrBussQuotaDefVoList.add(atrBussQuotaDefVo);
        }
        return atrBussQuotaDefVoList;
    }

    @Override
    public Map<String, Object> findPeriodMapHis(AtrConfQuotaVo atrConfQuotaVo) {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        Map<String, Object> dataMap = new LinkedHashMap<>();
        //DevelopPeriod 发展期downloadTemplate
        atrConfQuotaVo.setQuotaType(ActuarialConstant.QuotaType.DEVELOP_PERIOD);
        List<AtrConfQuotaDefVo> atrConfQuotaDefVoList;
        List<AtrConfQuotaPeriodVo> period = null;
        List<AtrConfQuotaPeriodVo> periodTemp = null;
        periodTemp = atrConfQuotaHisDao.findConfQuotaPeriodRisk(atrConfQuotaVo);
        period = periodTemp;

        atrConfQuotaVo.setValidIs("1");
        atrConfQuotaVo.setAuditState(CommonConstant.AuditStatus.APPROVED);
        atrConfQuotaDefVoList= atrConfQuotaHisDao.findDefListByPeriod(atrConfQuotaVo);

        for(AtrConfQuotaDefVo atrConfQuotaDefVo : atrConfQuotaDefVoList) {
            atrConfQuotaVo.setQuotaId(atrConfQuotaDefVo.getQuotaId());
            List<String> valueList = atrConfQuotaHisDao.findConfQuotaPeriodValue(atrConfQuotaVo);
            if(null != valueList && valueList.size() > 0 && !"".equals(valueList.get(0))){
                // 非新增时，正常下有已录入发展期期次数据
                valueList = this.convertPerNumberToPeriodList(valueList, atrConfQuotaDefVo);
            }
            atrConfQuotaDefVo.setValueList(valueList);
            dataMap.put(atrConfQuotaDefVo.getQuotaCode(), atrConfQuotaDefVo);
        }
        if(period.isEmpty()){
            AtrConfQuotaPeriodVo p = new AtrConfQuotaPeriodVo();
            p.setQuotaPeriod(new Long(0));
            period.add(p);
        }
        resultMap.put("data", dataMap);
        resultMap.put("period", period);
        return resultMap;
    }

    @Override
    public List<AtrConfQuotaImportVo> excelImport(MultipartFile file, AtrConfQuotaImportVo atrConfQuotaVo, Long userId) throws Exception {
        List<AtrConfQuotaImportVo> importCheckDataResultList=  this.checkImportData(file, atrConfQuotaVo);
        if (ObjectUtils.isNotEmpty(importCheckDataResultList)) {
            return importCheckDataResultList;
        }
        List<AtrConfQuotaImportVo> confQuotaImportVoList = atrConfQuotaDao.findImportQuotaList(atrConfQuotaVo);
        Map<String,Long> map = confQuotaImportVoList.stream().collect(Collectors.toMap(AtrConfQuotaImportVo::getQuotaCode, AtrConfQuotaImportVo::getQuotaDefId));
        List<AtrConfQuota> atrConfQuotaList = new ArrayList<>();
        AtrConfQuota atrConfQuota = new AtrConfQuota();
        atrConfQuota.setEntityId(atrConfQuotaVo.getEntityId());
        atrConfQuota.setBusinessSourceCode(atrConfQuotaVo.getBusinessSourceCode());
        atrConfQuota.setDimension(atrConfQuotaVo.getDimension());
        atrConfQuota.setValidIs(CommonConstant.ValidStatus.VALID);
        atrConfQuota.setAuditState(CommonConstant.AuditStatus.TO_BE_AUDITED);
        List<AtrConfQuotaImportVo> list= ExcelExportUtil.read(file.getInputStream(), AtrConfQuotaImportVo.class);
        list.forEach( importVo -> {
            atrConfQuota.setDimensionValue(importVo.getDimensionValue());
            atrConfQuota.setQuotaDefId(map.get(importVo.getQuotaCode()));
            atrConfQuota.setQuotaValue(importVo.getQuotaValue());
            System.out.println(atrConfQuota.toString());
            atrConfQuotaDao.save(atrConfQuota);
            this.dealSaveHis(atrConfQuota, userId, CommonConstant.OperType.ADD);
        });
        atrImportService.importFile(file,atrConfQuotaVo.getTargetRouter(),userId);
        return null;
    }

    public List<AtrConfQuotaImportVo> checkImportData(MultipartFile file, AtrConfQuotaImportVo atrConfQuotaVo) throws Exception {
        List<AtrConfQuotaImportVo> confQuotaImportVoList = atrConfQuotaDao.findImportQuotaList(atrConfQuotaVo);
        int quotaDefCount = confQuotaImportVoList.size();
        AtrConfQuota atrConfQuota = new AtrConfQuota();
        atrConfQuota.setEntityId(atrConfQuotaVo.getEntityId());
        atrConfQuota.setBusinessSourceCode(atrConfQuotaVo.getBusinessSourceCode());
        atrConfQuota.setDimension(atrConfQuotaVo.getDimension());
        List<AtrConfQuotaImportVo> list = ExcelExportUtil.read(file.getInputStream(), AtrConfQuotaImportVo.class);
        Map<String, List<AtrConfQuotaImportVo>> granularityMap = list.stream().collect(Collectors.groupingBy(AtrConfQuotaImportVo::getDimensionValue, HashMap::new, Collectors.toList()));
        //记录问题数据的颗粒度值和问题类型
        List<AtrConfQuotaImportVo> importCheckDataResultList = new ArrayList<>();
        for (Map.Entry<String, List<AtrConfQuotaImportVo>> entry : granularityMap.entrySet()){
            atrConfQuota.setDimensionValue(entry.getKey());
            if(atrConfQuotaDao.hasSameQuota(atrConfQuota)) {
                AtrConfQuotaImportVo importVo = new AtrConfQuotaImportVo();
                importVo.setDimensionValue(entry.getKey());
                importVo.setMsgType("1");
                importCheckDataResultList.add(importVo);
                continue;
            }
            long count = entry.getValue().stream().map(AtrConfQuotaImportVo :: getQuotaCode).distinct().count();
            if(quotaDefCount != count ) {
                AtrConfQuotaImportVo importVo = new AtrConfQuotaImportVo();
                importVo.setDimensionValue(entry.getKey());
                importVo.setMsgType("2");
                importCheckDataResultList.add(importVo);
                continue;
            } else if (entry.getValue().size() != count){
                AtrConfQuotaImportVo importVo = new AtrConfQuotaImportVo();
                importVo.setDimensionValue(entry.getKey());
                importVo.setMsgType("2");
                importCheckDataResultList.add(importVo);
                continue;
            }
        }
        return importCheckDataResultList;
    }


    @Override
    public void generateTemplate(HttpServletRequest request, HttpServletResponse response, AtrConfQuotaImportVo importVo) throws IOException {
        //获取文件的相应保存路径
        AtrConfCodeVo atrCodeVo = new AtrConfCodeVo();
        atrCodeVo.setCodeCodeIdx("QuotaDimension/Base");
        atrCodeVo.setCodeCode(importVo.getDimension());
        AtrConfCode atrConfCode = atrConfCodeService.findByCodeAndIdx(atrCodeVo);
        if (ObjectUtils.isEmpty(atrConfCode)) {
            return;
        }
        String prefixPath = appConfig.getBasePath();
        String uploadBasePath = appConfig.getUploadBasePath();
        String fileTemplate = appConfig.getFileTemplate();
        String filePath = prefixPath + uploadBasePath + fileTemplate;
        List<AtrExcelCellVo> excelCells = this.generateQuotMainCell(request, atrConfCode, importVo);
        List<AtrExcelCellVo> quotaDetailExcelCells = this.generateQuotaDetailCell(request,  atrConfCode, importVo);
        List<List<AtrExcelCellVo>> atrExcelCellVoLists = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(excelCells)) {
            atrExcelCellVoLists.add(excelCells);
        }
        if (ObjectUtils.isNotEmpty(quotaDetailExcelCells)) {
            atrExcelCellVoLists.add(quotaDetailExcelCells);
        }

        AtrExcelGenerateUtils.generateExcelWithHeader(response, atrExcelCellVoLists, atrConfCode.getCodeCName(),
                        filePath);
    }

    @Override
    public Map<String, Object> executeUpload(HttpServletRequest request, MultipartFile file, AtrConfQuotaImportVo importVo, Long userId) throws Exception {
        Map<String, Object> dealMap = null;
        InputStream fs = file.getInputStream();

        importVo.setQuotaType("0");
        List<AtrExcelParseVo> parseList =  this.generateExcelParseVo(request, importVo);
        //解析excel
        List<List<AtrExcelParseVo>> lists = ExcelImportUtils.parseExcel(fs, parseList, 0);
        if (CollectionUtils.isEmpty(lists)) {
            throw new NoSuchElementException("Cannot upload an empty data template Sheet1!");
        }
        importVo.setQuotaType("1");
        fs = file.getInputStream();
        List<AtrExcelParseVo> detailParseList = this.generateExcelParseVo(request, importVo);
        List<List<AtrExcelParseVo>> detailLists = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(detailParseList)) {
            //解析excel
            detailLists = ExcelImportUtils.parseExcel(fs, detailParseList, 1);
            if (CollectionUtils.isEmpty(detailLists)) {
                throw new NoSuchElementException("Cannot upload an empty data template Sheet2!");
            }
        }
        try {
            this.saveExcelData(lists, importVo, userId);
            this.saveExcelDetailData(detailLists, importVo, userId);
        } catch (Exception ex) {
            LOG.error("Exception : ", ex);
            throw new IllegalWriteException("Illegal character input ");
        }
        return dealMap;
    }

    @Override
    @Async("ruleThreadPool")
    public List<AtrConfQuotaVo> quotaIcgImport(MultipartFile file, AtrConfQuotaImportVo importVo) throws Exception {
        // 验证文件
        if (file == null || file.isEmpty()) {
            LOG.error("上传的文件为空");
            throw new Exception("上传的文件为空");
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null || !fileName.endsWith(".xlsx")) {
            LOG.error("文件格式不正确，只支持.xlsx格式");
            throw new Exception("文件格式不正确，只支持.xlsx格式");
        }

        LOG.info("开始按列位置处理Excel文件: {}", fileName);
        // 验证基本参数
        if (importVo == null) {
            LOG.error("导入参数为空");
            throw new Exception("导入参数为空");
        }

        if (importVo.getEntityId() == null) {
            LOG.error("业务单位ID不能为空");
            throw new Exception("业务单位ID不能为空");
        }
        List<AtrConfQuotaVo> atrConfQuotaVoList = atrConfQuotaImportUtil.quotaIcgImport(file, importVo);

        List<AtrConfQuotaVo> atrConfQuotaVos = atrConfQuotaVoList.stream()
                .filter(distinctByKey(p -> Arrays.asList(
                        p.getEntityId(),
                        p.getYearMonth(),
                        p.getBusinessSourceCode(),
                        p.getDimension(),
                        p.getDimensionValue(),
                        p.getRiskClassCode(),
                        p.getQuotaDefId()
                )))
                .collect(Collectors.toList());

        try {
            LOG.info("成功按列位置读取Excel文件，第一个工作表(非发展期)数据行数: {}, 第二个工作表(发展期)数据行数: {}",
                    atrConfQuotaVos.size(),
                    atrConfQuotaVos.size());
            
            // 保存数据
            if (CollectionUtils.isNotEmpty(atrConfQuotaVos)) {
                LOG.info("开始保存解析的数据，共 {} 条记录", atrConfQuotaVos.size());
                this.deleteByQuota(atrConfQuotaVos);
                for (AtrConfQuotaVo quotaVo : atrConfQuotaVos) {
                    // 保存主表数据
                    AtrConfQuota atrConfQuota = ClassUtil.convert(quotaVo, AtrConfQuota.class);
                    atrConfQuotaDao.save(atrConfQuota);
                    // 保存明细表数据
                    if (CollectionUtils.isNotEmpty(quotaVo.getAtrConfQuotaDetailVoList())) {
                        saveQuotaPeriod(atrConfQuota.getQuotaId(), quotaVo.getAtrConfQuotaDetailVoList());
                    }
                }
                LOG.info("数据保存完成");
            } else {
                LOG.warn("没有有效数据需要保存");
            }
        } catch (Exception e) {
            LOG.error("按列位置处理Excel文件时发生错误: {}", e.getMessage(), e);
            throw new Exception("按列位置处理Excel文件时发生错误: " + e.getMessage());
        }
        return atrConfQuotaVos;
    }

    // 自定义去重过滤函数
    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }


    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deleteByQuota(List<AtrConfQuotaVo> atrConfQuotaVos){
        List<AtrConfQuotaFilterVo> groupedByRiskClass = atrConfQuotaVos.stream()
                .filter(person ->
                        person.getEntityId() != null && person.getYearMonth() != null &&  person.getDimension() != null
                                &&  person.getDimensionValue() != null &&  person.getRiskClassCode() != null
                )
                .map(p -> new AtrConfQuotaFilterVo(p.getEntityId(), p.getYearMonth(), p.getDimension(), p.getDimensionValue(),p.getRiskClassCode()))
                .collect(Collectors.toCollection(LinkedHashSet::new)) // 保持插入顺序
                .stream()
                .collect(Collectors.toList());
        groupedByRiskClass.parallelStream().forEach(key -> {
            AtrConfQuota atrConfQuota = ClassUtil.convert(key, AtrConfQuota.class);
            atrConfQuotaDetailDao.deleteByDimensionVo(atrConfQuota);
            atrConfQuotaDao.deleteByDimensionVo(atrConfQuota);
        });

        List<AtrConfQuotaFilterVo> groupedByNoRiskClass = atrConfQuotaVos.stream()
                .filter(person ->
                        person.getEntityId() != null && person.getYearMonth() != null &&  person.getDimension() != null
                                &&  person.getDimensionValue() != null &&  person.getRiskClassCode() == null
                )
                .map(p -> new AtrConfQuotaFilterVo(p.getEntityId(), p.getYearMonth(), p.getDimension(), p.getDimensionValue()))
                .collect(Collectors.toCollection(LinkedHashSet::new)) // 保持插入顺序
                .stream()
                .collect(Collectors.toList());
        groupedByNoRiskClass.parallelStream().forEach(key -> {
            AtrConfQuota atrConfQuota = ClassUtil.convert(key, AtrConfQuota.class);
            atrConfQuotaDetailDao.deleteByDimensionVo(atrConfQuota);
            atrConfQuotaDao.deleteByDimensionVo(atrConfQuota);
        });
    }


    @Override
    public void downloadIcgTemplate(HttpServletRequest request, HttpServletResponse response, AtrConfQuotaImportVo confQuotaVo) throws Exception {
        List<AtrDapIcgRiskClassVo> atrDapTreatyVos =  atrDapTreatyService.findTreatyIcgList(confQuotaVo);

        List<AtrDapIcgRiskClassVo> ddIcgList = atrDapTreatyService.findDDIcgList(confQuotaVo.getEntityId());
        List<AtrDapIcgRiskClassVo> foIcgList = atrDapTreatyService.findFOIcgList(confQuotaVo.getEntityId());
        List<AtrDapIcgRiskClassVo> tiIcgList = atrDapTreatyVos.stream().filter(vo -> "I".equals(vo.getRiDirectionCode())).collect(Collectors.toList());
        List<AtrDapIcgRiskClassVo> toIcgList = atrDapTreatyVos.stream().filter(vo -> "O".equals(vo.getRiDirectionCode())).collect(Collectors.toList());
        foIcgList.addAll(toIcgList);


        List<ExcelSheet> sheetList  = new ArrayList<>();
        List<ExcelSheetData> sheetDataList1 = new ArrayList<>();
        ExcelSheetData excelSheetData = new ExcelSheetData("DD", ddIcgList);
        sheetDataList1.add(excelSheetData);
        ExcelSheet sheet  = new ExcelSheet(1, sheetDataList1, false);
        sheetList.add(sheet);

        List<ExcelSheetData> sheetDataList2 = new ArrayList<>();
        ExcelSheetData excelSheetData2 = new ExcelSheetData("TI", tiIcgList);
        sheetDataList2.add(excelSheetData2);
        ExcelSheet sheet2  = new ExcelSheet(2, sheetDataList2, false);
        sheetList.add(sheet2);

        List<ExcelSheetData> sheetDataList3 = new ArrayList<>();
        ExcelSheetData excelSheetData3 = new ExcelSheetData("FO", foIcgList);
        sheetDataList3.add(excelSheetData3);
        ExcelSheet sheet3  = new ExcelSheet(3, sheetDataList3, false);
        sheetList.add(sheet3);

        List<ExcelSheetData> sheetDataList4 = new ArrayList<>();
        ExcelSheetData excelSheetData4 = new ExcelSheetData("FO", foIcgList);
        sheetDataList4.add(excelSheetData4);
        ExcelSheet sheet4  = new ExcelSheet(4, sheetDataList4, false);
        sheetList.add(sheet4);

        //未到期直保&临分分入发展期
        List<ExcelSheetData> sheetDataList5 = new ArrayList<>();
        ExcelSheetData excelSheetData5 = new ExcelSheetData("DD", ddIcgList);
        sheetDataList5.add(excelSheetData5);
        ExcelSheet sheet5  = new ExcelSheet(5, sheetDataList5, false);
        sheetList.add(sheet5);

        //未决直保&临分分入发展期
        List<ExcelSheetData> sheetDataList6 = new ArrayList<>();
        ExcelSheetData excelSheetData6 = new ExcelSheetData("DD", ddIcgList);
        sheetDataList6.add(excelSheetData6);
        ExcelSheet sheet6  = new ExcelSheet(6, sheetDataList6, false);
        sheetList.add(sheet6);

        //未决直保&临分分入发展期
        List<ExcelSheetData> sheetDataList7 = new ArrayList<>();
        ExcelSheetData excelSheetData7 = new ExcelSheetData("TI", tiIcgList);
        sheetDataList7.add(excelSheetData7);
        ExcelSheet sheet7  = new ExcelSheet(7, sheetDataList7, false);
        sheetList.add(sheet7);

        //未决直保&临分分入发展期
        List<ExcelSheetData> sheetDataList8 = new ArrayList<>();
        ExcelSheetData excelSheetData8 = new ExcelSheetData("TI", tiIcgList);
        sheetDataList8.add(excelSheetData8);
        ExcelSheet sheet8  = new ExcelSheet(8, sheetDataList8, false);
        sheetList.add(sheet8);

        //未决直保&临分分入发展期
        List<ExcelSheetData> sheetDataList9 = new ArrayList<>();
        ExcelSheetData excelSheetData9 = new ExcelSheetData("FO", foIcgList);
        sheetDataList9.add(excelSheetData9);
        ExcelSheet sheet9  = new ExcelSheet(9, sheetDataList9, false);
        sheetList.add(sheet9);

        atrExportService.exportExcelSheetList(request, response, sheetList, confQuotaVo.getTemplateFileName(), "",  confQuotaVo.getTargetRouter(), confQuotaVo.getCreatorId());
    }

    @Override
    public void downloadQuotaExcel(HttpServletRequest request, HttpServletResponse response, AtrConfQuotaImportVo qtcConfQuotaVo) throws Exception {

        List<AtrConfQuotaVo> atrConfQuotaVos = atrConfQuotaDao.findAtrConfQuotaList(qtcConfQuotaVo);
        List<AtrConfQuotaVo> atrConfQuotaDetailVos = atrConfQuotaDao.findAtrConfQuotaDetailList(qtcConfQuotaVo);

        List<AtrConfQuotaVo> ddList = atrConfQuotaVos.stream().filter(vo-> "DD".equals(vo.getBusinessSourceCode())).collect(Collectors.toList());
        // 分组实现
        Map<QuotaValueKey, List<AtrConfQuotaVo>> groupedOrders = ddList.stream()
                .collect(Collectors.groupingBy(
                        order -> new QuotaValueKey(order.getYearMonth(),order.getBusinessSourceCode(),order.getDimensionValue(),order.getRiskClassCode())
                ));

        List<HashMap<String, String>>  ddIcgList = new ArrayList<>();
        groupedOrders.entrySet().stream()
                .forEach(entry -> {
                    HashMap<String, String> dapIcgRiskClassVo = new HashMap<>(16);
                    dapIcgRiskClassVo.put("yearMonth", entry.getKey().getYearMonth());
                    dapIcgRiskClassVo.put("dimensionValue", entry.getKey().getDimensionValue());
                    dapIcgRiskClassVo.put("riskClassCode", entry.getKey().getRiskClassCode());
                    entry.getValue().forEach(vo->{
                        dapIcgRiskClassVo.put(vo.getQuotaCode(), vo.getQuotaValue());
                    });
                    ddIcgList.add(dapIcgRiskClassVo);
                });
        List<ExcelSheet> sheetList  = new ArrayList<>();
        List<ExcelSheetData> sheetDataList1 = new ArrayList<>();
        ExcelSheetData excelSheetData = new ExcelSheetData("DD", ddIcgList);
        sheetDataList1.add(excelSheetData);
        ExcelSheet sheet  = new ExcelSheet(1, sheetDataList1, false);
        sheetList.add(sheet);


        List<AtrConfQuotaVo> tiList = atrConfQuotaVos.stream().filter(vo-> "TI".equals(vo.getBusinessSourceCode())).collect(Collectors.toList());
        // 分组实现
        Map<QuotaValueKey, List<AtrConfQuotaVo>> groupedTIs = tiList.stream()
                .collect(Collectors.groupingBy(
                        order -> new QuotaValueKey(order.getYearMonth(),order.getBusinessSourceCode(),order.getDimensionValue(),order.getRiskClassCode())
                ));
        List<HashMap<String, String>> tiIcgList = new ArrayList();
        //List<AtrDapIcgRiskClassVo> tiIcgList = new ArrayList();
        groupedTIs.entrySet().stream()
                .forEach(entry -> {
                    HashMap<String, String> dapIcgRiskClassVo = new HashMap<>(16);
                    dapIcgRiskClassVo.put("yearMonth", entry.getKey().getYearMonth());
                    dapIcgRiskClassVo.put("dimensionValue", entry.getKey().getDimensionValue());
                    dapIcgRiskClassVo.put("riskClassCode", entry.getKey().getRiskClassCode());
                    entry.getValue().forEach(vo->{
                        dapIcgRiskClassVo.put(vo.getQuotaCode(), vo.getQuotaValue());
                    });
                    tiIcgList.add(dapIcgRiskClassVo);
                });
        List<ExcelSheetData> sheetDataList2 = new ArrayList<>();
        ExcelSheetData excelSheetData2 = new ExcelSheetData("TI", tiIcgList);
        sheetDataList2.add(excelSheetData2);
        ExcelSheet sheet2  = new ExcelSheet(2, sheetDataList2, false);
        sheetList.add(sheet2);


        List<AtrConfQuotaVo> foList = atrConfQuotaVos.stream().filter(vo-> "FO".equals(vo.getBusinessSourceCode()) || "TO".equals(vo.getBusinessSourceCode())).collect(Collectors.toList());
        // 分组实现
        Map<QuotaValueKey, List<AtrConfQuotaVo>> groupedFOs = foList.stream()
                .collect(Collectors.groupingBy(
                        order -> new QuotaValueKey(order.getYearMonth(),order.getBusinessSourceCode(),order.getDimensionValue(),order.getRiskClassCode())
                ));


        List<HashMap<String, String>> foIcgRaList = new ArrayList();
        List<HashMap<String, String>> foIcgRdList = new ArrayList();
        //List<AtrDapIcgRiskClassVo> foIcgRaList = new ArrayList();
        //List<AtrDapIcgRiskClassVo> foIcgRdList = new ArrayList();
        groupedFOs.entrySet().stream()
                .forEach(entry -> {
                    HashMap<String, String> dapIcgRiskClassRaVo = new HashMap<>();
                    HashMap<String, String> dapIcgRiskClassRdVo = new HashMap<>();
                    dapIcgRiskClassRaVo.put("yearMonth", entry.getKey().getYearMonth());
                    dapIcgRiskClassRaVo.put("businessSourceCode", entry.getKey().getBusinessSourceCode());
                    dapIcgRiskClassRaVo.put("dimensionValue", entry.getKey().getDimensionValue());
                    dapIcgRiskClassRaVo.put("riskClassCode", entry.getKey().getRiskClassCode());
                    dapIcgRiskClassRdVo.put("yearMonth", entry.getKey().getYearMonth());
                    dapIcgRiskClassRdVo.put("businessSourceCode", entry.getKey().getBusinessSourceCode());
                    dapIcgRiskClassRdVo.put("dimensionValue", entry.getKey().getDimensionValue());
                    dapIcgRiskClassRdVo.put("riskClassCode", entry.getKey().getRiskClassCode());

                    entry.getValue().forEach(vo->{
                        if ("lic_ra_ratio".equals(vo.getQuotaCode())) {
                            dapIcgRiskClassRaVo.put(vo.getQuotaCode(), vo.getQuotaValue());
                            foIcgRaList.add(dapIcgRiskClassRaVo);
                        }
                        if ("lic_rd_ratio".equals(vo.getQuotaCode())) {
                            dapIcgRiskClassRdVo.put(vo.getQuotaCode(), vo.getQuotaValue());
                            foIcgRdList.add(dapIcgRiskClassRdVo);
                        }
                    });
                });
        List<ExcelSheetData> sheetDataList3 = new ArrayList<>();
        ExcelSheetData excelSheetData3 = new ExcelSheetData("FO", foIcgRaList);
        sheetDataList3.add(excelSheetData3);
        ExcelSheet sheet3  = new ExcelSheet(3, sheetDataList3, false);
        sheetList.add(sheet3);

        List<ExcelSheetData> sheetDataList4 = new ArrayList<>();
        ExcelSheetData excelSheetData4 = new ExcelSheetData("FO", foIcgRdList);
        sheetDataList4.add(excelSheetData4);
        ExcelSheet sheet4  = new ExcelSheet(4, sheetDataList4, false);
        sheetList.add(sheet4);

        List<AtrConfQuotaVo> ddDevList = atrConfQuotaDetailVos.stream()
                .filter(vo-> "DD".equals(vo.getBusinessSourceCode()))
                .filter(vo-> "#lrc_claim_settled_pattern".equals(vo.getQuotaCode()))
                .collect(Collectors.toList());
        // 分组实现
        Map<QuotaValueKey, List<AtrConfQuotaVo>> groupDev = ddDevList.stream()
                .collect(Collectors.groupingBy(
                        order -> new QuotaValueKey(order.getYearMonth(),order.getBusinessSourceCode(),order.getDimensionValue(),order.getRiskClassCode())
                ));

        List<HashMap<String, String>> ddIcgLrcPatternList = new ArrayList();
        groupDev.entrySet().stream()
                .forEach(entry -> {
                    HashMap<String, String> dapDevIcgRiskClassVo = new HashMap<>(16);
                    dapDevIcgRiskClassVo.put("yearMonth", entry.getKey().getYearMonth());
                    dapDevIcgRiskClassVo.put("dimensionValue", entry.getKey().getDimensionValue());
                    dapDevIcgRiskClassVo.put("riskClassCode", entry.getKey().getRiskClassCode());
                    entry.getValue().forEach(vo->{
                        dapDevIcgRiskClassVo.put("quotaCode", vo.getQuotaCode());
                        dapDevIcgRiskClassVo.put("quotaName", vo.getQuotaCName());
                        dapDevIcgRiskClassVo.put(vo.getQuotaPeriod(), vo.getQuotaValue());
                    });
                    ddIcgLrcPatternList.add(dapDevIcgRiskClassVo);
                });
        //未到期直保&临分分入发展期
        List<ExcelSheetData> sheetDataList5 = new ArrayList<>();
        ExcelSheetData excelSheetData5 = new ExcelSheetData("DD", ddIcgLrcPatternList);
        sheetDataList5.add(excelSheetData5);
        ExcelSheet sheet5  = new ExcelSheet(5, sheetDataList5, false);
        sheetList.add(sheet5);


        List<AtrConfQuotaVo> ddDevLicList = atrConfQuotaDetailVos.stream()
                .filter(vo-> "DD".equals(vo.getBusinessSourceCode()))
                .filter(vo-> "#lic_claim_settled_pattern".equals(vo.getQuotaCode()))
                .collect(Collectors.toList());
        // 分组实现
        Map<QuotaValueKey, List<AtrConfQuotaVo>> groupDevLic = ddDevLicList.stream()
                .collect(Collectors.groupingBy(
                        order -> new QuotaValueKey(order.getYearMonth(),order.getBusinessSourceCode(),order.getDimensionValue(),order.getRiskClassCode())
                ));
        List<HashMap<String, String>> ddIcgLicPatternList = new ArrayList();
        groupDevLic.entrySet().stream()
                .forEach(entry -> {
                    HashMap<String, String> dapDevIcgRiskClassVo = new HashMap<>(16);
                    dapDevIcgRiskClassVo.put("yearMonth", entry.getKey().getYearMonth());
                    dapDevIcgRiskClassVo.put("dimensionValue", entry.getKey().getDimensionValue());
                    dapDevIcgRiskClassVo.put("riskClassCode", entry.getKey().getRiskClassCode());
                    entry.getValue().forEach(vo->{
                        dapDevIcgRiskClassVo.put("quotaCode", vo.getQuotaCode());
                        dapDevIcgRiskClassVo.put("quotaName", vo.getQuotaCName());
                        dapDevIcgRiskClassVo.put(vo.getQuotaPeriod(), vo.getQuotaValue());
                    });
                    ddIcgLicPatternList.add(dapDevIcgRiskClassVo);
                });
        //未决直保&临分分入发展期
        List<ExcelSheetData> sheetDataList6 = new ArrayList<>();
        ExcelSheetData excelSheetData6 = new ExcelSheetData("DD", ddIcgLicPatternList);
        sheetDataList6.add(excelSheetData6);
        ExcelSheet sheet6  = new ExcelSheet(6, sheetDataList6, false);
        sheetList.add(sheet6);



        List<AtrConfQuotaVo> tiDevLrcList = atrConfQuotaDetailVos.stream()
                .filter(vo-> "TI".equals(vo.getBusinessSourceCode()))
                .filter(vo-> "#lrc_claim_settled_pattern".equals(vo.getQuotaCode()))
                .collect(Collectors.toList());
        // 分组实现
        Map<QuotaValueKey, List<AtrConfQuotaVo>> groupLrcDev = tiDevLrcList.stream()
                .collect(Collectors.groupingBy(
                        order -> new QuotaValueKey(order.getYearMonth(),order.getBusinessSourceCode(),order.getDimensionValue(),order.getRiskClassCode())
                ));

        List<HashMap<String, String>> tiIcgLrcPatternList = new ArrayList();
        groupLrcDev.entrySet().stream()
                .forEach(entry -> {
                    HashMap<String, String> dapDevIcgRiskClassVo = new HashMap<>(16);
                    dapDevIcgRiskClassVo.put("yearMonth", entry.getKey().getYearMonth());
                    dapDevIcgRiskClassVo.put("dimensionValue", entry.getKey().getDimensionValue());
                    dapDevIcgRiskClassVo.put("riskClassCode", entry.getKey().getRiskClassCode());
                    entry.getValue().forEach(vo->{
                        dapDevIcgRiskClassVo.put("quotaCode", vo.getQuotaCode());
                        dapDevIcgRiskClassVo.put("quotaName", vo.getQuotaCName());
                        dapDevIcgRiskClassVo.put(vo.getQuotaPeriod(), vo.getQuotaValue());
                    });
                    tiIcgLrcPatternList.add(dapDevIcgRiskClassVo);
                });
        //未决直保&临分分入发展期
        List<ExcelSheetData> sheetDataList7 = new ArrayList<>();
        ExcelSheetData excelSheetData7 = new ExcelSheetData("TI", tiIcgLrcPatternList);
        sheetDataList7.add(excelSheetData7);
        ExcelSheet sheet7  = new ExcelSheet(7, sheetDataList7, false);
        sheetList.add(sheet7);


        List<AtrConfQuotaVo> tiDevLicList = atrConfQuotaDetailVos.stream()
                .filter(vo-> "TI".equals(vo.getBusinessSourceCode()))
                .filter(vo-> "#lic_claim_settled_pattern".equals(vo.getQuotaCode()))
                .collect(Collectors.toList());
        // 分组实现
        Map<QuotaValueKey, List<AtrConfQuotaVo>> groupLicDev = tiDevLicList.stream()
                .collect(Collectors.groupingBy(
                        order -> new QuotaValueKey(order.getYearMonth(),order.getBusinessSourceCode(),order.getDimensionValue(),order.getRiskClassCode())
                ));

        List<HashMap<String, String>> tiIcgLicPatternList = new ArrayList();
        groupLicDev.entrySet().stream()
                .forEach(entry -> {
                    HashMap<String, String> dapDevIcgRiskClassVo = new HashMap<>(16);
                    dapDevIcgRiskClassVo.put("yearMonth", entry.getKey().getYearMonth());
                    dapDevIcgRiskClassVo.put("dimensionValue", entry.getKey().getDimensionValue());
                    dapDevIcgRiskClassVo.put("riskClassCode", entry.getKey().getRiskClassCode());
                    entry.getValue().forEach(vo->{
                        dapDevIcgRiskClassVo.put("quotaCode", vo.getQuotaCode());
                        dapDevIcgRiskClassVo.put("quotaName", vo.getQuotaCName());
                        dapDevIcgRiskClassVo.put(vo.getQuotaPeriod(), vo.getQuotaValue());
                    });
                    tiIcgLicPatternList.add(dapDevIcgRiskClassVo);
                });
        //未决直保&临分分入发展期
        List<ExcelSheetData> sheetDataList8 = new ArrayList<>();
        ExcelSheetData excelSheetData8 = new ExcelSheetData("TI", tiIcgLicPatternList);
        sheetDataList8.add(excelSheetData8);
        ExcelSheet sheet8  = new ExcelSheet(8, sheetDataList8, false);
        sheetList.add(sheet8);


        List<AtrConfQuotaVo> foDevLicList = atrConfQuotaDetailVos.stream()
                .filter(vo-> "FO".equals(vo.getBusinessSourceCode()) || "TO".equals(vo.getBusinessSourceCode()))
                .filter(vo-> "#lic_claim_settled_pattern".equals(vo.getQuotaCode()))
                .collect(Collectors.toList());
        // 分组实现
        Map<QuotaValueKey, List<AtrConfQuotaVo>> foGroupLicDev = foDevLicList.stream()
                .collect(Collectors.groupingBy(
                        order -> new QuotaValueKey(order.getYearMonth(),order.getBusinessSourceCode(),order.getDimensionValue(),order.getRiskClassCode())
                ));

        List<HashMap<String, String>> foIcgLicPatternList = new ArrayList();
        foGroupLicDev.entrySet().stream()
                .forEach(entry -> {
                    HashMap<String, String> dapDevIcgRiskClassVo = new HashMap<>(16);
                    dapDevIcgRiskClassVo.put("yearMonth", entry.getKey().getYearMonth());
                    dapDevIcgRiskClassVo.put("businessSourceCode", entry.getKey().getBusinessSourceCode());
                    dapDevIcgRiskClassVo.put("dimensionValue", entry.getKey().getDimensionValue());
                    dapDevIcgRiskClassVo.put("riskClassCode", entry.getKey().getRiskClassCode());
                    entry.getValue().forEach(vo->{
                        dapDevIcgRiskClassVo.put("quotaCode", vo.getQuotaCode());
                        dapDevIcgRiskClassVo.put("quotaName", vo.getQuotaCName());
                        dapDevIcgRiskClassVo.put(vo.getQuotaPeriod(), vo.getQuotaValue());
                    });
                    foIcgLicPatternList.add(dapDevIcgRiskClassVo);
                });
        //未决直保&临分分入发展期
        List<ExcelSheetData> sheetDataList9 = new ArrayList<>();
        ExcelSheetData excelSheetData9 = new ExcelSheetData("FO", foIcgLicPatternList);
        sheetDataList9.add(excelSheetData9);
        ExcelSheet sheet9  = new ExcelSheet(9, sheetDataList9, false);
        sheetList.add(sheet9);

        atrExportService.exportExcelSheetList(request, response, sheetList, qtcConfQuotaVo.getTemplateFileName(), "",  qtcConfQuotaVo.getTargetRouter(), qtcConfQuotaVo.getCreatorId());


    }

    public List<AtrExcelCellVo> generateQuotMainCell(HttpServletRequest request, AtrConfCode atrConfCode, AtrConfQuotaImportVo importVo) throws IOException {
        importVo.setQuotaType("0");
        List<AtrConfQuotaDefVo> atrConfQuotaDefVoList = atrConfQuotaDao.findConfQuotaDefList(importVo);
        if (ObjectUtils.isEmpty(atrConfQuotaDefVoList)) {
            return new ArrayList<>();
        }
        //获取语言
        String language = request.getHeader("ss-Language");
        if (StringUtil.isEmpty(language)) {
            language = "en";
        }

        List<AtrConfQuotaDefVo> newConfQuotaDefVoList = new ArrayList();
        AtrConfQuotaDefVo atrConfQuotaDefVo = new AtrConfQuotaDefVo();
        atrConfQuotaDefVo.setQuotaCode(atrConfCode.getCodeCode());
        atrConfQuotaDefVo.setQuotaCName(atrConfCode.getCodeCName());
        atrConfQuotaDefVo.setQuotaEName(atrConfCode.getCodeEName());
        atrConfQuotaDefVo.setQuotaLName(atrConfCode.getCodeLName());
        atrConfQuotaDefVo.setQuotaValueType("1");
        newConfQuotaDefVoList.add(atrConfQuotaDefVo);
        newConfQuotaDefVoList.addAll(atrConfQuotaDefVoList);
        List<AtrExcelCellVo> excelCells = atrConfExcelService.generateCells(newConfQuotaDefVoList, language);
        return excelCells;
    }

    public List<AtrExcelCellVo> generateQuotaDetailCell(HttpServletRequest request, AtrConfCode atrConfCode, AtrConfQuotaImportVo importVo) throws IOException {
        importVo.setQuotaType("1");
        List<AtrConfQuotaDefVo> atrConfQuotaDefVoList = atrConfQuotaDao.findConfQuotaDefList(importVo);
        if (ObjectUtils.isEmpty(atrConfQuotaDefVoList)) {
            return new ArrayList<>();
        }
        //获取语言
        String language = request.getHeader("ss-Language");
        if (StringUtil.isEmpty(language)) {
            language = "en";
        }

        List<AtrConfQuotaDefVo> newConfQuotaDefVoList = new ArrayList();
        AtrConfQuotaDefVo atrConfQuotaDefVo = new AtrConfQuotaDefVo();
        atrConfQuotaDefVo.setQuotaCode(atrConfCode.getCodeCode());
        atrConfQuotaDefVo.setQuotaCName(atrConfCode.getCodeCName());
        atrConfQuotaDefVo.setQuotaEName(atrConfCode.getCodeEName());
        atrConfQuotaDefVo.setQuotaLName(atrConfCode.getCodeLName());
        atrConfQuotaDefVo.setQuotaValueType("1");
        newConfQuotaDefVoList.add(atrConfQuotaDefVo);
        AtrConfQuotaDefVo developPeriodDefVo = new AtrConfQuotaDefVo();
        developPeriodDefVo.setQuotaCode("period");
        developPeriodDefVo.setQuotaValueType("2");
        newConfQuotaDefVoList.add(developPeriodDefVo);
        newConfQuotaDefVoList.addAll(atrConfQuotaDefVoList);
        //获取文件的相应保存路径
        List<AtrExcelCellVo> excelCells = atrConfExcelService.generateCells(newConfQuotaDefVoList, language);
        return excelCells;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void saveExcelData(List<List<AtrExcelParseVo>> lists, AtrConfQuotaImportVo importVo, Long userId) throws Exception{
        if (CollectionUtils.isNotEmpty(lists)) {
            importVo.setQuotaType("0");
            List<AtrConfQuotaImportVo> confQuotaImportVoList = atrConfQuotaDao.findImportQuotaList(importVo);
            Map<String, Long> quotaDefIdMap = confQuotaImportVoList.stream().collect(Collectors.toMap(AtrConfQuotaImportVo::getQuotaCode, AtrConfQuotaImportVo::getQuotaDefId));
            Map<String, String> quotaValueTypeMap = confQuotaImportVoList.stream().collect(Collectors.toMap(AtrConfQuotaImportVo::getQuotaCode, AtrConfQuotaImportVo::getQuotaValueType));
            BigDecimal percent = new BigDecimal(100);
            List<AtrConfQuotaImportVo> list = new ArrayList<>();
            AtrConfQuotaImportVo excelVo;
            for(List<AtrExcelParseVo> atrExcelParseVos : lists) {
                AtrExcelParseVo granularityExcelParseVo = atrExcelParseVos.stream().findFirst().orElse(null);
                for (int i = 1; i < atrExcelParseVos.size(); i++) {
                    if (ObjectUtils.isEmpty(quotaDefIdMap.get(atrExcelParseVos.get(i).getColCode().split("-")[0]))) {
                        throw new ParseException("Granularity : " + granularityExcelParseVo.getColValue().toString() + ", column : " + atrExcelParseVos.get(i).getColCode() + " Can't be mismatch");
                    }
                    excelVo = new AtrConfQuotaImportVo();
                    excelVo.setDimensionValue(granularityExcelParseVo.getColValue().toString());
                    excelVo.setQuotaDefId(quotaDefIdMap.get(atrExcelParseVos.get(i).getColCode().split("-")[0]));
                    excelVo.setQuotaValue(ActuarialConstant.QuotaType.VALUE_TYPE_PERCENTAGES.equals(quotaValueTypeMap.get(atrExcelParseVos.get(i).getColCode().split("-")[0])) ? new BigDecimal(atrExcelParseVos.get(i).getColValue().toString()).divide(percent).toString() : atrExcelParseVos.get(i).getColValue().toString());
                    list.add(excelVo);
                }
            }

            int spitLen = 500;
            int queueSize = list.size() <= spitLen ? 1 : (int) Math.ceil(list.size() / spitLen) + 1;
            List handleList = null;
            for (int i = 0; i < queueSize; i++) {
                if ((i + 1) == queueSize) {
                    int startIndex = i * spitLen;
                    int endIndex = list.size();
                    handleList = list.subList(startIndex, endIndex);
                } else {
                    int startIndex = i * spitLen;
                    int endIndex = (i + 1) * spitLen;
                    handleList = list.subList(startIndex, endIndex);
                }
                saveExcelQuotaData(importVo, handleList, userId);
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void saveExcelDetailData(List<List<AtrExcelParseVo>> lists, AtrConfQuotaImportVo importVo, Long userId) throws Exception{
        if (CollectionUtils.isNotEmpty(lists)) {
            importVo.setQuotaType("1");
            List<AtrConfQuotaImportVo> confQuotaImportVoList = atrConfQuotaDao.findImportQuotaList(importVo);
            Map<String, Long> quotaDefIdMap = confQuotaImportVoList.stream().collect(Collectors.toMap(AtrConfQuotaImportVo::getQuotaCode, AtrConfQuotaImportVo::getQuotaDefId));
            Map<String, String> quotaValueTypeMap = confQuotaImportVoList.stream().collect(Collectors.toMap(AtrConfQuotaImportVo::getQuotaCode, AtrConfQuotaImportVo::getQuotaValueType));
            BigDecimal percent = new BigDecimal(100);
            List<AtrConfQuotaDetailImportVo> list = new ArrayList<>();
            AtrConfQuotaDetailImportVo excelVo;
            for(List<AtrExcelParseVo> atrExcelParseVos : lists) {
                AtrExcelParseVo granularityExcelParseVo = atrExcelParseVos.stream().findFirst().orElse(null);
                AtrExcelParseVo periodExcelParseVo = atrExcelParseVos.stream().skip(1).findFirst().orElse(null);
                for (int i = 2; i < atrExcelParseVos.size(); i++) {
                    if (ObjectUtils.isEmpty(quotaDefIdMap.get(atrExcelParseVos.get(i).getColCode().split("-")[0]))) {
                        throw new ParseException("Granularity : " + granularityExcelParseVo.getColValue().toString() + ", column : " + atrExcelParseVos.get(i).getColCode() + " Can't be mismatch");
                    }
                    excelVo = new AtrConfQuotaDetailImportVo();
                    excelVo.setDimensionValue(granularityExcelParseVo.getColValue().toString());
                    excelVo.setQuotaDefId(quotaDefIdMap.get(atrExcelParseVos.get(i).getColCode().split("-")[0]));
                    excelVo.setQuotaPeriod(Double.valueOf(periodExcelParseVo.getColValue().toString()).longValue());
                    excelVo.setQuotaValue(ActuarialConstant.QuotaType.VALUE_TYPE_PERCENTAGES.equals(quotaValueTypeMap.get(atrExcelParseVos.get(i).getColCode().split("-")[0])) ? new BigDecimal(atrExcelParseVos.get(i).getColValue().toString()).divide(percent).toString() : atrExcelParseVos.get(i).getColValue().toString());
                    list.add(excelVo);
                }
            }

            int spitLen = 500;
            int queueSize = list.size() <= spitLen ? 1 : (int) Math.ceil(list.size() / spitLen) + 1;
            List handleList = null;
            for (int i = 0; i < queueSize; i++) {
                if ((i + 1) == queueSize) {
                    int startIndex = i * spitLen;
                    int endIndex = list.size();
                    handleList = list.subList(startIndex, endIndex);
                } else {
                    int startIndex = i * spitLen;
                    int endIndex = (i + 1) * spitLen;
                    handleList = list.subList(startIndex, endIndex);
                }
                saveExcelQuotaDetailData(importVo, handleList, userId);
            }
        }
    }


    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void saveExcelQuotaDetailData(AtrConfQuotaImportVo vo, List<AtrConfQuotaDetailImportVo> list, Long userId) {
        AtrConfQuota atrConfQuota = new AtrConfQuota();
        atrConfQuota.setEntityId(vo.getEntityId());
        atrConfQuota.setYearMonth(vo.getYearMonth());
        atrConfQuota.setBusinessSourceCode(vo.getBusinessSourceCode());
        atrConfQuota.setQuotaClass(vo.getQuotaClass());
        atrConfQuota.setDimension(vo.getDimension());
        atrConfQuota.setValidIs(CommonConstant.ValidStatus.VALID);
        atrConfQuota.setAuditState(CommonConstant.AuditStatus.APPROVED);
        atrConfQuota.setSerialNo(1);
        atrConfQuota.setCreatorId(userId);
        list.stream().map(AtrConfQuotaDetailImportVo:: getDimensionValue).distinct().forEach(dimensionValue -> {
            atrConfQuota.setDimensionValue(dimensionValue);
            List<AtrConfQuota> quotaList = atrConfQuotaDao.findListByDimensionVo(atrConfQuota);
            if(!quotaList.isEmpty()){
                AtrConfQuotaDetailImportVo atrConfQuotaImportVo = list.stream().filter(detailVo -> dimensionValue.equals(detailVo.getDimensionValue())).findFirst().get();
                atrConfQuota.setDimensionValue(atrConfQuotaImportVo.getDimensionValue());
                atrConfQuota.setQuotaDefId(atrConfQuotaImportVo.getQuotaDefId());
                atrConfQuotaDao.save(atrConfQuota);
                this.dealSaveHis(atrConfQuota, userId, CommonConstant.OperType.ADD);
                list.stream().filter(detailVo -> dimensionValue.equals(detailVo.getDimensionValue())).forEach(detailImportVo -> {
                    AtrConfQuotaDetail atrConfQuotaDetail = new AtrConfQuotaDetail();
                    atrConfQuotaDetail.setQuotaId(atrConfQuota.getQuotaId());
                    atrConfQuotaDetail.setQuotaPeriod(detailImportVo.getQuotaPeriod());
                    atrConfQuotaDetail.setQuotaValue(detailImportVo.getQuotaValue());
                    atrConfQuotaDetailDao.save(atrConfQuotaDetail);
                });
            }
        });
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void saveExcelQuotaData(AtrConfQuotaImportVo vo, List<AtrConfQuotaImportVo> list, Long userId) {
        AtrConfQuota atrConfQuota = new AtrConfQuota();
        atrConfQuota.setEntityId(vo.getEntityId());
        atrConfQuota.setQuotaClass(vo.getQuotaClass());
        atrConfQuota.setBusinessSourceCode(vo.getBusinessSourceCode());
        atrConfQuota.setDimension(vo.getDimension());
        atrConfQuota.setYearMonth(vo.getYearMonth());
        atrConfQuota.setValidIs(CommonConstant.ValidStatus.VALID);
        atrConfQuota.setAuditState(CommonConstant.AuditStatus.APPROVED);
        atrConfQuota.setSerialNo(1);
        atrConfQuota.setCreatorId(userId);

        list.stream().map(AtrConfQuotaImportVo:: getDimensionValue).distinct().forEach(dimensionValue -> {
            atrConfQuota.setDimensionValue(dimensionValue);
            this.deleteByQuota(atrConfQuota, userId);
        });
        list.forEach( importVo -> {
            atrConfQuota.setDimensionValue(importVo.getDimensionValue());
            atrConfQuota.setQuotaDefId(importVo.getQuotaDefId());
            atrConfQuota.setQuotaValue(importVo.getQuotaValue());
            atrConfQuotaDao.save(atrConfQuota);
            this.dealSaveHis(atrConfQuota, userId, CommonConstant.OperType.ADD);
        });
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void deleteByQuota(AtrConfQuota atrConfQuota, Long userId){
        List<AtrConfQuota> list = atrConfQuotaDao.findListByDimensionVo(atrConfQuota);
        if(!list.isEmpty()){
            atrConfQuotaDetailHisDao.saveDetailListByQuota(atrConfQuota, userId, CommonConstant.OperType.DELETE, false);
            atrConfQuotaHisDao.saveListByQuota(atrConfQuota, userId, CommonConstant.OperType.DELETE, false);
            atrConfQuotaDetailDao.deleteByDimensionVo(atrConfQuota);
            atrConfQuotaDao.deleteByDimensionVo(atrConfQuota);
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void deleteByQuotaLoa(AtrConfQuota atrConfQuota, Long userId){
        List<AtrConfQuota> list = atrConfQuotaDao.selectByLoaVo(atrConfQuota);
        if(!list.isEmpty()){
            atrConfQuotaDetailHisDao.saveDetailListByQuota(atrConfQuota, userId, CommonConstant.OperType.DELETE, true);
            atrConfQuotaHisDao.saveListByQuota(atrConfQuota, userId, CommonConstant.OperType.DELETE, true);
            atrConfQuotaDetailDao.deleteByLoaDimension(atrConfQuota);
            atrConfQuotaDao.deleteByLoaDimension(atrConfQuota);
        }
    }

    /*
    * 增加假设值：删除不属于当前模型的源假设数据
    * */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void deleteQuotaByOtherModel(AtrConfQuota atrConfQuota, Long userId){
        atrConfQuotaDetailDao.deleteByOtherModel(atrConfQuota);
        atrConfQuotaDao.deleteByOtherModel(atrConfQuota);
    }

    //计量数据单元格组装
    public List<AtrExcelParseVo> generateExcelParseVo(HttpServletRequest request, AtrConfQuotaImportVo importVo) {
        List<AtrExcelParseVo> parseList = new ArrayList<>();
        List<AtrConfQuotaDefVo> atrConfQuotaDefVoList = atrConfQuotaDao.findConfQuotaDefList(importVo);
        if (ObjectUtils.isNotEmpty(atrConfQuotaDefVoList)) {
            AtrConfCodeVo atrCodeVo = new AtrConfCodeVo();
            atrCodeVo.setCodeCodeIdx("QuotaDimension/Base");
            atrCodeVo.setCodeCode(importVo.getDimension());
            AtrConfCode atrConfCode = atrConfCodeService.findByCodeAndIdx(atrCodeVo);
            //获取语言
            String language = request.getHeader("ss-Language");
            if (StringUtil.isEmpty(language)) {
                language = "en";
            }
            //填充[业务号、发展期]
            parseList.add(new AtrExcelParseVo(atrConfCode.getCodeCode() + '-' + LanguageUtil.getLocalesName(language, atrConfCode.getCodeEName(),
                    atrConfCode.getCodeCName(), atrConfCode.getCodeLName()), ExcelConstant.ExcelCellType.STRING, "64", "1"));
            if ("1".equals(importVo.getQuotaType())) {
                parseList.add(new AtrExcelParseVo("period", "NUMBER" , "11", "1"));
            }
            String colType;
            String colLength = null;
            for (AtrConfQuotaDefVo atrConfQuotaDefVo : atrConfQuotaDefVoList) {
                switch (atrConfQuotaDefVo.getQuotaValueType()) {
                    case "3":
                        colType = ExcelConstant.ExcelCellType.DATE;
                        break;
                    case "2":
                        colType = "NUMBER";
                        colLength = "16,2";
                        break;
                    case "4":
                        colType = "NUMBER";
                        colLength = "9,6";
                        break;
                    case "1":
                    default:
                        colType = ExcelConstant.ExcelCellType.STRING;
                        colLength = "64";
                        break;
                }
                parseList.add(new AtrExcelParseVo(atrConfQuotaDefVo.getQuotaCode() + '-' + LanguageUtil.getLocalesName(language, atrConfQuotaDefVo.getQuotaEName(),
                        atrConfQuotaDefVo.getQuotaCName(), atrConfQuotaDefVo.getQuotaLName()), colType, colLength, "1"));
            }
        }
        return parseList;
    }


    public Boolean verifyQuotaVo(AtrConfQuotaVo atrConfQuotaVo) {
        if(ObjectUtils.isEmpty(atrConfQuotaVo) || ObjectUtils.isEmpty(atrConfQuotaVo.getEntityId()) || ObjectUtils.isEmpty(atrConfQuotaVo.getBusinessSourceCode()) || ObjectUtils.isEmpty(atrConfQuotaVo.getDimensionValue())) {
            return false;
        }
        return true;
    }



    // 定义复合键类（必须重写 equals/hashCode）
    class QuotaValueKey {
        private String yearMonth;
        private String businessSourceCode;
        private String dimensionValue;
        private String riskClassCode;

        public String getYearMonth() {
            return yearMonth;
        }
        public String getBusinessSourceCode() {
            return businessSourceCode;
        }
        public String getDimensionValue() {
            return dimensionValue;
        }
        String getRiskClassCode() {
            return riskClassCode;
        }
        public QuotaValueKey(String yearMonth, String businessSourceCode, String dimensionValue, String riskClassCode) {
            this.yearMonth = yearMonth;
            this.businessSourceCode = businessSourceCode;
            this.dimensionValue = dimensionValue;
            this.riskClassCode = riskClassCode;
        }
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            QuotaValueKey orderKey = (QuotaValueKey) o;
            return Objects.equals(yearMonth, orderKey.yearMonth) &&
                    Objects.equals(businessSourceCode, orderKey.businessSourceCode)&&
                    Objects.equals(dimensionValue, orderKey.dimensionValue)&&
                    Objects.equals(riskClassCode, orderKey.riskClassCode);
        }
        @Override
        public int hashCode() {
            return Objects.hash(yearMonth, businessSourceCode, dimensionValue, riskClassCode);
        }
    }
}
