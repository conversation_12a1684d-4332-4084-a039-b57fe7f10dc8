
    //重测结果允许弹窗
    var fields={//结果列表配置，一个对象一列
        actionNO:{
            prop: 'actionNo', //属性
            labelKey: 'ctcScheduleTaskCode',
            width: '160px',
        },
        entityId:{
            prop: 'entityCode', //属性
            labelKey: 'gBplCenterCode',
        },
        policyNo:{
            prop: 'policyNo', //属性
            labelKey: 'gDmPolicyNo',
            width:"160px",
            showOverflowTooltip: true,
        },
        endorseSeqNo:{
            prop: 'endorseSeqNo', //属性
            labelKey: 'gDmEndorseSeqNo',
        },
        atrCenter1:{
            prop: 'companyCode1', //属性
            labelKey: 'atrCenter1',
        },
        atrCenter2:{
            prop: 'companyCode2', //属性
            labelKey: 'atrCenter2',
        },
        atrCenter3:{
            prop: 'companyCode3', //属性
            labelKey: 'atrCenter3',
        },
        atrCenter4:{
            prop: 'companyCode4', //属性
            labelKey: 'atrCenter4',
        },
        icgName:{
            prop: 'icgNoName', //属性
            labelKey: 'atrIcgNoName',
            width:"140px",
            sortable: false,
        },
        plJudgeRslt:{
            prop: 'plJudgeRslt', //属性
            labelKey: 'atrPLResult',
            width:"140px",
            sortable: false,
        },
        riPolicyNo:{
            prop:"riPolicyNo",
            labelKey:"aioiRIPolicyNo",
            width:"180px",
        },
        riEndorseSeqNo:{
            prop:"riEndorseSeqNo",
            labelKey:"aioiRIEndorseSeqNo",
        },
        kindCode: {
            prop:"kindCode",
            labelKey:"atrIbnrKindCode",
        },
        riStatementNo:{
            prop:"riStatementNo",
            labelKey:"aioiBillNo",
        },
        currencyCode:{
            prop: 'currencyCode', //属性
            labelKey: 'atrCurrency',
            align: 'center',
            sortable: false,
        },
        yearMonth:{
            prop: 'yearMonth', //属性
            labelKey: 'atrEvaluationYearMonth',
            align: 'center',
            sortable: false,
        },
        portfolioNo:{
            prop: 'portfolioNo', //属性
            labelKey: 'gDmPortfolioNo',
            width:"140px",
            sortable: false,
        },
        icgNo:{
            prop: 'icgNo', //属性
            labelKey: 'atrExcelIcgNo',
            width:"140px",
            sortable: false,
        },
        riskClassCode: {
            prop: 'riskClassCode', //属性
            labelKey: 'atrInsuranceCode',
            width:"140px",
            sortable: false,
        },
        evaluateApproach:{
            prop: 'evaluateApproach', //属性
            labelKey: 'atrExcelEvaluateApproach',
            sortable: false,
        },
        businessSourceCode:{
            prop: 'businessSourceCode', //属性
            labelKey: 'dmBusinessType',
            sortable: false,
        },
        loaCode:{
            prop: 'loaCode', //属性
            labelKey: 'gLoa',
            width:"200px",
            sortable: false,
        },
        cmunitNo:{
            prop: 'cmunitNo', //属性
            labelKey: 'atrExcelUnitNo',
            width:"180px",
            sortable: false,
            showOverflowTooltip: true,
        },
        productCode:{
            prop: 'productCode', //属性
            labelKey: 'dmProductCode',
        },
        riskCode:{
            prop: 'riskCode', //属性
            labelKey: 'gRisk',
            width:"80px",
            format:function (row,column, cellValue) {
                return cellValue
            }
        },
        evaluateDate:{
            prop: 'evaluateDate', //属性
            labelKey: 'gEvaluateDate',
            align: 'center',
            sortable: false,
        },
        contractDate:{
            prop: 'contractDate', //属性
            labelKey: 'gDmBorderDate',
            align: 'center',
            sortable: false,
        },
        effectiveDate:{
            prop: 'effectiveDate', //属性
            labelKey: 'aioiEffectDate',
            align: 'center',
            sortable: false,
        },
        expiryDate:{
            prop: 'expiryDate', //属性
            labelKey: 'aioiExpiryDate',
            align: 'center',
            sortable: false,
        },
        comfirmDate:{
            prop: 'comfirmDate', //属性
            labelKey: '承保确认日期',
            align: 'center',
            sortable: false,
        },
        approvalDate:{
            prop: 'approvalDate', //属性
            labelKey: 'atrUnderwritingDate',
            align: 'center',
            sortable: false,
        },
        issueDate:{
            prop: 'issueDate', //属性
            labelKey: 'dmSignatureDate',
            align: 'center',
            sortable: false,
        },
        premiumFrequency:{
            prop: 'premiumFrequency', //属性
            labelKey: 'gPremiumFrequency',
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        premiumTerm:{
            prop: 'premiumTerm', //属性
            labelKey: 'gPremiumTerm',
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        grossPremium:{
            prop: 'premium', //属性
            labelKey: 'aioiGrossPremium',
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        premium:{
            prop: 'premium', //属性
            labelKey: 'atrReservePremium',
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        preEdPremium: {
            prop: 'preCumlEdPremium', //属性
            labelKey: 'atrPreAccumEdPremium',
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        preCumlEdNetFee: {
          prop: 'preCumlEdNetFee',
          labelKey: 'preEdNetFee',
          align: 'right'
        },
        preAccumEdPremium:{
            prop: 'preCumlEdPremium',
            labelKey: 'atrPreAccumPaidPremium',
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        preAccumPaidPremium:{
            prop: 'preAccumPaidPremium', //属性
            labelKey: 'atrPreAccumPaidPremium',
            align: 'right',
            width: '160px',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        preAccumNetFee:{
            prop: 'preAccumNetFee', //属性
            labelKey: 'aioiPreAccumNetFee',
            width: '200px',
            showOverflowTooltip: true,
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        accumPaidPremium:{
            prop: 'accumPaidPremium', //属性
            labelKey: 'atrAccumPaidPremium',
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        invCompAmount:{
            prop: 'invCompAmount', //属性
            labelKey: 'aioiInvCompAmount',
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        coverageAmount:{
            prop: 'coverageAmount', //属性
            labelKey: 'atrCoverageAmount',
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        iaehcIn: {
            prop: 'iaehcIn',
            labelKey: 'atrIaehcIn',
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        preCumlEdIaehcIn: {
            prop: 'preCumlEdIaehcIn',
            labelKey: 'atrPreCumlEdIaehcIn',
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        iaehcOut: {
            prop: 'iaehcOut',
            labelKey: 'atrIaehcOut',
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        preCumlEdIaehcOut: {
            prop: 'preCumlEdIaehcOut',
            labelKey: 'atrPreCumlEdIaehcOut',
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        preCumlPaidPremium: {
            prop: 'preCumlPaidPremium',
            labelKey: 'atrPreCumlPaidPremium',
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        preCumlPaidNetFee: {
            prop: 'preCumlPaidNetFee',
            labelKey: 'atrPreCumlPaidNetFee',
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        // 上期已赚跟单获取费用
        preCumlEdIacf: {
            prop: 'preCumlEdIacf',
            labelKey: 'atrPreCumlEdIacf',
            align: 'center',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        iacf:{
            prop: 'iacf', //属性
            labelKey: 'atrIacf',
            align: 'center',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        accumPaidIacf:{
            prop: 'accumPaidIacf', //属性
            labelKey: 'atrAccumPaidIacf',
            align: 'center',
            sortable: false,
        },
        passedMonths:{
            prop: 'passedDates', //属性
            labelKey: 'atrPassedMonths',
            align: 'center',
            sortable: false,
        },
        remainingMonths:{
            prop: 'remainingMonths', //属性
            labelKey: 'atrRemainingMonths',
            align: 'center',
            sortable: false,
        },
        untilReportRemainCsmRate:{
            prop: 'untilReportRemainCsmRate', //属性
            labelKey: 'atrLrcRptPerRemainUnRate',
            align:'right',
            headerAlign:'center',
            sortable: false,
        },
        claimRatio:{
            prop: 'claimRatio', //属性
            labelKey: 'atrExpectedLossRate',//预期损失率
            align:'right',
            headerAlign:'center',
            sortable: false,
            format(row){
                if(!Vue.gvUtil.isEmpty(row.claimRatio)) {
                    return Vue.gvUtil.toPercentage(row.claimRatio) + '%'
                }
            }
        },
        lapseRatio:{
            prop: 'lapseRatio', //属性
            labelKey: 'aioiEtLapse',//退保率
            align:'right',
            headerAlign:'center',
            sortable: false,
            format(row){
                if(!Vue.gvUtil.isEmpty(row.lapseRatio)) {
                    return Vue.gvUtil.toPercentage(row.lapseRatio) + '%'
                }
            }
        },
        ulaeRatio:{
            prop: 'ulaeRatio', //属性
            labelKey: 'atrClaimExpenseRate',//间接理赔费用率
            align:'right',
            headerAlign:'center',
            sortable: false,
            format(row){
                if(!Vue.gvUtil.isEmpty(row.ulaeRatio)) {
                    return Vue.gvUtil.toPercentage(row.ulaeRatio) + '%'
                }
            }
        },
        raRatio:{
            prop: 'raRatio', //属性
            labelKey: 'atrRARate',//非金融风险调整率
            align:'right',
            headerAlign:'center',
            sortable: false,
            format(row){
                if(!Vue.gvUtil.isEmpty(row.raRatio)) {
                    return Vue.gvUtil.toPercentage(row.raRatio) + '%'
                }
            }
        },
        mtRatio :{
            prop: 'mtRatio', //属性
            labelKey: 'atrMaintenanceCostRate',//维持费用率
            align:'right',
            headerAlign:'center',
            sortable: false,
            format(row){
                if(!Vue.gvUtil.isEmpty(row.mtRatio)) {
                    return Vue.gvUtil.toPercentage(row.mtRatio) + '%'
                }
            }
        },
        acRate:{
            prop: 'acRate', //属性
            labelKey: 'atrAdjFeeRate',//调整手续费用率
            align:'right',
            headerAlign:'center',
            sortable: false,
            format(row){
                if(!Vue.gvUtil.isEmpty(row.acRatio)) {
                    return Vue.gvUtil.toPercentage(row.acRatio) + '%'
                }
            }
        },
        pcRatio:{
            prop: 'pcRatio', //属性
            labelKey: 'atrProfitFeeRate',//纯益手续费用率
            align: 'center',
            sortable: false,
            format(row){
                if(!Vue.gvUtil.isEmpty(row.pcRatio)) {
                    return Vue.gvUtil.toPercentage(row.pcRatio) + '%'
                }
            }
        },
        iaehcRatio:{
            prop: 'iaehcRatio', //属性
            labelKey: 'atrProfitFeeRate',//纯益手续费用率
            align: 'center',
            sortable: false,
            format(row){
                if(!Vue.gvUtil.isEmpty(row.iaehcRatio)) {
                    return Vue.gvUtil.toPercentage(row.iaehcRatio) + '%'
                }
            }
        },
        invCompRatio:{
            prop: 'invCompRatio', //属性
            labelKey: 'aioiInvCompRatio',//纯益手续费用率
            align: 'center',
            sortable: false,
            format(row){
                if(!Vue.gvUtil.isEmpty(row.invCompRatio)) {
                    return Vue.gvUtil.toPercentage(row.invCompRatio) + '%'
                }
            }
        },
        badDebt:{
            prop: 'badDebt', //属性
            labelKey: 'atrBadDebt',
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        lapseRate:{
            prop: 'lapseRate', //属性
            labelKey: 'atrLapseRate',
            align: 'right',
            sortable: false,
            format(row){
                if(!Vue.gvUtil.isEmpty(row.lapseRate)) {
                    return Vue.gvUtil.toPercentage(row.lapseRate) + '%'
                }
            }
        },
        mtRate: {
            prop: 'mtRate', //属性
            labelKey: 'atrMtRate',
            align: 'right',
            sortable: false,
            format(row){
                if(!Vue.gvUtil.isEmpty(row.mtRate)) {
                    return Vue.gvUtil.toPercentage(row.mtRate) + '%'
                }
            }
        },
        claimRate:{
            prop: 'claimRate', //属性
            labelKey: 'atrClaimRate',
            align: 'right',
            sortable: false,
            format(row){
                if(!Vue.gvUtil.isEmpty(row.claimRate)) {
                    return Vue.gvUtil.toPercentage(row.claimRate) + '%'
                }
            }
        },
        ulaeRate: {
            prop: 'ulaeRate', //属性
            labelKey: 'atrUlaeRate',
            align: 'right',
            sortable: false,
            format(row){
                if(!Vue.gvUtil.isEmpty(row.ulaeRate)) {
                    return Vue.gvUtil.toPercentage(row.ulaeRate) + '%'
                }
            }
        },
        treatyNo:{
            prop: 'treatyNo', //属性
            labelKey: 'reinsTreatyNo',
            sortable: false,
        },
        treatyName:{
            prop: 'treatyName', //属性
            labelKey: 'atrTreatyName',
            sortable: false,
        },
        statementPeriod:{
            prop: 'accountPeriod', //属性
            labelKey: 'atrStatementPeriod',
            sortable: false,
        },
        dataType:{
            prop: 'dataType', //属性
            labelKey: 'configSceneAppTreatyType',
            sortable: false,
        },
        netFee:{
            prop: 'netFee', //属性
            labelKey: 'aioiNetFee',
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        // 净额结算手续费率
        feeRate: {
            prop: 'feeRate',
            labelKey: 'feeRate',
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        // 上期累计已赚签单净额结算手续费
        preEdNetFee: {
            prop: 'preEdNetFee',
            labelKey: 'preEdNetFee',
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        reinsCode:{
            prop: 'reinsCode', //属性
            labelKey: 'atrLrcReinsCode',
            width: '200px',
            sortable: false,
        },
        lrRiskCode:{
            prop: 'riskCode', //属性
            labelKey: 'atrLrcRiskCode',
            width: '180px',
            sortable: false,
        },
        businessNature:{
            prop: 'businessNature', //属性
            labelKey: 'atrLrcBusinessNature',
            width: '150px',
            sortable: false,
        },
        reinsFlag:{
            prop: 'reinsFlag', //属性
            labelKey: 'atrLrcReinsFlag',
            width: '110px',
            sortable: false,
        },
        shareholderFlag:{
            prop: 'shareholderFlag', //属性
            labelKey: 'atrLrcShareholderFlag',
            width: '120px',
            sortable: false,
        },
        comCode:{
            prop: 'comCode', //属性
            labelKey: 'atrLrcComCode',
            width: '200px',
            sortable: false,
        },
        centerCode:{
            prop: 'centerCode', //属性
            labelKey: 'atrLrcCenterCode',
            width: '250px',
            sortable: false,
        },
        iaehc:{
            prop: 'iaehc', //属性
            labelKey: '0',
            width: '100px',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        // 新增投资成分导出相关字段
        sectionoCode:{
            prop: 'sectionoCode', //属性
            labelKey: 'atrSectionoCode', // 再保人分项
            width: '120px',
            sortable: false,
        },
        reinsurerCode:{
            prop: 'reinsurerCode', //属性
            labelKey: 'atrReinsurerCode', // 再保人编码
            width: '120px',
            sortable: false,
        },
        riEffectiveDate:{
            prop: 'riEffectiveDate', //属性
            labelKey: 'atrRiEffectiveDate', // 合约起期
            align: 'center',
            sortable: false,
        },
        riExpiryDate:{
            prop: 'riExpiryDate', //属性
            labelKey: 'atrRiExpiryDate', // 合约止期
            align: 'center',
            sortable: false,
        },
        prepaidFeeRate:{
            prop: 'prepaidFeeRate', //属性
            labelKey: 'atrPrepaidFeeRate', // 预付手续费率
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 4);
            }
        },
        floatingHandlingFeeCap:{
            prop: 'floatingHandlingFeeCap', //属性
            labelKey: 'atrFloatingHandlingFeeCap', // 浮动手续费率上限
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 4);
            }
        },
        invAmount:{
            prop: 'invAmount', //属性
            labelKey: 'atrInvAmount', // 投资成分
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        devNo:{
            prop: 'devNo', //属性
            labelKey: 'atrDevNo', // 发展期
            align: 'center',
            sortable: false,
        },
        paidEdPremium:{
            prop: 'paidEdPremium', //属性
            labelKey: 'atrPaidEdPremium', // 已写入已赚
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        edPremium:{
            prop: 'edPremium', //属性
            labelKey: 'atrEdPremium', // 已赚保费(含EPI)
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        paidNetFee:{
            prop: 'paidNetFee', //属性
            labelKey: 'atrPaidNetFee', // 已写入手续费
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
        edNetFee:{
            prop: 'edNetFee', //属性
            labelKey: 'atrEdNetFee', // 已赚手续费(含EPI)
            align: 'right',
            sortable: false,
            format:function (row,column, cellValue) {
                return  Vue.filter('amount')(cellValue, true, 2);
            }
        },
    }

export default {
    getfieldList : function(arr){
        var list=[]
        arr.forEach(function(key){
            if(fields[key]==undefined) return
            list.push(fields[key])
        })
        return list
    }

}