package com.ss.ifrs.actuarial.service.impl;


import com.alibaba.excel.EasyExcel;
import com.ss.ifrs.actuarial.constant.ActuarialConstant;
import com.ss.ifrs.actuarial.dao.AtrBussInterestRateDao;
import com.ss.ifrs.actuarial.dao.buss.AtrBussIrForwardDao;
import com.ss.ifrs.actuarial.dao.buss.AtrBussIrInitialDao;
import com.ss.ifrs.actuarial.dao.buss.AtrBussIrWeightDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfCbTreasuryYcDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfInterestBaseDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfInterestRateDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfInterestRateDetailDao;
import com.ss.ifrs.actuarial.pojo.atrbuss.ir.po.AtrBussIrForward;
import com.ss.ifrs.actuarial.pojo.atrbuss.ir.po.AtrBussIrInitial;
import com.ss.ifrs.actuarial.pojo.atrbuss.ir.po.AtrBussIrWeight;
import com.ss.ifrs.actuarial.pojo.atrbuss.ir.vo.AtrBussIrDevVo;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfCbTreasuryYc;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestBase;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestRate;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestRateDetail;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.*;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrDuctEvaluatePeriodVo;
import com.ss.ifrs.actuarial.service.AtrConfCodeService;
import com.ss.ifrs.actuarial.service.AtrConfInterestRateService;
import com.ss.ifrs.actuarial.service.AtrExportService;
import com.ss.ifrs.actuarial.service.AtrImportService;
import com.ss.ifrs.actuarial.util.EcfUtil;
import com.ss.ifrs.actuarial.util.abp.AsyncBatchProcessor;
import com.ss.library.excel.ExcelSheet;
import com.ss.library.excel.ExcelSheetData;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ClassUtil;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.listener.DataListener;
import com.ss.platform.util.ExcelExportUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AtrConfInterestRateServiceImpl implements AtrConfInterestRateService {
    @Autowired
    AtrConfInterestRateDao atrConfInterestRateDao;

    @Autowired
    AtrConfInterestRateDetailDao atrConfInterestRateDetailDao;

    @Autowired
    AtrBussIrForwardDao atrBussIrForwardDao;
    @Autowired
    AtrBussIrInitialDao atrBussIrInitialDao;
    @Autowired
    AtrBussIrWeightDao atrBussIrWeightDao;

    @Autowired
    private AtrImportService atrImportService;
    @Autowired
    AtrExportService atrExportService;

    @Autowired
    AtrConfCodeService atrConfCodeService;

    @Autowired
    private AtrConfInterestBaseDao atrConfInterestBaseDao;

    @Autowired
    private AtrConfCbTreasuryYcDao atrConfCbTreasuryYcDao;

    @Autowired
    private AtrBussInterestRateService atrBussInterestRateService;

    @Autowired
    private AtrBussInterestRateDao atrBussInterestRateDao;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void importDomesticInterestRate(MultipartFile file, AtrConfInterestRateVo atrConfInterestRateVo, Long userId) throws Exception {
        if (ObjectUtils.isEmpty(atrConfInterestRateVo.getYearMonth())) {
            return;
        }
        DataListener<AtrConfInterestRateDetailDomesticVo> listener = new DataListener<>();
        EasyExcel.read(file.getInputStream(), AtrConfInterestRateDetailDomesticVo.class, listener).sheet(1).headRowNumber(2).doRead();
        List<AtrConfInterestRateDetailDomesticVo> interestRateDetailVoList = listener.getRows();
        List<AtrConfCbTreasuryYc> atrConfCbTreasuryYcs = new ArrayList<>();
        interestRateDetailVoList.forEach(interestRateDetailVo -> {
            AtrConfCbTreasuryYc treasuryYc = ClassUtil.convert(interestRateDetailVo, AtrConfCbTreasuryYc.class);
            // 将百分比转换为小数（除以100）
            if (treasuryYc.getAvgValue() != null) {
                treasuryYc.setAvgValue(treasuryYc.getAvgValue().divide(new BigDecimal(100), 15, RoundingMode.HALF_UP));
            }
            atrConfCbTreasuryYcs.add(treasuryYc);
        });
        this.saveInterestRate(atrConfCbTreasuryYcs, atrConfInterestRateVo, userId);
        atrImportService.importFile(file, atrConfInterestRateVo.getTargetRouter(), userId);
    }

    @Override
    public void importForeignInterestRate(MultipartFile file, AtrConfInterestRateVo atrConfInterestRateVo, Long userId) throws Exception {
        if (ObjectUtils.isEmpty(atrConfInterestRateVo.getConfigYear())) {
            return;
        }
        List<AtrConfInterestRateDetailForeignVo> list = ExcelExportUtil.read(file.getInputStream(), AtrConfInterestRateDetailForeignVo.class, 1);
        List<AtrConfInterestRateDetailVo> interestRateDetailVoList = ClassUtil.convert(list, AtrConfInterestRateDetailVo.class).subList(0, atrConfInterestRateVo.getConfigYear() * 12);
//        this.saveInterestRate(interestRateDetailVoList, atrConfInterestRateVo, userId);
        atrImportService.importFile(file, atrConfInterestRateVo.getTargetRouter(), userId);
    }

    private void saveInterestRate(List<AtrConfCbTreasuryYc> interestRateDetailVoList, AtrConfInterestRateVo atrConfInterestRateVo, Long userId) throws Exception {
        if (ObjectUtils.isEmpty(interestRateDetailVoList)) {
            return;
        }

        // 1. 准备 AtrConfInterestRate 记录
        atrConfInterestRateVo.setCreatorId(userId);
        atrConfInterestRateVo.setCreateTime(new Date());
        atrConfInterestRateVo.setConfirmIs(CommonConstant.VersionStatus.PENDING);
        atrConfInterestRateVo.setVersionNo(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        AtrConfInterestRate po = ClassUtil.convert(atrConfInterestRateVo, AtrConfInterestRate.class);
        
        // 2. 先插入主表记录以获取主键ID
        atrConfInterestRateDao.save(po);
        // 3. 筛选出 tenor_years ≤ 20 的记录
        List<AtrConfCbTreasuryYc> filteredList = interestRateDetailVoList.stream()
                .filter(detail -> detail.getTenorYears() != null && detail.getTenorYears().compareTo(new BigDecimal(20)) <= 0)
                .sorted(Comparator.comparing(AtrConfCbTreasuryYc::getTenorYears))
                .collect(Collectors.toList());
        
        int size = filteredList.size();
        
        // 计算 RevTenorYears 值
        Map<BigDecimal, BigDecimal> tenorToRevMap = new HashMap<>();
        for (int i = 0; i < size; i++) {
            AtrConfCbTreasuryYc current = filteredList.get(i);
            AtrConfCbTreasuryYc reversed = filteredList.get(size - 1 - i);
            tenorToRevMap.put(current.getTenorYears(), reversed.getTenorYears());
        }
        
        // 4. 设置所有记录的属性
        for (AtrConfCbTreasuryYc detail : interestRateDetailVoList) {
            // 设置主键和创建信息
            detail.setInterestRateId(po.getInterestRateId());
            detail.setCreatorId(userId);
            detail.setCreatedTime(new Date());
            
            // 如果是 tenor_years ≤ 20 的记录，设置 RevTenorYears
            if (detail.getTenorYears() != null && detail.getTenorYears().compareTo(new BigDecimal(20)) <= 0) {
                detail.setRevTenorYears(tenorToRevMap.get(detail.getTenorYears()));
            }
        }
        
        // 5. 使用 AsyncBatchProcessor 进行批量插入所有记录
        try (AsyncBatchProcessor processor = new AsyncBatchProcessor(jdbcTemplate, EcfUtil.THREADS_DB)) {
            // 注册要处理的实体类型
            processor.addType(AtrConfCbTreasuryYc.class);
            
            // 批量插入所有记录
            for (AtrConfCbTreasuryYc detail : interestRateDetailVoList) {
                processor.insert(detail);
            }
            
            // 确保所有数据都已插入
            processor.end();
        }

        // 6. 调用计算方法
        atrBussInterestRateService.calcForwardAndInitail(po.getInterestRateId());
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public AtrConfInterestRateVo findById(Long interestRateId) {
        AtrConfInterestRateVo atrConfInterestRateVo = atrConfInterestRateDao.findByInterestRateId(interestRateId);
        List<AtrConfInterestRateDetail>  atrConfInterestRateDetailList;
        AtrConfInterestRateDetail atrConfInterestRateDetail = new AtrConfInterestRateDetail();
        atrConfInterestRateDetail.setInterestRateId(atrConfInterestRateVo.getInterestRateId());
        atrConfInterestRateDetailList = atrConfInterestRateDetailDao.findList(atrConfInterestRateDetail);
        atrConfInterestRateVo.setAtrConfInterestRateDetailVoList(ClassUtil.convert(atrConfInterestRateDetailList, AtrConfInterestRateDetailVo.class));
        return atrConfInterestRateVo;
    }

    @Override
    public Map<String, Object> findBussResultById(Long interestRateId) {
        Map<String, Object> resultMap = new HashMap<>();
        AtrConfInterestRateVo atrConfInterestRateVo = atrConfInterestRateDao.findByInterestRateId(interestRateId);
        if (ObjectUtils.isEmpty(atrConfInterestRateVo)) {
            return resultMap;
        }
        Map<String, AtrDuctEvaluatePeriodVo> dataMap;

        AtrBussIrForward atrBussIrForward = new AtrBussIrForward();
        atrBussIrForward.setUploadRateId(interestRateId);
        AtrBussIrInitial atrBussIrInitial = ClassUtil.convert(atrBussIrForward, AtrBussIrInitial.class);
        AtrBussIrWeight atrBussIrWeight = ClassUtil.convert(atrBussIrForward, AtrBussIrWeight.class);

        List<AtrBussIrDevVo> forwardDevVoList = atrBussIrForwardDao.findBussIrForwardDevList(atrBussIrForward);
        List<AtrBussIrDevVo> initialDevVoList = atrBussIrInitialDao.findBussIrInitialDevList(atrBussIrInitial);
        //List<AtrBussIrDevVo> weightDevVoList = atrBussIrWeightDao.findBussIrWeightDevList(atrBussIrWeight);

        AtrConfCodeVo sysCodeVo = new AtrConfCodeVo();
        sysCodeVo.setCodeCodeIdx(ActuarialConstant.InterestRateTime.CODE);
        List<AtrConfCodeVo> confCodeVoList = atrConfCodeService.findCodeByCodeIdx(sysCodeVo);


        dataMap = interestPeriodMap(forwardDevVoList, ActuarialConstant.InterestRateTime.CodeType.BEGINNING);
        resultMap.put("forwardDevelopItem", dataMap);
        dataMap = interestPeriodMap(initialDevVoList, ActuarialConstant.InterestRateTime.CodeType.MIDDLE);
        resultMap.put("initialDevelopItem", dataMap);
        //dataMap = interestPeriodMap(weightDevVoList, ActuarialConstant.InterestRateTime.CodeType.END);
        //resultMap.put("forwardDevelopItem", dataMap);

        List<Map<String,Object>> interestRateInitialVoList =  this.interestPeriodDataMapList(initialDevVoList, confCodeVoList , atrConfInterestRateVo.getYearMonth() , null);
        resultMap.put("interestRateInitialVoList", interestRateInitialVoList);

        // 在findBussResultById方法中,修改数据处理逻辑
        List<AtrBussIrWeight> atrBussInterestWeightList = atrBussIrWeightDao.findBussIrWeightLessUploadId(interestRateId);

        // 转换数据格式
        List<Map<String, Object>> horizontalDataList = new ArrayList<>();

        // 按icgNo分组
        Map<String, List<AtrBussIrWeight>> groupedByIcgNo = atrBussInterestWeightList.stream()
                .collect(Collectors.groupingBy(AtrBussIrWeight::getIcgNo));

        // 转换为横向数据
        for (Map.Entry<String, List<AtrBussIrWeight>> entry : groupedByIcgNo.entrySet()) {
            Map<String, Object> rowData = new HashMap<>();
            rowData.put("yearMonth", atrConfInterestRateVo.getYearMonth());
            rowData.put("icgNo", entry.getKey());

            // 将每个月的数据放入对应的1-12列
            List<AtrBussIrWeight> monthlyData = entry.getValue();
            for (AtrBussIrWeight weight : monthlyData) {
                String month = weight.getYearMonth().substring(4); // 获取月份
                rowData.put(Integer.valueOf(month).toString(), weight.getPremium()); // 使用月份作为key
            }

            // 确保1-12月都有值,没有的补null
            for (int i = 1; i <= 12; i++) {
                if (!rowData.containsKey(String.valueOf(i))) {
                    rowData.put(String.valueOf(i), null);
                }
            }

            horizontalDataList.add(rowData);
        }

        // 将转换后的数据放入resultMap
        resultMap.put("currInitialRateVoList", horizontalDataList);

        return resultMap;
    }


    private Map<String, AtrDuctEvaluatePeriodVo> interestPeriodMap(List<AtrBussIrDevVo> atrBussInterestDetailVoList, String irTime) {
        AtrDuctEvaluatePeriodVo periodVo ;
        Map<String, AtrDuctEvaluatePeriodVo> dataMap = new LinkedHashMap<>();
        List<Long> devPeriodList =  atrBussInterestDetailVoList.stream().map(AtrBussIrDevVo :: getDevNo).distinct().sorted().collect(Collectors.toList());
        for(Long devPeriod : devPeriodList) {
            periodVo = new AtrDuctEvaluatePeriodVo();
            periodVo.setQuotaCode(devPeriod.toString());
            periodVo.setQuotaEName(devPeriod.toString());
            periodVo.setQuotaCName(devPeriod.toString());
            periodVo.setQuotaLName(devPeriod.toString());
            switch (irTime) {
                case ActuarialConstant.InterestRateTime.CodeType.BEGINNING:
                    periodVo.setValueList(atrBussInterestDetailVoList.stream().sorted(Comparator.comparing(AtrBussIrDevVo:: getYearMonth)).filter(vo -> vo.getDevNo().equals(devPeriod)).map(AtrBussIrDevVo :: getRateB).collect(Collectors.toList()));
                    break;
                case ActuarialConstant.InterestRateTime.CodeType.MIDDLE:
                    periodVo.setValueList(atrBussInterestDetailVoList.stream().sorted(Comparator.comparing(AtrBussIrDevVo:: getYearMonth)).filter(vo -> vo.getDevNo().equals(devPeriod)).map(AtrBussIrDevVo :: getRateM).collect(Collectors.toList()));
                    break;
                case ActuarialConstant.InterestRateTime.CodeType.END:
                    periodVo.setValueList(atrBussInterestDetailVoList.stream().sorted(Comparator.comparing(AtrBussIrDevVo:: getYearMonth)).filter(vo -> vo.getDevNo().equals(devPeriod)).map(AtrBussIrDevVo :: getRateE).collect(Collectors.toList()));
                    break;
            }
            dataMap.put(devPeriod.toString(), periodVo);
        }
        return dataMap;
    }

    private List<Map<String,Object>> interestPeriodDataMapList(List<AtrBussIrDevVo> atrBussIrForwards, List<AtrConfCodeVo> IRTime, String yearMonth, String icgNo) {
        List<Map<String,Object>> interestRateForwardVoList = new ArrayList<>();
        List<Long> devPeriodList =  atrBussIrForwards.stream().map(AtrBussIrDevVo :: getDevNo).distinct().sorted().collect(Collectors.toList());
        for (AtrConfCodeVo atrConfCodeVo: IRTime){
            Map<String, Object> dataMap = new LinkedHashMap<>();
            dataMap.put("yearMonth", yearMonth);
            dataMap.put("icgNo", icgNo);
            switch (atrConfCodeVo.getCodeCode()) {
                case ActuarialConstant.InterestRateTime.CodeType.BASE:
                    dataMap.put("payTime", ActuarialConstant.InterestRateTime.CodeType.BASE);
                    for(Long devPeriod : devPeriodList) {
                        dataMap.put(devPeriod.toString(), atrBussIrForwards.stream().sorted(Comparator.comparing(AtrBussIrDevVo:: getYearMonth))
                                .filter(vo -> vo.getDevNo().equals(devPeriod)).map(AtrBussIrDevVo :: getRateBase).collect(Collectors.toList()).get(0));
                    }
                    break;
                case ActuarialConstant.InterestRateTime.CodeType.BEGINNING:
                    dataMap.put("payTime", ActuarialConstant.InterestRateTime.CodeType.BEGINNING);
                    for(Long devPeriod : devPeriodList) {
                        dataMap.put(devPeriod.toString(), atrBussIrForwards.stream().sorted(Comparator.comparing(AtrBussIrDevVo:: getYearMonth)).filter(vo -> vo.getDevNo().equals(devPeriod)).map(AtrBussIrDevVo :: getRateB).collect(Collectors.toList()).get(0));
                    }
                    break;
                case ActuarialConstant.InterestRateTime.CodeType.END:
                    dataMap.put("payTime", ActuarialConstant.InterestRateTime.CodeType.END);
                    for(Long devPeriod : devPeriodList) {
                        dataMap.put(devPeriod.toString(), atrBussIrForwards.stream().sorted(Comparator.comparing(AtrBussIrDevVo:: getYearMonth))
                                .filter(vo -> vo.getDevNo().equals(devPeriod)).map(AtrBussIrDevVo :: getRateE).collect(Collectors.toList()).get(0));
                    }
                    break;
            }
            interestRateForwardVoList.add(dataMap);
        }

        return interestRateForwardVoList;
    }


    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
    public Page<AtrConfInterestRateVo> searchPage(AtrConfInterestRateVo atrConfInterestRateVo, Pageable pageParam) {
        Page<AtrConfInterestRateVo> atrConfInterestRateVos = atrConfInterestRateDao.fuzzySearchPage(atrConfInterestRateVo, pageParam);
        return atrConfInterestRateVos;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void delete(Long interestRateId, Long userId) {
        AtrConfInterestRateVo atrConfInterestRateVo = this.findById(interestRateId);
        if(ObjectUtils.isNotEmpty(atrConfInterestRateVo) && "1".equals(atrConfInterestRateVo.getConfirmIs())) {
            return;
        }
        HashMap<String, Object> map = new HashMap(2);
        map.put("interestRateId", interestRateId);
        atrConfCbTreasuryYcDao.deleteByInterestRateId(interestRateId);
        atrConfInterestRateDetailDao.deleteByMap(map);
        atrConfInterestRateDao.deleteById(interestRateId);
        
        // 先删除子表atr_buss_ir_initial_dev的数据
        atrBussInterestRateDao.deleteInitialDev(interestRateId);
        // 再删除主表atr_buss_ir_initial的数据
        atrBussInterestRateDao.deleteInitial(interestRateId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void updateValid(AtrConfInterestRateVo atrConfInterestRateVo, Long userId) {
        AtrConfInterestRate po = new AtrConfInterestRate();
        Long interestRateId = atrConfInterestRateVo.getInterestRateId();
        if (interestRateId != null) {
            po = ClassUtil.convert(this.findById(interestRateId), AtrConfInterestRate.class);
            po.setValidIs(atrConfInterestRateVo.getValidIs());
        }
        if (po != null) {
            po.setUpdateTime(new Date());
            //待审核时，审核意见应为空
            atrConfInterestRateDao.updateById(po);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void confirm(AtrConfInterestRateVo atrConfInterestRateVo, Long userId) {
        AtrConfInterestRate po = atrConfInterestRateDao.findById(atrConfInterestRateVo.getInterestRateId());
        po.setConfirmIs("1");
        po.setConfirmId(userId);
        po.setConfirmTime(new Date());
        po.setUpdatorId(userId);
        po.setUpdateTime(new Date());
        atrConfInterestRateDao.updateById(po);
        atrBussIrInitialDao.updateByMainId(po);

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void auditList(List<AtrConfInterestRateVo> atrConfInterestRateVoList, Long userId) {
        for (AtrConfInterestRateVo atrConfInterestRateVo : atrConfInterestRateVoList) {
            this.confirm(atrConfInterestRateVo, userId);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void update(AtrConfInterestRateVo atrConfInterestRateVo, Long userId) {
        atrConfInterestRateDao.updateById(ClassUtil.convert(atrConfInterestRateVo, AtrConfInterestRate.class));
    }


    @Override
    public void saveList(List<AtrConfInterestRateVo> atrConfInterestRateVos, Long userId) {
        List<AtrConfInterestRate> atrConfInterestRates = ClassUtil.convert(atrConfInterestRateVos, AtrConfInterestRate.class);
        for (AtrConfInterestRate atrConfInterestRate : atrConfInterestRates) {
            atrConfInterestRateDao.save(atrConfInterestRate);
        }
    }

    @Override
    public List<AtrConfInterestRateVo> findList(AtrConfInterestRateVo atrConfInterestRateVo) {
        List<AtrConfInterestRate> atrConfInterestRateVos = atrConfInterestRateDao.findList(ClassUtil.convert(atrConfInterestRateVo, AtrConfInterestRate.class));
        return ClassUtil.convert(atrConfInterestRateVos,AtrConfInterestRateVo.class);
    }

    @Override
    public void downloadInterest(HttpServletRequest request, HttpServletResponse response, AtrConfInterestRateVo atrConfInterestRateVo, Long userId) throws Exception {
        String fileName = atrConfInterestRateVo.getTemplateFileName();
        if (ObjectUtils.isEmpty(fileName)) {
            throw new Exception("Excel Name is null");
        }

        // 获取利率ID
        Long interestRateId = atrConfInterestRateVo.getInterestRateId();
        if (ObjectUtils.isEmpty(interestRateId)) {
            throw new Exception("Interest Rate ID is null");
        }

        // 准备导出的sheet数据列表
        List<ExcelSheet> sheetList = new ArrayList<>();

        // 1. 获取atr_conf_interest_rate_detail表的数据 (sheet 1)
        AtrConfInterestRateDetail atrConfInterestRateDetail = new AtrConfInterestRateDetail();
        atrConfInterestRateDetail.setInterestRateId(interestRateId);
        List<AtrConfInterestRateDetail> interestRateDetailList = atrConfInterestRateDetailDao.findList(atrConfInterestRateDetail);
        if (!ObjectUtils.isEmpty(interestRateDetailList)) {
            ExcelSheetData sheetData1 = new ExcelSheetData("df", interestRateDetailList);
            List<ExcelSheetData> sheetDataList1 = new ArrayList<>();
            sheetDataList1.add(sheetData1);
            ExcelSheet sheet1 = new ExcelSheet(0, sheetDataList1);
            sheetList.add(sheet1);
        } else {
            // 如果没有数据，添加一个空记录以避免占位符显示
            List<AtrConfInterestRateDetail> emptyList = new ArrayList<>();
            emptyList.add(new AtrConfInterestRateDetail()); // 添加一个空对象
            ExcelSheetData sheetData1 = new ExcelSheetData("df", emptyList);
            List<ExcelSheetData> sheetDataList1 = new ArrayList<>();
            sheetDataList1.add(sheetData1);
            ExcelSheet sheet1 = new ExcelSheet(0, sheetDataList1);
            sheetList.add(sheet1);
        }

        // 2. 获取atr_buss_ir_initial_dev表的数据 (sheet 2)
        AtrBussIrInitial atrBussIrInitial = new AtrBussIrInitial();
        atrBussIrInitial.setUploadRateId(interestRateId);
        List<AtrBussIrDevVo> initialDevVoList = atrBussIrInitialDao.findBussIrInitialDevList(atrBussIrInitial);
        if (!ObjectUtils.isEmpty(initialDevVoList)) {
            ExcelSheetData sheetData2 = new ExcelSheetData("df", initialDevVoList);
            List<ExcelSheetData> sheetDataList2 = new ArrayList<>();
            sheetDataList2.add(sheetData2);
            ExcelSheet sheet2 = new ExcelSheet(1, sheetDataList2);
            sheetList.add(sheet2);
        } else {
            // 如果没有数据，添加一个空记录以避免占位符显示
            List<AtrBussIrDevVo> emptyList = new ArrayList<>();
            emptyList.add(new AtrBussIrDevVo()); // 添加一个空对象
            ExcelSheetData sheetData2 = new ExcelSheetData("df", emptyList);
            List<ExcelSheetData> sheetDataList2 = new ArrayList<>();
            sheetDataList2.add(sheetData2);
            ExcelSheet sheet2 = new ExcelSheet(1, sheetDataList2);
            sheetList.add(sheet2);
        }
        
        // 3. 获取atr_buss_ir_weight表的数据 (sheet 3)
        List<AtrBussIrWeight> weightList = atrBussIrWeightDao.findBussIrWeightLessUploadId(interestRateId);
        
        // 获取当前月份及以前的premium数据
        if (!ObjectUtils.isEmpty(weightList)) {
            String currentYearMonth = atrConfInterestRateVo.getYearMonth();
            
            // 按照icgNo分组
            Map<String, List<AtrBussIrWeight>> groupedByIcgNo = weightList.stream()
                    .filter(weight -> weight.getYearMonth().compareTo(currentYearMonth) <= 0)
                    .collect(Collectors.groupingBy(AtrBussIrWeight::getIcgNo));
            
            List<Map<String, Object>> horizontalDataList = new ArrayList<>();
            
            // 转换为横向数据
            for (Map.Entry<String, List<AtrBussIrWeight>> entry : groupedByIcgNo.entrySet()) {
                Map<String, Object> rowData = new HashMap<>();
                rowData.put("yearMonth", currentYearMonth);
                rowData.put("icgNo", entry.getKey());
                
                // 按月份排序并设置month1-month12的值
                Map<Integer, BigDecimal> monthPremiumMap = entry.getValue().stream()
                        .collect(Collectors.toMap(
                            weight -> Integer.parseInt(weight.getYearMonth().substring(4)), // 从yearMonth中提取月份
                            AtrBussIrWeight::getPremium
                        ));
                
                // 确保12个月的数据都存在，不存在的用null填充
                for (int i = 1; i <= 12; i++) {
                    rowData.put("month" + i, monthPremiumMap.getOrDefault(i, null));
                }
                
                horizontalDataList.add(rowData);
            }
            
            ExcelSheetData sheetData3 = new ExcelSheetData("df", horizontalDataList);
            List<ExcelSheetData> sheetDataList3 = new ArrayList<>();
            sheetDataList3.add(sheetData3);
            ExcelSheet sheet3 = new ExcelSheet(2, sheetDataList3);
            sheetList.add(sheet3);
        } else {
            // 如果没有数据，添加一个空记录以避免占位符显示
            List<Map<String, Object>> emptyList = new ArrayList<>();
            Map<String, Object> emptyRow = new HashMap<>();
            emptyRow.put("yearMonth", "");
            emptyRow.put("icgNo", "");
            // 添加空的月份数据
            for (int i = 1; i <= 12; i++) {
                emptyRow.put("month" + i, null);
            }
            emptyList.add(emptyRow);
            
            ExcelSheetData sheetData3 = new ExcelSheetData("df", emptyList);
            List<ExcelSheetData> sheetDataList3 = new ArrayList<>();
            sheetDataList3.add(sheetData3);
            ExcelSheet sheet3 = new ExcelSheet(2, sheetDataList3);
            sheetList.add(sheet3);
        }
        
        // 4. 获取atr_buss_ir_weight_dev表的数据 (sheet 4)
        AtrBussIrWeight atrBussIrWeight = new AtrBussIrWeight();
        atrBussIrWeight.setUploadRateId(interestRateId);
        List<AtrBussIrDevVo> weightDevVoList = atrBussIrWeightDao.findBussIrWeightDevList(atrBussIrWeight);
        if (!ObjectUtils.isEmpty(weightDevVoList)) {
            // 处理第0期的数据
            weightDevVoList.stream()
                    .filter(vo -> vo.getDevNo() != null && vo.getDevNo() == 0)
                    .forEach(vo -> {
                        vo.setRateB(null);     // 设置第0期的rateB为null
                    });
            ExcelSheetData sheetData4 = new ExcelSheetData("df", weightDevVoList);
            List<ExcelSheetData> sheetDataList4 = new ArrayList<>();
            sheetDataList4.add(sheetData4);
            ExcelSheet sheet4 = new ExcelSheet(3, sheetDataList4);
            sheetList.add(sheet4);
        } else {
            // 如果没有数据，添加一个空记录以避免占位符显示
            List<AtrBussIrDevVo> emptyList = new ArrayList<>();
            emptyList.add(new AtrBussIrDevVo()); // 添加一个空对象
            ExcelSheetData sheetData4 = new ExcelSheetData("df", emptyList);
            List<ExcelSheetData> sheetDataList4 = new ArrayList<>();
            sheetDataList4.add(sheetData4);
            ExcelSheet sheet4 = new ExcelSheet(3, sheetDataList4);
            sheetList.add(sheet4);
        }

        String logFileName = atrConfInterestRateVo.getLogFileName();
        atrExportService.exportExcelSheetList(request, response, sheetList,
                fileName, logFileName, atrConfInterestRateVo.getTargetRouter(), userId);
    }


    @Override
    public void downloadTemplate(HttpServletRequest request, HttpServletResponse response, AtrConfInterestRateVo atrConfInterestRateVo, Long userId) throws Exception {
        atrExportService.downloadTemplateExcel(request, response, atrConfInterestRateVo.getTemplateFileName(), atrConfInterestRateVo.getLogFileName(), userId);
    }

    @Override
    public void saveOrUpdateInterestBase(List<AtrConfInterestBase> atrConfInterestBase,Long userId) {
        atrConfInterestBase.forEach(item -> {
            item.setCreatorId(userId);
            item.setUpdatorId(userId);
            atrConfInterestBaseDao.saveOrUpdateByCode(item);
        });
    }

    @Override
    public List<AtrConfInterestBase> listInterestBase() {
        return atrConfInterestBaseDao.findList(null);
    }
}
