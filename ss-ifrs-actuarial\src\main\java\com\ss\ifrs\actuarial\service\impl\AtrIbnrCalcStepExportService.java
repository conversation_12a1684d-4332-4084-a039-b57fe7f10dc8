package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.conf.AppConfig;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.*;
import com.ss.library.utils.HttpResponseUtil;
import com.ss.library.utils.LazyValidator;
import com.ss.library.utils.StringUtil;
import com.ss.platform.util.ClassUtil;
import lombok.var;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.hpsf.Decimal;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

@Service
public class AtrIbnrCalcStepExportService {

    @Resource
    private AppConfig appConfig;

    @Resource
    private AtrIbnrCalcService atrIbnrCalcService;

    public void export(String actionNo, HttpServletResponse response) throws IOException {
        LazyValidator.validate(v -> v.notEmpty(actionNo, "action no."));

        byte[] bytes = FileUtils.readFileToByteArray(getTemplateFile());
        ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try (Workbook wb = new XSSFWorkbook(bis)) {
            List<AtrBussIbnrcalcStepVo> vos = atrIbnrCalcService.getAllActionStep(actionNo, null);
            for (AtrBussIbnrcalcStepVo vo : vos) {
                exportOne(wb, vo);
            }
            deleteTplSheet(wb);
            wb.write(bos);
        }

        String fileName = "step"
                + DateFormatUtils.format(new Date(), "_yyyyMMdd_HHmmss_")
                + RandomStringUtils.randomAlphanumeric(3).toLowerCase()
                + ".xlsx";
        HttpResponseUtil.xlsx(response, fileName, bos.toByteArray());
    }

    private File getTemplateFile() {
        String path = StringUtil.joinPaths(appConfig.getBasePath(),
                appConfig.getExportExcelTemplate(),
                "Ibnrcalc_step_tpl.xlsx");
        File file = new File(path);
        if (!file.exists()) {
            throw new RuntimeException("There is no configuration template file");
        }
        return file;
    }

    private void exportOne(Workbook wb, AtrBussIbnrcalcStepVo vo) {
        Sheet sheetSource = wb.getSheet("tpl##");
        Sheet sheet = copySheet(sheetSource, vo.getLoaCode() + "-" + vo.getPortfolioNo());

        handleBase(sheet, vo);
        handleAccDev("paid", sheet, vo);
        handleAccDev("os", sheet, vo);
        handleInflation(sheet, vo);
        handleAccDev("paid_incr", sheet, vo);
        handleAccDev("paid_cumulative", sheet, vo);
        handleAccDev("dev_factor", sheet, vo);
        handleDev(sheet, vo);
        handleAcc("acc_amount", sheet, vo);
        handleAcc("lr", sheet, vo);
        handleAcc("cl", sheet, vo);
        handleAcc("bf", sheet, vo);

        // Step4
        handleClaim("claim", sheet, vo);
        handleUndiscountCashFlow("clUn", sheet, vo);
        handleTimeValueDiscount("clTVD", sheet, vo);
        handleFinalTrace("clFinal", sheet, vo);

        handleUndiscountCashFlow("lrUn", sheet, vo);
        handleTimeValueDiscount("lrTVD", sheet, vo);
        handleFinalTrace("lrFinal", sheet, vo);

        handleUndiscountCashFlow("bfUn", sheet, vo);
        handleTimeValueDiscount("bfTVD", sheet, vo);
        handleFinalTrace("bfFinal", sheet, vo);
        cleanMark(sheet);
    }

    private void handleBase(Sheet sheet, AtrBussIbnrcalcStepVo vo) {
        setCellValue(sheet, 0, vo.getEntityCode() + " -- " + vo.getEntityName());
        setCellValue(sheet, 1, vo.getExtractionIntervalName());
        setCellValue(sheet, 2, String.valueOf(vo.getExtractionZones()));
        setCellValue(sheet, 3, vo.getExtractionDeadline());
        setCellValue(sheet, 4, vo.getIbnrTypeName());
        setCellValue(sheet, 5, vo.getLoaCode() + " -- " + vo.getLoaName());
        setCellValue(sheet, 6, vo.getPortfolioNo());
        setCellValue(sheet, 7, vo.getCreateTime());
    }

    private void setCellValue(Sheet sheet, int rowIndex, Object value) {
        Cell cell = sheet.getRow(rowIndex).getCell(1, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
        if (value == null) {
            cell.setCellValue((String) null);
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Date) {
            cell.setCellValue((Date) value);
        } else if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else {
            throw new IllegalArgumentException("Unsupport type " + value.getClass().getName());
        }
    }

    private void handleDev(Sheet sheet, AtrBussIbnrcalcStepVo vo) {
        int markIndex = getMarkRowIndex(sheet, "#{dev_data}");

        List<Long> devNos = vo.getDevNos();
        int size = devNos.size();

        for (int i = 0; i < 11; i++) {
            if (i == 0) {
                addMergedRegion(sheet, markIndex + i, 1, size);
            } else if (i == 2 || i == 7) {
                addMergedRegion(sheet, markIndex + i, 0, size + 1);
            } else if (i != 1 && i < 9) {
                copyCell(sheet, markIndex + i, 1, size - 1);
            } else {
                copyCell(sheet, markIndex + i, 1, size);
            }
        }

        for (AtrBussIbnrcalcStepDevDataVo devVo : vo.getDevData()) {
            int cellIndex = 1 + devVo.getDevNo().intValue();
            setCellValue(sheet, markIndex + 1, cellIndex, new BigDecimal(devVo.getDevNo()));
            setCellValue(sheet, markIndex + 3, cellIndex, devVo.getAvSimple());
            setCellValue(sheet, markIndex + 4, cellIndex, devVo.getAvWeighted());
            setCellValue(sheet, markIndex + 5, cellIndex, devVo.getAvLast3());
            setCellValue(sheet, markIndex + 6, cellIndex, devVo.getAvLast2());
            setCellValue(sheet, markIndex + 8, cellIndex, devVo.getSlCurrent());
            setCellValue(sheet, markIndex + 9, cellIndex, devVo.getSlCumulative());
            setCellValue(sheet, markIndex + 10, cellIndex, devVo.getSlExpectedReportedRatio());
        }
    }

    private void handleClaim(String mark, Sheet sheet, AtrBussIbnrcalcStepVo vo) {
        int markIndex = getMarkRowIndex(sheet, "#{" + mark + "}");
        AtrBussIbnrcalcClaimVo atrBussIbnrcalcClaimVo = (AtrBussIbnrcalcClaimVo) vo.getAtrBussIbnrcalcStep4DataVo().get("claimInfo");
        CellStyle cellStyle = getCellStyle(sheet, 1);
        CellStyle radioCellStyle = getCellStyle(sheet, 3);
        setCellStyleAndValue(sheet, markIndex + 2, 0, atrBussIbnrcalcClaimVo.getSettledFee(), BigDecimal.ZERO, cellStyle);
        setCellStyleAndValue(sheet, markIndex + 2, 1, atrBussIbnrcalcClaimVo.getSettled(), BigDecimal.ZERO, cellStyle);
        setCellStyleAndValue(sheet, markIndex + 2, 2, atrBussIbnrcalcClaimVo.getOs(), BigDecimal.ZERO, cellStyle);
        setCellStyleAndValue(sheet, markIndex + 2, 3, atrBussIbnrcalcClaimVo.getRatio().divide(BigDecimal.valueOf(100)), BigDecimal.ZERO, radioCellStyle);

    }

    private void handleUndiscountCashFlow(String mark, Sheet sheet, AtrBussIbnrcalcStepVo vo) {
        int markIndex = getMarkRowIndex(sheet, "#{" + mark + "}");
        AtrBussIbnrcalcStep4DataVo step4DataVo = (AtrBussIbnrcalcStep4DataVo) vo.getAtrBussIbnrcalcStep4DataVo().get(mark.substring(0, 2).toUpperCase());
        List<AtrBussIbnrcalcReportClaimAmountVo> atrBussIbnrcalcReportClaimAmountVoList = step4DataVo.getAtrBussIbnrcalcReportClaimAmountVoList();
        // 未来时间节点与出现时间节点的数量一致
        addMergedRegion(sheet, markIndex, 0, atrBussIbnrcalcReportClaimAmountVoList.size() + 4);
        copyCell(sheet, markIndex + 1, 3, 2);
        addMergedRegion(sheet, markIndex + 1, 4, atrBussIbnrcalcReportClaimAmountVoList.size() - 1);
        sheet.getRow(markIndex + 1).getCell(4).setCellValue("Reported in");
        Set<String> futureNodes = atrBussIbnrcalcReportClaimAmountVoList.get(0).getFutureDataInfo().keySet();
        CellStyle cellStyleTeble2FutureNode = getCellStyle(sheet, 19);
        int offset = 0;
        for (String futureNode : futureNodes) {
            Row row = sheet.getRow(markIndex + 2);
            if (row != null) {
                Cell cell = row.getCell(4 + offset, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                offset = offset + 1;
                cell.setCellValue(futureNode);
                cell.setCellStyle(cellStyleTeble2FutureNode);
            }
        }
        // 添加SUM
        CellRangeAddress region = new CellRangeAddress(markIndex + 1, markIndex + 2, 4 + offset, 4 + offset);
        sheet.addMergedRegion(region);
        Row row = sheet.getRow(markIndex + 1);
        if (row != null) {
            Cell cell = row.getCell(4 + offset, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
            cell.setCellValue("SUM");
            cell.setCellStyle(cellStyleTeble2FutureNode);
        }
        //  遍历acidentNode数据
        copyRow(sheet, markIndex + 3, atrBussIbnrcalcReportClaimAmountVoList.size());
        CellStyle accidentNodeCellStyle = getCellStyle(sheet, 13);
        CellStyle moneyBold = getCellStyle(sheet,21);
        CellStyle moneyCellStyle = getCellStyle(sheet, 1);
        for (int i = 0; i < atrBussIbnrcalcReportClaimAmountVoList.size() ; i++) {
            AtrBussIbnrcalcReportClaimAmountVo atrBussIbnrcalcReportClaimAmountVo = atrBussIbnrcalcReportClaimAmountVoList.get(i);
            Row tempRow = sheet.getRow(markIndex + 3 + i);
            if (tempRow != null) {
                Cell cell = tempRow.getCell(0, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                cell.setCellValue(atrBussIbnrcalcReportClaimAmountVo.getAccidentNode());
                cell.setCellStyle("TOTAL".equals(atrBussIbnrcalcReportClaimAmountVo.getAccidentNode())? cellStyleTeble2FutureNode :accidentNodeCellStyle);
            }
            setCellStyleAndValue(sheet, markIndex + 3 + i, 1, atrBussIbnrcalcReportClaimAmountVo.getIbnr(), BigDecimal.ZERO, !"TOTAL".equals(atrBussIbnrcalcReportClaimAmountVo.getAccidentNode())?moneyCellStyle:moneyBold);
            setCellStyleAndValue(sheet, markIndex + 3 + i, 2, atrBussIbnrcalcReportClaimAmountVo.getOs(), BigDecimal.ZERO, !"TOTAL".equals(atrBussIbnrcalcReportClaimAmountVo.getAccidentNode())?moneyCellStyle:moneyBold);
            setCellStyleAndValue(sheet, markIndex + 3 + i, 3, atrBussIbnrcalcReportClaimAmountVo.getUnpaidAmount(), BigDecimal.ZERO, !"TOTAL".equals(atrBussIbnrcalcReportClaimAmountVo.getAccidentNode())?moneyCellStyle:moneyBold);
            Collection<BigDecimal> values = atrBussIbnrcalcReportClaimAmountVo.getFutureDataInfo().values();
            ArrayList<BigDecimal> bigDecimals = new ArrayList<>(values);
            for (int j = 0; j < bigDecimals.size(); j++) {
                setCellStyleAndValue(sheet, markIndex + 3 + i, 4 + j, bigDecimals.get(j), BigDecimal.ZERO, !"TOTAL".equals(atrBussIbnrcalcReportClaimAmountVo.getAccidentNode())?moneyCellStyle:moneyBold);
                if (j == bigDecimals.size() - 1) {
                    setCellStyleAndValue(sheet, markIndex + 3 + i, 5 + j, atrBussIbnrcalcReportClaimAmountVo.getSum(), BigDecimal.ZERO, !"TOTAL".equals(atrBussIbnrcalcReportClaimAmountVo.getAccidentNode())?moneyCellStyle:moneyBold);
                }
            }
        }


    }

    private void handleTimeValueDiscount(String mark, Sheet sheet, AtrBussIbnrcalcStepVo vo) {
        int markIndex = getMarkRowIndex(sheet, "#{" + mark + "}");
        addMergedRegion(sheet, markIndex, 0, vo.getDevNos().size() + 1);
        AtrBussIbnrcalcStep4DataVo step4DataVo = (AtrBussIbnrcalcStep4DataVo) vo.getAtrBussIbnrcalcStep4DataVo().get(mark.substring(0, 2).toUpperCase());
        List<String> descTable2 = (List<String>) vo.getAtrBussIbnrcalcStep4DataVo().get("descTable2");
        CellStyle descTable2CellStyle = getCellStyle(sheet, 15);
        CellStyle ratioChange = getCellStyle(sheet, 9);
        CellStyle ratioInf = getCellStyle(sheet, 3);
        CellStyle money = getCellStyle(sheet, 1);
        // 初始化单元格
        copyRow(sheet, markIndex + 1, descTable2.size());
        List<AtrBussIbnrcalcTVDInfoVo> atrBussIbnrcalcTVDiscountVos = step4DataVo.getAtrBussIbnrcalcTVDiscountVos();
        String[] fieldName = new String[]{"devNo", "slExpectedReportedRatio", "interestRatio", "timePeriod", "yearMonth", "discountUnpaid", "timeValueDiscount"};
        addMergedRegion(sheet, markIndex + descTable2.size() + 1, 0, vo.getDevNos().size() + 1);
        //对数据进行填充,先遍历列，再遍历行
        for (int i = 0; i < vo.getDevNos().size() + 2; i++) {
            for (int j = 0; j < descTable2.size() + 1; j++) {
                if (j == 4)
                    continue;
                if (i == 0) {
                    Row row = sheet.getRow(markIndex + j + 1);
                    if (row != null) {
                        Cell cell = row.getCell(i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                        if (j > 4) {
                            cell.setCellValue(descTable2.get(j - 1));
                        } else {
                            cell.setCellValue(descTable2.get(j));
                        }
                        cell.setCellStyle(descTable2CellStyle);
                    }
                } else {
                    AtrBussIbnrcalcTVDInfoVo atrBussIbnrcalcTVDInfoVo = atrBussIbnrcalcTVDiscountVos.get(i - 1);
                    if (j == 0) {
                        Integer fieldValue = (Integer) ClassUtil.getFieldValue(atrBussIbnrcalcTVDInfoVo, fieldName[j]);
                        Row row = sheet.getRow(markIndex + 1);
                        if (row != null && fieldValue != null) {
                            Cell cell = row.getCell(i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                            cell.setCellValue(fieldValue);
                        }
                    }
                    if (j == 1 || j == 2) {
                        BigDecimal fieldValue = (BigDecimal) ClassUtil.getFieldValue(atrBussIbnrcalcTVDInfoVo, fieldName[j]);
                        if (fieldValue != null)
                            setCellStyleAndValue(sheet, markIndex + j + 1, i, fieldValue.divide(BigDecimal.valueOf(100)), BigDecimal.ZERO, j == 1 ? ratioInf : ratioChange);
                    }
                    if (j == 3) {
                        BigDecimal fieldValue = (BigDecimal) ClassUtil.getFieldValue(atrBussIbnrcalcTVDInfoVo, fieldName[j]);
                        if (fieldValue != null)
                            setCellValue(sheet, markIndex + j + 1, i, fieldValue, BigDecimal.ZERO);
                    }
                    if (j == 5) {
                        String fieldValue = (String) ClassUtil.getFieldValue(atrBussIbnrcalcTVDInfoVo, fieldName[j - 1]);
                        Row row = sheet.getRow(markIndex + j + 1);
                        if (row != null && fieldValue != null) {
                            Cell cell = row.getCell(i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                            cell.setCellValue(fieldValue);
                            cell.setCellStyle(descTable2CellStyle);
                        }
                        if (vo.getDevNos().size() + 1 == i) {
                            Cell cell = row.getCell(i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                            cell.setCellValue("Sum");
                            cell.setCellStyle(descTable2CellStyle);
                        }
                    }
                    if (j == 6) {
                        BigDecimal fieldValue = (BigDecimal) ClassUtil.getFieldValue(atrBussIbnrcalcTVDInfoVo, fieldName[j - 1]);
                        Row row = sheet.getRow(markIndex + j + 1);
                        if (row != null && fieldValue != null) {
                            Cell cell = row.getCell(i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                            cell.setCellValue(fieldValue.doubleValue());
                            cell.setCellStyle(money);
                        }
                    }
                    //  最后一次
                    if (vo.getDevNos().size() + 1 == i && j == 7) {
                        BigDecimal fieldValue = (BigDecimal) ClassUtil.getFieldValue(atrBussIbnrcalcTVDInfoVo, fieldName[j - 1]);
                        Row row = sheet.getRow(markIndex + j + 1);
                        if (row != null) {
                            Cell cell = row.getCell(i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                            cell.setCellValue(fieldValue.divide(BigDecimal.valueOf(100)).doubleValue());
                            cell.setCellStyle(ratioInf);
                        }
                    }
                }
            }

        }
    }

    private void handleFinalTrace(String mark, Sheet sheet, AtrBussIbnrcalcStepVo vo) {
        int markIndex = getMarkRowIndex(sheet, "#{" + mark + "}");
        CellStyle ratioChange = getCellStyle(sheet, 9);
        CellStyle traceTypeCellStyle = getCellStyle(sheet, 17);
        CellStyle money = getCellStyle(sheet, 1);
        AtrBussIbnrcalcStep4DataVo step4DataVo = (AtrBussIbnrcalcStep4DataVo) vo.getAtrBussIbnrcalcStep4DataVo().get(mark.substring(0, 2).toUpperCase());
        List<AtrBussIbnrcalcFinalTraceVo> atrBussIbnrcalcFinalTraceVos = step4DataVo.getAtrBussIbnrcalcFinalTraceVos();
        String[] fieldNames = new String[]{"traceType", "factor", "amount", "ibnr", "os"};
        copyRow(sheet, markIndex, atrBussIbnrcalcFinalTraceVos.size()-2);
        for (int i = 0; i < atrBussIbnrcalcFinalTraceVos.size() - 2; i++) {
            AtrBussIbnrcalcFinalTraceVo atrBussIbnrcalcFinalTraceVo = atrBussIbnrcalcFinalTraceVos.get(i);
            for (int j = 0; j < fieldNames.length; j++) {
                // traceType
                if (j == 0) {
                    String stringValue = atrBussIbnrcalcFinalTraceVo.getTraceType();
                    Row row = sheet.getRow(markIndex + i + 1);
                    if (stringValue != null && row != null) {
                        Cell cell = row.getCell(j, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                        cell.setCellValue(stringValue);
                        cell.setCellStyle(traceTypeCellStyle);
                    }
                } else if (j == 1 && (i == 2 || i == 4 || i == 6)) {
                    BigDecimal factor = (BigDecimal) ClassUtil.getFieldValue(atrBussIbnrcalcFinalTraceVo, fieldNames[j]);
                    if (factor != null)
                        setCellStyleAndValue(sheet, markIndex + i + 1, j, factor.divide(BigDecimal.valueOf(100)), BigDecimal.ZERO, ratioChange);
                } else {
                    BigDecimal factor = (BigDecimal) ClassUtil.getFieldValue(atrBussIbnrcalcFinalTraceVo, fieldNames[j]);
                    setCellStyleAndValue(sheet, markIndex + i + 1, j, factor, null, money);
                }
            }
        }
        String methodType = mark.substring(0, 2);
        int markIbnrIndex = getMarkRowIndex(sheet, "#{" + methodType+"ibnr" + "}");
        int markOsIndex = getMarkRowIndex(sheet, "#{" + methodType+"os" + "}");
        setCellValue(sheet,markIbnrIndex,2,atrBussIbnrcalcFinalTraceVos.get(atrBussIbnrcalcFinalTraceVos.size()-2).getAmount());
        setCellValue(sheet,markOsIndex,2,atrBussIbnrcalcFinalTraceVos.get(atrBussIbnrcalcFinalTraceVos.size()-1).getAmount());

    }

    private void handleAcc(String mark, Sheet sheet, AtrBussIbnrcalcStepVo vo) {
        int markIndex = getMarkRowIndex(sheet, "#{" + mark + "}");

        CellStyle moneyChangeStyle0 = getCellStyle(sheet, 5);
        CellStyle moneyChangeStyle1 = getCellStyle(sheet, 7);
        CellStyle ratioChangeStyle0 = getCellStyle(sheet, 9);
        CellStyle ratioChangeStyle1 = getCellStyle(sheet, 11);

        List<String> accidentNodes = vo.getAccidentNodes();
        int size = accidentNodes.size();
        sheet.getRow(markIndex).getCell(0).setCellValue(vo.getAccidentNodeDesc());
        copyRow(sheet, markIndex + 2, size);
        for (int i = 0; i < size; i++) {
            sheet.getRow(markIndex + 2 + i).getCell(0).setCellValue(accidentNodes.get(i));
        }

        BigDecimal zero = BigDecimal.ZERO;

        for (AtrBussIbnrcalcStepAccDataVo accVo : vo.getAccData()) {
            String accidentNode = accVo.getAccidentNode();
            for (int r = 0; r < size + 1; r++) {
                int rowIndex = markIndex + 2 + r;
                Row row = sheet.getRow(rowIndex);
                if (accidentNode.equals(row.getCell(0).getStringCellValue())) {
                    boolean isTotal = "Total".equals(accidentNode);
                    if ("acc_amount".equals(mark)) {
                        setCellValue(sheet, rowIndex, 1, accVo.getEdPremiumOri(), zero);
                        setCellValue(sheet, rowIndex, 2, accVo.getEdPremium(), zero);
                        setCellValue(sheet, rowIndex, 3, accVo.getSetteldAmount(), zero);
                        setCellValue(sheet, rowIndex, 4, accVo.getOsAmount(), zero);
                        setCellValue(sheet, rowIndex, 5, accVo.getReportedAmount(), zero);
                        if (!isTotal) {
                            if (notEquals(accVo.getEdPremiumOri(), accVo.getEdPremium())) {
                                row.getCell(2).setCellStyle(moneyChangeStyle0);
                            }
                        }
                    } else if ("lr".equals(mark)) {
                        setCellValue(sheet, rowIndex, 1, accVo.getEdPremium(), zero);
                        setCellValue(sheet, rowIndex, 2, accVo.getSetteldAmount(), zero);
                        setCellValue(sheet, rowIndex, 3, accVo.getOsAmount(), zero);
                        setCellValue(sheet, rowIndex, 4, accVo.getReportedAmount(), zero);
                        setCellValue(sheet, rowIndex, 5, accVo.getReportedLossRatio(), isTotal ? null : zero);
                        setCellValue(sheet, rowIndex, 6, accVo.getLrExpectedLossRatio(), isTotal ? null : zero);
                        setCellValue(sheet, rowIndex, 7, accVo.getLrUltimateLoss(), zero);
                        setCellValue(sheet, rowIndex, 8, accVo.getLrIbnr());

                        if (!isTotal) {
                            if (notEquals(accVo.getEdPremiumOri(), accVo.getEdPremium())) {
                                row.getCell(1).setCellStyle(moneyChangeStyle1);
                            }
                            if (notEquals(accVo.getReportedLossRatio(), accVo.getLrExpectedLossRatio())) {
                                row.getCell(6).setCellStyle(ratioChangeStyle0);
                            }
                        }
                    } else if ("cl".equals(mark)) {
                        setCellValue(sheet, rowIndex, 2, accVo.getSetteldAmount(), zero);
                        setCellValue(sheet, rowIndex, 3, accVo.getOsAmount(), zero);
                        setCellValue(sheet, rowIndex, 4, accVo.getReportedAmount(), zero);
                        setCellValue(sheet, rowIndex, 6, accVo.getExpectedReportedRatio(), isTotal ? null : zero);
                        setCellValue(sheet, rowIndex, 7, accVo.getClProjReportedUltimate(), zero);
                        setCellValue(sheet, rowIndex, 8, accVo.getClIbnr(), zero);
                    } else if ("bf".equals(mark)) {
                        setCellValue(sheet, rowIndex, 1, accVo.getEdPremium(), zero);
                        setCellValue(sheet, rowIndex, 2, accVo.getSetteldAmount(), zero);
                        setCellValue(sheet, rowIndex, 3, accVo.getOsAmount(), zero);
                        setCellValue(sheet, rowIndex, 4, accVo.getReportedAmount(), zero);
                        setCellValue(sheet, rowIndex, 5, accVo.getBfExpectedLossRatio(), isTotal ? null : zero);
                        setCellValue(sheet, rowIndex, 6, accVo.getExpectedReportedRatio(), isTotal ? null : zero);
                        setCellValue(sheet, rowIndex, 7, accVo.getBfProjReportedUltimate(), zero);
                        setCellValue(sheet, rowIndex, 8, accVo.getBfIbnr(), zero);
                        if (!isTotal) {
                            if (notEquals(accVo.getEdPremiumOri(), accVo.getEdPremium())) {
                                row.getCell(1).setCellStyle(moneyChangeStyle1);
                            }
                            if (notEquals(accVo.getReportedLossRatio(), accVo.getBfExpectedLossRatio())) {
                                row.getCell(5).setCellStyle(ratioChangeStyle1);
                            }
                        }
                    }
                }
            }
        }
    }

    private boolean notEquals(BigDecimal a, BigDecimal b) {
        if (a == null && b == null) {
            return false;
        }
        if (a == null || b == null) {
            return true;
        }
        return a.compareTo(b) != 0;
    }

    private void setCellValue(Sheet sheet, int rowIndex, int cellIndex,
                              BigDecimal value, BigDecimal defalutValue) {
        if (value == null) {
            if (defalutValue != null) {
                value = defalutValue;
            }
        }
        if (value != null) {
            Row row = sheet.getRow(rowIndex);
            if (row != null) {
                Cell cell = row.getCell(cellIndex, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                cell.setCellValue(value.doubleValue());
            }
        }
    }

    private void setCellStyleAndValue(Sheet sheet, int rowIndex, int cellIndex,
                                      BigDecimal value, BigDecimal defalutValue, CellStyle cellStyle) {
        if (value == null) {
            if (defalutValue != null) {
                value = defalutValue;
            }
        }
        if (value != null) {
            Row row = sheet.getRow(rowIndex);
            if (row != null) {
                Cell cell = row.getCell(cellIndex, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                cell.setCellValue(value.doubleValue());
                cell.setCellStyle(cellStyle);
            }
        }
    }

    private void setCellValue(Sheet sheet, int rowIndex, int cellIndex, BigDecimal value) {
        setCellValue(sheet, rowIndex, cellIndex, value, null);
    }


    private void handleAccDev(String mark, Sheet sheet, AtrBussIbnrcalcStepVo vo) {
        int markIndex = getMarkRowIndex(sheet, "#{" + mark + "}");
        CellStyle emptyCellStyle = getEmptyCellStyle(sheet);

        int accidentNodeSize = vo.getAccidentNodes().size();
        int devNoSize = vo.getDevNos().size();

        addMergedRegion(sheet, markIndex, 0, devNoSize + 1);
        addMergedRegion(sheet, markIndex + 1, 1, devNoSize);
        // 复制发展期的 cell;  paid 是第1次这么做， 在此调整下 cell 的列宽
        copyCell(sheet, markIndex + 2, 1, devNoSize, "paid".equals(mark));
        copyCell(sheet, markIndex + 3, 1, devNoSize);
        copyRow(sheet, markIndex + 3, accidentNodeSize);

        Row row;

        row = sheet.getRow(markIndex + 1);
        row.getCell(0).setCellValue(vo.getAccidentNodeDesc());

        row = sheet.getRow(markIndex + 2);
        for (int i = 0; i < devNoSize; i++) {
            row.getCell(1 + i).setCellValue(vo.getDevNos().get(i));
        }

        for (int i = 0; i < accidentNodeSize; i++) {
            sheet.getRow(markIndex + 3 + i).getCell(0).setCellValue(vo.getAccidentNodes().get(i));
        }

        List<AtrBussIbnrcalcStepAccDevDataVo> accDevData = vo.getAccDevData();
        Row devRow = sheet.getRow(markIndex + 2);
        for (int r = 0; r < accidentNodeSize; r++) {
            row = sheet.getRow(markIndex + 3 + r);
            for (int c = 0; c < devNoSize; c++) {
                long devNo = (long) devRow.getCell(1 + c).getNumericCellValue();
                String accidentNode = row.getCell(0).getStringCellValue();
                Cell cell = row.getCell(1 + c, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);

                if (mark.startsWith("paid")) {
                    // 第 N 行， 倒数第 N - 1 及后面的格子为空
                    if (c > devNoSize - 1 - r) {
                        cell.setCellStyle(emptyCellStyle);
                        continue;
                    }
                } else if (mark.equals("os")) {
                    // 第 N 行， 只有倒数第 N - 1 个格子不为空格子
                    if (c != devNoSize - 1 - r) {
                        cell.setCellStyle(emptyCellStyle);
                        continue;
                    }
                } else if (mark.equals("dev_factor")) {
                    // 第 N 行， 倒数第 N - 2 及后面的格子为空
                    if (c > devNoSize - 2 - r) {
                        cell.setCellStyle(emptyCellStyle);
                        continue;
                    }
                }

                for (AtrBussIbnrcalcStepAccDevDataVo accDevDataVo : accDevData) {
                    if (accDevDataVo.getDevNo() == devNo && accDevDataVo.getAccidentNode().equals(accidentNode)) {
                        BigDecimal value = null;
                        if ("paid".equals(mark)) {
                            value = accDevDataVo.getSettledAmountOri();
                        } else if ("os".equals(mark)) {
                            value = accDevDataVo.getOsAmount();
                        } else if ("paid_incr".equals(mark)) {
                            value = accDevDataVo.getSettledAmount();
                        } else if ("paid_cumulative".equals(mark)) {
                            value = accDevDataVo.getSettledAmountCumulative();
                        } else if ("dev_factor".equals(mark)) {
                            value = accDevDataVo.getDevFactor();
                        }
                        if (value != null) {
                            cell.setCellValue(value.doubleValue());
                        }
                        break;
                    }
                }
            }
        }
    }

    private void handleInflation(Sheet sheet, AtrBussIbnrcalcStepVo vo) {
        int markIndex = getMarkRowIndex(sheet, "#{inf}");
        CellStyle infStyle = getCellStyle(sheet, 3);
        AtrInflationRatioOverviewVo infVo = vo.getInfRatioData();
        int[] years = infVo.getYears();
        BigDecimal[][] ratios = infVo.getRatios();

        addMergedRegion(sheet, markIndex, 0, years.length + 1);
        addMergedRegion(sheet, markIndex + 1, 1, years.length);
        copyCell(sheet, markIndex + 2, 1, years.length);
        copyRow(sheet, markIndex + 3, ratios.length);

        Row yearRow = sheet.getRow(markIndex + 2);
        for (int c = 0; c < years.length; c++) {
            yearRow.getCell(c + 1).setCellValue(years[c]);
        }

        for (int r = 0; r < ratios.length; r++) {
            Row row = sheet.getRow(markIndex + 3 + r);
            for (int c = -1; c < ratios[r].length; c++) {
                Cell cell = row.getCell(c + 1, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                if (c == -1) {
                    cell.setCellValue(String.valueOf(years[r]));
                } else {
                    BigDecimal value = ratios[r][c];
                    if (value != null) {
                        cell.setCellStyle(infStyle);
                        cell.setCellValue(value.doubleValue());
                    }
                }
            }
        }
    }

    private Sheet copySheet(Sheet sheetSource, String name) {
        Workbook wb = sheetSource.getWorkbook();
        Sheet sheet = wb.cloneSheet(wb.getSheetIndex(sheetSource));
        wb.setSheetName(wb.getSheetIndex(sheet), name);
        return sheet;
    }

    private void copyRow(Sheet sheet, int rowIndex, int len) {
        if ( len == 1 )
            return;
        sheet.shiftRows(rowIndex + 1, sheet.getLastRowNum(), len - 1);
        Row startRow = sheet.getRow(rowIndex);
        for (int i = 1; i < len; i++) {
            Row row = sheet.createRow(rowIndex + i);
            for (int k = 0; k < startRow.getLastCellNum(); k++) {
                Cell cellSource = startRow.getCell(k);
                if (cellSource != null) {
                    Cell cell = row.getCell(k, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                    cell.setCellStyle(cellSource.getCellStyle());
                }
            }
        }
    }

    private void copyCell(Sheet sheet, int rowIndex, int cellIndex, int len) {
        copyCell(sheet, rowIndex, cellIndex, len, false);
    }

    private void copyCell(Sheet sheet, int rowIndex, int cellIndex, int len, boolean copyWidth) {
        Row row = sheet.getRow(rowIndex);
        Cell startCell = row.getCell(cellIndex);
        int columnWidth = sheet.getColumnWidth(startCell.getColumnIndex());
        for (int i = 1; i < len; i++) {
            Cell cell = row.getCell(cellIndex + i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
            cell.setCellStyle(startCell.getCellStyle());
            if (copyWidth) {
                sheet.setColumnWidth(cell.getColumnIndex(), columnWidth);
            }
        }
    }

    private void addMergedRegion(Sheet sheet, int rowIndex, int cellIndex, int len) {
        if ( len == 1)
            return;
        copyCell(sheet, rowIndex, cellIndex, len);
        CellRangeAddress region =
                new CellRangeAddress(rowIndex, rowIndex, cellIndex, cellIndex + len - 1);
        sheet.addMergedRegion(region);
    }

    private int getMarkRowIndex(Sheet sheet, String mark) {
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                Cell cell = row.getCell(0);
                if (cell != null && cell.getCellType().equals(CellType.STRING)) {
                    String value = StringUtils.trimToEmpty(cell.getStringCellValue());
                    if (value.startsWith(mark)) {
                        return i;
                    }
                }
            }
        }

        throw new RuntimeException("Not found " + mark);
    }

    private void deleteTplSheet(Workbook wb) {
        Iterator<Sheet> itr = wb.sheetIterator();
        List<Sheet> sheets = new ArrayList<>();
        while (itr.hasNext()) {
            Sheet sheet = itr.next();
            if (sheet.getSheetName().startsWith("tpl##")) {
                sheets.add(sheet);
            }
        }

        for (Sheet sheet : sheets) {
            wb.removeSheetAt(wb.getSheetIndex(sheet));
        }
    }

    private void cleanMark(Sheet sheet) {
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                Cell cell = row.getCell(0);
                if (cell != null && cell.getCellType().equals(CellType.STRING)) {
                    String value = StringUtils.trimToEmpty(cell.getStringCellValue());
                    if (value.matches("#\\{.+?}.*")) {
                        value = value.replaceFirst("#\\{.+?}", "").trim();
                        cell.setCellValue(value);
                    }
                }
            }
        }
    }

    private CellStyle getCellStyle(Sheet sheet, int rowIndex) {
        Sheet styleSheet = sheet.getWorkbook().getSheet("tpl##style");
        return styleSheet.getRow(rowIndex).getCell(2).getCellStyle();
    }

    private CellStyle getEmptyCellStyle(Sheet sheet) {
        Sheet styleSheet = sheet.getWorkbook().getSheet("tpl##style");
        Row row = styleSheet.getRow(1000);
        if (row == null) {
            row = styleSheet.createRow(1000);
        }
        return row.getCell(100, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK).getCellStyle();
    }
}
