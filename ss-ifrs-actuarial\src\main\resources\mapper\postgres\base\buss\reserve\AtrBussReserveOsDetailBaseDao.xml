<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2022-02-17 17:42:26 -->
<!-- Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.reserve.AtrBussReserveOsDetailDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveOsDetail">
    <result column="os_detail_id" property="osDetailId" jdbcType="NUMERIC" />
    <result column="reserve_os_id" property="reserveOsId" jdbcType="NUMERIC" />
    <result column="loa_code" property="loaCode" jdbcType="VARCHAR" />
    <result column="risk_code" property="riskCode" jdbcType="VARCHAR" />
    <result column="treaty_class" property="treatyClass" jdbcType="VARCHAR" />
    <result column="claim_no" property="claimNo" jdbcType="VARCHAR" />
    <result column="policy_no" property="policyNo" jdbcType="VARCHAR" />
    <result column="endorse_seq_no" property="endorseSeqNo" jdbcType="VARCHAR" />
    <result column="endorse_no" property="endorseNo" jdbcType="VARCHAR" />
    <result column="business_source_code" property="businessSourceCode" jdbcType="VARCHAR" />
    <result column="reins_type" property="reinsType" jdbcType="VARCHAR" />
    <result column="damage_date" property="damageDate" jdbcType="TIMESTAMP" />
    <result column="start_date" property="startDate" jdbcType="DATE" />
    <result column="end_date" property="endDate" jdbcType="DATE" />
    <result column="CURRENCY_CODE" property="currencyCode" jdbcType="VARCHAR" />
    <result column="os_amount" property="osAmount" jdbcType="NUMERIC" />
    <result column="paid_amount" property="paidAmount" jdbcType="NUMERIC" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    os_detail_id, reserve_os_id, loa_code, risk_code, treaty_class, claim_no, policy_no,
    endorse_seq_no, endorse_no, business_source_code, reins_type, damage_date, start_date,
    end_date, CURRENCY_CODE, os_amount, paid_amount
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="osDetailId != null ">
          and os_detail_id = #{osDetailId,jdbcType=NUMERIC}
      </if>
      <if test="reserveOsId != null ">
          and reserve_os_id = #{reserveOsId,jdbcType=NUMERIC}
      </if>
      <if test="loaCode != null and loaCode != ''">
          and loa_code = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="riskCode != null and riskCode != ''">
          and risk_code = #{riskCode,jdbcType=VARCHAR}
      </if>
      <if test="treatyClass != null and treatyClass != ''">
          and treaty_class = #{treatyClass,jdbcType=VARCHAR}
      </if>
      <if test="claimNo != null and claimNo != ''">
          and claim_no = #{claimNo,jdbcType=VARCHAR}
      </if>
      <if test="policyNo != null and policyNo != ''">
          and policy_no = #{policyNo,jdbcType=VARCHAR}
      </if>
      <if test="endorseSeqNo != null and endorseSeqNo != ''">
          and endorse_seq_no = #{endorseSeqNo,jdbcType=VARCHAR}
      </if>
      <if test="endorseNo != null and endorseNo != ''">
          and endorse_no = #{endorseNo,jdbcType=VARCHAR}
      </if>
      <if test="businessSourceCode != null and businessSourceCode != ''">
          and business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="reinsType != null and reinsType != ''">
          and reins_type = #{reinsType,jdbcType=VARCHAR}
      </if>
      <if test="damageDate != null ">
          and damage_date = #{damageDate,jdbcType=TIMESTAMP}
      </if>
      <if test="startDate != null ">
          and start_date = #{startDate,jdbcType=DATE}
      </if>
      <if test="endDate != null ">
          and end_date = #{endDate,jdbcType=DATE}
      </if>
      <if test="currencyCode != null and currencyCode != ''">
          and CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="osAmount != null ">
          and os_amount = #{osAmount,jdbcType=NUMERIC}
      </if>
      <if test="paidAmount != null ">
          and paid_amount = #{paidAmount,jdbcType=NUMERIC}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.osDetailId != null ">
          and os_detail_id = #{condition.osDetailId,jdbcType=NUMERIC}
      </if>
      <if test="condition.reserveOsId != null ">
          and reserve_os_id = #{condition.reserveOsId,jdbcType=NUMERIC}
      </if>
      <if test="condition.loaCode != null and condition.loaCode != ''">
          and loa_code = #{condition.loaCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.riskCode != null and condition.riskCode != ''">
          and risk_code = #{condition.riskCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.treatyClass != null and condition.treatyClass != ''">
          and treaty_class = #{condition.treatyClass,jdbcType=VARCHAR}
      </if>
      <if test="condition.claimNo != null and condition.claimNo != ''">
          and claim_no = #{condition.claimNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.policyNo != null and condition.policyNo != ''">
          and policy_no = #{condition.policyNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.endorseSeqNo != null and condition.endorseSeqNo != ''">
          and endorse_seq_no = #{condition.endorseSeqNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.endorseNo != null and condition.endorseNo != ''">
          and endorse_no = #{condition.endorseNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.businessSourceCode != null and condition.businessSourceCode != ''">
          and business_source_code = #{condition.businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.reinsType != null and condition.reinsType != ''">
          and reins_type = #{condition.reinsType,jdbcType=VARCHAR}
      </if>
      <if test="condition.damageDate != null ">
          and damage_date = #{condition.damageDate,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.startDate != null ">
          and start_date = #{condition.startDate,jdbcType=DATE}
      </if>
      <if test="condition.endDate != null ">
          and end_date = #{condition.endDate,jdbcType=DATE}
      </if>
      <if test="condition.currencyCode != null and condition.currencyCode != ''">
          and CURRENCY_CODE = #{condition.currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.osAmount != null ">
          and os_amount = #{condition.osAmount,jdbcType=NUMERIC}
      </if>
      <if test="condition.paidAmount != null ">
          and paid_amount = #{condition.paidAmount,jdbcType=NUMERIC}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="osDetailId != null ">
          and os_detail_id = #{osDetailId,jdbcType=NUMERIC}
      </if>
      <if test="reserveOsId != null ">
          and reserve_os_id = #{reserveOsId,jdbcType=NUMERIC}
      </if>
      <if test="loaCode != null and loaCode != ''">
          and loa_code = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="riskCode != null and riskCode != ''">
          and risk_code = #{riskCode,jdbcType=VARCHAR}
      </if>
      <if test="treatyClass != null and treatyClass != ''">
          and treaty_class = #{treatyClass,jdbcType=VARCHAR}
      </if>
      <if test="claimNo != null and claimNo != ''">
          and claim_no = #{claimNo,jdbcType=VARCHAR}
      </if>
      <if test="policyNo != null and policyNo != ''">
          and policy_no = #{policyNo,jdbcType=VARCHAR}
      </if>
      <if test="endorseSeqNo != null and endorseSeqNo != ''">
          and endorse_seq_no = #{endorseSeqNo,jdbcType=VARCHAR}
      </if>
      <if test="endorseNo != null and endorseNo != ''">
          and endorse_no = #{endorseNo,jdbcType=VARCHAR}
      </if>
      <if test="businessSourceCode != null and businessSourceCode != ''">
          and business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="reinsType != null and reinsType != ''">
          and reins_type = #{reinsType,jdbcType=VARCHAR}
      </if>
      <if test="damageDate != null ">
          and damage_date = #{damageDate,jdbcType=TIMESTAMP}
      </if>
      <if test="startDate != null ">
          and start_date = #{startDate,jdbcType=DATE}
      </if>
      <if test="endDate != null ">
          and end_date = #{endDate,jdbcType=DATE}
      </if>
      <if test="currencyCode != null and currencyCode != ''">
          and CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="osAmount != null ">
          and os_amount = #{osAmount,jdbcType=NUMERIC}
      </if>
      <if test="paidAmount != null ">
          and paid_amount = #{paidAmount,jdbcType=NUMERIC}
      </if>
    </trim>
  </sql>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from atr_buss_reserve_os_detail
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveOsDetail">
    select 
    <include refid="Base_Column_List" />
    from atr_buss_reserve_os_detail
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from atr_buss_reserve_os_detail
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveOsDetail">
    <selectKey resultType="long" keyProperty="osDetailId" order="BEFORE">
      select nextval('atr_seq_buss_reserve_os_detail') as sequenceNo 
    </selectKey>
    insert into atr_buss_reserve_os_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="osDetailId != null">
        os_detail_id,
      </if>
      <if test="reserveOsId != null">
        reserve_os_id,
      </if>
      <if test="loaCode != null">
        loa_code,
      </if>
      <if test="riskCode != null">
        risk_code,
      </if>
      <if test="treatyClass != null">
        treaty_class,
      </if>
      <if test="claimNo != null">
        claim_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="endorseSeqNo != null">
        endorse_seq_no,
      </if>
      <if test="endorseNo != null">
        endorse_no,
      </if>
      <if test="businessSourceCode != null">
        business_source_code,
      </if>
      <if test="reinsType != null">
        reins_type,
      </if>
      <if test="damageDate != null">
        damage_date,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="currencyCode != null">
        CURRENCY_CODE,
      </if>
      <if test="osAmount != null">
        os_amount,
      </if>
      <if test="paidAmount != null">
        paid_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="osDetailId != null">
        #{osDetailId,jdbcType=NUMERIC},
      </if>
      <if test="reserveOsId != null">
        #{reserveOsId,jdbcType=NUMERIC},
      </if>
      <if test="loaCode != null">
        #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="riskCode != null">
        #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="treatyClass != null">
        #{treatyClass,jdbcType=VARCHAR},
      </if>
      <if test="claimNo != null">
        #{claimNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="endorseSeqNo != null">
        #{endorseSeqNo,jdbcType=VARCHAR},
      </if>
      <if test="endorseNo != null">
        #{endorseNo,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceCode != null">
        #{businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="reinsType != null">
        #{reinsType,jdbcType=VARCHAR},
      </if>
      <if test="damageDate != null">
        #{damageDate,jdbcType=TIMESTAMP},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=DATE},
      </if>
      <if test="currencyCode != null">
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="osAmount != null">
        #{osAmount,jdbcType=NUMERIC},
      </if>
      <if test="paidAmount != null">
        #{paidAmount,jdbcType=NUMERIC},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert into atr_buss_reserve_os_detail
     (os_detail_id, reserve_os_id, loa_code,
      risk_code, treaty_class, claim_no, 
      policy_no, endorse_seq_no, endorse_no, 
      business_source_code, reins_type, damage_date,
      start_date, end_date, CURRENCY_CODE,
      os_amount, paid_amount)
     values 
    <foreach collection="list" item="item" index="index" separator=",">
       (#{item.osDetailId,jdbcType=NUMERIC}, #{item.reserveOsId,jdbcType=NUMERIC}, #{item.loaCode,jdbcType=VARCHAR},
        #{item.riskCode,jdbcType=VARCHAR}, #{item.treatyClass,jdbcType=VARCHAR}, #{item.claimNo,jdbcType=VARCHAR}, 
        #{item.policyNo,jdbcType=VARCHAR}, #{item.endorseSeqNo,jdbcType=VARCHAR}, #{item.endorseNo,jdbcType=VARCHAR}, 
        #{item.businessSourceCode,jdbcType=VARCHAR}, #{item.reinsType,jdbcType=VARCHAR}, #{item.damageDate,jdbcType=TIMESTAMP},
        #{item.startDate,jdbcType=DATE}, #{item.endDate,jdbcType=DATE}, #{item.currencyCode,jdbcType=VARCHAR},
        #{item.osAmount,jdbcType=NUMERIC}, #{item.paidAmount,jdbcType=NUMERIC})
    </foreach>
  </insert>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveOsDetail">
    update atr_buss_reserve_os_detail
    <set>
      <if test="record.osDetailId != null">
        os_detail_id = #{record.osDetailId,jdbcType=NUMERIC},
      </if>
      <if test="record.reserveOsId != null">
        reserve_os_id = #{record.reserveOsId,jdbcType=NUMERIC},
      </if>
      <if test="record.loaCode != null">
        loa_code = #{record.loaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.riskCode != null">
        risk_code = #{record.riskCode,jdbcType=VARCHAR},
      </if>
      <if test="record.treatyClass != null">
        treaty_class = #{record.treatyClass,jdbcType=VARCHAR},
      </if>
      <if test="record.claimNo != null">
        claim_no = #{record.claimNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.endorseSeqNo != null">
        endorse_seq_no = #{record.endorseSeqNo,jdbcType=VARCHAR},
      </if>
      <if test="record.endorseNo != null">
        endorse_no = #{record.endorseNo,jdbcType=VARCHAR},
      </if>
      <if test="record.businessSourceCode != null">
        business_source_code = #{record.businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.reinsType != null">
        reins_type = #{record.reinsType,jdbcType=VARCHAR},
      </if>
      <if test="record.damageDate != null">
        damage_date = #{record.damageDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.startDate != null">
        start_date = #{record.startDate,jdbcType=DATE},
      </if>
      <if test="record.endDate != null">
        end_date = #{record.endDate,jdbcType=DATE},
      </if>
      <if test="record.currencyCode != null">
        CURRENCY_CODE = #{record.currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.osAmount != null">
        os_amount = #{record.osAmount,jdbcType=NUMERIC},
      </if>
      <if test="record.paidAmount != null">
        paid_amount = #{record.paidAmount,jdbcType=NUMERIC},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from atr_buss_reserve_os_detail
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveOsDetail">
    select count(1) from atr_buss_reserve_os_detail
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>