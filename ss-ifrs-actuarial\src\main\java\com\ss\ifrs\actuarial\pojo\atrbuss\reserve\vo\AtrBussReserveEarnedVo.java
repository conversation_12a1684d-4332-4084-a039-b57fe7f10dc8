/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-06-05 13:55:06
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-06-05 13:55:06<br/>
 * Description: Earned提取主表<br/>
 * Table Name: ATR_BUSS_RESERVE_EARNED<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "Earned提取主表")
public class AtrBussReserveEarnedVo implements Serializable {
    /**
     * Database column: ATR_BUSS_RESERVE_EARNED.RESERVE_EARNED_ID
     * Database remarks: RESERVE_EARNED_ID|Earned提取主表主键
     */
    @ApiModelProperty(value = "RESERVE_EARNED_ID|Earned提取主表主键", required = true)
    private Long reserveEarnedId;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED.VERSION_NO
     * Database remarks: VERSION_NO|版本号
     */
    @ApiModelProperty(value = "VERSION_NO|版本号", required = false)
    private String versionNo;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED.CENTER_ID
     * Database remarks: ENTITY_ID|业务单位id
     */
    @ApiModelProperty(value = "ENTITY_ID|业务单位id", required = false)
    private Integer entityId;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED.START_DATE
     * Database remarks: START_DATE|开始时间
     */
    @ApiModelProperty(value = "START_DATE|开始时间", required = false)
    private Date startDate;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED.END_DATE
     * Database remarks: END_DATE|终止时间
     */
    @ApiModelProperty(value = "END_DATE|终止时间", required = false)
    private Date endDate;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED.RISK_CLASS
     * Database remarks: RISK_CLASS|险类
     */
    @ApiModelProperty(value = "RISK_CLASS|险类", required = false)
    private String riskClassCode;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED.RISK_CODE
     * Database remarks: RISK_CODE|险种
     */
    @ApiModelProperty(value = "RISK_CODE|险种", required = false)
    private String riskCode;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED.DATA_SOURCE
     * Database remarks: DATA_SOURCE|TRIAL
     */
    @ApiModelProperty(value = "DATA_SOURCE|TRIAL", required = false)
    private String dataSource;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED.ATR_TYPE
     * Database remarks: ATR_TYPE|提取类型
     */
    @ApiModelProperty(value = "ATR_TYPE|提取类型", required = false)
    private String atrType;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED.CONFIRM_IS
     * Database remarks: CONFIRM_IS|是否确认
     */
    @ApiModelProperty(value = "CONFIRM_IS|是否确认", required = false)
    private String confirmIs;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED.CONFIRM_ID
     * Database remarks: CONFIRM_ID|确认人
     */
    @ApiModelProperty(value = "CONFIRM_ID|确认人", required = false)
    private Long confirmId;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED.CONFIRM_TIME
     * Database remarks: CONFIRM_TIME|确认时间
     */
    @ApiModelProperty(value = "CONFIRM_TIME|确认时间", required = false)
    private Date confirmTime;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED.CREATOR_ID
     * Database remarks: CREATOR_ID|创建人
     */
    @ApiModelProperty(value = "CREATOR_ID|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED.CREATE_TIME
     * Database remarks: CREATE_TIME|创建时间
     */
    @ApiModelProperty(value = "CREATE_TIME|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED.UPDATOR_ID
     * Database remarks: UPDATOR_ID|最后修改人
     */
    @ApiModelProperty(value = "UPDATOR_ID|最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: ATR_BUSS_RESERVE_EARNED.UPDATE_TIME
     * Database remarks: UPDATE_TIME|最后修改时间
     */
    @ApiModelProperty(value = "UPDATE_TIME|最后修改时间", required = false)
    private Date updateTime;

    private String entityCode;
    private String entityEName;
    private String entityCName;
    private String entityLName;

    private String classCName;
    private String classEName;
    private String classLName;
    private String creatorName;

    private List<String> columnList;
    private String columnSql;

    private Date extractStartDate;
    private Date extractEndDate;

    private static final long serialVersionUID = 1L;

    public Long getReserveEarnedId() {
        return reserveEarnedId;
    }

    public void setReserveEarnedId(Long reserveEarnedId) {
        this.reserveEarnedId = reserveEarnedId;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

    public Integer getEntityId() {
        return entityId;
    }

    public void setEntityId(Integer entityId) {
        this.entityId = entityId;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public String getRiskCode() {
        return riskCode;
    }

    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public String getAtrType() {
        return atrType;
    }

    public void setAtrType(String atrType) {
        this.atrType = atrType;
    }

    public String getConfirmIs() {
        return confirmIs;
    }

    public void setConfirmIs(String confirmIs) {
        this.confirmIs = confirmIs;
    }

    public Long getConfirmId() {
        return confirmId;
    }

    public void setConfirmId(Long confirmId) {
        this.confirmId = confirmId;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getEntityCode() {
        return entityCode;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public String getEntityEName() {
        return entityEName;
    }

    public void setEntityEName(String entityEName) {
        this.entityEName = entityEName;
    }

    public String getEntityCName() {
        return entityCName;
    }

    public void setEntityCName(String entityCName) {
        this.entityCName = entityCName;
    }

    public String getEntityLName() {
        return entityLName;
    }

    public void setEntityLName(String entityLName) {
        this.entityLName = entityLName;
    }

    public String getClassCName() {
        return classCName;
    }

    public void setClassCName(String classCName) {
        this.classCName = classCName;
    }

    public String getClassEName() {
        return classEName;
    }

    public void setClassEName(String classEName) {
        this.classEName = classEName;
    }

    public String getClassLName() {
        return classLName;
    }

    public void setClassLName(String classLName) {
        this.classLName = classLName;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public List<String> getColumnList() {
        return columnList;
    }

    public void setColumnList(List<String> columnList) {
        this.columnList = columnList;
    }

    public String getColumnSql() {
        return columnSql;
    }

    public void setColumnSql(String columnSql) {
        this.columnSql = columnSql;
    }

    public Date getExtractStartDate() {
        return extractStartDate;
    }

    public void setExtractStartDate(Date extractStartDate) {
        this.extractStartDate = extractStartDate;
    }

    public Date getExtractEndDate() {
        return extractEndDate;
    }

    public void setExtractEndDate(Date extractEndDate) {
        this.extractEndDate = extractEndDate;
    }
}