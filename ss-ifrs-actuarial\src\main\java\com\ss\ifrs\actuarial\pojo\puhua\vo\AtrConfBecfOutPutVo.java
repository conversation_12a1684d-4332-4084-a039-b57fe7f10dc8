package com.ss.ifrs.actuarial.pojo.puhua.vo;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

public class AtrConfBecfOutPutVo implements Serializable{
    protected static final long serialVersionUID = 1L;

    /**
     * Database column: code_id
     * Database remarks: codeId|主键
     */
    @ApiModelProperty(value = "codeId|主键", required = true)
    protected Long becfOutId;


    protected String businessSourceCode;
    /**
     * Database column: code_code
     * Database remarks: codeCode|基础代码
     */
    @ApiModelProperty(value = "codeCode|基础代码", required = true)
    protected String outCode;
    /**
     * Database column: code_c_name
     * Database remarks: codeName|基础代码名称
     */
    @ApiModelProperty(value = "codeName|基础代码名称", required = true)
    @NotBlank(message = "The Chinese Name can't be null|中文名称不能为空|中文名稱不能為空")
    @Size(max = 400, message = "The Chinese Name's length is too long|中文名称过长|中文名稱過長")
    protected String outCName;
    /**
     * Database column: code_l_name
     * Database remarks: codeLName|基础代码繁体名称
     */
    @ApiModelProperty(value = "codeLName|基础代码繁体名称", required = false)
    @NotBlank(message = "The Traditional Chinese Name can't be null|繁体名称不能为空|繁體名稱不能為空")
    @Size(max = 400, message = "The Traditional Chinese Name's length is too long|繁体名称过长|繁體名稱過長")
    protected String outLName;
    /**
     * Database column: code_e_name
     * Database remarks: codeName|基础代码英文名称
     */
    @ApiModelProperty(value = "codeName|基础代码英文名称", required = false)
    @NotBlank(message = "The English Name can't be null|英文名称不能为空|英文名稱不能為空")
    @Size(max = 400, message = "The English Name's length is too long|英文名称过长|英文名稱過長")
    protected String outEName;
    /**
     * Database column: remark
     * Database remarks: remark|备注
     */
    @ApiModelProperty(value = "remark|备注", required = false)
    protected String remark;

    /**
     * Database column: display_no
     * Database remarks: displayNo|显示序号
     */
    @ApiModelProperty(value = "displayNo|显示序号", required = false)
    protected Integer displayNo;

    protected String dimension;

    protected String type;

    private Integer startDevNo;

    private Integer endDevNo;

    protected String becfType;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getBecfOutId() {
        return becfOutId;
    }

    public void setBecfOutId(Long becfOutId) {
        this.becfOutId = becfOutId;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getOutCode() {
        return outCode;
    }

    public void setOutCode(String outCode) {
        this.outCode = outCode;
    }

    public String getOutCName() {
        return outCName;
    }

    public void setOutCName(String outCName) {
        this.outCName = outCName;
    }

    public String getOutLName() {
        return outLName;
    }

    public void setOutLName(String outLName) {
        this.outLName = outLName;
    }

    public String getOutEName() {
        return outEName;
    }

    public void setOutEName(String outEName) {
        this.outEName = outEName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getDisplayNo() {
        return displayNo;
    }

    public void setDisplayNo(Integer displayNo) {
        this.displayNo = displayNo;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public Integer getStartDevNo() {
        return startDevNo;
    }

    public void setStartDevNo(Integer startDevNo) {
        this.startDevNo = startDevNo;
    }

    public Integer getEndDevNo() {
        return endDevNo;
    }

    public void setEndDevNo(Integer endDevNo) {
        this.endDevNo = endDevNo;
    }

    public String getBecfType() {
        return becfType;
    }

    public void setBecfType(String becfType) {
        this.becfType = becfType;
    }
}