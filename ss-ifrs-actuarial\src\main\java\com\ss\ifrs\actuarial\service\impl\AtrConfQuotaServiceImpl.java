package com.ss.ifrs.actuarial.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.WriteDirectionEnum;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.ss.ifrs.actuarial.conf.AppConfig;
import com.ss.ifrs.actuarial.constant.ActuarialConstant;
import com.ss.ifrs.actuarial.dao.conf.*;
import com.ss.ifrs.actuarial.feign.BmsConfCodeFeignClient;
import com.ss.ifrs.actuarial.pojo.atrconf.po.*;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.*;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrExcelCellVo;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrExcelParseVo;
import com.ss.ifrs.actuarial.service.*;
import com.ss.ifrs.actuarial.util.AtrExcelGenerateUtils;
import com.ss.ifrs.actuarial.util.ExcelImportUtils;
import com.ss.ifrs.actuarial.util.ExcelUtil;
import com.ss.library.constant.ExcelConstant;
import com.ss.library.excel.ExcelSheet;
import com.ss.library.excel.ExcelSheetData;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ClassUtil;
import com.ss.library.utils.DataUtil;
import com.ss.library.utils.ExcelImportUtil;
import com.ss.library.utils.StringUtil;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.core.exception.BusinessException;
import com.ss.platform.pojo.bbs.vo.BbsConfLoaVo;
import com.ss.platform.pojo.bbs.vo.BbsConfRegularRuleVo;
import com.ss.platform.pojo.com.po.ConfCode;
import com.ss.platform.util.ExcelExportUtil;
import com.ss.platform.util.LanguageUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.el.parser.ParseException;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.tomcat.util.http.fileupload.FileUploadException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.mail.IllegalWriteException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/11/10
 * @Param param
 * @return return
 **/
@Service("atrConfQuotaService")
public class AtrConfQuotaServiceImpl implements AtrConfQuotaService {

    @Autowired
    AtrConfQuotaDao atrConfQuotaDao;

    @Autowired
    AtrConfQuotaHisDao atrConfQuotaHisDao;

    @Autowired
    AtrConfQuotaDetailDao atrConfQuotaDetailDao;

    @Autowired
    AtrConfQuotaDetailHisDao atrConfQuotaDetailHisDao;

    @Autowired
    AtrConfQuotaDefService atrConfQuotaDefService;

    @Autowired
    AtrConfQuotaDefDao atrConfQuotaDefDao;

    @Autowired
    AtrExportService atrExportService;

    @Autowired
    AtrConfExcelService atrConfExcelService;

    @Autowired
    AppConfig appConfig;

    @Autowired
    AtrConfCodeService atrConfCodeService;


    @Autowired(required = false)
    private BmsConfCodeFeignClient bmsConfCodeFeignClient;

    final Logger LOG = LoggerFactory.getLogger(getClass());

    // 在类的顶部添加内部类
    private static class CustomSheetWriteHandler implements SheetWriteHandler {
        private final List<String> options;
        private final int columnIndex;

        public CustomSheetWriteHandler(List<String> options, int columnIndex) {
            this.options = options;
            this.columnIndex = columnIndex;
        }

        @Override
        public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        }

        @Override
        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            Sheet sheet = writeSheetHolder.getSheet();
            DataValidationHelper helper = sheet.getDataValidationHelper();
            DataValidationConstraint constraint = helper.createExplicitListConstraint(options.toArray(new String[0]));
            // 将下拉选项应用到指定列
            CellRangeAddressList addressList = new CellRangeAddressList(1, 999, columnIndex, columnIndex);
            DataValidation validation = helper.createValidation(constraint, addressList);
            validation.setShowErrorBox(true);
            sheet.addValidationData(validation);
        }
    }



    private void saveQuotaPeriod(Long quotaId, List<AtrConfQuotaDetailVo> quotaDetailVoList) {
        if (ObjectUtils.isEmpty(quotaId)) {
            LOG.error("保存发展期数据失败：quotaId为空");
            throw new RuntimeException("保存发展期数据失败：quotaId为空");
        }
        
        if (CollectionUtils.isEmpty(quotaDetailVoList)) {
            LOG.info("没有发展期数据需要保存，quotaId: {}", quotaId);
            return;
        }
        
        LOG.info("开始保存发展期数据，quotaId: {}, 数据条数: {}", quotaId, quotaDetailVoList.size());
        
        try {
            // 转换为PO对象
        List<AtrConfQuotaDetail> detailList = ClassUtil.convert(quotaDetailVoList, AtrConfQuotaDetail.class);
            
            // 为每个明细设置quotaId
            for (AtrConfQuotaDetail detail : detailList) {
                detail.setQuotaId(quotaId);
                // 确保发展期不为空
                if (detail.getQuotaPeriod() == null) {
                    LOG.warn("发展期为空，设置为默认值1，quotaId: {}", quotaId);
                    detail.setQuotaPeriod(1L);
                }
                
                // 确保值不为空
                if (StringUtil.isEmpty(detail.getQuotaValue())) {
                    LOG.warn("发展期 {} 的值为空，设置为默认值'0'，quotaId: {}", detail.getQuotaPeriod(), quotaId);
                    detail.setQuotaValue("0");
                }
            }
            
            // 保存到明细表
            int savedCount = atrConfQuotaDetailDao.saveQuotaDetailList(quotaId, detailList);
            LOG.info("成功保存发展期数据 {} 条，quotaId: {}", savedCount, quotaId);
        } catch (Exception e) {
            LOG.error("保存发展期数据失败，quotaId: {}, 错误: {}", quotaId, e.getMessage(), e);
            throw new RuntimeException("保存发展期数据失败: " + e.getMessage());
        }
    }

    @Override
    public Long countQuota(AtrConfQuotaVo atrConfQuotaVo) {
        return atrConfQuotaDao.countQuota(ClassUtil.convert(atrConfQuotaVo, AtrConfQuota.class));
    }


    public List<AtrConfQuotaImportVo> checkImportData(MultipartFile file, AtrConfQuotaImportVo atrConfQuotaVo) throws Exception {
        List<AtrConfQuotaImportVo> confQuotaImportVoList = atrConfQuotaDao.findImportQuotaList(atrConfQuotaVo);
        int quotaDefCount = confQuotaImportVoList.size();
        AtrConfQuota atrConfQuota = new AtrConfQuota();
        atrConfQuota.setEntityId(atrConfQuotaVo.getEntityId());
        atrConfQuota.setBusinessSourceCode(atrConfQuotaVo.getBusinessSourceCode());
        atrConfQuota.setDimension(atrConfQuotaVo.getDimension());
        List<AtrConfQuotaImportVo> list = ExcelExportUtil.read(file.getInputStream(), AtrConfQuotaImportVo.class);
        Map<String, List<AtrConfQuotaImportVo>> granularityMap = list.stream().collect(Collectors.groupingBy(AtrConfQuotaImportVo::getDimensionValue, HashMap::new, Collectors.toList()));
        //记录问题数据的颗粒度值和问题类型
        List<AtrConfQuotaImportVo> importCheckDataResultList = new ArrayList<>();
        for (Map.Entry<String, List<AtrConfQuotaImportVo>> entry : granularityMap.entrySet()) {
            atrConfQuota.setDimensionValue(entry.getKey());
            if (atrConfQuotaDao.hasSameQuota(atrConfQuota)) {
                AtrConfQuotaImportVo importVo = new AtrConfQuotaImportVo();
                importVo.setDimensionValue(entry.getKey());
                importVo.setMsgType("1");
                importCheckDataResultList.add(importVo);
                continue;
            }
            long count = entry.getValue().stream().map(AtrConfQuotaImportVo::getQuotaCode).distinct().count();
            if (quotaDefCount != count) {
                AtrConfQuotaImportVo importVo = new AtrConfQuotaImportVo();
                importVo.setDimensionValue(entry.getKey());
                importVo.setMsgType("2");
                importCheckDataResultList.add(importVo);
                continue;
            } else if (entry.getValue().size() != count) {
                AtrConfQuotaImportVo importVo = new AtrConfQuotaImportVo();
                importVo.setDimensionValue(entry.getKey());
                importVo.setMsgType("2");
                importCheckDataResultList.add(importVo);
                continue;
            }
        }
        return importCheckDataResultList;
    }

    @Override
    public void downloadQtpQuota(HttpServletRequest request, HttpServletResponse response, AtrConfQuotaImportVo confQuotaVo) throws Exception {
        String language = request.getHeader("ss-Language");
        AtrConfQuotaDefVo confQuotaDef = new AtrConfQuotaDefVo();
        confQuotaDef.setQuotaClass(confQuotaVo.getQuotaClass());
        confQuotaDef.setAuditState(CommonConstant.AuditStatus.APPROVED);
        confQuotaDef.setValidIs(CommonConstant.ValidStatus.VALID);
        confQuotaDef.setLanguage(language);
        confQuotaVo.setLanguage(language);
        
        // 获取假设值定义列表
        List<AtrConfQuotaDef> confQuotaDefVoList = atrConfQuotaDefDao.findList(null);
        if (ObjectUtils.isEmpty(confQuotaDefVoList)) {
            throw new Exception("没有找到假设值定义数据");
        }
        
        // 区分发展期相关和非发展期相关的假设值
        List<AtrConfQuotaDef> periodRelatedQuotas = confQuotaDefVoList.stream()
            .filter(def -> "1".equals(def.getQuotaType()))
            .collect(Collectors.toList());
        
        List<AtrConfQuotaDef> nonPeriodRelatedQuotas = confQuotaDefVoList.stream()
            .filter(def -> "0".equals(def.getQuotaType()))
            .collect(Collectors.toList());
        
        // 创建下拉选项列表,根据语言设置显示名称
        List<String> nonPeriodOptions = nonPeriodRelatedQuotas.stream()
            .map(def -> {
                String displayName;
                if ("en".equals(language)) {
                    displayName = def.getQuotaEName();
                } else if ("tn".equals(language)) {
                    displayName = def.getQuotaLName();
                } else {
                    displayName = def.getQuotaCName();
                }
                return def.getQuotaCode() + "-" + displayName;
            })
            .collect(Collectors.toList());
        
        List<String> periodOptions = periodRelatedQuotas.stream()
            .map(def -> {
                String displayName;
                if ("en".equals(language)) {
                    displayName = def.getQuotaEName();
                } else if ("tn".equals(language)) {
                    displayName = def.getQuotaLName();
                } else {
                    displayName = def.getQuotaCName();
                }
                return def.getQuotaCode() + "-" + displayName;
            })
            .collect(Collectors.toList());
        
        // 创建sheet列表
        List<ExcelSheet> sheetList = new ArrayList<>();
        
        // 创建非发展期sheet
        ExcelSheet nonPeriodSheet = new ExcelSheet();
        nonPeriodSheet.setSheetNo(0);
        nonPeriodSheet.setSheetName("非发展期");
        // 初始化sheetDataList，避免空指针异常
        nonPeriodSheet.setSheetDataList(new ArrayList<>());
        
        // 创建发展期sheet
        ExcelSheet periodSheet = new ExcelSheet();
        periodSheet.setSheetNo(1);
        periodSheet.setSheetName("发展期");
        // 初始化sheetDataList，避免空指针异常
        periodSheet.setSheetDataList(new ArrayList<>());
        
        // 添加到sheet列表
        sheetList.add(nonPeriodSheet);
        sheetList.add(periodSheet);
        
        // 使用自定义的处理器
        Map<String, Object> extendMap = new HashMap<>();
        extendMap.put("sheet0DropdownOptions", nonPeriodOptions);  // 非发展期sheet的下拉选项
        extendMap.put("sheet1DropdownOptions", periodOptions);     // 发展期sheet的下拉选项
        
        // 导出Excel
        atrExportService.exportExcelSheetList(request, response, sheetList,
                confQuotaVo.getTemplateFileName(), extendMap, confQuotaVo.getTargetRouter(), confQuotaVo.getCreatorId());
    }


    /**
     * 按列位置解析Excel非发展期数据，不依赖列名
     * 
     * @param sheetDataList Excel数据列表，按列位置索引组织
     * @param atrConfQuotaVo 假设值导入VO对象
     * @return 解析后的假设值列表
     */
    private List<AtrConfQuotaVo> analysisQuotaExcelByPosition(List<Map<Integer, String>> sheetDataList, AtrConfQuotaImportVo atrConfQuotaVo) {
        if (CollectionUtils.isEmpty(sheetDataList)) {
            LOG.info("数据为空，无需解析");
            return new ArrayList<>();
        }
        
        LOG.info("开始按列位置解析数据，共 {} 行", sheetDataList.size());
        
        // 定义Excel列位置常量（从0开始）
        final int COL_YEAR_MONTH_INDEX = 0;      // 评估期在第1列
        final int COL_BUSINESS_MODEL_INDEX = 1;  // 业务类型在第2列
        final int COL_RISK_CLASS_INDEX = 2;      // 险类在第3列
        final int COL_ICG_NO_INDEX = 3;          // 合同组在第4列
        final int COL_QUOTA_DEF_INDEX = 4;       // 假设值定义在第5列
        final int COL_ASSUME_VALUE_INDEX = 5;    // 数值在第6列
        
        // 获取业务模型列表
        List<ConfCode> bplCodes = bmsConfCodeFeignClient.findCodeByCodeType("BusinessModel%2FBase");
        if (CollectionUtils.isEmpty(bplCodes)) {
            LOG.warn("未找到业务模型定义，请检查BusinessModel/Base配置");
        }

        List<AtrConfQuotaVo> qtcConfQuotas = new ArrayList<>();

        // 获取假设值定义列表
        AtrConfQuotaDefVo confQuotaDef = new AtrConfQuotaDefVo();
        confQuotaDef.setQuotaClass(atrConfQuotaVo.getQuotaClass());
        confQuotaDef.setValidIs(CommonConstant.ValidStatus.VALID);
        confQuotaDef.setAuditState(CommonConstant.AuditStatus.APPROVED);
        confQuotaDef.setLanguage("en");
        
        List<AtrConfQuotaDefVo> confQuotaDefVoList = atrConfQuotaDefDao.findAtrQuotaDataClass(confQuotaDef);
        if (CollectionUtils.isEmpty(confQuotaDefVoList)) {
            LOG.warn("未找到有效的假设值定义，请检查假设值定义配置");
            return qtcConfQuotas;
        }
        
        // 筛选非发展期相关的假设值定义
        List<AtrConfQuotaDefVo> nonDevelopQuotaDefList = confQuotaDefVoList.stream()
                .filter(def -> "0".equals(def.getQuotaType()))
                .collect(Collectors.toList());
        
        if (CollectionUtils.isEmpty(nonDevelopQuotaDefList)) {
            LOG.warn("未找到非发展期相关的假设值定义，请检查假设值定义配置");
        }

        List<ConfCode> finalConfModelDefs = ClassUtil.convert(bplCodes, ConfCode.class);

        // 处理每一行数据
        int rowIndex = 0; // 行索引从0开始
        for (Map<Integer, String> dataMap : sheetDataList) {
            rowIndex++;
            try {
                // 从Excel中获取所有必要信息，使用按列位置的方式获取
                String yearMonth = ExcelUtil.getValueByPosition(dataMap, COL_YEAR_MONTH_INDEX, "");
                String businessModel = ExcelUtil.getValueByPosition(dataMap, COL_BUSINESS_MODEL_INDEX, "");
                String riskClassCode = ExcelUtil.getValueByPosition(dataMap, COL_RISK_CLASS_INDEX, "");
                String icgNo = ExcelUtil.getValueByPosition(dataMap, COL_ICG_NO_INDEX, "");
                String quotaDef = ExcelUtil.getValueByPosition(dataMap, COL_QUOTA_DEF_INDEX, "");
                String assumeValue = ExcelUtil.getValueByPosition(dataMap, COL_ASSUME_VALUE_INDEX, "");
                
                // 验证必填字段
                if (StringUtil.isEmpty(yearMonth)) {
                    LOG.warn("第 {} 行数据缺少评估期，跳过处理", rowIndex);
                    continue;
                }
                
                if (StringUtil.isEmpty(businessModel)) {
                    LOG.warn("第 {} 行数据缺少业务类型，跳过处理", rowIndex);
                    continue;
                }
                
                if (StringUtil.isEmpty(riskClassCode)) {
                    LOG.warn("第 {} 行数据缺少险类，跳过处理", rowIndex);
                    continue;
                }
                
                if (StringUtil.isEmpty(icgNo)) {
                    LOG.warn("第 {} 行数据缺少合同组，跳过处理", rowIndex);
                    continue;
                }
                
                if (StringUtil.isEmpty(quotaDef)) {
                    LOG.warn("第 {} 行数据缺少假设值定义，跳过处理", rowIndex);
                    continue;
                }
                
                if (StringUtil.isEmpty(assumeValue)) {
                    LOG.warn("第 {} 行数据缺少假设值，跳过处理", rowIndex);
                    continue;
                }
                
                // 查找业务模型
                Optional<ConfCode> businessModelOpt = finalConfModelDefs.stream()
                        .filter(model -> isAtrModel(model, businessModel))
                        .findFirst();

                if (!businessModelOpt.isPresent()) {
                    LOG.warn("第 {} 行数据的业务类型 '{}' 无效，跳过处理", rowIndex, businessModel);
                    continue;
                }
                
                // 查找假设定义 - 处理可能有编码-名称格式的情况
                String quotaCode = quotaDef.contains("-") ? quotaDef.split("-")[0] : quotaDef;
                Optional<AtrConfQuotaDefVo> quotaDefOpt = nonDevelopQuotaDefList.stream()
                        .filter(def -> def.getQuotaCode().equals(quotaCode))
                        .findFirst();
                
                if (!quotaDefOpt.isPresent()) {
                    LOG.warn("第 {} 行数据的假设编码 '{}' 无效，跳过处理", rowIndex, quotaDef);
                    continue;
                }
                
                // 创建主表对象
                AtrConfQuotaVo qtcConfQuota = new AtrConfQuotaVo();
                qtcConfQuota.setEntityId(atrConfQuotaVo.getEntityId());
                qtcConfQuota.setYearMonth(yearMonth);
                qtcConfQuota.setQuotaClass(atrConfQuotaVo.getQuotaClass());
                qtcConfQuota.setDimension("C");
                qtcConfQuota.setValidIs(CommonConstant.ValidStatus.VALID);
                qtcConfQuota.setAuditState(CommonConstant.AuditStatus.APPROVED);
                qtcConfQuota.setRiskClassCode(riskClassCode);
                qtcConfQuota.setSerialNo(1);
                qtcConfQuota.setBusinessSourceCode(businessModel);
                qtcConfQuota.setDimensionValue(icgNo);
                qtcConfQuota.setQuotaDefId(quotaDefOpt.get().getQuotaDefId());
                qtcConfQuota.setQuotaValue(assumeValue);
                
                qtcConfQuotas.add(qtcConfQuota);
                LOG.debug("成功解析第 {} 行数据，假设编码: {}, 假设值: {}", 
                        rowIndex, quotaDef, qtcConfQuota.getQuotaValue());

            } catch (Exception e) {
                LOG.error("解析第 {} 行数据时发生错误: {}", rowIndex, e.getMessage(), e);
            }
        }

        LOG.info("非发展期数据解析完成，共解析出 {} 条有效记录", qtcConfQuotas.size());
        return qtcConfQuotas;
    }
    
    /**
     * 按列位置解析Excel发展期数据，不依赖列名
     * 
     * @param sheetDataList Excel数据列表，按列位置索引组织
     * @param atrConfQuotaVo 假设值导入VO对象
     * @return 解析后的假设值列表
     */
    private List<AtrConfQuotaVo> analysisExcelQuotaDetailByPosition(List<Map<Integer, String>> sheetDataList, AtrConfQuotaImportVo atrConfQuotaVo) {
        if (CollectionUtils.isEmpty(sheetDataList)) {
            LOG.info("发展期数据为空，无需解析");
            return new ArrayList<>();
        }
        
        LOG.info("开始按列位置解析发展期数据，共 {} 行", sheetDataList.size());
        
        // 定义Excel列位置常量（从0开始）
        final int COL_YEAR_MONTH_INDEX = 0;      // 评估期在第1列
        final int COL_BUSINESS_MODEL_INDEX = 1;  // 业务类型在第2列
        final int COL_RISK_CLASS_INDEX = 2;      // 险类在第3列
        final int COL_ICG_NO_INDEX = 3;          // 合同组在第4列
        final int COL_QUOTA_DEF_INDEX = 4;       // 假设值定义在第5列
        final int FIRST_PERIOD_COL_INDEX = 5;    // 发展期列从第6列开始，第6列为第1期
        
        // 获取业务模型列表
        List<ConfCode> bplCodes = bmsConfCodeFeignClient.findCodeByCodeType("BusinessModel%2FBase");
        if (CollectionUtils.isEmpty(bplCodes)) {
            LOG.warn("未找到业务模型定义，请检查BusinessModel/Base配置");
        }

        List<AtrConfQuotaVo> qtcConfQuotas = new ArrayList<>();

        // 获取假设值定义列表
        AtrConfQuotaDefVo confQuotaDef = new AtrConfQuotaDefVo();
        confQuotaDef.setQuotaClass(atrConfQuotaVo.getQuotaClass());
        confQuotaDef.setValidIs(CommonConstant.ValidStatus.VALID);
        confQuotaDef.setAuditState(CommonConstant.AuditStatus.APPROVED);
        confQuotaDef.setLanguage("en");
        
        List<AtrConfQuotaDefVo> confQuotaDefVoList = atrConfQuotaDefDao.findAtrQuotaDataClass(confQuotaDef);
        if (CollectionUtils.isEmpty(confQuotaDefVoList)) {
            LOG.warn("未找到有效的假设值定义，请检查假设值定义配置");
            return qtcConfQuotas;
        }
        
        // 筛选发展期相关的假设值定义
        List<AtrConfQuotaDefVo> developQuotaDefList = confQuotaDefVoList.stream()
                .filter(def -> "1".equals(def.getQuotaType()))
                .collect(Collectors.toList());
        
        if (CollectionUtils.isEmpty(developQuotaDefList)) {
            LOG.warn("未找到发展期相关的假设值定义，请检查假设值定义配置");
        }
        
        // 直接处理所有数据行（readExcelSecondSheetByPosition返回的数据已经不包含表头行）
        
        // 处理每一行数据
        int rowIndex = 0; // 行索引从0开始
        for (Map<Integer, String> dataMap : sheetDataList) {
            rowIndex++;
            
            try {
                // 从Excel中获取所有必要信息，使用按列位置的方式获取
                String yearMonth = ExcelUtil.getValueByPosition(dataMap, COL_YEAR_MONTH_INDEX, "");
                String businessModel = ExcelUtil.getValueByPosition(dataMap, COL_BUSINESS_MODEL_INDEX, "");
                String riskClassCode = ExcelUtil.getValueByPosition(dataMap, COL_RISK_CLASS_INDEX, "");
                String icgNo = ExcelUtil.getValueByPosition(dataMap, COL_ICG_NO_INDEX, "");
                String quotaDef = ExcelUtil.getValueByPosition(dataMap, COL_QUOTA_DEF_INDEX, "");
                
                // 验证必填字段
                if (StringUtil.isEmpty(yearMonth)) {
                    LOG.warn("第 {} 行数据缺少评估期，跳过处理", rowIndex);
                    continue;
                }
                
                if (StringUtil.isEmpty(businessModel)) {
                    LOG.warn("第 {} 行数据缺少业务类型，跳过处理", rowIndex);
                    continue;
                }
                
                if (StringUtil.isEmpty(riskClassCode)) {
                    LOG.warn("第 {} 行数据缺少险类，跳过处理", rowIndex);
                    continue;
                }
                
                if (StringUtil.isEmpty(icgNo)) {
                    LOG.warn("第 {} 行数据缺少合同组，跳过处理", rowIndex);
                    continue;
                }
                
                if (StringUtil.isEmpty(quotaDef)) {
                    LOG.warn("第 {} 行数据缺少假设值定义，跳过处理", rowIndex);
                    continue;
                }
                
                // 查找业务模型
                Optional<ConfCode> businessModelOpt = bplCodes.stream()
                        .filter(model -> isAtrModel(model, businessModel))
                        .findFirst();

                if (!businessModelOpt.isPresent()) {
                    LOG.warn("第 {} 行数据的业务类型 '{}' 无效，跳过处理", rowIndex, businessModel);
                    continue;
                }
                
                // 查找假设定义 - 处理可能有编码-名称格式的情况
                String quotaCode = quotaDef.contains("-") ? quotaDef.split("-")[0] : quotaDef;
                Optional<AtrConfQuotaDefVo> quotaDefOpt = developQuotaDefList.stream()
                        .filter(def -> def.getQuotaCode().equals(quotaCode))
                        .findFirst();
                
                if (!quotaDefOpt.isPresent()) {
                    LOG.warn("第 {} 行数据的假设编码 '{}' 无效，跳过处理", rowIndex, quotaDef);
                    continue;
                }
                
                // 创建主表对象
                AtrConfQuotaVo qtcConfQuota = new AtrConfQuotaVo();
                qtcConfQuota.setEntityId(atrConfQuotaVo.getEntityId());
                qtcConfQuota.setYearMonth(yearMonth);
                qtcConfQuota.setQuotaClass(atrConfQuotaVo.getQuotaClass());
                qtcConfQuota.setDimension("C");
                qtcConfQuota.setValidIs(CommonConstant.ValidStatus.VALID);
                qtcConfQuota.setAuditState(CommonConstant.AuditStatus.APPROVED);
                qtcConfQuota.setRiskClassCode(riskClassCode);
                qtcConfQuota.setBusinessSourceCode(businessModel);
                qtcConfQuota.setDimensionValue(icgNo);
                qtcConfQuota.setQuotaDefId(quotaDefOpt.get().getQuotaDefId());
                
                // 处理发展期数据
                List<AtrConfQuotaDetailVo> qtcConfQuotaDetailVos = new ArrayList<>();
                
                // 直接从第6列开始，将第6列作为第1期，第7列作为第2期，依此类推
                // 通过检查列是否有值来判断有多少期的假设值
                int periodIndex = 1; // 期次从1开始
                int colIndex = FIRST_PERIOD_COL_INDEX; // 列索引从第6列开始
                
                while (dataMap.containsKey(colIndex)) {
                    String periodValue = ExcelUtil.getValueByPosition(dataMap, colIndex, "");
                    
                    if (!StringUtil.isEmpty(periodValue)) {
                        AtrConfQuotaDetailVo detailVo = new AtrConfQuotaDetailVo();
                        detailVo.setQuotaPeriod((long) periodIndex);
                        detailVo.setSerialNo(periodIndex);
                        detailVo.setQuotaValue(periodValue);
                        
                        qtcConfQuotaDetailVos.add(detailVo);
                        LOG.debug("第 {} 行数据，第 {} 期，假设值: {}", rowIndex, periodIndex, periodValue);
                    }
                    
                    periodIndex++;
                    colIndex++;
                }
                
                if (!qtcConfQuotaDetailVos.isEmpty()) {
                    qtcConfQuota.setAtrConfQuotaDetailVoList(qtcConfQuotaDetailVos);
                    qtcConfQuotas.add(qtcConfQuota);
                    LOG.debug("成功解析第 {} 行数据，假设编码: {}, 发展期数据: {} 条", 
                            rowIndex, quotaDef, qtcConfQuotaDetailVos.size());
                } else {
                    LOG.warn("第 {} 行数据没有有效的发展期数据，跳过处理", rowIndex);
                }
            } catch (Exception e) {
                LOG.error("解析第 {} 行数据时发生错误: {}", rowIndex, e.getMessage(), e);
            }
        }

        LOG.info("发展期数据解析完成，共解析出 {} 条有效记录", qtcConfQuotas.size());
        return qtcConfQuotas;
    }


    private Boolean isAtrModel(ConfCode model, String businessType) {
        return ObjectUtils.isEmpty(model)? false :model.getCodeLName().equals(businessType) || model.getCodeCName().equals(businessType)
                || model.getCodeEName().equals(businessType) || model.getCodeCode().equals(businessType) ;
    }


    /**
     * 使用按列位置读取Excel数据导入假设值，解决列名可能变更的问题
     * 
     * @param file Excel文件
     * @param atrConfQuotaVo 导入参数
     * @return 导入的假设值列表
     * @throws Exception 导入异常
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public List<AtrConfQuotaVo> excelImportToQuotaByPosition(MultipartFile file, AtrConfQuotaImportVo atrConfQuotaVo) throws Exception {
        // 验证文件
        if (file == null || file.isEmpty()) {
            LOG.error("上传的文件为空");
            throw new Exception("上传的文件为空");
        }
        
        String fileName = file.getOriginalFilename();
        if (fileName == null || !fileName.endsWith(".xlsx")) {
            LOG.error("文件格式不正确，只支持.xlsx格式");
            throw new Exception("文件格式不正确，只支持.xlsx格式");
        }
        
        LOG.info("开始按列位置处理Excel文件: {}", fileName);
        
        // 验证基本参数
        if (atrConfQuotaVo == null) {
            LOG.error("导入参数为空");
            throw new Exception("导入参数为空");
        }
        
        if (atrConfQuotaVo.getEntityId() == null) {
            LOG.error("业务单位ID不能为空");
            throw new Exception("业务单位ID不能为空");
        }
        
        // 按列位置读取Excel文件，不依赖列名
        List<Map<Integer, String>> sheetDataList = ExcelUtil.readExcelFirstSheetByPosition(file);
        List<Map<Integer, String>> sheetDataDetailList = ExcelUtil.readExcelSecondSheetByPosition(file);
        
        // 删除存在数据库中，现有导入的假设值（使用按列位置的方式）
        deleteQuotaDataByPosition(sheetDataList, atrConfQuotaVo);
        deleteQuotaDataByPosition(sheetDataDetailList, atrConfQuotaVo);
        
        List<AtrConfQuotaVo> result = new ArrayList<>();
        
        try {
            LOG.info("成功按列位置读取Excel文件，第一个工作表(非发展期)数据行数: {}, 第二个工作表(发展期)数据行数: {}",
                    sheetDataList.size(),
                    sheetDataDetailList.size());
            
            // 处理非发展期数据（使用按列位置分析）
            if (CollectionUtils.isNotEmpty(sheetDataList)) {
                LOG.info("开始按列位置处理非发展期数据");
                List<AtrConfQuotaVo> nonDevPeriodQuotas = analysisQuotaExcelByPosition(sheetDataList, atrConfQuotaVo);
                
                if (CollectionUtils.isNotEmpty(nonDevPeriodQuotas)) {
                    LOG.info("非发展期数据处理完成，共 {} 条记录", nonDevPeriodQuotas.size());
                    result.addAll(nonDevPeriodQuotas);
                } else {
                    LOG.warn("未从非发展期数据中解析出有效记录");
                }
            } else {
                LOG.warn("非发展期数据为空，跳过处理");
            }
            
            // 处理发展期数据（使用按列位置分析）
            if (CollectionUtils.isNotEmpty(sheetDataDetailList)) {
                LOG.info("开始按列位置处理发展期数据");
                List<AtrConfQuotaVo> devPeriodQuotas = analysisExcelQuotaDetailByPosition(sheetDataDetailList, atrConfQuotaVo);
                
                if (CollectionUtils.isNotEmpty(devPeriodQuotas)) {
                    LOG.info("发展期数据处理完成，共 {} 条记录", devPeriodQuotas.size());
                    result.addAll(devPeriodQuotas);
                } else {
                    LOG.warn("未从发展期数据中解析出有效记录");
                }
            } else {
                LOG.warn("发展期数据为空，跳过处理");
            }
            
            // 保存数据
            if (CollectionUtils.isNotEmpty(result)) {
                LOG.info("开始保存解析的数据，共 {} 条记录", result.size());
                
                for (AtrConfQuotaVo quotaVo : result) {
                    // 保存主表数据
                    AtrConfQuota atrConfQuota = ClassUtil.convert(quotaVo, AtrConfQuota.class);
                    atrConfQuotaDao.save(atrConfQuota);
                    
                    // 保存明细表数据
                    if (CollectionUtils.isNotEmpty(quotaVo.getAtrConfQuotaDetailVoList())) {
                        saveQuotaPeriod(atrConfQuota.getQuotaId(), quotaVo.getAtrConfQuotaDetailVoList());
                    }
                }
                
                LOG.info("数据保存完成");
            } else {
                LOG.warn("没有有效数据需要保存");
            }
        } catch (Exception e) {
            LOG.error("按列位置处理Excel文件时发生错误: {}", e.getMessage(), e);
            throw new Exception("按列位置处理Excel文件时发生错误: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 按列位置获取信息删除现有数据，不依赖列名
     * 
     * @param sheetDataList Excel数据列表，按列位置索引组织
     * @param atrConfQuotaVo 假设值导入VO对象
     * @throws Exception 删除异常
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void deleteQuotaDataByPosition(List<Map<Integer, String>> sheetDataList, AtrConfQuotaImportVo atrConfQuotaVo) throws Exception {
        // 在导入前先删除相同的数据
        try {
            if (CollectionUtils.isEmpty(sheetDataList)) {
                LOG.info("Excel数据为空，无需删除");
                return;
            }
            
            LOG.info("按列位置解析Excel数据以准备删除现有数据，共 {} 行", sheetDataList.size());
            
            // 定义Excel列位置常量（从0开始）
            final int COL_YEAR_MONTH_INDEX = 0;      // 评估期在第1列
            final int COL_RISK_CLASS_INDEX = 2;      // 险类在第3列
            final int COL_ICG_NO_INDEX = 3;          // 合同组在第4列
            final int COL_QUOTA_DEF_INDEX = 4;       // 假设值定义在第5列
            
            // 创建要删除的记录集合，键为"评估期|险类|合同组"，值为假设值定义代码集合
            Map<String, Set<String>> dimensionMap = new HashMap<>();
            
            // 遍历所有数据行，收集需要删除的维度组合
            for (Map<Integer, String> dataMap : sheetDataList) {
                // 获取关键字段（使用列位置）
                String yearMonth = ExcelUtil.getValueByPosition(dataMap, COL_YEAR_MONTH_INDEX, "");
                String riskClass = ExcelUtil.getValueByPosition(dataMap, COL_RISK_CLASS_INDEX, "");
                String icgNo = ExcelUtil.getValueByPosition(dataMap, COL_ICG_NO_INDEX, "");
                
                // 导入的数据不存在评估期不处理
                if (StringUtil.isEmpty(yearMonth)) {
                    continue;
                }
                
                // 生成删除键
                if (StringUtil.isNotEmpty(riskClass) && StringUtil.isNotEmpty(icgNo)) {
                    String key = yearMonth + "|" + riskClass + "|" + icgNo;
                    
                    // 获取假设值定义
                    String quotaDef = ExcelUtil.getValueByPosition(dataMap, COL_QUOTA_DEF_INDEX, "");
                    if (StringUtil.isNotEmpty(quotaDef)) {
                        // 处理可能有编码-名称格式的情况
                        String quotaCode = quotaDef.contains("-") ? quotaDef.split("-")[0] : quotaDef;
                        
                        // 如果该维度组合不存在，创建新集合
                        dimensionMap.computeIfAbsent(key, k -> new HashSet<>());
                        // 添加假设值定义编码
                        dimensionMap.get(key).add(quotaCode);
                    }
                }
            }
            
            // 执行删除操作
            if (!dimensionMap.isEmpty()) {
                LOG.info("开始执行删除操作，共有 {} 组维度数据需要删除", dimensionMap.size());
                
                for (Map.Entry<String, Set<String>> entry : dimensionMap.entrySet()) {
                    String[] parts = entry.getKey().split("\\|");
                    if (parts.length == 3) {
                        String yearMonth = parts[0];
                        String riskClass = parts[1];
                        String icgNo = parts[2];
                        
                        // 记录日志
                        LOG.info("删除数据: 评估期={}, 险类={}, 合同组={}, 假设值数量={}",
                                yearMonth, riskClass, icgNo, entry.getValue().size());
                        
                        // 构建删除条件
                        AtrConfQuota deleteCondition = new AtrConfQuota();
                        deleteCondition.setEntityId(atrConfQuotaVo.getEntityId());
                        deleteCondition.setYearMonth(yearMonth);
                        deleteCondition.setRiskClassCode(riskClass);
                        deleteCondition.setDimension("C"); // 默认使用C维度
                        deleteCondition.setDimensionValue(icgNo);
                        
                        try {
                            // 先删除明细表数据，再删除主表数据，保证数据完整性
                            atrConfQuotaDetailDao.deleteByDimensionVo(deleteCondition);
                            atrConfQuotaDao.deleteByDimensionVo(deleteCondition);
                            
                            LOG.debug("成功删除维度组合: {}", entry.getKey());
                        } catch (Exception e) {
                            LOG.error("删除维度组合 {} 时发生错误: {}", entry.getKey(), e.getMessage(), e);
                            throw e; // 抛出异常以触发事务回滚
                        }
                    }
                }
            } else {
                LOG.info("未找到需要删除的数据");
            }
        } catch (Exception e) {
            LOG.error("按列位置删除现有数据时发生错误: {}", e.getMessage(), e);
            throw new Exception("按列位置删除现有数据时发生错误: " + e.getMessage());
        }
    }

}
