package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrTemplateVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaImportVo;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrDapIcgRiskClassVo;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrDapTreatyVo;
import java.util.List;

public interface AtrDapTreatyService {

    List<AtrDapTreatyVo> findDapTreatyList(AtrTemplateVo entityId);
    List<AtrDapIcgRiskClassVo> findTreatyIcgList(AtrConfQuotaImportVo atrConfQuotaImportVo);

    List<AtrDapIcgRiskClassVo> findDDIcgList(Long entityId);

    List<AtrDapIcgRiskClassVo> findFOIcgList(Long entityId);
}
