<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-11-29 14:13:36 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.conf.AtrConfInterestBaseDao">
    <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
    <!-- 请勿修改基础配置(**BaseDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
    <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
    <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
    <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestBase">
        <id column="code" property="code" jdbcType="VARCHAR" />
        <result column="value" property="value" jdbcType="DECIMAL" />
        <result column="creator_id" property="creatorId" jdbcType="BIGINT" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="updator_id" property="updatorId" jdbcType="BIGINT" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>
    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        code, value, creator_id, create_time, updator_id, update_time
    </sql>
    <sql id="Blob_Column_List" />
    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <where>
            <if test="code != null and code != ''">
                and code = #{code,jdbcType=VARCHAR}
            </if>
            <if test="value != null">
                and value = #{value,jdbcType=DECIMAL}
            </if>
            <if test="creatorId != null">
                and creator_id = #{creatorId,jdbcType=BIGINT}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updatorId != null">
                and updator_id = #{updatorId,jdbcType=BIGINT}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
    <sql id="Base_Update_By_Entity_Where">
        <trim prefixOverrides="and">
            <if test="condition.code != null and condition.code != ''">
                and code = #{condition.code,jdbcType=VARCHAR}
            </if>
            <if test="condition.value != null">
                and value = #{condition.value,jdbcType=DECIMAL}
            </if>
            <if test="condition.creatorId != null">
                and creator_id = #{condition.creatorId,jdbcType=BIGINT}
            </if>
            <if test="condition.createTime != null">
                and create_time = #{condition.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="condition.updatorId != null">
                and updator_id = #{condition.updatorId,jdbcType=BIGINT}
            </if>
            <if test="condition.updateTime != null">
                and update_time = #{condition.updateTime,jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>
    <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
    <sql id="Base_Delete_By_Entity_Where">
        <trim prefixOverrides="and">
            <if test="code != null and code != ''">
                and code = #{code,jdbcType=VARCHAR}
            </if>
            <if test="value != null">
                and value = #{value,jdbcType=DECIMAL}
            </if>
            <if test="creatorId != null">
                and creator_id = #{creatorId,jdbcType=BIGINT}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updatorId != null">
                and updator_id = #{updatorId,jdbcType=BIGINT}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>
    <!-- 按主键查询一条记录 -->
    <select id="findByCode" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List" />
        from "atr_conf_interest_base"
        where code = #{code,jdbcType=VARCHAR}
    </select>
    <!-- 按主键数组查询列表 -->
    <select id="findByCodes" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
        select
        <include refid="Base_Column_List" />
        from "atr_conf_interest_base"
        where code in
        <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 简单列表查询语句-->
    <select id="findAll" flushCache="true" useCache="false" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from "atr_conf_interest_base"
    </select>
    <!-- 通用列表查询语句-->
    <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestBase">
        select
        <include refid="Base_Column_List" />
        from "atr_conf_interest_base"
        <include refid="Base_Select_By_Entity_Where" />
    </select>
    <!-- 通用列表查询语句(Map版本)-->
    <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        <include refid="Base_Column_List" />
        from "atr_conf_interest_base"
        <include refid="Base_Select_By_Entity_Where" />
    </select>
    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestBase">
        insert into "atr_conf_interest_base"
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">
                code,
            </if>
            <if test="value != null">
                value,
            </if>
            <if test="creatorId != null">
                creator_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updatorId != null">
                updator_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="value != null">
                #{value,jdbcType=DECIMAL,scale=8},
            </if>
            <if test="creatorId != null">
                #{creatorId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatorId != null">
                #{updatorId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
    <insert id="saveList" flushCache="true" parameterType="java.util.List">
        insert into "atr_conf_interest_base"
        (code, value, creator_id, create_time, updator_id, update_time)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.code,jdbcType=VARCHAR}, #{item.value,jdbcType=DECIMAL,scale=8},
            #{item.creatorId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updatorId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 根据主键，更新一条记录(为空的字段不操作) -->
    <update id="updateByCode" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestBase">
        update "atr_conf_interest_base"
        <set>
            <if test="value != null">
                value = #{value,jdbcType=DECIMAL,scale=8},
            </if>
            <if test="creatorId != null">
                creator_id = #{creatorId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatorId != null">
                updator_id = #{updatorId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where code = #{code,jdbcType=VARCHAR}
    </update>
    <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
    <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
    <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestBase">
        update "atr_conf_interest_base"
        <set>
            <if test="record.value != null">
                value = #{record.value,jdbcType=DECIMAL,scale=8},
            </if>
            <if test="record.creatorId != null">
                creator_id = #{record.creatorId,jdbcType=BIGINT},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updatorId != null">
                updator_id = #{record.updatorId,jdbcType=BIGINT},
            </if>
            <if test="record.updateTime != null">
                update_time = #{record.updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where
        <include refid="Base_Update_By_Entity_Where" />
    </update>
    <!-- 按主键删除一条记录 -->
    <delete id="deleteByCode" flushCache="true" parameterType="java.lang.String">
        delete from "atr_conf_interest_base"
        where code = #{code,jdbcType=VARCHAR}
    </delete>
    <!-- 按主键批量删除多条记录 -->
    <delete id="deleteByCodes" flushCache="true" parameterType="java.util.List">
        delete from "atr_conf_interest_base"
        where code in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
    <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
        delete from "atr_conf_interest_base"
        where
        <include refid="Base_Delete_By_Entity_Where" />
    </delete>
    <!-- 根据条件，进行简单的统计 -->
    <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestBase">
        select count(1) from "atr_conf_interest_base"
        <include refid="Base_Select_By_Entity_Where" />
    </select>
</mapper>