package com.ss.ifrs.actuarial.api;

import com.ss.ifrs.actuarial.dao.conf.AtrConfInflationFactorDao;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrInflationRatioOverviewVo;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInflationFactor;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfInflationFactorConditionVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfInflationFactorPageVo;
import com.ss.ifrs.actuarial.service.impl.AtrInflationConfService;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.platform.core.constant.ResCodeConstant;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since  2023-11-29
 */
@RestController
@RequestMapping("/inflationConf")
@Api(value = "通货膨胀系数配置")
@Slf4j
public class AtrInflationConfApi extends BaseApi {

    @Resource
    private AtrConfInflationFactorDao atrConfInflationFactorDao;
    @Resource
    private AtrInflationConfService atrInflationConfService;

    @RequestMapping(value = "/overviewRatio", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<?> overviewRatio() {
        AtrInflationRatioOverviewVo result = atrInflationConfService.overviewConfRatio();
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @RequestMapping(value = "/searchPage", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<?> searchPage(@RequestBody AtrConfInflationFactorConditionVo vo,
                                                                    HttpServletRequest request,
                                                                    int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        vo.setLanguage(getLanguage(request));
        Page<AtrConfInflationFactorPageVo> data = atrConfInflationFactorDao.searchPage(vo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("data", data);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<?> save(@RequestBody AtrConfInflationFactor vo) {
        atrInflationConfService.save(vo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "ok");
    }

    @RequestMapping(value = "/get", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<?> get(@RequestBody AtrConfInflationFactorConditionVo vo) {
        AtrConfInflationFactor po = atrConfInflationFactorDao.findById(vo.getId());
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, po);
    }

    @RequestMapping(value = "/confirm", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<?> confirm(@RequestBody AtrConfInflationFactorConditionVo vo,
                                   HttpServletRequest request) {
        atrInflationConfService.confirm(vo, loginUserId(request));
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "ok");
    }

    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<?> delete(@RequestBody AtrConfInflationFactorConditionVo vo) {
        atrInflationConfService.delete(vo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "ok");
    }

    private String getLanguage(HttpServletRequest request) {
        return request.getHeader("ss-Language");
    }
}
