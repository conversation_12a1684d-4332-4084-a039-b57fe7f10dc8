package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.dao.conf.AtrConfQuotaDetailDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfQuotaDetailHisDao;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDetail;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDetailHis;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaDetailVo;
import com.ss.ifrs.actuarial.service.AtrConfQuotaDetailService;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.library.utils.ClassUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.transaction.annotation.Transactional;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/11/10
 * @Param param
 * @return return
 **/
@Service("atrConfQuotaDetailService")
@Transactional
public class AtrConfQuotaDetailServiceImpl implements AtrConfQuotaDetailService {

    @Autowired
    AtrConfQuotaDetailDao atrConfQuotaDetailDao;

    @Autowired
    AtrConfQuotaDetailHisDao atrConfQuotaDetailHisDao;

    final Logger LOG = LoggerFactory.getLogger(getClass());

    @Override
    public void addOrUpdateVo(AtrConfQuotaDetailVo atrConfQuotaDetailVo, Long userId){
        try{
            if(atrConfQuotaDetailVo.getQuotaId() == null){
                AtrConfQuotaDetail po = ClassUtil.convert(atrConfQuotaDetailVo, AtrConfQuotaDetail.class);
                po.setCreateTime(new Date());
                po.setCreatorId(userId);
                atrConfQuotaDetailDao.save(po);
                //写入轨迹表
                this.dealSaveHis(po, userId, CommonConstant.OperType.ADD);
            }
        } catch (UnexpectedRollbackException e) {
            LOG.error(e.getLocalizedMessage(), e);
            throw e;
        }
    }

    @Override
    public AtrConfQuotaDetailVo findByPk(Long quotaDetailId){
        AtrConfQuotaDetail atrConfQuotaDetail = atrConfQuotaDetailDao.findById(quotaDetailId);
        AtrConfQuotaDetailVo atrConfQuotaDetailVo = ClassUtil.convert(atrConfQuotaDetail, AtrConfQuotaDetailVo.class);
        return atrConfQuotaDetailVo;
    }

    @Override
    public void deleteByPk(Long quotaDetailId, Long userId){
        try{
            //写入轨迹表
            AtrConfQuotaDetail atrConfQuotaDetail = atrConfQuotaDetailDao.findById(quotaDetailId);
            this.dealSaveHis(atrConfQuotaDetail, userId, CommonConstant.OperType.DELETE);
            atrConfQuotaDetailDao.deleteById(quotaDetailId);
        }catch (UnexpectedRollbackException e){
            LOG.error(e.getLocalizedMessage(), e);
            throw e;
        }
    }


    private void dealSaveHis(AtrConfQuotaDetail atrConfQuotaDetail, Long userId, String operType){
        AtrConfQuotaDetailHis atrConfQuotaDetailHis = new AtrConfQuotaDetailHis();
        ClassUtil.copyProperties(atrConfQuotaDetail, atrConfQuotaDetailHis);
        atrConfQuotaDetailHis.setOperId(userId);
        atrConfQuotaDetailHis.setOperTime(new Date());
        atrConfQuotaDetailHis.setOperType(operType);
        atrConfQuotaDetailHisDao.save(atrConfQuotaDetailHis);
    }
}
