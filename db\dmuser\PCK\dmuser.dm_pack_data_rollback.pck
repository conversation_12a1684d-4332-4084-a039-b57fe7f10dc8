CREATE OR REPLACE PACKAGE dm_pack_data_rollback AUTHID CURRENT_USER IS

  PROCEDURE proc_buss_data_drop_all_model;

  PROCEDURE proc_buss_data_drop_init_model;

  PROCEDURE proc_buss_data_drop_single_model(p_biz_code VARCHAR2);

  PROCEDURE proc_buss_data_drop_year_month(p_year_month VARCHAR2);

  PROCEDURE proc_buss_data_drop_biz_code(p_biz_code   VARCHAR2,
                                         p_year_month VARCHAR2);

  PROCEDURE proc_buss_data_drop_cmunit(p_biz_code   VARCHAR2,
                                       p_year_month VARCHAR2);

  PROCEDURE proc_buss_data_drop_signal(p_biz_code   VARCHAR2,
                                       p_year_month VARCHAR2);

  PROCEDURE proc_buss_data_insert_signal_err_data_deal(p_year_month VARCHAR2);

  PROCEDURE proc_buss_data_drop_stat(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2);

  PROCEDURE proc_buss_data_drop_bussperiod(p_entity_id  NUMBER,
                                           p_year_month VARCHAR2);

  PROCEDURE proc_buss_data_pass_check_msg(p_biz_code  VARCHAR2,
                                          p_check_msg VARCHAR2);

  PROCEDURE proc_buss_data_verify(p_biz_code   VARCHAR2,
                                  p_year_month VARCHAR2);

END dm_pack_data_rollback;
/
CREATE OR REPLACE PACKAGE BODY dm_pack_data_rollback IS
  /**
  ** proc name: proc_buss_data_drop_all_model
  ** describe : 全模型全月份清理数据 接口表_清理TGT
  ** author: lgx
  ** date: 2023-07-26
  **/
  PROCEDURE proc_buss_data_drop_all_model IS
    v_error_msg VARCHAR2(2000);
  BEGIN
    --清除src、tgt的数据
    FOR cur_model IN (SELECT biz_code
                        FROM dm_conf_table
                       WHERE valid_is = '1'
                       ORDER BY (CASE
                                  WHEN type_group = '7' THEN
                                   '0'
                                  ELSE
                                   type_group
                                END),
                                display_no) LOOP
      proc_buss_data_drop_biz_code(cur_model.biz_code, NULL);
    END LOOP;
    --清除信号表数据
    proc_buss_data_drop_signal(null, NULL);
    --清除盈亏配置信息
    EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_duct_profit_param_value';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_duct_profit_unit';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_duct_profit_unit_sub';

    --清除文件上传的日志\清除校验日志
    EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_duct_draw_log';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_log_check_rule';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_log_data_verify';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_log_data_verify_detail';

    --清除统计数据
    proc_buss_data_drop_stat(NULL, NULL);

    --业务期间，并初始化
    proc_buss_data_drop_bussperiod(1, NULL);

  EXCEPTION
    WHEN OTHERS THEN

      --意外处理
      v_error_msg := '[EXCEPTION][全模型全月份清除]报错：' || SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004, v_error_msg);
  END proc_buss_data_drop_all_model;

  /**
  ** proc name: proc_buss_data_drop_init_model
  ** describe : 清理TGT数据不包含SRC数据
  ** author: lgx
  ** date: 2023-07-26
  **/
  PROCEDURE proc_buss_data_drop_init_model IS
    v_error_msg VARCHAR2(2000);
    TYPE CUR_MODEL_TYPE IS REF CURSOR;
    C1          CUR_MODEL_TYPE;
    V_TASK_CODE VARCHAR2(100);

  BEGIN
    --初始化src表
    FOR cur_model IN (SELECT 'ods_' || biz_code AS src_name,
                             'dm_' || biz_code AS tgt_name,
                             biz_code
                        FROM dm_conf_table
                       WHERE valid_is = '1'
                       ORDER BY (CASE
                                  WHEN type_group = '7' THEN
                                   '0'
                                  ELSE
                                   type_group
                                END),
                                display_no) LOOP
      open c1 for 'select distinct task_code from '||cur_model.src_name||' where task_status <> ''0'' ';

      loop fetch c1 into V_TASK_CODE ;
           exit when C1%NOTFOUND;
            --初始化src表状态
            EXECUTE IMMEDIATE 'merge into '||cur_model.src_name||' a
                                using (select * from '||cur_model.src_name||' b where b.task_status <> ''0'' and b.task_code = '''||V_TASK_CODE||''' )b
                                on (a.id = b.id)
                                when matched then
                                  update
                                     set
                                      a.task_status = ''0'' ';
              commit;
        END LOOP;
        CLOSE C1;

      --删除tgt的数据
      EXECUTE IMMEDIATE 'TRUNCATE TABLE ' || cur_model.tgt_name;
      --清除计量单元
      proc_buss_data_drop_cmunit(cur_model.biz_code, NULL);

      --更改信号表的状态
      EXECUTE IMMEDIATE 'merge into ods_data_push_signal a
                        using ods_data_push_signal b
                        on (a.DATA_PUSH_SIGNAL_ID = b.DATA_PUSH_SIGNAL_ID)
                        when matched then
                          update
                             set
                              a.task_status = :1,
                              START_DEAL_TIME = null,
                              DEAL_MSG = null,
                              END_DEAL_TIME = null'
        USING '0';

      EXECUTE IMMEDIATE 'TRUNCATE TABLE ods_data_push_signalhis';

    END LOOP;

    --清除盈亏配置信息
    EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_duct_profit_param_value';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_duct_profit_unit';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_duct_profit_unit_sub';

    --清除文件上传的日志\清除校验日志
    EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_duct_draw_log';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_log_check_rule';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_log_data_verify';
    EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_log_data_verify_detail';

    --重新统计
    proc_buss_data_drop_stat(1, NULL);
    --初始化业务期间
    proc_buss_data_drop_bussperiod(1, NULL);

  EXCEPTION
    WHEN OTHERS THEN
      --意外处理
      v_error_msg := '[EXCEPTION][清理TGT数据不包含SRC数据]报错：' || SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004, v_error_msg);

  END proc_buss_data_drop_init_model;

  /**
  ** proc name: proc_buss_data_drop_single_model
  ** param: p_biz_code 指定的模型
  ** describe : 指定模型全量清除
  ** author: lgx
  ** date: 2023-07-26
  **/
  PROCEDURE proc_buss_data_drop_single_model(p_biz_code VARCHAR2) IS
    v_error_msg VARCHAR2(2000);
  BEGIN
    --清除src、tgt
    proc_buss_data_drop_biz_code(p_biz_code, NULL);
    --清除统计数据
    proc_buss_data_drop_stat(NULL, '201101');

  EXCEPTION
    WHEN OTHERS THEN
      --意外处理
      v_error_msg := '[EXCEPTION][指定模型清除]报错：' || SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004, v_error_msg);

  END proc_buss_data_drop_single_model;

  /**
  ** proc name: proc_buss_data_drop_year_month
  ** param:  p_year_month 指定的业务期间
  ** describe : 指定模型指定月份全量清除
  ** author: lgx
  ** date: 2023-07-26
  **/
  PROCEDURE proc_buss_data_drop_year_month(p_year_month VARCHAR2) IS
    v_error_msg VARCHAR2(2000);
  BEGIN
    --清除src、tgt的数据
    FOR cur_model IN (SELECT t.biz_code
                        FROM ods_data_push_signal dps
                        LEFT JOIN dm_conf_table t
                          ON dps.push_model = t.biz_code
                       WHERE valid_is = '1'
                         AND dps.year_month = p_year_month
                       ORDER BY (CASE
                                  WHEN t.type_group = '7' THEN
                                   '0'
                                  ELSE
                                   t.type_group
                                END),
                                t.display_no) LOOP

      proc_buss_data_drop_biz_code(cur_model.biz_code, p_year_month);

    END LOOP;

    --清除统计数据
    proc_buss_data_drop_stat(NULL, p_year_month);
    --业务期间，并初始化
    proc_buss_data_drop_bussperiod(1, p_year_month);

  EXCEPTION
    WHEN OTHERS THEN
      --意外处理
      v_error_msg := '[EXCEPTION][指定月份清除]报错：' || SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004, v_error_msg);
  END proc_buss_data_drop_year_month;


  /**
  ** proc name: proc_buss_data_drop_biz_code
  ** param: p_biz_code 指定的模型， p_year_month 指定的业务期间
  ** describe : 指定模型指定月份全量清除
  ** author: lgx
  ** date: 2023-07-26
  **/
  PROCEDURE proc_buss_data_drop_biz_code(p_biz_code   VARCHAR2,
                                         p_year_month VARCHAR2) IS
    v_count     NUMBER;
    v_error_msg VARCHAR2(2000);
    v_seq_name  VARCHAR2(100);
  BEGIN
    SELECT COUNT(1)
      INTO v_count
      FROM dm_conf_table
     WHERE valid_is = '1'
       AND biz_code = p_biz_code;

    IF v_count < 0 THEN
      v_error_msg := '[EXCEPTION][指定模型指定月份全量清除]无效模型：' || p_biz_code;
      raise_application_error(-20002, v_error_msg);
    END IF;

    --清除计量单元表数据
    proc_buss_data_drop_cmunit(p_biz_code, p_year_month);

    SELECT src_seq_name INTO v_seq_name FROM dm_conf_table WHERE biz_code = p_biz_code;

    IF p_year_month IS NULL THEN

      --清除src、tgt表数据
      EXECUTE IMMEDIATE 'delete from ods_' || p_biz_code;
      EXECUTE IMMEDIATE 'truncate table dm_' || p_biz_code;
      --重置序列
      --dm_pack_commonutils.drop_sequence(v_seq_name);
      -- Create sequence
      --dm_pack_commonutils.add_sequence(v_seq_name);

          --重新统计
      --8、清除统计数据
        EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_stat_'||p_biz_code;

        --序列重置
        dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_'||p_biz_code);
        dm_pack_commonutils.add_sequence('DM_SEQ_STAT_'||p_biz_code);

    ELSE
      FOR cur_dps IN (SELECT task_code,
                             push_model
                        FROM ods_data_push_signal
                       WHERE push_model = p_biz_code
                         AND year_month = p_year_month) LOOP

        --清除指定业务期间的src与tgt数据
        EXECUTE IMMEDIATE 'delete from ods_' || cur_dps.push_model || ' where task_code = ''' || cur_dps.task_code || '''';
        EXECUTE IMMEDIATE 'delete from dm_' || cur_dps.push_model || ' where task_code = ''' || cur_dps.task_code || '''';
        EXECUTE IMMEDIATE 'delete from dm_stat_' || cur_dps.push_model || ' where task_code = ''' || cur_dps.task_code || '''';

        COMMIT;

      END LOOP;

    END IF;


    --清除信号表数据
    proc_buss_data_drop_signal(p_biz_code, NULL);


  EXCEPTION
    WHEN OTHERS THEN

      --意外处理
      v_error_msg := '[EXCEPTION][指定模型指定月份清除]报错：' || SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004, v_error_msg);

  END proc_buss_data_drop_biz_code;

  /**
  ** proc name: proc_buss_data_drop_cmunit
  ** param: p_biz_code 指定的模型， p_year_month 指定的业务期间
  ** describe : 计量单元清除
  ** author: lgx
  ** date: 2023-07-26
  **/
  PROCEDURE proc_buss_data_drop_cmunit(p_biz_code   VARCHAR2,
                                       p_year_month VARCHAR2) IS
    v_error_msg VARCHAR2(2000);
    v_count     NUMBER;

  BEGIN

    SELECT COUNT(1)
      INTO v_count
      FROM dm_conf_table
     WHERE valid_is = '1'
       AND biz_code = p_biz_code;

    IF v_count < 0 THEN
      v_error_msg := '[EXCEPTION][计量单元清除]无效模型：' || p_biz_code;
      raise_application_error(-20002, v_error_msg);
    END IF;

    --是否为计量单元关联表
    IF p_biz_code NOT IN (upper('policy_main'), upper('policy_premium'), upper('reins_outward'), upper('reins_treaty')) THEN
      RETURN;
    END IF;

    IF p_year_month IS NULL THEN
      --根据模型清除指定的计量单元
      IF p_biz_code IN (upper('policy_main'), upper('policy_premium')) THEN
        --清除直保计量单元表
        EXECUTE IMMEDIATE 'truncate table dm_buss_cmunit_direct';
        --重置序列
        dm_pack_commonutils.drop_sequence('dm_seq_buss_cmunit_direct');
        -- Create sequence
        dm_pack_commonutils.add_sequence('dm_seq_buss_cmunit_direct');

        --计量单元编码序列重置
        --dm_seq_cmunitno
        dm_pack_commonutils.drop_sequence('dm_seq_cmunitno');
        -- Create sequence
        dm_pack_commonutils.add_sequence('dm_seq_cmunitno');

        --清除直保过程表
        EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_duct_direct_profit_unit';
        EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_duct_direct_profit_amount';
        EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_duct_direct_profit_param';

      ELSIF p_biz_code = upper('reins_outward') THEN

        --清除临分分出计量单元表
        EXECUTE IMMEDIATE 'truncate table dm_buss_cmunit_fac_outwards';
        --重置序列
        dm_pack_commonutils.drop_sequence('dm_seq_buss_cmunit_fac_outward');
        -- Create sequence
        dm_pack_commonutils.add_sequence('dm_seq_buss_cmunit_fac_outward');

        --计量单元编码序列重置
        --dm_seq_fac_out_cmunitno
        dm_pack_commonutils.drop_sequence('dm_seq_fac_out_cmunitno');
        -- Create sequence
        dm_pack_commonutils.add_sequence('dm_seq_fac_out_cmunitno');

      ELSIF p_biz_code = upper('reins_outward') THEN

        --清除合约计量单元表
        EXECUTE IMMEDIATE 'truncate table dm_buss_cmunit_treaty';
        --重置序列
        dm_pack_commonutils.drop_sequence('dm_seq_buss_cmunit_treaty');
        -- Create sequence
        dm_pack_commonutils.add_sequence('dm_seq_buss_cmunit_treaty');

        --dm_seq_treaty_in_cmunitno
        dm_pack_commonutils.drop_sequence('dm_seq_treaty_in_cmunitno');
        -- Create sequence
        dm_pack_commonutils.add_sequence('dm_seq_treaty_in_cmunitno');

        --dm_seq_treaty_out_cmunitno
        dm_pack_commonutils.drop_sequence('dm_seq_treaty_out_cmunitno');
        -- Create sequence
        dm_pack_commonutils.add_sequence('dm_seq_treaty_out_cmunitno');

        --清除合约过程表
        EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_duct_treaty_profit_amount';
        EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_duct_treaty_profit_param';
        EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_duct_treaty_profit_unit';

      END IF;

    ELSE
      --根据模型清除指定的计量单元
      IF p_biz_code IN (upper('policy_premium')) THEN
        --清除直保计量单元
        EXECUTE IMMEDIATE 'delete from dm_buss_cmunit_direct cm
                  where exists (select 1 from dm_policy_premium pp where pp.entity_id = cm.entity_id and cm.policy_no = pp.policy_no
                        and exists(select 1 from ods_data_push_signal dps where dps.push_model = upper(''policy_premium'')
                            and dps.year_month = ''' || p_year_month || ''' and dps.task_code = pp.task_code) )';

      ELSIF p_biz_code = upper('reins_outward') THEN

        --清除临分分出计量单元
        EXECUTE IMMEDIATE 'delete from dm_buss_cmunit_fac_outwards cm
                  where exists (select 1 from dm_reins_outward ro where ro.entity_id = cm.entity_id and cm.fac_no = ro.ri_policy_no
                        and exists(select 1 from ods_data_push_signal dps where dps.push_model = upper(''reins_outward'')
                            and dps.year_month = ''' || p_year_month || ''' and dps.task_code = ro.task_code) )';

      ELSIF p_biz_code = upper('reins_treaty') THEN
        --清除合约计量单元
        EXECUTE IMMEDIATE 'delete from dm_buss_cmunit_treaty cm
                  where exists (select 1 from dm_reins_treaty rt where rt.entity_id = cm.entity_id and cm.treaty_no = rt.treaty_no
                        and rt.ri_direction_code = cm.ri_direction_code
                        and exists(select 1 from ods_data_push_signal dps where dps.push_model = upper(''reins_outward'')
                            and dps.year_month = ''' || p_year_month || ''' and dps.task_code = rt.task_code) )';

      END IF;

    END IF;

    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN
      --意外处理
      v_error_msg := '[EXCEPTION][计量单元清除]报错：' || SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004, v_error_msg);

  END proc_buss_data_drop_cmunit;

  /**
  ** proc name: proc_buss_data_drop_signal
  ** param: p_biz_code 指定的模型， p_year_month 指定的业务期间
  ** describe : 信号表清除
  ** author: lgx
  ** date: 2023-07-26
  **/
  PROCEDURE proc_buss_data_drop_signal(p_biz_code   VARCHAR2,
                                       p_year_month VARCHAR2) IS
    v_error_msg VARCHAR2(2000);
  BEGIN
    IF p_biz_code IS NULL
       AND p_year_month IS NULL THEN
      --清除信号表
      EXECUTE IMMEDIATE 'delete from ods_data_push_signal';
      EXECUTE IMMEDIATE 'delete from ods_data_push_signalhis';
      --重建信号表序列
      --ods_seq_data_push_signal
      --dm_pack_commonutils.drop_sequence('ods_seq_data_push_signal');
      -- Create sequence
      --dm_pack_commonutils.add_sequence('ods_seq_data_push_signal');

      --ods_seq_data_push_signalhis
      --dm_pack_commonutils.drop_sequence('ods_seq_data_push_signalhis');
      -- Create sequence
      --dm_pack_commonutils.add_sequence('ods_seq_data_push_signalhis');

    ELSIF p_biz_code IS NOT NULL
          AND p_year_month IS NULL THEN

      EXECUTE IMMEDIATE 'delete from ods_data_push_signal where push_model = ''' || p_biz_code || '''';

    ELSIF p_biz_code IS NOT NULL
          AND p_year_month IS NOT NULL THEN

      EXECUTE IMMEDIATE 'delete from ods_data_push_signal where push_model = ''' || p_biz_code || '''
                                 and year_month = ''' || p_year_month || '''';

    END IF;

    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN
      --意外处理
      v_error_msg := '[EXCEPTION][信号表清除]报错：' || SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004, v_error_msg);

  END proc_buss_data_drop_signal;
  /**
  ** proc name: proc_buss_data_insert_signal_err_data_deal
  ** param:  p_year_month 指定的业务期间
  ** describe : 校验异常信号表插入0
  ** author: lgx
  ** date: 2023-07-26
  **/
  PROCEDURE proc_buss_data_insert_signal_err_data_deal(p_year_month VARCHAR2) IS
    v_error_msg VARCHAR2(2000);
  BEGIN

    IF p_year_month IS NULL THEN
      ---校验异常插入0的任推送记录
      INSERT INTO ods_data_push_signal
        (data_push_signal_id,
         push_model,
         year_month,
         row_count,
         push_time,
         task_code,
         start_deal_time,
         task_status,
         deal_msg,
         end_deal_time)
        SELECT ods_seq_data_push_signal.nextval asdata_push_signal_id, --根据序列动态滚动
               dps.push_model AS push_model,
               dps.year_month AS year_month,
               0 AS row_count,
               systimestamp AS push_time,
               substr(dps.task_code, 0, 13) || lpad((to_number(substr(dps.task_code, 14, 5)) + 1), 5, '0') AS task_code, --往当前task_code加1个序号
               NULL AS start_deal_time,
               '0' AS task_status,
               NULL AS deal_msg,
               NULL AS end_deal_time
          FROM (SELECT t.year_month,
                       t.push_model,
                       MAX(task_code) AS task_code
                  FROM ods_data_push_signal t
                 WHERE t.task_status = '2' --0-带待处理，1-校验中，2-失败，3-成功，4-无效
                   AND NOT EXISTS (SELECT 1
                          FROM ods_data_push_signal a
                         WHERE a.year_month = t.year_month
                           AND a.push_model = t.push_model
                           AND a.task_status = '3' --前面已有成功的不会重复插入
                        )
                 GROUP BY t.year_month,
                          t.push_model) dps;

    ELSE
      ---校验异常插入0的任推送记录
      INSERT INTO ods_data_push_signal
        (data_push_signal_id,
         push_model,
         year_month,
         row_count,
         push_time,
         task_code,
         start_deal_time,
         task_status,
         deal_msg,
         end_deal_time)
        SELECT ods_seq_data_push_signal.nextval asdata_push_signal_id, --根据序列动态滚动
               dps.push_model AS push_model,
               dps.year_month AS year_month,
               0 AS row_count,
               systimestamp AS push_time,
               substr(dps.task_code, 0, 12) || (to_number(substr(dps.task_code, 13, 18)) + 1) AS task_code, --往当前task_code加1个序号
               NULL AS start_deal_time,
               '0' AS task_status,
               NULL AS deal_msg,
               NULL AS end_deal_time
          FROM (SELECT t.year_month,
                       t.push_model,
                       MAX(task_code) AS task_code
                  FROM ods_data_push_signal t
                 WHERE t.year_month = p_year_month --需要变动的业务期间
                   AND t.task_status = '2' --0-带待处理，1-校验中，2-失败，3-成功，4-无效
                   AND NOT EXISTS (SELECT 1
                          FROM ods_data_push_signal a
                         WHERE a.year_month = t.year_month
                           AND a.push_model = t.push_model
                           AND a.task_status = '3' --前面已有成功的不会重复插入
                        )
                 GROUP BY t.year_month,
                          t.push_model) dps;

    END IF;

    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN
      --意外处理
      v_error_msg := '[EXCEPTION][校验异常信号表插入0]报错：' || SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004, v_error_msg);
  END proc_buss_data_insert_signal_err_data_deal;

  /**
  ** proc name: proc_buss_data_drop_stat
  ** param: p_entity_id 指定业务单位， p_year_month 指定的业务期间
  ** describe : 数据统计清除
  ** author: lgx
  ** date: 2023-07-26
  **/
  PROCEDURE proc_buss_data_drop_stat(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2) IS
    v_error_msg VARCHAR2(2000);
  BEGIN

    IF p_year_month IS NULL THEN

      for cur in (select t.biz_code from dm_conf_table t where t.valid_is = '1') loop
        --8、清除统计数据
        EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_stat_'||cur.biz_code;

        --序列重置
        dm_pack_commonutils.drop_sequence('DM_SEQ_STAT_'||cur.biz_code);
        dm_pack_commonutils.add_sequence('DM_SEQ_STAT_'||cur.biz_code);
      end loop;

    ELSE
      dm_pack_duct_stat.proc_paring_stat_all();

    END IF;

    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN
      --意外处理
      v_error_msg := '[EXCEPTION][统计数量、统计金额清除]报错：' || SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004, v_error_msg);
  END proc_buss_data_drop_stat;

  /**
  ** proc name: proc_buss_data_drop_bussperiod
  ** param: p_entity_id 指定业务单位， p_year_month 指定的业务期间
  ** describe : 业务期间清除
  ** author: lgx
  ** date: 2023-07-26
  **/
  PROCEDURE proc_buss_data_drop_bussperiod(p_entity_id  NUMBER,
                                           p_year_month VARCHAR2) IS
    v_error_msg       VARCHAR2(2000);
    v_init_year_month VARCHAR2(50);
  BEGIN

    v_init_year_month := '201101';

    IF p_year_month IS NULL THEN

      --10、业务期间初始化
      EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_conf_bussperiod_detail';
      EXECUTE IMMEDIATE 'TRUNCATE TABLE dm_conf_bussperiod';

      --序列重置
      ----10、业务期间初始化
      --DM_SEQ_CONF_BUSSPERIOD
      dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_BUSSPERIOD');
      dm_pack_commonutils.add_sequence('DM_SEQ_CONF_BUSSPERIOD');

      --DM_SEQ_CONF_BUSSPERIOD_DETAIL
      dm_pack_commonutils.drop_sequence('DM_SEQ_CONF_BUSSPERIOD_DETAIL');
      dm_pack_commonutils.add_sequence('DM_SEQ_CONF_BUSSPERIOD_DETAIL');

      --初始化业务期间为201101
      INSERT INTO dm_conf_bussperiod
        (buss_period_id,
         entity_id,
         year_month,
         period_state,
         valid_is,
         creator_id,
         create_time,
         updator_id,
         update_time)
      VALUES
        (dm_seq_conf_bussperiod.nextval,
         p_entity_id,
         v_init_year_month,
         '0',
         '1',
         1,
         SYSDATE,
         0,
         NULL);

      --11、增加业务详细表数据
      FOR cur IN (SELECT biz_type_id,
                         '1' AS direction
                    FROM dm_conf_table
                   WHERE valid_is = '1'
                  UNION ALL
                  SELECT biz_type_id,
                         '0' AS direction
                    FROM dm_conf_table_output
                   WHERE valid_is = '1'
                   ORDER BY direction DESC,
                            biz_type_id) LOOP
        INSERT INTO dm_conf_bussperiod_detail
          (period_detail_id,
           buss_period_id,
           biz_type_id,
           exec_result,
           direction,
           ready_state,
           creator_id,
           create_time,
           updator_id,
           update_time)
        VALUES
          (dm_seq_conf_bussperiod_detail.nextval,
           (SELECT buss_period_id FROM dm_conf_bussperiod WHERE year_month = v_init_year_month),
           cur.biz_type_id,
           NULL,
           cur.direction,
           '0',
           1,
           SYSDATE,
           NULL,
           NULL);

        COMMIT;

      END LOOP;

    ELSE
      --更改指定业务期间状态为准备中

      update dm_conf_bussperiod
      set period_state = '0'
      where year_month = p_year_month
      and entity_id = p_entity_id;

      commit;

        update dm_conf_bussperiod_detail
      set READY_STATE = '0'
      where BUSS_PERIOD_ID =
      (select BUSS_PERIOD_ID from dm_conf_bussperiod where year_month = p_year_month and entity_id = p_entity_id );

      commit;

    END IF;


  EXCEPTION
    WHEN OTHERS THEN
      --意外处理
      v_error_msg := '[EXCEPTION][业务期间清除]报错：' || SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004, v_error_msg);
  END proc_buss_data_drop_bussperiod;


  /**
  ** proc name: proc_buss_data_pass_check_msg
  ** param: p_biz_code 指定模型， p_check_msg 指定异常信息
  ** describe : 异常数据手动通过
  ** author: lgx
  ** date: 2023-07-26
  **/
  PROCEDURE proc_buss_data_pass_check_msg(p_biz_code  VARCHAR2,
                                          p_check_msg VARCHAR2) IS
    v_error_msg   VARCHAR2(2000);
    v_count       NUMBER;
    v_biz_type_id NUMBER;
    TYPE cur_model_type IS REF CURSOR;
    c1 cur_model_type;

    v_year_month VARCHAR2(100);
    v_task_code  ods_policy_main.task_code%TYPE;
    v_draw_type  ods_policy_main.draw_type%TYPE;
  BEGIN
    SELECT COUNT(1)
      INTO v_count
      FROM dm_conf_table
     WHERE valid_is = '1'
       AND biz_code = p_biz_code;
    IF v_count < 0 THEN
      v_error_msg := '[EXCEPTION][异常数据手动通过]无效模型：' || p_biz_code;
      raise_application_error(-20002, v_error_msg);
    END IF;

    SELECT biz_type_id
      INTO v_biz_type_id
      FROM dm_conf_table
     WHERE valid_is = '1'
       AND biz_code = p_biz_code;

    --更新状态
    EXECUTE IMMEDIATE ' merge into ods_' || p_biz_code || ' a
        using (select * from ods_' || p_biz_code || ' where check_msg = ''' || p_check_msg || ''') b
        on (a.id = b.id)
        when matched then
          update set
             a.task_status = :1'
      USING '3';
    COMMIT;
    OPEN c1 FOR 'select distinct task_code,draw_type
                                  from ods_' || p_biz_code || ' where task_status = ''3'' order by task_code asc';
    LOOP
      FETCH c1
        INTO v_task_code,
             v_draw_type;
      EXIT WHEN c1%NOTFOUND;
      v_year_month := substr(v_task_code, 8, 6);
      dm_pack_data_verify.proc_data_verify_to_target(1, v_task_code, v_year_month, v_biz_type_id, 1, v_draw_type);
      --对校验已完成的数据进行统计
      dm_pack_duct_stat.proc_paring_stat(1, v_task_code, v_biz_type_id);
    END LOOP;
    CLOSE c1;

  EXCEPTION
    WHEN OTHERS THEN
      --意外处理
      v_error_msg := '[EXCEPTION][异常数据手动通过]报错：' || SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004, v_error_msg);
  END proc_buss_data_pass_check_msg;

  /**
  ** proc name: proc_buss_data_verify
  ** param: p_biz_code 指定模型， p_year_month 指定业务期间
  ** describe : 数据校验，参数为空时，全量校验
  ** author: lgx
  ** date: 2023-07-26
  **/
  PROCEDURE proc_buss_data_verify(p_biz_code   VARCHAR2,
                                  p_year_month VARCHAR2) IS
    v_error_msg VARCHAR2(2000);
  BEGIN

    IF p_biz_code IS NULL
       AND p_year_month IS NULL THEN
      --全量校验
      FOR cur_verify IN (SELECT ps.task_code  AS task_code,
                                ps.year_month AS year_month,
                                t.biz_code    AS biz_code,
                                t.biz_type_id AS biz_type_id,
                                t.type_group  AS type_group
                           FROM ods_data_push_signal ps
                           LEFT JOIN dm_conf_table t
                             ON ps.push_model = t.biz_code
                          WHERE ps.task_status = '0'
                          ORDER BY ps.year_month,
                                   (CASE
                                     WHEN t.type_group = '7' THEN
                                      '0'
                                     ELSE
                                      t.type_group
                                   END),
                                   t.display_no,
                                   ps.task_code) LOOP

        dm_pack_data_verify.proc_data_verify(1, cur_verify.task_code, cur_verify.biz_type_id, 1, '2');

      END LOOP;

    ELSIF p_biz_code IS NOT NULL
          AND p_year_month IS NOT NULL THEN
      --指定模型，指定业务期间校验
      FOR cur_verify IN (SELECT ps.task_code  AS task_code,
                                ps.year_month AS year_month,
                                t.biz_code    AS biz_code,
                                t.biz_type_id AS biz_type_id,
                                t.type_group  AS type_group
                           FROM ods_data_push_signal ps
                           LEFT JOIN dm_conf_table t
                             ON ps.push_model = t.biz_code
                          WHERE ps.task_status = '0'
                            AND ps.push_model = upper(p_biz_code)
                            AND ps.year_month = p_year_month
                          ORDER BY ps.year_month,
                                   (CASE
                                     WHEN t.type_group = '7' THEN
                                      '0'
                                     ELSE
                                      t.type_group
                                   END),
                                   t.display_no,
                                   ps.task_code) LOOP

        dm_pack_data_verify.proc_data_verify(1, cur_verify.task_code, cur_verify.biz_type_id, 1, '2');

      END LOOP;
    ELSIF p_biz_code IS NULL
          AND p_year_month IS NOT NULL THEN
      --指定业务期间校验
      FOR cur_verify IN (SELECT ps.task_code  AS task_code,
                                ps.year_month AS year_month,
                                t.biz_code    AS biz_code,
                                t.biz_type_id AS biz_type_id,
                                t.type_group  AS type_group
                           FROM ods_data_push_signal ps
                           LEFT JOIN dm_conf_table t
                             ON ps.push_model = t.biz_code
                          WHERE ps.task_status = '0'
                            AND ps.year_month = p_year_month
                          ORDER BY ps.year_month,
                                   (CASE
                                     WHEN t.type_group = '7' THEN
                                      '0'
                                     ELSE
                                      t.type_group
                                   END),
                                   t.display_no,
                                   ps.task_code) LOOP

        dm_pack_data_verify.proc_data_verify(1, cur_verify.task_code, cur_verify.biz_type_id, 1, '2');

      END LOOP;

    END IF;

  EXCEPTION
    WHEN OTHERS THEN
      --意外处理
      v_error_msg := '[EXCEPTION][数据校验]报错：' || SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004, v_error_msg);
  END proc_buss_data_verify;

END dm_pack_data_rollback;
/
