<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-02-10 16:34:25 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussDDLrcIcuCalcDao">
  <!-- 本配置文件由GIS MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**IDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->
  <resultMap id="CustResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussDDLrcIcuCalcVo">
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="ACTION_NO" property="actionNo" jdbcType="VARCHAR" />
    <result column="TASK_CODE" property="taskCode" jdbcType="VARCHAR" />
    <result column="CALC_TYPE" property="calcType" jdbcType="VARCHAR" />
    <result column="DATA_KEY" property="dataKey" jdbcType="DECIMAL" />
    <result column="entity_id" property="entityId" jdbcType="DECIMAL" />
    <result column="POLICY_NO" property="policyNo" jdbcType="VARCHAR" />
    <result column="ENDORSE_SEQ_NO" property="endorseSeqNo" jdbcType="VARCHAR" />
    <result column="CURRENCY_CODE" property="currencyCode" jdbcType="VARCHAR" />
    <result column="YEAR_MONTH" property="yearMonth" jdbcType="VARCHAR" />
    <result column="PORTFOLIO_NO" property="portfolioNo" jdbcType="VARCHAR" />
    <result column="ICG_NO" property="icgNo" jdbcType="VARCHAR" />
    <result column="EVALUATE_APPROACH" property="evaluateApproach" jdbcType="VARCHAR" />
    <result column="LOA_CODE" property="loaCode" jdbcType="VARCHAR" />
    <result column="CMUNIT_NO" property="cmunitNo" jdbcType="VARCHAR" />
    <result column="PRODUCT_CODE" property="productCode" jdbcType="VARCHAR" />
    <result column="EVALUATE_DATE" property="evaluateDate" jdbcType="TIMESTAMP" />
    <result column="CONTRACT_DATE" property="contractDate" jdbcType="TIMESTAMP" />
    <result column="EFFECTIVE_DATE_IN_DATE" property="effectiveDateInDate" jdbcType="TIMESTAMP" />
    <result column="APPROVAL_DATE_IN_DATE" property="approvalDateInDate" jdbcType="TIMESTAMP" />
    <result column="EXPIRY_DATE_IN_DATE" property="expiryDateInDate" jdbcType="TIMESTAMP" />
    <result column="EFFECTIVE_DATE_BOM" property="effectiveDateBom" jdbcType="TIMESTAMP" />
    <result column="EXPIRY_DATE_EOM" property="expiryDateEom" jdbcType="TIMESTAMP" />
    <result column="payment_frequency_code" property="paymentFrequencyCode" jdbcType="VARCHAR" />
    <result column="payment_frequency_no" property="paymentFrequencyNo" jdbcType="DECIMAL" />
    <result column="GROSS_PREMIUM" property="grossPremium" jdbcType="DECIMAL" />
    <result column="COVERAGE_AMOUNT" property="coverageAmount" jdbcType="DECIMAL" />
    <result column="FACULTATIVE_IS" property="facultativeIs" jdbcType="VARCHAR" />
    <result column="PASSED_DATES" property="passedDates" jdbcType="DECIMAL" />
    <result column="PASSED_MONTHS" property="passedMonths" jdbcType="DECIMAL" />
    <result column="REMAINING_MONTHS" property="remainingMonths" jdbcType="DECIMAL" />
    <result column="REMAINING_PREM_TERM_PE" property="remainingPremTermPe" jdbcType="DECIMAL" />
    <result column="REMAINING_MONTHS_FUTURE" property="remainingMonthsFuture" jdbcType="DECIMAL" />
    <result column="REMAINING_PREM_TERM_CB" property="remainingPremTermCb" jdbcType="DECIMAL" />
    <result column="PAYMENT_QUARTER" property="paymentQuarter" jdbcType="DECIMAL" />
    <result column="ed_premium_per_coverage_day" property="edPremiumPerCoverageDay" jdbcType="DECIMAL" />
    <result column="ED_PREMIUM" property="edPremium" jdbcType="DECIMAL" />
    <result column="REMAINING_QUARTERS" property="remainingQuarters" jdbcType="DECIMAL" />
    <result column="PRI_CUR_END_REMAIN_CSM_RATE" property="priCurEndRemainCsmRate" jdbcType="DECIMAL" />
    <result column="PRI_UNTIL_REPORT_REMAIN_CSM_RATE" property="priUntilReportRemainCsmRate" jdbcType="DECIMAL" />
    <result column="CUMULATIVE_ED_RATE" property="cumulativeEdRate" jdbcType="DECIMAL" />
    <result column="CUR_END_REMAIN_CSM_RATE" property="curEndRemainCsmRate" jdbcType="DECIMAL" />
    <result column="UNTIL_REPORT_REMAIN_CSM_RATE" property="untilReportRemainCsmRate" jdbcType="DECIMAL" />
    <result column="DEPT_ID" property="deptId" jdbcType="DECIMAL"/>
    <result column="ELR" property="elr" jdbcType="DECIMAL"/>

      <result column="entity_code" property="entityCode" jdbcType="VARCHAR" />
    <result column="entity_c_name" property="entityCName" jdbcType="VARCHAR" />
    <result column="entity_l_name" property="entityLName" jdbcType="VARCHAR" />
    <result column="entity_e_name" property="entityEName" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Cust_Column_List">
    a.ID, a.TASK_CODE, a.CALC_TYPE, a.DATA_KEY, a.entity_id, a.POLICY_NO, a.ENDORSE_SEQ_NO, a.currency_code,
    a.YEAR_MONTH, a.PORTFOLIO_NO, a.ICG_NO, a.EVALUATE_APPROACH, a.LOA_CODE, a.CMUNIT_NO,
    a.PRODUCT_CODE, a.EVALUATE_DATE, a.CONTRACT_DATE, a.EFFECTIVE_DATE_IN_DATE, a.APPROVAL_DATE_IN_DATE,
    a.EXPIRY_DATE_IN_DATE, a.EFFECTIVE_DATE_BOM, a.EXPIRY_DATE_EOM, a.payment_frequency_code, a.payment_frequency_no,
    a.GROSS_PREMIUM, a.COVERAGE_AMOUNT, a.FACULTATIVE_IS, a.PASSED_DATES, a.PASSED_MONTHS, a.REMAINING_MONTHS,
    a.REMAINING_PREM_TERM_PE, a.REMAINING_MONTHS_FUTURE, a.REMAINING_PREM_TERM_CB, a.PAYMENT_QUARTER,
    a.ed_premium_per_coverage_day, a.ED_PREMIUM, a.PRI_CUR_END_REMAIN_CSM_RATE,
    a.PRI_UNTIL_REPORT_REMAIN_CSM_RATE, a.CUMULATIVE_ED_RATE, a.CUR_END_REMAIN_CSM_RATE, a.UNTIL_REPORT_REMAIN_CSM_RATE,
    a.DEPT_ID, a.ELR
    c.entity_e_name, c.entity_c_name, c.entity_l_name
  </sql>


  <select id="findDateByVo" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
    select
    a.ID as "id",
    a.ACTION_NO as "actionNo",
    a.TASK_CODE as "taskCode",
	a.DATA_KEY as "dataKey",
	a.entity_id as "entityId",
	a.POLICY_NO as "policyNo",
	a.ENDORSE_SEQ_NO as "endorseSeqNo",
	a.currency_code as "currency",
	a.YEAR_MONTH as "yearMonth",
	a.PORTFOLIO_NO as "portfolioNo",
	a.ICG_NO as "icgNo",
	a.EVALUATE_APPROACH as "evaluateApproach",
	a.LOA_CODE as "loaCode",
	a.CMUNIT_NO as "cmunitNo",
	a.PRODUCT_CODE as "productCode",
	to_char(a.EVALUATE_DATE,'yyyy/mm/dd') as "evaluateDate",
	to_char(a.CONTRACT_DATE,'yyyy/mm/dd') as "contractDate",
	to_char(a.EFFECTIVE_DATE_IN_DATE,'yyyy/mm/dd') as "effectiveDateInDate",
	to_char(a.APPROVAL_DATE_IN_DATE,'yyyy/mm/dd') as "checkDateInDate",
	to_char(a.EXPIRY_DATE_IN_DATE,'yyyy/mm/dd') as "expiryDateInDate",
	to_char(a.EFFECTIVE_DATE_BOM,'yyyy/mm/dd') as "effectiveDateBom",
	to_char(a.EXPIRY_DATE_EOM,'yyyy/mm/dd') as "expiryDateEom",
	a.payment_frequency_code as "premiumFrequency",
	a.payment_frequency_no as "premiumTerm",
	a.GROSS_PREMIUM as "grossPremium",
	a.COVERAGE_AMOUNT as "coverageAmount",
	a.FACULTATIVE_IS as "facultativeIs",
	a.PASSED_DATES as "passedDates",
	a.PASSED_MONTHS as "passedMonths",
	a.remaining_months_for_recv as "remainingMonthsForRecv",
	a.REMAINING_PREM_TERM_PE as "remainingPremTermPe",
	a.REMAINING_MONTHS_FUTURE as "remainingMonthsFuture",
	a.REMAINING_PREM_TERM_CB as "remainingPremTermCb",
	a.PAYMENT_QUARTER as "paymentQuarter",
	a.ed_premium_per_coverage_day as "edPremiumPerCoverageDay",
	a.ED_PREMIUM as "edPremium",
	a.PRI_CUR_END_REMAIN_CSM_RATE as "priCurEndRemainCsmRate",
    a.PRI_UNTIL_REPORT_REMAIN_CSM_RATE as "priUntilReportRemainCsmRate",
    a.CUMULATIVE_ED_RATE as "cumulativeEdRate",
    a.CUR_END_REMAIN_CSM_RATE as "curEndRemainCsmRate",
    a.UNTIL_REPORT_REMAIN_CSM_RATE as "untilReportRemainCsmRate",
    a.DEPT_ID as "deptId",
    a.ELR as "elr",
    (select QUOTA_VALUE from Atr_Buss_Quota_Value t1 where a.action_no=t1.action_no
    AND a.loa_code=t1.dimension_value AND quota_code='BE012'  limit 1) as "BE012",
    (select QUOTA_VALUE from Atr_Buss_Quota_Value t1 where a.action_no=t1.action_no
    AND a.loa_code=t1.dimension_value AND quota_code='QR003'  limit 1) as "QR003",
    (select QUOTA_VALUE from Atr_Buss_Quota_Value t1 where a.action_no=t1.action_no
    AND a.loa_code=t1.dimension_value AND quota_code='QR010' limit 1) as "QR010",
	c.entity_code as "entityCode",
	c.entity_e_name as "entityEName",
	c.entity_c_name as "entityCName",
	c.entity_l_name  as "entityLName"
    from ATR_BUSS_DD_LRC_ICU_CALC a
    left join bpluser.bbs_conf_entity c on a.entity_id = c.entity_id
    left join bpluser.BBS_CONF_CURRENCY cur on a.currency_code = cur.CURRENCY_CODE
    where a.action_no = #{actionNo,jdbcType=VARCHAR}
  </select>


    <select id="countDateByVo" fetchSize="2000" flushCache="false" useCache="true" resultType="Long"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
        select
        count(a.ACTION_NO) as "actionNo"
        from atruser.ATR_BUSS_DD_LRC_ICU_CALC a
        where a.action_no = #{actionNo,jdbcType=VARCHAR}
        <if test="portfolioNo != null and portfolioNo != ''">
            and a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
        </if>
        <if test="icgNo != null and icgNo != ''">
            and a.icg_no = #{icgNo,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="findDateByVo2" fetchSize="2000" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
      select
        a.ACTION_NO as "actionNo",
        a.POLICY_NO as "policyNo",
        a.ENDORSE_SEQ_NO as "endorseSeqNo",
        a.currency_code as "currency",
        a.YEAR_MONTH as "yearMonth",
        a.PORTFOLIO_NO as "portfolioNo",
        a.ICG_NO as "icgNo",
        a.EVALUATE_APPROACH as "evaluateApproach",
        a.LOA_CODE as "loaCode",
        a.CMUNIT_NO as "cmunitNo",
        a.PRODUCT_CODE as "productCode",
        to_char(a.EVALUATE_DATE,'yyyy/mm/dd') as "evaluateDate",
        to_char(a.CONTRACT_DATE,'yyyy/mm/dd') as "contractDate",
        to_char(a.EFFECTIVE_DATE_IN_DATE,'yyyy/mm/dd') as "effectiveDateInDate",
        to_char(a.APPROVAL_DATE_IN_DATE,'yyyy/mm/dd') as "checkDateInDate",
        to_char(a.EXPIRY_DATE_IN_DATE,'yyyy/mm/dd') as "expiryDateInDate",
        to_char(a.EFFECTIVE_DATE_BOM,'yyyy/mm/dd') as "effectiveDateBom",
        to_char(a.EXPIRY_DATE_EOM,'yyyy/mm/dd') as "expiryDateEom",
        a.payment_frequency_code as "premiumFrequency",
        a.payment_frequency_no as "premiumTerm",
        a.GROSS_PREMIUM as "grossPremium",
        a.COVERAGE_AMOUNT as "coverageAmount",
        a.FACULTATIVE_IS as "facultativeIs",
        a.PASSED_DATES as "passedDates",
        a.PASSED_MONTHS as "passedMonths",
        a.remaining_months_for_recv as "remainingMonthsForRecv",
        a.REMAINING_PREM_TERM_PE as "remainingPremTermPe",
        a.REMAINING_MONTHS_FUTURE as "remainingMonthsFuture",
        a.REMAINING_PREM_TERM_CB as "remainingPremTermCb",
        a.PAYMENT_QUARTER as "paymentQuarter",
        a.CUMULATIVE_PAID_PREMIUM AS "cumulativePaidPremium",
        a.ed_premium_per_coverage_day as "edPremiumPerCoverageDay",
        a.ED_PREMIUM as "edPremium",
        a.PRI_CUR_END_REMAIN_CSM_RATE as "priCurEndRemainCsmRate",
        a.PRI_UNTIL_REPORT_REMAIN_CSM_RATE as "priUntilReportRemainCsmRate",
        a.CUMULATIVE_ED_RATE as "cumulativeEdRate",
        a.CUR_END_REMAIN_CSM_RATE as "curEndRemainCsmRate",
        a.UNTIL_REPORT_REMAIN_CSM_RATE as "untilReportRemainCsmRate",
        a.ELR as "elr",
        a.iacf_Fee_Rate as "iacfFeeRate",
        (select QUOTA_VALUE from atruser.ATR_Buss_Quota_Value t1 where a.action_no=t1.action_no
        AND a.loa_code=t1.dimension_value AND quota_code='QR003'  limit 1) as "QR003",
        (select QUOTA_VALUE from atruser.ATR_Buss_Quota_Value t1 where a.action_no=t1.action_no
        AND a.loa_code=t1.dimension_value AND quota_code='QR010' limit 1) as "QR010",
        c.entity_code as "entityCode",
        c.entity_e_name as "entityEName"
        <foreach collection="devNoList" item="item" index="index" open="," separator=",">
            MAX(CASE cd.DEV_NO WHEN ${item} THEN cd.${feeType} ELSE NULL END) AS "${item}"
        </foreach>
        from (select * from atruser.ATR_BUSS_DD_LRC_ICU_CALC a
                where a.action_no = #{actionNo,jdbcType=VARCHAR}
                <if test="portfolioNo != null and portfolioNo != ''">
                    and a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
                </if>
                <if test="icgNo != null and icgNo != ''">
                    and a.icg_no = #{icgNo,jdbcType=VARCHAR}
                </if>
                order by a.ICG_NO,a.POLICY_NO
                <if test="limit != null and offset != null">
                    limit ${limit} offset ${offset}
                </if>
            ) a
        left join atruser.ATR_BUSS_DD_LRC_ICU_CALC_detail cd on a.id = cd.main_id
        left join bpluser.bbs_conf_entity c on a.entity_id = c.entity_id
        GROUP BY
        a.ACTION_NO,
        a.POLICY_NO,
        a.ENDORSE_SEQ_NO,
        a.currency_code,
        a.YEAR_MONTH,
        a.PORTFOLIO_NO,
        a.ICG_NO,
        a.EVALUATE_APPROACH,
        a.LOA_CODE,
        a.CMUNIT_NO,
        a.PRODUCT_CODE,
        to_char(a.EVALUATE_DATE,'yyyy/mm/dd'),
        to_char(a.CONTRACT_DATE,'yyyy/mm/dd'),
        to_char(a.EFFECTIVE_DATE_IN_DATE,'yyyy/mm/dd'),
        to_char(a.APPROVAL_DATE_IN_DATE,'yyyy/mm/dd'),
        to_char(a.EXPIRY_DATE_IN_DATE,'yyyy/mm/dd'),
        to_char(a.EFFECTIVE_DATE_BOM,'yyyy/mm/dd'),
        to_char(a.EXPIRY_DATE_EOM,'yyyy/mm/dd'),
        a.payment_frequency_code,
        a.payment_frequency_no ,
        a.GROSS_PREMIUM ,
        a.COVERAGE_AMOUNT ,
        a.FACULTATIVE_IS ,
        a.PASSED_DATES ,
        a.PASSED_MONTHS ,
        a.REMAINING_MONTHS_FOR_RECV ,
        a.REMAINING_PREM_TERM_PE ,
        a.REMAINING_MONTHS_FUTURE ,
        a.REMAINING_PREM_TERM_CB ,
        a.PAYMENT_QUARTER ,
        a.CUMULATIVE_PAID_PREMIUM,
        a.ed_premium_per_coverage_day ,
        a.ED_PREMIUM,
        a.PRI_CUR_END_REMAIN_CSM_RATE ,
        a.PRI_UNTIL_REPORT_REMAIN_CSM_RATE,
        a.CUMULATIVE_ED_RATE,
        a.CUR_END_REMAIN_CSM_RATE ,
        a.UNTIL_REPORT_REMAIN_CSM_RATE ,
        a.ELR,
        a.iacf_Fee_Rate,
        c.entity_code ,
        c.entity_e_name,
        a.id
        order by a.id
    </select>



    <select id="findPublicDateByVo" fetchSize="2000" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
        select
        a.ACTION_NO as "actionNo",
        a.POLICY_NO as "policyNo",
        a.ENDORSE_SEQ_NO as "endorseSeqNo",
        a.currency_code as "currency",
        a.YEAR_MONTH as "yearMonth",
        a.PORTFOLIO_NO as "portfolioNo",
        a.ICG_NO as "icgNo",
        a.EVALUATE_APPROACH as "evaluateApproach",
        a.LOA_CODE as "loaCode",
        a.CMUNIT_NO as "cmunitNo",
        a.PRODUCT_CODE as "productCode",
        to_char(a.EVALUATE_DATE,'yyyy/mm/dd') as "evaluateDate",
        to_char(a.CONTRACT_DATE,'yyyy/mm/dd') as "contractDate",
        to_char(a.EFFECTIVE_DATE_IN_DATE,'yyyy/mm/dd') as "effectiveDateInDate",
        to_char(a.APPROVAL_DATE_IN_DATE,'yyyy/mm/dd') as "checkDateInDate",
        to_char(a.EXPIRY_DATE_IN_DATE,'yyyy/mm/dd') as "expiryDateInDate",
        to_char(a.EFFECTIVE_DATE_BOM,'yyyy/mm/dd') as "effectiveDateBom",
        to_char(a.EXPIRY_DATE_EOM,'yyyy/mm/dd') as "expiryDateEom",
        a.payment_frequency_code as "premiumFrequency",
        a.payment_frequency_no as "premiumTerm",
        a.GROSS_PREMIUM as "grossPremium",
        a.COVERAGE_AMOUNT as "coverageAmount",
        a.FACULTATIVE_IS as "facultativeIs",
        a.PASSED_DATES as "passedDates",
        a.PASSED_MONTHS as "passedMonths",
        a.remaining_months_for_recv as "remainingMonthsForRecv",
        a.REMAINING_PREM_TERM_PE as "remainingPremTermPe",
        a.REMAINING_MONTHS_FUTURE as "remainingMonthsFuture",
        a.REMAINING_PREM_TERM_CB as "remainingPremTermCb",
        a.PAYMENT_QUARTER as "paymentQuarter",
        a.ed_premium_per_coverage_day as "edPremiumPerCoverageDay",
        a.ED_PREMIUM as "edPremium",
        a.PRI_CUR_END_REMAIN_CSM_RATE as "priCurEndRemainCsmRate",
        a.PRI_UNTIL_REPORT_REMAIN_CSM_RATE as "priUntilReportRemainCsmRate",
        a.CUMULATIVE_ED_RATE as "cumulativeEdRate",
        a.CUR_END_REMAIN_CSM_RATE as "curEndRemainCsmRate",
        a.UNTIL_REPORT_REMAIN_CSM_RATE as "untilReportRemainCsmRate",
        a.ELR as "elr",
        (select QUOTA_VALUE from atruser.ATR_Buss_Quota_Value t1 where a.action_no=t1.action_no
        AND a.loa_code=t1.dimension_value AND quota_code='BE012' limit 1) as "BE012",
        (select QUOTA_VALUE from atruser.ATR_Buss_Quota_Value t1 where a.action_no=t1.action_no
        AND a.loa_code=t1.dimension_value AND quota_code='QR003' limit 1) as "QR003",
        (select QUOTA_VALUE from atruser.ATR_Buss_Quota_Value t1 where a.action_no=t1.action_no
        AND a.loa_code=t1.dimension_value AND quota_code='QR010' limit 1) as "QR010",
        c.entity_code as "entityCode",
        c.entity_e_name as "entityEName",
        c.entity_c_name as "entityCName",
        c.entity_l_name  as "entityLName"
        from atruser.ATR_BUSS_DD_LRC_ICU_CALC a
        left join atruser.ATR_BUSS_DD_LRC_ICU_CALC_detail cd on a.id = cd.main_id
        left join bpluser.bbs_conf_entity c on a.entity_id = c.entity_id
        where a.action_no = #{actionNo,jdbcType=VARCHAR}
        <if test="portfolioNo != null and portfolioNo != ''">
            and a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
        </if>
        <if test="icgNo != null and icgNo != ''">
            and a.icg_no = #{icgNo,jdbcType=VARCHAR}
        </if>
        GROUP BY
        a.ACTION_NO,
        a.entity_id,
        a.POLICY_NO,
        a.ENDORSE_SEQ_NO,
        a.currency_code,
        a.YEAR_MONTH,
        a.PORTFOLIO_NO,
        a.ICG_NO,
        a.EVALUATE_APPROACH,
        a.LOA_CODE,
        a.CMUNIT_NO,
        a.PRODUCT_CODE,
        to_char(a.EVALUATE_DATE,'yyyy/mm/dd'),
        to_char(a.CONTRACT_DATE,'yyyy/mm/dd'),
        to_char(a.EFFECTIVE_DATE_IN_DATE,'yyyy/mm/dd'),
        to_char(a.APPROVAL_DATE_IN_DATE,'yyyy/mm/dd'),
        to_char(a.EXPIRY_DATE_IN_DATE,'yyyy/mm/dd'),
        to_char(a.EFFECTIVE_DATE_BOM,'yyyy/mm/dd'),
        to_char(a.EXPIRY_DATE_EOM,'yyyy/mm/dd'),
        a.payment_frequency_code,
        a.payment_frequency_no ,
        a.GROSS_PREMIUM ,
        a.COVERAGE_AMOUNT ,
        a.FACULTATIVE_IS ,
        a.PASSED_DATES ,
        a.PASSED_MONTHS ,
        a.REMAINING_MONTHS_FOR_RECV ,
        a.REMAINING_PREM_TERM_PE ,
        a.REMAINING_MONTHS_FUTURE ,
        a.REMAINING_PREM_TERM_CB ,
        a.PAYMENT_QUARTER ,
        a.ed_premium_per_coverage_day ,
        a.ED_PREMIUM,
        a.PRI_CUR_END_REMAIN_CSM_RATE ,
        a.PRI_UNTIL_REPORT_REMAIN_CSM_RATE,
        a.CUMULATIVE_ED_RATE,
        a.CUR_END_REMAIN_CSM_RATE ,
        a.UNTIL_REPORT_REMAIN_CSM_RATE ,
        a.ELR,
        c.entity_code  ,
        c.entity_e_name,
        c.entity_c_name,
        c.entity_l_name
        <if test="limit != null and offset != null">
            limit ${limit} offset ${offset}
        </if>
    </select>

    <select id="findDevelopDateByVo" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
    select
    <foreach collection="devNoList" item="item" index="index" open=","  separator=",">
        MAX(CASE cd.DEV_NO WHEN ${item} THEN cd.${feeType} ELSE NULL END) AS "${item}"
    </foreach>
    from ATR_BUSS_DD_LRC_ICU_CALC a
    left join bpluser.bbs_conf_entity c on a.entity_id = c.entity_id
    left join bpluser.BBS_CONF_CURRENCY cur on a.currency_code = cur.CURRENCY_CODE
    where a.action_no = #{actionNo,jdbcType=VARCHAR}
        <if test="portfolioNo != null and portfolioNo != ''">
            and a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
        </if>
        <if test="icgNo != null and icgNo != ''">
            and a.icg_no = #{icgNo,jdbcType=VARCHAR}
        </if>
        GROUP BY
        a.ACTION_NO,
        a.entity_id,
        a.POLICY_NO,
        a.ENDORSE_SEQ_NO,
        a.currency_code,
        a.YEAR_MONTH,
        a.PORTFOLIO_NO,
        a.ICG_NO,
        a.EVALUATE_APPROACH,
        a.LOA_CODE,
        a.CMUNIT_NO,
        a.PRODUCT_CODE,
        to_char(a.EVALUATE_DATE,'yyyy/mm/dd'),
        to_char(a.CONTRACT_DATE,'yyyy/mm/dd'),
        to_char(a.EFFECTIVE_DATE_IN_DATE,'yyyy/mm/dd'),
        to_char(a.APPROVAL_DATE_IN_DATE,'yyyy/mm/dd'),
        to_char(a.EXPIRY_DATE_IN_DATE,'yyyy/mm/dd'),
        to_char(a.EFFECTIVE_DATE_BOM,'yyyy/mm/dd'),
        to_char(a.EXPIRY_DATE_EOM,'yyyy/mm/dd'),
        a.payment_frequency_code,
        a.payment_frequency_no ,
        a.GROSS_PREMIUM ,
        a.COVERAGE_AMOUNT ,
        a.FACULTATIVE_IS ,
        a.PASSED_DATES ,
        a.PASSED_MONTHS ,
        a.REMAINING_MONTHS_FOR_RECV ,
        a.REMAINING_PREM_TERM_PE ,
        a.REMAINING_MONTHS_FUTURE ,
        a.REMAINING_PREM_TERM_CB ,
        a.PAYMENT_QUARTER ,
        a.ed_premium_per_coverage_day ,
        a.ED_PREMIUM,
        a.PRI_CUR_END_REMAIN_CSM_RATE ,
        a.PRI_UNTIL_REPORT_REMAIN_CSM_RATE,
        a.CUMULATIVE_ED_RATE,
        a.CUR_END_REMAIN_CSM_RATE ,
        a.UNTIL_REPORT_REMAIN_CSM_RATE ,
        a.ELR,
        c.entity_code  ,
        c.entity_e_name,
        c.entity_c_name,
        c.entity_l_name
        <if test="limit != null and offset != null">
            limit ${limit} offset ${offset}
        </if>
  </select>

    <select id="findDateByVo1" flushCache="false" useCache="true" resultType="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLrcIcuCalcExportVo"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
        select
        a.ACTION_NO as actionNo,
        a.POLICY_NO as policyNo,
        a.ENDORSE_SEQ_NO as endorseSeqNo,
        a.currency_code as currency,
        a.YEAR_MONTH as yearMonth,
        a.PORTFOLIO_NO as portfolioNo,
        a.ICG_NO as icgNo,
        a.EVALUATE_APPROACH as evaluateApproach,
        a.LOA_CODE as loaCode,
        a.CMUNIT_NO as cmunitNo,
        a.PRODUCT_CODE as productCode,
        to_char(a.EVALUATE_DATE,'yyyy/mm/dd') as evaluateDate,
        to_char(a.CONTRACT_DATE,'yyyy/mm/dd') as contractDate,
        to_char(a.EFFECTIVE_DATE_IN_DATE,'yyyy/mm/dd') as effectiveDateInDate,
        to_char(a.EXPIRY_DATE_IN_DATE,'yyyy/mm/dd') as expiryDateInDate,
        to_char(a.EFFECTIVE_DATE_BOM,'yyyy/mm/dd') as effectiveDateBom,
        to_char(a.EXPIRY_DATE_EOM,'yyyy/mm/dd') as expiryDateEom,
        a.payment_frequency_code as premiumFrequency,
        a.payment_frequency_no as premiumTerm,
        a.GROSS_PREMIUM as grossPremium,
        a.COVERAGE_AMOUNT as coverageAmount,
        a.PASSED_MONTHS as passedMonths,
        a.remaining_months_for_recv as remainingMonthsForRecv,
        a.REMAINING_PREM_TERM_PE as remainingPremTermPe,
        a.REMAINING_MONTHS_FUTURE as remainingMonthsFuture,
        a.REMAINING_PREM_TERM_CB as remainingPremTermCb,
        a.CUMULATIVE_PAID_PREMIUM AS cumulativePaidPremium,
        a.ed_premium_per_coverage_day as edPremiumPerCoverageDay,
        a.PRI_CUR_END_REMAIN_CSM_RATE as priCurEndRemainCsmRate,
        a.PRI_UNTIL_REPORT_REMAIN_CSM_RATE as priUntilReportRemainCsmRate,
        a.CUMULATIVE_ED_RATE as cumulativeEdRate,
        a.CUR_END_REMAIN_CSM_RATE as curEndRemainCsmRate,
        a.UNTIL_REPORT_REMAIN_CSM_RATE as untilReportRemainCsmRate,
        a.ELR as elr,
        a.iacf_Fee_Rate as iacfFeeRate,
        (select QUOTA_VALUE from atruser.ATR_Buss_Quota_Value t1 where a.action_no=t1.action_no
        AND a.loa_code=t1.dimension_value AND quota_code='QR003'  limit 1) as QR003,
        (select QUOTA_VALUE from atruser.ATR_Buss_Quota_Value t1 where a.action_no=t1.action_no
        AND a.loa_code=t1.dimension_value AND quota_code='QR010' limit 1) as QR010,
        c.entity_code as entityCode,
        c.entity_e_name as entityEName
        <foreach collection="devNoList" item="item" index="index" open=","  separator=",">
            MAX(CASE cd.DEV_NO WHEN ${item} THEN cd.${feeType}  END) AS ep${item}
        </foreach>
        from (select * from  atruser.ATR_BUSS_DD_LRC_ICU_CALC a
        where a.action_no = #{actionNo,jdbcType=VARCHAR}
        <if test="portfolioNo != null and portfolioNo != ''">
            and a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
        </if>
        <if test="icgNo != null and icgNo != ''">
            and a.icg_no = #{icgNo,jdbcType=VARCHAR}
        </if>
        order by a.id
        <if test="limit != null and offset != null">
            offset ${offset} rows fetch next ${limit} rows only
        </if>
        ) a
        left join atruser.ATR_BUSS_DD_LRC_ICU_CALC_detail cd on a.id = cd.main_id
        left join bpluser.bbs_conf_entity c on a.entity_id = c.entity_id
        GROUP BY
        a.ACTION_NO,
        a.POLICY_NO,
        a.ENDORSE_SEQ_NO,
        a.currency_code,
        a.YEAR_MONTH,
        a.PORTFOLIO_NO,
        a.ICG_NO,
        a.EVALUATE_APPROACH,
        a.LOA_CODE,
        a.CMUNIT_NO,
        a.PRODUCT_CODE,
        to_char(a.EVALUATE_DATE,'yyyy/mm/dd'),
        to_char(a.CONTRACT_DATE,'yyyy/mm/dd'),
        to_char(a.EFFECTIVE_DATE_IN_DATE,'yyyy/mm/dd'),
        to_char(a.EXPIRY_DATE_IN_DATE,'yyyy/mm/dd'),
        to_char(a.EFFECTIVE_DATE_BOM,'yyyy/mm/dd'),
        to_char(a.EXPIRY_DATE_EOM,'yyyy/mm/dd'),
        a.payment_frequency_code,
        a.payment_frequency_no ,
        a.GROSS_PREMIUM ,
        a.COVERAGE_AMOUNT ,
        a.PASSED_MONTHS ,
        a.REMAINING_MONTHS_FOR_RECV ,
        a.REMAINING_PREM_TERM_PE ,
        a.REMAINING_MONTHS_FUTURE ,
        a.REMAINING_PREM_TERM_CB ,
        a.CUMULATIVE_PAID_PREMIUM,
        a.ed_premium_per_coverage_day ,
        a.PRI_CUR_END_REMAIN_CSM_RATE ,
        a.PRI_UNTIL_REPORT_REMAIN_CSM_RATE,
        a.CUMULATIVE_ED_RATE,
        a.CUR_END_REMAIN_CSM_RATE ,
        a.UNTIL_REPORT_REMAIN_CSM_RATE ,
        a.ELR,
        a.iacf_Fee_Rate,
        c.entity_code  ,
        c.entity_e_name,
        a.id
        order by a.id
    </select>

    <select id="findDateByVo22" fetchSize="1000" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
        select
            a.ACTION_NO as "actionNo",
            a.POLICY_NO as "policyNo",
            a.ENDORSE_SEQ_NO as "endorseSeqNo",
            a.currency_code as "currency",
            a.YEAR_MONTH as "yearMonth",
            a.PORTFOLIO_NO as "portfolioNo",
            a.ICG_NO as "icgNo",
            a.EVALUATE_APPROACH as "evaluateApproach",
            a.LOA_CODE as "loaCode",
            a.CMUNIT_NO as "cmunitNo",
            a.PRODUCT_CODE as "productCode",
            to_char(a.EVALUATE_DATE,'yyyy/mm/dd') as "evaluateDate",
            to_char(a.CONTRACT_DATE,'yyyy/mm/dd') as "contractDate",
            to_char(a.EFFECTIVE_DATE_IN_DATE,'yyyy/mm/dd') as "effectiveDateInDate",
            to_char(a.APPROVAL_DATE_IN_DATE,'yyyy/mm/dd') as "checkDateInDate",
            to_char(a.EXPIRY_DATE_IN_DATE,'yyyy/mm/dd') as "expiryDateInDate",
            to_char(a.EFFECTIVE_DATE_BOM,'yyyy/mm/dd') as "effectiveDateBom",
            to_char(a.EXPIRY_DATE_EOM,'yyyy/mm/dd') as "expiryDateEom",
            a.payment_frequency_code as "premiumFrequency",
            a.payment_frequency_no as "premiumTerm",
            a.GROSS_PREMIUM as "grossPremium",
            a.COVERAGE_AMOUNT as "coverageAmount",
            a.FACULTATIVE_IS as "facultativeIs",
            a.PASSED_DATES as "passedDates",
            a.PASSED_MONTHS as "passedMonths",
            a.remaining_months_for_recv as "remainingMonthsForRecv",
            a.REMAINING_PREM_TERM_PE as "remainingPremTermPe",
            a.REMAINING_MONTHS_FUTURE as "remainingMonthsFuture",
            a.REMAINING_PREM_TERM_CB as "remainingPremTermCb",
            a.PAYMENT_QUARTER as "paymentQuarter",
            a.CUMULATIVE_PAID_PREMIUM AS "cumulativePaidPremium",
            a.ed_premium_per_coverage_day as "edPremiumPerCoverageDay",
            a.ED_PREMIUM as "edPremium",
            a.PRI_CUR_END_REMAIN_CSM_RATE as "priCurEndRemainCsmRate",
            a.PRI_UNTIL_REPORT_REMAIN_CSM_RATE as "priUntilReportRemainCsmRate",
            a.CUMULATIVE_ED_RATE as "cumulativeEdRate",
            a.CUR_END_REMAIN_CSM_RATE as "curEndRemainCsmRate",
            a.UNTIL_REPORT_REMAIN_CSM_RATE as "untilReportRemainCsmRate",
            a.ELR as "elr",
            a.iacf_Fee_Rate as "iacfFeeRate",
            (select QUOTA_VALUE from atruser.ATR_Buss_Quota_Value t1 where a.action_no=t1.action_no
                                                                       AND a.loa_code=t1.dimension_value AND quota_code='QR003' limit 1) as "QR003",
            (select QUOTA_VALUE from atruser.ATR_Buss_Quota_Value t1 where a.action_no=t1.action_no
                                                                       AND a.loa_code=t1.dimension_value AND quota_code='QR010' limit 1) as "QR010",
            c.entity_code as "entityCode",
            c.entity_e_name as "entityEName"
        from  ATR_BUSS_DD_LRC_ICU_CALC a
                  left join bpluser.bbs_conf_entity c on a.entity_id = c.entity_id
        where a.ID between  #{limit} and #{offset}
        order by a.id
    </select>

    <select id="findDateByVo3" fetchSize="1000" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
        select
        cd.main_id as "id"
        <foreach collection="devNoList" item="item" index="index" open=","  separator=",">
            MAX(CASE cd.DEV_NO WHEN ${item} THEN cd.${feeType} ELSE NULL END) AS "${item}"
        </foreach>
        from  ATR_BUSS_DD_LRC_ICU_CALC_detail cd
        where  cd.main_id between  #{limit} and #{offset}
        GROUP BY cd.main_id
        order by cd.main_id
    </select>
</mapper>