/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-03-09 10:18:30
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName: AtrBussLicQueryResVo
 * @Description: Lic计量查询数据返回对象
 * @Author: OQJ.
 * @CreateDate: 2021/3/14 18:32
 * @Version: 1.0
 */
public class AtrBussLicResVo implements Serializable {
	
    /**
     * 主表ID
     */
    private Long licMainId;
    
    /**
     * 事故年月
     */
    private String accidentYearMonth;
    
    /**
     * 发展年月汇总金额list
     */
    private List<AtrBussLicQueryDevYearMonthVo> devYearMonthList;
    
    /**
     * 指标list
     */
    private List<AtrBussLicStageVo> stageList;
    
    /**
        * 辅助计算列
    */
    private BigDecimal lossRate;
       
    private static final long serialVersionUID = 1L;

	public Long getLicMainId() {
		return licMainId;
	}

	public void setLicMainId(Long licMainId) {
		this.licMainId = licMainId;
	}

	public String getAccidentYearMonth() {
		return accidentYearMonth;
	}

	public void setAccidentYearMonth(String accidentYearMonth) {
		this.accidentYearMonth = accidentYearMonth;
	}

	public List<AtrBussLicStageVo> getStageList() {
		return stageList;
	}

	public void setStageList(List<AtrBussLicStageVo> stageList) {
		this.stageList = stageList;
	}

	public List<AtrBussLicQueryDevYearMonthVo> getDevYearMonthList() {
		return devYearMonthList;
	}

	public void setDevYearMonthList(List<AtrBussLicQueryDevYearMonthVo> devYearMonthList) {
		this.devYearMonthList = devYearMonthList;
	}

	public BigDecimal getLossRate() {
		return lossRate;
	}

	public void setLossRate(BigDecimal lossRate) {
		this.lossRate = lossRate;
	}
    
}