package com.ss.ifrs.actuarial.pojo.other.vo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class AtrDapIcgRiskClassVo implements Serializable {

    private String yearMonth;

    @ApiModelProperty(value = "业务类型|DD-直保&临分分入；FO-临分分出；TI-合约分入；TO-合约分出", required = true)
    private String businessSourceCode;

    private String riDirectionCode;

    private String  icgNo;

    private String dimensionValue;

    private String riskClassCode;


    private static final long serialVersionUID = 1L;

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getRiDirectionCode() {
        return riDirectionCode;
    }

    public void setRiDirectionCode(String riDirectionCode) {
        this.riDirectionCode = riDirectionCode;
    }

    public String getDimensionValue() {
        return dimensionValue;
    }

    public void setDimensionValue(String dimensionValue) {
        this.dimensionValue = dimensionValue;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }
  }
