package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.ti;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class AtrDapLrcTiPayPlan {

    private Long planMainId;

    private Long entityId;

    private String firstYearMonth;

    private String yearMonth;

    private String treatyNo;

    private String treatyName;

    private String riskClassCode;

    private String riskCode;

    private Date effectiveDate;

    private Date expiryDate;

    private Integer devNo;

    private BigDecimal premium;

}
