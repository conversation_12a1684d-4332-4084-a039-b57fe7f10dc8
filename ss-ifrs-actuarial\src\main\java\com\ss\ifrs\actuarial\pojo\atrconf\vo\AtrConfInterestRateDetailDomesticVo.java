/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2022-06-24 18:16:46
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@ApiModel(value = "中债国债收益率曲线")
@ExcelIgnoreUnannotated
@Data
public class AtrConfInterestRateDetailDomesticVo implements Serializable {

    @ExcelProperty(index = 0)
    private BigDecimal tenorYears;
    
    @ExcelProperty(index = 1)
    private BigDecimal avgValue;
}