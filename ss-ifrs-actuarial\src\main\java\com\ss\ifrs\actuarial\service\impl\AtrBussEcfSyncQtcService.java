package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.constant.ActuarialConstant;
import com.ss.ifrs.actuarial.dao.AtrBussEcfDao;
import com.ss.ifrs.actuarial.dao.AtrBussEcfSyncQtcDao;
import com.ss.ifrs.actuarial.feign.QtcDapAtrFeignClient;
import com.ss.ifrs.actuarial.pojo.ecf.vo.syncqtc.AtrLogSyncQtc;
import com.ss.ifrs.actuarial.service.AtrConfBussPeriodService;
import com.ss.ifrs.actuarial.util.EcfUtil;
import com.ss.ifrs.actuarial.util.ThreadUtil;
import com.ss.ifrs.actuarial.util.abp.AsyncBatchProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 精算同步计量 service  （去存过版）
 * <AUTHOR>
 */
@Service
@Scope("prototype")
@Slf4j
public class AtrBussEcfSyncQtcService {

    @Resource
    public JdbcTemplate jdbcTemplate;
    @Resource
    public AtrBussInterestRateService atrBussInterestRateService;
    @Resource
    public AtrBussEcfSyncQtcDao atrBussEcfSyncQtcDao;
    @Resource
    public AtrBussEcfDao atrBussEcfDao;
    @Resource
    private AtrConfBussPeriodService atrConfBussPeriodService;
    @Resource
    private QtcDapAtrFeignClient qtcDapAtrFeignClient;

    private String actionNo;
    private Long entityId;
    private String yearMonth;

    private AsyncBatchProcessor abp;

    private AtomicLong mainIdGen;

    public void entry(Long entityId, String yearMonth) {
        this.entityId = entityId;
        this.yearMonth = yearMonth;
        this.actionNo = EcfUtil.createActionNo();
        mainIdGen = new AtomicLong(atrBussEcfSyncQtcDao.getMaxLogId());
        try (AsyncBatchProcessor abp = new AsyncBatchProcessor(actionNo, jdbcTemplate, 3)) {
            this.abp = abp;
            this.abp.addType(AtrLogSyncQtc.class);
            execute("weight", () -> atrBussInterestRateService.calcWeight(entityId, yearMonth));
            execute("clear", this::clearData);
            execute("sync", this::sync);
            abp.end();
            atrConfBussPeriodService.executionPeriod(entityId, yearMonth, ActuarialConstant.BussCaseFlows.BUSS_BECF);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void clearData() {
        List<String> tables = new ArrayList<>();
        tables.add("qtc_dap_ecf_fr_ir");
        tables.add("qtc_dap_ecf_wa_ir");
        tables.add("qtc_dap_ecf_icg_cf");
        tables.add("qtc_dap_ecf_icg");
        tables.add("qtc_dap_ecf_lic_pv");
        tables.add("qtc_dap_lic_dd_apt");
        tables.add("qtc_dap_lic_fo_apt");
        tables.add("qtc_dap_lic_ti_apt");
        tables.add("qtc_dap_lic_to_apt");
        tables.add("qtc_dap_lrc_dd_apt");
        tables.add("qtc_dap_lrc_fo_apt");
        tables.add("qtc_dap_lrc_ti_apt");
        tables.add("qtc_dap_lrc_to_apt");
        tables.add("qtc_dap_recovered");

        ThreadUtil.runThreadsThrow(tables,
                table -> execute("delete-" + table, () -> deleteTable(table)),
                tables::size);
    }

    private void sync() {
        String lrcDdActionNo = atrBussEcfSyncQtcDao.getLrcDdActionNo(yearMonth);

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("yearMonth", yearMonth);
        paramMap.put("entityId", entityId);
        paramMap.put("lrcDdActionNo", lrcDdActionNo);

        List<String> modules = Arrays.asList("EcfFrIr", "EcfWaIr", "EcfIcg", "EcfIcgCf", "EcfLicPv",
                "EcfRecovered","EcfLicPvU","EcfLrcPvU");

        ThreadUtil.runThreadsThrow(modules, module ->
                execute("sync-" + module,
                        () -> {
                            if ("EcfFrIr".equals(module)) {
                                atrBussEcfSyncQtcDao.syncEcfFrIr(paramMap);
                            } else if ("EcfWaIr".equals(module)) {
                                atrBussEcfSyncQtcDao.syncEcfWaIr(paramMap);
                            } else if ("EcfIcg".equals(module)) {
                                atrBussEcfSyncQtcDao.syncEcfIcg(paramMap);
                            } else if ("EcfIcgCf".equals(module)) {
                                atrBussEcfSyncQtcDao.syncEcfIcgCf(paramMap);
                            } else if ("EcfLicPv".equals(module)) {
                                atrBussEcfSyncQtcDao.syncEcfLicPv(paramMap);
                            } else if ("EcfLicPvU".equals(module)){
                                this.syncEcfLicPvU(paramMap);
                            } else if ("EcfLrcPvU".equals(module)){
                                this.syncEcfLrcUCf(paramMap);
                            } else if ("EcfRecovered".equals(module)) {
                                this.syncEcfLrcGRiPremium(paramMap);
                            }
                        }), modules::size);
    }

    private void deleteTable(String table) {
        jdbcTemplate.update(
                "delete from qtcuser." + table + " where entity_id = ? and year_month = ?", entityId, yearMonth);
    }

    private void logInfo(String busiInfo, String mark) {
        AtrLogSyncQtc vo = new AtrLogSyncQtc();
        vo.setId(mainIdGen.incrementAndGet());
        vo.setLogType("INFO");
        vo.setBusinessInfo(busiInfo);
        vo.setMark(mark);
        vo.setActionNo(actionNo);
        vo.setCreateTime(new Date());
        atrBussEcfSyncQtcDao.insertLog(vo);
    }

    private void syncEcfLrcUCf(Map<String,Object> paramMap){
        EcfUtil.analyseTable(jdbcTemplate,"atr_buss_lrc_action");
        EcfUtil.analyseTable(jdbcTemplate,"atr_buss_dd_lrc_u");
        EcfUtil.analyseTable(jdbcTemplate,"atr_buss_dd_lrc_u_dev");
        atrBussEcfSyncQtcDao.syncExfLrcDDUCf(paramMap);
        atrBussEcfSyncQtcDao.syncExfLrcFOUCf(paramMap);
        atrBussEcfSyncQtcDao.syncExfLrcTIUCf(paramMap);
        atrBussEcfSyncQtcDao.syncExfLrcTOUCf(paramMap);
    }

    private void syncEcfLicPvU(Map<String, Object> paramMap) {
        atrBussEcfSyncQtcDao.syncEcfLicPvUDD(paramMap);
        atrBussEcfSyncQtcDao.syncEcfLicPvUFO(paramMap);
        atrBussEcfSyncQtcDao.syncEcfLicPvUTI(paramMap);
        atrBussEcfSyncQtcDao.syncEcfLicPvUTO(paramMap);
    }

    // 推送合同组层级的分出未赚以及原单合同组层级的未赚
    private void syncEcfLrcGRiPremium(Map<String, Object> paramMap) {
        atrBussEcfSyncQtcDao.syncEcfRecoveredFO(paramMap);
        atrBussEcfSyncQtcDao.syncEcfRecoveredTO(paramMap);
    }

    private void execute(String mark, Runnable job) {
        logInfo("start", mark);
        job.run();
        logInfo("end", mark);
    }

}
