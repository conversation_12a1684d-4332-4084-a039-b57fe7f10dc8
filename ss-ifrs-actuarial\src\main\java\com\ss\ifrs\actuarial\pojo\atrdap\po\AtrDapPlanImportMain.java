/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2025-06-11 14:44:32
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrdap.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2025-06-11 14:44:32<br/>
 * Description: null<br/>
 * Table Name: atr_dap_plan_import_main<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
public class AtrDapPlanImportMain implements Serializable {
    /**
     * Database column: atr_dap_plan_import_main.plan_main_id
     * Database remarks: null
     */
    private Long planMainId;

    /**
     * Database column: atr_dap_plan_import_main.entity_id
     * Database remarks: entity_id|业务单位id
     */
    @ApiModelProperty(value = "entity_id|业务单位id", required = false)
    private Long entityId;

    /**
     * Database column: atr_dap_plan_import_main.year_month
     * Database remarks: null
     */
    private String yearMonth;

    /**
     * Database column: atr_dap_plan_import_main.data_type
     * Database remarks: null
     */
    private String dataType;

    /**
     * Database column: atr_dap_plan_import_main.version_no
     * Database remarks: null
     */
    private String versionNo;

    /**
     * Database column: atr_dap_plan_import_main.confirm_is
     * Database remarks: null
     */
    private String confirmIs;

    /**
     * Database column: atr_dap_plan_import_main.confirm_id
     * Database remarks: null
     */
    private Long confirmId;

    /**
     * Database column: atr_dap_plan_import_main.confirm_time
     * Database remarks: null
     */
    private Date confirmTime;

    /**
     * Database column: atr_dap_plan_import_main.creator_id
     * Database remarks: null
     */
    private Long creatorId;

    /**
     * Database column: atr_dap_plan_import_main.create_time
     * Database remarks: null
     */
    private Date createTime;

    /**
     * Database column: atr_dap_plan_import_main.updator_id
     * Database remarks: null
     */
    private Long updatorId;

    /**
     * Database column: atr_dap_plan_import_main.update_time
     * Database remarks: null
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public Long getPlanMainId() {
        return planMainId;
    }

    public void setPlanMainId(Long planMainId) {
        this.planMainId = planMainId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

    public String getConfirmIs() {
        return confirmIs;
    }

    public void setConfirmIs(String confirmIs) {
        this.confirmIs = confirmIs;
    }

    public Long getConfirmId() {
        return confirmId;
    }

    public void setConfirmId(Long confirmId) {
        this.confirmId = confirmId;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}