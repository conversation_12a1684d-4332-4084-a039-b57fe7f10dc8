package com.ss.ifrs.actuarial.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.ss.ifrs.actuarial.dao.conf.AtrConfCodeAdapterDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfCodeDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfCodeHisDao;
import com.ss.ifrs.actuarial.feign.BmsConfCodeFeignClient;
import com.ss.ifrs.actuarial.pojo.atrcode.po.AtrConfCodeAdapter;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfCode;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfCodeHis;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfCodeVo;
import com.ss.ifrs.actuarial.service.AtrConfCodeService;
import com.ss.platform.core.model.Tree;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.platform.core.annotation.SsTranslateCode;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.core.constant.SystemConstant;
import com.ss.platform.util.ClassUtil;
import com.ss.library.utils.CacheUtil;
import com.ss.library.utils.FilterUtil;
import com.ss.library.utils.StringUtil;
import com.ss.platform.util.TreeForCodeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

@Service(value = "atrCodeService")
public class AtrConfCodeServiceImpl implements AtrConfCodeService {

	@Autowired
	private AtrConfCodeAdapterDao atrConfCodeAdapterDao;

	@Autowired
	private AtrConfCodeDao atrConfCodeDao;

	@Autowired
	private AtrConfCodeHisDao atrConfCodeHisDao;
	
	@Autowired
	private BmsConfCodeFeignClient bmsConfCodeFeignClient;

	final Logger LOG = LoggerFactory.getLogger(getClass());

	static class TreeMap {
		// 在此处配置数据库相应字段
		private static final HashMap<String, String> MENU_COLUMN = new HashMap<String, String>() {
			private static final long serialVersionUID = 1L;

			{
				put("tableName", "bpl_menu");
				put("treeId", "id");
				put("upperid", "upper_id");
				put("treeLabel", "menu_cname");
			}
		};
		private static final HashMap<String, String> COMPANY_COLUMN = new HashMap<String, String>() {
			private static final long serialVersionUID = 1L;

			{
				put("tableName", "bpl_company");
				put("treeId", "company_code");
				put("upperid", "upper_company_code");
				put("treeLabel", "company_name");
			}
		};

		private static HashMap<String, String> getMap(String tableName) {
			HashMap<String, String> rule = new HashMap<>();
			// 其他表配置对应关系
			if (tableName.equals(TreeMap.MENU_COLUMN.get("tableName"))) {
				rule = TreeMap.MENU_COLUMN;
			} else if (tableName.equals(TreeMap.COMPANY_COLUMN.get("tableName"))) {
				rule = TreeMap.COMPANY_COLUMN;
			} else {

			}
			return rule;
		}
	}

	/**
	 * 分页查询
	 *
	 * @param pageParam
	 * @param vo
	 * @return
	 */
	@Override
	@Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
	public Page<AtrConfCodeVo> findSysCodePage(AtrConfCodeVo vo, Pageable pageParam) {
		AtrConfCodeVo sysCode = ClassUtil.convert(vo, AtrConfCodeVo.class);
		sysCode.setCodeCode(FilterUtil.transitionSearch(sysCode.getCodeCode()));
		sysCode.setCodeCName(FilterUtil.transitionSearch(sysCode.getCodeCName()));
		Page<AtrConfCodeVo> page = atrConfCodeDao.fuzzySearchPage(sysCode, pageParam);
		return ClassUtil.convert(page, AtrConfCodeVo.class);
	}

	/**
	 * 不分页查询
	 *
	 * @return
	 */
	@Override
	@Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
	public List<AtrConfCode> findList(AtrConfCodeVo sysCodeVo) {
		List<AtrConfCode> page = atrConfCodeDao.findList(ClassUtil.convert(sysCodeVo, AtrConfCode.class));
		return page;
	}

	/**
	 * 查找有效的sysCode的列表
	 *
	 * @param pageParam
	 * @param vo
	 * @return
	 */
	@Override
	@Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
	public Page<AtrConfCodeVo> findListValid(AtrConfCodeVo vo, Pageable pageParam) {
		Page<AtrConfCodeVo> page = null;
		vo.setValidIs(null);

		// sysCode字符串转数组
		String codeCodes = vo.getCodeCode();

		if (StringUtil.isNotEmpty(codeCodes)) {
			String[] codeCodesArr = codeCodes.split(",");
			ArrayList<String> codeCodesList = new ArrayList<>(Arrays.asList(codeCodesArr));

			// 判断传来的sysCode是否为多个
			if (codeCodesArr.length >= 2) {
				ArrayList<AtrConfCodeVo> sysCodesPage = new ArrayList<>();
				// 如果传来的参数为多个
				ArrayList<AtrConfCodeVo> sysCodes = atrConfCodeDao.findBycodeArrs(codeCodesList);
				// 过滤upperCode
				for (AtrConfCodeVo sysCode : sysCodes) {
					if (StringUtil.isNotEmpty(vo.getCodeCodeIdx())) {
						if (vo.getCodeCodeIdx().equals(sysCode.getCodeCodeIdx()) && "1".equals(sysCode.getValidIs())) {
							sysCodesPage.add(sysCode);
						}
					}
				}
				page = new Page<>(sysCodesPage);
				//
			} else {
				// 如果传来的syscode为一个
				page = atrConfCodeDao.findListValid(vo, pageParam);
			}
		} else {
			page = atrConfCodeDao.findListValid(vo, pageParam);
		}

		return page;
	}

	/**
	 * 插入、更新方法
	 *
	 * @return
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void saveAtrCode(AtrConfCodeVo sysCodeAddReqVo) {
		AtrConfCode sysCode = ClassUtil.convert(sysCodeAddReqVo, AtrConfCode.class);
		sysCode.setCreateTime(new Date());
		try {
			atrConfCodeDao.save(sysCode);
		} catch (UnexpectedRollbackException e) {
			LOG.error(e.getLocalizedMessage(), e);
			throw e;
		}
	}

	/**
	 * 最终返回json树字符串 前端可直接使用
	 */
	@Override
	public String getTree(Map<String, String> params) {

		String tableName = params.get("tableName");
		String parentSql = params.get("parentSql");
		String allSql = params.get("allSql");

		List<HashMap<String, Object>> list = this.findTreeList(tableName, parentSql);
		if (list == null || list.size() == 0) {
			System.out.println(" not parent data");
			return "";
		}
		TreeTemp parent = dtoList2TreeTempList(list, tableName).get(0);

		List<HashMap<String, Object>> allData = this.findTreeList(tableName, allSql);
		List<TreeTemp> data = dtoList2TreeTempList(allData, tableName);

		this.getTreeResult(data, parent);

		return JSONObject.toJSONString(parent);
	}

	@SuppressWarnings("unchecked")
	@Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
	public List<HashMap<String, Object>> findTreeList(String tableName, String sql) {
		if (sql == null || "".equals(sql)) {
			return atrConfCodeDao.findTreeData(tableName);
		} else {
			return atrConfCodeDao.findTreeData(tableName, sql);
		}
	}

	/**
	 * 递归思路 通过父节点查找出下级节点 然后以下级节点作为父节点递归。直至没有下级节点跳出。 注意会不断查询库
	 *
	 * @param parent
	 */
	@SuppressWarnings("unused")
	private void getTreeResult(TreeTemp parent, String tableName) {
		List<HashMap<String, Object>> childList = this.findTreeList(tableName,
				TreeMap.getMap(tableName).get("upperid") + " = '" + parent.getTreeId() + "'");
		if (childList.size() > 0) {
			for (HashMap<String, Object> child : childList) {
				// 排除父子点本身
				if (child.get("id").toString().equals(parent.getTreeId())) {
					continue;
				}
				TreeTemp treeChild = new TreeTemp(child.get("id").toString(), child.get("upper_id").toString(),
						child.get("menu_cname").toString());
				parent.addChildren(treeChild);
				getTreeResult(treeChild, tableName);
			}
		} else {
			return;
		}
	}

	/**
	 * 思路同上 只是一开始就把数据全部查询出来 因一直遍历初始数据 若初始数据量太大的 建议用上面的方法
	 *
	 * @param parent
	 */
	private void getTreeResult(List<TreeTemp> data, TreeTemp parent) {
		List<TreeTemp> childList = getChildrenList(data, parent);
		if (childList.size() > 0) {
			for (TreeTemp child : childList) {
				getTreeResult(data, child);
			}
		} else {
			return;
		}
	}

	/**
	 * 在已查询出的数据筛选出子节点
	 *
	 * @param data
	 * @param parent
	 * @return
	 */
	private static List<TreeTemp> getChildrenList(List<TreeTemp> data, TreeTemp parent) {
		List<TreeTemp> childList = new ArrayList<>();
		for (TreeTemp dto : data) {
			// 判断是否子节点&&排除父子点本身
			if (dto.getUpperid().equals(parent.getTreeId()) && !dto.getTreeId().equals(dto.getUpperid())) {
				childList.add(dto);
				parent.addChildren(dto);
			}
		}
		return childList;
	}

	/**
	 * DTO数据转换为树结构数据
	 *
	 * @param list
	 * @param tableName
	 * @return
	 */
	private List<TreeTemp> dtoList2TreeTempList(List<HashMap<String, Object>> list, String tableName) {
		List<TreeTemp> result = new ArrayList<TreeTemp>();
		HashMap<String, String> rule = TreeMap.getMap(tableName);
		for (HashMap<String, Object> map : list) {
			// toString 查询出来的数据有可能为null导致异常
			TreeTemp temp = new TreeTemp(toString(map.get(rule.get("treeId"))), toString(map.get(rule.get("upperid"))),
					toString(map.get(rule.get("treeLabel"))));
			result.add(temp);
		}
		return result;

	}

	private static String toString(Object obj) {
		if (obj == null) {
			return "";
		} else {
			return obj.toString();
		}
	}

	public class TreeTemp {

		// 以下属性必须
		private String treeId;
		/**
		 * id
		 **/
		private String upperid;
		/**
		 * 上级id
		 **/
		private String treeLabel;
		/**
		 * 页面显示字段
		 **/

		// 以下属性可空
		private List<TreeTemp> children = new ArrayList<TreeTemp>();
		private String level;
		/**
		 * 层：暂时未用到 可以用在快速排序，查找中
		 **/
		private Integer displayNo;
		/**
		 * 页面排序：预留未实现
		 **/

		// 控制状态
		private Boolean disabled;

		/**
		 * 是否禁用状态
		 **/

		public TreeTemp(String treeId, String upperid, String treeLabel) {
			this.treeId = treeId;
			this.upperid = upperid;
			this.treeLabel = treeLabel;
		}

		public String getTreeId() {
			return treeId;
		}

		public void setTreeId(String treeId) {
			this.treeId = treeId;
		}

		public String getTreeLabel() {
			return treeLabel;
		}

		public void setTreeLabel(String treeLabel) {
			this.treeLabel = treeLabel;
		}

		public String getUpperid() {
			return upperid;
		}

		public void setUpperid(String upperid) {
			this.upperid = upperid;
		}

		public List<TreeTemp> getChildren() {
			return children;
		}

		public void setChildren(List<TreeTemp> children) {
			this.children = children;
		}

		public void addChildren(TreeTemp children) {
			this.children.add(children);
		}

		public String getLevel() {
			return level;
		}

		public void setLevel(String level) {
			this.level = level;
		}

		public Integer getDisplayNo() {
			return displayNo;
		}

		public void setDisplayNo(Integer displayNo) {
			this.displayNo = displayNo;
		}

		public Boolean getDisabled() {
			return disabled;
		}

		public void setDisabled(Boolean disabled) {
			this.disabled = disabled;
		}
	}

	@Override
	public Page<?> dynamicSql(Map map, Pageable pageParam) {
		return atrConfCodeDao.dynamicSql(map, pageParam);

	}

	@Override
	public List<?> dynamicSql(Map map) {
		return atrConfCodeDao.dynamicSql(map);
	}

	@Override
	public AtrConfCodeAdapter getSearchConfig(String code) {
		return atrConfCodeAdapterDao.findByCode(code);
	}

	@Override
	public AtrConfCodeVo findAtrCodeByPk(Long codeId) {
		AtrConfCodeVo sysCode = atrConfCodeDao.findByCodeId(codeId);
		if (sysCode.getUpperCodeId() != 0) {
			AtrConfCodeVo parentCode = atrConfCodeDao.findByCodeId(sysCode.getUpperCodeId());
			sysCode.setCodeCodeIdx(parentCode.getCodeCode());
			sysCode.setParentEName(parentCode.getCodeEName());
			sysCode.setParentCName(parentCode.getCodeCName());
			sysCode.setParentLName(parentCode.getCodeLName());
		}
		return sysCode;
	}

	@Override
	public String disableAtrConfCode(AtrConfCodeVo confCodeVo) throws UnexpectedRollbackException {
		AtrConfCode confCode = atrConfCodeDao.findById(confCodeVo.getCodeId());
		if (!confCode.getValidIs().equals(confCodeVo.getValidIs())) {
			try {
				atrConfCodeDao.updateById(ClassUtil.convert(confCodeVo, AtrConfCode.class));
				return ResCodeConstant.ResCode.SUCCESS;
			} catch (UnexpectedRollbackException e) {
				LOG.error(e.getLocalizedMessage(), e);
				throw e;
			}

		} else {
			return confCodeVo.getValidIs();
		}
	}

	@Override
	public AtrConfCodeVo findByCodeCode(String codeCode) {
		AtrConfCodeVo upperCode = atrConfCodeDao.findByCodeCode(codeCode);
		if (upperCode != null) {
			return ClassUtil.convert(upperCode, AtrConfCodeVo.class);
		}
		return null;
	}

	@Override
	public Page<Tree<AtrConfCodeVo>> searchAtrConfCodePage(AtrConfCodeVo confCodeVo, Pageable pageParam) {
		if (StringUtils.isNotEmpty(confCodeVo.getCodeCName())) {
			if (confCodeVo.getCodeCName().contains("*") || confCodeVo.getCodeCName().contains("%")) {
				confCodeVo.setCodeCName(FilterUtil.transitionSearch(confCodeVo.getCodeCName()));
			} else {
				confCodeVo.setCodeCName("%" + confCodeVo.getCodeCName() + "%");
			}
		}
		if (StringUtils.isNotEmpty(confCodeVo.getCodeCode())) {
			confCodeVo.setCodeCName(confCodeVo.getCodeCodeIdx().trim().split(" -- ")[1]);
		}
		// 分页查询一级节点id
		Page<Long> idPage = atrConfCodeDao.fuzzyCodeIdPage(confCodeVo, pageParam);
		List<Long> codeIdList = new ArrayList<>(idPage.getContent());
		// 添加一级节点
		// 无Code对象信息，返回空List的Page对象
		if (CollectionUtils.isEmpty(codeIdList) || codeIdList.size() == 0) {
			return new Page(new ArrayList<>(), pageParam, idPage.getTotal());
		}
		// 根据一级节点id查询下级数据
		confCodeVo.setCodeIdList(codeIdList);
		List<AtrConfCodeVo> list = atrConfCodeDao.findCodeVoListByIds(confCodeVo);
		TreeForCodeUtil builder = new TreeForCodeUtil<AtrConfCodeVo>();
		List<AtrConfCodeVo> t = builder.buildVo(list, codeIdList, confCodeVo);
		Page page = new Page(t, pageParam, idPage.getTotal());
		return page;
	}

	@Override
	public void saveAtrUpperCode(AtrConfCodeVo upperCodeVo) throws UnexpectedRollbackException {
		try {
			atrConfCodeDao.save(ClassUtil.convert(upperCodeVo, AtrConfCode.class));
		} catch (UnexpectedRollbackException e) {
			LOG.error(e.getLocalizedMessage(), e);
			throw e;
		}
	}

	@Override
	public AtrConfCodeVo findByUpperCodeId(Long upperCodeId) {
		AtrConfCode upperCode = atrConfCodeDao.findById(upperCodeId);
		if (upperCode != null) {
			return ClassUtil.convert(upperCode, AtrConfCodeVo.class);
		}
		return null;
	}

	@Override
	public void updateAtrUpperCode(AtrConfCodeVo confUpperCodeVo) throws UnexpectedRollbackException {
		try {
			atrConfCodeDao.updateById(ClassUtil.convert(confUpperCodeVo, AtrConfCode.class));
		} catch (UnexpectedRollbackException e) {
			LOG.error(e.getLocalizedMessage(), e);
			throw e;
		}
	}

	@Override
	public void delete(AtrConfCodeVo confCodeVo) {
		try {
			AtrConfCodeHis confCodeHis = ClassUtil.convert(confCodeVo, AtrConfCodeHis.class);
			atrConfCodeHisDao.save(confCodeHis);
			List<AtrConfCodeVo> childrens = atrConfCodeDao.findByUpperCodeId(confCodeVo.getCodeId());
			// 找到所有子项
			for (int i = 0; i < childrens.size(); i++) {
				List<AtrConfCodeVo> childrenList = atrConfCodeDao.findByUpperCodeId(childrens.get(i).getCodeId());
				if (ObjectUtils.isNotEmpty(childrenList)) {
					childrens.addAll(childrenList);
				}
			}
			atrConfCodeDao.deleteById(confCodeVo.getCodeId());
			for (int i = 0; i < childrens.size(); i++) {
				confCodeHis = ClassUtil.convert(childrens.get(i), AtrConfCodeHis.class);
				atrConfCodeHisDao.save(confCodeHis);
				atrConfCodeDao.deleteById(childrens.get(i).getCodeId());
			}
		} catch (UnexpectedRollbackException e) {
			LOG.error(e.getLocalizedMessage(), e);
			throw e;
		}
	}

	/**
	 * 把code翻译成name 表名 ：sysCode code : codecode name : codename
	 * <p>
	 * 返回的字段名 ：nameField
	 */
	public String toName(String tableName, String codeName, String codeValue, String nameField) {

		Map<String, String> sqlParam = new HashMap<>();
		sqlParam.put("tableName", tableName);
		sqlParam.put("condition", "where " + codeName + " = '" + codeValue + "'");
		List<?> list = this.dynamicSql(sqlParam);

		if (list == null || list.size() == 0) {
			return "";
		} else {
			return ClassUtil.getStringValue(list.get(0), nameField);
		}

	}

	@Override
	public void updateAtrCode(AtrConfCodeVo sysgCodeUpdateReqVo) {
		AtrConfCode sysCode = ClassUtil.convert(sysgCodeUpdateReqVo, AtrConfCode.class);
		sysCode.setUpdateTime(new Date());
		try {
			atrConfCodeDao.updateById(sysCode);
		} catch (UnexpectedRollbackException e) {
			LOG.error(e.getLocalizedMessage(), e);
			throw e;
		}
	}

	@Override
	public Page<AtrConfCodeVo> findListForAutoComplete(AtrConfCodeVo sysCodeVo, Pageable pageParam) {
		Page<AtrConfCodeVo> page = atrConfCodeDao.findListForAutoComplete(sysCodeVo, pageParam);
		return page;
	}

	@Override
	public Map<String, Object> findListV2(AtrConfCodeVo sysCodeVo) {
		String[] upperCodes = sysCodeVo.getCodeCodeIdx().split(",");
		Map<String, Object> result = new HashMap<>();
		for (String upperCode : upperCodes) {
			AtrConfCode sysCode = ClassUtil.convert(sysCodeVo, AtrConfCode.class);
			AtrConfCodeVo confUpperCode = atrConfCodeDao.findUpperCode(upperCode);
			sysCode.setUpperCodeId(ObjectUtils.isEmpty(confUpperCode) ? null : confUpperCode.getCodeId());
			sysCode.setValidIs("1");
			List<AtrConfCode> sysCodes = atrConfCodeDao.findListOrderByDisplayNo(sysCode);
			result.put(upperCode, ClassUtil.convert(sysCodes, AtrConfCodeVo.class));
		}
		return result;
	}
	
	@Override
	public AtrConfCode findByCodeAndIdx(AtrConfCodeVo atrConfCodeVo){
		if(null != atrConfCodeVo && StringUtil.isNotEmpty(atrConfCodeVo.getCodeCode())
			&& StringUtil.isNotEmpty(atrConfCodeVo.getCodeCodeIdx())){
			return atrConfCodeDao.findByCodeAndIdx(atrConfCodeVo);
		}else {
			return null;
		}
	}
	
	@Override
	@Transactional(propagation = Propagation.NOT_SUPPORTED, readOnly = true)
	public List<AtrConfCodeVo> findByCodeIdx(AtrConfCodeVo sysCodeVo) {
		List<AtrConfCodeVo> volist;
		if (CacheUtil.hasKey(SystemConstant.AtrIdentity.APP_CONTEXT +"/"+ CommonConstant.BussConf.GG_V_CODE, sysCodeVo.getCodeCodeIdx())) {
			volist = (List<AtrConfCodeVo>) CacheUtil.get(SystemConstant.AtrIdentity.APP_CONTEXT +"/"+ CommonConstant.BussConf.GG_V_CODE, sysCodeVo.getCodeCodeIdx());
		} else {
			volist = atrConfCodeDao.findListVoByUpperCode(sysCodeVo.getCodeCodeIdx());
			CacheUtil.put(SystemConstant.AtrIdentity.APP_CONTEXT +"/"+ CommonConstant.BussConf.GG_V_CODE, sysCodeVo.getCodeCodeIdx(), volist);
		}
		return volist;
	}

	/**
	 * 根据对象加载注解码值缓存
	 * @param voClazz
	 */
	@Override
	public void cacheSsTranslateCode(Class voClazz) {
		//解释码表注解并缓存结果
		Map mapAnnots;
		String clazzName = voClazz.getName();
		if (CacheUtil.hasKey("SsTranslateCode", clazzName)) {
			mapAnnots = (Map) CacheUtil.get("SsTranslateCode", clazzName);
		} else {
			mapAnnots = ClassUtil.getAnnotations(voClazz, SsTranslateCode.class);
			CacheUtil.put("SsTranslateCode", clazzName, mapAnnots);
		}
		if (ObjectUtils.isNotEmpty(mapAnnots)) {
			AtomicReference<AtrConfCodeVo> accCodeVo = new AtomicReference<>(new AtrConfCodeVo());
			//根据注解进行解码
			mapAnnots.forEach((key, item) -> {
				SsTranslateCode ssTranslateCode = (SsTranslateCode) item;
				String context = ssTranslateCode.context();
				String codeCodeIdx = ssTranslateCode.codeCodeIdx();
				if (StringUtil.isNotEmpty(codeCodeIdx)) {
					// 根据注解请求码表进行缓存

					switch (context) {
						case SystemConstant.BbsIdentity.APP_CONTEXT:
							bmsConfCodeFeignClient.findCodeByCodeType(codeCodeIdx);
							break;
						default:
							accCodeVo.set(new AtrConfCodeVo());
							accCodeVo.get().setCodeCodeIdx(codeCodeIdx);
							accCodeVo.get().setValidIs("1");
							this.findByCodeIdx(accCodeVo.get());
							break;
					}
				}
			});
		}
	}

	@Override
	public void clearCacheAllVCode() {
		CacheUtil.remove(SystemConstant.AtrIdentity.APP_CONTEXT + "/" + CommonConstant.BussConf.GG_V_CODE);
	}

	@Override
	public List<AtrConfCodeVo> findCodeByCodeIdx(AtrConfCodeVo sysCodeVo) {
		List<AtrConfCodeVo> confCodeVos = atrConfCodeDao.findListVoByUpperCode(sysCodeVo.getCodeCodeIdx());
		return confCodeVos;
	}

	@Override
	public List<String> findCodeByCodeIdx(String codeIdx) {
		List<String> model = atrConfCodeDao.findByCodeIdx(codeIdx);
		return model;
	}

}
