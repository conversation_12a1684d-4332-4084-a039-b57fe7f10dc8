<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-02-06 17:15:22 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussIbnrImportDetailDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrImportDetail">
    <id column="IBNR_DETAIL_ID" property="ibnrDetailId" jdbcType="DECIMAL" />
    <result column="IBNR_MAIN_ID" property="ibnrMainId" jdbcType="DECIMAL" />
    <result column="BUSINESS_MODEL" property="businessModel" jdbcType="VARCHAR" />
    <result column="IBNR_AMOUNT" property="ibnrAmount" jdbcType="DECIMAL" />
    <result column="ri_ibnr_amount" property="riIbnrAmount" jdbcType="DECIMAL" />
    <result column="ri_case_amount" property="riCaseAmount" jdbcType="DECIMAL" />

    <result column="year_month" property="yearMonth" jdbcType="VARCHAR" />
    <result column="accident_quarter" property="accidentQuarter" jdbcType="VARCHAR" />
    <result column="risk_class_code" property="riskClassCode" jdbcType="VARCHAR" />
    <result column="center_code" property="centerCode" jdbcType="VARCHAR" />
    <result column="ri_dept" property="riDept" jdbcType="VARCHAR" />
    <result column="treaty_no" property="treatyNo" jdbcType="VARCHAR" />
    <result column="treaty_name" property="treatyName" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    IBNR_DETAIL_ID, IBNR_MAIN_ID, BUSINESS_MODEL, IBNR_AMOUNT, ri_ibnr_amount,ri_case_amount,
    year_month,accident_quarter,risk_class_code,center_code,ri_dept,treaty_no,treaty_name
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="ibnrDetailId != null ">
          and IBNR_DETAIL_ID = #{ibnrDetailId,jdbcType=DECIMAL}
      </if>
      <if test="ibnrMainId != null ">
          and IBNR_MAIN_ID = #{ibnrMainId,jdbcType=DECIMAL}
      </if>
      <if test="businessModel != null and businessModel != ''">
          and BUSINESS_MODEL = #{businessModel,jdbcType=VARCHAR}
      </if>
      <if test="ibnrAmount != null ">
          and IBNR_AMOUNT = #{ibnrAmount,jdbcType=DECIMAL}
      </if>
      <if test="riIbnrAmount != null ">
          and ri_ibnr_amount = #{riIbnrAmount,jdbcType=DECIMAL}
      </if>
      <if test="riCaseAmount != null ">
        and ri_case_amount = #{riCaseAmount,jdbcType=DECIMAL}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.ibnrDetailId != null ">
          and IBNR_DETAIL_ID = #{condition.ibnrDetailId,jdbcType=DECIMAL}
      </if>
      <if test="condition.ibnrMainId != null ">
          and IBNR_MAIN_ID = #{condition.ibnrMainId,jdbcType=DECIMAL}
      </if>
      <if test="condition.businessModel != null and condition.businessModel != ''">
          and BUSINESS_MODEL = #{condition.businessModel,jdbcType=VARCHAR}
      </if>
      <if test="condition.ibnrAmount != null ">
          and IBNR_AMOUNT = #{condition.ibnrAmount,jdbcType=DECIMAL}
      </if>
      <if test="condition.riIbnrAmount != null ">
          and ri_ibnr_amount = #{condition.riIbnrAmount,jdbcType=DECIMAL}
      </if>
      <if test="condition.riCaseAmount != null ">
        and ri_case_amount = #{condition.riCaseAmount,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="ibnrDetailId != null ">
          and IBNR_DETAIL_ID = #{ibnrDetailId,jdbcType=DECIMAL}
      </if>
      <if test="ibnrMainId != null ">
          and IBNR_MAIN_ID = #{ibnrMainId,jdbcType=DECIMAL}
      </if>
      <if test="businessModel != null and businessModel != ''">
          and BUSINESS_MODEL = #{businessModel,jdbcType=VARCHAR}
      </if>
      <if test="ibnrAmount != null ">
          and IBNR_AMOUNT = #{ibnrAmount,jdbcType=DECIMAL}
      </if>
      <if test="riIbnrAmount != null ">
          and ri_ibnr_amount = #{riIbnrAmount,jdbcType=DECIMAL}
      </if>
      <if test="riCaseAmount != null ">
        and ri_case_amount = #{riCaseAmount,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_IBNR_IMPORT_DETAIL
    where IBNR_DETAIL_ID = #{ibnrDetailId,jdbcType=DECIMAL}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_IBNR_IMPORT_DETAIL
    where IBNR_DETAIL_ID in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_IBNR_IMPORT_DETAIL
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrImportDetail">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_IBNR_IMPORT_DETAIL
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_IBNR_IMPORT_DETAIL
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="IBNR_DETAIL_ID" keyProperty="ibnrDetailId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrImportDetail">
    <selectKey resultType="long" keyProperty="ibnrDetailId" order="BEFORE">
      select nextval('atr_seq_buss_ibnr_import_dtl')  as sequenceNo 
    </selectKey>
    insert into ATR_BUSS_IBNR_IMPORT_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ibnrDetailId != null">
        IBNR_DETAIL_ID,
      </if>
      <if test="ibnrMainId != null">
        IBNR_MAIN_ID,
      </if>
      <if test="businessModel != null">
        BUSINESS_MODEL,
      </if>
      <if test="ibnrAmount != null">
        IBNR_AMOUNT,
      </if>
      <if test="riIbnrAmount != null">
        ri_ibnr_amount,
      </if>
      <if test="riCaseAmount != null ">
        ri_case_amount,
      </if>
      <if test="yearMonth != null">
        year_month,
      </if>
      <if test="accidentQuarter != null">
        accident_quarter,
      </if>
      <if test="riskClassCode != null">
        risk_class_code,
      </if>
      <if test="centerCode != null">
        center_code,
      </if>
      <if test="riDept != null">
        ri_dept,
      </if>
      <if test="treatyNo != null">
        treaty_no,
      </if>
      <if test="treatyName != null">
        treaty_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ibnrDetailId != null">
        #{ibnrDetailId,jdbcType=DECIMAL},
      </if>
      <if test="ibnrMainId != null">
        #{ibnrMainId,jdbcType=DECIMAL},
      </if>
      <if test="businessModel != null">
        #{businessModel,jdbcType=VARCHAR},
      </if>
      <if test="ibnrAmount != null">
        #{ibnrAmount,jdbcType=DECIMAL},
      </if>
      <if test="riIbnrAmount != null">
        #{riIbnrAmount,jdbcType=DECIMAL},
      </if>
      <if test="riCaseAmount != null ">
        #{riCaseAmount,jdbcType=DECIMAL},
      </if>
      <if test="yearMonth != null">
        #{yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="accidentQuarter != null">
        #{accidentQuarter,jdbcType=VARCHAR},
      </if>
      <if test="riskClassCode != null">
        #{riskClassCode,jdbcType=VARCHAR},
      </if>
      <if test="centerCode != null">
        #{centerCode,jdbcType=VARCHAR},
      </if>
      <if test="riDept != null">
        #{riDept,jdbcType=VARCHAR},
      </if>
      <if test="treatyNo != null">
        #{treatyNo,jdbcType=VARCHAR},
      </if>
      <if test="treatyName != null">
        #{treatyName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all 
    <foreach collection="list" item="item" index="index">
      into ATR_BUSS_IBNR_IMPORT_DETAIL values 
       (#{item.ibnrDetailId,jdbcType=DECIMAL}, 
        #{item.ibnrMainId,jdbcType=DECIMAL},#{item.businessModel,jdbcType=VARCHAR},
         #{item.ibnrAmount,jdbcType=DECIMAL}, #{item.riIbnrAmount,jdbcType=DECIMAL}, #{item.riCaseAmount,jdbcType=DECIMAL}
        )
    </foreach>
    select 1 
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrImportDetail">
    update ATR_BUSS_IBNR_IMPORT_DETAIL
    <set>
      <if test="ibnrMainId != null">
        IBNR_MAIN_ID = #{ibnrMainId,jdbcType=DECIMAL},
      </if>
      <if test="businessModel != null">
        BUSINESS_MODEL = #{businessModel,jdbcType=VARCHAR},
      </if>
      <if test="ibnrAmount != null">
        IBNR_AMOUNT = #{ibnrAmount,jdbcType=DECIMAL},
      </if>
      <if test="riIbnrAmount != null">
        ri_ibnr_amount = #{riIbnrAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where IBNR_DETAIL_ID = #{ibnrDetailId,jdbcType=DECIMAL}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrImportDetail">
    update ATR_BUSS_IBNR_IMPORT_DETAIL
    <set>
      <if test="record.ibnrMainId != null">
        IBNR_MAIN_ID = #{record.ibnrMainId,jdbcType=DECIMAL},
      </if>
      <if test="record.businessModel != null">
        BUSINESS_MODEL = #{record.businessModel,jdbcType=VARCHAR},
      </if>
      <if test="record.ibnrAmount != null">
        IBNR_AMOUNT = #{record.ibnrAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.riIbnrAmount != null">
        ri_ibnr_amount = #{record.riIbnrAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from ATR_BUSS_IBNR_IMPORT_DETAIL
    where IBNR_DETAIL_ID = #{ibnrDetailId,jdbcType=DECIMAL}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from ATR_BUSS_IBNR_IMPORT_DETAIL
    where IBNR_DETAIL_ID in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from ATR_BUSS_IBNR_IMPORT_DETAIL
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrImportDetail">
    select count(1) from ATR_BUSS_IBNR_IMPORT_DETAIL
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>