<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by Taiping MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2025-03-24 19:27:36 -->
<!-- Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.alloc.AtrBussIbnrAllocActionDao">
  <!-- 本配置文件由Taiping MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**BaseDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->
  <resultMap id="VoResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrAllocActionVo">
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="action_no" property="actionNo" jdbcType="VARCHAR" />
    <result column="entity_id" property="entityId" jdbcType="BIGINT" />
    <result column="year_month" property="yearMonth" jdbcType="VARCHAR" />
    <result column="business_source_code" property="businessSourceCode" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="VARCHAR" />
    <result column="confirm_is" property="confirmIs" jdbcType="VARCHAR" />
    <result column="confirm_user" property="confirmUser" jdbcType="BIGINT" />
    <result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP" />
    <result column="creator_id" property="creatorId" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="updator_id" property="updatorId" jdbcType="BIGINT" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />

    <result column="entity_code" property="entityCode" jdbcType="VARCHAR" />
    <result column="entity_c_name" property="entityCName" jdbcType="VARCHAR" />
    <result column="entity_l_name" property="entityLName" jdbcType="VARCHAR" />
    <result column="entity_e_name" property="entityEName" jdbcType="VARCHAR" />

    <result column="creatorName" property="creatorName" jdbcType="VARCHAR" />
    <result column="confirmName" property="confirmName" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Vo_Column_List">
    a.id, a.action_no, a.entity_id, a.year_month, a.business_source_code, a.status, a.confirm_is, a.confirm_user,
    a.confirm_time, a.creator_id, a.create_time, a.updator_id, a.update_time,
    c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name, u.user_name as creatorName, cf.user_name as confirmName
  </sql>

  <select id="fuzzySearchPage" flushCache="false" useCache="true" resultMap="VoResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrAllocActionVo">
    select
    <include refid="Vo_Column_List" />
    from atr_buss_ibnr_alloc_action a
    left join bpluser.bbs_conf_entity c on a.entity_id = c.entity_id
    left join bpluser.bpl_saa_user u on a.creator_id = u.user_id
    left join bpluser.bpl_saa_user cf on a.confirm_user = cf.user_id
    <include refid="Cust_Select_By_Entity_Where" />
    order by ID desc
  </select>

  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Cust_Select_By_Entity_Where">
    <where>
      <if test="actionNo != null and actionNo != ''">
        and a.ACTION_NO = #{actionNo,jdbcType=VARCHAR}
      </if>
      <if test="entityId != null ">
        and a.entity_id = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
        and a.YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="businessSourceCode != null and businessSourceCode != ''">
        and a.business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="status != null and status != ''">
        and a.STATUS = #{status,jdbcType=VARCHAR}
      </if>
      <if test="confirmIs != null and confirmIs != ''">
        and a.CONFIRM_IS = #{confirmIs,jdbcType=VARCHAR}
      </if>
      <if test="confirmUser != null ">
        and a.CONFIRM_USER = #{confirmUser,jdbcType=DECIMAL}
      </if>
    </where>
  </sql>

  <select id="updateConfirm" flushCache="false" useCache="true"   parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrAllocAction">
    update atr_buss_ibnr_alloc_action
    <set>
      <if test="confirmIs != null">
        CONFIRM_IS = #{confirmIs,jdbcType=VARCHAR},
      </if>
      <if test="confirmUser != null">
        CONFIRM_USER = #{confirmUser,jdbcType=DECIMAL},
      </if>
      <if test="confirmTime != null">
        CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        CREATOR_ID = #{creatorId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        UPDATOR_ID = #{updatorId,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = (
    SELECT T.ID
    FROM atr_buss_ibnr_alloc_action T
    WHERE entity_id = #{entityId, jdbcType = DECIMAL}
    AND YEAR_MONTH = #{yearMonth, jdbcType = VARCHAR}
    AND business_source_code = #{businessSourceCode, jdbcType = VARCHAR}
    and t.status = 'S'
    ORDER BY CREATE_TIME DESC
    limit 1
    )
  </select>

  <select id="cleanDuctIbnrEpData">
    truncate table atr_duct_ibnr_alloc_ep
  </select>

  <select id="cleanDuctIbnrEpDataOut">
    truncate table atr_duct_ibnr_alloc_ep_out
  </select>
  <select id="cleanDuctIbnrEpDataOutX">
    truncate table atr_duct_ibnr_alloc_ep_out_x
  </select>

  <select id="cleanDuctIbnrImportData">
    truncate table atr_duct_ibnr_import_ep
  </select>


  <select id="countDDIbnrAllocImBalance" flushCache="false" useCache="true"
          parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrAllocAction"
          resultType="java.lang.Long">
    SELECT COUNT(*)
    FROM (	SELECT BIID.YEAR_MONTH,
                  ACCIDENT_QUARTER,
                  RISK_CLASS_CODE,
                  CENTER_CODE,
                  IBNR_AMOUNT AS IBNR_AMOUNT
                  from atr_duct_ibnr_import_ep BIID
              WHERE BIID.action_no = #{actionNo,jdbcType=VARCHAR}
          ) FD
           FULL JOIN (SELECT TO_CHAR(TO_DATE(BIADR.ACC_YEAR_MONTH, 'YYYYMM'), 'YYYY"Q"Q') AS ACCIDENT_QUARTER,
                             RISK_CLASS_CODE,
                             CENTER_CODE,
                             ROUND(SUM(IBNR_AMOUNT), 8) AS IBNR_AMOUNT
                      FROM ATR_BUSS_IBNR_ALLOC_ACTION BIAA
                       LEFT JOIN ATR_BUSS_IBNR_ALLOC_DD_RESULT BIADR ON BIAA.ACTION_NO = BIADR.ACTION_NO
                      WHERE BIAA.action_no = #{actionNo,jdbcType=VARCHAR}
                        AND BIAA.BUSINESS_SOURCE_CODE = 'DD'
                      GROUP BY TO_CHAR(TO_DATE(BIADR.ACC_YEAR_MONTH, 'YYYYMM'), 'YYYY"Q"Q'),
                               RISK_CLASS_CODE,
                               CENTER_CODE) BA
                     ON FD.ACCIDENT_QUARTER = BA.ACCIDENT_QUARTER
                       AND FD.RISK_CLASS_CODE = BA.RISK_CLASS_CODE
                       AND FD.CENTER_CODE = BA.CENTER_CODE
    WHERE COALESCE(FD.IBNR_AMOUNT, 0) != COALESCE(BA.IBNR_AMOUNT, 0)
  </select>


  <select id="countOutIbnrAllocImBalance" flushCache="false" useCache="true"
          parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrAllocAction"
          resultType="java.lang.Long">
    SELECT COUNT(*)
    FROM (SELECT BIID.YEAR_MONTH,
                 ACCIDENT_QUARTER,
                 RISK_CLASS_CODE,
                 CENTER_CODE,
                 RI_IBNR_AMOUNT AS IBNR_AMOUNT
          from atr_duct_ibnr_import_ep BIID
          WHERE BIID.action_no = #{actionNo,jdbcType=VARCHAR}) FD
           FULL JOIN (SELECT TO_CHAR(TO_DATE(BIADR.ACC_YEAR_MONTH, 'YYYYMM'), 'YYYY"Q"Q') AS ACCIDENT_QUARTER,
                             RISK_CLASS_CODE,
                             CENTER_CODE,
                             ROUND(SUM(IBNR_AMOUNT), 8) AS IBNR_AMOUNT
                      FROM ATR_BUSS_IBNR_ALLOC_ACTION BIAA
                             LEFT JOIN atr_buss_ibnr_alloc_out_result BIADR ON BIAA.ACTION_NO = BIADR.ACTION_NO
                      WHERE BIAA.action_no = #{actionNo,jdbcType=VARCHAR}
                        AND BIAA.BUSINESS_SOURCE_CODE = 'OUT'
                      GROUP BY TO_CHAR(TO_DATE(BIADR.ACC_YEAR_MONTH, 'YYYYMM'), 'YYYY"Q"Q'),
                               RISK_CLASS_CODE,
                               CENTER_CODE) BA
                     ON FD.ACCIDENT_QUARTER = BA.ACCIDENT_QUARTER
                       AND FD.RISK_CLASS_CODE = BA.RISK_CLASS_CODE
                       AND FD.CENTER_CODE = BA.CENTER_CODE
    WHERE COALESCE(FD.IBNR_AMOUNT, 0) != COALESCE(BA.IBNR_AMOUNT, 0)
  </select>

  <select id="countTIIbnrAllocImBalance" flushCache="false" useCache="true"
          parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrAllocAction"
          resultType="java.lang.Long">
      SELECT COUNT(*)
      FROM (SELECT
                RISK_CLASS_CODE,
                treaty_no,
                ROUND(SUM(ibnr_amount), 2) AS IBNR_AMOUNT
            FROM ATR_BUSS_IBNR_IMPORT_MAIN BIIM
                     LEFT JOIN ATR_BUSS_IBNR_IMPORT_DETAIL BIID ON BIIM.IBNR_MAIN_ID = BIID.IBNR_MAIN_ID
            WHERE BIIM.ENTITY_ID =  #{entityId,jdbcType=DECIMAL}
              AND BIIM.YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
              AND BIIM.CONFIRM_IS = '1'
              AND BIID.BUSINESS_MODEL = 'TI'
            GROUP BY RISK_CLASS_CODE,
                     treaty_no) FD
               FULL JOIN (SELECT  RISK_CLASS_CODE,
                                  treaty_no,
                                  ROUND(SUM(IBNR_AMOUNT), 2) AS IBNR_AMOUNT
                          FROM ATR_BUSS_IBNR_ALLOC_ACTION BIAA
                                   LEFT JOIN atr_buss_ibnr_alloc_ti_result BIADR ON BIAA.ACTION_NO = BIADR.ACTION_NO
                          WHERE BIAA.action_no = #{actionNo,jdbcType=VARCHAR}
                            AND BIAA.BUSINESS_SOURCE_CODE = 'TI'
                          GROUP BY RISK_CLASS_CODE,
                                   treaty_no) BA
                         ON FD.RISK_CLASS_CODE = BA.RISK_CLASS_CODE
                             AND FD.treaty_no = BA.treaty_no
      WHERE COALESCE(FD.IBNR_AMOUNT, 0) != COALESCE(BA.IBNR_AMOUNT, 0)
  </select>

    <select id="countCaseAllocImBalance" flushCache="false" useCache="true"
            parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrAllocAction"
            resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM (SELECT treaty_no,
                     ROUND(SUM(ri_case_amount), 8) AS case_amount
              FROM ATR_BUSS_IBNR_IMPORT_MAIN BIIM
                       LEFT JOIN ATR_BUSS_IBNR_IMPORT_DETAIL BIID ON BIIM.IBNR_MAIN_ID = BIID.IBNR_MAIN_ID
              WHERE BIIM.ENTITY_ID =  #{entityId,jdbcType=DECIMAL}
                AND BIIM.YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
                AND BIIM.CONFIRM_IS = '1'
                AND BIID.BUSINESS_MODEL = 'XO'
              GROUP BY treaty_no) FD
                 FULL JOIN (SELECT treaty_no,
                                   ROUND(SUM(case_amount), 8) AS case_amount
                            FROM ATR_BUSS_IBNR_ALLOC_ACTION BIAA
                                     LEFT JOIN atr_buss_ibnr_alloc_to_result_x BIADR ON BIAA.ACTION_NO = BIADR.ACTION_NO
                            WHERE BIAA.ENTITY_ID =  #{entityId,jdbcType=DECIMAL}
                              AND BIAA.YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
                              AND BIAA.CONFIRM_IS = '1'
                              AND BIAA.BUSINESS_SOURCE_CODE = 'XO'
                            GROUP BY treaty_no) BA
                           ON  FD.treaty_no = BA.treaty_no
        WHERE COALESCE(FD.case_amount, 0) != COALESCE(BA.case_amount, 0)
    </select>

    <select id="reSetIbnrCashFlow">
        update atr_buss_ibnr_alloc_action
        set confirm_is='0',
            CONFIRM_USER=null,
            confirm_time=null
        where entity_id = #{entityId,jdbcType=DECIMAL}
          and year_month >= #{yearMonth,jdbcType=VARCHAR}
          and confirm_is = '1'
    </select>

    <select id="findDDTailDiffList" resultType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussDiffIbnrAllocVo">
        SELECT
            FD.YEAR_MONTH as yearMonth,
            fd.ACCIDENT_QUARTER as accidentQuarter,
            fd.RISK_CLASS_CODE as riskClassCode,
            fd.CENTER_CODE,
            fd.IBNR_AMOUNT - ba.IBNR_AMOUNT as diffAmount,
            (SELECT MAX(id) FROM
                    ATR_BUSS_IBNR_ALLOC_DD_RESULT BIADR
                WHERE BIADR.action_no = BA.action_no
                  AND TO_CHAR( TO_DATE( BIADR.ACC_YEAR_MONTH, 'YYYYMM' ), 'YYYY"Q"Q' )= BA.ACCIDENT_QUARTER
                  AND BIADR.RISK_CLASS_CODE = BA.RISK_CLASS_CODE
                  AND BIADR.CENTER_CODE = BA.CENTER_CODE
                  AND BIADR.IBNR_AMOUNT = BA.maxAmount
            ) as ibnrDetailId
        FROM
            ( SELECT
                    BIID.YEAR_MONTH,
                    ACCIDENT_QUARTER,
                    RISK_CLASS_CODE,
                    CENTER_CODE,
                    IBNR_AMOUNT AS IBNR_AMOUNT
                from atr_duct_ibnr_import_ep BIID
                WHERE BIID.action_no = #{actionNo,jdbcType=VARCHAR}
            ) FD
                FULL JOIN (
                SELECT
                    BIAA.ACTION_NO,
                    TO_CHAR( TO_DATE( BIADR.ACC_YEAR_MONTH, 'YYYYMM' ), 'YYYY"Q"Q' ) AS ACCIDENT_QUARTER,
                    RISK_CLASS_CODE,
                    CENTER_CODE,
                    ROUND( SUM ( IBNR_AMOUNT ), 8) AS IBNR_AMOUNT,
                    MAX ( IBNR_AMOUNT ) AS maxAmount
                FROM
                    ATR_BUSS_IBNR_ALLOC_ACTION BIAA
                        LEFT JOIN ATR_BUSS_IBNR_ALLOC_DD_RESULT BIADR ON BIAA.ACTION_NO = BIADR.ACTION_NO
                WHERE BIAA.action_no = #{actionNo,jdbcType=VARCHAR}
                GROUP BY
                    BIAA.ACTION_NO,
                    TO_CHAR( TO_DATE( BIADR.ACC_YEAR_MONTH, 'YYYYMM' ), 'YYYY"Q"Q' ),
                    RISK_CLASS_CODE,
                    CENTER_CODE
            ) BA ON FD.ACCIDENT_QUARTER = BA.ACCIDENT_QUARTER
                AND FD.RISK_CLASS_CODE = BA.RISK_CLASS_CODE
                AND FD.CENTER_CODE = BA.CENTER_CODE
        WHERE COALESCE ( FD.IBNR_AMOUNT, 8 ) != COALESCE ( BA.IBNR_AMOUNT, 8 )
    </select>

    <update id="allocDDBalance" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussDiffIbnrAllocVo">
        UPDATE ATR_BUSS_IBNR_ALLOC_DD_RESULT s
        SET IBNR_AMOUNT = IBNR_AMOUNT + #{diffAmount,jdbcType=DECIMAL}
        WHERE id = #{ibnrDetailId,jdbcType=DECIMAL}
    </update>

    <select id="findOutTailDiffList" resultType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussDiffIbnrAllocVo">
        SELECT
            FD.YEAR_MONTH as yearMonth,
            fd.ACCIDENT_QUARTER as accidentQuarter,
            fd.RISK_CLASS_CODE as riskClassCode,
            fd.CENTER_CODE,
            fd.IBNR_AMOUNT - ba.IBNR_AMOUNT as diffAmount,
            (SELECT MAX(id) FROM
                ATR_BUSS_IBNR_ALLOC_OUT_RESULT BIADR
             WHERE BIADR.action_no = BA.action_no
               AND TO_CHAR( TO_DATE( BIADR.ACC_YEAR_MONTH, 'YYYYMM' ), 'YYYY"Q"Q' )= BA.ACCIDENT_QUARTER
               AND BIADR.RISK_CLASS_CODE = BA.RISK_CLASS_CODE
               AND BIADR.CENTER_CODE = BA.CENTER_CODE
               AND BIADR.IBNR_AMOUNT = BA.maxAmount
            ) as ibnrDetailId
        FROM
            ( SELECT
                  BIID.YEAR_MONTH,
                  ACCIDENT_QUARTER,
                  RISK_CLASS_CODE,
                  CENTER_CODE,
                  RI_IBNR_AMOUNT AS IBNR_AMOUNT
              from atr_duct_ibnr_import_ep BIID
              WHERE BIID.action_no = #{actionNo,jdbcType=VARCHAR}
            ) FD
                FULL JOIN (
                SELECT
                    BIAA.ACTION_NO,
                    TO_CHAR( TO_DATE( BIADR.ACC_YEAR_MONTH, 'YYYYMM' ), 'YYYY"Q"Q' ) AS ACCIDENT_QUARTER,
                    RISK_CLASS_CODE,
                    CENTER_CODE,
                    ROUND( SUM ( IBNR_AMOUNT ), 8 ) AS IBNR_AMOUNT,
                    MAX ( IBNR_AMOUNT ) AS maxAmount
                FROM
                    ATR_BUSS_IBNR_ALLOC_ACTION BIAA
                        LEFT JOIN ATR_BUSS_IBNR_ALLOC_OUT_RESULT BIADR ON BIAA.ACTION_NO = BIADR.ACTION_NO
                WHERE BIAA.action_no = #{actionNo,jdbcType=VARCHAR}
                GROUP BY
                    BIAA.ACTION_NO,
                    TO_CHAR( TO_DATE( BIADR.ACC_YEAR_MONTH, 'YYYYMM' ), 'YYYY"Q"Q' ),
                    RISK_CLASS_CODE,
                    CENTER_CODE
            ) BA ON FD.ACCIDENT_QUARTER = BA.ACCIDENT_QUARTER
                AND FD.RISK_CLASS_CODE = BA.RISK_CLASS_CODE
                AND FD.CENTER_CODE = BA.CENTER_CODE
        WHERE COALESCE ( FD.IBNR_AMOUNT, 8 ) != COALESCE ( BA.IBNR_AMOUNT, 8 )
    </select>

    <update id="allocOutBalance" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussDiffIbnrAllocVo">
        UPDATE ATR_BUSS_IBNR_ALLOC_OUT_RESULT s
        SET ibnr_amount = ibnr_amount + #{diffAmount,jdbcType=DECIMAL}
        WHERE id = #{ibnrDetailId,jdbcType=DECIMAL}
    </update>
</mapper>