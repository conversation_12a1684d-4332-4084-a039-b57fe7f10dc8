package com.ss.ifrs.actuarial.api;

import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfIbnrcalcQuotaDetailVo;
import com.ss.ifrs.actuarial.service.AtrConfIbnrcalcQuotaDevApiService;
import com.ss.platform.core.annotation.TrackUserBehavioral;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.core.constant.ResCodeConstant;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/ibnrcalc")
public class AtrConfIbnrcalcQuotaDevApi extends BaseApi {
    @Autowired
    private AtrConfIbnrcalcQuotaDevApiService atrConfIbnrcalcQuotaDevApiService;
    @ApiOperation(value = "查询ibnr计算发展期配置")
    @TrackUserBehavioral(description = "query conf ibnr_calc")
    @PostMapping("/queryIbnrcalcQuota")
    public BaseResponse<Map<String, Object>> queryAllIbnrcalcDev(){
        List<AtrConfIbnrcalcQuotaDetailVo> atrConfIbnrcalcQuotaDetailVos = atrConfIbnrcalcQuotaDevApiService.queryAllIbnrcalcDev();
        Map<String, Object> result = new HashMap<>();
        result.put("result", atrConfIbnrcalcQuotaDetailVos);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
    }
    @ApiOperation(value = "保存或更新ibnr计算发展期配置")
    @TrackUserBehavioral(description = "save or update conf ibnr_calc")
    @PostMapping(value = "/saveOrUpdateIbnrcalcQuota")
    public BaseResponse<?> saveConfWeight(@RequestBody List<AtrConfIbnrcalcQuotaDetailVo> vos,
                                          HttpServletRequest request) {
        Long userId = loginUserId(request);
        if (userId == null) {
            userId = 1L;
        }
        atrConfIbnrcalcQuotaDevApiService.saveOrUpdateIbnrcalc(vos, userId);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "OK");
    }
}
