[2m2025-07-21 15:09:07.824[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 15:14:07.842[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 15:19:07.855[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 15:20:25.829[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[     Thread-166][0;39m [36mc.n.l.PollingServerListUpdater          [0;39m [2m:[0;39m Shutting down the Executor Pool for PollingServerListUpdater
[2m2025-07-21 15:20:25.982[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application SS-IFRS-ACTUARIAL with eureka with status DOWN
[2m2025-07-21 15:20:25.983[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Saw local status change event StatusChangeEvent [timestamp=1753082425983, current=DOWN, previous=UP]
[2m2025-07-21 15:20:26.026[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[nfoReplicator-0][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m DiscoveryClient_SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608: registering service...
[2m2025-07-21 15:20:26.869[0;39m [APP_ENV_IS_UNDEFINED] [31mERROR [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[nfoReplicator-0][0;39m [36mc.n.d.s.t.d.RedirectingEurekaHttpClient [0;39m [2m:[0;39m Request execution error. endpoint=DefaultEndpoint{ serviceUrl='**********************************************/eureka/}

com.sun.jersey.api.client.ClientHandlerException: org.apache.http.NoHttpResponseException: 127.0.0.1:7602 failed to respond
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.post(WebResource.java:570)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.register(AbstractJerseyEurekaHttpClient.java:57)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:857)
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121)
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.apache.http.NoHttpResponseException: 127.0.0.1:7602 failed to respond
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:141)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:56)
	at org.apache.http.impl.io.AbstractMessageParser.parse(AbstractMessageParser.java:259)
	at org.apache.http.impl.AbstractHttpClientConnection.receiveResponseHeader(AbstractHttpClientConnection.java:294)
	at org.apache.http.impl.conn.DefaultClientConnection.receiveResponseHeader(DefaultClientConnection.java:257)
	at org.apache.http.impl.conn.AbstractClientConnAdapter.receiveResponseHeader(AbstractClientConnAdapter.java:230)
	at org.apache.http.protocol.HttpRequestExecutor.doReceiveResponse(HttpRequestExecutor.java:273)
	at org.apache.http.protocol.HttpRequestExecutor.execute(HttpRequestExecutor.java:125)
	at org.apache.http.impl.client.DefaultRequestDirector.tryExecute(DefaultRequestDirector.java:679)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:481)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 29 common frames omitted

[2m2025-07-21 15:20:26.870[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[nfoReplicator-0][0;39m [36mc.n.d.s.t.d.RetryableEurekaHttpClient   [0;39m [2m:[0;39m Request execution failed with message: org.apache.http.NoHttpResponseException: 127.0.0.1:7602 failed to respond
[2m2025-07-21 15:20:26.895[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.s.l.thread.SSThreadPoolTaskExecutor   [0;39m [2m:[0;39m Shutting down ExecutorService 'dataSliceThreadPool'
[2m2025-07-21 15:20:27.338[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.s.l.thread.SSThreadPoolTaskExecutor   [0;39m [2m:[0;39m Shutting down ExecutorService 'ruleThreadPool'
[2m2025-07-21 15:20:27.370[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.u.concurrent.ShutdownEnabledTimer   [0;39m [2m:[0;39m Shutdown hook removed for: NFLoadBalancer-PingTimer-SS-PLATFORM-COMMON
[2m2025-07-21 15:20:27.370[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.u.concurrent.ShutdownEnabledTimer   [0;39m [2m:[0;39m Exception caught (might be ok if at shutdown)

java.lang.IllegalStateException: Shutdown in progress
	at java.lang.ApplicationShutdownHooks.remove(ApplicationShutdownHooks.java:82)
	at java.lang.Runtime.removeShutdownHook(Runtime.java:239)
	at com.netflix.util.concurrent.ShutdownEnabledTimer.cancel(ShutdownEnabledTimer.java:70)
	at com.netflix.loadbalancer.BaseLoadBalancer.cancelPingTask(BaseLoadBalancer.java:632)
	at com.netflix.loadbalancer.BaseLoadBalancer.shutdown(BaseLoadBalancer.java:883)
	at com.netflix.loadbalancer.DynamicServerListLoadBalancer.shutdown(DynamicServerListLoadBalancer.java:285)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.invokeCustomDestroyMethod(DisposableBeanAdapter.java:339)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:273)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:978)
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:92)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:948)

[2m2025-07-21 15:20:27.411[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m IFRSHikariCPForATR - Shutdown initiated...
[2m2025-07-21 15:20:27.422[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m IFRSHikariCPForATR - Shutdown completed.
[2m2025-07-21 15:20:27.425[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Shutting down DiscoveryClient ...
[2m2025-07-21 15:20:28.938[0;39m [APP_ENV_IS_UNDEFINED] [31mERROR [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[nfoReplicator-0][0;39m [36mc.n.d.s.t.d.RedirectingEurekaHttpClient [0;39m [2m:[0;39m Request execution error. endpoint=DefaultEndpoint{ serviceUrl='**********************************************/eureka/}

com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused: connect
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.post(WebResource.java:570)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.register(AbstractJerseyEurekaHttpClient.java:57)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:118)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:79)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:857)
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121)
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 30 common frames omitted

[2m2025-07-21 15:20:28.938[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[nfoReplicator-0][0;39m [36mc.n.d.s.t.d.RetryableEurekaHttpClient   [0;39m [2m:[0;39m Request execution failed with message: java.net.ConnectException: Connection refused: connect
[2m2025-07-21 15:20:28.951[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[nfoReplicator-0][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m DiscoveryClient_SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 - registration failed Cannot execute request on any known server

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:857)
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121)
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

[2m2025-07-21 15:20:28.952[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[nfoReplicator-0][0;39m [36mc.n.discovery.InstanceInfoReplicator    [0;39m [2m:[0;39m There was a problem with the instance info replicator

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:857)
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121)
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

[2m2025-07-21 15:20:29.010[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Unregistering ...
[2m2025-07-21 15:20:31.251[0;39m [APP_ENV_IS_UNDEFINED] [31mERROR [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.d.s.t.d.RedirectingEurekaHttpClient [0;39m [2m:[0;39m Request execution error. endpoint=DefaultEndpoint{ serviceUrl='**********************************************/eureka/}

com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused: connect
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:118)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:79)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:953)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:929)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:948)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 43 common frames omitted

[2m2025-07-21 15:20:31.252[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.d.s.t.d.RetryableEurekaHttpClient   [0;39m [2m:[0;39m Request execution failed with message: java.net.ConnectException: Connection refused: connect
[2m2025-07-21 15:20:31.252[0;39m [APP_ENV_IS_UNDEFINED] [31mERROR [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m DiscoveryClient_SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 - de-registration failedCannot execute request on any known server

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:953)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:929)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:948)

[2m2025-07-21 15:20:31.268[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m27672[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Completed shut down of DiscoveryClient
[2m2025-07-21 16:20:44.068[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 16:25:44.082[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 16:30:44.097[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 16:35:44.102[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 16:40:44.107[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 16:41:54.719[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-07-21 16:41:54.795[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 74ms. Found 0 Redis repository interfaces.
[2m2025-07-21 16:41:54.797[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mo.m.s.mapper.ClassPathMapperScanner     [0;39m [2m:[0;39m No MyBatis mapper was found in '[com.ss.*.**.dao]' package. Please check your configuration.
[2m2025-07-21 16:41:55.207[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,6e1bc712cfc18fe6,c9941f6dbf336858,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mpertySourcedRequestMappingHandlerMapping[0;39m [2m:[0;39m Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
[2m2025-07-21 16:41:55.215[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6e1bc712cfc18fe6,c9941f6dbf336858,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-21 16:41:55.217[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6e1bc712cfc18fe6,c9941f6dbf336858,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lrc.AtrBussPHLrcActionDao.fuzzySearchPage
[2m2025-07-21 16:41:55.217[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6e1bc712cfc18fe6,c9941f6dbf336858,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrBussLrcActionVo":{"businessSourceCode":"DD","confirmIs":"","currencyCode":"","entityCode":"01 -- 海峡金桥财产保险股份有限公司","entityId":1,"loaCode":"","portfolioNo":"","riskClassCode":"","yearMonth":"202502"},"pageParam":{"countable":true,"offset":0,"page":0,"pageNumber":0,"pageSize":10,"paged":true,"size":10,"unpaged":false},"param1":{"$ref":"$.atrBussLrcActionVo"},"param2":{"$ref":"$.pageParam"}}
[2m2025-07-21 16:41:55.217[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6e1bc712cfc18fe6,c9941f6dbf336858,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter2: {"businessSourceCode":"DD","confirmIs":"","currencyCode":"","entityCode":"01 -- 海峡金桥财产保险股份有限公司","entityId":1,"loaCode":"","portfolioNo":"","riskClassCode":"","yearMonth":"202502"}
[2m2025-07-21 16:41:55.218[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6e1bc712cfc18fe6,c9941f6dbf336858,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-21 16:41:55.218[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6e1bc712cfc18fe6,c9941f6dbf336858,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-21 16:41:55.218[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6e1bc712cfc18fe6,c9941f6dbf336858,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql begin.
[2m2025-07-21 16:41:55.218[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6e1bc712cfc18fe6,c9941f6dbf336858,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL CacheKey: Cache:-70957366
[2m2025-07-21 16:41:55.288[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6e1bc712cfc18fe6,c9941f6dbf336858,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m CountSql: select count(1) FROM ('SQL') tmp_count
[2m2025-07-21 16:41:55.412[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6e1bc712cfc18fe6,c9941f6dbf336858,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql end. Time cost: 194ms, total num： 0.
[2m2025-07-21 16:41:55.413[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6e1bc712cfc18fe6,c9941f6dbf336858,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m ==>  Preparing: select a.ID,a.ACTION_NO, a.entity_id, a.YEAR_MONTH,a.business_source_code,a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name, u.user_name as creatorName FROM ATR_BUSS_LRC_ACTION a left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id left JOIN bpluser.bpl_saa_user u on a.creator_id = u.user_id WHERE a.entity_id = ? and a.YEAR_MONTH = ? and a.business_source_code = ? ORDER BY ID desc limit 10 
[2m2025-07-21 16:41:55.414[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6e1bc712cfc18fe6,c9941f6dbf336858,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m ==> Parameters: 1(Long), 202502(String), DD(String)
[2m2025-07-21 16:41:55.454[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6e1bc712cfc18fe6,c9941f6dbf336858,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-21 16:41:55.455[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,6e1bc712cfc18fe6,c9941f6dbf336858,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-8][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 238
[2m2025-07-21 16:42:55.665[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,a25ad956e602acf1,a52daa77ab519e71,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-21 16:42:55.665[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,a25ad956e602acf1,a52daa77ab519e71,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.buss.puhua.lrc.AtrBussPHLrcActionDao.fuzzySearchPage
[2m2025-07-21 16:42:55.665[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,a25ad956e602acf1,a52daa77ab519e71,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"atrBussLrcActionVo":{"businessSourceCode":"DD","confirmIs":"","currencyCode":"","entityCode":"01 -- 海峡金桥财产保险股份有限公司","entityId":1,"loaCode":"","portfolioNo":"","riskClassCode":"","yearMonth":"202502"},"pageParam":{"countable":true,"offset":0,"page":0,"pageNumber":0,"pageSize":10,"paged":true,"size":10,"unpaged":false},"param1":{"$ref":"$.atrBussLrcActionVo"},"param2":{"$ref":"$.pageParam"}}
[2m2025-07-21 16:42:55.665[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,a25ad956e602acf1,a52daa77ab519e71,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter2: {"businessSourceCode":"DD","confirmIs":"","currencyCode":"","entityCode":"01 -- 海峡金桥财产保险股份有限公司","entityId":1,"loaCode":"","portfolioNo":"","riskClassCode":"","yearMonth":"202502"}
[2m2025-07-21 16:42:55.665[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,a25ad956e602acf1,a52daa77ab519e71,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-21 16:42:55.665[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,a25ad956e602acf1,a52daa77ab519e71,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-21 16:42:55.665[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,a25ad956e602acf1,a52daa77ab519e71,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql begin.
[2m2025-07-21 16:42:55.665[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,a25ad956e602acf1,a52daa77ab519e71,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL CacheKey: Cache:-70957366
[2m2025-07-21 16:42:55.754[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,a25ad956e602acf1,a52daa77ab519e71,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m CountSql: select count(1) FROM ('SQL') tmp_count
[2m2025-07-21 16:42:55.795[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,a25ad956e602acf1,a52daa77ab519e71,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m Execute countSql end. Time cost: 130ms, total num： 0.
[2m2025-07-21 16:42:55.795[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,a25ad956e602acf1,a52daa77ab519e71,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m ==>  Preparing: select a.ID,a.ACTION_NO, a.entity_id, a.YEAR_MONTH,a.business_source_code,a.STATUS, a.CONFIRM_IS, a.CONFIRM_USER, a.CONFIRM_TIME, a.CREATOR_ID, a.CREATE_TIME, a.UPDATOR_ID, a.UPDATE_TIME, c.entity_code, c.entity_e_name, c.entity_c_name, c.entity_l_name, u.user_name as creatorName FROM ATR_BUSS_LRC_ACTION a left JOIN bpluser.bbs_conf_entity c on a.entity_id = c.entity_id left JOIN bpluser.bpl_saa_user u on a.creator_id = u.user_id WHERE a.entity_id = ? and a.YEAR_MONTH = ? and a.business_source_code = ? ORDER BY ID desc limit 10 
[2m2025-07-21 16:42:55.795[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,a25ad956e602acf1,a52daa77ab519e71,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m ==> Parameters: 1(Long), 202502(String), DD(String)
[2m2025-07-21 16:42:55.858[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,a25ad956e602acf1,a52daa77ab519e71,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.i.a.d.b.p.l.A.fuzzySearchPage       [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-21 16:42:55.858[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,a25ad956e602acf1,a52daa77ab519e71,true][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-3][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 193
[2m2025-07-21 16:45:44.109[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 16:50:44.122[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 16:55:44.124[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 17:00:44.125[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 17:05:44.127[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 17:10:44.134[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 17:15:44.141[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 17:20:44.146[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 17:25:44.150[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 17:30:44.151[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 17:35:44.166[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 17:40:44.172[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 17:45:44.181[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 17:50:44.182[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 17:54:45.664[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-07-21 17:54:45.750[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 84ms. Found 0 Redis repository interfaces.
[2m2025-07-21 17:54:45.752[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mo.m.s.mapper.ClassPathMapperScanner     [0;39m [2m:[0;39m No MyBatis mapper was found in '[com.ss.*.**.dao]' package. Please check your configuration.
[2m2025-07-21 17:54:52.986[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,e501a04142c00989,855618c4572523d7,false][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mpertySourcedRequestMappingHandlerMapping[0;39m [2m:[0;39m Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
[2m2025-07-21 17:54:52.997[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e501a04142c00989,855618c4572523d7,false][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL Command type: SELECT
[2m2025-07-21 17:54:52.997[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e501a04142c00989,855618c4572523d7,false][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL MappedStatement id: com.ss.ifrs.actuarial.dao.conf.AtrConfBussPeriodDao.findByVo
[2m2025-07-21 17:54:52.997[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e501a04142c00989,855618c4572523d7,false][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL parameter: {"bizCode":"BUSS_ACTION","entityCode":"","entityId":1,"periodState":"2"}
[2m2025-07-21 17:54:52.998[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e501a04142c00989,855618c4572523d7,false][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: limitedUrls is null, checking limitedTables.
[2m2025-07-21 17:54:52.998[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e501a04142c00989,855618c4572523d7,false][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m MybatisPageableInterceptor getDataFilterSQL: no limitedTables in app common config, skip getDataFilterSQL.
[2m2025-07-21 17:54:53.025[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e501a04142c00989,855618c4572523d7,false][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.c.A.findByVo                  [0;39m [2m:[0;39m ==>  Preparing: select t1.buss_period_id, t1.entity_id, t1.year_month, t1.period_state, t1.valid_is, t1.creator_id, t1.create_time, t1.updator_id, t1.update_time ,t2.entity_code ,t2.entity_c_name ,t2.entity_l_name ,t2.entity_e_name FROM ATR_CONF_BUSSPERIOD t1 LEFT JOIN bpluser.bbs_conf_entity t2 ON t2.entity_id = t1.entity_id WHERE T1.entity_id = ? and T1.period_state = ? and T1.VALID_IS = '1' ORDER BY t1.year_month ASC limit 1 
[2m2025-07-21 17:54:53.025[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e501a04142c00989,855618c4572523d7,false][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.c.A.findByVo                  [0;39m [2m:[0;39m ==> Parameters: 1(Long), 2(String)
[2m2025-07-21 17:54:53.058[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e501a04142c00989,855618c4572523d7,false][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.i.a.d.c.A.findByVo                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-21 17:54:53.059[0;39m [APP_ENV_IS_UNDEFINED] [32mDEBUG [ss-ifrs-actuarial,e501a04142c00989,855618c4572523d7,false][0;39m [35m19352[0;39m [2m---[0;39m [2m[nio-7608-exec-2][0;39m [36mc.s.l.m.interceptor.QueryInterceptor    [0;39m [2m:[0;39m SQL COST(ms): 62
[2m2025-07-21 17:55:44.195[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 18:00:44.205[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 18:05:44.207[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 18:10:44.209[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 18:15:44.215[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 18:20:44.224[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 18:25:44.236[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 18:30:44.243[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 18:35:44.258[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 18:40:44.269[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[trap-executor-0][0;39m [36mc.n.d.s.r.aws.ConfigClusterResolver     [0;39m [2m:[0;39m Resolving eureka endpoints via configuration
[2m2025-07-21 18:44:38.975[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[     Thread-157][0;39m [36mc.n.l.PollingServerListUpdater          [0;39m [2m:[0;39m Shutting down the Executor Pool for PollingServerListUpdater
[2m2025-07-21 18:44:39.020[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application SS-IFRS-ACTUARIAL with eureka with status DOWN
[2m2025-07-21 18:44:39.020[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Saw local status change event StatusChangeEvent [timestamp=1753094679020, current=DOWN, previous=UP]
[2m2025-07-21 18:44:39.021[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[nfoReplicator-0][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m DiscoveryClient_SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608: registering service...
[2m2025-07-21 18:44:39.053[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[nfoReplicator-0][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m DiscoveryClient_SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 - registration status: 204
[2m2025-07-21 18:44:39.091[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.s.l.thread.SSThreadPoolTaskExecutor   [0;39m [2m:[0;39m Shutting down ExecutorService 'dataSliceThreadPool'
[2m2025-07-21 18:44:39.705[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.s.l.thread.SSThreadPoolTaskExecutor   [0;39m [2m:[0;39m Shutting down ExecutorService 'ruleThreadPool'
[2m2025-07-21 18:44:39.725[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.u.concurrent.ShutdownEnabledTimer   [0;39m [2m:[0;39m Shutdown hook removed for: NFLoadBalancer-PingTimer-SS-PLATFORM-COMMON
[2m2025-07-21 18:44:39.725[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.u.concurrent.ShutdownEnabledTimer   [0;39m [2m:[0;39m Exception caught (might be ok if at shutdown)

java.lang.IllegalStateException: Shutdown in progress
	at java.lang.ApplicationShutdownHooks.remove(ApplicationShutdownHooks.java:82)
	at java.lang.Runtime.removeShutdownHook(Runtime.java:239)
	at com.netflix.util.concurrent.ShutdownEnabledTimer.cancel(ShutdownEnabledTimer.java:70)
	at com.netflix.loadbalancer.BaseLoadBalancer.cancelPingTask(BaseLoadBalancer.java:632)
	at com.netflix.loadbalancer.BaseLoadBalancer.shutdown(BaseLoadBalancer.java:883)
	at com.netflix.loadbalancer.DynamicServerListLoadBalancer.shutdown(DynamicServerListLoadBalancer.java:285)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.invokeCustomDestroyMethod(DisposableBeanAdapter.java:339)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:273)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:978)
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:92)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:948)

[2m2025-07-21 18:44:39.741[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.u.concurrent.ShutdownEnabledTimer   [0;39m [2m:[0;39m Shutdown hook removed for: NFLoadBalancer-PingTimer-SS-PLATFORM-SCHEDULE
[2m2025-07-21 18:44:39.741[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.u.concurrent.ShutdownEnabledTimer   [0;39m [2m:[0;39m Exception caught (might be ok if at shutdown)

java.lang.IllegalStateException: Shutdown in progress
	at java.lang.ApplicationShutdownHooks.remove(ApplicationShutdownHooks.java:82)
	at java.lang.Runtime.removeShutdownHook(Runtime.java:239)
	at com.netflix.util.concurrent.ShutdownEnabledTimer.cancel(ShutdownEnabledTimer.java:70)
	at com.netflix.loadbalancer.BaseLoadBalancer.cancelPingTask(BaseLoadBalancer.java:632)
	at com.netflix.loadbalancer.BaseLoadBalancer.shutdown(BaseLoadBalancer.java:883)
	at com.netflix.loadbalancer.DynamicServerListLoadBalancer.shutdown(DynamicServerListLoadBalancer.java:285)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.invokeCustomDestroyMethod(DisposableBeanAdapter.java:339)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:273)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:978)
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:92)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:948)

[2m2025-07-21 18:44:39.745[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m IFRSHikariCPForATR - Shutdown initiated...
[2m2025-07-21 18:44:39.751[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m IFRSHikariCPForATR - Shutdown completed.
[2m2025-07-21 18:44:39.756[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Shutting down DiscoveryClient ...
[2m2025-07-21 18:44:42.760[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Unregistering ...
[2m2025-07-21 18:44:44.876[0;39m [APP_ENV_IS_UNDEFINED] [31mERROR [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.d.s.t.d.RedirectingEurekaHttpClient [0;39m [2m:[0;39m Request execution error. endpoint=DefaultEndpoint{ serviceUrl='**********************************************/eureka/}

com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused: connect
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:953)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:929)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:948)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 42 common frames omitted

[2m2025-07-21 18:44:44.877[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.d.s.t.d.RetryableEurekaHttpClient   [0;39m [2m:[0;39m Request execution failed with message: java.net.ConnectException: Connection refused: connect
[2m2025-07-21 18:44:46.928[0;39m [APP_ENV_IS_UNDEFINED] [31mERROR [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.d.s.t.d.RedirectingEurekaHttpClient [0;39m [2m:[0;39m Request execution error. endpoint=DefaultEndpoint{ serviceUrl='**********************************************/eureka/}

com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused: connect
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:118)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:79)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:953)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:929)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:948)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 43 common frames omitted

[2m2025-07-21 18:44:46.928[0;39m [APP_ENV_IS_UNDEFINED] [33m WARN [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.d.s.t.d.RetryableEurekaHttpClient   [0;39m [2m:[0;39m Request execution failed with message: java.net.ConnectException: Connection refused: connect
[2m2025-07-21 18:44:46.934[0;39m [APP_ENV_IS_UNDEFINED] [31mERROR [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m DiscoveryClient_SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 - de-registration failedCannot execute request on any known server

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:953)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:929)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1091)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.AbstractBeanFactory.destroySingletons(AbstractBeanFactory.java:44004)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1084)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1060)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1029)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:948)

[2m2025-07-21 18:44:46.949[0;39m [APP_ENV_IS_UNDEFINED] [32m INFO [ss-ifrs-actuarial,,,][0;39m [35m19352[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Completed shut down of DiscoveryClient
