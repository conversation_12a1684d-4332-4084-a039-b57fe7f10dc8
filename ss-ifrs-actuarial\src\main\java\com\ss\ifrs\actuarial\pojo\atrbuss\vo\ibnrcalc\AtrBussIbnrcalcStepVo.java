package com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Setter @Getter
public class AtrBussIbnrcalcStepVo {

    private String actionNo;

    private Long icpId;

    private Long entityId;

    private String entityCode;

    private String entityName;

    private String extractionInterval;

    private String extractionIntervalName;

    private Integer extractionZones;

    private String extractionDeadline;

    private String currencyCode;

    private String ibnrType;

    private String ibnrTypeName;

    private String loaCode;

    private String loaName;

    private String portfolioNo;

    private Date createTime;

    private String accidentNodeDesc;

    private List<String> accidentNodes = new ArrayList<>();

    private List<Long> devNos = new ArrayList<>();

    private AtrInflationRatioOverviewVo infRatioData;

    private List<AtrBussIbnrcalcStepAccDevDataVo> accDevData = new ArrayList<>();

    private List<AtrBussIbnrcalcStepDevDataVo> devData = new ArrayList<>();

    private List<AtrBussIbnrcalcStepAccDataVo> accData = new ArrayList<>();
    private Map<String,Object> atrBussIbnrcalcStep4DataVo;

}
