package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/4/15
 */
public class AtrBussCalcVersionVo {

    private Long licMainId;

    private Long lrcMainId;

    private String calcType;

    private String versionNo;

    private String yearMonth;

    private String riskClassCode;

    private Date drawTime;

    private Long confirmId;

    private String isConfirm;

    private String icgNo ;


    private BigDecimal premium;

    private BigDecimal claims;

    private BigDecimal expenses;

    private BigDecimal ra;

    private BigDecimal pvFcf;

    private BigDecimal pvRa;

    private BigDecimal pvAp;



    private String cmUnitNo;

    private String policyNo;

    private String endorseNo;

    private BigDecimal incomeAmount;

    private BigDecimal realityAmount;

    private BigDecimal lrcAmount;

    public Long getLicMainId() {
        return licMainId;
    }

    public void setLicMainId(Long licMainId) {
        this.licMainId = licMainId;
    }

    public String getCalcType() {
        return calcType;
    }

    public void setCalcType(String calcType) {
        this.calcType = calcType;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public Date getDrawTime() {
        return drawTime;
    }

    public void setDrawTime(Date drawTime) {
        this.drawTime = drawTime;
    }

    public Long getConfirmId() {
        return confirmId;
    }

    public void setConfirmId(Long confirmId) {
        this.confirmId = confirmId;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public BigDecimal getPremium() {
        return premium;
    }

    public void setPremium(BigDecimal premium) {
        this.premium = premium;
    }

    public BigDecimal getClaims() {
        return claims;
    }

    public void setClaims(BigDecimal claims) {
        this.claims = claims;
    }

    public BigDecimal getExpenses() {
        return expenses;
    }

    public void setExpenses(BigDecimal expenses) {
        this.expenses = expenses;
    }

    public BigDecimal getRa() {
        return ra;
    }

    public void setRa(BigDecimal ra) {
        this.ra = ra;
    }

    public BigDecimal getPvFcf() {
        return pvFcf;
    }

    public void setPvFcf(BigDecimal pvFcf) {
        this.pvFcf = pvFcf;
    }

    public BigDecimal getPvRa() {
        return pvRa;
    }

    public void setPvRa(BigDecimal pvRa) {
        this.pvRa = pvRa;
    }

    public String getIsConfirm() {
        return isConfirm;
    }

    public void setIsConfirm(String isConfirm) {
        this.isConfirm = isConfirm;
    }

    public BigDecimal getPvAp() {
        return pvAp;
    }

    public void setPvAp(BigDecimal pvAp) {
        this.pvAp = pvAp;
    }

    public Long getLrcMainId() {
        return lrcMainId;
    }

    public void setLrcMainId(Long lrcMainId) {
        this.lrcMainId = lrcMainId;
    }

    public BigDecimal getIncomeAmount() {
        return incomeAmount;
    }

    public void setIncomeAmount(BigDecimal incomeAmount) {
        this.incomeAmount = incomeAmount;
    }

    public BigDecimal getRealityAmount() {
        return realityAmount;
    }

    public void setRealityAmount(BigDecimal realityAmount) {
        this.realityAmount = realityAmount;
    }

    public BigDecimal getLrcAmount() {
        return lrcAmount;
    }

    public void setLrcAmount(BigDecimal lrcAmount) {
        this.lrcAmount = lrcAmount;
    }

    public String getCmUnitNo() {
        return cmUnitNo;
    }

    public void setCmUnitNo(String cmUnitNo) {
        this.cmUnitNo = cmUnitNo;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getEndorseNo() {
        return endorseNo;
    }

    public void setEndorseNo(String endorseNo) {
        this.endorseNo = endorseNo;
    }
}
