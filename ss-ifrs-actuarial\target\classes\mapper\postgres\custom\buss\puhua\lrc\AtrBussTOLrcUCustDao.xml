<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-03-08 10:07:11 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.puhua.lrc.AtrBussTOLrcUDao">
  <!-- 本配置文件由GIS MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**IDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->

    <select id="findTotLrcDetailPage" fetchSize="1000" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap" parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
        select
        a.ACTION_NO as "actionNo",
        a.YEAR_MONTH as "yearMonth",
        a.TREATY_NO as "treatyNo",
        a.treaty_name as "treatyName",
        a.icg_name as "icgNoName",
        a.risk_class_code as "riskClassCode",
        a.ri_policy_no as "riPolicyNo",
        a.ri_endorse_seq_no as "riEndorseSeqNo",
        a.kind_code as "kindCode",
        a.portfolio_no as "portfolioNo",
        a.icg_no as "icgNo",
        a.CMUNIT_NO as "cmunitNo",
        a.pl_judge_rslt as "plJudgeRslt",
        a.company_code1 as "companyCode1",
        a.company_code2 as "companyCode2",
        a.company_code3 as "companyCode3",
        a.company_code4 as "companyCode4",
        a.fee_rate as "feeRate",
        to_char( (date_trunc('month', to_date(a.YEAR_MONTH, 'YYYYMM')) + interval '1 month' - interval '1 day')::date , 'YYYY/MM/DD') as "evaluateDate",
        to_char(a.CONTRACT_DATE,'yyyy/mm/dd') as "contractDate",
        to_char(a.effective_date,'yyyy/mm/dd') as "effectiveDate",
        to_char(a.expiry_date,'yyyy/mm/dd') as "expiryDate",
        to_char(a.ri_effective_date,'yyyy/mm/dd') as "riEffectiveDate",
        to_char(a.ri_expiry_date,'yyyy/mm/dd') as "riExpiryDate",
        to_char(a.approval_date,'yyyy/mm/dd') as "approvalDate",
        to_char(a.issue_date,'yyyy/mm/dd') as "issueDate",
        to_char(a.policy_issue_date,'yyyy/mm/dd') as "policyIssueDate",
        to_char(a.policy_confirm_date,'yyyy/mm/dd') as "policyConfirmDate",
        to_char(a.policy_approval_date,'yyyy/mm/dd') as "policyApprovalDate",
        to_char(a.policy_contract_date,'yyyy/mm/dd') as "policyContractDate",
        c.entity_code as "entityCode",
        c.entity_c_name as "entityCName",
        'CNY' as "currencyCode",
        a.premium as "riPremium",
        a.net_fee as "riNetFee",
        a.cur_premium as "curPremium",
        a.cur_net_fee as "curNetFee",
        a.cur_ed_net_fee as "curEdNetFee",
        a.pre_cuml_ed_premium as "preCumlEdPremium",
        a.pre_cuml_ed_net_fee as "preCumlEdNetFee",
        a.pre_cuml_paid_premium as "preCumlPaidPremium",
        a.pre_cuml_paid_net_fee as "preCumlPaidNetFee",
        a.policy_premium as "policyPremium",
        a.policy_netfee as "policyNetfee",
        a.policy_no as "policyNo",
        a.endorse_seq_no as "endorseSeqNo",
        'T' as "contractType"
        <if test="null != devNoList and devNoList.size > 0">
            <foreach collection="devNoList" item="item" index="index" open=","  separator=",">
                dev_data."${item}" AS "${item}"
            </foreach>
        </if>
        FROM atruser.atr_buss_to_lrc_t_ul a
        LEFT JOIN (
            SELECT
                dev.main_id
                <if test="null != devNoList and devNoList.size > 0">
                    <foreach collection="devNoList" item="item" index="index" open=","  separator=",">
                         MAX(CASE dev.DEV_NO WHEN ${item} THEN dev.${feeType} ELSE NULL END) AS "${item}"
                    </foreach>
                </if>
            FROM atruser.atr_buss_to_lrc_t_ul_dev dev
            INNER JOIN atruser.atr_buss_to_lrc_t_ul m ON dev.main_id = m.id
            WHERE m.action_no = #{actionNo,jdbcType=VARCHAR}
                <if test="portfolioNo != null and portfolioNo != ''">
                    AND m.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
                </if>
                <if test="icgNo != null and icgNo != ''">
                    AND m.icg_no = #{icgNo,jdbcType=VARCHAR}
                </if>
            GROUP BY dev.main_id
        ) dev_data ON a.id = dev_data.main_id
        left join bpluser.bbs_conf_entity c on a.entity_id = c.entity_id
        where a.action_no = #{actionNo,jdbcType=VARCHAR}
        <if test="portfolioNo != null and portfolioNo != ''">
            and a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
        </if>
        <if test="icgNo != null and icgNo != ''">
            and a.icg_no = #{icgNo,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="findToxLrcDetailPage" fetchSize="1000" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap" parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
        select a.action_no as "actionNo",
        a.entity_id as "entityId",
        a.year_month as "yearMonth",
        a.treaty_no as "treatyNo",
        a.icg_no as "icgNo",
        a.portfolio_no as "portfolioNo",
        a.kind_code as "kindCode",
        a.pl_judge_rslt as "plJudgeRslt",
        a.policy_no as "policyNo",
        a.endorse_seq_no as "endorseSeqNo",
        a.treaty_name as "treatyName",
        a.icg_name as "icgNoName",
        a.cuml_cur_ed_mdp as "preEdPremium",
        to_char(a.contract_date,'yyyy/mm/dd') as "contractDate",
        a.cmunit_no as "cmunitNo",
        to_char(a.effective_date,'yyyy/mm/dd') as "effectiveDate",
        c.entity_code as "entityCode",
        c.entity_c_name as "entityCName",
        to_char(a.expiry_date,'yyyy/mm/dd') as "expiryDate",
        to_char( (date_trunc('month', to_date(a.YEAR_MONTH, 'YYYYMM')) + interval '1 month' - interval '1 day')::date ,
        'YYYY/MM/DD') as "evaluateDate"
        <if test="null != devNoList and devNoList.size > 0">
            <foreach collection="devNoList" item="item" index="index" open="," separator=",">
                dev_data."${item}" AS "${item}"
            </foreach>
            , dev_data.sum_premium AS "premium"
            , dev_data.sum_netFee AS "netFee"
        </if>
        from atruser.atr_buss_to_lrc_x_ul a
        LEFT JOIN (
            SELECT
                dev.main_id
                <if test="null != devNoList and devNoList.size > 0">
                    <foreach collection="devNoList" item="item" index="index" open=","  separator=",">
                         MAX(CASE dev.DEV_NO WHEN ${item} THEN dev.${feeType} ELSE NULL END) AS "${item}"
                    </foreach>
                    , SUM(CASE WHEN dev.DEV_NO IN (<foreach collection="devNoList" item="item" separator=",">
                    ${item}</foreach>) THEN dev.${feeType} ELSE 0 END) AS sum_premium
                    , SUM(CASE WHEN dev.DEV_NO IN (<foreach collection="devNoList" item="item" separator=",">
                    ${item}</foreach>) THEN dev.${feeType} ELSE 0 END) AS sum_netFee
                </if>
            FROM atruser.atr_buss_to_lrc_x_ul_dev dev
            INNER JOIN atruser.atr_buss_to_lrc_x_ul m ON dev.main_id = m.id
            WHERE m.action_no = #{actionNo,jdbcType=VARCHAR}
                <if test="portfolioNo != null and portfolioNo != ''">
                    AND m.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
                </if>
                <if test="icgNo != null and icgNo != ''">
                    AND m.icg_no = #{icgNo,jdbcType=VARCHAR}
                </if>
            GROUP BY dev.main_id
        ) dev_data ON a.id = dev_data.main_id
        left join bpluser.bbs_conf_entity c on a.entity_id = c.entity_id
        where a.action_no = #{actionNo,jdbcType=VARCHAR}
        <if test="portfolioNo != null and portfolioNo != ''">
            and a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
        </if>
        <if test="icgNo != null and icgNo != ''">
            and a.icg_no = #{icgNo,jdbcType=VARCHAR}
        </if>
        ORDER BY a.id

    </select>

    <select id="findTotLrcDetailPageByR" fetchSize="1000" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap" parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
        select
            a.action_no as "actionNo",
            a.treaty_no as "treatyNo",
            a.policy_no as "policyNo",
            a.endorse_seq_no as "endorseSeqNo",
            a.kind_code as "kindCode",
            a.sectiono_code as "sectionoCode",
            a.reinsurer_code as "reinsurerCode",
            a.effective_date as "effectiveDate",
            a.expiry_date as "expiryDate",
            a.ri_effective_date as "riEffectiveDate",
            a.ri_expiry_date as "riExpiryDate",
            a.prepaid_fee_rate as "prepaidFeeRate",
            a.floating_handling_fee_cap as "floatingHandlingFeeCap",
            a.inv_amount as "invAmount",
            b.dev_no as "devNo",
            b.paid_ed_premium as "paidEdPremium",
            b.ed_premium as "edPremium",
            b.paid_net_fee as "paidNetFee",
            b.ed_net_fee as "edNetFee"
        from atr_buss_lrc_action q join atr_buss_to_lrc_t_ul_r a on q.action_no = a.action_no
        left join atr_buss_to_lrc_t_ul_r_dev b on a.id = b.main_id
        where q.action_no = #{actionNo,jdbcType=VARCHAR}
        <if test="portfolioNo != null and portfolioNo != ''">
            and a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
        </if>
        <if test="icgNo != null and icgNo != ''">
            and a.icg_no = #{icgNo,jdbcType=VARCHAR}
        </if>
        order by action_no, treaty_no, policy_no, endorse_seq_no,sectiono_code,reinsurer_code, kind_code, dev_no
    </select>

    <select id="findLrcHandelDetailByRPage" fetchSize="1000" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap" parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
        select
            a.action_no as "actionNo",
            a.treaty_no as "treatyNo",
            a.policy_no as "policyNo",
            a.endorse_seq_no as "endorseSeqNo",
            a.kind_code as "kindCode",
            a.sectiono_code as "sectionoCode",
            a.reinsurer_code as "reinsurerCode",
            a.effective_date as "effectiveDate",
            a.expiry_date as "expiryDate",
            a.ri_effective_date as "riEffectiveDate",
            a.ri_expiry_date as "riExpiryDate",
            a.prepaid_fee_rate as "prepaidFeeRate",
            a.floating_handling_fee_cap as "floatingHandlingFeeCap",
            a.inv_amount as "invAmount",
            b.dev_no as "devNo",
            b.paid_ed_premium as "paidEdPremium",
            b.ed_premium as "edPremium",
            b.paid_net_fee as "paidNetFee",
            b.ed_net_fee as "edNetFee"
        from atr_buss_lrc_action q join atr_buss_to_lrc_t_ul_r a on q.action_no = a.action_no
        left join atr_buss_to_lrc_t_ul_r_dev b on a.id = b.main_id
        where q.confirm_is = '1'
        <if test="entityId != null">
            and ablc.entity_id = #{entityId,jdbcType=BIGINT}
        </if>
        <if test="businessSourceCode != null and businessSourceCode != ''">
            and a.BUSINESS_SOURCE_CODE = #{businessSourceCode,jdbcType=VARCHAR}
        </if>
        <if test="yearMonthStart != null ">
            and a.YEAR_MONTH &gt;= #{yearMonthStart,jdbcType=VARCHAR}
        </if>
        <if test="portfolioNo != null and portfolioNo != ''">
            and a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
        </if>
        <if test="icgNo != null and icgNo != ''">
            and a.icg_no = #{icgNo,jdbcType=VARCHAR}
        </if>
        <if test="riskClassCode != null and riskClassCode != ''">
            and a.risk_class_code = #{riskClassCode,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="countLrcUDetail" resultType="Long">
        select count(1)
        FROM atr_buss_lrc_action ablc
        left join  atruser.atr_buss_to_lrc_t_ul a on ablc.action_no= a.action_no
        where ablc.confirm_is = '1'
        <if test="entityId != null">
            and ablc.entity_id = #{entityId,jdbcType=BIGINT}
        </if>
        <if test="businessSourceCode != null and businessSourceCode != ''">
            and ablc.BUSINESS_SOURCE_CODE = #{businessSourceCode,jdbcType=VARCHAR}
        </if>
        <if test="entityId != null">
            and ablc.entity_id = #{entityId,jdbcType=BIGINT}
        </if>
        <if test="yearMonthStart != null ">
            and ablc.YEAR_MONTH &gt;= #{yearMonthStart,jdbcType=VARCHAR}
        </if>
        <if test="yearMonthEnd != null ">
            and ablc.YEAR_MONTH &lt;= #{yearMonthEnd,jdbcType=VARCHAR}
        </if>
        <if test="portfolioNo != null and portfolioNo != ''">
            and a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
        </if>
        <if test="icgNo != null and icgNo != ''">
            and a.icg_no = #{icgNo,jdbcType=VARCHAR}
        </if>
        <if test="riskClassCode != null and riskClassCode != ''">
            and a.risk_class_code = #{riskClassCode,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="findLrcHandelDetailPage" fetchSize="1000" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
        select
        a.ACTION_NO as "actionNo",
        a.YEAR_MONTH as "yearMonth",
        a.TREATY_NO as "treatyNo",
        a.treaty_name as "treatyName",
        a.icg_name as "icgNoName",
        a.risk_class_code as "riskClassCode",
        a.ri_policy_no as "riPolicyNo",
        a.ri_endorse_seq_no as "riEndorseSeqNo",
        a.kind_code as "kindCode",
        a.portfolio_no as "portfolioNo",
        a.icg_no as "icgNo",
        a.CMUNIT_NO as "cmunitNo",
        a.pl_judge_rslt as "plJudgeRslt",
        a.company_code1 as "companyCode1",
        a.company_code2 as "companyCode2",
        a.company_code3 as "companyCode3",
        a.company_code4 as "companyCode4",
        a.fee_rate as "feeRate",
        to_char( (date_trunc('month', to_date(a.YEAR_MONTH, 'YYYYMM')) + interval '1 month' - interval '1 day')::date , 'YYYY/MM/DD') as "evaluateDate",
        to_char(a.CONTRACT_DATE,'yyyy/mm/dd') as "contractDate",
        to_char(a.effective_date,'yyyy/mm/dd') as "effectiveDate",
        to_char(a.expiry_date,'yyyy/mm/dd') as "expiryDate",
        to_char(a.ri_effective_date,'yyyy/mm/dd') as "riEffectiveDate",
        to_char(a.ri_expiry_date,'yyyy/mm/dd') as "riExpiryDate",
        to_char(a.approval_date,'yyyy/mm/dd') as "approvalDate",
        to_char(a.issue_date,'yyyy/mm/dd') as "issueDate",
        to_char(a.policy_issue_date,'yyyy/mm/dd') as "policyIssueDate",
        to_char(a.policy_confirm_date,'yyyy/mm/dd') as "policyConfirmDate",
        to_char(a.policy_approval_date,'yyyy/mm/dd') as "policyApprovalDate",
        to_char(a.policy_contract_date,'yyyy/mm/dd') as "policyContractDate",
        c.entity_code as "entityCode",
        c.entity_c_name as "entityCName",
        'CNY' as "currencyCode",
        a.premium as "riPremium",
        a.net_fee as "riNetFee",
        a.cur_premium as "curPremium",
        a.cur_net_fee as "curNetFee",
        a.cur_ed_net_fee as "curEdNetFee",
        a.pre_cuml_ed_premium as "preCumlEdPremium",
        a.pre_cuml_ed_net_fee as "preCumlEdNetFee",
        a.pre_cuml_paid_premium as "preCumlPaidPremium",
        a.pre_cuml_paid_net_fee as "preCumlPaidNetFee",
        a.policy_premium as "policyPremium",
        a.policy_netfee as "policyNetfee",
        a.policy_no as "policyNo",
        a.endorse_seq_no as "endorseSeqNo",
        'T' as "contractType"
        <if test="null != devNoList and devNoList.size > 0">
            <foreach collection="devNoList" item="item" index="index" open=","  separator=",">
                dev_data."${item}" AS "${item}"
            </foreach>
        </if>
        FROM atr_buss_lrc_action ablc
        LEFT JOIN atruser.atr_buss_to_lrc_t_ul a ON ablc.action_no = a.action_no
        LEFT JOIN (
            SELECT
                dev.main_id
                <if test="null != devNoList and devNoList.size > 0">
                    <foreach collection="devNoList" item="item" index="index" open=","  separator=",">
                         MAX(CASE dev.DEV_NO WHEN ${item} THEN dev.${feeType} ELSE NULL END) AS "${item}"
                    </foreach>
                </if>
            FROM atruser.atr_buss_to_lrc_t_ul_dev dev
            INNER JOIN atruser.atr_buss_to_lrc_t_ul m ON dev.main_id = m.id
            INNER JOIN atr_buss_lrc_action ablc_sub ON m.action_no = ablc_sub.action_no
            WHERE ablc_sub.confirm_is = '1'
                <if test="entityId != null">
                    AND ablc_sub.entity_id = #{entityId,jdbcType=BIGINT}
                </if>
                <if test="businessSourceCode != null and businessSourceCode != ''">
                    AND ablc_sub.BUSINESS_SOURCE_CODE = #{businessSourceCode,jdbcType=VARCHAR}
                </if>
                <if test="yearMonthStart != null ">
                    AND ablc_sub.YEAR_MONTH &gt;= #{yearMonthStart,jdbcType=VARCHAR}
                </if>
                <if test="yearMonthEnd != null ">
                    AND ablc_sub.YEAR_MONTH &lt;= #{yearMonthEnd,jdbcType=VARCHAR}
                </if>
                <if test="portfolioNo != null and portfolioNo != ''">
                    AND m.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
                </if>
                <if test="icgNo != null and icgNo != ''">
                    AND m.icg_no = #{icgNo,jdbcType=VARCHAR}
                </if>
                <if test="riskClassCode != null and riskClassCode != ''">
                    AND m.risk_class_code = #{riskClassCode,jdbcType=VARCHAR}
                </if>
            GROUP BY dev.main_id
        ) dev_data ON a.id = dev_data.main_id
        left join bpluser.bbs_conf_entity c on a.entity_id = c.entity_id
        where ablc.confirm_is = '1'
        <if test="entityId != null">
            and ablc.entity_id = #{entityId,jdbcType=BIGINT}
        </if>
        <if test="businessSourceCode != null and businessSourceCode != ''">
            and ablc.BUSINESS_SOURCE_CODE = #{businessSourceCode,jdbcType=VARCHAR}
        </if>
        <if test="yearMonthStart != null ">
            and ablc.YEAR_MONTH &gt;= #{yearMonthStart,jdbcType=VARCHAR}
        </if>
        <if test="yearMonthEnd != null ">
            and ablc.YEAR_MONTH &lt;= #{yearMonthEnd,jdbcType=VARCHAR}
        </if>
        <if test="portfolioNo != null and portfolioNo != ''">
            and a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
        </if>
        <if test="icgNo != null and icgNo != ''">
            and a.icg_no = #{icgNo,jdbcType=VARCHAR}
        </if>
        <if test="riskClassCode != null and riskClassCode != ''">
            and a.risk_class_code = #{riskClassCode,jdbcType=VARCHAR}
        </if>

        <if test="feeType != null and (feeType == 'ed_premium' or feeType == 'recv_premium')">
        UNION ALL

        select
        a.ACTION_NO as "actionNo",
        a.YEAR_MONTH as "yearMonth",
        a.TREATY_NO as "treatyNo",
        a.treaty_name as "treatyName",
        a.icg_name as "icgNoName",
        NULL as "riskClassCode",
        NULL as "riPolicyNo",
        NULL as "riEndorseSeqNo",
        a.kind_code as "kindCode",
        a.portfolio_no as "portfolioNo",
        a.icg_no as "icgNo",
        a.CMUNIT_NO as "cmunitNo",
        a.pl_judge_rslt as "plJudgeRslt",
        a.company_code1 as "companyCode1",
        a.company_code2 as "companyCode2",
        a.company_code3 as "companyCode3",
        a.company_code4 as "companyCode4",
        NULL as "feeRate",
        to_char( (date_trunc('month', to_date(a.YEAR_MONTH, 'YYYYMM')) + interval '1 month' - interval '1 day')::date , 'YYYY/MM/DD') as "evaluateDate",
        to_char(a.CONTRACT_DATE,'yyyy/mm/dd') as "contractDate",
        to_char(a.effective_date,'yyyy/mm/dd') as "effectiveDate",
        to_char(a.expiry_date,'yyyy/mm/dd') as "expiryDate",
        NULL as "riEffectiveDate",
        NULL as "riExpiryDate",
        NULL as "approvalDate",
        NULL as "issueDate",
        NULL as "policyIssueDate",
        NULL as "policyConfirmDate",
        NULL as "policyApprovalDate",
        NULL as "policyContractDate",
        c.entity_code as "entityCode",
        c.entity_c_name as "entityCName",
        'CNY' as "currencyCode",
        a.premium as "riPremium",
        NULL as "riNetFee",
        NULL as "curPremium",
        NULL as "curNetFee",
        NULL as "curEdNetFee",
        a.earned_premium as "preCumlEdPremium",
        NULL as "preCumlEdNetFee",
        NULL as "preCumlPaidPremium",
        NULL as "preCumlPaidNetFee",
        NULL as "policyPremium",
        NULL as "policyNetfee",
        a.policy_no as "policyNo",
        a.endorse_seq_no as "endorseSeqNo",
        'X' as "contractType"
        <if test="null != devNoList and devNoList.size > 0">
            <foreach collection="devNoList" item="item" index="index" open=","  separator=",">
                dev_data."${item}" AS "${item}"
            </foreach>
        </if>
        FROM atr_buss_lrc_action ablc
        LEFT JOIN atruser.atr_buss_to_lrc_x_ul a ON ablc.action_no = a.action_no
        LEFT JOIN (
            SELECT
                dev.main_id
                <if test="null != devNoList and devNoList.size > 0">
                    <foreach collection="devNoList" item="item" index="index" open=","  separator=",">
                         MAX(CASE dev.DEV_NO WHEN ${item} THEN dev.${feeType} ELSE NULL END) AS "${item}"
                    </foreach>
                </if>
            FROM atruser.atr_buss_to_lrc_x_ul_dev dev
            INNER JOIN atruser.atr_buss_to_lrc_x_ul m ON dev.main_id = m.id
            INNER JOIN atr_buss_lrc_action ablc_sub ON m.action_no = ablc_sub.action_no
            WHERE ablc_sub.confirm_is = '1'
                AND '${feeType}' IN ('ed_premium', 'recv_premium')
                <if test="entityId != null">
                    AND ablc_sub.entity_id = #{entityId,jdbcType=BIGINT}
                </if>
                <if test="businessSourceCode != null and businessSourceCode != ''">
                    AND ablc_sub.BUSINESS_SOURCE_CODE = #{businessSourceCode,jdbcType=VARCHAR}
                </if>
                <if test="yearMonthStart != null ">
                    AND ablc_sub.YEAR_MONTH &gt;= #{yearMonthStart,jdbcType=VARCHAR}
                </if>
                <if test="yearMonthEnd != null ">
                    AND ablc_sub.YEAR_MONTH &lt;= #{yearMonthEnd,jdbcType=VARCHAR}
                </if>
                <if test="portfolioNo != null and portfolioNo != ''">
                    AND m.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
                </if>
                <if test="icgNo != null and icgNo != ''">
                    AND m.icg_no = #{icgNo,jdbcType=VARCHAR}
                </if>
            GROUP BY dev.main_id
        ) dev_data ON a.id = dev_data.main_id
        left join bpluser.bbs_conf_entity c on a.entity_id = c.entity_id
        where ablc.confirm_is = '1'
        <if test="entityId != null">
            and ablc.entity_id = #{entityId,jdbcType=BIGINT}
        </if>
        <if test="businessSourceCode != null and businessSourceCode != ''">
            and ablc.BUSINESS_SOURCE_CODE = #{businessSourceCode,jdbcType=VARCHAR}
        </if>
        <if test="yearMonthStart != null ">
            and ablc.YEAR_MONTH &gt;= #{yearMonthStart,jdbcType=VARCHAR}
        </if>
        <if test="yearMonthEnd != null ">
            and ablc.YEAR_MONTH &lt;= #{yearMonthEnd,jdbcType=VARCHAR}
        </if>
        <if test="portfolioNo != null and portfolioNo != ''">
            and a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
        </if>
        <if test="icgNo != null and icgNo != ''">
            and a.icg_no = #{icgNo,jdbcType=VARCHAR}
        </if>
        </if>

        ORDER BY "actionNo", "contractType", "treatyNo"
    </select>
</mapper>
