/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-03-09 10:18:30
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName: AtrBussLicQueryResVo
 * @Description: Lic计量查询数据返回对象
 * @Author: yinxh.
 * @CreateDate: 2021/3/10 18:32
 * @Version: 1.0
 */
public class AtrBussLicQueryResVo implements Serializable {
	
    /**
     * 主表ID
     */
    private Long licMainId;
    
    /**
     * 事故年月
     */
    private String accidentYearMonth;
    
    /**
     * 发展年月汇总金额list
     */
    private List<AtrBussLicQueryDevYearMonthVo> devYearMonthList;
    
    /**
     * 合同组数据list
      */
    private List<AtrBussLicQueryContractGroupVo> devIcgList;
    
    
    private String stageCName;
    private String stageLName;
    private String stageEName;           
    
    /**
         * 辅助计算列
     */
    private BigDecimal lossRate;
    private BigDecimal premium;
    private BigDecimal accidentAll; 
    private BigDecimal ibrm;
    
    private static final long serialVersionUID = 1L;

	public Long getLicMainId() {
		return licMainId;
	}

	public void setLicMainId(Long licMainId) {
		this.licMainId = licMainId;
	}

	public String getAccidentYearMonth() {
		return accidentYearMonth;
	}

	public void setAccidentYearMonth(String accidentYearMonth) {
		this.accidentYearMonth = accidentYearMonth;
	}

	public List<AtrBussLicQueryDevYearMonthVo> getDevYearMonthList() {
		return devYearMonthList;
	}

	public void setDevYearMonthList(List<AtrBussLicQueryDevYearMonthVo> devYearMonthList) {
		this.devYearMonthList = devYearMonthList;
	}

	public List<AtrBussLicQueryContractGroupVo> getDevIcgList() {
		return devIcgList;
	}

	public void setDevIcgList(List<AtrBussLicQueryContractGroupVo> devIcgList) {
		this.devIcgList = devIcgList;
	}

	public String getStageCName() {
		return stageCName;
	}

	public void setStageCName(String stageCName) {
		this.stageCName = stageCName;
	}

	public String getStageLName() {
		return stageLName;
	}

	public void setStageLName(String stageLName) {
		this.stageLName = stageLName;
	}

	public String getStageEName() {
		return stageEName;
	}

	public void setStageEName(String stageEName) {
		this.stageEName = stageEName;
	}

	public BigDecimal getLossRate() {
		return lossRate;
	}

	public void setLossRate(BigDecimal lossRate) {
		this.lossRate = lossRate;
	}

	public BigDecimal getPremium() {
		return premium;
	}

	public void setPremium(BigDecimal premium) {
		this.premium = premium;
	}

	public BigDecimal getAccidentAll() {
		return accidentAll;
	}

	public void setAccidentAll(BigDecimal accidentAll) {
		this.accidentAll = accidentAll;
	}

	public BigDecimal getIbrm() {
		return ibrm;
	}

	public void setIbrm(BigDecimal ibrm) {
		this.ibrm = ibrm;
	}
   
}