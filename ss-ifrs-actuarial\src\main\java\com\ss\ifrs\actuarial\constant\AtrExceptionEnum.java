package com.ss.ifrs.actuarial.constant;

import com.ss.platform.core.exception.BusinessExceptionAssert;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum AtrExceptionEnum implements BusinessExceptionAssert {

    IBNR_NOT_BUSINESS("ibnr_000",  "ibnr not exists BusinessSourceCode"),
    IBNR_NO_BALANCE("ibnr_001", "Imbalance before and after IBNR allocation"),
    ;


    /**
     * 返回码
     */
    private String code;
    /**
     * 返回消息
     */
    private String message;


    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }
}
