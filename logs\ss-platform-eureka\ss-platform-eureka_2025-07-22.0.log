[2m2025-07-22 10:14:38.662[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m The following profiles are active: dev
[2m2025-07-22 10:14:39.213[0;39m [dev] [33m WARN[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-22 10:14:39.314[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=ff1a2d2c-d19d-3931-a591-81b4348e9e38
[2m2025-07-22 10:14:39.596[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 7602 (http)
[2m2025-07-22 10:14:39.658[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 972 ms
[2m2025-07-22 10:14:39.713[0;39m [dev] [33m WARN[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-22 10:14:39.713[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-22 10:14:39.721[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.netflix.config.DynamicPropertyFactory [0;39m [2m:[0;39m DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@4c1bdcc2
[2m2025-07-22 10:14:40.403[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-22 10:14:40.404[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-22 10:14:40.557[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-22 10:14:40.558[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-22 10:14:41.125[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.web.DefaultSecurityFilterChain    [0;39m [2m:[0;39m Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1fc0d9b4, org.springframework.security.web.context.SecurityContextPersistenceFilter@474e34e4, org.springframework.security.web.header.HeaderWriterFilter@7404ddca, org.springframework.security.web.authentication.logout.LogoutFilter@51ed2f68, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@44da7eb3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5fb7ab9c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3dea226b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@42107318, org.springframework.security.web.session.SessionManagementFilter@3af36922, org.springframework.security.web.access.ExceptionTranslationFilter@4ebd6fd6, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@653a5967]
[2m2025-07-22 10:14:41.135[0;39m [dev] [33m WARN[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-22 10:14:41.135[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-22 10:14:41.342[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService 'applicationTaskExecutor'
[2m2025-07-22 10:14:42.161[0;39m [dev] [33m WARN[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mockingLoadBalancerClientRibbonWarnLogger[0;39m [2m:[0;39m You already have RibbonLoadBalancerClient on your classpath. It will be used by default. As Spring Cloud Ribbon is in maintenance mode. We recommend switching to BlockingLoadBalancerClient instead. In order to use it, set the value of `spring.cloud.loadbalancer.ribbon.enabled` to `false` or remove spring-cloud-starter-netflix-ribbon from your project.
[2m2025-07-22 10:14:42.250[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-22 10:14:42.276[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Initializing Eureka in region us-east-1
[2m2025-07-22 10:14:42.276[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Client configured to neither register nor query for data.
[2m2025-07-22 10:14:42.284[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Discovery Client initialized at timestamp 1753150482282 with initial instances count: 0
[2m2025-07-22 10:14:42.323[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initializing ...
[2m2025-07-22 10:14:42.326[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Adding new peer nodes [**********************************************/eureka/]
[2m2025-07-22 10:14:42.673[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-22 10:14:42.674[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-22 10:14:42.674[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-22 10:14:42.674[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-22 10:14:42.740[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Replica node URL:  **********************************************/eureka/
[2m2025-07-22 10:14:42.747[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Finished initializing remote region registries. All known remote regions: []
[2m2025-07-22 10:14:42.748[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initialized
[2m2025-07-22 10:14:42.757[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 2 endpoint(s) beneath base path '/actuator'
[2m2025-07-22 10:14:42.818[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application SS-PLATFORM-EUREKA with eureka with status UP
[2m2025-07-22 10:14:42.819[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[      Thread-23][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Setting the eureka configuration..
[2m2025-07-22 10:14:42.819[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[      Thread-23][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka data center value eureka.datacenter is not set, defaulting to default
[2m2025-07-22 10:14:42.821[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[      Thread-23][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka environment value eureka.environment is not set, defaulting to test
[2m2025-07-22 10:14:42.832[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[      Thread-23][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m isAws returned false
[2m2025-07-22 10:14:42.832[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[      Thread-23][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Initialized server context
[2m2025-07-22 10:14:42.832[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[      Thread-23][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Got 1 instances from neighboring DS node
[2m2025-07-22 10:14:42.832[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[      Thread-23][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Renew threshold is: 1
[2m2025-07-22 10:14:42.832[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[      Thread-23][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Changing status to UP
[2m2025-07-22 10:14:42.838[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[      Thread-23][0;39m [36me.s.EurekaServerInitializerConfiguration[0;39m [2m:[0;39m Started Eureka Server
[2m2025-07-22 10:14:42.853[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 7602 (http) with context path ''
[2m2025-07-22 10:14:42.854[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 7602
[2m2025-07-22 10:14:43.204[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m Started EurekaServerApplication in 5.666 seconds (JVM running for 7.799)
[2m2025-07-22 10:14:43.719[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[2)-************][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-22 10:14:43.728[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[2)-************][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 8 ms
[2m2025-07-22 10:15:13.070[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=false)
[2m2025-07-22 10:15:13.973[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=true)
[2m2025-07-22 10:15:22.741[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=false)
[2m2025-07-22 10:15:23.273[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-6][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=true)
[2m2025-07-22 10:15:23.597[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=false)
[2m2025-07-22 10:15:24.119[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-9][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=true)
[2m2025-07-22 10:15:29.224[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=false)
[2m2025-07-22 10:15:29.745[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=true)
[2m2025-07-22 10:15:42.835[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-22 10:16:42.871[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 35ms
[2m2025-07-22 10:16:49.177[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-22 10:16:49.892[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-22 10:17:42.873[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-22 10:18:42.880[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 10:19:42.883[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 10:20:42.892[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 10:21:42.906[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 10:21:42.906[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Evicting 1 items (expired=1, evictionLimit=1)
[2m2025-07-22 10:21:42.906[0;39m [dev] [33m WARN[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m DS: Registry: expired lease for SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608
[2m2025-07-22 10:21:42.907[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Cancelled instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 (replication=false)
[2m2025-07-22 10:22:42.913[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 10:23:42.918[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 10:24:42.924[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 10:25:42.937[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 10:26:42.937[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-22 10:27:42.947[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 10:28:42.951[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-22 10:29:42.753[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 10:29:42.957[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 10:30:42.960[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 10:30:48.464[0;39m [dev] [33m WARN[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-7][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m DS: Registry: lease doesn't exist, registering resource: SS-IFRS-ACTUARIAL - DESKTOP-658MVB3:ss-ifrs-actuarial:7608
[2m2025-07-22 10:30:48.464[0;39m [dev] [33m WARN[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-7][0;39m [36mc.n.eureka.resources.InstanceResource   [0;39m [2m:[0;39m Not Found (Renew): SS-IFRS-ACTUARIAL - DESKTOP-658MVB3:ss-ifrs-actuarial:7608
[2m2025-07-22 10:30:48.566[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-22 10:30:49.093[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-22 10:31:42.973[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-22 10:32:42.977[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-22 10:33:42.985[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 10:34:42.995[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-22 10:35:42.996[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-22 10:35:42.996[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Evicting 1 items (expired=1, evictionLimit=1)
[2m2025-07-22 10:35:42.996[0;39m [dev] [33m WARN[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m DS: Registry: expired lease for SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608
[2m2025-07-22 10:35:42.996[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Cancelled instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 (replication=false)
[2m2025-07-22 10:36:43.008[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-22 10:37:11.655[0;39m [dev] [33m WARN[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-9][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m DS: Registry: lease doesn't exist, registering resource: SS-IFRS-ACTUARIAL - DESKTOP-658MVB3:ss-ifrs-actuarial:7608
[2m2025-07-22 10:37:11.655[0;39m [dev] [33m WARN[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-9][0;39m [36mc.n.eureka.resources.InstanceResource   [0;39m [2m:[0;39m Not Found (Renew): SS-IFRS-ACTUARIAL - DESKTOP-658MVB3:ss-ifrs-actuarial:7608
[2m2025-07-22 10:37:11.685[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-22 10:37:12.205[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-22 10:37:43.013[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-22 10:38:43.023[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-22 10:39:43.037[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 10:40:43.040[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 10:41:43.054[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 10:42:43.067[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 10:43:43.076[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-22 10:44:42.766[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 10:44:43.078[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-22 10:45:43.088[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 10:46:43.095[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 10:47:43.103[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 10:48:43.104[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-22 10:49:43.117[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 10:50:43.130[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-22 10:51:43.133[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 10:52:43.147[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 10:53:43.156[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 10:54:43.163[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 10:55:43.163[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-22 10:56:43.168[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 10:57:43.182[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 10:58:43.195[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-22 10:59:42.774[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 10:59:43.205[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-22 11:00:43.220[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 11:01:43.223[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-22 11:02:43.233[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-22 11:03:43.247[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 11:04:43.254[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 11:05:43.263[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 11:06:43.278[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 11:07:43.293[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-22 11:08:43.299[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 11:09:43.306[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 11:10:43.307[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-22 11:11:43.321[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 11:12:43.332[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-22 11:13:43.334[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-22 11:14:42.786[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 11:14:43.345[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-22 11:15:43.353[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 11:16:43.356[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-22 11:17:43.361[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 11:18:43.370[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-22 11:19:43.374[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-22 11:20:43.380[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 11:21:43.384[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-22 11:22:43.389[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-22 11:23:43.394[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-22 11:24:43.398[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-22 11:25:43.413[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 11:26:43.417[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 11:27:43.470[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 53ms
[2m2025-07-22 11:28:43.480[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 11:29:42.796[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 11:29:43.480[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-22 11:30:43.483[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-22 11:31:43.495[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-22 11:32:43.509[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 11:33:43.515[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 11:34:43.520[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-22 11:35:43.526[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 11:36:43.535[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-22 11:37:43.548[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-22 11:38:43.553[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 11:39:43.554[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-22 11:40:43.558[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 11:41:43.572[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 11:42:43.576[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-22 11:43:43.587[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-22 11:44:42.807[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 11:44:43.592[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 11:45:43.602[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 11:46:43.614[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-22 11:47:43.616[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-22 11:48:43.622[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 11:49:43.627[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-22 11:50:43.635[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-22 11:51:43.637[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-22 11:52:43.642[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 11:53:43.648[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 11:54:43.660[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-22 11:55:43.670[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 11:56:43.672[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-22 11:57:43.675[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 11:58:43.687[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-22 11:59:42.813[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 11:59:43.698[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-22 12:00:08.815[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status DOWN (replication=false)
[2m2025-07-22 12:00:08.839[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application SS-PLATFORM-EUREKA with eureka with status DOWN
[2m2025-07-22 12:00:09.019[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shutting down ...
[2m2025-07-22 12:00:09.102[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.batchSize.Will start computing stats again.
[2m2025-07-22 12:00:09.190[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.executionTime.Will start computing stats again.
[2m2025-07-22 12:00:09.313[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.executionTime.Will start computing stats again.
[2m2025-07-22 12:00:09.359[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.batchSize.Will start computing stats again.
[2m2025-07-22 12:00:09.388[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status DOWN (replication=false)
[2m2025-07-22 12:00:09.428[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shut down
[2m2025-07-22 12:00:09.539[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Shutting down ExecutorService 'applicationTaskExecutor'
[2m2025-07-22 12:00:09.658[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status DOWN (replication=false)
[2m2025-07-22 12:00:09.681[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Shutting down DiscoveryClient ...
[2m2025-07-22 12:00:09.682[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-7][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status DOWN (replication=false)
[2m2025-07-22 12:00:09.684[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Completed shut down of DiscoveryClient
[2m2025-07-22 12:00:10.047[0;39m [dev] [32m INFO[0;39m [35m28084[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status DOWN (replication=false)
[2m2025-07-22 14:47:01.144[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m The following profiles are active: dev
[2m2025-07-22 14:47:01.796[0;39m [dev] [33m WARN[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-22 14:47:01.919[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=ff1a2d2c-d19d-3931-a591-81b4348e9e38
[2m2025-07-22 14:47:02.209[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 7602 (http)
[2m2025-07-22 14:47:02.269[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 1105 ms
[2m2025-07-22 14:47:02.315[0;39m [dev] [33m WARN[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-22 14:47:02.315[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-22 14:47:02.322[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.netflix.config.DynamicPropertyFactory [0;39m [2m:[0;39m DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@56da7487
[2m2025-07-22 14:47:03.957[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.commons.util.InetUtils        [0;39m [2m:[0;39m Cannot determine local hostname
[2m2025-07-22 14:47:04.243[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-22 14:47:04.245[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-22 14:47:04.415[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-22 14:47:04.417[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-22 14:47:05.054[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.web.DefaultSecurityFilterChain    [0;39m [2m:[0;39m Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@67e77f52, org.springframework.security.web.context.SecurityContextPersistenceFilter@77663cd7, org.springframework.security.web.header.HeaderWriterFilter@50a7c72b, org.springframework.security.web.authentication.logout.LogoutFilter@59f45950, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@5599b5bb, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5c70d7f0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@58601e7a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7ead1d80, org.springframework.security.web.session.SessionManagementFilter@63e4484d, org.springframework.security.web.access.ExceptionTranslationFilter@25e8e59, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@748904e8]
[2m2025-07-22 14:47:05.067[0;39m [dev] [33m WARN[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-22 14:47:05.067[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-22 14:47:05.230[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService 'applicationTaskExecutor'
[2m2025-07-22 14:47:07.125[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.commons.util.InetUtils        [0;39m [2m:[0;39m Cannot determine local hostname
[2m2025-07-22 14:47:07.151[0;39m [dev] [33m WARN[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mockingLoadBalancerClientRibbonWarnLogger[0;39m [2m:[0;39m You already have RibbonLoadBalancerClient on your classpath. It will be used by default. As Spring Cloud Ribbon is in maintenance mode. We recommend switching to BlockingLoadBalancerClient instead. In order to use it, set the value of `spring.cloud.loadbalancer.ribbon.enabled` to `false` or remove spring-cloud-starter-netflix-ribbon from your project.
[2m2025-07-22 14:47:07.277[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-22 14:47:07.332[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Initializing Eureka in region us-east-1
[2m2025-07-22 14:47:07.332[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Client configured to neither register nor query for data.
[2m2025-07-22 14:47:07.347[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Discovery Client initialized at timestamp 1753166827346 with initial instances count: 0
[2m2025-07-22 14:47:07.409[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initializing ...
[2m2025-07-22 14:47:07.411[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Adding new peer nodes [**********************************************/eureka/]
[2m2025-07-22 14:47:07.978[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-22 14:47:07.979[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-22 14:47:07.979[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-22 14:47:07.979[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-22 14:47:08.075[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Replica node URL:  **********************************************/eureka/
[2m2025-07-22 14:47:08.087[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Finished initializing remote region registries. All known remote regions: []
[2m2025-07-22 14:47:08.087[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initialized
[2m2025-07-22 14:47:08.100[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 2 endpoint(s) beneath base path '/actuator'
[2m2025-07-22 14:47:08.184[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application SS-PLATFORM-EUREKA with eureka with status UP
[2m2025-07-22 14:47:08.187[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[      Thread-32][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Setting the eureka configuration..
[2m2025-07-22 14:47:08.187[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[      Thread-32][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka data center value eureka.datacenter is not set, defaulting to default
[2m2025-07-22 14:47:08.187[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[      Thread-32][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka environment value eureka.environment is not set, defaulting to test
[2m2025-07-22 14:47:08.207[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[      Thread-32][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m isAws returned false
[2m2025-07-22 14:47:08.208[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[      Thread-32][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Initialized server context
[2m2025-07-22 14:47:08.209[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[      Thread-32][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Got 1 instances from neighboring DS node
[2m2025-07-22 14:47:08.209[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[      Thread-32][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Renew threshold is: 1
[2m2025-07-22 14:47:08.210[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[      Thread-32][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Changing status to UP
[2m2025-07-22 14:47:08.224[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[      Thread-32][0;39m [36me.s.EurekaServerInitializerConfiguration[0;39m [2m:[0;39m Started Eureka Server
[2m2025-07-22 14:47:08.252[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 7602 (http) with context path ''
[2m2025-07-22 14:47:08.254[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 7602
[2m2025-07-22 14:47:09.771[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.commons.util.InetUtils        [0;39m [2m:[0;39m Cannot determine local hostname
[2m2025-07-22 14:47:09.773[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m Started EurekaServerApplication in 11.776 seconds (JVM running for 14.46)
[2m2025-07-22 14:47:10.393[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[3)-************][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-22 14:47:10.400[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[3)-************][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 7 ms
[2m2025-07-22 14:48:08.219[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-22 14:49:08.234[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 14:50:08.247[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-22 14:51:08.260[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 14:52:08.264[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 14:53:08.266[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-22 14:54:08.278[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-22 14:54:17.134[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/localhost:ss-platform-gateway:7601 with status UP (replication=false)
[2m2025-07-22 14:54:17.784[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[nio-7602-exec-6][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/localhost:ss-platform-gateway:7601 with status UP (replication=true)
[2m2025-07-22 14:54:26.405[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[nio-7602-exec-7][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/localhost:ss-platform-authzth:7604 with status UP (replication=false)
[2m2025-07-22 14:54:26.938[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/localhost:ss-platform-authzth:7604 with status UP (replication=true)
[2m2025-07-22 14:54:28.182[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/localhost:ss-platform-common:7612 with status UP (replication=false)
[2m2025-07-22 14:54:28.702[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[io-7602-exec-10][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/localhost:ss-platform-common:7612 with status UP (replication=true)
[2m2025-07-22 14:54:30.924[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/localhost:ss-platform-schedule:7606 with status UP (replication=false)
[2m2025-07-22 14:54:31.454[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/localhost:ss-platform-schedule:7606 with status UP (replication=true)
[2m2025-07-22 14:55:08.292[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 14:55:12.140[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/localhost:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-22 14:55:12.664[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/localhost:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-22 14:56:08.301[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 14:57:08.310[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 14:58:08.313[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-22 14:59:08.322[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 15:00:08.332[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 15:01:08.339[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-22 15:02:08.093[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 15:02:08.351[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-22 15:03:08.356[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 15:04:08.359[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 15:05:08.369[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-22 15:06:08.384[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 15:07:08.393[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 15:08:08.396[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-22 15:09:08.399[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-22 15:10:08.410[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-22 15:11:08.417[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 15:12:08.431[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 15:13:08.439[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-22 15:14:08.448[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 15:15:08.463[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 15:16:08.468[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 15:17:08.103[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 15:17:08.477[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-22 15:18:08.485[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 15:19:08.488[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 15:20:08.501[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-22 15:21:08.505[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-22 15:22:08.512[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 15:23:08.527[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 15:24:08.536[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-22 15:25:08.547[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-22 15:26:08.559[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-22 15:27:08.565[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 15:28:08.576[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-22 15:29:08.588[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-22 15:30:08.597[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 15:31:08.601[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 15:32:08.114[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 15:32:08.610[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-22 15:33:08.622[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-22 15:34:08.637[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 15:35:08.647[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-22 15:36:08.651[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 15:37:08.660[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 15:38:08.661[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-22 15:39:08.664[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-22 15:40:08.671[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 15:41:08.672[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-22 15:42:08.687[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 15:43:08.693[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 15:44:08.700[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 15:45:08.710[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 15:46:08.712[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-22 15:47:08.116[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 15:47:08.724[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-22 15:48:08.729[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 15:49:08.736[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 15:50:08.746[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 15:51:08.760[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 15:52:08.764[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-22 15:53:08.772[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 15:54:08.782[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-22 15:55:08.783[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-22 15:56:08.788[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 15:57:08.794[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 15:58:08.794[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-22 15:59:08.806[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-22 16:00:08.815[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-22 16:01:08.816[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-22 16:02:08.117[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 16:02:08.831[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-22 16:03:08.839[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 16:04:08.845[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 16:05:08.859[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 16:06:08.866[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 16:07:08.875[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 16:08:08.885[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 16:09:08.886[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-22 16:10:08.896[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 16:11:08.900[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-22 16:12:08.912[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-22 16:13:08.921[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-22 16:14:08.928[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 16:15:08.942[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 16:16:08.956[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 16:17:08.127[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 16:17:08.964[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-22 16:18:08.967[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 16:19:08.981[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 16:20:08.985[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 16:21:08.993[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 16:22:09.008[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 16:23:09.010[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-22 16:24:09.022[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-22 16:25:10.037[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-22 16:26:10.046[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-22 16:27:10.056[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 16:28:10.119[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 63ms
[2m2025-07-22 16:29:10.131[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-22 16:30:10.137[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 16:31:10.142[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-22 16:32:09.153[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 16:32:10.145[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 16:33:10.149[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 16:34:10.159[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-22 16:35:10.160[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-22 16:36:10.170[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 16:37:10.172[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-22 16:38:10.178[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 16:39:10.181[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 16:40:10.185[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 16:41:10.192[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 16:42:10.199[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 16:43:10.212[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-22 16:44:10.221[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 16:45:10.230[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-22 16:46:10.241[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-22 16:47:09.156[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 16:47:10.256[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 16:48:10.265[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 16:49:10.280[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 16:50:10.290[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-22 16:51:10.303[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 16:52:10.319[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 16ms
[2m2025-07-22 16:53:10.331[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-22 16:54:10.331[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-22 16:55:10.347[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-22 16:56:10.348[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-22 16:57:10.358[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-22 16:58:10.371[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 16:59:10.373[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-22 17:00:10.388[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 17:01:10.398[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 17:02:09.158[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 17:02:10.408[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 17:03:10.409[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-22 17:04:10.411[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-22 17:05:10.422[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-22 17:06:10.435[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 17:07:10.439[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 17:08:10.446[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 17:09:10.449[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 17:10:10.454[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-22 17:11:10.460[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 17:12:10.461[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-22 17:13:10.472[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-22 17:14:10.482[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-22 17:15:10.483[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-22 17:16:10.491[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 17:17:09.174[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 17:17:10.494[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-22 17:18:10.507[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-22 17:19:10.509[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-22 17:20:10.523[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 17:21:10.534[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-22 17:22:10.548[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 17:23:10.560[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-22 17:24:10.573[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-22 17:25:10.577[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-22 17:26:10.591[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 17:27:10.606[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 17:28:10.612[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 17:29:10.620[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 17:30:10.634[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 17:31:10.645[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-22 17:32:09.184[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 17:32:10.652[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 17:33:10.655[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-22 17:34:10.664[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 17:35:10.670[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 17:36:10.678[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 17:37:10.690[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-22 17:38:10.691[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-22 17:39:10.705[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 17:40:10.714[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 17:41:10.729[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 17:42:10.738[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 17:43:10.742[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-22 17:44:10.745[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-22 17:45:10.756[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-22 17:46:10.766[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 17:47:09.185[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 17:47:10.777[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-22 17:48:10.778[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-22 17:49:10.791[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-22 17:50:10.806[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 17:51:10.821[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 17:52:10.830[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-22 17:53:10.832[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-22 17:54:10.836[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 17:55:10.842[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 17:56:10.848[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 17:57:10.854[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 17:58:10.864[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 17:59:10.871[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 18:00:10.872[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-22 18:01:10.878[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 18:02:09.199[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 18:02:10.884[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 18:03:10.890[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 18:04:10.899[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-22 18:05:10.905[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 18:06:10.910[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 18:07:10.923[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 18:08:10.936[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 18:09:10.945[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 18:10:10.958[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 18:11:10.965[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 18:12:10.980[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-22 18:13:10.986[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 18:14:11.000[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 18:15:11.002[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-22 18:16:11.005[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-22 18:17:09.210[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 18:17:11.020[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-22 18:18:11.027[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 18:19:11.028[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-22 18:20:11.034[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-22 18:21:11.043[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 18:22:11.059[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-22 18:23:11.069[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-22 18:24:11.081[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-22 18:25:11.081[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-22 18:26:11.090[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 18:27:11.101[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-22 18:28:11.116[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-22 18:29:11.125[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-22 18:30:11.134[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-22 18:31:11.149[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-22 18:32:09.225[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-22 18:32:11.153[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 18:33:11.163[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-22 18:34:11.172[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-22 18:35:11.180[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-22 18:36:11.190[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-22 18:37:11.205[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-22 18:38:11.211[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-22 18:39:11.223[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-22 18:40:11.223[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-22 18:41:11.237[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-22 18:42:11.252[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-22 18:42:47.154[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/localhost:ss-platform-schedule:7606 with status DOWN (replication=false)
[2m2025-07-22 18:42:47.229[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/localhost:ss-platform-common:7612 with status DOWN (replication=false)
[2m2025-07-22 18:42:47.255[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application SS-PLATFORM-EUREKA with eureka with status DOWN
[2m2025-07-22 18:42:47.279[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/localhost:ss-platform-gateway:7601 with status DOWN (replication=false)
[2m2025-07-22 18:42:47.293[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[io-7602-exec-10][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/localhost:ss-platform-authzth:7604 with status DOWN (replication=false)
[2m2025-07-22 18:42:47.301[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shutting down ...
[2m2025-07-22 18:42:47.307[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.batchSize.Will start computing stats again.
[2m2025-07-22 18:42:47.310[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.executionTime.Will start computing stats again.
[2m2025-07-22 18:42:47.311[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.executionTime.Will start computing stats again.
[2m2025-07-22 18:42:47.312[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.batchSize.Will start computing stats again.
[2m2025-07-22 18:42:47.345[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shut down
[2m2025-07-22 18:42:47.356[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Shutting down ExecutorService 'applicationTaskExecutor'
[2m2025-07-22 18:42:47.370[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Shutting down DiscoveryClient ...
[2m2025-07-22 18:42:47.370[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Completed shut down of DiscoveryClient
[2m2025-07-22 18:42:47.387[0;39m [dev] [32m INFO[0;39m [35m28076[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/localhost:ss-ifrs-actuarial:7608 with status DOWN (replication=false)
