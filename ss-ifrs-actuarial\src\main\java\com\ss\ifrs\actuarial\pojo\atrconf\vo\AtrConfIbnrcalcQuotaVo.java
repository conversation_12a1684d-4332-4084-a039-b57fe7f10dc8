/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2024-04-17 16:10:42
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2024-04-17 16:10:42<br/>
 * Description: ibnr计算配置-发展期配置<br/>
 * Table Name: atr_conf_ibnrcalc_quota<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "ibnr计算配置-发展期配置")
public class AtrConfIbnrcalcQuotaVo implements Serializable {
    /**
     * Database column: atr_conf_ibnrcalc_quota.id
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: atr_conf_ibnrcalc_quota.loa_code
     * Database remarks: 业务线
     */
    @ApiModelProperty(value = "业务线", required = true)
    private String loaCode;

    /**
     * Database column: atr_conf_ibnrcalc_quota.pad
     * Database remarks: Provision for Adverse Deviation
     */
    @ApiModelProperty(value = "Provision for Adverse Deviation", required = false)
    private BigDecimal pad;

    /**
     * Database column: atr_conf_ibnrcalc_quota.oper_id
     * Database remarks: 操作人
     */
    @ApiModelProperty(value = "操作人", required = true)
    private Long operId;

    /**
     * Database column: atr_conf_ibnrcalc_quota.oper_time
     * Database remarks: 操作时间
     */
    @ApiModelProperty(value = "操作时间", required = true)
    private Date operTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public BigDecimal getPad() {
        return pad;
    }

    public void setPad(BigDecimal pad) {
        this.pad = pad;
    }

    public Long getOperId() {
        return operId;
    }

    public void setOperId(Long operId) {
        this.operId = operId;
    }

    public Date getOperTime() {
        return operTime;
    }

    public void setOperTime(Date operTime) {
        this.operTime = operTime;
    }
}