/**
 * 
 * This file was generated by Taiping MyBatis Generator(v1.2.12).
 * Date: 2024-05-08 10:55:27
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by Taiping MyBatis Generator(v1.2.12).
 * Create Date: 2024-05-08 10:55:27<br/>
 * Description: ibnr计算-最终轨迹<br/>
 * Table Name: atr_buss_ibnrcalc_final_trace<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@Setter
@Getter
public class AtrBussIbnrcalcFinalTraceVo implements Serializable {
    private String traceType;
    private BigDecimal factor;
    private BigDecimal amount;
    private BigDecimal ibnr;
    private BigDecimal os;
    private Boolean isModify;

}