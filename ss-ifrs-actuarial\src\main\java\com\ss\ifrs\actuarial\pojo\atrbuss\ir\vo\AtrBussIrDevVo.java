/**
 * 
 * This file was generated by Taiping MyBatis Generator(v1.2.12).
 * Date: 2024-08-21 18:15:20
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.ir.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by Taiping MyBatis Generator(v1.2.12).
 * Create Date: 2024-08-21 18:15:20<br/>
 * Description: 当期远期利率主表<br/>
 * Table Name: ATR_BUSS_IR_FORWARD<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "当期远期利率主表")
public class AtrBussIrDevVo implements Serializable {
    /**
     * Database column: ATR_BUSS_IR_FORWARD.ID
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: ATR_BUSS_IR_FORWARD.UPLOAD_RATE_ID
     * Database remarks: 上传数据的ID
     */
    @ApiModelProperty(value = "上传数据的ID", required = true)
    private Long uploadRateId;

    /**
     * Database column: ATR_BUSS_IR_FORWARD.ENTITY_ID
     * Database remarks: 机构ID
     */
    @ApiModelProperty(value = "机构ID", required = true)
    private Long entityId;

    /**
     * Database column: ATR_BUSS_IR_FORWARD.YEAR_MONTH
     * Database remarks: 评估年月
     */
    @ApiModelProperty(value = "评估年月", required = true)
    private String yearMonth;

    /**
     * Database column: ATR_BUSS_IR_FORWARD.CURRENCY_CODE
     * Database remarks: 币别
     */
    @ApiModelProperty(value = "币别", required = true)
    private String currencyCode;


    private String icgNo;
    /**
     * Database column: ATR_BUSS_IR_FORWARD_DEV.MAIN_ID
     * Database remarks: 主表ID
     */
    @ApiModelProperty(value = "主表ID", required = true)
    private Long mainId;

    /**
     * Database column: ATR_BUSS_IR_FORWARD_DEV.DEV_NO
     * Database remarks: 发展期序号
     */
    @ApiModelProperty(value = "发展期序号", required = true)
    private Long devNo;

    /**
     * Database column: ATR_BUSS_IR_FORWARD_DEV.RATE_BASE
     * Database remarks: 基础利率
     */
    @ApiModelProperty(value = "基础利率", required = true)
    private BigDecimal rateBase;

    /**
     * Database column: ATR_BUSS_IR_FORWARD_DEV.RATE_B
     * Database remarks: 期初利率
     */
    @ApiModelProperty(value = "期初利率", required = true)
    private BigDecimal rateB;

    /**
     * Database column: ATR_BUSS_IR_FORWARD_DEV.RATE_M
     * Database remarks: 期中利率
     */
    @ApiModelProperty(value = "期中利率", required = true)
    private BigDecimal rateM;

    /**
     * Database column: ATR_BUSS_IR_FORWARD_DEV.RATE_E
     * Database remarks: 期末利率
     */
    @ApiModelProperty(value = "期末利率", required = true)
    private BigDecimal rateE;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUploadRateId() {
        return uploadRateId;
    }

    public void setUploadRateId(Long uploadRateId) {
        this.uploadRateId = uploadRateId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public Long getMainId() {
        return mainId;
    }

    public void setMainId(Long mainId) {
        this.mainId = mainId;
    }

    public Long getDevNo() {
        return devNo;
    }

    public void setDevNo(Long devNo) {
        this.devNo = devNo;
    }

    public BigDecimal getRateBase() {
        return rateBase;
    }

    public void setRateBase(BigDecimal rateBase) {
        this.rateBase = rateBase;
    }

    public BigDecimal getRateB() {
        return rateB;
    }

    public void setRateB(BigDecimal rateB) {
        this.rateB = rateB;
    }

    public BigDecimal getRateM() {
        return rateM;
    }

    public void setRateM(BigDecimal rateM) {
        this.rateM = rateM;
    }

    public BigDecimal getRateE() {
        return rateE;
    }

    public void setRateE(BigDecimal rateE) {
        this.rateE = rateE;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }
}