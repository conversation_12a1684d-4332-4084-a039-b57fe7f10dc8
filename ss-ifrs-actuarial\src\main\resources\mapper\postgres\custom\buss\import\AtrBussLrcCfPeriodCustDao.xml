<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-01-11 16:41:01 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussLrcCfPeriodDao">
  <!-- 本配置文件由GIS MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**IDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->
    <resultMap id="Enquiry_ResultMap" type="HashMap">
        <id column="LRC_CF_PERIOD_ID" property="lrcCfPeriodId" jdbcType="DECIMAL" />
        <result column="LRC_CF_MAIN_ID" property="lrcCfMainId" jdbcType="DECIMAL" />
        <result column="POLICY_NO_ENDORSEMENT" property="policyNoEndorsement" jdbcType="VARCHAR" />
        <result column="POLICY_NO" property="policyNo" jdbcType="VARCHAR" />
        <result column="PORTFOLIO_NO" property="portfolioNo" jdbcType="VARCHAR" />
        <result column="ICG_NO" property="icgNo" jdbcType="VARCHAR" />
        <result column="ACCUMULATED_EARNED_RATE" property="accumulatedEarnedRate" jdbcType="DECIMAL" />
        <result column="CUR_END_REMAIN_UN_RATE" property="curEndRemainUnRate" jdbcType="DECIMAL" />
        <result column="RPT_PER_REMAIN_UN_RATE" property="rptPerRemainUnRate" jdbcType="DECIMAL" />
        <result column="LP_CUR_END_REMAIN_UN_RATE" property="lpCurEndRemainUnRate" jdbcType="DECIMAL" />
        <result column="LP_RPT_PER_REMAIN_UN_RATE" property="lpRptPerRemainUnRate" jdbcType="DECIMAL" />
        <result column="LRC_CF_FEE_TYPE" property="lrcCfFeeType" jdbcType="VARCHAR" />
    </resultMap>

  <select id="findCfPeriodList" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfPeriod">
    select
       LRC_CF_PERIOD_ID as "lrcCfPeriodId",
       LRC_CF_MAIN_ID as "lrcCfMainId",
       POLICY_NO_ENDORSEMENT as "policyNoEndorsement",
       POLICY_NO as "policyNo",
       PORTFOLIO_NO as "portfolioNo",
       ICG_NO as "icgNo",
       ACCUMULATED_EARNED_RATE as "accumulatedEarnedRate",
       CUR_END_REMAIN_UN_RATE as "curEndRemainUnRate",
       RPT_PER_REMAIN_UN_RATE as "rptPerRemainUnRate",
       LP_CUR_END_REMAIN_UN_RATE as "lpCurEndRemainUnRate",
       LP_RPT_PER_REMAIN_UN_RATE as "lpRptPerRemainUnRate"
    from ATR_BUSS_LRC_CF_PERIOD
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>