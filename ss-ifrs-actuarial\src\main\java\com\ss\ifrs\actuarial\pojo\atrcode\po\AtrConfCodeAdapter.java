/**
 *
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2020-08-14 15:53:16
 * Author liebin.zheng
 *
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 *
 */
package com.ss.ifrs.actuarial.pojo.atrcode.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2020-08-14 15:53:16<br/>
 * Description: atrCodeConfig|双击域和下拉列表查询配置表<br>
 * Table Name: atr_code_config<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "AtrConfCodeAdapter|双击域和下拉列表查询配置表")
public class AtrConfCodeAdapter implements Serializable {
    /**
     *
     * @param codeConfId
     * @return Long  返回类型
     * <AUTHOR>
     * @date 2020/10/12
     */
    @ApiModelProperty(value = "codeConfId|主键", required = true)
    private Long codeConfId;

    /**
     * Database column: atr_code_config.code
     * Database remarks: code|双击域和下拉框查询代码
     */
    @ApiModelProperty(value = "code|双击域和下拉框查询代码", required = true)
    private String code;

    /**
     * Database column: atr_code_config.po_name
     * Database remarks: poName|双击域和下拉查询表名
     */
    @ApiModelProperty(value = "poName|双击域和下拉查询表名", required = true)
    private String poName;

    /**
     * Database column: atr_code_config.search_params
     * Database remarks: searchParams|双击域和下拉查询,条件多个用，分隔
     */
    @ApiModelProperty(value = "searchParams|双击域和下拉查询,条件多个用，分隔", required = false)
    private String searchParams;

    /**
     * Database column: atr_code_config.creator_id
     * Database remarks: creatorId|创建人代码
     */
    @ApiModelProperty(value = "creatorId|创建人代码", required = false)
    private Long creatorId;

    /**
     * Database column: atr_code_config.create_time
     * Database remarks: createTime|创建时间
     */
    @ApiModelProperty(value = "createTime|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: atr_code_config.updator_id
     * Database remarks: updatorId|修改人代码
     */
    @ApiModelProperty(value = "updatorId|修改人代码", required = false)
    private Long updatorId;

    /**
     * Database column: atr_code_config.update_time
     * Database remarks: updateTime|修改时间
     */
    @ApiModelProperty(value = "updateTime|修改时间", required = false)
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public Long getCodeConfId() {
        return codeConfId;
    }

    public void setCodeConfId(Long codeConfId) {
        this.codeConfId = codeConfId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getPoName() {
        return poName;
    }

    public void setPoName(String poName) {
        this.poName = poName;
    }

    public String getSearchParams() {
        return searchParams;
    }

    public void setSearchParams(String searchParams) {
        this.searchParams = searchParams;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}