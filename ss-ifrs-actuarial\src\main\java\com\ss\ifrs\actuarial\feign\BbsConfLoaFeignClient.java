package com.ss.ifrs.actuarial.feign;

import com.ss.platform.pojo.bbs.vo.BbsConfLoaVo;
import com.ss.platform.core.conf.FeignAuthConfig;
import com.ss.platform.pojo.base.po.BaseResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import java.util.List;

/**
 * 调用险种信息的接口
 * 
 * <AUTHOR>
 */
@FeignClient(name = "SS-PLATFORM-COMMON", configuration = { FeignAuthConfig.class })
public interface BbsConfLoaFeignClient {
	/**
	 * "根据险种代码查询险种"
	 * 
	 * @param
	 * @return
	 */
	@RequestMapping(method = RequestMethod.POST, value = "/conf_loa/find_list")
	BaseResponse<List<BbsConfLoaVo>> findLoaList(@RequestBody BbsConfLoaVo bbsConfLoaVo);

}
