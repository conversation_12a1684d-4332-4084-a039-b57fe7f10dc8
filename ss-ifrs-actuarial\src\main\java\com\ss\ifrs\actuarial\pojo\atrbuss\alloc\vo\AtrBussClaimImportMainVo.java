/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2025-05-28 17:57:11
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo;

import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussIbnrImportDetailVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2025-05-28 17:57:11<br/>
 * Description: null<br/>
 * Table Name: atr_buss_calim_import_main<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
public class AtrBussClaimImportMainVo implements Serializable {
    /**
     * Database column: atr_buss_calim_import_main.claim_main_id
     * Database remarks: null
     */
    private Long claimMainId;

    /**
     * Database column: atr_buss_calim_import_main.entity_id
     * Database remarks: null
     */
    private Long entityId;

    /**
     * Database column: atr_buss_calim_import_main.year_month
     * Database remarks: null
     */
    private String yearMonth;

    /**
     * Database column: atr_buss_calim_import_main.version_no
     * Database remarks: null
     */
    private String versionNo;

    /**
     * Database column: atr_buss_calim_import_main.confirm_is
     * Database remarks: null
     */
    private String confirmIs;

    /**
     * Database column: atr_buss_calim_import_main.confirm_id
     * Database remarks: null
     */
    private Long confirmId;

    /**
     * Database column: atr_buss_calim_import_main.confirm_time
     * Database remarks: null
     */
    private Date confirmTime;

    /**
     * Database column: atr_buss_calim_import_main.creator_id
     * Database remarks: null
     */
    private Long creatorId;

    /**
     * Database column: atr_buss_calim_import_main.create_time
     * Database remarks: null
     */
    private Date createTime;

    /**
     * Database column: atr_buss_calim_import_main.updator_id
     * Database remarks: null
     */
    private Long updatorId;

    /**
     * Database column: atr_buss_calim_import_main.update_time
     * Database remarks: null
     */
    private Date updateTime;

    private String entityCode;
    private String entityEName;
    private String entityCName;
    private String entityLName;

    private String confirmName;
    private String creatorName;
    private String updatorName;

    private Integer maxImportLine;

    /**
     * 导出文件名
     */
    private String templateFileName;

    private String targetRouter;

    private List<AtrBussIbnrImportClaimVo> ibnrImportCalimVoList;

    private static final long serialVersionUID = 1L;

    public Long getClaimMainId() {
        return claimMainId;
    }

    public void setClaimMainId(Long claimMainId) {
        this.claimMainId = claimMainId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

    public String getConfirmIs() {
        return confirmIs;
    }

    public void setConfirmIs(String confirmIs) {
        this.confirmIs = confirmIs;
    }

    public Long getConfirmId() {
        return confirmId;
    }

    public void setConfirmId(Long confirmId) {
        this.confirmId = confirmId;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getEntityCode() {
        return entityCode;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public String getEntityEName() {
        return entityEName;
    }

    public void setEntityEName(String entityEName) {
        this.entityEName = entityEName;
    }

    public String getEntityCName() {
        return entityCName;
    }

    public void setEntityCName(String entityCName) {
        this.entityCName = entityCName;
    }

    public String getEntityLName() {
        return entityLName;
    }

    public void setEntityLName(String entityLName) {
        this.entityLName = entityLName;
    }

    public String getConfirmName() {
        return confirmName;
    }

    public void setConfirmName(String confirmName) {
        this.confirmName = confirmName;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }

    public Integer getMaxImportLine() {
        return maxImportLine;
    }

    public void setMaxImportLine(Integer maxImportLine) {
        this.maxImportLine = maxImportLine;
    }

    public String getTemplateFileName() {
        return templateFileName;
    }

    public void setTemplateFileName(String templateFileName) {
        this.templateFileName = templateFileName;
    }

    public String getTargetRouter() {
        return targetRouter;
    }

    public void setTargetRouter(String targetRouter) {
        this.targetRouter = targetRouter;
    }

    public List<AtrBussIbnrImportClaimVo> getIbnrImportCalimVoList() {
        return ibnrImportCalimVoList;
    }

    public void setIbnrImportCalimVoList(List<AtrBussIbnrImportClaimVo> ibnrImportCalimVoList) {
        this.ibnrImportCalimVoList = ibnrImportCalimVoList;
    }
}