CREATE OR REPLACE VIEW EXP_V_CONF_CODE AS
SELECT code_id,
            upper_code_id,
            code_code,
            substr(code_code_idx,2) as code_code_idx,
            code_c_name,
            code_e_name,
            code_l_name,
            valid_is,
            display_no,
            creator_id,
            create_time,
            updator_id,
            update_time
            from(SELECT item.code_id,
              item.upper_code_id,
              item.code_code,
              SYS_CONNECT_BY_PATH(code_code,'/') as code_code_idx,
              item.code_c_name,
              item.code_e_name,
              item.code_l_name,
              item.valid_is,
              item.display_no,
              item.creator_id,
              item.create_time,
              item.updator_id,
              item.update_time
             FROM exp_conf_code item
             start with item.upper_code_id = 0
             connect by PRIOR item.code_id = item.upper_code_id)tc
            order by code_code_idx asc;