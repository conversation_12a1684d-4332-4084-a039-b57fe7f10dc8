<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by Taiping MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2025-03-24 19:27:36 -->
<!-- Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.alloc.AtrBussIbnrAllocToResultXDao">
  <!-- 本配置文件由Taiping MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**BaseDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->
  <insert id="saveDuctCase">
      INSERT INTO atr_duct_case_ep_x (
        ID, action_no, data_type, entity_id, year_month,acc_year_month,
        portfolio_no,icg_no,icg_no_name,treaty_no,treaty_name,
        risk_class_code, policy_no,claim_no,kind_code, case_amount )
      SELECT
        nextval('atr_seq_duct_ibnr_alloc_ep_out_x') AS ID,
        biaa.action_no as action_no,
        '1' as data_type,
        biaa.entity_id,
        biaa.year_month,
        dflo.acc_year_month,
        dflo.portfolio_no,
        dflo.icg_no,
        '' as icg_no_name,
        biic.treaty_no,
        '' as treaty_name,
        dflo.risk_class_code,
        dflo.policy_no,
        biic.claim_no,
        dflo.kind_code,
        dflo.amount as amount
      FROM atr_buss_ibnr_alloc_action biaa,
           atr_buss_ibnr_import_claim biic,
           atr_dap_to_os_x dflo
      WHERE biaa.action_no =  #{allocAction.actionNo,jdbcType=VARCHAR}
        and biic.claim_main_id = #{ibnrImport.claimMainId,jdbcType=DECIMAL}
        and biaa.year_month =  dflo.year_month
        and biic.treaty_no = dflo.treaty_no
        and biic.policy_no = dflo.policy_no
        and biic.kind_code = dflo.kind_code
  </insert>



  <insert id="saveDuctAllocEpDm">
    INSERT INTO atr_duct_ibnr_alloc_ep_out_x (
      ID, action_no, data_type, entity_id, year_month,acc_year_month,
      portfolio_no,icg_no,icg_no_name,treaty_no,treaty_name,
      risk_class_code, policy_no,claim_no,kind_code, case_amount )
    SELECT
      nextval('atr_seq_duct_ibnr_alloc_ep_out_x') AS ID,
      biaa.action_no as action_no,
      '1' as data_type,
      biaa.entity_id,
      biaa.year_month,
      dflo.acc_year_month,
      dflo.portfolio_no,
      dflo.icg_no,
      '' as icg_no_name,
      biic.treaty_no,
      '' as treaty_name,
      dflo.risk_class_code,
      dflo.policy_no,
      biic.claim_no,
      dflo.kind_code,
      dflo.amount as amount
    FROM atr_buss_ibnr_alloc_action biaa,
         atr_buss_ibnr_import_claim biic,
         atr_dap_to_os_x dflo
    WHERE biaa.action_no =  #{allocAction.actionNo,jdbcType=VARCHAR}
      and biic.claim_main_id = #{ibnrImport.claimMainId,jdbcType=DECIMAL}
      and biaa.year_month =  dflo.year_month
      and biic.treaty_no = dflo.treaty_no
      and biic.policy_no = dflo.policy_no
      and biic.kind_code = dflo.kind_code
  </insert>


  <insert id="saveDuctAllocEp">
    INSERT INTO atr_duct_ibnr_alloc_ep_out_x (
      ID, action_no, data_type, entity_id, year_month,acc_year_month,treaty_no,treaty_name,
      portfolio_no,icg_no,icg_no_name, risk_class_code,
      pl_judge_rslt,fin_detail_code, fin_product_code, fin_sub_product_code,
      fin_acc_channel, dept_id, channel_id, company_code4,
      policy_no,claim_no,kind_code, case_amount )
    SELECT
      nextval('atr_seq_duct_ibnr_alloc_ep_out_x') AS ID,
      biaa.action_no as action_no,
      '1' as data_type,
      biaa.entity_id,
      biaa.year_month,
      dflo.acc_year_month,
      biic.treaty_no,
      '' as treaty_name,
      dd.portfolio_no,
      dd.icg_no,
      '' as icg_no_name,
      dd.risk_class_code,
      dd.pl_judge_rslt, '' as fin_detail_code, '' as fin_product_code,'01' as fin_sub_product_code,
      '' as fin_acc_channel,'' as dept_id,'0100' as channel_id,'' as company_code4,
      dflo.policy_no,
      biic.claim_no,
      dflo.kind_code,
      sum(dflo.amount) as amount
    FROM atr_buss_ibnr_alloc_action biaa,
         atr_buss_ibnr_import_claim biic,
         dmuser.dm_buss_cmunit_treaty_outward dd,
        (SELECT
           to_char( accident_date_time, 'yyyymm' ) AS acc_year_month,
           T.policy_no,
           t.claim_no,
           T.clause_code AS kind_code,
           sum(outstanding_amount) AS amount
         FROM dmuser.dm_claim_outstanding T
         WHERE T.business_type_code = 'BF'
         group by  to_char( accident_date_time, 'yyyymm'),
                   T.policy_no,
                   t.claim_no,
                   T.clause_code
         union all
         SELECT
           to_char( T.approval_date, 'yyyymm' ) AS acc_year_month,
           T.policy_no,
           t.claim_no,
           T.clause_code AS kind_code,
           sum(amount) AS amount
         FROM dmuser.dm_claim_loss_detail T
         group by  to_char( T.approval_date, 'yyyymm'),
                   T.policy_no,
                   t.claim_no,
                   T.clause_code
            ) dflo
    WHERE biaa.action_no =  #{allocAction.actionNo,jdbcType=VARCHAR}
      and biic.claim_main_id = #{ibnrImport.claimMainId,jdbcType=DECIMAL}
      and biic.treaty_no = dd.treaty_no
      and biic.policy_no = dflo.policy_no
      and biic.claim_no =  dflo.claim_no
      and biic.kind_code = dflo.kind_code
    group by   biaa.action_no,
               biaa.entity_id,
               biaa.year_month,
               dflo.acc_year_month,
               biic.treaty_no,
               dd.portfolio_no,
               dd.icg_no,
               dd.risk_class_code,
               dd.pl_judge_rslt,
               dflo.policy_no,
               biic.claim_no,
               dflo.kind_code
  </insert>


  <insert id="saveDuctAllocEpSum">
    INSERT INTO atr_duct_ibnr_alloc_ep_out_x (
       ID, action_no, data_type, entity_id, year_month, acc_year_month,treaty_no,treaty_name,
       portfolio_no,icg_no,icg_no_name, risk_class_code,
       pl_judge_rslt,fin_detail_code, fin_product_code, fin_sub_product_code,
       fin_acc_channel, dept_id, channel_id, company_code4,
       policy_no,claim_no, kind_code, case_amount, case_sum_amount )
    SELECT
      nextval( 'atr_seq_duct_ibnr_alloc_ep_out_x' ) AS ID,
      diae.action_no,
      '2' as data_type,
      diae.entity_id,
      diae.year_month,
      diae.acc_year_month,
      diae.treaty_no,
      diae.treaty_name,
      diae.portfolio_no,
      diae.icg_no,
      diae.icg_no_name,
      diae.risk_class_code,
      diae.pl_judge_rslt, diae.fin_detail_code, diae.fin_product_code, diae.fin_sub_product_code,
      diae.fin_acc_channel, diae.dept_id, diae.channel_id, diae.company_code4,
      diae.policy_no,
      diae.claim_no,
      diae.kind_code,
      diae.case_amount,
      SUM(case_amount) OVER (PARTITION BY entity_id,year_month,treaty_no) AS case_sum_amount
    FROM
      atr_duct_ibnr_alloc_ep_out_x diae
    WHERE diae.action_no = #{actionNo,jdbcType=VARCHAR} and data_type='1'
  </insert>

  <insert id="saveIbnrAllocOutXData">
    INSERT INTO atr_buss_ibnr_alloc_to_result_x (
      ID,
      action_no,
      entity_id,
      year_month,
      portfolio_no,
      icg_no,
      icg_no_name,
      acc_year_month,
      treaty_no,
      treaty_name,
      risk_class_code,center_code,
      pl_judge_rslt,fin_detail_code, fin_product_code, fin_sub_product_code,
      fin_acc_channel, dept_id, channel_id, company_code4,
      policy_no,
      claim_no,
      kind_code,
      rl_amount,
      rl_ratio,
      case_amount,
      create_time
    )
    SELECT
      nextval( 'atr_seq_buss_ibnr_alloc_out_result_x' ) AS ID,
      biaa.action_no,
      biaa.entity_id,
      biaa.year_month,
      diae.portfolio_no,
      diae.icg_no,
      diae.icg_no_name,
      diae.acc_year_month,
      diae.treaty_no,
      diae.treaty_name,
      diae.risk_class_code,
      diae.center_code,
      diae.pl_judge_rslt, diae.fin_detail_code, diae.fin_product_code, diae.fin_sub_product_code,
      diae.fin_acc_channel, diae.dept_id, diae.channel_id, diae.company_code4,
      diae.policy_no,
      diae.claim_no,
      diae.kind_code,
      diae.case_amount,
      round(diae.case_amount/diae.case_sum_amount,8),
      biid.ri_case_amount * round(diae.case_amount/diae.case_sum_amount,8),
      LOCALTIMESTAMP as create_time
    FROM atr_buss_ibnr_alloc_action biaa
     LEFT JOIN atr_buss_ibnr_import_detail biid
         ON biid.business_model = 'XO'
     LEFT JOIN atr_duct_ibnr_alloc_ep_out_x diae
         on biaa.entity_id=diae.entity_id
        and biid.treaty_no=diae.treaty_no
        and biaa.action_no=diae.action_no
    WHERE diae.action_no = #{allocAction.actionNo,jdbcType=VARCHAR}
      and biid.ibnr_main_id = #{ibnrImport.ibnrMainId,jdbcType=DECIMAL}
      and data_type='2'
  </insert>

  <resultMap id="ResultVoResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrAllocToResultXVo">
    <result column="action_no" property="actionNo" jdbcType="VARCHAR" />
    <result column="entity_id" property="entityId" jdbcType="BIGINT" />
    <result column="year_month" property="yearMonth" jdbcType="VARCHAR" />
    <result column="portfolio_no" property="portfolioNo" jdbcType="VARCHAR" />
    <result column="icg_no" property="icgNo" jdbcType="VARCHAR" />
    <result column="icg_no_name" property="icgNoName" jdbcType="VARCHAR" />
    <result column="evaluate_approach" property="evaluateApproach" jdbcType="VARCHAR" />
    <result column="acc_year_month" property="accYearMonth" jdbcType="VARCHAR" />
    <result column="treaty_no" property="treatyNo" jdbcType="VARCHAR" />
    <result column="risk_class_code" property="riskClassCode" jdbcType="VARCHAR" />
    <result column="pl_judge_rslt" property="plJudgeRslt" jdbcType="VARCHAR" />
    <result column="fin_detail_code" property="finDetailCode" jdbcType="VARCHAR" />
    <result column="fin_product_code" property="finProductCode" jdbcType="VARCHAR" />
    <result column="fin_sub_product_code" property="finSubProductCode" jdbcType="VARCHAR" />
    <result column="fin_acc_channel" property="finAccChannel" jdbcType="VARCHAR" />
    <result column="center_code" property="centerCode" jdbcType="VARCHAR" />
    <result column="dept_id" property="deptId" jdbcType="VARCHAR" />
    <result column="channel_id" property="channelId" jdbcType="VARCHAR" />
    <result column="company_code4" property="companyCode4" jdbcType="VARCHAR" />
    <result column="policy_no" property="policyNo" jdbcType="VARCHAR" />
    <result column="kind_code" property="kindCode" jdbcType="VARCHAR" />
    <result column="claim_no" property="claimNo" jdbcType="VARCHAR" />
    <result column="rl_amount" property="rlAmount" jdbcType="NUMERIC" />
    <result column="rl_ratio" property="rlRatio" jdbcType="NUMERIC" />
    <result column="case_amount" property="caseAmount" jdbcType="NUMERIC" />
    <result column="creator_id" property="creatorId" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <select id="findAllocList" resultMap="ResultVoResultMap">
    select biaa.action_no, biaa.year_month,biadr.acc_year_month,biadr.portfolio_no,
           biadr.icg_no, biadr.icg_no_name,biadr.treaty_no, biadr.risk_class_code,
           biadr.pl_judge_rslt, biadr.fin_detail_code, biadr.fin_product_code, biadr.fin_sub_product_code,
           biadr.fin_acc_channel, biadr.dept_id, biadr.channel_id, biadr.company_code4,
           biadr.policy_no, biadr.kind_code,biadr.rl_amount,biadr.rl_ratio, biadr.case_amount
    FROM atr_buss_ibnr_alloc_action biaa,atr_buss_ibnr_alloc_to_result_x biadr
    where biaa.action_No = biadr.action_No
    <choose>
      <when test="actionNo != null">
        and biaa.action_no = #{actionNo,jdbcType=VARCHAR}
      </when>
      <otherwise>
        <if test="entityId != null ">
          and biaa.entity_id = #{entityId,jdbcType=BIGINT}
        </if>
        <if test="yearMonth != null and yearMonth != ''">
          and biaa.year_month = #{yearMonth,jdbcType=VARCHAR}
        </if>
        <if test="yearMonthStart != null and yearMonthStart != ''">
          and biaa.year_month >= #{yearMonthStart}
        </if>
        <if test="yearMonthEnd != null and yearMonthEnd != ''">
          and biaa.year_month  <![CDATA[ <= ]]> #{yearMonthEnd}
        </if>
        <if test="status != null and status != ''">
          and biaa.STATUS = #{status,jdbcType=VARCHAR}
        </if>
        <if test="confirmIs != null and confirmIs != ''">
          and biaa.CONFIRM_IS = #{confirmIs,jdbcType=VARCHAR}
        </if>
      </otherwise>
    </choose>
  </select>

  <delete id="deleteDapIbnr">
    delete from atr_dap_to_os_x
    where entity_id = #{entityId,jdbcType=BIGINT}
      and year_month = #{yearMonth,jdbcType=VARCHAR}
  </delete>


  <insert id="confirm">
    INSERT INTO atr_dap_to_os_x (
      year_month,
      entity_id,
      acc_year_month,
      portfolio_no,
      icg_no,
      risk_class_code,
      pl_judge_rslt,center_code,
      fin_detail_code, fin_product_code, fin_sub_product_code,
      fin_acc_channel, dept_id, channel_id, company_code4,
      treaty_no,
      policy_no,
      kind_code,
      amount,
      draw_time
    )
    SELECT
      biaa.year_month,
      biaa.entity_id,
      biadr.acc_year_month,
      biadr.portfolio_no,
      biadr.icg_no,
      biadr.risk_class_code,
      biadr.pl_judge_rslt,biadr.center_code,
      biadr.fin_detail_code, biadr.fin_product_code, biadr.fin_sub_product_code,
      biadr.fin_acc_channel, biadr.dept_id, biadr.channel_id, biadr.company_code4,
      biadr.treaty_no,
      biadr.policy_no,
      biadr.kind_code,
      biadr.case_amount,
      LOCALTIMESTAMP as create_time
    FROM atr_buss_ibnr_alloc_action biaa
      LEFT JOIN atr_buss_ibnr_alloc_to_result_x  biadr ON biadr.action_no =biaa.action_no
    WHERE biaa.confirm_is='1'
      and biadr.action_no is not null
    <choose>
      <when test="actionNo != null">
        and biaa.action_no = #{actionNo,jdbcType=VARCHAR}
      </when>
      <otherwise>
        <if test="entityId != null ">
          and biaa.entity_id = #{entityId,jdbcType=BIGINT}
        </if>
        <if test="yearMonth != null and yearMonth != ''">
          and biaa.year_month = #{yearMonth,jdbcType=VARCHAR}
        </if>
        <if test="businessSourceCode != null and businessSourceCode != ''">
          and biaa.business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
        </if>
      </otherwise>
    </choose>
  </insert>
</mapper>