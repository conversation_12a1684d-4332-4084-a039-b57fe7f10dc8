package com.ss.ifrs.actuarial.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.Map;

@Mapper
public interface AtrBussDapDao {

    String getDmPeriodState(Map<String, Object> paramMap);

    String getExpPeriodState(Map<String, Object> paramMap);

    String getAtrPeriodState(Map<String, Object> paramMap);

    void completeDirection1(Long entityId, String yearMonth);

    void truncateDapData(Map<String, Object> paramMap);

    void deleteDapData(Map<String, Object> paramMap);
}
