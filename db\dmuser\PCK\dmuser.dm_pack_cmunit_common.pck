CREATE OR REPLACE PACKAGE dm_pack_cmunit_common IS

  FUNCTION func_factor_value_cal(p_cm_unit_id     NUMBER,
                                 p_entity_id      NUMBER,
                                 p_buss_def_code      VARCHAR2,
                                 p_rule_id        NUMBER,
                                 p_rule_type      VARCHAR2,
                                 p_business_model VARCHAR2) RETURN VARCHAR2;

  FUNCTION func_get_operator(p_firvalue VARCHAR2,
                             p_operator VARCHAR2,
                             p_secvalue VARCHAR2) RETURN NUMBER;


  FUNCTION func_rule_parsing(p_cm_unit_id     NUMBER,
                             p_rule           VARCHAR2,
                             p_business_model VARCHAR2) RETURN VARCHAR2;

  FUNCTION func_get_contractcode(p_entity_id      NUMBER,
                                 p_code_type      VARCHAR2,
                                 p_business_model VARCHAR2) RETURN VARCHAR2;

  FUNCTION func_get_cmunitno(p_entity_id NUMBER,
                             p_year      VARCHAR2,
                             p_risk_code VARCHAR2,
                             p_param     VARCHAR2) RETURN VARCHAR2;

  FUNCTION func_get_ri_cmunitno(p_entity_id          NUMBER,
                                p_business_model     VARCHAR2,
                                p_business_direction VARCHAR2) RETURN VARCHAR2;

  PROCEDURE proc_direct_profit_check(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2,
                                     p_data_key   VARCHAR2);

  PROCEDURE proc_direct_profit_param(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2,
                                     p_data_key   VARCHAR2,
                                     p_method     VARCHAR2);

  PROCEDURE proc_direct_judge_paa(p_entity_id  NUMBER,
                                  p_year_month VARCHAR2,
                                  p_data_key   VARCHAR2);

  PROCEDURE proc_direct_judge_bba(p_entity_id  NUMBER,
                                  p_year_month VARCHAR2,
                                  p_data_key   VARCHAR2);

  PROCEDURE proc_fac_profit_param(p_entity_id  NUMBER,
                                  p_year_month VARCHAR2,
                                  p_data_key   VARCHAR2,
                                  p_method     VARCHAR2);

  PROCEDURE proc_treaty_profit_check(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2,
                                     p_data_key   VARCHAR2);

  PROCEDURE proc_treaty_profit_param(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2,
                                     p_data_key   VARCHAR2,
                                     p_method     VARCHAR2);

  PROCEDURE proc_treaty_judge_paa(p_entity_id  NUMBER,
                                  p_year_month VARCHAR2,
                                  p_data_key   VARCHAR2);

  PROCEDURE proc_treaty_judge_bba(p_entity_id  NUMBER,
                                  p_year_month VARCHAR2,
                                  p_data_key   VARCHAR2);

  PROCEDURE proc_buss_cmunit_dtl_log(p_trace_no VARCHAR2,
                                     p_trace_code VARCHAR2,
                                     p_trace_status VARCHAR2,
                                     p_trace_msg VARCHAR2);
                                     
  PROCEDURE proc_backup_profit_unit_result_tab(p_entity_id  NUMBER,
                                           p_year_month VARCHAR2,
                                           p_table_name VARCHAR2);

END dm_pack_cmunit_common;
/
CREATE OR REPLACE PACKAGE BODY dm_pack_cmunit_common IS

  FUNCTION func_factor_value_cal(p_cm_unit_id     NUMBER,
                                 p_entity_id      NUMBER,
                                 p_buss_def_code      VARCHAR2,
                                 p_rule_id        NUMBER,
                                 p_rule_type      VARCHAR2,
                                 p_business_model VARCHAR2) RETURN VARCHAR2 IS

    cur_contract_rule   VARCHAR2(200);
    v_rule_operator     VARCHAR2(200);
    v_firvalue          VARCHAR2(400);
    v_secvalue          VARCHAR2(400);
    v_majorrisk         VARCHAR2(200);
    v_ruleid            NUMBER;
    v_rule_count        NUMBER;
    v_get_operator_flag NUMBER;
  BEGIN

    IF p_rule_id IS NULL THEN
      --合约号字段匹配
      if upper(substr(p_business_model,1,1)) = upper('T') then
         --非合约号字段匹配
        SELECT tt.contract_rule_id
          INTO v_ruleid
          FROM (SELECT contract_rule_id
                  FROM dm_conf_contract_rule_def
                 WHERE valid_is = '1'
                   AND audit_state = '1'
                   AND contract_rule_type = p_rule_type
                   AND entity_id = p_entity_id
                   AND (treaty_no = p_buss_def_code OR coalesce(treaty_no, 'null') = 'null')-- 未按风险配置时取业务单位下的配置
                   AND business_model||business_direction = p_business_model
                 --ORDER BY coalesce(risk_code, '') DESC) tt
                 ORDER BY coalesce(treaty_no, '') DESC) tt
         WHERE rownum = 1;
      else
        --非合约号字段匹配
        SELECT tt.contract_rule_id
          INTO v_ruleid
          FROM (SELECT contract_rule_id
                  FROM dm_conf_contract_rule_def
                 WHERE valid_is = '1'
                   AND audit_state = '1'
                   AND contract_rule_type = p_rule_type
                   AND entity_id = p_entity_id
                   --AND (risk_code = p_buss_def_code OR coalesce(risk_code, '') = '') -- 未按风险配置时取业务单位下的配置
                   AND (product_code = p_buss_def_code OR coalesce(product_code, 'null') = 'null')
                   AND business_model||business_direction = p_business_model
                 --ORDER BY coalesce(risk_code, '') DESC) tt
                 ORDER BY coalesce(product_code, '') DESC) tt
         WHERE rownum = 1;
      end if;
    ELSE
      v_ruleid := p_rule_id;
    END IF;

    SELECT COUNT(1)
      INTO v_rule_count
      FROM dm_conf_contract_rule
     WHERE contract_rule_id = v_ruleid
       AND business_model||business_direction = p_business_model;

    IF v_rule_count IS NULL
       OR v_rule_count < 1 THEN

      RETURN 'R:';
    END IF;
    FOR cur_contract_rule IN (SELECT rule_operator,
                                     fir_value,
                                     sec_value,
                                     true_type,
                                     false_type,
                                     true_rule_id,
                                     true_value,
                                     false_rule_id,
                                     false_value
                                FROM dm_conf_contract_rule
                               WHERE contract_rule_id = v_ruleid
                                 AND business_model||business_direction = p_business_model
                                 and valid_is = '1'
                                 AND audit_state = '1' ) LOOP
      --DBMS_OUTPUT.PUT_LINE('调用规则:' || cur_contract_rule);
      --DBMS_OUTPUT.PUT_LINE( %cur_contract_rule);
      --获取操作符
      v_rule_operator := cur_contract_rule.rule_operator;
      --RAISE notice'fir_value : (%)',cur_contract_rule.fir_value;
      IF lower(cur_contract_rule.fir_value) = lower('null') THEN
        v_firvalue := '0';
      ELSE
        v_firvalue := func_rule_parsing(p_cm_unit_id, cur_contract_rule.fir_value, p_business_model);
      END IF;

      IF lower(cur_contract_rule.sec_value) = lower('null') THEN
        v_secvalue := '0';
      ELSE
        v_secvalue := func_rule_parsing(p_cm_unit_id, cur_contract_rule.sec_value, p_business_model);
      END IF;


      IF v_firvalue LIKE 'R:%' THEN
        RETURN v_firvalue;
      END IF;

      IF v_secvalue LIKE 'R:%' THEN
        RETURN v_secvalue;
      END IF;

      --IF  func_get_operator(v_firValue, v_rule_operator, v_secValue)  THEN
      SELECT func_get_operator(v_firvalue, v_rule_operator, v_secvalue) INTO v_get_operator_flag FROM dual;
      IF v_get_operator_flag = 1 THEN
        IF cur_contract_rule.true_type = '1' THEN
          v_majorrisk := func_factor_value_cal(p_cm_unit_id, p_entity_id, p_buss_def_code, cur_contract_rule.true_rule_id, p_rule_type, p_business_model);
        ELSE
          v_majorrisk := func_rule_parsing(p_cm_unit_id, cur_contract_rule.true_value, p_business_model);
        END IF;
      ELSE
        IF cur_contract_rule.false_type = '1' THEN
          v_majorrisk := func_factor_value_cal(p_cm_unit_id, p_entity_id, p_buss_def_code, cur_contract_rule.false_rule_id, p_rule_type, p_business_model);
        ELSE
          v_majorrisk := func_rule_parsing(p_cm_unit_id, cur_contract_rule.false_value, p_business_model);
        END IF;
        --dbms_output.put_line('456:'||v_majorrisk);
      END IF;
    END LOOP;

    --返回实际计算结果
    RETURN v_majorrisk;

    --   if v_majorrisk = 'Y' then
    --     return 'Y';
    --   else
    --     return 'N';
    --   end if;

  EXCEPTION
    WHEN OTHERS THEN
      ---异常时返回异常标记与相关信息
      RETURN 'E:' || SQLERRM;

  END func_factor_value_cal;



  FUNCTION func_get_operator(p_firvalue VARCHAR2,
                             p_operator VARCHAR2,
                             p_secvalue VARCHAR2) RETURN NUMBER IS

    v_sql      VARCHAR2(400);
    v_result   NUMBER;
    v_operator VARCHAR2(100);
  BEGIN
    CASE p_operator
      WHEN '0' THEN
        v_operator := '<';

      WHEN '1' THEN
        v_operator := '<=';

      WHEN '2' THEN
        v_operator := '>';

      WHEN '3' THEN
        v_operator := '>=';

      WHEN '4' THEN
        v_operator := '!=';

      WHEN '5' THEN
        v_operator := '=';
      ELSE
        v_operator := '';
    END CASE;

    --p_firvalue IS NULL
    IF (p_firvalue IS NULL OR lower(p_firvalue) = lower('null')) THEN
      --AND p_secvalue IS NOT NULL
      IF (p_secvalue IS NOT NULL OR lower(p_secvalue) != lower('null')) THEN
        -- NULL != NULL
        IF p_operator = '4' THEN
          RETURN 1;
        ELSE
          RETURN 0;
        END IF;
        --AND p_secvalue IS NULL
      ELSIF (p_secvalue IS NULL OR lower(p_secvalue) = lower('null')) THEN
        -- NULL == NULL
        IF p_operator = '5' THEN
          RETURN 1;
        ELSE
          RETURN 0;
        END IF;
      END IF;

      --p_firvalue IS NOT NULL AND p_secvalue IS NULL
    ELSIF (p_secvalue IS NULL OR lower(p_secvalue) = lower('null')) THEN
      -- NULL != NULL
      IF p_operator = '4' THEN
        RETURN 1;
      ELSE
        RETURN 0;
      END IF;

    END IF;

    --p_firvalue IS NOT NULL AND p_secvalue IS NOT NULL
    v_sql := 'SELECT coalesce(1,0) from dual where ' || p_firvalue || v_operator || p_secvalue || '';
    --RAISE notice'(%)',( v_sql );
    EXECUTE IMMEDIATE v_sql
      INTO v_result;

    RETURN v_result;

  EXCEPTION
    WHEN OTHERS THEN
      RETURN 0;
  END func_get_operator;


  FUNCTION func_rule_parsing(p_cm_unit_id     NUMBER,
                             p_rule           VARCHAR2,
                             p_business_model VARCHAR2) RETURN VARCHAR2 IS

    --cur_rule_factor record;--切割之后的数据
    --cur_cmunit record;
    v_rule_record   VARCHAR2(200); --接收去掉小括号的值
    v_rule          VARCHAR2(200); --包含[]的规则因子
    v_result_rule   VARCHAR2(200) := p_rule; --最终返回的运算表达式  0-(10.00+100.00))*3
    v_result2       VARCHAR2(200); --最终返回的结果
    v_code_code     VARCHAR2(200); --业务编码值的code
    v_colmn_ref     VARCHAR2(200); --计量单元表对应的字段
    v_match_type    VARCHAR2(200); --取值类型
    v_colmn_result  VARCHAR2(200); --最终返回的结果
    v_sql           VARCHAR2(2000); --查询sql
    v_code_rule     NUMBER; --切割完后是否包含[]的因子
    v_related_table VARCHAR2(200); --字段对应的表名
    v_count         NUMBER := 0; --是否需要进行运算还是直接返回
    v_colref_count  NUMBER;
    v_col_business  VARCHAR2(200);

    v_default_value VARCHAR2(200); --取默认值
    v_risk_class    VARCHAR2(200); --险类
    v_treaty_class  VARCHAR2(200); --合约大类

    v_year_month     VARCHAR2(200); --当前评估期
    v_end_date       DATE;
    v_endorse_seq_no VARCHAR2(200);

    v_entity_id       NUMBER;
    v_policy_no       VARCHAR2(200);
    v_risk_code       VARCHAR2(200);
    v_treaty_no       VARCHAR2(200);
    v_reins_direction VARCHAR2(200);

  pragma autonomous_transaction;
  BEGIN

    --解析规则 ([MAXRATE]-[MINRATE])*3
    --根据运算符切割
    --去掉小括号
    v_rule_record := REPLACE(p_rule, '(', '');
    v_rule_record := REPLACE(v_rule_record, ')', '');

    --SELECT regexp_split_to_table(v_rule_record, '\*|\-|\+|\/')
    FOR cur_rule_factor IN (SELECT regexp_substr(q.nums, '[^,]+', 1, rownum, 'i') AS rule_factor_value
                              FROM (SELECT regexp_replace(v_rule_record, '\+|\-|\*|\/', ',') nums FROM dual) q
                            CONNECT BY rownum <= length(q.nums) - length(regexp_replace(q.nums, ',', '')) + 1) LOOP
      --切割完后是否包含[]的因子
      SELECT instr(cur_rule_factor.rule_factor_value, '[') INTO v_code_rule FROM dual;
      IF v_code_rule > 0 THEN
        v_count := v_count + 1;

        --获取到业务编码值的code
        --v_code_code := regexp_replace(regexp_replace(cur_rule_factor.rule_factor_value, '\(\[|', ''), '\]\)', '');
        v_code_code := regexp_replace(cur_rule_factor.rule_factor_value, '\(\[|\[|\]\)|\]', '');

        --根据code查询到对应的字段
        v_sql := 'SELECT Column_Ref,related_table,default_value,MATCH_TYPE
                 from dm_conf_contract_code where code_code = ''' || v_code_code || ''' AND business_model||business_direction = ''' ||p_business_model||''' ';

        EXECUTE IMMEDIATE v_sql
          INTO v_colmn_ref, v_related_table, v_default_value,v_match_type;

        --查询计量单元中对应的字段 start
       IF upper(p_business_model) = upper('FO') THEN
          v_sql := 'SELECT entity_id, policy_no, risk_code, null, ri_direction_code from dm_buss_cmunit_fac_outwards WHERE cm_unit_outwards_id = ''' || p_cm_unit_id || '''';

        ELSIF upper(substr(p_business_model, 1, 1)) = upper('T') THEN
          v_sql := 'SELECT entity_id, null, risk_code, treaty_no, ri_direction_code from dm_buss_cmunit_treaty WHERE cm_unit_itreaty_id = ''' || p_cm_unit_id || '''';

        ELSE
          v_sql := 'SELECT entity_id, policy_no, risk_code, null, null from dm_buss_cmunit_direct WHERE CM_UNIT_ID = ''' || p_cm_unit_id || '''';
        END IF;

        EXECUTE IMMEDIATE v_sql
          INTO v_entity_id, v_policy_no, v_risk_code, v_treaty_no, v_reins_direction;

        --查询计量单元中对应的字段 end

        --获取当前评估期
        SELECT year_month
          INTO v_year_month
          FROM dm_conf_bussperiod
         WHERE entity_id = v_entity_id
           AND period_state IN ('2') --处理中
           AND rownum = 1
         ORDER BY year_month;

        v_end_date := to_date(to_char(last_day(to_date(v_year_month, 'yyyymm')), 'yyyy/mm/dd') || '23:59:59', 'yyyy-mm-dd hh24:mi:ss');

        --求和
        IF v_match_type = '6' THEN
          v_colmn_ref := 'sum(' || v_colmn_ref || ')';
        END IF;

        --获取对应字段值 start
        IF lower(v_related_table) = 'dm_buss_cmunit_direct' THEN

          v_sql := 'SELECT  ' || v_colmn_ref || ' from ' || v_related_table || ' WHERE CM_UNIT_ID = ''' || p_cm_unit_id || '''';

        ELSIF lower(v_related_table) = 'dm_buss_cmunit_fac_outwards' THEN

          v_sql := 'SELECT  ' || v_colmn_ref || ' from ' || v_related_table || ' WHERE cm_unit_outwards_id = ''' || p_cm_unit_id || '''';

        ELSIF lower(v_related_table) = 'dm_buss_cmunit_treaty' THEN

          v_sql := 'SELECT  ' || v_colmn_ref || ' from ' || v_related_table || ' WHERE cm_unit_itreaty_id = ''' || p_cm_unit_id || '''';

        ELSIF lower(v_related_table) = 'dm_v_bbs_conf_conf_quota' THEN

          v_sql := 'SELECT ' || v_colmn_ref || ' from ' || v_related_table || ' WHERE  valid_is = ''1'' AND audit_state = ''1'' AND entity_id = ' || v_entity_id;


          IF upper(substr(p_business_model, 1, 1)) = upper('T') THEN
            --合约大类

            --v_sql := v_sql || '  AND treaty_class_code = ''' || cur_cmunit.treaty_class_code || '''';
            v_sql := v_sql || '';
          ELSE
            --险类
            SELECT t.risk_class_code
              INTO v_risk_class
              FROM bpluser.bbs_conf_risk t
             WHERE t.risk_code = v_risk_code
               AND t.valid_is = '1'
               AND t.audit_state = '1'
               AND rownum = 1;

            IF v_risk_class IS NULL THEN
              RETURN 'R:Risk Class not find ';
            END IF;

            v_sql := v_sql || '  AND risk_class_code = ''' || v_risk_class || ''' AND rownum=1 order by COALESCE(risk_class_code,'''') desc ';
          END IF;

        ELSIF lower(v_related_table) = 'dm_policy_main' THEN

          v_sql := 'SELECT ' || v_colmn_ref || ' from ' || v_related_table || ' WHERE policy_no = ''' || v_policy_no || ''' AND entity_id = ' || v_entity_id ||
                   ' AND ((endorse_effective_date is not null AND  endorse_effective_date <= ''' || v_end_date || ''') or (endorse_effective_date is null AND approval_date <= ''' || v_end_date || ''' )) ';

          IF v_match_type = '1' THEN
            v_sql := v_sql || ' AND rownum=1 ORDER BY endorse_seq_no desc ';
          END IF;

        ELSIF lower(v_related_table) = 'dm_policy_premium' THEN

          SELECT endorse_seq_no
            INTO v_endorse_seq_no
            FROM dm_policy_main
           WHERE policy_no = v_policy_no
             AND entity_id = v_entity_id
             AND ((endorse_effective_date IS NOT NULL AND endorse_effective_date <= v_end_date) OR (endorse_effective_date IS NULL AND effective_date <= v_end_date))
             AND rownum = 1
           ORDER BY endorse_seq_no DESC;

          v_sql := 'SELECT ' || v_colmn_ref || ' from ' || v_related_table || ' WHERE policy_no = ''' || v_policy_no || ''' AND entity_id = ' || v_entity_id;

          IF v_endorse_seq_no IS NOT NULL THEN
            v_sql := v_sql || ' AND endorse_seq_no = ''' || v_endorse_seq_no || '''';
          END IF;

          IF v_match_type = '1' THEN
            v_sql := v_sql || ' AND rownum=1 ORDER BY endorse_seq_no desc ';
          END IF;

        ELSIF lower(v_related_table) = 'dm_reins_outward'
              AND upper(p_business_model) = 'FO' THEN

          v_sql := 'SELECT ' || v_colmn_ref || ' FROM ' || v_related_table || '
          where entity_id = ' || v_entity_id || '
            AND policy_no is not null
            AND endorse_seq_no is not null
            AND treaty_no = ''' || v_treaty_no || '''
            AND ri_direction_code = ''' || v_reins_direction || '''
            AND policy_no = ''' || v_policy_no || '''
            AND issue_date<= ''' || v_end_date || '''';

          IF v_match_type = '1' THEN
            v_sql := v_sql || ' AND rownum=1 ORDER BY endorse_seq_no desc ';
          END IF;

        ELSIF lower(v_related_table) = 'dm_reins_outward'
              AND upper(substr(p_business_model, 0, 1)) = 'T' THEN

          v_sql := 'SELECT ' || v_colmn_ref || ' FROM ' || v_related_table || '
          where entity_id = ' || v_entity_id || '
           AND treaty_no is not null
           AND policy_no is null
           AND endorse_seq_no is null
           AND ri_direction_code = ''' || v_reins_direction || '''
           AND treaty_no = ''' || v_treaty_no || '''
           AND issue_date<= ''' || v_end_date || '''';

          IF v_match_type = '1' THEN
            v_sql := v_sql || ' AND rownum=1 ORDER BY endorse_seq_no desc ';
          END IF;

        ELSIF lower(v_related_table) = 'dm_reins_bill' THEN

          v_sql := 'SELECT ' || v_colmn_ref || '
            from  dm_reins_bill rb
               join dm_reins_treaty t1
                  on t1.treaty_no = rb.treaty_no
                  AND t1.entity_id = rb.entity_id
                  AND t1.ri_direction_code = rb.ri_direction_code
              where  rb.entity_id = ' || v_entity_id || '
                 AND t1.issue_date<= ''' || v_end_date || '''
                 AND rb.ri_direction_code = ''' || v_reins_direction || '''
                 AND rb.treaty_no = ''' || v_treaty_no || '''';

          IF v_match_type = '1' THEN
            v_sql := v_sql || ' AND rownum=1 ';
          END IF;

        ELSIF lower(v_related_table) = 'dm_reins_treaty' THEN

          v_sql := 'SELECT ' || v_colmn_ref || '
          from  dm_reins_treaty rt
            where  rt.entity_id = ' || v_entity_id || '
               AND rt.ri_direction_code = ''' || v_reins_direction || '''
               AND rt.treaty_no = ''' || v_treaty_no || '''
               AND rt.effective_date < ''' || v_end_date || '''
               AND rt.expiry_date >= ''' || v_end_date || '''';

          IF v_match_type = '1' THEN
            v_sql := v_sql || ' AND rownum=1 ';
          END IF;
        ELSE

          v_sql := 'SELECT ' || v_colmn_ref || ' from ' || v_related_table || ' WHERE  valid_is = ''1'' AND audit_state = ''1'' AND entity_id = ' || v_entity_id;

          IF upper(substr(p_business_model, 1, 1)) = 'T' THEN
            v_sql := v_sql || '  AND treaty_no = ''' || v_treaty_no || '''';
          --ELSE
            --v_sql := v_sql || '  AND (risk_code = ''' || v_risk_code || ''' or risk_code is null or risk_code = '''') ';
          END IF;

          SELECT COUNT(1)
            INTO v_colref_count
            FROM user_tab_columns
           WHERE lower(table_name) = lower(v_related_table)
             AND lower(column_name) = lower('business_model');
          IF v_colref_count > 0 THEN
            v_sql := v_sql || ' AND business_model = ''' || substr(p_business_model, 0, 1) || ''' AND business_direction = ''' ||
                     substr(p_business_model, length(p_business_model), length(p_business_model) - 1) || '''';
          END IF;
          --v_sql := v_sql || ' order by risk_code desc LIMIT 1 ';
        END IF;

        EXECUTE IMMEDIATE v_sql
          INTO v_colmn_result;

       --获取对应字段值 end

        --为空值时取默认值
        IF v_colmn_result IS NULL
           AND v_default_value IS NOT NULL THEN
          v_colmn_result := v_default_value;
        END IF;

        --替换规则中对应 []
        v_rule        := regexp_replace(regexp_replace(cur_rule_factor.rule_factor_value, '\(|', ''), '\)', '');
        v_result_rule := REPLACE(v_result_rule, v_rule, to_char(v_colmn_result));

      END IF;

    END LOOP;

    --进行运算
    IF v_result_rule IS NOT NULL THEN
      IF v_count > 0 THEN
        v_sql := 'SELECT ' || v_result_rule ||' from dual';
        EXECUTE IMMEDIATE v_sql
          INTO v_result2;
        RETURN v_result2;
      ELSE
        RETURN v_result_rule;
      END IF;
    ELSE
      --RAISE notice '(%)', '结果为空';
      RETURN NULL;
    END IF;
commit;
  EXCEPTION
    WHEN OTHERS THEN
      dbms_output.put_line(SQLERRM);
      RETURN NULL;
  END func_rule_parsing;

  FUNCTION func_get_contractcode(p_entity_id      NUMBER,
                                 p_code_type      VARCHAR2,
                                 p_business_model VARCHAR2) RETURN VARCHAR2 IS

    v_result VARCHAR2(4000) := '';
    --G_COLUMN ROWTYPE;
  BEGIN
    -- 根据 CODE_BIT  依次拼接字段，组成编码 to_number(nvl(substr(CODE_BIT,0,instr(CODE_BIT,'-')-1),CODE_BIT))
    FOR g_column IN (SELECT d.match_type,
                            d.start_bit,
                            d.end_bit,
                            coalesce(d.default_value, '*') default_value,
                            d.script_sql,
                            d.column_ref,
                            d.column_type,
                            d.code_type,
                            coalesce(t.code_length, 1) code_length
                       FROM dm_conf_contract_codematchdef t
                       LEFT JOIN dm_conf_contract_code d
                         ON t.code_id = d.code_id
                        AND t.business_model = d.business_model
                        AND t.business_direction = d.business_direction
                      WHERE t.entity_id = p_entity_id
                        AND t.business_model = substr(p_business_model, 0, 1)
                        AND t.business_direction = substr(p_business_model, length(p_business_model), length(p_business_model) - 1)
                        AND t.code_type = p_code_type --  合同组/合同组合
                        AND t.audit_state = '1' --  审核通过
                        AND t.valid_is = '1' --  有效状态
                     --ORDER BY t.display_no 这种方式不对
                      ORDER BY to_number(nvl(substr(t.CODE_BIT,0,instr(t.CODE_BIT,'-')-1),t.CODE_BIT))) LOOP

      IF g_column.match_type = '1' THEN

        -- 取字段值
        v_result := v_result || 'rpad(COALESCE((' || g_column.column_ref || ' ),''' || g_column.default_value || '''), ' || g_column.code_length || ', ''' || g_column.default_value || ''')||';

      ELSIF g_column.match_type = '5' THEN
        -- 码值转换\特殊
        v_result := v_result || 'rpad(COALESCE((to_char(' || g_column.script_sql || ' )),''' || g_column.default_value || '''), ' || g_column.code_length || ', ''' || g_column.default_value ||
                    ''')||';

      ELSIF g_column.match_type = '3' THEN
        -- 截取长度
        IF g_column.column_type = '3' THEN
          v_result := v_result || 'rpad(COALESCE((' || 'substr((to_CHAR(' || g_column.column_ref || ', ''yyyymmdd'')),' || g_column.start_bit || ',' || g_column.end_bit || ')' || ' ),''' ||
                      g_column.default_value || ''' ), ' || g_column.code_length || ', ''' || g_column.default_value || ''')||';

        ELSE
          v_result := v_result || 'rpad(COALESCE( (' || 'substr(' || g_column.column_ref || ',' || g_column.start_bit || ',' || g_column.end_bit || ')' || ' ),''' || g_column.default_value || '''), ' ||
                      g_column.code_length || ', ''' || g_column.default_value || ''')||';
        END IF;
      ELSIF g_column.match_type = '4' THEN
        -- 预留字段
        v_result := v_result || 'rpad(COALESCE((' || '''' || g_column.default_value || '''' || ' ),''' || g_column.default_value || '''), ' || g_column.code_length || ', ''' || g_column.default_value ||
                    ''')||';
      END IF;

    END LOOP;

    -- 去除多余的 ||
    IF length(v_result) > 1 THEN
      v_result := substr(v_result, 0, length(v_result) - 2);
    ELSE
      v_result := NULL;
    END IF;

    RETURN v_result;

  EXCEPTION
    WHEN OTHERS THEN
      dbms_output.put_line(SQLCODE); -- 异常编号
      dbms_output.put_line(SQLERRM); -- 异常描述信息
      RETURN NULL;
  END func_get_contractcode;


  FUNCTION func_get_cmunitno(p_entity_id NUMBER,
                             p_year      VARCHAR2,
                             p_risk_code VARCHAR2,
                             p_param     VARCHAR2) RETURN VARCHAR2 IS

    v_unit_no   VARCHAR2(200); --计量单元编码
    v_totalnum  INTEGER; --总位数
    v_maxpadnum INTEGER := 11; --最大补0位数
  BEGIN
    --计量单元总位数设置
    BEGIN
      SELECT tt.total_code_num
        INTO v_totalnum
        FROM (SELECT total_code_num
                FROM dm_conf_contract_codedef
               WHERE entity_id = p_entity_id
                 AND code_type = 'MU') tt
       WHERE rownum = 1;

      IF v_totalnum = NULL THEN
        v_totalnum := 40;
      END IF;
    EXCEPTION
      WHEN OTHERS THEN
        v_totalnum := 40;
    END;
    --计量单元编码 以业务部门类型 + 三级业务部门编码（6位，尾部补0）
    BEGIN
      SELECT entity_type || rpad(entity_code, 6, '0')
        INTO v_unit_no
        FROM bpluser.bbs_entity
       WHERE entity_id = p_entity_id
         AND rownum = 1;
    EXCEPTION
      WHEN OTHERS THEN
        NULL;
    END;

    --计量单元 + 业务年度 + 险种编码 + 责任编码
    v_unit_no := v_unit_no || p_year || p_risk_code;

    --计量单元 + 动态参数（在设定长度内）
    v_unit_no := v_unit_no || coalesce(substr(p_param, 0, (v_totalnum - coalesce(length(v_unit_no), 0))), '');


    --增加序列号，不足位补0
    IF coalesce(length(v_unit_no), 0) < v_totalnum THEN
      --补位后不能超过总长度设值
      IF (v_totalnum - length(v_unit_no)) < v_maxpadnum THEN
        v_maxpadnum := (v_totalnum - length(v_unit_no));
      END IF;
      --补充序号
      v_unit_no := v_unit_no || lpad(to_char(dm_seq_cmunitno.nextval, 'FM999999999999999999'), v_maxpadnum, '0');
    END IF;
    RETURN v_unit_no;
  END func_get_cmunitno;


  FUNCTION func_get_ri_cmunitno(p_entity_id          NUMBER,
                                p_business_model     VARCHAR2,
                                p_business_direction VARCHAR2) RETURN VARCHAR2 IS

    v_unit_no   VARCHAR2(200); --计量单元编码
    v_maxpadnum INTEGER := 11; --最大补0位数
    v_seq number;
  BEGIN

    --计量单元编码： 业务单位 || 合约/临分标志 || 分入/分出标志 || 流水号(000001)
    BEGIN
      SELECT entity_code
        INTO v_unit_no
        FROM bpluser.bbs_entity
       WHERE entity_id = p_entity_id
         AND rownum = 1;
    EXCEPTION
      WHEN OTHERS THEN
        NULL;
    END;

    --业务单位 || 合约/临分标志 || 分入/分出标志
    v_unit_no := v_unit_no || p_business_model || p_business_direction;

    if p_business_model || p_business_direction = 'FI' then
          v_seq := dm_seq_fac_in_cmunitno.nextval;
        elsif p_business_model || p_business_direction = 'FO' then
          v_seq := dm_seq_fac_out_cmunitno.nextval;
        elsif p_business_model || p_business_direction = 'TI' then
          v_seq := dm_seq_treaty_in_cmunitno.nextval;
        elsif p_business_model || p_business_direction = 'TO' then
          v_seq := dm_seq_treaty_out_cmunitno.nextval;
        else
          v_seq := dm_seq_ri_cmunitno.nextval;
     end if;

    --增加序列号，不足位补0
    v_unit_no := v_unit_no || lpad(to_char(v_seq, 'FM999999999999999999'), v_maxpadnum, '0');

    RETURN v_unit_no;
  END func_get_ri_cmunitno;


  PROCEDURE proc_direct_profit_check(p_entity_id  NUMBER,
                                    p_year_month VARCHAR2,
                                    p_data_key   VARCHAR2) IS
  BEGIN

    -- 无评估方法对应的有效计量模型
    /*UPDATE dm_duct_direct_profit_unit t
       SET node_state = '2',
           fail_code = 'DM_004'
     WHERE data_key = p_data_key
       AND NOT EXISTS(SELECT 1
                        FROM qtcuser.qtc_conf_model_plan p
                        WHERE p.entity_id = p_entity_id
                        AND p.business_model = 'D'
                        AND p.business_direction = 'D'
                        AND p.evaluate_approach = t.evaluate
                        --AND p.audit_state = '1'
                        AND p.valid_is = '1');
    COMMIT;
    */

    -- 存在未配置数据
    UPDATE dm_duct_direct_profit_unit t
       SET node_state = '2',
           fail_code  = (CASE WHEN fail_code IS NOT NULL THEN fail_code||',' ELSE '' END) || 'DM_005'
     WHERE data_key = p_data_key
         --假设配置
       AND NOT EXISTS(SELECT 1
                FROM qtcuser.qtc_conf_model_plan cmp
                LEFT JOIN qtcuser.qtc_conf_quota cq
                  ON cmp.entity_id = cq.entity_id
                 AND cmp.model_def_id = cq.model_def_id
                 AND cq.dimension = 'G'
                 AND cq.audit_state = '1'
                 AND cq.valid_is = '1'
                 and cq.year_month = p_year_month --增加评估期条件
                LEFT JOIN qtcuser.qtc_conf_quota_def cqd
                  ON cq.quota_def_id = cqd.quota_def_id
               WHERE cmp.business_model = 'D'
                 AND cmp.business_direction = 'D'
                 AND cmp.evaluate_approach = t.evaluate
                 AND cq.dimension_value = t.loa_code
                 --AND cmp.audit_state = '1'
                 AND cmp.valid_is = '1'
                 AND cmp.entity_id = p_entity_id
                 AND upper(cqd.quota_code) IN ('QR002', 'QR003', 'BE013', 'QR008', 'QP001')
                 AND cq.quota_value IS NOT NULL);
    COMMIT;

    -- 存在未配置压力情景数据
    UPDATE dm_duct_direct_profit_unit t
       SET node_state = '2',
           fail_code  = (CASE WHEN fail_code IS NOT NULL THEN fail_code||',' ELSE '' END) || 'DM_006'
     WHERE data_key = p_data_key
           --压力情景配置查询，BBA时提示异常
       AND t.evaluate = 'BBA'
       AND NOT EXISTS (SELECT 1
                     FROM bpluser.bbs_conf_loa loa
                     LEFT JOIN dm_conf_bba_profit_loss_factor lf
                       ON loa.loa_id = lf.loa_id
                       AND lf.entity_id = loa.entity_id
                       AND lf.business_model = loa.business_model
                       AND lf.business_direction = loa.business_direction
                       AND lf.valid_is = '1'
                       AND lf.audit_state = '1'
                     WHERE loa.entity_id = t.entity_id
                       AND loa.loa_code = t.loa_code
                       AND loa.business_model = 'D'
                       AND loa.business_direction = 'D'
                       AND loa.valid_is = '1'
                       AND loa.audit_state = '1'
                       AND loa.evaluate_approach = t.evaluate
                       --AND year_month = p_year_month
                       AND lf.loss_rate IS NOT NULL);
    COMMIT;


    -- 存在未配置无风险曲线数据
    UPDATE dm_duct_direct_profit_unit t
       SET node_state = '2',
           fail_code  = (CASE WHEN fail_code IS NOT NULL THEN fail_code||',' ELSE '' END) ||'DM_007'
     WHERE data_key = p_data_key
           -- 无风险曲线,确认结果是确认已采用的
       AND(NOT EXISTS (SELECT 1
                  FROM qtcuser.qtc_conf_interest_rate t1
                 WHERE t1.entity_id = t.entity_id
                   AND t1.year_month = t.year_month
                   AND t1.confirm_is = '1'
                   AND t1.valid_is = '1'
                   AND t1.currency_code = dm_pack_buss_accountperiod.func_get_accountperiod_currency(t1.entity_id)--使用会计期间的BookI17账套
                   )
        );
    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN
      --dbms_output.put_line(SQLERRM);
      proc_buss_cmunit_dtl_log(p_data_key, 'proc_direct_profit_check', '2', SQLERRM);
  END proc_direct_profit_check;

  /**
  ** 初始化计算参数数据
  **
  ** p_method : SET 集合成本率
  **            UNIT  现金流
  **            QUOTA  假设数据
  **/
  PROCEDURE proc_direct_profit_param(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2,
                                     p_data_key   VARCHAR2,
                                     p_method     VARCHAR2) IS
    v_begin_date DATE;
    v_end_date   DATE;
    v_month_count NUMBER;
  BEGIN
    IF p_method = 'QUOTA' THEN

      DELETE FROM dm_duct_direct_profit_param t
      WHERE t.data_key = p_data_key
        AND param_code IN ('D005','QR002', 'QR003', 'BE013', 'QR008', 'QP001');
      COMMIT;

      --D005
      INSERT INTO dm_duct_direct_profit_param
            (profit_param_id,
             data_key,
             param_code,
             param_value,
             dev_month,
             buss_code)
        SELECT dm_seq_direct_profit_param.nextval,
               p_data_key,
               'D005',
               t4.interest_rate,-- 利率
               t4.dev_period,-- 发展期
               t2.dimension_value
          FROM qtcuser.qtc_conf_model_plan t1
          LEFT JOIN bpluser.bbs_conf_loa t7
            ON t7.entity_id = t1.entity_id
           AND t7.business_model = t1.business_model
           AND t7.business_direction = t1.business_direction
           AND t7.evaluate_approach = t1.evaluate_approach
           AND t7.valid_is = '1'
           AND t7.audit_state = '1'
          LEFT JOIN qtcuser.qtc_conf_quota t2
            ON t2.entity_id = t1.entity_id
           AND t2.model_def_id = t1.model_def_id
           AND t2.dimension_value = t7.loa_code
           AND t2.dimension = 'G'
           AND t2.valid_is = '1'
           AND t2.audit_state = '1'
           and t2.year_month = p_year_month --增加评估期条件
          LEFT JOIN qtcuser.qtc_conf_quota_def t3
            ON t3.quota_def_id = t2.quota_def_id
           AND t3.quota_code = 'QT002' -- 保费时点编码
          LEFT JOIN qtcuser.qtc_buss_interest_detail t4
            ON t4.pay_time = t2.quota_value
          LEFT JOIN qtcuser.qtc_buss_interest_main t5
            ON t5.interest_main_id = t4.interest_main_id
           AND t5.entity_id = t1.entity_id
           AND t5.currency_code = dm_pack_buss_accountperiod.func_get_accountperiod_currency(t1.entity_id)--使用会计期间的BookI17账套
           AND t5.interest_type = '1' --  利率类型： 1- 远期利率/期末利率； 2-初始利率 ； 3-加权初始利率
          LEFT JOIN qtcuser.qtc_conf_interest_rate t6
            ON t6.interest_rate_id = t5.interest_rate_id
           AND t6.currency_code = t5.currency_code
           AND t6.entity_id = t5.entity_id
           AND t6.year_month = t5.year_month -- 业务年月/评估年月
           AND t6.valid_is = '1'
           AND t6.audit_state = '1'
           AND t6.confirm_is = '1' -- 确认版本
         WHERE t1.business_model = 'D' -- 业务模型
           AND t1.business_direction = 'D' -- 业务方向
           AND t1.audit_state = '1'
           AND t1.valid_is = '1'
           AND t3.quota_code = 'QT002' -- 保费时点编码
           AND t6.entity_id = p_entity_id
           AND t6.year_month = p_year_month -- 业务年月/评估年月
         ;
      COMMIT;

      -- QR002,QR003,BE013,QR008 (预期赔付率%，非金融风险调整%,非跟单IACF%,预期维持费用率%)
      INSERT INTO dm_duct_direct_profit_param
        (profit_param_id,
         data_key,
         param_code,
         param_value,
         dev_month,
         buss_code)
      SELECT dm_seq_direct_profit_param.nextval,
             p_data_key,
             upper(t3.quota_code)quota_code,
             CAST(t2.quota_value AS NUMBER) AS quota_value,
             0,
             t2.dimension_value
        FROM qtcuser.qtc_conf_model_plan t1
        LEFT JOIN bpluser.bbs_conf_loa t7
          ON t7.business_model = t1.business_model
         AND t7.business_direction = t1.business_direction
         AND t7.evaluate_approach = t1.evaluate_approach
         AND t7.entity_id = t1.entity_id
         AND t7.valid_is = '1'
         AND t7.audit_state = '1'
        LEFT JOIN qtcuser.qtc_conf_quota t2
          ON t2.entity_id = t1.entity_id
         AND t2.model_def_id = t1.model_def_id
         AND t2.dimension_value = t7.loa_code
         AND t2.dimension = 'G'
         AND t2.valid_is = '1'
         AND t2.audit_state = '1'
         and t2.year_month = p_year_month --增加评估期条件
        LEFT JOIN qtcuser.qtc_conf_quota_def t3
          ON t3.quota_def_id = t2.quota_def_id
         AND t3.valid_is = '1'
         AND t3.audit_state = '1'
       WHERE t1.entity_id = p_entity_id
         AND t1.business_model = 'D'
         AND t1.business_direction = 'D'
         AND upper(t3.quota_code) IN ('QR002', 'QR003', 'BE013', 'QR008')
         AND EXISTS (SELECT 1
                FROM dm_duct_direct_profit_unit t5
               WHERE t5.data_key = p_data_key
                 AND t5.loa_code = t2.dimension_value
                 AND t5.evaluate = t1.evaluate_approach);

      COMMIT;

      -- QP001:(预期赔付模式%)
      INSERT INTO dm_duct_direct_profit_param
        (profit_param_id,
         data_key,
         param_code,
         param_value,
         dev_month,
         buss_code)
        SELECT dm_seq_direct_profit_param.nextval,
               p_data_key,
               upper(t3.quota_code)quota_code,
               CAST(t4.quota_value AS NUMBER) AS quota_value,
               t4.quota_period,
               t2.dimension_value
          FROM qtcuser.qtc_conf_model_plan t1
          LEFT JOIN bpluser.bbs_conf_loa t7
            ON t7.business_model = t1.business_model
           AND t7.business_direction = t1.business_direction
           AND t7.evaluate_approach = t1.evaluate_approach
           AND t7.entity_id = t1.entity_id
           AND t7.valid_is = '1'
           AND t7.audit_state = '1'
          LEFT JOIN qtcuser.qtc_conf_quota t2
            ON t2.entity_id = t1.entity_id
           AND t2.model_def_id = t1.model_def_id
           AND t2.dimension_value = t7.loa_code
           AND t2.dimension = 'G'
           AND t2.valid_is = '1'
           AND t2.audit_state = '1'
           and t2.year_month = p_year_month --增加评估期条件
          LEFT JOIN qtcuser.qtc_conf_quota_def t3
            ON t3.quota_def_id = t2.quota_def_id
           AND t3.valid_is = '1'
           AND t3.audit_state = '1'
          LEFT JOIN qtcuser.qtc_conf_quota_detail t4
            ON t4.quota_id = t2.quota_id
         WHERE t1.entity_id = p_entity_id
           AND t1.business_model = 'D'
           AND t1.business_direction = 'D'
           AND upper(t3.quota_code) = 'QP001'
           AND EXISTS (SELECT 1
                  FROM dm_duct_direct_profit_unit t5
                 WHERE t5.data_key = p_data_key
                   AND t5.evaluate = t1.evaluate_approach
                   AND t5.loa_code = t2.dimension_value
               );
      COMMIT;

    ELSIF p_method = 'UNIT' THEN
    -- 基本情景-计量单元发展期保费

      DELETE FROM dm_duct_direct_profit_amount t
       WHERE t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND EXISTS (SELECT 1
                FROM dm_duct_direct_profit_unit t5
               WHERE t5.cmunit_id = t.cmunit_id
                 AND t5.entity_id = p_entity_id
                 AND t5.year_month = p_year_month);
      COMMIT;

      /*-- 截至评估时间已实收保费
      INSERT INTO dm_duct_direct_profit_param
        (profit_param_id,
         data_key,
         param_code,
         param_value,
         dev_month,
         cmunit_id)
      SELECT dm_seq_direct_profit_param.nextval,
             t1.data_key,
             'U003',
             t1.premium,
             0,
             t1.cmunit_id
        FROM dm_duct_direct_profit_unit t1
       WHERE t1.data_key = p_data_key ;
      COMMIT;*/

      -- 月度已赚保费
      v_begin_date := to_date(p_year_month||'01','YYYYMMDD');
      SELECT MAX(ceil(months_between(expiry_date, GREATEST(effective_date,v_begin_date))))
        INTO v_month_count -- 未来发展期个数：评估年月 到 终保时间 相隔月份数
        FROM dm_duct_direct_profit_unit t
       WHERE t.data_key = p_data_key;
      -- 循环各个发展期保费占比
      FOR i IN 0 .. v_month_count LOOP

        IF i = 0 THEN
          v_begin_date := to_date(p_year_month||'01','YYYYMMDD');
        ELSE
          v_begin_date := add_months(v_begin_date, 1);
        END IF;
        v_end_date := last_day(v_begin_date);

        -- 月度保费：总保费 * 各月份天数/总承保天数
        INSERT INTO dm_duct_direct_profit_amount
          (cmunit_id,
           entity_id,
           year_month,
           data_key,
           param_code,
           dev_month,
           amount
           )
          SELECT cmunit_id,
                 p_entity_id,
                 p_year_month,
                 p_data_key,
                 'A002',
                 i,
                 (CASE WHEN policy_count_days = 0 THEN 0 ELSE ROUND(premium * (dev_month_days / policy_count_days),8)END)
            FROM (SELECT cmunit_id,
                         premium,
                         --fee_amount,
                         (LEAST(expiry_date, v_end_date) - (CASE WHEN i = 0 THEN effective_date ELSE v_begin_date END) + 1) AS dev_month_days,
                         (expiry_date - effective_date + 1) AS policy_count_days
                    FROM dm_duct_direct_profit_unit
                   WHERE data_key = p_data_key
                     AND effective_date <= v_end_date
                     AND expiry_date >= v_begin_date
                   ) t;
        COMMIT;
      END LOOP;

    ELSIF p_method = 'SET' THEN

      DELETE FROM dm_duct_direct_profit_param t
      WHERE t.data_key = p_data_key
        AND param_code IN ('S001','S002');
      COMMIT;

      INSERT INTO dm_duct_direct_profit_param
          (profit_param_id,
           data_key,
           param_code,
           param_value,
           dev_month,
           buss_code)
      SELECT dm_seq_direct_profit_param.nextval,
             p_data_key,
             'S001',
             t.premium,
             0,
             t.set_no
        FROM (SELECT t1.set_no,
                     SUM(t1.premium) premium
                FROM dm_duct_direct_profit_unit t1
               WHERE t1.data_key = p_data_key
                 AND t1.evaluate = 'PAA'
               GROUP BY t1.set_no
              ) t;
      COMMIT;

      INSERT INTO dm_duct_direct_profit_param
          (profit_param_id,
           data_key,
           param_code,
           param_value,
           dev_month,
           buss_code)
      SELECT dm_seq_direct_profit_param.nextval,
             p_data_key,
             'S002',
             t.fee_amount,
             0,
             t.set_no
        FROM (SELECT t1.set_no,
                     SUM(t1.fee_amount) fee_amount
                FROM dm_duct_direct_profit_unit t1
               WHERE t1.data_key = p_data_key
                 AND t1.evaluate = 'PAA'
               GROUP BY t1.set_no
             ) t;
      COMMIT;

    END IF;

  EXCEPTION
    WHEN OTHERS THEN
      --dbms_output.put_line(SQLERRM);
      proc_buss_cmunit_dtl_log(p_data_key, 'proc_direct_profit_param', '2', p_method||'-'||SQLERRM);
  END proc_direct_profit_param;

  PROCEDURE proc_direct_judge_paa(p_entity_id  NUMBER,
                                  p_year_month VARCHAR2,
                                  p_data_key   VARCHAR2) IS
  BEGIN

    --跟單IACF率
    MERGE INTO dm_duct_direct_profit_unit t
    USING (SELECT t1.cmunit_id,
                   (CASE WHEN coalesce(s1.param_value,0) <> 0 THEN ((coalesce(s2.param_value,0))/(coalesce(s1.param_value,0))) ELSE 0 END) iacf_rate
              FROM dm_duct_direct_profit_unit t1
              LEFT JOIN dm_duct_direct_profit_param s1
                ON s1.data_key = t1.data_key
               AND s1.buss_code = t1.set_no
               AND s1.param_code = 'S001' -- set_premium
              LEFT JOIN dm_duct_direct_profit_param s2
                ON s2.data_key = t1.data_key
               AND s2.buss_code = t1.set_no
               AND s2.param_code = 'S002' -- set_fee_amount
             WHERE t1.data_key = p_data_key
               AND t1.entity_id = p_entity_id
               AND t1.year_month = p_year_month
               AND t1.evaluate = 'PAA'
             ) c
    ON (t.cmunit_id = c.cmunit_id)
    WHEN MATCHED THEN
      UPDATE
         SET t.iacf_rate = c.iacf_rate
       WHERE t.data_key = p_data_key
         AND t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND t.evaluate = 'PAA';
    COMMIT;

    --集合成本率
    MERGE INTO dm_duct_direct_profit_unit t
    USING (SELECT t1.cmunit_id,
                   --(t1.premium * p2.param_value) niacf_amount,
                   (coalesce(t1.iacf_rate,0) + (coalesce(p2.param_value,0))+((coalesce(p4.param_value,0)) + (coalesce(p3.param_value,0)))*(1 + (coalesce(p8.param_value,0))))cost_rate
                   --iacf_rate + niacf_rate + (loss_rate + mtc_rate)*(1 + ra_rate)
              FROM dm_duct_direct_profit_unit t1
              LEFT JOIN dm_duct_direct_profit_param p2
                ON p2.data_key = t1.data_key
               AND p2.buss_code = t1.loa_code
               AND p2.dev_month = 0
               AND p2.param_code = 'QR002' -- niacf_rate 非跟单IACF费用比例
              LEFT JOIN dm_duct_direct_profit_param p3
                ON p3.data_key = t1.data_key
               AND p3.buss_code = t1.loa_code
               AND p3.dev_month = 0
               AND p3.param_code = 'QR003' -- mtc_rate 预期维持费用率
              LEFT JOIN dm_duct_direct_profit_param p4
                ON p4.data_key = t1.data_key
               AND p4.buss_code = t1.loa_code
               AND p4.dev_month = 0
               AND p4.param_code = 'BE013' --loss_rate预期赔付率
              LEFT JOIN dm_duct_direct_profit_param p8
                ON p8.data_key = t1.data_key
               AND p8.buss_code = t1.loa_code
               AND p8.dev_month = 0
               AND p8.param_code = 'QR008' -- ra_rate非金融风险调整
             WHERE t1.data_key = p_data_key
               AND t1.entity_id = p_entity_id
               AND t1.year_month = p_year_month
               AND t1.evaluate = 'PAA'
             ) c
    ON (t.cmunit_id = c.cmunit_id)
    WHEN MATCHED THEN
      UPDATE
         SET --t.niacf_amount = c.niacf_amount,
             t.cost_rate = c.cost_rate
       WHERE t.data_key = p_data_key
         AND t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND t.evaluate = 'PAA';
    COMMIT;

    --预期维持费用现金流现值, 跟单IACF费用,非跟单IACF费用
    MERGE INTO dm_duct_direct_profit_unit t
    USING (SELECT t1.cmunit_id,
                  SUM(t2.amount * d5.param_value) AS premium_future, --premium*dis_rate
                  SUM(t2.amount * p3.param_value * d5.param_value) AS mtc_amount, --premium*mtc_rate*dis_rate
                  SUM(t2.amount * t1.iacf_rate * d5.param_value) AS iacf_amount, --premium*iacf_rate*dis_rate
                  SUM(t2.amount * p2.param_value * d5.param_value) AS niacf_amount --premium*niacf_rate*dis_rate
             FROM dm_duct_direct_profit_unit t1
             LEFT JOIN dm_duct_direct_profit_amount t2
               ON t2.cmunit_id = t1.cmunit_id
              AND t2.entity_id = t1.entity_id
              AND t2.year_month = t1.year_month
              AND t2.param_code = 'A002' --premium
             LEFT JOIN dm_duct_direct_profit_param p2
               ON p2.data_key = t1.data_key
              AND p2.buss_code = t1.loa_code
              AND p2.dev_month = 0
              AND p2.param_code = 'QR002' -- niacf_rate 非跟单IACF费用比例
             LEFT JOIN dm_duct_direct_profit_param p3
               ON p3.data_key = t1.data_key
              AND p3.buss_code = t1.loa_code
              AND p3.dev_month = 0
              AND p3.param_code = 'QR003' -- mtc_rate 预期维持费用率
             LEFT JOIN dm_duct_direct_profit_param d5
               ON d5.data_key = t1.data_key
              AND d5.dev_month = (t2.dev_month + 1)
              AND d5.buss_code = t1.loa_code
              AND d5.param_code = 'D005' --dis_rate
            WHERE t1.data_key = p_data_key
              AND t1.entity_id = p_entity_id
              AND t1.year_month = p_year_month
              AND t1.evaluate = 'PAA'
             GROUP BY t1.cmunit_id
           ) c
    ON (t.cmunit_id = c.cmunit_id)
    WHEN MATCHED THEN
      UPDATE
         SET t.mtc_amount   = c.mtc_amount,
             t.iacf_amount  = c.iacf_amount,
             t.niacf_amount = c.niacf_amount,
             t.premium_future = c.premium_future
       WHERE t.data_key = p_data_key
         AND t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND t.evaluate = 'PAA';
    COMMIT;


    --预期赔付现金流现值
    MERGE INTO dm_duct_direct_profit_unit t
    USING (SELECT t1.cmunit_id,
                   --t1.loa_code,
                   --(t2.dev_month+q1.dev_month) AS dev_month,
                   SUM(t2.amount * p4.param_value * q1.param_value*d5.param_value) AS loss_amount --premium*loss_rate*loss_pay_rate*dis_rate
              FROM dm_duct_direct_profit_unit t1
              LEFT JOIN dm_duct_direct_profit_amount t2
                ON t2.cmunit_id = t1.cmunit_id
              AND t2.entity_id = t1.entity_id
              AND t2.year_month = t1.year_month
               AND t2.param_code = 'A002' --premium
              LEFT JOIN dm_duct_direct_profit_param p4
                ON p4.data_key = t1.data_key
               AND p4.buss_code = t1.loa_code
               AND p4.dev_month = 0
               AND p4.param_code = 'BE013' --loss_rate预期赔付率
              LEFT JOIN dm_duct_direct_profit_param q1
                ON q1.data_key = t1.data_key
               AND q1.buss_code = t1.loa_code
               AND q1.param_code = 'QP001' --loss_pay_rate
              LEFT JOIN dm_duct_direct_profit_param d5
                ON d5.data_key = t1.data_key
               AND d5.dev_month = (t2.dev_month+q1.dev_month+1)
               AND d5.buss_code = t1.loa_code
               AND d5.param_code = 'D005' --dis_rate
             WHERE t1.data_key = p_data_key
               AND t1.entity_id = p_entity_id
               AND t1.year_month = p_year_month
               AND t1.evaluate = 'PAA'
             GROUP BY t1.cmunit_id--,t1.loa_code,(t2.dev_month+q1.dev_month)
           ) c
    ON (t.cmunit_id = c.cmunit_id)
    WHEN MATCHED THEN
      UPDATE
         SET t.loss_amount = c.loss_amount
       WHERE t.data_key = p_data_key
         AND t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND t.evaluate = 'PAA';
    COMMIT;

    --成本率
    MERGE INTO dm_duct_direct_profit_unit t
    USING (SELECT t1.entity_id,
                  t1.year_month,
                  t1.set_no,
                  SUM(t1.premium_future) premium,
                  SUM((coalesce(t1.loss_amount,0) + coalesce(t1.mtc_amount,0))*(1 + coalesce(p8.param_value,0)) + coalesce(t1.iacf_amount,0) + coalesce(t1.niacf_amount,0)) cost_amount
                    FROM dm_duct_direct_profit_unit t1
                     LEFT JOIN dm_duct_direct_profit_param p8
                       ON p8.data_key = t1.data_key
                      AND p8.buss_code = t1.loa_code
                      AND p8.dev_month = 0
                      AND p8.param_code = 'QR008' -- ra_rate非金融风险调整
                    WHERE t1.data_key = p_data_key
                      AND t1.entity_id = p_entity_id
                      AND t1.year_month = p_year_month
                      AND t1.evaluate = 'PAA'
             GROUP BY t1.entity_id,t1.year_month,t1.set_no
           ) c
    ON (t.entity_id = c.entity_id AND t.year_month = c.year_month AND t.set_no = c.set_no)
    WHEN MATCHED THEN
      UPDATE
         SET t.cost_rate2 = (CASE WHEN c.premium = 0 THEN 0 ELSE c.cost_amount / c.premium END)
       WHERE t.data_key = p_data_key
         AND t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND t.evaluate = 'PAA';
    COMMIT;

      -- PAA更新盈亏标识
      MERGE INTO dm_duct_direct_profit_unit t
      USING (SELECT cmunit_id,
                    (CASE WHEN (coalesce(t1.premium,0) = 0 OR coalesce(t1.cost_rate,0) = 0 ) THEN 'O' WHEN t1.cost_rate <= 1 THEN 'P' WHEN t1.cost_rate2 > 1 THEN 'L' ELSE 'P' END) judge_rslt
               FROM dm_duct_direct_profit_unit t1
              WHERE t1.data_key = p_data_key
                AND t1.entity_id = p_entity_id
                AND t1.year_month = p_year_month
                AND t1.evaluate = 'PAA'
             ) c
      ON (t.cmunit_id = c.cmunit_id)
      WHEN MATCHED THEN
        UPDATE
           SET t.judge_rslt = c.judge_rslt,
               t.judge_date = (CASE
                                WHEN c.judge_rslt = 'L' THEN
                                 last_day(to_date(p_year_month, 'yyyymm'))
                                ELSE
                                 NULL
                              END),-- 判定结果为亏损，设置判定时间
               t.node_state = (CASE
                                WHEN c.judge_rslt IS NULL THEN
                                 '2'
                                ELSE
                                 '1'
                              END),
               t.fail_code = (CASE
                              WHEN c.judge_rslt IS NULL THEN
                               'DM_001'
                              ELSE
                               NULL
                            END)
         WHERE t.data_key = p_data_key
           AND t.entity_id = p_entity_id
           AND t.year_month = p_year_month
           AND t.evaluate = 'PAA';
      COMMIT;

    proc_buss_cmunit_dtl_log(p_data_key, 'proc_direct_judge_paa', '1', NULL);
  EXCEPTION
    WHEN OTHERS THEN
      --dbms_output.put_line(SQLERRM);
      proc_buss_cmunit_dtl_log(p_data_key, 'proc_direct_judge_paa', '2', SQLERRM);
  END proc_direct_judge_paa;

  PROCEDURE proc_direct_judge_bba(p_entity_id  NUMBER,
                                  p_year_month VARCHAR2,
                                  p_data_key   VARCHAR2) IS
  BEGIN

    --跟單IACF率
    MERGE INTO dm_duct_direct_profit_unit t
    USING (SELECT t1.cmunit_id,
                   (CASE WHEN coalesce(t1.premium,0) <> 0 THEN ((coalesce(t1.fee_amount,0))/(coalesce(t1.premium,0))) ELSE 0 END) iacf_rate
              FROM dm_duct_direct_profit_unit t1
             WHERE t1.data_key = p_data_key
               AND t1.entity_id = p_entity_id
               AND t1.year_month = p_year_month
               AND t1.evaluate = 'BBA'
             ) c
    ON (t.cmunit_id = c.cmunit_id)
    WHEN MATCHED THEN
      UPDATE
         SET t.iacf_rate = c.iacf_rate
       WHERE t.data_key = p_data_key
         AND t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND t.evaluate = 'BBA';
    COMMIT;

    --预期维持费用现金流现值, 跟单IACF费用,非跟单IACF费用
    MERGE INTO dm_duct_direct_profit_unit t
    USING (SELECT t1.cmunit_id,
                  SUM(t2.amount * d5.param_value) AS premium_future, --premium*dis_rate
                  SUM(t2.amount * p3.param_value * d5.param_value) AS mtc_amount, --premium*mtc_rate*dis_rate
                  SUM(t2.amount * t1.iacf_rate * d5.param_value) AS iacf_amount, --premium*iacf_rate*dis_rate
                  SUM(t2.amount * p2.param_value * d5.param_value) AS niacf_amount, --premium*niacf_rate*dis_ratepremium_future
                  SUM(t2.amount * p3.param_value * (1 + coalesce(f.maintenance_rate, 0.0)) * d5.param_value) AS mtc_amount2 --premium*mtc_rate*(1+X)*dis_rate
             FROM dm_duct_direct_profit_unit t1
             LEFT JOIN dm_duct_direct_profit_amount t2
               ON t2.data_key = p_data_key
              and t2.cmunit_id = t1.cmunit_id
              AND t2.entity_id = t1.entity_id
              AND t2.year_month = t1.year_month
              AND t2.param_code = 'A002' --premium
             LEFT JOIN dm_duct_direct_profit_param p2
               ON p2.data_key = t1.data_key
              AND p2.buss_code = t1.loa_code
              AND p2.dev_month = 0
              AND p2.param_code = 'QR002' -- niacf_rate 非跟单IACF费用比例
             LEFT JOIN dm_duct_direct_profit_param p3
               ON p3.data_key = t1.data_key
              AND p3.buss_code = t1.loa_code
              AND p3.dev_month = 0
              AND p3.param_code = 'QR003' -- mtc_rate 预期维持费用率
             LEFT JOIN dm_duct_direct_profit_param d5
               ON d5.data_key = t1.data_key
              AND d5.dev_month = (t2.dev_month + 1)
              AND d5.buss_code = t1.loa_code
              AND d5.param_code = 'D005' --dis_rate
             LEFT JOIN bpluser.bbs_conf_loa l
               ON l.entity_id = t1.entity_id
              AND l.loa_code = t1.loa_code
              AND l.business_model = 'D'
              AND l.business_direction = 'D'
              AND l.valid_is = '1'
              AND l.audit_state = '1'
             LEFT JOIN dm_conf_bba_profit_loss_factor f
               ON f.loa_id = l.loa_id
              AND f.business_model= 'D'
              AND f.business_direction = 'D'
              AND f.valid_is = '1'
              AND f.audit_state = '1'
            WHERE t1.data_key = p_data_key
              AND t1.entity_id = p_entity_id
              AND t1.year_month = p_year_month
              AND t1.evaluate = 'BBA'
            GROUP BY t1.cmunit_id) c
    ON (t.cmunit_id = c.cmunit_id)
    WHEN MATCHED THEN
      UPDATE
         SET t.mtc_amount   = c.mtc_amount,
             t.iacf_amount  = c.iacf_amount,
             t.niacf_amount = c.niacf_amount,
             t.mtc_amount2  = c.mtc_amount2,
             t.premium_future = c.premium_future
       WHERE t.data_key = p_data_key
         AND t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND t.evaluate = 'BBA';
    COMMIT;

    --预期赔付现金流现值
    MERGE INTO dm_duct_direct_profit_unit t
    USING (SELECT t1.cmunit_id,
                  SUM(t2.amount * p4.param_value * q1.param_value * d5.param_value) AS loss_amount, --premium*loss_rate*loss_pay_rate*dis_rate
                  SUM(t2.amount * p4.param_value *(1 + coalesce(f.loss_rate, 0.0)) * q1.param_value * d5.param_value) AS loss_amount2 --premium*loss_rate*(1+X)*loss_pay_rate*dis_rate
             FROM dm_duct_direct_profit_unit t1
             LEFT JOIN dm_duct_direct_profit_amount t2
               ON t2.data_key = p_data_key
              and t2.cmunit_id = t1.cmunit_id
              AND t2.entity_id = t1.entity_id
              AND t2.year_month = t1.year_month
              AND t2.param_code = 'A002' --premium
             LEFT JOIN dm_duct_direct_profit_param p4
               ON p4.data_key = t1.data_key
              AND p4.buss_code = t1.loa_code
              AND p4.dev_month = 0
              AND p4.param_code = 'BE013' --loss_rate预期赔付率
             LEFT JOIN dm_duct_direct_profit_param q1
               ON q1.data_key = t1.data_key
              AND q1.buss_code = t1.loa_code
              AND q1.param_code = 'QP001' --loss_pay_rate
             LEFT JOIN dm_duct_direct_profit_param d5
               ON d5.data_key = t2.data_key
              AND d5.dev_month = (t2.dev_month + q1.dev_month + 1)
              AND d5.buss_code = t1.loa_code
              AND d5.param_code = 'D005' --dis_rate
             LEFT JOIN bpluser.bbs_conf_loa l
               ON l.entity_id = t1.entity_id
              AND l.loa_code = t1.loa_code
              AND l.business_model = 'D'
              AND l.business_direction = 'D'
              AND l.valid_is = '1'
              AND l.audit_state = '1'
             LEFT JOIN dm_conf_bba_profit_loss_factor f
               ON f.loa_id = l.loa_id
              AND f.business_model= 'D'
              AND f.business_direction = 'D'
              AND f.valid_is = '1'
              AND f.audit_state = '1'
            WHERE t1.data_key = p_data_key
              AND t1.entity_id = p_entity_id
              AND t1.year_month = p_year_month
              AND t1.evaluate = 'BBA'
            GROUP BY t1.cmunit_id --,t1.loa_code,(t2.dev_month+q1.dev_month)
           ) c
    ON (t.cmunit_id = c.cmunit_id)
    WHEN MATCHED THEN
      UPDATE
         SET t.loss_amount = c.loss_amount,
         t.loss_amount2 = c.loss_amount2
       WHERE t.data_key = p_data_key
         AND t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND t.evaluate = 'BBA';
    COMMIT;

    --成本率
    MERGE INTO dm_duct_direct_profit_unit t
    USING (SELECT t1.cmunit_id,
                  t1.premium_future premium,
                  ((coalesce(t1.loss_amount,0) + coalesce(t1.mtc_amount,0))*(1 + coalesce(p8.param_value,0)) + coalesce(t1.iacf_amount,0) + t1.niacf_amount) cost_amount,
                  ((coalesce(t1.loss_amount2,0) + coalesce(t1.mtc_amount2,0))*(1 + coalesce(p8.param_value,0)) + t1.iacf_amount + coalesce(t1.niacf_amount,0)) cost_amount2
                    FROM dm_duct_direct_profit_unit t1
                        LEFT JOIN dm_duct_direct_profit_param p8
                          ON p8.data_key = t1.data_key
                         AND p8.buss_code = t1.loa_code
                         AND p8.dev_month = 0
                         AND p8.param_code = 'QR008' -- ra_rate非金融风险调整
                       WHERE t1.data_key = p_data_key
                         AND t1.entity_id = p_entity_id
                         AND t1.year_month = p_year_month
               AND t1.evaluate = 'BBA'
           ) c
    ON (t.cmunit_id = c.cmunit_id)
    WHEN MATCHED THEN
      UPDATE
         SET t.cost_rate = (CASE WHEN coalesce(c.premium,0) = 0 THEN 0 ELSE coalesce(c.cost_amount,0) / coalesce(c.premium,0) END),
             t.cost_rate2 = (CASE WHEN coalesce(c.premium,0) = 0 THEN 0 ELSE coalesce(c.cost_amount2,0) / coalesce(c.premium,0) END)
       WHERE t.data_key = p_data_key
         AND t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND t.evaluate = 'BBA';
    COMMIT;

    -- 更新盈亏标识
    MERGE INTO dm_duct_direct_profit_unit t
    USING (SELECT cmunit_id,
                  (CASE WHEN (coalesce(t1.premium,0) = 0 OR coalesce(t1.cost_rate,0) = 0 ) THEN 'O' WHEN t1.cost_rate > 1 THEN 'L' WHEN t1.cost_rate2 > 1 THEN 'O' ELSE 'P' END) judge_rslt
             FROM dm_duct_direct_profit_unit t1
            WHERE t1.data_key = p_data_key
              AND t1.entity_id = p_entity_id
              AND t1.year_month = p_year_month
              AND t1.evaluate = 'BBA'
           ) c
    ON (t.cmunit_id = c.cmunit_id)
    WHEN MATCHED THEN
      UPDATE
         SET t.judge_rslt = c.judge_rslt,
             t.judge_date = (CASE WHEN c.judge_rslt = 'L' THEN
                               last_day(to_date(p_year_month, 'yyyymm'))
                              ELSE NULL
                            END),-- 判定结果为亏损，设置判定时间
             t.node_state = (CASE WHEN c.judge_rslt IS NULL THEN
                               '2'
                              ELSE
                               '1'
                            END) ,
             t.fail_code = (CASE WHEN c.judge_rslt IS NULL THEN
                             'DM_001'
                            ELSE
                             NULL
                          END)
       WHERE t.data_key = p_data_key
         AND t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND t.evaluate = 'BBA';
    COMMIT;

    proc_buss_cmunit_dtl_log(p_data_key, 'proc_direct_judge_bba', '1', NULL);
  EXCEPTION
    WHEN OTHERS THEN
      --dbms_output.put_line(SQLERRM);
      proc_buss_cmunit_dtl_log(p_data_key, 'proc_direct_judge_bba', '2', SQLERRM);
  END proc_direct_judge_bba;

  PROCEDURE proc_fac_profit_param(p_entity_id  NUMBER,
                                  p_year_month VARCHAR2,
                                  p_data_key   VARCHAR2,
                                  p_method     VARCHAR2) IS

    v_data_key      VARCHAR2(200);
    v_entity_id     NUMBER;
    v_evaluate_date DATE; -- 评估时间
    v_dis_factor    NUMBER; -- 折现因子
    v_n             INTEGER;
    v_m_days        INTEGER; --月份天数
    v_m_premium     NUMBER;
    --cur_m           record;
    --cur_g           record;
    --cur_f           record;
    v_s016 NUMBER; -- 未来保费现值
    v_s017 NUMBER; -- 未来现金流现值

    v_p005 NUMBER; --
    v_p001 NUMBER; -- 预期赔付率
    v_p009 NUMBER; -- 预期赔付模式
    v_p002 NUMBER; -- 预期维持费用比例
    v_p010 NUMBER; -- 压力情景预期赔付率
    v_p012 NUMBER; -- 压力情景预期维持费用率
    v_p004 NUMBER; -- 非金融风险调整_未到期

    v_u016 NUMBER; -- 未来保费现值
    v_u017 NUMBER; -- 未来现金流现值

    v_uy1 NUMBER; -- 未来预期赔付现值
    v_uy2 NUMBER; -- 压力情景未来预期赔付现值
    v_uw1 NUMBER; -- 未来维护费用现值
    v_uw2 NUMBER; -- 压力情景未来维护费用现值


  BEGIN

    IF p_method = 'PAA-A' THEN

      -- 预期赔付率-P001

      -- 不带发展期配置数据:(预期赔付率%，非金融风险调整%,非跟单IACF%,预期维持费用率%)
      INSERT INTO dm_duct_profit_param_value
        (profit_param_value_id,
         data_key,
         param_code,
         param_value,
         dev_month,
         buss_code)
        SELECT dm_seq_profit_unit.nextval,
               v_data_key,
               t2.quota_code,
               nvl(t2.quota_value, 0),
               0,
               t1.portfolio_no
          FROM (SELECT LOA_CODE,
                       portfolio_no
                  FROM dm_duct_profit_unit
                 WHERE data_key = v_data_key
                   AND node_state = '1'
                 GROUP BY LOA_CODE,
                          portfolio_no) t1,
               (SELECT f2.dimension_value LOA_CODE,
                       f1.quota_code,
                       CAST(f2.quota_value AS NUMBER) AS quota_value
                  FROM qtcuser.qtc_conf_quota_def f1,
                       qtcuser.qtc_conf_quota     f2
                 WHERE f1.quota_def_id = f2.quota_def_id
                   AND f2.entity_id = v_entity_id
                   AND f2.dimension = 'G'
                   and f2.year_month = p_year_month --增加评估期条件
                   AND upper(f1.quota_code) IN (upper('BE013'), upper('QR008'), upper('QR002'), upper('QR003'))) t2
         WHERE t2.LOA_CODE = t1.LOA_CODE;

      -- 带发展期配置数据:(预期赔付模式%)
      INSERT INTO dm_duct_profit_param_value
        (profit_param_value_id,
         data_key,
         param_code,
         param_value,
         dev_month,
         buss_code)
        SELECT dm_seq_profit_unit.nextval,
               v_data_key,
               t2.quota_code,
               coalesce(t2.quota_value, 0.0),
               t2.quota_period,
               t1.portfolio_no
          FROM (SELECT LOA_CODE,
                       portfolio_no
                  FROM dm_duct_profit_unit
                 WHERE data_key = v_data_key
                   AND node_state = '1'
                 GROUP BY LOA_CODE,
                          portfolio_no) t1,
               (SELECT f2.dimension_value LOA_CODE,
                       f1.quota_code,
                       f3.quota_period,
                       CAST(f3.quota_value AS NUMBER) AS quota_value
                  FROM qtcuser.qtc_conf_quota_def    f1,
                       qtcuser.qtc_conf_quota        f2,
                       qtcuser.qtc_conf_quota_detail f3
                 WHERE f1.quota_def_id = f2.quota_def_id
                   AND f2.quota_id = f3.quota_id
                   AND f2.entity_id = v_entity_id
                   AND f2.dimension_value = 'G'
                   and f2.year_month = p_year_month --增加评估期条件
                   AND upper(f1.quota_code) IN (upper('QP001'))) t2
         WHERE t2.LOA_CODE = t1.LOA_CODE;



    -- 基本情景-计量单元发展期保费
    ELSIF p_method = 'UNIT' THEN
      -- 截至评估时间已实收保费
      INSERT INTO dm_duct_profit_param_value
        (profit_param_value_id,
         data_key,
         param_code,
         param_value,
         dev_month,
         buss_code)
        SELECT dm_seq_profit_unit.nextval,
               v_data_key,
               'U008',
               coalesce(t2.amount, t1.premium), --todo 实收保费暂取不到值，实际取实收和过责未收的保费，若均无取缴费计划保费
               0,
               t1.unit_no
          FROM dm_duct_profit_unit t1,
               (SELECT risk_code,
                       policy_no,
                       endorse_no,
                       SUM(amount) AS amount
                  FROM dm_acc_payment
                 WHERE expenses_type_code = 'R10' -- 实收保费
                   AND actual_payment_date < add_months(v_evaluate_date, 1)
                 GROUP BY risk_code,
                          policy_no,
                          endorse_no) t2
         WHERE t1.policy_no = t2.policy_no
           AND t1.endorse_no = t2.endorse_no
           AND t1.risk_code = t2.risk_code
           AND t1.data_key = v_data_key
           AND t1.node_state = '1';

      -- 计量单元未来保费现金流
      FOR cur_g IN (SELECT t1.unit_no,
                           trunc(t2.est_payment_date, 'MM') AS paydate,
                           SUM(t2.est_payment_amount) AS payamout
                      FROM dm_duct_profit_unit t1,
                           dm_policy_payment_plan t2
                     WHERE t1.policy_no = t2.policy_no
                       AND t1.endorse_no = t2.endorse_no
                       AND t2.est_payment_date >= v_evaluate_date
                       AND t1.data_key = v_data_key
                       AND t1.node_state = '1'
                     GROUP BY t1.unit_no,
                              trunc(t2.est_payment_date, 'MM')
                     ORDER BY trunc(t2.est_payment_date, 'MM')) LOOP

        SELECT (extract(YEAR FROM cur_g.paydate) - extract(YEAR FROM v_evaluate_date)) * 12 + (extract(MONTH FROM cur_g.paydate) - extract(MONTH FROM v_evaluate_date)) INTO v_n FROM dual;

        -- 首个月份与累计实收保费累加
        IF v_n = 0 THEN
          UPDATE dm_duct_profit_param_value
             SET param_value = param_value + cur_g.payamout
           WHERE data_key = v_data_key
             AND param_code = 'U008'
             AND buss_code = cur_g.unit_no
             AND dev_month = 0;
          --else
          --                     insert into dm_duct_profit_param_value(profit_param_value_id, data_key, param_code,
          --                                                            param_value, dev_month, buss_code)
          --                     values (dm_seq_profit_unit.nextval, v_data_key, 'U008', cur_g.payamout, v_n, cur_g.unit_no);
        END IF;
      END LOOP;

      -- 月度已赚保费 - 计量单元 ：总保费 * 各月份天数/总承保天数
      FOR cur_m IN (SELECT unit_no,
                           effective_date,
                           expiry_date,
                           premium
                      FROM dm_duct_profit_unit
                     WHERE data_key = v_data_key
                       AND node_state = '1'
                    --AND judge_rslt = 'L'
                    ) LOOP
        -- 发展期个数：评估年月 到 终保时间 相隔月份数
        v_n := (extract(YEAR FROM cur_m.expiry_date) - extract(YEAR FROM v_evaluate_date)) * 12 + (extract(MONTH FROM cur_m.expiry_date) - extract(MONTH FROM v_evaluate_date));
        -- 循环各个发展期保费占比
        FOR i IN 0 .. v_n LOOP
          -- 当前月份天数
          --v_m_days := extract(day fromtrunc(month from  (cur_m.effective_date + (i + 1) * interval '1 month')) - interval '1 day');
          --获取当月天数
          v_m_days := to_char(last_day(add_months(cur_m.effective_date, (i + 1))), 'dd');

          IF i = 0 THEN
            v_m_days := v_m_days - extract(DAY FROM cur_m.effective_date) + 1;
          END IF;
          IF i = v_n THEN
            v_m_days := extract(DAY FROM cur_m.expiry_date);
          END IF;
          -- 月度保费
          v_m_premium := cur_m.premium * (v_m_days / (extract(DAY FROM CAST(cur_m.expiry_date AS TIMESTAMP) - CAST(cur_m.effective_date AS TIMESTAMP)) + 1));
          INSERT INTO dm_duct_profit_param_value
            (profit_param_value_id,
             data_key,
             param_code,
             param_value,
             dev_month,
             buss_code)
          VALUES
            (dm_seq_profit_unit.nextval,
             v_data_key,
             'U009',
             v_m_premium,
             i,
             cur_m.unit_no);

        END LOOP;
      END LOOP;

    END IF;


    IF p_method = 'PAA-B' THEN
      -- 预期保费现金流 - 集合
      FOR cur_m IN (SELECT risk_code,
                           portfolio_no,
                           set_no,
                           SUM(premium) AS premium,
                           CASE
                             WHEN SUM(premium) = 0 THEN
                              0.0
                             ELSE
                              SUM(fee_amount) / SUM(premium)
                           END AS chargerate
                      FROM dm_duct_profit_unit
                     WHERE data_key = v_data_key
                       AND node_state = '1'
                    --AND judge_rslt is null  --judge_rslt = 'L'
                     GROUP BY risk_code,
                              portfolio_no,
                              set_no) LOOP



        -- 非跟单IACF费用比例 , 非金融风险调整, 预期赔付率, 预期维持费用率
        SELECT SUM(CASE
                     WHEN upper(param_code) = upper('QR002') THEN
                      coalesce(param_value, 0.0)
                   END),
               SUM(CASE
                     WHEN upper(param_code) = upper('QR008') THEN
                      coalesce(param_value, 0.0)
                   END),
               SUM(CASE
                     WHEN upper(param_code) = upper('BE013') THEN
                      coalesce(param_value, 0.0)
                   END),
               SUM(CASE
                     WHEN upper(param_code) = upper('QR003') THEN
                      coalesce(param_value, 0.0)
                   END)
          INTO v_p005,
               v_p004,
               v_p001,
               v_p002
          FROM dm_duct_profit_param_value
         WHERE data_key = v_data_key
           AND buss_code = cur_m.portfolio_no
           AND dev_month = 0
           AND upper(param_code) IN (upper('QR002'), upper('QR008'), upper('BE013'), upper('QR003'));

        v_s016 := 0.0;
        v_s017 := 0.0;
        FOR cur_g IN (SELECT t2.dev_month,
                             SUM(t2.param_value) AS payamout
                        FROM dm_duct_profit_unit        t1,
                             dm_duct_profit_param_value t2
                       WHERE t1.unit_no = t2.buss_code
                         AND t1.set_no = cur_m.set_no
                         AND t1.data_key = v_data_key
                         AND t2.param_code = 'U008'
                       GROUP BY t2.dev_month
                       ORDER BY t2.dev_month) LOOP

          SELECT coalesce(param_value, 0.0)
            INTO v_dis_factor
            FROM dm_duct_profit_param_value
           WHERE buss_code = cur_m.portfolio_no
             AND param_code = 'discFactor'
             AND dev_month = cur_g.dev_month
             AND data_key = p_data_key;

          -- 未来保费现值
          v_s016 := v_s016 + cur_g.payamout * v_dis_factor;

          --RAISE notice 'cur_g.set_no(%)', cur_m.set_no;
          --RAISE notice 'cur_g.payamout(%),v_dis_factor(%)', cur_g.payamout, v_dis_factor;

          -- 未来现金流现值
          v_s017 := v_s017 + (cur_g.payamout * (cur_m.chargerate)) * v_dis_factor;
          --RAISE notice 'cur_g.set_no(%),cur_m.chargeRate(%)', cur_m.set_no,cur_m.chargeRate;
        END LOOP;

        -- 未来保费现值
        INSERT INTO dm_duct_profit_param_value
          (profit_param_value_id,
           data_key,
           param_code,
           param_value,
           dev_month,
           buss_code)
        VALUES
          (dm_seq_profit_unit.nextval,
           v_data_key,
           'S016',
           v_s016,
           0,
           cur_m.set_no);

        -- 未来现金流现值
        INSERT INTO dm_duct_profit_param_value
          (profit_param_value_id,
           data_key,
           param_code,
           param_value,
           dev_month,
           buss_code)
        VALUES
          (dm_seq_profit_unit.nextval,
           v_data_key,
           'S017',
           v_s017,
           0,
           cur_m.set_no);

        v_uy1 := 0.0;
        v_uw1 := 0.0;
        -- 预期赔付模式
        FOR cur_f IN (SELECT t2.dev_month   AS step,
                             t2.param_value AS payrate
                        FROM dm_duct_profit_param_value t2
                       WHERE t2.buss_code = cur_m.set_no
                         AND t2.data_key = v_data_key
                         AND upper(t2.param_code) = upper('QP001')
                       ORDER BY t2.dev_month) LOOP
          FOR cur_g IN (SELECT t2.dev_month,
                               SUM(t2.param_value) AS payamout
                          FROM dm_duct_profit_unit        t1,
                               dm_duct_profit_param_value t2
                         WHERE t1.unit_no = t2.buss_code
                           AND t1.set_no = cur_m.set_no
                           AND t1.data_key = v_data_key
                           AND t2.param_code = 'U009'
                         GROUP BY t2.dev_month
                         ORDER BY t2.dev_month) LOOP
            -- 预期赔付率
            v_p009 := cur_f.payrate;
            -- 折现因子
            SELECT coalesce(param_value, 0.0)
              INTO v_dis_factor
              FROM dm_duct_profit_param_value
             WHERE buss_code = cur_m.portfolio_no
               AND param_code = 'discFactor'
               AND dev_month = cur_g.dev_month + cur_f.step
               AND data_key = p_data_key;

            -- 预期赔款费用
            v_uy1 := v_uy1 + v_dis_factor * v_p001 * v_p009 * cur_g.payamout;
            --if cur_f.step = 0 then -- 仅在第一轮计算
          -- 预期维持费用
          --    v_uw1 := v_uw1 + v_dis_factor * (v_p002) * cur_g.payamout;
          --end if;
          --raise notice 'cur_m.portfolio_no(%),cur_g.payamout(%),v_uy1(%),v_uw1(%),v_dis_factor(%,%)',cur_m.portfolio_no,cur_g.payamout,v_uy1,v_uw1,cur_g.dev_month + cur_f.step,v_dis_factor;
          --raise notice 'v_p001 * v_p009 * cur_g.payamout(%)',v_p001 * v_p009 * cur_g.payamout;
          END LOOP;
        END LOOP;
        INSERT INTO dm_duct_profit_param_value
          (profit_param_value_id,
           data_key,
           param_code,
           param_value,
           dev_month,
           buss_code)
          SELECT dm_seq_profit_unit.nextval,
                 v_data_key,
                 'S018',
                 (v_uy1 + v_uw1) * (1 + v_p004),
                 0,
                 cur_m.set_no
            FROM dual;

      END LOOP;

    END IF;

    IF p_method = 'BBA' THEN

      -- 预期保费现金流 - 计量单元
      FOR cur_m IN (SELECT risk_code,
                           portfolio_no,
                           unit_no,
                           set_no,
                           premium,
                           CASE
                             WHEN premium = 0 THEN
                              0.0
                             ELSE
                              fee_amount / premium
                           END AS chargerate
                      FROM dm_duct_profit_unit
                     WHERE data_key = v_data_key
                       AND node_state = '1'
                       AND evaluate = 'BBA') LOOP

        /* -- 非跟单IACF费用
        select coalesce(sum(param_value), 0.0)
        into v_p005
        from dm_duct_profit_param_value
        where data_key = v_data_key
          AND buss_code = cur_m.portfolio_no
          AND param_code = 'P005';

        -- 签单保费
        select coalesce(sum(param_value), 0.0)
        into v_p006
        from dm_duct_profit_param_value
        where data_key = v_data_key
          AND buss_code = cur_m.portfolio_no
          AND param_code = 'P006';

        -- 非跟单IACF费用比例
        if v_p006 = 0 then
            v_p005 := 0;
        else
            v_p005 := v_p005 / v_p006;
        end if;

        -- 非金融风险调整_未到期
        select coalesce(sum(param_value), 0.0)
        into v_p004
        from dm_duct_profit_param_value
        where data_key = v_data_key
          AND buss_code = cur_m.portfolio_no
          AND param_code = 'P004';

        -- 预期赔付率
        select coalesce(sum(param_value), 0.0)
        into v_p001
        from dm_duct_profit_param_value
        where data_key = v_data_key
          AND buss_code = cur_m.portfolio_no
          AND param_code = 'P001';

        -- 预期维持费用
        select coalesce(sum(param_value), 0.0)
        into v_p002
        from dm_duct_profit_param_value
        where data_key = v_data_key
          AND buss_code = cur_m.portfolio_no
          AND param_code = 'P002';
        -- 已赚保费
        select coalesce(sum(param_value), 0.0)
        into v_p003
        from dm_duct_profit_param_value
        where data_key = v_data_key
          AND buss_code = cur_m.portfolio_no
          AND param_code = 'P003';

         -- 预期维持费用率
        if v_p003 = 0 then
            v_p002 := 0;
        else
            v_p002 := v_p002 / v_p003;
        end if;

        -- 压力情景:预期赔付率上升比例,预期维持费用率上升比例
        select coalesce(sum(loss_rate), 0.0), coalesce(sum(maintenance_rate), 0.0)
        into v_p010, v_p012
        from dm_conf_bba_profit_loss_factor
        where entity_id = p_entity_id
          AND year_month = p_year_month
          AND risk_code = cur_m.risk_code;
        */


        -- 非跟单IACF费用比例 , 非金融风险调整, 预期赔付率, 预期维持费用率,
        SELECT SUM(CASE
                     WHEN upper(param_code) = upper('QR002') THEN
                      coalesce(param_value, 0.0)
                   END),
               SUM(CASE
                     WHEN upper(param_code) = upper('QR008') THEN
                      coalesce(param_value, 0.0)
                   END),
               SUM(CASE
                     WHEN upper(param_code) = upper('BE013') THEN
                      coalesce(param_value, 0.0)
                   END),
               SUM(CASE
                     WHEN upper(param_code) = upper('QR003') THEN
                      coalesce(param_value, 0.0)
                   END)
        --sum(case when upper(param_code) = upper('bbaLoss') then coalesce(param_value, 0.0) end),
        --sum(case when upper(param_code) = upper('bbaMaintenance') then coalesce(param_value, 0.0) end)
          INTO v_p005,
               v_p004,
               v_p001,
               v_p002 --, v_p010, v_p012
          FROM dm_duct_profit_param_value
         WHERE data_key = v_data_key
           AND buss_code = cur_m.portfolio_no
           AND dev_month = 0
           AND upper(param_code) IN (upper('QR002'), upper('QR008'), upper('BE013'), upper('QR003'), upper('bbaLoss'), upper('bbaMaintenance'));
        --压力情景:预期赔付率上升比例,预期维持费用率上升比例
        SELECT coalesce(loss_rate, 0.0),
               coalesce(maintenance_rate, 0.0)
          INTO v_p010,
               v_p012
          FROM dm_conf_bba_profit_loss_factor
         WHERE entity_id = p_entity_id
           AND year_month = p_year_month
           AND risk_code = cur_m.risk_code
           AND valid_is = '1'
           AND audit_state = '1';


        v_u016 := 0.0;
        v_u017 := 0.0;
        FOR cur_g IN (SELECT t2.dev_month,
                             coalesce(SUM(t2.param_value), 0.0) AS payamout
                        FROM dm_duct_profit_unit        t1,
                             dm_duct_profit_param_value t2
                       WHERE t1.unit_no = t2.buss_code
                         AND t1.unit_no = cur_m.unit_no
                         AND t1.data_key = v_data_key
                         AND t2.param_code = 'U008'
                       GROUP BY t2.dev_month
                       ORDER BY t2.dev_month) LOOP

          -- 折现因子
          SELECT coalesce(param_value, 0.0)
            INTO v_dis_factor
            FROM dm_duct_profit_param_value
           WHERE buss_code = cur_m.portfolio_no
             AND param_code = 'discFactor'
             AND dev_month = cur_g.dev_month
             AND data_key = p_data_key;

          -- 未来保费现值
          v_u016 := v_u016 + cur_g.payamout * v_dis_factor;

          -- 未来现金流现值: chargeRate为跟单IACF率
          v_u017 := v_u017 + (cur_g.payamout * (cur_m.chargerate)) * v_dis_factor;
          --RAISE notice 'cur_g.payamout(%),%,%', cur_g.payamout,cur_m.chargeRate, cur_m.premium;
        END LOOP;

        -- 未来保费现值
        INSERT INTO dm_duct_profit_param_value
          (profit_param_value_id,
           data_key,
           param_code,
           param_value,
           dev_month,
           buss_code)
        VALUES
          (dm_seq_profit_unit.nextval,
           v_data_key,
           'U016',
           v_u016,
           0,
           cur_m.unit_no);

        -- 未来现金流现值
        INSERT INTO dm_duct_profit_param_value
          (profit_param_value_id,
           data_key,
           param_code,
           param_value,
           dev_month,
           buss_code)
        VALUES
          (dm_seq_profit_unit.nextval,
           v_data_key,
           'U017',
           v_u017,
           0,
           cur_m.unit_no);


        v_uy1 := 0.0;
        v_uy2 := 0.0;
        v_uw1 := 0.0;
        v_uw2 := 0.0;
        -- 预期赔付模式
        FOR cur_f IN (SELECT t2.dev_month   AS step,
                             t2.param_value AS payrate
                        FROM dm_duct_profit_param_value t2
                       WHERE t2.buss_code = cur_m.set_no
                         AND t2.data_key = v_data_key
                         AND upper(t2.param_code) = upper('QP001')
                       ORDER BY t2.dev_month) LOOP
          FOR cur_g IN (SELECT t2.dev_month,
                               coalesce(SUM(t2.param_value), 0.0) AS payamout
                          FROM dm_duct_profit_unit        t1,
                               dm_duct_profit_param_value t2
                         WHERE t1.unit_no = t2.buss_code
                           AND t1.unit_no = cur_m.unit_no
                           AND t1.data_key = v_data_key
                           AND t2.param_code = 'U009'
                         GROUP BY t2.dev_month
                         ORDER BY t2.dev_month) LOOP

            -- 预期赔付率
            v_p009 := cur_f.payrate;
            -- 折现因子
            SELECT coalesce(param_value, 0.0)
              INTO v_dis_factor
              FROM dm_duct_profit_param_value
             WHERE buss_code = cur_m.portfolio_no
               AND param_code = 'discFactor'
               AND dev_month = cur_g.dev_month + cur_f.step
               AND data_key = p_data_key;

            -- 赔付现金
            v_uy1 := v_uy1 + v_dis_factor * v_p001 * v_p009 * cur_g.payamout;
            v_uy2 := v_uy2 + v_dis_factor * v_p001 * v_p009 * (1 + v_p010) * cur_g.payamout; --
          --RAISE notice 'cur_m.unit_no(%),v_uy(%,%),cur_g.payamout(%,%)',cur_m.unit_no, v_uy1,v_p001, cur_g.payamout,v_p009;
          --if cur_f.step = 0 then --仅在第一轮计算
          -- 维护费用
          --  v_uw1 := v_uw1 + v_dis_factor * v_p002 * cur_g.payamout;
          --  v_uw2 := v_uw2 + v_dis_factor * v_p002 * (1 + v_p012) * cur_g.payamout;--
          --end if;
          END LOOP;
        END LOOP;
        INSERT INTO dm_duct_profit_param_value
          (profit_param_value_id,
           data_key,
           param_code,
           param_value,
           dev_month,
           buss_code)
          SELECT dm_seq_profit_unit.nextval,
                 v_data_key,
                 'U018',
                 (v_uy1 + v_uw1) * (1 + v_p004),
                 0,
                 cur_m.unit_no
            FROM dual;

        INSERT INTO dm_duct_profit_param_value
          (profit_param_value_id,
           data_key,
           param_code,
           param_value,
           dev_month,
           buss_code)
          SELECT dm_seq_profit_unit.nextval,
                 v_data_key,
                 'U019',
                 (v_uy2 + v_uw2) * (1 + v_p004),
                 0,
                 cur_m.unit_no
            FROM dual;

      END LOOP;

    END IF;
  EXCEPTION
    WHEN OTHERS THEN
      dbms_output.put_line(SQLERRM);

  END proc_fac_profit_param;

  PROCEDURE proc_treaty_profit_check(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2,
                                     p_data_key   VARCHAR2) IS
  BEGIN

    /*
    -- 无评估方法对应的有效计量模型
    UPDATE dm_duct_treaty_profit_unit t
       SET node_state = '2',
           fail_code = 'DM_004'
     WHERE data_key = p_data_key
       AND NOT EXISTS(SELECT 1
                        FROM qtcuser.qtc_conf_model_plan p
                        WHERE p.entity_id = p_entity_id
                        AND p.business_model = 'T'
                        AND p.business_direction = 'I'
                        AND p.evaluate_approach = t.evaluate
                        --AND p.audit_state = '1'
                        AND p.valid_is = '1');
    COMMIT;
    */

    -- 存在未配置数据
    UPDATE dm_duct_treaty_profit_unit t
       SET node_state = '2',
           fail_code  = (CASE WHEN fail_code IS NOT NULL THEN fail_code||',' ELSE '' END) || 'DM_005'
     WHERE data_key = p_data_key
         --假设配置
       AND (NOT EXISTS(SELECT 1
                FROM qtcuser.qtc_conf_model_plan cmp
                LEFT JOIN qtcuser.qtc_conf_quota cq
                  ON cmp.entity_id = cq.entity_id
                 AND cmp.model_def_id = cq.model_def_id
                 AND cq.audit_state = '1'
                 AND cq.valid_is = '1'
                 and cq.year_month = p_year_month --增加评估期条件
                LEFT JOIN qtcuser.qtc_conf_quota_def cqd
                  ON cq.quota_def_id = cqd.quota_def_id
               WHERE cmp.business_model = 'T'
                 AND cmp.business_direction = 'I'
                 AND cmp.evaluate_approach = t.evaluate
                 AND cq.dimension_value = t.loa_code
                 AND cq.dimension = 'G'
                 --AND cmp.audit_state = '1'
                 AND cmp.valid_is = '1'
                 AND cmp.entity_id = p_entity_id
                 AND upper(cqd.quota_code) IN(upper('BE013'), upper('QP001'), upper('QR008'))
                 AND cq.quota_value IS NOT NULL));
    COMMIT;

    -- 存在未配置压力情景数据
    UPDATE dm_duct_treaty_profit_unit t
       SET node_state = '2',
           fail_code  =(CASE WHEN fail_code IS NOT NULL THEN fail_code||',' ELSE '' END) || 'DM_006'
     WHERE data_key = p_data_key
           --压力情景配置查询，BBA时提示异常
       AND t.evaluate = 'BBA'
       AND NOT EXISTS (SELECT 1
                     FROM bpluser.bbs_conf_loa loa
                     LEFT JOIN dm_conf_bba_profit_loss_factor lf
                       ON loa.loa_id = lf.loa_id
                       AND lf.entity_id = loa.entity_id
                       AND lf.business_model = loa.business_model
                       AND lf.business_direction = loa.business_direction
                       AND lf.valid_is = '1'
                       AND lf.audit_state = '1'
                     WHERE loa.entity_id = t.entity_id
                       AND loa.evaluate_approach = t.evaluate
                       AND loa.loa_code = t.loa_code
                       AND loa.business_model = 'T'
                       AND loa.business_direction = t.ri_direction_code
                       AND loa.valid_is = '1'
                       AND loa.audit_state = '1'
                       AND lf.loss_rate IS NOT NULL);
    COMMIT;

    -- 存在未配置无风险曲线数据
    UPDATE dm_duct_treaty_profit_unit t
       SET node_state = '2',
           fail_code  = (CASE WHEN fail_code IS NOT NULL THEN fail_code||',' ELSE '' END) || 'DM_007'
     WHERE data_key = p_data_key
           -- 无风险曲线,确认结果是确认已采用的
       AND(NOT EXISTS (SELECT 1
                  FROM qtcuser.qtc_conf_interest_rate t1
                 WHERE t1.entity_id =t.entity_id
                   AND t1.year_month = t.year_month
                   AND t1.confirm_is = '1'
                   AND t1.valid_is = '1'
                   AND t1.currency_code = dm_pack_buss_accountperiod.func_get_accountperiod_currency(t1.entity_id)--使用会计期间的BookI17账套
                   )
        );
    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN
      dbms_output.put_line(SQLERRM);

  END proc_treaty_profit_check;

  /**
  ** 初始化计算参数数据
  **
  ** p_method : SET 集合成本率
  **            UNIT  现金流
  **            QUOTA  假设数据
  **/
  PROCEDURE proc_treaty_profit_param(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2,
                                     p_data_key   VARCHAR2,
                                     p_method     VARCHAR2) IS
    v_begin_date DATE;
    v_end_date   DATE;
    v_month_count NUMBER;
  BEGIN
    IF p_method = 'QUOTA' THEN

      DELETE FROM dm_duct_treaty_profit_param t
      WHERE t.data_key = p_data_key
        AND param_code IN ('D005','QR002', 'QR003', 'BE013', 'QR008', 'QP001');
      COMMIT;

      --D005
      INSERT INTO dm_duct_treaty_profit_param
            (profit_param_id,
             data_key,
             param_code,
             param_value,
             dev_month,
             buss_code)
        SELECT dm_seq_treaty_profit_param.nextval,
               p_data_key,
               'D005',
               t4.interest_rate,-- 利率
               t4.dev_period,-- 发展期
               t2.dimension_value
          FROM qtcuser.qtc_conf_model_plan t1
          LEFT JOIN bpluser.bbs_conf_loa t7
            ON t7.entity_id = t1.entity_id
           AND t7.business_model = t1.business_model
           AND t7.business_direction = t1.business_direction
           AND t7.evaluate_approach = t1.evaluate_approach
           AND t7.valid_is = '1'
           AND t7.audit_state = '1'
          LEFT JOIN qtcuser.qtc_conf_quota t2
            ON t2.entity_id = t1.entity_id
           AND t2.model_def_id = t1.model_def_id
           AND t2.dimension_value = t7.loa_code
           AND t2.dimension = 'G'
           AND t2.valid_is = '1'
           AND t2.audit_state = '1'
           and t2.year_month = p_year_month --增加评估期条件
          LEFT JOIN qtcuser.qtc_conf_quota_def t3
            ON t3.quota_def_id = t2.quota_def_id
           AND t3.quota_code = 'QT002' -- 保费时点编码
          LEFT JOIN qtcuser.qtc_buss_interest_detail t4
            ON t4.pay_time = t2.quota_value
          LEFT JOIN qtcuser.qtc_buss_interest_main t5
            ON t5.interest_main_id = t4.interest_main_id
           AND t5.entity_id = t1.entity_id
           AND t5.year_month = p_year_month -- 业务年月/评估年月
           AND t5.currency_code = dm_pack_buss_accountperiod.func_get_accountperiod_currency(t1.entity_id)--使用会计期间的BookI17账套
           AND t5.interest_type = '1' --  利率类型： 1- 远期利率/期末利率； 2-初始利率 ； 3-加权初始利率
          LEFT JOIN qtcuser.qtc_conf_interest_rate t6
            ON t6.interest_rate_id = t5.interest_rate_id
           AND t6.currency_code = t5.currency_code
           AND t6.entity_id = t5.entity_id
           AND t6.year_month = t5.year_month -- 业务年月/评估年月
           AND t6.valid_is = '1'
           AND t6.audit_state = '1'
           AND t6.confirm_is = '1' -- 确认版本
         WHERE t1.business_model = 'T' -- 业务模型
           AND t1.business_direction = 'I' -- 业务方向
           AND t1.audit_state = '1'
           AND t1.valid_is = '1'
           AND t3.quota_code = 'QT002' -- 保费时点编码
           AND t6.entity_id = p_entity_id
           AND t6.year_month = p_year_month -- 业务年月/评估年月
         ;
      COMMIT;

      -- QR002,QR003,BE013,QR008 (预期赔付率%，非金融风险调整%,非跟单IACF%,预期维持费用率%)
      INSERT INTO dm_duct_treaty_profit_param
        (profit_param_id,
         data_key,
         param_code,
         param_value,
         dev_month,
         buss_code)
      SELECT dm_seq_treaty_profit_param.nextval,
             p_data_key,
             upper(t3.quota_code)quota_code,
             CAST(t2.quota_value AS NUMBER) AS quota_value,
             0,
             t2.dimension_value
        FROM qtcuser.qtc_conf_model_plan t1
        LEFT JOIN bpluser.bbs_conf_loa t7
          ON t7.entity_id = t1.entity_id
         AND t7.business_model = t1.business_model
         AND t7.business_direction = t1.business_direction
         AND t7.evaluate_approach = t1.evaluate_approach
         AND t7.valid_is = '1'
         AND t7.audit_state = '1'
        LEFT JOIN qtcuser.qtc_conf_quota t2
          ON t2.entity_id = t1.entity_id
         AND t2.model_def_id = t1.model_def_id
         AND t2.dimension_value = t7.loa_code
         AND t2.dimension = 'G'
         AND t2.valid_is = '1'
         AND t2.audit_state = '1'
         and t2.year_month = p_year_month --增加评估期条件
        LEFT JOIN qtcuser.qtc_conf_quota_def t3
          ON t3.quota_def_id = t2.quota_def_id
         AND t3.valid_is = '1'
         AND t3.audit_state = '1'
       WHERE t1.entity_id = p_entity_id
         AND t1.business_model = 'T'
         AND t1.business_direction = 'I'
         AND upper(t3.quota_code) IN ('QR002', 'QR003', 'BE013', 'QR008')
         AND EXISTS (SELECT 1
                FROM dm_duct_treaty_profit_unit t5
               WHERE t5.data_key = p_data_key
                 AND t5.loa_code = t2.dimension_value
                 AND t5.evaluate = t1.evaluate_approach);

      COMMIT;

      -- QP001:(预期赔付模式%)
      INSERT INTO dm_duct_treaty_profit_param
        (profit_param_id,
         data_key,
         param_code,
         param_value,
         dev_month,
         buss_code)
        SELECT dm_seq_treaty_profit_param.nextval,
               p_data_key,
               upper(t3.quota_code)quota_code,
               CAST(t4.quota_value AS NUMBER) AS quota_value,
               t4.quota_period,
               t2.dimension_value
          FROM qtcuser.qtc_conf_model_plan t1
          LEFT JOIN bpluser.bbs_conf_loa t7
            ON t7.business_model = t1.business_model
           AND t7.business_direction = t1.business_direction
           AND t7.evaluate_approach = t1.evaluate_approach
           AND t7.entity_id = t1.entity_id
           AND t7.valid_is = '1'
           AND t7.audit_state = '1'
          LEFT JOIN qtcuser.qtc_conf_quota t2
            ON t2.entity_id = t1.entity_id
           AND t2.model_def_id = t1.model_def_id
           AND t2.dimension_value = t7.loa_code
           AND t2.dimension = 'G'
           AND t2.valid_is = '1'
           AND t2.audit_state = '1'
           and t2.year_month = p_year_month --增加评估期条件
          LEFT JOIN qtcuser.qtc_conf_quota_def t3
            ON t3.quota_def_id = t2.quota_def_id
           AND t3.valid_is = '1'
           AND t3.audit_state = '1'
          LEFT JOIN qtcuser.qtc_conf_quota_detail t4
            ON t4.quota_id = t2.quota_id
         WHERE t1.entity_id = p_entity_id
           AND t1.business_model = 'T'
           AND t1.business_direction = 'I'
           AND upper(t3.quota_code) = 'QP001'
           AND EXISTS (SELECT 1
                  FROM dm_duct_treaty_profit_unit t5
                 WHERE t5.data_key = p_data_key
                   AND t5.evaluate = t1.evaluate_approach
                   AND t5.loa_code = t2.dimension_value);
      COMMIT;

    ELSIF p_method = 'UNIT' THEN
    -- 基本情景-计量单元发展期保费
      DELETE FROM dm_duct_treaty_profit_amount t
       WHERE t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND EXISTS (SELECT 1
                FROM dm_duct_treaty_profit_unit t5
               WHERE t5.cmunit_id = t.cmunit_id
                 AND t5.entity_id = p_entity_id
                 AND t5.year_month = p_year_month);
      COMMIT;

      /*-- 截至评估时间已实收保费
      INSERT INTO dm_duct_treaty_profit_param
        (profit_param_id,
         data_key,
         param_code,
         param_value,
         dev_month,
         cmunit_id)
      SELECT dm_seq_treaty_profit_param.nextval,
             t1.data_key,
             'U003',
             t1.premium,
             0,
             t1.cmunit_id
        FROM dm_duct_treaty_profit_unit t1
       WHERE t1.data_key = p_data_key ;
      COMMIT;*/

      -- 月度已赚保费
      v_begin_date := to_date(p_year_month||'01','YYYYMMDD');
      SELECT MAX(ceil(months_between(expiry_date, GREATEST(effective_date,v_begin_date))))
        INTO v_month_count -- 未来发展期个数：评估年月 到 终保时间 相隔月份数
        FROM dm_duct_treaty_profit_unit t
       WHERE t.data_key = p_data_key;

      -- 循环各个发展期保费占比
      FOR i IN 0 .. v_month_count LOOP
        IF i = 0 THEN
          v_begin_date := to_date(p_year_month||'01','YYYYMMDD');
        ELSE
          v_begin_date := add_months(v_begin_date, 1);
        END IF;
        v_end_date := last_day(v_begin_date);

        -- 月度保费：总保费 * 各月份天数/总承保天数
        INSERT INTO dm_duct_treaty_profit_amount
          (cmunit_id,
           entity_id,
           year_month,
           data_key,
           param_code,
           dev_month,
           amount
           )
        SELECT cmunit_id,
               p_entity_id,
               p_year_month,
               p_data_key,
               'A002',
               i,
               (CASE WHEN policy_count_days = 0 THEN 0 ELSE ROUND(premium * (dev_month_days / policy_count_days),8)END)
          FROM (SELECT cmunit_id,
                       premium,
                       --fee_amount,
                       (LEAST(expiry_date, v_end_date) - (CASE WHEN i = 0 THEN effective_date ELSE v_begin_date END) + 1) AS dev_month_days,
                       (expiry_date - effective_date + 1) AS policy_count_days
                  FROM dm_duct_treaty_profit_unit
                 WHERE data_key = p_data_key
                   AND ri_direction_code = 'I'
                   AND effective_date <= v_end_date
                   AND expiry_date >= v_begin_date
                 ) t;
        COMMIT;
      END LOOP;

    ELSIF p_method = 'SET' THEN

      DELETE FROM dm_duct_treaty_profit_param t
      WHERE t.data_key = p_data_key
        AND param_code IN ('S001','S002');
      COMMIT;

      INSERT INTO dm_duct_treaty_profit_param
          (profit_param_id,
           data_key,
           param_code,
           param_value,
           dev_month,
           buss_code)
      SELECT dm_seq_treaty_profit_param.nextval,
             p_data_key,
             'S001',
             t.premium,
             0,
             t.set_no
        FROM (SELECT t1.set_no,
                     SUM(t1.premium) premium
                FROM dm_duct_treaty_profit_unit t1
               WHERE t1.data_key = p_data_key
                 AND t1.ri_direction_code = 'I'
                 AND t1.evaluate = 'PAA'
               GROUP BY t1.set_no
              ) t;
      COMMIT;

      INSERT INTO dm_duct_treaty_profit_param
          (profit_param_id,
           data_key,
           param_code,
           param_value,
           dev_month,
           buss_code)
      SELECT dm_seq_treaty_profit_param.nextval,
             p_data_key,
             'S002',
             t.fee_amount,
             0,
             t.set_no
        FROM (SELECT t1.set_no,
                     SUM(t1.fee_amount) fee_amount
                FROM dm_duct_treaty_profit_unit t1
               WHERE t1.data_key = p_data_key
                 AND t1.ri_direction_code = 'I'
                 AND t1.evaluate = 'PAA'
               GROUP BY t1.set_no
             ) t;
      COMMIT;

    END IF;

  EXCEPTION
    WHEN OTHERS THEN
      --dbms_output.put_line(SQLERRM);
      proc_buss_cmunit_dtl_log(p_data_key, 'proc_treaty_profit_param', '2', p_method||'-'||SQLERRM);
  END proc_treaty_profit_param;

  PROCEDURE proc_treaty_judge_paa(p_entity_id  NUMBER,
                                  p_year_month VARCHAR2,
                                  p_data_key   VARCHAR2) IS
  BEGIN

    --跟單IACF率
    MERGE INTO dm_duct_treaty_profit_unit t
    USING (SELECT t1.cmunit_id,
                   (CASE WHEN coalesce(s1.param_value,0) <> 0 THEN ((coalesce(s2.param_value,0))/(coalesce(s1.param_value,0))) ELSE 0 END) iacf_rate
              FROM dm_duct_treaty_profit_unit t1
              LEFT JOIN dm_duct_treaty_profit_param s1
                ON s1.data_key = t1.data_key
               AND s1.buss_code = t1.set_no
               AND s1.param_code = 'S001' -- set_premium
              LEFT JOIN dm_duct_treaty_profit_param s2
                ON s2.data_key = t1.data_key
               AND s2.buss_code = t1.set_no
               AND s2.param_code = 'S002' -- set_fee_amount
             WHERE t1.data_key = p_data_key
               AND t1.entity_id = p_entity_id
               AND t1.year_month = p_year_month
               AND t1.ri_direction_code = 'I'
               AND t1.evaluate = 'PAA'
             ) c
    ON (t.cmunit_id = c.cmunit_id)
    WHEN MATCHED THEN
      UPDATE
         SET t.iacf_rate = c.iacf_rate
       WHERE t.data_key = p_data_key
         AND t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND t.ri_direction_code = 'I'
         AND t.evaluate = 'PAA';
    COMMIT;

    --集合成本率
    MERGE INTO dm_duct_treaty_profit_unit t
    USING (SELECT t1.cmunit_id,
                   (coalesce(t1.iacf_rate,0) + (coalesce(p4.param_value,0))*(1 + (coalesce(p8.param_value,0))))cost_rate
                   --iacf_rate + (loss_rate)*(1 + ra_rate)
              FROM dm_duct_treaty_profit_unit t1
              LEFT JOIN dm_duct_treaty_profit_param p4
                ON p4.data_key = t1.data_key
               AND p4.buss_code = t1.loa_code
               AND p4.dev_month = 0
               AND p4.param_code = 'BE013' --loss_rate预期赔付率
              LEFT JOIN dm_duct_treaty_profit_param p8
                ON p8.data_key = t1.data_key
               AND p8.buss_code = t1.loa_code
               AND p8.dev_month = 0
               AND p8.param_code = 'QR008' -- ra_rate非金融风险调整
             WHERE t1.data_key = p_data_key
               AND t1.entity_id = p_entity_id
               AND t1.year_month = p_year_month
               AND t1.ri_direction_code = 'I'
               AND t1.evaluate = 'PAA'
             ) c
    ON (t.cmunit_id = c.cmunit_id)
    WHEN MATCHED THEN
      UPDATE
         SET t.cost_rate = c.cost_rate
       WHERE t.data_key = p_data_key
         AND t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND t.ri_direction_code = 'I'
         AND t.evaluate = 'PAA';
    COMMIT;

    --预期维持费用现金流现值, 跟单IACF费用,非跟单IACF费用
    MERGE INTO dm_duct_treaty_profit_unit t
    USING (SELECT t1.cmunit_id,
                  SUM(t2.amount * d5.param_value) AS premium_future, --premium*dis_rate
                  SUM(t2.amount * t1.iacf_rate * d5.param_value) AS iacf_amount --premium*iacf_rate*dis_rate
             FROM dm_duct_treaty_profit_unit t1
             LEFT JOIN dm_duct_treaty_profit_amount t2
               ON t2.cmunit_id = t1.cmunit_id
              AND t2.entity_id = t1.entity_id
              AND t2.year_month = t1.year_month
              AND t2.param_code = 'A002' --premium
             LEFT JOIN dm_duct_treaty_profit_param d5
               ON d5.data_key = t2.data_key
              AND d5.dev_month = (t2.dev_month + 1)
              AND d5.buss_code = t1.loa_code
              AND d5.param_code = 'D005' --dis_rate
            WHERE t1.data_key = p_data_key
              AND t1.entity_id = p_entity_id
              AND t1.year_month = p_year_month
               AND t1.ri_direction_code = 'I'
              AND t1.evaluate = 'PAA'
             GROUP BY t1.cmunit_id
           ) c
    ON (t.cmunit_id = c.cmunit_id)
    WHEN MATCHED THEN
      UPDATE
         SET t.premium_future = c.premium_future,
             t.iacf_amount = c.iacf_amount
       WHERE t.data_key = p_data_key
         AND t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND t.ri_direction_code = 'I'
         AND t.evaluate = 'PAA';
    COMMIT;

    --预期赔付现金流现值
    MERGE INTO dm_duct_treaty_profit_unit t
    USING (SELECT t1.cmunit_id,
                   SUM(t2.amount * p4.param_value * q1.param_value*d5.param_value) AS loss_amount --premium*loss_rate*loss_pay_rate*dis_rate
              FROM dm_duct_treaty_profit_unit t1
              LEFT JOIN dm_duct_treaty_profit_amount t2
                ON t2.cmunit_id = t1.cmunit_id
               AND t2.entity_id = t1.entity_id
               AND t2.year_month = t1.year_month
               AND t2.param_code = 'A002' --premium
              LEFT JOIN dm_duct_treaty_profit_param p4
                ON p4.data_key = t1.data_key
               AND p4.buss_code = t1.loa_code
               AND p4.dev_month = 0
               AND p4.param_code = 'BE013' --loss_rate预期赔付率
              LEFT JOIN dm_duct_treaty_profit_param q1
                ON q1.data_key = t1.data_key
               AND q1.buss_code = t1.loa_code
               AND q1.param_code = 'QP001' --loss_pay_rate
              LEFT JOIN dm_duct_treaty_profit_param d5
                ON d5.data_key = t2.data_key
               AND d5.dev_month = (t2.dev_month+q1.dev_month+1)
               AND d5.buss_code = t1.loa_code
               AND d5.param_code = 'D005' --dis_rate
             WHERE t1.data_key = p_data_key
               AND t1.entity_id = p_entity_id
               AND t1.year_month = p_year_month
               AND t1.ri_direction_code = 'I'
               AND t1.evaluate = 'PAA'
             GROUP BY t1.cmunit_id
           ) c
    ON (t.cmunit_id = c.cmunit_id)
    WHEN MATCHED THEN
      UPDATE
         SET t.loss_amount = c.loss_amount
       WHERE t.data_key = p_data_key
         AND t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND t.ri_direction_code = 'I'
         AND t.evaluate = 'PAA';
    COMMIT;

    --成本率
    MERGE INTO dm_duct_treaty_profit_unit t
    USING (SELECT t1.entity_id,
                  t1.year_month,
                  t1.set_no,
                  SUM(coalesce(t1.premium_future,0)) premium,
                  SUM((coalesce(t1.loss_amount,0))*(1 + coalesce(p8.param_value,0)) + coalesce(t1.iacf_amount,0)) cost_amount
                    FROM dm_duct_treaty_profit_unit t1
                     LEFT JOIN dm_duct_treaty_profit_param p8
                       ON p8.data_key = t1.data_key
                      AND p8.buss_code = t1.loa_code
                      AND p8.dev_month = 0
                      AND p8.param_code = 'QR008' -- ra_rate非金融风险调整
                    WHERE t1.data_key = p_data_key
                      AND t1.entity_id = p_entity_id
                      AND t1.year_month = p_year_month
               AND t1.ri_direction_code = 'I'
                      AND t1.evaluate = 'PAA'
             GROUP BY t1.entity_id,t1.year_month,t1.set_no
           ) c
    ON (t.entity_id = c.entity_id AND t.year_month = c.year_month AND t.set_no = c.set_no)
    WHEN MATCHED THEN
      UPDATE
         SET t.cost_rate2 = (CASE WHEN c.premium = 0 THEN 0 ELSE c.cost_amount / c.premium END)
       WHERE t.data_key = p_data_key
         AND t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND t.ri_direction_code = 'I'
         AND t.evaluate = 'PAA';
    COMMIT;

      -- PAA更新盈亏标识
      MERGE INTO dm_duct_treaty_profit_unit t
      USING (SELECT cmunit_id,
                    (CASE WHEN (coalesce(t1.premium,0) = 0 OR coalesce(t1.cost_rate,0) IS NULL) THEN 'O' WHEN t1.cost_rate <= 1 THEN 'P' WHEN t1.cost_rate2 > 1 THEN 'L' ELSE 'P' END) judge_rslt
               FROM dm_duct_treaty_profit_unit t1
              WHERE t1.data_key = p_data_key
                AND t1.entity_id = p_entity_id
                AND t1.year_month = p_year_month
                AND t1.ri_direction_code = 'I'
                AND t1.evaluate = 'PAA'
             ) c
      ON (t.cmunit_id = c.cmunit_id)
      WHEN MATCHED THEN
        UPDATE
           SET t.judge_rslt = c.judge_rslt,
               t.judge_date = (CASE
                                WHEN c.judge_rslt = 'L' THEN
                                 last_day(to_date(p_year_month, 'yyyymm'))
                                ELSE
                                 NULL
                              END),-- 判定结果为亏损，设置判定时间
               t.node_state = (CASE
                                WHEN c.judge_rslt IS NULL THEN
                                 '2'
                                ELSE
                                 '1'
                              END),
               t.fail_code = (CASE
                              WHEN c.judge_rslt IS NULL THEN
                               'DM_001'
                              ELSE
                               NULL
                            END)
         WHERE t.data_key = p_data_key
           AND t.entity_id = p_entity_id
           AND t.year_month = p_year_month
           AND t.ri_direction_code = 'I'
           AND t.evaluate = 'PAA';
      COMMIT;

    proc_buss_cmunit_dtl_log(p_data_key, 'proc_treaty_judge_paa', '1', NULL);
  EXCEPTION
    WHEN OTHERS THEN
      --dbms_output.put_line(SQLERRM);
      proc_buss_cmunit_dtl_log(p_data_key, 'proc_treaty_judge_paa', '2', SQLERRM);
  END proc_treaty_judge_paa;

  PROCEDURE proc_treaty_judge_bba(p_entity_id  NUMBER,
                                  p_year_month VARCHAR2,
                                  p_data_key   VARCHAR2) IS
  BEGIN

    --跟單IACF率
    MERGE INTO dm_duct_treaty_profit_unit t
    USING (SELECT t1.cmunit_id,
                   (CASE WHEN coalesce(t1.premium,0) <> 0 THEN ((coalesce(t1.fee_amount,0))/(coalesce(t1.premium,0))) ELSE 0 END) iacf_rate
              FROM dm_duct_treaty_profit_unit t1
             WHERE t1.data_key = p_data_key
               AND t1.entity_id = p_entity_id
               AND t1.year_month = p_year_month
               AND t1.ri_direction_code = 'I'
               AND t1.evaluate = 'BBA'
             ) c
    ON (t.cmunit_id = c.cmunit_id)
    WHEN MATCHED THEN
      UPDATE
         SET t.iacf_rate = c.iacf_rate
       WHERE t.data_key = p_data_key
         AND t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND t.ri_direction_code = 'I'
         AND t.evaluate = 'BBA';
    COMMIT;

    --预期保费现金流现值, 跟单IACF费用
    MERGE INTO dm_duct_treaty_profit_unit t
    USING (SELECT t1.cmunit_id,
                  SUM(t2.amount * d5.param_value) AS premium_future, --premium*dis_rate
                  SUM(t2.amount * t1.iacf_rate * d5.param_value) AS iacf_amount --premium*iacf_rate*dis_rate
             FROM dm_duct_treaty_profit_unit t1
             LEFT JOIN dm_duct_treaty_profit_amount t2
               ON t2.cmunit_id = t1.cmunit_id
              AND t2.entity_id = t1.entity_id
              AND t2.year_month = t1.year_month
              AND t2.param_code = 'A002' --premium
             LEFT JOIN dm_duct_treaty_profit_param d5
               ON d5.data_key = t2.data_key
              AND d5.dev_month = (t2.dev_month + 1)
              AND d5.buss_code = t1.loa_code
              AND d5.param_code = 'D005' --dis_rate
            WHERE t1.data_key = p_data_key
              AND t1.entity_id = p_entity_id
              AND t1.year_month = p_year_month
              AND t1.ri_direction_code = 'I'
              AND t1.evaluate = 'BBA'
            GROUP BY t1.cmunit_id) c
    ON (t.cmunit_id = c.cmunit_id)
    WHEN MATCHED THEN
      UPDATE
         SET t.iacf_amount  = c.iacf_amount,
             t.premium_future = c.premium_future
       WHERE t.data_key = p_data_key
         AND t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND t.ri_direction_code = 'I'
         AND t.evaluate = 'BBA';
    COMMIT;

    --预期赔付现金流现值
    MERGE INTO dm_duct_treaty_profit_unit t
    USING (SELECT t1.cmunit_id,
                  SUM(t2.amount * p4.param_value * q1.param_value * d5.param_value) AS loss_amount, --premium*loss_rate*loss_pay_rate*dis_rate
                  SUM(t2.amount * p4.param_value *(1 + coalesce(f.loss_rate, 0.0)) * q1.param_value * d5.param_value) AS loss_amount2 --premium*loss_rate*(1+X)*loss_pay_rate*dis_rate
             FROM dm_duct_treaty_profit_unit t1
             LEFT JOIN dm_duct_treaty_profit_amount t2
               ON t2.cmunit_id = t1.cmunit_id
              AND t2.entity_id = t1.entity_id
              AND t2.year_month = t1.year_month
              AND t2.param_code = 'A002' --premium
             LEFT JOIN dm_duct_treaty_profit_param p4
               ON p4.data_key = t1.data_key
              AND p4.buss_code = t1.loa_code
              AND p4.dev_month = 0
              AND p4.param_code = 'BE013' --loss_rate预期赔付率
             LEFT JOIN dm_duct_treaty_profit_param q1
               ON q1.data_key = t1.data_key
              AND q1.buss_code = t1.loa_code
              AND q1.param_code = 'QP001' --loss_pay_rate
             LEFT JOIN dm_duct_treaty_profit_param d5
               ON d5.data_key = t2.data_key
              AND d5.dev_month = (t2.dev_month + q1.dev_month + 1)
              AND d5.buss_code = t1.loa_code
              AND d5.param_code = 'D005' --dis_rate
             LEFT JOIN bpluser.bbs_conf_loa l
               ON l.entity_id = t1.entity_id
              AND l.loa_code = t1.loa_code
              AND l.business_model = 'T'
              AND l.business_direction = 'I'
              AND l.valid_is = '1'
              AND l.audit_state = '1'
             LEFT JOIN dm_conf_bba_profit_loss_factor f
               ON f.loa_id = l.loa_id
              AND f.business_model= 'T'
              AND f.business_direction = 'I'
              AND f.valid_is = '1'
              AND f.audit_state = '1'
            WHERE t1.data_key = p_data_key
              AND t1.entity_id = p_entity_id
              AND t1.year_month = p_year_month
              AND t1.ri_direction_code = 'I'
              AND t1.evaluate = 'BBA'
            GROUP BY t1.cmunit_id --,t1.loa_code,(t2.dev_month+q1.dev_month)
           ) c
    ON (t.cmunit_id = c.cmunit_id)
    WHEN MATCHED THEN
      UPDATE
         SET t.loss_amount = c.loss_amount, t.loss_amount2 = c.loss_amount2
       WHERE t.data_key = p_data_key
         AND t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND t.ri_direction_code = 'I'
         AND t.evaluate = 'BBA';
    COMMIT;

    --成本率
    MERGE INTO dm_duct_treaty_profit_unit t
    USING (SELECT t1.cmunit_id,
                  t1.premium_future premium,
                  ((coalesce(t1.loss_amount,0) )*(1 + coalesce(p8.param_value,0)) + coalesce(t1.iacf_amount,0)) cost_amount,
                  ((coalesce(t1.loss_amount2,0) )*(1 + coalesce(p8.param_value,0)) + coalesce(t1.iacf_amount,0)) cost_amount2
            FROM dm_duct_treaty_profit_unit t1
            LEFT JOIN dm_duct_treaty_profit_param p8
              ON p8.data_key = t1.data_key
             AND p8.buss_code = t1.loa_code
             AND p8.dev_month = 0
             AND p8.param_code = 'QR008' -- ra_rate非金融风险调整
           WHERE t1.data_key = p_data_key
             AND t1.entity_id = p_entity_id
             AND t1.year_month = p_year_month
             AND t1.ri_direction_code = 'I'
             AND t1.evaluate = 'BBA'
           ) c
    ON (t.cmunit_id = c.cmunit_id)
    WHEN MATCHED THEN
      UPDATE
         SET t.cost_rate = (CASE WHEN c.premium = 0 THEN 0 ELSE c.cost_amount / c.premium END),
             t.cost_rate2 = (CASE WHEN c.premium = 0 THEN 0 ELSE c.cost_amount2 / c.premium END)
       WHERE t.data_key = p_data_key
         AND t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND t.ri_direction_code = 'I'
         AND t.evaluate = 'BBA';
    COMMIT;

    -- 更新盈亏标识
    MERGE INTO dm_duct_treaty_profit_unit t
    USING (SELECT cmunit_id,
                  (CASE WHEN (coalesce(t1.premium,0) = 0 OR coalesce(t1.cost_rate,0) = 0) THEN 'O' WHEN t1.cost_rate > 1 THEN 'L' WHEN t1.cost_rate2 > 1 THEN 'O' ELSE 'P' END) judge_rslt
             FROM dm_duct_treaty_profit_unit t1
            WHERE t1.data_key = p_data_key
              AND t1.entity_id = p_entity_id
              AND t1.year_month = p_year_month
              AND t1.ri_direction_code = 'I'
              AND t1.evaluate = 'BBA'
           ) c
    ON (t.cmunit_id = c.cmunit_id)
    WHEN MATCHED THEN
      UPDATE
         SET t.judge_rslt = c.judge_rslt,
             t.judge_date = (CASE WHEN c.judge_rslt = 'L' THEN
                               last_day(to_date(p_year_month, 'yyyymm'))
                              ELSE NULL
                            END),-- 判定结果为亏损，设置判定时间
             t.node_state = (CASE WHEN c.judge_rslt IS NULL THEN
                               '2'
                              ELSE
                               '1'
                            END) ,
             t.fail_code = (CASE WHEN c.judge_rslt IS NULL THEN
                             'DM_001'
                            ELSE
                             NULL
                          END)
       WHERE t.data_key = p_data_key
         AND t.entity_id = p_entity_id
         AND t.year_month = p_year_month
         AND t.ri_direction_code = 'I'
         AND t.evaluate = 'BBA';
    COMMIT;

    proc_buss_cmunit_dtl_log(p_data_key, 'proc_treaty_judge_bba', '1', NULL);
  EXCEPTION
    WHEN OTHERS THEN
      --dbms_output.put_line(SQLERRM);
      proc_buss_cmunit_dtl_log(p_data_key, 'proc_treaty_judge_bba', '2', SQLERRM);
  END proc_treaty_judge_bba;

  PROCEDURE proc_buss_cmunit_dtl_log(p_trace_no VARCHAR2,
                                     p_trace_code VARCHAR2,
                                     p_trace_status VARCHAR2,
                                     p_trace_msg VARCHAR2) IS
  BEGIN
    INSERT INTO DM_LOG_BUSS_CMUNIT_DETAIL
        (trace_no,
         trace_code,
         trace_status,
         trace_msg,
         create_time)
      VALUES
        (p_trace_no,
         p_trace_code,
         p_trace_status,
         p_trace_msg,
         SYSDATE);
     COMMIT;

  EXCEPTION
    --意外处理
      WHEN OTHERS THEN
      NULL;
  END proc_buss_cmunit_dtl_log;
  
  PROCEDURE proc_backup_profit_unit_result_tab(p_entity_id  NUMBER,
                                           p_year_month VARCHAR2,
                                           p_table_name VARCHAR2) IS

    v_count NUMBER(15);
    v_bak_table_name varchar2(2000);
    v_condition VARCHAR2(2000);

  begin

        v_bak_table_name := p_table_name||'_' || p_entity_id || '_' ||p_year_month;


      SELECT count(*)
        INTO v_count
        FROM user_tables
       WHERE table_name = upper(v_bak_table_name);

      v_condition := ' WHERE DATA_KEY = '''|| p_entity_id  || p_year_month ||''' ' ;

       IF v_count > 0 THEN

            EXECUTE IMMEDIATE ('DELETE FROM '|| v_bak_table_name|| ' ' || v_condition );
            commit;
            EXECUTE IMMEDIATE ('INSERT INTO ' || v_bak_table_name ||' SELECT * FROM  '|| p_table_name||' '|| v_condition);
      ELSE
            EXECUTE IMMEDIATE ( 'CREATE TABLE '|| v_bak_table_name ||' AS SELECT * FROM  '|| p_table_name||' '|| v_condition);
      END IF;

       EXECUTE IMMEDIATE ('DELETE FROM '|| p_table_name|| ' ' || v_condition );

       commit;
  END proc_backup_profit_unit_result_tab;

END dm_pack_cmunit_common;
/
