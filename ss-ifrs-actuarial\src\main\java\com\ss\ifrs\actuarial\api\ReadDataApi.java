package com.ss.ifrs.actuarial.api;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.pojo.base.po.BaseResponse;

import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @ClassName : ReadDataApi
 * 
 * @date 2019/5/16 9:22
 **/
@RestController
@RequestMapping("/init_data")
public class ReadDataApi extends BaseApi {

	@Autowired
	private JdbcTemplate jdbcTemplate;
	@Autowired
	DataSource dataSource;

	@ApiOperation(value = "初始化第三方权限数据")
	@RequestMapping(value = "/init_third_party", method = RequestMethod.GET)
	public BaseResponse<String> viewAllMenu() {
		// readDataService.initThridPartyAuths();
		System.out.println(dataSource);
		System.out.println(jdbcTemplate);
		return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "init completely");
	}
}
