/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-01-13 16:44:16
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao.buss.puhua.lic;


import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLicCashFlow;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLicCashFlowVo;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-01-13 16:44:16<br/>
 * Description: LIC赔付现金流提取主表 Dao类<br/>
 * Related Table Name: ATR_BUSS_LIC_ACTION<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrBussPHLicActionDao extends IDao<AtrBussLicCashFlow, Long> {

    Page<AtrBussLicCashFlowVo> fuzzySearchPage(AtrBussLicCashFlowVo atrBussLicCashFlowVo, Pageable pageParam);

    AtrBussLicCashFlowVo findByid(Long id);

    AtrBussLicCashFlow findByVo(AtrBussLicCashFlow atrBussLicCashFlow);

    Page<AtrDapDrawVo> findPortfolioData(AtrDapDrawVo atrDapDrawVo, Pageable pageParam);

    Integer updateConfirm(AtrBussLicCashFlow atrBussLicCashFlow);

    Integer revoke(AtrBussLicCashFlow atrBussLicCashFlow);
}