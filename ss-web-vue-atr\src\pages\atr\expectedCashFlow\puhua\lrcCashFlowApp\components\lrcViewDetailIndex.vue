<template>
    <el-dialog :title="'atrLrcCashFlowTitle' | translate" custom-class="gv-dialog-form" :visible.sync="dialogFormVisible"
               width="96%" :close-on-click-modal="false" top="5vh" append-to-body>
        <gv-form :model="form" ref="form" :rules="rules">
            <el-collapse v-model="mixinObject.activeNames">
                <el-collapse-item :title="'gTitleBasics' | translate('Basics Data')" name="1" class="table-line">
                    <div class="gv-row">
                        <gv-form-item key-name="gCenterCode" prop="entityCode" >
                            <gv-auto-complete context-name="common" url="/basic_center/find_Branch" code-name="entityCode,entityCName"
                                              :is-readonly="isReadonlySave"
                                              label-name="entityCode,entityCName"
                                              v-model="form.entityCode" ></gv-auto-complete>
                        </gv-form-item>
                        <gv-form-item key-name="dmBusinessType" prop="businessSourceCode">
                            <gv-select :disabled="isReadonlySave" size="mini" options-set="0" code-type="BusinessModel/Base" v-model="form.businessSourceCode"></gv-select>
                        </gv-form-item>
                        <gv-form-item key-name="atrEvaluationYearMonth" prop="yearMonth">
                            <el-input maxlength="6" placeholder="yyyymm" v-model="form.yearMonth" :disabled="isReadonlySave">
                            </el-input>
                        </gv-form-item>
                    </div>
                    <div class="gv-row">
                        <gv-form-item key-name="gDmPortfolioNo" prop="portfolioNo" v-if="type !=='add'">
                            <el-input v-model="form.portfolioNo" :disabled="isReadonlySave"></el-input>
                        </gv-form-item>
                        <gv-form-item key-name="gDmContractGroupNo" prop="icgNo" v-if="type !=='add'">
                            <el-input v-model="form.icgNo" :disabled="isReadonlySave"></el-input>
                        </gv-form-item>
                      <gv-form-item key-name="atrInsuranceClass"  v-if="showRiskClassCode" prop="riskClassCode">
                        <el-input v-model="form.riskClassCode" :disabled="isReadonlySave"></el-input>
                      </gv-form-item>
                    </div>
                </el-collapse-item>

                <!--aioi-->
                <el-collapse-item :title="'atrAcquisitionInfo' | translate" name="2" v-if="form.businessSourceCode !== 'TO' && form.businessSourceCode !== 'FO'">
                    <div class="gv-atr-collapse-content">
                        <div id="tabs-atr">
                            <table class="custom-table">
                                <thead>
                                  <!-- 第一行表头 -->
                                  <tr>
                                    <template v-for="(field, index) in quotaTable.firstRowFields">
                                      <th v-if="field.rowspan" 
                                          :key="'first-'+index"
                                          :rowspan="field.rowspan" 
                                          :class="field.className"
                                          :style="{ backgroundColor: field.headColor, textAlign: 'center' }">
                                        {{ field.labelKey | translate }}
                                      </th>
                                      <th v-else 
                                          :key="('first-'+index)"
                                          :colspan="field.colspan" 
                                          :class="field.className"
                                          :style="{ backgroundColor: field.headColor, textAlign: 'center' }">
                                        {{ field.labelKey | translate }}
                                      </th>
                                    </template>
                                  </tr>
                                  <!-- 第二行表头 -->
                                  <tr>
                                    <template v-for="(field, index) in quotaTable.secondRowFields">
                                      <th v-if="field.prop !== 'insuranceType'"
                                          :key="'second-'+index" 
                                          :class="field.className"
                                          :style="{ backgroundColor: field.headColor, textAlign: 'center' }">
                                        {{ field.labelKey | translate }}
                                      </th>
                                    </template>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr v-for="(row, rowIndex) in bussQuotaVoList" :key="rowIndex">
                                    <!-- 险种列 -->
                                    <td style="text-align: center;">{{ row.insuranceType }}</td>
                                    <!-- 数据单元格 -->
                                    <template v-for="(field, fieldIndex) in quotaTable.secondRowFields">
                                      <td v-if="field.prop !== 'insuranceType'"
                                          :key="'cell-'+fieldIndex" 
                                          :class="field.className" 
                                          style="text-align: right;">
                                        <!-- 如果是发展期指标(quotaType=1)，只显示图标不显示值 -->
                                        <template v-if="field.quotaType === '1'">
                                          <i class="el-icon-my-line-graph"
                                             style="margin-left: 5px; cursor: pointer;"
                                             @click="onListBtn(row, 'develop', field.prop)"></i>
                                        </template>
                                        <!-- 否则根据字段类型格式化数据 -->
                                        <template v-else>
                                          <span v-if="field.fieldType === '2'">
                                            {{ row[field.prop] | amount(true, 2) }}
                                          </span>
                                          <span v-else-if="field.fieldType === '4'">
                                            {{ row[field.prop] | amountZero(false, 2) }}%
                                          </span>
                                          <span v-else>
                                            {{ row[field.prop] }}
                                          </span>
                                        </template>
                                      </td>
                                    </template>
                                  </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="gv-atr-collapse-content" v-if="periodShow">
                        <div>
                            <span class="rectangularr"></span>
                            <span class="gv-panel-titleBar">{{quotaDefName}}</span>
                            <div  class="pull-right">
                                <el-button class="mr15" style="margin-right: 8px" type="text" @click="onFold"><i class="el-dialog__close el-icon el-icon-close"></i></el-button>
                            </div>
                        </div>
                        <div>
                            <el-table :data="bussQuotaDevelopVoList" border :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266' }">
                                <el-table-column prop="quotaPeriod" :label="'atrDevelopMonth' | translate" width="180"></el-table-column>
                                <el-table-column
                                    v-for="(col, index) in developmentColumns"
                                    :key="index"
                                    :prop="col.prop"
                                    :label="col.label"
                                    align="right">
                                    <template v-slot="scope">
                                        {{ scope.row[col.prop] | amountZero(false, 2) }} %
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                </el-collapse-item>
                <el-collapse-item :title="'atrAioiExpectedPremiumCF' | translate" name="3">
                    <el-tabs v-model="lrcLicTab"  v-if="loding" @tab-click="handleClick">
                        <el-tab-pane v-for="obj in this.premTypeArray" :key="obj.remark" :label="obj.outCName"
                                     :name="obj.remark" >
                            <template  >
                                <gv-data-table-list v-if="obj.remark===lrcLicTab && tabMap[obj.remark]" :ref="obj.remark+'Table'" @upListData="handleSetList" :table="tabMap[obj.remark].table" :list="tabMap[obj.remark].list" :id="obj.remark"   :listTableTop="true" :paging="true"
                                                    :currentPage="mixinObject.searchSet.currentPage" :MaxHeight='"400px"' :total="mixinObject.searchSet.total" >
                                </gv-data-table-list>
                            </template>
                       </el-tab-pane>
                    </el-tabs>
                </el-collapse-item>
            </el-collapse>
            <el-row class="toolbar-btn txt-center">
                <el-button class="gv-btn gv-btn-primary" :disabled="isReadonlySave" v-if="type!='view'" type="primary" @click="onSubmit()">{{
                        'atrBtnDraw' | translate }}
                </el-button>
                <el-button class="gv-btn gv-btn-white" @click="onClose()">{{ 'gBtnClose' | translate}}</el-button>
            </el-row>
        </gv-form>
    </el-dialog>
</template>

<script>
import feildCard from './card.js'
export default {
    name: 'lrcAppEditIndex',
    props: {
        type:'',
        licAction:{},
        title:'',
        licShowDataForLossCurrent:'',
        id: '',
        riskClass:'',
        yearMonth:'',
        dataType: '',
        drawTime: '',
        statisZones: '',
        centerId: '',
        currency: '',
        portfolioNo: '',
        mainId: '',
    },
    data: function () {
        return {
            form: {
                centerId: '',
                entityCode: '',
                riskClass: null,
                currency: '',
                ibnrType: '',
                businessSourceCode: '',
                statisZones: '',
                drawTime: '',
                dataType: '',
                riskCName: '',
                loaCode: '',
                loaName: '',
                portfolioNo: null,
                yearMonth: '',
                currencyName: '',
                taskCode: '',
                riskClassCode: '',
                recvDetailVoList:[],
                gepDetailVoList:[],
                covDetailVoList:[],
                uepDetailVoList:[],
                adjDetailVoList:[],
                mainDetailVoList:[],
                iacfDetailVoList:[],
                nonMaimDetailVoList:[],
                csmDetailVoList:[],
            },
            loding: true,
            isReadonly: false,
            isDisabled: false,// 禁用下拉选项

          tableField: {
            DD: {
              edPremiumDetailTableFields:[
                'actionNO','entityId','policyNo', 'endorseSeqNo',
                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',
                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt', 'cmunitNo',
                'businessSourceCode','evaluateDate', 'contractDate',
                'effectiveDate','expiryDate', 'issueDate', 'comfirmDate','approvalDate',
                'currencyCode', 'grossPremium','preAccumEdPremium' ],
              edNetFeeDetailTableFields:[
                'actionNO','entityId','policyNo', 'endorseSeqNo',
                'riskCode','kindCode' ,'atrCenter1','atrCenter2','atrCenter3','atrCenter4',
                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt', 'cmunitNo','evaluateApproach',
                'businessSourceCode','evaluateDate', 'contractDate',
                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',
                  'currencyCode','feeRate','netFee', 'preCumlEdNetFee'],
              edIacfDetailTableFields:[
                'actionNO','entityId','policyNo', 'endorseSeqNo',
                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',
                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo','evaluateApproach',
                'businessSourceCode','evaluateDate', 'contractDate',
                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',
                'currencyCode','iacf','preCumlEdIacf'
                 ],
              edIaehcInDetailTableFields:[
                'actionNO','entityId','policyNo', 'endorseSeqNo',
                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',
                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt',  'cmunitNo','evaluateApproach',
                'businessSourceCode','evaluateDate', 'contractDate',
                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',
                'currencyCode','iaehcIn','preCumlEdIaehcIn'],
              edIaehcOutDetailTableFields:[
                'actionNO','entityId','policyNo', 'endorseSeqNo',
                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',
                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo','evaluateApproach',
                'businessSourceCode','evaluateDate', 'contractDate',
                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',
                'currencyCode','iaehcOut','preCumlEdIaehcOut'],
              premiumDetailTableFields:[
                'actionNO','entityId','policyNo', 'endorseSeqNo',
                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',
                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt',  'cmunitNo','evaluateApproach',
                'businessSourceCode','evaluateDate', 'contractDate',
                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',
                'currencyCode','premium','preCumlPaidPremium'],
              netFeeDetailTableFields:[
                'actionNO','entityId','policyNo', 'endorseSeqNo',
                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',
                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt',  'cmunitNo','evaluateApproach',
                'businessSourceCode','evaluateDate', 'contractDate',
                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',
                'currencyCode', 'netFee','preCumlPaidNetFee'],
              badDebtDetailTableFields:[
                'actionNO','entityId','policyNo', 'endorseSeqNo',
                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',
                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt',  'cmunitNo','evaluateApproach',
                'businessSourceCode','evaluateDate', 'contractDate',
                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',
                'currencyCode','badDebt'],
              iacfDetailTableFields:[
                'actionNO','entityId','policyNo', 'endorseSeqNo',
                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',
                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo','evaluateApproach',
                'businessSourceCode','evaluateDate', 'contractDate',
                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',
                'currencyCode','iacf','preCumlEdIacf'],
              iaehcInDetailTableFields:[
                'actionNO','entityId','policyNo', 'endorseSeqNo',
                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',
                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo','evaluateApproach',
                'businessSourceCode','evaluateDate', 'contractDate',
                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',
                'currencyCode','iaehcIn'],
              iaehcOutDetailTableFields:[
                'actionNO','entityId','policyNo', 'endorseSeqNo',
                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',
                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt',  'cmunitNo','evaluateApproach',
                'businessSourceCode','evaluateDate', 'contractDate',
                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',
                'currencyCode','iaehcOut'],
              lapseDetailTableFields:[
                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode',
                 'lapseRate'],
              mtFeeDetailTableFields:[
                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode',
                'mtRate'],
              claimDetailTableFields:[
                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode',
                'claimRate'],
              ulaeDetailTableFields:[
                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode',
                'ulaeRate'],
              raDetailTableFields:[
                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode',
                'raRatio'],

            },
            TI: {
              edPremiumDetailTableFields:[
                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',
                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',
                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',
                'evaluateDate', 'contractDate','currencyCode',
                'effectiveDate','expiryDate',
                'grossPremium','preCumlPaidPremium' ],
              edNetFeeDetailTableFields:[
                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',
                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',
                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',
                'evaluateDate', 'contractDate','currencyCode',
                'effectiveDate','expiryDate','feeRate','netFee', 'preCumlEdNetFee'],
              edIacfDetailTableFields:[
                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',
                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',
                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',
                'evaluateDate', 'contractDate','currencyCode',
                'effectiveDate','expiryDate','iacf','preCumlEdIacf'
              ],
              edIaehcInDetailTableFields:[
                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',
                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',
                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',
                'evaluateDate', 'contractDate','currencyCode',
                'effectiveDate','expiryDate','iaehcIn','preCumlEdIaehcIn'],
              edIaehcOutDetailTableFields:[
                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',
                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',
                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',
                'evaluateDate', 'contractDate','currencyCode',
                'effectiveDate','expiryDate','iaehcOut','preCumlEdIaehcOut'],
              premiumDetailTableFields:[
                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',
                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',
                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',
                'evaluateDate', 'contractDate','currencyCode',
                'effectiveDate','expiryDate','premium','preCumlPaidPremium'],
              netFeeDetailTableFields:[
                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',
                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',
                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',
                'evaluateDate', 'contractDate','currencyCode',
                'effectiveDate','expiryDate', 'netFee','preCumlPaidNetFee'],
              iacfDetailTableFields:[
                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',
                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',
                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',
                'evaluateDate', 'contractDate','currencyCode',
                'effectiveDate','expiryDate','iacf','preCumlEdIacf'],
              iaehcInDetailTableFields:[
                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',
                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',
                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',
                'evaluateDate', 'contractDate','currencyCode',
                'effectiveDate','expiryDate','iaehcIn'],
              iaehcOutDetailTableFields:[
                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',
                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',
                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',
                'evaluateDate', 'contractDate','currencyCode',
                'effectiveDate','expiryDate','iaehcOut'],
              lapseDetailTableFields:[
                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode','currencyCode',
                'lapseRate'],
              mtFeeDetailTableFields:[
                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode','currencyCode',
                'mtRate'],
              claimDetailTableFields:[
                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode','currencyCode',
                'claimRate'],
              ulaeDetailTableFields:[
                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode','currencyCode',
                'ulaeRate'],
              raDetailTableFields:[
                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode','currencyCode',
                'raRatio'],
            },
            TO: {
              edPremiumDetailTableFields:[
                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','policyNo', 'endorseSeqNo','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',
                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',
                'effectiveDate','expiryDate',
                'grossPremium','preEdPremium' ],
              premiumCfDetailTableFields:[
                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','policyNo', 'endorseSeqNo','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',
                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo',
                'evaluateDate', 'contractDate',
                'effectiveDate','expiryDate',
                'grossPremium', 'preAccumEdPremium' ],
              netFeeCfDetailTableFields:[
                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','policyNo', 'endorseSeqNo','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',
                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',
                'effectiveDate','expiryDate',
                 'netFee','preAccumNetFee'],
              edNetFeeDetailTableFields:[
                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','policyNo', 'endorseSeqNo','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',
                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',
                'effectiveDate','expiryDate', 'feeRate','netFee', 'preEdNetFee'],
              invAmountDetailTableFields:[

              ]
            },
            TX: {
              edPremiumDetailTableFields:[
                'actionNO','entityId','treatyNo','policyNo', 'endorseSeqNo','kindCode','yearMonth',
                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',
                'effectiveDate','expiryDate',
                'grossPremium','preEdPremium' ],
              premiumCfDetailTableFields:[
                'actionNO','entityId','treatyNo','policyNo', 'endorseSeqNo','kindCode','yearMonth',
                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo',
                'evaluateDate', 'contractDate',
                'effectiveDate','expiryDate',
                'grossPremium' ]
            },
            FO: {
              edPremiumDetailTableFields:[
                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',
                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',
                'effectiveDate','expiryDate',
                'grossPremium','preEdPremium' ],
              recvPremiumDetailTableFields:[
                'actionNO','entityId','treatyNo', 'treatyName','yearMonth',
                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo',
                'evaluateDate', 'contractDate',
                'effectiveDate','expiryDate',
                'grossPremium', 'preAccumEdPremium' ],
              netFeeDetailTableFields:[
                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',
                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',
                'effectiveDate','expiryDate',
                'netFee','preAccumNetFee'],
              edNetFeeDetailTableFields:[
                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',
                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',
                'effectiveDate','expiryDate','netFee', 'preEdNetFee']
            },
          },
            isReadonlySave: false,
            rules: {},
            dialogFormVisible: true,
            pickerOptions: {
                disabledDate: function (time) {
                    return time.getTime() < Date.now() - 8.64e7;
                }
            },
            activeName: 'first',

            /****************aioi假设值*****************/
            //版本指标假设业务值
            quotaTable: {
                basic: {
                    headerstyle:true,
                    cellstyle:{},
                    custom: true, // 添加自定义渲染支持
                },
                fields: [],
                firstRowFields: [], // 第一行表头字段
                secondRowFields: [] // 第二行表头字段
            },
            //版本指标发展期业务值
            devPeriodTable: {
                basic: {
                    fieldInit: false,
                    cellstyle: true,
                    headerstyle: true
                },
                fields: [{
                    prop: "quotaPeriod", //属性
                    labelKey: 'atrDevelopMonth',
                    sortable:false,
                    showOverflowTooltip: true,
                    width:"150px",
                }]
            },
            developmentColumns: [], // 存储发展期表格列
            quotaDtl:{
                quota:''
            },
            bussQuotaVoList:[],
            bussQuotaDevelopVoList:[],
            quotaObj:'',
            loading: false,
            periodShow: false,
            quotaDefName:'',
            palettes:['#DAD4F0','#ECE9F7'],
            rowPalettes:['purple-to-gray','light-gray'],
            lrcLicTab: '',
            premTypeArray:[],
            tabMap: {
            },
            showRiskClassCode: false,
        }
    },
    watch: {
        dialogFormVisible: function (n, o) {
            !n && this.$emit('close');
        },
        'licAction.businessSourceCode': function(newVal) {
            this.showRiskClassCode = newVal === 'DD' || newVal === 'TI';
        },
        'form.riskClassCode': function(newVal, oldVal) {
            // 当险类代码变化且有值时，重新获取假设数据
            if (newVal && newVal !== oldVal) {
                this.findBussQuotaDataById();
            }
        }
    },

    methods: {
        // 确认按钮（表单提交）
        onSubmit: function() {
            var _this = this,
                url;
            this.$refs.form.validate(function(valid) {
                if (valid) {
                    Vue.gvUtil.confirm({
                        msg: Vue.gvUtil.getInzTranslate('gSaveSubmit')
                    }).then(function() {
                        // 新增
                        url = Vue.gvUtil.getUrl({
                            apiName: 'lrcCashFlowAdd',
                            contextName: 'actuarial'
                        });
                        Vue.gvUtil.http.post(url, _this.form).then(function (res) {
                            if (_this.isDialog) {
                                // _this.dialogSuccessSubmit();
                            } else {
                                _this.successSubmit(res)
                            }
                            if (res.resCode === '0017') {
                                msg: Vue.gvUtil.getInzTranslate('gSaveError');
                            }

                        });
                    }).catch(function(){});
                } else {
                    Vue.gvUtil.message(Vue.gvUtil.getInzTranslate('gValidateContent'));
                    return false;
                }
            });
        },
        // 清除表单
        resetForm: function (formName) {
            var lrcCfMainId = this.form.lrcCfMainId;
            var entityCode = this.form.entityCode;
            this.$refs[formName].resetFields();
            this.form.lrcCfMainId = '';
            if (this.type === 'edit') {
                this.form.lrcCfMainId = lrcCfMainId;
                this.form.entityCode = entityCode;
            }
        },
        // 关闭
        onClose: function () {
            this.dialogFormVisible = false;
            this.$emit('close');
        },
        onEditorChange: function (val) {
            this.form.modelContent = val.text;
        },

        // 初始化页面，低层直接调用
        initPage: function () {
            if (this.type !== 'add') {
                this.requestData();
            }
            if (this.type === 'view') {
                this.isReadonly = true;
                this.isDisabled = true;
                this.isReadonlySave = true;
            }
            if (this.type === 'add') {
                var user = sessionStorage.getItem('user');
                if (user) {
                    user = JSON.parse(user);
                    this.form.centerId = user.userCenterId;

                }
                this.selectCenterId();
            }
        },
        //配置初始化核算单位，并查询数据
        selectCenterId: function () {
            this.form.entityCode = Vue.gvUtil.setentityCode()
        },
        // 初始化校验，低层直接调用
        initRules: function () {
            this.rules = {
                entityCode: [{
                    trigger: 'blur',
                    required: true,
                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')
                }, {
                    trigger: 'change',
                    required: true,
                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')
                }],
                businessType: [{
                    trigger: 'blur',
                    required: true,
                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')
                }, {
                    trigger: 'change',
                    required: true,
                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')
                }],
                yearMonth: [{
                    trigger: 'blur',
                    required: true,
                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')
                }, {
                    trigger: 'change',
                    required: true,
                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')
                }],
                currency: [{
                    trigger: 'blur',
                    required: true,
                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')
                }, {
                    trigger: 'change',
                    required: true,
                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')
                }],
                drawTime: [{
                    trigger: 'blur',
                    required: true,
                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')
                }, {
                    trigger: 'change',
                    required: true,
                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')
                }],
            };
        },
        requestData: function () {
            var _this = this,
                url = Vue.gvUtil.getUrl({
                    apiName: 'bussLrcCashFlowFindByPk',
                    urlParams: {
                        id: _this.licAction.id
                    },
                    contextName: 'actuarial'
                });
            Vue.gvUtil.http.get(url).then(function (res) {
                if (res.resCode === '0000') {
                    // $.extend(true, _this.form, res.resData);
                    //表单基础数据查询和格式化
                    res.resData.portfolioNo = _this.licAction.portfolioNo;
                    res.resData.icgNo = _this.licAction.icgNo;
                    res.resData.riskClassCode = _this.licAction.riskClassCode;
                    _this.form=res.resData
                    _this.handleCenterData(res.resData);
                    _this.handleCurrencyData(res.resData);
                    
                    // 只有当业务类型不是TO和FO时才获取假设数据
                    if (_this.licAction.businessSourceCode !== 'TO' && _this.licAction.businessSourceCode !== 'FO') {
                        _this.findBussQuotaDataById();
                    }
                    
                    _this.initLrcFeeType(_this.licAction.businessSourceCode);
                    _this.timer = setTimeout(function(){
                        _this.requestData1();
                    },1000)// 进入该分支说明当前并没有在计时，那么就开始一个计时
                    //表单LRC的明细数据
                }
            });
        },

        //业务单位国际化
        initLrcFeeType: function(businessSourceCode) {
            var param = {
              businessSourceCode : businessSourceCode,
              becfType:'Lrc'
            };
            var _this = this,
                url = Vue.gvUtil.getUrl({
                    apiName: 'findLrcFeeTypeByCodeIdx',
                    contextName: 'actuarial'
                });
            Vue.gvUtil.http.post(url, param).then(function(res) {
                if (res.resCode === '0000') {
                    var shareData = res.resData;
                    if (!Vue.gvUtil.isEmpty(shareData)) {
                        _this.premTypeArray = shareData;
                        _this.lrcLicTab = shareData[0].remark
                    }
                }
            });
        },

        requestData1: function () {
            var _this = this,
                url = Vue.gvUtil.getUrl({
                    apiName: 'lrcFindPeriodHeader',
                    contextName: 'actuarial'
                });
            var urlParams={
                actionNo: _this.form.actionNo,
                businessSourceCode : _this.licAction.businessSourceCode,
                icgNo: _this.form.icgNo,
            };
            _this.loding = false
            Vue.gvUtil.http.post(url,urlParams).then(function (res) {
                if (res.resCode === '0000') {
                    for(let item in _this.premTypeArray){
                        var devNoFiled =_this.getPeriodFiled(res.resData.devNo, _this.premTypeArray[item]);
                        let key = _this.premTypeArray[item].remark;
                        let fee = _this.premTypeArray[item].remark;
                        let fields = feildCard.getfieldList(_this[`tableField`][`${_this.licAction.businessSourceCode}`][`${fee}DetailTableFields`]);
                        if (_this.premTypeArray[item].type=='1') {
                            fields.push(devNoFiled)
                        }
                        _this.$set(_this.tabMap,key,{
                            table: {
                                basic: {
                                    api: "findLrcDataDetail", //分页列表请求api
                                    vo: "lrcCashFlowVoList", //分页列表返回的vo
                                    context: "actuarial", //分页列表请求上下文
                                    isShowMore: true
                                },
                                search: { //查询域元数据
                                    actionNo: _this.form.actionNo,
                                    icgNo: _this.form.icgNo,
                                    businessSourceCode:_this.licAction.businessSourceCode,
                                    feeType: key,
                                },
                                fields: fields,
                            },

                            list:[]
                        })
                    }
                    _this.searchList();
                }
                _this.loding = true
            });
        },


        getPeriodFiled: function (periods, becfVo) {
            var devNoField = {
                width: "auto",
                sortable: false,
                labelKey: 'atrDevelopMonth',
                childrenFields:[]
            };
            var periodList=[];
            var start =0;
            var end = periods.length-1;
            if (!Vue.gvUtil.isEmpty(becfVo.startDevNo)) {
                start = becfVo.startDevNo;
            }
            if (!Vue.gvUtil.isEmpty(becfVo.endDevNo)) {
                end = becfVo.endDevNo;
            }
            for(start; start<= end; start++) {
                periodList[periods[start].toString()] = {
                    prop: periods[start].toString(),
                    quotaEName: periods[start].toString(),
                    quotaCName: periods[start].toString(),
                    quotaTName: periods[start].toString(),
                    fieldType: '2'
                }
            }
            devNoField.childrenFields = Vue.gvUtil.fieldSplic(periodList)
            return devNoField;
        },


        requestData3: function (feeType) {
            var _this = this,
                url = Vue.gvUtil.getUrl({
                    apiName: 'lrcFindDate',
                    contextName: 'actuarial'
                });
            var urlParams={
                actionNo: _this.form.actionNo,
                icgNO: _this.form.icgNO,
                businessSourceCode:_this.licAction.businessSourceCode,
                feeType:_this.licAction.businessSourceCode,
            };
            Vue.gvUtil.http.post(url,urlParams).then(function (res) {
                if (res.resCode === '0000') {
                   _this.tabMap.get(feeType).list = res.resData.lrcCashFlowVoList
                }
            });
        },


        selectRowCurrency: function (row) {
            if(row) {
                this.form.currency = row.currencyCode;
            } else {
                this.form.currency = '';
            }
        },
        // 业务单位国际化处理
        handleCenterData: function (centerVo) {
            var _this = this;
            _this.form.entityCode = centerVo.entityCode + ' -- ' + Vue.gvUtil.getInzName(centerVo.entityEName, centerVo.entityCName, centerVo.entityTName);
        },
        // 业务单位国际化处理
        handleCurrencyData: function (currencyVo) {
            var _this = this;
            if (Vue.gvUtil.isEmpty(Vue.gvUtil.getInzName(currencyVo.currencyEName, currencyVo.currencyCName, currencyVo.currencyTName))) {
                return _this.form.currencyName = currencyVo.currency;
            }
            _this.form.currencyName = currencyVo.currency + ' -- ' + Vue.gvUtil.getInzName(currencyVo.currencyEName, currencyVo.currencyCName, currencyVo.currencyTName);
        },
        // 保存成功后回调的方法
        successSubmit: function (data) {
            Vue.gvUtil.message(Vue.gvUtil.getInzTranslate('atrExtractionTip'),1500,'success');
            this.$emit('searchList');
            this.$emit('findLicVersionData');
            this.onClose();
        },

        /*aioi*/
        //版本操作列事件
        onListBtn: function (row, flag, prop) {
            if (flag === 'develop') {
                this.onViewQuotaDevelopPeriod(row, null, prop);
            }
        },
        //查询版本的计量假设数据
        findBussQuotaDataById:function () {
            var actionVo = {
                actionNo : this.licAction.actionNo,
                dimensionValue : this.licAction.icgNo,
                riskClassCode: this.form.riskClassCode
            };
            var _this = this,
                url = Vue.gvUtil.getUrl({
                    apiName: 'findAtrBussQuotaData',
                    contextName: 'actuarial'
                });
            Vue.gvUtil.http.post(url, actionVo).then(function (res) {
                if (res.resCode === '0000') {
                    var obj =  res.resData.data;
                    var objKeys = Object.keys(obj)
                    if (Vue.gvUtil.isEmpty(obj) || objKeys.length==0) {
                        actionVo.dimensionValue = _this.licAction.portfolioNo;
                        Vue.gvUtil.http.post(url, actionVo).then(function (res) {
                            if (res.resCode === '0000') {
                                obj =  res.resData.data;
                                _this.initQuotaTableAndData(obj);
                            }
                        });
                    } else {
                        _this.initQuotaTableAndData(obj);
                    }
                }
            });
        },
        initQuotaTableAndData: function (obj) {
            var data = {};
            var _this = this;
            var objKeys = Object.keys(obj);
            
            // 准备表格结构 - 第一行和第二行的表头结构
            var firstRowFields = [];
            var secondRowFields = [];
            
            // 第一列显示险种代码
            firstRowFields.push({
                prop: 'insuranceType',
                labelKey: 'atrInsuranceClass',
                width: "120px",
                rowspan: 2, // 第一列需要跨越两行
                headerAlign: 'center',
                headColor: _this.palettes[0]
            });
            
            // 创建只包含当前险类的 bussQuotaVoList
            var row = {
                insuranceType: this.form.riskClassCode || '' // 显示当前险类代码
            };
            
            // 处理API返回的一般假设和事故年月等分组
            for (var i = 0; i < objKeys.length; i++) {
                var quota = obj[objKeys[i]];
                var children = [];  // 存储该分组下的所有指标
                
                // 如果没有子项，跳过
                if (!quota.childQuota) continue;
                
                // 获取该组下所有的不同指标代码（非险种），如"#lic_ulae_ratio"和"#lic_ra_ratio"
                var uniqueBaseCodes = new Set();
                var childQuota = quota.childQuota;
                var childKeys = Object.keys(childQuota);
                
                for (var j = 0; j < childKeys.length; j++) {
                    var childItem = childQuota[childKeys[j]];
                    // 从完整代码中提取基础指标代码，例如从"#lic_ulae_ratio01"提取"#lic_ulae_ratio"
                    var baseCode = childItem.quotaCode;
                    uniqueBaseCodes.add(baseCode);
                }
                
                // 将Set转换为数组
                var baseCodes = Array.from(uniqueBaseCodes);
                
                // 为每个指标创建一个表头列
                for (var j = 0; j < baseCodes.length; j++) {
                    var baseCode = baseCodes[j];
                    
                    // 找到第一个匹配此指标的项目，用于获取名称
                    var sampleItem = null;
                    for (var k = 0; k < childKeys.length; k++) {
                        if (childQuota[childKeys[k]].quotaCode === baseCode) {
                            sampleItem = childQuota[childKeys[k]];
                            break;
                        }
                    }
                    
                    if (!sampleItem) continue;
                    
                    // 添加第二行表头字段（指标名称）
                    var fieldObj = {
                        prop: baseCode,
                        labelKey: Vue.gvUtil.getInzName(
                            sampleItem.quotaEName,
                            sampleItem.quotaCName,
                            sampleItem.quotaLName
                        ),
                        headColor: _this.palettes[i % 2],
                        className: _this.rowPalettes[i % 2],
                        headerAlign: 'center',
                        fieldType: sampleItem.quotaValueType,
                        quotaType: sampleItem.quotaType, // 添加quotaType属性用于判断是否是发展期指标
                        width: "120px"
                    };
                    
                    // 查找当前险类的值
                    var currentRiskClassCode = this.form.riskClassCode;
                    var fullFieldName = baseCode + currentRiskClassCode;
                    
                    // 检查该险类的这个指标是否存在
                    if (childQuota[fullFieldName]) {
                        // 如果存在，添加到行数据中
                        row[baseCode] = childQuota[fullFieldName].value;
                    }
                    
                    secondRowFields.push(fieldObj);
                    children.push(fieldObj);
                }
                
                // 创建第一行表头的分组项
                var groupField = {
                    prop: objKeys[i],
                    labelKey: Vue.gvUtil.getInzName(
                        quota.quotaEName,
                        quota.quotaCName,
                        quota.quotaLName
                    ),
                    headerAlign: 'center',
                    headColor: _this.palettes[i % 2],
                    colspan: children.length, // 子项数量
                    children: children
                };
                
                if (children.length > 0) {
                    firstRowFields.push(groupField);
                }
            }
            
            // 设置表头结构
            _this.quotaTable.fields = [];
            _this.quotaTable.firstRowFields = firstRowFields;
            _this.quotaTable.secondRowFields = secondRowFields;
            
            // 添加行数据
            _this.bussQuotaVoList = [row];
            
            // 保存原始数据以便后续使用
            _this.quotaObj = obj;
            _this.initTableHeader();
        },
        
        // 从数据中提取所有险种代码
        extractRiskClassCodes: function(obj) {
            var riskClasses = [];
            var codePattern = /(\d+)$/; // 匹配字段名末尾的数字
            
            for (var groupKey in obj) {
                var group = obj[groupKey];
                if (!group.childQuota) continue;
                
                for (var fieldKey in group.childQuota) {
                    var matches = fieldKey.match(codePattern);
                    if (matches && matches[1]) {
                        var riskClass = matches[1];
                        if (riskClasses.indexOf(riskClass) === -1) {
                            riskClasses.push(riskClass);
                        }
                    }
                }
            }
            
            return riskClasses;
        },

        //初始化合同组输出表头
        initTableHeader: function (){
            var _this = this;
            
            // 添加自定义样式
            var style = document.createElement('style');
            style.innerHTML = `
                .custom-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                .custom-table th, .custom-table td {
                    border: 1px solid #dfe6ec;
                    padding: 8px;
                }
                .custom-table thead th {
                    background-color: #f5f7fa;
                    font-weight: bold;
                    color: #606266;
                }
                .custom-table tbody tr:hover {
                    background-color: #f5f7fa;
                }
                .purple-to-gray {
                    background-color: #ECE9F7;
                }
                .light-gray {
                    background-color: #F5F7FA;
                }
            `;
            document.head.appendChild(style);
            
            // 自定义表格渲染
            _this.quotaTable.basic.custom = true;
            
            _this.loading = true;
        },
        //查看发展期指标数据
        onViewQuotaDevelopPeriod: function (row, value, prop) {
            var quotaDef;
            var objKeys = Object.keys(this.quotaObj);
            
            // 构建完整的字段名称，例如 "#lic_ulae_ratio01"
            var fullFieldName = prop + this.form.riskClassCode;
            
            // 在表格数据中查找匹配的指标
            for (var i = 0; i < objKeys.length; i++) {
                var childQuota = this.quotaObj[objKeys[i]].childQuota;
                if (!childQuota) continue;
                
                // 直接通过完整字段名查找
                if (childQuota[fullFieldName]) {
                    quotaDef = childQuota[fullFieldName];
                    break;
                } else if (childQuota[prop]) {
                    // 回退到只使用基础指标代码
                    quotaDef = childQuota[prop];
                    break;
                }
            }
            
            if (quotaDef) {
                this.quotaDtl.quota = quotaDef;
                this.findBussQuotaPeriod(prop);
            } else {
                console.error('未找到匹配的指标定义', fullFieldName);
                Vue.gvUtil.message(Vue.gvUtil.getInzTranslate('gError'));
            }
        },
        //关闭指标发展期框
        onFold: function () {
            this.periodShow = false;
        },

        //查询计量假设配置发展期数据
        findBussQuotaPeriod: function (quotaCode) {
            this.quotaDefName = Vue.gvUtil.getInzName(this.quotaDtl.quota.quotaEName, this.quotaDtl.quota.quotaCName, this.quotaDtl.quota.quotaLName);
            this.periodShow = true;   // 显示页面
            
            // 显示加载指示器或处理逻辑
            this.loading = true;
            
            var param = {
                actionNo : this.licAction.actionNo,
                dimensionValue : this.licAction.icgNo,
                quotaCode: quotaCode,
                riskClassCode: this.form.riskClassCode
            };
            
            var _this = this;
            var url = Vue.gvUtil.getUrl({
                apiName: 'findAtrBussQuotaDataDetail',
                contextName: 'actuarial'
            });
            
            Vue.gvUtil.http.post(url, param).then(function (res) {
                if (res.resCode === '0000' && res.resData) {
                    _this.bussQuotaDevelopVoList = [];
                    var obj = res.resData;
                    var objKeys = Object.keys(obj);
                    
                    if (Vue.gvUtil.isEmpty(obj) || objKeys.length == 0) {
                        param.dimensionValue = _this.licAction.portfolioNo;
                        Vue.gvUtil.http.post(url, param).then(function (res) {
                            if (res.resCode === '0000') {
                                obj = res.resData;
                                _this.initQuotaDetailTableAndData(obj);
                            }
                        }).finally(function() {
                            _this.loading = false;
                        });
                    } else {
                        _this.initQuotaDetailTableAndData(obj);
                        _this.loading = false;
                    }
                } else {
                    _this.loading = false;
                }
            }).catch(function() {
                _this.loading = false;
            }).finally(function() {
                _this.periodShow = true;
            });
        },

        // 更新发展期数据处理方法
        initQuotaDetailTableAndData: function (obj) {
            var _this = this;
            
            // 清空当前数据
            _this.bussQuotaDevelopVoList = [];
            _this.developmentColumns = [];
            
            if (!obj) return;
            
            var objKeys = Object.keys(obj);
            if (objKeys.length === 0) return;
            
            // 创建列配置
            for (var i = 0; i < objKeys.length; i++) {
                _this.developmentColumns.push({
                    prop: objKeys[i],
                    label: objKeys[i]
                });
            }
            
            // 创建行数据
            var rowData = {
                quotaPeriod: _this.quotaDefName || '发展期'
            };
            
            // 填充行数据
            for (var i = 0; i < objKeys.length; i++) {
                var key = objKeys[i];
                if (obj[key] && !Vue.gvUtil.isEmpty(obj[key].quotaValue)) {
                    rowData[key] = obj[key].quotaValue; // 使用原始值，由模板负责格式化
                } else {
                    rowData[key] = '';
                }
            }
            
            // 添加行数据
            _this.bussQuotaDevelopVoList.push(rowData);
        },

        handleClick: function (tab, event) {
            this.$nextTick(() => {
                this.searchList(tab.name);
            })
        },

        getParamsMixin: function getParamsMixin(params) {
            this.cacheFilters = Object.assign({
                _pageSize: this.mixinObject.searchSet.pageSize,
                _pageNo: this.mixinObject.searchSet.pageNo
            }, params);
            return this.cacheFilters;
        },

        /**
         * 页码变动
         * @param val 码数
         */
        onHandleCurrentChange: function onHandleCurrentChange(val) {
            if (typeof val === 'undefined') {
                return;
            }
            this.mixinObject.searchSet.pageNo = val - 1;
            this.mixinObject.isInit = true;
            this.searchList('uprDetailsVoList');
        },

        /**
         * 查询行数变动
         * @param 行数
         */
        onHandleSizeChange: function onHandleSizeChange(val) {
            this.mixinObject.searchSet.pageSize = val;
            this.mixinObject.isInit = true;
            this.searchList('uprDetailsVoList');
        },

        /**
         * 获取查询数据
         */
        searchList: function searchList(tabName) {
            tabName = Vue.gvUtil.isEmpty(this.lrcLicTab)? tabName : this.lrcLicTab;
            var urlParams={
                actionNo: this.form.actionNo,
                icgNo: this.form.icgNo,
                businessSourceCode:this.licAction.businessSourceCode,
                riskClassCode: this.form.riskClassCode,
                feeType: tabName,
            };
            var voName= 'lrcCashFlowVoList';
            if (!this.mixinObject.isInit) {
                this.mixinObject.searchSet.pageNo = 0;
                this.mixinObject.searchSet.currentPage = 1;
            } else {
                this.mixinObject.isInit = false;
            }
            var params = this.getParamsMixin(),
                url = Vue.gvUtil.getUrl({
                    apiName: 'findLrcDataDetail',
                    contextName: 'actuarial',
                    serachParms: { _pageSize: params._pageSize, _pageNo: params._pageNo }
                }),
                _this = this,
                list = [];
            Vue.gvUtil.http.post(url, urlParams).then(function (res) {
                if (res.resCode === '0000') {
                    _this.uprDetailsVoList = res.resData[voName].content;
                    _this.$set(_this.tabMap[tabName],'list',res.resData[voName].content)
                    _this.mixinObject.searchSet.total = res.resData[voName]['total'] ? res.resData[voName].total : res.resData[voName].totalElements;
                    _this.lrcLicTab = tabName;
                } else {
                    _this.mixinObject.searchSet.total = 0;
                    _this.lrcLicTab = tabName;
                }
            });
        },

        handleSetList(list){
            let tableObj = this.tabMap[this.lrcLicTab]
            this.$set(tableObj,'list',list)
            this.$set(this.tabMap,this.lrcLicTab,tableObj)

        },
    },
    mounted() {
        this.$nextTick(() => {
            this.showRiskClassCode = this.licAction && (this.licAction.businessSourceCode === 'DD' || this.licAction.businessSourceCode === 'TI');
        });
    },
}

</script>