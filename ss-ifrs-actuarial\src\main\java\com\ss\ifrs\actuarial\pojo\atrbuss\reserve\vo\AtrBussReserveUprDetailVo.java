/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2022-06-23 17:24:59
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2022-06-23 17:24:59<br/>
 * Description: upr结果明细表<br/>
 * Table Name: atr_buss_reserve_upr_detail<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "upr结果明细表")
public class AtrBussReserveUprDetailVo implements Serializable {
    /**
     * Database column: atr_buss_reserve_upr_detail.upr_detail_id
     * Database remarks: 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    private Long uprDetailId;

    /**
     * Database column: atr_buss_reserve_upr_detail.reserve_upr_id
     * Database remarks: 主表id
     */
    @ApiModelProperty(value = "主表id", required = true)
    private Long reserveUprId;

    /**
     * Database column: atr_buss_reserve_upr_detail.risk_class
     * Database remarks: risk_class|风险大类
     */
    @ApiModelProperty(value = "risk_class|风险大类", required = false)
    private String riskClassCode;

    /**
     * Database column: atr_buss_reserve_upr_detail.risk_code
     * Database remarks: 险种
     */
    @ApiModelProperty(value = "险种", required = false)
    private String riskCode;

    /**
     * Database column: atr_buss_reserve_upr_detail.cmunit_no
     * Database remarks: 计量单元号
     */
    @ApiModelProperty(value = "计量单元号", required = false)
    private String cmunitNo;

    /**
     * Database column: atr_buss_reserve_upr_detail.policy_no
     * Database remarks: 保单号
     */
    @ApiModelProperty(value = "保单号", required = false)
    private String policyNo;

    /**
     * Database column: atr_buss_reserve_upr_detail.endorse_seq_no
     * Database remarks: 批改序号
     */
    @ApiModelProperty(value = "批改序号", required = false)
    private String endorseSeqNo;

    /**
     * Database column: atr_buss_reserve_upr_detail.endorse_no
     * Database remarks: endorse_no|批单号
     */
    @ApiModelProperty(value = "endorse_no|批单号", required = false)
    private String endorseNo;

    /**
     * Database column: atr_buss_reserve_upr_detail.business_type
     * Database remarks: businessSourceCode|业务类型
     */
    @ApiModelProperty(value = "businessSourceCode|业务类型", required = false)
    private String businessSourceCode;

    /**
     * Database column: atr_buss_reserve_upr_detail.reins_type
     * Database remarks: reins_type|再保类型(DN直保，RN再保)
     */
    @ApiModelProperty(value = "reins_type|再保类型(DN直保，RN再保)", required = false)
    private String reinsType;

    /**
     * Database column: atr_buss_reserve_upr_detail.start_date
     * Database remarks: start_date|开始时间
     */
    @ApiModelProperty(value = "start_date|开始时间", required = false)
    private Date startDate;

    /**
     * Database column: atr_buss_reserve_upr_detail.end_date
     * Database remarks: end_date|结束时间
     */
    @ApiModelProperty(value = "end_date|结束时间", required = false)
    private Date endDate;

    /**
     * Database column: atr_buss_reserve_upr_detail.currency
     * Database remarks: currency|币别
     */
    @ApiModelProperty(value = "currency|币别", required = false)
    private String currencyCode;

    /**
     * Database column: atr_buss_reserve_upr_detail.premium
     * Database remarks: premium|保费
     */
    @ApiModelProperty(value = "premium|保费", required = false)
    private BigDecimal premium;

    /**
     * Database column: atr_buss_reserve_upr_detail.draw_premium
     * Database remarks: draw_premium|未了责任保费
     */
    @ApiModelProperty(value = "draw_premium|未了责任保费", required = false)
    private BigDecimal drawPremium;

    /**
     * Database column: atr_buss_reserve_upr_detail.draw_premium_adjust
     * Database remarks: draw_premium_adjust|提取保费
     */
    @ApiModelProperty(value = "draw_premium_adjust|提取保费", required = false)
    private BigDecimal drawPremiumAdjust;

    /**
     * Database column: atr_buss_reserve_upr_detail.icg_no
     * Database remarks: 合同组号码
     */
    @ApiModelProperty(value = "合同组号码", required = false)
    private String icgNo;

    /**
     * Database column: atr_buss_reserve_upr_detail.check_date
     * Database remarks: Check Date|核保日期
     */
    @ApiModelProperty(value = "Check Date|核保日期", required = false)
    private Date checkDate;

    /**
     * Database column: atr_buss_reserve_upr_detail.accrual_method
     * Database remarks: Accrual Method|计提方法
     */
    @ApiModelProperty(value = "Accrual Method|计提方法", required = false)
    private String atrMethod;

    private String versionNo;
    private Long entityId;
    private String yearMonth;

    private String entityCode;
    private String entityEName;
    private String entityCName;
    private String entityLName;

    private String loaCName;
    private String loaEName;
    private String loaLName;

    private String classCName;
    private String classEName;
    private String classLName;



    private String riskCName;
    private String riskEName;
    private String riskLName;

    private BigDecimal beforePremium;
    private BigDecimal unearnedBeforePremium;
    private BigDecimal outwardPremium;
    private BigDecimal unearnedOutwardPremium;
    private BigDecimal afterPremium;
    private BigDecimal unearnedAfterPremium;

    private String loaCode;

    private static final long serialVersionUID = 1L;

    public String getVersionNo() {
        return versionNo;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }	

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getEntityCode() {
        return entityCode;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public String getEntityEName() {
        return entityEName;
    }

    public void setEntityEName(String entityEName) {
        this.entityEName = entityEName;
    }

    public String getEntityCName() {
        return entityCName;
    }

    public void setEntityCName(String entityCName) {
        this.entityCName = entityCName;
    }

    public String getEntityLName() {
        return entityLName;
    }

    public void setEntityLName(String entityLName) {
        this.entityLName = entityLName;
    }

    public String getLoaCName() {
        return loaCName;
    }

    public void setLoaCName(String loaCName) {
        this.loaCName = loaCName;
    }

    public String getLoaEName() {
        return loaEName;
    }

    public void setLoaEName(String loaEName) {
        this.loaEName = loaEName;
    }

    public String getLoaLName() {
        return loaLName;
    }

    public void setLoaLName(String loaLName) {
        this.loaLName = loaLName;
    }

    public String getRiskCName() {
        return riskCName;
    }

    public void setRiskCName(String riskCName) {
        this.riskCName = riskCName;
    }

    public String getRiskEName() {
        return riskEName;
    }

    public void setRiskEName(String riskEName) {
        this.riskEName = riskEName;
    }

    public String getRiskLName() {
        return riskLName;
    }

    public void setRiskLName(String riskLName) {
        this.riskLName = riskLName;
    }

    public BigDecimal getBeforePremium() {
        return beforePremium;
    }

    public void setBeforePremium(BigDecimal beforePremium) {
        this.beforePremium = beforePremium;
    }

    public BigDecimal getUnearnedBeforePremium() {
        return unearnedBeforePremium;
    }

    public void setUnearnedBeforePremium(BigDecimal unearnedBeforePremium) {
        this.unearnedBeforePremium = unearnedBeforePremium;
    }

    public BigDecimal getOutwardPremium() {
        return outwardPremium;
    }

    public void setOutwardPremium(BigDecimal outwardPremium) {
        this.outwardPremium = outwardPremium;
    }

    public BigDecimal getUnearnedOutwardPremium() {
        return unearnedOutwardPremium;
    }

    public void setUnearnedOutwardPremium(BigDecimal unearnedOutwardPremium) {
        this.unearnedOutwardPremium = unearnedOutwardPremium;
    }

    public BigDecimal getAfterPremium() {
        return afterPremium;
    }

    public void setAfterPremium(BigDecimal afterPremium) {
        this.afterPremium = afterPremium;
    }

    public BigDecimal getUnearnedAfterPremium() {
        return unearnedAfterPremium;
    }

    public void setUnearnedAfterPremium(BigDecimal unearnedAfterPremium) {
        this.unearnedAfterPremium = unearnedAfterPremium;
    }

    public Long getUprDetailId() {
        return uprDetailId;
    }

    public void setUprDetailId(Long uprDetailId) {
        this.uprDetailId = uprDetailId;
    }

    public Long getReserveUprId() {
        return reserveUprId;
    }

    public void setReserveUprId(Long reserveUprId) {
        this.reserveUprId = reserveUprId;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public String getRiskCode() {
        return riskCode;
    }

    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    public String getCmunitNo() {
        return cmunitNo;
    }

    public void setCmunitNo(String cmunitNo) {
        this.cmunitNo = cmunitNo;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getEndorseSeqNo() {
        return endorseSeqNo;
    }

    public void setEndorseSeqNo(String endorseSeqNo) {
        this.endorseSeqNo = endorseSeqNo;
    }

    public String getEndorseNo() {
        return endorseNo;
    }

    public void setEndorseNo(String endorseNo) {
        this.endorseNo = endorseNo;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getReinsType() {
        return reinsType;
    }

    public void setReinsType(String reinsType) {
        this.reinsType = reinsType;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public BigDecimal getPremium() {
        return premium;
    }

    public void setPremium(BigDecimal premium) {
        this.premium = premium;
    }

    public BigDecimal getDrawPremium() {
        return drawPremium;
    }

    public void setDrawPremium(BigDecimal drawPremium) {
        this.drawPremium = drawPremium;
    }

    public BigDecimal getDrawPremiumAdjust() {
        return drawPremiumAdjust;
    }

    public void setDrawPremiumAdjust(BigDecimal drawPremiumAdjust) {
        this.drawPremiumAdjust = drawPremiumAdjust;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public Date getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(Date checkDate) {
        this.checkDate = checkDate;
    }

    public String getAtrMethod() {
        return atrMethod;
    }

    public void setAtrMethod(String atrMethod) {
        this.atrMethod = atrMethod;
    }

    public String getClassCName() {
        return classCName;
    }

    public void setClassCName(String classCName) {
        this.classCName = classCName;
    }

    public String getClassEName() {
        return classEName;
    }

    public void setClassEName(String classEName) {
        this.classEName = classEName;
    }

    public String getClassLName() {
        return classLName;
    }

    public void setClassLName(String classLName) {
        this.classLName = classLName;
    }
}