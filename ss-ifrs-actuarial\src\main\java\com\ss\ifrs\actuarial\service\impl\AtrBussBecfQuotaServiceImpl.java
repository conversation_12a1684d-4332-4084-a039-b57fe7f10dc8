package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.dao.buss.AtrBussQuotaDao;
import com.ss.ifrs.actuarial.feign.BmsConfCodeFeignClient;
import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrBussBecfViewVo;
import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrBussQuotaVo;
import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrBussQuotaDetailVo;
import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrConfBecfOutPutVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussQuotaValueVo;
import com.ss.ifrs.actuarial.service.AtrBussBecfQuotaService;
import com.ss.ifrs.actuarial.util.EcfUtil;
import com.ss.library.utils.DataUtil;
import com.ss.platform.pojo.com.po.ConfCode;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.HashSet;
import java.util.Set;

@Service
public class AtrBussBecfQuotaServiceImpl implements AtrBussBecfQuotaService {
    @Autowired(required = false)
    private BmsConfCodeFeignClient bplConfCodeFeignClient;

    @Autowired(required = false)
    private AtrBussQuotaDao atrBussQuotaDao;

    @Override
    public Map<String, Object> findBussQuotaData(AtrBussQuotaValueVo quotaValueVo) {
        LinkedHashMap<String, AtrBussQuotaVo> dataMap;
        Map<String, AtrBussQuotaVo> groupMap = new LinkedHashMap<>();
        Map<String, Object> resultMap = new LinkedHashMap<>();
        Set<String> riskClassCodeSet = new HashSet<>();
        AtrBussQuotaVo licRdRatioVo = null;

        List<ConfCode> bplCodeList = bplConfCodeFeignClient.findCodeListByCodeType("QuotaGroup");
        for(ConfCode bplCodePo : bplCodeList) {
            dataMap = new LinkedHashMap<>();
            //quotaValueVo.setQuotaType("0");
            List<AtrBussQuotaVo> confQuotaDefVoList = atrBussQuotaDao.findBussQuotaVoByAtrActionVo(quotaValueVo, bplCodePo.getCodeCode());
            if(ObjectUtils.isEmpty(confQuotaDefVoList)) {
                continue;
            }
            
            // 收集所有risk_class_code
            for(AtrBussQuotaVo vo : confQuotaDefVoList) {
                if(ObjectUtils.isNotEmpty(vo.getRiskClassCode())) {
                    riskClassCodeSet.add(vo.getRiskClassCode());
                }
                // 保存LIC_RD_RATIO值
                if(EcfUtil.Q_LIC_RD_RATIO.equals(vo.getQuotaCode())) {
                    licRdRatioVo = vo;
                }
            }
            
            for(AtrBussQuotaVo vo : confQuotaDefVoList) {
                vo.setValue("4".equals(vo.getQuotaValueType())? tranPercentValue(vo.getValue()) : vo.getValue());
                if(ObjectUtils.isNotEmpty(vo.getQuotaCode()) && !EcfUtil.Q_LIC_RD_RATIO.equals(vo.getQuotaCode())) {
                    dataMap.put(vo.getQuotaCode()+vo.getRiskClassCode(), vo);
                }
            }
            
            // 对每个险类设置相同的LIC_RD_RATIO值
            if(licRdRatioVo != null) {
                for(String riskClassCode : riskClassCodeSet) {
                    AtrBussQuotaVo newLicRdRatioVo = new AtrBussQuotaVo();
                    // 复制原始对象的属性
                    newLicRdRatioVo.setQuotaDefId(licRdRatioVo.getQuotaDefId());
                    newLicRdRatioVo.setQuotaCode(licRdRatioVo.getQuotaCode());
                    newLicRdRatioVo.setQuotaCName(licRdRatioVo.getQuotaCName());
                    newLicRdRatioVo.setQuotaEName(licRdRatioVo.getQuotaEName());
                    newLicRdRatioVo.setQuotaLName(licRdRatioVo.getQuotaLName());
                    newLicRdRatioVo.setQuotaType(licRdRatioVo.getQuotaType());
                    newLicRdRatioVo.setQuotaValueType(licRdRatioVo.getQuotaValueType());
                    newLicRdRatioVo.setCodeType(licRdRatioVo.getCodeType());
                    newLicRdRatioVo.setValue(licRdRatioVo.getValue());
                    newLicRdRatioVo.setRiskClassCode(riskClassCode);
                    // 添加到dataMap中
                    dataMap.put(newLicRdRatioVo.getQuotaCode()+riskClassCode, newLicRdRatioVo);
                }
            }
            
            AtrBussQuotaVo atrConfQuotaDefVo = new AtrBussQuotaVo();
            atrConfQuotaDefVo.setQuotaCode(bplCodePo.getCodeCode());
            atrConfQuotaDefVo.setQuotaEName(bplCodePo.getCodeEName());
            atrConfQuotaDefVo.setQuotaCName(bplCodePo.getCodeCName());
            atrConfQuotaDefVo.setQuotaLName(bplCodePo.getCodeLName());
            atrConfQuotaDefVo.setChildQuota(dataMap);
            groupMap.put(atrConfQuotaDefVo.getQuotaCode(), atrConfQuotaDefVo);
        }
        resultMap.put("data", groupMap);
        return resultMap;
    }

    @Override
    public Map<String, Object> findBussQuotaDataDetail(AtrBussQuotaValueVo atrBussQuotaValueVo) {
        Map<String, Object> resultMap = new LinkedHashMap<>();

        List<AtrBussQuotaDetailVo> periodList = atrBussQuotaDao.findBussQuotaPeriod(atrBussQuotaValueVo);
        for(AtrBussQuotaDetailVo vo : periodList) {
            resultMap.put(vo.getQuotaPeriod() + "", vo);
        }
        return resultMap;
    }

    @Override
    public List<AtrConfBecfOutPutVo> findBecfOutPut(AtrBussQuotaValueVo atrBussQuotaValueVo) {
        return atrBussQuotaDao.findBecfOutPut(atrBussQuotaValueVo);
    }

    @Override
    public List<AtrConfBecfOutPutVo> findBecfListByIds(AtrBussBecfViewVo atrBussBecfViewVo) {
        return atrBussQuotaDao.findBecfListByIds(atrBussBecfViewVo);
    }

    public String tranPercentValue(String quotaValue) {
        if (ObjectUtils.isEmpty(quotaValue)) {
            return quotaValue;
        }
        BigDecimal percent = new BigDecimal(100);
        return DataUtil.convert2String(new BigDecimal(quotaValue).multiply(percent).setScale(4, BigDecimal.ROUND_HALF_UP));
    }
}
