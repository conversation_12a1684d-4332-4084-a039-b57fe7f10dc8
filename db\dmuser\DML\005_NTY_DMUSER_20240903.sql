--修改保单数据保费一致性校验规则脚本
UPDATE dm_conf_checkrule SET rule_sql = 'select id
  from odsuser.ods_policy_payment_plan x
 where exists
 (select 1
          from odsuser.ods_policy_payment_plan a
         where a.entity_code = x.entity_code
           and a.policy_no = x.policy_no
           and a.endorse_seq_no = x.endorse_seq_no
           and a.task_code = x.task_code
         group by a.entity_code,
                  a.policy_no,
                  a.endorse_seq_no,
                  a.risk_code,
                  a.currency_code
        having abs((select coalesce(sum(b.premium), 0)
                 from dm_policy_premium b
                where a.entity_code = b.entity_code
                  and a.policy_no = b.policy_no
                  and a.endorse_seq_no = b.endorse_seq_no
                  and a.risk_code = b.risk_code
                  and a.currency_code = b.currency_code) - coalesce(sum(a.est_payment_amount), 0)) > to_number(''0.1'', ''999999999.99999999'') )', valid_is = '1'
WHERE rule_code = 'POLICY_PAYMENT_PLAN_PREMUIM_UNIFORMITY_IS';