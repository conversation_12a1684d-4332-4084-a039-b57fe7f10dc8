create or replace package rpt_pack_trial is


    --同步计量输出数据
    PROCEDURE proc_sync_atr_evaluate_result(p_entity_id        NUMBER,
                                            p_year_month_start VARCHAR2,
                                            p_year_month_end   VARCHAR2,
                                            p_oper_id          NUMBER);
    -- 提取计量数据
    PROCEDURE proc_draw_qtc_evaluate(p_entity_id        NUMBER,
                                     p_user_id          NUMBER,
                                     p_start_year_month varchar2,
                                     p_end_year_month   varchar2,
                                     p_qtc_version_no   varchar2,
                                     p_icg_no           varchar2,
                                     p_model_id         number,
                                     p_loa_code         varchar2,
                                     p_trial_record_id  number);
end rpt_pack_trial;
/

create or replace PACKAGE BODY rpt_pack_trial IS

    --同步计量输出数据
    PROCEDURE proc_sync_atr_evaluate_result(p_entity_id        NUMBER,
                                            p_year_month_start VARCHAR2,
                                            p_year_month_end   VARCHAR2,
                                            p_oper_id          NUMBER) as
        v_serialno_src_count NUMBER;
        v_log_msg            VARCHAR2(4000); --日志信息
        v_error_msg          VARCHAR2(200); --异常信息
    begin

        --查看源数据表是否有数据,只要EXECUTION_STATE = S 的数据，代表已完成
        SELECT COUNT(1)
        INTO v_serialno_src_count
        FROM QTCUSER.qtc_buss_evaluate_main t
        WHERE t.entity_id = p_entity_id
          AND t.year_month between p_year_month_start and p_year_month_end
          and t.EXECUTION_STATE = 'S';

        dbms_output.put_line('计量输出源数据表数据量为:' || v_serialno_src_count);

        IF v_serialno_src_count = 0 THEN
            --源数据表无数据，不同步
            --抛出自定义异常信息 【会中断事务】
            v_error_msg := '计量输出源数据表数据量为:' || v_serialno_src_count || '，不执行同步：[' ||
                           p_entity_id || ']-[' || p_year_month_start || ']-[' || p_year_month_end || ']';
            --往外层抛出异常信息
            raise_application_error(-20002, v_error_msg);
        END IF;

        -- 备份目标表数据
        insert into rpt_dap_qtc_evaluatehis
        (dap_his_id,
         dap_id,
         evaluate_main_id,
         entity_id,
         model_def_id,
         currency_code,
         evaluate_date,
         year_month,
         version_no,
         confirm_is,
         confirm_id,
         confirm_time,
         business_model,
         business_direction,
         loa_code,
         portfolio_no,
         icg_no,
         unit_no,
         dev_period,
         var1,
         var2,
         var3,
         var4,
         var5,
         num1,
         num2,
         num3,
         num4,
         num5,
         num6,
         num7,
         num8,
         num9,
         num10,
         num11,
         num12,
         num13,
         num14,
         num15,
         num16,
         num17,
         num18,
         num19,
         num20,
         num21,
         num22,
         num23,
         num24,
         num25,
         num26,
         num27,
         num28,
         num29,
         num30,
         num31,
         num32,
         num33,
         num34,
         num35,
         num36,
         num37,
         num38,
         num39,
         num40,
         num41,
         num42,
         num43,
         num44,
         num45,
         num46,
         num47,
         num48,
         num49,
         num50,
         num51,
         num52,
         num53,
         num54,
         num55,
         num56,
         num57,
         num58,
         num59,
         num60,
         num61,
         num62,
         num63,
         num64,
         num65,
         num66,
         num67,
         num68,
         num69,
         num70,
         num71,
         num72,
         num73)
        select RPT_SEQ_DAP_QTC_EVALUATEHIS.nextval,
               t.dap_id,
               t.evaluate_main_id,
               t.entity_id,
               t.model_def_id,
               t.currency_code,
               t.evaluate_date,
               t.year_month,
               t.version_no,
               t.confirm_is,
               t.confirm_id,
               t.confirm_time,
               t.business_model,
               t.business_direction,
               t.loa_code,
               t.portfolio_no,
               t.icg_no,
               t.unit_no,
               t.dev_period,
               t.var1,
               t.var2,
               t.var3,
               t.var4,
               t.var5,
               t.num1,
               t.num2,
               t.num3,
               t.num4,
               t.num5,
               t.num6,
               t.num7,
               t.num8,
               t.num9,
               t.num10,
               t.num11,
               t.num12,
               t.num13,
               t.num14,
               t.num15,
               t.num16,
               t.num17,
               t.num18,
               t.num19,
               t.num20,
               t.num21,
               t.num22,
               t.num23,
               t.num24,
               t.num25,
               t.num26,
               t.num27,
               t.num28,
               t.num29,
               t.num30,
               t.num31,
               t.num32,
               t.num33,
               t.num34,
               t.num35,
               t.num36,
               t.num37,
               t.num38,
               t.num39,
               t.num40,
               t.num41,
               t.num42,
               t.num43,
               t.num44,
               t.num45,
               t.num46,
               t.num47,
               t.num48,
               t.num49,
               t.num50,
               t.num51,
               t.num52,
               t.num53,
               t.num54,
               t.num55,
               t.num56,
               t.num57,
               t.num58,
               t.num59,
               t.num60,
               t.num61,
               t.num62,
               t.num63,
               t.num64,
               t.num65,
               t.num66,
               t.num67,
               t.num68,
               t.num69,
               t.num70,
               t.num71,
               t.num72,
               t.num73
        from rpt_dap_qtc_evaluate t
        where t.ENTITY_ID = p_entity_id
          and t.year_month between p_year_month_start and p_year_month_end;

        -- 删除原表数据
        delete
        from rpt_dap_qtc_evaluate t
        where t.ENTITY_ID = p_entity_id
          and t.year_month between p_year_month_start and p_year_month_end;
        commit;
        -- 同步计量输出表数据
        insert into rpt_dap_qtc_evaluate
        (dap_id,
         evaluate_main_id,
         entity_id,
         model_def_id,
         currency_code,
         evaluate_date,
         year_month,
         version_no,
         confirm_is,
         confirm_id,
         confirm_time,
         business_model,
         business_direction,
         loa_code,
         portfolio_no,
         icg_no,
         unit_no,
         dev_period,
         var1,
         var2,
         var3,
         var4,
         var5,
         num1,
         num2,
         num3,
         num4,
         num5,
         num6,
         num7,
         num8,
         num9,
         num10,
         num11,
         num12,
         num13,
         num14,
         num15,
         num16,
         num17,
         num18,
         num19,
         num20,
         num21,
         num22,
         num23,
         num24,
         num25,
         num26,
         num27,
         num28,
         num29,
         num30,
         num31,
         num32,
         num33,
         num34,
         num35,
         num36,
         num37,
         num38,
         num39,
         num40,
         num41,
         num42,
         num43,
         num44,
         num45,
         num46,
         num47,
         num48,
         num49,
         num50,
         num51,
         num52,
         num53,
         num54,
         num55,
         num56,
         num57,
         num58,
         num59,
         num60,
         num61,
         num62,
         num63,
         num64,
         num65,
         num66,
         num67,
         num68,
         num69,
         num70,
         num71,
         num72,
         num73)
        select rpt_seq_dap_qtc_evaluate.nextval,
               t.evaluate_main_id,
               t.entity_id,
               t.model_def_id,
               t.currency_code,
               t.evaluate_date,
               t.year_month,
               t.version_no,
               t.confirm_is,
               t.confirm_id,
               t.confirm_time,
               t.business_model,
               t.business_direction,
               t.loa_code,
               r.portfolio_no,
               r.icg_no,
               r.unit_no,
               r.dev_period,
               r.var1,
               r.var2,
               r.var3,
               r.var4,
               r.var5,
               r.num1,
               r.num2,
               r.num3,
               r.num4,
               r.num5,
               r.num6,
               r.num7,
               r.num8,
               r.num9,
               r.num10,
               r.num11,
               r.num12,
               r.num13,
               r.num14,
               r.num15,
               r.num16,
               r.num17,
               r.num18,
               r.num19,
               r.num20,
               r.num21,
               r.num22,
               r.num23,
               r.num24,
               r.num25,
               r.num26,
               r.num27,
               r.num28,
               r.num29,
               r.num30,
               r.num31,
               r.num32,
               r.num33,
               r.num34,
               r.num35,
               r.num36,
               r.num37,
               r.num38,
               r.num39,
               r.num40,
               r.num41,
               r.num42,
               r.num43,
               r.num44,
               r.num45,
               r.num46,
               r.num47,
               r.num48,
               r.num49,
               r.num50,
               r.num51,
               r.num52,
               r.num53,
               r.num54,
               r.num55,
               r.num56,
               r.num57,
               r.num58,
               r.num59,
               r.num60,
               r.num61,
               r.num62,
               r.num63,
               r.num64,
               r.num65,
               r.num66,
               r.num67,
               r.num68,
               r.num69,
               r.num70,
               r.num71,
               r.num72,
               r.num73
        from QTCUSER.qtc_buss_evaluate_main t
                 left join QTCUSER.qtc_buss_evaluate_result r
                           on t.EVALUATE_MAIN_ID = r.EVALUATE_MAIN_ID
        where t.ENTITY_ID = p_entity_id
          and t.year_month between p_year_month_start and p_year_month_end
          and t.EXECUTION_STATE = 'S';
        --提交事务
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            --抛出自定义异常信息 【会中断事务】
            v_log_msg := substr('报表同步计量输出表发生异常，请检查！：' || v_error_msg ||
                                '；**SQLERRM: ' || SQLERRM || '**Error line: ' ||
                                dbms_utility.format_error_backtrace() || '',
                                1,
                                4000);
            --往外层抛出异常信息
            raise_application_error(-20003, v_log_msg);
    end proc_sync_atr_evaluate_result;

    PROCEDURE proc_draw_qtc_evaluate(p_entity_id        NUMBER,
                                     p_user_id          NUMBER,
                                     p_start_year_month varchar2,
                                     p_end_year_month   varchar2,
                                     p_qtc_version_no   varchar2,
                                     p_icg_no           varchar2,
                                     p_model_id         number,
                                     p_loa_code         varchar2,
                                     p_trial_record_id  number) is
        /***********************************************************************
            NAME : proc_draw_qtc_evaluate
            DESCRIPTION : 按条件提取计量输出表对应的因子数据至buss表，因不涉及计算，所以不入过程表
            DATE :2024-01-11
            AUTHOR :XZX
        ***********************************************************************/
        v_log_msg       VARCHAR2(4000); --日志信息
        v_error_msg     VARCHAR2(2000); --异常信息
        v_new_serial_no NUMBER(6, 0); --新版本号
        v_sql           VARCHAR2(4000) := '';
        v_condition     VARCHAR2(4000) := '';
    begin
        -- 查找最新的版本号
        select coalesce(max(SERIAL_NO), 0) + 1
        into v_new_serial_no
        from RPT_BUSS_TRIAL_REPORT_ITEM_DATA
        WHERE TRIAL_RECORD_ID = p_trial_record_id;

        --删除对应数据
        insert into RPT_BUSS_TRIAL_REPORT_ITEM_DATAHIS(REPORT_ITEM_DATA_HIS_ID,
                                                       REPORT_ITEM_DATA_ID,
                                                       SERIAL_NO,
                                                       trial_record_id,
                                                       ENTITY_ID,
                                                       BOOK_CODE,
                                                       YEAR_MONTH,
                                                       REPORT_ITEM_ID,
                                                       EXPR_TYPE,
                                                       CURRENCY_CODE,
                                                       AMOUNT,
                                                       CREATOR_ID,
                                                       CREATE_TIME,
                                                       REPORT_ITEM_RULE_SERIAL_NO)
        select rpt_seq_buss_rpt_item_datahis.nextval,
               REPORT_ITEM_DATA_ID,
               SERIAL_NO,
               trial_record_id,
               ENTITY_ID,
               BOOK_CODE,
               YEAR_MONTH,
               REPORT_ITEM_ID,
               EXPR_TYPE,
               CURRENCY_CODE,
               AMOUNT,
               CREATOR_ID,
               CREATE_TIME,
               REPORT_ITEM_RULE_SERIAL_NO
        from RPT_BUSS_TRIAL_REPORT_ITEM_DATA
        WHERE TRIAL_RECORD_ID = p_trial_record_id;

        DELETE
        FROM RPT_BUSS_TRIAL_REPORT_ITEM_DATA
        WHERE TRIAL_RECORD_ID = p_trial_record_id;
        commit;

        -- 提前处理条件
        v_condition := ' where t.ENTITY_ID = ' || p_entity_id || ' ' || '
                        and t.YEAR_MONTH between ' || p_start_year_month || ' ' || '
                        and ' || p_end_year_month;
        if p_model_id is not null then
            v_condition := v_condition || ' and t.MODEL_DEF_ID = ' || p_model_id || ' ';
        end if;
        if p_icg_no is not null and p_icg_no !=  '' then
            v_condition := v_condition || ' and t.ICG_NO = ''' || p_icg_no || ''' ' ;
        end if;
        if p_loa_code is not null and p_loa_code !=  '' then
            v_condition := v_condition || ' and t.LOA_CODE = ''' || p_loa_code || ''' ';
        end if;
        -- 给版本号就确定版本号，不给就取已确认的数据
        if p_qtc_version_no is not null and p_qtc_version_no !=  '' then
            v_condition := v_condition || ' and t.VERSION_NO = ''' || p_qtc_version_no || ''' ';
        else
            v_condition := v_condition || ' and t.CONFIRM_IS = ''1'' ';
        end if;
        -- 针对配置的计量的列报项进行提数
        for rec_item in ( SELECT DISTINCT r1.report_item_id,
                                          r2.report_item_code,
                                          r1.expr_desc,
                                          r1.valid_is,
                                          r1.audit_state,
                                          r1.NEED_DIMENSION_IS,
                                          r1.report_item_rule_id,
                                          r1.SERIAL_NO,
                                          m.MODEL_CODE,
                                          m.TAB_COL_NAME
                          FROM rpt_conf_report_item_rule r1
                                   LEFT JOIN rpt_conf_report_item r2
                                             ON r2.report_item_id = r1.report_item_id
                                   left join RPT_CONF_ITEM_RULE_QTC_MODEL m
                                             on r1.REPORT_ITEM_RULE_ID = m.REPORT_ITEM_RULE_ID
                          WHERE r1.entity_id = p_entity_id --业务单位id
                            AND r1.deal_type = '1'         -- 1-提取统计
                            AND r1.data_source = '6' -- 6 -计量数据
            )
            loop
                v_sql := 'insert into RPT_BUSS_TRIAL_REPORT_ITEM_DATA (report_item_data_id,
                                                       serial_no,
                                                       trial_record_id,
                                                       entity_id,
                                                       book_code,
                                                       year_month,
                                                       report_item_id,
                                                       expr_type,
                                                       currency_code,
                                                       amount,
                                                       creator_id,
                                                       create_time,
                                                       report_item_rule_serial_no)
                select rpt_seq_buss_rpt_item_datahis.nextval,'
                    || v_new_serial_no || ' ,'
                    || p_trial_record_id || ' ,'
                    || p_entity_id || ',
                       ''BookI17'',
                       d.year_month,
                       ' || rec_item.REPORT_ITEM_ID || ',
                       null,
                       d.CURRENCY_CODE,
                       d.amount,
                       ' || p_user_id || ',
                       sysdate,
                       ' || rec_item.SERIAL_NO || '
                 from (select sum(t.' || rec_item.TAB_COL_NAME || ')  as amount,
                             t.YEAR_MONTH as year_month,
                             t.CURRENCY_CODE
                      from RPT_DAP_QTC_EVALUATE t
                     ' || v_condition || '
                      group by t.YEAR_MONTH, t.CURRENCY_CODE) d';
                --dbms_output.put_line(v_sql);
                EXECUTE IMMEDIATE v_sql;

            end loop;
        commit;
    EXCEPTION
        WHEN OTHERS THEN
            -- 发生异常回滚
            ROLLBACK;
            v_log_msg := substr('提取计量数据异常：' || v_error_msg || '; ' || '**SQLERRM: ' ||
                                SQLERRM || '**Error line: ' || dbms_utility.format_error_backtrace() || '', 1, 4000);

            -- 往外层抛自定义异常
            raise_application_error(-20003, v_log_msg);


    end proc_draw_qtc_evaluate;
END rpt_pack_trial;
/

