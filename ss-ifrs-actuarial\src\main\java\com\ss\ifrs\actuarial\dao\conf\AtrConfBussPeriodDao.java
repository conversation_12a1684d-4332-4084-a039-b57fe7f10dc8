/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-07-08 11:25:49
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao.conf;

import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfBussPeriod;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.ifrs.actuarial.pojo.domain.vo.buss.BussPeriodReqVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.platform.pojo.com.vo.TrackWorkFlowActionVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12). Create Date:
 * 2021-07-08 11:25:49<br/>
 * Description: null Dao类<br/>
 * Related Table Name: atr_conf_bussperiod<br/>
 * <br/>
 * Remark: 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrConfBussPeriodDao extends IDao<AtrConfBussPeriod, Long> {

	Page<AtrConfBussPeriodVo> fuzzySearchPage(AtrConfBussPeriodVo vo, Pageable pageParam);

	void updateNext(AtrConfBussPeriod atrConfBussPeriod);

	ArrayList<AtrConfBussPeriod> findBefore(AtrConfBussPeriod atrConfBussPeriod);

	AtrConfBussPeriodVo findByVo(AtrConfBussPeriodVo bussPeriodVo);

	List<AtrConfBussPeriod> findExecBussPeriod(AtrConfBussPeriod po);
	
	List<AtrConfBussPeriodVo> findBussPeriodVoForJob(Long entityId, String periodState);

	List<TrackWorkFlowActionVo> findAtrOwActionLog(AtrConfBussPeriodVo atrConfBussPeriod);

	/**
	 * 查询未完成的业务期间
	 */
	List<String> findIncomplete();

	AtrConfBussPeriod checkYearMonthExecutable(BussPeriodReqVo bussPeriodReqVo);

	// no proc
	String getMaxYearMonth(@Param("entityId") Long entityId);

	void readyAllDetails(@Param("entityId") Long entityId,
						 @Param("yearMonth") String yearMonth,
						 @Param("now") Date now);

	/**
	 * @param ran 屏蔽缓存
	 */
	int countNotReady(@Param("bussPeriodId") Long bussPeriodId,
					  @Param("direction") String direction,
					  @Param("ran") double ran);


	void clearPeriod(BussPeriodReqVo bussPeriodReqVo);
}
