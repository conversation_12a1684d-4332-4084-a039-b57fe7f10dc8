<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-03-08 10:07:11 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussTILrcIcuCalcDao">
  <!-- 本配置文件由GIS MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**IDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->
  <select id="countDateByVo" fetchSize="2000" flushCache="false" useCache="true" resultType="Long"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
    select
    count(a.ACTION_NO) as "actionNo"
    from atruser.ATR_BUSS_TI_LRC_ICU_CALC a
    where a.action_no = #{actionNo,jdbcType=VARCHAR}
    <if test="portfolioNo != null and portfolioNo != ''">
      and a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
    </if>
    <if test="icgNo != null and icgNo != ''">
      and a.icg_no = #{icgNo,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="findDateByVo" fetchSize="1000" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
     select
      a.ACTION_NO as "actionNo",
      a.TREATY_NO as "treatyNo",
      a.currency_code as "currency",
      a.YEAR_MONTH as "yearMonth",
      a.PORTFOLIO_NO as "portfolioNo",
      a.ICG_NO as "icgNo",
      a.EVALUATE_APPROACH as "evaluateApproach",
      a.LOA_CODE as "loaCode",
      a.CMUNIT_NO as "cmunitNo",
      a.PRODUCT_CODE as "productCode",
      to_char(a.EVALUATE_DATE,'yyyy/mm/dd') as "evaluateDate",
      to_char(a.CONTRACT_DATE,'yyyy/mm/dd') as "contractDate",
      to_char(a.EFFECTIVE_DATE_IN_DATE,'yyyy/mm/dd') as "effectiveDateInDate",
      to_char(a.APPROVAL_DATE_IN_DATE,'yyyy/mm/dd') as "checkDateInDate",
      to_char(a.EXPIRY_DATE_IN_DATE,'yyyy/mm/dd') as "expiryDateInDate",
      to_char(a.EFFECTIVE_DATE_BOM,'yyyy/mm/dd') as "effectiveDateBom",
      to_char(a.EXPIRY_DATE_EOM,'yyyy/mm/dd') as "expiryDateEom",
      a.payment_frequency_code as "premiumFrequency",
      a.payment_frequency_no as "premiumTerm",
      a.GROSS_PREMIUM as "grossPremium",
      a.COVERAGE_AMOUNT as "coverageAmount",
      a.PASSED_MONTHS as "passedMonths",
      a.remaining_months_for_recv as "remainingMonthsForRecv",
      a.MORTGAGE_RISK_IND AS "mortgageRiskInd",
      a.UL_UNDERWRITTEN_RATE AS "ulUnderwrittenTate",
      a.EST_TOTAL_RI_PREMIUM AS "estTotalRiPremium",
      a.RISK_EXPANSION AS "riskExpansion",
      a.RECV_PREMIUM AS "recvPremium",
      a.CUMULATIVE_PAID_PREMIUM AS "cumulativePaidPremium",
      a.ed_premium_per_coverage_day as "edPremiumPerCoverageDay",
      a.ED_PREMIUM as "edPremium",
      a.PRI_CUR_END_REMAIN_CSM_RATE as "priCurEndRemainCsmRate",
      a.PRI_UNTIL_REPORT_REMAIN_CSM_RATE as "priUntilReportRemainCsmRate",
      a.CUMULATIVE_ED_RATE as "cumulativeEdRate",
      a.CUR_END_REMAIN_CSM_RATE as "curEndRemainCsmRate",
      a.UNTIL_REPORT_REMAIN_CSM_RATE as "untilReportRemainCsmRate",
      a.ELR as "elr",
      a.LOAN_OS AS "loanOs",
      a.AFNP_LOAN_OS AS "afnpLoanOs",
      a.DELINQUENCY_RATE AS "delinquencyRate",
      a.DEFAULT_RATE AS "defaultRate",
      (select QUOTA_VALUE from Atr_Buss_Quota_Value t1 where a.action_no=t1.action_no
                                                            AND a.loa_code=t1.dimension_value AND quota_code='BE005' limit 1) as "BE005",
      (select QUOTA_VALUE from Atr_Buss_Quota_Value t1 where a.action_no=t1.action_no
                                                            AND a.loa_code=t1.dimension_value AND quota_code='QR003' limit 1) as "QR003",
      (select QUOTA_VALUE from Atr_Buss_Quota_Value t1 where a.action_no=t1.action_no
                                                            AND a.loa_code=t1.dimension_value AND quota_code='QR010'  limit 1) as "QR010",
      c.entity_code as "entityCode",
      c.entity_e_name as "entityEName"
      <foreach collection="devNoList" item="item" index="index" open=","  separator=",">
        MAX(CASE cd.DEV_NO WHEN ${item} THEN cd.${feeType} ELSE NULL END) AS "${item}"
      </foreach>
    from (select * from ATR_BUSS_TI_LRC_ICU_CALC a
          where a.action_no = #{actionNo,jdbcType=VARCHAR}
          <if test="portfolioNo != null and portfolioNo != ''">
            and a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
          </if>
          <if test="icgNo != null and icgNo != ''">
            and a.icg_no = #{icgNo,jdbcType=VARCHAR}
          </if>
          order by a.ICG_NO, a.TREATY_NO
          <if test="limit != null and offset != null">
            limit ${limit} offset ${offset}
          </if>
          ) a
    left join atruser.ATR_BUSS_TI_LRC_ICU_CALC_detail cd on a.id = cd.main_id
           left join bpluser.bbs_conf_entity c on a.entity_id = c.entity_id
           left join bpluser.BBS_CONF_CURRENCY cur on a.currency_code = cur.CURRENCY_CODE
    GROUP BY
    a.ACTION_NO,
    a.TREATY_NO ,
    a.currency_code ,
    a.YEAR_MONTH ,
    a.PORTFOLIO_NO ,
    a.ICG_NO,
    a.EVALUATE_APPROACH ,
    a.LOA_CODE,
    a.CMUNIT_NO ,
    a.PRODUCT_CODE,
    to_char(a.EVALUATE_DATE,'yyyy/mm/dd'),
    to_char(a.CONTRACT_DATE,'yyyy/mm/dd') ,
    to_char(a.EFFECTIVE_DATE_IN_DATE,'yyyy/mm/dd') ,
    to_char(a.APPROVAL_DATE_IN_DATE,'yyyy/mm/dd') ,
    to_char(a.EXPIRY_DATE_IN_DATE,'yyyy/mm/dd') ,
    to_char(a.EFFECTIVE_DATE_BOM,'yyyy/mm/dd') ,
    to_char(a.EXPIRY_DATE_EOM,'yyyy/mm/dd'),
    a.payment_frequency_code,
    a.payment_frequency_no,
    a.GROSS_PREMIUM,
    a.COVERAGE_AMOUNT ,
    a.PASSED_MONTHS ,
    a.REMAINING_MONTHS_FOR_RECV ,
    a.MORTGAGE_RISK_IND,
    a.UL_UNDERWRITTEN_RATE ,
    a.EST_TOTAL_RI_PREMIUM ,
    a.RISK_EXPANSION ,
    a.CUMULATIVE_PAID_PREMIUM,
    a.RECV_PREMIUM ,
    a.CUMULATIVE_PAID_PREMIUM,
    a.ed_premium_per_coverage_day,
    a.ED_PREMIUM ,
    a.PRI_CUR_END_REMAIN_CSM_RATE,
    a.PRI_UNTIL_REPORT_REMAIN_CSM_RATE,
    a.CUMULATIVE_ED_RATE,
    a.CUR_END_REMAIN_CSM_RATE,
    a.UNTIL_REPORT_REMAIN_CSM_RATE,
    a.ELR,
    a.LOAN_OS,
    a.AFNP_LOAN_OS,
    a.DELINQUENCY_RATE,
    a.DEFAULT_RATE,
    c.entity_code ,
    c.entity_e_name
    order by a.ICG_NO, a.TREATY_NO
  </select>


    <select id="findLrcDetailPage" fetchSize="1000" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
        select
        a.ACTION_NO as "actionNo",
        a.YEAR_MONTH as "yearMonth",
        a.TREATY_NO as "treatyNo",
        a.STATEMENT_PERIOD as "statementPeriod",
        a.PORTFOLIO_NO as "portfolioNo",
        a.ICG_NO as "icgNo",
        a.EVALUATE_APPROACH as "evaluateApproach",
        a.LOA_CODE as "loaCode",
        a.CMUNIT_NO as "cmunitNo",
        a.PRODUCT_CODE as "productCode",
        to_char(a.CONTRACT_DATE,'yyyy/mm/dd') as "contractDate",
        to_char(a.effective_date,'yyyy/mm/dd') as "effectiveDate",
        to_char(a.expiry_date,'yyyy/mm/dd') as "expiryDate",
        a.COVERAGE_AMOUNT as "coverageAmount",
        a.remaining_months AS "remainingMonths",
        a.currency_code as "currencyCode",
        c.entity_code as "entityCode",
        c.entity_e_name as "entityEName"
        <if test="null != devNoList and devNoList.size > 0">
            <foreach collection="devNoList" item="item" index="index" open=","  separator=",">
                MAX(CASE cd.DEV_NO WHEN ${item} THEN cd.${feeType} ELSE NULL END) AS "${item}"
            </foreach>
        </if>
        FROM atruser.ATR_BUSS_TI_LRC_U a
        left join atruser.ATR_BUSS_TI_LRC_U_DEV cd on a.id = cd.main_id
        left join bpluser.bbs_conf_entity c on a.entity_id = c.entity_id
        where a.action_no = #{actionNo,jdbcType=VARCHAR}
        <if test="portfolioNo != null and portfolioNo != ''">
            and a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
        </if>
        <if test="icgNo != null and icgNo != ''">
            and a.icg_no = #{icgNo,jdbcType=VARCHAR}
        </if>
        GROUP BY
        a.ACTION_NO,
        a.TREATY_NO,
        a.STATEMENT_PERIOD,
        a.currency_code,
        a.YEAR_MONTH,
        a.PORTFOLIO_NO,
        a.ICG_NO,
        a.EVALUATE_APPROACH,
        a.LOA_CODE,
        a.CMUNIT_NO,
        a.PRODUCT_CODE,
        to_char(a.CONTRACT_DATE,'yyyy/mm/dd'),
        to_char(a.effective_date,'yyyy/mm/dd'),
        to_char(a.expiry_date,'yyyy/mm/dd'),
        a.COVERAGE_AMOUNT ,
        a.remaining_months,
        c.entity_code  ,
        c.entity_e_name,
        a.id
        order by a.id
    </select>
</mapper>
