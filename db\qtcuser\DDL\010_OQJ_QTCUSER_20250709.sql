
drop table  if exists  qtc_buss_calc_cfg ;

create table qtc_buss_calc_cfg
(
    cfg_id        numeric(11) not null
        constraint pk_qtc_buss_calc_cfg
            primary key,
    entity_id     numeric(11),
    model_def_id  numeric(11),
    year_month    varchar(6),
    currency_code varchar(6),
    evl_main_id   numeric(11),
    buss_model    varchar(6),
    loa_code      varchar(32),
    evl_l_main_id numeric(11),
    ym_last       varchar(6),
    rpt_is        integer,
    ver_type      varchar(3),
    cf_dire       smallint,
    show_no       varchar(64)
)
    tablespace qtc_space;

comment on table qtc_buss_calc_cfg is '计算参数';

comment on column qtc_buss_calc_cfg.entity_id is '机构ID';

comment on column qtc_buss_calc_cfg.model_def_id is '模型定义ID';

comment on column qtc_buss_calc_cfg.year_month is '计量年月';

comment on column qtc_buss_calc_cfg.currency_code is '币别';

comment on column qtc_buss_calc_cfg.evl_main_id is '计量主表ID';

comment on column qtc_buss_calc_cfg.buss_model is '业务模型';

comment on column qtc_buss_calc_cfg.loa_code is '业务线';

comment on column qtc_buss_calc_cfg.evl_l_main_id is '上期确认-计量主表ID';

comment on column qtc_buss_calc_cfg.ym_last is '上期计量年月';

comment on column qtc_buss_calc_cfg.rpt_is is '报告期初';

comment on column qtc_buss_calc_cfg.ver_type is '版本类型';

comment on column qtc_buss_calc_cfg.cf_dire is '现金流方向';

alter table qtc_buss_calc_cfg
    owner to qtcuser;

create index idx_qtc_buss_calc_cfg_qy
    on qtc_buss_calc_cfg (evl_main_id);



drop table if exists qtc_buss_evl_paa_pre ;

create table qtc_buss_evl_paa_pre
(
    paa_pre_id  numeric(11) not null
        constraint pk_qtc_buss_evl_paa_pre
            primary key,
    evl_main_id numeric(11),
    icg_no      varchar(64),
    buss_key    varchar(120),
    type_no     varchar(6),
    dev_period  integer,
    num1        numeric(32, 8),
    num2        numeric(32, 8),
    num3        numeric(32, 8),
    num4        numeric(32, 8),
    num5        numeric(32, 8),
    num6        numeric(32, 8),
    num7        numeric(32, 8),
    num8        numeric(32, 8),
    num9        numeric(32, 8),
    num10       numeric(32, 8),
    num11       numeric(32, 8),
    num12       numeric(32, 8),
    num13       numeric(32, 8),
    num14       numeric(32, 8),
    num15       numeric(32, 8),
    num16       numeric(32, 8),
    num17       numeric(32, 8),
    num18       numeric(32, 8),
    num19       numeric(32, 8),
    num20       numeric(32, 8),
    num21       numeric(32, 8),
    num22       numeric(32, 8),
    num23       numeric(32, 8),
    num24       numeric(32, 8),
    num25       numeric(32, 8),
    num26       numeric(32, 8),
    num27       numeric(32, 8),
    num28       numeric(32, 8),
    num29       numeric(32, 8),
    num30       numeric(32, 8)
)
    tablespace qtc_space;

comment on table qtc_buss_evl_paa_pre is '计量计算-PAA-预处理数据';

alter table qtc_buss_evl_paa_pre
    owner to qtcuser;

create index idx_qtc_buss_evl_paa_pre_qy
    on qtc_buss_evl_paa_pre (evl_main_id, type_no);


drop table if exists qtc_buss_paa_cal_icg ;

create table qtc_buss_paa_cal_icg
(
    cal_icg_id  numeric(11) not null
        constraint pk_qtc_buss_paa_cal_icg
            primary key,
    evl_main_id numeric(11) not null,
    type_no     varchar(6),
    icg_no      varchar(64),
    dev_no      numeric,
    num1        numeric(32, 8),
    num2        numeric(32, 8),
    num3        numeric(32, 8),
    num4        numeric(32, 8),
    num5        numeric(32, 8),
    num6        numeric(32, 8),
    num7        numeric(32, 8),
    num8        numeric(32, 8),
    num9        numeric(32, 8),
    num10       numeric(32, 8),
    num11       numeric(32, 8),
    num12       numeric(32, 8),
    num13       numeric(32, 8),
    num14       numeric(32, 8),
    num15       numeric(32, 8),
    num16       numeric(32, 8),
    num17       numeric(32, 8),
    num18       numeric(32, 8),
    num19       numeric(32, 8),
    num20       numeric(32, 8),
    num21       numeric(32, 8),
    num22       numeric(32, 8),
    num23       numeric(32, 8),
    num24       numeric(32, 8),
    num25       numeric(32, 8)
)
    tablespace qtc_space;

comment on table qtc_buss_paa_cal_icg is 'PAA-计算过程-合同组';

comment on column qtc_buss_paa_cal_icg.evl_main_id is '计量主表ID';

comment on column qtc_buss_paa_cal_icg.type_no is '数据类型';

comment on column qtc_buss_paa_cal_icg.icg_no is '合同组';

comment on column qtc_buss_paa_cal_icg.dev_no is '发展期';

alter table qtc_buss_paa_cal_icg
    owner to qtcuser;

create index idx_qtc_buss_paa_cal_icg_qy
    on qtc_buss_paa_cal_icg (evl_main_id);


drop table if exists qtc_buss_evl_lic_pre ;

create table qtc_buss_evl_lic_pre
(
    lic_pre_id  bigint not null
        constraint pk_qtc_buss_evl_lic_pre
            primary key,
    evl_main_id numeric(11),
    icg_no      varchar(64),
    type_no     varchar(6),
    dev_period  integer,
    num1        numeric(32, 8),
    num2        numeric(32, 8),
    num3        numeric(32, 8),
    num4        numeric(32, 8),
    num5        numeric(32, 8),
    num6        numeric(32, 8),
    num7        numeric(32, 8),
    num8        numeric(32, 8),
    num9        numeric(32, 8),
    num10       numeric(32, 8),
    num11       numeric(32, 8),
    num12       numeric(32, 8),
    num13       numeric(32, 8),
    num14       numeric(32, 8),
    num15       numeric(32, 8),
    treaty_no   varchar(60)
)
    tablespace qtc_space;

comment on table qtc_buss_evl_lic_pre is '计量计算-LIC-预处理现金流数据';

alter table qtc_buss_evl_lic_pre
    owner to qtcuser;

create index idx_qtc_buss_evl_lic_pre_qy
    on qtc_buss_evl_lic_pre (evl_main_id, type_no);


drop table if exists qtc_buss_lic_cal_icg ;

create table qtc_buss_lic_cal_icg
(
    cal_icg_id  bigint      not null
        constraint pk_qtc_buss_lic_cal_icg
            primary key,
    evl_main_id numeric(11) not null,
    type_no     varchar(6),
    icg_no      varchar(64),
    num1        numeric(32, 8),
    num2        numeric(32, 8),
    num3        numeric(32, 8),
    num4        numeric(32, 8),
    num5        numeric(32, 8),
    num6        numeric(32, 8),
    num7        numeric(32, 8),
    num8        numeric(32, 8),
    num9        numeric(32, 8),
    num10       numeric(32, 8),
    num11       numeric(32, 8),
    num12       numeric(32, 8),
    num13       numeric(32, 8),
    num14       numeric(32, 8),
    num15       numeric(32, 8),
    num16       numeric(32, 8),
    num17       numeric(32, 8),
    num18       numeric(32, 8),
    num19       numeric(32, 8),
    num20       numeric(32, 8),
    num21       numeric(32, 8),
    num22       numeric(32, 8),
    num23       numeric(32, 8),
    num24       numeric(32, 8),
    num25       numeric(32, 8)
)
    tablespace qtc_space;

comment on table qtc_buss_lic_cal_icg is 'LIC-计算过程';

alter table qtc_buss_lic_cal_icg
    owner to qtcuser;

create index idx_qtc_buss_lic_cal_icg_qy
    on qtc_buss_lic_cal_icg (evl_main_id);


drop table if exists qtc_buss_evaluate_result ;

create table qtc_buss_evaluate_result
(
    evaluate_result_id bigint not null
        constraint pk_qtc_buss_evaluate_result
            primary key,
    evaluate_main_id   bigint,
    portfolio_no       varchar(64),
    icg_no             varchar(64),
    unit_no            varchar(64),
    dev_period         bigint,
    var1               varchar(64),
    var2               varchar(64),
    var3               varchar(64),
    var4               varchar(64),
    var5               varchar(64),
    num1               numeric(32, 8) ,
    num2               numeric(32, 8) ,
    num3               numeric(32, 8) ,
    num4               numeric(32, 8) ,
    num5               numeric(32, 8) ,
    num6               numeric(32, 8) ,
    num7               numeric(32, 8) ,
    num8               numeric(32, 8) ,
    num9               numeric(32, 8) ,
    num10              numeric(32, 8) ,
    num11              numeric(32, 8) ,
    num12              numeric(32, 8) ,
    num13              numeric(32, 8) ,
    num14              numeric(32, 8) ,
    num15              numeric(32, 8) ,
    num16              numeric(32, 8) ,
    num17              numeric(32, 8) ,
    num18              numeric(32, 8) ,
    num19              numeric(32, 8) ,
    num20              numeric(32, 8) ,
    num21              numeric(32, 8) ,
    num22              numeric(32, 8) ,
    num23              numeric(32, 8) ,
    num24              numeric(32, 8) ,
    num25              numeric(32, 8) ,
    num26              numeric(32, 8) ,
    num27              numeric(32, 8) ,
    num28              numeric(32, 8) ,
    num29              numeric(32, 8) ,
    num30              numeric(32, 8) ,
    num31              numeric(32, 8) ,
    num32              numeric(32, 8) ,
    num33              numeric(32, 8) ,
    num34              numeric(32, 8) ,
    num35              numeric(32, 8) ,
    num36              numeric(32, 8) ,
    num37              numeric(32, 8) ,
    num38              numeric(32, 8) ,
    num39              numeric(32, 8) ,
    num40              numeric(32, 8) ,
    num41              numeric(32, 8) ,
    num42              numeric(32, 8) ,
    num43              numeric(32, 8) ,
    num44              numeric(32, 8) ,
    num45              numeric(32, 8) ,
    num46              numeric(32, 8) ,
    num47              numeric(32, 8) ,
    num48              numeric(32, 8) ,
    num49              numeric(32, 8) ,
    num50              numeric(32, 8) ,
    num51              numeric(32, 8) ,
    num52              numeric(32, 8) ,
    num53              numeric(32, 8) ,
    num54              numeric(32, 8) ,
    num55              numeric(32, 8) ,
    num56              numeric(32, 8) ,
    num57              numeric(32, 8) ,
    num58              numeric(32, 8) ,
    num59              numeric(32, 8) ,
    num60              numeric(32, 8) ,
    num61              numeric(32, 8),
    num62              numeric(32, 8),
    num63              numeric(32, 8),
    num64              numeric(32, 8),
    num65              numeric(32, 8),
    num66              numeric(32, 8),
    num67              numeric(32, 8),
    num68              numeric(32, 8),
    num69              numeric(32, 8),
    num70              numeric(32, 8),
    num71              numeric(32, 8),
    num72              numeric(32, 8),
    num73              numeric(32, 8)
);

comment on table qtc_buss_evaluate_result is '计量输出数据';

alter table qtc_buss_evaluate_result
    owner to qtcuser;


create index idx_qtc_buss_evaluate_result_qy
    on qtc_buss_evaluate_result (evaluate_main_id);


drop table if exists qtc_buss_evaluate_main ;

create table qtc_buss_evaluate_main
(
    evaluate_main_id   bigint not null
        constraint pk_qtc_buss_evaluate_main
            primary key,
    entity_id          bigint,
    model_def_id       bigint,
    currency_code      varchar(3),
    evaluate_date      timestamp(6),
    year_month         varchar(6),
    version_no         varchar(32),
    confirm_is         char,
    confirm_id         bigint,
    confirm_time       timestamp(6),
    creator_id         bigint,
    create_time        timestamp(6),
    updator_id         bigint,
    update_time        timestamp(6),
    business_model     varchar(2),
    business_direction varchar(2),
    execution_state    varchar(2),
    loa_code           varchar(32),
    err_msg            varchar(2000),
    show_no            varchar(64),
    ver_type           varchar(3)
);

comment on table qtc_buss_evaluate_main is '计量评估主表';

comment on column qtc_buss_evaluate_main.evaluate_main_id is 'evaluate_main_id|主键';

comment on column qtc_buss_evaluate_main.entity_id is 'entity_id|业务单位ID';

comment on column qtc_buss_evaluate_main.model_def_id is 'model_def_id|模型ID';

comment on column qtc_buss_evaluate_main.currency_code is 'currency_code|币别';

comment on column qtc_buss_evaluate_main.evaluate_date is 'evaluate_date|评估日期';

comment on column qtc_buss_evaluate_main.year_month is 'year_month|计量月份';

comment on column qtc_buss_evaluate_main.version_no is 'version_no|版本';

comment on column qtc_buss_evaluate_main.confirm_is is 'confirm_is|是否确认';

comment on column qtc_buss_evaluate_main.confirm_id is 'confirm_id|确认人';

comment on column qtc_buss_evaluate_main.confirm_time is 'confirm_time|确认时间';

comment on column qtc_buss_evaluate_main.creator_id is 'Creator_Id|创建人';

comment on column qtc_buss_evaluate_main.create_time is 'Create_Time|创建时间';

comment on column qtc_buss_evaluate_main.updator_id is 'Updator_Id|最后修改人';

comment on column qtc_buss_evaluate_main.update_time is 'Update_Time|最后修改时间';

comment on column qtc_buss_evaluate_main.business_model is 'Business_Model|业务模型：DD-直保 TD-合约 FI-分入 FO-分出';

comment on column qtc_buss_evaluate_main.business_direction is 'Business_Direction|业务方向：D-不区分 I-分入 O-分出';

comment on column qtc_buss_evaluate_main.execution_state is 'execution_state|执行状态：0-执行中 1-已完成 2-异常终止';

comment on column qtc_buss_evaluate_main.loa_code is 'Loa Code|LOA编码';

comment on column qtc_buss_evaluate_main.err_msg is '异常信息';

comment on column qtc_buss_evaluate_main.ver_type is '版本类型：I-内部管理；O-监管版本';

alter table qtc_buss_evaluate_main
    owner to qtcuser;


create index idx_qtc_buss_evaluate_main_qy
    on qtc_buss_evaluate_main (evaluate_main_id);


drop table if exists qtc_buss_calc_alloc_cfg ;

create table qtc_buss_calc_alloc_cfg
(
    cfg_id        serial
        constraint pk_qtc_buss_calc_alloc_cfg
            primary key,
    entity_id     numeric(11),
    year_month    varchar(6),
    currency_code varchar(6),
    evl_main_id   numeric(11),
    buss_model    varchar(6),
    loa_code      varchar(64),
    evl_l_main_id numeric(11),
    ym_last       varchar(6),
    rpt_is        varchar(3),
    show_no       varchar(64),
    ver_type      varchar(3)
)
    tablespace qtc_space;

comment on table qtc_buss_calc_alloc_cfg is '计量结果分摊-计算信息';


create index idx_qtc_buss_calc_alloc_cfg_evl_main_id
    on qtc_buss_calc_alloc_cfg (evl_main_id);


drop table if exists  qtc_temp_evl_alloc_calc ;

create table qtc_temp_evl_alloc_calc
(
    evl_main_id  numeric(11),
    portfolio_no varchar(64),
    icg_no       varchar(64),
    type_no      varchar(6),
    var1         varchar(64),
    var2         varchar(64),
    var3         varchar(64),
    var4         varchar(64),
    var5         varchar(64),
    var6         varchar(64),
    var7         varchar(64),
    var8         varchar(64),
    var9         varchar(64),
    var10        varchar(64),
    var11        varchar(64),
    var12        varchar(64),
    var13        varchar(64),
    var14        varchar(64),
    var15        varchar(64),
    num1         numeric(32, 8),
    num2         numeric(32, 8),
    num3         numeric(32, 8),
    num4         numeric(32, 8),
    num5         numeric(32, 8),
    num6         numeric(32, 8),
    num7         numeric(32, 8),
    num8         numeric(32, 8),
    num9         numeric(32, 8),
    num10        numeric(32, 8),
    num11        numeric(32, 8),
    num12        numeric(32, 8),
    num13        numeric(32, 8),
    num14        numeric(32, 8),
    num15        numeric(32, 8),
    num16        numeric(32, 8),
    num17        numeric(32, 8),
    num18        numeric(32, 8),
    num19        numeric(32, 8),
    num20        numeric(32, 8),
    num21        numeric(32, 8),
    num22        numeric(32, 8),
    num23        numeric(32, 8),
    num24        numeric(32, 8),
    num25        numeric(32, 8)
)
    tablespace qtc_space;

comment on table qtc_temp_evl_alloc_calc is '计量结果分摊-数据处理';


create index idx_qtc_temp_evl_alloc_calc_evl_main_id
    on qtc_temp_evl_alloc_calc (evl_main_id);


drop table if exists qtc_buss_evl_lrc_alloc_calc ;

create table qtc_buss_evl_lrc_alloc_calc
(
    calc_id      serial
        constraint pk_qtc_buss_evl_lrc_alloc_calc
            primary key,
    evl_main_id  bigint,
    portfolio_no varchar(64),
    icg_no       varchar(64),
    str1         varchar(32),
    str2         varchar(32),
    str3         varchar(32),
    str4         varchar(32),
    str5         varchar(32),
    str6         varchar(32),
    str7         varchar(32),
    str8         varchar(32),
    str9         varchar(32),
    str10        varchar(32),
    str11        varchar(32),
    str12        varchar(32),
    str13        varchar(32),
    str14        varchar(32),
    str15        varchar(32),
    level        varchar(6),
    num1         numeric(32, 8),
    num2         numeric(32, 8),
    num3         numeric(32, 8),
    num4         numeric(32, 8),
    num5         numeric(32, 8),
    num6         numeric(32, 8),
    num7         numeric(32, 8),
    num8         numeric(32, 8),
    num9         numeric(32, 8),
    num10        numeric(32, 8),
    num11        numeric(32, 8),
    num12        numeric(32, 8),
    num13        numeric(32, 8),
    num14        numeric(32, 8),
    num15        numeric(32, 8),
    num16        numeric(32, 8),
    num17        numeric(32, 8),
    num18        numeric(32, 8),
    num19        numeric(32, 8),
    num20        numeric(32, 8),
    num21        numeric(32, 8),
    num22        numeric(32, 8),
    num23        numeric(32, 8),
    num24        numeric(32, 8),
    num25        numeric(32, 8),
    num26        numeric(32, 8),
    num27        numeric(32, 8),
    num28        numeric(32, 8),
    num29        numeric(32, 8),
    num30        numeric(32, 8),
    num31        numeric(32, 8),
    num32        numeric(32, 8),
    num33        numeric(32, 8),
    num34        numeric(32, 8),
    num35        numeric(32, 8)
)
    tablespace qtc_space;

comment on table qtc_buss_evl_lrc_alloc_calc is '计量分摊-计算因子';

alter table qtc_buss_evl_lrc_alloc_calc
    owner to qtcuser;

create index idx_qtc_buss_evl_lrc_alloc_calc_evl_main_id
    on qtc_buss_evl_lrc_alloc_calc (evl_main_id);


drop table if exists qtc_buss_evl_lic_alloc_calc ;

create table qtc_buss_evl_lic_alloc_calc
(
    calc_id      serial
        constraint pk_qtc_buss_evl_lic_alloc_calc
            primary key,
    evl_main_id  bigint,
    portfolio_no varchar(64),
    icg_no       varchar(64),
    str1         varchar(32),
    str2         varchar(32),
    str3         varchar(32),
    str4         varchar(32),
    str5         varchar(32),
    str6         varchar(32),
    str7         varchar(32),
    str8         varchar(32),
    str9         varchar(32),
    str10        varchar(32),
    str11        varchar(32),
    str12        varchar(32),
    str13        varchar(32),
    str14        varchar(32),
    str15        varchar(32),
    level        varchar(6),
    num1         numeric(32, 8),
    num2         numeric(32, 8),
    num3         numeric(32, 8),
    num4         numeric(32, 8),
    num5         numeric(32, 8),
    num6         numeric(32, 8),
    num7         numeric(32, 8),
    num8         numeric(32, 8),
    num9         numeric(32, 8),
    num10        numeric(32, 8),
    num11        numeric(32, 8),
    num12        numeric(32, 8),
    num13        numeric(32, 8),
    num14        numeric(32, 8),
    num15        numeric(32, 8),
    num16        numeric(32, 8),
    num17        numeric(32, 8),
    num18        numeric(32, 8),
    num19        numeric(32, 8),
    num20        numeric(32, 8),
    num21        numeric(32, 8),
    num22        numeric(32, 8),
    num23        numeric(32, 8),
    num24        numeric(32, 8),
    num25        numeric(32, 8),
    num26        numeric(32, 8),
    num27        numeric(32, 8),
    num28        numeric(32, 8),
    num29        numeric(32, 8),
    num30        numeric(32, 8),
    num31        numeric(32, 8),
    num32        numeric(32, 8),
    num33        numeric(32, 8),
    num34        numeric(32, 8),
    num35        numeric(32, 8)
)
    tablespace qtc_space;

comment on table qtc_buss_evl_lic_alloc_calc is '计量分摊-计算因子';

alter table qtc_buss_evl_lic_alloc_calc
    owner to qtcuser;

create index idx_qtc_buss_evl_lic_alloc_calc_evl_main_id
    on qtc_buss_evl_lic_alloc_calc (evl_main_id);


drop table if exists qtc_buss_evl_lic_alloc_calc ;

create table qtc_buss_evl_lic_alloc_calc
(
    calc_id      serial
        constraint pk_qtc_buss_evl_lic_alloc_calc
            primary key,
    evl_main_id  bigint,
    portfolio_no varchar(64),
    icg_no       varchar(64),
    str1         varchar(32),
    str2         varchar(32),
    str3         varchar(32),
    str4         varchar(32),
    str5         varchar(32),
    str6         varchar(32),
    str7         varchar(32),
    str8         varchar(32),
    str9         varchar(32),
    str10        varchar(32),
    str11        varchar(32),
    str12        varchar(32),
    str13        varchar(32),
    str14        varchar(32),
    str15        varchar(32),
    level        varchar(6),
    num1         numeric(32, 8),
    num2         numeric(32, 8),
    num3         numeric(32, 8),
    num4         numeric(32, 8),
    num5         numeric(32, 8),
    num6         numeric(32, 8),
    num7         numeric(32, 8),
    num8         numeric(32, 8),
    num9         numeric(32, 8),
    num10        numeric(32, 8),
    num11        numeric(32, 8),
    num12        numeric(32, 8),
    num13        numeric(32, 8),
    num14        numeric(32, 8),
    num15        numeric(32, 8),
    num16        numeric(32, 8),
    num17        numeric(32, 8),
    num18        numeric(32, 8),
    num19        numeric(32, 8),
    num20        numeric(32, 8),
    num21        numeric(32, 8),
    num22        numeric(32, 8),
    num23        numeric(32, 8),
    num24        numeric(32, 8),
    num25        numeric(32, 8),
    num26        numeric(32, 8),
    num27        numeric(32, 8),
    num28        numeric(32, 8),
    num29        numeric(32, 8),
    num30        numeric(32, 8),
    num31        numeric(32, 8),
    num32        numeric(32, 8),
    num33        numeric(32, 8),
    num34        numeric(32, 8),
    num35        numeric(32, 8)
)
    tablespace qtc_space;

comment on table qtc_buss_evl_lic_alloc_calc is '计量分摊-计算因子';

alter table qtc_buss_evl_lic_alloc_calc
    owner to qtcuser;

create index idx_qtc_buss_evl_lic_alloc_calc_evl_main_id
    on qtc_buss_evl_lic_alloc_calc (evl_main_id);


drop table if exists qtc_temp_lrc_alloc_calc ;

create table qtc_temp_lrc_alloc_calc
(
    evl_main_id  numeric(11),
    year_month   varchar(6),
    loa_code     varchar(6),
    portfolio_no varchar(64),
    icg_no       varchar(64),
    type_no      varchar(6),
    str1         varchar(64),
    str2         varchar(64),
    str3         varchar(64),
    str4         varchar(64),
    str5         varchar(64),
    str6         varchar(64),
    str7         varchar(64),
    str8         varchar(64),
    str9         varchar(64),
    str10        varchar(64),
    str11        varchar(64),
    str12        varchar(64),
    str13        varchar(64),
    str14        varchar(64),
    str15        varchar(64),
    num1         numeric(32, 8),
    num2         numeric(32, 8),
    num3         numeric(32, 8),
    num4         numeric(32, 8),
    num5         numeric(32, 8),
    num6         numeric(32, 8),
    num7         numeric(32, 8),
    num8         numeric(32, 8),
    num9         numeric(32, 8),
    num10        numeric(32, 8),
    num11        numeric(32, 8),
    num12        numeric(32, 8),
    num13        numeric(32, 8),
    num14        numeric(32, 8),
    num15        numeric(32, 8),
    num16        numeric(32, 8),
    num17        numeric(32, 8),
    num18        numeric(32, 8),
    num19        numeric(32, 8),
    num20        numeric(32, 8),
    num21        numeric(32, 8),
    num22        numeric(32, 8),
    num23        numeric(32, 8),
    num24        numeric(32, 8),
    num25        numeric(32, 8)
)
    tablespace qtc_space;

comment on table qtc_temp_lrc_alloc_calc is '计量结果分摊-数据处理';

alter table qtc_temp_lrc_alloc_calc
    owner to qtcuser;

create index idx_qtc_temp_lrc_alloc_calc_evl_main_id
    on qtc_temp_lrc_alloc_calc (evl_main_id);


drop table if exists qtc_temp_lrc_alloc_icg ;

create table qtc_temp_lrc_alloc_icg
(
    evl_main_id  numeric(11),
    year_month   varchar(6),
    loa_code     varchar(6),
    portfolio_no varchar(64),
    icg_no       varchar(64),
    type_no      varchar(6),
    num1         numeric(32, 8),
    num2         numeric(32, 8),
    num3         numeric(32, 8),
    num4         numeric(32, 8),
    num5         numeric(32, 8),
    num6         numeric(32, 8),
    num7         numeric(32, 8),
    num8         numeric(32, 8),
    num9         numeric(32, 8),
    num10        numeric(32, 8),
    num11        numeric(32, 8),
    num12        numeric(32, 8),
    num13        numeric(32, 8),
    num14        numeric(32, 8),
    num15        numeric(32, 8),
    num16        numeric(32, 8),
    num17        numeric(32, 8),
    num18        numeric(32, 8),
    num19        numeric(32, 8),
    num20        numeric(32, 8),
    num21        numeric(32, 8),
    num22        numeric(32, 8),
    num23        numeric(32, 8),
    num24        numeric(32, 8),
    num25        numeric(32, 8)
)
    tablespace qtc_space;

comment on table qtc_temp_lrc_alloc_icg is '计量结果分摊：合同组-数据处理';

alter table qtc_temp_lrc_alloc_icg
    owner to qtcuser;

create index idx_qtc_temp_lrc_alloc_icg_evl_main_id
    on qtc_temp_lrc_alloc_icg (evl_main_id);


drop table if exists qtc_buss_lrc_alloc_calc ;

create table qtc_buss_lrc_alloc_calc
(
    calc_id      bigint generated always as identity,
    year_month   varchar(6),
    evl_main_id  bigint,
    loa_code     varchar(6),
    portfolio_no varchar(64),
    icg_no       varchar(64),
    type_no      varchar(6),
    str1         varchar(32),
    str2         varchar(32),
    str3         varchar(32),
    str4         varchar(32),
    str5         varchar(32),
    str6         varchar(32),
    str7         varchar(32),
    str8         varchar(32),
    str9         varchar(32),
    str10        varchar(32),
    str11        varchar(32),
    str12        varchar(32),
    str13        varchar(32),
    str14        varchar(32),
    str15        varchar(32),
    num1         numeric(32, 8),
    num2         numeric(32, 8),
    num3         numeric(32, 8),
    num4         numeric(32, 8),
    num5         numeric(32, 8),
    num6         numeric(32, 8),
    num7         numeric(32, 8),
    num8         numeric(32, 8),
    num9         numeric(32, 8),
    num10        numeric(32, 8),
    num11        numeric(32, 8),
    num12        numeric(32, 8),
    num13        numeric(32, 8),
    num14        numeric(32, 8),
    num15        numeric(32, 8),
    num16        numeric(32, 8),
    num17        numeric(32, 8),
    num18        numeric(32, 8),
    num19        numeric(32, 8),
    num20        numeric(32, 8),
    num21        numeric(32, 8),
    num22        numeric(32, 8),
    num23        numeric(32, 8),
    num24        numeric(32, 8),
    num25        numeric(32, 8),
    num26        numeric(32, 8),
    num27        numeric(32, 8),
    num28        numeric(32, 8),
    num29        numeric(32, 8),
    num30        numeric(32, 8),
    num31        numeric(32, 8),
    num32        numeric(32, 8),
    num33        numeric(32, 8),
    num34        numeric(32, 8),
    num35        numeric(32, 8)
)
     partition by LIST (year_month);

alter table qtc_buss_lrc_alloc_calc set tablespace qtc_space ;

comment on table qtc_buss_lrc_alloc_calc is '计量分摊-LRC计算过程';

create index idx_qtc_buss_lrc_alloc_calc_evl_main_id
    on qtc_buss_lrc_alloc_calc (evl_main_id);


drop table if exists qtc_buss_lrc_alloc_rate ;

create table qtc_buss_lrc_alloc_rate
(
    calc_id      bigint generated always as identity,
    year_month   varchar(6),
    evl_main_id  bigint,
    loa_code     varchar(6),
    portfolio_no varchar(64),
    icg_no       varchar(64),
    type_no      varchar(6),
    str1         varchar(32),
    str2         varchar(32),
    str3         varchar(32),
    str4         varchar(32),
    str5         varchar(32),
    str6         varchar(32),
    str7         varchar(32),
    str8         varchar(32),
    str9         varchar(32),
    str10        varchar(32),
    str11        varchar(32),
    str12        varchar(32),
    str13        varchar(32),
    str14        varchar(32),
    str15        varchar(32),
    num1         numeric(32, 16),
    num2         numeric(32, 16),
    num3         numeric(32, 16),
    num4         numeric(32, 16),
    num5         numeric(32, 16),
    num6         numeric(32, 16),
    num7         numeric(32, 16),
    num8         numeric(32, 16),
    num9         numeric(32, 16),
    num10        numeric(32, 16),
    num11        numeric(32, 16),
    num12        numeric(32, 16),
    num13        numeric(32, 16),
    num14        numeric(32, 16),
    num15        numeric(32, 16),
    num16        numeric(32, 16),
    num17        numeric(32, 16),
    num18        numeric(32, 16),
    num19        numeric(32, 16),
    num20        numeric(32, 16)
)
     partition by LIST (year_month);

alter table qtc_buss_lrc_alloc_rate set tablespace qtc_space ;

comment on table qtc_buss_lrc_alloc_rate is '计量分摊-LRC分摊比例';

create index idx_qtc_buss_lrc_alloc_rate_evl_main_id
    on qtc_buss_lrc_alloc_rate (evl_main_id);


drop table if exists qtc_buss_evl_alloc_result ;

create table qtc_buss_evl_alloc_result
(
    result_id            bigint generated always as identity,
    year_month           varchar(6),
    evl_main_id          bigint,
    business_source_code varchar(6),
    portfolio_no         varchar(64),
    icg_no               varchar(64),
    str1                 varchar(32),
    str2                 varchar(32),
    str3                 varchar(32),
    str4                 varchar(32),
    str5                 varchar(32),
    str6                 varchar(32),
    str7                 varchar(32),
    str8                 varchar(32),
    str9                 varchar(32),
    str10                varchar(32),
    str11                varchar(32),
    str12                varchar(32),
    str13                varchar(32),
    str14                varchar(32),
    str15                varchar(32),
    var1                 varchar(6),
    num1                 numeric(32, 8),
    num2                 numeric(32, 8),
    num3                 numeric(32, 8),
    num4                 numeric(32, 8),
    num5                 numeric(32, 8),
    num6                 numeric(32, 8),
    num7                 numeric(32, 8),
    num8                 numeric(32, 8),
    num9                 numeric(32, 8),
    num10                numeric(32, 8),
    num11                numeric(32, 8),
    num12                numeric(32, 8),
    num13                numeric(32, 8),
    num14                numeric(32, 8),
    num15                numeric(32, 8),
    num16                numeric(32, 8),
    num17                numeric(32, 8),
    num18                numeric(32, 8),
    num19                numeric(32, 8),
    num20                numeric(32, 8),
    num21                numeric(32, 8),
    num22                numeric(32, 8),
    num23                numeric(32, 8),
    num24                numeric(32, 8),
    num25                numeric(32, 8),
    num26                numeric(32, 8),
    num27                numeric(32, 8),
    num28                numeric(32, 8),
    num29                numeric(32, 8),
    num30                numeric(32, 8),
    num31                numeric(32, 8),
    num32                numeric(32, 8),
    num33                numeric(32, 8),
    num34                numeric(32, 8),
    num35                numeric(32, 8)
)
     partition by LIST (year_month);

alter table qtc_buss_evl_alloc_result set tablespace qtc_space ;

comment on table qtc_buss_evl_alloc_result is '计量分摊-分摊结果表';

create index idx_qtc_buss_evl_alloc_result_evl_main_id
    on qtc_buss_evl_alloc_result (evl_main_id);







do
$$
    DECLARE
        -- 声明变量
        v_tbm   varchar ;
        rec_tb  record ;
        v_sql   text ;
        v_count integer ;
        v_ym    varchar ;
        v_num   integer ;
        rec_ym  record ;
        p_ym    varchar ;

    BEGIN

        v_ym := '202401';
        v_num := 24;

        for rec_ym in 0..v_num - 1
            loop

                p_ym := to_char(to_date(v_ym, 'yyyymm') + rec_ym * interval '1 month', 'yyyymm');

                for rec_tb in (select 'qtc_buss_lrc_alloc_calc' tm
                               union all
                               select 'qtc_buss_lrc_alloc_rate' tm
                               union all
                               select 'qtc_buss_evl_alloc_result' tm )
                    loop

                        v_tbm := rec_tb.tm || '_' || p_ym;

                        execute ('drop table if exists ' || v_tbm);

                        execute ('create table ' || v_tbm || ' partition of ' || rec_tb.tm || ' for values in (''' || p_ym || ''')');

                      --  execute ('grant delete, insert, select, update on ' || v_tbm || ' to atruser ');

                    end loop;

            end loop;

    END
$$;

