package com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Setter @Getter
public class AtrBussIbnrcalcStepAccDataVo {

    private Long icpId;
    private String accidentNode;
    private BigDecimal edPremiumOri;
    private BigDecimal edPremium;
    private BigDecimal setteldAmount;
    private BigDecimal osAmount;
    private BigDecimal reportedAmount;
    private BigDecimal reportedLossRatio;
    private BigDecimal expectedReportedRatio;
    private BigDecimal lrExpectedLossRatio;
    private BigDecimal lrUltimateLoss;
    private BigDecimal lrIbnr;
    private BigDecimal clProjReportedUltimate;
    private BigDecimal clIbnr;
    private BigDecimal bfExpectedLossRatio;
    private BigDecimal bfProjReportedUltimate;
    private BigDecimal bfIbnr;

}
