create or replace package dm_pack_buss_cmunit is

 PROCEDURE proc_procdeal(p_entity_id    NUMBER,
                     p_buss_type    VARCHAR2,
                     p_buss_no      VARCHAR2,
                     p_endor_seq_no VARCHAR2,
                     p_risk_code    VARCHAR2,
                     p_proc_id      NUMBER,
                     p_node_state   VARCHAR2,
                     p_operator_id  NUMBER);

end dm_pack_buss_cmunit;
/
create or replace package body dm_pack_buss_cmunit is


  PROCEDURE proc_procdeal(p_entity_id    NUMBER,
                     p_buss_type    VARCHAR2,
                     p_buss_no      VARCHAR2,
                     p_endor_seq_no VARCHAR2,
                     p_risk_code    VARCHAR2,
                     p_proc_id      NUMBER,
                     p_node_state   VARCHAR2,
                     p_operator_id  NUMBER) is

  v_error_msg VARCHAR2(200); --错误信息
BEGIN
  IF p_buss_no IS NOT NULL THEN
    IF p_buss_type = 'P' THEN
      -- 1、当业务类型是保批单时，业务单号传递的是 保单号, 批单号可能有值，可能为空，险种可能有值，可能为空
      UPDATE dm_buss_cmunit_direct t
         SET proc_id     = p_proc_id,
             node_state  = p_node_state,
             updator_id  = p_operator_id,
             update_time = SYSDATE
       WHERE t.entity_id = p_entity_id
         AND t.policy_no = p_buss_no
        -- AND (t.endorse_seq_no = p_endor_seq_no OR p_endor_seq_no IS NULL)
         AND (t.risk_code = p_risk_code OR p_risk_code IS NULL);

    ELSIF p_buss_type = 'G' THEN
      -- 2、当业务类型是合同组时，业务单号传递的是 合同组编号
      UPDATE dm_buss_cmunit_direct t
         SET proc_id     = p_proc_id,
             node_state  = p_node_state,
             updator_id  = p_operator_id,
             update_time = SYSDATE
       WHERE t.entity_id = p_entity_id
         AND t.icg_no = p_buss_no;

    ELSIF p_buss_type = 'C' THEN
      -- 3、当业务类型是计量单元时，业务单号传递的是 计量单元编号
      UPDATE dm_buss_cmunit_direct t
         SET proc_id     = p_proc_id,
             node_state  = p_node_state,
             updator_id  = p_operator_id,
             update_time = SYSDATE
       WHERE t.entity_id = p_entity_id
         AND t.cmunit_no = p_buss_no;

    END IF;

    -- 提交事务
    --COMMIT;

  ELSE
    v_error_msg := '业务单号为空，请检查业务数据[' || p_entity_id || ',' || p_buss_type || ',' ||
                   'BUSS_NO' || ',' || p_proc_id || ']';
    DBMS_OUTPUT.PUT_LINE(v_error_msg);
  END IF;

EXCEPTION
  WHEN OTHERS THEN
    --ROLLBACK;
           v_error_msg :=  '[EXCEPTION][计量单元业务处理]proc_procdeal:'||SQLERRM;
    --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);
END proc_procdeal;


end dm_pack_buss_cmunit;
/
