-- 为 atr_buss_to_lrc_t_ul_r 表添加已赚保费和已赚手续费相关字段

-- 添加当期已赚保费字段
ALTER TABLE atruser.atr_buss_to_lrc_t_ul_r 
ADD COLUMN IF NOT EXISTS cur_ed_premium DECIMAL(21,4) DEFAULT 0 
COMMENT '当期已赚保费';

-- 添加当期已赚手续费字段  
ALTER TABLE atruser.atr_buss_to_lrc_t_ul_r 
ADD COLUMN IF NOT EXISTS cur_ed_net_fee DECIMAL(21,4) DEFAULT 0 
COMMENT '当期已赚手续费';

-- 添加上期累计已赚保费字段
ALTER TABLE atruser.atr_buss_to_lrc_t_ul_r 
ADD COLUMN IF NOT EXISTS pre_cuml_ed_premium DECIMAL(21,4) DEFAULT 0 
COMMENT '上期累计已赚保费';

-- 添加上期累计已赚手续费字段
ALTER TABLE atruser.atr_buss_to_lrc_t_ul_r 
ADD COLUMN IF NOT EXISTS pre_cuml_ed_net_fee DECIMAL(21,4) DEFAULT 0 
COMMENT '上期累计已赚手续费';

-- 为字段添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_atr_buss_to_lrc_t_ul_r_earned 
ON atruser.atr_buss_to_lrc_t_ul_r (treaty_no, policy_no, endorse_seq_no, kind_code, sectiono_code, reinsurer_code);

-- 添加表注释
COMMENT ON TABLE atruser.atr_buss_to_lrc_t_ul_r IS '合约分出底单临时表(R表) - 包含6维度详细数据';
