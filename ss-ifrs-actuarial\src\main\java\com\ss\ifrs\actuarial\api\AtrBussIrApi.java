package com.ss.ifrs.actuarial.api;

import com.ss.ifrs.actuarial.service.impl.AtrBussInterestRateService;
import com.ss.ifrs.actuarial.util.Dates;
import com.ss.ifrs.actuarial.util.EcfUtil;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import org.joda.time.DateTime;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping( "/ir")
public class AtrBussIrApi {

    @Resource
    private AtrBussInterestRateService atrBussInterestRateService;

    @RequestMapping(value = "/fi/{yearMonthStart}-{yearMonthEnd}")
    @PermissionRequest(required = false)
    public BaseResponse<Object> fi(@PathVariable String yearMonthStart, @PathVariable String yearMonthEnd) {
        long start = System.currentTimeMillis();

        for (int i = 0; i < 10000; i++) {
            String yearMonth = Dates.getYearMonth(yearMonthStart, i);
            if (yearMonth.compareTo(yearMonthEnd) > 0) {
                break;
            }
            atrBussInterestRateService.calcForwardAndInitail(yearMonth);
        }

        double cost = EcfUtil.round((System.currentTimeMillis() - start) / 1000.0f, 2);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "ir-fi " + new DateTime() + ", cost " + cost);
    }



}
