package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.dao.AtrBussDataCheckDao;
import com.ss.ifrs.actuarial.domain.service.AtrDomainTaskService;
import com.ss.ifrs.actuarial.feign.BmsActProcFeignClient;
import com.ss.ifrs.actuarial.feign.BmsConfCodeFeignClient;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.ifrs.actuarial.service.AtrBussConfigCheckService;
import com.ss.platform.pojo.com.vo.ActOverviewVo;
import com.ss.platform.core.constant.*;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

@Service
public class AtrBussConfigCheckServiceImpl implements AtrBussConfigCheckService {

    private static final Logger log = LoggerFactory.getLogger(AtrBussConfigCheckServiceImpl.class);
    @Autowired
    AtrBussDataCheckDao atrBussDataCheckDao;

    @Autowired
    private BmsActProcFeignClient actProcFeignClient;

    @Autowired
    private AtrDomainTaskService atrDomainTaskService;

    @Autowired
    private BmsConfCodeFeignClient bmsConfCodeFeignClient;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = UnexpectedRollbackException.class)
    public Map<String, Boolean> configCheck(AtrConfBussPeriodVo atrConfBussPeriodVo, Long userId) throws UnexpectedRollbackException {
        Map<String, Boolean> map = new HashMap<>();
        ActOverviewVo param = new ActOverviewVo();
        param.setProcCode(CommonConstant.ProcMap.SYSTEM_CONFIG.get(SystemConstant.AtrIdentity.APP_CODE));
        ActOverviewVo actOverviewVo = atrDomainTaskService.getActOverviewVoByObject(param);
        if (ObjectUtils.isEmpty(actOverviewVo)) {
            throw new UnexpectedRollbackException("The nodes in the metering configuration process are not configured!");
        }
        String taskCode = this.findTaskCode();
        atrConfBussPeriodVo.setProcId(actOverviewVo.getProcId());
        atrConfBussPeriodVo.setTaskCode(taskCode);
        atrDomainTaskService.configCheck(atrConfBussPeriodVo);
        map.put("dataCheckFlag", true);
        return map;
    }

    private String findTaskCode() {
        try {
            Map<String, Object> paramMap = new HashMap<>(3);
            // DM,EXP...
            paramMap.put("platform", SystemConstant.AtrIdentity.APP_CODE);
            // A-Auto，M-Manual
            paramMap.put("taskMode", CommonConstant.ScheduleTaskMode.MANUAL);
            // GG..
            paramMap.put("taskSection", "ACT");
            // 自动生成手动任务编码
            return (String) bmsConfCodeFeignClient.findTaskCode(paramMap).getResData().get("TASKCODE");
        } catch (Exception e) {
            log.error("findTaskCode error :{}", e.getMessage());
        }
        return null;
    }

}
