CREATE OR REPLACE PACKAGE dm_pack_cmunit_treaty_outward IS

  PROCEDURE proc_cmunit_identify_all(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2);

  PROCEDURE proc_cmunit_identify(p_entity_id  NUMBER,
                                 p_year_month VARCHAR2);

  PROCEDURE proc_majorrisk_test(p_entity_id  NUMBER,
                                p_year_month VARCHAR2);

  PROCEDURE proc_majorrisk_test_nopass(p_entity_id  NUMBER,
                                       p_year_month VARCHAR2);

  PROCEDURE proc_approach_discern(p_entity_id  NUMBER,
                                  p_year_month VARCHAR2);

  PROCEDURE proc_portfolio_discern(p_entity_id  NUMBER,
                                   p_year_month VARCHAR2);

  PROCEDURE proc_profit_loss_discern(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2);

  PROCEDURE proc_icg_discern(p_entity_id  NUMBER,
                             p_year_month VARCHAR2);

  /*PROCEDURE proc_icg_fixed(p_entity_id  NUMBER,
                           p_year_month VARCHAR2);*/

  PROCEDURE proc_icg_group(p_entity_id  NUMBER,
                           p_year_month VARCHAR2);

  PROCEDURE proc_investment_separate(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2);

  PROCEDURE proc_contract_group_confirm(p_entity_id  NUMBER,
                                        p_year_month VARCHAR2);

END dm_pack_cmunit_treaty_outward;
/
CREATE OR REPLACE PACKAGE BODY dm_pack_cmunit_treaty_outward IS

  FUNCTION func_get_current_year_month(p_entity_id  NUMBER,
                                       p_year_month VARCHAR2) RETURN VARCHAR2 IS
  BEGIN
    RETURN dm_pack_buss_period.func_get_current_year_month(p_entity_id, p_year_month, 'BUSS_CMUNIT_TREATY_OUT');

  END func_get_current_year_month;

  FUNCTION func_get_valid_year_month(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2) RETURN VARCHAR2 IS
  BEGIN
    RETURN dm_pack_buss_period.func_get_valid_year_month(p_entity_id, p_year_month, 'BUSS_CMUNIT_TREATY_OUT');

  END func_get_valid_year_month;

  PROCEDURE proc_cmunit_identify_all(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2) IS
    cur_yearmonth VARCHAR2(200);
  BEGIN

    IF p_year_month IS NULL
       OR length(p_year_month) = 0 THEN

      FOR cur_yearmonth IN (SELECT year_month
                              FROM dm_conf_bussperiod
                             WHERE entity_id = p_entity_id
                               AND period_state IN ('0', '1', '2')
                             ORDER BY year_month) LOOP
        -- 合约分入分出 - 生成计量单元
        proc_cmunit_identify(p_entity_id, cur_yearmonth.year_month);
      END LOOP;
    ELSE
      -- 合约分入分出 - 生成计量单元
      proc_cmunit_identify(p_entity_id, p_year_month);
    END IF;

  END proc_cmunit_identify_all;

  PROCEDURE proc_cmunit_identify(p_entity_id  NUMBER,
                                 p_year_month VARCHAR2) IS
    v_proc_id                  INTEGER;
    v_end_date                 DATE;
    v_short_risk_flag_open_is  VARCHAR(1);
    v_error_msg                varchar2(2000);
  BEGIN

    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分出-合同分组]计量单元划分-传参不为空：proc_cmunit_identify(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_valid_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分出-合同分组]计量单元划分-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    v_end_date := add_months(to_date(p_year_month, 'yyyymm'), 1);

    --根据proc_code查询proc_id
    v_proc_id := dm_pack_common.func_get_procid('DM_UNIT_RECOGNIZER');

    --长短险标识生成是否开启 0-不开启
    SELECT MIN('1')
      INTO v_short_risk_flag_open_is
      FROM bpluser.bpl_conf_code
     WHERE code_code = 'ShortRiskFlagOpenIs'
       AND upper_code_id = 0
       AND valid_is = '1';

    IF v_short_risk_flag_open_is <> '1' THEN
      v_short_risk_flag_open_is := '0';
    END IF;

    --合约分出：直接使用合约信息表生成，再保方向为分出，合约类型不为91、92、81、82
     INSERT  /*+APPEND */ INTO dm_buss_cmunit_treaty
      (cm_unit_itreaty_id,
       cmunit_no,
       entity_id,
       ri_direction_code,
       treaty_no,
       treaty_type_code,
       fac_no,
       effective_date,
       expiry_date,
       issue_date,
       epi_currency_code,
       premium,
       currency_code,
       billing_frequency_code,
       floating_charge_is,
       profit_fee_is,
       min_return_amount,
       related_party, --关联交易方
       short_risk_flag, --长短险标志
       draw_type,
       create_time,
       risk_class_code,
       risk_code,
       loa_code,
       business_source_code,
       offshore_is,
       buss_year_month, --业务年月
       proc_id,
       node_state,
       reason_of_failure,
       exception_message
       )
    SELECT dm_seq_buss_cmunit_treaty.nextval cm_unit_itreaty_id,
           dm_pack_cmunit_common.func_get_ri_cmunitno(t.entity_id, 'T', 'O') cmunit_no,
           -- 计量单元编码： 业务单位 || 合约/临分标志 || 分入/分出标志 || 流水号(000001)
           t.entity_id,
           t.ri_direction_code,
           t.treaty_no,
           t.treaty_type_code,
           t.fac_no,
           t.effective_date,
           t.expiry_date,
           t.issue_date,
           t.epi_currency_code,
           t.premium,
           t.currency_code,
           t.billing_frequency_code,
           t.floating_charge_is,
           t.profit_fee_is,
           t.min_return_amount,
           t.related_party,
           t.short_risk_flag, --长短险标志
           t.draw_type,
           localtimestamp as create_time,
           t.risk_class_code,
           t.risk_code,
           t.loa_code,
           t.business_source_code,
           t.offshore_is,
           p_year_month        AS buss_year_month,
           v_proc_id           AS proc_id,
           (CASE WHEN (t.loa_code) IS NULL THEN '2' ELSE '1' END) node_state,
           (CASE WHEN (t.loa_code) IS NULL THEN 'DM_loa' ELSE NULL END)reason_of_failure,
           (CASE WHEN (t.loa_code) IS NULL THEN 'not find loa config' ELSE NULL END)exception_message
      FROM (SELECT row_number() over(PARTITION BY t1.entity_id, t1.treaty_no ORDER BY 1) rn,
                   t1.entity_id,
                   t1.ri_direction_code,
                   t1.treaty_no,
                   MIN(t1.treaty_type_code) treaty_type_code,
                   NULL AS fac_no,
                   MIN(t1.effective_date) effective_date,
                   MIN(t1.expiry_date) expiry_date,
                   MIN(t1.issue_date) issue_date,
                   MIN(t1.epi_currency_code) epi_currency_code,
                   SUM(t1.premium) AS premium,
                   MIN(t1.currency_code) currency_code,
                   MIN(t1.billing_frequency_code) billing_frequency_code,
                   MIN(t1.floating_charge_is) floating_charge_is,
                   MIN(t1.profit_fee_is) profit_fee_is,
                   MIN(t1.min_return_amount) min_return_amount,
                   MIN(t1.related_party) related_party,
                   (CASE
                      WHEN v_short_risk_flag_open_is = '0' THEN '0'
                      WHEN add_months(MIN(t1.effective_date), 12) > MIN(t1.expiry_date) THEN
                       '1' -- 短险
                      ELSE
                       '2' -- 长险
                    END) AS short_risk_flag, --长短险标志
                   MIN(t1.draw_type) draw_type,
                   '00' AS risk_class_code,
                   NULL AS risk_code,
                   MIN(loa.loa_code)loa_code,
                   null AS business_source_code,--业务来源，默认null
                   MIN(t1.offshore_is) offshore_is
              FROM dm_reins_treaty t1
              LEFT JOIN bpluser.bbs_conf_loa_detail loat
                ON loat.business_id = t1.base_treaty_id
              LEFT JOIN bpluser.bbs_conf_loa loa
                ON loat.loa_id = loa.loa_id
               AND loa.business_model = 'T'
               AND loa.business_direction = 'O'
               AND loa.valid_is = '1'
               AND loa.audit_state = '1'
               AND loa.entity_id = t1.entity_id
             WHERE t1.entity_id = p_entity_id
               AND t1.task_status = '4'
               AND t1.effective_date <  v_end_date
               AND t1.expiry_date >= v_end_date
                  --AND T1.issue_date < V_END_DATE
               AND t1.ri_direction_code = 'O' --v_reins_direction_out
               AND t1.treaty_type_code NOT IN ('81', '82','91', '92') --81,82自留合约,91,92临分合约不进行触发计量单元
               AND NOT EXISTS (SELECT 1
                      FROM dm_buss_cmunit_treaty cmt
                     WHERE cmt.entity_id = t1.entity_id
                       AND cmt.treaty_no = t1.treaty_no
                       AND cmt.ri_direction_code = t1.ri_direction_code
                    --确保不重复生成计量单元
                    )
             GROUP BY t1.entity_id, t1.treaty_no, t1.ri_direction_code
         )t
         WHERE rn = 1;

   COMMIT;

    --已有计量单元loa重新配置
    --只有处理中的才能重新执行
    IF func_get_current_year_month(p_entity_id, p_year_month) IS NOT NULL THEN

      MERGE INTO dm_buss_cmunit_treaty cma
      USING (SELECT cm.cm_unit_itreaty_id,
                    loa.loa_code
               FROM dm_buss_cmunit_treaty cm
               LEFT JOIN bpluser.bbs_conf_treaty ty
                 ON ty.entity_id = cm.entity_id
                AND ty.treaty_no = cm.treaty_no
                AND ty.ri_direction_code = cm.ri_direction_code
                AND ty.treaty_type_code NOT IN ('81', '82','91', '92') --81,82自留合约、91,92临分合约不进行触发计量单元
                left join (select loa.entity_id,
                          loa.loa_code,
                          loat.business_id,
                          loa.business_direction
                    from bpluser.bbs_conf_loa loa
                    JOIN bpluser.bbs_conf_loa_detail loat
                 ON loa.loa_id = loat.loa_id
                    where 1=1
                AND loa.business_model = 'T'
                AND loa.valid_is = '1'
                    AND loa.audit_state = '1') loa
                     ON loa.business_id = ty.base_treaty_id
                     and loa.entity_id = ty.entity_id
                     AND loa.business_direction = cm.ri_direction_code
              WHERE cm.ri_direction_code = 'O'
                AND cm.year_month IS NULL
                AND cm.buss_year_month <= p_year_month
             ) cmb
      ON (cma.cm_unit_itreaty_id = cmb.cm_unit_itreaty_id)
      WHEN MATCHED THEN
        UPDATE
           SET major_risk           = NULL,
               evaluate_approach    = NULL, --评估方法
               portfolio_no         = NULL,
               pl_judge_rslt        = NULL, --盈亏
               pl_judge_date        = NULL, --盈亏时间
               year_month           = NULL,
               border_date          = NULL,
               icg_no               = NULL,
               invest_rate          = NULL,
               invest_amount        = NULL,
               proc_id              = v_proc_id,
               loa_code             = cmb.loa_code,
               node_state           = (CASE WHEN cmb.loa_code IS NULL THEN '2' ELSE '1' END),
               reason_of_failure    = (CASE WHEN cmb.loa_code IS NULL THEN 'DM_loa' ELSE NULL END),
               reason_of_mr_failure = NULL,
               exception_message    = (CASE WHEN cmb.loa_code IS NULL THEN 'not find loa config' ELSE NULL END);

      COMMIT;

    END IF;

    -- 修改已生成计量单元标识 非比例合约分出
    UPDATE dm_reins_treaty t1
       SET task_status = '5'
     WHERE EXISTS (SELECT 1
              FROM dm_buss_cmunit_treaty cmt
             WHERE cmt.entity_id = t1.entity_id
               AND cmt.treaty_no = t1.treaty_no
               AND cmt.ri_direction_code = t1.ri_direction_code
               AND cmt.treaty_type_code = t1.treaty_type_code)
       AND t1.entity_id = p_entity_id
       AND t1.task_status = '4'
       AND t1.effective_date < v_end_date
       AND t1.expiry_date >= v_end_date
       AND t1.treaty_type_code NOT IN ('81', '82','91','92') --自留合约不进行触发计量单元
       AND t1.ri_direction_code = 'O';

    COMMIT;

    -- 不符合计量单元生成规则的数据,不再处理
    UPDATE dm_reins_treaty t1
       SET task_status = '6'
     WHERE t1.entity_id = p_entity_id
       AND t1.task_status = '4'
       AND t1.effective_date < v_end_date
       AND t1.expiry_date >= v_end_date
       AND t1.treaty_type_code not IN ('91', '92') --临分合约不进行处理
       --AND t1.treaty_type_code IN ('81', '82') --自留合约不进行触发计量单元
       AND t1.ri_direction_code = 'O';

    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN

       --意外处理
      v_error_msg := '[EXCEPTION][合约分出-合同分组]计量单元划分:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);


  END proc_cmunit_identify;

  PROCEDURE proc_majorrisk_test(p_entity_id  NUMBER,
                                p_year_month VARCHAR2) IS
    v_proc_id        NUMBER;
    v_error_msg      varchar2(2000);
  BEGIN

    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分出-合同分组]重测处理-传参不为空：proc_majorrisk_test(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]重测处理-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 当前业务年月存在未进行重大风险测试的数据
    -- 根据PROC_CODE查询PROC_ID
    v_proc_id := dm_pack_common.func_get_procid('DM_RISK_TEST');

    --全部通过(不看任何配置)

    -- 根据配置表设置全部通过
    UPDATE dm_buss_cmunit_treaty t
       SET proc_id           = v_proc_id,
           evaluate_approach = NULL, --评估方法
           portfolio_no      = NULL,
           pl_judge_rslt     = NULL, --盈亏
           pl_judge_date     = NULL, --盈亏时间
           year_month        = NULL,
           border_date       = NULL,
           icg_no            = NULL,
           invest_rate       = NULL,
           invest_amount     = NULL,
           node_state        = '1',
           major_risk        = 'Y' --不在配置表的计量单元默认通过
     WHERE t.entity_id = p_entity_id
       AND t.buss_year_month <= p_year_month
       AND t.year_month IS NULL
       AND t.ri_direction_code = 'O';
    --AND (T.MAJOR_RISK IS NULL OR  (T.NODE_STATE = '2' AND T.PROC_ID = V_PROC_ID));
    COMMIT;

    proc_majorrisk_test_nopass(p_entity_id, p_year_month);

  EXCEPTION
    WHEN OTHERS THEN

      --意外处理
      -- 执行异常，记录异常原因
      v_error_msg := '[EXCEPTION][合约分出-合同分组]重测处理:'||SQLERRM;
        --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_majorrisk_test;

  PROCEDURE proc_majorrisk_test_nopass(p_entity_id  NUMBER,
                                       p_year_month VARCHAR2) IS
    v_icg_column         VARCHAR2(4000);
    v_portfolio_column    VARCHAR2(4000);
    v_icg_proc_id        NUMBER;
    v_portflio_proc_id   NUMBER;
    v_error_msg          varchar2(2000);
  BEGIN
    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分出-合同分组]重测处理不通过-传参不为空：proc_majorrisk_test_nopass(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分出-合同分组]重测处理不通过-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 重测业务不通过，生成合同组合
    v_portfolio_column := dm_pack_cmunit_common.func_get_contractcode(p_entity_id, 'G', 'TO');

    IF v_portfolio_column IS NOT NULL THEN
      -- 根据PROC_CODE查询PROC_ID
      v_portflio_proc_id := dm_pack_common.func_get_procid('DM_PORTFOLIO');

      v_icg_column := dm_pack_cmunit_common.func_get_contractcode(p_entity_id, 'C', 'TO');

      IF v_icg_column IS NOT NULL THEN
         v_icg_proc_id := dm_pack_common.func_get_procid('DM_CONTRACTGROUP');
      END IF;
    END IF;

    -- 1,评估方法及盈亏判定设置为 D-不区分
    -- 2,生成合同组合编码
    -- 3,更新合同组编码
    -- 4,《确认日期》为确认不通过的业务期间月末

    EXECUTE IMMEDIATE ('update dm_buss_cmunit_treaty t '
                       ||' set evaluate_approach = ''D'''
                       ||', pl_judge_rslt = ''D'''
                       ||', portfolio_no = ' || v_portfolio_column
                       ||', icg_no = ' || v_icg_column
                       ||', border_date = ' || (CASE WHEN v_icg_column IS NOT NULL THEN 'last_day(to_date('''||p_year_month||'01'',''YYYYMMDD''))' END)
                       ||', proc_id= ' || (CASE WHEN v_portfolio_column IS NULL THEN v_portflio_proc_id ELSE v_icg_proc_id END)
                       ||', node_state= ''' || (CASE WHEN v_portflio_proc_id IS NULL OR v_icg_proc_id IS NULL THEN '2' ELSE '1' END)||''''
                       ||', reason_of_failure = ''' || (CASE WHEN v_portfolio_column IS NULL OR v_icg_column IS NULL THEN 'DM_003' ELSE NULL END) ||''''
                       ||', reason_of_mr_failure = ''' || (CASE WHEN v_portfolio_column IS NULL OR v_icg_column IS NULL THEN 'DM_003' ELSE NULL END) ||''''
                    ||' where entity_id = ' || p_entity_id
                    ||' and MAJOR_RISK in (''N'',''O'') '
                    --||' and major_risk in (''N'',''O'')'
                    --||' and pl_judge_rslt is not null '
                    --||' and  portfolio_no is null '
                    ||' and ri_direction_code = ''O'' '
                    ||' and year_month is null '
                    ||' and buss_year_month <= ''' || p_year_month || '''');
    COMMIT;

    /*
    --合约分入：投成拆分
    -- 根据PROC_CODE查询PROC_ID
    v_proc_id := dm_pack_common.func_get_procid('DM_INVESTMENT_COST');
    IF v_proc_id IS NULL THEN
      RETURN;
    END IF;

    UPDATE DM_BUSS_CMUNIT_TREATY CMIN
      SET INVEST_RATE = COALESCE(TO_NUMBER(BCQ.QUOTA_VALUE,'999.999999'),0) ,INVEST_AMOUNT = COALESCE(PREMIUM,0)*COALESCE(TO_NUMBER(BCQ.QUOTA_VALUE,'999.999999'),0),
        PROC_ID = V_PROC_ID,NODE_STATE= '1' ,
        REASON_OF_FAILURE = NULL
    FROM
        BPLUSER.BBS_CONF_QUOTA BCQ,DM_REINS_CONF_TREATY RCT
      WHERE
        BCQ.QUOTA_DEF_ID = ( SELECT QUOTA_DEF_ID FROM BPLUSER.BBS_CONF_QUOTA_DEF WHERE QUOTA_CODE = 'QR001' )--取投成拆分字段
        AND BCQ.BUSINESS_MODEL = 'T'--取再保临分的
        AND CMIN.entity_id = BCQ.entity_id
        AND CMIN.entity_id = RCT.entity_id
        AND CMIN.TREATY_NO = RCT.TREATY_NO
        AND RCT.treaty_class_code = BCQ.treaty_class_code
        AND BCQ.VALID_IS = '1'
        AND BCQ.AUDIT_STATE = '1'
        AND CMIN.MAJOR_RISK = 'O'
        AND CMIN.EVALUATE_APPROACH IS NOT NULL
        AND CMIN.PL_JUDGE_RSLT IS NOT NULL
        AND CMIN.BORDER_DATE IS NOT NULL
        AND CMIN.ICG_NO IS NOT NULL
        AND CMIN.INVEST_RATE IS NULL
        AND CMIN.INVEST_AMOUNT IS NULL
        AND CMIN.entity_id = p_entity_id
        AND CMIN.ri_direction_code = V_REINS_DIRECTION
        AND CMIN.YEAR_MONTH IS NULL
        AND CMIN.BUSS_YEAR_MONTH <= P_YEAR_MONTH;*/

    /* UPDATE DM_BUSS_CMUNIT_TREATY CMIN
      SET (INVEST_RATE,
           INVEST_AMOUNT,
           PROC_ID,
           NODE_STATE,
           REASON_OF_FAILURE) =
          (SELECT COALESCE(TO_NUMBER(BCQ.QUOTA_VALUE, '999.999999'), 0) AS INVEST_RATE,
                  COALESCE(PREMIUM, 0) *
                  COALESCE(TO_NUMBER(BCQ.QUOTA_VALUE, '999.999999'), 0) AS INVEST_AMOUNT,
                  V_PROC_ID AS PROC_ID,
                  '1' AS NODE_STATE,
                  NULL AS REASON_OF_FAILURE
             FROM BBS_CONF_QUOTA BCQ
            WHERE BCQ.QUOTA_DEF_ID =
                  (SELECT QUOTA_DEF_ID
                     FROM BBS_CONF_QUOTA_DEF
                    WHERE QUOTA_CODE = 'ReportingPeriodRatio')
              AND BCQ.BUSINESS_MODEL = 'T'
              AND CMIN.entity_id = BCQ.entity_id
              --AND CMIN.treaty_class_code = BCQ.treaty_class_code
              AND BCQ.VALID_IS = '1'
              AND BCQ.AUDIT_STATE = '1'
              AND CMIN.MAJOR_RISK = 'O')
    WHERE CMIN.EVALUATE_APPROACH IS NOT NULL
      AND CMIN.PL_JUDGE_RSLT IS NOT NULL
      AND CMIN.BORDER_DATE IS NOT NULL
      AND CMIN.ICG_NO IS NOT NULL
      AND CMIN.INVEST_RATE IS NULL
      AND CMIN.INVEST_AMOUNT IS NULL
      AND CMIN.entity_id = p_entity_id
      AND CMIN.ri_direction_code = V_REINS_DIRECTION
      AND CMIN.YEAR_MONTH IS NULL
      AND CMIN.BUSS_YEAR_MONTH <= P_YEAR_MONTH;

     --合约分入未配置投成拆分规则
       UPDATE DM_BUSS_CMUNIT_TREATY CMIN
       SET PROC_ID = V_PROC_ID,NODE_STATE= '2',REASON_OF_FAILURE = 'DM_001'
       WHERE  CMIN.MAJOR_RISK = 'O'
         AND CMIN.EVALUATE_APPROACH IS NOT NULL
         AND CMIN.PL_JUDGE_RSLT IS NOT NULL
         AND CMIN.BORDER_DATE IS NOT NULL
         AND CMIN.ICG_NO IS NOT NULL
         AND CMIN.ri_direction_code = V_REINS_DIRECTION
         AND CMIN.INVEST_RATE IS NULL
         AND CMIN.INVEST_AMOUNT IS NULL
         AND CMIN.entity_id = p_entity_id
         AND CMIN.YEAR_MONTH IS NULL
         AND CMIN.BUSS_YEAR_MONTH <= P_YEAR_MONTH;*/

  EXCEPTION
    WHEN OTHERS THEN

      -- 执行异常，记录异常原因
      --意外处理
      v_error_msg := '[EXCEPTION][合约分出-合同分组]重测处理:'||SQLERRM;

      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_majorrisk_test_nopass;

  PROCEDURE proc_approach_discern(p_entity_id  NUMBER,
                                  p_year_month VARCHAR2) IS
    v_proc_id        NUMBER;
    v_error_msg      varchar2(2000);
  BEGIN
    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分出-合同分组]评估方法适配-传参不为空：proc_approach_discern(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分出-合同分组]评估方法适配-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 根据proc_code查询proc_id
    v_proc_id := dm_pack_common.func_get_procid('DM_EVALDEF_CONFIG');

    MERGE INTO dm_buss_cmunit_treaty t
    USING (SELECT t1.cm_unit_itreaty_id,
                  ce.evaluate_approach
             FROM dm_buss_cmunit_treaty t1
             LEFT JOIN bpluser.bbs_conf_loa ce
               ON ce.entity_id = t1.entity_id
              AND ce.loa_code = t1.loa_code
              AND ce.audit_state = '1'
              AND ce.valid_is = '1'
              AND ce.business_model = 'T'
              AND ce.business_direction = t1.ri_direction_code
            WHERE t1.major_risk IN ('Y', 'P')
              AND t1.ri_direction_code = 'O'
              AND t1.year_month IS NULL
              AND t1.entity_id = p_entity_id
              AND t1.buss_year_month <= p_year_month
           ) c
    ON (t.cm_unit_itreaty_id = c.cm_unit_itreaty_id)
    WHEN MATCHED THEN
      UPDATE
         SET t.portfolio_no      = NULL,
             t.pl_judge_rslt     = NULL, --盈亏
             t.pl_judge_date     = NULL, --盈亏时间
             t.year_month        = NULL,
             t.border_date       = NULL,
             t.icg_no            = NULL,
             t.invest_rate       = NULL,
             t.invest_amount     = NULL,
             t.proc_id           = v_proc_id,
             t.evaluate_approach = c.evaluate_approach,
             t.node_state        = (case when c.evaluate_approach is not null then '1' else '2' end),--未配置loa_code提示异常
             t.reason_of_failure = (case when c.evaluate_approach is not null then null else 'DM_001' end)
       WHERE t.major_risk IN ('Y', 'P')
         AND t.ri_direction_code = 'O'
         AND t.year_month IS NULL
         AND t.entity_id = p_entity_id
         AND t.buss_year_month <= p_year_month
      ;
      COMMIT;

  EXCEPTION
    WHEN OTHERS THEN

      -- 执行异常，记录异常原因
      v_error_msg := '[EXCEPTION][合约分出-合同分组]评方法适配:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_approach_discern;

  PROCEDURE proc_portfolio_discern(p_entity_id  NUMBER,
                                   p_year_month VARCHAR2) IS
    v_column      VARCHAR2(4000);
    v_proc_id     INTEGER;
    v_error_msg   varchar2(2000);
  BEGIN
    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分出-合同分组]合同组合划分-传参不为空：proc_portfolio_discern(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分出-合同分组]合同组合划分-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 根据PROC_CODE查询PROC_ID(合同组合)
    v_proc_id := dm_pack_common.func_get_procid('DM_PORTFOLIO');
    IF v_proc_id IS NULL THEN
      RETURN;
    END IF;

    -- 合约分出
    v_column := dm_pack_cmunit_common.func_get_contractcode(p_entity_id, 'G', 'TO');

      -- 更新合同组合编码
    --DBMS_OUTPUT.PUT_LINE(V_COLUMN);
    EXECUTE IMMEDIATE ('update  dm_buss_cmunit_treaty t'
                      ||' set PORTFOLIO_NO = ' || v_column || ','
                      ||' proc_id=' || v_proc_id || ','
                      ||' pl_judge_rslt = null ,'
                      ||' pl_judge_date = null ,'
                      ||' year_month = null ,'
                      ||' border_date = null ,'
                      ||' icg_no = null,'
                      ||' invest_rate = null,'
                      ||' invest_amount = null ,'
                      ||' node_state= '''|| (CASE WHEN v_column IS NULL THEN '2' ELSE '1' END) ||''','
                      ||' reason_of_failure = ''' || (CASE WHEN v_column IS NULL THEN 'DM_003' ELSE NULL END) ||''''
                      ||' where entity_id = ' || p_entity_id
                      ||' and evaluate_approach <> ''D'' '
                      --|| ' and major_risk in (''Y'', ''P'') '
                      --|| ' and EVALUATE_APPROACH is not null '
                      ||' and year_month is null '
                      ||' and ri_direction_code = ''O'' '
                      ||' and buss_year_month <= ''' || p_year_month || ''' ');
    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN

      -- 执行异常，记录异常原因
      v_error_msg := '[EXCEPTION][合约分出-合同分组]合同组合划分:'||SQLERRM;
      --意外处理
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_portfolio_discern;

  PROCEDURE proc_profit_loss_discern(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2) IS

    v_proc_id                        INTEGER;
    v_current_day                    DATE; -- 当前业务年月第一天
    v_error_msg                      varchar2(2000);

  BEGIN
    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分出-合同分组]盈亏判定-传参不为空：proc_profit_loss_discern(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

       --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分出-合同分组]盈亏判定-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 根据PROC_CODE查询PROC_ID
    v_proc_id := dm_pack_common.func_get_procid('DM_DETERMINATION');

    --盈亏取业务年月最一天作为盈亏判定时间
    v_current_day := add_months(to_date(p_year_month, 'yyyymm'), 1) - INTERVAL '1' SECOND;
    -- 合约分出：盈亏判定设定为不区分
    UPDATE dm_buss_cmunit_treaty
       SET proc_id       = v_proc_id,
           node_state    = '1',
           pl_judge_date = v_current_day,
           pl_judge_rslt = 'D',
           year_month    = NULL,
           border_date   = NULL,
           icg_no        = NULL,
           invest_rate   = NULL,
           invest_amount = NULL
     WHERE entity_id = p_entity_id
       AND evaluate_approach <> 'D'
       --AND major_risk IN ('Y', 'P')
       AND buss_year_month <= p_year_month
       AND ri_direction_code = 'O' -- 再保方向  分出
       AND portfolio_no IS NOT NULL
       AND year_month IS NULL; --未确认的评估期
    --AND PL_JUDGE_RSLT IS NULL
    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN

      --意外处理
      v_error_msg := '[EXCEPTION][合约分出-合同分组]盈亏判定:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_profit_loss_discern;

  PROCEDURE proc_icg_fixed(p_entity_id  NUMBER,
                           p_year_month VARCHAR2) IS
    v_end_date             TIMESTAMP;
    v_error_msg            varchar2(2000);
  BEGIN
    /*-- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      RETURN;
    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      RETURN;
    END IF;*/

    v_end_date := add_months(to_date(p_year_month, 'yyyymm'),1);-- 当前业务年月的下个月第一天

    --更新D1,D2,D3
    MERGE INTO dm_buss_cmunit_treaty cma
    USING (SELECT cmt.cm_unit_itreaty_id AS cm_unit_itreaty_id,
                  MIN(rt.issue_date) AS d1,
                  MIN(rb.statement_payment_date) AS d2,
                  MIN(rt.effective_date) AS d3--直接取合约信息表起保日期为D3，
             FROM dmuser.dm_buss_cmunit_treaty cmt
             LEFT JOIN dmuser.dm_reins_bill rb -- D2,账单应收应付款日期
               ON rb.entity_id = cmt.entity_id
              AND rb.treaty_no = cmt.treaty_no
              AND rb.ri_direction_code = cmt.ri_direction_code
              AND rb.statement_payment_date < v_end_date
              AND cmt.fac_no IS NULL
             LEFT JOIN dmuser.dm_reins_treaty rt --获取D1:REINS_TREATY.issue_date,D3:REINS_TREATY.EFFECTIVE_DATE
               ON rt.entity_id = cmt.entity_id
              AND rt.treaty_no = cmt.treaty_no
              AND rt.ri_direction_code = cmt.ri_direction_code
            WHERE cmt.entity_id = p_entity_id
              AND cmt.evaluate_approach <> 'D'
              AND cmt.pl_judge_rslt IS NOT NULL -- 盈亏判定
              AND cmt.year_month IS NULL -- 未生成合同组
              AND cmt.buss_year_month <= p_year_month
              AND cmt.ri_direction_code = 'O'
            GROUP BY cmt.cm_unit_itreaty_id
          ) cmb
    ON (cma.cm_unit_itreaty_id = cmb.cm_unit_itreaty_id)
    WHEN MATCHED THEN
      UPDATE
         SET cma.d1 = cmb.d1,
             cma.d2 = cmb.d2,
             cma.d3 = cmb.d3
       WHERE cma.entity_id = p_entity_id
         AND cma.evaluate_approach <> 'D'
         AND cma.pl_judge_rslt IS NOT NULL -- 盈亏判定
         AND cma.year_month IS NULL -- 未生成合同组
         AND cma.buss_year_month <= p_year_month
         AND cma.ri_direction_code = 'O';
    COMMIT;

    --更新 D4,border_date
    MERGE INTO dm_buss_cmunit_treaty cma
    USING (SELECT cmt.cm_unit_itreaty_id,
                  MIN(CASE WHEN cmt.treaty_type_code IN ('71', 72) THEN  greatest(cmt.d1, cmt.d3)--非比例合约分出 max(D1,D3)
                      ELSE greatest(cmt.d1, (case when cmt.d3 is not null then cmt.d3 else cmt.d1 end)) --比例合约分出 max(D1,D3)
                   END) AS d4
             FROM dmuser.dm_buss_cmunit_treaty cmt
            WHERE cmt.entity_id = p_entity_id
              AND cmt.evaluate_approach <> 'D'
              AND cmt.pl_judge_rslt IS NOT NULL -- 盈亏判定
              AND cmt.year_month IS NULL -- 未生成合同组
              AND cmt.buss_year_month <= p_year_month
              AND cmt.ri_direction_code = 'O'
            GROUP BY cmt.cm_unit_itreaty_id) cmb
    ON (cma.cm_unit_itreaty_id = cmb.cm_unit_itreaty_id)
    WHEN MATCHED THEN
      UPDATE
         SET
             cma.d4          = cmb.d4 ,
             cma.border_date = (CASE WHEN cmb.d4 >= v_end_date THEN NULL ELSE cmb.d4 END)
       WHERE cma.entity_id = p_entity_id
         AND cma.evaluate_approach <> 'D'
         AND cma.pl_judge_rslt IS NOT NULL -- 盈亏判定
         AND cma.year_month IS NULL -- 未生成合同组
         AND cma.buss_year_month <= p_year_month
         AND cma.ri_direction_code = 'O';
    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN

      --意外处理
      v_error_msg := '[EXCEPTION][合约分出-合同分组]合同分组-获取合同确认日期业务数据:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_icg_fixed;

  PROCEDURE proc_icg_discern(p_entity_id  NUMBER,
                             p_year_month VARCHAR2) IS

    v_column     VARCHAR2(4000);
    v_proc_id    INTEGER;
    v_error_msg  varchar2(2000);
  BEGIN
    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分出-合同分组]合同分组-传参不为空：proc_icg_discern(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分出-合同分组]合同分组-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 合同确认日期
    proc_icg_fixed(p_entity_id, p_year_month);

    -- 根据PROC_CODE查询PROC_ID
    v_proc_id := dm_pack_common.func_get_procid('DM_CONTRACTGROUP');

    -- 合约分出
    v_column := dm_pack_cmunit_common.func_get_contractcode(p_entity_id, 'C', 'TO');

    -- 更新合同组编码
    --DBMS_OUTPUT.PUT_LINE(V_COLUMN);
    EXECUTE IMMEDIATE ('update dm_buss_cmunit_treaty t'
                      ||' set icg_no = (case when border_date is not null then ' || v_column || 'else null end),'
                      ||' proc_id=' || v_proc_id || ','
                      ||' node_state= '''|| (CASE WHEN v_column IS NULL THEN '2' ELSE '1' END) ||''' ,'
                      ||' reason_of_failure = ''' || (CASE WHEN v_column IS NULL THEN 'DM_003' ELSE NULL END) ||''' ,'
                      ||' invest_rate = null ,'
                      ||' invest_amount = null '
                      ||' where entity_id = ' || p_entity_id
                      ||' and ri_direction_code = ''O'' '
                      ||' and evaluate_approach <> ''D'' '
                      ||' and pl_judge_rslt IS NOT NULL'
                      ||' and year_month IS NULL '
                      ||' and buss_year_month <= ''' || p_year_month || ''' ');
    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN

      --意外处理
      v_error_msg := '[EXCEPTION][合约分出-合同分组]合同分组:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_icg_discern;

  PROCEDURE proc_icg_group(p_entity_id  NUMBER,
                           p_year_month VARCHAR2) IS
  BEGIN

    --  生成合同组编码
    proc_icg_discern(p_entity_id, p_year_month);

  END proc_icg_group;

  PROCEDURE proc_investment_separate(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2) IS

    v_proc_id     INTEGER;
    v_data_key    VARCHAR2(200);
    v_row         INTEGER;
    v_current_day DATE; -- 当前业务年月第一天
    v_year_month  VARCHAR2(200);
    v_error_msg   varchar2(2000);
  BEGIN


    -- 条件不满足,结束判定
    IF p_entity_id IS NULL
       AND p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分出-合同分组]投成拆分-传参不为空：proc_investment_separate(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);
    END IF;

    -- 限定只处理 处于处理中的业务年月
    SELECT year_month
      INTO v_year_month
      FROM dm_conf_bussperiod
     WHERE entity_id = p_entity_id
       AND year_month = p_year_month
       AND period_state = '2' -- 处理中
       AND valid_is = '1'
       AND rownum = 1;

    -- 条件不满足,结束判定
    IF v_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分出-合同分组]投成拆分-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 当前业务年月所有计量单元都生成合同分组，才能进行投成拆分
    SELECT 1
      INTO v_row
      FROM dm_buss_cmunit_treaty
     WHERE entity_id = p_entity_id
       AND buss_year_month <= p_year_month
       AND icg_no IS NOT NULL
       AND invest_rate IS NULL
       AND invest_amount IS NULL
       AND rownum = 1;
    -- 存在合同分组为空的数据
    IF v_row != 1 THEN
      RETURN;
    END IF;

    -- 根据PROC_CODE查询PROC_ID
    v_proc_id := dm_pack_common.func_get_procid('DM_INVESTMENT_COST');
    IF v_proc_id IS NULL THEN
      RETURN;
    END IF;

    --不需要做
    RETURN;

    /*UPDATE DM_BUSS_CMUNIT_TREATY CMTR
    SET INVEST_RATE = COALESCE(TO_NUMBER(BCQ.QUOTA_VALUE,'999.999999'),0) ,INVEST_AMOUNT = COALESCE(PREMIUM,0)*COALESCE(TO_NUMBER(BCQ.QUOTA_VALUE,'999.999999'),0),
      PROC_ID = V_PROC_ID,NODE_STATE= '1' ,
      REASON_OF_FAILURE = NULL
    FROM
      BPLUSER.BBS_CONF_QUOTA BCQ,DM_REINS_CONF_TREATY RCT
    WHERE
      BCQ.QUOTA_DEF_ID = ( SELECT QUOTA_DEF_ID FROM BPLUSER.BBS_CONF_QUOTA_DEF WHERE QUOTA_CODE = 'QR001' )--取投成拆分字段
      AND BCQ.BUSINESS_MODEL = 'T'--取再保临分的
      AND CMTR.entity_id = BCQ.entity_id
      AND CMTR.entity_id = RCT.entity_id
      AND CMTR.TREATY_NO = RCT.TREATY_NO
      AND RCT.treaty_class_code = BCQ.treaty_class_code
      AND BCQ.VALID_IS = '1'
      AND BCQ.AUDIT_STATE = '1'
      AND CMTR.MAJOR_RISK IS NOT NULL
      AND CMTR.EVALUATE_APPROACH IS NOT NULL
      AND CMTR.PL_JUDGE_RSLT IS NOT NULL
      AND CMTR.BORDER_DATE IS NOT NULL
      AND CMTR.ICG_NO IS NOT NULL
      --AND CMTR.INVEST_RATE IS NULL
      --AND CMTR.INVEST_AMOUNT IS NULL
      AND CMTR.entity_id = p_entity_id
      AND CMTR.YEAR_MONTH IS NULL
      AND CMTR.ri_direction_code = 'O'
      AND CMTR.BUSS_YEAR_MONTH <= P_YEAR_MONTH;*/

    --WLI未有投成拆分的功能
    IF v_proc_id is NULL
       OR v_proc_id = '' THEN
      RETURN;
    END IF;

  EXCEPTION
    WHEN OTHERS THEN

      --意外处理
      v_error_msg := '[EXCEPTION][合约分出-合同分组]投成拆分:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_investment_separate;

  PROCEDURE proc_contract_group_confirm(p_entity_id  NUMBER,
                                        p_year_month VARCHAR2) IS
    v_error_msg          varchar2(2000);
  BEGIN

    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分出-合同分组]确认-传参不为空：proc_contract_group_confirm(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

    --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][合约分出-合同分组]确认-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    UPDATE dm_buss_cmunit_treaty
       SET year_month = p_year_month
     WHERE entity_id = p_entity_id
       AND buss_year_month <= p_year_month
       AND year_month IS NULL
       AND icg_no IS NOT NULL
       AND ri_direction_code = 'O';
    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN

     --意外处理
      v_error_msg := '[EXCEPTION][合约分出-合同分组]确认:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_contract_group_confirm;

END dm_pack_cmunit_treaty_outward;
/
