<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-03-08 14:14:11 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussFOLrcIcgCalcDao">
  <!-- 本配置文件由GIS MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**IDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->
  <select id="findDateByVo" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
    select
      a.ID as "id",
      a.ACTION_NO as "actionNo",
      a.TASK_CODE as "taskCode",
      a.entity_id as "entityId",
      a.currency_code as "currency",
      a.YEAR_MONTH as "yearMonth",
      a.PORTFOLIO_NO as "portfolioNo",
      a.ICG_NO as "icgNo",
      a.EVALUATE_APPROACH as "evaluateApproach",
      a.LOA_CODE as "loaCode",
      c.entity_code as "entityCode",
      c.entity_e_name as "entityEName",
      c.entity_c_name as "entityCName",
      c.entity_l_name  as "entityLName"
    from ATR_BUSS_FO_LRC_ICG_CALC a
           left join bpluser.bbs_conf_entity c on a.entity_id = c.entity_id
           left join bpluser.BBS_CONF_CURRENCY cur on a.currency_code = cur.CURRENCY_CODE
    where a.action_no = #{actionNo,jdbcType=VARCHAR}
    <if test="portfolioNo != null and portfolioNo != ''">
      and a.PORTFOLIO_NO = #{portfolioNo,jdbcType=VARCHAR}
    </if>
    <if test="icgNo != null and icgNo != ''">
      and a.icg_no = #{icgNo,jdbcType=VARCHAR}
    </if>
  </select>

</mapper>