<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-02-28 10:45:52 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.dap.AtrDapDDIcgClaimPaidDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrdap.po.AtrDapDDIcgClaimPaid">
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="entity_id" property="entityId" jdbcType="DECIMAL" />
    <result column="LOSS_NO" property="lossNo" jdbcType="VARCHAR" />
    <result column="LOSS_SEQ_NO" property="lossSeqNo" jdbcType="DECIMAL" />
    <result column="CHECK_DATE" property="checkDate" jdbcType="TIMESTAMP" />
    <result column="DEAL_DATE" property="dealDate" jdbcType="TIMESTAMP" />
    <result column="YEAR_MONTH" property="yearMonth" jdbcType="VARCHAR" />
    <result column="PORTFOLIO_NO" property="portfolioNo" jdbcType="VARCHAR" />
    <result column="ICG_NO" property="icgNo" jdbcType="VARCHAR" />
    <result column="EVALUATE_APPROACH" property="evaluateApproach" jdbcType="VARCHAR" />
    <result column="LOA_CODE" property="loaCode" jdbcType="VARCHAR" />
    <result column="PRODUCT_CODE" property="productCode" jdbcType="VARCHAR" />
    <result column="RISK_CODE" property="riskCode" jdbcType="VARCHAR" />
    <result column="CMUNIT_NO" property="cmunitNo" jdbcType="VARCHAR" />
    <result column="CLAIM_NO" property="claimNo" jdbcType="VARCHAR" />
    <result column="POLICY_NO" property="policyNo" jdbcType="VARCHAR" />
    <result column="ENDORSE_SEQ_NO" property="endorseSeqNo" jdbcType="VARCHAR" />
    <result column="DAMAGE_DATE" property="damageDate" jdbcType="TIMESTAMP" />
    <result column="DEPT_ID" property="deptId" jdbcType="DECIMAL" />
    <result column="CURRENCY_CODE" property="currencyCode" jdbcType="VARCHAR" />
    <result column="PAID_AMOUNT" property="paidAmount" jdbcType="DECIMAL" />
    <result column="DRAW_TIME" property="drawTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    ID, entity_id, LOSS_NO, LOSS_SEQ_NO, CHECK_DATE, DEAL_DATE, YEAR_MONTH, PORTFOLIO_NO,
    ICG_NO, EVALUATE_APPROACH, LOA_CODE, PRODUCT_CODE, RISK_CODE, CMUNIT_NO, CLAIM_NO, 
    POLICY_NO, ENDORSE_SEQ_NO, DAMAGE_DATE, DEPT_ID, CURRENCY_CODE, PAID_AMOUNT, DRAW_TIME
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="id != null ">
        and ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="entityId != null ">
        and entity_id = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="lossNo != null and lossNo != ''">
        and LOSS_NO = #{lossNo,jdbcType=VARCHAR}
      </if>
      <if test="lossSeqNo != null ">
        and LOSS_SEQ_NO = #{lossSeqNo,jdbcType=DECIMAL}
      </if>
      <if test="checkDate != null ">
        and CHECK_DATE = #{checkDate,jdbcType=TIMESTAMP}
      </if>
      <if test="dealDate != null ">
        and DEAL_DATE = #{dealDate,jdbcType=TIMESTAMP}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
        and YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="portfolioNo != null and portfolioNo != ''">
        and PORTFOLIO_NO = #{portfolioNo,jdbcType=VARCHAR}
      </if>
      <if test="icgNo != null and icgNo != ''">
        and ICG_NO = #{icgNo,jdbcType=VARCHAR}
      </if>
      <if test="evaluateApproach != null and evaluateApproach != ''">
        and EVALUATE_APPROACH = #{evaluateApproach,jdbcType=VARCHAR}
      </if>
      <if test="loaCode != null and loaCode != ''">
        and LOA_CODE = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="productCode != null and productCode != ''">
        and PRODUCT_CODE = #{productCode,jdbcType=VARCHAR}
      </if>
      <if test="riskCode != null and riskCode != ''">
        and RISK_CODE = #{riskCode,jdbcType=VARCHAR}
      </if>
      <if test="cmunitNo != null and cmunitNo != ''">
        and CMUNIT_NO = #{cmunitNo,jdbcType=VARCHAR}
      </if>
      <if test="claimNo != null and claimNo != ''">
        and CLAIM_NO = #{claimNo,jdbcType=VARCHAR}
      </if>
      <if test="policyNo != null and policyNo != ''">
        and POLICY_NO = #{policyNo,jdbcType=VARCHAR}
      </if>
      <if test="endorseSeqNo != null and endorseSeqNo != ''">
        and ENDORSE_SEQ_NO = #{endorseSeqNo,jdbcType=VARCHAR}
      </if>
      <if test="damageDate != null ">
        and DAMAGE_DATE = #{damageDate,jdbcType=TIMESTAMP}
      </if>
      <if test="deptId != null ">
        and DEPT_ID = #{deptId,jdbcType=DECIMAL}
      </if>
      <if test="currencyCode != null and currencyCode != ''">
        and CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="paidAmount != null ">
        and PAID_AMOUNT = #{paidAmount,jdbcType=DECIMAL}
      </if>
      <if test="drawTime != null ">
        and DRAW_TIME = #{drawTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.id != null ">
        and ID = #{condition.id,jdbcType=DECIMAL}
      </if>
      <if test="condition.entityId != null ">
        and entity_id = #{condition.entityId,jdbcType=DECIMAL}
      </if>
      <if test="condition.lossNo != null and condition.lossNo != ''">
        and LOSS_NO = #{condition.lossNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.lossSeqNo != null ">
        and LOSS_SEQ_NO = #{condition.lossSeqNo,jdbcType=DECIMAL}
      </if>
      <if test="condition.checkDate != null ">
        and CHECK_DATE = #{condition.checkDate,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.dealDate != null ">
        and DEAL_DATE = #{condition.dealDate,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.yearMonth != null and condition.yearMonth != ''">
        and YEAR_MONTH = #{condition.yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="condition.portfolioNo != null and condition.portfolioNo != ''">
        and PORTFOLIO_NO = #{condition.portfolioNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.icgNo != null and condition.icgNo != ''">
        and ICG_NO = #{condition.icgNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.evaluateApproach != null and condition.evaluateApproach != ''">
        and EVALUATE_APPROACH = #{condition.evaluateApproach,jdbcType=VARCHAR}
      </if>
      <if test="condition.loaCode != null and condition.loaCode != ''">
        and LOA_CODE = #{condition.loaCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.productCode != null and condition.productCode != ''">
        and PRODUCT_CODE = #{condition.productCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.riskCode != null and condition.riskCode != ''">
        and RISK_CODE = #{condition.riskCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.cmunitNo != null and condition.cmunitNo != ''">
        and CMUNIT_NO = #{condition.cmunitNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.claimNo != null and condition.claimNo != ''">
        and CLAIM_NO = #{condition.claimNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.policyNo != null and condition.policyNo != ''">
        and POLICY_NO = #{condition.policyNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.endorseSeqNo != null and condition.endorseSeqNo != ''">
        and ENDORSE_SEQ_NO = #{condition.endorseSeqNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.damageDate != null ">
        and DAMAGE_DATE = #{condition.damageDate,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.deptId != null ">
        and DEPT_ID = #{condition.deptId,jdbcType=DECIMAL}
      </if>
      <if test="condition.currencyCode != null and condition.currencyCode != ''">
        and CURRENCY_CODE = #{condition.currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.paidAmount != null ">
        and PAID_AMOUNT = #{condition.paidAmount,jdbcType=DECIMAL}
      </if>
      <if test="condition.drawTime != null ">
        and DRAW_TIME = #{condition.drawTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="id != null ">
        and ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="entityId != null ">
        and entity_id = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="lossNo != null and lossNo != ''">
        and LOSS_NO = #{lossNo,jdbcType=VARCHAR}
      </if>
      <if test="lossSeqNo != null ">
        and LOSS_SEQ_NO = #{lossSeqNo,jdbcType=DECIMAL}
      </if>
      <if test="checkDate != null ">
        and CHECK_DATE = #{checkDate,jdbcType=TIMESTAMP}
      </if>
      <if test="dealDate != null ">
        and DEAL_DATE = #{dealDate,jdbcType=TIMESTAMP}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
        and YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="portfolioNo != null and portfolioNo != ''">
        and PORTFOLIO_NO = #{portfolioNo,jdbcType=VARCHAR}
      </if>
      <if test="icgNo != null and icgNo != ''">
        and ICG_NO = #{icgNo,jdbcType=VARCHAR}
      </if>
      <if test="evaluateApproach != null and evaluateApproach != ''">
        and EVALUATE_APPROACH = #{evaluateApproach,jdbcType=VARCHAR}
      </if>
      <if test="loaCode != null and loaCode != ''">
        and LOA_CODE = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="productCode != null and productCode != ''">
        and PRODUCT_CODE = #{productCode,jdbcType=VARCHAR}
      </if>
      <if test="riskCode != null and riskCode != ''">
        and RISK_CODE = #{riskCode,jdbcType=VARCHAR}
      </if>
      <if test="cmunitNo != null and cmunitNo != ''">
        and CMUNIT_NO = #{cmunitNo,jdbcType=VARCHAR}
      </if>
      <if test="claimNo != null and claimNo != ''">
        and CLAIM_NO = #{claimNo,jdbcType=VARCHAR}
      </if>
      <if test="policyNo != null and policyNo != ''">
        and POLICY_NO = #{policyNo,jdbcType=VARCHAR}
      </if>
      <if test="endorseSeqNo != null and endorseSeqNo != ''">
        and ENDORSE_SEQ_NO = #{endorseSeqNo,jdbcType=VARCHAR}
      </if>
      <if test="damageDate != null ">
        and DAMAGE_DATE = #{damageDate,jdbcType=TIMESTAMP}
      </if>
      <if test="deptId != null ">
        and DEPT_ID = #{deptId,jdbcType=DECIMAL}
      </if>
      <if test="currencyCode != null and currencyCode != ''">
        and CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="paidAmount != null ">
        and PAID_AMOUNT = #{paidAmount,jdbcType=DECIMAL}
      </if>
      <if test="drawTime != null ">
        and DRAW_TIME = #{drawTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List" />
    from ATR_DAP_DD_CLAIM_PAID
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select
    <include refid="Base_Column_List" />
    from ATR_DAP_DD_CLAIM_PAID
    where ID in
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ATR_DAP_DD_CLAIM_PAID
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrdap.po.AtrDapDDIcgClaimPaid">
    select
    <include refid="Base_Column_List" />
    from ATR_DAP_DD_CLAIM_PAID
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select
    <include refid="Base_Column_List" />
    from ATR_DAP_DD_CLAIM_PAID
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="ID" keyProperty="id" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrdap.po.AtrDapDDIcgClaimPaid">
    insert into ATR_DAP_DD_CLAIM_PAID
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="entityId != null">
        entity_id,
      </if>
      <if test="lossNo != null">
        LOSS_NO,
      </if>
      <if test="lossSeqNo != null">
        LOSS_SEQ_NO,
      </if>
      <if test="checkDate != null">
        CHECK_DATE,
      </if>
      <if test="dealDate != null">
        DEAL_DATE,
      </if>
      <if test="yearMonth != null">
        YEAR_MONTH,
      </if>
      <if test="portfolioNo != null">
        PORTFOLIO_NO,
      </if>
      <if test="icgNo != null">
        ICG_NO,
      </if>
      <if test="evaluateApproach != null">
        EVALUATE_APPROACH,
      </if>
      <if test="loaCode != null">
        LOA_CODE,
      </if>
      <if test="productCode != null">
        PRODUCT_CODE,
      </if>
      <if test="riskCode != null">
        RISK_CODE,
      </if>
      <if test="cmunitNo != null">
        CMUNIT_NO,
      </if>
      <if test="claimNo != null">
        CLAIM_NO,
      </if>
      <if test="policyNo != null">
        POLICY_NO,
      </if>
      <if test="endorseSeqNo != null">
        ENDORSE_SEQ_NO,
      </if>
      <if test="damageDate != null">
        DAMAGE_DATE,
      </if>
      <if test="deptId != null">
        DEPT_ID,
      </if>
      <if test="currencyCode != null">
        CURRENCY_CODE,
      </if>
      <if test="paidAmount != null">
        PAID_AMOUNT,
      </if>
      <if test="drawTime != null">
        DRAW_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=DECIMAL},
      </if>
      <if test="lossNo != null">
        #{lossNo,jdbcType=VARCHAR},
      </if>
      <if test="lossSeqNo != null">
        #{lossSeqNo,jdbcType=DECIMAL},
      </if>
      <if test="checkDate != null">
        #{checkDate,jdbcType=TIMESTAMP},
      </if>
      <if test="dealDate != null">
        #{dealDate,jdbcType=TIMESTAMP},
      </if>
      <if test="yearMonth != null">
        #{yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="portfolioNo != null">
        #{portfolioNo,jdbcType=VARCHAR},
      </if>
      <if test="icgNo != null">
        #{icgNo,jdbcType=VARCHAR},
      </if>
      <if test="evaluateApproach != null">
        #{evaluateApproach,jdbcType=VARCHAR},
      </if>
      <if test="loaCode != null">
        #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null">
        #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="riskCode != null">
        #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="cmunitNo != null">
        #{cmunitNo,jdbcType=VARCHAR},
      </if>
      <if test="claimNo != null">
        #{claimNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="endorseSeqNo != null">
        #{endorseSeqNo,jdbcType=VARCHAR},
      </if>
      <if test="damageDate != null">
        #{damageDate,jdbcType=TIMESTAMP},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=DECIMAL},
      </if>
      <if test="currencyCode != null">
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="paidAmount != null">
        #{paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="drawTime != null">
        #{drawTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all
    <foreach collection="list" item="item" index="index">
      into ATR_DAP_DD_CLAIM_PAID values
      (#{item.id,jdbcType=DECIMAL}, #{item.entityId,jdbcType=DECIMAL},
      #{item.lossNo,jdbcType=VARCHAR}, #{item.lossSeqNo,jdbcType=DECIMAL}, #{item.checkDate,jdbcType=TIMESTAMP},
      #{item.dealDate,jdbcType=TIMESTAMP}, #{item.yearMonth,jdbcType=VARCHAR}, #{item.portfolioNo,jdbcType=VARCHAR},
      #{item.icgNo,jdbcType=VARCHAR}, #{item.evaluateApproach,jdbcType=VARCHAR}, #{item.loaCode,jdbcType=VARCHAR},
      #{item.productCode,jdbcType=VARCHAR}, #{item.riskCode,jdbcType=VARCHAR}, #{item.cmunitNo,jdbcType=VARCHAR},
      #{item.claimNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, #{item.endorseSeqNo,jdbcType=VARCHAR},
      #{item.damageDate,jdbcType=TIMESTAMP}, #{item.deptId,jdbcType=DECIMAL}, #{item.currencyCode,jdbcType=VARCHAR},
      #{item.paidAmount,jdbcType=DECIMAL}, #{item.drawTime,jdbcType=TIMESTAMP})
    </foreach>
    select 1 
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrdap.po.AtrDapDDIcgClaimPaid">
    update ATR_DAP_DD_CLAIM_PAID
    <set>
      <if test="entityId != null">
        entity_id = #{entityId,jdbcType=DECIMAL},
      </if>
      <if test="lossNo != null">
        LOSS_NO = #{lossNo,jdbcType=VARCHAR},
      </if>
      <if test="lossSeqNo != null">
        LOSS_SEQ_NO = #{lossSeqNo,jdbcType=DECIMAL},
      </if>
      <if test="checkDate != null">
        CHECK_DATE = #{checkDate,jdbcType=TIMESTAMP},
      </if>
      <if test="dealDate != null">
        DEAL_DATE = #{dealDate,jdbcType=TIMESTAMP},
      </if>
      <if test="yearMonth != null">
        YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="portfolioNo != null">
        PORTFOLIO_NO = #{portfolioNo,jdbcType=VARCHAR},
      </if>
      <if test="icgNo != null">
        ICG_NO = #{icgNo,jdbcType=VARCHAR},
      </if>
      <if test="evaluateApproach != null">
        EVALUATE_APPROACH = #{evaluateApproach,jdbcType=VARCHAR},
      </if>
      <if test="loaCode != null">
        LOA_CODE = #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null">
        PRODUCT_CODE = #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="riskCode != null">
        RISK_CODE = #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="cmunitNo != null">
        CMUNIT_NO = #{cmunitNo,jdbcType=VARCHAR},
      </if>
      <if test="claimNo != null">
        CLAIM_NO = #{claimNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        POLICY_NO = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="endorseSeqNo != null">
        ENDORSE_SEQ_NO = #{endorseSeqNo,jdbcType=VARCHAR},
      </if>
      <if test="damageDate != null">
        DAMAGE_DATE = #{damageDate,jdbcType=TIMESTAMP},
      </if>
      <if test="deptId != null">
        DEPT_ID = #{deptId,jdbcType=DECIMAL},
      </if>
      <if test="currencyCode != null">
        CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="paidAmount != null">
        PAID_AMOUNT = #{paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="drawTime != null">
        DRAW_TIME = #{drawTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrdap.po.AtrDapDDIcgClaimPaid">
    update ATR_DAP_DD_CLAIM_PAID
    <set>
      <if test="record.entityId != null">
        entity_id = #{record.entityId,jdbcType=DECIMAL},
      </if>
      <if test="record.lossNo != null">
        LOSS_NO = #{record.lossNo,jdbcType=VARCHAR},
      </if>
      <if test="record.lossSeqNo != null">
        LOSS_SEQ_NO = #{record.lossSeqNo,jdbcType=DECIMAL},
      </if>
      <if test="record.checkDate != null">
        CHECK_DATE = #{record.checkDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dealDate != null">
        DEAL_DATE = #{record.dealDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.yearMonth != null">
        YEAR_MONTH = #{record.yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="record.portfolioNo != null">
        PORTFOLIO_NO = #{record.portfolioNo,jdbcType=VARCHAR},
      </if>
      <if test="record.icgNo != null">
        ICG_NO = #{record.icgNo,jdbcType=VARCHAR},
      </if>
      <if test="record.evaluateApproach != null">
        EVALUATE_APPROACH = #{record.evaluateApproach,jdbcType=VARCHAR},
      </if>
      <if test="record.loaCode != null">
        LOA_CODE = #{record.loaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.productCode != null">
        PRODUCT_CODE = #{record.productCode,jdbcType=VARCHAR},
      </if>
      <if test="record.riskCode != null">
        RISK_CODE = #{record.riskCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cmunitNo != null">
        CMUNIT_NO = #{record.cmunitNo,jdbcType=VARCHAR},
      </if>
      <if test="record.claimNo != null">
        CLAIM_NO = #{record.claimNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        POLICY_NO = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.endorseSeqNo != null">
        ENDORSE_SEQ_NO = #{record.endorseSeqNo,jdbcType=VARCHAR},
      </if>
      <if test="record.damageDate != null">
        DAMAGE_DATE = #{record.damageDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deptId != null">
        DEPT_ID = #{record.deptId,jdbcType=DECIMAL},
      </if>
      <if test="record.currencyCode != null">
        CURRENCY_CODE = #{record.currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.paidAmount != null">
        PAID_AMOUNT = #{record.paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.drawTime != null">
        DRAW_TIME = #{record.drawTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from ATR_DAP_DD_CLAIM_PAID
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from ATR_DAP_DD_CLAIM_PAID
    where ID in
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from ATR_DAP_DD_CLAIM_PAID
    where
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrdap.po.AtrDapDDIcgClaimPaid">
    select count(1) from ATR_DAP_DD_CLAIM_PAID
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>