package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to;

import com.ss.ifrs.actuarial.util.abp.IgnoreCol;
import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 合约分出底单临时表对象
 */
@Tab("atr_buss_to_lrc_t_ul_r")
@Data
public class AtrBussToLrcTUlR {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 动作编号
     */
    private String actionNo;
    
    // 实际业务发生年月
    /**
     * 业务单位ID
     * 对应数据库字段: entity_id (BIGINT)
     */
    private Long entityId;


    private String yearMonth;
    /**
     * 业务发生年月
     * 对应数据库字段: year_month (VARCHAR(6))
     */
    private String bussYearMonth;

    /**
     * 险类代码
     * 对应数据库字段: risk_class_code (VARCHAR(20))
     */
    private String riskClassCode;

    /**
     * 合约号
     * 对应数据库字段: treaty_no (VARCHAR(60))
     */
    private String treatyNo;

    /**
     * 分保单号
     * 对应数据库字段: ri_policy_no (VARCHAR(60))
     */
    private String riPolicyNo;

    /**
     * 分批单序号
     * 对应数据库字段: ri_endorse_seq_no (VARCHAR(10))
     */
    private String riEndorseSeqNo;

    /**
     * 保单号
     * 对应数据库字段: policy_no (VARCHAR(60))
     */
    private String policyNo;

    /**
     * 批单序号
     * 对应数据库字段: endorse_seq_no (VARCHAR(10))
     */
    private String endorseSeqNo;

    /**
     * 险种代码
     * 对应数据库字段: risk_code (VARCHAR(10))
     */
    private String riskCode;

    /**
     * 险别代码
     * 对应数据库字段: kind_code (VARCHAR(30))
     */
    private String kindCode;

    /*
    * 合同组合编码，合约层级
    * */
    private String portfolioNo;

    /**
     * 标的合同组号码
     * 对应数据库字段: policy_icg_no (VARCHAR(60))
     */
    private String policyIcgNo;


    private String icgName;

    /**
     * 合约合同组号码
     * 对应数据库字段: icg_no (VARCHAR(60))
     */
    private String icgNo;

    /**
     * 计量单元编号
     * 对应数据库字段: cmunit_no (VARCHAR(60))
     */
    private String cmunitNo;

    /**
     * 一级机构
     * 对应数据库字段: company_code1 (VARCHAR(30))
     */
    private String companyCode1;

    /**
     * 二级机构
     * 对应数据库字段: company_code2 (VARCHAR(30))
     */
    private String companyCode2;

    /**
     * 三级机构
     * 对应数据库字段: company_code3 (VARCHAR(30))
     */
    private String companyCode3;

    /**
     * 四级机构
     * 对应数据库字段: company_code4 (VARCHAR(30))
     */
    private String companyCode4;

    /**
     * 标的保单责任起始日期
     * 对应数据库字段: effective_date (DATE)
     */
    private Date effectiveDate;

    /**
     * 标的保单责任终止日期
     * 对应数据库字段: expiry_date (DATE)
     */
    private Date expiryDate;

    /**
     * 再保合约责任起始日期
     * 对应数据库字段: ri_effective_date (DATE)
     */
    private Date riEffectiveDate;

    /**
     * 再保合约责任终止日期
     * 对应数据库字段: ri_expiry_date (DATE)
     */
    private Date riExpiryDate;

    /**
     * 分保的核保通过日期
     * 对应数据库字段: approval_date (DATE)
     */
    private Date approvalDate;

    /*
    * 底单合同确认日期
    * */
    private Date policyContractDate;

    /**
     * 合同确认日期
     * 对应数据库字段: contract_date (DATE)
     */
    private Date contractDate;

    /**
     * 合同签发日
     * 对应数据库字段: issue_date (DATE)
     */
    private Date issueDate;

    /**
     * 保费
     * 对应数据库字段: premium (DECIMAL(21, 8))
     */
    private BigDecimal premium;

    /**
     * 净额结算手续费
     * 对应数据库字段: net_fee (DECIMAL(21, 8))
     */
    private BigDecimal netFee;

    /**
     * 固定手续费率
     * 对应数据库字段: fixed_fee_rate (DECIMAL(19, 6))
     */
    private BigDecimal fixedFeeRate;

    /**
     * 预付手续费率
     * 对应数据库字段: prepaid_fee_rate (DECIMAL(19, 6))
     */
    private BigDecimal prepaidFeeRate;


    /** 浮动手续费率上限 */
    private BigDecimal floatingHandlingFeeCap;

    /**
     * 标的保单占比
     */
    private BigDecimal riCedingRate;

    /** 财务渠道 (fin_acc_channel) */
    private String finAccChannel;
    
    /** 财务产品代码 (fin_product_code) */
    private String finProductCode;
    
    /** 财务明细代码 (fin_detail_code) */
    private String finDetailCode;
    
    /** 财务子产品代码 (fin_sub_product_code) */
    private String finSubProductCode;
    
    /** 盈亏判定结果 (pl_judge_rslt) */
    private String plJudgeRslt;

    /**
     * 合约名称
     */
    private String treatyName;
    
    /**
     * 标的保单签发日期
     */
    private Date policyIssueDate;
    
    /**
     * 标的单承保认可日期
     */
    private Date policyConfirmDate;
    
    /**
     * 标的保单核保日期
     */
    private Date policyApprovalDate;
    
    /**
     * 标的保单保费
     */
    private BigDecimal policyPremium;
    
    /**
     * 标的保单净额结算手续费
     */
    private BigDecimal policyNetfee;
    
    /**
     * 部门段
     * 对应数据库字段: dept_id (VARCHAR(60))
     * 对应acepayment的 article5
     */
    private String deptId;
    
    /**
     * 渠道段
     * 对应数据库字段: channel_id (VARCHAR(60))
     * 对应acepayment的 article7
     */
    private String channelId;
    
    /**
     * 核算机构
     * 对应数据库字段: center_code (VARCHAR(60))
     * 对应acepayment的 centercode
     */
    private String centerCode;

    /* 分项信息 */
    private String sectionoCode;

    private String reinsurerCode;

    private BigDecimal curPaidPremium;

    private BigDecimal curNetFee;
    
    /**
     * 手续费率
     */
    private BigDecimal feeRate;
    
    /**
     * 投资成分
     */
    private BigDecimal invAmount;
    
    /**
     * 当期已赚保费
     * 对应数据库字段: cur_ed_premium (DECIMAL(21, 4))
     */
    private BigDecimal curEdPremium;

    /**
     * 当期已赚手续费
     * 对应数据库字段: cur_ed_net_fee (DECIMAL(21, 4))
     */
    private BigDecimal curEdNetFee;

    /** 上期累计已赚保费 (pre_cuml_ed_premium) */
    private BigDecimal preCumlEdPremium;

    /** 上期累计已赚净额结算手续费 (pre_cuml_ed_net_fee) */
    private BigDecimal preCumlEdNetFee;

    /** 批改类型代码 (endorse_type_code) */
    private String endorseTypeCode;

    /** 特殊处理类型 (special_process_type) - 0:正常处理, 1:只计算第0期, 2:不计算发展期 */
    @IgnoreCol
    private Integer specialProcessType;

    /** 分区号 (pn) - 用于分批处理 */
    @IgnoreCol
    private Integer pn;

}
