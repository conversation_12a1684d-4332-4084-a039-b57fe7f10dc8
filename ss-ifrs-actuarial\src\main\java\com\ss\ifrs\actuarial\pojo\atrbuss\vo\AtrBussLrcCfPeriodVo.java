/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2023-01-11 16:41:00
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2023-01-11 16:41:00<br/>
 * Description: LRC输出现金流发展期主表<br/>
 * Table Name: ATR_BUSS_LRC_CF_PERIOD<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "LRC输出现金流发展期主表")
public class AtrBussLrcCfPeriodVo implements Serializable {
    /**
     * Database column: ATR_BUSS_LRC_CF_PERIOD.LRC_CF_PERIOD_ID
     * Database remarks: lrc_cf_period_id|主键
     */
    @ApiModelProperty(value = "lrc_cf_period_id|主键", required = true)
    private Long lrcCfPeriodId;

    /**
     * Database column: ATR_BUSS_LRC_CF_PERIOD.LRC_CF_MAIN_ID
     * Database remarks: lrc_cf_main_id|主键
     */
    @ApiModelProperty(value = "lrc_cf_main_id|主键", required = true)
    private Long lrcCfMainId;

    /**
     * Database column: ATR_BUSS_LRC_CF_PERIOD.POLICY_NO_ENDORSEMENT
     * Database remarks: policy_no_endorsement|保单号批单序号
     */
    @ApiModelProperty(value = "policy_no_endorsement|保单号批单序号", required = false)
    private String policyNoEndorsement;

    /**
     * Database column: ATR_BUSS_LRC_CF_PERIOD.POLICY_NO
     * Database remarks: policy_no|保单号
     */
    @ApiModelProperty(value = "policy_no|保单号", required = false)
    private String policyNo;

    /**
     * Database column: ATR_BUSS_LRC_CF_PERIOD.PORTFOLIO_NO
     * Database remarks: portfolio_no|合同组合
     */
    @ApiModelProperty(value = "portfolio_no|合同组合", required = false)
    private String portfolioNo;

    /**
     * Database column: ATR_BUSS_LRC_CF_PERIOD.ICG_NO
     * Database remarks: icg_no|合同组
     */
    @ApiModelProperty(value = "icg_no|合同组", required = false)
    private String icgNo;

    /**
     * Database column: ATR_BUSS_LRC_CF_PERIOD.ACCUMULATED_EARNED_RATE
     * Database remarks: rpt_per_remain_un_rate|截至报告期初剩余未摊销比例
     */
    @ApiModelProperty(value = "rpt_per_remain_un_rate|截至报告期初剩余未摊销比例", required = false)
    private BigDecimal accumulatedEarnedRate;

    /**
     * Database column: ATR_BUSS_LRC_CF_PERIOD.CUR_END_REMAIN_UN_RATE
     * Database remarks: null
     */
    private BigDecimal curEndRemainUnRate;

    /**
     * Database column: ATR_BUSS_LRC_CF_PERIOD.RPT_PER_REMAIN_UN_RATE
     * Database remarks: lp_cur_end_remain_un_rate|(上期)当期期末剩余未摊销比例
     */
    @ApiModelProperty(value = "lp_cur_end_remain_un_rate|(上期)当期期末剩余未摊销比例", required = false)
    private BigDecimal rptPerRemainUnRate;

    /**
     * Database column: ATR_BUSS_LRC_CF_PERIOD.LP_CUR_END_REMAIN_UN_RATE
     * Database remarks: lp_cur_end_remain_un_rate|(上期)截至报告期初剩余未摊销比例
     */
    @ApiModelProperty(value = "lp_cur_end_remain_un_rate|(上期)截至报告期初剩余未摊销比例", required = false)
    private BigDecimal lpCurEndRemainUnRate;

    /**
     * Database column: ATR_BUSS_LRC_CF_PERIOD.LP_RPT_PER_REMAIN_UN_RATE
     * Database remarks: null
     */
    private BigDecimal lpRptPerRemainUnRate;

    /**
     * Database column: ATR_BUSS_LRC_CF_PERIOD.LRC_CF_FEE_TYPE
     * Database remarks: lrc_cf_fee_type|lrc现金流费用类型
     */
    @ApiModelProperty(value = "lrc_cf_fee_type|lrc现金流费用类型", required = false)
    private String lrcCfFeeType;

    private static final long serialVersionUID = 1L;

    public Long getLrcCfPeriodId() {
        return lrcCfPeriodId;
    }

    public void setLrcCfPeriodId(Long lrcCfPeriodId) {
        this.lrcCfPeriodId = lrcCfPeriodId;
    }

    public Long getLrcCfMainId() {
        return lrcCfMainId;
    }

    public void setLrcCfMainId(Long lrcCfMainId) {
        this.lrcCfMainId = lrcCfMainId;
    }

    public String getPolicyNoEndorsement() {
        return policyNoEndorsement;
    }

    public void setPolicyNoEndorsement(String policyNoEndorsement) {
        this.policyNoEndorsement = policyNoEndorsement;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public BigDecimal getAccumulatedEarnedRate() {
        return accumulatedEarnedRate;
    }

    public void setAccumulatedEarnedRate(BigDecimal accumulatedEarnedRate) {
        this.accumulatedEarnedRate = accumulatedEarnedRate;
    }

    public BigDecimal getCurEndRemainUnRate() {
        return curEndRemainUnRate;
    }

    public void setCurEndRemainUnRate(BigDecimal curEndRemainUnRate) {
        this.curEndRemainUnRate = curEndRemainUnRate;
    }

    public BigDecimal getRptPerRemainUnRate() {
        return rptPerRemainUnRate;
    }

    public void setRptPerRemainUnRate(BigDecimal rptPerRemainUnRate) {
        this.rptPerRemainUnRate = rptPerRemainUnRate;
    }

    public BigDecimal getLpCurEndRemainUnRate() {
        return lpCurEndRemainUnRate;
    }

    public void setLpCurEndRemainUnRate(BigDecimal lpCurEndRemainUnRate) {
        this.lpCurEndRemainUnRate = lpCurEndRemainUnRate;
    }

    public BigDecimal getLpRptPerRemainUnRate() {
        return lpRptPerRemainUnRate;
    }

    public void setLpRptPerRemainUnRate(BigDecimal lpRptPerRemainUnRate) {
        this.lpRptPerRemainUnRate = lpRptPerRemainUnRate;
    }

    public String getLrcCfFeeType() {
        return lrcCfFeeType;
    }

    public void setLrcCfFeeType(String lrcCfFeeType) {
        this.lrcCfFeeType = lrcCfFeeType;
    }
}