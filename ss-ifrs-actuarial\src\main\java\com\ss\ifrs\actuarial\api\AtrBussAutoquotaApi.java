package com.ss.ifrs.actuarial.api;

import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import com.ss.ifrs.actuarial.dao.AtrBussAutoquotaActionDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfBussPeriodDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfQuotaDefDao;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaActionVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaApproveVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaConfWeightQueryVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaDrawVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaGroupQueryVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaLoaVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaQuotaVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaUnitResultVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaValueResultVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaWeightDetailVo;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDef;
import com.ss.ifrs.actuarial.service.AtrBussAutoquotaService;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.platform.core.annotation.TrackUserBehavioral;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.pojo.bbs.vo.BbsConfLoaVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/autoquota")
@Api(value = "假设值自动计算接口")
public class AtrBussAutoquotaApi extends BaseApi {

    private static final Set<String> GROUP_COLUMNS =
            Sets.newHashSet("business_source_code", "loa_code", "buss_year");

    @Resource
    private AtrBussAutoquotaService atrBussAutoquotaService;
    @Resource
    private AtrConfQuotaDefDao atrConfQuotaDefDao;
    @Resource
    private AtrConfBussPeriodDao atrConfBussPeriodDao;
    @Resource
    private AtrBussAutoquotaActionDao atrBussAutoquotaActionDao;


    @ApiOperation(value = "执行")
    @TrackUserBehavioral(description = "do action")
    @RequestMapping(value = "/doAction", method = RequestMethod.POST)
    public BaseResponse<?> doAction(@RequestBody AtrBussAutoquotaDrawVo vo,
                                    HttpServletRequest request) {
        Long userId = loginUserId(request);
        String actionNo = atrBussAutoquotaService.saveAction(vo, userId);
        return atrBussAutoquotaService.doAction(actionNo);
    }

    @TrackUserBehavioral(description = "delete action")
    @RequestMapping(value = "/deleteAction", method = RequestMethod.POST)
    public BaseResponse<Map<String, Object>> deleteAction(@RequestParam("id") long id) {
        atrBussAutoquotaService.deleteAction(id);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, Collections.emptyMap());
    }

    @ApiOperation(value = "审核")
    @TrackUserBehavioral(description = "approve action")
    @RequestMapping(value = "/approveAction", method = RequestMethod.POST)
    public BaseResponse<Map<String, Object>> approveAction(@RequestBody AtrBussAutoquotaApproveVo vo,
                                                           HttpServletRequest request) {
        Long userId = loginUserId(request);
        atrBussAutoquotaService.approveAction(vo, userId);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, Collections.emptyMap());
    }

    @ApiOperation(value = "根据业务条件查询公共权重配置")
    @TrackUserBehavioral(description = "query weight")
    @RequestMapping(value = "/queryConfWeightByBuss", method = RequestMethod.POST)
    public BaseResponse<Map<String, Object>> queryConfWeightByBuss(@RequestBody AtrBussAutoquotaConfWeightQueryVo vo) {
        Map<String, Object> result = new HashMap<>();
        if ("FO".equals(vo.getBusinessSourceCode())) {
            vo.setBusinessSourceCode("DD");
        }

        BbsConfLoaVo loaVo = new BbsConfLoaVo();
        loaVo.setLoaCode(vo.getLoaCode());
        if (StringUtils.isNotBlank(vo.getBusinessSourceCode())) {
            loaVo.setBusinessModel(vo.getBusinessSourceCode().substring(0, 1));
            loaVo.setBusinessDirection(vo.getBusinessSourceCode().substring(1, 2));
        }

        List<AtrBussAutoquotaWeightDetailVo> detailVos = atrBussAutoquotaActionDao.queryConfWeightByBuss(loaVo);
        for (AtrBussAutoquotaWeightDetailVo detailVo : detailVos) {
            String valuesAgg = detailVo.getWeightValuesAgg();
            List<BigDecimal> weightValues = new ArrayList<>();
            detailVo.setWeightValues(weightValues);
            if (StringUtils.isNotBlank(valuesAgg)) {
                for (String valueStr : valuesAgg.split(",", -1)) {
                    if (StringUtils.isNotBlank(valueStr)) {
                        weightValues.add(new BigDecimal(valueStr));
                    } else {
                        weightValues.add(null);
                    }
                }
            }
        }

        result.put("result", detailVos);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "查询公共权重配置")
    @TrackUserBehavioral(description = "query conf weight")
    @RequestMapping(value = "/queryConfWeight", method = RequestMethod.POST)
    public BaseResponse<Map<String, Object>> queryConfWeight() {
        List<AtrBussAutoquotaWeightDetailVo> queryResults = atrBussAutoquotaActionDao.queryConfWeight();
        List<AtrBussAutoquotaWeightDetailVo> vos = new ArrayList<>();
        for (AtrBussAutoquotaWeightDetailVo queryResult : queryResults) {
            String loaCode = queryResult.getLoaCode();
            String weightValuesAgg = queryResult.getWeightValuesAgg();

            List<BigDecimal> weightValues = new ArrayList<>();

            if (StringUtils.isNotBlank(weightValuesAgg)) {
                String[] weightValueArray = weightValuesAgg.split(",", -1);
                for (String weightValueStr : weightValueArray) {
                    if (StringUtils.isBlank(weightValueStr)) {
                        weightValues.add(null);
                    } else {
                        weightValues.add(new BigDecimal(weightValueStr));
                    }
                }
            }

            AtrBussAutoquotaWeightDetailVo vo = new AtrBussAutoquotaWeightDetailVo();
            vo.setLoaCode(loaCode);
            vo.setWeightValues(weightValues);
            vos.add(vo);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("result", vos);

        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "保存公共权重配置")
    @TrackUserBehavioral(description = "save conf weight")
    @RequestMapping(value = "/saveConfWeight", method = RequestMethod.POST)
    public BaseResponse<?> saveConfWeight(@RequestBody List<AtrBussAutoquotaWeightDetailVo> vos,
                                          HttpServletRequest request) {
        Long userId = loginUserId(request);
        if (userId == null) {
            userId = 1L;
        }
        atrBussAutoquotaService.saveConfWeight(vos, userId);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "OK");
    }


    @ApiOperation(value = "查询假设值值代码")
    @TrackUserBehavioral(description = "query quota codes")
    @RequestMapping(value = "/queryQuotaCodes", method = RequestMethod.POST)
    public BaseResponse<?> queryQuotaCodes() {
        AtrConfQuotaDef po = new AtrConfQuotaDef();
        po.setAutoCalculatedIs("1");
        po.setValidIs("1");
        po.setAuditState("1");
        List<AtrConfQuotaDef> defs = atrConfQuotaDefDao.findList(po);

        List<AtrBussAutoquotaQuotaVo> vos = new ArrayList<>();
        for (AtrConfQuotaDef def : defs) {
            AtrBussAutoquotaQuotaVo vo = new AtrBussAutoquotaQuotaVo();
            BeanUtils.copyProperties(def, vo);
            vos.add(vo);
        }

        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, vos);
    }

    @ApiOperation(value = "查询可用的评估期")
    @TrackUserBehavioral(description = "query available ev year month")
    @RequestMapping(value = "/queryAvailableEvYearMonths", method = RequestMethod.POST)
    public BaseResponse<?> queryAvailableEvYearMonths() {
        Map<String, Object> result = new HashMap<>();
        result.put("evYearMonths", atrConfBussPeriodDao.findIncomplete());
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "基本信息列表查询")
    @RequestMapping(value = "/queryPage", method = RequestMethod.POST)
    public BaseResponse<?> queryPage(@RequestBody AtrBussAutoquotaActionVo vo, HttpServletRequest request,
                                     int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        vo.setLanguage(getLanguage(request));
        Page<AtrBussAutoquotaActionVo> posPage = atrBussAutoquotaService.queryPage(vo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("result", posPage);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "查询详情（单数据）")
    @RequestMapping(value = "/queryUnitPage", method = RequestMethod.POST)
    public BaseResponse<?> queryUnitPage(@RequestBody AtrBussAutoquotaGroupQueryVo vo, HttpServletRequest request,
                                         int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Assert.hasText(vo.getActionNo(), "action no. can't be empty");
        vo.setGroupColumns(extractGroupColumns(vo.getGroupColumns()));

        String newGroupColumns = Splitter.on(",")
                .trimResults()
                .omitEmptyStrings()
                .splitToList(vo.getGroupColumns())
                .stream()
                .map(t -> (t.equals("entity_id") ? "a." : "t.") + t)
                .collect(Collectors.joining(","));
        vo.setGroupColumns(newGroupColumns);

        vo.setLanguage(getLanguage(request));

        Page<AtrBussAutoquotaUnitResultVo> page = atrBussAutoquotaActionDao.queryUnitPage(vo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("result", page);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
    }


    @ApiOperation(value = "查询详情（假设值）")
    @RequestMapping(value = "/queryQuotaValuePage", method = RequestMethod.POST)
    public BaseResponse<?> queryQuotaValuePage(@RequestBody AtrBussAutoquotaGroupQueryVo vo, HttpServletRequest request,
                                               int _pageSize, int _pageNo) {
        Assert.hasText(vo.getActionNo(), "action no. can't be empty");
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        vo.setLanguage(getLanguage(request));
        Page<AtrBussAutoquotaValueResultVo> page =
                atrBussAutoquotaActionDao.queryQuotaValuePage(vo.getActionNo(), pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("result", page);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "查询 Loa")
    @RequestMapping(value = "/queryLoaPage", method = RequestMethod.POST)
    public BaseResponse<?> queryLoaPage(@RequestBody AtrBussAutoquotaLoaVo vo, HttpServletRequest request,
                                        int _pageSize, int _pageNo) {
        String businessSourceCode = vo.getBusinessSourceCode();
        if ("FO".equals(businessSourceCode)) {
            businessSourceCode = "DD";
        }

        BbsConfLoaVo loaVo = new BbsConfLoaVo();
        loaVo.setLanguage(getLanguage(request));
        loaVo.setLoaCode(vo.getLoaCode());
        if (StringUtils.isNotBlank(businessSourceCode)) {
            loaVo.setBusinessModel(businessSourceCode.substring(0, 1));
            loaVo.setBusinessDirection(businessSourceCode.substring(1, 2));
        }

        Pageable pageParam = new Pageable(_pageNo, _pageSize);

        Page<AtrBussAutoquotaLoaVo> page = atrBussAutoquotaActionDao.queryLoaPage(loaVo, pageParam);

        Map<String, Object> result = new HashMap<>();
        result.put("result", page);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "查询权重（业务录入）")
    @TrackUserBehavioral(description = "query weight")
    @RequestMapping(value = "/queryBussWeight", method = RequestMethod.POST)
    public BaseResponse<Map<String, Object>> queryBussWeight(String actionNo) {
        Map<String, Object> result = new HashMap<>();
        Assert.hasText(actionNo, "action no. can't be empty");
        result.put("result", atrBussAutoquotaService.queryBussWeight(actionNo));
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
    }


    private String extractGroupColumns(String groupColumns) {
        if (groupColumns == null) {
            groupColumns = "entity_id";
        } else if (!groupColumns.contains("entity_id")) {
            groupColumns = groupColumns + ",entity_id";
        }
        return StringUtils.isBlank(groupColumns) ? "" :
                Splitter.on(",")
                        .trimResults()
                        .omitEmptyStrings()
                        .splitToList(groupColumns)
                        .stream()
                        .filter(GROUP_COLUMNS::contains)
                        .collect(Collectors.joining(","));
    }

    private String getLanguage(HttpServletRequest request) {
        return request.getHeader("Ss-Language");
    }
}
