package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.dao.AtrBussDapDao;
import com.ss.ifrs.actuarial.service.AtrConfBussPeriodService;
import com.ss.platform.core.constant.CommonConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class AtrBussDapService {

    @Resource
    private AtrBussDapDao atrBussDapDao;
    @Resource
    private AtrConfBussPeriodService atrConfBussPeriodService;

    @Transactional
    public void checkIfReady(Long entityId, String yearMonth) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("entityId", entityId);
        paramMap.put("yearMonth", yearMonth);

        List<String> errors = new ArrayList<>();

        String periodState = atrBussDapDao.getDmPeriodState(paramMap);
        if (!CommonConstant.BussPeriod.PeriodStatus.COMPLETED.equals(periodState)) {
            errors.add("DM 平台未完成");
        }

        periodState = atrBussDapDao.getExpPeriodState(paramMap);
        if (!CommonConstant.BussPeriod.PeriodStatus.COMPLETED.equals(periodState)) {
            errors.add("EXP 平台未完成");
        }

        periodState = atrBussDapDao.getAtrPeriodState(paramMap);
        if (StringUtils.isBlank(periodState)) {
            errors.add("ATR 平台未设置业务期间");
        } else {
            if (!CommonConstant.BussPeriod.PeriodStatus.PREPARING.equals(periodState)) {
                errors.add("ATR 平台的业务期间状态不是预期的 0-准备中");
            }
        }

        if (!errors.isEmpty()) {
            throw new RuntimeException(String.join("; ", errors));
        }

        atrBussDapDao.completeDirection1(entityId, yearMonth);
        atrConfBussPeriodService.syncPeriodStatus(entityId, CommonConstant.BussPeriod.PeriodStatus.PREPARED);
    }

}
