package com.ss.ifrs.actuarial.dao.buss;


import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrBussBecfViewVo;
import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrBussQuotaVo;
import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrBussQuotaDetailVo;
import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrConfBecfOutPutVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussQuotaValue;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussQuotaValueVo;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AtrBussQuotaDao extends IDao<AtrBussQuotaValue, Long> {

    List<AtrBussQuotaVo> findBussQuotaVoByAtrActionVo(@Param("atrAction") AtrBussQuotaValueVo quotaValueVo, @Param("quotaGroup") String quotaGroup);

    //根据指标编码和评估id查询指标发展期数据
    List<AtrBussQuotaDetailVo> findBussQuotaPeriod(@Param("atrAction") AtrBussQuotaValueVo atrBussQuotaVo);

    List<AtrConfBecfOutPutVo> findBecfOutPut(AtrBussQuotaValueVo atrBussQuotaValueVo);

    List<AtrConfBecfOutPutVo> findBecfListByIds(AtrBussBecfViewVo atrBussBecfViewVo);
}
