# 基于jdk8
FROM openjdk:8

# 设置工作目录
WORKDIR /app

# copy镜像到容器内目录
COPY ss-ifrs-actuarial-1.0.0.RELEASE.jar /app/ss-ifrs-actuarial.jar

# 设置环境变量
ENV SPRING_CONFIG_LOCATION=classpath:/app/config/ss-ifrs-actuarial.yml
ENV LOG_PATH=/app/logs
ENV REGISTER_URLS=127.0.0.1:7602/eureka/
ENV JAVA_OPTS="-Xms3072m -Xmx3072m -Xmn1229m -Xss256k"

# 创建日志目录
RUN mkdir -p /app/logs

# 暴露对应的端口
EXPOSE 7608

# 启动 Spring Boot 应用
CMD ["sh", "-c", "if [ -z \"$REGISTER_URLS\" ]; then echo 'Error: REGISTER_URLS not set'; exit 1; fi; java $JAVA_OPTS -Dspring.config.location=${SPRING_CONFIG_LOCATION} -Dlogging.file.path=${LOG_PATH} -Dregister.urls=${REGISTER_URLS} -jar ss-ifrs-actuarial.jar"]