/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2022-08-19 14:51:44
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2022-08-19 14:51:44<br/>
 * Description: Excel表数据配置表<br/>
 * Table Name: atr_conf_excel_table<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "Excel表数据配置表")
public class AtrConfExcelTableVo implements Serializable {
    /**
     * Database column: atr_conf_excel_table.biz_type_id
     * Database remarks: id|主键
     */
    @ApiModelProperty(value = "id|主键", required = true)
    private Long bizTypeId;

    /**
     * Database column: atr_conf_excel_table.biz_code
     * Database remarks: biz_Code|业务编码标识
     */
    @ApiModelProperty(value = "biz_Code|业务编码标识", required = true)
    private String bizCode;

    /**
     * Database column: atr_conf_excel_table.type_e_name
     * Database remarks: Rule_EName|规则类型名称E
     */
    @ApiModelProperty(value = "Rule_EName|规则类型名称E", required = false)
    private String typeEName;

    /**
     * Database column: atr_conf_excel_table.type_t_name
     * Database remarks: Rule_TName|规则类型名称T
     */
    @ApiModelProperty(value = "Rule_TName|规则类型名称T", required = false)
    private String typeLName;

    /**
     * Database column: atr_conf_excel_table.type_c_name
     * Database remarks: Rule_CName|规则类型名称C
     */
    @ApiModelProperty(value = "Rule_CName|规则类型名称C", required = false)
    private String typeCName;

    /**
     * Database column: atr_conf_excel_table.sys_id
     * Database remarks: Sys_Id|系统id
     */
    @ApiModelProperty(value = "Sys_Id|系统id", required = false)
    private Long sysId;

    /**
     * Database column: atr_conf_excel_table.type_group
     * Database remarks: Type_Group|分组类型
     */
    @ApiModelProperty(value = "Type_Group|分组类型", required = false)
    private String typeGroup;

    /**
     * Database column: atr_conf_excel_table.redirect_to_url
     * Database remarks: Redirect_To_Url|跳转URL
     */
    @ApiModelProperty(value = "Redirect_To_Url|跳转URL", required = false)
    private String redirectToUrl;

    /**
     * Database column: atr_conf_excel_table.valid_is
     * Database remarks: Valind_is|有效标志
     */
    @ApiModelProperty(value = "Valind_is|有效标志", required = false)
    private String validIs;

    /**
     * Database column: atr_conf_excel_table.display_no
     * Database remarks: Display_No|排序
     */
    @ApiModelProperty(value = "Display_No|排序", required = false)
    private BigDecimal displayNo;

    /**
     * Database column: atr_conf_excel_table.create_time
     * Database remarks: Create_Time|创建时间
     */
    @ApiModelProperty(value = "Create_Time|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: atr_conf_excel_table.creator_id
     * Database remarks: Creator_code|创建人
     */
    @ApiModelProperty(value = "Creator_code|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: atr_conf_excel_table.update_time
     * Database remarks: Update_time|最后变更时间
     */
    @ApiModelProperty(value = "Update_time|最后变更时间", required = false)
    private Date updateTime;

    /**
     * Database column: atr_conf_excel_table.updator_id
     * Database remarks: Updator_Code|最后变更经手人
     */
    @ApiModelProperty(value = "Updator_Code|最后变更经手人", required = false)
    private Long updatorId;

    /**
     * Database column: atr_conf_excel_table.original_is
     * Database remarks: Original_Is|是否原字段，0-是，1-否
     */
    @ApiModelProperty(value = "Original_Is|是否原字段，0-是，1-否", required = false)
    private String originalIs;

    /**
     * Database column: atr_conf_excel_table.upload_is
     * Database remarks: Upload_Is|是否能上传，0-否，1是
     */
    @ApiModelProperty(value = "Upload_Is|是否能上传，0-否，1是", required = false)
    private String uploadIs;

    /**
     * Database column: atr_conf_excel_table.seq_name
     * Database remarks: seq_name|序列名
     */
    @ApiModelProperty(value = "seq_name|序列名", required = false)
    private String seqName;

    private static final long serialVersionUID = 1L;

    public Long getBizTypeId() {
        return bizTypeId;
    }

    public void setBizTypeId(Long bizTypeId) {
        this.bizTypeId = bizTypeId;
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public String getTypeEName() {
        return typeEName;
    }

    public void setTypeEName(String typeEName) {
        this.typeEName = typeEName;
    }

    public String getTypeLName() {
        return typeLName;
    }

    public void setTypeLName(String typeLName) {
        this.typeLName = typeLName;
    }

    public String getTypeCName() {
        return typeCName;
    }

    public void setTypeCName(String typeCName) {
        this.typeCName = typeCName;
    }

    public Long getSysId() {
        return sysId;
    }

    public void setSysId(Long sysId) {
        this.sysId = sysId;
    }

    public String getTypeGroup() {
        return typeGroup;
    }

    public void setTypeGroup(String typeGroup) {
        this.typeGroup = typeGroup;
    }

    public String getRedirectToUrl() {
        return redirectToUrl;
    }

    public void setRedirectToUrl(String redirectToUrl) {
        this.redirectToUrl = redirectToUrl;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }

    public BigDecimal getDisplayNo() {
        return displayNo;
    }

    public void setDisplayNo(BigDecimal displayNo) {
        this.displayNo = displayNo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public String getOriginalIs() {
        return originalIs;
    }

    public void setOriginalIs(String originalIs) {
        this.originalIs = originalIs;
    }

    public String getUploadIs() {
        return uploadIs;
    }

    public void setUploadIs(String uploadIs) {
        this.uploadIs = uploadIs;
    }

    public String getSeqName() {
        return seqName;
    }

    public void setSeqName(String seqName) {
        this.seqName = seqName;
    }
}