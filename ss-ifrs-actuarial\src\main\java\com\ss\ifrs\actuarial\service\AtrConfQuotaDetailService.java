package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaDetailVo;

/**
 * <AUTHOR>
 */
public interface AtrConfQuotaDetailService {

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 新增或修改指标配置明细对象信息
     */
    void addOrUpdateVo(AtrConfQuotaDetailVo bbsConfQuotaDetailVo, Long userId);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 根据主键ID查找指标配置明细对象信息
     */
    AtrConfQuotaDetailVo findByPk(Long quotaDetailId);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 根据主键ID删除指标配置明细对象信息
     */
    void deleteByPk(Long quotaDetailId, Long userId);
}
