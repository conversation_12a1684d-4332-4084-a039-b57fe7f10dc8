/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-07-08 11:25:49
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2021-07-08 11:25:49<br/>
 * Description: null<br/>
 * Table Name: atr_conf_bussperiod<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
public class AtrConfBussPeriodVo implements Serializable {
    /**
     * Database column: atr_conf_bussperiod.buss_period_id
     * Database remarks: buss_period_id|主键id
     */
    @ApiModelProperty(value = "buss_period_id|主键id", required = true)
    private Long bussPeriodId;

    /**
     * Database column: atr_conf_bussperiod.center_id
     * Database remarks: ENTITY_ID|业务单位id
     */
    @ApiModelProperty(value = "ENTITY_ID|业务单位id", required = true)
    private Long entityId;

    /**
     * Database column: atr_conf_bussperiod.year_month
     * Database remarks: year_month|业务年月
     */
    @ApiModelProperty(value = "year_month|业务年月", required = true)
    private String yearMonth;

    /**
     * Database column: atr_conf_bussperiod.period_state
     * Database remarks: period_state|完成标识  0-待同步 1-执行中 2-完成
     */
    @ApiModelProperty(value = "period_state|完成标识  0-待同步 1-执行中 2-完成", required = false)
    private String periodState;

    /**
     * Database column: atr_conf_bussperiod.valid_is
     * Database remarks: valid_is|有效状态
     */
    @ApiModelProperty(value = "valid_is|有效状态", required = false)
    private String validIs;

    /**
     * Database column: atr_conf_bussperiod.creator_id
     * Database remarks: creator_id|创建人id
     */
    @ApiModelProperty(value = "creator_id|创建人id", required = false)
    private Long creatorId;

    /**
     * Database column: atr_conf_bussperiod.create_time
     * Database remarks: create_Time|创建时间
     */
    @ApiModelProperty(value = "create_Time|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: atr_conf_bussperiod.updator_id
     * Database remarks: updator_id|最后更新人id
     */
    @ApiModelProperty(value = "updator_id|最后更新人id", required = false)
    private Long updatorId;

    /**
     * Database column: atr_conf_bussperiod.update_time
     * Database remarks: update_Time|最后更新时间
     */
    @ApiModelProperty(value = "update_Time|最后更新时间", required = false)
    private Date updateTime;

    private String bizCode;
    private String entityCode;
    private String entityCName;
    private String entityLName;
    private String entityEName;

    private String parentProcCode;

    private String procCode;

    private Long procId;

    private String taskCode;

    /**
     *  未准备的 direction 1 的 biz 数量
     */
    private Integer dir1Ready0Count;

    private static final long serialVersionUID = 1L;



    public Long getBussPeriodId() {
        return bussPeriodId;
    }

    public void setBussPeriodId(Long bussPeriodId) {
        this.bussPeriodId = bussPeriodId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }	

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getPeriodState() {
        return periodState;
    }

    public void setPeriodState(String periodState) {
        this.periodState = periodState;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getEntityCode() {
        return entityCode;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public String getEntityCName() {
        return entityCName;
    }

    public void setEntityCName(String entityCName) {
        this.entityCName = entityCName;
    }

    public String getEntityLName() {
        return entityLName;
    }

    public void setEntityLName(String entityLName) {
        this.entityLName = entityLName;
    }

    public String getEntityEName() {
        return entityEName;
    }

    public void setEntityEName(String entityEName) {
        this.entityEName = entityEName;
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public Long getProcId() {
        return procId;
    }

    public void setProcId(Long procId) {
        this.procId = procId;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getParentProcCode() {
        return parentProcCode;
    }

    public void setParentProcCode(String parentProcCode) {
        this.parentProcCode = parentProcCode;
    }

    public String getProcCode() {
        return procCode;
    }

    public void setProcCode(String procCode) {
        this.procCode = procCode;
    }

    public Integer getDir1Ready0Count() {
        return dir1Ready0Count;
    }

    public void setDir1Ready0Count(Integer dir1Ready0Count) {
        this.dir1Ready0Count = dir1Ready0Count;
    }
}