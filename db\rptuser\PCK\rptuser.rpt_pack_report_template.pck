CREATE OR REPLACE PACKAGE rpt_pack_report_template IS
    -- Author  : ZOPT
    -- Created : 2022/5/11 16:30:22
    -- Purpose :
    PROCEDURE proc_rule_analytical(p_entity_id          NUMBER,
                                   p_book_code          VARCHAR2,
                                   p_year_month         VARCHAR2,
                                   p_report_template_id NUMBER,
                                   p_sheet_no           NUMBER,
                                   p_task_code          varchar2);

END rpt_pack_report_template;
/
CREATE OR REPLACE PACKAGE BODY rpt_pack_report_template IS

    -- Private type declarations
    PROCEDURE proc_rule_analytical(p_entity_id          NUMBER,
                                   p_book_code          VARCHAR2,
                                   p_year_month         VARCHAR2,
                                   p_report_template_id NUMBER,
                                   p_sheet_no           NUMBER,
                                   p_task_code          varchar2) AS
        /***********************************************************************
        NAME : rpt_pack_report_template_proc_rule_analytical
        DESCRIPTION : 报表模板规则解析，更新模板单元格解析值
        DATE :2021-10-11
        AUTHOR :YINXH

        -------
        MODIFY LOG

          UPDATE DATE :
          UPDATE BY   :
          UPDATE DESC :
        ***********************************************************************/

        v_log_msg   VARCHAR2(4000); --日志信息
        v_error_msg VARCHAR2(300); --异常信息
        e_error EXCEPTION; --异常

        v_cell_rule_expr                     VARCHAR2(2000); --表达式
        v_cell_rule_expr_analytical          VARCHAR2(2000); --解析后的规则表达式

        v_count_rule_is_num                  NUMBER(11, 0); --表达式是否全为数字项

        v_item_amount                        number(32, 8); --科目金额
        v_cell_cal_value                     number(32, 8); --报表模板单元格解析值

        v_item_amount_is_num                 NUMBER(16, 0); --科目金额是否为null

        v_new_report_template_duct_detail_id NUMBER(11, 0); --新的解析模板过程子表id

        v_rule_serial_no                     number(6, 0); -- 规则版本号

    BEGIN

        /**
        * 处理规则：
            1、从模板解析过程表捞取符合条件的数据列表，
            2、解析其中的列项规则表达式
            3、将解析后的数值回写到表中
            4、返回结果列表
        *
        */

        FOR cur_template_cell IN (SELECT report_template_duct_id,
                                         cell_row_no,
                                         cell_column_no,
                                         cell_rule_expr,
                                         serial_no
                                  FROM rpt_duct_report_template
                                  WHERE entity_id = p_entity_id
                                    AND book_code = p_book_code
                                    AND year_month = p_year_month
                                    AND report_template_id = p_report_template_id
                                    and TASK_CODE = p_task_code
                                    AND sheet_no = p_sheet_no)
            LOOP

                --初始金额
                v_item_amount := 0.00;
                v_cell_cal_value := 0.00;

                -- 获取相关变量
                v_cell_rule_expr := cur_template_cell.cell_rule_expr;
                v_cell_rule_expr_analytical := cur_template_cell.cell_rule_expr;

                -- 获取表达式全为数字项计数值,
                SELECT COUNT(1)
                INTO v_count_rule_is_num
                FROM dual
                WHERE regexp_like(regexp_replace(v_cell_rule_expr, '\(|\)|\*|\-|\+|\/'), '(^[+-]?\d{0,}\.?\d{0,}$)');

                -- 如果全为数字项，则计数值大于0
                IF v_count_rule_is_num > 0 THEN

                    BEGIN
                        --拼接规则表达式解析后的计算SQL脚本，返回规则表达式解析后的计算结果
                        EXECUTE IMMEDIATE 'select (' || v_cell_rule_expr || ') from dual'
                            INTO v_cell_cal_value;
                    EXCEPTION
                        WHEN OTHERS THEN
                            v_error_msg := '报表模板解析列项规则执行发生错误：' || v_cell_rule_expr || ', ' || SQLERRM;
                            -- 往外层抛自定义异常
                            raise_application_error(-20003, v_error_msg);

                    END;

                    -- 未取到科目值，补0
                    IF v_cell_cal_value IS NULL THEN
                        v_cell_cal_value := 0.00;
                    END IF;

                ELSE
                    --非全数字项处理
                    -- 正则匹配获取[]包含项，排除数字项，并去重
                    FOR cur_item IN (SELECT DISTINCT regexp_substr(v_cell_rule_expr, '\[(.+?)\]', 1, LEVEL) AS cal_item
                                     FROM dual
                                     CONNECT BY regexp_substr(v_cell_rule_expr, '\[(.+?)\]', 1, LEVEL) IS NOT NULL)
                        LOOP

                            --保存单元格内各列项信息到解析过程子表中
                            v_new_report_template_duct_detail_id := rpt_seq_duct_temp_detail.nextval;

                            INSERT INTO rpt_duct_report_temp_detail
                            (report_template_duct_detail_id,
                             report_template_duct_id,
                             template_duct_serial_no,
                             report_item_id,
                             report_item_code,
                             creator_id,
                             create_time)
                            SELECT v_new_report_template_duct_detail_id,
                                   cur_template_cell.report_template_duct_id,
                                   cur_template_cell.serial_no,
                                   ri.report_item_id,
                                   ri.report_item_code,
                                   NULL,
                                   SYSDATE
                            FROM rpt_conf_report_item_rule rb,
                                 rpt_conf_report_item ri
                            WHERE rb.report_item_id = ri.report_item_id --列报项ID
                              AND ri.report_item_code = regexp_replace(cur_item.cal_item, '\[|\]', '')
                              AND rb.entity_id = p_entity_id
                              AND rb.book_code = p_book_code;


                            -- 从列项结果表取数
                            SELECT COUNT(1)
                            INTO v_item_amount_is_num
                            FROM rpt_buss_report_item_data rb,
                                 rpt_conf_report_item ri
                            WHERE rb.report_item_id = ri.report_item_id --列报项ID
                              AND ri.report_item_code = regexp_replace(cur_item.cal_item, '\[|\]', '')
                              AND rb.entity_id = p_entity_id
                              AND rb.book_code = p_book_code
                              AND rb.year_month = p_year_month
                              and rb.TASK_CODE = p_task_code;

                            IF v_item_amount_is_num > 0 THEN
                                SELECT rb.amount,
                                       rb.report_item_rule_serial_no
                                INTO v_item_amount,v_rule_serial_no
                                FROM rpt_buss_report_item_data rb,
                                     rpt_conf_report_item ri
                                WHERE rb.report_item_id = ri.report_item_id --列报项ID
                                  AND ri.report_item_code = regexp_replace(cur_item.cal_item, '\[|\]', '')
                                  AND rb.entity_id = p_entity_id
                                  AND rb.book_code = p_book_code
                                  AND rb.year_month = p_year_month
                                  and rb.TASK_CODE = p_task_code;
                            else
                                v_item_amount := 0.00;
                                v_rule_serial_no := null;
                            END IF;


                            -- 未取到科目值，补0
                            IF v_item_amount IS NULL THEN
                                v_item_amount := 0.00;
                            END IF;

                            -- [汇总币别]比较科目项的值，用正则(需转义处理)匹配替换表达式中科目项的编码
                            IF v_item_amount > 0 THEN
                                -- 当科目项的值为正数时，直接替换，数字转字符串 cast(123 as VARCHAR2)
                                v_cell_rule_expr_analytical := REPLACE(v_cell_rule_expr_analytical, cur_item.cal_item,
                                                                       CAST(v_item_amount AS VARCHAR2));
                            ELSIF v_item_amount < 0 THEN
                                -- 当科目项的值为负数时，需要添加小括号
                                v_cell_rule_expr_analytical := REPLACE(v_cell_rule_expr_analytical, cur_item.cal_item,
                                                                       '(' || CAST(v_item_amount AS VARCHAR2) || ')');

                            ELSE

                                -- 若查询不到科目项的值，作为除数时，需要替换为1
                                IF instr(v_cell_rule_expr, ('/' || cur_item.cal_item)) > 0 THEN
                                    v_cell_rule_expr_analytical :=
                                            REPLACE(v_cell_rule_expr_analytical, ('/' || cur_item.cal_item), '/1');
                                ELSE
                                    --不作为除数时，直接替换为0
                                    v_cell_rule_expr_analytical :=
                                            REPLACE(v_cell_rule_expr_analytical, cur_item.cal_item, '0');

                                END IF;
                            END IF;

                            -- 回写detail记录中的金额和规则版本号
                            update rpt_duct_report_temp_detail
                            set AMOUNT                     = v_item_amount,
                                REPORT_ITEM_RULE_SERIAL_NO = v_rule_serial_no
                            where report_template_duct_detail_id = v_new_report_template_duct_detail_id;


                            --记录到轨迹表
                            INSERT INTO rpt_duct_report_temp_detailhis
                            (report_template_duct_detail_his_id,
                             report_template_duct_detail_id,
                             report_template_duct_id,
                             template_duct_serial_no,
                             report_item_id,
                             report_item_code,
                             report_item_rule_serial_no,
                             amount,
                             creator_id,
                             create_time)
                            SELECT rpt_seq_duct_temp_detailhis.nextval,
                                   d.report_template_duct_detail_id,
                                   d.report_template_duct_id,
                                   d.template_duct_serial_no,
                                   d.report_item_id,
                                   d.report_item_code,
                                   d.report_item_rule_serial_no,
                                   d.amount,
                                   d.creator_id,
                                   d.create_time
                            FROM rpt_duct_report_temp_detail d
                            WHERE d.report_template_duct_detail_id = v_new_report_template_duct_detail_id;

                        END LOOP;
                    --遍历科目项

                    -- 计算表达式的值
                    IF v_cell_rule_expr_analytical IS NOT NULL THEN

                        BEGIN
                            --[原币]拼接规则表达式解析后的计算SQL脚本，返回规则表达式解析后的计算结果
                            EXECUTE IMMEDIATE 'select (' || v_cell_rule_expr_analytical || ') from dual'
                                INTO v_cell_cal_value;

                        EXCEPTION
                            WHEN OTHERS THEN
                                v_error_msg := '报表模板解析列项规则执行发生错误：' || v_cell_rule_expr || ', ' || SQLERRM;
                                -- 往外层抛自定义异常
                                raise_application_error(-20003, v_error_msg);

                        END;
                    END IF;
                END IF;

                -- 回写单元格解析值
                UPDATE rpt_duct_report_template
                SET cell_cal_value     = v_cell_cal_value,
                    cell_rule_analytic = v_cell_rule_expr_analytical
                WHERE report_template_duct_id = cur_template_cell.report_template_duct_id;

                --调试打印信息
                dbms_output.put_line('报表模板规则解析中：' || p_sheet_no || ', ' || cur_template_cell.cell_row_no ||
                                     ', ' || cur_template_cell.cell_column_no || ', ' || v_cell_cal_value || ', ' ||
                                     cur_template_cell.cell_rule_expr || ', ' || v_cell_rule_expr_analytical);

            END LOOP;
        --遍历报表模板单元格

        --提交事务
        COMMIT;

    EXCEPTION
        WHEN OTHERS THEN
            -- 发生异常回滚
            ROLLBACK;

            v_log_msg := substr('报表模板解析异常：' || 'p_entity_id: ' || p_entity_id || ', ' || 'p_book_code: ' ||
                                p_book_code || ', ' || 'p_year_month: ' || p_year_month || ', ' ||
                                'p_report_template_id: ' ||
                                p_report_template_id || ', ' || 'p_sheet_no: ' || p_sheet_no || ', ' || v_error_msg ||
                                '; ' || '**SQLERRM: ' || SQLERRM || '**Error line: ' ||
                                dbms_utility.format_error_backtrace() || '', 1, 4000);

            -- 往外层抛自定义异常
            raise_application_error(-20002, v_log_msg);

    END proc_rule_analytical;
END rpt_pack_report_template;
/
