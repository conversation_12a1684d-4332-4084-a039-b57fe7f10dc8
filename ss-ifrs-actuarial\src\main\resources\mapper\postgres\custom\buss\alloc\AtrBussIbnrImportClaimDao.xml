<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by Taiping MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2025-03-24 19:27:36 -->
<!-- Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.alloc.AtrBussIbnrImportClaimDao">
  <!-- 本配置文件由Taiping MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**BaseDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->

  <insert id="saveIbnrDetailList" flushCache="true" parameterType="java.util.List">
    insert into atr_buss_ibnr_import_claim
    (ibnr_claim_id, claim_main_id, treaty_no, treaty_name,
    policy_no, kind_code, claim_no
    )
    VALUES
    <foreach collection="list" item="item" index="index" separator="," >
      ( nextval('atr_seq_buss_ibnr_import_dtl'), #{mainId,jdbcType=DECIMAL},  #{item.treatyNo,jdbcType=VARCHAR}, #{item.treatyName,jdbcType=VARCHAR},
      #{item.policyNo,jdbcType=VARCHAR}, #{item.kindCode,jdbcType=VARCHAR}, #{item.claimNo,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
</mapper>