package com.ss.ifrs.actuarial.api;

import com.ss.platform.core.annotation.TrackUserBehavioralEnable;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.core.api.DynamicsApi;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.core.mybatis.dynamics.DynamicsParam;
import com.ss.platform.core.mybatis.dynamics.RunDynamicsUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @ClassName: AtrDynamicsSqlApi
 * @Description: 动态sqlAPI
 */
@RestController
@RequestMapping("/dynamics")
@Api(value = "动态sqlAPI")
@TrackUserBehavioralEnable
public class AtrDynamicsApi extends BaseApi implements DynamicsApi {

    @Resource
    JdbcTemplate jdbcTemplate;

    @ApiOperation(value = "动态sql查询")
    @PostMapping(value = "/query")
    @PermissionRequest(required = false)
    public Object dynamics(HttpServletRequest request, HttpServletResponse response, @RequestBody DynamicsParam param) {
        checkPermission(request, param);
        param.setOperId(loginUserId(request));
        return RunDynamicsUtil.run(jdbcTemplate, param);
    }

    @ApiOperation(value = "导出结果")
    @PostMapping(value = "/export")
    @PermissionRequest(required = false)
    public void export(HttpServletRequest request, HttpServletResponse response, @RequestBody DynamicsParam param) {
        BaseResponse<Object> dynamics = (BaseResponse<Object>) this.dynamics(request, response, param);
        export(response,dynamics);
    }
}
