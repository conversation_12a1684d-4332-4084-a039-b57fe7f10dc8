create or replace package exp_pack_common is

  -- Author  : ZOPT
  -- Created : 2022/5/9 19:48:10
  -- Purpose :
  FUNCTION func_get_depttype(p_entity_id NUMBER,
                        p_dept_code VARCHAR2,
                        p_dept_id   NUMBER) RETURN VARCHAR2;

  FUNCTION func_get_exchange(p_entity_id IN NUMBER,
                             p_currency_org VARCHAR2,
                             p_currency_cu  VARCHAR2,
                             p_yearmonth    VARCHAR2,
                             p_exch_type IN VARCHAR2) RETURN NUMBER;


end exp_pack_common;
/
create or replace package body exp_pack_common is

FUNCTION func_get_depttype(p_entity_id NUMBER, p_dept_code VARCHAR2, p_dept_id NUMBER)
  RETURN VARCHAR2 AS --获取是否待摊部门
  --声明部分
  v_dept_type VARCHAR2(1) := '0'; --v_dept_type: 是否待摊部门 0-否,1-是
  v_dept_count NUMBER ( 3, 0 ) := 0;--entity_id 的下属部门数量
BEGIN--PL/SQL程序块

  --如果机构参数为空，则判断部门专项是不是部门
  IF p_entity_id is null THEN
    select count(1) into v_dept_count from bpluser.bbs_entity C where unit_type = 'D' and entity_code = p_dept_code;
    if v_dept_count > 0 then
      v_dept_type := '1' ;
    else
      v_dept_type := '0' ;
    end if;
    RETURN v_dept_type;
  END IF;

  IF p_dept_id is null THEN
    v_dept_type := '0';
    RETURN v_dept_type;
  END IF;

--获取下属部门数量
with tb_result as( select entity_id,unit_type
from (
select c.* from
bpluser.bbs_entity C LEFT JOIN bpluser.bpl_company T on T.company_id = C.entity_id
    WHERE
      C.valid_is = '1'
      AND C.audit_state = '1'
      AND T.valid_is = '1'
)
start with entity_id = p_entity_id
connect by prior entity_id = upper_entity_id )

  SELECT
    count(*) into v_dept_count
  FROM
    tb_result
  WHERE
    unit_type = 'D' and entity_id = p_dept_id
  ;
  if v_dept_count > 0 then
    v_dept_type := '1' ;
  else
    v_dept_type := '0' ;
  end if;
  RETURN v_dept_type;

END func_get_depttype;



FUNCTION func_get_exchange(p_entity_id IN NUMBER,p_currency_org VARCHAR2, p_currency_cu VARCHAR2, p_yearmonth VARCHAR2,p_exch_type IN VARCHAR2)
  RETURN NUMBER AS
--获取兑换率
--声明部分
  V_RATE NUMBER(32, 8);
BEGIN--PL/SQL程序块
  IF
    p_currency_org = p_currency_cu THEN
      V_RATE := 1.000000;
    RETURN V_RATE;

  END IF;
--获取会计期间相应的兑换率
  SELECT
    exchange_rate INTO V_RATE
  FROM
    (
    SELECT A .exchange_rate
    FROM
      bpluser.bbs_conf_currencyrate A
    WHERE  entity_id = p_entity_id
      AND A.CURRENCY_CODE = p_currency_org
      AND A.EXCH_CURRENCY_CODE = p_currency_cu
      AND to_char(A.EFFECTIVE_DATE, 'yyyymm' ) <= P_YEARMONTH
      AND FREQUENCY_CODE = p_exch_type
      AND A.VALID_IS = '1'
      AND A.AUDIT_STATE = '1'
    ORDER BY
      A.EFFECTIVE_DATE DESC
      FETCH NEXT 1 rows ONLY
    ) B ;
  RETURN V_RATE;

END func_get_exchange;
end exp_pack_common;
/
