package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.x;

import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Tab("atr_buss_to_lrc_x_ul")
@Getter @Setter
public class AtrBussLrcToUlx {

    /** ID */
    private Long id;

    /** 执行编号 */
    private String actionNo;

    /** 合约号 */
    private String treatyNo;

    /** 合约名称 */
    private String treatyName;

    /** 评估期年月 */
    private String yearMonth;

    /** 保单号 */
    private String policyNo;

    /** 批单序号 */
    private String endorseSeqNo;

    /** 险别代码 */
    private String kindCode;
    
    /** 险种代码 */
    private String riskCode;

    /** 合同组合编码 */
    private String portfolioNo;

    /** 合同组编码 */
    private String icgNo;
    
    /** 合同组名称 */
    private String icgName;

    /** 计量单元编号 */
    private String cmunitNo;

    /** 实体ID */
    private Long entityId;

    /** 合同确认日期 */
    private Date contractDate;

    /** 一级机构 */
    private String companyCode1;

    /** 二级机构 */
    private String companyCode2;

    /** 三级机构 */
    private String companyCode3;

    /** 四级机构 */
    private String companyCode4;

    /** 财务渠道 */
    private String finAccChannel;

    /** 财务产品代码 */
    private String finProductCode;

    /** 财务明细代码 */
    private String finDetailCode;

    /** 财务子产品代码 */
    private String finSubProductCode;

    /** 部门段 */
    private String deptId;

    /** 渠道段 */
    private String channelId;

    /** 核算机构 */
    private String centerCode;

    /** 盈亏判定结果 */
    private String plJudgeRslt;

    /** 起保日期 */
    private Date effectiveDate;

    /** 终保日期 */
    private Date expiryDate;

    /** 签单保费 */
    private BigDecimal premium;

    /** 满期保费 */
    private BigDecimal earnedPremium;

    /** 累计当期已赚MDP */
    private BigDecimal cumlCurEdMdp;

    /** 当期已赚保费 */
    private BigDecimal curEdPremium;

}
