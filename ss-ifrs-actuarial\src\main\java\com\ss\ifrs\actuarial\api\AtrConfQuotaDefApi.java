package com.ss.ifrs.actuarial.api;

import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaDefVo;
import com.ss.ifrs.actuarial.service.AtrConfModelDefService;
import com.ss.ifrs.actuarial.service.AtrConfQuotaDefService;
import com.ss.ifrs.actuarial.service.AtrExportService;
import com.ss.platform.core.annotation.TrackUserBehavioralEnable;
import com.ss.platform.core.annotation.TrackUserBehavioral;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.library.constant.RestfulCodeConstant;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.platform.util.CheckParamsUtil;
import com.ss.library.utils.ExceptionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName: AtrConfQuotaDefApi
 * @Description: 指标定义配置Api
 * @Date 2021/11/10
 **/
@RestController
@RequestMapping("/quota_def")
@TrackUserBehavioralEnable
@Api(value = "指标定义配置管理")
public class AtrConfQuotaDefApi extends BaseApi {
    @Autowired
    AtrConfQuotaDefService atrConfQuotaDefService;

    @Autowired
    AtrConfModelDefService atrConfModelDefService;

    @Autowired
    AtrExportService atrExportService;

    @ApiOperation(value = "查询指标定义配置信息")
    @RequestMapping(value = "/enquiry", method = {RequestMethod.POST})
    public BaseResponse<Object> enquiry(HttpServletRequest request, @RequestBody AtrConfQuotaDefVo atrConfQuotaDefVo, int _pageNo, int _pageSize) {
        // 结果排序：指标分组+指标类型+序号
        Pageable pageable = new Pageable(_pageNo, _pageSize, Sort.by(Sort.Direction.DESC, "update_time", "quota_def_id"));
        Page<?> page = atrConfQuotaDefService.findAtrConfQuotaDefList(atrConfQuotaDefVo, pageable);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("atrConfQuotaDefVoList", page);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
    }

    @ApiOperation("新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public BaseResponse<Object> add(HttpServletRequest request, @RequestBody @Validated AtrConfQuotaDefVo atrConfQuotaDefVo, BindingResult br){
        if (br.hasErrors()) {
            // 该状态码为“其它格式转换错误”
            StringBuffer errorMessages = new StringBuffer();
            for (int i = 0; i < br.getErrorCount(); i++) {
                errorMessages.append(br.getAllErrors().get(i).getDefaultMessage() + ";");
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.FORMAT_TRANSFORM_ERROR,new CheckParamsUtil().getMessages(errorMessages.toString()));
        }
        try {
            Long userId = this.loginUserId(request);
            atrConfQuotaDefService.addOrUpdateVo(atrConfQuotaDefVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, "SUCCESS");
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "删除指标对象信息")
    @RequestMapping(value = "/delete_by_pk/{quotaDefId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<Object> delete(HttpServletRequest request,@PathVariable("quotaDefId") Long quotaDefId) {
        Long userId = this.loginUserId(request);
        try {
            String result = atrConfQuotaDefService.deleteByPk(quotaDefId, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
        } catch (Exception ex) {
            String message = ExceptionUtil.getMessage(ex);
            logger.error(ex.getLocalizedMessage(), ex);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, message, RestfulCodeConstant.RestfulCode.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "激活或禁用")
    @RequestMapping(value = "/valid_status", method = RequestMethod.POST)
    public BaseResponse<Object> validStatus (HttpServletRequest request, @RequestBody AtrConfQuotaDefVo atrConfQuotaDefVo){
        try {
            Long userId = this.loginUserId(request);
            String resMsg = atrConfQuotaDefService.disableValid(atrConfQuotaDefVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, RestfulCodeConstant.RestfulCode.SUCCESS);
        } catch (Exception ex) {
            String message = ExceptionUtil.getMessage(ex);
            logger.error(ex.getLocalizedMessage(), ex);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, message, RestfulCodeConstant.RestfulCode.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "审核")
    @RequestMapping(value = "/audit", method = RequestMethod.POST)
    public BaseResponse<String> audit (HttpServletRequest request, @RequestBody @Validated AtrConfQuotaDefVo
            atrConfQuotaDefVo, BindingResult br){
        Long userId = this.loginUserId(request);
        atrConfQuotaDefService.updateAudit(atrConfQuotaDefVo, userId);
        return new BaseResponse<String>(ResCodeConstant.ResCode.SUCCESS, "SUCCESS");
    }

    @ApiOperation(value = "批量审核")
    @RequestMapping(value = "/batch_audit", method = RequestMethod.POST)
    public BaseResponse<String> batchAudit (HttpServletRequest
                                                    request, @RequestBody ArrayList< AtrConfQuotaDefVo > auditDealList, String auditState, String checkedMsg){
        // 获取当前操作人Id
        Long userId = this.loginUserId(request);
        for (AtrConfQuotaDefVo atrConfQuotaDefVo : auditDealList) {
            atrConfQuotaDefVo.setAuditState(auditState);
            atrConfQuotaDefVo.setCheckedMsg(checkedMsg);
            atrConfQuotaDefVo.setCheckedId(this.loginUserId(request));
            atrConfQuotaDefVo.setCheckedTime(new Date());
        }
        try {
            atrConfQuotaDefService.batchAudit(auditDealList, userId);
            return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, ResCodeConstant.ResCode.SUCCESS);
        } catch (UnexpectedRollbackException e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<String>(ResCodeConstant.ResCode.OTHER_ERROR, null);
        }
    }

    @ApiOperation(value = "校验指标定义编码的有效性")
    @RequestMapping(value = "/validate_code", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<String> findValidateCode(HttpServletRequest request, @RequestBody String quotaCode){
        String validRptItemCode = atrConfQuotaDefService.findValidAtrQuotaCode(quotaCode);
        return new BaseResponse<String>(ResCodeConstant.ResCode.SUCCESS, validRptItemCode);
    }

    @ApiOperation("根据ID查找对象")
    @RequestMapping(value = "/find_vo_by_pk/{quotaDefId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<AtrConfQuotaDefVo> findVoById(@PathVariable("quotaDefId") Long quotaDefId){
        AtrConfQuotaDefVo atrConfQuotaDefVo = atrConfQuotaDefService.findVoByPk(quotaDefId);
        return new BaseResponse<AtrConfQuotaDefVo>(ResCodeConstant.ResCode.SUCCESS, atrConfQuotaDefVo);
    }

    @ApiOperation("根据ID查找对象")
    @RequestMapping(value = "/his/find_vo_by_pk/{quotaDefId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<AtrConfQuotaDefVo> findHisVoById(@PathVariable("quotaDefId") Long quotaDefId){
        AtrConfQuotaDefVo atrConfQuotaDefVo = atrConfQuotaDefService.findHisVoByPk(quotaDefId);
        return new BaseResponse<AtrConfQuotaDefVo>(ResCodeConstant.ResCode.SUCCESS, atrConfQuotaDefVo);
    }

    @ApiOperation(value = "导出假设定义配置")
    @TrackUserBehavioral(description = "exportExcelModel")
    @RequestMapping(value = "/export_excel", method = RequestMethod.POST)
    public BaseResponse<Map<String, Object>> exportExcelModel(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrConfQuotaDefVo atrConfQuotaDefVo) throws Exception {
        Pageable pageParam = new Pageable(0, maxExcelPageSize, Sort.by(Sort.Direction.ASC, "QUOTA_GROUP", "QUOTA_TYPE", "DISPLAY_NO"));
        String language = request.getHeader("ss-Language");
        atrConfQuotaDefVo.setLanguage(language);
        Page<AtrConfQuotaDefVo> atrConfRiskRefVoPage = (Page<AtrConfQuotaDefVo>) atrConfQuotaDefService.findAtrConfQuotaDefList(atrConfQuotaDefVo, pageParam);
        try {
            Long userId = this.loginUserId(request);
            atrExportService.exportPage(request, response, atrConfRiskRefVoPage, AtrConfQuotaDefVo.class, "df", atrConfQuotaDefVo.getTemplateFileName(), atrConfQuotaDefVo.getTargetRouter(), userId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, null);
    }
}
