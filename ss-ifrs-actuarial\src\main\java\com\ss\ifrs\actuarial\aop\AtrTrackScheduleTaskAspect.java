package com.ss.ifrs.actuarial.aop;

import com.ss.ifrs.actuarial.feign.BmsConfCodeFeignClient;
import com.ss.ifrs.actuarial.feign.BmsScheduleFeignClient;
import com.ss.platform.core.annotation.LogScheduleTask;
import com.ss.platform.pojo.bms.log.vo.BmsLogPubTaskVo;
import com.ss.platform.pojo.bms.job.po.BmsScheduleDetail;
import com.ss.platform.pojo.bms.log.po.BmsLogScheduleJob;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.SystemConstant;
import com.ss.library.utils.ClassUtil;
import com.ss.library.utils.DataUtil;
import com.ss.library.utils.FastJsonUtil;
import com.ss.platform.pojo.bms.job.vo.BmsScheduleJobVo;
import com.ss.platform.pojo.bms.log.vo.BmsLogScheduleJobVo;
import com.ss.platform.pojo.bms.qrtz.vo.BmsQrtzConfTaskVo;
import com.ss.platform.pojo.bms.qrtz.vo.BmsQrtzConfTaskDetailVo;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.*;

/**
 * Title: AtrTrackScheduleTaskAspect Description: 通用功能日志切面类
 * <AUTHOR>
 */
@Aspect
@Component
public class AtrTrackScheduleTaskAspect extends BaseApi {

	@Autowired
	private BmsScheduleFeignClient bmsScheduleFeignClient;

	@Autowired
	private BmsConfCodeFeignClient bmsConfCodeFeignClient;

	private SpelExpressionParser spelParser = new SpelExpressionParser();

	/**
	 * @description 获取注解中对方法的业务编码信息 用于Controller层注解
	 */
	public static String getControllerMethodBizCode(JoinPoint joinPoint) throws Exception {
		String targetName = joinPoint.getTarget().getClass().getName();
		// 目标方法名
		String methodName = joinPoint.getSignature().getName();
		Object[] arguments = joinPoint.getArgs();
		Class targetClass = Class.forName(targetName);
		Method[] methods = targetClass.getMethods();
		String bizCode = "";
		for (Method method : methods) {
			if (method.getName().equals(methodName)) {
				Class[] clazzs = method.getParameterTypes();
				if (clazzs.length == arguments.length) {
					bizCode = method.getAnnotation(LogScheduleTask.class).bizCode();
					break;
				}
			}
		}
		return bizCode;
	}

	/**
	 * @description 用于当前平台系统切面获取任务编码
	 */
	public String getTaskCodeForAspect() {

		Map<String, Object> paramMap = new HashMap<>(3);
		// DM,EXP...
		paramMap.put("platform", SystemConstant.AtrIdentity.APP_CODE);
		// A-Auto，M-Manual
		paramMap.put("taskMode", CommonConstant.ScheduleTaskMode.MANUAL);
		// GG..
		paramMap.put("taskSection", "GG");
		// 自动生成手动任务编码
		return (String) bmsConfCodeFeignClient.findTaskCode(paramMap).getResData().get("TASKCODE");

	}

	/**
	 * @Description 环绕通知，用于处理拦截方法执行增强操作
	 * @param proceedingJoinPoint
	 */
	@Around("@annotation(com.ss.platform.core.annotation.LogScheduleTask) && @annotation(logScheduleTask)")
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
	public BaseResponse<Object> doAround(ProceedingJoinPoint proceedingJoinPoint,
	                                     LogScheduleTask logScheduleTask) throws Exception {
		HttpServletRequest request = null;
		if (ObjectUtils.isNotEmpty(RequestContextHolder.getRequestAttributes())) {
			request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
		}
		Long userId = ObjectUtils.isEmpty(this.loginUserId(request)) ? 1L : this.loginUserId(request);
		long entityId = 1L;
		String yearMonth = "";
		String bookCode = "";

		String taskMode = "";
		String taskCode = ""; // 自动任务有值
		long retryOrder = 0L; // 用于执行次数计数，初始为0

		// 获取方法的参数名和参数值
		MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
		List<String> paramNameList = Arrays.asList(methodSignature.getParameterNames());
		List<Object> paramList = Arrays.asList(proceedingJoinPoint.getArgs());

		// 将方法的参数名和参数值一一对应的放入上下文中
		EvaluationContext ctx = new StandardEvaluationContext();
		for (int i = 0; i < paramNameList.size(); i++) {
			ctx.setVariable(paramNameList.get(i), paramList.get(i));
		}

		/** 注意参数字段名要转换成小写后再匹配 */
		String paramValue = "";
		for (String argName : logScheduleTask.argsValue()) {
			paramValue = DataUtil.convert2String(spelParser.parseExpression(argName).getValue(ctx));
			if (argName.toLowerCase().endsWith("entityid") && ObjectUtils.isNotEmpty(paramValue)) {
				entityId = Long.parseLong(paramValue);
			} else if (argName.toLowerCase().endsWith("yearmonth")) {
				yearMonth = paramValue;
			} else if (argName.toLowerCase().endsWith("bookcode")) {
				bookCode = paramValue;
			}  else if (argName.toLowerCase().endsWith("taskmode")) {
				// 自动任务有值
				taskMode = paramValue;
			} else if (argName.toLowerCase().endsWith("taskcode")) {
				// 自动任务有值
				taskCode = paramValue;
			} else if (argName.toLowerCase().endsWith("retryorder") && ObjectUtils.isNotEmpty(paramValue)) {
				// 自动任务有值
				retryOrder = Long.parseLong(paramValue);
			}

		}

		/**
		 * 切面形参中获取功能的功能编码参数
		 */
		String funcCode = getControllerMethodBizCode(proceedingJoinPoint);


		/**
		 * ===============以下代码各平台都一致=================
		 */

		// 获取功能配置信息插入日志(funcCode)
		BmsQrtzConfTaskDetailVo confTaskDtlVo = bmsScheduleFeignClient.findConfTaskDtlByFuncCode(entityId, funcCode);

		/**
		 * 功能执行控制：
		 *      控制场景一：自动模式中，当前频率下，根据功能编码，业务单位，若未查询到功能配置，不允许执行
		 *      控制场景二：自动/手动模式中，当前频率下，根据功能id，业务单位，业务年月，账套，
		 *                  若查询到0-待执行，1-执行中的功能任务日志，不允许执行
		 *      控制场景三：自动模式中，当前频率下，根据功能id，业务单位，业务年月，账套，任务编码
		 *                  若未查询到依赖功能有2-已完成的功能日志，不允许执行
		 */
		// 判定结果字段：默认为true-允许执行
		boolean judgeResult = true;
		String judgeResultMsg = "";

		// 控制场景一：自动模式中，当前频率下，根据功能编码，业务单位，若未查询到功能配置，不允许执行
		if(CommonConstant.ScheduleTaskMode.AUTO.equals(taskMode) && ObjectUtils.isEmpty(confTaskDtlVo)) {
			judgeResult = false;
			judgeResultMsg = "The Function " + funcCode + " is not configured. Please configure it before executing! ";
		}

		if (judgeResult) {
			// 控制场景二：自动/手动模式中，当前频率下，根据功能id，业务单位，业务年月，账套，
			// 若查询到0-待执行，1-执行中的功能任务日志，不允许执行
			BmsLogPubTaskVo pubTaskLogVoForQuery = new BmsLogPubTaskVo();
			pubTaskLogVoForQuery.setTaskDetailId(confTaskDtlVo.getTaskDetailId());
			pubTaskLogVoForQuery.setEntityId(entityId);
			if(ObjectUtils.isNotEmpty(yearMonth)){
				pubTaskLogVoForQuery.setYearMonth(yearMonth);
			}
			if(ObjectUtils.isNotEmpty(bookCode)){
				pubTaskLogVoForQuery.setBookCode(bookCode);
			}
			// 查询0-待执行的功能日志
			pubTaskLogVoForQuery.setTaskStatus(CommonConstant.ScheduleLogStatus.TOEXEC);
			if(bmsScheduleFeignClient.findTaskDtlProcessingOrSuccessLogByVo(pubTaskLogVoForQuery) > 0) {
				judgeResult = false;
				judgeResultMsg = "The Function " + funcCode + " to be executed. Please try again later! ";
			}

			if (judgeResult) {
				// 查询1-执行中的功能日志
				pubTaskLogVoForQuery.setTaskStatus(CommonConstant.ScheduleLogStatus.RUNNING);
				if(bmsScheduleFeignClient.findTaskDtlProcessingOrSuccessLogByVo(pubTaskLogVoForQuery) > 0){
					judgeResult = false;
					judgeResultMsg = "The Function " + funcCode + " is in progress. Please try again later! ";
				}

				// 控制场景三：自动模式中，当前频率下，根据功能id，业务单位，业务年月，账套，任务编码
				// 若未查询到依赖功能有2-已完成的功能日志，不允许执行
				if (judgeResult && ObjectUtils.isNotEmpty(confTaskDtlVo.getRelationTaskDtlId())) {
					// 依赖的功能ID
					pubTaskLogVoForQuery.setTaskDetailId(confTaskDtlVo.getRelationTaskDtlId());
					// 查询2-完成的功能日志
					pubTaskLogVoForQuery.setTaskStatus(CommonConstant.ScheduleLogStatus.SUCCESS);
					// 自动任务时，增加任务编码条件，避免当月多次执行计量影响
					if(ObjectUtils.isNotEmpty(taskCode)){
						pubTaskLogVoForQuery.setTaskCode(taskCode);
					}
					if(CommonConstant.ScheduleTaskMode.AUTO.equals(taskMode) && bmsScheduleFeignClient.findTaskDtlProcessingOrSuccessLogByVo(pubTaskLogVoForQuery) == 0) {
						judgeResult = false;

						// 获取依赖功能名称
						BaseResponse<BmsQrtzConfTaskDetailVo> taskDetailVoBaseResponse = bmsScheduleFeignClient.findTaskDetailById(confTaskDtlVo.getRelationTaskDtlId());
						String relationTaskDetailName = "";
						if(ObjectUtils.isNotEmpty(taskDetailVoBaseResponse) && ObjectUtils.isNotEmpty(taskDetailVoBaseResponse.getResData())){
							relationTaskDetailName = taskDetailVoBaseResponse.getResData().getFuncEName();
						}
						judgeResultMsg = "The Function " + funcCode
								+ " can't be executed, because its dependent function: "
								+ relationTaskDetailName
								+" is not completed, Please try to execute its dependent function first! ";
					}
				}
			}
		}


		// 组装功能日志对象
		BmsLogPubTaskVo addPubTaskLogVo = new BmsLogPubTaskVo();
		addPubTaskLogVo.setTaskDetailId(confTaskDtlVo.getTaskDetailId());
		addPubTaskLogVo.setJobFrequency(confTaskDtlVo.getFrequency());
		addPubTaskLogVo.setEntityId(entityId);
		if(ObjectUtils.isNotEmpty(yearMonth)){
			addPubTaskLogVo.setYearMonth(yearMonth);
		}
		if(ObjectUtils.isNotEmpty(bookCode)){
			addPubTaskLogVo.setBookCode(bookCode);
		}
		// 模式、任务编码、状态执行判定后赋值

		// 记录任务开始时间
		addPubTaskLogVo.setStartTime(new Date());
		// 执行次数
		addPubTaskLogVo.setRetryOrder(retryOrder);
		addPubTaskLogVo.setCreatorId(userId);
		addPubTaskLogVo.setCreateTime(new Date());

		// 业务API执行返回结果
		BaseResponse<Object> returnResultsOfProceed = null;
		String afterTaskStatus = null;
		// 功能任务执行异常信息
		String taskDtlErrorMsg = "";
		try {
			// 若允许执行，先初始化功能日志及功能组日志【手动模式时】
			if (judgeResult) {

				/**
				 * 业务方法执行前功能日志新增或更新处理
				 *  场景一：自动模式，功能组点击执行，每次生成新的任务编号，功能组日志、功能日志都会新增
				 *  场景二：自动模式，功能组内部重复执行，没有生成新的任务编码，功能组日志会更新，功能日志也应该更新
				 *  场景三：手动干预模式，页面点击功能执行，本身没有任务编码(查询当前频率下，状态为0-待执行或1-执行中的功能组日获取编码)
				 */


				// 无值时，表示手动任务执行【手动模式特殊处理】
				if(ObjectUtils.isEmpty(taskCode)){
					// 未传任务编号值，则为手动模式
					taskMode = CommonConstant.ScheduleTaskMode.MANUAL;

					// 手动模式：先查询当前频率下，状态为1-执行中的功能组日志，若有则获取任务编号
					BaseResponse<BmsLogScheduleJobVo> scheduleJobLogVoResponse = bmsScheduleFeignClient.findScheduleJobLogInProgressByConfTaskId(confTaskDtlVo.getConfTaskId(),entityId);

					if(ObjectUtils.isNotEmpty(scheduleJobLogVoResponse) && ObjectUtils.isNotEmpty(scheduleJobLogVoResponse.getResData())){
						// 从功能组日志获取任务编号，再根据任务编号及其他参数查询功能日志，后续进行更新或新增；
						taskCode = scheduleJobLogVoResponse.getResData().getTaskCode();

					}else {
						// 若无功能组日志，则新增功能组日志
						// 自动生成手动任务编码
						taskCode = getTaskCodeForAspect();

						/**
						 * 功能组日志新增并更新
						 */
						// 获取定时任务数据对象
						BmsScheduleDetail scheduleDetail = bmsScheduleFeignClient.findJobDetailsByEntityIdAndConfTaskId(entityId, confTaskDtlVo.getConfTaskId());
						if(ObjectUtils.isNotEmpty(scheduleDetail)){

							/** 初始化调度任务功能组执行日志对象数据 */
							BmsLogScheduleJob scheduleJobLog = new BmsLogScheduleJob();
							scheduleJobLog.setTaskCode(taskCode);
							// M-手动
							scheduleJobLog.setTaskMode(CommonConstant.ScheduleTaskMode.MANUAL);
							scheduleJobLog.setEntityId(entityId);
							if(ObjectUtils.isNotEmpty(yearMonth)){
								scheduleJobLog.setYearMonth(yearMonth);
							}
							if(ObjectUtils.isNotEmpty(bookCode)){
								scheduleJobLog.setBookCode(bookCode);
							}
							scheduleJobLog.setJobFrequency(scheduleDetail.getJobFrequency());
							scheduleJobLog.setScheduleJobGroup(scheduleDetail.getJobGroup());
							scheduleJobLog.setScheduleJobName(scheduleDetail.getJobName());
							scheduleJobLog.setJobEName(scheduleDetail.getJobEName());
							scheduleJobLog.setJobCName(scheduleDetail.getJobCName());
							scheduleJobLog.setJobLName(scheduleDetail.getJobLName());
							// 创建人
							scheduleJobLog.setCreatorId(userId);
							// 任务创建日期
							scheduleJobLog.setCreateTime(new Date());

							// 满足执行控制条件，设功能组任务调用状态：1-执行中
							scheduleJobLog.setStatus(CommonConstant.ScheduleLogStatus.RUNNING);
							// 满足执行控制条件，设功能组任务执行状态：1-执行中
							scheduleJobLog.setTaskStatus(CommonConstant.ScheduleLogStatus.RUNNING);
							// 调度任务最近一次执行开始时间
							scheduleJobLog.setLastStartDate(new Date());

							// 保存到数据库
							bmsScheduleFeignClient.saveScheduleJobLog(scheduleJobLog);
						}

					}

				}

				/**
				 * 功能日志新增或更新【自动/手动模式】
				 */
				addPubTaskLogVo.setTaskMode(taskMode);
				addPubTaskLogVo.setTaskCode(taskCode);
				// 业务方法开始执行：状态为1-执行中
				addPubTaskLogVo.setTaskStatus(CommonConstant.ScheduleLogStatus.RUNNING);

				// 【自动/手动模式】先根据功能id、业务单位、业务年月、账套、任务编码查询当前频率下的功能日志，若存在则更新，不存在则新增
				BmsLogPubTaskVo pubTaskLogVoForAdd = new BmsLogPubTaskVo();
				pubTaskLogVoForAdd.setTaskDetailId(confTaskDtlVo.getTaskDetailId());
				pubTaskLogVoForAdd.setEntityId(entityId);
				if(ObjectUtils.isNotEmpty(yearMonth)){
					pubTaskLogVoForAdd.setYearMonth(yearMonth);
				}
				if(ObjectUtils.isNotEmpty(bookCode)){
					pubTaskLogVoForAdd.setBookCode(bookCode);
				}
				pubTaskLogVoForAdd.setTaskCode(taskCode);

				BaseResponse<BmsLogPubTaskVo> resultVoForAddQuery = bmsScheduleFeignClient.findTaskLog(pubTaskLogVoForAdd);

				if(ObjectUtils.isNotEmpty(resultVoForAddQuery) && ObjectUtils.isNotEmpty(resultVoForAddQuery.getResData())){
					// 存在则更新
					addPubTaskLogVo.setTaskLogId(resultVoForAddQuery.getResData().getTaskLogId());
					// 根据对象更新功能日志表
					bmsScheduleFeignClient.updateTaskLog(addPubTaskLogVo);
				}else {
					// 不存在则保存
					bmsScheduleFeignClient.addTaskLog(addPubTaskLogVo);
				}


				// 执行目标方法
				returnResultsOfProceed = (BaseResponse<Object>) proceedingJoinPoint.proceed();

				// 检查执行结果编码
				if(ResCodeConstant.ResCode.SUCCESS.equals((String) returnResultsOfProceed.getResCode())){
					afterTaskStatus = CommonConstant.ScheduleLogStatus.SUCCESS;
				}else {
					// 功能执行失败
					afterTaskStatus = CommonConstant.ScheduleLogStatus.FAILED;
					// 允许执行将返回结果信息存放判定结果字段
					if(ObjectUtils.isNotEmpty(returnResultsOfProceed.getResData())){
						judgeResultMsg = DataUtil.getMessageWithMaxLength(FastJsonUtil.toJson(returnResultsOfProceed.getResData()));
					}
				}

			} else {
				// 不允许执行，不执行目标方法，自动模式和手动模式都新增记录功能日志，后续也不用更新或新增功能组日志

				// 无值时，表示手动任务执行【手动模式特殊处理】
				if(ObjectUtils.isEmpty(taskCode)) {
					// 未传任务编号值，则为手动模式
					taskMode = CommonConstant.ScheduleTaskMode.MANUAL;

					// 若无功能组日志，则新增功能组日志
					// 自动生成手动任务编码
					taskCode = getTaskCodeForAspect();

				}

				/**
				 * 功能日志新增
				 */
				addPubTaskLogVo.setTaskMode(taskMode);
				addPubTaskLogVo.setTaskCode(taskCode);
				// 业务方法开始执行：状态为4-不执行
				addPubTaskLogVo.setTaskStatus(CommonConstant.ScheduleLogStatus.NONEXEC);
				// 不执行记录信息
				addPubTaskLogVo.setErrorMsg(judgeResultMsg);

				// 新增功能日志
				bmsScheduleFeignClient.addTaskLog(addPubTaskLogVo);

				// 功能任务不执行
				afterTaskStatus = CommonConstant.ScheduleLogStatus.NONEXEC;
			}

		} catch (Throwable e) {
			// 记录本地异常日志
			logger.error("异常信息：{}", e.getMessage());

			// 执行失败
			afterTaskStatus = CommonConstant.ScheduleLogStatus.FAILED;

			// 回写每个功能执行异常信息，方便跟踪
			taskDtlErrorMsg = "The Function " + funcCode + " has an exception occurred, Exception info: " + ExceptionUtils.getStackTrace(e);

			//记录异常信息
			taskDtlErrorMsg = DataUtil.getMessageWithMaxLength(taskDtlErrorMsg);

		}finally {
			// 功能执行失败或成功则回写功能日志状态
			if (CommonConstant.ScheduleLogStatus.SUCCESS.equals(afterTaskStatus) || CommonConstant.ScheduleLogStatus.FAILED.equals(afterTaskStatus)) {
				BmsLogPubTaskVo pubTaskLogVoForUpdata = new BmsLogPubTaskVo();
				pubTaskLogVoForUpdata.setTaskDetailId(confTaskDtlVo.getTaskDetailId());
				pubTaskLogVoForUpdata.setEntityId(entityId);
				if(ObjectUtils.isNotEmpty(yearMonth)){
					pubTaskLogVoForUpdata.setYearMonth(yearMonth);
				}
				if(ObjectUtils.isNotEmpty(bookCode)){
					pubTaskLogVoForUpdata.setBookCode(bookCode);
				}
				pubTaskLogVoForUpdata.setTaskMode(taskMode);
				pubTaskLogVoForUpdata.setTaskCode(taskCode);

				// 查询日志执行状态：1-执行中的功能日志
				pubTaskLogVoForUpdata.setTaskStatus(CommonConstant.ScheduleLogStatus.RUNNING);

				BaseResponse<BmsLogPubTaskVo> resultVo = bmsScheduleFeignClient.findTaskLog(pubTaskLogVoForUpdata);
				/**
				 * 业务方法执行完后：继续更新功能日志
				 */
				if(ObjectUtils.isNotEmpty(resultVo) && ObjectUtils.isNotEmpty(resultVo.getResData())){
					addPubTaskLogVo.setTaskLogId(resultVo.getResData().getTaskLogId());

					// 任务状态：2-成功
					addPubTaskLogVo.setTaskStatus(afterTaskStatus);
					// 记录任务执行结束时间
					addPubTaskLogVo.setEndTime(new Date());
					addPubTaskLogVo.setUpdatorId(userId);
					addPubTaskLogVo.setUpdateTime(new Date());

					// 记录异常信息
					if(taskDtlErrorMsg.length() > 0){
						// 记录异常发生信息、时间
						addPubTaskLogVo.setErrorTime(new Date());
						judgeResultMsg = judgeResultMsg + "; " + taskDtlErrorMsg;
					}

					judgeResultMsg = DataUtil.getMessageWithMaxLength(judgeResultMsg);
					// 记录不执行的信息及异常信息
					addPubTaskLogVo.setErrorMsg(judgeResultMsg);

					// 根据对象更新功能日志表
					bmsScheduleFeignClient.updateTaskLog(addPubTaskLogVo);
				}

			}

			/**
			 * 功能组日志处理：
			 *  1、自动/手动模式，在切面执行功能后更新完功能日志，同步更新功能组日志，如果功能执行失败，
			 *      更新功能组日志状态为失败，记录信息；
			 *  2、自动/手动模式，如果当前功能是最后一个功能，且执行成功，则更新功能组日志状态为2-成功。否则为1-处理中
			 */

			BaseResponse<BmsLogScheduleJobVo> jobLogByConfTaskId = bmsScheduleFeignClient.findScheduleJobLogInProgressByConfTaskId(confTaskDtlVo.getConfTaskId(),entityId);
			if(ObjectUtils.isNotEmpty(jobLogByConfTaskId) && ObjectUtils.isNotEmpty(jobLogByConfTaskId.getResData())) {
				BmsLogScheduleJobVo scheduleJobLogVo = jobLogByConfTaskId.getResData();
				// 功能执行失败或者不执行，则回写功能组日志
				if (CommonConstant.ScheduleLogStatus.NONEXEC.equals(afterTaskStatus) || CommonConstant.ScheduleLogStatus.FAILED.equals(afterTaskStatus)) {

					scheduleJobLogVo.setTaskStatus(afterTaskStatus);
					scheduleJobLogVo.setResData(judgeResultMsg);
					scheduleJobLogVo.setUpdatorId(userId);
					scheduleJobLogVo.setUpdateTime(new Date());

					// 更新功能组日志
					if (ObjectUtils.isNotEmpty(yearMonth)) {
						scheduleJobLogVo.setYearMonth(yearMonth);
					}
					if (ObjectUtils.isNotEmpty(bookCode)) {
						scheduleJobLogVo.setBookCode(bookCode);
					}
					BmsLogScheduleJob bmsLogScheduleJob = ClassUtil.convert(scheduleJobLogVo, BmsLogScheduleJob.class);
					bmsScheduleFeignClient.updateScheduleJobLog(bmsLogScheduleJob);

				} else if (CommonConstant.ScheduleLogStatus.SUCCESS.equals(afterTaskStatus)) {
					/**
					 * 功能组日志处理：
					 *      1、如果当前功能是最后一个功能，则设置功能组日志状态为2-已完成
					 *      2、然后触发下一个依赖它的自动任务执行
					 */

					BmsQrtzConfTaskDetailVo lastTaskDtlVo = bmsScheduleFeignClient.findLastTaskDtlByConfTaskId(entityId, confTaskDtlVo.getConfTaskId());
					// 最后一个功能完成后，更新功能组日志状态为2-已完成
					if (ObjectUtils.isNotEmpty(lastTaskDtlVo) && funcCode.equals(lastTaskDtlVo.getFuncCode())) {
						// 成功
						scheduleJobLogVo.setTaskStatus(afterTaskStatus);
						// 清除旧信息
						scheduleJobLogVo.setResCode(null);
						scheduleJobLogVo.setResData(null);
						// 最近一次成功结束时间
						scheduleJobLogVo.setLastEndDate(new Date());
						scheduleJobLogVo.setUpdatorId(userId);
						scheduleJobLogVo.setUpdateTime(new Date());

						// 更新功能组日志
						if (ObjectUtils.isNotEmpty(yearMonth)) {
							scheduleJobLogVo.setYearMonth(yearMonth);
						}
						if (ObjectUtils.isNotEmpty(bookCode)) {
							scheduleJobLogVo.setBookCode(bookCode);
						}
						BmsLogScheduleJob bmsLogScheduleJob = ClassUtil.convert(scheduleJobLogVo, BmsLogScheduleJob.class);
						bmsScheduleFeignClient.updateScheduleJobLog(bmsLogScheduleJob);


						// 查询该功能组作为被依赖的对象的任务【可能多个】，然后触发下一个依赖它的自动任务执行
						List<BmsQrtzConfTaskVo> taskVoRelationTaskList = bmsScheduleFeignClient.findConfTaskVoByEntityIdAndRelationTaskId(entityId, confTaskDtlVo.getConfTaskId());

						// 获取被依赖的功能组定时任务信息
						if(ObjectUtils.isNotEmpty(taskVoRelationTaskList)){
							for(BmsQrtzConfTaskVo taskVoRelationTaskVo: taskVoRelationTaskList){
								BmsScheduleDetail scheduleDetail = bmsScheduleFeignClient.findJobDetailsByEntityIdAndConfTaskId(entityId, taskVoRelationTaskVo.getConfTaskId());
								if(ObjectUtils.isNotEmpty(scheduleDetail)){
									BmsScheduleJobVo bmsScheduleJobVo = new BmsScheduleJobVo();
									bmsScheduleJobVo.setJobGroup(scheduleDetail.getJobGroup());
									bmsScheduleJobVo.setJobName(scheduleDetail.getJobName());
									// 立即触发依赖任务执行
									bmsScheduleFeignClient.immediateExecutionTask(bmsScheduleJobVo);
								}
							}
						}
					}
				}
			}

			// 若存在待执行的功能组任务日志，则更新日志状态为4-不执行
			BaseResponse<BmsLogScheduleJobVo> jobLogToExecByConfTaskId = bmsScheduleFeignClient.findScheduleJobLogToExecByConfTaskId(confTaskDtlVo.getConfTaskId(),entityId);
			if(ObjectUtils.isNotEmpty(jobLogToExecByConfTaskId) && ObjectUtils.isNotEmpty(jobLogToExecByConfTaskId.getResData())) {
				BmsLogScheduleJobVo scheduleJobLogVo = jobLogToExecByConfTaskId.getResData();

				// 更新功能组日志
				if (ObjectUtils.isNotEmpty(yearMonth)) {
					scheduleJobLogVo.setYearMonth(yearMonth);
				}
				if (ObjectUtils.isNotEmpty(bookCode)) {
					scheduleJobLogVo.setBookCode(bookCode);
				}
				// 已经执行过后，更新为其他待执行的状态为4-不执行
				scheduleJobLogVo.setTaskStatus(CommonConstant.ScheduleLogStatus.NONEXEC);
				BmsLogScheduleJob bmsLogScheduleJob = ClassUtil.convert(scheduleJobLogVo, BmsLogScheduleJob.class);
				bmsScheduleFeignClient.updateScheduleJobLog(bmsLogScheduleJob);
			}

		}

		// 业务API执行返回结果，如果这里不返回，则目标方法对象返回值会被置为null
		return returnResultsOfProceed;

	}

}