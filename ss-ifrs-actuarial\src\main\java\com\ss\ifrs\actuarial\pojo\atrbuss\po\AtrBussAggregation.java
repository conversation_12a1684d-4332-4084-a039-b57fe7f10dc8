package com.ss.ifrs.actuarial.pojo.atrbuss.po;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2022-09-09 15:52:23<br/>
 * Description: null<br/>
 * Table Name: BBS_CONF_LOA<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
public class AtrBussAggregation implements Serializable {
    /**
     * Database column: QTC_BUSS_AGGREGATION.QTC_AGGREGATION_ID
     * Database remarks: QTC_AGGREGATION_ID|主键
     */
    @ApiModelProperty(value = "QTC_AGGREGATION_ID|主键", required = true)
    private Long atrAggregationId;

    /**
     * Database column: QTC_BUSS_AGGREGATION.ENTITY_ID
     * Database remarks: entityId|业务单位
     */
    @ApiModelProperty(value = "entityId|业务单位", required = true)
    private Long entityId;

    /**
     * Database column: QTC_BUSS_AGGREGATION.YEAR_MONTH
     * Database remarks: year_month|业务年月
     */
    @ApiModelProperty(value = "year_month|业务年月", required = true)
    private String yearMonth;

    /**
     * Database column: QTC_BUSS_AGGREGATION.BUSINESS_SOURCE_CODE
     * Database remarks: null
     */
    private String businessSourceCode;

    /**
     * Database column: QTC_BUSS_AGGREGATION.EVALUATE_APPROACH
     * Database remarks: null
     */
    private String evaluateApproach;

    /**
     * Database column: QTC_BUSS_AGGREGATION.MODEL_DEF_ID
     * Database remarks: null
     */
    private Long modelDefId;

    /**
     * Database column: QTC_BUSS_AGGREGATION.LOA_CODE
     * Database remarks: null
     */
    private String loaCode;

    /**
     * Database column: QTC_BUSS_AGGREGATION.LOA_C_NAME
     * Database remarks: null
     */
    private String loaCName;

    /**
     * Database column: QTC_BUSS_AGGREGATION.LOA_L_NAME
     * Database remarks: null
     */
    private String loaLName;

    /**
     * Database column: QTC_BUSS_AGGREGATION.LOA_E_NAME
     * Database remarks: null
     */
    private String loaEName;

    private String newIs;

    /**
     * Database column: QTC_BUSS_AGGREGATION.SERIAL_NO
     * Database remarks: null
     */
    private Integer serialNo;


    private Long aggregationBussId;

    /**
     * Database column: QTC_BUSS_AGGREGATION.CREATOR_ID
     * Database remarks: creator_id|创建人id
     */
    @ApiModelProperty(value = "creator_id|创建人id", required = false)
    private Long creatorId;

    /**
     * Database column: QTC_BUSS_AGGREGATION.CREATE_TIME
     * Database remarks: create_Time|创建时间
     */
    @ApiModelProperty(value = "create_Time|创建时间", required = false)
    private Date createTime;




    private static final long serialVersionUID = 1L;

    public Long getAtrAggregationId() {
        return atrAggregationId;
    }

    public void setAtrAggregationId(Long atrAggregationId) {
        this.atrAggregationId = atrAggregationId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public String getLoaCName() {
        return loaCName;
    }

    public void setLoaCName(String loaCName) {
        this.loaCName = loaCName;
    }

    public String getLoaLName() {
        return loaLName;
    }

    public void setLoaLName(String loaLName) {
        this.loaLName = loaLName;
    }

    public String getLoaEName() {
        return loaEName;
    }

    public void setLoaEName(String loaEName) {
        this.loaEName = loaEName;
    }

    public Long getAggregationBussId() {
        return aggregationBussId;
    }

    public void setAggregationBussId(Long aggregationBussId) {
        this.aggregationBussId = aggregationBussId;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getEvaluateApproach() {
        return evaluateApproach;
    }

    public void setEvaluateApproach(String evaluateApproach) {
        this.evaluateApproach = evaluateApproach;
    }

    public Long getModelDefId() {
        return modelDefId;
    }

    public void setModelDefId(Long modelDefId) {
        this.modelDefId = modelDefId;
    }

    public Integer getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Integer serialNo) {
        this.serialNo = serialNo;
    }

    public String getNewIs() {
        return newIs;
    }

    public void setNewIs(String newIs) {
        this.newIs = newIs;
    }
}