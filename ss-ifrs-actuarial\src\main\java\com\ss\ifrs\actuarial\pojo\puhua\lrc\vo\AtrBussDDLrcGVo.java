/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-02-10 17:38:42
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.puhua.lrc.vo;

import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussDDLrcIcgCalcDetailVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-10 17:38:42<br/>
 * Description: LRC 计算结果主表(合同组维度，直保&临分分入)<br/>
 * Table Name: ATR_BUSS_DD_LRC_ICG_CALC<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "LRC 计算结果主表(合同组维度，直保&临分分入)")
public class AtrBussDDLrcGVo implements Serializable {
    /**
     * Database column: ATR_BUSS_DD_LRC_ICG_CALC.ID
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.ACTION_NO
     * Database remarks: 执行编号|一次操作的全局唯一编号， 一次操作的维度是模块（LIC或LRC）、业务类型、业务单位、业务年月、币别
     */
    @ApiModelProperty(value = "执行编号|一次操作的全局唯一编号， 一次操作的维度是模块（LIC或LRC）、业务类型、业务单位、业务年月、币别", required = true)
    private String actionNo;

    /**
     * Database column: ATR_BUSS_DD_LRC_ICG_CALC.TASK_CODE
     * Database remarks: 任务号|一次操作（提数+计算+输出）一个唯一的TASK_CODE
     */
    @ApiModelProperty(value = "任务号|一次操作（提数+计算+输出）一个唯一的TASK_CODE", required = true)
    private String taskCode;

    /**
     * Database column: ATR_BUSS_DD_LRC_ICG_CALC.CALC_TYPE
     * Database remarks: 计算类型|1-一般计算(包括应收保费、GEP、UEP等)； 2-CSM相关；3-UEP累计相关
     */
    @ApiModelProperty(value = "计算类型|1-一般计算(包括应收保费、GEP、UEP等)； 2-CSM相关；3-UEP累计相关", required = true)
    private String calcType;

    /**
     * Database column: ATR_BUSS_DD_LRC_ICG_CALC.CENTER_ID
     * Database remarks: 业务单位ID
     */
    @ApiModelProperty(value = "业务单位ID", required = true)
    private Long entityId;

    /**
     * Database column: ATR_BUSS_DD_LRC_ICG_CALC.CURRENCY
     * Database remarks: 币种
     */
    @ApiModelProperty(value = "币种", required = true)
    private String currencyCode;

    /**
     * Database column: ATR_BUSS_DD_LRC_ICG_CALC.YEAR_MONTH
     * Database remarks: 业务年月|评估期的年月
     */
    @ApiModelProperty(value = "业务年月|评估期的年月", required = true)
    private String yearMonth;

    /**
     * Database column: ATR_BUSS_DD_LRC_ICG_CALC.PORTFOLIO_NO
     * Database remarks: 合同组合号码
     */
    @ApiModelProperty(value = "合同组合号码", required = true)
    private String portfolioNo;

    /**
     * Database column: ATR_BUSS_DD_LRC_ICG_CALC.ICG_NO
     * Database remarks: 合同组号码
     */
    @ApiModelProperty(value = "合同组号码", required = true)
    private String icgNo;

    /**
     * Database column: ATR_BUSS_DD_LRC_ICG_CALC.EVALUATE_APPROACH
     * Database remarks: 评估方法
     */
    @ApiModelProperty(value = "评估方法", required = true)
    private String evaluateApproach;

    /**
     * Database column: ATR_BUSS_DD_LRC_ICG_CALC.MODEL_DEF_ID
     * Database remarks: 计量模型
     */
    @ApiModelProperty(value = "计量模型", required = true)
    private Long modelDefId;

    /**
     * Database column: ATR_BUSS_DD_LRC_ICG_CALC.LOA_CODE
     * Database remarks: LOA编码
     */
    @ApiModelProperty(value = "LOA编码", required = true)
    private String loaCode;


    private String entityCode;
    private String entityCName;
    private String entityLName;
    private String entityEName;

    private List<AtrBussDDLrcIcgCalcDetailVo> icgDetailList;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getCalcType() {
        return calcType;
    }

    public void setCalcType(String calcType) {
        this.calcType = calcType;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }	

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public String getEvaluateApproach() {
        return evaluateApproach;
    }

    public void setEvaluateApproach(String evaluateApproach) {
        this.evaluateApproach = evaluateApproach;
    }

    public Long getModelDefId() {
        return modelDefId;
    }

    public void setModelDefId(Long modelDefId) {
        this.modelDefId = modelDefId;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public String getEntityCode() {
        return entityCode;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public String getEntityCName() {
        return entityCName;
    }

    public void setEntityCName(String entityCName) {
        this.entityCName = entityCName;
    }

    public String getEntityLName() {
        return entityLName;
    }

    public void setEntityLName(String entityLName) {
        this.entityLName = entityLName;
    }

    public String getEntityEName() {
        return entityEName;
    }

    public void setEntityEName(String entityEName) {
        this.entityEName = entityEName;
    }

    public List<AtrBussDDLrcIcgCalcDetailVo> getIcgDetailList() {
        return icgDetailList;
    }

    public void setIcgDetailList(List<AtrBussDDLrcIcgCalcDetailVo> icgDetailList) {
        this.icgDetailList = icgDetailList;
    }
}