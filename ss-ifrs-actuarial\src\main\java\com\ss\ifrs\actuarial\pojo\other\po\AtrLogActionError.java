/**
 * 
 * This file was generated by Taiping MyBatis Generator(v1.2.12).
 * Date: 2024-06-13 15:35:27
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.other.po;

import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by Taiping MyBatis Generator(v1.2.12).
 * Create Date: 2024-06-13 15:35:27<br/>
 * Description: null<br/>
 * Table Name: atr_log_action_error<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
public class AtrLogActionError implements Serializable {
    /**
     * Database column: atr_log_action_error.id
     * Database remarks: null
     */
    private Long id;

    /**
     * Database column: atr_log_action_error.action_no
     * Database remarks: null
     */
    private String actionNo;

    /**
     * Database column: atr_log_action_error.mark
     * Database remarks: null
     */
    private String mark;

    /**
     * Database column: atr_log_action_error.error_msg
     * Database remarks: null
     */
    private String errorMsg;

    /**
     * Database column: atr_log_action_error.create_time
     * Database remarks: null
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public String getMark() {
        return mark;
    }

    public void setMark(String mark) {
        this.mark = mark;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}