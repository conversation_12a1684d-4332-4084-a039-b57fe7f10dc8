package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaDefVo;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrExcelCellVo;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrExcelHeaderVo;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrExcelVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 *
 * Excel导入、导出配置接口
 *
 */
public interface AtrConfExcelService {


    /**
     * @Method exportExcel 导出Excel表格
     * <AUTHOR>
     * @Date 2022/9/5
     * @Description 导出Excel表格
     * @param response 响应
     * @param atrExcelVo Excel表信息（文件名、语言和表头信息）
     * @param objList 数据List
     * @Throws 抛出异常
     * @Return
     */
    void exportExcel(HttpServletResponse response, AtrExcelVo atrExcelVo, List<Object> objList) throws Exception;

    /**
     * @Method generateExcelCell 生成Excel所有单元格信息
     * <AUTHOR>
     * @Date 2022/9/5
     * @Description 通过表头信息（属性、数据类型等）遍历源数据，组装到所有的单元格信息内
     * @param headerCells Excel表头信息List
     * @param objList 源数据信息List
     * @param language 语言
     * @Return
     */
    List<AtrExcelCellVo> generateExcelCell(List<AtrExcelHeaderVo> headerCells, List<Object> objList, String language);

    List<AtrExcelCellVo> generateCells(List<AtrConfQuotaDefVo> atrConfQuotaDefVos, String language);
}
