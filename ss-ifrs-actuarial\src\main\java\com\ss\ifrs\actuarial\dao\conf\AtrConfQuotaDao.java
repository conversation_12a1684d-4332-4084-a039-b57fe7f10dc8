/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2022-09-16 14:50:26
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao.conf;


import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuota;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.*;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrExcelParseVo;
import com.ss.library.mybatis.interfaces.IDao;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2022-09-16 14:50:26<br/>
 * Description: 指标配置表 Dao类<br/>
 * Related Table Name: ATR_CONF_QUOTA<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrConfQuotaDao extends IDao<AtrConfQuota, Long> {
    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/13
     * @Description 模糊查询
     * @Return
     */
    Page<Map<String,Object>> fuzzySearchPage(AtrConfQuotaVo bbsConfQuotaVo, Pageable pageable);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 修改审核状态
     * @Return
     */
    void updateAudit(AtrConfQuotaVo bbsBussQuotaVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 激活或禁用
     * @Return
     */
    void updateValid(AtrConfQuotaVo bbsConfQuotaVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/17
     * @Description 更新指标值和审核状态
     * @Return
     */
    void updateValue(AtrConfQuotaVo bbsConfQuotaVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 根据Vo对象查询列表
     * @Return
     */
    List<AtrConfQuotaVo> findListByVo(AtrConfQuotaVo bbsConfQuotaVo);


    List<AtrConfQuotaVo> selectQuotaValue(AtrConfQuotaVo bbsConfQuotaVo);
    
    /**  无用代码
     * @Method
     * <AUTHOR>
     * @Date 2021/11/15
     * @Description 查找指标定义Map
     * @Return
     */
    List<AtrConfQuotaDefVo> findDefMap(AtrConfQuotaVo bbsConfQuotaVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/15
     * @Description 查找指标配置Vo
     * @Return
     */
    List<AtrConfQuotaVo> findVoBy(AtrConfQuotaVo bbsConfQuotaVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/16
     * @Description 查找指标定义Vo列报
     * @Return
     */
    List<AtrConfQuotaDefVo> findConfQuotaVo(AtrConfQuotaVo bbsConfQuotaVo);


    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/16
     * @Description 查找发展期Vo列表
     * @Return
     */
    List<AtrConfQuotaPeriodVo> findConfQuotaPeriod(AtrConfQuotaVo bbsConfQuotaVo);
    
    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/24
     * @Description 查找发展期Vo列表
     * @Return
     */
    List<AtrConfQuotaPeriodVo> findConfQuotaPeriodByAdd();

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/16
     * @Description 查找发展期Vo列报
     * @Return
     */
    List<String> findConfQuotaPeriodValue(AtrConfQuotaVo bbsConfQuotaVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/24
     * @Description 查找发展期Vo列报
     * @Return
     */
    List<String> findConfQuotaPeriodValueByAdd();


    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/16
     * @Description 查找指标定义Vo列报
     * @Return
     */
    List<AtrConfQuotaDefVo> findQuotaByAdd(AtrConfQuotaVo bbsConfQuotaVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/16
     * @Description 查找发展期Vo列报
     * @Return
     */
    //List<AtrConfQuotaDefVo> findQuotaPeriodByAdd();

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/23
     * @Description 根据指标类型查找List
     * @Return
     */
    List<AtrConfQuota> findListByVoAndType(AtrConfQuotaVo bbsConfQuotaVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/23
     * @Description 根据指标类型查找List
     * @Return
     */
    AtrConfQuota findByUnique(AtrConfQuotaVo vo);

    /**
     * <AUTHOR>
     * @Date 2021/12/27
     * @Description 根据指标类型查找List
     * @Return
     */
    AtrConfQuota findByUniqueLoa(AtrConfQuotaVo vo);


    List<AtrConfQuotaDetailVo> findQuotaDetailByVo(AtrConfQuotaVo bbsConfQuotaVo);

    /*
    * 查询当前最大版本号
    * */
    Integer findMaxSerialNo(AtrConfQuotaVo bbsConfQuotaVo);

    /**
     * @Method
     * @Date 2021/11/23
     * @Description 根据假设值审计信息
     */
    AtrConfQuotaVo findAuditInformation(AtrConfQuotaVo vo);


    List<AtrConfQuotaImportVo> findImportQuotaList(AtrConfQuotaImportVo atrConfQuotaImportVo);


    boolean hasSameQuota(AtrConfQuota atrConfQuota);

    List<AtrConfQuotaDefVo> findConfQuotaDefList(AtrConfQuotaImportVo atrConfQuotaImportVo);

    void saveTableData(@Param("lists") List<List<AtrExcelParseVo>> lists);


    /**
     * @Date 2022/12/01
     * @Description 根据假设值维度信息删除
     */
    List<AtrConfQuota> selectByLoaVo(AtrConfQuota bbsConfQuota);
    /**
     * @Date 2022/12/01
     * @Description 根据假设值维度信息删除
     */
    void deleteByLoaDimension(AtrConfQuota bbsConfQuota);


    /**
     * @Date 2022/12/01
     * @Description 根据假设值维度信息查询
     */
    List<AtrConfQuota> findListByDimensionVo(AtrConfQuota bbsConfQuota);

    /**
     * @Date 2022/12/01
     * @Description 根据假设值维度信息删除
     */
    void deleteByDimensionVo(AtrConfQuota bbsConfQuota);

    /**
     * @Date 2022/12/01
     * @Description 根据假设值维度信息删除
     */
    void deleteByOtherModel(AtrConfQuota bbsConfQuota);

    Long countQuota(AtrConfQuota bbsConfQuota);

    String selectPreviousPeriod(AtrConfQuota bbsConfQuota);

    void loadPrePeriodQuota(AtrConfQuotaVo bbsConfQuota);

    void syncQuotaClassByQuotaDef(AtrConfQuotaDefVo confQuotaDefVo);

    List<AtrConfQuotaVo> findAtrConfQuotaList(AtrConfQuotaImportVo atrConfQuotaImportVo);

    List<AtrConfQuotaVo> findAtrConfQuotaDetailList(AtrConfQuotaImportVo atrConfQuotaImportVo);
}