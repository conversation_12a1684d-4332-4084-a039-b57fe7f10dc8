package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to;

import com.ss.ifrs.actuarial.util.abp.IgnoreCol; // 保留 import
import com.ss.ifrs.actuarial.util.abp.Tab;     // 保留 import
import lombok.Data;
import java.math.BigDecimal; // 引入 BigDecimal
import java.util.HashMap;    // 保留 import
import java.util.Map;        // 保留 import

/**
 * LRC 主表 (合同组维度, 合约分出) - 对应表 atr_buss_to_lrc_g
 */
@Tab("atr_buss_to_lrc_g")
@Data
public class AtrBussLrcToIcg {

    /** ID (id) */
    private Long id; // number(11) -> Long

    /** 执行编号 (action_no) */
    private String actionNo; // varchar2(32) -> String

    /** 业务单位ID (entity_id) */
    private Long entityId; // number(11) -> Long

    /** 业务年月 (year_month) - 备注: 评估期的年月 */
    private String yearMonth; // varchar2(6) -> String

    /** 合同组合号码 (portfolio_no) */
    private String portfolioNo; // varchar2(64) -> String

    /** 合同组号码 (icg_no) */
    private String icgNo; // varchar2(60) -> String

    /** 总保费 (total_premium) */
    private BigDecimal totalPremium; // 新增字段, decimal(21, 4) -> BigDecimal

    /** 总净额结算手续费 (total_net_fee) */
    private BigDecimal totalNetFee; // 新增字段, decimal(21, 4) -> BigDecimal

    /** 当期确认的投资成分 (inv_amount) */
    private BigDecimal invAmount; // number(21, 4) -> BigDecimal (已有字段，类型确认)

    private String businessType;


    // --- 保留 @IgnoreCol 字段 ---
    @IgnoreCol
    private Map<Integer, BigDecimal> devEdPremiumMap = new HashMap<>();

    @IgnoreCol
    private Map<Integer, BigDecimal> devRecvPremiumMap = new HashMap<>();

    @IgnoreCol
    private Map<Integer, BigDecimal> devEdNetFeeMap = new HashMap<>();

    @IgnoreCol
    private Map<Integer, BigDecimal> devNetFeeCfMap = new HashMap<>();

    @IgnoreCol
    private int maxDevNo;

}