package com.ss.ifrs.actuarial.dao;

import com.ss.ifrs.actuarial.pojo.ecf.vo.AtrBussExch;
import com.ss.ifrs.actuarial.pojo.ecf.vo.AtrBussQuota;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Mapper
public interface AtrBussEcfDao {

    String getBaseCurrency(@Param("entityId") Long entityId);

    List<AtrBussExch> getExchList(@Param("entityId") Long entityId,
                                  @Param("deadline") Date deadline,
                                  @Param("baseCurrencyCode") String baseCurrencyCode);

    void collectQuotaDef(@Param("actionNo") String actionNo, @Param("quotaCodes") Set<String> quotaCodes);

    List<AtrBussQuota> findQuota(@Param("actionNo") String actionNo);

    Integer getConfParts(@Param("yearMonth") String yearMonth, 
                        @Param("businessSourceCode") String businessSourceCode,
                        @Param("mode") String mode);
    void collectQuotaForICG(Map<String, Object> paramMap);
    void collectQuotaForICGDev(Map<String, Object> paramMap);

}
