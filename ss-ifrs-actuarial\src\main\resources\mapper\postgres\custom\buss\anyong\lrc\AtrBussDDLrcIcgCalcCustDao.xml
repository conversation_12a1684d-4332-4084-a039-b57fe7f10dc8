<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-02-10 17:38:42 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussDDLrcIcgCalcDao">
  <!-- 本配置文件由GIS MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**IDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->
  <resultMap id="CustResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussDDLrcIcgCalcVo">
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="ACTION_NO" property="actionNo" jdbcType="VARCHAR" />
    <result column="TASK_CODE" property="taskCode" jdbcType="VARCHAR" />
    <result column="CALC_TYPE" property="calcType" jdbcType="VARCHAR" />
    <result column="entity_id" property="entityId" jdbcType="DECIMAL" />
    <result column="CURRENCY_CODE" property="currencyCode" jdbcType="VARCHAR" />
    <result column="YEAR_MONTH" property="yearMonth" jdbcType="VARCHAR" />
    <result column="PORTFOLIO_NO" property="portfolioNo" jdbcType="VARCHAR" />
    <result column="ICG_NO" property="icgNo" jdbcType="VARCHAR" />
    <result column="EVALUATE_APPROACH" property="evaluateApproach" jdbcType="VARCHAR" />
    <result column="LOA_CODE" property="loaCode" jdbcType="VARCHAR" />

    <result column="entity_code" property="entityCode" jdbcType="VARCHAR" />
    <result column="entity_c_name" property="entityCName" jdbcType="VARCHAR" />
    <result column="entity_l_name" property="entityLName" jdbcType="VARCHAR" />
    <result column="entity_e_name" property="entityEName" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Cust_Column_List">
    a.ID, a.TASK_CODE, a.CALC_TYPE, a.entity_id, a.currency_code, a.YEAR_MONTH, a.PORTFOLIO_NO, a.ICG_NO,
    a.EVALUATE_APPROACH,a.LOA_CODE,
    c.entity_e_name, c.entity_c_name, c.entity_l_name
  </sql>

  <select id="findByVo" flushCache="false" useCache="true" resultMap="CustResultMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussDDLrcIcgCalcVo">
    select
    <include refid="Cust_Column_List" />
    from ATR_BUSS_DD_LRC_ICG_CALC a
    left join bpluser.bbs_conf_entity c on a.entity_id = c.entity_id
    left join bpluser.BBS_CONF_CURRENCY cur on a.currency_code = cur.CURRENCY_CODE
    where a.TASK_CODE = #{taskCode,jdbcType=VARCHAR}
  </select>

  <select id="findDateByVo" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
    select
    a.ID as "id",
    a.ACTION_NO as "actionNo",
    a.TASK_CODE as "taskCode",
    a.entity_id as "entityId",
    a.currency_code as "currency",
    a.YEAR_MONTH as "yearMonth",
    a.PORTFOLIO_NO as "portfolioNo",
    a.ICG_NO as "icgNo",
    a.EVALUATE_APPROACH as "evaluateApproach",
    a.LOA_CODE as "loaCode",
    c.entity_code as "entityCode",
	c.entity_e_name as "entityEName",
	c.entity_c_name as "entityCName",
	c.entity_l_name  as "entityLName"
    from ATR_BUSS_DD_LRC_ICG_CALC a
    left join bpluser.bbs_conf_entity c on a.entity_id = c.entity_id
    left join bpluser.BBS_CONF_CURRENCY cur on a.currency_code = cur.CURRENCY_CODE
    where a.action_no = #{actionNo,jdbcType=VARCHAR}
    <if test="portfolioNo != null and portfolioNo != ''">
      and a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
    </if>
    <if test="icgNo != null and icgNo != ''">
      and a.icg_no = #{icgNo,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="findLrcIcgDetail" fetchSize="1000" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
    select
    a.ACTION_NO as "actionNo",
    a.YEAR_MONTH as "yearMonth",
    a.PORTFOLIO_NO as "portfolioNo",
    a.ICG_NO as "icgNo",
    a.LOA_CODE as "loaCode",
    to_char(a.CONTRACT_DATE,'yyyy/mm/dd') as "contractDate",
    a.currency_code AS "currencyCode",
    a.remaining_months AS "remainingMonths",
    c.entity_code as "entityCode",
    c.entity_e_name as "entityEName"
    <if test="null != devNoList and devNoList.size > 0">
      <foreach collection="devNoList" item="item" index="index" open=","  separator=",">
        MAX(CASE cd.DEV_NO WHEN ${item} THEN cd.${feeType} ELSE NULL END) AS "${item}"
      </foreach>
    </if>
    FROM atruser.ATR_BUSS_DD_LRC_ICG_CALC a
    left join atruser.ATR_BUSS_DD_LRC_ICG_CALC_DETAIL cd on a.id = cd.main_id
    left join bpluser.bbs_conf_entity c on a.entity_id = c.entity_id
    where a.action_no = #{actionNo,jdbcType=VARCHAR}
    <if test="portfolioNo != null and portfolioNo != ''">
      and a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
    </if>
    <if test="icgNo != null and icgNo != ''">
      and a.icg_no = #{icgNo,jdbcType=VARCHAR}
    </if>
    GROUP BY
    a.ACTION_NO,
    a.currency_code,
    a.YEAR_MONTH,
    a.PORTFOLIO_NO,
    a.ICG_NO,
    a.LOA_CODE,
    to_char(a.CONTRACT_DATE,'yyyy/mm/dd'),
    a.remaining_months,
    c.entity_code  ,
    c.entity_e_name,
    a.id
    order by a.id
  </select>
</mapper>