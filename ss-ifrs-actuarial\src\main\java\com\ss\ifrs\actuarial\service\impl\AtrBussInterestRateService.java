package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.dao.AtrBussEcfDao;
import com.ss.ifrs.actuarial.dao.AtrBussInterestRateDao;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfCbTreasuryYc;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestBase;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestRateDetail;
import com.ss.ifrs.actuarial.pojo.ecf.vo.ir.AtrBussIrInitial;
import com.ss.ifrs.actuarial.pojo.ecf.vo.ir.AtrBussIrInitialDev;
import com.ss.ifrs.actuarial.pojo.ecf.vo.ir.AtrBussIrWeight;
import com.ss.ifrs.actuarial.pojo.ecf.vo.ir.AtrBussIrWeightDev;
import com.ss.ifrs.actuarial.pojo.ecf.vo.ir.AtrConfIr;
import com.ss.ifrs.actuarial.pojo.ecf.vo.ir.AtrDuctWeightBussData;
import com.ss.ifrs.actuarial.pojo.ecf.vo.ir.AtrDuctWeightRateBase;
import com.ss.ifrs.actuarial.util.Dates;
import com.ss.ifrs.actuarial.util.EcfUtil;
import com.ss.ifrs.actuarial.util.abp.AsyncBatchProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class AtrBussInterestRateService {

    @Resource
    private AtrBussInterestRateDao atrBussInterestRateDao;

    @Resource
    private AtrBussEcfDao atrBussEcfDao;

    @Resource
    private JdbcTemplate jdbcTemplate;

    public void calcForwardAndInitail(String yearMonth) {
        Long uploadRateId = atrBussInterestRateDao.findUploadRateIdForYearMonth(yearMonth);
        if (uploadRateId == null) {
            throw new RuntimeException("没有上传利率配置 " + yearMonth);
        }
        calcForwardAndInitail(uploadRateId);
//        atrBussInterestRateDao.confirmForward(uploadRateId);
        atrBussInterestRateDao.confirmInitial(uploadRateId);
    }

    public void calcForwardAndInitail(Long uploadRateId) {
        calcInitial(uploadRateId);
    }

    public void calcWeight(Long entityId, String yearMonth) {
        atrBussInterestRateDao.deleteWeightDev(entityId, yearMonth);
        atrBussInterestRateDao.deleteWeight(entityId, yearMonth);
        long mainId = atrBussInterestRateDao.getMaxWeightId();
        String baseCurrency = atrBussEcfDao.getBaseCurrency(entityId);
        String preYearMonth = Dates.preYearMonth(yearMonth);

        // 获取当期初始利率
        Long uploadId = atrBussInterestRateDao.getInitialUploadId(entityId, preYearMonth, baseCurrency);
        if (uploadId == null) {
            throw new RuntimeException(String.format("没有配置上期初始利率， year_month = %s, '， currency_code = %s",
                    yearMonth, baseCurrency));
        }
        Long initialUploadId = atrBussInterestRateDao.getInitialUploadId(entityId, yearMonth, baseCurrency);
        if ( initialUploadId == null){
            throw new RuntimeException(String.format("没有配置当期初始利率， year_month = %s, '， currency_code = %s",
                    yearMonth, baseCurrency));
        }
        try (AsyncBatchProcessor abp = new AsyncBatchProcessor(jdbcTemplate, EcfUtil.THREADS_DB)) {
            abp.addType(AtrBussIrWeight.class);
            abp.addType(AtrBussIrWeightDev.class);

            // 1. 业务数据获取
            List<AtrDuctWeightBussData> bussDatas = atrBussInterestRateDao.collectWeightData(entityId, yearMonth);
            
            // 2. 获取当前月份的monthly_forward_rate_with_premium
            List<AtrConfInterestRateDetail> forwardRateDetails = atrBussInterestRateDao.findMonthlyForwardRatesByUploadId(uploadId);
            if (forwardRateDetails == null || forwardRateDetails.isEmpty()) {
                throw new RuntimeException("未找到月度远期利率数据，uploadId=" + uploadId);
            }
            
            // 将月度远期利率放入Map，以便按normMonth查找
            Map<Integer, BigDecimal> monthlyForwardRateMap = new HashMap<>();
            for (AtrConfInterestRateDetail detail : forwardRateDetails) {
                if (detail.getMonthlyForwardRateWithPremium() != null) {
                    monthlyForwardRateMap.put(detail.getNormMonth().intValue(), detail.getMonthlyForwardRateWithPremium());
                }
            }
            
            // 3. 从上一期的计算结果中获取数据（如果有的话）
            Map<String, Map<Integer, Double>> previousCalculationMap = new HashMap<>();
            try {
                List<AtrDuctWeightRateBase> preWeightRateBases = 
                    atrBussInterestRateDao.collectWeightRateBase(entityId, preYearMonth, baseCurrency);
                
                if (preWeightRateBases != null && !preWeightRateBases.isEmpty()) {
                    // 按照合同组编号分组存储上一期的计算结果
                    for (AtrDuctWeightRateBase rateBase : preWeightRateBases) {
                        if (!previousCalculationMap.containsKey(rateBase.getIcgNo())) {
                            previousCalculationMap.put(rateBase.getIcgNo(), new HashMap<>());
                        }
                        previousCalculationMap.get(rateBase.getIcgNo()).put(rateBase.getDevNo(), rateBase.getRateBase());
                    }
                }
            } catch (Exception e) {
                // 如果是第一期计算，可能没有上一期的数据，忽略异常
                log.warn("获取上一期权重基础利率数据失败，可能是首次计算: {}" , e.getMessage());
            }
            
            // 4. 进行实际计算
            Date now = new Date();

            // 按合同组分组存储计算结果
            Map<String, Map<Integer, Double>> currentCalculationMap = new HashMap<>();
            
            // 为每个合同组创建主表记录
            for (AtrDuctWeightBussData bussData : bussDatas) {

                
                // 创建主表记录
                AtrBussIrWeight weightRecord = new AtrBussIrWeight();
                weightRecord.setId(++mainId);
                weightRecord.setUploadRateId(initialUploadId);
                weightRecord.setEntityId(entityId);
                weightRecord.setYearMonth(yearMonth);
                weightRecord.setCurrencyCode(baseCurrency);
                weightRecord.setIcgNo(bussData.getIcgNo());

                weightRecord.setPremium(BigDecimal.valueOf(bussData.getNewPremium()));
                
                weightRecord.setCreateTime(now);
                

                if (bussData.getBusinessSourceCode() != null) {
                    weightRecord.setBusinessSourceCode(bussData.getBusinessSourceCode());
                }
                if (bussData.getActionNo() != null) {
                    weightRecord.setActionNo(bussData.getActionNo());
                }
                if (bussData.getPortfolioNo() != null) {
                    weightRecord.setPortfolioNo(bussData.getPortfolioNo());
                }
                
                abp.insert(weightRecord);

                // 创建发展期记录
                Map<Integer, Double> icgCalculationMap = new HashMap<>();
                currentCalculationMap.put(bussData.getIcgNo(), icgCalculationMap);
                
                // 存储上一期发展期的rate_e值，用于下一期的rate_b
                BigDecimal previousRateE = BigDecimal.ZERO;
                
                // 计算每个发展期的数据
                for (int devNo = 0; devNo <= 600; devNo++) {
                    AtrBussIrWeightDev devRecord = new AtrBussIrWeightDev();
                    devRecord.setMainId(weightRecord.getId());
                    devRecord.setDevNo(devNo);
                    
                    // 按照图示计算规则进行计算
                    BigDecimal rateBase;
                    BigDecimal rateE;
                    
                    // rate_b等于上一期的rate_e
                    BigDecimal rateB = previousRateE;
                    
                    // 首先处理第0个月的情况
                    /*if (devNo == 0) {
                        rateBase = BigDecimal.ZERO;
                        // 第0期，rateE = 1/(1+rateBase) = 1/(1+0) = 1
                        rateE = BigDecimal.ONE;
                    } else {*/
                    // 计算当前月的rateBase
                    BigDecimal newPremiumBd = BigDecimal.valueOf(bussData.getNewPremium());
                    BigDecimal oldPremiumBd = bussData.getOldPremium() != null ? BigDecimal.valueOf(bussData.getOldPremium()) : BigDecimal.ZERO;


                    // 获取当前月的远期利率
                    BigDecimal currentMonthRate = monthlyForwardRateMap.getOrDefault(devNo+1, BigDecimal.ZERO);

                    // 计算分子和分母
                    BigDecimal numerator = newPremiumBd.multiply(currentMonthRate);
                    BigDecimal denominator = newPremiumBd;

                    // 如果这个合同组存在上一期的权重数据，则还需要加上上一期的
                    // 从上一期获取下一个发展期的rateBase
                    double prevRateBase = previousCalculationMap.containsKey(bussData.getIcgNo()) ?
                            previousCalculationMap.get(bussData.getIcgNo()).getOrDefault(devNo + 1, 0.0) : 0.0;

                    if (oldPremiumBd.compareTo(BigDecimal.ZERO) > 0) {
                        // 使用上一期的上一个发展期的rateBase
                        BigDecimal prevRateBaseBd = new BigDecimal(prevRateBase);
                        numerator = numerator.add(oldPremiumBd.multiply(prevRateBaseBd));
                        denominator = denominator.add(oldPremiumBd);
                    }

                    // 计算rateBase
                    if (denominator.compareTo(BigDecimal.ZERO) > 0) {
                        rateBase = numerator.divide(denominator, 15, RoundingMode.HALF_UP);
                    } else {
                        rateBase = currentMonthRate;
                    }

                    // 存储计算结果供后续使用
                    icgCalculationMap.put(devNo, rateBase.doubleValue());

                    // 计算rateE = 1/(1+rateBase)
                    rateE = BigDecimal.ONE.divide(BigDecimal.ONE.add(rateBase), 15, RoundingMode.HALF_UP);
//                    }
                    
                    // 设置计算结果
                    devRecord.setRateBase(rateBase);
                    devRecord.setRateB(rateB);  // 使用上一期的rate_e值
                    devRecord.setRateE(rateE);
                    
                    // 保存当前的rateE，用于下一期的rateB
                    previousRateE = rateE;
                    
                    // 批量插入
                    abp.insert(devRecord);
                }
            }
            
            abp.end();
        } catch (Exception e) {
            throw new RuntimeException("计算权重失败: " + e.getMessage(), e);
        }
    }

    private void calcInitial(Long interestRateId) {
        try (AsyncBatchProcessor abp = new AsyncBatchProcessor(jdbcTemplate, EcfUtil.THREADS_DB)) {
            abp.addType(AtrBussIrInitial.class);
            abp.addType(AtrBussIrInitialDev.class);
            abp.addType(AtrConfInterestRateDetail.class);

            // 删除旧数据
            atrBussInterestRateDao.deleteInitialDev(interestRateId);
            atrBussInterestRateDao.deleteInitial(interestRateId);
            atrBussInterestRateDao.deleteInterestRateDetail(interestRateId);

            // 获取新的主表ID
            long mainId = atrBussInterestRateDao.getMaxInitialId() + 1;

            // 获取主表数据
            AtrConfIr confMain = atrBussInterestRateDao.findConfMain(interestRateId);
            if (confMain == null) {
                throw new RuntimeException("未找到利率主表数据, interestRateId=" + interestRateId);
            }

            // 获取基础参数数据
            BigDecimal liquidityPremiumBase = getBigDecimalFromInterestBase("LIQUIDITY_PREMIUM");
            BigDecimal ultimateYear = getBigDecimalFromInterestBase("ULTIMATE_YEAR");
            BigDecimal finalLiquidityPeriod = getBigDecimalFromInterestBase("FINAL_LIQUIDITY_PERIOD");
            BigDecimal ultimateRate = getBigDecimalFromInterestBase("ULTIMATE_RATE");
            BigDecimal ultimateLiquidityPremium = getBigDecimalFromInterestBase("ULTIMATE_LIQUIDITY_PREMIUM");

            // 获取中债国债收益率曲线数据
            List<AtrConfCbTreasuryYc> cbTreasuryYcList = atrBussInterestRateDao.findCbTreasuryYcByInterestRateId(interestRateId);
            if (cbTreasuryYcList == null || cbTreasuryYcList.isEmpty()) {
                throw new RuntimeException("未找到国债收益率曲线数据, interestRateId=" + interestRateId);
            }

            // 创建利率明细对象列表
            List<AtrConfInterestRateDetail> detailList = new ArrayList<>();

            // 创建索引用于查找最近的曲线数据
            Map<BigDecimal, AtrConfCbTreasuryYc> tenorYearsMap = new HashMap<>();
            Map<BigDecimal, AtrConfCbTreasuryYc> revTenorYearsMap = new HashMap<>();

            // 获取最大标准期限年
            BigDecimal maxTenorYears = BigDecimal.ZERO;

            // 填充索引
            for (AtrConfCbTreasuryYc yc : cbTreasuryYcList) {
                if (yc.getTenorYears() != null) {
                    tenorYearsMap.put(yc.getTenorYears(), yc);
                    if (yc.getTenorYears().compareTo(maxTenorYears) > 0) {
                        maxTenorYears = yc.getTenorYears();
                    }
                }
                if (yc.getRevTenorYears() != null) {
                    revTenorYearsMap.put(yc.getRevTenorYears(), yc);
                }
            }

            // 初始化流动性溢价
            BigDecimal liquidityPremium = liquidityPremiumBase.divide(BigDecimal.valueOf(10000), 15, RoundingMode.HALF_UP);

            // 创建主表记录
            AtrBussIrInitial vo = new AtrBussIrInitial();
            vo.setId(mainId);
            vo.setYearMonth(confMain.getYearMonth());
            vo.setConfirmIs("0");
            vo.setUploadRateId(interestRateId);
            vo.setCreateTime(new Date());
            vo.setEntityId(confMain.getEntityId());
            vo.setCurrencyCode("CNY"); // 设置默认货币代码为CNY
            abp.insert(vo);
            long maxInterestRateDetailId = atrBussInterestRateDao.getMaxInterestRateDetailId() + 1;
            
            // 存储即期折现因子，用于后续转存到atr_buss_ir_initial_dev表
            Map<Integer, BigDecimal> spotDiscountFactorMap = new HashMap<>();
            
            // 计算从0到600个月的利率明细
            for (int i = 0; i <= 600; i++) {
                AtrConfInterestRateDetail detail = new AtrConfInterestRateDetail();

                // 设置基本字段
                detail.setInterestRateDetailId(maxInterestRateDetailId + i+1); // 设置唯一ID
                detail.setInterestRateId(interestRateId);
                detail.setNormMonth(Short.valueOf(String.valueOf(i)));
                detail.setCreatorId(confMain.getCreatorId());
                detail.setCreateTime(new Date());

                // 计算标准期限（年）
                BigDecimal normTime = BigDecimal.valueOf(i).divide(BigDecimal.valueOf(12), 15, RoundingMode.HALF_UP);
                detail.setNormTime(normTime);

                // 第0期特殊处理
                if (i == 0) {
                    detail.setNormMonth((short) 0);
                    detail.setNormTime(BigDecimal.ZERO);
                    // I列：interpolated_spot_rate
                    detail.setInterpolatedSpotRate(BigDecimal.ZERO);
                    // L列：annual_spot_rate_with_premium
                    detail.setAnnualSpotRateWithPremium(BigDecimal.ZERO);
                    // 计算M列：monthly_spot_rate_with_premium（根据L列）
                    detail.setMonthlySpotRateWithPremium(BigDecimal.ZERO);
                    // O列：spot_discount_factor
                    detail.setSpotDiscountFactor(BigDecimal.ONE);
                    
                    // 存储第0期的即期折现因子
                    spotDiscountFactorMap.put(i, BigDecimal.ONE);

                    detailList.add(detail);
                    continue;
                }

                // 如果标准期限小于等于20年，执行插值计算
                if (normTime.compareTo(BigDecimal.valueOf(20)) <= 0) {
                    // 查找最近的低值和高值
                    AtrConfCbTreasuryYc lowYc = findNearest(tenorYearsMap, normTime, true);

                    if (lowYc != null) {
                        detail.setLowValue(lowYc.getTenorYears());
                        detail.setLowProfitRate(lowYc.getAvgValue());
                    }

                    // 查找最近的高值
                    AtrConfCbTreasuryYc highYc = findNearest(revTenorYearsMap, normTime, false);

                    if (highYc != null) {
                        AtrConfCbTreasuryYc nearest = findNearest(tenorYearsMap, highYc.getRevTenorYears(), false);
                        detail.setHighValue(highYc.getRevTenorYears());
                        detail.setHighProfitRate(nearest.getAvgValue());
                        /*int matchPosition = -1;
                        int count = 0;
                        // 过滤并找出标准期限小于等于20年中的最大tenorYears值
                        List<AtrConfCbTreasuryYc> filteredYcList = new ArrayList<>();
                        for (AtrConfCbTreasuryYc yc : cbTreasuryYcList) {
                            if (yc.getTenorYears() != null && yc.getTenorYears().compareTo(BigDecimal.valueOf(20)) <= 0) {
                                filteredYcList.add(yc);
                                count++;
                            }
                        }

                        // 查找当前normTime在revTenorYears中的匹配位置(MATCH函数)
                        List<BigDecimal> revTenorYearsList = new ArrayList<>(revTenorYearsMap.keySet());
                        Collections.sort(revTenorYearsList);

                        for (int j = 0; j < revTenorYearsList.size(); j++) {
                            if (highYc.getTenorYears().compareTo(revTenorYearsList.get(j)) <= 0) {
                                matchPosition = j;
                                break;
                            }
                        }

                        if (matchPosition == -1 && !revTenorYearsList.isEmpty()) {
                            matchPosition = revTenorYearsList.size() - 1;
                        }

                        // 计算VLOOKUP的查找索引: MAX()-MATCH()
                        int lookupIndex = count - matchPosition;
                        // 在过滤后的列表中查找对应的avgValue
                        for (AtrConfCbTreasuryYc yc : filteredYcList) {
                            if (yc.getTenorYears() != null && yc.getTenorYears().compareTo(revTenorYearsList.get(lookupIndex - 1)) == 0) {
                                detail.setHighProfitRate(yc.getAvgValue());
                                highYc.setAvgValue(yc.getAvgValue());
                                break;
                            }
                        }

                        // 如果未找到匹配值，使用原始的highYc的avgValue
                        if (detail.getHighProfitRate() == null) {
                            detail.setHighProfitRate(highYc.getAvgValue());
                        }*/
                    }

                    // 线性插值计算即期利率
                    if (lowYc != null && highYc != null) {
                        BigDecimal lowValue = detail.getLowValue();
                        BigDecimal highValue = detail.getHighValue();
                        BigDecimal lowRate = detail.getLowProfitRate();
                        BigDecimal highRate = detail.getHighProfitRate();

                        // 如果低值和高值相同，直接使用低值
                        if (lowValue.equals(highValue)) {
                            detail.setInterpolatedSpotRate(lowRate);
                        } else {
                            // 线性插值公式：(high - low)/(highX - lowX) * (x - lowX) + low
                            BigDecimal interpolatedRate = highRate.subtract(lowRate)
                                    .divide(highValue.subtract(lowValue), 15, RoundingMode.HALF_UP)
                                    .multiply(normTime.subtract(lowValue))
                                    .add(lowRate);
                            detail.setInterpolatedSpotRate(interpolatedRate);
                        }
                    } else if (lowYc != null) {
                        detail.setInterpolatedSpotRate(detail.getLowProfitRate());
                    } else if (highYc != null) {
                        detail.setInterpolatedSpotRate(detail.getHighProfitRate());
                    }
                } else {
                    // 标准期限大于20年
                    // 计算最终期限
                    if (normTime.compareTo(ultimateYear) < 0) {
                        // 计算权重因子
                        BigDecimal weight = BigDecimal.valueOf(Math.ceil(normTime.doubleValue()))
                                .subtract(finalLiquidityPeriod)
                                .divide(ultimateYear.subtract(finalLiquidityPeriod), 15, RoundingMode.HALF_UP);

                        // 计算年化即期利率
                        BigDecimal interpolatedRate;
                        AtrConfCbTreasuryYc ycByTenorYears = getYcByTenorYears(cbTreasuryYcList, BigDecimal.valueOf(20));

                        // 使用插值公式计算
                        if (weight.compareTo(BigDecimal.ONE) <= 0 && weight.compareTo(BigDecimal.ZERO) >= 0) {
                            BigDecimal ultimateComponent = ultimateRate.multiply(weight);
                            if ( ycByTenorYears != null ){
                                ultimateComponent = weight.multiply(ultimateComponent.add(ycByTenorYears.getAvgValue().multiply(BigDecimal.ONE.subtract(weight))));
                            }
                            // 查找国债收益率曲线
                            BigDecimal treasuryRate = BigDecimal.ZERO;
                            AtrConfCbTreasuryYc treasuryYc = getYcByTenorYears(cbTreasuryYcList,
                                    BigDecimal.valueOf(Math.ceil(normTime.doubleValue())));

                            if (treasuryYc != null) {
                                treasuryRate = treasuryYc.getAvgValue();
                            } else if (!cbTreasuryYcList.isEmpty()) {
                                // 如果找不到具体的年限，使用最后一个
                                treasuryRate = cbTreasuryYcList.get(cbTreasuryYcList.size() - 1).getAvgValue();
                            }

                            BigDecimal nonUltimateComponent = treasuryRate.multiply(BigDecimal.ONE.subtract(weight));
                            interpolatedRate = ultimateComponent.add(nonUltimateComponent);
                        } else {
                            interpolatedRate = ultimateRate;
                        }

                        detail.setInterpolatedSpotRate(interpolatedRate);
                    } else {
                        detail.setInterpolatedSpotRate(ultimateRate);
                    }
                }

                // 计算流动性溢价
                if (normTime.compareTo(finalLiquidityPeriod) <= 0) {
                    detail.setLiquidityPremium(liquidityPremium);
                } else if (normTime.compareTo(ultimateYear) >= 0) {
                    detail.setLiquidityPremium(ultimateLiquidityPremium);
                } else {
                    // 计算流动性溢价衰减
                    BigDecimal factor = ultimateYear.subtract(BigDecimal.valueOf(Math.ceil(normTime.doubleValue())))
                            .divide(ultimateYear.subtract(finalLiquidityPeriod), 15, RoundingMode.HALF_UP);

                    BigDecimal adjustedLiquidityPremium = liquidityPremium.multiply(factor);
                    detail.setLiquidityPremium(adjustedLiquidityPremium);
                }

                // 计算年化溢价后即期利率
                BigDecimal interpolatedRate = detail.getInterpolatedSpotRate();
                if (interpolatedRate != null && detail.getLiquidityPremium() != null) {
                    BigDecimal annualSpotRateWithPremium = interpolatedRate.add(detail.getLiquidityPremium());
                    detail.setAnnualSpotRateWithPremium(annualSpotRateWithPremium);

                    // 计算月度溢价后即期利率
                    BigDecimal monthlySpotRateWithPremium = BigDecimal.valueOf(
                            Math.pow(annualSpotRateWithPremium.add(BigDecimal.ONE).doubleValue(), 1.0 / 12.0) - 1.0);
                    detail.setMonthlySpotRateWithPremium(monthlySpotRateWithPremium);


                    // 获取上一个月的数据
                    AtrConfInterestRateDetail prevDetail = detailList.get(i - 1);
                    BigDecimal prevMonthlySpotRate = prevDetail.getMonthlySpotRateWithPremium();

                    if (prevMonthlySpotRate != null) {
                        BigDecimal currentTerm = BigDecimal.valueOf(i);
                        BigDecimal prevTerm = BigDecimal.valueOf(i - 1);

                        BigDecimal numerator = BigDecimal.valueOf(
                                Math.pow(monthlySpotRateWithPremium.add(BigDecimal.ONE).doubleValue(), currentTerm.doubleValue()));
                        BigDecimal denominator = BigDecimal.valueOf(
                                Math.pow(prevMonthlySpotRate.add(BigDecimal.ONE).doubleValue(), prevTerm.doubleValue()));

                        BigDecimal monthlyForwardRate = numerator.divide(denominator, 15, RoundingMode.HALF_UP)
                                .subtract(BigDecimal.ONE);
                        detail.setMonthlyForwardRateWithPremium(monthlyForwardRate);
                    }



                    // 计算远期折现因子
                    if (detail.getMonthlyForwardRateWithPremium() != null) {
                        BigDecimal forwardDiscountFactor = BigDecimal.ONE.divide(
                                detail.getMonthlyForwardRateWithPremium().add(BigDecimal.ONE), 15, RoundingMode.HALF_UP);
                        detail.setForwardDiscountFactor(forwardDiscountFactor);
                    }
                    // 计算即期折现因子
                    if (prevDetail.getSpotDiscountFactor() != null && detail.getForwardDiscountFactor() != null) {
                        BigDecimal spotDiscountFactor = prevDetail.getSpotDiscountFactor()
                                .multiply(detail.getForwardDiscountFactor());
                        detail.setSpotDiscountFactor(spotDiscountFactor);
                        
                        // 存储即期折现因子
                        spotDiscountFactorMap.put(i, spotDiscountFactor);
                    }

                }

                detailList.add(detail);
            }

            // 批量保存利率明细
            for (AtrConfInterestRateDetail detail : detailList) {
              abp.insert(detail);
            }
            
            // 将即期折现因子存入atr_buss_ir_initial_dev表
            BigDecimal previousRateE = null; // 用于跟踪上一期的rateE值
            
            for (int i = 0; i <= 600; i++) {
                AtrBussIrInitialDev dev = new AtrBussIrInitialDev();
                dev.setMainId(mainId);
                dev.setDevNo(i);
                
                // 获取当前期的即期折现因子
                BigDecimal spotDiscountFactor = spotDiscountFactorMap.getOrDefault(i, BigDecimal.ZERO);
                
                // 设置rateB，等于上一期的rateE值（第0期为null）
                if (i == 0) {
                    dev.setRateB(null); // 第0期rateB为null
                } else {
                    dev.setRateB(previousRateE); // 其他期rateB为上一期的rateE值
                }
                
                // 设置rateE为当前期的spotDiscountFactor值
                dev.setRateE(spotDiscountFactor);
                
                // 存储当前rateE值供下一期使用
                previousRateE = spotDiscountFactor;
                
                // 设置rate_m和rate_base的默认值
                dev.setRateBase(BigDecimal.ZERO);
                
                // 使用abp异步批量插入
                abp.insert(dev);
            }

            abp.end();
        } catch (Exception e) {
            throw new RuntimeException("计算初始利率曲线失败", e);
        }
    }

    /**
     * 从基础参数表获取配置值
     * @param code 参数编码
     * @return 参数值
     */
    private BigDecimal getBigDecimalFromInterestBase(String code) {
        AtrConfInterestBase interestBase = atrBussInterestRateDao.findInterestBaseByCode(code);
        if (interestBase != null && interestBase.getValue() != null) {
            return interestBase.getValue();
        }
        return BigDecimal.ZERO;
    }

    /**
     * 查找最接近的期限值
     * @param map 期限映射
     * @param target 目标期限
     * @param findLower 是否查找小于等于目标的值
     * @return 最接近的国债收益率曲线
     */
    private AtrConfCbTreasuryYc findNearest(Map<BigDecimal, AtrConfCbTreasuryYc> map, BigDecimal target, boolean findLower) {
        BigDecimal nearestKey = null;

        for (BigDecimal key : map.keySet()) {
            if (findLower) {
                // 寻找小于等于target的最大值
                if (key.compareTo(target) <= 0 && (nearestKey == null || key.compareTo(nearestKey) > 0)) {
                    nearestKey = key;
                }
            } else {
                // 寻找大于等于target的最小值
                if (key.compareTo(target) >= 0 && (nearestKey == null || key.compareTo(nearestKey) < 0)) {
                    nearestKey = key;
                }
            }
        }

        return nearestKey != null ? map.get(nearestKey) : null;
    }

    /**
     * 根据标准期限获取国债收益率曲线
     * @param list 国债收益率曲线列表
     * @param tenorYears 标准期限
     * @return 匹配的国债收益率曲线
     */
    private AtrConfCbTreasuryYc getYcByTenorYears(List<AtrConfCbTreasuryYc> list, BigDecimal tenorYears) {
        for (AtrConfCbTreasuryYc yc : list) {
            if (yc.getTenorYears().compareTo(tenorYears) == 0) {
                return yc;
            }
        }
        return null;
    }

}
