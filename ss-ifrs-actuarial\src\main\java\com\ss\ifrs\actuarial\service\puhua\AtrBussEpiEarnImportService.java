package com.ss.ifrs.actuarial.service.puhua;

import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrAllocActionVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussIbnrImportMainVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLrcActionVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrTemplateVo;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapPlanImportMainVo;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.ti.AtrDapLrcTiPayPlan;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: AtrBussCalcService
 * @Description: 计量计算服务接口类
 * @Author: yinxh.
 * @CreateDate: 2021/3/8 17:56
 * @Version: 1.0
 */
public interface AtrBussEpiEarnImportService {

    Page<AtrDapPlanImportMainVo> searchPage(AtrDapPlanImportMainVo atrDapPlanImportMainVo, Pageable pageParam);

    void delete(Long id);

    /**
     * @description: IBNR分摊结果确认
     * @param : [allocActionVo， userId]
     * @Date 2025/4/8
     * */
    Boolean confirm(AtrDapPlanImportMainVo atrDapPlanImportMainVo, Long userId);

    void epiImport(AtrDapPlanImportMainVo atrDapPlanImportMainVo, MultipartFile file) throws Exception;

    void earnedImport(AtrDapPlanImportMainVo atrDapPlanImportMainVo, MultipartFile file) throws Exception;

    /**
     * 获取导入数据列表
     * @param atrDapPlanImportMainVo
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<Map<String, Object>> getImportDataList(AtrDapPlanImportMainVo atrDapPlanImportMainVo, Pageable pageable);

    /**
     * 获取EPI导入数据详情
     * @param entityId 业务单位ID
     * @param treatyNo 合约号
     * @param riskClassCode 险类代码
     * @return 详情数据列表
     */
    List<Map<String, Object>> getEpiImportDetail(AtrDapPlanImportMainVo atrDapPlanImportMainVo);

    /**
     * 获取已赚保费导入数据详情
     * @param entityId 业务单位ID
     * @param treatyNo 合约号
     * @param riskClassCode 险类代码
     * @return 详情数据列表
     */
    List<Map<String, Object>> getEarnedImportDetail(AtrDapPlanImportMainVo atrDapPlanImportMainVo);

    /**
     * 删除导入数据
     * @param entityId 业务单位ID
     * @param treatyNo 合约号
     * @param riskClassCode 险类代码
     * @param dataType 数据类型 (EPI/EARNED)
     * @return 是否删除成功
     */
    boolean deleteImportData(Long entityId, String yearMonth, String treatyNo, String riskClassCode, String dataType);

    void  downloadEpiTemplate(AtrTemplateVo atrTemplateVo, HttpServletRequest request, HttpServletResponse response) throws Exception;

}
