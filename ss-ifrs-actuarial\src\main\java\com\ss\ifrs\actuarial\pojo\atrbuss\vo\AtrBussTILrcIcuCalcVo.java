/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-03-08 10:07:11
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-03-08 10:07:11<br/>
 * Description: LRC 计算结果主表(单维度，合约分入)<br/>
 * Table Name: ATR_BUSS_TI_LRC_ICU_CALC<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "LRC 计算结果主表(单维度，合约分入)")
public class AtrBussTILrcIcuCalcVo implements Serializable {
    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.ID
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.ACTION_NO
     * Database remarks: 执行编号|一次操作的全局唯一编号， 一次操作的维度是模块（LIC或LRC）、业务类型、业务单位、业务年月、币别
     */
    @ApiModelProperty(value = "执行编号|一次操作的全局唯一编号， 一次操作的维度是模块（LIC或LRC）、业务类型、业务单位、业务年月、币别", required = true)
    private String actionNo;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.TASK_CODE
     * Database remarks: 任务号|一次计算分配一个TASK_CODE, 区别于其他批次的计算
     */
    @ApiModelProperty(value = "任务号|一次计算分配一个TASK_CODE, 区别于其他批次的计算", required = true)
    private String taskCode;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.CALC_TYPE
     * Database remarks: 计算类型|1-业务发生一次计算一次； 2-每评估期重复计算，涉及CSM、UEP的相关累计计算项
     */
    @ApiModelProperty(value = "计算类型|1-业务发生一次计算一次； 2-每评估期重复计算，涉及CSM、UEP的相关累计计算项", required = true)
    private String calcType;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.DATA_KEY
     * Database remarks: 接口表数据的ID
     */
    @ApiModelProperty(value = "接口表数据的ID", required = true)
    private Long dataKey;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.CENTER_ID
     * Database remarks: 公共项/业务单位ID
     */
    @ApiModelProperty(value = "公共项/业务单位ID", required = true)
    private Long entityId;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.DEPT_ID
     * Database remarks: 公共项/业务部门
     */
    @ApiModelProperty(value = "公共项/业务部门", required = true)
    private Long deptId;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.BILL_NO
     * Database remarks: 公共项/帐单号
     */
    @ApiModelProperty(value = "公共项/帐单号", required = true)
    private String billNo;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.MASTER_BILL_NO
     * Database remarks: 公共项/总账单号码
     */
    @ApiModelProperty(value = "公共项/总账单号码", required = true)
    private String masterBillNo;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.TREATY_NO
     * Database remarks: 公共项/合约号
     */
    @ApiModelProperty(value = "公共项/合约号", required = true)
    private String treatyNo;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.CURRENCY
     * Database remarks: 公共项/币种
     */
    @ApiModelProperty(value = "公共项/币种", required = true)
    private String currencyCode;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.YEAR_MONTH
     * Database remarks: 公共项/业务年月 （评估期的年月）
     */
    @ApiModelProperty(value = "公共项/业务年月 （评估期的年月）", required = true)
    private String yearMonth;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.PORTFOLIO_NO
     * Database remarks: 公共项/合同组合号码
     */
    @ApiModelProperty(value = "公共项/合同组合号码", required = true)
    private String portfolioNo;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.ICG_NO
     * Database remarks: 公共项/合同组号码
     */
    @ApiModelProperty(value = "公共项/合同组号码", required = true)
    private String icgNo;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.EVALUATE_APPROACH
     * Database remarks: 公共项/评估方法
     */
    @ApiModelProperty(value = "公共项/评估方法", required = true)
    private String evaluateApproach;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.MODEL_DEF_ID
     * Database remarks: 公共项/计量模型
     */
    @ApiModelProperty(value = "公共项/计量模型", required = true)
    private Long modelDefId;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.LOA_CODE
     * Database remarks: 公共项/LOA编码
     */
    @ApiModelProperty(value = "公共项/LOA编码", required = true)
    private String loaCode;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.CMUNIT_NO
     * Database remarks: 公共项/计量单元编号
     */
    @ApiModelProperty(value = "公共项/计量单元编号", required = false)
    private String cmunitNo;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.PRODUCT_CODE
     * Database remarks: 公共项/产品代码
     */
    @ApiModelProperty(value = "公共项/产品代码", required = false)
    private String productCode;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.EVALUATE_DATE
     * Database remarks: 公共项/评估日期 （存日期）
     */
    @ApiModelProperty(value = "公共项/评估日期 （存日期）", required = false)
    private Date evaluateDate;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.CONTRACT_DATE
     * Database remarks: 公共项/合同确认日期 (即初始确认日期)
     */
    @ApiModelProperty(value = "公共项/合同确认日期 (即初始确认日期)", required = false)
    private Date contractDate;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.EFFECTIVE_DATE_IN_DATE
     * Database remarks: 公共项/保单起保日期（存日期）
     */
    @ApiModelProperty(value = "公共项/保单起保日期（存日期）", required = false)
    private Date effectiveDateInDate;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.CHECK_DATE_IN_DATE
     * Database remarks: 公共项/审核通过日期（存日期）
     */
    @ApiModelProperty(value = "公共项/审核通过日期（存日期）", required = false)
    private Date checkDateInDate;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.EXPIRY_DATE_IN_DATE
     * Database remarks: 公共项/保单终保日期（存日期）
     */
    @ApiModelProperty(value = "公共项/保单终保日期（存日期）", required = false)
    private Date expiryDateInDate;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.EFFECTIVE_DATE_BOM
     * Database remarks: 公共项/保单起保日期（月初）
     */
    @ApiModelProperty(value = "公共项/保单起保日期（月初）", required = false)
    private Date effectiveDateBom;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.EXPIRY_DATE_EOM
     * Database remarks: 公共项/保单终保日期（月底）
     */
    @ApiModelProperty(value = "公共项/保单终保日期（月底）", required = false)
    private Date expiryDateEom;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.PREMIUM_FREQUENCY
     * Database remarks: 公共项/缴费频率
     */
    @ApiModelProperty(value = "公共项/缴费频率", required = false)
    private String premiumFrequency;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.PREMIUM_TERM
     * Database remarks: 公共项/缴费期次
     */
    @ApiModelProperty(value = "公共项/缴费期次", required = false)
    private Long premiumTerm;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.GROSS_PREMIUM
     * Database remarks: 公共项/毛保费
     */
    @ApiModelProperty(value = "公共项/毛保费", required = false)
    private BigDecimal grossPremium;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.COVERAGE_AMOUNT
     * Database remarks: 公共项/保单理赔限额
     */
    @ApiModelProperty(value = "公共项/保单理赔限额", required = false)
    private BigDecimal coverageAmount;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.MAIN_TREATY_TYPE
     * Database remarks: 公共项/合约大类 T-比例合约，X-超赔合约
     */
    @ApiModelProperty(value = "公共项/合约大类 T-比例合约，X-超赔合约", required = false)
    private String mainTreatyType;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.PAS_FREQUENCY
     * Database remarks: 公共项/PAS输入频率
     */
    @ApiModelProperty(value = "公共项/PAS输入频率", required = false)
    private String pasFrequency;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.PASSED_MONTHS
     * Database remarks: 公共项/已过月份
     */
    @ApiModelProperty(value = "公共项/已过月份", required = false)
    private Long passedMonths;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.REMAINING_MONTHS
     * Database remarks: 公共项/未到期月份（评估日期-终保日期）
     */
    @ApiModelProperty(value = "公共项/未到期月份（评估日期-终保日期）", required = false)
    private Long remainingMonths;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.MORTGAGE_RISK_IND
     * Database remarks: 公共项/MORTGAGE RISK IND
     */
    @ApiModelProperty(value = "公共项/MORTGAGE RISK IND", required = false)
    private String mortgageRiskInd;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.UL_UNDERWRITTEN_RATE
     * Database remarks: 公共项/已赚比例（合约）
     */
    @ApiModelProperty(value = "公共项/已赚比例（合约）", required = false)
    private BigDecimal ulUnderwrittenRate;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.EST_TOTAL_RI_PREMIUM
     * Database remarks: 公共项/预估总分保保费（合约）
     */
    @ApiModelProperty(value = "公共项/预估总分保保费（合约）", required = false)
    private BigDecimal estTotalRiPremium;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.RISK_EXPANSION
     * Database remarks: 公共项/风险附加次数 (即 底层保单的保障时长（月））
     */
    @ApiModelProperty(value = "公共项/风险附加次数 (即 底层保单的保障时长（月））", required = false)
    private Long riskExpansion;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.CUMULATIVE_PAID_PREMIUM
     * Database remarks: 预期应收保费/累计已收保费
     */
    @ApiModelProperty(value = "预期应收保费/累计已收保费", required = false)
    private BigDecimal cumulativePaidPremium;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.RECV_PREMIUM
     * Database remarks: 预期应收保费/应收保费
     */
    @ApiModelProperty(value = "预期应收保费/应收保费", required = false)
    private BigDecimal recvPremium;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.ed_premium_per_coverage_day
     * Database remarks: GEP_Persist 已赚保费/已赚保费（每个覆盖月份）
     */
    @ApiModelProperty(value = "GEP_Persist 已赚保费/已赚保费（每个覆盖月份）", required = false)
    private BigDecimal edPremiumPerCoverageDay;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.ED_PREMIUM
     * Database remarks: GEP_Persist 已赚保费/已赚保费
     */
    @ApiModelProperty(value = "GEP_Persist 已赚保费/已赚保费", required = false)
    private BigDecimal edPremium;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.COVERAGE_MONTHS
     * Database remarks: 未赚保费/覆盖期（Mth）
     */
    @ApiModelProperty(value = "未赚保费/覆盖期（Mth）", required = false)
    private Long coverageMonths;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.PRI_CUR_END_REMAIN_CSM_RATE
     * Database remarks: 未赚保费/(上期)当期期末剩余未摊销比例
     */
    @ApiModelProperty(value = "未赚保费/(上期)当期期末剩余未摊销比例", required = false)
    private BigDecimal priCurEndRemainCsmRate;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.PRI_UNTIL_REPORT_REMAIN_CSM_RATE
     * Database remarks: 未赚保费/(上期)截至报告期初剩余未摊销比例
     */
    @ApiModelProperty(value = "未赚保费/(上期)截至报告期初剩余未摊销比例", required = false)
    private BigDecimal priUntilReportRemainCsmRate;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.CUMULATIVE_ED_RATE
     * Database remarks: 未赚保费/累计已赚比例
     */
    @ApiModelProperty(value = "未赚保费/累计已赚比例", required = false)
    private BigDecimal cumulativeEdRate;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.CUR_END_REMAIN_CSM_RATE
     * Database remarks: 未赚保费/当期期末剩余未摊销比例
     */
    @ApiModelProperty(value = "未赚保费/当期期末剩余未摊销比例", required = false)
    private BigDecimal curEndRemainCsmRate;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.UNTIL_REPORT_REMAIN_CSM_RATE
     * Database remarks: 未赚保费/截至报告期初剩余未摊销比例
     */
    @ApiModelProperty(value = "未赚保费/截至报告期初剩余未摊销比例", required = false)
    private BigDecimal untilReportRemainCsmRate;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.ELR
     * Database remarks: Claim_byUWQ_AQ_Vert/ELR
     */
    @ApiModelProperty(value = "Claim_byUWQ_AQ_Vert/ELR", required = false)
    private BigDecimal elr;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.LOAN_OS
     * Database remarks: Claim_byAQ_Vert_Mortgage/@ Val Date Loan O/S
     */
    @ApiModelProperty(value = "Claim_byAQ_Vert_Mortgage/@ Val Date Loan O/S", required = false)
    private BigDecimal loanOs;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.AFNP_LOAN_OS
     * Database remarks: Claim_byAQ_Vert_Mortgage/After Future NB Projection?Loan O/S
     */
    @ApiModelProperty(value = "Claim_byAQ_Vert_Mortgage/After Future NB Projection?Loan O/S", required = false)
    private BigDecimal afnpLoanOs;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.DELINQUENCY_RATE
     * Database remarks: Claim_byAQ_Vert_Mortgage/Delinquency Ratio
     */
    @ApiModelProperty(value = "Claim_byAQ_Vert_Mortgage/Delinquency Ratio", required = false)
    private BigDecimal delinquencyRate;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC.DEFAULT_RATE
     * Database remarks: Claim_byAQ_Vert_Mortgage/Default Ratio
     */
    @ApiModelProperty(value = "Claim_byAQ_Vert_Mortgage/Default Ratio", required = false)
    private BigDecimal defaultRate;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getCalcType() {
        return calcType;
    }

    public void setCalcType(String calcType) {
        this.calcType = calcType;
    }

    public Long getDataKey() {
        return dataKey;
    }

    public void setDataKey(Long dataKey) {
        this.dataKey = dataKey;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }	

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getMasterBillNo() {
        return masterBillNo;
    }

    public void setMasterBillNo(String masterBillNo) {
        this.masterBillNo = masterBillNo;
    }

    public String getTreatyNo() {
        return treatyNo;
    }

    public void setTreatyNo(String treatyNo) {
        this.treatyNo = treatyNo;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public String getEvaluateApproach() {
        return evaluateApproach;
    }

    public void setEvaluateApproach(String evaluateApproach) {
        this.evaluateApproach = evaluateApproach;
    }

    public Long getModelDefId() {
        return modelDefId;
    }

    public void setModelDefId(Long modelDefId) {
        this.modelDefId = modelDefId;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public String getCmunitNo() {
        return cmunitNo;
    }

    public void setCmunitNo(String cmunitNo) {
        this.cmunitNo = cmunitNo;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public Date getEvaluateDate() {
        return evaluateDate;
    }

    public void setEvaluateDate(Date evaluateDate) {
        this.evaluateDate = evaluateDate;
    }

    public Date getContractDate() {
        return contractDate;
    }

    public void setContractDate(Date contractDate) {
        this.contractDate = contractDate;
    }

    public Date getEffectiveDateInDate() {
        return effectiveDateInDate;
    }

    public void setEffectiveDateInDate(Date effectiveDateInDate) {
        this.effectiveDateInDate = effectiveDateInDate;
    }

    public Date getCheckDateInDate() {
        return checkDateInDate;
    }

    public void setCheckDateInDate(Date checkDateInDate) {
        this.checkDateInDate = checkDateInDate;
    }

    public Date getExpiryDateInDate() {
        return expiryDateInDate;
    }

    public void setExpiryDateInDate(Date expiryDateInDate) {
        this.expiryDateInDate = expiryDateInDate;
    }

    public Date getEffectiveDateBom() {
        return effectiveDateBom;
    }

    public void setEffectiveDateBom(Date effectiveDateBom) {
        this.effectiveDateBom = effectiveDateBom;
    }

    public Date getExpiryDateEom() {
        return expiryDateEom;
    }

    public void setExpiryDateEom(Date expiryDateEom) {
        this.expiryDateEom = expiryDateEom;
    }

    public String getPremiumFrequency() {
        return premiumFrequency;
    }

    public void setPremiumFrequency(String premiumFrequency) {
        this.premiumFrequency = premiumFrequency;
    }

    public Long getPremiumTerm() {
        return premiumTerm;
    }

    public void setPremiumTerm(Long premiumTerm) {
        this.premiumTerm = premiumTerm;
    }

    public BigDecimal getGrossPremium() {
        return grossPremium;
    }

    public void setGrossPremium(BigDecimal grossPremium) {
        this.grossPremium = grossPremium;
    }

    public BigDecimal getCoverageAmount() {
        return coverageAmount;
    }

    public void setCoverageAmount(BigDecimal coverageAmount) {
        this.coverageAmount = coverageAmount;
    }

    public String getMainTreatyType() {
        return mainTreatyType;
    }

    public void setMainTreatyType(String mainTreatyType) {
        this.mainTreatyType = mainTreatyType;
    }

    public String getPasFrequency() {
        return pasFrequency;
    }

    public void setPasFrequency(String pasFrequency) {
        this.pasFrequency = pasFrequency;
    }

    public Long getPassedMonths() {
        return passedMonths;
    }

    public void setPassedMonths(Long passedMonths) {
        this.passedMonths = passedMonths;
    }

    public Long getRemainingMonths() {
        return remainingMonths;
    }

    public void setRemainingMonths(Long remainingMonths) {
        this.remainingMonths = remainingMonths;
    }

    public String getMortgageRiskInd() {
        return mortgageRiskInd;
    }

    public void setMortgageRiskInd(String mortgageRiskInd) {
        this.mortgageRiskInd = mortgageRiskInd;
    }

    public BigDecimal getUlUnderwrittenRate() {
        return ulUnderwrittenRate;
    }

    public void setUlUnderwrittenRate(BigDecimal ulUnderwrittenRate) {
        this.ulUnderwrittenRate = ulUnderwrittenRate;
    }

    public BigDecimal getEstTotalRiPremium() {
        return estTotalRiPremium;
    }

    public void setEstTotalRiPremium(BigDecimal estTotalRiPremium) {
        this.estTotalRiPremium = estTotalRiPremium;
    }

    public Long getRiskExpansion() {
        return riskExpansion;
    }

    public void setRiskExpansion(Long riskExpansion) {
        this.riskExpansion = riskExpansion;
    }

    public BigDecimal getCumulativePaidPremium() {
        return cumulativePaidPremium;
    }

    public void setCumulativePaidPremium(BigDecimal cumulativePaidPremium) {
        this.cumulativePaidPremium = cumulativePaidPremium;
    }

    public BigDecimal getRecvPremium() {
        return recvPremium;
    }

    public void setRecvPremium(BigDecimal recvPremium) {
        this.recvPremium = recvPremium;
    }

    public BigDecimal getEdPremiumPerCoverageDay() {
        return edPremiumPerCoverageDay;
    }

    public void setEdPremiumPerCoverageDay(BigDecimal edPremiumPerCoverageDay) {
        this.edPremiumPerCoverageDay = edPremiumPerCoverageDay;
    }

    public BigDecimal getEdPremium() {
        return edPremium;
    }

    public void setEdPremium(BigDecimal edPremium) {
        this.edPremium = edPremium;
    }

    public Long getCoverageMonths() {
        return coverageMonths;
    }

    public void setCoverageMonths(Long coverageMonths) {
        this.coverageMonths = coverageMonths;
    }

    public BigDecimal getPriCurEndRemainCsmRate() {
        return priCurEndRemainCsmRate;
    }

    public void setPriCurEndRemainCsmRate(BigDecimal priCurEndRemainCsmRate) {
        this.priCurEndRemainCsmRate = priCurEndRemainCsmRate;
    }

    public BigDecimal getPriUntilReportRemainCsmRate() {
        return priUntilReportRemainCsmRate;
    }

    public void setPriUntilReportRemainCsmRate(BigDecimal priUntilReportRemainCsmRate) {
        this.priUntilReportRemainCsmRate = priUntilReportRemainCsmRate;
    }

    public BigDecimal getCumulativeEdRate() {
        return cumulativeEdRate;
    }

    public void setCumulativeEdRate(BigDecimal cumulativeEdRate) {
        this.cumulativeEdRate = cumulativeEdRate;
    }

    public BigDecimal getCurEndRemainCsmRate() {
        return curEndRemainCsmRate;
    }

    public void setCurEndRemainCsmRate(BigDecimal curEndRemainCsmRate) {
        this.curEndRemainCsmRate = curEndRemainCsmRate;
    }

    public BigDecimal getUntilReportRemainCsmRate() {
        return untilReportRemainCsmRate;
    }

    public void setUntilReportRemainCsmRate(BigDecimal untilReportRemainCsmRate) {
        this.untilReportRemainCsmRate = untilReportRemainCsmRate;
    }

    public BigDecimal getElr() {
        return elr;
    }

    public void setElr(BigDecimal elr) {
        this.elr = elr;
    }

    public BigDecimal getLoanOs() {
        return loanOs;
    }

    public void setLoanOs(BigDecimal loanOs) {
        this.loanOs = loanOs;
    }

    public BigDecimal getAfnpLoanOs() {
        return afnpLoanOs;
    }

    public void setAfnpLoanOs(BigDecimal afnpLoanOs) {
        this.afnpLoanOs = afnpLoanOs;
    }

    public BigDecimal getDelinquencyRate() {
        return delinquencyRate;
    }

    public void setDelinquencyRate(BigDecimal delinquencyRate) {
        this.delinquencyRate = delinquencyRate;
    }

    public BigDecimal getDefaultRate() {
        return defaultRate;
    }

    public void setDefaultRate(BigDecimal defaultRate) {
        this.defaultRate = defaultRate;
    }
}