package com.ss.ifrs.actuarial.api;

import com.ss.ifrs.actuarial.feign.BmsActProcFeignClient;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.ifrs.actuarial.service.AtrConfBussPeriodService;
import com.ss.library.utils.ClassUtil;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.core.constant.SystemConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.pojo.com.vo.ActOverviewVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/overview")
@Api(value = "流程概览日志接口")
public class AtrOverviewApi extends BaseApi {

    @Autowired
    private BmsActProcFeignClient bmsActProcFeignClient;

    @Autowired
    private AtrConfBussPeriodService bussPeriodService;

    @ApiOperation(value = "流程概览接口")
    @RequestMapping(value = "/search_overview_log", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Map<String, Object>> findOwActProcDef(HttpServletRequest request, @RequestBody AtrConfBussPeriodVo atrConfBussPeriodVo){
        Map<String, Object> map = new HashMap<>();
        if (ObjectUtils.isNotEmpty(atrConfBussPeriodVo)) {
            AtrConfBussPeriodVo bussPeriodVo = bussPeriodService.findCurrentPeriod(atrConfBussPeriodVo);
            ActOverviewVo convert = ClassUtil.convert(bussPeriodVo, ActOverviewVo.class);
            convert.setSystemCode(SystemConstant.AtrIdentity.APP_CODE);
            convert.setProcCode(CommonConstant.RootProcCode.ATR);
            BaseResponse<Map<String, Object>> subOverview = bmsActProcFeignClient.findOverview(convert);
            if (ObjectUtils.isNotEmpty(subOverview) && subOverview.getResCode().equals(ResCodeConstant.ResCode.SUCCESS) && ObjectUtils.isNotEmpty(subOverview.getResData())) {
                map = subOverview.getResData();
            }
        }
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, map);    }

}
