package com.ss.ifrs.actuarial.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.ss.ifrs.actuarial.dao.buss.reserve.AtrBussReserveIbnrDetailDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfCodeDao;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveIbnrDetail;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveIbnrDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveIbnrExcelVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveIbnrVo;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrExcelCellVo;
import com.ss.ifrs.actuarial.service.AtrBussReserveIbnrDetailService;
import com.ss.library.utils.ClassUtil;
import com.ss.platform.util.LanguageUtil;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Service
@Transactional
public class AtrBussReserveIbnrDetailServiceImpl implements AtrBussReserveIbnrDetailService {

	final Logger LOG = LoggerFactory.getLogger(getClass());

	@Autowired
	private AtrBussReserveIbnrDetailDao atrBussReserveIbnrDetailDao;
	
	@Autowired
	private AtrConfCodeDao atrConfCodeDao;

	@Override
	public List<AtrBussReserveIbnrDetailVo> findCumulative(AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo) {
		Long reserveIbnrId = atrBussReserveIbnrDetailVo.getReserveIbnrId();
		if (reserveIbnrId == null) {
			return new ArrayList<AtrBussReserveIbnrDetailVo>();
		}
		// 查找事故年月
		List<AtrBussReserveIbnrDetailVo> resultList = atrBussReserveIbnrDetailDao.findYearMonth(reserveIbnrId);

		for (AtrBussReserveIbnrDetailVo vo : resultList) {
			vo.setDataType(atrBussReserveIbnrDetailVo.getDataType());
			vo.setReserveIbnrId(atrBussReserveIbnrDetailVo.getReserveIbnrId());
			vo.setPortfolioNo(atrBussReserveIbnrDetailVo.getPortfolioNo());
			List<AtrBussReserveIbnrDetailVo> devYearMonthList = atrBussReserveIbnrDetailDao.findCumulative(vo);
			vo.setDevYearMonthList(devYearMonthList);
		}
		return resultList;
	}


	@Override
	public List<AtrBussReserveIbnrDetailVo> findReparations(AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo) {

		Long reserveIbnrId = atrBussReserveIbnrDetailVo.getReserveIbnrId();
		if (reserveIbnrId == null) {
			return new ArrayList<AtrBussReserveIbnrDetailVo>();
		}

		List<AtrBussReserveIbnrDetailVo> stageList = Lists.newArrayList();
		
		stageList = atrConfCodeDao.findCodeByUpperCode("IbnrValue/Reparations");

		for(AtrBussReserveIbnrDetailVo vo : stageList) {
			vo.setReserveIbnrId(atrBussReserveIbnrDetailVo.getReserveIbnrId());
			vo.setDataType(vo.getCodeCode());
			vo.setPortfolioNo(atrBussReserveIbnrDetailVo.getPortfolioNo());
			List<AtrBussReserveIbnrDetailVo> list = atrBussReserveIbnrDetailDao.findReparations(vo);
			vo.setDevYearMonthList(list);

			/*switch (vo.getCodeCode()){
				case "I1":
					vo.setDataType("3");
					List<AtrBussReserveIbnrDetailVo> I1 = atrBussReserveIbnrDetailDao.findReparations(vo);
					vo.setDevYearMonthList(I1);
					break;
				case "I2":
					vo.setDataType("4");
					List<AtrBussReserveIbnrDetailVo> I2 = atrBussReserveIbnrDetailDao.findReparations(vo);
					vo.setDevYearMonthList(I2);
					break;
				case "I3":
					vo.setDataType("5");
					List<AtrBussReserveIbnrDetailVo> I3 = atrBussReserveIbnrDetailDao.findReparations(vo);
					vo.setDevYearMonthList(I3);
					break;
				case "I4":
					vo.setDataType("6");
					List<AtrBussReserveIbnrDetailVo> I4 = atrBussReserveIbnrDetailDao.findReparations(vo);
					vo.setDevYearMonthList(I4);
					break;
				case "I5":
					vo.setDataType("7");
					List<AtrBussReserveIbnrDetailVo> I5 = atrBussReserveIbnrDetailDao.findReparations(vo);
					vo.setDevYearMonthList(I5);
					break;
				case "I6":
					vo.setDataType("8");
					List<AtrBussReserveIbnrDetailVo> I6 = atrBussReserveIbnrDetailDao.findReparations(vo);
					vo.setDevYearMonthList(I6);
					break;
				case "I7":
					vo.setDataType("9");
					List<AtrBussReserveIbnrDetailVo> I7 = atrBussReserveIbnrDetailDao.findReparations(vo);
					vo.setDevYearMonthList(I7);
					break;
				case "I8":
					vo.setDataType("10");
					List<AtrBussReserveIbnrDetailVo> I8 = atrBussReserveIbnrDetailDao.findReparations(vo);
					vo.setDevYearMonthList(I8);
					break;
				case "I9":
					vo.setDataType("11");
					List<AtrBussReserveIbnrDetailVo> I9 = atrBussReserveIbnrDetailDao.findReparations(vo);
					vo.setDevYearMonthList(I9);
					break;
			}*/

		}

		return stageList;
	}

	@Override
	public List<AtrBussReserveIbnrDetailVo> findBFLossRate(AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo) {

		Long reserveIbnrId = atrBussReserveIbnrDetailVo.getReserveIbnrId();
		if (reserveIbnrId == null) {
			return new ArrayList<AtrBussReserveIbnrDetailVo>();
		}

		List<AtrBussReserveIbnrDetailVo> stageList = Lists.newArrayList();

		stageList = atrConfCodeDao.findCodeByUpperCode("IbnrValue/BFLossRate");

		for(AtrBussReserveIbnrDetailVo vo : stageList) {
			vo.setReserveIbnrId(atrBussReserveIbnrDetailVo.getReserveIbnrId());
			vo.setDataType(vo.getCodeCode());
			vo.setPortfolioNo(atrBussReserveIbnrDetailVo.getPortfolioNo());
			List<AtrBussReserveIbnrDetailVo> list = atrBussReserveIbnrDetailDao.findReparations(vo);
			vo.setDevYearMonthList(list);

			/*switch (vo.getCodeCode()){
				case "I10":
					vo.setDataType("12");
					List<AtrBussReserveIbnrDetailVo> I1 = atrBussReserveIbnrDetailDao.findReparations(vo);
					vo.setDevYearMonthList(I1);
					break;
				case "I11":
					vo.setDataType("13");
					List<AtrBussReserveIbnrDetailVo> I2 = atrBussReserveIbnrDetailDao.findReparations(vo);
					vo.setDevYearMonthList(I2);
					break;
				case "I12":
					vo.setDataType("14");
					List<AtrBussReserveIbnrDetailVo> I3 = atrBussReserveIbnrDetailDao.findReparations(vo);
					vo.setDevYearMonthList(I3);
					break;
				case "I13":
					vo.setDataType("15");
					List<AtrBussReserveIbnrDetailVo> I4 = atrBussReserveIbnrDetailDao.findReparations(vo);
					vo.setDevYearMonthList(I4);
					break;
			}*/



		}

		return stageList;
	}

	@Override
	public List<AtrBussReserveIbnrDetailVo> findBFCalculate(AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo) {

		Long reserveIbnrId = atrBussReserveIbnrDetailVo.getReserveIbnrId();
		if (reserveIbnrId == null) {
			return new ArrayList<AtrBussReserveIbnrDetailVo>();
		}

		List<AtrBussReserveIbnrDetailVo> stageList = Lists.newArrayList();

		stageList = atrConfCodeDao.findCodeByUpperCode("IbnrValue/BFCalculate");

		atrBussReserveIbnrDetailVo.setTotal(BigDecimal.valueOf(0));
		BigDecimal total = atrBussReserveIbnrDetailVo.getTotal();
		for(AtrBussReserveIbnrDetailVo vo : stageList) {
			//vo.setDataType("16");
			vo.setDataType(vo.getCodeCode());
			vo.setReserveIbnrId(atrBussReserveIbnrDetailVo.getReserveIbnrId());
			vo.setPortfolioNo(atrBussReserveIbnrDetailVo.getPortfolioNo());
			List<AtrBussReserveIbnrDetailVo> I1 = atrBussReserveIbnrDetailDao.findReparations(vo);
			for(AtrBussReserveIbnrDetailVo totalVo : I1){
				total = total.add(totalVo.getValue());
			}
			vo.setTotal(total);
			vo.setDevYearMonthList(I1);
		}

		return stageList;
	}

	@Override
	public List<AtrBussReserveIbnrDetailVo> showLtIbnrData(AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo) {

		Long reserveIbnrId = atrBussReserveIbnrDetailVo.getReserveIbnrId();
		if (reserveIbnrId == null) {
			return new ArrayList<AtrBussReserveIbnrDetailVo>();
		}
		// 查找事故年月
		List<AtrBussReserveIbnrDetailVo> resultList = atrBussReserveIbnrDetailDao.findYearMonth(reserveIbnrId);

		for (AtrBussReserveIbnrDetailVo vo : resultList) {
			vo.setDataType(atrBussReserveIbnrDetailVo.getDataType());
			vo.setReserveIbnrId(atrBussReserveIbnrDetailVo.getReserveIbnrId());
			vo.setPortfolioNo(atrBussReserveIbnrDetailVo.getPortfolioNo());
			List<AtrBussReserveIbnrDetailVo> devYearMonthList = atrBussReserveIbnrDetailDao.findCumulative(vo);
			vo.setDevYearMonthList(devYearMonthList);
		}

		List<AtrBussReserveIbnrDetailVo> dataList = atrBussReserveIbnrDetailDao.showLtIbnrData(reserveIbnrId);

		for (int i = 0; i < resultList.size(); i++) {
			AtrBussReserveIbnrDetailVo vo = resultList.get(i);


			for(int j = 0; j < dataList.size(); j++) {
				AtrBussReserveIbnrDetailVo atrDetailVo = dataList.get(j);
				if (atrDetailVo.getDamageYearMonth().equals(vo.getDamageYearMonth())){
					if(atrDetailVo.getDataType().equals("22") && atrDetailVo.getDamageYearMonth().equals(vo.getDamageYearMonth())){
						vo.setAtrFCValue(atrDetailVo.getValue());
					}else if(atrDetailVo.getDataType().equals("23") && atrDetailVo.getDamageYearMonth().equals(vo.getDamageYearMonth())){
						vo.setAtrTOSValue(atrDetailVo.getValue());
					}else if(atrDetailVo.getDataType().equals("24") && atrDetailVo.getDamageYearMonth().equals(vo.getDamageYearMonth())){
						vo.setAtrIBNRValue(atrDetailVo.getValue());
					}else{
						vo.getDevYearMonthList().add(atrDetailVo);
					}
				}
			}
			
		}

		return resultList;
	}

	@Override
	public List<AtrBussReserveIbnrExcelVo> findListByIbnrId(Long reserveIbnrId){
		return atrBussReserveIbnrDetailDao.findListByIbnrId(reserveIbnrId);
	}


	@Override
	public void updateValueAndCDFCalculate(AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo) throws UnexpectedRollbackException {
		try {
			for(AtrBussReserveIbnrDetailVo vo : atrBussReserveIbnrDetailVo.getThisYearMonthList()) {
				atrBussReserveIbnrDetailDao.updateById(ClassUtil.convert(vo, AtrBussReserveIbnrDetail.class));
			}
			atrBussReserveIbnrDetailDao.cdfCalculate(atrBussReserveIbnrDetailVo.getReserveIbnrId());
		} catch (UnexpectedRollbackException e) {
			LOG.error(e.getLocalizedMessage(), e);
			throw e;
		}
	}

	@Override
	public void updateValueAndOSCalculate(AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo) throws UnexpectedRollbackException {
		try {
			for(AtrBussReserveIbnrDetailVo vo : atrBussReserveIbnrDetailVo.getThisYearMonthList()) {
				atrBussReserveIbnrDetailDao.updateById(ClassUtil.convert(vo, AtrBussReserveIbnrDetail.class));
			}
			atrBussReserveIbnrDetailDao.osCalculate(atrBussReserveIbnrDetailVo.getReserveIbnrId());
		} catch (UnexpectedRollbackException e) {
			LOG.error(e.getLocalizedMessage(), e);
			throw e;
		}
	}

	@Override
	public List<AtrExcelCellVo> generateIbnrCells(List<AtrExcelCellVo> cells, Object object, String language){
		if(!ObjectUtils.isEmpty(object)){

			ObjectMapper objectMapper = new ObjectMapper();
			AtrBussReserveIbnrVo atrBussReserveIbnrVo = objectMapper.convertValue(object, AtrBussReserveIbnrVo.class);

			if(null != atrBussReserveIbnrVo && null != atrBussReserveIbnrVo.getReserveIbnrId()){
				//数据导出默认行数，第0行为表头
				int j=1;
				AtrExcelCellVo dataCellVo;
				String dataTypeName;

				//获取Excel源数据list
				List<AtrBussReserveIbnrExcelVo> ibnrDetailList = this.findListByIbnrId(atrBussReserveIbnrVo.getReserveIbnrId());

				//把源数据list数据存入单元格cells中
				for(AtrBussReserveIbnrExcelVo detailVo : ibnrDetailList){
					//获取dataType对应枚举值的名称
					dataTypeName = LanguageUtil.getLocalesName(language,detailVo.getCodeEName(),detailVo.getCodeCName(),detailVo.getCodeLName());

					dataCellVo = new AtrExcelCellVo(null, j, 0, dataTypeName, "string", null, null,
							language, HorizontalAlignment.LEFT);
					cells.add(dataCellVo);
					dataCellVo = new AtrExcelCellVo(null, j, 1, detailVo.getDamageYearMonth(), "string", null, null,
							language, HorizontalAlignment.LEFT);
					cells.add(dataCellVo);
					dataCellVo = new AtrExcelCellVo(null, j, 2, detailVo.getDevPeriod(), "integer", null, null,
							language, HorizontalAlignment.RIGHT);
					cells.add(dataCellVo);
					if("11".equals(detailVo.getDataType())){
						dataCellVo = new AtrExcelCellVo(null, j, 3, detailVo.getValue(), "string", null, null,
								language, HorizontalAlignment.RIGHT);
					}else {
						dataCellVo = new AtrExcelCellVo(null, j, 3, Double.valueOf(detailVo.getValue()), "double", null, null,
								language, HorizontalAlignment.RIGHT);
					}

					cells.add(dataCellVo);

					j++;
				}
			}
		}

		return cells;
	}
}
