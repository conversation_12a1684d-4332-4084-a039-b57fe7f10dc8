package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrcode.po.AtrConfCodeAdapter;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfCode;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfCodeVo;
import com.ss.platform.core.model.Tree;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.springframework.transaction.UnexpectedRollbackException;

import java.util.List;
import java.util.Map;

public interface AtrConfCodeService {

	// code
	public AtrConfCodeVo findAtrCodeByPk(Long codeId);

	public String disableAtrConfCode(AtrConfCodeVo confCodeVo) throws UnexpectedRollbackException;

	public AtrConfCodeVo findByCodeCode(String codeCode);

	public Page<Tree<AtrConfCodeVo>> searchAtrConfCodePage(AtrConfCodeVo confCodeVo, Pageable pageParam);

	public void saveAtrUpperCode(AtrConfCodeVo upperCodeVo) throws UnexpectedRollbackException;

	public AtrConfCodeVo findByUpperCodeId(Long upperCodeId);

	public void updateAtrUpperCode(AtrConfCodeVo confUpperCodeVo) throws UnexpectedRollbackException;

	public void delete(AtrConfCodeVo confCodeVo);

	public void saveAtrCode(AtrConfCodeVo atrCodeAddReqVo) throws UnexpectedRollbackException;

	public void updateAtrCode(AtrConfCodeVo atrCodeUpdateReqVo) throws UnexpectedRollbackException;

	public Page<AtrConfCodeVo> findSysCodePage(AtrConfCodeVo vo, Pageable pageParam);

	public Page<AtrConfCodeVo> findListValid(AtrConfCodeVo atrCodeVo, Pageable pageParam);

	public List<AtrConfCode> findList(AtrConfCodeVo atrCodeVo);

	public Map<String, Object> findListV2(AtrConfCodeVo atrCodeVo);

	// AutoComplete
	// Code_Config
	public AtrConfCodeAdapter getSearchConfig(String code);

	Page<AtrConfCodeVo> findListForAutoComplete(AtrConfCodeVo atrCodeVo, Pageable pageParam);

	public String getTree(Map<String, String> params);

	public Page<?> dynamicSql(Map map, Pageable pageParam);

	public List<?> dynamicSql(Map map);

	/**
	 * @Method findByCodeAndIdx
	 * <AUTHOR>
	 * @Date 2022/9/2
	 * @Description 根据CodeCode和CodeCodeIdx查找对象信息
	 * @param atrConfCodeVo 对象VO（含CodeCode、CodeCodeIdx）
	 */
	AtrConfCode findByCodeAndIdx(AtrConfCodeVo atrConfCodeVo);

	public List<AtrConfCodeVo> findByCodeIdx(AtrConfCodeVo atrCodeVo);
	/**
	 * 根据对象加载注解码值缓存
	 * @param voClazz
	 */
	public void cacheSsTranslateCode(Class voClazz);

	void clearCacheAllVCode();

	List<AtrConfCodeVo> findCodeByCodeIdx(AtrConfCodeVo sysCodeVo);

	List<String> findCodeByCodeIdx(String codeIdx);
}
