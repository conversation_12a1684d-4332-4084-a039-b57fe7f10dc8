/**
 * 
 * This file was generated by Taiping MyBatis Generator(v1.2.12).
 * Date: 2024-10-23 09:36:20
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by Taiping MyBatis Generator(v1.2.12).
 * Create Date: 2024-10-23 09:36:20<br/>
 * Description: IBNR二次分摊金额校验表<br/>
 * Table Name: ATR_SEC_ALLOC_VER_AMT<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "IBNR二次分摊金额校验表")
public class AtrSecAllocVerAmt implements Serializable {
    /**
     * Database column: ATR_SEC_ALLOC_VER_AMT.ID
     * Database remarks: ID|主键
     */
    @ApiModelProperty(value = "ID|主键", required = true)
    private Long id;

    /**
     * Database column: ATR_SEC_ALLOC_VER_AMT.ENTITY_ID
     * Database remarks: 业务单位
     */
    @ApiModelProperty(value = "业务单位", required = false)
    private Long entityId;

    /**
     * Database column: ATR_SEC_ALLOC_VER_AMT.YEAR_MONTH
     * Database remarks: 业务年月
     */
    @ApiModelProperty(value = "业务年月", required = false)
    private String yearMonth;

    /**
     * Database column: ATR_SEC_ALLOC_VER_AMT.ACTION_NO
     * Database remarks: 执行任务编码
     */
    @ApiModelProperty(value = "执行任务编码", required = false)
    private String actionNo;

    /**
     * Database column: ATR_SEC_ALLOC_VER_AMT.ICG_NO
     * Database remarks: 合同组号
     */
    @ApiModelProperty(value = "合同组号", required = false)
    private String icgNo;

    /**
     * Database column: ATR_SEC_ALLOC_VER_AMT.TREATY_NO
     * Database remarks: 合约号
     */
    @ApiModelProperty(value = "合约号", required = false)
    private String treatyNo;

    /**
     * Database column: ATR_SEC_ALLOC_VER_AMT.PAY_FLAG
     * Database remarks: 计算标识 | 41 再保前 | 43 再保后分摊
     */
    @ApiModelProperty(value = "计算标识 | 41 再保前 | 43 再保后分摊", required = false)
    private String payFlag;

    /**
     * Database column: ATR_SEC_ALLOC_VER_AMT.DRAW_MONTH
     * Database remarks: 执行期间
     */
    @ApiModelProperty(value = "执行期间", required = false)
    private String drawMonth;

    /**
     * Database column: ATR_SEC_ALLOC_VER_AMT.ACC_MONTH
     * Database remarks: 事故年月
     */
    @ApiModelProperty(value = "事故年月", required = false)
    private String accMonth;

    /**
     * Database column: ATR_SEC_ALLOC_VER_AMT.RISK_CATEGORY_CODE
     * Database remarks: 监管大类代码
     */
    @ApiModelProperty(value = "监管大类代码", required = false)
    private String riskCategoryCode;

    /**
     * Database column: ATR_SEC_ALLOC_VER_AMT.COM_CODE
     * Database remarks: 机构代码
     */
    @ApiModelProperty(value = "机构代码", required = false)
    private String comCode;

    /**
     * Database column: ATR_SEC_ALLOC_VER_AMT.CLASS_CODE
     * Database remarks: 险类代码
     */
    @ApiModelProperty(value = "险类代码", required = false)
    private String classCode;

    /**
     * Database column: ATR_SEC_ALLOC_VER_AMT.RISK_CODE
     * Database remarks: 险种代码
     */
    @ApiModelProperty(value = "险种代码", required = false)
    private String riskCode;

    /**
     * Database column: ATR_SEC_ALLOC_VER_AMT.BUSINESS_TYPE
     * Database remarks: 二次分摊维度汇总金额
     */
    @ApiModelProperty(value = "二次分摊维度汇总金额", required = false)
    private String businessType;

    /**
     * Database column: ATR_SEC_ALLOC_VER_AMT.ONE_SUM_AMOUNT
     * Database remarks: 一次分摊维度汇总金额
     */
    @ApiModelProperty(value = "一次分摊维度汇总金额", required = false)
    private BigDecimal oneSumAmount;

    /**
     * Database column: ATR_SEC_ALLOC_VER_AMT.TWO_SUM_AMOUNT
     * Database remarks: null
     */
    private BigDecimal twoSumAmount;

    /**
     * Database column: ATR_SEC_ALLOC_VER_AMT.IS_BALANCE
     * Database remarks: 是否平衡 | 0:平衡  1:不平衡
     */
    @ApiModelProperty(value = "是否平衡 | 0:平衡  1:不平衡", required = false)
    private String isBalance;

    /**
     * Database column: ATR_SEC_ALLOC_VER_AMT.BALANCE_AMOUNT
     * Database remarks: 差额
     */
    @ApiModelProperty(value = "差额", required = false)
    private BigDecimal balanceAmount;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public String getTreatyNo() {
        return treatyNo;
    }

    public void setTreatyNo(String treatyNo) {
        this.treatyNo = treatyNo;
    }

    public String getPayFlag() {
        return payFlag;
    }

    public void setPayFlag(String payFlag) {
        this.payFlag = payFlag;
    }

    public String getDrawMonth() {
        return drawMonth;
    }

    public void setDrawMonth(String drawMonth) {
        this.drawMonth = drawMonth;
    }

    public String getAccMonth() {
        return accMonth;
    }

    public void setAccMonth(String accMonth) {
        this.accMonth = accMonth;
    }

    public String getRiskCategoryCode() {
        return riskCategoryCode;
    }

    public void setRiskCategoryCode(String riskCategoryCode) {
        this.riskCategoryCode = riskCategoryCode;
    }

    public String getComCode() {
        return comCode;
    }

    public void setComCode(String comCode) {
        this.comCode = comCode;
    }

    public String getClassCode() {
        return classCode;
    }

    public void setClassCode(String classCode) {
        this.classCode = classCode;
    }

    public String getRiskCode() {
        return riskCode;
    }

    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public BigDecimal getOneSumAmount() {
        return oneSumAmount;
    }

    public void setOneSumAmount(BigDecimal oneSumAmount) {
        this.oneSumAmount = oneSumAmount;
    }

    public BigDecimal getTwoSumAmount() {
        return twoSumAmount;
    }

    public void setTwoSumAmount(BigDecimal twoSumAmount) {
        this.twoSumAmount = twoSumAmount;
    }

    public String getIsBalance() {
        return isBalance;
    }

    public void setIsBalance(String isBalance) {
        this.isBalance = isBalance;
    }

    public BigDecimal getBalanceAmount() {
        return balanceAmount;
    }

    public void setBalanceAmount(BigDecimal balanceAmount) {
        this.balanceAmount = balanceAmount;
    }
}