<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-08-28 15:48:15 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussAutoquotaWeightDao">
  <!-- 本配置文件由SS MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**IDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->

    <select id="queryForExitedWeight" parameterType="java.lang.String"
            resultType="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussAutoquotaWeightDetailVo">

        select t.business_source_code                                                              as businessSourceCode,
               t.loa_code                                                                          as loaCode,
               string_agg(coalesce(t.weight_value::varchar, ''), ',' order by t.offset_years desc) as weightValuesAgg
        from atr_buss_autoquota_weight t
        where t.action_no = #{actionNo,jdbcType=VARCHAR}
        group by t.business_source_code, t.loa_code
        order by t.loa_code

    </select>

</mapper>