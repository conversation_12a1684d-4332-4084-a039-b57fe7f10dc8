<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-02-13 15:48:14 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.conf.AtrConfQuotaDymDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDym">
    <id column="QUOTA_ID" property="quotaId" jdbcType="DECIMAL" />
    <result column="entity_id" property="entityId" jdbcType="DECIMAL" />
    <result column="YEAR_MONTH" property="yearMonth" jdbcType="VARCHAR" />
    <result column="LOA_CODE" property="loaCode" jdbcType="VARCHAR" />
    <result column="business_source_code" property="businessSourceCode" jdbcType="VARCHAR" />
    <result column="DIMENSION_VALUE" property="dimensionValue" jdbcType="VARCHAR" />
    <result column="QUOTA_DEF_ID" property="quotaDefId" jdbcType="DECIMAL" />
    <result column="SERIAL_NO" property="serialNo" jdbcType="DECIMAL" />
    <result column="VALID_IS" property="validIs" jdbcType="VARCHAR" />
    <result column="AUDIT_STATE" property="auditState" jdbcType="VARCHAR" />
    <result column="CHECKED_MSG" property="checkedMsg" jdbcType="VARCHAR" />
    <result column="CHECKED_ID" property="checkedId" jdbcType="DECIMAL" />
    <result column="CHECKED_TIME" property="checkedTime" jdbcType="TIMESTAMP" />
    <result column="CREATOR_ID" property="creatorId" jdbcType="DECIMAL" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATOR_ID" property="updatorId" jdbcType="DECIMAL" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    QUOTA_ID, entity_id, YEAR_MONTH, LOA_CODE, BUSINESS_SOURCE_CODE, DIMENSION_VALUE, QUOTA_DEF_ID, SERIAL_NO, VALID_IS,
    AUDIT_STATE, CHECKED_MSG, CHECKED_ID, CHECKED_TIME, CREATOR_ID, CREATE_TIME, UPDATOR_ID,
    UPDATE_TIME
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="quotaId != null ">
        and QUOTA_ID = #{quotaId,jdbcType=DECIMAL}
      </if>
      <if test="entityId != null ">
        and entity_id = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
        and YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="loaCode != null and loaCode != ''">
        and LOA_CODE = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="businessSourceCode != null and businessSourceCode != '' ">
        and business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="dimensionValue != null and dimensionValue != ''">
        and DIMENSION_VALUE = #{dimensionValue,jdbcType=VARCHAR}
      </if>
      <if test="quotaDefId != null ">
        and QUOTA_DEF_ID = #{quotaDefId,jdbcType=DECIMAL}
      </if>
      <if test="serialNo != null ">
        and SERIAL_NO = #{serialNo,jdbcType=DECIMAL}
      </if>
      <if test="validIs != null and validIs != ''">
        and VALID_IS = #{validIs,jdbcType=VARCHAR}
      </if>
      <if test="auditState != null and auditState != ''">
        and AUDIT_STATE = #{auditState,jdbcType=VARCHAR}
      </if>
      <if test="checkedMsg != null and checkedMsg != ''">
        and CHECKED_MSG = #{checkedMsg,jdbcType=VARCHAR}
      </if>
      <if test="checkedId != null ">
        and CHECKED_ID = #{checkedId,jdbcType=DECIMAL}
      </if>
      <if test="checkedTime != null ">
        and CHECKED_TIME = #{checkedTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creatorId != null ">
        and CREATOR_ID = #{creatorId,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null ">
        and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
        and UPDATOR_ID = #{updatorId,jdbcType=DECIMAL}
      </if>
      <if test="updateTime != null ">
        and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.quotaId != null ">
        and QUOTA_ID = #{condition.quotaId,jdbcType=DECIMAL}
      </if>
      <if test="condition.entityId != null ">
        and entity_id = #{condition.entityId,jdbcType=DECIMAL}
      </if>
      <if test="condition.yearMonth != null and condition.yearMonth != ''">
        and YEAR_MONTH = #{condition.yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="condition.loaCode != null and condition.loaCode != ''">
        and LOA_CODE = #{condition.loaCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.businessSourceCode != null and condition.businessSourceCode != ''">
        and business_source_code = #{condition.businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.dimensionValue != null and condition.dimensionValue != ''">
        and DIMENSION_VALUE = #{condition.dimensionValue,jdbcType=VARCHAR}
      </if>
      <if test="condition.quotaDefId != null ">
        and QUOTA_DEF_ID = #{condition.quotaDefId,jdbcType=DECIMAL}
      </if>
      <if test="condition.serialNo != null ">
        and SERIAL_NO = #{condition.serialNo,jdbcType=DECIMAL}
      </if>
      <if test="condition.validIs != null and condition.validIs != ''">
        and VALID_IS = #{condition.validIs,jdbcType=VARCHAR}
      </if>
      <if test="condition.auditState != null and condition.auditState != ''">
        and AUDIT_STATE = #{condition.auditState,jdbcType=VARCHAR}
      </if>
      <if test="condition.checkedMsg != null and condition.checkedMsg != ''">
        and CHECKED_MSG = #{condition.checkedMsg,jdbcType=VARCHAR}
      </if>
      <if test="condition.checkedId != null ">
        and CHECKED_ID = #{condition.checkedId,jdbcType=DECIMAL}
      </if>
      <if test="condition.checkedTime != null ">
        and CHECKED_TIME = #{condition.checkedTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.creatorId != null ">
        and CREATOR_ID = #{condition.creatorId,jdbcType=DECIMAL}
      </if>
      <if test="condition.createTime != null ">
        and CREATE_TIME = #{condition.createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.updatorId != null ">
        and UPDATOR_ID = #{condition.updatorId,jdbcType=DECIMAL}
      </if>
      <if test="condition.updateTime != null ">
        and UPDATE_TIME = #{condition.updateTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="quotaId != null ">
        and QUOTA_ID = #{quotaId,jdbcType=DECIMAL}
      </if>
      <if test="entityId != null ">
        and entity_id = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
        and YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="loaCode != null and loaCode != ''">
        and LOA_CODE = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="businessSourceCode != null ">
        and business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="dimensionValue != null and dimensionValue != ''">
        and DIMENSION_VALUE = #{dimensionValue,jdbcType=VARCHAR}
      </if>
      <if test="quotaDefId != null ">
        and QUOTA_DEF_ID = #{quotaDefId,jdbcType=DECIMAL}
      </if>
      <if test="serialNo != null ">
        and SERIAL_NO = #{serialNo,jdbcType=DECIMAL}
      </if>
      <if test="validIs != null and validIs != ''">
        and VALID_IS = #{validIs,jdbcType=VARCHAR}
      </if>
      <if test="auditState != null and auditState != ''">
        and AUDIT_STATE = #{auditState,jdbcType=VARCHAR}
      </if>
      <if test="checkedMsg != null and checkedMsg != ''">
        and CHECKED_MSG = #{checkedMsg,jdbcType=VARCHAR}
      </if>
      <if test="checkedId != null ">
        and CHECKED_ID = #{checkedId,jdbcType=DECIMAL}
      </if>
      <if test="checkedTime != null ">
        and CHECKED_TIME = #{checkedTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creatorId != null ">
        and CREATOR_ID = #{creatorId,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null ">
        and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
        and UPDATOR_ID = #{updatorId,jdbcType=DECIMAL}
      </if>
      <if test="updateTime != null ">
        and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List" />
    from ATR_CONF_QUOTA_DYM
    where QUOTA_ID = #{quotaId,jdbcType=DECIMAL}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select
    <include refid="Base_Column_List" />
    from ATR_CONF_QUOTA_DYM
    where QUOTA_ID in
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ATR_CONF_QUOTA_DYM
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDym">
    select
    <include refid="Base_Column_List" />
    from ATR_CONF_QUOTA_DYM
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select
    <include refid="Base_Column_List" />
    from ATR_CONF_QUOTA_DYM
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="QUOTA_ID" keyProperty="quotaId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDym">
    <selectKey resultType="long" keyProperty="quotaId" order="BEFORE">
      select ATR_SEQ_CONF_QUOTA_DYM.nextval sequenceNo from dual
    </selectKey>
    insert into ATR_CONF_QUOTA_DYM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="quotaId != null">
        QUOTA_ID,
      </if>
      <if test="entityId != null">
        entity_id,
      </if>
      <if test="yearMonth != null">
        YEAR_MONTH,
      </if>
      <if test="businessSourceCode != null">
        business_source_code,
      </if>
      <if test="loaCode != null">
        LOA_CODE,
      </if>
      <if test="dimensionValue != null">
        DIMENSION_VALUE,
      </if>
      <if test="quotaDefId != null">
        QUOTA_DEF_ID,
      </if>
      <if test="serialNo != null">
        SERIAL_NO,
      </if>
      <if test="validIs != null">
        VALID_IS,
      </if>
      <if test="auditState != null">
        AUDIT_STATE,
      </if>
      <if test="checkedMsg != null">
        CHECKED_MSG,
      </if>
      <if test="checkedId != null">
        CHECKED_ID,
      </if>
      <if test="checkedTime != null">
        CHECKED_TIME,
      </if>
      <if test="creatorId != null">
        CREATOR_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updatorId != null">
        UPDATOR_ID,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="quotaId != null">
        #{quotaId,jdbcType=DECIMAL},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=DECIMAL},
      </if>
      <if test="yearMonth != null">
        #{yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceCode != null">
        #{businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="loaCode != null">
        #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="dimensionValue != null">
        #{dimensionValue,jdbcType=VARCHAR},
      </if>
      <if test="quotaDefId != null">
        #{quotaDefId,jdbcType=DECIMAL},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=DECIMAL},
      </if>
      <if test="validIs != null">
        #{validIs,jdbcType=VARCHAR},
      </if>
      <if test="auditState != null">
        #{auditState,jdbcType=VARCHAR},
      </if>
      <if test="checkedMsg != null">
        #{checkedMsg,jdbcType=VARCHAR},
      </if>
      <if test="checkedId != null">
        #{checkedId,jdbcType=DECIMAL},
      </if>
      <if test="checkedTime != null">
        #{checkedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        #{updatorId,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all
    <foreach collection="list" item="item" index="index">
      into ATR_CONF_QUOTA_DYM values
      (#{item.quotaId,jdbcType=DECIMAL}, #{item.entityId,jdbcType=DECIMAL}, #{item.yearMonth,jdbcType=VARCHAR},#{item.businessSourceCode,jdbcType=VARCHAR},
      #{item.loaCode,jdbcType=VARCHAR}, #{item.dimensionValue,jdbcType=VARCHAR}, #{item.quotaDefId,jdbcType=DECIMAL},
      #{item.serialNo,jdbcType=DECIMAL}, #{item.validIs,jdbcType=VARCHAR}, #{item.auditState,jdbcType=VARCHAR},
      #{item.checkedMsg,jdbcType=VARCHAR}, #{item.checkedId,jdbcType=DECIMAL}, #{item.checkedTime,jdbcType=TIMESTAMP},
      #{item.creatorId,jdbcType=DECIMAL}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updatorId,jdbcType=DECIMAL},
      #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
    select 1 from dual
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDym">
    update ATR_CONF_QUOTA_DYM
    <set>
      <if test="entityId != null">
        entity_id = #{entityId,jdbcType=DECIMAL},
      </if>
      <if test="yearMonth != null">
        YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceCode != null">
        business_source_code = #{businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="loaCode != null">
        LOA_CODE = #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="dimensionValue != null">
        DIMENSION_VALUE = #{dimensionValue,jdbcType=VARCHAR},
      </if>
      <if test="quotaDefId != null">
        QUOTA_DEF_ID = #{quotaDefId,jdbcType=DECIMAL},
      </if>
      <if test="serialNo != null">
        SERIAL_NO = #{serialNo,jdbcType=DECIMAL},
      </if>
      <if test="validIs != null">
        VALID_IS = #{validIs,jdbcType=VARCHAR},
      </if>
      <if test="auditState != null">
        AUDIT_STATE = #{auditState,jdbcType=VARCHAR},
      </if>
      <if test="checkedMsg != null">
        CHECKED_MSG = #{checkedMsg,jdbcType=VARCHAR},
      </if>
      <if test="checkedId != null">
        CHECKED_ID = #{checkedId,jdbcType=DECIMAL},
      </if>
      <if test="checkedTime != null">
        CHECKED_TIME = #{checkedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorId != null">
        CREATOR_ID = #{creatorId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        UPDATOR_ID = #{updatorId,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where QUOTA_ID = #{quotaId,jdbcType=DECIMAL}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDym">
    update ATR_CONF_QUOTA_DYM
    <set>
      <if test="record.entityId != null">
        entity_id = #{record.entityId,jdbcType=DECIMAL},
      </if>
      <if test="record.yearMonth != null">
        YEAR_MONTH = #{record.yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="record.businessSourceCode != null">
        business_source_code = #{record.businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.loaCode != null">
        LOA_CODE = #{record.loaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.dimensionValue != null">
        DIMENSION_VALUE = #{record.dimensionValue,jdbcType=VARCHAR},
      </if>
      <if test="record.quotaDefId != null">
        QUOTA_DEF_ID = #{record.quotaDefId,jdbcType=DECIMAL},
      </if>
      <if test="record.serialNo != null">
        SERIAL_NO = #{record.serialNo,jdbcType=DECIMAL},
      </if>
      <if test="record.validIs != null">
        VALID_IS = #{record.validIs,jdbcType=VARCHAR},
      </if>
      <if test="record.auditState != null">
        AUDIT_STATE = #{record.auditState,jdbcType=VARCHAR},
      </if>
      <if test="record.checkedMsg != null">
        CHECKED_MSG = #{record.checkedMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.checkedId != null">
        CHECKED_ID = #{record.checkedId,jdbcType=DECIMAL},
      </if>
      <if test="record.checkedTime != null">
        CHECKED_TIME = #{record.checkedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creatorId != null">
        CREATOR_ID = #{record.creatorId,jdbcType=DECIMAL},
      </if>
      <if test="record.createTime != null">
        CREATE_TIME = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatorId != null">
        UPDATOR_ID = #{record.updatorId,jdbcType=DECIMAL},
      </if>
      <if test="record.updateTime != null">
        UPDATE_TIME = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from ATR_CONF_QUOTA_DYM
    where QUOTA_ID = #{quotaId,jdbcType=DECIMAL}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from ATR_CONF_QUOTA_DYM
    where QUOTA_ID in
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from ATR_CONF_QUOTA_DYM
    where
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultType="Long" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDym">
    select count(1) from ATR_CONF_QUOTA_DYM
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>