package com.ss.ifrs.actuarial.feign;

import com.ss.platform.pojo.bbs.vo.BbsConfRiskClassVo;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.conf.FeignAuthConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;


/**
 * @ClassName BbsRiskClassFeignClient
 * @Description TODO 调用风险大类信息的接口
 * <AUTHOR>
 * @Date 2022/1/24
 **/
@FeignClient(name = "SS-PLATFORM-COMMON", configuration = { FeignAuthConfig.class })
public interface BbsRiskClassFeignClient {

    /**
     * @Method
     * <AUTHOR>
     * @Date 2022/1/24
     * @Description 根据险种代码查询险种
     */
    @RequestMapping(method = RequestMethod.GET, value = "/risk_class/find_by_code/{riskClass}")
    @PermissionRequest(required = false)
    BbsConfRiskClassVo findByRiskClass(@PathVariable("riskClass") String riskClass);
}
