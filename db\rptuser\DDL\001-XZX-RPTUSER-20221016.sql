-- 报表专项余额表和总账余额表增加索引
call rpt_pack_commonutils.DROP_TABLE_INDEX('rpt_idx_dap_article_account_id');
call rpt_pack_commonutils.DROP_TABLE_INDEX('rpt_idx_dap_ledger_account_id');

call rpt_pack_commonutils.ADD_TABLE_INDEX('RPT_DAP_ARTICLE_BALANCE','account_id,ENTITY_ID, BOOK_CODE, YEAR_MONTH','rpt_idx_dap_article_account_id');

call rpt_pack_commonutils.ADD_TABLE_INDEX('RPT_DAP_LEDGER_BALANCE','account_id,ENTITY_ID, BOOK_CODE, YEAR_MONTH','rpt_idx_dap_ledger_account_id');

commit;