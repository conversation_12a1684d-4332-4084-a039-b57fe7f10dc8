package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.ti;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class AtrDapLrcTiEdPlan {

    private Long planMainId;

    private String firstYearMonth;

    private String yearMonth;

    private Long entityId;

    private Date effectiveDate;

    private Date expiryDate;

    private String treatyName;

    private String treatyNo;

    private String riskClassCode;

    private String riskCode;

    private Integer devNo;

    private BigDecimal premium;

}
