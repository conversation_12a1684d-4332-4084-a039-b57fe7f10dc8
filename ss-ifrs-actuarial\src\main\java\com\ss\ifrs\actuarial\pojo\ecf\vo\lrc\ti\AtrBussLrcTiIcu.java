package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.ti;

import com.ss.ifrs.actuarial.util.abp.IgnoreCol;
import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 合约分入计量单元表 - 对应表 atr_buss_ti_lrc_u
 * <AUTHOR>
 */
@Data
@Tab("atr_buss_ti_lrc_u")
public class AtrBussLrcTiIcu {
    /** ID (id) */
    private Long id;

    /** 执行编号 (action_no) */
    private String actionNo;

    /** 业务单位ID (entity_id) */
    private Long entityId;

    /** 评估期年月 (year_month) */
    private String yearMonth;

    /** 险类代码 (risk_class_code) */
    private String riskClassCode;

    /** 合约号 (treaty_no) */
    private String treatyNo;
    
    /** 合约名称 (treaty_name) */
    private String treatyName;

    /** 合同组合编号 (portfolio_no) */
    private String portfolioNo;

    /** 合同组号码 (icg_no) */
    private String icgNo;
    
    /** 合同组名称 (icg_name) */
    private String icgName;

    /** 计量单元编号 (cmunit_no) */
    private String cmunitNo;

    /** 一级机构 (company_code1) */
    private String companyCode1;

    /** 二级机构 (company_code2) */
    private String companyCode2;

    /** 三级机构 (company_code3) */
    private String companyCode3;

    /** 四级机构 (company_code4) */
    private String companyCode4;
    
    /** 核算机构 (center_code) */
    private String centerCode;

    /** 起保日期 (effective_date) */
    private Date effectiveDate;

    /** 终保日期 (expiry_date) */
    private Date expiryDate;

    /** 合同确认日期 (contract_date) */
    private Date contractDate;
    
    /** 合约录入日期 (input_date) */
    private Date inputDate;
    
    /** 合约核保日期 (approval_date) */
    private Date approvalDate;

    /** 业务发生年月 (dap_year_month) */
    private String dapYearMonth;

    /** 保费 (premium) */
    private BigDecimal premium;

    /** 净额结算手续费率 (net_fee_rate) */
    private BigDecimal netFeeRate;

    /** 净额结算手续费 (net_fee) */
    private BigDecimal netFee;

    /** 跟单手续费 (iacf) */
    private BigDecimal iacf;

    private BigDecimal iaehcIn;
    private BigDecimal iaehcOut;

    /** 经纪费率 (brokerage_rate) */
    private BigDecimal brokerageRate;

    /** 固定手续费率 (fixed_fee_rate) */
    private BigDecimal fixedFeeRate;

    /** 预付手续费率 (prepaid_fee_rate) */
    private BigDecimal prepaidFeeRate;

    /** 上期累计实收保费 (pre_cuml_paid_premium) */
    private BigDecimal preCumlPaidPremium;

    /** 上期累计实收净额结算手续费 (pre_cuml_paid_net_fee) */
    private BigDecimal preCumlPaidNetFee;

    /** 上期累计实收跟单获取费用 (pre_cuml_paid_iacf) */
    private BigDecimal preCumlPaidIacf;

    /** 上期累计已赚保费 (pre_cuml_ed_premium) */
    private BigDecimal preCumlEdPremium;

    /** 上期累计已赚净额结算手续费 (pre_cuml_ed_net_fee) */
    private BigDecimal preCumlEdNetFee;

    /** 上期累计已赚跟单获取费用 (pre_cuml_ed_iacf) */
    private BigDecimal preCumlEdIacf;

    /** 上期累计已赚非跟单获取费用-对内 (pre_cuml_ed_iaehc_in) */
    private BigDecimal preCumlEdIaehcIn;

    /** 上期累计已赚非跟单获取费用-对外 (pre_cuml_ed_iaehc_out) */
    private BigDecimal preCumlEdIaehcOut;

    /** 当期已赚保费 (cur_ed_premium) */
    private BigDecimal curEdPremium;

    /** 当期已赚净额结算手续费 (cur_ed_net_fee) */
    private BigDecimal curEdNetFee;

    /** 当期已赚跟单获取费用 (cur_ed_iacf) */
    private BigDecimal curEdIacf;

    /** 当期已赚非跟单获取费用-对内 (cur_ed_iaehc_in) */
    private BigDecimal curEdIaehcIn;

    /** 当期已赚非跟单获取费用-对外 (cur_ed_iaehc_out) */
    private BigDecimal curEdIaehcOut;

    /** 财务渠道 (fin_acc_channel) */
    private String finAccChannel;
    
    /** 财务产品代码 (fin_product_code) */
    private String finProductCode;
    
    /** 财务明细代码 (fin_detail_code) */
    private String finDetailCode;
    
    /** 财务子产品代码 (fin_sub_product_code) */
    private String finSubProductCode;
    
    /** 盈亏判定结果 (pl_judge_rslt) */
    private String plJudgeRslt;

    private String deptId;
    private String channelId;

    private String riskCode;

    private BigDecimal invAmount;

    @IgnoreCol
    private BigDecimal totalBadDebt;

    @IgnoreCol
    private BigDecimal floatingHandlingFeeCap;
}
