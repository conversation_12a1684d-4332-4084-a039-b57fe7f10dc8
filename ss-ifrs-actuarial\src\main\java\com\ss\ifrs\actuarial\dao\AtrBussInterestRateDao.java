package com.ss.ifrs.actuarial.dao;

import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfCbTreasuryYc;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestBase;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestRateDetail;
import com.ss.ifrs.actuarial.pojo.ecf.vo.ir.AtrBussIrForward;
import com.ss.ifrs.actuarial.pojo.ecf.vo.ir.AtrBussIrForwardDev;
import com.ss.ifrs.actuarial.pojo.ecf.vo.ir.AtrConfIr;
import com.ss.ifrs.actuarial.pojo.ecf.vo.ir.AtrConfIrDetail;
import com.ss.ifrs.actuarial.pojo.ecf.vo.ir.AtrDuctInitialRateBase;
import com.ss.ifrs.actuarial.pojo.ecf.vo.ir.AtrDuctWeightBussData;
import com.ss.ifrs.actuarial.pojo.ecf.vo.ir.AtrDuctWeightRateBase;
import com.ss.ifrs.actuarial.pojo.ecf.vo.ir.AtrBussIrInitialDev;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 利率计算DAO接口
 */
@Mapper
public interface AtrBussInterestRateDao {

    /**
     * 根据interestRateId查询中债国债收益率曲线数据
     * @param interestRateId 利率ID
     * @return 中债国债收益率曲线数据列表
     */
    List<AtrConfCbTreasuryYc> findCbTreasuryYcByInterestRateId(@Param("interestRateId") Long interestRateId);

    /**
     * 根据code查询基础参数数据
     * @param code 参数编码
     * @return 基础参数数据
     */
    AtrConfInterestBase findInterestBaseByCode(@Param("code") String code);

    /**
     * 获取最大的Initial ID
     * @return 最大ID
     */
    Long getMaxInitialId();

    /**
     * 删除Initial Dev表数据
     * @param interestRateId 利率ID
     */
    void deleteInitialDev(@Param("interestRateId") Long interestRateId);

    /**
     * 删除Initial表数据
     * @param interestRateId 利率ID
     */
    void deleteInitial(@Param("interestRateId") Long interestRateId);

    /**
     * 批量保存利率明细
     * @param detailList 利率明细列表
     */
    void saveInterestRateDetailBatch(List<AtrConfInterestRateDetail> detailList);

    /**
     * 根据年月查询上传利率配置的ID
     * @param yearMonth 年月
     * @return 上传利率配置ID
     */
    Long findUploadRateIdForYearMonth(@Param("yearMonth") String yearMonth);

    /**
     * 确认forward表数据
     * @param uploadRateId 上传利率ID
     */
    void confirmForward(@Param("uploadRateId") Long uploadRateId);

    /**
     * 确认initial表数据
     * @param uploadRateId 上传利率ID
     */
    void confirmInitial(@Param("uploadRateId") Long uploadRateId);

    /**
     * 获取最大的forward ID
     * @return 最大ID
     */
    Long getMaxForwardId();

    /**
     * 删除Forward Dev表数据
     * @param uploadRateId 上传利率ID
     */
    void deleteForwardDev(@Param("uploadRateId") Long uploadRateId);

    /**
     * 删除Forward表数据
     * @param uploadRateId 上传利率ID
     */
    void deleteForward(@Param("uploadRateId") Long uploadRateId);

    /**
     * 查询主表数据
     * @param interestRateId 利率ID
     * @return 主表数据
     */
    AtrConfIr findConfMain(@Param("interestRateId") Long interestRateId);

    /**
     * 查询明细表数据
     * @param interestRateId 利率ID
     * @return 明细表数据列表
     */
    List<AtrConfIrDetail> findConfDetail(@Param("interestRateId") Long interestRateId);

    /**
     * 获取最大的weight ID
     * @return 最大ID
     */
    Long getMaxWeightId();

    /**
     * 删除Weight Dev表数据
     * @param entityId 机构ID
     * @param yearMonth 年月
     */
    void deleteWeightDev(@Param("entityId") Long entityId, @Param("yearMonth") String yearMonth);

    /**
     * 删除Weight表数据
     * @param entityId 机构ID
     * @param yearMonth 年月
     */
    void deleteWeight(@Param("entityId") Long entityId, @Param("yearMonth") String yearMonth);

    /**
     * 获取初始利率上传ID
     * @param entityId 机构ID
     * @param yearMonth 年月
     * @param currencyCode 币种代码
     * @return 上传利率ID
     */
    Long getInitialUploadId(@Param("entityId") Long entityId, @Param("yearMonth") String yearMonth, @Param("currencyCode") String currencyCode);

    /**
     * 收集权重数据
     * @param entityId 机构ID
     * @param yearMonth 年月
     * @return 权重数据列表
     */
    List<AtrDuctWeightBussData> collectWeightData(@Param("entityId") Long entityId, @Param("yearMonth") String yearMonth);

    /**
     * 收集权重基础利率数据
     * @param entityId 机构ID
     * @param yearMonth 年月
     * @param currencyCode 币种代码
     * @return 权重基础利率数据列表
     */
    List<AtrDuctWeightRateBase> collectWeightRateBase(@Param("entityId") Long entityId, @Param("yearMonth") String yearMonth, @Param("currencyCode") String currencyCode);

    /**
     * 收集上一期初始利率基础数据
     * @param entityId 机构ID
     * @param yearMonth 年月
     * @param currencyCode 币种代码
     * @return 上一期初始利率基础数据列表
     */
    List<AtrDuctInitialRateBase> collectPreInitialRateBase(@Param("entityId") Long entityId, @Param("yearMonth") String yearMonth, @Param("currencyCode") String currencyCode);

    /**
     * 收集初始利率基础数据
     * @param uploadRateId 上传利率ID
     * @return 初始利率基础数据列表
     */
    List<AtrDuctInitialRateBase> collectInitialRateBase(@Param("uploadRateId") Long uploadRateId);

    /**
     * 获取最大的InterestRateDetail ID
     * @return 最大ID
     */
    Long getMaxInterestRateDetailId();

    /**
     * 删除InterestRateDetail表数据
     * @param interestRateId 利率ID
     */
    void deleteInterestRateDetail(@Param("interestRateId") Long interestRateId);

    /**
     * 批量保存初始利率发展期数据
     * @param initialDevList 初始利率发展期数据列表
     */
    void saveInitialDevBatch(List<AtrBussIrInitialDev> initialDevList);

    /**
     * 根据上传ID获取月度远期利率
     * @param uploadId 上传ID
     * @return 利率明细列表
     */
    List<AtrConfInterestRateDetail> findMonthlyForwardRatesByUploadId(@Param("uploadId") Long uploadId);
}
