package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.x;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class AtrDuctLrcToxPolicy {

    private String policyNo;
    private String endorseSeqNo;
    private String kindCode;
    private String riskClassCode;
    private String riskCode;
    private String issueYear;
    private Date effectiveDate;
    private Date expiryDate;
    private BigDecimal premium;
    private Long entityId;

    /** 满期保费 */
    private BigDecimal earnedPremium;
    
    /** 财务产品代码 */
    private String finProductCode;
    
    /** 财务明细代码 */
    private String finDetailCode;
    
    /** 财务子产品代码 */
    private String finSubProductCode;
    
    /** 部门段 */
    private String deptId;
    
    /** 渠道段 */
    private String channelId;
    
    /** 核算机构 */
    private String centerCode;
}
