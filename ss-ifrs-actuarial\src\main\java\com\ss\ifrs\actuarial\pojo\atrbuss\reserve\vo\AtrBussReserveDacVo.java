/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2022-02-15 17:55:55
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2022-02-15 17:55:55<br/>
 * Description: null<br/>
 * Table Name: atr_buss_reserve_dac<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
public class AtrBussReserveDacVo implements Serializable {
    /**
     * Database column: atr_buss_reserve_dac.reserve_dac_id
     * Database remarks: 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    private Long reserveDacId;

    /**
     * Database column: atr_buss_reserve_dac.version_no
     * Database remarks: version_no|版本号
     */
    @ApiModelProperty(value = "version_no|版本号", required = false)
    private String versionNo;

    /**
     * Database column: atr_buss_reserve_dac.center_id
     * Database remarks: 业务单位id
     */
    @ApiModelProperty(value = "业务单位id", required = false)
    @NotNull(message = "The Center Id can't be null|业务单位不能为空|業務單位不能為空")
    //@DecimalMax(value = "2048", message = "Center Id must be less than 2048|业务单位必须小于2048|業務單位必須小於2048")
    private Long entityId;

    /**
     * Database column: atr_buss_reserve_dac.year_month
     * Database remarks: 提取年月
     */
    @ApiModelProperty(value = "提取年月", required = false)
    private String yearMonth;

    /**
     * Database column: atr_buss_reserve_dac.risk_class
     * Database remarks: 险类
     */
    @ApiModelProperty(value = "险类", required = false)
    private String riskClassCode;

    /**
     * Database column: atr_buss_reserve_dac.loa_code
     * Database remarks: loaCode|业务线
     */
    @ApiModelProperty(value = "loaCode|业务线", required = false)
    private String loaCode;

    /**
     * Database column: atr_buss_reserve_dac.risk_code
     * Database remarks: 险种
     */
    @ApiModelProperty(value = "险种", required = false)
    private String riskCode;

    /**
     * Database column: atr_buss_reserve_dac.data_source
     * Database remarks: null
     */
    private String dataSource;

    /**
     * Database column: atr_buss_reserve_dac.draw_time
     * Database remarks: null
     */
    @ApiModelProperty(value = "draw_end_time|提取终止时间", required = false)
    @NotNull(message = "The draw　time can't be null|提取终止时间不能为空|提數終止時間不能為空")
    private Date drawTime;

    /**
     * Database column: atr_buss_reserve_dac.confirm_is
     * Database remarks: null
     */
    private String confirmIs;

    /**
     * Database column: atr_buss_reserve_dac.confirm_id
     * Database remarks: null
     */
    private Long confirmId;

    /**
     * Database column: atr_buss_reserve_dac.confirm_time
     * Database remarks: null
     */
    private Date confirmTime;

    /**
     * Database column: atr_buss_reserve_dac.creator_id
     * Database remarks: null
     */
    private Long creatorId;

    /**
     * Database column: atr_buss_reserve_dac.create_time
     * Database remarks: null
     */
    private Date createTime;

    /**
     * Database column: atr_buss_reserve_dac.updator_id
     * Database remarks: null
     */
    private Long updatorId;

    /**
     * Database column: atr_buss_reserve_dac.update_time
     * Database remarks: null
     */
    private Date updateTime;

    private String entityCode;
    private String entityEName;
    private String entityCName;
    private String entityLName;


    private String loaCName;
    private String loaEName;
    private String loaLName;

    private String classCName;
    private String classEName;
    private String classLName;

    private String creatorName;

    private List<String> columnList;
    private String columnSql;

    private static final long serialVersionUID = 1L;

    public Long getReserveDacId() {
        return reserveDacId;
    }

    public void setReserveDacId(Long reserveDacId) {
        this.reserveDacId = reserveDacId;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }	

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public String getRiskCode() {
        return riskCode;
    }

    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public Date getDrawTime() {
        return drawTime;
    }

    public void setDrawTime(Date drawTime) {
        this.drawTime = drawTime;
    }

    public String getConfirmIs() {
        return confirmIs;
    }

    public void setConfirmIs(String confirmIs) {
        this.confirmIs = confirmIs;
    }

    public Long getConfirmId() {
        return confirmId;
    }

    public void setConfirmId(Long confirmId) {
        this.confirmId = confirmId;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getEntityCode() {
        return entityCode;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public String getEntityEName() {
        return entityEName;
    }

    public void setEntityEName(String entityEName) {
        this.entityEName = entityEName;
    }

    public String getEntityCName() {
        return entityCName;
    }

    public void setEntityCName(String entityCName) {
        this.entityCName = entityCName;
    }

    public String getEntityLName() {
        return entityLName;
    }

    public void setEntityLName(String entityLName) {
        this.entityLName = entityLName;
    }

    public String getLoaCName() {
        return loaCName;
    }

    public void setLoaCName(String loaCName) {
        this.loaCName = loaCName;
    }

    public String getLoaEName() {
        return loaEName;
    }

    public void setLoaEName(String loaEName) {
        this.loaEName = loaEName;
    }

    public String getLoaLName() {
        return loaLName;
    }

    public void setLoaLName(String loaLName) {
        this.loaLName = loaLName;
    }

    public List<String> getColumnList() {
        return columnList;
    }

    public void setColumnList(List<String> columnList) {
        this.columnList = columnList;
    }

    public String getColumnSql() {
        return columnSql;
    }

    public void setColumnSql(String columnSql) {
        this.columnSql = columnSql;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public String getClassCName() {
        return classCName;
    }

    public void setClassCName(String classCName) {
        this.classCName = classCName;
    }

    public String getClassEName() {
        return classEName;
    }

    public void setClassEName(String classEName) {
        this.classEName = classEName;
    }

    public String getClassLName() {
        return classLName;
    }

    public void setClassLName(String classLName) {
        this.classLName = classLName;
    }
}