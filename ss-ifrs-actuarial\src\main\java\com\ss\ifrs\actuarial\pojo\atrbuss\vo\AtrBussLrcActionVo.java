/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-02-08 17:38:36
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-08 17:38:36<br/>
 * Description: LRC 预期现金流操作表<br/>
 * Table Name: ATR_BUSS_LRC_ACTION<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "LRC 预期现金流操作表")
public class AtrBussLrcActionVo implements Serializable {
    /**
     * Database column: ATR_BUSS_LRC_ACTION.ID
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.ACTION_NO
     * Database remarks: 执行编号|一次操作的全局唯一编号， 一次操作的维度是模块（LIC或LRC）、业务类型、业务单位、业务年月、币别
     */
    @ApiModelProperty(value = "执行编号|一次操作的全局唯一编号， 一次操作的维度是模块（LIC或LRC）、业务类型、业务单位、业务年月、币别", required = true)
    private String actionNo;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.TASK_CODE
     * Database remarks: 版本号
     */
    @ApiModelProperty(value = "版本号", required = true)
    private String taskCode;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.CENTER_ID
     * Database remarks: 业务单位ID
     */
    @ApiModelProperty(value = "业务单位ID", required = true)
    private Long entityId;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.YEAR_MONTH
     * Database remarks: 业务年月|评估期
     */
    @ApiModelProperty(value = "业务年月|评估期", required = true)
    private String yearMonth;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.CURRENCY
     * Database remarks: 币别
     */
    @ApiModelProperty(value = "币别", required = true)
    private String currencyCode;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.BUSINESS_TYPE
     * Database remarks: 业务类型|DD-直保&临分分入；FO-临分分出；TI-合约分入；TO-合约分出
     */
    @ApiModelProperty(value = "业务类型|DD-直保&临分分入；FO-临分分出；TI-合约分入；TO-合约分出", required = true)
    private String businessSourceCode;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.STATUS
     * Database remarks: 执行状态|R-执行中；E-执行异常；S-执行成功
     */
    @ApiModelProperty(value = "执行状态|R-执行中；E-执行异常；S-执行成功", required = true)
    private String status;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.CONFIRM_IS
     * Database remarks: 是否确认|1-是、0-否
     */
    @ApiModelProperty(value = "是否确认|1-是、0-否", required = true)
    private String confirmIs;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.CONFIRM_USER
     * Database remarks: 确认人
     */
    @ApiModelProperty(value = "确认人", required = false)
    private Long confirmUser;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.CONFIRM_TIME
     * Database remarks: 确认时间
     */
    @ApiModelProperty(value = "确认时间", required = false)
    private Date confirmTime;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.CREATOR_ID
     * Database remarks: 创建人
     */
    @ApiModelProperty(value = "创建人", required = false)
    private Long creatorId;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.CREATE_TIME
     * Database remarks: 创建时间
     */
    @ApiModelProperty(value = "创建时间", required = false)
    private Date createTime;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.UPDATOR_ID
     * Database remarks: 最后修改人
     */
    @ApiModelProperty(value = "最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: ATR_BUSS_LRC_ACTION.UPDATE_TIME
     * Database remarks: 最后修改时间
     */
    @ApiModelProperty(value = "最后修改时间", required = false)
    private Date updateTime;

    private String entityCode;
    private String entityCName;
    private String entityLName;
    private String entityEName;

    private String creatorName;

    private String language;

    private String templateFileName;

    private String logFileName;

    private String targetRouter;

    private String zipName;
    /**
     * 自动任务传输字段：任务模式：A-自动
     */
    private String taskMode;

    private String riskClassCode;

    private String loaCode;
    private String portfolioNo;
    private String icgNo;
    private List<Long> becfIds;
    private String yearMonthStart;
    private String yearMonthEnd;
    private String businessType;

    // 是否需要额外导出超赔分出模板
    private Boolean needExportTX;
    
    // 超赔分出模板名称
    private String txTemplateName;

    /**
     * 自动任务传输字段：重试次数
     */
    private Long retryOrder;

    private static final long serialVersionUID = 1L;

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }	

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getConfirmIs() {
        return confirmIs;
    }

    public void setConfirmIs(String confirmIs) {
        this.confirmIs = confirmIs;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getConfirmUser() {
        return confirmUser;
    }

    public void setConfirmUser(Long confirmUser) {
        this.confirmUser = confirmUser;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getEntityCode() {
        return entityCode;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public String getEntityCName() {
        return entityCName;
    }

    public void setEntityCName(String entityCName) {
        this.entityCName = entityCName;
    }

    public String getEntityLName() {
        return entityLName;
    }

    public void setEntityLName(String entityLName) {
        this.entityLName = entityLName;
    }

    public String getEntityEName() {
        return entityEName;
    }

    public void setEntityEName(String entityEName) {
        this.entityEName = entityEName;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public String getTemplateFileName() {
        return templateFileName;
    }

    public void setTemplateFileName(String templateFileName) {
        this.templateFileName = templateFileName;
    }

    public String getTargetRouter() {
        return targetRouter;
    }

    public void setTargetRouter(String targetRouter) {
        this.targetRouter = targetRouter;
    }

    public String getTaskMode() {
        return taskMode;
    }

    public void setTaskMode(String taskMode) {
        this.taskMode = taskMode;
    }

    public Long getRetryOrder() {
        return retryOrder;
    }

    public void setRetryOrder(Long retryOrder) {
        this.retryOrder = retryOrder;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getZipName() {
        return zipName;
    }

    public void setZipName(String zipName) {
        this.zipName = zipName;
    }

    public String getLogFileName() {
        return logFileName;
    }

    public void setLogFileName(String logFileName) {
        this.logFileName = logFileName;
    }

    public List<Long> getBecfIds() {
        return becfIds;
    }

    public void setBecfIds(List<Long> becfIds) {
        this.becfIds = becfIds;
    }

    public String getYearMonthStart() {
        return yearMonthStart;
    }

    public void setYearMonthStart(String yearMonthStart) {
        this.yearMonthStart = yearMonthStart;
    }

    public String getYearMonthEnd() {
        return yearMonthEnd;
    }

    public void setYearMonthEnd(String yearMonthEnd) {
        this.yearMonthEnd = yearMonthEnd;
    }

    public Boolean getNeedExportTX() {
        return needExportTX;
    }

    public void setNeedExportTX(Boolean needExportTX) {
        this.needExportTX = needExportTX;
    }

    public String getTxTemplateName() {
        return txTemplateName;
    }

    public void setTxTemplateName(String txTemplateName) {
        this.txTemplateName = txTemplateName;
    }
}