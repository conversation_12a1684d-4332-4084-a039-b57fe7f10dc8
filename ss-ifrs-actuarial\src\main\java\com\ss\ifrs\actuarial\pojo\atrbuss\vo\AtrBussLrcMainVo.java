/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-04-15 19:53:58
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2021-04-15 19:53:58<br/>
 * Description: LRC计量数据主表<br/>
 * Table Name: ATR_BUSS_LRC_MAIN<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "LRC计量数据主表")
public class AtrBussLrcMainVo implements Serializable {
    /**
     * Database column: ATR_BUSS_LRC_MAIN.LRC_MAIN_ID
     * Database remarks: lrc_main_id|主键
     */
    @ApiModelProperty(value = "lrc_main_id|主键", required = true)
    private Long lrcMainId;

    /**
     * Database column: ATR_BUSS_LRC_MAIN.CENTER_ID
     * Database remarks: Center_Id|核算单位ID
     */
    @ApiModelProperty(value = "Center_Id|核算单位ID", required = false)
    private Long entityId;

    /**
     * Database column: ATR_BUSS_LRC_MAIN.YEAR_MONTH
     * Database remarks: year_month|计量月份
     */
    @ApiModelProperty(value = "year_month|计量月份", required = false)
    private String yearMonth;

    /**
     * Database column: ATR_BUSS_LRC_MAIN.DATA_SOURCE
     * Database remarks: data_source|数据来源
     */
    @ApiModelProperty(value = "data_source|数据来源", required = false)
    private String dataSource;

    /**
     * Database column: ATR_BUSS_LRC_MAIN.DRAW_TIME
     * Database remarks: draw_time|提数时间
     */
    @ApiModelProperty(value = "draw_time|提数时间", required = false)
    private Date drawTime;

    /**
     * Database column: ATR_BUSS_LRC_MAIN.VERSION_NO
     * Database remarks: version_no|版本
     */
    @ApiModelProperty(value = "version_no|版本", required = false)
    private String versionNo;

    /**
     * Database column: ATR_BUSS_LRC_MAIN.EXECUTE_TIME
     * Database remarks: execute_time|执行时间
     */
    @ApiModelProperty(value = "execute_time|执行时间", required = false)
    private Date executeTime;

    /**
     * Database column: ATR_BUSS_LRC_MAIN.EXECUTOR_ID
     * Database remarks: executor_id|执行人
     */
    @ApiModelProperty(value = "executor_id|执行人", required = false)
    private Long executorId;

    /**
     * Database column: ATR_BUSS_LRC_MAIN.STATE
     * Database remarks: state|状态
     */
    @ApiModelProperty(value = "state|状态", required = false)
    private String state;

    /**
     * Database column: ATR_BUSS_LRC_MAIN.IS_CONFIRM
     * Database remarks: is_confirm|是否确认
     */
    @ApiModelProperty(value = "is_confirm|是否确认", required = false)
    private String isConfirm;

    /**
     * Database column: ATR_BUSS_LRC_MAIN.CONFIRM_ID
     * Database remarks: confirm_id|确认人
     */
    @ApiModelProperty(value = "confirm_id|确认人", required = false)
    private Long confirmId;

    /**
     * Database column: ATR_BUSS_LRC_MAIN.CONFIRM_TIME
     * Database remarks: confirm_time|确认时间
     */
    @ApiModelProperty(value = "confirm_time|确认时间", required = false)
    private Date confirmTime;

    /**
     * Database column: ATR_BUSS_LRC_MAIN.CREATOR_ID
     * Database remarks: Creator_Id|创建人
     */
    @ApiModelProperty(value = "Creator_Id|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: ATR_BUSS_LRC_MAIN.CREATE_TIME
     * Database remarks: Create_Time|创建时间
     */
    @ApiModelProperty(value = "Create_Time|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: ATR_BUSS_LRC_MAIN.UPDATOR_ID
     * Database remarks: Updator_Id|最后修改人
     */
    @ApiModelProperty(value = "Updator_Id|最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: ATR_BUSS_LRC_MAIN.UPDATE_TIME
     * Database remarks: Update_Time|最后修改时间
     */
    @ApiModelProperty(value = "Update_Time|最后修改时间", required = false)
    private Date updateTime;

    private String currencyCode;
    /**
     * Database column: ATR_BUSS_LRC_MAIN.CALC_TYPE
     * Database remarks: null
     */
    private String riskClassCode;

    private String atrType;

    private String entityCode;

    private String entityEName;

    private String entityCName;

    private String entityLName;

    private String classEName;

    private String classCName;

    private String classLName;

    private static final long serialVersionUID = 1L;

    public Long getLrcMainId() {
        return lrcMainId;
    }

    public void setLrcMainId(Long lrcMainId) {
        this.lrcMainId = lrcMainId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }	

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public Date getDrawTime() {
        return drawTime;
    }

    public void setDrawTime(Date drawTime) {
        this.drawTime = drawTime;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

    public Date getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(Date executeTime) {
        this.executeTime = executeTime;
    }

    public Long getExecutorId() {
        return executorId;
    }

    public void setExecutorId(Long executorId) {
        this.executorId = executorId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getIsConfirm() {
        return isConfirm;
    }

    public void setIsConfirm(String isConfirm) {
        this.isConfirm = isConfirm;
    }

    public Long getConfirmId() {
        return confirmId;
    }

    public void setConfirmId(Long confirmId) {
        this.confirmId = confirmId;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public String getEntityCode() {
        return entityCode;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public String getEntityEName() {
        return entityEName;
    }

    public void setEntityEName(String entityEName) {
        this.entityEName = entityEName;
    }

    public String getEntityCName() {
        return entityCName;
    }

    public void setEntityCName(String entityCName) {
        this.entityCName = entityCName;
    }

    public String getEntityLName() {
        return entityLName;
    }

    public void setEntityLName(String entityLName) {
        this.entityLName = entityLName;
    }

    public String getClassEName() {
        return classEName;
    }

    public void setClassEName(String classEName) {
        this.classEName = classEName;
    }

    public String getClassCName() {
        return classCName;
    }

    public void setClassCName(String classCName) {
        this.classCName = classCName;
    }

    public String getClassLName() {
        return classLName;
    }

    public void setClassLName(String classLName) {
        this.classLName = classLName;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getAtrType() {
        return atrType;
    }

    public void setAtrType(String atrType) {
        this.atrType = atrType;
    }
}