package com.ss.ifrs.actuarial.pojo.ecf.vo.ir;

import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Tab("atr_buss_ir_weight")
public class AtrBussIrWeight {

    private Long id;

    private Long uploadRateId;

    private Long entityId;

    private String businessSourceCode;

    private String actionNo;

    private String yearMonth;

    private String currencyCode;

    private String portfolioNo;

    private String icgNo;

    private BigDecimal premium;

    private Date createTime;

}
