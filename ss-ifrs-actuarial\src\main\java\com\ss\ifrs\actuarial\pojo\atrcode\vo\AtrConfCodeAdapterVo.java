package com.ss.ifrs.actuarial.pojo.atrcode.vo;

import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator. Create Date: 2018-02-07
 * 16:18:36<br>
 * Description: AtrConfCodeAdapter|双击域和下拉列表查询配置表<br>
 * Table: atr_code_type<br>
 * <br>
 */
public class AtrConfCodeAdapterVo implements Serializable {
    /**
     * Database column: atr_code_type.code_config_id Database remarks:
     * codeConfigId|主键
     */
    private Long codeConfigId;

    /**
     * Database column: atr_code_type.code Database remarks: code|双击域和下拉框查询代码
     */
    private String code;

    /**
     * Database column: atr_code_type.poname Database remarks:
     * poName|双击域和下拉查询表名,条件多个用，分隔
     */
    private String poName;

    /**
     * Database column: atr_code_type.searchparams Database remarks:
     * searchParams|双击域和下拉查询条件
     */
    private String searchParams;

    /**
     * Database column: atr_code_type.creatorcode Database remarks:
     * creatorId|创建人代码
     */
    private Long creatorId;

    /**
     * Database column: atr_code_type.createtime Database remarks:
     * createTime|创建时间
     */
    private Date createTime;

    /**
     * Database column: atr_code_type.updatercode Database remarks:
     * updatorId|修改人代码
     */
    private Long updatorId;

    /**
     * Database column: atr_code_type.updatetime Database remarks:
     * updateTime|修改时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public Long getCodeConfigId() {
        return codeConfigId;
    }

    public void setCodeConfigId(Long codeConfigId) {
        this.codeConfigId = codeConfigId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getPoName() {
        return poName;
    }

    public void setPoName(String poName) {
        this.poName = poName;
    }

    public String getSearchParams() {
        return searchParams;
    }

    public void setSearchParams(String searchParams) {
        this.searchParams = searchParams;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}