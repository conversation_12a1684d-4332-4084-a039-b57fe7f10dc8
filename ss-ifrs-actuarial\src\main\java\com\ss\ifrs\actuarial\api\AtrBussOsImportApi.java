package com.ss.ifrs.actuarial.api;

import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussIbnrImportMainVo;
import com.ss.ifrs.actuarial.service.AtrBussIbnrImportService;
import com.ss.ifrs.actuarial.service.AtrExportService;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ExceptionUtil;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.annotation.TrackUserBehavioral;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/os_import")
@Api(value = "导入接口")
public class AtrBussOsImportApi extends BaseApi {

    @Autowired
    AtrBussIbnrImportService atrBussIbnrImportService;

    @Autowired
    AtrExportService atrExportService;

    /**
     *  becf模块
     * */
    @ApiOperation(value = "赔付现金流查询")
    @TrackUserBehavioral(description = "enquiry CashFlow")
    @RequestMapping(value = "/enquiry", method = RequestMethod.POST)
    public BaseResponse<Map<String, Object>> enquiryBecfPage(@RequestBody AtrBussIbnrImportMainVo atrBussBecfMainVo, int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrBussIbnrImportMainVo> atrConfInterestRateVoList = atrBussIbnrImportService.searchLicPage(atrBussBecfMainVo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("bussBecfMainVoList", atrConfInterestRateVoList);
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "下载上传模板")
    @TrackUserBehavioral(description = "addImportInterestRate")
    @RequestMapping(value = "/exportTemplate", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public void downTemplate(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrBussIbnrImportMainVo atrBussBecfMainVo) {
        try {
            Long userId = this.loginUserId(request);
            atrExportService.downloadTemplateExcel(request, response, atrBussBecfMainVo.getTemplateFileName(), null, userId);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
        }
    }

    @ApiOperation(value = "赔付现金流导入")
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> becfImport(HttpServletRequest request, @RequestParam(value = "file", required = false) MultipartFile file,
                                             AtrBussIbnrImportMainVo atrBussIbnrImportMainVo)  {
        Long userId = this.loginUserId(request);
        String result = "0";
        String exceptionMsg = null;
        try{
            result = atrBussIbnrImportService.licExcelImport(file, atrBussIbnrImportMainVo, userId);
        } catch (Exception e) {
            exceptionMsg = ExceptionUtil.getMessage(e);
            logger.error(e.getLocalizedMessage(), e);
        }
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, exceptionMsg, result);
    }

    @ApiOperation(value = "赔付现金流确认")
    @RequestMapping(value = "/confirm", method = RequestMethod.POST)
    public BaseResponse<Object> confirm(@RequestBody AtrBussIbnrImportMainVo atrBussIbnrImportMainVo, HttpServletRequest request) {
        Long userId = this.loginUserId(request);
        try {
            atrBussIbnrImportService.licDataConfirm(atrBussIbnrImportMainVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "赔付现金流是否已确认")
    @RequestMapping(value = "/check_confirm", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> checkPK(@RequestBody AtrBussIbnrImportMainVo atrBussBecfMainVo) {
        String result = "0";
        try {
            List<AtrBussIbnrImportMainVo> vos = atrBussIbnrImportService.findIbnrList(atrBussBecfMainVo);
            if(vos.size()>0){
                result = "1";
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, result);
        }
    }

    @ApiOperation(value = "根据id查询赔付现金流信息")
    @RequestMapping(value = "/find_by_id/{ibnrMainId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<AtrBussIbnrImportMainVo> findById(@PathVariable("ibnrMainId") Long ibnrMainId) {
        AtrBussIbnrImportMainVo atrBussBecfMainVo = atrBussIbnrImportService.findById(ibnrMainId);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, atrBussBecfMainVo);
    }

    @ApiOperation(value = "根据id删除赔付现金流信息")
    @RequestMapping(value = "/delete_by_pk/{ibnrMainId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<Object> deleteById(@PathVariable("ibnrMainId") Long ibnrMainId) {
        AtrBussIbnrImportMainVo atrBussBecfMainVo = atrBussIbnrImportService.deleteIbnrByBecfId(ibnrMainId);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, atrBussBecfMainVo);
    }

 
}
