/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2022-06-06 10:15:00
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2022-06-06 10:15:00<br/>
 * Description: IBNR明细数据表<br/>
 * Table Name: atr_buss_reserve_ibnr_detail<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "IBNR明细数据表")
public class AtrBussReserveIbnrDetailVo implements Serializable {
    /**
     * Database column: atr_buss_reserve_ibnr_detail.ibnr_detail_id
     * Database remarks: Ibnr_Detail_Id|主键
     */
    @ApiModelProperty(value = "Ibnr_Detail_Id|主键", required = true)
    private Long ibnrDetailId;

    /**
     * Database column: atr_buss_reserve_ibnr_detail.reserve_ibnr_id
     * Database remarks: reserve_ibnr_id|IBNR提取主表主键
     */
    @ApiModelProperty(value = "reserve_ibnr_id|IBNR提取主表主键", required = true)
    private Long reserveIbnrId;

    /**
     * Database column: atr_buss_reserve_ibnr_detail.data_type
     * Database remarks: Data_Type|数据类型
     */
    @ApiModelProperty(value = "Data_Type|数据类型", required = true)
    private String dataType;

    /**
     * Database column: atr_buss_reserve_ibnr_detail.damage_year_month
     * Database remarks: Damage_Year_Month|事故出险月份
     */
    @ApiModelProperty(value = "Damage_Year_Month|事故出险月份", required = false)
    private String damageYearMonth;

    /**
     * Database column: atr_buss_reserve_ibnr_detail.dev_period
     * Database remarks: Dev_Period|发展期次
     */
    @ApiModelProperty(value = "Dev_Period|发展期次", required = true)
    private Integer devPeriod;

    /**
     * Database column: atr_buss_reserve_ibnr_detail.currency
     * Database remarks: Currency|币别（原币）
     */
    @ApiModelProperty(value = "Currency|币别（原币）", required = false)
    private String currencyCode;

    /**
     * Database column: atr_buss_reserve_ibnr_detail.amount
     * Database remarks: Amount|金额（原币）
     */
    @ApiModelProperty(value = "Amount|金额（原币）", required = false)
    private BigDecimal value;

    /**
     * Database column: atr_buss_reserve_ibnr_detail.exch_rate
     * Database remarks: EXCHANGE_RATE|兑换率
     */
    @ApiModelProperty(value = "EXCHANGE_RATE|兑换率", required = false)
    private BigDecimal exchangeRate;

    private List<AtrBussReserveIbnrDetailVo> devYearMonthList;
    private List<AtrBussReserveIbnrDetailVo> thisYearMonthList;

    private BigDecimal atrFCValue;
    private BigDecimal atrTOSValue;
    private BigDecimal atrIBNRValue;


    private String codeCodeInx;

    private String codeCode;
    private String codeCName;
    private String codeLName;
    private String codeEName;

    private BigDecimal total;

    List<String> devNoList;

    List<String> yearMonthList;


    @ApiModelProperty(value = "loaCode|风险大类", required = true)
    private String loaCode;

    @ApiModelProperty(value = "portfolio_no|合同组合", required = true)
    private String portfolioNo;

    private static final long serialVersionUID = 1L;

    public BigDecimal getTotal() {
        return total;
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }

    public List<AtrBussReserveIbnrDetailVo> getThisYearMonthList() {
        return thisYearMonthList;
    }

    public void setThisYearMonthList(List<AtrBussReserveIbnrDetailVo> thisYearMonthList) {
        this.thisYearMonthList = thisYearMonthList;
    }

    public String getCodeCode() {
        return codeCode;
    }

    public void setCodeCode(String codeCode) {
        this.codeCode = codeCode;
    }

    public String getCodeCName() {
        return codeCName;
    }

    public void setCodeCName(String codeCName) {
        this.codeCName = codeCName;
    }

    public String getCodeLName() {
        return codeLName;
    }

    public void setCodeLName(String codeLName) {
        this.codeLName = codeLName;
    }

    public String getCodeEName() {
        return codeEName;
    }

    public void setCodeEName(String codeEName) {
        this.codeEName = codeEName;
    }

    public String getCodeCodeInx() {
        return codeCodeInx;
    }

    public void setCodeCodeInx(String codeCodeInx) {
        this.codeCodeInx = codeCodeInx;
    }

    public List<AtrBussReserveIbnrDetailVo> getDevYearMonthList() {
        return devYearMonthList;
    }

    public void setDevYearMonthList(List<AtrBussReserveIbnrDetailVo> devYearMonthList) {
        this.devYearMonthList = devYearMonthList;
    }

    public Long getIbnrDetailId() {
        return ibnrDetailId;
    }

    public void setIbnrDetailId(Long ibnrDetailId) {
        this.ibnrDetailId = ibnrDetailId;
    }

    public Long getReserveIbnrId() {
        return reserveIbnrId;
    }

    public void setReserveIbnrId(Long reserveIbnrId) {
        this.reserveIbnrId = reserveIbnrId;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getDamageYearMonth() {
        return damageYearMonth;
    }

    public void setDamageYearMonth(String damageYearMonth) {
        this.damageYearMonth = damageYearMonth;
    }

    public Integer getDevPeriod() {
        return devPeriod;
    }

    public void setDevPeriod(Integer devPeriod) {
        this.devPeriod = devPeriod;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal Amount) {
        this.value = Amount;
    }

    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public BigDecimal getAtrFCValue() {
        return atrFCValue;
    }

    public void setAtrFCValue(BigDecimal atrFCValue) {
        this.atrFCValue = atrFCValue;
    }

    public BigDecimal getAtrTOSValue() {
        return atrTOSValue;
    }

    public void setAtrTOSValue(BigDecimal atrTOSValue) {
        this.atrTOSValue = atrTOSValue;
    }

    public BigDecimal getAtrIBNRValue() {
        return atrIBNRValue;
    }

    public void setAtrIBNRValue(BigDecimal atrIBNRValue) {
        this.atrIBNRValue = atrIBNRValue;
    }

    public List<String> getDevNoList() {
        return devNoList;
    }

    public void setDevNoList(List<String> devNoList) {
        this.devNoList = devNoList;
    }

    public List<String> getYearMonthList() {
        return yearMonthList;
    }

    public void setYearMonthList(List<String> yearMonthList) {
        this.yearMonthList = yearMonthList;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }
}