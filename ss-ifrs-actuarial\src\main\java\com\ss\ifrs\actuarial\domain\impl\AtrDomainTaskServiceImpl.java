package com.ss.ifrs.actuarial.domain.impl;

import com.alibaba.fastjson.JSONObject;
import com.ss.ifrs.actuarial.annotation.TrackActuarialProcess;
import com.ss.ifrs.actuarial.dao.AtrBussDataCheckDao;
import com.ss.ifrs.actuarial.domain.service.AtrDomainTaskService;
import com.ss.ifrs.actuarial.feign.BmsActProcFeignClient;
import com.ss.ifrs.actuarial.feign.BmsTrackBussActionFeignClient;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.pojo.com.vo.ActOverviewVo;
import com.ss.platform.pojo.com.vo.BussActionStateVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.UnexpectedRollbackException;

import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AtrDomainTaskServiceImpl implements AtrDomainTaskService {

    @Autowired
    private BmsTrackBussActionFeignClient bmsTrackBussActionFeignClient;

    @Autowired
    private AtrBussDataCheckDao atrBussDataCheckDao;

    @Autowired
    private BmsActProcFeignClient bmsActProcFeignClient;

    @Override
    @Async("ruleThreadPool")
    public void mergeActuarialProcessState(BussActionStateVo bussActionStateVo) {
        try {
            bmsTrackBussActionFeignClient.synchronizeProcessNodeStatus(bussActionStateVo);
        } catch (Exception e) {
            log.error("Merge Expense Process State :{}", e.getMessage());
        }

    }

    @Override
    @TrackActuarialProcess
    public void configCheck(AtrConfBussPeriodVo atrConfBussPeriodVo) {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("taskCode", atrConfBussPeriodVo.getTaskCode());
            param.put("entityId", atrConfBussPeriodVo.getEntityId());
            param.put("bookCode", null);
            param.put("yearMonth", atrConfBussPeriodVo.getYearMonth());
            param.put("procId", atrConfBussPeriodVo.getProcId());
            param.put("userId", atrConfBussPeriodVo.getCreatorId());

            // 调用执行
            atrBussDataCheckDao.checkRule(param);
        } catch (UnexpectedRollbackException e) {
            throw e;
        }
    }

    @Override
    public ActOverviewVo getActOverviewVoByObject(ActOverviewVo actOverviewVo) {
        ActOverviewVo result = new ActOverviewVo();
        try {
            BaseResponse<Object> actOverviewByObject = bmsActProcFeignClient.findActOverviewByObject(actOverviewVo);
            if (ObjectUtils.isNotEmpty(actOverviewByObject) && actOverviewByObject.getResCode().equals(ResCodeConstant.ResCode.SUCCESS) && ObjectUtils.isNotEmpty(actOverviewByObject.getResData())) {
                result = JSONObject.parseObject(JSONObject.toJSONString(actOverviewByObject.getResData()),
                        ActOverviewVo.class);
            }
        } catch (Exception e) {
            log.error("Description Failed to obtain flow node information ：{}", e.getMessage());
        }
        return result;
    }


}
