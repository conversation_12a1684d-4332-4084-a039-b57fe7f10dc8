/**
 * 
 * This file was generated by Taiping MyBatis Generator(v1.2.12).
 * Date: 2024-05-08 10:20:12
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by Taiping MyBatis Generator(v1.2.12).
 * Create Date: 2024-05-08 10:20:12<br/>
 * Description: ibnr计算-action下的基于出险时间节点的未来发展期数据<br/>
 * Table Name: atr_buss_ibnrcalc_acc_future_data<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "ibnr计算-action下的基于出险时间节点的未来发展期数据")
public class AtrBussIbnrcalcAccFutureData implements Serializable {
    /**
     * Database column: atr_buss_ibnrcalc_acc_future_data.id
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: atr_buss_ibnrcalc_acc_future_data.action_no
     * Database remarks: action编号
     */
    @ApiModelProperty(value = "action编号", required = true)
    private String actionNo;

    /**
     * Database column: atr_buss_ibnrcalc_acc_future_data.icp_id
     * Database remarks: 合同组合层ID
     */
    @ApiModelProperty(value = "合同组合层ID", required = true)
    private Long icpId;

    /**
     * Database column: atr_buss_ibnrcalc_acc_future_data.accident_node
     * Database remarks: 出险时间节点（年或月）
     */
    @ApiModelProperty(value = "出险时间节点（年或月）", required = true)
    private String accidentNode;

    /**
     * Database column: atr_buss_ibnrcalc_acc_future_data.dev_no
     * Database remarks: 发展期次
     */
    @ApiModelProperty(value = "发展期次", required = false)
    private Short devNo;

    /**
     * Database column: atr_buss_ibnrcalc_acc_future_data.future_node
     * Database remarks: 未来时间节点（年或月）
     */
    @ApiModelProperty(value = "未来时间节点（年或月）", required = false)
    private String futureNode;

    /**
     * Database column: atr_buss_ibnrcalc_acc_future_data.lr_reported_amount
     * Database remarks: LR 算法的已报告赔款
     */
    @ApiModelProperty(value = "LR 算法的已报告赔款", required = false)
    private BigDecimal lrReportedAmount;

    /**
     * Database column: atr_buss_ibnrcalc_acc_future_data.cl_reported_amount
     * Database remarks: CL 算法的已报告赔款
     */
    @ApiModelProperty(value = "CL 算法的已报告赔款", required = false)
    private BigDecimal clReportedAmount;

    /**
     * Database column: atr_buss_ibnrcalc_acc_future_data.bf_reported_amount
     * Database remarks: BF 算法的已报告赔款
     */
    @ApiModelProperty(value = "BF 算法的已报告赔款", required = false)
    private BigDecimal bfReportedAmount;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public Long getIcpId() {
        return icpId;
    }

    public void setIcpId(Long icpId) {
        this.icpId = icpId;
    }

    public String getAccidentNode() {
        return accidentNode;
    }

    public void setAccidentNode(String accidentNode) {
        this.accidentNode = accidentNode;
    }

    public Short getDevNo() {
        return devNo;
    }

    public void setDevNo(Short devNo) {
        this.devNo = devNo;
    }

    public String getFutureNode() {
        return futureNode;
    }

    public void setFutureNode(String futureNode) {
        this.futureNode = futureNode;
    }

    public BigDecimal getLrReportedAmount() {
        return lrReportedAmount;
    }

    public void setLrReportedAmount(BigDecimal lrReportedAmount) {
        this.lrReportedAmount = lrReportedAmount;
    }

    public BigDecimal getClReportedAmount() {
        return clReportedAmount;
    }

    public void setClReportedAmount(BigDecimal clReportedAmount) {
        this.clReportedAmount = clReportedAmount;
    }

    public BigDecimal getBfReportedAmount() {
        return bfReportedAmount;
    }

    public void setBfReportedAmount(BigDecimal bfReportedAmount) {
        this.bfReportedAmount = bfReportedAmount;
    }
}