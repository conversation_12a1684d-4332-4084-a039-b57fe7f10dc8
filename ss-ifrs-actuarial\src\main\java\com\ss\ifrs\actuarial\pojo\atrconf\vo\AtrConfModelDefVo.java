/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2022-02-24 15:32:59
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2022-02-24 15:32:59<br/>
 * Description: 模型定义<br/>
 * Table Name: atr_conf_model_def<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "模型定义")
public class AtrConfModelDefVo implements Serializable {
    /**
     * Database column: atr_conf_model_def.model_def_id
     * Database remarks: 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    private Long modelDefId;

    /**
     * Database column: atr_conf_model_def.center_id
     * Database remarks: 业务单位
     */
    @ApiModelProperty(value = "业务单位", required = false)
    @NotNull(message = "The Center Id can't be null|业务单位不能为空|業務單位不能為空")
    //@DecimalMax(value = "2048", message = "Center Id must be less than 2048|业务单位必须小于2048|業務單位必須小於2048")
    private Long entityId;

    /**
     * Database column: atr_conf_model_def.model_code
     * Database remarks: 模型编码
     */
    @ApiModelProperty(value = "模型编码", required = true)
    @NotBlank(message = "The modelCode can't be null|模型编码不能为空|模型編碼不能為空")
    @Size(max = 32, message = "The modelCode's length is too long|模型编码过长|模型編碼過長")
    private String modelCode;

    /**
     * Database column: atr_conf_model_def.model_c_name
     * Database remarks: 模型中文名称
     */
    @ApiModelProperty(value = "模型中文名称", required = true)
    @NotBlank(message = "The Chinese Name can't be null|中文名称不能为空|中文名稱不能為空")
    @Size(max = 200, message = "The Chinese Name's length is too long|中文名称过长|中文名稱過長")
    private String modelCName;

    /**
     * Database column: atr_conf_model_def.model_t_name
     * Database remarks: 模型繁体名称
     */
    @ApiModelProperty(value = "模型繁体名称", required = true)
    @NotBlank(message = "The Traditional Chinese Name can't be null|繁体名称不能为空|繁體名稱不能為空")
    @Size(max = 200, message = "The Traditional Chinese Name's length is too long|繁体名称过长|繁體名稱過長")
    private String modelLName;

    /**
     * Database column: atr_conf_model_def.model_e_name
     * Database remarks: 模型英文名称
     */
    @ApiModelProperty(value = "模型英文名称", required = true)
    @NotBlank(message = "The English Name can't be null|英文名称不能为空|英文名稱不能為空")
    @Size(max = 200, message = "The English Name's length is too long|英文名称过长|英文名稱過長")
    private String modelEName;

    /**
     * Database column: atr_conf_model_def.system_code
     * Database remarks: 适用平台
     */
    @ApiModelProperty(value = "适用平台", required = true)
    private String systemCode;

    /**
     * Database column: atr_conf_model_def.model_dimension
     * Database remarks: 计量维度
     */
    @ApiModelProperty(value = "计量维度", required = true)
    private String modelDimension;

    /**
     * Database column: atr_conf_model_def.cfSource
     * Database remarks: 计量现金流来源(码值CaseFlowSource)
     */
    @ApiModelProperty(value = "计量现金流来源", required = true)
    private String cfSource;

    /**
     * Database column: atr_conf_model_def.serial_no
     * Database remarks: serial_no|版本号
     */
    @ApiModelProperty(value = "serial_no|版本号", required = false)
    private Integer serialNo;

    /**
     * Database column: atr_conf_model_def.audit_state
     * Database remarks: Audit_State|审核状态
     */
    @ApiModelProperty(value = "Audit_State|审核状态", required = false)
    private String auditState;

    /**
     * Database column: atr_conf_model_def.checked_id
     * Database remarks: Checked_ID|审核人
     */
    @ApiModelProperty(value = "Checked_ID|审核人", required = false)
    private Long checkedId;

    /**
     * Database column: atr_conf_model_def.checked_time
     * Database remarks: Checked_Time|审核时间
     */
    @ApiModelProperty(value = "Checked_Time|审核时间", required = false)
    private Date checkedTime;

    /**
     * Database column: atr_conf_model_def.checked_msg
     * Database remarks: Checked_Msg|审核意见
     */
    @ApiModelProperty(value = "Checked_Msg|审核意见", required = false)
    private String checkedMsg;

    /**
     * Database column: atr_conf_model_def.valid_is
     * Database remarks: Valid_Is|是否有效
     */
    @ApiModelProperty(value = "Valid_Is|是否有效", required = false)
    private String validIs;

    /**
     * Database column: atr_conf_model_def.remark
     * Database remarks: REMARK|备注
     */
    @ApiModelProperty(value = "REMARK|备注", required = false)
    private String REMARK;

    /**
     * Database column: atr_conf_model_def.creator_id
     * Database remarks: Creator_Id|创建人
     */
    @ApiModelProperty(value = "Creator_Id|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: atr_conf_model_def.create_time
     * Database remarks: Create_Time|创建时间
     */
    @ApiModelProperty(value = "Create_Time|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: atr_conf_model_def.updator_id
     * Database remarks: Updator_Id|最后修改人
     */
    @ApiModelProperty(value = "Updator_Id|最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: atr_conf_model_def.update_time
     * Database remarks: Update_Time|最后修改时间
     */
    @ApiModelProperty(value = "Update_Time|最后修改时间", required = false)
    private Date updateTime;

    /**
     * Database column: atr_conf_model_def.business_model
     * Database remarks: Business_Model|业务模型：DD-直保 TD-合约 FI-分入 FO-分出
     */
    @ApiModelProperty(value = "Business_Model|业务模型：DD-直保 TD-合约 FI-分入 FO-分出", required = false)
    private String businessModel;

    /**
     * Database column: atr_conf_model_def.business_direction
     * Database remarks: Business_Direction|业务方向：D-不区分 I-分入 O-分出
     */
    @ApiModelProperty(value = "Business_Direction|业务方向：D-不区分 I-分入 O-分出", required = false)
    private String businessDirection;

    /**
     * Database column: atr_conf_model_def.evaluateApproach
     * Database remarks: evaluate_approach|评估方法
     */
    @ApiModelProperty(value = "evaluate_approach|评估方法", required = false)
    private String evaluateApproach;

    private String modelName;

    private String entityCode;
    private String entityEName;
    private String entityCName;
    private String entityLName;

    private static final long serialVersionUID = 1L;

    public String getEvaluateApproach() {
        return evaluateApproach;
    }

    public void setEvaluateApproach(String evaluateApproach) {
        this.evaluateApproach = evaluateApproach;
    }

    public Long getModelDefId() {
        return modelDefId;
    }

    public void setModelDefId(Long modelDefId) {
        this.modelDefId = modelDefId;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public String getModelCName() {
        return modelCName;
    }

    public void setModelCName(String modelCName) {
        this.modelCName = modelCName;
    }

    public String getModelLName() {
        return modelLName;
    }

    public void setModelLName(String modelLName) {
        this.modelLName = modelLName;
    }

    public String getModelEName() {
        return modelEName;
    }

    public void setModelEName(String modelEName) {
        this.modelEName = modelEName;
    }

    public String getSystemCode() {
        return systemCode;
    }

    public void setSystemCode(String systemCode) {
        this.systemCode = systemCode;
    }

    public String getModelDimension() {
        return modelDimension;
    }

    public void setModelDimension(String modelDimension) {
        this.modelDimension = modelDimension;
    }

    public String getCfSource() {
        return cfSource;
    }

    public void setCfSource(String cfSource) {
        this.cfSource = cfSource;
    }

    public Integer getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Integer serialNo) {
        this.serialNo = serialNo;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    public Long getCheckedId() {
        return checkedId;
    }

    public void setCheckedId(Long checkedId) {
        this.checkedId = checkedId;
    }

    public Date getCheckedTime() {
        return checkedTime;
    }

    public void setCheckedTime(Date checkedTime) {
        this.checkedTime = checkedTime;
    }

    public String getCheckedMsg() {
        return checkedMsg;
    }

    public void setCheckedMsg(String checkedMsg) {
        this.checkedMsg = checkedMsg;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }

    public String getREMARK() {
        return REMARK;
    }

    public void setREMARK(String REMARK) {
        this.REMARK = REMARK;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getEntityCode() {
        return entityCode;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public String getEntityEName() {
        return entityEName;
    }

    public void setEntityEName(String entityEName) {
        this.entityEName = entityEName;
    }

    public String getEntityCName() {
        return entityCName;
    }

    public void setEntityCName(String entityCName) {
        this.entityCName = entityCName;
    }

    public String getEntityLName() {
        return entityLName;
    }

    public void setEntityLName(String entityLName) {
        this.entityLName = entityLName;
    }

    public String getBusinessModel() {
        return businessModel;
    }

    public void setBusinessModel(String businessModel) {
        this.businessModel = businessModel;
    }

    public String getBusinessDirection() {
        return businessDirection;
    }

    public void setBusinessDirection(String businessDirection) {
        this.businessDirection = businessDirection;
    }
}