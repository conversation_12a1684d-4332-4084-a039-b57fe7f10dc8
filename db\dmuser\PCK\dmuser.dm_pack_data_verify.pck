CREATE OR REPLACE PACKAGE dm_pack_data_verify IS

  FUNCTION func_get_current_year_month(p_entity_id  NUMBER,
                                       p_year_month VARCHAR2,
                                       p_biz_code   VARCHAR2) RETURN VARCHAR2;

  PROCEDURE proc_data_verify(p_entity_id   NUMBER,
                             p_task_code   VARCHAR2,
                             p_biz_type_id NUMBER,
                             p_user_id     NUMBER,
                             p_draw_type   VARCHAR2);

  PROCEDURE proc_data_verify_prepare(p_entity_id   NUMBER,
                                     p_task_code   VARCHAR2,
                                     p_year_month  VARCHAR2,
                                     p_biz_type_id NUMBER,
                                     p_user_id     NUMBER,
                                     p_draw_type   VARCHAR2);

  PROCEDURE proc_data_verify_deal(p_entity_id   NUMBER,
                                  p_task_code   VARCHAR2,
                                  p_year_month  VARCHAR2,
                                  p_biz_type_id NUMBER,
                                  p_user_id     NUMBER,
                                  p_draw_type   VARCHAR2);

  PROCEDURE proc_data_verify_rule_polling(p_entity_id   NUMBER,
                                     p_task_code   VARCHAR2,
                                     p_year_month  VARCHAR2,
                                     p_biz_type_id NUMBER,
                                     p_user_id     NUMBER,
                                     p_draw_type   VARCHAR2);

  PROCEDURE proc_data_verify_testing(p_entity_id   NUMBER,
                                     p_task_code   VARCHAR2,
                                     p_year_month  VARCHAR2,
                                     p_biz_type_id NUMBER,
                                     p_user_id     NUMBER,
                                     p_draw_type   VARCHAR2,
                                     p_rule_type varchar2);

  PROCEDURE proc_data_verify_result(p_entity_id   NUMBER,
                                    p_task_code   VARCHAR2,
                                    p_year_month  VARCHAR2,
                                    p_biz_type_id NUMBER,
                                    p_user_id     NUMBER);

  PROCEDURE proc_data_verify_to_target(p_entity_id   NUMBER,
                                       p_task_code   VARCHAR2,
                                       p_year_month  VARCHAR2,
                                       p_biz_type_id NUMBER,
                                       p_user_id     NUMBER,
                                       p_draw_type   VARCHAR2);
  --清理数据
  PROCEDURE proc_data_verify_clean(p_entity_id  NUMBER,
                                   p_task_code  VARCHAR2,
                                   p_push_model VARCHAR2,
                                   p_year_month VARCHAR2,
                                   p_user_id    NUMBER,
                                   p_draw_type  VARCHAR2);

  PROCEDURE proc_data_verify_log(p_entity_id    NUMBER,
                                 p_year_month   VARCHAR2,
                                 p_task_code    VARCHAR2,
                                 p_user_id      NUMBER,
                                 p_biz_type_id  NUMBER,
                                 p_trace_no     VARCHAR2,
                                 p_trace_code   VARCHAR2,
                                 p_trace_status VARCHAR2,
                                 p_trace_msg    VARCHAR2);

  function func_check_entity(p_entity_id varchar2, p_entity_code varchar2)
    return number;

  function func_check_department(p_entity_id   varchar2,
                                 p_entity_code varchar2) return number;

  function func_check_account(p_book_code    varchar2,
                              p_account_id   varchar2,
                              p_account_code varchar2) return number;

  FUNCTION func_check_endorse_type(p_endorse_type_str VARCHAR2) RETURN NUMBER;

  ------------------------------------------------------------
  -- 检查是否存在 tgt_acc_article_balance 表中， 是返回 1， 否返回 0
  ------------------------------------------------------------
  function func_check_exits_acc_article_balance(p_entity_code       varchar2,
                                                p_base_account_code varchar2,
                                                p_account_code      varchar2,
                                                p_book_code         varchar2,
                                                p_year_month        varchar2,
                                                p_article           varchar2,
                                                p_currency          varchar2)
    return number;

  ------------------------------------------------------------
  -- 检查是否存在 DM_TGT_ACC_VOUCHERDETAIL 表中， 是返回 1， 否返回 0
  ------------------------------------------------------------
  function func_check_exists_acc_voucherdetail(p_entity_code    varchar2,
                                               p_VOUCHER_NO     varchar2,
                                               p_VOUCHER_SEQ_NO number)
    return number;

  ------------------------------------------------------------
  -- 检查是否存在 other ods_fin_article_balance ， 是返回 1， 否返回 0
  ------------------------------------------------------------
  function func_check_exists_ods_fin_article_balance(p_id                number,
                                                     p_task_code         varchar2,
                                                     p_entity_code       varchar2,
                                                     p_book_code         varchar2,
                                                     p_year_month        varchar2,
                                                     p_root_account_code varchar2,
                                                     p_account_code      varchar2,
                                                     p_article           varchar2,
                                                     p_currency_code     varchar2)
    return number;

END dm_pack_data_verify;
/
CREATE OR REPLACE PACKAGE BODY dm_pack_data_verify IS
  p_trace_no VARCHAR2(50);

  type type_int_map is table of number(1) index by string(400);

  type type_array is table of varchar2(4000) index by binary_integer;

  -- 会话级别缓存所有有效的批改类型
  v_endorse_type_cache type_int_map;

  -- 会话级别缓存所有的 entity
  v_entity_cache type_int_map;

  -- 会话级别缓存所有的 department
  v_department_cache type_int_map;

  -- 会话级别缓存所有的 account
  v_account_cache type_int_map;

  FUNCTION func_get_current_year_month(p_entity_id  NUMBER,
                                       p_year_month VARCHAR2,
                                       p_biz_code   VARCHAR2) RETURN VARCHAR2 IS
  BEGIN
    RETURN dm_pack_buss_period.func_get_current_year_month(p_entity_id,
                                                           p_year_month,
                                                           p_biz_code);

  END func_get_current_year_month;

  FUNCTION func_check_valid_year_month(p_entity_id  NUMBER,
                                       p_year_month VARCHAR2,
                                       p_biz_code   VARCHAR2) RETURN boolean IS
    v_valid_year_month varchar2(100);
    v_count            number(5);
  BEGIN
    if p_year_month like '%JS' then
      -- 年结业务只有当当年的12月已完成才可执行
      SELECT count(*)
        into v_count
        FROM dm_conf_bussperiod t
       where t.entity_id = p_entity_id
         and t.year_month = substr(p_year_month, 1, 4) || '12'
         and t.period_state = '3'
         and t.valid_is = '1';
      return v_count > 0;
    else
      v_valid_year_month := dm_pack_buss_period.func_get_valid_year_month(p_entity_id,
                                                                          null,
                                                                          p_biz_code);
      return v_valid_year_month is null or v_valid_year_month <= p_year_month;
    end if;
  END func_check_valid_year_month;

  PROCEDURE proc_data_verify(p_entity_id   NUMBER,
                             p_task_code   VARCHAR2,
                             p_biz_type_id NUMBER,
                             p_user_id     NUMBER,
                             p_draw_type   VARCHAR2) IS
    v_biz_code   VARCHAR(1000);
    v_year_month VARCHAR(10);
    v_count      NUMBER;
    v_type_group VARCHAR(10);
    v_errm       VARCHAR2(4000);
    v_error_msg  VARCHAR2(2000);
  BEGIN

    --init trace_no
    p_trace_no := dm_seq_log_data_verify.nextval;

    --业务期间
    v_year_month := substr(p_task_code, 8, 6);
    -- 年月不满足不处理
    if not regexp_like(v_year_month, '\d{4}(0[1-9]|1[0-2]|JS)') then

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][数据校验]proc_data_verify-无效月份：' ||
                     v_year_month;
      raise_application_error(-20002, v_error_msg);

    end if;

    -- routine body goes here...
    IF p_biz_type_id IS NULL THEN

      --先检验公共数据
      --业务数据
      FOR rec_biz_type IN (SELECT type_code,
                                  biz_type_id,
                                  biz_code,
                                  type_group,
                                  display_no
                             FROM (SELECT 'ODS_' || biz_code AS type_code,
                                          biz_type_id,
                                          biz_code,
                                          (CASE
                                            WHEN type_group = '7' THEN
                                             '0'
                                            ELSE
                                             type_group
                                          END) AS type_group,
                                          display_no
                                     FROM dm_conf_table t
                                    WHERE t.valid_is = '1') t
                            ORDER BY type_group, display_no) LOOP

        --校验数据
        proc_data_verify_deal(p_entity_id,
                              p_task_code,
                              v_year_month,
                              rec_biz_type.biz_type_id,
                              p_user_id,
                              p_draw_type);

      END LOOP;
    ELSE

      --biz_code，push_model查找
      SELECT COUNT(1)
        INTO v_count
        FROM dm_conf_table
       WHERE biz_type_id = p_biz_type_id;

      --Check if not existing src table then return
      IF v_count = 0 THEN

        proc_data_verify_log(p_entity_id,
                             v_year_month,
                             p_task_code,
                             p_user_id,
                             p_biz_type_id,
                             p_trace_no,
                             'proc_data_verify',
                             '2',
                             'not such model');
        --抛出已自定义异常信息【会中断事务】
        v_error_msg := '[EXCEPTION][数据校验]proc_data_verify-无效模型：not such model';
        raise_application_error(-20002, v_error_msg);

      END IF;

      -- remore check history
      --DELETE FROM dm_log_data_verify WHERE entity_id = p_entity_id AND year_month = v_year_month AND biz_type_id = p_biz_type_id AND task_code = p_task_code;

      SELECT biz_code, type_group
        INTO v_biz_code, v_type_group
        FROM dm_conf_table
       WHERE biz_type_id = p_biz_type_id;

      --校验数据
      proc_data_verify_deal(p_entity_id,
                            p_task_code,
                            v_year_month,
                            p_biz_type_id,
                            p_user_id,
                            p_draw_type);

    END IF;

    proc_data_verify_log(p_entity_id,
                         v_year_month,
                         p_task_code,
                         p_user_id,
                         p_biz_type_id,
                         p_trace_no,
                         'proc_data_verify',
                         '1',
                         NULL);

  EXCEPTION

    --意外处理
    WHEN OTHERS THEN
      --ROLLBACK;
      v_errm := SQLERRM;
      --dbms_output.put_line('执行异常');
      proc_data_verify_log(p_entity_id,
                           v_year_month,
                           p_task_code,
                           p_user_id,
                           p_biz_type_id,
                           p_trace_no,
                           'proc_data_verify',
                           '2',
                           v_errm);

      --意外处理
      v_error_msg := '[EXCEPTION][数据校验]proc_data_verify:' || SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004, v_error_msg);

  END proc_data_verify;

  PROCEDURE proc_data_verify_prepare(p_entity_id   NUMBER,
                                     p_task_code   VARCHAR2,
                                     p_year_month  VARCHAR2,
                                     p_biz_type_id NUMBER,
                                     p_user_id     NUMBER,
                                     p_draw_type   VARCHAR2) IS

    v_errm       VARCHAR2(4000);
    v_biz_code   VARCHAR(100);
    v_type_group VARCHAR(100);
    v_sql        VARCHAR2(1000);
    v_count      NUMBER;
    v_error_msg  VARCHAR2(2000);
  BEGIN

    IF p_trace_no IS NULL THEN
      p_trace_no := dm_seq_log_data_verify.nextval;
    END IF;

    SELECT biz_code, type_group
      INTO v_biz_code, v_type_group
      FROM dm_conf_table
     WHERE biz_type_id = p_biz_type_id;

    /*
    --if existing the buss period, re-correcting the claim_status_code
    --清除业务期间详情的状态，
    update dm_conf_bussperiod_detail
        set EXEC_RESULT = null,READY_STATE = '0'
    where BUSS_PERIOD_ID = (select BUSS_PERIOD_ID from dm_conf_bussperiod where year_month = p_year_month and entity_id = p_entity_id )
          and BIZ_TYPE_ID = p_biz_type_id;

    --更新业务期间主表的状态
    UPDATE dm_conf_bussperiod
        SET period_state = '0', updator_id = p_user_id, update_time = sysdate
      WHERE entity_id = p_entity_id
        AND year_month = p_year_month;
    COMMIT;
    */
    --文件交换、etl重新校验逻辑
    if p_draw_type in ('1','2') then
      --清理计量单元数据
      IF upper(v_biz_code) = upper('reins_treaty') THEN
        --再保合约

        DELETE FROM dm_buss_cmunit_treaty t
         WHERE t.entity_id = p_entity_id
           AND t.buss_year_month = p_year_month
           AND t.draw_type = p_draw_type
           AND t.fac_no IS NULL
           AND EXISTS (SELECT 1
                  FROM dm_reins_treaty rt
                 WHERE rt.entity_id = t.entity_id
                   AND rt.treaty_no = rt.treaty_no
                   AND rt.task_code = p_task_code
                   AND rt.draw_type = p_draw_type);
        COMMIT;
      ELSIF upper(v_biz_code) = upper('policy_main') OR
            upper(v_biz_code) = upper('policy_premium') THEN
        --直保
        --计量单元
        IF upper(v_biz_code) = upper('policy_main') THEN
          DELETE FROM dm_buss_cmunit_direct t
           WHERE t.entity_id = p_entity_id
             AND t.buss_year_month = p_year_month
             AND t.draw_type = p_draw_type
             AND EXISTS (SELECT 1
                    FROM dm_policy_main pm
                   WHERE pm.entity_id = t.entity_id
                     AND pm.policy_no = t.policy_no
                     AND pm.task_code = p_task_code
                     AND pm.draw_type = p_draw_type
                     AND pm.endorse_seq_no = '000');
          COMMIT;
        ELSIF upper(v_biz_code) = upper('policy_premium') THEN
          DELETE FROM dm_buss_cmunit_direct t
           WHERE t.entity_id = p_entity_id
             AND t.buss_year_month = p_year_month
             AND t.draw_type = p_draw_type
             AND EXISTS (SELECT 1
                    FROM dm_policy_premium pp
                   WHERE pp.entity_id = t.entity_id
                     AND pp.policy_no = t.policy_no
                     AND pp.task_code = p_task_code
                     AND pp.draw_type = p_draw_type
                     AND pp.endorse_seq_no = '000');
          COMMIT;
        END IF;

        UPDATE dm_policy_main
           SET task_status = '4'
         WHERE task_code = p_task_code
           AND draw_type = p_draw_type;
        COMMIT;

        UPDATE dm_policy_premium
           SET task_status = '4'
         WHERE task_code = p_task_code
           AND draw_type = p_draw_type;
        COMMIT;

      ELSIF upper(v_biz_code) = upper('reins_outward_detail') THEN
        --分出
        DELETE FROM dm_buss_cmunit_fac_outwards t
         WHERE t.entity_id = p_entity_id
           AND t.buss_year_month = p_year_month
           AND t.draw_type = p_draw_type
           AND EXISTS (SELECT 1
                  FROM dm_reins_outward_detail rfs
                 WHERE rfs.entity_id = t.entity_id
                   AND t.fac_no = rfs.ri_policy_no
                   AND rfs.task_code = p_task_code
                   AND rfs.draw_type = p_draw_type);
        COMMIT;

        DELETE FROM dm_buss_cmunit_treaty t
         WHERE t.entity_id = p_entity_id
           AND t.buss_year_month = p_year_month
           AND t.draw_type = p_draw_type
           AND t.fac_no IS NOT NULL
           AND t.ri_direction_code = 'O'
           AND EXISTS (SELECT 1
                  FROM dm_reins_outward rfs
                 WHERE rfs.entity_id = t.entity_id
                   AND t.fac_no = rfs.ri_policy_no
                   AND rfs.task_code = p_task_code
                   AND rfs.draw_type = p_draw_type);
        COMMIT;

      END IF;
    end if;
    SELECT COUNT(1)
      INTO v_count
      FROM user_tab_cols t
     WHERE t.table_name = upper('DM_' || v_biz_code)
       AND t.column_name = upper('entity_code');

   proc_data_verify_log(p_entity_id,
                         p_year_month,
                         p_task_code,
                         p_user_id,
                         p_biz_type_id,
                         p_trace_no,
                         'proc_data_verify_prepare:update-prepare-status-start',
                         '1',
                         NULL);
    IF v_count > 0 and v_biz_code <> upper('base_entity') THEN

    --文件交换、etl重新校验逻辑
    if p_draw_type in ('1','2') then
        v_sql := ' delete from DM_' || v_biz_code ||
                 ' t where t.entity_id = ' || p_entity_id ||
                 '  and task_code = ''' || p_task_code ||
                 ''' and draw_type =''' || p_draw_type || '''';
        EXECUTE IMMEDIATE (v_sql);
        commit;
      end if;

      v_sql := 'merge into ODS_' || v_biz_code ||' a
                using (select t.id,t.task_code from ODS_' || v_biz_code ||' t
                where t.entity_code = (SELECT c.company_code FROM bpluser.bpl_company c WHERE c.company_id = ' ||
               p_entity_id || ' )
               and task_code = ''' || p_task_code ||'''
               and draw_type =''' || p_draw_type || ''') b
                on (a.id = b.id and a.task_code = b.task_code)
                when matched then
                  update
                  set task_status = ''1'', CHECK_MSG = null';

      /*v_sql := ' update ODS_' || v_biz_code ||
               ' t set task_status = ''1'', CHECK_MSG = null where t.entity_code = (SELECT c.company_code FROM bpluser.bpl_company c WHERE c.company_id = ' ||
               p_entity_id || ' )  and task_code = ''' || p_task_code ||
               ''' and draw_type =''' || p_draw_type || '''';*/

      EXECUTE IMMEDIATE (v_sql);
      commit;
    ELSE

      --文件交换、etl重新校验逻辑
      if p_draw_type in ('1','2') then

        v_sql := ' delete from DM_' || v_biz_code ||
                 ' t where task_code = ''' || p_task_code ||
                 ''' and draw_type =''' || p_draw_type || '''';

        EXECUTE IMMEDIATE (v_sql);
        commit;
       end if;

       v_sql := 'merge into ODS_' || v_biz_code ||' a
          using (select t.id,t.task_code from ODS_' || v_biz_code ||' t
          where t.task_code = ''' || p_task_code ||'''
         and draw_type =''' || p_draw_type || ''') b
          on (a.id = b.id and a.task_code = b.task_code)
          when matched then
            update
            set task_status = ''1'', CHECK_MSG = null';
      /*v_sql := ' update ODS_' || v_biz_code ||
               ' t set task_status = ''1'', CHECK_MSG = null where task_code = ''' ||
               p_task_code || ''' and draw_type =''' || p_draw_type || '''';*/

      EXECUTE IMMEDIATE (v_sql);
      commit;
    END IF;
   proc_data_verify_log(p_entity_id,
                         p_year_month,
                         p_task_code,
                         p_user_id,
                         p_biz_type_id,
                         p_trace_no,
                         'proc_data_verify_prepare:update-prepare-status-end',
                         '1',
                         NULL);

    --clean check result
    delete from dm_log_check_rule t
     where t.entity_id = p_entity_id
       and t.biz_type_id = p_biz_type_id
       and t.task_code = p_task_code;
    COMMIT;

    SELECT COUNT(1)
      INTO v_count
      FROM ods_data_push_signal a
     WHERE a.push_model = v_biz_code
       AND a.task_code = p_task_code;

    IF v_count > 0 THEN
      --更新数据为0的为已完成
      SELECT ROW_COUNT
        INTO v_count
        from ods_data_push_signal a
       where a.push_model = v_biz_code
         and a.task_code = p_task_code;

      IF v_count > 0 THEN
        update ods_data_push_signal a
           set task_status     = '1',
               deal_msg        = null,
               start_deal_time = SYSDATE
         where a.push_model = v_biz_code
           and a.task_code = p_task_code;
      ELSE
        --dbms_output.put_line('更新数据为0的为已完成:'||v_count);
        update ods_data_push_signal a
           set task_status     = '3',
               deal_msg        = null,
               start_deal_time = SYSDATE,
               end_deal_time   = SYSDATE
         where a.push_model = v_biz_code
           and a.task_code = p_task_code;
      END IF;
      COMMIT;
    END IF;

    --log trace msg
    proc_data_verify_log(p_entity_id,
                         p_year_month,
                         p_task_code,
                         p_user_id,
                         p_biz_type_id,
                         p_trace_no,
                         'proc_data_verify_prepare',
                         '1',
                         NULL);

  EXCEPTION
    --意外处理
    WHEN OTHERS THEN
      --ROLLBACK;
      v_errm := SQLERRM;
      proc_data_verify_log(p_entity_id,
                           p_year_month,
                           p_task_code,
                           p_user_id,
                           p_biz_type_id,
                           p_trace_no,
                           'proc_data_verify_prepare',
                           '2',
                           v_errm);

      --意外处理
      v_error_msg := '[EXCEPTION][数据校验]proc_data_verify_prepare:' ||
                     SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004, v_error_msg);

  END proc_data_verify_prepare;

  PROCEDURE proc_data_verify_deal(p_entity_id   NUMBER,
                                  p_task_code   VARCHAR2,
                                  p_year_month  VARCHAR2,
                                  p_biz_type_id NUMBER,
                                  p_user_id     NUMBER,
                                  p_draw_type   VARCHAR2) IS
    v_biz_code   varchar2(100);
    v_type_group varchar2(10);
    v_error_msg  VARCHAR2(2000);
  BEGIN

    select t.biz_code, t.type_group
      into v_biz_code, v_type_group
      from dm_conf_table t
     where t.biz_type_id = p_biz_type_id;

    if not
        func_check_valid_year_month(p_entity_id, p_year_month, v_biz_code) then
      proc_data_verify_log(p_entity_id,
                           p_year_month,
                           p_task_code,
                           p_user_id,
                           p_biz_type_id,
                           p_trace_no,
                           'proc_data_verify',
                           '2',
                           'yearmonth is invalid');
      --意外处理
      v_error_msg := '[EXCEPTION][数据校验]proc_data_verify_deal-无效月份:' ||
                     p_year_month;
      --捕获异常，往外抛
      raise_application_error(-20004, v_error_msg);

    end if;

    IF p_trace_no IS NULL THEN
      p_trace_no := dm_seq_log_data_verify.nextval;
    END IF;

    --校验前处理
    proc_data_verify_prepare(p_entity_id,
                               p_task_code,
                               p_year_month,
                               p_biz_type_id,
                               p_user_id,
                               p_draw_type);

   proc_data_verify_rule_polling(p_entity_id,
                             p_task_code,
                             p_year_month,
                             p_biz_type_id,
                             p_user_id,
                             p_draw_type);

    --校验数据
   /*proc_data_verify_testing(p_entity_id,
                             p_task_code,
                             p_year_month,
                             p_biz_type_id,
                             p_user_id,
                             p_draw_type);*/

    --处理校验结果（更新源表数据结果与状态）
    proc_data_verify_result(p_entity_id,
                            p_task_code,
                            p_year_month,
                            p_biz_type_id,
                            p_user_id);


    --插入TGT
    proc_data_verify_to_target(p_entity_id,
                               p_task_code,
                               p_year_month,
                               p_biz_type_id,
                               p_user_id,
                               p_draw_type);

    --对校验已完成的数据进行统计
    dm_pack_duct_stat.proc_paring_stat(p_entity_id,
                                       p_task_code,
                                       p_biz_type_id);

    --Upload/API
    IF p_draw_type IN ('1', '3') AND
       func_get_current_year_month(p_entity_id, p_year_month, v_biz_code) IS NOT NULL THEN
      --生成计量单元
      IF upper(v_biz_code) = upper('reins_treaty') THEN
        --再保合约
        dm_pack_cmunit_treaty_inward.proc_cmunit_identify_all(p_entity_id,
                                                              p_year_month);
        dm_pack_cmunit_treaty_outward.proc_cmunit_identify_all(p_entity_id,
                                                               p_year_month);

      ELSIF upper(v_biz_code) = upper('policy_main') OR
            upper(v_biz_code) = upper('policy_premium') THEN
        --直保
        --生成计量单元
        dm_pack_cmunit_direct.proc_cmunit_identify_all(p_entity_id,
                                                       p_year_month);

      ELSIF upper(v_biz_code) = upper('reins_outward_detail') THEN
        --分出
        dm_pack_cmunit_fac_outward.proc_cmunit_identify_all(p_entity_id,
                                                            p_year_month);
        --dm_pack_cmunit_treaty_outward.proc_cmunit_identify_all(p_entity_id, p_year_month);

      END IF;
    END IF;

    --校验完数据后按类型排序分场景进行数据同步
    IF v_type_group = '7' THEN
      bpluser.bpl_pack_data_sync.add_public_data(v_biz_code,
                                                 p_task_code,
                                                 p_user_id);
    END IF;

    --log trace msg
    proc_data_verify_log(p_entity_id,
                         p_year_month,
                         p_task_code,
                         p_user_id,
                         p_biz_type_id,
                         p_trace_no,
                         'proc_data_verify_deal',
                         '1',
                         NULL);

  END proc_data_verify_deal;

 PROCEDURE proc_data_verify_rule_polling(p_entity_id   NUMBER,
                                     p_task_code   VARCHAR2,
                                     p_year_month  VARCHAR2,
                                     p_biz_type_id NUMBER,
                                     p_user_id     NUMBER,
                                     p_draw_type   VARCHAR2) IS
    v_errm        VARCHAR2(1000);
  BEGIN

    IF p_trace_no IS NULL THEN
      p_trace_no := dm_seq_log_data_verify.nextval;
    END IF;

    for cur in (select distinct rule_type from dm_conf_checkrule where biz_type_id = p_biz_type_id order by rule_type) loop
       proc_data_verify_log(p_entity_id,
                             p_year_month,
                             p_task_code,
                             p_user_id,
                             p_biz_type_id,
                             p_trace_no,
                             'proc_data_verify_rule_polling：start',
                             '1',
                             'rule_type：'||cur.rule_type);
        proc_data_verify_testing(p_entity_id,
                                 p_task_code,
                                 p_year_month,
                                 p_biz_type_id,
                                 p_user_id,
                                 p_draw_type,
                                 cur.rule_type);
        proc_data_verify_log(p_entity_id,
                             p_year_month,
                             p_task_code,
                             p_user_id,
                             p_biz_type_id,
                             p_trace_no,
                             'proc_data_verify_rule_polling：end',
                             '1',
                             'rule_type：'||cur.rule_type);
    end loop;



  EXCEPTION
    --意外处理
    WHEN OTHERS THEN
      --ROLLBACK;
      v_errm := SQLERRM;
      proc_data_verify_log(p_entity_id,
                           p_year_month,
                           p_task_code,
                           p_user_id,
                           p_biz_type_id,
                           p_trace_no,
                           'proc_data_verify_rule_polling',
                           '2',
                           v_errm);

      --意外处理
      v_errm := '[EXCEPTION][数据校验]proc_data_verify_rule_polling:' || SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004, v_errm);

  END proc_data_verify_rule_polling;


  /**
  **  业务数据校验
  **
  **/
  PROCEDURE proc_data_verify_testing(p_entity_id   NUMBER,
                                     p_task_code   VARCHAR2,
                                     p_year_month  VARCHAR2,
                                     p_biz_type_id NUMBER,
                                     p_user_id     NUMBER,
                                     p_draw_type   VARCHAR2,
                                     p_rule_type varchar2) IS
    v_entity_code VARCHAR2(20);
    v_bussness_no VARCHAR2(2000);
    V_SQL         VARCHAR2(4000);
    v_biz_code    VARCHAR2(100);
    v_type_group  VARCHAR(100);
    v_count       NUMBER;
    v_errm        VARCHAR2(1000);
  BEGIN

    IF p_trace_no IS NULL THEN
      p_trace_no := dm_seq_log_data_verify.nextval;
    END IF;

    SELECT c.company_code
      INTO v_entity_code
      FROM bpl_company c
     WHERE c.company_id = p_entity_id;

    SELECT biz_code, type_group
      INTO v_biz_code, v_type_group
      FROM dm_conf_table
     WHERE biz_type_id = p_biz_type_id;

    SELECT COUNT(1)
      INTO v_count
      FROM user_tab_cols t
     WHERE t.table_name = upper('DM_' || v_biz_code)
       AND t.column_name = upper('entity_code');

    --Check the table if existing entity_code
    IF v_count > 0  and v_biz_code <> upper('base_entity') THEN
      EXECUTE IMMEDIATE 'select count(1) from ODS_' || v_biz_code ||
                        ' where entity_code = ''' || v_entity_code ||
                        ''' and task_code = ''' || p_task_code ||
                        ''' and draw_type = ''' || p_draw_type || ''''
        INTO v_count;
    ELSE
      EXECUTE IMMEDIATE 'select count(1) from ODS_' || v_biz_code ||
                        ' where draw_type = ''' || p_draw_type ||
                        ''' and task_code = ''' || p_task_code || ''''
        INTO v_count;
    END IF;

    --Check if not existing waiting verify data then return
    IF v_count < 1 THEN
      --日志处理
      proc_data_verify_log(p_entity_id,
                           p_year_month,
                           p_task_code,
                           p_user_id,
                           p_biz_type_id,
                           p_trace_no,
                           'proc_data_verify_testing',
                           '3',
                           'not data');

      return;
    END IF;

    SELECT listagg('t.' || b.col_code, '||'',''||') within GROUP(ORDER BY b.display_no)
      INTO v_bussness_no
      FROM dm_conf_table_column b
     WHERE b.constraint_type = '1'
       AND b.biz_type_id = p_biz_type_id;

    --对于没有业务主键的，使用id字段
    if v_bussness_no is null then
      v_bussness_no := 't.id';
    end if;

    FOR rec_config_rule IN (SELECT distinct t.config_rule_id,
                                            t.RULE_CODE,
                                            t.RULE_E_NAME,
                                            t.RULE_L_NAME,
                                            t.RULE_C_NAME,
                                            --t.RULE_CONFIG,
                                            t.rule_version,
                                            --t.col_id,
                                            t.biz_type_id,
                                            --t.OPT_TYPE,
                                            --t.MATCH_VALUE,
                                            --t.RULE_SQL,
                                            t.rule_direction,
                                            --t.rule_type_code,
                                            --A.COL_CODE,
                                            --A.COL_TYPE,
                                            --cc.code_c_name,
                                            b.biz_code,
                                            (case
                                              when t.rule_type_code = '0' then
                                               'SELECT id FROM ods_' ||
                                               b.biz_code || ' where ' ||
                                               A.COL_CODE || (CASE
                                                 WHEN TRIM(UPPER(t.MATCH_VALUE)) =
                                                      'NULL' AND
                                                      t.OPT_TYPE = '5' THEN
                                                  ' IS NULL '
                                                 WHEN TRIM(UPPER(t.MATCH_VALUE)) =
                                                      'NULL' AND
                                                      t.OPT_TYPE = '4' THEN
                                                  ' IS NOT NULL '
                                                 WHEN t.MATCH_VALUE IS NULL THEN
                                                  cc.code_c_name || ' ' ||
                                                  ''''''
                                                 WHEN t.MATCH_VALUE IS not NULL and
                                                      upper(a.col_type) =
                                                      upper('VARCHAR') THEN
                                                  cc.code_c_name || ' ''' ||
                                                  t.MATCH_VALUE || ''''
                                                 ELSE
                                                  cc.code_c_name || ' ' ||
                                                  t.MATCH_VALUE
                                               END)
                                              else
                                               REPLACE(t.rule_sql,
                                                       '#TASK_CODE#',
                                                       p_task_code)
                                            end) || ' AND TASK_CODE =''' ||
                                            p_task_code ||
                                            ''' and TASK_STATUS <> ''7''' as check_rule_sql,
                                            t.check_type,
                                            t.display_no,
                                            t.RULE_TYPE--规则类型
                              FROM dm_conf_checkrule T
                              LEFT JOIN dm_conf_table_column A
                                ON t.col_id = a.col_id
                              left join dm_conf_table b
                                on b.biz_type_id = t.biz_type_id
                              LEFT JOIN dm_conf_code cc
                                ON cc.code_code = t.opt_type
                              left JOIN dm_conf_code uc
                                ON uc.code_id = cc.upper_code_id
                               AND uc.code_code = 'OperationType'
                             WHERE T.VALID_IS = '1'
                               and t.RULE_TYPE = p_rule_type --规则类型
                               AND T.biz_type_id = p_biz_type_id
                             ORDER BY t.check_type, t.display_no) LOOP

      BEGIN

        V_SQL := 'INSERT /*+append */ INTO dm_log_check_rule(' || 'CHECK_ID,' ||
                 'DRAW_TYPE,' || 'entity_id,' || 'BIZ_TYPE_ID,' || 'TASK_CODE,' ||
                 'DATA_KEY,' || 'BUSINESS_NO,' || 'CHECK_STATE,' || 'RULE_ID,' ||
                 'RULE_VERSION,' || 'VALID_IS,' || 'CREATOR_ID,' ||
                 'CREATE_TIME,'||'rule_code,'||'RULE_TYPE)' || 'SELECT ' || 'DM_SEQ_DUCT_CHECK_LOG.NEXTVAL,' ||
                 't.DRAW_TYPE,' || p_entity_id || ',' ||
                 rec_config_rule.biz_type_id || ',' || '''' || p_task_code ||
                 ''',' || 't.ID,' || v_bussness_no || ',' || '''0'',' ||
                 rec_config_rule.config_rule_id || ',' ||
                 rec_config_rule.rule_version || ',' || '''1'',' || p_user_id || ',' ||
                 'localtimestamp' || ','''||rec_config_rule.rule_code||''' '||
                 ','''||rec_config_rule.RULE_TYPE||''' FROM ods_' || rec_config_rule.biz_code ||
                 ' t   WHERE t.id ' || (case
                   when rec_config_rule.rule_direction = '1' THEN
                   -- 正向规则8
                    ' NOT IN ' --反向结果为错误数据
                   ELSE
                   -- 反向规则
                    ' IN ' --反向结果为错误数据
                 END) || ' (' || rec_config_rule.check_rule_sql || ')' ||
                 'AND t.TASK_CODE =''' || p_task_code || '''' ||
                 'AND draw_type = ''' || p_draw_type || ''''
         ;
        --dbms_output.put_line('-- '||rec_config_rule.RULE_CODE||'-- '||rec_config_rule.RULE_C_NAME);
        --dbms_output.put_line(V_SQL);
         proc_data_verify_log(p_entity_id,
                           p_year_month,
                           p_task_code,
                           p_user_id,
                           p_biz_type_id,
                           p_trace_no,
                           rec_config_rule.rule_code,
                           '1',
                           'rule check start:'||V_SQL);
        EXECUTE IMMEDIATE (V_SQL);
        COMMIT;

        proc_data_verify_log(p_entity_id,
                       p_year_month,
                       p_task_code,
                       p_user_id,
                       p_biz_type_id,
                       p_trace_no,
                       rec_config_rule.rule_code,
                       '1',
                       'rule check end');

      EXCEPTION
        --意外处理
        WHEN OTHERS THEN
          --ROLLBACK;
          --V_ERRM := SQLCODE || '|' || SQLERRM;
          v_errm := SQLERRM;
          --日志处理
          INSERT INTO dm_log_data_verify_detail
            (entity_id,
             year_month,
             task_code,
             user_id,
             biz_type_id,
             business_key,
             business_no,
             trace_code,
             trace_status,
             trace_msg,
             create_time)
          VALUES
            (p_entity_id,
             p_year_month,
             p_task_code,
             p_user_id,
             rec_config_rule.biz_type_id,
             rec_config_rule.config_rule_id,
             v_bussness_no,
             'proc_data_verify_testing',
             '2',
             substrb('/*' || v_errm || '*/' || chr(10) || V_SQL, 1, 4000),
             SYSDATE);
          COMMIT;
      END;
    END loop;

    --规则化处理以下规则，无须另行处理方案
    /*
    --不满足接收规则的数据异常处理
    IF v_type_group != '7' THEN
      --非公共数据更新状态
      FOR control_cur IN (SELECT DISTINCT dps.year_month     AS year_month,
                                          tvc.control_column AS control_column,
                                          NULL AS config_rule_id, --ref to check rule
                                          NULL AS rule_version
                            FROM ods_data_push_signal dps,
                                 dm_conf_table_verify_control tvc
                           WHERE upper(dps.push_model) = upper(v_biz_code)
                             AND upper(tvc.biz_table_code) = upper(dps.push_model)
                             AND dps.year_month = p_year_month
                             --AND dps.task_status IN ('0', '1', '2', '3')
                           ORDER BY dps.year_month) LOOP

        BEGIN

          V_SQL := 'INSERT INTO dm_log_check_rule('
                  ||'CHECK_ID,'
                  ||'DRAW_TYPE,'
                  ||'entity_id,'
                  ||'BIZ_TYPE_ID,'
                  ||'TASK_CODE,'
                  ||'DATA_KEY,'
                  ||'BUSINESS_NO,'
                  ||'CHECK_STATE,'
                  ||'RULE_ID,'
                  ||'RULE_VERSION,'
                  ||'VALID_IS,'
                  ||'CREATOR_ID,'
                  ||'CREATE_TIME)'
                  ||'SELECT '
                  ||'DM_SEQ_DUCT_CHECK_LOG.NEXTVAL,'
                  ||'t.DRAW_TYPE,'
                  ||p_entity_id ||','
                  ||p_biz_type_id||','
                  ||''''|| p_task_code||''','
                  ||'t.ID,'
                  ||v_bussness_no||','
                  ||'''0'','
                  ||control_cur.config_rule_id||','
                  ||control_cur.rule_version||','
                  ||'''1'','
                  ||p_user_id ||','
                  ||'localtimestamp'
                  ||' FROM ods_' || v_biz_code
                  ||' t where task_status = ''0'''
                  ||' and t.draw_type = ''' || p_draw_type || ''''
                  ||' and t.task_code = ''' || p_task_code || ''''
                  ||' and t.' || control_cur.control_column || ' < ''' || p_year_month || ''''

                  ;
           --dbms_output.put_line('-- '||rec_config_rule.RULE_CODE||'-- '||rec_config_rule.RULE_C_NAME);
           --dbms_output.put_line(V_SQL);
           EXECUTE IMMEDIATE (V_SQL);
          COMMIT;
        EXCEPTION
            --意外处理
            WHEN OTHERS THEN
            --ROLLBACK;
            --V_ERRM := SQLCODE || '|' || SQLERRM;
            v_errm := SQLERRM;

            INSERT INTO dm_log_data_verify_detail
              (entity_id,
               year_month,
               task_code,
               user_id,
               biz_type_id,
               business_key,
               business_no,
               trace_code,
               trace_status,
               trace_msg,
               create_time)
            VALUES
              (p_entity_id,
               p_year_month,
               p_task_code,
               p_user_id,
               p_biz_type_id,
               control_cur.config_rule_id,
               v_bussness_no,
               'proc_data_verify_testing',
               '2',
               V_SQL,
               SYSDATE);
            COMMIT;
        END;
      END LOOP;
    END IF;*/

    IF v_errm IS NULL THEN
      proc_data_verify_log(p_entity_id,
                           p_year_month,
                           p_task_code,
                           p_user_id,
                           p_biz_type_id,
                           p_trace_no,
                           'proc_data_verify_testing',
                           '1',
                           NULL);
    ELSE
      proc_data_verify_log(p_entity_id,
                           p_year_month,
                           p_task_code,
                           p_user_id,
                           p_biz_type_id,
                           p_trace_no,
                           'proc_data_verify_testing',
                           '2',
                           'Check rule error');
    END IF;
  END proc_data_verify_testing;

  PROCEDURE proc_data_verify_result(p_entity_id   NUMBER,
                                    p_task_code   VARCHAR2,
                                    p_year_month  VARCHAR2,
                                    p_biz_type_id NUMBER,
                                    p_user_id     NUMBER) IS
    /***********************************************************************
    NAME : dm_pack_duct_verify_data_proc_data_rule_update_check_msg
    DESCRIPTION : 更新SRC表校验规则日志异常信息 Check_Msg，
                  更新信号表异常状态ods_data_push_signal
    DATE :2022-4-1
    AUTHOR :chenjf
    ***********************************************************************/
    v_count           NUMBER;
    v_src_count       NUMBER;
    v_success_count   NUMBER;
    v_error_count     NUMBER;
    v_exception_count number;
    v_day             VARCHAR2(10);
    v_ready_state     VARCHAR2(2);
    v_biz_code        VARCHAR2(100);
    v_errm            VARCHAR2(4000);
    v_error_msg       VARCHAR2(2000);
  BEGIN

    IF p_trace_no IS NULL THEN
      p_trace_no := dm_seq_log_data_verify.nextval;
    END IF;

    SELECT COUNT(1)
      INTO v_count
      FROM dm_log_check_rule a
     WHERE a.entity_id = p_entity_id
       AND a.biz_type_id = p_biz_type_id
       AND a.task_code = p_task_code;

    --获取表名
    SELECT t.biz_code
      INTO v_biz_code
      FROM dm_conf_table t
     WHERE t.biz_type_id = p_biz_type_id;

    --update error claim_status_code and msg
    IF v_count > 0 THEN
          proc_data_verify_log(p_entity_id,
                         p_year_month,
                         p_task_code,
                         p_user_id,
                         p_biz_type_id,
                         p_trace_no,
                         'proc_data_verify_result:update-fail-start',
                         '1',
                         NULL);
      EXECUTE IMMEDIATE ('merge into ods_' || v_biz_code ||' a
                        using (select a.data_key as id,
                              min(a.rule_type) as rule_type2,
                             listagg(a.rule_code, '','') within group(order by a.rule_code) as check_msg2
                            from dm_log_check_rule a
                           where a.entity_id = ' || p_entity_id ||'
                             and a.biz_type_id = ' || p_biz_type_id ||'
                             and a.task_code = ''' || p_task_code ||'''
                             and exists (select t.id
                                    from ods_' || v_biz_code ||' t
                                   where t.task_code = ''' || p_task_code ||'''
                                     and t.task_status = ''1''
                                     and a.data_key = t.id
                                     and  a.draw_type = t.draw_type
                                     and a.task_code = t.task_code)
                           group by a.data_key)b
                        on (a.id = b.id)
                        when matched then
                          update
                          set task_status = (case when rule_type2 = ''1'' then ''7'' else ''2'' end),
                              check_msg = check_msg2');
       commit;
                 proc_data_verify_log(p_entity_id,
                         p_year_month,
                         p_task_code,
                         p_user_id,
                         p_biz_type_id,
                         p_trace_no,
                         'proc_data_verify_result:update-fail-end',
                         '1',
                         NULL);
      /*EXECUTE IMMEDIATE ('UPDATE ODS_' || v_biz_code || ' t' ||
                        ' SET task_status = ''2'',' ||
                        ' check_msg  = (SELECT listagg(b.rule_code, '','') within GROUP(ORDER BY rule_code)' ||
                        ' FROM dm_log_check_rule a' ||
                        ' LEFT JOIN dm_conf_checkrule b' ||
                        ' ON b.config_rule_id = a.rule_id' ||
                        ' AND b.biz_type_id = a.biz_type_id' ||
                        ' WHERE a.draw_type = t.draw_type' ||
                        ' AND a.entity_id = ' || p_entity_id ||
                        ' AND a.task_code = t.task_code' ||
                        ' AND a.data_key = t.id' ||
                        ' AND a.biz_type_id =  ' || p_biz_type_id
                        --||' GROUP BY a.data_key, b.biz_type_id '
                        || ')' || ' WHERE t.task_code = ''' || p_task_code ||
                        ''' '
                        --||'and TASK_STATUS <> ''7'''
                        || ' AND EXISTS (SELECT 1' ||
                        ' FROM dm_log_check_rule a' ||
                        ' WHERE a.biz_type_id = ' || p_biz_type_id ||
                        ' AND a.entity_id = ' || p_entity_id ||
                        ' AND a.data_key = t.id' ||
                        ' AND a.task_code = t.task_code)');*/

    END IF;

    SELECT COUNT(1)
      INTO v_count
      FROM dm_log_data_verify l
     WHERE l.entity_id = p_entity_id
       AND l.biz_type_id = p_biz_type_id
       AND l.task_code = p_task_code
       AND lower(l.trace_code) = lower('proc_data_verify_testing')
       AND l.trace_status = '1'
       AND l.trace_no = p_trace_no;

    --if not extsing the check processing 'proc_data_verify_testing' seccess then return
    IF v_count > 0 THEN
      --update success claim_status_code
                proc_data_verify_log(p_entity_id,
                         p_year_month,
                         p_task_code,
                         p_user_id,
                         p_biz_type_id,
                         p_trace_no,
                         'proc_data_verify_result:update-success-start',
                         '1',
                         NULL);
      EXECUTE IMMEDIATE ('merge into ods_' || v_biz_code ||' a
                       using (select t.id,t.task_code
                        from ods_' || v_biz_code ||' t
                        WHERE t.task_code = ''' || p_task_code ||'''
                         and t.task_status = ''1''
                         AND NOT EXISTS (SELECT 1
                        FROM dm_log_check_rule l
                         WHERE l.entity_id = ' || p_entity_id ||'
                         AND l.biz_type_id = ' || p_biz_type_id ||'
                         AND l.data_key = t.id
                         AND l.task_code = t.task_code))b
                        on (a.id = b.id and a.task_code = b.task_code)
                        when matched then
                          update
                          set task_status = ''3'', check_msg  = null ');
                          commit;
              proc_data_verify_log(p_entity_id,
                         p_year_month,
                         p_task_code,
                         p_user_id,
                         p_biz_type_id,
                         p_trace_no,
                         'proc_data_verify_result:update-success-end',
                         '1',
                         NULL);
      /*EXECUTE IMMEDIATE ('UPDATE ODS_' || v_biz_code || '  t ' ||
                        ' SET task_status = ''3'', check_msg  = null ' ||
                        ' WHERE t.task_code = ''' || p_task_code ||
                        ''' and TASK_STATUS <> ''7''' ||
                        ' AND NOT EXISTS (SELECT 1 ' ||
                        ' FROM dm_log_check_rule l ' ||
                        ' WHERE l.entity_id = ' || p_entity_id ||
                        ' AND l.biz_type_id = ' || p_biz_type_id ||
                        ' AND l.data_key = t.id ' ||
                        ' AND l.task_code = t.task_code)');*/
    END IF;

    --statistics result
    EXECUTE IMMEDIATE ('SELECT COUNT(1),' ||
                      ' COUNT(CASE WHEN t.task_status in (''2'',''7'') THEN 1 ELSE NULL END),' ||
                      ' COUNT(CASE WHEN t.task_status = ''3'' THEN 1 ELSE NULL END),' ||
                      ' COUNT(CASE WHEN t.task_status = ''1'' THEN 1 ELSE NULL END)' ||
                      ' FROM ODS_' || v_biz_code || '  t' ||
                      ' WHERE t.task_code = ''' || p_task_code || '''')
      INTO v_src_count, v_error_count, v_success_count, v_exception_count;

    --re-correcting push signal claim_status_code
    UPDATE ods_data_push_signal a
       SET task_status = (CASE
                           WHEN v_exception_count = 0 AND v_error_count = 0 AND
                                v_src_count = a.row_count THEN
                            '3'
                           ELSE
                            '2'
                         END),
           deal_msg = (CASE
                        WHEN v_exception_count <> 0 THEN
                         'PerformAnomalies'
                        WHEN v_error_count <> 0 AND
                             v_src_count <> a.row_count THEN
                         'AllAnomalies'
                        WHEN v_error_count <> 0 THEN
                         'DataLevelAnomalies'
                        WHEN v_src_count <> a.row_count THEN
                         'TableLevelAnomalies'
                        ELSE
                         NULL
                      END),
           end_deal_time = SYSDATE
     WHERE upper(a.push_model) = upper(v_biz_code)
       AND a.task_code = p_task_code;
    COMMIT;

    IF v_error_count = 0 and v_exception_count = 0 THEN

      --业务期间日期
      v_day := substr(p_task_code, 14, 2);
      --按月 或 按天(是否是最后一天，如果是，调整为已准备，否则为准备中)
      IF (v_day = '00' OR
         p_year_month || v_day =
         to_char(last_day(to_date(p_year_month || '01', 'yyyyMMdd')),
                  'yyyyMMdd')) THEN

        v_ready_state := '1';

        UPDATE dm_conf_bussperiod_detail
           SET ready_state = v_ready_state, exec_result = 'success'
         WHERE buss_period_id =
               (SELECT buss_period_id
                  FROM dm_conf_bussperiod
                 WHERE entity_id = p_entity_id
                   AND year_month = p_year_month)
           AND biz_type_id = p_biz_type_id
           AND direction = '1';
        COMMIT;

        --查找所有输入为未准备的数据
        SELECT COUNT(1)
          INTO v_count
          FROM dm_conf_bussperiod_detail
         WHERE buss_period_id =
               (SELECT buss_period_id
                  FROM dm_conf_bussperiod
                 WHERE  entity_id = p_entity_id
                   AND year_month = p_year_month)
           AND direction = '1'
           AND ready_state <> '1';

        IF v_count = 0 THEN
          dm_pack_buss_period.proc_period_execution(p_entity_id,
                                                    v_ready_state);
        END IF;
      END IF;

    END IF;

    proc_data_verify_log(p_entity_id,
                         p_year_month,
                         p_task_code,
                         p_user_id,
                         p_biz_type_id,
                         p_trace_no,
                         'proc_data_verify_result',
                         '1',
                         NULL);

  EXCEPTION
    --意外处理
    WHEN OTHERS THEN
      --ROLLBACK;
      --V_ERRM := SQLCODE || '|' || SQLERRM;
      v_errm := SQLERRM;
      proc_data_verify_log(p_entity_id,
                           p_year_month,
                           p_task_code,
                           p_user_id,
                           p_biz_type_id,
                           p_trace_no,
                           'proc_data_verify_result',
                           '2',
                           v_errm);

      --意外处理
      v_error_msg := '[EXCEPTION][数据校验]proc_data_verify_result:' || SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004, v_error_msg);

  END proc_data_verify_result;

  PROCEDURE proc_data_verify_to_target(p_entity_id   NUMBER,
                                       p_task_code   VARCHAR2,
                                       p_year_month  VARCHAR2,
                                       p_biz_type_id NUMBER,
                                       p_user_id     NUMBER,
                                       p_draw_type   VARCHAR2) IS
    v_biz_code        VARCHAR2(64);
    v_count           NUMBER;
    v_cmunit_is       VARCHAR2(1);
    v_sql             VARCHAR2(4000);
    v_src_column      VARCHAR2(2000);
    v_tgt_column      VARCHAR2(2000);
    v_src_comm_column VARCHAR2(2000);
    v_tgt_comm_column VARCHAR2(2000);
    v_type_code_ref   VARCHAR2(64); --选择映射表的表名
    v_errm            VARCHAR2(2000);
    v_error_msg       VARCHAR2(2000);
  BEGIN

    IF p_trace_no IS NULL THEN
      p_trace_no := dm_seq_log_data_verify.nextval;
    END IF;

    -- routine body goes here...
    SELECT biz_code, cmunit_is
      INTO v_biz_code, v_cmunit_is
      FROM dm_conf_table t
     WHERE t.biz_type_id = p_biz_type_id;

    EXECUTE IMMEDIATE ('select count(1) from ODS_' || v_biz_code ||
                      ' WHERE TASK_STATUS = ''3''' || ' AND TASK_CODE =''' ||
                      p_task_code || '''' || ' AND draw_type = ''' ||
                      p_draw_type || '''')
      INTO v_count;

    IF v_count > 0 THEN

      SELECT listagg(CASE
                       WHEN upper(t.col_code) = upper(ref.src_column) AND
                            nvl(instr(ref.exclude_biz_code,
                                      '''' || v_biz_code || ''''),
                                0) < 1 THEN
                        CASE ref.column_rule
                          WHEN '0' THEN
                           REPLACE(ref.rule_config, '{param}', ref.src_column)
                          WHEN '1' THEN
                          -- modify by majingyun ， 增加： 同时怼进相同字段， 避免校验时连表查询
                           ' (' || REPLACE(ref.rule_config, ';', '') || ' )' || ',' ||
                           ref.src_column
                          WHEN '2' THEN --默认值
                              (case when upper(ref.COL_TYPE) = 'VARCHAR' then
                                    'coalesce('||ref.src_column||','''||ref.rule_config||''')'
                              else 'coalesce('||ref.src_column||','||ref.rule_config||')' end )
                          ELSE
                           t.col_code
                        END
                       ELSE
                        t.col_code
                     END,
                     ',') within GROUP(ORDER BY t.col_code, ref.tgt_column) AS src_column,
             listagg(CASE
                       WHEN upper(t.col_code) = upper(ref.src_column) AND
                            nvl(instr(ref.exclude_biz_code,
                                      '''' || v_biz_code || ''''),
                                0) < 1 THEN
                        CASE ref.column_rule
                        -- modify by majingyun ， 增加： 同时怼进相同字段， 避免校验时连表查询
                          WHEN '1' THEN
                           ref.tgt_column || ',' || ref.src_column
                          WHEN '2' THEN --默认值
                              ref.tgt_column
                          ELSE
                           ref.tgt_column
                        END
                       ELSE
                        t.col_code
                     END,
                     ',') within GROUP(ORDER BY t.col_code, ref.tgt_column) AS tgt_column
        INTO v_src_column, v_tgt_column
        FROM dm_conf_table_column t
        LEFT JOIN dm_conf_table_column_ref REF
          ON upper(t.col_code) = upper(ref.src_column)
         AND ref.original_is = '0'
       WHERE t.biz_type_id = p_biz_type_id;

      SELECT listagg(CASE t.column_rule
                       WHEN '0' THEN
                        REPLACE(t.rule_config, '{param}', t.src_column)
                       WHEN '1' THEN
                        ' (' || REPLACE(t.rule_config, ';', '') || ' )'
                       ELSE
                        t.src_column
                     END,
                     ',') within GROUP(ORDER BY t.src_column) AS src_comm,
             listagg(t.tgt_column, ',') within GROUP(ORDER BY t.src_column) AS tgt_comm
        INTO v_src_comm_column, v_tgt_comm_column
        FROM dm_conf_table_column_ref t
       WHERE t.original_is = '1';

       EXECUTE IMMEDIATE('merge into ods_' || v_biz_code ||' a
       using (select t.id,t.task_code from ods_' || v_biz_code ||' t WHERE t.TASK_STATUS = ''3''
             and t.task_code = ''' || p_task_code || '''
             and t.draw_type = ''' || p_draw_type || ''')b
      on (a.id = b.id and a.task_code = b.task_code)
      when matched then
        update
        set task_status = ''6'' ');
        commit;
      /*EXECUTE IMMEDIATE ('update ods_' || v_biz_code ||
                        ' set task_status = ''6''' ||
                        ' where task_status = ''3''' ||
                        ' and task_code = ''' || p_task_code || '''' ||
                        ' AND draw_type = ''' || p_draw_type || '''');*/

      --处理映射表名
      SELECT COUNT(1)
        INTO v_count
        FROM dm_conf_table_column_ref_ext
       WHERE upper(biz_code) = upper(v_biz_code);

      IF v_count > 0 THEN
        SELECT ref_view_table
          INTO v_type_code_ref
          FROM dm_conf_table_column_ref_ext
         WHERE upper(biz_code) = upper(v_biz_code);
      END IF;

      IF v_type_code_ref IS NULL THEN
        v_type_code_ref := 'ods_' || v_biz_code;
      END IF;

      v_sql := 'insert /*+append */  into dm_' || v_biz_code || ' (' || v_tgt_comm_column || ',' ||
               v_tgt_column || ')' || ' select ' || v_src_comm_column || ',' ||
               v_src_column || '' || ' from ' || v_type_code_ref || ' t ' ||
               ' where task_status = ''6'' ' || ' and task_code =''' ||
               p_task_code || ''' ' || ' AND draw_type = ''' || p_draw_type || '''' ||
               ' AND NOT EXISTS (SELECT a.id' || ' FROM dm_' || v_biz_code ||
               ' a ' || ' WHERE a.id = t.id)';

      EXECUTE IMMEDIATE (v_sql);
commit;
      --处理任务状态标识
      IF v_cmunit_is = '1' THEN
        EXECUTE IMMEDIATE ('update dm_' || v_biz_code ||
                          ' set task_status = ''4''' ||
                          ' where task_status = ''6''' ||
                          ' and task_code =''' || p_task_code || ''' ' ||
                          ' AND draw_type = ''' || p_draw_type || '''');
                          commit;
      END IF;

    END IF;

    proc_data_verify_log(p_entity_id,
                         p_year_month,
                         p_task_code,
                         p_user_id,
                         p_biz_type_id,
                         p_trace_no,
                         'proc_data_verify_to_target',
                         '1',
                         NULL);

    commit; --提交事务

  EXCEPTION
    WHEN OTHERS THEN
      v_errm := SQLERRM;
      proc_data_verify_log(p_entity_id,
                           p_year_month,
                           p_task_code,
                           p_user_id,
                           p_biz_type_id,
                           p_trace_no,
                           'proc_data_verify_to_target',
                           '2',
                           v_errm);

      --意外处理
      v_error_msg := '[EXCEPTION][数据校验]proc_data_verify_to_target:' ||
                     SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004, v_error_msg);

  END proc_data_verify_to_target;

  PROCEDURE proc_data_verify_clean(p_entity_id  NUMBER,
                                   p_task_code  VARCHAR2,
                                   p_push_model VARCHAR2,
                                   p_year_month VARCHAR2,
                                   p_user_id    NUMBER,
                                   p_draw_type  VARCHAR2) IS

    v_biz_type_id NUMBER;
    v_errm        VARCHAR2(4000);
    v_error_msg   VARCHAR2(2000);
  BEGIN

    IF p_trace_no IS NULL THEN
      p_trace_no := dm_seq_log_data_verify.nextval;
    END IF;

    IF p_push_model IS NOT NULL THEN

      SELECT t.biz_type_id
        INTO v_biz_type_id
        FROM dm_conf_table t
       WHERE t.biz_code = p_push_model;

      --delete src data
      EXECUTE IMMEDIATE ('delete from ODS_' || p_push_model ||
                        ' where task_code = ''' || p_task_code ||
                        ''' and draw_type =''' || p_draw_type || '''');

      --the data clean of tgt and cmunit
      proc_data_verify_prepare(p_entity_id,
                               p_task_code,
                               v_biz_type_id,
                               p_year_month,
                               p_user_id,
                               p_draw_type);

      --更新信号表状态
      update ods_data_push_signal t
         set task_status   = '4',
             DEAL_MSG      = 'Invalid data',
             end_deal_time = sysdate
       where upper(t.push_model) = upper(p_push_model)
         and t.task_code = p_task_code;
      COMMIT;

      --更新统计数据  add by majingyun
      dm_pack_duct_stat.proc_paring_stat(p_entity_id,
                                         p_task_code,
                                         v_biz_type_id);

      --log trace msg
      proc_data_verify_log(p_entity_id,
                           p_year_month,
                           p_task_code,
                           p_user_id,
                           v_biz_type_id,
                           p_trace_no,
                           'proc_data_verify_clean',
                           '1',
                           NULL);
    ELSE
      proc_data_verify_log(p_entity_id,
                           p_year_month,
                           p_task_code,
                           p_user_id,
                           v_biz_type_id,
                           p_trace_no,
                           'proc_data_verify_clean',
                           '2',
                           'not data');
    END IF;

  EXCEPTION
    --意外处理
    WHEN OTHERS THEN
      ROLLBACK;
      v_errm := SQLERRM;
      proc_data_verify_log(p_entity_id,
                           p_year_month,
                           p_task_code,
                           p_user_id,
                           v_biz_type_id,
                           p_trace_no,
                           'proc_data_verify_clean',
                           '2',
                           v_errm);

      --意外处理
      v_error_msg := '[EXCEPTION][数据校验]proc_data_verify_clean:' || SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004, v_error_msg);

  END proc_data_verify_clean;

  PROCEDURE proc_data_verify_log(p_entity_id    NUMBER,
                                 p_year_month   VARCHAR2,
                                 p_task_code    VARCHAR2,
                                 p_user_id      NUMBER,
                                 p_biz_type_id  NUMBER,
                                 p_trace_no     VARCHAR2,
                                 p_trace_code   VARCHAR2,
                                 p_trace_status VARCHAR2,
                                 p_trace_msg    VARCHAR2) IS
  BEGIN
    INSERT INTO dm_log_data_verify
      (entity_id,
       year_month,
       task_code,
       user_id,
       biz_type_id,
       trace_no,
       trace_code,
       trace_status,
       trace_msg,
       create_time)
    VALUES
      (p_entity_id,
       p_year_month,
       p_task_code,
       p_user_id,
       p_biz_type_id,
       p_trace_no,
       p_trace_code,
       p_trace_status,
       p_trace_msg,
       SYSDATE);
    COMMIT;

  EXCEPTION
    --意外处理
    WHEN OTHERS THEN
      NULL;
  END proc_data_verify_log;

  FUNCTION func_check_endorse_type(p_endorse_type_str varchar2) RETURN NUMBER IS
    -- 校验一个批改类型字符串是否合法， 合法返回 1， 不合法返回 0
    -- 合法的批改类型字符串必须满足：
    --  1) 不为空
    --  2) 所有的批改类型代码都必须有效， 多个时用中英文逗号分隔， 且不可重复
    --  3) 中英文逗号左右必须都是批改类型代码， 不可有空格
    v_array                   type_array;
    v_temp_endorse_type_array type_array;
    v_array_index             number(3) := 1;
    v_last_sep_index          number(3) := 0;
    v_len                     number(3);
    v_temp                    varchar2(1000) := '';
  begin

    -- 如果缓存为空， 构造缓存
    if v_endorse_type_cache.count = 0 then
      select cc.code_code
        bulk collect
        INTO v_temp_endorse_type_array
        from bpluser.bpl_v_conf_code cc
       WHERE exists (SELECT 1
                FROM bpluser.bpl_v_conf_code c
               WHERE c.code_code_idx = 'PolicyEndorseType'
                 AND c.valid_is = '1'
                 and c.code_id = cc.upper_code_id)
         and cc.valid_is = '1';

      for i in 1 .. v_temp_endorse_type_array.count loop
        v_endorse_type_cache(v_temp_endorse_type_array(i)) := 1;
      end loop;
    end if;

    if trim(p_endorse_type_str) is null then
      return 0;
    end if;

    -- 将原批改类型字符串按中英文逗号分割， 拆到数组中
    v_len := length(p_endorse_type_str);
    for i in 1 .. v_len loop
      v_temp := null;

      if substr(p_endorse_type_str, i, 1) in (',', '，') then
        -- 检查非法情况： 逗号连续、逗号是第一个或最后一个字符
        if i = v_last_sep_index + 1 or i = v_len then
          return 0;
        end if;

        v_temp           := substr(p_endorse_type_str,
                                   v_last_sep_index + 1,
                                   i - v_last_sep_index - 1);
        v_last_sep_index := i;
      elsif i = v_len then
        v_temp := substr(p_endorse_type_str,
                         v_last_sep_index + 1,
                         i - v_last_sep_index);
      end if;

      if v_temp is not null then
        -- 检查重复
        for k in 1 .. v_array.count loop
          if v_array(k) = v_temp then
            return 0;
          end if;
        end loop;

        v_array(v_array_index) := v_temp;
        v_array_index := v_array_index + 1;
      end if;
    end loop;

    -- 检查每个批改类型是否合法
    for i in 1 .. v_array.count loop
      begin
        v_len := v_endorse_type_cache(v_array(i));
      exception
        when no_data_found then
          return 0;
      end;
    end loop;

    return 1;
  end func_check_endorse_type;

  function func_check_entity(p_entity_id varchar2, p_entity_code varchar2)
    return number is
    -- 校验一个 entity 是否合法， 合法返回 1， 不合法返回 0
    v_temp_array type_array;
    v_temp       number(1);
  begin
    -- 如果缓存为空， 构造缓存
    if v_entity_cache.count = 0 then
      select t.entity_id || '/#' || t.entity_code
        bulk collect
        INTO v_temp_array
        from bpluser.bbs_entity t
       where t.unit_type = 'B';

      for i in 1 .. v_temp_array.count loop
        v_entity_cache(v_temp_array(i)) := 1;
      end loop;
    end if;

    begin
      v_temp := v_entity_cache(p_entity_id || '/#' || p_entity_code);
    exception
      when no_data_found then
        return 0;
    end;

    return v_temp;
  end func_check_entity;

  function func_check_department(p_entity_id   varchar2,
                                 p_entity_code varchar2) return number is
    -- 校验一个 Department 是否合法， 合法返回 1， 不合法返回 0
    v_temp_array type_array;
    v_temp       number(1);
  begin
    -- 如果缓存为空， 构造缓存
    if v_department_cache.count = 0 then
      select t.entity_id || '/#' || t.entity_code
        bulk collect
        INTO v_temp_array
        from bpluser.bbs_entity t
       where t.unit_type = 'D';

      for i in 1 .. v_temp_array.count loop
        v_department_cache(v_temp_array(i)) := 1;
      end loop;
    end if;

    begin
      v_temp := v_department_cache(p_entity_id || '/#' || p_entity_code);
    exception
      when no_data_found then
        return 0;
    end;

    return v_temp;
  end func_check_department;

  function func_check_account(p_book_code    varchar2,
                              p_account_id   varchar2,
                              p_account_code varchar2) return number is
    -- 校验一个 account 是否合法， 合法返回 1， 不合法返回 0
    v_temp_array type_array;
    v_temp       number(1);
  begin
    -- 如果缓存为空， 构造缓存
    if v_account_cache.count = 0 then
      select t.account_id || '/#' || t.account_code || '/#' || t.book_code
        bulk collect
        INTO v_temp_array
        from bpluser.bbs_v_account t;

      for i in 1 .. v_temp_array.count loop
        v_account_cache(v_temp_array(i)) := 1;
      end loop;
    end if;

    begin
      v_temp := v_account_cache(p_account_id || '/#' || p_account_code || '/#' ||
                                p_book_code);
    exception
      when no_data_found then
        return 0;
    end;

    return v_temp;
  end func_check_account;

  function func_check_exits_acc_article_balance(p_entity_code       varchar2,
                                                p_base_account_code varchar2,
                                                p_account_code      varchar2,
                                                p_book_code         varchar2,
                                                p_year_month        varchar2,
                                                p_article           varchar2,
                                                p_currency          varchar2)
    return number is
    ------------------------------------------------------------
    -- 检查是否存在 tgt_acc_article_balance 表中， 是返回 1， 否返回 0
    ------------------------------------------------------------
    v_count number(1);
  begin

    select count(*)
      into v_count
      from dm_fin_article_balance tgt
     where tgt.entity_code = p_entity_code
       and tgt.root_account_code = p_base_account_code
       and tgt.account_code = p_account_code
       and tgt.book_code = p_book_code
       and tgt.year_month = p_year_month
       and tgt.article = p_article
       and tgt.currency_code = p_currency
       and rownum = 1;

    return v_count;

  end func_check_exits_acc_article_balance;

  function func_check_exists_acc_voucherdetail(p_entity_code    varchar2,
                                               p_VOUCHER_NO     varchar2,
                                               p_VOUCHER_SEQ_NO number)
    return number is
    ------------------------------------------------------------
    -- 检查是否存在 DM_ACC_VOUCHERDETAIL 表中， 是返回 1， 否返回 0
    ------------------------------------------------------------
    v_temp number(1);
  begin
    select (SELECT 1
              FROM DM_FIN_VOUCHER_DETAIL tgt
             WHERE tgt.entity_code = p_entity_code
               and tgt.VOUCHER_NO = p_VOUCHER_NO
               and tgt.VOUCHER_SEQ_NO = p_VOUCHER_SEQ_NO)
      into v_temp
      from dual;
    return nvl(v_temp, 0);
  end func_check_exists_acc_voucherdetail;

  function func_check_exists_ods_fin_article_balance(p_id                number,
                                                     p_task_code         varchar2,
                                                     p_entity_code       varchar2,
                                                     p_book_code         varchar2,
                                                     p_year_month        varchar2,
                                                     p_root_account_code varchar2,
                                                     p_account_code      varchar2,
                                                     p_article           varchar2,
                                                     p_currency_code     varchar2)
    return number is
    ------------------------------------------------------------
    -- 检查是否存在 other ods_fin_article_balance ， 是返回 1， 否返回 0
    ------------------------------------------------------------
    v_count number(11);
  begin
    select /*+index (t ODS_IDX_FIN_ARTICLE_BALANCE_BL_HASH)  */
     count(*)
      into v_count
      from ods_fin_article_balance t
     where t.task_code = p_task_code
       and t.entity_code = p_entity_code
       and t.book_code = p_book_code
       and t.year_month = p_year_month
       and t.root_account_code = p_root_account_code
       and t.account_code = p_account_code
       and t.article = p_article
       and t.currency_code = p_currency_code
       and t.id <> p_id;

    if v_count > 0 then
      return 1;
    end if;
    return 0;
  end func_check_exists_ods_fin_article_balance;

END dm_pack_data_verify;
/
