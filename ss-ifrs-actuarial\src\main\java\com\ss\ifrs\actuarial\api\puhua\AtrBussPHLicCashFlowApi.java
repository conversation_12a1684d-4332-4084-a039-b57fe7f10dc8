package com.ss.ifrs.actuarial.api.puhua;

import com.ss.ifrs.actuarial.constant.ActuarialConstant;
import com.ss.ifrs.actuarial.domain.service.AtrDomainTaskService;
import com.ss.ifrs.actuarial.pojo.puhua.lrc.vo.AtrBussLicGAccAmountVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLicCashFlowVo;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo;
import com.ss.ifrs.actuarial.service.puhua.AtrBussPuHuaLicActionService;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.platform.core.annotation.LogScheduleTask;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.core.constant.SystemConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.pojo.com.vo.ActOverviewVo;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: AtrLicCsmCalcApi
 * @Description: LIC和CSM计算对外Api接口
 * @Author: yinxh.
 * @CreateDate: 2021/3/8 17:46
 * @Version: 1.0
 */
@RestController
@RequestMapping( "/lic_action/puhua")
public class AtrBussPHLicCashFlowApi extends BaseApi{
	
	// 日志管理
	final Logger LOG  = LoggerFactory.getLogger(getClass());
	
	@Autowired
	private AtrBussPuHuaLicActionService atrBussLicCashFlowService;

	@Autowired
	private AtrDomainTaskService atrDomainTaskService;

	@ApiOperation(value = "查找对象")
	@RequestMapping(value = "/enquiry", method = { RequestMethod.POST })
	public BaseResponse<Object> search(HttpServletRequest request, @RequestBody AtrBussLicCashFlowVo atrBussLicCashFlowVo,
									   int _pageNo, int _pageSize) {
		Pageable pageParam = new Pageable(_pageNo, _pageSize );
		Page<AtrBussLicCashFlowVo> result = atrBussLicCashFlowService.findForDataTables(atrBussLicCashFlowVo, pageParam);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("atrBussLicCashFlowVoList", result);
		return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
	}

	@ApiOperation(value = "提取")
	@LogScheduleTask(bizCode = "BUSS_LIC_BECF_CALC", argsValue = { "#atrBussLicCashFlowVo.entityId",
			"#atrBussLicCashFlowVo.yearMonth",
			"#atrBussLicCashFlowVo.taskMode",
			"#atrBussLicCashFlowVo.taskCode",
			"#atrBussLicCashFlowVo.retryOrder"})
	@RequestMapping(value = "/add", method = RequestMethod.POST)
	public BaseResponse<Object> add(HttpServletRequest request, @RequestBody @Validated
									AtrBussLicCashFlowVo atrBussLicCashFlowVo) {
		atrBussLicCashFlowVo.setCreatorId(this.loginUserId(request));
		atrBussLicCashFlowVo.setCreateTime(new Date());
		atrBussLicCashFlowVo.setDrawUser(this.loginUserId(request));
		atrBussLicCashFlowVo.setDrawTime(new Date());
		atrBussLicCashFlowVo.setDrawType("3");
		try {
			Long userId = this.loginUserId(request);
			atrBussLicCashFlowService.save(atrBussLicCashFlowVo, userId);
			return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
		} catch (Exception e) {
			LOG.error(e.getLocalizedMessage(), e);
			return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, e.getMessage());
		}
	}

	@ApiOperation(value = "根据id查询Lic计算信息")
	@RequestMapping(value = "/find_by_id/{id}", method = RequestMethod.GET)
	@PermissionRequest(required = false)
	public BaseResponse<AtrBussLicCashFlowVo> findById(@PathVariable("id") Long id) {
		AtrBussLicCashFlowVo atrBussLicCashFlowVo = atrBussLicCashFlowService.findById(id);
		return new BaseResponse<AtrBussLicCashFlowVo>(ResCodeConstant.ResCode.SUCCESS, atrBussLicCashFlowVo);
	}

	@ApiOperation(value = "删除Lic计算信息")
	@RequestMapping(value = "/delete_by_id/{id}", method = RequestMethod.GET)
	@PermissionRequest(required = false)
	public BaseResponse<Object> delete(HttpServletRequest request, @PathVariable("id") Long id) {
		try {
			Long userId = this.loginUserId(request);
			atrBussLicCashFlowService.delete(id, userId);
			return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
		} catch (Exception e) {
			logger.error(e.getLocalizedMessage(), e);
			return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
		}
	}

	@ApiOperation("实付赔款查询")
	@RequestMapping(value = "/detail/showDataForClaimPaid", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<Object> showDataForClaimPaid(@RequestBody AtrDapDrawVo atrDapDrawVo) {
		Map<String, Object> map = atrBussLicCashFlowService.findClaimPaid(atrDapDrawVo);
		return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
	}

	@ApiOperation("赔付模式查询")
	@RequestMapping(value = "/detail/showPaidMode", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<Object> showPaidMode(@RequestBody AtrDapDrawVo atrDapDrawVo) {
		List<AtrDapDrawVo> atrPaidModeDetailVos = atrBussLicCashFlowService.findPaidMode(atrDapDrawVo);
		return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, atrPaidModeDetailVos);
	}

/*	@ApiOperation("Ult查询")
	@RequestMapping(value = "/detail/showUlt", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<Object> showUlt(@RequestBody AtrDapDrawVo atrDapDrawVo) {
		Map<String, Object> map = atrBussLicCashFlowService.findUlt(atrDapDrawVo);
		return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
	}*/

	@ApiOperation(value = "查询计算主表id")
	@RequestMapping(value = "/findId", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<AtrDapDrawVo> findId(@RequestBody AtrDapDrawVo vo) {
		AtrDapDrawVo atrDapDrawVo = atrBussLicCashFlowService.findId(vo);
		return new BaseResponse<AtrDapDrawVo>(ResCodeConstant.ResCode.SUCCESS, atrDapDrawVo);
	}

	@ApiOperation("预期赔付查询")
	@RequestMapping(value = "/detail/showExpectedClaim", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<Object> showExpectedClaim(@RequestBody AtrDapDrawVo atrDapDrawVo) {
		Map<String, Object> map = atrBussLicCashFlowService.showExpectedClaim(atrDapDrawVo);
		return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
	}

	@ApiOperation("Calc实付赔款查询")
	@RequestMapping(value = "/detail/showCalcClaimPaid", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<Object> findCalcClaimPaid(@RequestBody AtrDapDrawVo atrDapDrawVo) {
		Map<String, Object> map = atrBussLicCashFlowService.findCalcClaimPaid(atrDapDrawVo);
		return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
	}

	@ApiOperation(value = "确认")
	@RequestMapping(value = "/confirm", method = RequestMethod.POST)
	@LogScheduleTask(bizCode = "BUSS_LIC_BECF_CFM", argsValue = { "#atrBussLicCashFlowVo.entityId",
			"#atrBussLicCashFlowVo.yearMonth",
			"#atrBussLicCashFlowVo.taskMode",
			"#atrBussLicCashFlowVo.taskCode",
			"#atrBussLicCashFlowVo.retryOrder"})
	@PermissionRequest(required = false)
	public BaseResponse<Object> confirm(HttpServletRequest request, @RequestBody @Validated
			AtrBussLicCashFlowVo atrBussLicCashFlowVo) {
		atrBussLicCashFlowVo.setUpdatorId(this.loginUserId(request));
		atrBussLicCashFlowVo.setUpdateTime(new Date());
		try {
			Long userId = this.loginUserId(request);
			Boolean confirmFlag = atrBussLicCashFlowService.confirm(atrBussLicCashFlowVo, userId);
			return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, confirmFlag);
		} catch (Exception e) {
			LOG.error(e.getLocalizedMessage(), e);
			return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, e.getMessage());
		}
	}

	@ApiOperation("查询同一批次下不同合同组合数据")
	@RequestMapping(value = "/findPortfolioData", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<Object> findPortfolioData(@RequestBody AtrDapDrawVo atrDapDrawVo, int _pageSize, int _pageNo) {
		Pageable pageParam = new Pageable(_pageNo, _pageSize);
		Page<AtrDapDrawVo> page = atrBussLicCashFlowService.findPortfolioData(atrDapDrawVo, pageParam);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("resultList",page);
		return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
	}

	@ApiOperation(value = "预期赔付现金流计算")
	@PermissionRequest(required = false)
	@LogScheduleTask(bizCode = "BUSS_LIC_BECF_CALC", argsValue = { "#atrBussLicCashFlowVo.entityId",
			"#atrBussLicCashFlowVo.yearMonth",
			"#atrBussLicCashFlowVo.taskMode",
			"#atrBussLicCashFlowVo.taskCode",
			"#atrBussLicCashFlowVo.retryOrder"})
	@RequestMapping(value = "/addAll", method = RequestMethod.POST)
	public BaseResponse<Object> calculateAll(HttpServletRequest request, @RequestBody @Validated
											AtrBussLicCashFlowVo atrBussLicCashFlowVo) {
		atrBussLicCashFlowVo.setCreatorId(this.loginUserId(request));
		atrBussLicCashFlowVo.setCreateTime(new Date());
		atrBussLicCashFlowVo.setDrawUser(this.loginUserId(request));
		atrBussLicCashFlowVo.setDrawTime(new Date());
		atrBussLicCashFlowVo.setDrawType("3");
		try {
			Long userId = this.loginUserId(request);
			// 添加同步预期现金流节点下子节点流程执行状态
			ActOverviewVo param = new ActOverviewVo();
			param.setProcCode(ActuarialConstant.ProcCode.EXPECTED_CF);
			ActOverviewVo actOverviewVo = atrDomainTaskService.getActOverviewVoByObject(param);
			atrBussLicCashFlowVo.setProcId(actOverviewVo.getProcId());
			atrBussLicCashFlowVo.setSystemCode(SystemConstant.AtrIdentity.APP_CODE);
			atrBussLicCashFlowService.calculateAll(atrBussLicCashFlowVo, userId);
			return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, null);
		} catch (Exception e) {
			LOG.error(e.getLocalizedMessage(), e);
			return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, e.getMessage());
		}
	}

	@ApiOperation(value = "预期赔付现金流计算后确认")
	@PermissionRequest(required = false)
	@LogScheduleTask(bizCode = "BUSS_LIC_BECF_CFM", argsValue = { "#atrBussLicCashFlowVo.entityId",
			"#atrBussLicCashFlowVo.yearMonth",
			"#atrBussLicCashFlowVo.taskMode",
			"#atrBussLicCashFlowVo.taskCode",
			"#atrBussLicCashFlowVo.retryOrder"})
	@RequestMapping(value = "/confirmAll", method = RequestMethod.POST)
	public BaseResponse<Object> confirmPeriod(HttpServletRequest request, @RequestBody @Validated AtrBussLicCashFlowVo atrBussLicCashFlowVo) {
		atrBussLicCashFlowVo.setUpdatorId(this.loginUserId(request));
		atrBussLicCashFlowVo.setUpdateTime(new Date());
		try {
			Long userId = this.loginUserId(request);
			// 添加同步折现节点下子节点流程执行状态
			atrBussLicCashFlowVo.setSystemCode(SystemConstant.AtrIdentity.APP_CODE);
			atrBussLicCashFlowService.confirmLicCfVersion(atrBussLicCashFlowVo, userId);
			return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
		} catch (Exception e) {
			LOG.error(e.getLocalizedMessage(), e);
			return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, e.getMessage());
		}
	}

	@ApiOperation(value = "导出预期赔付现金流数据")
	@RequestMapping(value = "/export_excel", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public void exportExcel(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrBussLicCashFlowVo atrBussLicCashFlowVo) {
		try {
			Long userId = this.loginUserId(request);
			atrBussLicCashFlowService.exportLicResultExcel(request, response, atrBussLicCashFlowVo, userId);
		} catch (Exception e) {
			logger.error(e.getLocalizedMessage(), e);
		}
	}

	@ApiOperation("预期赔付查询")
	@RequestMapping(value = "/detail/cf_total", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<List<AtrBussLicGAccAmountVo>> showExpectedClaimTotal(@RequestBody AtrDapDrawVo atrDapDrawVo) {
		List<AtrBussLicGAccAmountVo> licIcgAmountVos = atrBussLicCashFlowService.showExpectedClaimTotal(atrDapDrawVo);
		return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, licIcgAmountVos);
	}

	@ApiOperation(value = "导出预期赔付现金流数据")
	@RequestMapping(value = "/download", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public void exportAioiExcel(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrBussLicCashFlowVo atrBussLicCashFlowVo) {
		try {
			Long userId = this.loginUserId(request);
			atrBussLicCashFlowService.exportLicAioiExcel(request, response, atrBussLicCashFlowVo, userId);
		} catch (Exception e) {
			logger.error(e.getLocalizedMessage(), e);
		}
	}
}

