package com.ss.ifrs.actuarial.api.puhua;


import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrBussBecfViewVo;
import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrConfBecfOutPutVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcAction;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussQuotaValueVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaDefVo;
import com.ss.ifrs.actuarial.service.AtrBussBecfQuotaService;
import com.ss.ifrs.actuarial.service.AtrConfQuotaDefService;
import com.ss.ifrs.actuarial.service.puhua.AtrBussPuHuaActionService;
import com.ss.ifrs.actuarial.service.puhua.AtrBussPuHuaLrcActionService;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController("becfQuotaApi")
@RequestMapping("/becf_quota")
@Api(value = "计量评估接口")
public class AtrBussBecfQuotaApi extends BaseApi {

    @Autowired
    AtrConfQuotaDefService atrConfQuotaDefService;

    @Autowired
    AtrBussBecfQuotaService bussBecfQuotaService;

    @Autowired
    AtrBussPuHuaActionService atrBussPuHuaActionService;

    @ApiOperation(value = "查询假设配置中的枚举类型编码")
    @RequestMapping(value = "/base/find_quota_code_type", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findConfQuotaCodeType() {
        List<AtrConfQuotaDefVo> map =  atrConfQuotaDefService.findConfQuotaCodeType();
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
    }


    @ApiOperation(value = "查询预期计量的业务假设数据表头")
    @RequestMapping(value = "/find_buss_quota_def", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findBussQuotaDtlByVo(@RequestBody AtrBussLrcAction atrBussLrcAction, HttpServletRequest request) {
        //Map<String, Object> resultMap = atrBussEvaluateService.findBussQuotaDtlByVo(atrBussLrcAction);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, null);
    }

    @ApiOperation(value = "查询预期计量的业务假设数据数据")
    @RequestMapping(value = "/find_buss_quota", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findBussQuotaById(@RequestBody AtrBussQuotaValueVo atrBussLrcAction ) {
        Map<String, Object> resultMap = bussBecfQuotaService.findBussQuotaData(atrBussLrcAction);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, resultMap);
    }

    @ApiOperation(value = "查询预期计量的业务假设发展期数据")
    @RequestMapping(value = "/find_buss_quota_period", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findBussQuotaPeriodById(@RequestBody AtrBussQuotaValueVo atrBussLrcAction) {
        Map<String, Object> resultMap = bussBecfQuotaService.findBussQuotaDataDetail(atrBussLrcAction);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, resultMap);
    }



    @ApiOperation(value = "根据codeCodeIdx查询有效的数据")
    @RequestMapping(value = "/find_becf_out", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findByCodeIdx(@RequestBody AtrBussQuotaValueVo atrBussQuotaValueVo) {
        List<AtrConfBecfOutPutVo> list = bussBecfQuotaService.findBecfOutPut(atrBussQuotaValueVo);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, list);

    }

    @ApiOperation(value = "下载")
    @RequestMapping(value = "/becf_download", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public void becfDownload(@RequestBody AtrBussBecfViewVo atrBussLrcActionVo, HttpServletRequest request, HttpServletResponse response) {
        atrBussLrcActionVo.setLanguage(request.getHeader("ss-Language"));
        atrBussLrcActionVo.setCreatorId(this.loginUserId(request));
        try {
            atrBussPuHuaActionService.becfDownload(atrBussLrcActionVo, request, response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
