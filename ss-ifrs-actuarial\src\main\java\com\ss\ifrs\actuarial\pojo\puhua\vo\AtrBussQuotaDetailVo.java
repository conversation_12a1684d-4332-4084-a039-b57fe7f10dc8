/**
 * 
 * This file was generated by  MyBatis Generator(v1.2.12).
 * Date: 2021-11-11 16:15:28
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027,   All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.puhua.vo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * This code was generated by  MyBatis Generator(v1.2.12).
 * Create Date: 2021-11-11 16:15:28<br/>
 * Description: null<br/>
 * Table Name: atr_buss_quota_detail<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
public class AtrBussQuotaDetailVo implements Serializable {
    /**
     * Database column: atr_buss_quota_detail.quota_detail_id
     * Database remarks: quotaDetailId|主键
     */
    @ApiModelProperty(value = "quotaDetailId|主键", required = true)
    private Long quotaDetailId;

    /**
     * Database column: atr_buss_quota_detail.buss_quota_id
     * Database remarks: bussQuotaId|指标
     */
    @ApiModelProperty(value = "bussQuotaId|指标", required = true)
    private Long bussQuotaId;

    /**
     * Database column: atr_buss_quota_detail.quota_period
     * Database remarks: quotaPeriod|发展期
     */
    @ApiModelProperty(value = "quotaPeriod|发展期", required = false)
    private Long quotaPeriod;

    /**
     * Database column: atr_buss_quota_detail.quota_value
     * Database remarks: quotaValue|指标值
     */
    @ApiModelProperty(value = "quotaValue|指标值", required = false)
    private String quotaValue;

    private Long quotaId;

    private Long quotaDefId;

    private String quotaCode;

    public Long getQuotaId() {
        return quotaId;
    }

    public void setQuotaId(Long quotaId) {
        this.quotaId = quotaId;
    }

    public Long getQuotaDefId() {
        return quotaDefId;
    }

    public void setQuotaDefId(Long quotaDefId) {
        this.quotaDefId = quotaDefId;
    }

    private static final long serialVersionUID = 1L;

    public Long getQuotaDetailId() {
        return quotaDetailId;
    }

    public void setQuotaDetailId(Long quotaDetailId) {
        this.quotaDetailId = quotaDetailId;
    }

    public Long getBussQuotaId() {
        return bussQuotaId;
    }

    public void setBussQuotaId(Long bussQuotaId) {
        this.bussQuotaId = bussQuotaId;
    }

    public Long getQuotaPeriod() {
        return quotaPeriod;
    }

    public void setQuotaPeriod(Long quotaPeriod) {
        this.quotaPeriod = quotaPeriod;
    }

    public String getQuotaValue() {
        return quotaValue;
    }

    public void setQuotaValue(String quotaValue) {
        this.quotaValue = quotaValue;
    }

    public String getQuotaCode() {
        return quotaCode;
    }

    public void setQuotaCode(String quotaCode) {
        this.quotaCode = quotaCode;
    }
}