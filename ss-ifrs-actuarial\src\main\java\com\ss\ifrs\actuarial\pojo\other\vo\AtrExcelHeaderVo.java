package com.ss.ifrs.actuarial.pojo.other.vo;


import org.apache.poi.ss.usermodel.HorizontalAlignment;

/**
 * @ClassName AtrExcelHeaderVo
 * @Description Excel表头对象
 * <AUTHOR>
 * @Date 2022/8/26
 **/
public class AtrExcelHeaderVo {


    /**
     * @Method AtrExcelHeaderVo
     * <AUTHOR>
     * @Date 2022/9/8
     * @Description
     * @param title 标题
     * @param property
     * @param propertyEName 属性英文名称
     * @param propertyCName 属性简体名称
     * @param propertyTName 属性繁体名称
     * @param dataType 数据类型
     * @param format 格式化
     * @param comment 批注
     * @param length 长度
     * @param codeType 枚举
     * @param alignment 对齐方式
     * @Return
     */
    public AtrExcelHeaderVo(String title, String property, String propertyEName, String propertyCName, String propertyTName, String dataType, String format, String comment, String length, String codeType, HorizontalAlignment alignment) {
        this.title = title;
        this.property = property;
        this.propertyEName = propertyEName;
        this.propertyCName = propertyCName;
        this.propertyTName = propertyTName;
        this.dataType = dataType;
        this.format = format;
        this.comment = comment;
        this.length = length;
        this.codeType = codeType;
        this.alignment = alignment;
    }

    /**
     * 标题
     */
    private String title;

    /**
     * 属性
     */
    private String property;

    /**
     * 属性英文名称
     */
    private String propertyEName;

    /**
     * 属性简体名称
     */
    private String propertyCName;

    /**
     * 属性繁体名称
     */
    private String propertyTName;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 格式化
     */
    private String format;

    /**
     * 批注
     */
    private String comment;

    /**
     * 字段长度
     */
    private String length;

    /**
     * 枚举
     */
    private String codeType;

    /**
     * 对齐方式
     */
    private HorizontalAlignment alignment;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getProperty() {
        return property;
    }

    public void setProperty(String property) {
        this.property = property;
    }

    public String getPropertyEName() {
        return propertyEName;
    }

    public void setPropertyEName(String propertyEName) {
        this.propertyEName = propertyEName;
    }

    public String getPropertyCName() {
        return propertyCName;
    }

    public void setPropertyCName(String propertyCName) {
        this.propertyCName = propertyCName;
    }

    public String getPropertyTName() {
        return propertyTName;
    }

    public void setPropertyTName(String propertyTName) {
        this.propertyTName = propertyTName;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getLength() {
        return length;
    }

    public void setLength(String length) {
        this.length = length;
    }

    public String getCodeType() {
        return codeType;
    }

    public void setCodeType(String codeType) {
        this.codeType = codeType;
    }

    public HorizontalAlignment getAlignment() {
        return alignment;
    }

    public void setAlignment(HorizontalAlignment alignment) {
        this.alignment = alignment;
    }
}
