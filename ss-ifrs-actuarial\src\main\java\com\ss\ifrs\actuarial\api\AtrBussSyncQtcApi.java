package com.ss.ifrs.actuarial.api;

import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussSyncQtcVo;
import com.ss.ifrs.actuarial.service.impl.AtrBussEcfSyncQtcService;
import com.ss.library.utils.LazyValidator;
import com.ss.library.utils.SpringContextUtil;
import com.ss.platform.core.annotation.LogScheduleTask;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping( "/sync_qtc")
@Slf4j
public class AtrBussSyncQtcApi extends BaseApi {

    @ApiOperation(value = "确认")
    @RequestMapping(value = "/entry", method = RequestMethod.POST)
    @LogScheduleTask(bizCode = "BUSS_ATR_SYNC_QTC",
            argsValue = {"#atrBussSyncQtcVo.entityId",
                    "#atrBussSyncQtcVo.yearMonth",
                    "#atrBussSyncQtcVo.taskMode",
                    "#atrBussSyncQtcVo.taskCode",
                    "#atrBussSyncQtcVo.retryOrder"})
    @PermissionRequest
    public BaseResponse<?> entry(@RequestBody AtrBussSyncQtcVo atrBussSyncQtcVo) {
        try {
            LazyValidator.validate(v -> {
                v.notEmpty(atrBussSyncQtcVo.getEntityId(), "entityId");
                v.notEmpty(atrBussSyncQtcVo.getYearMonth(), "yearMonth");
            });
            AtrBussEcfSyncQtcService service = SpringContextUtil.getBean(AtrBussEcfSyncQtcService.class);
            service.entry(atrBussSyncQtcVo.getEntityId(), atrBussSyncQtcVo.getYearMonth());
            return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "ok");
        } catch (Exception e) {
            log.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, e.getMessage());
        }
    }

}
