CREATE OR REPLACE PACKAGE dm_pack_buss_accountperiod IS


  FUNCTION func_get_accountperiod_currency(p_entity_id  NUMBER)return varchar2;


END dm_pack_buss_accountperiod;
/
CREATE OR REPLACE PACKAGE BODY dm_pack_buss_accountperiod IS


  /**
  ** name:func_get_accountperiod_currency
  ** param: p_entity_id
  ** author: lgx
  ** time: 2023-05-15
  ** describe: 根据业务期间，查找会计平台的会计期间，BookI17账套的币别
  **/
  FUNCTION func_get_accountperiod_currency(p_entity_id  NUMBER) return varchar2 IS
    v_num number;
    v_currency varchar2(50);
    v_error_msg VARCHAR2(2000);
  BEGIN

  SELECT count(1) into v_num
    FROM accuser.acc_conf_accountperiod t
    where t.entity_id = p_entity_id
    and t.book_code = 'BookI17';

  if v_num > 0 then

    SELECT max(currency_code) into v_currency
      FROM accuser.acc_conf_accountperiod t
      where t.entity_id = p_entity_id
        and t.book_code = 'BookI17';

    return v_currency;

  end if;

  return null;

  EXCEPTION
    WHEN OTHERS THEN
      --意外处理
      v_error_msg := '[EXCEPTION][查找会计平台的会计期间]查找当前业务期间:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);
  END func_get_accountperiod_currency;


END dm_pack_buss_accountperiod;
/
