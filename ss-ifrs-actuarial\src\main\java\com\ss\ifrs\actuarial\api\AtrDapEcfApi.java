package com.ss.ifrs.actuarial.api;

import com.ss.ifrs.actuarial.dao.AtrBussDapDao;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrDapEcfVo;
import com.ss.ifrs.actuarial.service.impl.AtrBussDapService;
import com.ss.platform.core.annotation.LogScheduleTask;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping( "/dap_ecf")
@Slf4j
public class AtrDapEcfApi extends BaseApi {

    @Resource
    private AtrBussDapService atrBussDapService;

    @Resource
    private AtrBussDapDao atrBussDapDao;

    @ApiOperation(value = "预处理")
    @PermissionRequest(required = false)
    @LogScheduleTask(bizCode = "EXT_DAP_FETCH_DATA", argsValue = {"#atrDapEcfVo.entityId",
            "#atrDapEcfVo.yearMonth"})
    @RequestMapping(value = "/fetch_all", method = RequestMethod.POST)
    public BaseResponse<?> fetchAll(HttpServletRequest request, @RequestBody AtrDapEcfVo atrDapEcfVo) {
        atrDapEcfVo.setUserId(this.loginUserId(request));
        try {
            atrBussDapService.checkIfReady(atrDapEcfVo.getEntityId(), atrDapEcfVo.getYearMonth());
            return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            log.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, e.getMessage());
        }
    }

    @ApiOperation("清接口数据")
    @RequestMapping(value = "/clearDapData/{table}/{yearMonth}")
    public BaseResponse<?> clearDapData(@PathVariable String table, @PathVariable String yearMonth) {
        if (!StringUtils.startsWithIgnoreCase(table, "atr_dap_")) {
            throw new IllegalArgumentException("Unsupported table: " + table);
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("table", table);
        paramMap.put("yearMonth", yearMonth);
        try {
            atrBussDapDao.truncateDapData(paramMap);
        } catch (Exception e) {
            log.warn("trucate data error for table: " + table + ", yearMonth: " + yearMonth, e);
            atrBussDapDao.deleteDapData(paramMap);
        }
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "ok");
    }

}
