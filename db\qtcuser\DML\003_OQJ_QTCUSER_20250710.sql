TRUNCATE TABLE qtc_conf_node_sql_ref ;

INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_A01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_B01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_B02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_C01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_C02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_C91', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_C92', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_C93', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_C94', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_C95', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_C96', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_D01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_D02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_D03', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_E01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_E02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_E03', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_E05', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_E06', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_E07', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_E09', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_F01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_F02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_F03', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_F04', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_F05', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_G01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_G99', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Y01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Y02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Y03', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Y04', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Y05', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Y06', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Y07', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Y08', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Y91', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Y92', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Y93', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Y94', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Y95', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Y96', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Z01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Z02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Z03', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Z04', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Z05', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Z06', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Z07', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_A01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_B01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_B02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_C01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_C02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_C91', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_C92', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_C93', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_C94', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_C95', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_C96', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_D01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_D02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_D03', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_E01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_E02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_E03', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_E05', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_E06', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_E07', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_E09', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_F01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_F02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_F03', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_F04', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_F05', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Y01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Y02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Y03', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Y04', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Y05', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Y06', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Y07', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Y08', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Y91', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Y92', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Y93', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Y94', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Y95', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Y96', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Z01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Z02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Z03', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Z04', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Z05', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Z06', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Z07', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_Y06', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_C01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_C02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_C03', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_C04', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_C05', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_C06', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_E01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_E02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_E03', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_E04', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_E05', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_F01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_F02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_Y05', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_F03', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_F04', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_F05', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_Y01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_Y02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_Y03', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_Y04', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_Y07', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_Y08', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_Y09', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_Y10', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_Z01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_Z02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_Z03', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_Z04', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_Z05', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LI_Z06', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_SR_END', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_Y06', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_C01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_C02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_C03', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_C04', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_C05', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_C06', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_E01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_E02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_E03', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_E04', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_E05', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_F01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_F02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_Y05', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_F03', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_F04', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_F05', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_Y01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_Y02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_Y03', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_Y04', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_Y07', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_Y08', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_Y09', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_Y10', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_Z01', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_Z02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_Z03', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_Z04', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_Z05', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LI_Z06', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_TC_01', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_TC_02', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_TC_03', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_TC_04', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_TC_05', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_TC_06', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_TC_07', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_TC_01', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_TC_02', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_TC_03', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_TC_04', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_TC_05', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_TC_06', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_TC_07', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_DL_01', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_DL_02', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_DL_01', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_DL_02', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_TC_08', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_TC_08', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Y97', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Y98', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Y97', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Y98', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_G02', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_G03', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_G04', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_G99', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_TR_A01', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_TR_C01', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_TR_C02', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_TR_E01', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_TR_E02', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_TR_E05', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_TR_E06', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_TR_Y01', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_TR_Y02', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_TR_Z01', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_TR_A01', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_TR_C01', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_TR_C02', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_TR_E03', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_TR_E04', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_TR_E05', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_TR_E06', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_TR_Y01', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_TR_Y02', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_TR_Z01', 'allocCalc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_C97', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_C97', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Y09', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Y09', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Y99', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Y99', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_Z08', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_Z08', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_LR_E10', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_LR_E10', 'calc');

COMMIT ;


TRUNCATE TABLE qtc_conf_calc_sql ;

INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_B02', 3003, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RB2\',
       T1.EVL_MAIN_ID,
       T2.ICG_NO ,
       T2.IR_BASE
FROM QTC_BUSS_CALC_CFG t1 ,
     QTC_DAP_ECF_WA_IR t2
WHERE T1.EVL_MAIN_ID = #evl_main_id#
AND T2.YEAR_MONTH = T1.YEAR_MONTH
AND T2.ENTITY_ID = T1.ENTITY_ID
AND T2.BUSS_MODEL = T1.BUSS_MODEL
and t2.LOA_CODE = t1.loa_code
AND T2.DEV_PERIOD = 0');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_C02', 3005, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12, num13, num14, num15, num16)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'), \'RC2\', TTT.*
from (select T1.EVL_MAIN_ID,
             T2.ICG_NO,
             SUM(CASE WHEN TYPE_NO = \'OA\' THEN NUM5 END)  A1,
             SUM(CASE WHEN TYPE_NO = \'OA\' THEN NUM8 END)  A2,
             SUM(CASE WHEN TYPE_NO = \'OA\' THEN NUM12 END) A3,
             SUM(CASE WHEN TYPE_NO = \'OB\' THEN NUM5 END)  B1,
             SUM(CASE WHEN TYPE_NO = \'OB\' THEN NUM8 END)  B2,
             SUM(CASE WHEN TYPE_NO = \'OB\' THEN NUM12 END) B3,
             SUM(CASE WHEN TYPE_NO = \'OC\' THEN NUM5 END)  C1,
             SUM(CASE WHEN TYPE_NO = \'OC\' THEN NUM8 END)  C2,
             SUM(CASE WHEN TYPE_NO = \'OC\' THEN NUM12 END) C3,
             SUM(CASE WHEN TYPE_NO = \'OD\' THEN NUM5 END)  D1,
             SUM(CASE WHEN TYPE_NO = \'OD\' THEN NUM8 END)  D2,
             SUM(CASE WHEN TYPE_NO = \'OD\' THEN NUM12 END) D3,
             SUM(CASE WHEN TYPE_NO = \'OF\' THEN NUM5 END)  F1,
             SUM(CASE WHEN TYPE_NO = \'OF\' THEN NUM8 END)  F2,
             SUM(CASE WHEN TYPE_NO = \'OF\' THEN NUM12 END) F3,
             SUM(CASE WHEN TYPE_NO = \'OE\' THEN NUM3 END)  E3
      FROM QTC_BUSS_CALC_CFG T1,
           QTC_BUSS_PAA_CAL_ICG T2
      WHERE T1.EVL_MAIN_ID = #evl_main_id#
        AND T2.EVL_MAIN_ID = T1.EVL_L_MAIN_ID
        AND T2.TYPE_NO IN (\'OA\', \'OB\', \'OC\', \'OD\', \'OF\', \'OE\')
      GROUP BY T1.EVL_MAIN_ID, T2.ICG_NO) TTT');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_C91', 3006, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'C91\',
       t1.evl_main_id,
       t2.icg_no,
       num1*rpt_is,
       num2*(1-rpt_is),
       num3*(1-rpt_is),
       num4*(1-rpt_is),
       num5*rpt_is,
       num6*rpt_is,
       num7*(1-rpt_is),
       num8*rpt_is,
       num9*rpt_is,
       num10*(1-rpt_is),
       num11*(1-rpt_is),
       num12*rpt_is
from QTC_BUSS_CALC_CFG T1,
     QTC_BUSS_PAA_CAL_ICG T2
WHERE T1.EVL_MAIN_ID = #evl_main_id#
  AND T2.EVL_MAIN_ID = T1.EVL_L_MAIN_ID
  AND T2.TYPE_NO = \'OA\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_C92', 3007, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'C92\',
       t1.evl_main_id,
       t2.icg_no,
       num1*rpt_is,
       num2*(1-rpt_is),
       num3*(1-rpt_is),
       num4*(1-rpt_is),
       num5*rpt_is,
       num6*rpt_is,
       num7*(1-rpt_is),
       num8*rpt_is,
       num9*rpt_is,
       num10*(1-rpt_is),
       num11*(1-rpt_is),
       num12*rpt_is
from QTC_BUSS_CALC_CFG T1,
     QTC_BUSS_PAA_CAL_ICG T2
WHERE T1.EVL_MAIN_ID = #evl_main_id#
  AND T2.EVL_MAIN_ID = T1.EVL_L_MAIN_ID
  AND T2.TYPE_NO = \'OB\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_C93', 3008, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'C93\',
       t1.evl_main_id,
       t2.icg_no,
       num1*rpt_is,
       num2*(1-rpt_is),
       num3*(1-rpt_is),
       num4*(1-rpt_is),
       num5*rpt_is,
       num6*rpt_is,
       num7*(1-rpt_is),
       num8*rpt_is,
       num9*rpt_is,
       num10*(1-rpt_is),
       num11*(1-rpt_is),
       num12*rpt_is
from QTC_BUSS_CALC_CFG T1,
     QTC_BUSS_PAA_CAL_ICG T2
WHERE T1.EVL_MAIN_ID = #evl_main_id#
  AND T2.EVL_MAIN_ID = T1.EVL_L_MAIN_ID
  AND T2.TYPE_NO = \'OC\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_C01', 3004, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5)
select nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RC1\',
       evl_main_id,
       icg_no,
       v1*cf_dire,
       v2*cf_dire*-1,
       case when ver_type = \'O\' then v3*cf_dire*-1 else v4*cf_dire*-1 end,
       v5*cf_dire*-1,
       v6*cf_dire*-1
from
(SELECT
       T1.EVL_MAIN_ID,
       T2.ICG_NO,
       t1.cf_dire,
       T1.ver_type,
       sum(T2.PREMIUM) v1,
       sum(T2.IACF_FEE) v2,
       sum(t2.IACF_FEE_NON) v3,
       sum(t2.iacf_fee_non_in) v4,
       sum(T2.NET_CHARGE) v5,
       sum(t2.dec_fee) v6
FROM QTC_BUSS_CALC_CFG T1,
     qtc_dap_ecf_icg_cf T2
WHERE T1.EVL_MAIN_ID = #evl_main_id#
  AND T2.ENTITY_ID = T1.ENTITY_ID
  AND T2.CURRENCY_CODE = T1.CURRENCY_CODE
  AND T2.YEAR_MONTH = T1.YM_LAST
  AND t2.BUSS_MODEL = t1.BUSS_MODEL
  and t2.LOA_CODE = t1.loa_code
  and t2.dev_period > 0
group by T1.EVL_MAIN_ID,T2.ICG_NO,t1.cf_dire,t1.ver_type) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_C94', 3009, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'C94\',
       t1.evl_main_id,
       t2.icg_no,
       num1*rpt_is,
       num2*(1-rpt_is),
       num3*(1-rpt_is),
       num4*(1-rpt_is),
       num5*rpt_is,
       num6*rpt_is,
       num7*(1-rpt_is),
       num8*rpt_is,
       num9*rpt_is,
       num10*(1-rpt_is),
       num11*(1-rpt_is),
       num12*rpt_is
from QTC_BUSS_CALC_CFG T1,
     QTC_BUSS_PAA_CAL_ICG T2
WHERE T1.EVL_MAIN_ID = #evl_main_id#
  AND T2.EVL_MAIN_ID = T1.EVL_L_MAIN_ID
  AND T2.TYPE_NO = \'OD\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_C95', 3010, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'C95\',
       t1.evl_main_id,
       t2.icg_no,
       num1*rpt_is,
       num2*(1-rpt_is),
       num3*(1-rpt_is),
       num4*(1-rpt_is),
       num5*rpt_is,
       num6*rpt_is,
       num7*(1-rpt_is),
       num8*rpt_is,
       num9*rpt_is,
       num10*(1-rpt_is),
       num11*(1-rpt_is),
       num12*rpt_is
from QTC_BUSS_CALC_CFG T1,
     QTC_BUSS_PAA_CAL_ICG T2
WHERE T1.EVL_MAIN_ID = #evl_main_id#
  AND T2.EVL_MAIN_ID = T1.EVL_L_MAIN_ID
  AND T2.TYPE_NO = \'OF\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_C96', 3011, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'C96\',
       t1.evl_main_id,
       t2.icg_no,
       num1*rpt_is,
       num2*(1-rpt_is),
       num3*rpt_is
from QTC_BUSS_CALC_CFG T1,
     QTC_BUSS_PAA_CAL_ICG T2
WHERE T1.EVL_MAIN_ID = #evl_main_id#
  AND T2.EVL_MAIN_ID = T1.EVL_L_MAIN_ID
  AND T2.TYPE_NO = \'OE\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_D01', 3012, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, DEV_PERIOD,
                                 num1, num2, num3, num4, num5, num6, num7, num8,
                                 num9)
select nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RD1\',
       t1.EVL_MAIN_ID,
       T3.DEV_PERIOD,
       t3.ir_rate1*(1-t2.num1) + t3.ir_rate3*t2.num1,
       t3.ir_rate1*(1-t2.num2) + t3.ir_rate3*t2.num2,
       t3.ir_rate1*(1-t2.num3) + t3.ir_rate3*t2.num3,
       t3.ir_rate1*(1-t2.num4) + t3.ir_rate3*t2.num4,
       t3.ir_rate1*(1-t2.num5) + t3.ir_rate3*t2.num5,
       t3.ir_rate1*(1-t2.num6) + t3.ir_rate3*t2.num6,
       t3.ir_rate1*(1-t2.num7) + t3.ir_rate3*t2.num7,
       t3.ir_rate1*(1-t2.num8) + t3.ir_rate3*t2.num8,
       t3.ir_rate1*(1-t2.num9) + t3.ir_rate3*t2.num9
from QTC_BUSS_CALC_CFG t1,
     QTC_BUSS_EVL_PAA_PRE t2,
     QTC_DAP_ECF_FR_IR t3
where t1.EVL_MAIN_ID = #evl_main_id#
  and t2.evl_main_id = t1.evl_main_id
  and t2.TYPE_NO = \'RB1\'
  and t3.ENTITY_ID = t1.ENTITY_ID
  and t3.CURRENCY_CODE = t1.CURRENCY_CODE
  and t3.YEAR_MONTH = t1.YEAR_MONTH');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_D02', 3013, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, DEV_PERIOD,
                                 num1, num2, num3, num4, num5, num6, num7, num8, num9)
select nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RD2\',
       t1.EVL_MAIN_ID,
       t2.ICG_NO,
       t2.DEV_PERIOD,
       t2.PREMIUM * t3.num1*t1.cf_dire,
       t2.IACF_FEE * t3.num2*t1.cf_dire*-1,
       case when t1.ver_type = \'O\' then t2.IACF_FEE_NON * t3.num3*t1.cf_dire*-1 else t2.iacf_fee_non_in*t3.num3*t1.cf_dire*-1 end ,
       t2.NET_CHARGE * t3.num4*t1.cf_dire*-1,
       t2.srd_fee * t3.num5*t1.cf_dire*-1,
       t2.MAINTENANCE * t3.num6*t1.cf_dire*-1,
       t2.LOSS_FEE * t3.num7*t1.cf_dire*-1,
       t2.RA_FEE * t3.num8*t1.cf_dire*-1,
       t2.dec_fee * t3.num9*t1.cf_dire*-1
from QTC_BUSS_CALC_CFG t1,
     QTC_DAP_ECF_ICG_CF t2,
     QTC_BUSS_EVL_PAA_PRE t3
where t1.EVL_MAIN_ID = #evl_main_id#
  and t2.ENTITY_ID = t1.ENTITY_ID
  and t2.CURRENCY_CODE = t1.CURRENCY_CODE
  and t2.YEAR_MONTH = t1.YEAR_MONTH
  and t2.BUSS_MODEL = t1.BUSS_MODEL
  and t2.LOA_CODE = t1.loa_code
  and t3.evl_main_id = t1.evl_main_id
  and t3.type_no = \'RD1\'
  and t3.DEV_PERIOD = t2.DEV_PERIOD');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_D03', 3014, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO,
                                 num1, num2, num3, num4, num5, num6, num7, num8, num9)
select nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'), \'RD3\', ttt.*
from (select EVL_MAIN_ID,
             ICG_NO,
             sum(num1),
             sum(num2),
             sum(num3),
             sum(num4),
             sum(num5),
             sum(num6),
             sum(num7),
             sum(num8),
             sum(num9)
      from QTC_BUSS_EVL_PAA_PRE
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO = \'RD2\'
        and DEV_PERIOD > 0
      group by EVL_MAIN_ID,
               ICG_NO) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_E02', 3016, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RE2\',
       T1.EVL_MAIN_ID,
       T2.ICG_NO,
       T2.RECV_PREMIUM*cf_dire*-1,
       T2.IACF_FEE*cf_dire,
       case when T1.ver_type = \'O\' then t2.IACF_FEE_NON*cf_dire else t2.iacf_fee_non_in*cf_dire end,
       T2.NET_CHARGE*cf_dire,
       T2.dec_fee*cf_dire
FROM QTC_BUSS_CALC_CFG T1,
     QTC_DAP_ECF_ICG_CF T2
WHERE T1.EVL_MAIN_ID = #evl_main_id#
  AND T2.ENTITY_ID = T1.ENTITY_ID
  AND T2.CURRENCY_CODE = T1.CURRENCY_CODE
  AND T2.YEAR_MONTH = T1.YEAR_MONTH
  AND t2.BUSS_MODEL = t1.BUSS_MODEL
  and t2.LOA_CODE = t1.loa_code
  AND T2.DEV_PERIOD = 0');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_E03', 3017, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RE3\',
       TTT.EVL_MAIN_ID,
       TTT.ICG_NO,
       (coalesce(TTT.C1, 0) - coalesce(TTT.P1, 0))*-1,
       (coalesce(TTT.C2, 0) - coalesce(TTT.P2, 0))*-1,
       (coalesce(TTT.C3, 0) - coalesce(TTT.P3, 0))*-1,
       (coalesce(TTT.C4, 0) - coalesce(TTT.P4, 0))*-1,
       (coalesce(TTT.C5, 0) - coalesce(TTT.P5, 0))*-1
from (select EVL_MAIN_ID,
             ICG_NO,
             SUM(CASE WHEN TYPE_NO = \'RE1\' THEN NUM1 END) C1,
             SUM(CASE WHEN TYPE_NO = \'RE1\' THEN NUM2 END) C2,
             SUM(CASE WHEN TYPE_NO = \'RE1\' THEN NUM3 END) C3,
             SUM(CASE WHEN TYPE_NO = \'RE1\' THEN NUM4 END) C4,
             SUM(CASE WHEN TYPE_NO = \'RE1\' THEN NUM5 END) C5,
             SUM(CASE WHEN TYPE_NO = \'RC1\' THEN NUM1 END) P1,
             SUM(CASE WHEN TYPE_NO = \'RC1\' THEN NUM2 END) P2,
             SUM(CASE WHEN TYPE_NO = \'RC1\' THEN NUM3 END) P3,
             SUM(CASE WHEN TYPE_NO = \'RC1\' THEN NUM4 END) P4,
             SUM(CASE WHEN TYPE_NO = \'RC1\' THEN NUM5 END) P5
      from QTC_BUSS_EVL_PAA_PRE
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO IN (\'RE1\', \'RC1\')
      GROUP BY EVL_MAIN_ID,
               ICG_NO) TTT');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_E05', 3018, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RE5\',
       EVL_MAIN_ID,
       ICG_NO,
       case when s1 = 0 then 1 else v1 / s1 end ,
       case when s2 = 0 then 1 else v2 / s2 end ,
       case when s3 = 0 then 1 else v3 / s3 end ,
       case when s4 = 0 then 1 else v4 / s4 end
FROM (SELECT t1.evl_main_id,
             t2.icg_no,
             sum(case when t2.dev_period = 0 then t2.ed_premium end)                              v1,
             sum(t2.ed_premium)                                                                   s1,
             sum(case when t2.dev_period = 0 then t2.ed_iacf_fee end)                             v2,
             sum(t2.ed_iacf_fee)                                                                  s2,
             sum(case
                     when t2.dev_period = 0 and t1.ver_type = \'O\' then t2.ed_iacf_fee_non
                     when t2.dev_period = 0 and t1.ver_type = \'I\' then t2.ed_iacf_fee_non_in end) v3,
             sum(case
                     when t1.ver_type = \'O\' then t2.ed_iacf_fee_non
                     when t1.ver_type = \'I\' then t2.ed_iacf_fee_non_in end)                       s3,
             sum(case when t2.dev_period = 0 then t2.ed_net_charge end)                           v4,
             sum(t2.ed_net_charge)                                                                s4
FROM QTC_BUSS_CALC_CFG T1,
     qtc_dap_ecf_icg_cf T2
WHERE T1.EVL_MAIN_ID = #evl_main_id#
  AND T2.ENTITY_ID = T1.ENTITY_ID
  AND T2.CURRENCY_CODE = T1.CURRENCY_CODE
  AND T2.YEAR_MONTH = T1.YEAR_MONTH
  AND t2.BUSS_MODEL = t1.BUSS_MODEL
  and t2.loa_code = t1.loa_code
group by t1.evl_main_id,
         t2.icg_no) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_E06', 3019, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RE6\',
       T1.EVL_MAIN_ID,
       T2.ICG_NO,
       T2.PREMIUM*t1.cf_dire*-1,
       T2.IACF_FEE*t1.cf_dire,
       case when T1.ver_type = \'O\' then t2.IACF_FEE_NON*t1.cf_dire else t2.iacf_fee_non_in*t1.cf_dire end,
       T2.NET_CHARGE*t1.cf_dire,
       T2.dec_fee*t1.cf_dire
FROM QTC_BUSS_CALC_CFG T1,
     qtc_dap_ecf_icg_cf T2
WHERE T1.EVL_MAIN_ID = #evl_main_id#
  AND T2.ENTITY_ID = T1.ENTITY_ID
  AND T2.CURRENCY_CODE = T1.CURRENCY_CODE
  AND T2.YEAR_MONTH = T1.ym_last
  AND t2.BUSS_MODEL = t1.BUSS_MODEL
  and t2.loa_code = t1.loa_code
  and t2.dev_period = 0');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_E07', 3020, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RE7\',
       T1.EVL_MAIN_ID,
       T2.ICG_NO,
       T2.num1,
       T2.num2,
       T2.num3,
       T2.num4,
       T2.num5
FROM QTC_BUSS_CALC_CFG T1,
     QTC_BUSS_PAA_CAL_ICG T2
WHERE T1.EVL_MAIN_ID = #evl_main_id#
  AND T2.evl_main_id = T1.evl_l_main_id
  AND T2.type_no = \'TT\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_E09', 3022, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RE9\',
       TTT.EVL_MAIN_ID,
       TTT.ICG_NO,
       (coalesce(TTT.P1, 0) + coalesce(TTT.P2, 0) + coalesce(TTT.C1, 0)) * R1,
       (coalesce(TTT.P3, 0) + coalesce(TTT.P4, 0) + coalesce(TTT.C2, 0)) * R1,
       (coalesce(TTT.P5, 0) + coalesce(TTT.P6, 0) + coalesce(TTT.C3, 0)) * R1,
       (coalesce(TTT.P7, 0) + coalesce(TTT.P8, 0) + coalesce(TTT.C4, 0)) * R1,
       (coalesce(TTT.P9, 0) + coalesce(TTT.P10, 0) + coalesce(TTT.C5, 0)) * R1
from (select EVL_MAIN_ID,
             ICG_NO,
             SUM(CASE WHEN TYPE_NO = \'RB2\' THEN NUM1 END)  R1,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM2 END)  P1,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM3 END)  P2,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM5 END)  P3,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM6 END)  P4,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM8 END)  P5,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM9 END) P6,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM11 END) P7,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM12 END) P8,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM14 END) P9,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM15 END) P10,
             SUM(CASE WHEN TYPE_NO = \'RE2\' THEN NUM1 END)  C1,
             SUM(CASE WHEN TYPE_NO = \'RE2\' THEN NUM2 END)  C2,
             SUM(CASE WHEN TYPE_NO = \'RE2\' THEN NUM3 END)  C3,
             SUM(CASE WHEN TYPE_NO = \'RE2\' THEN NUM4 END)  C4,
             SUM(CASE WHEN TYPE_NO = \'RE2\' THEN NUM5 END)  C5
      from QTC_BUSS_EVL_PAA_PRE
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO IN (\'RB2\', \'RE2\', \'RC2\')
      GROUP BY EVL_MAIN_ID,
               ICG_NO) TTT');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_F01', 3023, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO,
                                 num1, num2, num3, num4, num5, num6, num7, num8, num9, num10,
                                 num11, num12)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RF1\',
       EVL_MAIN_ID,
       ICG_NO,
       P1,
       C1,
       D1,
       (coalesce(P1,0)+coalesce(C1,0)+coalesce(D1,0))*coalesce(TTT.R1, 0)*-1,
       (coalesce(P1,0)+coalesce(C1,0)+coalesce(D1,0)) * (1-coalesce(TTT.R1, 0)),
       P2,
       S1,
       (coalesce(P2, 0)+coalesce(S1, 0)+(coalesce(P1,0)+coalesce(C1,0)+coalesce(D1,0))*coalesce(TTT.R1, 0)*-1),
       P3,
       C2,
       (coalesce(P3, 0) + coalesce(C2, 0)) * coalesce(TTT.R1, 0) * -1,
       (coalesce(P3, 0) + coalesce(C2, 0)) * (1 - coalesce(TTT.R1, 0))
from (select EVL_MAIN_ID,
             ICG_NO,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM1 END) P1,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM2 END) P2,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM3 END) P3,
             SUM(CASE WHEN TYPE_NO = \'RE3\' THEN NUM1 END) C1,
             SUM(CASE WHEN TYPE_NO = \'RE9\' THEN NUM1 END) C2,
             SUM(CASE WHEN TYPE_NO = \'RE8\' THEN NUM1 END) D1,
             SUM(CASE WHEN TYPE_NO = \'RE2\' THEN NUM1 END) S1,
             SUM(CASE WHEN TYPE_NO = \'RE5\' THEN NUM1 END) R1
      from QTC_BUSS_EVL_PAA_PRE
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO IN (\'RC2\', \'RE3\', \'RE9\', \'RE8\', \'RE2\', \'RE5\')
      GROUP BY EVL_MAIN_ID,
               ICG_NO) TTT');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_F02', 3024, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO,
                                 num1, num2, num3, num4, num5, num6, num7, num8, num9, num10,
                                 num11, num12)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RF2\',
       EVL_MAIN_ID,
       ICG_NO,
       P1,
       C1,
       D1,
       (coalesce(P1,0)+coalesce(C1,0)+coalesce(D1,0))*coalesce(TTT.R1, 0)*-1,
       (coalesce(P1,0)+coalesce(C1,0)+coalesce(D1,0)) * (1 - coalesce(TTT.R1, 0)),
       P2,
       S1,
       (coalesce(P2, 0)+coalesce(S1, 0)+(coalesce(P1,0)+coalesce(C1,0)+coalesce(D1,0))*coalesce(TTT.R1, 0)*-1),
       P3,
       C2,
       (coalesce(P3, 0) + coalesce(C2, 0)) * coalesce(TTT.R1, 0) * -1,
       (coalesce(P3, 0) + coalesce(C2, 0)) * (1 - coalesce(TTT.R1, 0))
from (select EVL_MAIN_ID,
             ICG_NO,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM4 END) P1,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM5 END) P2,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM6 END) P3,
             SUM(CASE WHEN TYPE_NO = \'RE3\' THEN NUM2 END) C1,
             SUM(CASE WHEN TYPE_NO = \'RE9\' THEN NUM2 END) C2,
             SUM(CASE WHEN TYPE_NO = \'RE8\' THEN NUM2 END) D1,
             SUM(CASE WHEN TYPE_NO = \'RE2\' THEN NUM2 END) S1,
             SUM(CASE WHEN TYPE_NO = \'RE5\' THEN NUM2 END) R1
      from QTC_BUSS_EVL_PAA_PRE
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO IN (\'RC2\', \'RE3\', \'RE9\', \'RE8\', \'RE2\', \'RE5\')
      GROUP BY EVL_MAIN_ID,
               ICG_NO) TTT');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_F03', 3025, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO,
                                 num1, num2, num3, num4, num5, num6, num7, num8, num9, num10,
                                 num11, num12)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RF3\',
       EVL_MAIN_ID,
       ICG_NO,
       P1,
       C1,
       D1,
       (coalesce(P1,0)+coalesce(C1,0)+coalesce(D1,0))*coalesce(TTT.R1, 0)*-1,
       (coalesce(P1,0)+coalesce(C1,0)+coalesce(D1,0)) * (1 - coalesce(TTT.R1, 0)),
       P2,
       S1,
       (coalesce(P2, 0)+coalesce(S1, 0)+(coalesce(P1,0)+coalesce(C1,0)+coalesce(D1,0))*coalesce(TTT.R1, 0)*-1),
       P3,
       C2,
       (coalesce(P3, 0)+coalesce(C2, 0))*coalesce(TTT.R1, 0)*-1,
       (coalesce(P3, 0)+coalesce(C2, 0))*(1-coalesce(TTT.R1, 0))
from (select EVL_MAIN_ID,
             ICG_NO,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM7 END) P1,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM8 END) P2,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM9 END) P3,
             SUM(CASE WHEN TYPE_NO = \'RE3\' THEN NUM3 END) C1,
             SUM(CASE WHEN TYPE_NO = \'RE9\' THEN NUM3 END) C2,
             SUM(CASE WHEN TYPE_NO = \'RE8\' THEN NUM3 END) D1,
             SUM(CASE WHEN TYPE_NO = \'RE2\' THEN NUM3 END) S1,
             SUM(CASE WHEN TYPE_NO = \'RE5\' THEN NUM3 END) R1
      from QTC_BUSS_EVL_PAA_PRE
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO IN (\'RC2\', \'RE3\', \'RE9\', \'RE8\', \'RE2\', \'RE5\')
      GROUP BY EVL_MAIN_ID,
               ICG_NO) TTT');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_F04', 3026, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO,
                                 num1, num2, num3, num4, num5, num6, num7, num8, num9, num10,
                                 num11, num12)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RF4\',
       EVL_MAIN_ID,
       ICG_NO,
       P1,
       C1,
       D1,
       (coalesce(P1,0)+coalesce(C1,0)+coalesce(D1,0))*coalesce(TTT.R1, 0)*-1,
       (coalesce(P1,0)+coalesce(C1,0)+coalesce(D1,0)) * (1 - coalesce(TTT.R1, 0)),
       P2,
       S1,
       (coalesce(P2, 0)+coalesce(S1, 0)+(coalesce(P1,0)+coalesce(C1,0)+coalesce(D1,0))*coalesce(TTT.R1, 0)*-1),
       P3,
       C2,
       (coalesce(P3, 0) + coalesce(C2, 0)) * coalesce(TTT.R1, 0) * -1,
       (coalesce(P3, 0) + coalesce(C2, 0)) * (1 - coalesce(TTT.R1, 0))
from (select EVL_MAIN_ID,
             ICG_NO,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM10 END) P1,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM11 END) P2,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM12 END) P3,
             SUM(CASE WHEN TYPE_NO = \'RE3\' THEN NUM4 END) C1,
             SUM(CASE WHEN TYPE_NO = \'RE9\' THEN NUM4 END) C2,
             SUM(CASE WHEN TYPE_NO = \'RE8\' THEN NUM4 END) D1,
             SUM(CASE WHEN TYPE_NO = \'RE2\' THEN NUM4 END) S1,
             SUM(CASE WHEN TYPE_NO = \'RE5\' THEN NUM4 END) R1
      from QTC_BUSS_EVL_PAA_PRE
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO IN (\'RC2\', \'RE3\', \'RE9\', \'RE8\', \'RE2\', \'RE5\')
      GROUP BY EVL_MAIN_ID,
               ICG_NO) TTT');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_F05', 3027, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO,
                                 num1, num2, num3, num4, num5, num6, num7, num8, num9, num10,
                                 num11, num12)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RF5\',
       EVL_MAIN_ID,
       ICG_NO,
       P1,
       C1,
       D1,
       (coalesce(P1,0)+coalesce(C1,0)+coalesce(D1,0))*coalesce(TTT.R1, 0)*-1,
       (coalesce(P1,0)+coalesce(C1,0)+coalesce(D1,0)) * (1 - coalesce(TTT.R1, 0)),
       P2,
       S1,
       (coalesce(P2, 0)+coalesce(S1, 0)+(coalesce(P1,0)+coalesce(C1,0)+coalesce(D1,0))*coalesce(TTT.R1, 0)*-1),
       P3,
       C2,
       (coalesce(P3, 0)+coalesce(C2, 0))*coalesce(TTT.R1, 0) * -1,
       (coalesce(P3, 0)+coalesce(C2, 0))*(1-coalesce(TTT.R1, 0))
from (select EVL_MAIN_ID,
             ICG_NO,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM13 END) P1,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM14 END) P2,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM15 END) P3,
             SUM(CASE WHEN TYPE_NO = \'RE3\' THEN NUM5 END) C1,
             SUM(CASE WHEN TYPE_NO = \'RE9\' THEN NUM5 END) C2,
             SUM(CASE WHEN TYPE_NO = \'RE8\' THEN NUM5 END) D1,
             SUM(CASE WHEN TYPE_NO = \'RE2\' THEN NUM5 END) S1,
             SUM(CASE WHEN TYPE_NO = \'RE5\' THEN NUM1 END) R1
      from QTC_BUSS_EVL_PAA_PRE
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO IN (\'RC2\', \'RE3\', \'RE9\', \'RE8\', \'RE2\', \'RE5\')
      GROUP BY EVL_MAIN_ID,
               ICG_NO) TTT');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Y92', 3110, e'insert into QTC_BUSS_PAA_CAL_ICG(CAL_ICG_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
select nextval(\'QTC_SEQ_BUSS_PAA_CAL_ICG\'),
       \'OB\',
       ttt.*
FROM (SELECT EVL_MAIN_ID,
             ICG_NO,
             SUM(case when type_no = \'C92\' then NUM5 end),
             SUM(NUM2),
             SUM(NUM3),
             SUM(NUM4),
             SUM(NUM5),
             SUM(case when type_no = \'C92\' then NUM8 end),
             SUM(NUM7),
             SUM(NUM8),
             SUM(case when type_no = \'C92\' then NUM12 end),
             SUM(NUM10),
             SUM(NUM11),
             SUM(NUM12)
      from QTC_BUSS_EVL_PAA_PRE
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO in (\'RF2\', \'C92\')
      GROUP BY EVL_MAIN_ID, ICG_NO) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_G01', 3028, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, NUM2, NUM3)
select nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RG1\',
       EVL_MAIN_ID,
       ICG_NO,
       V1,
       coalesce(V2, 0) + coalesce(V3, 0) + coalesce(V4, 0) + coalesce(V5, 0) + coalesce(V6, 0),
       CASE
           WHEN coalesce(V1, 0) -
                (coalesce(V2, 0) + coalesce(V3, 0) + coalesce(V4, 0) + coalesce(V5, 0) + coalesce(V6, 0)) < 0
               THEN coalesce(V1, 0) -
                    (coalesce(V2, 0) + coalesce(V3, 0) + coalesce(V4, 0) + coalesce(V5, 0) + coalesce(V6, 0))
           ELSE 0 END
FROM (SELECT EVL_MAIN_ID,
             ICG_NO,
             SUM(case
                     when TYPE_NO = \'RD3\' then coalesce(NUM1, 0) + coalesce(NUM2, 0) + coalesce(NUM3, 0) +
                                               coalesce(NUM4, 0) +
                                               coalesce(NUM5, 0) +
                                               coalesce(NUM6, 0) + coalesce(NUM7, 0) + coalesce(NUM8, 0) +
                                               coalesce(NUM9, 0) end)                       V1,
             SUM(case when TYPE_NO = \'RF1\' then coalesce(NUM8, 0) + coalesce(NUM12, 0) end) v2,
             SUM(case when TYPE_NO = \'RF2\' then coalesce(NUM8, 0) + coalesce(NUM12, 0) end) v3,
             SUM(case when TYPE_NO = \'RF3\' then coalesce(NUM8, 0) + coalesce(NUM12, 0) end) v4,
             SUM(case when TYPE_NO = \'RF4\' then coalesce(NUM8, 0) + coalesce(NUM12, 0) end) v5,
             SUM(case when TYPE_NO = \'RF5\' then coalesce(NUM8, 0) + coalesce(NUM12, 0) end) v6
      from QTC_BUSS_EVL_PAA_PRE
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO in (\'RD3\', \'RF1\', \'RF2\', \'RF3\', \'RF4\', \'RF5\')
      GROUP BY EVL_MAIN_ID, ICG_NO) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_G99', 3029, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, NUM2, NUM3)
select nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'G99\',
       EVL_MAIN_ID,
       ICG_NO,
       V1,
       coalesce(V2, 0) - coalesce(V1, 0) ,
       V2
FROM (SELECT EVL_MAIN_ID,
             ICG_NO,
             SUM(CASE WHEN TYPE_NO = \'RC2\' THEN NUM16 END) V1,
             SUM(CASE WHEN TYPE_NO = \'RG1\' THEN NUM3 END) V2
      from QTC_BUSS_EVL_PAA_PRE
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO in (\'RG1\', \'RC2\')
      GROUP BY EVL_MAIN_ID, ICG_NO) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Y01', 3101, e'insert into QTC_BUSS_PAA_CAL_ICG(CAL_ICG_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
select nextval(\'QTC_SEQ_BUSS_PAA_CAL_ICG\'),
       \'A1\',
       EVL_MAIN_ID,
       ICG_NO,
       num1,
       num2,
       num3,
       num4,
       num5,
       num6,
       num7,
       num8,
       num9,
       num10,
       num11,
       num12
from QTC_BUSS_EVL_PAA_PRE
where EVL_MAIN_ID = #evl_main_id#
  and TYPE_NO = \'RF1\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Y02', 3102, e'insert into QTC_BUSS_PAA_CAL_ICG(CAL_ICG_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
select nextval(\'QTC_SEQ_BUSS_PAA_CAL_ICG\'),
       \'B1\',
       EVL_MAIN_ID,
       ICG_NO,
       num1,
       num2,
       num3,
       num4,
       num5,
       num6,
       num7,
       num8,
       num9,
       num10,
       num11,
       num12
from QTC_BUSS_EVL_PAA_PRE
where EVL_MAIN_ID = #evl_main_id#
  and TYPE_NO = \'RF2\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Y03', 3103, e'insert into QTC_BUSS_PAA_CAL_ICG(CAL_ICG_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
select nextval(\'QTC_SEQ_BUSS_PAA_CAL_ICG\'),
       \'C1\',
       EVL_MAIN_ID,
       ICG_NO,
       num1,
       num2,
       num3,
       num4,
       num5,
       num6,
       num7,
       num8,
       num9,
       num10,
       num11,
       num12
from QTC_BUSS_EVL_PAA_PRE
where EVL_MAIN_ID = #evl_main_id#
  and TYPE_NO = \'RF3\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Y04', 3104, e'insert into QTC_BUSS_PAA_CAL_ICG(CAL_ICG_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
select nextval(\'QTC_SEQ_BUSS_PAA_CAL_ICG\'),
       \'D1\',
       EVL_MAIN_ID,
       ICG_NO,
       num1,
       num2,
       num3,
       num4,
       num5,
       num6,
       num7,
       num8,
       num9,
       num10,
       num11,
       num12
from QTC_BUSS_EVL_PAA_PRE
where EVL_MAIN_ID = #evl_main_id#
  and TYPE_NO = \'RF4\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Y05', 3105, e'insert into QTC_BUSS_PAA_CAL_ICG(CAL_ICG_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
select nextval(\'QTC_SEQ_BUSS_PAA_CAL_ICG\'),
       \'F1\',
       EVL_MAIN_ID,
       ICG_NO,
       num1,
       num2,
       num3,
       num4,
       num5,
       num6,
       num7,
       num8,
       num9,
       num10,
       num11,
       num12
from QTC_BUSS_EVL_PAA_PRE
where EVL_MAIN_ID = #evl_main_id#
  and TYPE_NO = \'RF5\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Y06', 3106, e'insert into QTC_BUSS_PAA_CAL_ICG(CAL_ICG_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3)
select nextval(\'QTC_SEQ_BUSS_PAA_CAL_ICG\'),
       \'E1\',
       EVL_MAIN_ID,
       ICG_NO,
       num1,
       num2,
       num3
from QTC_BUSS_EVL_PAA_PRE
where EVL_MAIN_ID = #evl_main_id#
  and TYPE_NO = \'G99\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Y07', 3107, e'insert into QTC_BUSS_PAA_CAL_ICG(CAL_ICG_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9)
select nextval(\'QTC_SEQ_BUSS_PAA_CAL_ICG\'),
       \'PV\',
       EVL_MAIN_ID,
       ICG_NO,
       num1,
       num2,
       num3,
       num4,
       num5,
       num6,
       num7,
       num8,
       num9
from QTC_BUSS_EVL_PAA_PRE
where EVL_MAIN_ID = #evl_main_id#
  and TYPE_NO = \'RD3\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Y08', 3108, e'insert into QTC_BUSS_PAA_CAL_ICG(CAL_ICG_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5)
select nextval(\'QTC_SEQ_BUSS_PAA_CAL_ICG\'),
       \'TT\',
       EVL_MAIN_ID,
       ICG_NO,
       num1,
       num2,
       num3,
       num4,
       num5
from QTC_BUSS_EVL_PAA_PRE
where EVL_MAIN_ID = #evl_main_id#
  and TYPE_NO = \'RE8\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Y91', 3109, e'insert into QTC_BUSS_PAA_CAL_ICG(CAL_ICG_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
select nextval(\'QTC_SEQ_BUSS_PAA_CAL_ICG\'),
       \'OA\',
       ttt.*
FROM (SELECT EVL_MAIN_ID,
             ICG_NO,
             SUM(case when type_no = \'C91\' then NUM5 end),
             SUM(NUM2),
             SUM(NUM3),
             SUM(NUM4),
             SUM(NUM5),
             SUM(case when type_no = \'C91\' then NUM8 end),
             SUM(NUM7),
             SUM(NUM8),
             SUM(case when type_no = \'C91\' then NUM12 end),
             SUM(NUM10),
             SUM(NUM11),
             SUM(NUM12)
      from QTC_BUSS_EVL_PAA_PRE
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO in (\'RF1\', \'C91\')
      GROUP BY EVL_MAIN_ID, ICG_NO) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_Z01', 3611, e'INSERT INTO QTC_BUSS_EVALUATE_RESULT(EVALUATE_RESULT_ID, VAR1 , EVALUATE_MAIN_ID, ICG_NO )
select nextval(\'QTC_SEQ_BUSS_EVALUATE_RESULT\'), \'Lic\', TTT.*
FROM (SELECT EVL_MAIN_ID, ICG_NO
      from QTC_BUSS_LIC_CAL_ICG
      where EVL_MAIN_ID = #evl_main_id#
      GROUP BY EVL_MAIN_ID, ICG_NO) TTT');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Y93', 3111, e'insert into QTC_BUSS_PAA_CAL_ICG(CAL_ICG_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
select nextval(\'QTC_SEQ_BUSS_PAA_CAL_ICG\'),
       \'OC\',
       ttt.*
FROM (SELECT EVL_MAIN_ID,
             ICG_NO,
             SUM(case when type_no = \'C93\' then NUM5 end),
             SUM(NUM2),
             SUM(NUM3),
             SUM(NUM4),
             SUM(NUM5),
             SUM(case when type_no = \'C93\' then NUM8 end),
             SUM(NUM7),
             SUM(NUM8),
             SUM(case when type_no = \'C93\' then NUM12 end),
             SUM(NUM10),
             SUM(NUM11),
             SUM(NUM12)
      from QTC_BUSS_EVL_PAA_PRE
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO in (\'RF3\', \'C93\')
      GROUP BY EVL_MAIN_ID, ICG_NO) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Y94', 3112, e'insert into QTC_BUSS_PAA_CAL_ICG(CAL_ICG_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
select nextval(\'QTC_SEQ_BUSS_PAA_CAL_ICG\'),
       \'OD\',
       ttt.*
FROM (SELECT EVL_MAIN_ID,
             ICG_NO,
             SUM(case when type_no = \'C94\' then NUM5 end),
             SUM(NUM2),
             SUM(NUM3),
             SUM(NUM4),
             SUM(NUM5),
             SUM(case when type_no = \'C94\' then NUM8 end),
             SUM(NUM7),
             SUM(NUM8),
             SUM(case when type_no = \'C94\' then NUM12 end),
             SUM(NUM10),
             SUM(NUM11),
             SUM(NUM12)
      from QTC_BUSS_EVL_PAA_PRE
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO in (\'RF4\', \'C94\')
      GROUP BY EVL_MAIN_ID, ICG_NO) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Y95', 3113, e'insert into QTC_BUSS_PAA_CAL_ICG(CAL_ICG_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
select nextval(\'QTC_SEQ_BUSS_PAA_CAL_ICG\'),
       \'OF\',
       ttt.*
FROM (SELECT EVL_MAIN_ID,
             ICG_NO,
             SUM(case when type_no = \'C95\' then NUM5 end),
             SUM(NUM2),
             SUM(NUM3),
             SUM(NUM4),
             SUM(NUM5),
             SUM(case when type_no = \'C95\' then NUM8 end),
             SUM(NUM7),
             SUM(NUM8),
             SUM(case when type_no = \'C95\' then NUM12 end),
             SUM(NUM10),
             SUM(NUM11),
             SUM(NUM12)
      from QTC_BUSS_EVL_PAA_PRE
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO in (\'RF5\', \'C95\')
      GROUP BY EVL_MAIN_ID, ICG_NO) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Y96', 3114, e'insert into QTC_BUSS_PAA_CAL_ICG(CAL_ICG_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3)
select nextval(\'QTC_SEQ_BUSS_PAA_CAL_ICG\'),
       \'OE\',
       ttt.*
FROM (SELECT EVL_MAIN_ID,
             ICG_NO,
             SUM(case when type_no = \'C96\' then NUM3 end),
             SUM(NUM2),
             SUM(NUM3)
      from QTC_BUSS_EVL_PAA_PRE
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO in (\'G99\', \'C96\')
      GROUP BY EVL_MAIN_ID, ICG_NO) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Y97', 3115, e'update QTC_BUSS_PAA_CAL_ICG set num5 = coalesce(num1,0)+coalesce(num2,0)+coalesce(num3,0)+coalesce(num4,0) ,
                                num8 = coalesce(num6,0)+coalesce(num7,0)+coalesce(num4,0) ,
                                num12 = coalesce(num9,0)+coalesce(num10,0)+coalesce(num11,0)
where evl_main_id = #evl_main_id# and type_no in (\'OA\',\'OB\',\'OC\',\'OD\',\'OF\')');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Y98', 3115, e'update QTC_BUSS_PAA_CAL_ICG set num3 = coalesce(num1,0)+coalesce(num2,0)
where evl_main_id = #evl_main_id# and type_no in (\'OE\')');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Z01', 3201, e'INSERT INTO QTC_BUSS_EVALUATE_RESULT(EVALUATE_RESULT_ID, VAR1 , EVALUATE_MAIN_ID, ICG_NO )
select nextval(\'QTC_SEQ_BUSS_EVALUATE_RESULT\'), \'Lrc\', TTT.*
FROM (SELECT EVL_MAIN_ID, ICG_NO
      from QTC_BUSS_PAA_CAL_ICG
      where EVL_MAIN_ID = #evl_main_id#
      GROUP BY EVL_MAIN_ID, ICG_NO) TTT');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Z02', 3202, e'MERGE INTO QTC_BUSS_EVALUATE_RESULT TG
USING (SELECT EVL_MAIN_ID, ICG_NO, num6+num9 v1 , num4+num11 v2, num10 v3, num7 v4, num8+num12 v5
           FROM QTC_BUSS_PAA_CAL_ICG
           WHERE EVL_MAIN_ID = #evl_main_id#
           AND TYPE_NO = \'OA\') SC
ON(TG.VAR1 = \'Lrc\' AND TG.EVALUATE_MAIN_ID = SC.EVL_MAIN_ID AND TG.ICG_NO = SC.ICG_NO)
WHEN MATCHED THEN UPDATE SET NUM1 = SC.v1, NUM2 = SC.v2, NUM3 = SC.v3, NUM4 = SC.v4, NUM5 = SC.v5');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Z03', 3203, e'MERGE INTO QTC_BUSS_EVALUATE_RESULT TG
USING (SELECT EVL_MAIN_ID, ICG_NO, num6+num9 v1 , num4+num11 v2, num10 v3, num7 v4, num8+num12 v5
           FROM QTC_BUSS_PAA_CAL_ICG
           WHERE EVL_MAIN_ID = #evl_main_id#
           AND TYPE_NO = \'OD\') SC
ON(TG.VAR1 = \'Lrc\' AND TG.EVALUATE_MAIN_ID = SC.EVL_MAIN_ID AND TG.ICG_NO = SC.ICG_NO)
WHEN MATCHED THEN UPDATE SET NUM6 = SC.v1, NUM7 = SC.v2, NUM8 = SC.v3, NUM9 = SC.v4, NUM10 = SC.v5');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Z04', 3204, e'MERGE INTO QTC_BUSS_EVALUATE_RESULT TG
USING (SELECT EVL_MAIN_ID, ICG_NO, num6+num9 v1 , num4+num11 v2, num10 v3, num7 v4, num8+num12 v5
           FROM QTC_BUSS_PAA_CAL_ICG
           WHERE EVL_MAIN_ID = #evl_main_id#
           AND TYPE_NO = \'OB\') SC
ON(TG.VAR1 = \'Lrc\' AND TG.EVALUATE_MAIN_ID = SC.EVL_MAIN_ID AND TG.ICG_NO = SC.ICG_NO)
WHEN MATCHED THEN UPDATE SET NUM11 = SC.v1, NUM12 = SC.v2, NUM13 = SC.v3, NUM14 = SC.v4, NUM15 = SC.v5');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Z05', 3205, e'MERGE INTO QTC_BUSS_EVALUATE_RESULT TG
USING (SELECT EVL_MAIN_ID, ICG_NO, num6+num9 v1 , num4+num11 v2, num10 v3, num7 v4, num8+num12 v5
           FROM QTC_BUSS_PAA_CAL_ICG
           WHERE EVL_MAIN_ID = #evl_main_id#
           AND TYPE_NO = \'OC\') SC
ON(TG.VAR1 = \'Lrc\' AND TG.EVALUATE_MAIN_ID = SC.EVL_MAIN_ID AND TG.ICG_NO = SC.ICG_NO)
WHEN MATCHED THEN UPDATE SET NUM16 = SC.v1, NUM17 = SC.v2, NUM18 = SC.v3, NUM19 = SC.v4, NUM20 = SC.v5');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Z06', 3206, e'MERGE INTO QTC_BUSS_EVALUATE_RESULT TG
USING (SELECT EVL_MAIN_ID, ICG_NO, num6+num9 v1 , num4+num11 v2, num10 v3, num7 v4, num8+num12 v5
           FROM QTC_BUSS_PAA_CAL_ICG
           WHERE EVL_MAIN_ID = #evl_main_id#
           AND TYPE_NO = \'OF\') SC
ON(TG.VAR1 = \'Lrc\' AND TG.EVALUATE_MAIN_ID = SC.EVL_MAIN_ID AND TG.ICG_NO = SC.ICG_NO)
WHEN MATCHED THEN UPDATE SET NUM21 = SC.v1, NUM22 = SC.v2, NUM23 = SC.v3, NUM24 = SC.v4, NUM25 = SC.v5');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Z07', 3207, e'MERGE INTO QTC_BUSS_EVALUATE_RESULT TG
USING (SELECT EVL_MAIN_ID, ICG_NO, num1, num2, num3
           FROM QTC_BUSS_PAA_CAL_ICG
           WHERE EVL_MAIN_ID = #evl_main_id#
           AND TYPE_NO = \'OE\') SC
ON(TG.VAR1 = \'Lrc\' AND TG.EVALUATE_MAIN_ID = SC.EVL_MAIN_ID AND TG.ICG_NO = SC.ICG_NO)
WHEN MATCHED THEN UPDATE SET NUM26 = SC.NUM1, NUM27 = SC.NUM2, NUM28 = SC.NUM3');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_C06', 3506, e'insert into qtc_buss_evl_lic_pre(lic_pre_id, type_no, evl_main_id, ICG_NO,  num1)
SELECT nextval(\'qtc_seq_buss_evl_lic_pre\'),
       \'IC6\',
       T1.evl_main_id,
       T2.icg_no,
       T2.num3
from QTC_BUSS_CALC_CFG T1,
     QTC_BUSS_LIC_CAL_ICG T2
WHERE T1.EVL_MAIN_ID = #evl_main_id#
  AND T2.EVL_MAIN_ID = T1.EVL_L_MAIN_ID
  AND T2.TYPE_NO = \'L1\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_E01', 3507, e'insert into qtc_buss_evl_lic_pre(LIC_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
SELECT nextval(\'qtc_seq_buss_evl_lic_pre\'), \'IE1\', TTT.*
from (select t1.EVL_MAIN_ID,
             t2.ICG_NO,
             sum(case when t2.acc_time = \'P\' then t2.p_pfr end),
             sum(case when t2.acc_time = \'P\' then t2.p_pwr end),
             sum(case when t2.acc_time = \'P\' then t2.p_cfr end),
             sum(case when t2.acc_time = \'P\' then t2.p_cwr end),
             sum(case when t2.acc_time = \'P\' then t2.c_cfr end),
             sum(case when t2.acc_time = \'P\' then t2.c_cwr end),
             sum(case when t2.acc_time = \'C\' then t2.p_pfr end),
             sum(case when t2.acc_time = \'C\' then t2.p_pwr end),
             sum(case when t2.acc_time = \'C\' then t2.p_cfr end),
             sum(case when t2.acc_time = \'C\' then t2.p_cwr end),
             sum(case when t2.acc_time = \'C\' then t2.c_cfr end),
             sum(case when t2.acc_time = \'C\' then t2.c_cwr end)
      from QTC_BUSS_CALC_CFG t1,
           qtc_dap_ecf_lic_pv t2
      where t1.EVL_MAIN_ID = #evl_main_id#
        and t2.ENTITY_ID = t1.ENTITY_ID
        and t2.YEAR_MONTH = t1.YEAR_MONTH
        and t2.BUSS_MODEL = t1.BUSS_MODEL
        and t2.LOA_CODE = t1.loa_code
        and t2.cf_type = \'OS\'
      group by t1.EVL_MAIN_ID, t2.ICG_NO) TTT');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_E02', 3508, e'insert into qtc_buss_evl_lic_pre(LIC_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
SELECT nextval(\'qtc_seq_buss_evl_lic_pre\'), \'IE2\', TTT.*
from (select t1.EVL_MAIN_ID,
             t2.ICG_NO,
             sum(case when t2.acc_time = \'P\' then t2.p_pfr end),
             sum(case when t2.acc_time = \'P\' then t2.p_pwr end),
             sum(case when t2.acc_time = \'P\' then t2.p_cfr end),
             sum(case when t2.acc_time = \'P\' then t2.p_cwr end),
             sum(case when t2.acc_time = \'P\' then t2.c_cfr end),
             sum(case when t2.acc_time = \'P\' then t2.c_cwr end),
             sum(case when t2.acc_time = \'C\' then t2.p_pfr end),
             sum(case when t2.acc_time = \'C\' then t2.p_pwr end),
             sum(case when t2.acc_time = \'C\' then t2.p_cfr end),
             sum(case when t2.acc_time = \'C\' then t2.p_cwr end),
             sum(case when t2.acc_time = \'C\' then t2.c_cfr end),
             sum(case when t2.acc_time = \'C\' then t2.c_cwr end)
      from QTC_BUSS_CALC_CFG t1,
           qtc_dap_ecf_lic_pv t2
      where t1.EVL_MAIN_ID = #evl_main_id#
        and t2.ENTITY_ID = t1.ENTITY_ID
        and t2.YEAR_MONTH = t1.YEAR_MONTH
        and t2.BUSS_MODEL = t1.BUSS_MODEL
        and t2.LOA_CODE = t1.loa_code
        and t2.cf_type = \'IBNR\'
      group by t1.EVL_MAIN_ID, t2.ICG_NO) TTT');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_E03', 3509, e'insert into qtc_buss_evl_lic_pre(LIC_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
SELECT nextval(\'qtc_seq_buss_evl_lic_pre\'), \'IE3\', TTT.*
from (select t1.EVL_MAIN_ID,
             t2.ICG_NO,
             sum(case when t2.acc_time = \'P\' then t2.p_pfr end),
             sum(case when t2.acc_time = \'P\' then t2.p_pwr end),
             sum(case when t2.acc_time = \'P\' then t2.p_cfr end),
             sum(case when t2.acc_time = \'P\' then t2.p_cwr end),
             sum(case when t2.acc_time = \'P\' then t2.c_cfr end),
             sum(case when t2.acc_time = \'P\' then t2.c_cwr end),
             sum(case when t2.acc_time = \'C\' then t2.p_pfr end),
             sum(case when t2.acc_time = \'C\' then t2.p_pwr end),
             sum(case when t2.acc_time = \'C\' then t2.p_cfr end),
             sum(case when t2.acc_time = \'C\' then t2.p_cwr end),
             sum(case when t2.acc_time = \'C\' then t2.c_cfr end),
             sum(case when t2.acc_time = \'C\' then t2.c_cwr end)
      from QTC_BUSS_CALC_CFG t1,
           qtc_dap_ecf_lic_pv t2
      where t1.EVL_MAIN_ID = #evl_main_id#
        and t2.ENTITY_ID = t1.ENTITY_ID
        and t2.YEAR_MONTH = t1.YEAR_MONTH
        and t2.BUSS_MODEL = t1.BUSS_MODEL
        and t2.LOA_CODE = t1.loa_code
        and t2.cf_type = \'ULAE\'
      group by t1.EVL_MAIN_ID, t2.ICG_NO) TTT');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_E04', 3510, e'insert into qtc_buss_evl_lic_pre(LIC_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
SELECT nextval(\'qtc_seq_buss_evl_lic_pre\'), \'IE4\', TTT.*
from (select t1.EVL_MAIN_ID,
             t2.ICG_NO,
              sum(case when t2.acc_time = \'P\' then t2.p_pfr end),
             sum(case when t2.acc_time = \'P\' then t2.p_pwr end),
             sum(case when t2.acc_time = \'P\' then t2.p_cfr end),
             sum(case when t2.acc_time = \'P\' then t2.p_cwr end),
             sum(case when t2.acc_time = \'P\' then t2.c_cfr end),
             sum(case when t2.acc_time = \'P\' then t2.c_cwr end),
             sum(case when t2.acc_time = \'C\' then t2.p_pfr end),
             sum(case when t2.acc_time = \'C\' then t2.p_pwr end),
             sum(case when t2.acc_time = \'C\' then t2.p_cfr end),
             sum(case when t2.acc_time = \'C\' then t2.p_cwr end),
             sum(case when t2.acc_time = \'C\' then t2.c_cfr end),
             sum(case when t2.acc_time = \'C\' then t2.c_cwr end)
      from QTC_BUSS_CALC_CFG t1,
           qtc_dap_ecf_lic_pv t2
      where t1.EVL_MAIN_ID = #evl_main_id#
        and t2.ENTITY_ID = t1.ENTITY_ID
        and t2.YEAR_MONTH = t1.YEAR_MONTH
        and t2.BUSS_MODEL = t1.BUSS_MODEL
        and t2.LOA_CODE = t1.loa_code
        and t2.cf_type = \'RA\'
      group by t1.EVL_MAIN_ID, t2.ICG_NO) TTT');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_Y09', 3609, e'insert into QTC_BUSS_LIC_CAL_ICG(CAL_ICG_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11)
select nextval(\'QTC_SEQ_BUSS_PAA_CAL_ICG\'),
       \'K1\',
       EVL_MAIN_ID,
       ICG_NO,
       num1,
       num2,
       num3,
       num4,
       num5,
       num6,
       num7,
       num8,
       num9,
       num10,
       num11
from qtc_buss_evl_lic_pre
where EVL_MAIN_ID = #evl_main_id#
  and TYPE_NO = \'IF4\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_F05', 3516, e'insert into qtc_buss_evl_lic_pre(lic_pre_id, type_no, evl_main_id, icg_no, num1, num2, num3)
select nextval(\'QTC_SEQ_BUSS_LIC_CAL_ICG\'),
       \'IF5\',
       EVL_MAIN_ID,
       ICG_NO,
       p1,
       coalesce(v1, 0) - coalesce(p1, 0),
       v1
FROM (SELECT EVL_MAIN_ID,
             ICG_NO,
             SUM(case when TYPE_NO = \'IE5\' then  coalesce(num1,0)+coalesce(num7,0) - coalesce(num6,0)+coalesce(num11,0) end) v1,
             SUM(case when TYPE_NO = \'IC6\' then NUM1 end) p1
      from qtc_buss_evl_lic_pre
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO in (\'IE5\', \'IC6\')
      GROUP BY EVL_MAIN_ID, ICG_NO) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_Y10', 3610, e'insert into QTC_BUSS_LIC_CAL_ICG(CAL_ICG_ID, TYPE_NO, EVL_MAIN_ID, ICG_NO, NUM1, NUM2, NUM3)
select nextval(\'QTC_SEQ_BUSS_PAA_CAL_ICG\'),
       \'L1\',
       EVL_MAIN_ID,
       ICG_NO,
       num1,
       num2,
       num3
from qtc_buss_evl_lic_pre
where EVL_MAIN_ID = #evl_main_id#
  and TYPE_NO = \'IF5\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_F04', 3515, e'insert into qtc_buss_evl_lic_pre(lic_pre_id, type_no, evl_main_id, icg_no,
                                 num1, num2, num3, num4, num5, num6, num7, num8, num9, num10, num11)
select nextval(\'qtc_seq_buss_evl_lic_pre\'),
       \'IF4\',
       evl_main_id,
       icg_no,
       coalesce(num4, 0) + coalesce(num6, 0),
       coalesce(num10, 0) + coalesce(num12, 0),
       coalesce(num4, 0) + coalesce(num6, 0) + coalesce(num10, 0) + coalesce(num12, 0),
       num5,
       num11,
       coalesce(num3, 0) - coalesce(num2, 0),
       coalesce(num9, 0) - coalesce(num8, 0),
       coalesce(num2, 0) - coalesce(num1, 0) + coalesce(num8, 0) - coalesce(num7, 0) +
       coalesce(num4, 0) - coalesce(num3, 0) + coalesce(num10, 0) - coalesce(num9, 0) +
       coalesce(num6, 0) - coalesce(num5, 0) + coalesce(num12, 0) - coalesce(num11, 0),
       coalesce(num5, 0) - coalesce(num11, 0),
       coalesce(num3, 0) - coalesce(num2, 0) + coalesce(num9, 0) - coalesce(num8, 0),
       coalesce(num2, 0) - coalesce(num1, 0) + coalesce(num8, 0) - coalesce(num7, 0) +
       coalesce(num4, 0) - coalesce(num3, 0) + coalesce(num10, 0) - coalesce(num9, 0) +
       coalesce(num6, 0) - coalesce(num5, 0) + coalesce(num12, 0) - coalesce(num11, 0)
from qtc_buss_evl_lic_pre
where evl_main_id = #evl_main_id#
  and type_no = \'IE4\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_Y01', 3601, e'insert into QTC_BUSS_LIC_CAL_ICG(CAL_ICG_ID, TYPE_NO, EVL_MAIN_ID, ICG_NO, NUM1, NUM2, NUM3, NUM4, NUM5, NUM6)
select nextval(\'QTC_SEQ_BUSS_LIC_CAL_ICG\'),
       \'OH\',
       EVL_MAIN_ID,
       ICG_NO,
       coalesce(p5,0) ,
       coalesce(v1,0)+coalesce(p1,0),
       coalesce(v2,0)+coalesce(p2,0),
       coalesce(v3,0)+coalesce(p3,0),
       coalesce(v4,0)+coalesce(p4,0),
       coalesce(p5,0)+coalesce(v1,0)+coalesce(p1,0)+coalesce(v2,0)+coalesce(p2,0)+coalesce(v3,0)+coalesce(p3,0)+coalesce(v4,0)+coalesce(p4,0)
FROM (SELECT EVL_MAIN_ID,
             ICG_NO,
             SUM(case when TYPE_NO = \'IF1\' then NUM4 end)  v1,
             SUM(case when TYPE_NO = \'IF1\' then NUM5 end)  v2,
             SUM(case when TYPE_NO = \'IF1\' then NUM10 end) v3,
             SUM(case when TYPE_NO = \'IF1\' then NUM11 end) v4,
             SUM(case when TYPE_NO = \'IC1\' then NUM2 end)  p1,
             SUM(case when TYPE_NO = \'IC1\' then NUM3 end)  p2,
             SUM(case when TYPE_NO = \'IC1\' then NUM4 end)  p3,
             SUM(case when TYPE_NO = \'IC1\' then NUM5 end)  p4,
             SUM(case when TYPE_NO = \'IC1\' then NUM6 end)  p5
      from qtc_buss_evl_lic_pre
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO in (\'IF1\', \'IC1\')
      GROUP BY EVL_MAIN_ID, ICG_NO) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_Y02', 3602, e'insert into QTC_BUSS_LIC_CAL_ICG(CAL_ICG_ID, TYPE_NO, EVL_MAIN_ID, ICG_NO, NUM1, NUM2, NUM3, NUM4, NUM5, NUM6)
select nextval(\'QTC_SEQ_BUSS_LIC_CAL_ICG\'),
       \'OI\',
       EVL_MAIN_ID,
       ICG_NO,
       coalesce(p5,0) ,
       coalesce(v1,0)+coalesce(p1,0),
       coalesce(v2,0)+coalesce(p2,0),
       coalesce(v3,0)+coalesce(p3,0),
       coalesce(v4,0)+coalesce(p4,0),
       coalesce(p5,0)+coalesce(v1,0)+coalesce(p1,0)+coalesce(v2,0)+coalesce(p2,0)+coalesce(v3,0)+coalesce(p3,0)+coalesce(v4,0)+coalesce(p4,0)
FROM (SELECT EVL_MAIN_ID,
             ICG_NO,
             SUM(case when TYPE_NO = \'IF2\' then NUM4 end)  v1,
             SUM(case when TYPE_NO = \'IF2\' then NUM5 end)  v2,
             SUM(case when TYPE_NO = \'IF2\' then NUM10 end) v3,
             SUM(case when TYPE_NO = \'IF2\' then NUM11 end) v4,
             SUM(case when TYPE_NO = \'IC2\' then NUM2 end)  p1,
             SUM(case when TYPE_NO = \'IC2\' then NUM3 end)  p2,
             SUM(case when TYPE_NO = \'IC2\' then NUM4 end)  p3,
             SUM(case when TYPE_NO = \'IC2\' then NUM5 end)  p4,
             SUM(case when TYPE_NO = \'IC2\' then NUM6 end)  p5
      from qtc_buss_evl_lic_pre
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO in (\'IF2\', \'IC2\')
      GROUP BY EVL_MAIN_ID, ICG_NO) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_Y03', 3603, e'insert into QTC_BUSS_LIC_CAL_ICG(CAL_ICG_ID, TYPE_NO, EVL_MAIN_ID, ICG_NO, NUM1, NUM2, NUM3, NUM4, NUM5, NUM6)
select nextval(\'QTC_SEQ_BUSS_LIC_CAL_ICG\'),
       \'OJ\',
       EVL_MAIN_ID,
       ICG_NO,
       coalesce(p5,0) ,
       coalesce(v1,0)+coalesce(p1,0),
       coalesce(v2,0)+coalesce(p2,0),
       coalesce(v3,0)+coalesce(p3,0),
       coalesce(v4,0)+coalesce(p4,0),
       coalesce(p5,0)+coalesce(v1,0)+coalesce(p1,0)+coalesce(v2,0)+coalesce(p2,0)+coalesce(v3,0)+coalesce(p3,0)+coalesce(v4,0)+coalesce(p4,0)
FROM (SELECT EVL_MAIN_ID,
             ICG_NO,
             SUM(case when TYPE_NO = \'IF3\' then NUM4 end)  v1,
             SUM(case when TYPE_NO = \'IF3\' then NUM5 end)  v2,
             SUM(case when TYPE_NO = \'IF3\' then NUM10 end) v3,
             SUM(case when TYPE_NO = \'IF3\' then NUM11 end) v4,
             SUM(case when TYPE_NO = \'IC3\' then NUM2 end)  p1,
             SUM(case when TYPE_NO = \'IC3\' then NUM3 end)  p2,
             SUM(case when TYPE_NO = \'IC3\' then NUM4 end)  p3,
             SUM(case when TYPE_NO = \'IC3\' then NUM5 end)  p4,
             SUM(case when TYPE_NO = \'IC3\' then NUM6 end)  p5
      from qtc_buss_evl_lic_pre
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO in (\'IF3\', \'IC3\')
      GROUP BY EVL_MAIN_ID, ICG_NO) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_Y05', 3605, e'insert into QTC_BUSS_LIC_CAL_ICG(CAL_ICG_ID, TYPE_NO, EVL_MAIN_ID, ICG_NO, NUM1, NUM2, NUM3)
select nextval(\'QTC_SEQ_BUSS_LIC_CAL_ICG\'),
       \'OL\',
       EVL_MAIN_ID,
       ICG_NO,
       P2,
       coalesce(v1, 0) + coalesce(p1, 0),
       coalesce(p2, 0)+coalesce(v1, 0) + coalesce(p1, 0)
FROM (SELECT EVL_MAIN_ID,
             ICG_NO,
             SUM(case when TYPE_NO = \'IF5\' then NUM2 end) v1,
             SUM(case when TYPE_NO = \'IC5\' then NUM2 end) p1,
             SUM(case when TYPE_NO = \'IC5\' then NUM3 end) p2
      from qtc_buss_evl_lic_pre
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO in (\'IF5\', \'IC5\')
      GROUP BY EVL_MAIN_ID, ICG_NO) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_Y06', 3606, e'insert into QTC_BUSS_LIC_CAL_ICG(CAL_ICG_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11)
select nextval(\'QTC_SEQ_BUSS_PAA_CAL_ICG\'),
       \'H1\',
       EVL_MAIN_ID,
       ICG_NO,
       num1,
       num2,
       num3,
       num4,
       num5,
       num6,
       num7,
       num8,
       num9,
       num10,
       num11
from qtc_buss_evl_lic_pre
where EVL_MAIN_ID = #evl_main_id#
  and TYPE_NO = \'IF1\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_Y07', 3607, e'insert into QTC_BUSS_LIC_CAL_ICG(CAL_ICG_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11)
select nextval(\'QTC_SEQ_BUSS_PAA_CAL_ICG\'),
       \'I1\',
       EVL_MAIN_ID,
       ICG_NO,
       num1,
       num2,
       num3,
       num4,
       num5,
       num6,
       num7,
       num8,
       num9,
       num10,
       num11
from qtc_buss_evl_lic_pre
where EVL_MAIN_ID = #evl_main_id#
  and TYPE_NO = \'IF2\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_Y08', 3608, e'insert into QTC_BUSS_LIC_CAL_ICG(CAL_ICG_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11)
select nextval(\'QTC_SEQ_BUSS_PAA_CAL_ICG\'),
       \'J1\',
       EVL_MAIN_ID,
       ICG_NO,
       num1,
       num2,
       num3,
       num4,
       num5,
       num6,
       num7,
       num8,
       num9,
       num10,
       num11
from qtc_buss_evl_lic_pre
where EVL_MAIN_ID = #evl_main_id#
  and TYPE_NO = \'IF3\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_Z02', 3612, e'MERGE INTO QTC_BUSS_EVALUATE_RESULT TG
USING (SELECT EVL_MAIN_ID, ICG_NO, num1, num2, num3, num4, num5, num6
           FROM QTC_BUSS_LIC_CAL_ICG
           WHERE EVL_MAIN_ID = #evl_main_id#
           AND TYPE_NO = \'OH\') SC
ON(TG.VAR1 = \'Lic\' AND TG.EVALUATE_MAIN_ID = SC.EVL_MAIN_ID AND TG.ICG_NO = SC.ICG_NO)
WHEN MATCHED THEN UPDATE SET NUM1 = SC.NUM1, NUM2 = SC.NUM3, NUM3 = SC.NUM2, NUM4 = SC.NUM4, num5=sc.num5, num6=sc.num6');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_Z03', 3613, e'MERGE INTO QTC_BUSS_EVALUATE_RESULT TG
USING (SELECT EVL_MAIN_ID, ICG_NO, num1, num2, num3, num4, num5, num6
           FROM QTC_BUSS_LIC_CAL_ICG
           WHERE EVL_MAIN_ID = #evl_main_id#
           AND TYPE_NO = \'OI\') SC
ON(TG.VAR1 = \'Lic\' AND TG.EVALUATE_MAIN_ID = SC.EVL_MAIN_ID AND TG.ICG_NO = SC.ICG_NO)
WHEN MATCHED THEN UPDATE SET NUM7 = SC.NUM1, NUM8 = SC.NUM3, NUM9 = SC.NUM2, NUM10 = SC.NUM4, num11=sc.num5, num12=sc.num6');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_Z04', 3614, e'MERGE INTO QTC_BUSS_EVALUATE_RESULT TG
USING (SELECT EVL_MAIN_ID, ICG_NO, num1, num2, num3, num4, num5, num6
           FROM QTC_BUSS_LIC_CAL_ICG
           WHERE EVL_MAIN_ID = #evl_main_id#
           AND TYPE_NO = \'OJ\') SC
ON(TG.VAR1 = \'Lic\' AND TG.EVALUATE_MAIN_ID = SC.EVL_MAIN_ID AND TG.ICG_NO = SC.ICG_NO)
WHEN MATCHED THEN UPDATE SET NUM13 = SC.NUM1, NUM14 = SC.NUM3, NUM15 = SC.NUM2, NUM16 = SC.NUM4, num17=sc.num5, num18=sc.num6');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_Z05', 3615, e'MERGE INTO QTC_BUSS_EVALUATE_RESULT TG
USING (SELECT EVL_MAIN_ID, ICG_NO, num1, num2, num3, num4, num5, num6
           FROM QTC_BUSS_LIC_CAL_ICG
           WHERE EVL_MAIN_ID = #evl_main_id#
           AND TYPE_NO = \'OK\') SC
ON(TG.VAR1 = \'Lic\' AND TG.EVALUATE_MAIN_ID = SC.EVL_MAIN_ID AND TG.ICG_NO = SC.ICG_NO)
WHEN MATCHED THEN UPDATE SET NUM19 = SC.NUM1, NUM20 = SC.NUM3, NUM21 = SC.NUM2, NUM22 = SC.NUM4, num23=sc.num5, num24=sc.num6');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_Z06', 3616, e'MERGE INTO QTC_BUSS_EVALUATE_RESULT TG
USING (SELECT EVL_MAIN_ID, ICG_NO, num1, num2, num3
           FROM QTC_BUSS_LIC_CAL_ICG
           WHERE EVL_MAIN_ID = #evl_main_id#
           AND TYPE_NO = \'OL\') SC
ON(TG.VAR1 = \'Lic\' AND TG.EVALUATE_MAIN_ID = SC.EVL_MAIN_ID AND TG.ICG_NO = SC.ICG_NO)
WHEN MATCHED THEN UPDATE SET NUM25 = SC.NUM1, num26=sc.num2, num27=sc.num3');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_DL_02', 5000, 'delete from qtc_buss_evl_alloc_result  where evl_main_id = #evl_main_id# ');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_DL_01', 5000, 'delete from qtc_buss_calc_alloc_cfg where evl_main_id = #evl_main_id# ');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_A01', 3001, e'insert into qtc_buss_calc_cfg(CFG_ID, ENTITY_ID, YEAR_MONTH, CURRENCY_CODE, EVL_MAIN_ID, BUSS_MODEL, LOA_CODE, EVL_L_MAIN_ID, YM_LAST, rpt_is, ver_type, cf_dire, show_no)
select nextval(\'qtc_seq_buss_calc_cfg\'),
       t1.ENTITY_ID,
       t1.YEAR_MONTH,
       t1.CURRENCY_CODE,
       t1.EVALUATE_MAIN_ID,
       t1.BUSINESS_MODEL || t1.BUSINESS_DIRECTION,
       t1.LOA_CODE,
       t2.EVALUATE_MAIN_ID,
       t2.YEAR_MONTH,
       CASE WHEN SUBSTR(T1.year_month, 5, 2) = \'01\' THEN 1 ELSE 0 END,
       t1.ver_type,
       case when t1.business_model||t1.business_direction in (\'DD\',\'TI\') then 1 else -1 end ,
       t1.show_no
from QTC_BUSS_EVALUATE_MAIN t1
         left join
     QTC_BUSS_EVALUATE_MAIN t2 on t2.YEAR_MONTH = to_char(to_date(t1.YEAR_MONTH, \'yyyymm\')-interval \'1 month\', \'yyyymm\')
         and t2.LOA_CODE = t1.LOA_CODE
         and t2.BUSINESS_MODEL = t1.BUSINESS_MODEL
         and t2.BUSINESS_DIRECTION = t1.BUSINESS_DIRECTION
         and t2.ver_type = t1.ver_type
         and t2.CONFIRM_IS = \'1\'
where t1.EVALUATE_MAIN_ID = #evl_main_id#');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_G03', 3022, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, buss_key,  num1)
select nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RG3\',
       t1.evl_main_id,
       t2.ri_icg_no,
       t2.icg_no,
       case when t2.upr=0 then 0 else t2.ri_upr / t2.upr  end
from qtc_buss_calc_cfg t1 ,
     qtc_dap_recovered t2
where t1.evl_main_id = #evl_main_id#
and t2.entity_id = t1.entity_id
and t2.year_month = t1.year_month
and t2.ri_buss_model = t1.buss_model
and t2.ri_loa_code = t1.loa_code');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_G04', 3023, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, NUM3)
select nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RG1\',
       ttt.*
FROM (SELECT t2.EVL_MAIN_ID,
             t2.ICG_NO,
             SUM(t2.num1*t1.num1) V1
      from QTC_BUSS_EVL_PAA_PRE T1 ,
           QTC_BUSS_EVL_PAA_PRE T2
      where T1.EVL_MAIN_ID = #evl_main_id#
        and t2.evl_main_id = t1.evl_main_id
        and t1.type_no = \'RG2\'
        and t2.type_no = \'RG3\'
        and t1.buss_key = t2.buss_key
      GROUP BY t2.EVL_MAIN_ID, t2.ICG_NO) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_G02', 3021, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, buss_key, num1)
select nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RG2\',
       t1.evl_main_id,
       t3.icg_no ,
       num28
from qtc_buss_calc_cfg t1 ,
     qtc_buss_evaluate_main t2 ,
     qtc_buss_evaluate_result t3
where t1.evl_main_id = #evl_main_id#
and t2.show_no = t1.show_no
and t2.model_def_id = 3
and t3.evaluate_main_id = t2.evaluate_main_id
and t3.var1 = \'Lrc\'
and abs(num28) > 0');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_TR_E06', 5026, e'insert into qtc_temp_lrc_alloc_icg(evl_main_id, type_no, year_month, loa_code, portfolio_no, icg_no, num1, num2, num3, num4, num5, num6,num7,num8,num9,num10, num11)
select evl_main_id,\'RE3\',year_month, loa_code, portfolio_no, icg_no,
        sum(num1), sum(num2), sum(num3), sum(num4), sum(num5), sum(num6), sum(num7), sum(num8), sum(num9), sum(num10), sum(num11)
from qtc_temp_lrc_alloc_calc
where evl_main_id = #evl_main_id#
  and type_no = \'RC2\'
group by evl_main_id, year_month, loa_code, portfolio_no, icg_no');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_TR_Y01', 5081, e'insert into qtc_buss_lrc_alloc_calc(evl_main_id, type_no, year_month, loa_code, portfolio_no, icg_no, str1, str2, str3, str4, str5, str6, str7, str8, str9, str10, str11, str12, str13, str14,
        num1, num2, num3, num4, num5, num6,num7,num8,num9,num10, num11,num12)
select evl_main_id,\'RY1\', year_month, loa_code, portfolio_no, icg_no, str1, str2, str3, str4, str5, str6, str7, str8, str9, str10, str11, str12, str13, str14,
        sum(num1), sum(num2), sum(num3), sum(num4), sum(num5), sum(num6), sum(num7), sum(num8), sum(num9), sum(num10), sum(num11),sum(num12)
from qtc_temp_lrc_alloc_calc
where evl_main_id = #evl_main_id#
  and type_no in (\'RC1\', \'RE1\')
group by evl_main_id, year_month, loa_code, portfolio_no, icg_no, str1, str2, str3, str4, str5, str6, str7, str8, str9, str10, str11, str12, str13, str14 ');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_E05', 3511, e'insert into qtc_buss_evl_lic_pre(LIC_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5, num6, num7,
                                 num8, num9, num10, num11, num12)
SELECT nextval(\'qtc_seq_buss_evl_lic_pre\'), \'IE5\', TTT.*
from (select t1.EVL_MAIN_ID,
             t2.ICG_NO,
              sum(case when t2.acc_time = \'P\' then t2.p_pfr end),
             sum(case when t2.acc_time = \'P\' then t2.p_pwr end),
             sum(case when t2.acc_time = \'P\' then t2.p_cfr end),
             sum(case when t2.acc_time = \'P\' then t2.p_cwr end),
             sum(case when t2.acc_time = \'P\' then t2.c_cfr end),
             sum(case when t2.acc_time = \'P\' then t2.c_cwr end),
             sum(case when t2.acc_time = \'C\' then t2.p_pfr end),
             sum(case when t2.acc_time = \'C\' then t2.p_pwr end),
             sum(case when t2.acc_time = \'C\' then t2.p_cfr end),
             sum(case when t2.acc_time = \'C\' then t2.p_cwr end),
             sum(case when t2.acc_time = \'C\' then t2.c_cfr end),
             sum(case when t2.acc_time = \'C\' then t2.c_cwr end)
      from QTC_BUSS_CALC_CFG t1,
           qtc_dap_ecf_lic_pv t2
      where t1.EVL_MAIN_ID = #evl_main_id#
        and t2.ENTITY_ID = t1.ENTITY_ID
        and t2.YEAR_MONTH = t1.YEAR_MONTH
        and t2.BUSS_MODEL = t1.BUSS_MODEL
        and t2.LOA_CODE = t1.loa_code
        and t2.cf_type = \'RD\'
      group by t1.EVL_MAIN_ID, t2.ICG_NO) TTT');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_C02', 3502, e'insert into qtc_buss_evl_lic_pre(lic_pre_id, type_no, evl_main_id, ICG_NO, num1, num2, num3, num4,num5,num6)
SELECT nextval(\'qtc_seq_buss_evl_lic_pre\'),
       \'IC2\',
       T1.evl_main_id,
       T2.icg_no,
       T2.num1*t1.rpt_is,
       T2.num2*(1-t1.rpt_is),
       T2.num3*(1-t1.rpt_is),
       T2.num4*(1-t1.rpt_is),
       t2.num5*(1-t1.rpt_is),
       t2.num6*t1.rpt_is
from QTC_BUSS_CALC_CFG T1,
     QTC_BUSS_LIC_CAL_ICG T2
WHERE T1.EVL_MAIN_ID = #evl_main_id#
  AND T2.EVL_MAIN_ID = T1.EVL_L_MAIN_ID
  AND T2.TYPE_NO = \'OI\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_C04', 3504, e'insert into qtc_buss_evl_lic_pre(lic_pre_id, type_no, evl_main_id, ICG_NO, num1, num2, num3, num4,num5,num6)
SELECT nextval(\'qtc_seq_buss_evl_lic_pre\'),
       \'IC4\',
       T1.evl_main_id,
       T2.icg_no,
       T2.num1*t1.rpt_is,
       T2.num2*(1-t1.rpt_is),
       T2.num3*(1-t1.rpt_is),
       T2.num4*(1-t1.rpt_is),
       t2.num5*(1-t1.rpt_is),
       t2.num6*t1.rpt_is
from QTC_BUSS_CALC_CFG T1,
     QTC_BUSS_LIC_CAL_ICG T2
WHERE T1.EVL_MAIN_ID = #evl_main_id#
  AND T2.EVL_MAIN_ID = T1.EVL_L_MAIN_ID
  AND T2.TYPE_NO = \'OK\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_C05', 3505, e'insert into qtc_buss_evl_lic_pre(lic_pre_id, type_no, evl_main_id, ICG_NO, num1, num2, num3)
SELECT nextval(\'qtc_seq_buss_evl_lic_pre\'),
       \'IC5\',
       T1.evl_main_id,
       T2.icg_no,
       T2.num1*t1.rpt_is,
       T2.num2*(1-t1.rpt_is),
       T2.num3*t1.rpt_is
from QTC_BUSS_CALC_CFG T1,
     QTC_BUSS_LIC_CAL_ICG T2
WHERE T1.EVL_MAIN_ID = #evl_main_id#
  AND T2.EVL_MAIN_ID = T1.EVL_L_MAIN_ID
  AND T2.TYPE_NO = \'OL\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_C03', 3503, e'insert into qtc_buss_evl_lic_pre(lic_pre_id, type_no, evl_main_id, ICG_NO, num1, num2, num3, num4,num5,num6)
SELECT nextval(\'qtc_seq_buss_evl_lic_pre\'),
       \'IC3\',
       T1.evl_main_id,
       T2.icg_no,
       T2.num1*t1.rpt_is,
       T2.num2*(1-t1.rpt_is),
       T2.num3*(1-t1.rpt_is),
       T2.num4*(1-t1.rpt_is),
       t2.num5*(1-t1.rpt_is),
       t2.num6*t1.rpt_is
from QTC_BUSS_CALC_CFG T1,
     QTC_BUSS_LIC_CAL_ICG T2
WHERE T1.EVL_MAIN_ID = #evl_main_id#
  AND T2.EVL_MAIN_ID = T1.EVL_L_MAIN_ID
  AND T2.TYPE_NO = \'OJ\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_C01', 3501, e'insert into qtc_buss_evl_lic_pre(lic_pre_id, type_no, evl_main_id, ICG_NO, num1, num2, num3, num4,num5,num6)
SELECT nextval(\'qtc_seq_buss_evl_lic_pre\'),
       \'IC1\',
       T1.evl_main_id,
       T2.icg_no,
       T2.num1*t1.rpt_is,
       T2.num2*(1-t1.rpt_is),
       T2.num3*(1-t1.rpt_is),
       T2.num4*(1-t1.rpt_is),
       t2.num5*(1-t1.rpt_is),
       t2.num6*t1.rpt_is
from QTC_BUSS_CALC_CFG T1,
     QTC_BUSS_LIC_CAL_ICG T2
WHERE T1.EVL_MAIN_ID = #evl_main_id#
  AND T2.EVL_MAIN_ID = T1.EVL_L_MAIN_ID
  AND T2.TYPE_NO = \'OH\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_TR_C02', 5012, e'insert into qtc_temp_lrc_alloc_calc(evl_main_id, type_no, year_month, loa_code, portfolio_no, icg_no, str1, str2, str3, str4, str5, str6, str7, str8, str9, str10, str11, str12, str13, str14,
        num1, num2, num3, num4, num5, num6,num7,num8,num9,num10, num11)
select t1.evl_main_id,\'RC2\', t1.year_month, t2.loa_code, t2.portfolio_no, t2.icg_no, t2.str1, t2.str2, t2.str3, str4, t2.str5, t2.str6,t2.str7, t2.str8, t2.str9, t2.str10, t2.str11, t2.str12, t2.str13 , t2.str14,
        t2.num1, t2.num2, t2.num3, t2.num4, t2.num5, t2.num6, t2.num7, t2.num8, t2.num9, t2.num10, t2.num11
from qtc_buss_calc_alloc_cfg t1,
     qtc_buss_lrc_alloc_calc t2
where t1.evl_main_id = #evl_main_id#
  and t1.rpt_is = \'1\'
  and t2.year_month = t1.ym_last
  and t2.evl_main_id = t1.evl_l_main_id
  and t2.type_no = \'RY1\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_TR_A01', 5001, e'insert into qtc_buss_calc_alloc_cfg(CFG_ID, ENTITY_ID, YEAR_MONTH, CURRENCY_CODE,
                              EVL_MAIN_ID, BUSS_MODEL, LOA_CODE, EVL_L_MAIN_ID, YM_LAST, rpt_is, ver_type)
select nextval(\'qtc_seq_buss_calc_cfg\'),
       t1.ENTITY_ID,
       t1.YEAR_MONTH,
       t1.CURRENCY_CODE,
       t1.EVALUATE_MAIN_ID,
       t1.BUSINESS_MODEL || t1.BUSINESS_DIRECTION,
       t1.LOA_CODE,
       t2.EVALUATE_MAIN_ID,
       t2.YEAR_MONTH,
       CASE WHEN SUBSTR(T1.year_month, 5, 2) = \'01\' THEN \'1\' ELSE \'0\' END,
       t1.ver_type
from QTC_BUSS_EVALUATE_MAIN t1
         left join
     QTC_BUSS_EVALUATE_MAIN t2 on t2.YEAR_MONTH = to_char(to_date(t1.YEAR_MONTH, \'yyyymm\')-interval \'1 month\', \'yyyymm\')
         and t2.LOA_CODE = t1.LOA_CODE
         and t2.BUSINESS_MODEL = t1.BUSINESS_MODEL
         and t2.BUSINESS_DIRECTION = t1.BUSINESS_DIRECTION
         and t2.CONFIRM_IS = \'1\'
         and t2.ver_type = t1.ver_type
where t1.EVALUATE_MAIN_ID = #evl_main_id#');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_TR_E05', 5025, e'insert into qtc_temp_lrc_alloc_icg(evl_main_id, type_no, year_month, loa_code, portfolio_no, icg_no,
                                   num1, num2, num3, num4, num5, num6,num7,num8,num9,num10, num11, num12)
select evl_main_id,\'RE2\', year_month, loa_code, portfolio_no, icg_no,
        sum(num1), sum(num2), sum(num3), sum(num4), sum(num5), sum(num6), sum(num7), sum(num8), sum(num9), sum(num10), sum(num11), sum(num12)
from qtc_temp_lrc_alloc_calc
where evl_main_id = #evl_main_id#
  and type_no in (\'RC1\', \'RE1\')
group by evl_main_id, year_month, loa_code, portfolio_no, icg_no');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_TR_E02', 5022, e'insert into qtc_temp_lrc_alloc_calc(evl_main_id, type_no, year_month, loa_code, portfolio_no, icg_no, str1, str2, str3, str4, str5, str6,str7, str8, str9, str10, str11, str12,str13,str14,
                                    num1, num2, num3, num4, num5, num6, num7, num8, num9, num10, num11, num12)
select t1.evl_main_id,
       \'RE1\',
       t1.year_month,
       t2.loa_code,
       t2.portfolio_no,
       t2.icg_no,
       t2.loa_code,
       t2.buss_source_code,
       t2.pl_judge_rslt,
       t2.dept_code,
       t2.product_code,
       t2.fin_detail_code,
       t2.fin_acc_channel,
       t2.sup_product_code,
       null,
       null,
       t2.treaty_no,
       t2.ri_dept,
       t2.risk_class,
       t2.center_code ,
       t2.ed_premium,
       t2.ed_iacf,
       case when t1.ver_type = \'I\' then t2.ed_iacf_non_in else t2.ed_iacf_non  end ,
       t2.ed_net_charge,
       t2.ed_dec_fee,
       t2.re_premium,
       t2.re_iacf,
       case when t1.ver_type = \'I\' then t2.re_iacf_non_in else t2.re_iacf_non  end ,
       t2.re_net_charge,
       t2.re_dec_fee ,
       t2.cr_premium,
       t2.invest_amount
from qtc_buss_calc_alloc_cfg t1,
     qtc_dap_lrc_ti_apt t2
where t1.evl_main_id = #evl_main_id#
  and t1.buss_model = \'TI\'
  and t2.year_month = t1.year_month
  and t2.entity_id = t1.entity_id
  and t2.currency_code = t1.currency_code
  and t2.loa_code = t1.loa_code');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_TR_E01', 5021, e'insert into qtc_temp_lrc_alloc_calc(evl_main_id, type_no, year_month, loa_code, portfolio_no, icg_no, str1, str2, str3, str4, str5, str6,
                                    str7, str8, str9, str10, str11, str12, str13 , str14, num1, num2, num3, num4, num5, num6,num7,num8,num9,num10, num11, num12)
select t1.evl_main_id,
       \'RE1\',
       t1.year_month ,
       t2.loa_code,
       t2.portfolio_no,
       t2.icg_no,
       t2.loa_code,
       t2.buss_source_code,
       t2.pl_judge_rslt,
       t2.dept_code,
       t2.product_code,
       t2.fin_detail_code,
       t2.fin_acc_channel,
       t2.sup_product_code,
       t2.policy_no,
       t2.kind_code,
       null,
       null,
       null,
       t2.center_code,
       t2.ed_premium,
       t2.ed_iacf,
       case when t1.ver_type = \'I\' then t2.ed_iacf_non_in else t2.ed_iacf_non  end ,
       t2.ed_net_charge,
       t2.ed_dec_fee,
       t2.re_premium,
       t2.re_iacf,
       case when t1.ver_type = \'I\' then t2.re_iacf_non_in else t2.re_iacf_non  end ,
       t2.re_net_charge,
       t2.re_dec_fee ,
       t2.cr_premium,
       t2.invest_amount
from qtc_buss_calc_alloc_cfg t1,
     qtc_dap_lrc_dd_apt t2
where t1.evl_main_id = #evl_main_id#
  and t1.buss_model = \'DD\'
  and t2.year_month = t1.year_month
  and t2.entity_id = t1.entity_id
  and t2.currency_code = t1.currency_code
  and t2.loa_code = t1.loa_code');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_TR_Z01', 5091, e'insert into qtc_buss_evl_alloc_result(evl_main_id, year_month, var1, portfolio_no, icg_no, str1, str2, str3, str4, str5, str6, str7, str8,str9, str10, str11, str12, str13,str14,
    num1, num2, num3,num4, num5,num6,num7,num8,num9,num10, num11, num12, num13,num14, num15,num16,num17,num18,num19,num20,num21, num22, num23,num24, num25,num26,num27,num28,num29)
select t1.evl_main_id,t1.year_month,t2.var1,t1.portfolio_no, t1.icg_no, t1.str1,t1.str2,t1.str3,t1.str4, t1.str5,t1.str6,t1.str7,t1.str8,t1.str9,t1.str10, t1.str11, t1.str12,t1.str13,t1.str14,
       t2.num1*t1.num1 ,
       t2.num2*t1.num1 ,
       t2.num3*t1.num2 ,
       t1.num3*-1 ,
       coalesce(t2.num1,0)+coalesce(t2.num2*t1.num1,0)+coalesce(t2.num3*t1.num2,0)+ coalesce(t1.num3*-1,0) ,
       t2.num6*t1.num4 ,
       t2.num7*t1.num4 ,
       t2.num8*t1.num5 ,
       t1.num6 ,
       coalesce(t2.num6*t1.num4,0) + coalesce(t2.num7*t1.num4,0) + coalesce(t2.num8*t1.num5,0) + coalesce(t1.num6,0) ,
       t2.num11*t1.num7 ,
       t2.num12*t1.num7 ,
       t2.num13*t1.num8 ,
       t1.num9 ,
       coalesce(t2.num11*t1.num7,0)+coalesce(t2.num12*t1.num7,0) + coalesce(t2.num13*t1.num8,0) + coalesce(t1.num9,0) ,
       t2.num16*t1.num10 ,
       t2.num17*t1.num10 ,
       t2.num18*t1.num11 ,
       t1.num12 ,
       coalesce(t2.num16*t1.num10,0) + coalesce(t2.num17*t1.num10,0) + coalesce(t2.num18*t1.num11,0) + coalesce(t1.num12,0) ,
       t2.num21*t1.num13 ,
       t2.num22*t1.num13 ,
       t2.num23*t1.num14 ,
       t1.num15 ,
       coalesce(t2.num21*t1.num13,0) + coalesce(t2.num22*t1.num13,0) + coalesce(t2.num23*t1.num14,0) + coalesce(t1.num15,0) ,
       t2.num26,
       t2.num28*t1.num16,
       t2.num28*t1.num16,
       t2.num29*t1.num17
from qtc_buss_calc_alloc_cfg cg ,
     qtc_buss_lrc_alloc_rate t1,
     qtc_buss_evaluate_result t2
where cg.evl_main_id = #evl_main_id#
  and t1.year_month = cg.year_month
  and t1.evl_main_id = cg.evl_main_id
  and t2.evaluate_main_id = t1.evl_main_id
  and t1.type_no = \'RA1\'
  and t2.var1 = \'Lrc\'
  and t2.icg_no = t1.icg_no ');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_TR_Y02', 5082, e'insert into qtc_buss_lrc_alloc_rate(evl_main_id, type_no, year_month, loa_code, portfolio_no, icg_no, str1, str2, str3, str4, str5, str6, str7, str8, str9, str10, str11, str12, str13, str14,
        num1, num2, num3, num4, num5, num6,num7,num8,num9,num10, num11, num12, num13,num14, num15, num16, num17)
select t1.evl_main_id, \'RA1\', t1.year_month, t1.loa_code, t1.portfolio_no, t2.icg_no, str1, str2, str3, str4, str5, str6, str7, str8, str9, str10, str11, str12, str13, str14,
        case when t1.num1 = 0 then 0 else t2.v1 / t1.num1 end ,
        case when t1.num6 = 0 then 0 else t2.v6 / t1.num6 end ,
        t2.v6 ,
        case when t1.num4 = 0 then 0 else t2.v4 / t1.num4 end ,
        case when t1.num9 = 0 then 0 else t2.v9 / t1.num9 end ,
        t2.v9 ,
        case when t1.num2 = 0 then 0 else t2.v2 / t1.num2 end ,
        case when t1.num7 = 0 then 0 else t2.v7 / t1.num7 end ,
        t2.v7 ,
        case when t1.num3 = 0 then 0 else t2.v3 / t1.num3 end ,
        case when t1.num8 = 0 then 0 else t2.v8 / t1.num8 end ,
        t2.v8 ,
        case when t1.num5 = 0 then 0 else t2.v5 / t1.num5 end ,
        case when t1.num10 = 0 then 0 else t2.v10 / t1.num10 end ,
        t2.v10 ,
        case when (t1.num11-t1.num1) = 0 then 0 else (t2.v11-t2.v1) / (t1.num11-t1.num1) end,
        case when t1.num12 = 0 then 0 else t2.v12 / t1.num12 end
from qtc_temp_lrc_alloc_icg t1 ,
     (select icg_no, str1, str2, str3, str4, str5, str6, str7, str8, str9, str10, str11, str12, str13, str14,
        sum(num1) v1, sum(num2) v2, sum(num3) v3, sum(num4) v4, sum(num5) v5, sum(num6) v6, sum(num7) v7, sum(num8) v8, sum(num9) v9, sum(num10) v10, sum(num11) v11, sum(num12) v12
from qtc_temp_lrc_alloc_calc
where evl_main_id = #evl_main_id#
  and type_no in (\'RC1\', \'RE1\')
group by evl_main_id, loa_code, portfolio_no, icg_no, str1, str2, str3, str4, str5, str6, str7, str8, str9, str10, str11, str12, str13, str14) t2
where t1.evl_main_id = #evl_main_id#
and t1.type_no = \'RE2\'
and t2.icg_no = t1.icg_no ');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_TC_06', 5106, e'insert into qtc_buss_evl_lic_alloc_calc(level, evl_main_id, portfolio_no, icg_no, num1, num2, num3, num4, num5,num6,num7,num8,num9,num10)
select \'TGA1\' , ttt.*
from (select evl_main_id,
             portfolio_no,
             icg_no,
             sum(num1) ,
             sum(num2) ,
             sum(num3) ,
             sum(num4) ,
             sum(num5) ,
             sum(num6) ,
             sum(num7) ,
             sum(num8) ,
             sum(num9) ,
             sum(num10)
      from qtc_temp_evl_alloc_calc
      where evl_main_id = #evl_main_id#
        and type_no = \'TCA1\'
      group by evl_main_id, portfolio_no, icg_no) ttt ');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_TC_02', 5102, e'insert into qtc_temp_evl_alloc_calc(type_no, evl_main_id, portfolio_no, icg_no, var1, var2, var3, var4, var5, var6,
                                    var7, var8, var9, var10, var11, var12, var13 , var14, num1, num2, num3, num4, num6, num7, num8, num9)
select \'TCA1\', ttt.*
from (select t1.evl_main_id,
       t2.portfolio_no,
       t2.icg_no,
       t2.loa_code,
       t2.buss_source_code,
       t2.pl_judge_rslt,
       t2.dept_code,
       t2.product_code,
       t2.fin_detail_code,
       t2.fin_acc_channel,
       t2.sup_product_code,
       null,
       null,
       t2.treaty_no,
       t2.ri_dept,
       t2.risk_class,
       t2.center_code,
       sum(t2.os),
       sum(t2.ibnr),
       sum(t2.uale),
       sum(t2.ra),
       sum(case when t2.acc_time = \'C\' then t2.os end),
       sum(case when t2.acc_time = \'C\' then t2.ibnr end),
       sum(case when t2.acc_time = \'C\' then t2.uale end),
       sum(case when t2.acc_time = \'C\' then t2.ra end)
from qtc_buss_calc_alloc_cfg t1,
     qtc_dap_lic_ti_apt t2
where t1.evl_main_id = #evl_main_id#
  and t1.buss_model = \'TI\'
  and t2.year_month = t1.year_month
  and t2.entity_id = t1.entity_id
  and t2.currency_code = t1.currency_code
  and t2.loa_code = t1.loa_code
group by t1.evl_main_id,
       t2.portfolio_no,
       t2.icg_no,
       t2.loa_code,
       t2.buss_source_code,
       t2.pl_judge_rslt,
       t2.dept_code,
       t2.product_code,
       t2.fin_detail_code,
       t2.fin_acc_channel,
       t2.sup_product_code,
       t2.treaty_no,
       t2.ri_dept,
       t2.risk_class,
       t2.center_code) ttt ');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_TC_04', 5104, e'insert into qtc_temp_evl_alloc_calc(type_no, evl_main_id, portfolio_no, icg_no, var1, var2, var3, var4, var5, var6,
                                    var7, var8, var9, var10, var11, var12, var13,var14,num1, num2, num4,num5, num6, num7, num9, num10)
select \'TCA1\', ttt.*
from (select t1.evl_main_id,
       t2.portfolio_no,
       t2.icg_no,
       t2.loa_code,
       t2.buss_source_code,
       t2.pl_judge_rslt,
       t2.dept_code,
       t2.product_code,
       t2.fin_detail_code,
       t2.fin_acc_channel,
       t2.sup_product_code,
       t2.policy_no,
       t2.kind_code,
       t2.treaty_no,
       null,
       null,
       t2.center_code,
       sum(t2.os),
       sum(t2.ibnr),
       sum(t2.ra),
       sum(ntc),
        sum(case when t2.acc_time = \'C\' then t2.os end),
       sum(case when t2.acc_time = \'C\' then t2.ibnr end),
       sum(case when t2.acc_time = \'C\' then t2.ra end),
       sum(case when t2.acc_time = \'C\' then t2.ntc end)
from qtc_buss_calc_alloc_cfg t1,
     qtc_dap_lic_to_apt t2
where t1.evl_main_id = #evl_main_id#
  and t1.buss_model = \'TO\'
  and t2.year_month = t1.year_month
  and t2.entity_id = t1.entity_id
  and t2.currency_code = t1.currency_code
  and t2.loa_code = t1.loa_code
group by t1.evl_main_id,
       t2.portfolio_no,
       t2.icg_no,
       t2.loa_code,
       t2.buss_source_code,
       t2.pl_judge_rslt,
       t2.dept_code,
       t2.product_code,
       t2.fin_detail_code,
       t2.fin_acc_channel,
       t2.sup_product_code,
       t2.policy_no,
       t2.kind_code,
       t2.treaty_no,
       t2.center_code) ttt ');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_TC_03', 5103, e'insert into qtc_temp_evl_alloc_calc(type_no, evl_main_id, portfolio_no, icg_no, var1, var2, var3, var4, var5, var6,
                                    var7, var8, var9, var10, var11, var12, var13 ,var14, num1, num2, num4,num5, num6, num7, num9, num10)
select \'TCA1\', ttt.*
from (select t1.evl_main_id,
       t2.portfolio_no,
       t2.icg_no,
       t2.loa_code,
       t2.buss_source_code,
       t2.pl_judge_rslt,
       t2.dept_code,
       t2.product_code,
       t2.fin_detail_code,
       t2.fin_acc_channel,
       t2.sup_product_code,
       t2.policy_no,
       t2.kind_code,
       t2.treaty_no,
       null,
       null,
       t2.center_code,
       sum(t2.os),
       sum(t2.ibnr),
       sum(t2.ra),
       sum(ntc),
        sum(case when t2.acc_time = \'C\' then t2.os end),
       sum(case when t2.acc_time = \'C\' then t2.ibnr end),
       sum(case when t2.acc_time = \'C\' then t2.ra end),
       sum(case when t2.acc_time = \'C\' then t2.ntc end)
from qtc_buss_calc_alloc_cfg t1,
     qtc_dap_lic_fo_apt t2
where t1.evl_main_id = #evl_main_id#
  and t1.buss_model = \'FO\'
  and t2.year_month = t1.year_month
  and t2.entity_id = t1.entity_id
  and t2.currency_code = t1.currency_code
  and t2.loa_code = t1.loa_code
group by t1.evl_main_id,
       t2.portfolio_no,
       t2.icg_no,
       t2.loa_code,
       t2.buss_source_code,
       t2.pl_judge_rslt,
       t2.dept_code,
       t2.product_code,
       t2.fin_detail_code,
       t2.fin_acc_channel,
       t2.sup_product_code,
       t2.policy_no,
       t2.kind_code,
       t2.treaty_no,
       t2.center_code) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_TC_07', 5107, e'insert into qtc_temp_evl_alloc_calc(type_no, evl_main_id,  portfolio_no, icg_no, var1, var2, var3, var4, var5, var6,
                                    var7, var8, var9, var10, var11, var12, var13,var14,
                                    num1, num2, num3, num4, num5,num6,num7,num8,num9,num10 )
select  \'TCB1\',
       t1.evl_main_id,
       t1.portfolio_no,
       t1.icg_no,
       t1.var1,
       t1.var2,
       t1.var3,
       t1.var4,
       t1.var5,
       t1.var6,
       t1.var7,
       t1.var8,
       t1.var9,
       t1.var10,
       t1.var11,
       t1.var12,
       t1.var13,
       t1.var14,
       case when t2.num1 = 0 then 0 else t1.num1/ t2.num1 end ,
       case when t2.num2 = 0 then 0 else t1.num2/ t2.num2 end ,
       case when t2.num3 = 0 then 0 else t1.num3/ t2.num3 end ,
       case when t2.num4 = 0 then 0 else t1.num4/ t2.num4 end ,
       case when t2.num5 = 0 then 0 else t1.num5/ t2.num5 end ,
       case when t2.num6 = 0 then 0 else t1.num6/ t2.num6 end ,
       case when t2.num7 = 0 then 0 else t1.num7/ t2.num7 end ,
       case when t2.num8 = 0 then 0 else t1.num8/ t2.num8 end ,
       case when t2.num9 = 0 then 0 else t1.num9/ t2.num9 end ,
       case when t2.num10 = 0 then 0 else t1.num10/ t2.num10 end
from qtc_temp_evl_alloc_calc t1,
     qtc_buss_evl_lic_alloc_calc t2
where t1.evl_main_id = #evl_main_id#
  and t2.evl_main_id = t1.evl_main_id
  and t1.type_no = \'TCA1\'
  and t2.level = \'TGA1\'
  and t2.portfolio_no = t1.portfolio_no
  and t2.icg_no = t1.icg_no');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_TC_01', 5101, e'insert into qtc_temp_evl_alloc_calc(type_no, evl_main_id, portfolio_no, icg_no, var1, var2, var3, var4, var5, var6,
                                    var7, var8, var9, var10, var11, var12, var13 , var14, num1, num2, num3, num4, num6, num7, num8, num9)
select \'TCA1\', ttt.*
from (select t1.evl_main_id,
       t2.portfolio_no,
       t2.icg_no,
       t2.loa_code,
       t2.buss_source_code,
       t2.pl_judge_rslt,
       t2.dept_code,
       t2.product_code,
       t2.fin_detail_code,
       t2.fin_acc_channel,
       t2.sup_product_code,
       t2.policy_no,
       t2.kind_code,
       null,
       null,
       null,
       t2.center_code,
       sum(t2.os),
       sum(t2.ibnr),
       sum(t2.uale),
       sum(t2.ra),
       sum(case when t2.acc_time = \'C\' then t2.os end),
       sum(case when t2.acc_time = \'C\' then t2.ibnr end),
       sum(case when t2.acc_time = \'C\' then t2.uale end),
       sum(case when t2.acc_time = \'C\' then t2.ra end)
from qtc_buss_calc_alloc_cfg t1,
     qtc_dap_lic_dd_apt t2
where t1.evl_main_id = #evl_main_id#
  and t1.buss_model = \'DD\'
  and t2.year_month = t1.year_month
  and t2.entity_id = t1.entity_id
  and t2.currency_code = t1.currency_code
  and t2.loa_code = t1.loa_code
group by t1.evl_main_id,
       t2.portfolio_no,
       t2.icg_no,
       t2.loa_code,
       t2.buss_source_code,
       t2.pl_judge_rslt,
       t2.dept_code,
       t2.product_code,
       t2.fin_detail_code,
       t2.fin_acc_channel,
       t2.sup_product_code,
       t2.policy_no,
       t2.kind_code,
       t2.center_code) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_TC_08', 5108, e'insert into qtc_buss_evl_alloc_result(evl_main_id, year_month, var1, portfolio_no, icg_no, str1, str2, str3, str4, str5, str6,str7, str8,str9, str10, str11, str12, str13,str14,num1,num2,num3,num4,num5,num6,num7,num8,num9,num10,num11,num12,num13,num14,num15,num16, num17, num18, num19, num20, num21, num22, num23, num24,num25,num26,num27)
select t1.evl_main_id,\'202501\', t2.var1,t1.portfolio_no,t1.icg_no,t1.var1,t1.var2,t1.var3, t1.var4,t1.var5,t1.var6,t1.var7,t1.var8,t1.var9,t1.var10,t1.var11,t1.var12,t1.var13,t1.var14,
       t1.p1 ,
       t1.v6*t2.num2 ,
       coalesce(t1.v1*t2.num6, 0) - (coalesce(t1.p1, 0)+coalesce(t1.v6*t2.num2, 0)+coalesce(t1.v1*t2.num4, 0)+coalesce(t1.v1*t2.num5, 0)) ,
       t1.v1*t2.num4 ,
       t1.v1*t2.num5 ,
       t1.v1*t2.num6 ,
       t1.p2 ,
       t1.v7*t2.num8 ,
       coalesce(t1.v2*t2.num12, 0) - (coalesce(t1.p2, 0)+coalesce(t1.v7*t2.num8, 0)+coalesce(t1.v2*t2.num10, 0)+coalesce(t1.v2*t2.num11, 0)) ,
       t1.v2*t2.num10 ,
       t1.v2*t2.num11 ,
       t1.v2*t2.num12 ,
       t1.p3 ,
       t1.v8*t2.num14 ,
       coalesce(t1.v3*t2.num18, 0) - (coalesce(t1.p3, 0)+coalesce(t1.v8*t2.num14, 0)+coalesce(t1.v3*t2.num16, 0)+coalesce(t1.v3*t2.num17, 0)) ,
       t1.v3*t2.num16 ,
       t1.v3*t2.num17 ,
       t1.v3*t2.num18 ,
       t1.p4 ,
       t1.v9*t2.num20 ,
       coalesce(t1.v4*t2.num24, 0) - (coalesce(t1.p4, 0)+coalesce(t1.v9*t2.num20, 0)+coalesce(t1.v4*t2.num22, 0)+coalesce(t1.v4*t2.num23, 0)) ,
       t1.v4*t2.num22 ,
       t1.v4*t2.num23 ,
       t1.v4*t2.num24 ,
       t1.p5 ,
       coalesce(t1.v4*t2.num24, 0) - coalesce(t1.p5) ,
       t1.v5*t2.num27
from (select evl_main_id, portfolio_no,icg_no,var1,var2, var3,var4,var5,var6,var7,var8,var9,var10,var11,var12,var13,var14,
       max(case when type_no=\'TCB1\' then num1 end) v1,
       max(case when type_no=\'TCB1\' then num2 end) v2,
       max(case when type_no=\'TCB1\' then num3 end) v3,
       max(case when type_no=\'TCB1\' then num4 end) v4,
       max(case when type_no=\'TCB1\' then num5 end) v5,
       max(case when type_no=\'TCB1\' then num6 end) v6,
       max(case when type_no=\'TCB1\' then num7 end) v7,
       max(case when type_no=\'TCB1\' then num8 end) v8,
       max(case when type_no=\'TCB1\' then num9 end) v9,
       max(case when type_no=\'TCB1\' then num10 end) v10,
       max(case when type_no=\'TCA3\' then num1 end) p1,
       max(case when type_no=\'TCA3\' then num2 end) p2,
       max(case when type_no=\'TCA3\' then num3 end) p3,
       max(case when type_no=\'TCA3\' then num4 end) p4,
       max(case when type_no=\'TCA3\' then num5 end) p5
from qtc_temp_evl_alloc_calc
where evl_main_id = #evl_main_id#
  and type_no in (\'TCB1\', \'TCA3\')
group by evl_main_id,portfolio_no,icg_no, var1,var2,var3,var4,var5,var6, var7, var8, var9,var10, var11,var12,var13,var14) t1,
     qtc_buss_evaluate_result t2
where t2.evaluate_main_id = #evl_main_id#
  and t2.var1 = \'Lic\'
  and t2.icg_no = t1.icg_no');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_TC_05', 5105, e'insert into qtc_temp_evl_alloc_calc(type_no, evl_main_id, portfolio_no, icg_no, var1, var2, var3, var4, var5, var6,
                                    var7, var8, var9, var10, var11, var12, var13,var14, num1, num2, num3, num4, num5)
select \'TCA3\',
       t1.evl_main_id,
       t2.portfolio_no,
       t2.icg_no,
       t2.str1,
       t2.str2,
       t2.str3,
       t2.str4,
       t2.str5,
       t2.str6,
       t2.str7,
       t2.str8,
       t2.str9,
       t2.str10,
       t2.str11,
       t2.str12,
       t2.str13,
       t2.str14,
       t2.num6,
       t2.num12,
       t2.num18,
       t2.num24,
       t2.num27
from qtc_buss_calc_alloc_cfg t1,
     qtc_buss_evl_alloc_result t2
where t1.evl_main_id = #evl_main_id#
  and t1.rpt_is = \'1\'
  and t2.year_month = t1.ym_last
  and t2.evl_main_id = t1.evl_l_main_id
  and t2.var1 = \'Lic\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_C97', 3011, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'C97\',
       t1.evl_main_id,
       t2.icg_no,
       num1*(1-rpt_is)
from QTC_BUSS_CALC_CFG T1,
     QTC_BUSS_PAA_CAL_ICG T2
WHERE T1.EVL_MAIN_ID = #evl_main_id#
  AND T2.EVL_MAIN_ID = T1.EVL_L_MAIN_ID
  AND T2.TYPE_NO = \'OG\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_TR_E03', 5023, e'insert into qtc_temp_lrc_alloc_calc(evl_main_id, type_no, year_month, loa_code, portfolio_no, icg_no, str1, str2, str3, str4, str5, str6,str7, str8, str9, str10, str11, str12, str13 , str14,
                                    num1, num2, num3, num4, num5, num6,num7,num8,num9,num10, num11,num12)
select t1.evl_main_id,
       \'RE1\',
       t1.year_month,
       t2.loa_code,
       t2.portfolio_no,
       t2.icg_no,
       t2.loa_code,
       t2.buss_source_code,
       t2.pl_judge_rslt,
       t2.dept_code,
       t2.product_code,
       t2.fin_detail_code,
       t2.fin_acc_channel,
       t2.sup_product_code,
       t2.policy_no,
       t2.kind_code,
       t2.treaty_no,
       null,
       null,
       t2.center_code,
       t2.ed_premium,
       t2.ed_iacf,
       case when t1.ver_type = \'I\' then t2.ed_iacf_non_in else t2.ed_iacf_non  end ,
       t2.ed_net_charge,
       t2.ed_dec_fee,
       t2.re_premium*-1,
       t2.re_iacf*-1,
      case when t1.ver_type = \'I\' then t2.re_iacf_non_in*-1 else t2.re_iacf_non*-1  end ,
       t2.re_net_charge*-1,
       t2.re_dec_fee*-1 ,
       t2.cr_premium,
       t2.invest_amount
from qtc_buss_calc_alloc_cfg t1,
     qtc_dap_lrc_fo_apt t2
where t1.evl_main_id = #evl_main_id#
  and t1.buss_model = \'FO\'
  and t2.year_month = t1.year_month
  and t2.entity_id = t1.entity_id
  and t2.currency_code = t1.currency_code
  and t2.loa_code = t1.loa_code');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Z08', 3207, e'MERGE INTO QTC_BUSS_EVALUATE_RESULT TG
USING (SELECT EVL_MAIN_ID, ICG_NO, num1
           FROM QTC_BUSS_PAA_CAL_ICG
           WHERE EVL_MAIN_ID = #evl_main_id#
           AND TYPE_NO = \'OG\') SC
ON(TG.VAR1 = \'Lrc\' AND TG.EVALUATE_MAIN_ID = SC.EVL_MAIN_ID AND TG.ICG_NO = SC.ICG_NO)
WHEN MATCHED THEN UPDATE SET NUM29 = SC.NUM1');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_E10', 3022, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RE10\',
       T1.EVL_MAIN_ID,
       T2.ICG_NO,
       T2.invest_amount*t1.cf_dire
FROM QTC_BUSS_CALC_CFG T1,
     QTC_DAP_ECF_ICG T2
WHERE T1.EVL_MAIN_ID = #evl_main_id#
  AND T2.ENTITY_ID = T1.ENTITY_ID
  AND T2.CURRENCY_CODE = T1.CURRENCY_CODE
  AND T2.YEAR_MONTH = T1.YEAR_MONTH
  AND t2.BUSS_MODEL = t1.BUSS_MODEL
  and t2.loa_code = t1.loa_code');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Y09', 3108, e'insert into QTC_BUSS_PAA_CAL_ICG(CAL_ICG_ID, TYPE_NO, evl_main_id, ICG_NO, num1)
SELECT nextval(\'QTC_SEQ_BUSS_PAA_CAL_ICG\'),
       \'G1\',
       EVL_MAIN_ID,
       ICG_NO,
       NUM1
FROM QTC_BUSS_EVL_PAA_PRE
WHERE EVL_MAIN_ID = #evl_main_id#
  AND type_no = \'RE10\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_Y99', 3115, e'insert into QTC_BUSS_PAA_CAL_ICG(CAL_ICG_ID, TYPE_NO, evl_main_id, ICG_NO, num1)
select nextval(\'QTC_SEQ_BUSS_PAA_CAL_ICG\'),
       \'OG\',
       ttt.*
FROM (SELECT EVL_MAIN_ID, ICG_NO, SUM(NUM1)
      from QTC_BUSS_EVL_PAA_PRE
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO in (\'RE10\', \'C97\')
      GROUP BY EVL_MAIN_ID, ICG_NO) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_TR_E04', 5024, e'insert into qtc_temp_lrc_alloc_calc(evl_main_id, type_no,year_month, loa_code, portfolio_no, icg_no, str1, str2, str3, str4, str5, str6, str7, str8, str9, str10, str11, str12, str13 , str14,
                                    num1, num2, num3, num4, num5, num6,num7,num8,num9,num10, num11, num12)
select t1.evl_main_id,
       \'RE1\',
       t1.year_month,
       t2.loa_code,
       t2.portfolio_no,
       t2.icg_no,
       t2.loa_code ,
       t2.buss_source_code,
       t2.pl_judge_rslt,
       t2.dept_code,
       t2.product_code,
       t2.fin_detail_code,
       t2.fin_acc_channel,
       t2.sup_product_code,
       t2.policy_no,
       t2.kind_code,
       t2.treaty_no,
       null,
       null,
       t2.center_code,
       t2.ed_premium,
       t2.ed_iacf,
       case when t1.ver_type = \'I\' then t2.ed_iacf_non_in else t2.ed_iacf_non  end ,
       t2.ed_net_charge,
       t2.ed_dec_fee,
       t2.re_premium*-1,
       t2.re_iacf*-1,
       case when t1.ver_type = \'I\' then t2.re_iacf_non_in*-1 else t2.re_iacf_non*-1  end ,
       t2.re_net_charge*-1,
       t2.re_dec_fee*-1 ,
       t2.cr_premium,
       t2.invest_amount
from qtc_buss_calc_alloc_cfg t1,
     qtc_dap_lrc_to_apt t2
where t1.evl_main_id = #evl_main_id#
  and t1.buss_model = \'TO\'
  and t2.year_month = t1.year_month
  and t2.entity_id = t1.entity_id
  and t2.currency_code = t1.currency_code
  and t2.loa_code = t1.loa_code');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_F02', 3513, e'insert into qtc_buss_evl_lic_pre(lic_pre_id, type_no, evl_main_id, icg_no,
                                 num1, num2, num3, num4, num5, num6, num7, num8, num9, num10, num11)
select nextval(\'qtc_seq_buss_evl_lic_pre\'),
       \'IF2\',
       evl_main_id,
       icg_no,
       coalesce(num4, 0) + coalesce(num6, 0),
       coalesce(num10, 0) + coalesce(num12, 0),
       coalesce(num4, 0) + coalesce(num6, 0) + coalesce(num10, 0) + coalesce(num12, 0),
       num5,
       num11,
       coalesce(num3, 0) - coalesce(num2, 0),
       coalesce(num9, 0) - coalesce(num8, 0),
       coalesce(num2, 0) - coalesce(num1, 0) + coalesce(num8, 0) - coalesce(num7, 0) +
       coalesce(num4, 0) - coalesce(num3, 0) + coalesce(num10, 0) - coalesce(num9, 0) +
       coalesce(num6, 0) - coalesce(num5, 0) + coalesce(num12, 0) - coalesce(num11, 0),
       coalesce(num5, 0) - coalesce(num11, 0),
       coalesce(num3, 0) - coalesce(num2, 0) + coalesce(num9, 0) - coalesce(num8, 0),
       coalesce(num2, 0) - coalesce(num1, 0) + coalesce(num8, 0) - coalesce(num7, 0) +
       coalesce(num4, 0) - coalesce(num3, 0) + coalesce(num10, 0) - coalesce(num9, 0) +
       coalesce(num6, 0) - coalesce(num5, 0) + coalesce(num12, 0) - coalesce(num11, 0)
from qtc_buss_evl_lic_pre
where evl_main_id = #evl_main_id#
  and type_no = \'IE2\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_F01', 3512, e'insert into qtc_buss_evl_lic_pre(lic_pre_id, type_no, evl_main_id, icg_no,
                                 num1, num2, num3, num4, num5, num6, num7, num8, num9, num10, num11)
select nextval(\'qtc_seq_buss_evl_lic_pre\'),
       \'IF1\',
       evl_main_id,
       icg_no,
       coalesce(num4, 0) + coalesce(num6, 0),
       coalesce(num10, 0) + coalesce(num12, 0),
       coalesce(num4, 0) + coalesce(num6, 0) + coalesce(num10, 0) + coalesce(num12, 0),
       num5,
       num11,
       coalesce(num3, 0) - coalesce(num2, 0),
       coalesce(num9, 0) - coalesce(num8, 0),
       coalesce(num2, 0) - coalesce(num1, 0) + coalesce(num8, 0) - coalesce(num7, 0) +
       coalesce(num4, 0) - coalesce(num3, 0) + coalesce(num10, 0) - coalesce(num9, 0) +
       coalesce(num6, 0) - coalesce(num5, 0) + coalesce(num12, 0) - coalesce(num11, 0),
       coalesce(num5, 0) - coalesce(num11, 0),
       coalesce(num3, 0) - coalesce(num2, 0) + coalesce(num9, 0) - coalesce(num8, 0),
       coalesce(num2, 0) - coalesce(num1, 0) + coalesce(num8, 0) - coalesce(num7, 0) +
       coalesce(num4, 0) - coalesce(num3, 0) + coalesce(num10, 0) - coalesce(num9, 0) +
       coalesce(num6, 0) - coalesce(num5, 0) + coalesce(num12, 0) - coalesce(num11, 0)
from qtc_buss_evl_lic_pre
where evl_main_id = #evl_main_id#
  and type_no = \'IE1\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_F03', 3514, e'insert into qtc_buss_evl_lic_pre(lic_pre_id, type_no, evl_main_id, icg_no,
                                 num1, num2, num3, num4, num5, num6, num7, num8, num9, num10, num11)
select nextval(\'qtc_seq_buss_evl_lic_pre\'),
       \'IF3\',
       evl_main_id,
       icg_no,
       coalesce(num4, 0) + coalesce(num6, 0),
       coalesce(num10, 0) + coalesce(num12, 0),
       coalesce(num4, 0) + coalesce(num6, 0) + coalesce(num10, 0) + coalesce(num12, 0),
       num5,
       num11,
       coalesce(num3, 0) - coalesce(num2, 0),
       coalesce(num9, 0) - coalesce(num8, 0),
       coalesce(num2, 0) - coalesce(num1, 0) + coalesce(num8, 0) - coalesce(num7, 0) +
       coalesce(num4, 0) - coalesce(num3, 0) + coalesce(num10, 0) - coalesce(num9, 0) +
       coalesce(num6, 0) - coalesce(num5, 0) + coalesce(num12, 0) - coalesce(num11, 0),
       coalesce(num5, 0) - coalesce(num11, 0),
       coalesce(num3, 0) - coalesce(num2, 0) + coalesce(num9, 0) - coalesce(num8, 0),
       coalesce(num2, 0) - coalesce(num1, 0) + coalesce(num8, 0) - coalesce(num7, 0) +
       coalesce(num4, 0) - coalesce(num3, 0) + coalesce(num10, 0) - coalesce(num9, 0) +
       coalesce(num6, 0) - coalesce(num5, 0) + coalesce(num12, 0) - coalesce(num11, 0)
from qtc_buss_evl_lic_pre
where evl_main_id = #evl_main_id#
  and type_no = \'IE3\'');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LI_Y04', 3604, e'insert into QTC_BUSS_LIC_CAL_ICG(CAL_ICG_ID, TYPE_NO, EVL_MAIN_ID, ICG_NO, NUM1, NUM2, NUM3, NUM4, NUM5, NUM6)
select nextval(\'QTC_SEQ_BUSS_LIC_CAL_ICG\'),
       \'OK\',
       EVL_MAIN_ID,
       ICG_NO,
       coalesce(p5,0) ,
       coalesce(v1,0)+coalesce(p1,0),
       coalesce(v2,0)+coalesce(p2,0),
       coalesce(v3,0)+coalesce(p3,0),
       coalesce(v4,0)+coalesce(p4,0),
       coalesce(p5,0)+coalesce(v1,0)+coalesce(p1,0)+coalesce(v2,0)+coalesce(p2,0)+coalesce(v3,0)+coalesce(p3,0)+coalesce(v4,0)+coalesce(p4,0)
FROM (SELECT EVL_MAIN_ID,
             ICG_NO,
             SUM(case when TYPE_NO = \'IF4\' then NUM4 end)  v1,
             SUM(case when TYPE_NO = \'IF4\' then NUM5 end)  v2,
             SUM(case when TYPE_NO = \'IF4\' then NUM10 end) v3,
             SUM(case when TYPE_NO = \'IF4\' then NUM11 end) v4,
             SUM(case when TYPE_NO = \'IC4\' then NUM2 end) p1,
             SUM(case when TYPE_NO = \'IC4\' then NUM3 end)  p2,
             SUM(case when TYPE_NO = \'IC4\' then NUM4 end)  p3,
             SUM(case when TYPE_NO = \'IC4\' then NUM5 end)  p4,
             SUM(case when TYPE_NO = \'IC4\' then NUM6 end)  p5
      from qtc_buss_evl_lic_pre
      where EVL_MAIN_ID = #evl_main_id#
        and TYPE_NO in (\'IF4\', \'IC4\')
      GROUP BY EVL_MAIN_ID, ICG_NO) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_E01', 3015, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, ICG_NO, num1, num2, num3, num4, num5)
select nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'),
       \'RE1\',
       evl_main_id,
       icg_no,
       v1*cf_dire,
       v2*cf_dire*-1,
       case when ver_type = \'O\' then v3*cf_dire*-1 else v4*cf_dire*-1 end,
       v5*cf_dire*-1,
       v6*cf_dire*-1
from
(SELECT
       T1.EVL_MAIN_ID,
       T2.ICG_NO,
       t1.cf_dire,
       T1.ver_type,
       sum(T2.PREMIUM) v1,
       sum(T2.IACF_FEE) v2,
       sum(t2.IACF_FEE_NON) v3,
       sum(t2.iacf_fee_non_in) v4,
       sum(T2.NET_CHARGE) v5,
       sum(t2.dec_fee) v6
FROM QTC_BUSS_CALC_CFG T1,
     qtc_dap_ecf_icg_cf T2
WHERE T1.EVL_MAIN_ID = #evl_main_id#
  AND T2.ENTITY_ID = T1.ENTITY_ID
  AND T2.CURRENCY_CODE = T1.CURRENCY_CODE
  AND T2.YEAR_MONTH = T1.year_month
  AND t2.BUSS_MODEL = t1.BUSS_MODEL
  and t2.LOA_CODE = t1.loa_code
group by T1.EVL_MAIN_ID,T2.ICG_NO,t1.cf_dire,t1.ver_type) ttt');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_LR_B01', 3002, e'insert into QTC_BUSS_EVL_PAA_PRE(PAA_PRE_ID, TYPE_NO, evl_main_id, num1, num2, num3, num4, num5, num6, num7, num8, num9)
SELECT nextval(\'QTC_SEQ_BUSS_EVL_PAA_PRE\'), \'RB1\', #evl_main_id#, TTT.*
FROM (select max(case when t1.QUOTA_CODE = \'QT002\' then cast(t2.QUOTA_VALUE as numeric) end) v1,
             max(case when t1.QUOTA_CODE = \'QT003\' then cast(t2.QUOTA_VALUE as numeric) end) v2,
             max(case when t1.QUOTA_CODE = \'QT004\' then cast(t2.QUOTA_VALUE as numeric) end) v3,
             max(case when t1.QUOTA_CODE = \'QT005\' then cast(t2.QUOTA_VALUE as numeric) end) v4,
             max(case when t1.QUOTA_CODE = \'QT006\' then cast(t2.QUOTA_VALUE as numeric) end) v5,
             max(case when t1.QUOTA_CODE = \'QT007\' then cast(t2.QUOTA_VALUE as numeric) end) v6,
             max(case when t1.QUOTA_CODE = \'QT008\' then cast(t2.QUOTA_VALUE as numeric) end) v7,
             max(case when t1.QUOTA_CODE = \'QT009\' then cast(t2.QUOTA_VALUE as numeric) end) v8,
             max(case when t1.QUOTA_CODE = \'QT010\' then cast(t2.QUOTA_VALUE as numeric) end) v9
      from QTC_CONF_QUOTA_DEF t1,
           QTC_CONF_QUOTA t2
      where t1.QUOTA_CODE in (\'QT002\', \'QT003\', \'QT004\', \'QT005\', \'QT006\', \'QT007\', \'QT008\', \'QT009\', \'QT010\')
        and t2.QUOTA_DEF_ID = t1.QUOTA_DEF_ID
        and t2.DIMENSION = \'A\') TTT');
INSERT INTO qtcuser.qtc_conf_calc_sql (sql_code, seq_no, sql_select) VALUES ('MA_TR_C01', 5011, e'insert into qtc_temp_lrc_alloc_calc(evl_main_id, type_no, year_month, loa_code, portfolio_no, icg_no, str1, str2, str3, str4, str5, str6, str7, str8, str9, str10, str11, str12, str13, str14,
        num1, num2, num3, num4, num5, num6,num7,num8,num9,num10, num11,num12)
select t1.evl_main_id,\'RC1\', t1.year_month, t2.loa_code, t2.portfolio_no, t2.icg_no, t2.str1, t2.str2, t2.str3, str4, t2.str5, t2.str6,t2.str7, t2.str8, t2.str9, t2.str10, t2.str11, t2.str12, t2.str13 , t2.str14,
        t2.num1, t2.num2, t2.num3, t2.num4, t2.num5, t2.num6, t2.num7, t2.num8, t2.num9, t2.num10, t2.num11, t2.num12
from qtc_buss_calc_alloc_cfg t1,
     qtc_buss_lrc_alloc_calc t2
where t1.evl_main_id = #evl_main_id#
  and t1.rpt_is = \'0\'
  and t2.year_month = t1.ym_last
  and t2.evl_main_id = t1.evl_l_main_id
   and t2.type_no = \'RY1\'');

COMMIT;
