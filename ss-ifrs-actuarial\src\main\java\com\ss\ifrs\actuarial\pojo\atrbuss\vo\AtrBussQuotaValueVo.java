/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-03-08 16:40:45
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-03-08 16:40:45<br/>
 * Description: 假设值预存表(直保&临分分入)<br/>
 * Table Name: ATR_BUSS_DD_QUOTA_VALUE<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "假设值预存表(直保&临分分入)")
public class AtrBussQuotaValueVo implements Serializable {
    /**
     * Database column: ATR_BUSS_DD_QUOTA_VALUE.ID
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: ATR_BUSS_DD_QUOTA_VALUE.TASK_CODE
     * Database remarks: 任务号|一次计算分配一个TASK_CODE, 区别于其他批次的计算
     */
    @ApiModelProperty(value = "任务号|一次计算分配一个TASK_CODE, 区别于其他批次的计算", required = true)
    private String actionNo;

    /**
     * Database column: ATR_BUSS_DD_QUOTA_VALUE.CENTER_ID
     * Database remarks: 业务单位ID
     */
    @ApiModelProperty(value = "业务单位ID", required = true)
    private Long entityId;

    /**
     * Database column: ATR_BUSS_DD_QUOTA_VALUE.QUOTA_CODE
     * Database remarks: 假设值代码
     */
    @ApiModelProperty(value = "假设值代码", required = true)
    private String quotaCode;

    /**
     * Database column: ATR_BUSS_DD_QUOTA_VALUE.MODEL_DEF_ID
     * Database remarks: 计量模型
     */
    @ApiModelProperty(value = "业务类型", required = true)
    private String businessSourceCode;

    /**
     * Database column: ATR_BUSS_DD_QUOTA_VALUE.DIMENSION
     * Database remarks: 维度|G-合同组合、C-合同组、U-保单
     */
    @ApiModelProperty(value = "维度|G-合同组合、C-合同组、U-保单", required = true)
    private String dimension;

    /**
     * Database column: ATR_BUSS_DD_QUOTA_VALUE.DIMENSION_VALUE
     * Database remarks: 维度值
     */
    @ApiModelProperty(value = "维度值", required = false)
    private String dimensionValue;

    /**
     * Database column: ATR_BUSS_DD_QUOTA_VALUE.DEV_NO
     * Database remarks: 发展期
     */
    @ApiModelProperty(value = "发展期", required = false)
    private Long devNo;

    /**
     * Database column: ATR_BUSS_DD_QUOTA_VALUE.QUOTA_VALUE
     * Database remarks: 假设值
     */
    @ApiModelProperty(value = "假设值", required = false)
    private BigDecimal quotaValue;

    private String quotaType;

    private String riskClassCode;

    private String becfType;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }	

    public String getQuotaCode() {
        return quotaCode;
    }

    public void setQuotaCode(String quotaCode) {
        this.quotaCode = quotaCode;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getDimensionValue() {
        return dimensionValue;
    }

    public void setDimensionValue(String dimensionValue) {
        this.dimensionValue = dimensionValue;
    }

    public Long getDevNo() {
        return devNo;
    }

    public void setDevNo(Long devNo) {
        this.devNo = devNo;
    }

    public BigDecimal getQuotaValue() {
        return quotaValue;
    }

    public void setQuotaValue(BigDecimal quotaValue) {
        this.quotaValue = quotaValue;
    }

    public String getQuotaType() {
        return quotaType;
    }

    public void setQuotaType(String quotaType) {
        this.quotaType = quotaType;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public String getBecfType() {
        return becfType;
    }

    public void setBecfType(String becfType) {
        this.becfType = becfType;
    }
}