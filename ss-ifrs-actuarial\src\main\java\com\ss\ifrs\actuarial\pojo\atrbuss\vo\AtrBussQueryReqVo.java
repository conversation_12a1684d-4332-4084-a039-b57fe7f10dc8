package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/4/15
 */
public class AtrBussQueryReqVo {

    private Long lrcMainId;

    private Long licMainId;

    private Long entityId;

    private String yearMonth;


    private Date drawTime;

    private String versionNo;

    private String riskClassCode;

    private String currencyCode;

    private String year;

    private String icgNo;

    private String portfolioNo;

    private String lineType;

    public Long getLrcMainId() {
        return lrcMainId;
    }

    public void setLrcMainId(Long lrcMainId) {
        this.lrcMainId = lrcMainId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public Date getDrawTime() {
        return drawTime;
    }

    public void setDrawTime(Date drawTime) {
        this.drawTime = drawTime;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }

    public Long getLicMainId() {
        return licMainId;
    }

    public void setLicMainId(Long licMainId) {
        this.licMainId = licMainId;
    }

    public String getLineType() {
        return lineType;
    }

    public void setLineType(String lineType) {
        this.lineType = lineType;
    }
}
