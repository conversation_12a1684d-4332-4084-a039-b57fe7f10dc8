<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2022-03-28 17:44:37 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.conf.AtrConfCodeHisDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfCodeHis">
    <id column="code_his_id" property="codeHisId" jdbcType="NUMERIC" />
    <result column="code_id" property="codeId" jdbcType="NUMERIC" />
    <result column="upper_code_id" property="upperCodeId" jdbcType="NUMERIC" />
    <result column="code_code" property="codeCode" jdbcType="VARCHAR" />
    <result column="code_c_name" property="codeCName" jdbcType="VARCHAR" />
    <result column="code_l_name" property="codeLName" jdbcType="VARCHAR" />
    <result column="code_e_name" property="codeEName" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="display_no" property="displayNo" jdbcType="NUMERIC" />
    <result column="valid_is" property="validIs" jdbcType="VARCHAR" />
    <result column="creator_id" property="creatorId" jdbcType="NUMERIC" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="updator_id" property="updatorId" jdbcType="NUMERIC" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    code_his_id, code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name,
    remark, display_no, valid_is, creator_id, create_time, updator_id, update_time
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="codeHisId != null ">
          and code_his_id = #{codeHisId,jdbcType=NUMERIC}
      </if>
      <if test="codeId != null ">
          and code_id = #{codeId,jdbcType=NUMERIC}
      </if>
      <if test="upperCodeId != null ">
          and upper_code_id = #{upperCodeId,jdbcType=NUMERIC}
      </if>
      <if test="codeCode != null and codeCode != ''">
          and code_code = #{codeCode,jdbcType=VARCHAR}
      </if>
      <if test="codeCName != null and codeCName != ''">
          and code_c_name = #{codeCName,jdbcType=VARCHAR}
      </if>
      <if test="codeLName != null and codeLName != ''">
          and code_l_name = #{codeLName,jdbcType=VARCHAR}
      </if>
      <if test="codeEName != null and codeEName != ''">
          and code_e_name = #{codeEName,jdbcType=VARCHAR}
      </if>
      <if test="remark != null and remark != ''">
          and remark = #{remark,jdbcType=VARCHAR}
      </if>
      <if test="displayNo != null ">
          and display_no = #{displayNo,jdbcType=NUMERIC}
      </if>
      <if test="validIs != null and validIs != ''">
          and valid_is = #{validIs,jdbcType=VARCHAR}
      </if>
      <if test="creatorId != null ">
          and creator_id = #{creatorId,jdbcType=NUMERIC}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and updator_id = #{updatorId,jdbcType=NUMERIC}
      </if>
      <if test="updateTime != null ">
          and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.codeHisId != null ">
          and code_his_id = #{condition.codeHisId,jdbcType=NUMERIC}
      </if>
      <if test="condition.codeId != null ">
          and code_id = #{condition.codeId,jdbcType=NUMERIC}
      </if>
      <if test="condition.upperCodeId != null ">
          and upper_code_id = #{condition.upperCodeId,jdbcType=NUMERIC}
      </if>
      <if test="condition.codeCode != null and condition.codeCode != ''">
          and code_code = #{condition.codeCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.codeCName != null and condition.codeCName != ''">
          and code_c_name = #{condition.codeCName,jdbcType=VARCHAR}
      </if>
      <if test="condition.codeLName != null and condition.codeLName != ''">
          and code_l_name = #{condition.codeLName,jdbcType=VARCHAR}
      </if>
      <if test="condition.codeEName != null and condition.codeEName != ''">
          and code_e_name = #{condition.codeEName,jdbcType=VARCHAR}
      </if>
      <if test="condition.remark != null and condition.remark != ''">
          and remark = #{condition.remark,jdbcType=VARCHAR}
      </if>
      <if test="condition.displayNo != null ">
          and display_no = #{condition.displayNo,jdbcType=NUMERIC}
      </if>
      <if test="condition.validIs != null and condition.validIs != ''">
          and valid_is = #{condition.validIs,jdbcType=VARCHAR}
      </if>
      <if test="condition.creatorId != null ">
          and creator_id = #{condition.creatorId,jdbcType=NUMERIC}
      </if>
      <if test="condition.createTime != null ">
          and create_time = #{condition.createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.updatorId != null ">
          and updator_id = #{condition.updatorId,jdbcType=NUMERIC}
      </if>
      <if test="condition.updateTime != null ">
          and update_time = #{condition.updateTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="codeHisId != null ">
          and code_his_id = #{codeHisId,jdbcType=NUMERIC}
      </if>
      <if test="codeId != null ">
          and code_id = #{codeId,jdbcType=NUMERIC}
      </if>
      <if test="upperCodeId != null ">
          and upper_code_id = #{upperCodeId,jdbcType=NUMERIC}
      </if>
      <if test="codeCode != null and codeCode != ''">
          and code_code = #{codeCode,jdbcType=VARCHAR}
      </if>
      <if test="codeCName != null and codeCName != ''">
          and code_c_name = #{codeCName,jdbcType=VARCHAR}
      </if>
      <if test="codeLName != null and codeLName != ''">
          and code_l_name = #{codeLName,jdbcType=VARCHAR}
      </if>
      <if test="codeEName != null and codeEName != ''">
          and code_e_name = #{codeEName,jdbcType=VARCHAR}
      </if>
      <if test="remark != null and remark != ''">
          and remark = #{remark,jdbcType=VARCHAR}
      </if>
      <if test="displayNo != null ">
          and display_no = #{displayNo,jdbcType=NUMERIC}
      </if>
      <if test="validIs != null and validIs != ''">
          and valid_is = #{validIs,jdbcType=VARCHAR}
      </if>
      <if test="creatorId != null ">
          and creator_id = #{creatorId,jdbcType=NUMERIC}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and updator_id = #{updatorId,jdbcType=NUMERIC}
      </if>
      <if test="updateTime != null ">
          and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_code_his
    where code_his_id = #{codeHisId,jdbcType=NUMERIC}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_code_his
    where code_his_id in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_code_his
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfCodeHis">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_code_his
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from atr_conf_code_his
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="code_his_id" keyProperty="codeHisId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfCodeHis">
    <selectKey resultType="long" keyProperty="codeHisId" order="BEFORE">
      select nextval('atr_seq_conf_code_his') as sequenceNo 
    </selectKey>
    insert into atr_conf_code_his
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="codeHisId != null">
        code_his_id,
      </if>
      <if test="codeId != null">
        code_id,
      </if>
      <if test="upperCodeId != null">
        upper_code_id,
      </if>
      <if test="codeCode != null">
        code_code,
      </if>
      <if test="codeCName != null">
        code_c_name,
      </if>
      <if test="codeLName != null">
        code_l_name,
      </if>
      <if test="codeEName != null">
        code_e_name,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="displayNo != null">
        display_no,
      </if>
      <if test="validIs != null">
        valid_is,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updatorId != null">
        updator_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="codeHisId != null">
        #{codeHisId,jdbcType=NUMERIC},
      </if>
      <if test="codeId != null">
        #{codeId,jdbcType=NUMERIC},
      </if>
      <if test="upperCodeId != null">
        #{upperCodeId,jdbcType=NUMERIC},
      </if>
      <if test="codeCode != null">
        #{codeCode,jdbcType=VARCHAR},
      </if>
      <if test="codeCName != null">
        #{codeCName,jdbcType=VARCHAR},
      </if>
      <if test="codeLName != null">
        #{codeLName,jdbcType=VARCHAR},
      </if>
      <if test="codeEName != null">
        #{codeEName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="displayNo != null">
        #{displayNo,jdbcType=NUMERIC},
      </if>
      <if test="validIs != null">
        #{validIs,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=NUMERIC},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        #{updatorId,jdbcType=NUMERIC},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert into atr_conf_code_his
     (code_his_id, code_id, upper_code_id, 
      code_code, code_c_name, code_l_name,
      code_e_name, remark, display_no, 
      valid_is, creator_id, create_time, 
      updator_id, update_time)
     values 
    <foreach collection="list" item="item" index="index" separator=",">
       (#{item.codeHisId,jdbcType=NUMERIC}, #{item.codeId,jdbcType=NUMERIC}, #{item.upperCodeId,jdbcType=NUMERIC}, 
        #{item.codeCode,jdbcType=VARCHAR}, #{item.codeCName,jdbcType=VARCHAR}, #{item.codeLName,jdbcType=VARCHAR},
        #{item.codeEName,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.displayNo,jdbcType=NUMERIC}, 
        #{item.validIs,jdbcType=VARCHAR}, #{item.creatorId,jdbcType=NUMERIC}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updatorId,jdbcType=NUMERIC}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfCodeHis">
    update atr_conf_code_his
    <set>
      <if test="codeId != null">
        code_id = #{codeId,jdbcType=NUMERIC},
      </if>
      <if test="upperCodeId != null">
        upper_code_id = #{upperCodeId,jdbcType=NUMERIC},
      </if>
      <if test="codeCode != null">
        code_code = #{codeCode,jdbcType=VARCHAR},
      </if>
      <if test="codeCName != null">
        code_c_name = #{codeCName,jdbcType=VARCHAR},
      </if>
      <if test="codeLName != null">
        code_l_name = #{codeLName,jdbcType=VARCHAR},
      </if>
      <if test="codeEName != null">
        code_e_name = #{codeEName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="displayNo != null">
        display_no = #{displayNo,jdbcType=NUMERIC},
      </if>
      <if test="validIs != null">
        valid_is = #{validIs,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=NUMERIC},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        updator_id = #{updatorId,jdbcType=NUMERIC},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where code_his_id = #{codeHisId,jdbcType=NUMERIC}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfCodeHis">
    update atr_conf_code_his
    <set>
      <if test="record.codeId != null">
        code_id = #{record.codeId,jdbcType=NUMERIC},
      </if>
      <if test="record.upperCodeId != null">
        upper_code_id = #{record.upperCodeId,jdbcType=NUMERIC},
      </if>
      <if test="record.codeCode != null">
        code_code = #{record.codeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.codeCName != null">
        code_c_name = #{record.codeCName,jdbcType=VARCHAR},
      </if>
      <if test="record.codeLName != null">
        code_l_name = #{record.codeLName,jdbcType=VARCHAR},
      </if>
      <if test="record.codeEName != null">
        code_e_name = #{record.codeEName,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.displayNo != null">
        display_no = #{record.displayNo,jdbcType=NUMERIC},
      </if>
      <if test="record.validIs != null">
        valid_is = #{record.validIs,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=NUMERIC},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatorId != null">
        updator_id = #{record.updatorId,jdbcType=NUMERIC},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from atr_conf_code_his
    where code_his_id = #{codeHisId,jdbcType=NUMERIC}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from atr_conf_code_his
    where code_his_id in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from atr_conf_code_his
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfCodeHis">
    select count(1) from atr_conf_code_his
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>