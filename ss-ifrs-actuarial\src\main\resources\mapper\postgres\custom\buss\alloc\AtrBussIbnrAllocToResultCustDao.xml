<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by Taiping MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2025-03-24 19:27:36 -->
<!-- Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.alloc.AtrBussIbnrAllocToResultDao">
  <!-- 本配置文件由Taiping MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**BaseDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->

  <insert id="saveDuctAllocEp">
    INSERT INTO atr_duct_ibnr_alloc_ep_out ( ID, action_no, data_type, entity_id, year_month, acc_year_month,accident_quarter,
       portfolio_no,icg_no, icg_no_name, treaty_no, treaty_name,
       center_code,risk_class_code, policy_no, kind_code, ep_amount )
    SELECT
      nextval( 'atr_seq_duct_ibnr_alloc_ep_out' ) AS ID,
      #{allocAction.actionNo,jdbcType=VARCHAR} as action_no,
      '1' as data_type,
      bla.entity_id,
      bla.year_month,
      bla.year_month,
      TO_CHAR(to_date(bla.year_month, 'YYYYMM'), 'YYYY"Q"Q'),
      bdl.portfolio_no,
      bdl.icg_no,
      '' as icg_no_name,
      bdl.treaty_no,
      '' as treaty_name,
      bdl.center_code,
      bdl.risk_class_code,
      bdl.policy_no,
      bdl.kind_code,
      bdld.ed_premium ep_amount
    FROM atr_buss_lrc_action bla,
         atr_buss_to_lrc_t_ul bdl,
         atr_buss_to_lrc_t_ul_dev bdld
    WHERE bla.entity_id = #{allocAction.entityId,jdbcType=DECIMAL}
      AND bla.business_source_code = 'TO'
      AND bla.confirm_is='1'
      AND bla.year_month between substr(${allocAction.yearMonth}||'',1,4)||'01' and #{allocAction.yearMonth,jdbcType=VARCHAR}
      AND bla.action_no = bdl.action_no
      AND bdl.id = bdld.main_id
      AND bdld.dev_no=0
  </insert>

  <insert id="saveDuctAllocEpSum">
    INSERT INTO atr_duct_ibnr_alloc_ep_out ( ID, action_no, data_type, entity_id, year_month, acc_year_month,accident_quarter,
      portfolio_no,icg_no, icg_no_name, treaty_no,treaty_name,
      center_code, risk_class_code, policy_no, kind_code, ep_amount, ep_sum_amount )
    SELECT
      nextval( 'atr_seq_duct_ibnr_alloc_ep_out' ) AS ID,
      diae.action_no,
      '2' as data_type,
      diae.entity_id,
      diae.year_month,
      diae.acc_year_month,
      diae.accident_quarter,
      diae.portfolio_no,
      diae.icg_no,
      diae.icg_no_name,
      diae.treaty_no,
      diae.treaty_name,
      diae.center_code,
      diae.risk_class_code,
      diae.policy_no,
      diae.kind_code,
      diae.ep_amount,
      SUM(ep_amount) OVER (PARTITION BY entity_id,year_month,accident_quarter,center_code,risk_class_code) AS ep_sum_amount
    FROM
      atr_duct_ibnr_alloc_ep_out diae
    WHERE diae.action_no = #{actionNo,jdbcType=VARCHAR} and data_type='1'
  </insert>

  <insert id="saveDuctImportSum">
    INSERT INTO atr_duct_ibnr_import_ep (
      ibnr_main_id,
      action_no,
      business_model,
      year_month,
      accident_quarter,
      risk_class_code,
      center_code,
      ri_ibnr_amount,
      create_time
    )
    select  ibnr_main_id,
            #{allocAction.actionNo,jdbcType=VARCHAR} as action_no,
            business_model,
            year_month,
            accident_quarter,
            risk_class_code,
            center_code,
            ri_ibnr_amount,
            LOCALTIMESTAMP as create_time
    from (
           SELECT
             ibnr_main_id,
             business_model,
             risk_class_code,
             year_month,
             accident_quarter,
             center_code,
             SUM (ri_ibnr_amount) AS ri_ibnr_amount,
             ROW_NUMBER() OVER (PARTITION BY ibnr_main_id, business_model,center_code, risk_class_code, accident_quarter ORDER BY year_month DESC ) AS rn
           FROM atr_buss_ibnr_import_detail biid
           where ibnr_main_id = #{ibnrImport.ibnrMainId,jdbcType=DECIMAL}
             and business_model='TO'
           group by ibnr_main_id,business_model,risk_class_code, accident_quarter,year_month,center_code
         ) t where rn=1
  </insert>

  <insert id="saveBussAllocData">
    INSERT INTO atr_buss_ibnr_alloc_to_result (
      ID,
      action_no,
      entity_id,
      year_month,
      portfolio_no,
      icg_no,
      icg_no_name,
      acc_year_month,
      treaty_no,
      treaty_name,
      risk_class_code,
      center_code,
      policy_no,
      kind_code,
      ep_amount,
      ep_ratio,
      ibnr_amount,
      create_time
    )
    SELECT
      nextval( 'atr_seq_buss_ibnr_alloc_to_result' ) AS ID,
      biaa.action_no,
      biaa.entity_id,
      biaa.year_month,
      diae.portfolio_no,
      diae.icg_no,
      diae.icg_no_name,
      diae.acc_year_month,
      diae.treaty_no,
      diae.treaty_name,
      diae.risk_class_code,
      diae.center_code,
      diae.policy_no,
      diae.kind_code,
      diae.ep_amount,
      round(diae.ep_amount/diae.ep_sum_amount,8),
      biid.ri_ibnr_amount * round(diae.ep_amount/diae.ep_sum_amount,8),
      LOCALTIMESTAMP as create_time
    FROM atr_buss_ibnr_alloc_action biaa
     LEFT JOIN atr_duct_ibnr_import_ep biid ON
        biaa.action_no=biid.action_no and  biaa.business_source_code = biid.business_model
    LEFT JOIN atr_duct_ibnr_alloc_ep_out diae
         on biaa.entity_id=diae.entity_id
        and biaa.action_no=diae.action_no
        and biid.risk_class_code=diae.risk_class_code
        and biid.accident_quarter=diae.accident_quarter
        and biid.center_code =diae.center_code
    WHERE biaa.action_no = #{actionNo,jdbcType=VARCHAR}
      and data_type='2'
      and diae.ep_sum_amount!=0
  </insert>

  <insert id="saveBussAllocDataTrans">
    INSERT INTO atr_buss_ibnr_alloc_to_result (
      ID,
      action_no,
      entity_id,
      year_month,
      acc_year_month,
      portfolio_no,
      icg_no,
      policy_no,
      kind_code,
      ep_amount,
      ibnr_amount,
      create_time
    )
    SELECT
      nextval( 'atr_seq_buss_ibnr_alloc_to_result' ) AS ID,
      biaa.action_no,
      biaa.entity_id,
      biaa.year_month,
      ddlo.acc_year_month,
      ddlo.portfolio_no,
      ddlo.icg_no,
      ddlo.policy_no,
      ddlo.kind_code,
      ddlo.amount,
      ddlo.amount*biim.provision_ratio as ibnr_amount,
      LOCALTIMESTAMP as create_time
    FROM atr_buss_ibnr_alloc_action biaa
           LEFT JOIN atr_buss_ibnr_import_main biim ON biim.ibnr_main_id = #{ibnrImport.ibnrMainId,jdbcType=DECIMAL}
           LEFT JOIN atr_dap_to_os_t ddlo ON biaa.year_month = ddlo.year_month
    WHERE diae.action_no = #{allocAction.actionNo,jdbcType=VARCHAR}
      and ddlo.acc_year_month between
      to_char(to_date(biaa.year_month,'yyyymm') - (25 * INTERVAL '3 months'),'yyyymm')
      and biaa.year_month
  </insert>

  <insert id="saveBussAllocDataTransDm">
    INSERT INTO atr_buss_ibnr_alloc_to_result (
      ID,
      action_no,
      entity_id,
      year_month,
      acc_year_month,
      portfolio_no,
      icg_no,
      policy_no,
      kind_code,
      ep_amount,
      ibnr_amount,
      create_time
    )
    SELECT
      nextval( 'atr_seq_buss_ibnr_alloc_to_result' ) AS ID,
      biaa.action_no,
      biaa.entity_id,
      biaa.year_month,
      ddlo.acc_year_month,
      ddlo.portfolio_no,
      ddlo.icg_no,
      ddlo.policy_no,
      ddlo.kind_code,
      ddlo.amount,
      ddlo.amount*biim.provision_ratio as ibnr_amount,
      LOCALTIMESTAMP as create_time
    FROM atr_buss_ibnr_alloc_action biaa
           LEFT JOIN atr_buss_ibnr_import_main biim ON biim.ibnr_main_id = #{ibnrImport.ibnrMainId,jdbcType=DECIMAL}
           LEFT JOIN (SELECT
                        to_char( accident_date_time, 'yyyymm' ) AS acc_year_month,
                        dd.portfolio_no,
                        dd.icg_no,
                        T.policy_no,
                        dd.risk_class_code,
                        dd.risk_class_code AS kind_code,
                        T.outstanding_amount AS amount
                      FROM dmuser.dm_claim_outstanding T LEFT JOIN dmuser.dm_buss_cmunit_treaty_outward dd ON T.ri_policy_no = dd.treaty_no
                      WHERE t.ri_direction_code = 'O'
                        and dd.year_month is not null) ddlo ON 1 = 1
    WHERE biaa.action_no = #{allocAction.actionNo,jdbcType=VARCHAR}
      and ddlo.acc_year_month between
      to_char(to_date(biaa.year_month,'yyyymm') - (25 * INTERVAL '3 months'),'yyyymm')
      and biaa.year_month
  </insert>
  <resultMap id="ResultVoResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrAllocResultVo">
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="action_no" property="actionNo" jdbcType="VARCHAR" />
    <result column="entity_id" property="entityId" jdbcType="BIGINT" />
    <result column="year_month" property="yearMonth" jdbcType="VARCHAR" />
    <result column="portfolio_no" property="portfolioNo" jdbcType="VARCHAR" />
    <result column="icg_no" property="icgNo" jdbcType="VARCHAR" />
    <result column="acc_year_month" property="accYearMonth" jdbcType="VARCHAR" />
    <result column="treaty_no" property="treatyNo" jdbcType="VARCHAR" />
    <result column="risk_class_code" property="riskClassCode" jdbcType="VARCHAR" />
    <result column="center_code" property="centerCode" jdbcType="VARCHAR" />
    <result column="policy_no" property="policyNo" jdbcType="VARCHAR" />
    <result column="kind_code" property="kindCode" jdbcType="VARCHAR" />
    <result column="ep_amount" property="epAmount" jdbcType="NUMERIC" />
    <result column="ep_ratio" property="epRatio" jdbcType="NUMERIC" />
    <result column="ibnr_amount" property="ibnrAmount" jdbcType="NUMERIC" />
    <result column="creator_id" property="creatorId" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <select id="findAllocList" resultMap="ResultVoResultMap">
    select biaa.action_no,  biaa.year_month,biadr.acc_year_month,biadr.portfolio_no,
           biadr.icg_no,biadr.treaty_no,biadr.risk_class_code, biadr.center_code,
           biadr.policy_no, biadr.kind_code,biadr.ep_amount,biadr.ep_ratio, biadr.ibnr_amount
    FROM atr_buss_ibnr_alloc_action biaa,
        atr_buss_ibnr_alloc_to_result biadr
    where biaa.action_No = biadr.action_No
    <choose>
      <when test="actionNo != null">
        and biaa.action_no = #{actionNo,jdbcType=VARCHAR}
      </when>
      <otherwise>
        <if test="entityId != null ">
          and biaa.entity_id = #{entityId,jdbcType=BIGINT}
        </if>
        <if test="yearMonth != null and yearMonth != ''">
          and biaa.year_month = #{yearMonth,jdbcType=VARCHAR}
        </if>
        <if test="yearMonthStart != null and yearMonthStart != ''">
          and biaa.year_month >= #{yearMonthStart}
        </if>
        <if test="yearMonthEnd != null and yearMonthEnd != ''">
          and biaa.year_month  <![CDATA[ <= ]]> #{yearMonthEnd}
        </if>
        <if test="businessSourceCode != null and businessSourceCode != ''">
          and biaa.business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != ''">
          and biaa.STATUS = #{status,jdbcType=VARCHAR}
        </if>
        <if test="confirmIs != null and confirmIs != ''">
          and biaa.CONFIRM_IS = #{confirmIs,jdbcType=VARCHAR}
        </if>
      </otherwise>
    </choose>
  </select>

  <delete id="deleteDapIbnr">
    delete from atr_dap_to_ibnr_t
    where entity_id = #{entityId,jdbcType=BIGINT}
      and year_month = #{yearMonth,jdbcType=VARCHAR}
  </delete>

  <insert id="confirm">
    INSERT INTO atr_dap_to_ibnr_t (
      year_month,
      entity_id,
      acc_year_month,
      portfolio_no,
      icg_no,
      risk_class_code,
      treaty_no,
      policy_no,
      kind_code,
      amount,
      draw_time
    )
    SELECT
      biaa.year_month,
      biaa.entity_id,
      biadr.acc_year_month,
      biadr.portfolio_no,
      biadr.icg_no,
      biadr.risk_class_code,
      biadr.treaty_no,
      biadr.policy_no,
      biadr.kind_code,
      biadr.ibnr_amount,
      LOCALTIMESTAMP as create_time
    FROM atr_buss_ibnr_alloc_action biaa
           LEFT JOIN atr_buss_ibnr_alloc_to_result  biadr ON biadr.action_no =biaa.action_no
    WHERE biaa.confirm_is='1'
      and biadr.action_no is not null
    <choose>
      <when test="actionNo != null">
        and biaa.action_no = #{actionNo,jdbcType=VARCHAR}
      </when>
      <otherwise>
        <if test="entityId != null ">
          and biaa.entity_id = #{entityId,jdbcType=BIGINT}
        </if>
        <if test="yearMonth != null and yearMonth != ''">
          and biaa.year_month = #{yearMonth,jdbcType=VARCHAR}
        </if>
        <if test="businessSourceCode != null and businessSourceCode != ''">
          and biaa.business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
        </if>
      </otherwise>
    </choose>
  </insert>
</mapper>