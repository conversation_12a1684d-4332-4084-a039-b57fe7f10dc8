package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodBackVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodDetailVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.ifrs.actuarial.pojo.domain.vo.buss.BussPeriodReqVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.springframework.transaction.UnexpectedRollbackException;

import java.util.List;
import java.util.Map;

public interface AtrConfBussPeriodService {

	AtrConfBussPeriodVo findCurrentPeriod(AtrConfBussPeriodVo vo);

	public Page<AtrConfBussPeriodVo> findForDataTables(AtrConfBussPeriodVo vo, Pageable pageParam);

	/**
	 * 验证业务年月是否可执行业务操作
	 */
	Boolean verifyBussPeriodIsProcessing(Long entityId, String yearMonth, String bizCode);

	/**
	 * 验证业务年月是否有效的业务期间
	 */
	Boolean verifyBussPeriodIsValid(Long entityId, String yearMonth);

	public String updateValidIsNext(AtrConfBussPeriodVo atrConfBussPeriodVo, Long userId)
			throws UnexpectedRollbackException;

	AtrConfBussPeriodVo findBefore(AtrConfBussPeriodVo atrConfBussPeriodVo);

	public String updateValidIs(AtrConfBussPeriodVo atrConfBussPeriodVo, Long userId)
			throws UnexpectedRollbackException;

	public List<AtrConfBussPeriodVo> findOwActionLog(AtrConfBussPeriodVo vo);

	public AtrConfBussPeriodVo getOwBussPeriod(AtrConfBussPeriodVo vo);

	List<AtrConfBussPeriodVo> findBussPeriodVosForTaskJob(Long entityId, String periodState);

	void syncPeriodStatus(Long entityId, String periodState, String bizCode);
	
	void syncPeriodStatusByEntity(Long entityId, String periodState);

	List<AtrConfBussPeriodVo> findValidPeriodList();

	//计量递延期间
	String executionPeriod(Long entityId, String yearMonth, String bizCode);

	void updatePeriodDetail(AtrConfBussPeriodDetailVo atrConfBussPeriodVo);

	Boolean reOpenBussPeriod(AtrConfBussPeriodBackVo confBussPeriodVo);

	Boolean checkYearMonthExecutable(BussPeriodReqVo bussPeriodReqVo);

	void exampleModifyTheCurrentTaskStatus(AtrConfBussPeriodVo atrConfBussPeriodVo);


	void syncPeriodStatus(Long entityId, String state);

	/**
	 * @Description: 检查是否可以重开财务账期
	 * @Param : [qtcConfBussPeriodVo]
	 * @Return: java.lang.String
	 * @Author: wyh.
	 */
	Map<String, Object> checkReOpenAccountPeriod(AtrConfBussPeriodVo atrConfBussPeriodVo);


	void reOpenAccountPeriod(AtrConfBussPeriodVo atrConfBussPeriodVo);
}
