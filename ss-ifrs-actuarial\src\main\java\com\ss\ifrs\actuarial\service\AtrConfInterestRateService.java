package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestBase;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfInterestRateVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface AtrConfInterestRateService {

    void importDomesticInterestRate(MultipartFile file, AtrConfInterestRateVo AtrConfInterestRateVo, Long userId) throws Exception;

    void importForeignInterestRate(MultipartFile file, AtrConfInterestRateVo AtrConfInterestRateVo, Long userId) throws Exception;


    AtrConfInterestRateVo findById(Long interestRateId);

    Map<String, Object> findBussResultById(Long interestRateId);

    Page<AtrConfInterestRateVo> searchPage(AtrConfInterestRateVo atrConfInterestRateVo, Pageable pageParam);

    void delete(Long interestRateId, Long userId);

    void updateValid(AtrConfInterestRateVo dmConfigCheckRuleVo, Long userId);

    void confirm(AtrConfInterestRateVo dmConfigCheckRuleVo, Long userId);

    void auditList(List<AtrConfInterestRateVo> atrConfInterestRateVos, Long userId);


    void update(AtrConfInterestRateVo atrConfInterestRateVo, Long userId);

    void saveList(List<AtrConfInterestRateVo> atrConfInterestRateVos, Long userId);

    List<AtrConfInterestRateVo> findList(AtrConfInterestRateVo atrConfInterestRateVo);

    void downloadInterest(HttpServletRequest request, HttpServletResponse response, AtrConfInterestRateVo atrConfInterestRateVo, Long userId) throws Exception;

    void downloadTemplate(HttpServletRequest request, HttpServletResponse response, AtrConfInterestRateVo AtrConfInterestRateVo, Long userId) throws Exception;

    void saveOrUpdateInterestBase(List<AtrConfInterestBase> atrConfInterestBase,Long userId);

    List<AtrConfInterestBase> listInterestBase();

}
