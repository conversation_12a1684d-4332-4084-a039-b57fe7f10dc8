package com.ss.ifrs.actuarial.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.data.DataFormatData;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.google.common.collect.Lists;
import com.ss.ifrs.actuarial.conf.AppConfig;
import com.ss.ifrs.actuarial.dao.AtrBussIbnrImportMainDao;
import com.ss.ifrs.actuarial.dao.buss.reserve.AtrBussReserveIbnrDao;
import com.ss.ifrs.actuarial.dao.buss.reserve.AtrBussReserveIbnrDetailDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfCodeDao;
import com.ss.ifrs.actuarial.feign.BbsCurrencyClient;
import com.ss.ifrs.actuarial.feign.BbsRiskClassFeignClient;
import com.ss.ifrs.actuarial.feign.BbsConfEntityFeignClient;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveIbnr;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveIbnrDetail;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveIbnrDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveIbnrExcelVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveIbnrVo;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrExcelHeaderVo;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrExcelVo;
import com.ss.ifrs.actuarial.service.*;
import com.ss.ifrs.actuarial.util.AtrExcelIbnrImportUtil;
import com.ss.platform.pojo.bbs.vo.BbsConfEntityVo;
import com.ss.platform.pojo.bbs.vo.BbsConfRiskClassVo;
import com.ss.platform.pojo.bbs.vo.BbsConfCurrencyVo;
import com.ss.library.excel.ExcelSheet;
import com.ss.library.excel.ExcelSheetData;
import com.ss.ifrs.actuarial.constant.ActuarialConstant;
import com.ss.platform.core.constant.LocalesConstanst;
import com.ss.platform.core.model.SsException;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ClassUtil;
import com.ss.library.utils.DateUtil;
import com.ss.library.utils.FileUtil;
import com.ss.library.utils.StringUtil;
import org.apache.commons.fileupload.FileUploadException;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.poi.hssf.usermodel.HSSFPalette;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class AtrBussReserveIbnrServiceImpl implements AtrBussReserveIbnrService {

	private static final int POSITION_ROW = 1;
	private static final int POSITION_COL = 1;

	final Logger LOG = LoggerFactory.getLogger(getClass());

	@Autowired
	AppConfig appConfig;

	@Autowired(required = false)
	private BbsConfEntityFeignClient bbsConfEntityFeignClient;

	@Autowired(required = false)
	private BbsRiskClassFeignClient bbsRiskClassFeignClient;

	@Autowired(required = false)
	private BbsCurrencyClient bbsCurrencyClient;

	@Autowired
	private AtrBussReserveIbnrDao atrBussReserveIbnrDao;

	@Autowired
	private AtrBussReserveIbnrDetailDao atrBussReserveIbnrDetailDao;

	@Autowired
	AtrBussReserveIbnrDetailService atrBussReserveIbnrDetailService;


	@Autowired
	private AtrConfExcelService atrConfExcelService;

	@Autowired
	private AtrImportService atrImportService;

	@Autowired
	AtrExportService atrExportService;


	@Autowired
	private AtrConfCodeDao atrConfCodeDao;

	@Autowired
	private AtrBussIbnrImportMainDao atrBussIbnrImportMainDao;

	@Override
	public Page<AtrBussReserveIbnrVo> searchPage(AtrBussReserveIbnrVo atrBussReserveIbnrVo, Pageable pageParam) {
		Page<AtrBussReserveIbnrVo> atrBussReserveIbnrVoPage = atrBussReserveIbnrDao.fuzzySearchPage(atrBussReserveIbnrVo, pageParam);
		return  atrBussReserveIbnrVoPage;
	}

	@Override
	public AtrBussReserveIbnrVo findById(Long reserveIbnrId) {
		return atrBussReserveIbnrDao.findByReserveIbnrId(reserveIbnrId);
	}

	@Override
	@Async("ruleThreadPool")
	@Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = RuntimeException.class)
	public void save(AtrBussReserveIbnrVo atrBussReserveIbnrVo, Long userId) {
		AtrBussReserveIbnr po = ClassUtil.convert(atrBussReserveIbnrVo, AtrBussReserveIbnr.class);
		// 正常提取
		po.setDrawType("1");
		po.setDrawStatus("1");
		po.setConfirmIs("0");
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		String dayStr = sdf.format(po.getCreateTime());
		po.setDrawUser(userId);
		po.setTaskCode(dayStr);
		atrBussReserveIbnrDao.save(po);
		atrBussReserveIbnrDao.ibnrDataGenerate(po.getReserveIbnrId());
	}

	@Override
	public List<AtrBussReserveIbnrVo> findList(AtrBussReserveIbnrVo atrBussReserveIbnrVo) {
		List<AtrBussReserveIbnrVo> atrBussReserveIbnrVos = atrBussReserveIbnrDao.checkIbnrConfirm(atrBussReserveIbnrVo);
		return ClassUtil.convert(atrBussReserveIbnrVos,AtrBussReserveIbnrVo.class);
	}

	@Override
	public void delete(AtrBussReserveIbnrVo atrBussReserveIbnrVo) {
		atrBussReserveIbnrDao.deleteById(atrBussReserveIbnrVo.getReserveIbnrId());
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("reserveIbnrId", atrBussReserveIbnrVo.getReserveIbnrId());
		atrBussReserveIbnrDetailDao.deleteByMap(paramMap);
	}

	@Override
	@Transactional
	public void confirm(AtrBussReserveIbnrVo atrBussReserveIbnrVo, Long userId) {
		AtrBussReserveIbnr po =  atrBussReserveIbnrDao.findById(atrBussReserveIbnrVo.getReserveIbnrId());
		po.setConfirmIs("1");
		po.setConfirmId(userId);
		po.setConfirmTime(new Date());
		po.setUpdatorId(userId);
		po.setUpdateTime(new Date());
		atrBussReserveIbnrDao.updateById(po);

		// todo 同步导入表
		syncIbnrImport(po.getReserveIbnrId(), userId);
	}

	/**
	 * 确认后， 同步导入表
	 */
	private void syncIbnrImport(long reserveIbnrId, Long userId) {
		Date now = new Date();

		long ibnrMainId = atrBussIbnrImportMainDao.nextSeq();

		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("reserveIbnrId", reserveIbnrId);
		paramMap.put("ibnrMainId", ibnrMainId);
		paramMap.put("userId", userId);
		paramMap.put("now", now);

		atrBussReserveIbnrDao.syncIbnrImport(paramMap);
		atrBussReserveIbnrDao.syncIbnrImportDetail(paramMap);
	}

	@Override
	public void excelImport(MultipartFile file, AtrBussReserveIbnrVo ibnrVo, Long userId) throws Exception {
		String format = DateUtil.nowStr("yyyy/MM/dd/");
		String formatTask = DateUtil.nowStr("yyyyMMddHHmmss");
		String basePath = appConfig.getBasePath();
		String uploadBasePath = appConfig.getUploadBasePath();
		String path = appConfig.getUploadPath() + format;
		String taskCode = formatTask;

		String saveFileName = taskCode + ibnrVo.getFileName().substring(ibnrVo.getFileName().lastIndexOf("."));
		try {
			FileUtil.uploadFile(file, basePath + uploadBasePath + path, taskCode);
			atrImportService.importFile(file,ibnrVo.getTargetRouter(),userId);
		} catch (Exception ex) {
			LOG.error("File Upload Fail : ", ex);
			throw new FileUploadException("File Upload Fail : " + ex.getCause());
		}

		File file2 = new File(basePath + uploadBasePath + path, saveFileName);
		FileInputStream fs = new FileInputStream(file2);
		//解析excel
		AtrBussReserveIbnrVo atrBussReserveIbnrVo = AtrExcelIbnrImportUtil.parseExcel(fs);

		//业务单位
		if(StringUtil.isNotEmpty(atrBussReserveIbnrVo.getEntityCode())){
			String centerCode = atrBussReserveIbnrVo.getEntityCode().split("--")[0].trim();
			if(StringUtil.isNotEmpty(centerCode)){
				BbsConfEntityVo bbsConfEntityVo = bbsConfEntityFeignClient.findByEntityCode(centerCode);
				if(ObjectUtils.isNotEmpty(bbsConfEntityVo)){
					atrBussReserveIbnrVo.setEntityId(bbsConfEntityVo.getEntityId());
				}else {
					throw new Exception("row : " + 2 + ", column : " + 1 + ", "  +
							" the business unit code " + centerCode + " does not exist!");
				}
			}
		}

		//风险大类
		if(StringUtil.isNotEmpty(atrBussReserveIbnrVo.getLoaCode())){
			String riskClass = atrBussReserveIbnrVo.getEntityCode().split("--")[0].trim();
			if(StringUtil.isNotEmpty(riskClass)){
				BbsConfRiskClassVo bbsRiskClassVo = bbsRiskClassFeignClient.findByRiskClass(riskClass);
				if(ObjectUtils.isNotEmpty(bbsRiskClassVo)){
					atrBussReserveIbnrVo.setLoaCode(riskClass);
				}else {
					throw new Exception("row : " + 3 + ", column : " + 1 + ", "  +
							" the risk class " + riskClass + " does not exist!");
				}
			}
		}

		//*币别
		if(StringUtil.isNotEmpty(atrBussReserveIbnrVo.getCurrencyCode())){
			BbsConfCurrencyVo bbsCurrencyVo = bbsCurrencyClient.findByCode(atrBussReserveIbnrVo.getCurrencyCode());
			if(null == bbsCurrencyVo || StringUtil.isEmpty(bbsCurrencyVo.getCurrencyCode())){
				throw new Exception("row : " + 4 + ", column : " + 1 + ", "  +
						" the currency " + atrBussReserveIbnrVo.getCurrencyCode() + " does not exist!");
			}
		}

		//IBNR类型
		if(StringUtil.isNotEmpty(atrBussReserveIbnrVo.getIbnrType())){
			String ibnrType = atrBussReserveIbnrVo.getIbnrType().split("--")[0].trim();
			if(StringUtil.isNotEmpty(ibnrType)){
				atrBussReserveIbnrVo.setIbnrType(ibnrType);
			}
		}

		//业务类型
		String businessType = atrBussReserveIbnrVo.getBusinessSourceCode();
		if(StringUtil.isNotEmpty(businessType)){
			// 当业务类型为所有或All时，设置为空
			if("所有".equals(businessType) || "All".toUpperCase().equals(businessType.toUpperCase())){
				atrBussReserveIbnrVo.setBusinessSourceCode(null);
			}else {
				businessType = atrBussReserveIbnrVo.getBusinessSourceCode().split("--")[0].trim();
				if(StringUtil.isNotEmpty(businessType)){
					atrBussReserveIbnrVo.setBusinessSourceCode(businessType);
				}
			}
		}

		// 保存主表提取条件，并返回其主键ID
		Long reserveIbnrId = saveByImport(atrBussReserveIbnrVo, userId);
		// 保存明细数据
		if(ObjectUtils.isNotEmpty(atrBussReserveIbnrVo.getList())){
			List<AtrBussReserveIbnrDetail> detailList = ClassUtil.convert(atrBussReserveIbnrVo.getList(), AtrBussReserveIbnrDetail.class);
			for(AtrBussReserveIbnrDetail detailPo : detailList){
				detailPo.setReserveIbnrId(reserveIbnrId);
				// IBNR未决赔款
				detailPo.setDataType("16");
				atrBussReserveIbnrDetailDao.save(detailPo);
			}
		}

	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
	public Long saveByImport(AtrBussReserveIbnrVo atrBussReserveIbnrVo, Long userId){
		AtrBussReserveIbnr po = ClassUtil.convert(atrBussReserveIbnrVo, AtrBussReserveIbnr.class);
		po.setDrawStatus("1");
		// Excel导入
		po.setDrawType("2");
		po.setConfirmIs("0");
		po.setCreateTime(new Date());
		po.setCreatorId(userId);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		String dayStr = sdf.format(po.getCreateTime());
		po.setDrawUser(userId);
		po.setTaskCode(dayStr);
		po.setDrawTime(po.getEndDate());
		atrBussReserveIbnrDao.save(po);
		Long reserveIbnrId = po.getReserveIbnrId();
		return reserveIbnrId;
	}

	@Override
	public void excelExport(HttpServletResponse response, AtrBussReserveIbnrVo atrBussReserveIbnrVo) throws Exception{
		try {
			// 获取Excel表头
			List<AtrExcelHeaderVo> headerCells = new ArrayList<>();
			headerCells.add(new AtrExcelHeaderVo(null, "dataType", "A/C",ActuarialConstant.ExportIbnr.ITEM,"項目","VARCHAR", "", "文本长度(32)", "32", "IbnrValue", null));
			headerCells.add(new AtrExcelHeaderVo(null, "damageYearMonth", "Date of Accident","事故发生年月","事故發生年月","VARCHAR", "", "文本长度(6)", "32", null, null));
			headerCells.add(new AtrExcelHeaderVo(null, "devPeriod", "Develop Period","发展期次","發展期次","INTEGER", "", "数值长度(19)", "19", null, null));
			headerCells.add(new AtrExcelHeaderVo(null, "value", "Value","值","值","DOUBLE", "", "数值长度(16,6)", "16", null, HorizontalAlignment.LEFT));

			// 把文件名、语言和表头信息存放到AtrExcelVo对象
			AtrExcelVo atrExcelVo = new AtrExcelVo("IBNR Reserve Data List", "IBNR准备金数据","IBNR準備金數據",atrBussReserveIbnrVo.getLanguage(), headerCells);

			// 获取Excel的数据List
			List<AtrBussReserveIbnrExcelVo> detailVoList = atrBussReserveIbnrDetailService.findListByIbnrId(atrBussReserveIbnrVo.getReserveIbnrId());
			List<Object> objList = new ArrayList<>();
			for(AtrBussReserveIbnrExcelVo vo : detailVoList){
				objList.add(vo);
			}

			// 生成Excel并导出
			atrConfExcelService.exportExcel(response, atrExcelVo, objList);
		}catch (Exception ex) {
			LOG.error(ex.getLocalizedMessage(), ex);
			throw ex;
		}
	}

	@Override
	public void export(HttpServletRequest request, HttpServletResponse response, AtrBussReserveIbnrVo atrBussReserveIbnrVo) throws Exception {
		AtrBussReserveIbnrVo reserveIbnrVo = atrBussReserveIbnrDao.findByReserveIbnrId(atrBussReserveIbnrVo.getReserveIbnrId());
		if (ObjectUtils.isEmpty(reserveIbnrVo)) {
			return;
		}
		//获取语言
		String language = request.getHeader("ss-Language");
		if(StringUtil.isEmpty(language)){
			language = "en";
		}
		List<AtrBussReserveIbnrVo> reserveIbnrVoList = Arrays.asList(reserveIbnrVo);

		//IBNR发展期列表
		List<String> devPeriodList =  atrBussReserveIbnrDetailDao.devPeriod(atrBussReserveIbnrVo.getReserveIbnrId());
		//IBNR事故年月列表
		List<AtrBussReserveIbnrDetailVo> ibnrDetailVoList =  atrBussReserveIbnrDetailDao.findYearMonth(atrBussReserveIbnrVo.getReserveIbnrId());
		List<String> yearMonthList = ibnrDetailVoList.stream().map(AtrBussReserveIbnrDetailVo::getDamageYearMonth).collect(Collectors.toList());

		//查詢第一步【累计赔款流量三角】数据
		AtrBussReserveIbnrDetailVo ibnrDetailVo = new AtrBussReserveIbnrDetailVo();
		ibnrDetailVo.setReserveIbnrId(atrBussReserveIbnrVo.getReserveIbnrId());
		ibnrDetailVo.setDataType("1");
		ibnrDetailVo.setDevNoList(devPeriodList);
 		List<Map<String, Object>> firstDetailList = atrBussReserveIbnrDetailDao.findDataTypeIbnr(ibnrDetailVo);
		//查詢第二步【赔款CDF流量三角】数据
		ibnrDetailVo.setDataType("2");
 		List<Map<String, Object>> secondDetailList = atrBussReserveIbnrDetailDao.findDataTypeIbnr(ibnrDetailVo);
		List<AtrBussReserveIbnrDetailVo> stageList = Lists.newArrayList();
		stageList = atrConfCodeDao.findCodeByUpperCode("IbnrValue/Reparations");
		List<Map<String, Object>> ibnrMapList;
		for(AtrBussReserveIbnrDetailVo vo : stageList) {
			vo.setReserveIbnrId(atrBussReserveIbnrVo.getReserveIbnrId());
			vo.setDataType(vo.getCodeCode());
			vo.setDevNoList(devPeriodList);
			ibnrMapList = atrBussReserveIbnrDetailDao.findDataTypeIbnr(vo);
			String ibnrTypeName = LocalesConstanst.Language.ZH.equals(language) ? vo.getCodeCName() : LocalesConstanst.Language.EN.equals(language) ? vo.getCodeEName() : vo.getCodeLName();
			ibnrMapList.forEach(ibnrMap -> {
				ibnrMap.put("damageYearMonth", ibnrTypeName);
			});
			secondDetailList.addAll(ibnrMapList);
		}
		//查詢第三步【BF法估计损失率】数据
		List<Map<String, Object>> thirdDetailList = new ArrayList<>();
		stageList = atrConfCodeDao.findCodeByUpperCode("IbnrValue/BFLossRate");
		List<Map<String, Object>> ibnrLossMapList;
		for(AtrBussReserveIbnrDetailVo vo : stageList) {
			vo.setReserveIbnrId(atrBussReserveIbnrVo.getReserveIbnrId());
			vo.setDataType(vo.getCodeCode());
			vo.setYearMonthList(yearMonthList);
			ibnrLossMapList = atrBussReserveIbnrDetailDao.findReparationIbnr(vo);
			String ibnrTypeName = LocalesConstanst.Language.ZH.equals(language) ? vo.getCodeCName() : LocalesConstanst.Language.EN.equals(language) ? vo.getCodeEName() : vo.getCodeLName();
			ibnrLossMapList.forEach(ibnrMap -> {
				ibnrMap.put(ActuarialConstant.ExportIbnr.ITEM, ibnrTypeName);
			});
			thirdDetailList.addAll(ibnrLossMapList);
		}

		//查詢第四步【BF计算的IBNR】数据
		List<Map<String, Object>> fourDetailList = new ArrayList<>();
		stageList = atrConfCodeDao.findCodeByUpperCode("IbnrValue/BFCalculate");
		List<Map<String, Object>> ibnrBfMapList;
		for(AtrBussReserveIbnrDetailVo vo : stageList) {
			vo.setReserveIbnrId(atrBussReserveIbnrVo.getReserveIbnrId());
			vo.setDataType(vo.getCodeCode());
			vo.setYearMonthList(yearMonthList);
			ibnrBfMapList = atrBussReserveIbnrDetailDao.findReparationIbnr(vo);
			String ibnrTypeName = LocalesConstanst.Language.ZH.equals(language) ? vo.getCodeCName() : LocalesConstanst.Language.EN.equals(language) ? vo.getCodeEName() : vo.getCodeLName();
			ibnrBfMapList.forEach(ibnrMap -> {
				ibnrMap.put(ActuarialConstant.ExportIbnr.ITEM, ibnrTypeName);
			});
			fourDetailList.addAll(ibnrBfMapList);
		}

		//组装Sheet页数据
		List<ExcelSheet> sheetList = new ArrayList<>();
		sheetList.add(getSheet(0, "", reserveIbnrVoList, AtrBussReserveIbnrVo.class));
		sheetList.add(getSheet(1, "", firstDetailList, null));
		sheetList.add(getSheet(2, "", secondDetailList, null));
		sheetList.add(getSheet(3, "", thirdDetailList, null));
		sheetList.add(getSheet(4, "", fourDetailList, null));
		//生成模板
 		String templateFile = ibnrExcelTemplate(atrBussReserveIbnrVo);
 		if (ObjectUtils.isNotEmpty(templateFile)) {
			atrExportService.exportSheetList(request,response, sheetList, templateFile,
					atrBussReserveIbnrVo.getTemplateFileName(), null, atrBussReserveIbnrVo.getTargetRouter(), atrBussReserveIbnrVo.getCreatorId());
		}
 	}

	@Override
	public Page<AtrBussReserveIbnrDetailVo> findPortfolioData(AtrBussReserveIbnrVo atrBussReserveIbnrVo, Pageable pageParam) {
		Page<AtrBussReserveIbnrDetailVo> bussIbnrDetailVoPage = atrBussReserveIbnrDetailDao.findIbnrPortfolioData(atrBussReserveIbnrVo, pageParam);
		return bussIbnrDetailVoPage;
	}

	//组装Excel的Sheet
	private ExcelSheet getSheet(int sheetNo, String sheetName, List<?> beanData, Class voClazz) {
		ExcelSheet sheet = new ExcelSheet();
		sheet.setSheetNo(sheetNo);
		sheet.setSheetName(sheetName);
		List<ExcelSheetData> sheetDataList = new ArrayList<>();
		ExcelSheetData sheetData = new ExcelSheetData();
		sheetData.setBeanKey("df");
		sheetData.setBeanData(beanData);
		sheetData.setVoClazz(voClazz);
		sheetDataList.add(sheetData);
		sheet.setSheetDataList(sheetDataList);
		return sheet;
	}

	/**
	 * List<Map>对象复制功能
	 * List<Map> 转化为 List<Map>
	 * @param list
	 */
	public static List<Map<String, Object>> convert(List<Map<String, Object>> list) {
		List<Map<String, Object>> newMapList = new ArrayList<>();
		for (Map map : list) {
			try {
				newMapList.add((Map) SerializationUtils.clone((Serializable) map));
			} catch (Exception ex) {
				throw new SsException(ex.getLocalizedMessage(), ex);
			}
		}
		return newMapList;
	}

	/*
	* Ibnr导出模板生成
	* */
	public String ibnrExcelTemplate(AtrBussReserveIbnrVo atrBussReserveIbnrVo) {
		String targetFilePath = getOutPutPath();
		Date outPutTime = new Date();
		SimpleDateFormat formatterTime = new SimpleDateFormat("HHmmss");
		String formatterTimeCode = formatterTime.format(outPutTime);
		String outFilePath = targetFilePath + atrBussReserveIbnrVo.getTemplateFileName() + formatterTimeCode + ".xlsx";
		File file = new File(targetFilePath);
		if (!file.exists()) {
			file.mkdirs();
		}
		// 表头
		List<List<String>> head0List = new ArrayList<>();
		head0List = ActuarialConstant.ExportIbnr.Sheet0HeadList;
		// 内容
		List<List<Object>> content0List = new ArrayList<>();
		List<Object> list1 = ActuarialConstant.ExportIbnr.ContentList;
		content0List.add(list1);

		List<String> devPeriodList =  atrBussReserveIbnrDetailDao.devPeriod(atrBussReserveIbnrVo.getReserveIbnrId());
		List<List<String>> head1List = new ArrayList<>();
		head1List.add(Arrays.asList(ActuarialConstant.ExportIbnr.ACC_YEAR_MONTH, ActuarialConstant.ExportIbnr.ACC_YEAR_MONTH));

		List<List<String>> head2List = new ArrayList<>();
		head2List.add(Arrays.asList(ActuarialConstant.ExportIbnr.ACC_YEAR_MONTH, ActuarialConstant.ExportIbnr.ACC_YEAR_MONTH));

		List<List<String>> head3List = new ArrayList<>();
		head3List.add(Arrays.asList(ActuarialConstant.ExportIbnr.ITEM, ActuarialConstant.ExportIbnr.ITEM));

		List<List<String>> head4List = new ArrayList<>();
		head4List.add(Arrays.asList(ActuarialConstant.ExportIbnr.ITEM, ActuarialConstant.ExportIbnr.ITEM));

		// 内容
		List<List<Object>> content1List = new ArrayList<>();
		List<Object> list21 = new ArrayList(Arrays.asList("{df.damageYearMonth}"));

		List<List<Object>> content2List = new ArrayList<>();
		List<Object> list31 = new ArrayList(Arrays.asList("{df.damageYearMonth}"));

		List<List<Object>> content3List = new ArrayList<>();
		List<Object> list41 = new ArrayList(Arrays.asList("{df.item}"));

		List<List<Object>> content4List = new ArrayList<>();
		List<Object> list51 = new ArrayList(Arrays.asList("{df.item}"));

		for(String period : devPeriodList) {
			head1List.add(Arrays.asList(ActuarialConstant.ExportIbnr.CLAIM_TIME, period));
			list21.add("{df." + period + "}");

			head2List.add(Arrays.asList(ActuarialConstant.ExportIbnr.CLAIM_TIME, period));
			list31.add("{df." + period + "}");
		}
		content1List.add(list21);
		content2List.add(list31);

		List<AtrBussReserveIbnrDetailVo> ibnrDetailVoList =  atrBussReserveIbnrDetailDao.findYearMonth(atrBussReserveIbnrVo.getReserveIbnrId());
		for(AtrBussReserveIbnrDetailVo period : ibnrDetailVoList) {
			head3List.add(Arrays.asList(ActuarialConstant.ExportIbnr.ACC_YEAR_MONTH, period.getDamageYearMonth()));
			list41.add("{df." + period.getDamageYearMonth() + "}");

			head4List.add(Arrays.asList(ActuarialConstant.ExportIbnr.ACC_YEAR_MONTH, period.getDamageYearMonth()));
			list51.add("{df." + period.getDamageYearMonth() + "}");
		}
		content3List.add(list41);
		// 内容
		content4List.add(list51);
		head4List.add(Arrays.asList(ActuarialConstant.ExportIbnr.TOTAL,ActuarialConstant.ExportIbnr.TOTAL));
		list51.add("{df.total}");


 		//自定义背景颜色
		int r = Integer.parseInt(("#dad4f0".substring(1,3)),16);
		int g = Integer.parseInt(("#dad4f0".substring(3,5)),16);
		int b = Integer.parseInt(("#dad4f0".substring(5,7)),16);
		HSSFWorkbook wb = new HSSFWorkbook();
		HSSFPalette palette = wb.getCustomPalette();
		HSSFColor hssfColor = palette.findSimilarColor(r, g, b);
		//数字金额内容样式策略
		WriteCellStyle contentNumberWriteCellStyle1 = new WriteCellStyle();
		//垂直居中,水平居中
		contentNumberWriteCellStyle1.setVerticalAlignment(VerticalAlignment.CENTER);
		contentNumberWriteCellStyle1.setHorizontalAlignment(HorizontalAlignment.RIGHT);
		DataFormatData dataFormatData1 = new DataFormatData();
		dataFormatData1.setFormat("0.00");
		contentNumberWriteCellStyle1.setDataFormatData(dataFormatData1);

		//数字比例内容样式策略
		WriteCellStyle contentNumberWriteCellStyle2 = new WriteCellStyle();
		//垂直居中,水平居中
		contentNumberWriteCellStyle2.setVerticalAlignment(VerticalAlignment.CENTER);
		contentNumberWriteCellStyle2.setHorizontalAlignment(HorizontalAlignment.RIGHT);
		DataFormatData dataFormatData2 = new DataFormatData();
		dataFormatData2.setFormat("0.0000");
		contentNumberWriteCellStyle2.setDataFormatData(dataFormatData2);

		//文本内容样式策略
		WriteCellStyle contentTextWriteCellStyle = new WriteCellStyle();
		//垂直居中,水平居中
		contentTextWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		contentTextWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

   		//表头样式策略
		WriteCellStyle headWriteCellStyle = new WriteCellStyle();
		WriteFont headWriteFont = new WriteFont();
		headWriteFont.setFontHeightInPoints((short) 12);
		headWriteCellStyle.setFillForegroundColor(hssfColor.getIndex());
		headWriteCellStyle.setWriteFont(headWriteFont);

		ExcelWriter excelWriter = EasyExcel.write(outFilePath).build();
        // 创建sheet页，设置sheet页名称，同时写入表头信息
		WriteSheet writeSheet = EasyExcel.writerSheet(ActuarialConstant.ExportIbnr.SHEET0).head(head0List)
				.registerWriteHandler(new HorizontalCellStyleStrategy(headWriteCellStyle, contentTextWriteCellStyle)).registerWriteHandler(new SimpleColumnWidthStyleStrategy(20)).build();
		excelWriter.write(content0List, writeSheet);

		writeSheet = EasyExcel.writerSheet(ActuarialConstant.ExportIbnr.SHEET1).head(head1List)
				.registerWriteHandler(new HorizontalCellStyleStrategy(headWriteCellStyle, contentNumberWriteCellStyle1))
				.registerWriteHandler(new SimpleColumnWidthStyleStrategy(14))
				.build();
		excelWriter.write(content1List, writeSheet);

		writeSheet = EasyExcel.writerSheet(ActuarialConstant.ExportIbnr.SHEET2).head(head2List)
				.registerWriteHandler(new HorizontalCellStyleStrategy(headWriteCellStyle, contentNumberWriteCellStyle2))
				.registerWriteHandler(new SimpleColumnWidthStyleStrategy(14)).build();
		excelWriter.write(content2List, writeSheet);

		writeSheet = EasyExcel.writerSheet(ActuarialConstant.ExportIbnr.SHEET3).head(head3List)
				.registerWriteHandler(new HorizontalCellStyleStrategy(headWriteCellStyle, contentNumberWriteCellStyle1))
				.registerWriteHandler(new SimpleColumnWidthStyleStrategy(14)).build();
		excelWriter.write(content3List, writeSheet);

		writeSheet = EasyExcel.writerSheet(ActuarialConstant.ExportIbnr.SHEET4).head(head4List)
				.registerWriteHandler(new HorizontalCellStyleStrategy(headWriteCellStyle, contentNumberWriteCellStyle1))
				.registerWriteHandler(new SimpleColumnWidthStyleStrategy(14)).build();
		excelWriter.write(content4List, writeSheet);

		//这一步很关键，不然文件会损坏
		excelWriter.finish();
		return outFilePath;
	}


	protected String getOutPutPath() {
		Date outPutTime = new Date();
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy/MM/dd");
		String formattedDate = formatter.format(outPutTime);
		String basePath = appConfig.getBasePath();
		String outPutPath = appConfig.getExportExcelOutTemplate();
		String targetFilePath = basePath + outPutPath + formattedDate + "/";
		return targetFilePath;
	}


	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
	public void saveDetail(AtrBussReserveIbnrDetailVo atrBussReserveIbnrDetailVo) {
		AtrBussReserveIbnrDetail po = ClassUtil.convert(atrBussReserveIbnrDetailVo, AtrBussReserveIbnrDetail.class);
		if(null != po.getReserveIbnrId()){
			atrBussReserveIbnrDetailDao.save(po);
		}
	}
}
