package com.ss.ifrs.actuarial.service.puhua;

import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po.AtrBussIbnrAllocAction;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrAllocActionVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrAllocResultVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussIbnrImportMainVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @ClassName: AtrBussCalcService
 * @Description: 计量计算服务接口类
 * @CreateDate: 2021/3/8 17:56
 * @Version: 1.0
 */
public interface AtrBussIbnrSecAllocService {
    //模糊查找对象信息
    Page<AtrBussIbnrAllocActionVo> findForDataTables(AtrBussIbnrAllocActionVo allocActionVo, Pageable pageParam);

    void delete(Long id, Long userId);

    /**
     * @description: IBNR分摊结果确认
     * @param : [allocActionVo， userId]
     * @Date 2025/4/8
     * */
    Boolean confirm(AtrBussIbnrAllocActionVo allocActionVo, Long userId);

    /**
     * @description: IBNR分摊计算
     * @param : [allocActionVo， userId]
     * @Date 2025/4/8
     * */
    void calculateAll(AtrBussIbnrAllocActionVo allocActionVo, Long userId);

    /**
     * @description: IBNR分摊结果查询
     * @param : [allocActionVo， userId]
     * @Date 2025/4/8
     * */
    Page<AtrBussIbnrAllocResultVo> findAllocList(AtrBussIbnrAllocActionVo allocActionVo, Pageable pageParam);

    /**
     * @description: IBNR分摊超赔结果查询
     * @param : [allocActionVo， userId]
     * @Date 2025/4/8
     * */
    Page<AtrBussIbnrAllocResultVo> findXAllocList(AtrBussIbnrAllocActionVo allocActionVo, Pageable pageParam);

    /**
     * @description: IBNR分摊数据导出
     * @param : [request, response, allocActionVo， userId]
     * @Date 2025/4/8
     * */
    void exportAllocData(HttpServletRequest request, HttpServletResponse response, AtrBussIbnrAllocActionVo allocActionVo, Long userId) throws Exception;


    /**
     * @description: 查询有无可计算的IBNR导入数据
     * @param : [ibnrImportMainVo]
     * @Date 2025/4/8
     * */
    Boolean hasIbnrCalc(AtrBussIbnrAllocActionVo allocActionVo);

    /**
     * 通过期间查询是否有分摊结果
     */
    Boolean  checkIbnrAllocResult(AtrBussIbnrAllocAction po);


    /**
     * @description: IBNR分摊数据导出
     * @param : [request, response, allocActionVo， userId]
     * @Date 2025/4/8
     * */
    void downloadAllocData(HttpServletRequest request, HttpServletResponse response, AtrBussIbnrAllocActionVo allocActionVo, Long userId) throws Exception;

    void reSetIbnrCashFlow(AtrConfBussPeriodVo atrConfBussPeriodVo);
}
