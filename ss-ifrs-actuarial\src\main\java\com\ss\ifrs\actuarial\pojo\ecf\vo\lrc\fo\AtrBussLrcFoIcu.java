package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.fo;

import com.ss.ifrs.actuarial.util.abp.IgnoreCol;
import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Tab("atr_buss_fo_lrc_u")
public class AtrBussLrcFoIcu {

    /** ID */
    private Long id;

    /** 执行编号 */
    private String actionNo;

    /** 业务单位ID */
    private Long entityId;

    /** 评估期年月 */
    private String yearMonth;

    /** 险类代码 */
    private String riskClassCode;

    /** 合约号 */
    private String treatyNo;

    private String riPolicyNo;

    private String riEndorseSeqNo;

    /** 保单号 */
    private String policyNo;

    /** 批单序号 */
    private String endorseSeqNo;

    /** 险种代码 */
    private String riskCode;

    /** 险别代码 */
    private String kindCode;

    /** 合同组合编号 */
    private String portfolioNo;

    /** 合同组号码 */
    private String icgNo;

    /** 计量单元编号 */
    private String cmunitNo;

    /** 一级机构 */
    private String companyCode1;

    /** 二级机构 */
    private String companyCode2;

    /** 三级机构 */
    private String companyCode3;

    /** 四级机构 */
    private String companyCode4;

    /** 起保日期 */
    private Date effectiveDate;

    /** 终保日期 */
    private Date expiryDate;

    /** 分保起保日期 */
    private Date riEffectiveDate;

    /** 分保终保日期 */
    private Date riExpiryDate;

    /** 核保通过日期 */
    private Date approvalDate;

    /** 合同确认日期 */
    private Date contractDate;

    /** 业务发生年月 */
    private String dapYearMonth;

    /** 保费 */
    private BigDecimal premium;

    /** 净额结算手续费 */
    private BigDecimal netFee;

    /** 上期累计实收保费 */
    private BigDecimal preCumlPaidPremium;

    /** 上期累计实收净额结算手续费 */
    private BigDecimal preCumlPaidNetFee;

    /** 上期累计已赚保费 */
    private BigDecimal preCumlEdPremium;

    /** 上期累计已赚净额结算手续费 */
    private BigDecimal preCumlEdNetFee;

    /** 当期已赚保费 */
    private BigDecimal curEdPremium;

    /** 当期已赚净额结算手续费 */
    private BigDecimal curEdNetFee;

    /** 财务渠道 (fin_acc_channel) */
    private String finAccChannel;
    
    /** 财务产品代码 (fin_product_code) */
    private String finProductCode;
    
    /** 财务明细代码 (fin_detail_code) */
    private String finDetailCode;
    
    /** 财务子产品代码 (fin_sub_product_code) */
    private String finSubProductCode;
    
    /** 合约名称 */
    private String treatyName;
    
    /** 合同组名称 */
    private String icgName;
    
    /** 标的保单签发日期 */
    private Date policyIssueDate;
    
    /** 标的单承保认可日期 */
    private Date policyConfirmDate;
    
    /** 标的保单核保日期 */
    private Date policyApprovalDate;
    
    /** 标的保单合同日期 */
    private Date policyContractDate;
    
    /** 标的保单保费 */
    private BigDecimal policyPremium;
    
    /** 标的保单净额结算手续费 */
    private BigDecimal policyNetfee;
    
    /** 核算机构 */
    private String centerCode;
    
    /** 盈亏判定结果 (pl_judge_rslt) */
    private String plJudgeRslt;

    /** 部门段 (dept_id) - 对应acepayment的article5 */
    private String deptId;
    
    /** 渠道段 (channel_id) - 对应acepayment的article7 */
    private String channelId;


    // 投资成分
    private BigDecimal invAmount;

    /** 批改类型代码 (endorse_type_code) */
    private String endorseTypeCode;

    /** 特殊处理类型 (special_process_type) - 0:正常处理, 1:只计算第0期, 2:不计算发展期 */
    @IgnoreCol
    private Integer specialProcessType;

    @IgnoreCol
    private int remainingMonths;

}
