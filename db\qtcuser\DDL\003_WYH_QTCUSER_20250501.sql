CALL qtc_pack_commonutils_proc_DROP_TABLE('qtc_conf_alloc_item_def');
create table qtc_conf_alloc_item_def
(
  alloc_item_id int8 not null,
  entity_id     int8,
  factor_def_id int8,
  item_code     VARCHAR(32),
  item_c_name   <PERSON><PERSON><PERSON><PERSON>(200),
  item_l_name   VA<PERSON><PERSON><PERSON>(200),
  item_e_name   VA<PERSON><PERSON><PERSON>(200),
  audit_state   CHAR(1),
  checked_id    int8,
  checked_time  TIMESTAMP(6),
  checked_msg   VARCHAR(1000),
  serial_no     int4,
  valid_is      CHAR(1),
  creator_id    int8,
  create_time   TIMESTAMP(6),
  updator_id    int8,
  update_time   TIMESTAMP(6) ,
	  business_source_code VARCHAR(2),
	factor_type VARCHAR(32)
 
);
-- Add comments to the table 
comment on table qtc_conf_alloc_item_def
  is '分摊输出项定义配置';
-- Add comments to the columns 
comment on column qtc_conf_alloc_item_def.alloc_item_id
  is '主键';
comment on column qtc_conf_alloc_item_def.serial_no
  is 'serial_no|版本号';
comment on column qtc_conf_alloc_item_def.item_code
  is '编码';
comment on column qtc_conf_alloc_item_def.item_c_name
  is '中文名称';
comment on column qtc_conf_alloc_item_def.item_l_name
  is '本地语言名称';
comment on column qtc_conf_alloc_item_def.item_e_name
  is '英文名称';
COMMENT on column qtc_conf_alloc_item_def.audit_state
  is 'Audit_State|审核状态';
comment on column qtc_conf_alloc_item_def.checked_id
  is 'Checked_ID|审核人';
comment on column qtc_conf_alloc_item_def.checked_time
  is 'Checked_Time|审核时间';
comment on column qtc_conf_alloc_item_def.checked_msg
  is 'Checked_Msg|审核意见';
comment on column qtc_conf_alloc_item_def.valid_is
  is 'Valid_Is|是否有效';
comment on column qtc_conf_alloc_item_def.creator_id
  is 'Creator_Id|创建人';
comment on column qtc_conf_alloc_item_def.create_time
  is 'Create_Time|创建时间';
comment on column qtc_conf_alloc_item_def.updator_id
  is 'Updator_Id|最后修改人';
comment on column qtc_conf_alloc_item_def.update_time
  is 'Update_Time|最后修改时间';
-- Create/Recreate primary, unique and foreign key constraints 
alter table qtc_conf_alloc_item_def
  add constraint pk_qtc_conf_alloc_item_def primary key (alloc_item_id) ;
  
  
CALL qtcuser.qtc_pack_commonutils_proc_DROP_SEQUENCE('qtc_seq_conf_alloc_item_def');
create sequence qtc_seq_conf_alloc_item_def
              minvalue 1
              start with 1
              increment by 1;
              
              
              
                     
CALL qtc_pack_commonutils_proc_DROP_TABLE('qtc_conf_alloc_factor');
create table qtc_conf_alloc_factor
(
  alloc_factor_id int8 not null,
  entity_id int8,
  business_source_code VARCHAR(2),
  factor_code     VARCHAR(32),
  factor_c_name   VARCHAR(200),
  factor_l_name   VARCHAR(200),
  factor_e_name   VARCHAR(200),
  cal_formula    VARCHAR(400),
  audit_state   CHAR(1),
  checked_id    int8,
  checked_time  TIMESTAMP(6),
  checked_msg   VARCHAR(1000),
  serial_no     int4,
  valid_is      CHAR(1),
  creator_id    int8,
  create_time   TIMESTAMP(6),
  updator_id    int8,
  update_time   TIMESTAMP(6) 
 
);
-- Add comments to the table 
comment on table qtc_conf_alloc_factor
  is '分摊因子配置';
-- Add comments to the columns 
comment on column qtc_conf_alloc_factor.alloc_factor_id
  is '主键';
comment on column qtc_conf_alloc_factor.serial_no
  is 'serial_no|版本号';
comment on column qtc_conf_alloc_factor.factor_code
  is '编码';
comment on column qtc_conf_alloc_factor.factor_c_name
  is '中文名称';
comment on column qtc_conf_alloc_factor.factor_l_name
  is '本地语言名称';
comment on column qtc_conf_alloc_factor.factor_e_name
  is '英文名称';
COMMENT on column qtc_conf_alloc_factor.audit_state
  is 'Audit_State|审核状态';
comment on column qtc_conf_alloc_factor.checked_id
  is 'Checked_ID|审核人';
comment on column qtc_conf_alloc_factor.checked_time
  is 'Checked_Time|审核时间';
comment on column qtc_conf_alloc_factor.checked_msg
  is 'Checked_Msg|审核意见';
comment on column qtc_conf_alloc_factor.valid_is
  is 'Valid_Is|是否有效';
comment on column qtc_conf_alloc_factor.creator_id
  is 'Creator_Id|创建人';
comment on column qtc_conf_alloc_factor.create_time
  is 'Create_Time|创建时间';
comment on column qtc_conf_alloc_factor.updator_id
  is 'Updator_Id|最后修改人';
comment on column qtc_conf_alloc_factor.update_time
  is 'Update_Time|最后修改时间';
-- Create/Recreate primary, unique and foreign key constraints 
alter table qtc_conf_alloc_factor
  add constraint pk_qtc_conf_alloc_factor primary key (alloc_factor_id) ;
  
  
CALL qtcuser.qtc_pack_commonutils_proc_DROP_SEQUENCE('qtc_seq_conf_alloc_factor');
create sequence qtc_seq_conf_alloc_factor
              minvalue 1
              start with 1
              increment by 1;
              
              
              
              
CALL qtc_pack_commonutils_proc_DROP_TABLE('qtc_conf_alloc_factor_dim');
create table qtc_conf_alloc_factor_dim
(
  alloc_factor_dim_id   int8 not null,  
  alloc_factor_id int8 not null,
  factor_code     VARCHAR(32),
  dim_code      VARCHAR(32),
  serial_no     int4,
  creator_id    int8,
  create_time   TIMESTAMP(6),
  updator_id    int8,
  update_time   TIMESTAMP(6) 
);
comment on table qtc_conf_alloc_factor_dim
  is '分摊因子关联维度配置';
comment on column qtc_conf_alloc_factor_dim.alloc_factor_dim_id
  is '主键';
comment on column qtc_conf_alloc_factor_dim.factor_code
  is '因子编码';
comment on column qtc_conf_alloc_factor_dim.dim_code
  is '维度编码';
comment on column qtc_conf_alloc_factor_dim.serial_no
  is 'serial_no|版本号';
comment on column qtc_conf_alloc_factor_dim.creator_id
  is 'Creator_Id|创建人';
comment on column qtc_conf_alloc_factor_dim.create_time
  is 'Create_Time|创建时间';
comment on column qtc_conf_alloc_factor_dim.updator_id
  is 'Updator_Id|最后修改人';
comment on column qtc_conf_alloc_factor_dim.update_time
  is 'Update_Time|最后修改时间';
alter table qtc_conf_alloc_factor_dim
  add constraint pk_qtc_conf_alloc_factor_dim primary key (alloc_factor_dim_id) ;
  
CALL qtcuser.qtc_pack_commonutils_proc_DROP_SEQUENCE('qtc_seq_conf_alloc_factor_dim');
create sequence qtc_seq_conf_alloc_factor_dim
              minvalue 1
              start with 1
              increment by 1;
              
              
              
CALL qtc_pack_commonutils_proc_DROP_TABLE('qtc_conf_alloc_item_factor_ref');
create table qtc_conf_alloc_item_factor_ref
(
  alloc_factor_ref_id   int8 not null,  
  entity_id     int8,
  alloc_item_id int8 not null,
  alloc_factor_id int8,
  audit_state   CHAR(1),
  checked_id    int8,
  checked_time  TIMESTAMP(6),
  checked_msg   VARCHAR(1000),
  serial_no     int4,
  valid_is      CHAR(1),
  creator_id    int8,
  create_time   TIMESTAMP(6),
  updator_id    int8,
  update_time   TIMESTAMP(6) 
);
comment on table qtc_conf_alloc_item_factor_ref
  is '分摊因子关联维度配置';
comment on column qtc_conf_alloc_item_factor_ref.alloc_factor_ref_id
  is '主键';
comment on column qtc_conf_alloc_item_factor_ref.alloc_item_id
  is '分摊项id';
comment on column qtc_conf_alloc_item_factor_ref.alloc_factor_id
  is '分摊因子id';
COMMENT on column qtc_conf_alloc_item_factor_ref.audit_state
  is 'Audit_State|审核状态';
comment on column qtc_conf_alloc_item_factor_ref.checked_id
  is 'Checked_ID|审核人';
comment on column qtc_conf_alloc_item_factor_ref.checked_time
  is 'Checked_Time|审核时间';
comment on column qtc_conf_alloc_item_factor_ref.checked_msg
  is 'Checked_Msg|审核意见';
comment on column qtc_conf_alloc_item_factor_ref.valid_is
  is 'Valid_Is|是否有效';
comment on column qtc_conf_alloc_item_factor_ref.serial_no
  is 'serial_no|版本号';
comment on column qtc_conf_alloc_item_factor_ref.creator_id
  is 'Creator_Id|创建人';
comment on column qtc_conf_alloc_item_factor_ref.create_time
  is 'Create_Time|创建时间';
comment on column qtc_conf_alloc_item_factor_ref.updator_id
  is 'Updator_Id|最后修改人';
comment on column qtc_conf_alloc_item_factor_ref.update_time
  is 'Update_Time|最后修改时间';
-- Create/Recreate primary, unique and foreign key constraints 
alter table qtc_conf_alloc_item_factor_ref
  add constraint pk_qtc_conf_alloc_item_factor_ref primary key (alloc_factor_ref_id) ;
  
CALL qtcuser.qtc_pack_commonutils_proc_DROP_SEQUENCE('qtc_seq_conf_alloc_item_factor_ref');
create sequence qtc_seq_conf_alloc_item_factor_ref
              minvalue 1
              start with 1
              increment by 1;
              
              
              
CALL qtc_pack_commonutils_proc_DROP_TABLE('qtc_conf_alloc_code');
create table qtc_conf_alloc_code
(
  alloc_code_id            int8 not null,
  code_code          VARCHAR(32) not null,
  business_source_code VARCHAR(2),
  code_type          VARCHAR(32),
  code_c_name        VARCHAR(64),
  code_e_name        VARCHAR(64),
  code_l_name        VARCHAR(64),
  related_table      VARCHAR(32),
  column_ref         VARCHAR(64),
  column_type        VARCHAR(24),
  column_ref_e_name  VARCHAR(64),
  column_ref_c_name  VARCHAR(64),
  column_ref_l_name  VARCHAR(64),
  script_sql         VARCHAR(1000),
  serial_no          int4,
  valid_is           VARCHAR(1),
  audit_state        VARCHAR(1),
  checked_id         int8,
  checked_time       TIMESTAMP(6),
  checked_msg        VARCHAR(1000),
  create_time        TIMESTAMP(6),
  creator_id         int8,
  update_time        TIMESTAMP(6),
  updator_id         int8
);
comment on table qtc_conf_alloc_code
  is '计量分摊编码表';
-- Add comments to the columns 
comment on column qtc_conf_alloc_code.alloc_code_id
  is 'id|主键';
comment on column qtc_conf_alloc_code.code_code
  is 'code_code|编码';
comment on column qtc_conf_alloc_code.business_source_code
  is 'business_source_code|业务模型：DD-直保 TI-合约分入 TO-合约分出 FO-临分';
comment on column qtc_conf_alloc_code.code_c_name
  is 'code_c_name|编码中文名称';
comment on column qtc_conf_alloc_code.code_e_name
  is 'code_e_name|编码英文名称';
comment on column qtc_conf_alloc_code.code_l_name
  is 'code_t_name|编码繁体名称';
comment on column qtc_conf_alloc_code.column_ref
  is 'column_ref|关联字段';
comment on column qtc_conf_alloc_code.column_type
  is 'column_type|字段类型';
comment on column qtc_conf_alloc_code.code_type
  is 'code_type|码值类型';
comment on column qtc_conf_alloc_code.script_sql
  is 'script_sql|SQL脚本';
comment on column qtc_conf_alloc_code.related_table
  is 'RELATED_TABLE|关联表';
comment on column qtc_conf_alloc_code.valid_is
  is 'Valind_is|有效标志';
comment on column qtc_conf_alloc_code.audit_state
  is 'Audit_State|审核状态';
comment on column qtc_conf_alloc_code.checked_id
  is 'Checked_Code|审核人';
comment on column qtc_conf_alloc_code.checked_time
  is 'Checked_Time|审核时间';
comment on column qtc_conf_alloc_code.checked_msg
  is 'Checked_Msg|审核意见';
comment on column qtc_conf_alloc_code.create_time
  is 'Create_Time|创建时间';
comment on column qtc_conf_alloc_code.creator_id
  is 'Creator_code|创建人';
comment on column qtc_conf_alloc_code.update_time
  is 'Update_time|最后变更时间';
comment on column qtc_conf_alloc_code.updator_id
  is 'Updator_Code|最后变更经手人';
comment on column qtc_conf_alloc_code.column_ref_e_name
  is 'column_ref_e_name|关联字段英文名称';
comment on column qtc_conf_alloc_code.column_ref_c_name
  is 'column_ref_c_name|关联字段中文名称';
comment on column qtc_conf_alloc_code.column_ref_l_name
  is 'column_ref_t_name|关联字段繁体名称';
comment on column qtc_conf_alloc_code.serial_no
  is 'SERIAL_NO|版本号id';
 
alter table qtc_conf_alloc_code
  add constraint pk_qtc_conf_alloc_code_id primary key (alloc_code_id) ;
  
CALL qtcuser.qtc_pack_commonutils_proc_DROP_SEQUENCE('qtc_seq_conf_alloc_code');
create sequence qtc_seq_conf_alloc_code
              minvalue 1
              start with 1
              increment by 1;