package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaDymMainVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaDymVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.springframework.web.multipart.MultipartFile;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public interface AtrConfQuotaDymService {

    Page<AtrConfQuotaDymVo> searchPage(AtrConfQuotaDymVo atrConfQuotaDymVo, Pageable pageParam);

    void addOrUpdate(AtrConfQuotaDymMainVo atrConfQuotaDymVo, Long userId);

    void delete(AtrConfQuotaDymMainVo atrConfQuotaDymVo, Long userId);

    void audit(AtrConfQuotaDymMainVo confQuotaDymMainVo, Long userId);

    /**
     * <AUTHOR>
     * @Date 2023/02/10
     * @Description 批量审核指标对象
     */
    String batchAudit(ArrayList<AtrConfQuotaDymMainVo> confQuotaDymMainVos, Long userId);

    /**
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 激活或禁用
     */
    String disableValid(AtrConfQuotaDymMainVo confQuotaDymMainVo, Long userId);

    AtrConfQuotaDymVo findHisById(Long riskRefHisId);


    void excelImport(MultipartFile file, AtrConfQuotaDymVo atrConfQuotaDymVo, Long userId) throws Exception ;

    /**
     * <AUTHOR>
     * @Date 2023/2/1
     * @Description 按维度查找是否存在相同数据
     */
    String findValidateDimension(AtrConfQuotaDymVo confQuotaDymVo);

    /**
     * <AUTHOR>
     * @Description 按维度查询事故假设值数据
     */
    List<AtrConfQuotaDymVo> findConfQuotaByVo(AtrConfQuotaDymVo confQuotaDymVo);

    /**
     * <AUTHOR>
     * @Date 2023/2/16
     * @Description 按维度查询事故假设值数据
     */
    Map<String, Object> findQuotaByDimensionVo(AtrConfQuotaDymMainVo confQuotaDymVo);


    AtrConfQuotaDymVo findAtrConfQuotaVo(AtrConfQuotaDymVo atrConfQuotaDymVo);


    Long countQuota(AtrConfQuotaDymVo atrConfQuotaVo);

    Map<String,Object> loadPrePeriodQuota(AtrConfQuotaDymVo atrConfQuotaVo);
}
