/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2025-07-03 11:27:52
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2025-07-03 11:27:52<br/>
 * Description: DD分摊结果操作表<br/>
 * Table Name: atr_buss_ibnr_alloc_dd_result<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "分摊结果尾差表")
public class AtrBussDiffIbnrAllocVo implements Serializable {
    /**
     * Database column: atr_buss_ibnr_alloc_dd_result.id
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: atr_buss_ibnr_alloc_dd_result.action_no
     * Database remarks: 执行编号
     */
    @ApiModelProperty(value = "执行编号", required = true)
    private String actionNo;

    /**
     * Database column: atr_buss_ibnr_alloc_dd_result.entity_id
     * Database remarks: 业务单位ID
     */
    @ApiModelProperty(value = "业务单位ID", required = true)
    private Long entityId;

    /**
     * Database column: atr_buss_ibnr_alloc_dd_result.year_month
     * Database remarks: 业务年月|评估期
     */
    @ApiModelProperty(value = "业务年月|评估期", required = true)
    private String yearMonth;

    /**
     * Database column: atr_buss_ibnr_alloc_dd_result.portfolio_no
     * Database remarks: 合同组合
     */
    @ApiModelProperty(value = "合同组合", required = false)
    private String portfolioNo;

    /**
     * Database column: atr_buss_ibnr_alloc_dd_result.icg_no
     * Database remarks: 合同组
     */
    @ApiModelProperty(value = "合同组", required = false)
    private String accidentQuarter;

    /**
     * Database column: atr_buss_ibnr_alloc_dd_result.acc_year_month
     * Database remarks: null
     */
    private String accYearMonth;

    /**
     * Database column: atr_buss_ibnr_alloc_dd_result.policy_no
     * Database remarks: null
     */
    private String policyNo;

    /**
     * Database column: atr_buss_ibnr_alloc_dd_result.risk_class_code
     * Database remarks: null
     */
    private String riskClassCode;

    /**
     * Database column: atr_buss_ibnr_alloc_dd_result.kind_code
     * Database remarks: null
     */
    private String kindCode;

    /**
     * Database column: atr_buss_ibnr_alloc_dd_result.business_type
     * Database remarks: 业务类型
     */
    @ApiModelProperty(value = "业务类型", required = false)
    private String businessType;

    /**
     * Database column: atr_buss_ibnr_alloc_dd_result.ep_amount
     * Database remarks: null
     */
    private BigDecimal epAmount;

    /**
     * Database column: atr_buss_ibnr_alloc_dd_result.ep_ratio
     * Database remarks: null
     */
    private BigDecimal epRatio;

    /**
     * Database column: atr_buss_ibnr_alloc_dd_result.ibnr_amount
     * Database remarks: null
     */
    private BigDecimal ibnrAmount;

    /**
     * Database column: atr_buss_ibnr_alloc_dd_result.center_code
     * Database remarks: null
     */
    private String centerCode;

    private BigDecimal diffAmount;

    private Long ibnrDetailId;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }

    public String getAccidentQuarter() {
        return accidentQuarter;
    }

    public void setAccidentQuarter(String accidentQuarter) {
        this.accidentQuarter = accidentQuarter;
    }

    public String getAccYearMonth() {
        return accYearMonth;
    }

    public void setAccYearMonth(String accYearMonth) {
        this.accYearMonth = accYearMonth;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public String getKindCode() {
        return kindCode;
    }

    public void setKindCode(String kindCode) {
        this.kindCode = kindCode;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public BigDecimal getEpAmount() {
        return epAmount;
    }

    public void setEpAmount(BigDecimal epAmount) {
        this.epAmount = epAmount;
    }

    public BigDecimal getEpRatio() {
        return epRatio;
    }

    public void setEpRatio(BigDecimal epRatio) {
        this.epRatio = epRatio;
    }

    public BigDecimal getIbnrAmount() {
        return ibnrAmount;
    }

    public void setIbnrAmount(BigDecimal ibnrAmount) {
        this.ibnrAmount = ibnrAmount;
    }

    public String getCenterCode() {
        return centerCode;
    }

    public void setCenterCode(String centerCode) {
        this.centerCode = centerCode;
    }

    public BigDecimal getDiffAmount() {
        return diffAmount;
    }

    public void setDiffAmount(BigDecimal diffAmount) {
        this.diffAmount = diffAmount;
    }

    public Long getIbnrDetailId() {
        return ibnrDetailId;
    }

    public void setIbnrDetailId(Long ibnrDetailId) {
        this.ibnrDetailId = ibnrDetailId;
    }
}