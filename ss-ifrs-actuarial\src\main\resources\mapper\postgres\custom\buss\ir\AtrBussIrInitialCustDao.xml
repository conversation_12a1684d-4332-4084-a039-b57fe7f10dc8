<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by Taiping MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2024-08-21 18:15:20 -->
<!-- Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.AtrBussIrInitialDao">
  <!-- 本配置文件由Taiping MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**BaseDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->
  <update id="updateByMainId" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInterestRate">
    update atr_buss_ir_initial
    <set>
      <if test="confirmIs != null">
        CONFIRM_IS = #{confirmIs,jdbcType=VARCHAR},
      </if>
      <if test="confirmTime != null">
        CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where UPLOAD_RATE_ID = #{interestRateId,jdbcType=NUMERIC}
  </update>

  <resultMap id="DevResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.ir.vo.AtrBussIrDevVo">
    <result column="UPLOAD_RATE_ID" property="uploadRateId" jdbcType="DECIMAL" />
    <result column="ENTITY_ID" property="entityId" jdbcType="DECIMAL" />
    <result column="YEAR_MONTH" property="yearMonth" jdbcType="VARCHAR" />
    <result column="CURRENCY_CODE" property="currencyCode" jdbcType="VARCHAR" />
    <result column="ID" property="id" jdbcType="DECIMAL"/>
    <result column="MAIN_ID" property="mainId" jdbcType="DECIMAL"/>
    <result column="DEV_NO" property="devNo" jdbcType="DECIMAL"/>
    <result column="RATE_BASE" property="rateBase" jdbcType="DECIMAL"/>
    <result column="RATE_B" property="rateB" jdbcType="DECIMAL"/>
    <result column="RATE_M" property="rateM" jdbcType="DECIMAL"/>
    <result column="RATE_E" property="rateE" jdbcType="DECIMAL"/>
  </resultMap>
  <!-- 通用列表查询语句-->
  <select id="findBussIrInitialDevList" flushCache="false" useCache="true" resultMap="DevResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.ir.po.AtrBussIrForward">
    select
      abii.UPLOAD_RATE_ID, abii.ENTITY_ID, abii.YEAR_MONTH, abii.CURRENCY_CODE, DEV_NO, RATE_BASE, RATE_B, RATE_E
    from ATR_BUSS_IR_Initial abii left join
         ATR_BUSS_IR_Initial_dev abifd
             on abii.ID= abifd.MAIN_ID
    where abii.UPLOAD_RATE_ID = #{uploadRateId,jdbcType=NUMERIC}
    order by DEV_NO
  </select>

</mapper>