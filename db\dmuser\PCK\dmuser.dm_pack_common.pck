CREATE OR REPLACE PACKAGE dm_pack_common IS

  FUNCTION func_get_procid(p_proc_code VARCHAR2) RETURN NUMBER;

  function func_get_task_code(p_entity_id number,
                              p_year_month varchar2,
                              p_biz_type_id number,
                              p_draw_type varchar2) return varchar2;

  --------------------
  -- 检查是否年月。 正确的年月是有 年份+月份 或 年份+JS 构成， 例如 202101、2021JS 。
  -- 如果是正确的年月， 返回 1， 否则返回 0 .
  --------------------
  function func_check_year_month(p_str varchar2) return number;

END dm_pack_common;
/
CREATE OR REPLACE PACKAGE BODY dm_pack_common IS

  FUNCTION func_get_procid(p_proc_code VARCHAR2) RETURN NUMBER IS
    --v_proc_id INTEGER;
  BEGIN
    -- 根据proc_code查询proc_id, proc_code不存在，则返回空值
    --SELECT proc_id INTO v_proc_id FROM bpl_act_re_procdef WHERE proc_code = p_proc_code;

    RETURN bpluser.bpl_pack_common.func_get_procid(p_proc_code);

  EXCEPTION
    WHEN OTHERS THEN
      RETURN NULL;
  END func_get_procid;

  /**
  ** func name : func_get_task_code
  ** 根据业务期间以及上传方式获取任务号
  **
  ** p_year_month：业务期间，准备中，已准备，处理中的业务期间
  ** p_biz_type_id：模型id
  ** p_draw_type：接入方式：1-文件交换，2-etl,3-文件上传，4-api
  **/
  function func_get_task_code(p_entity_id number,
                              p_year_month varchar2,
                              p_biz_type_id number,
                              p_draw_type varchar2) return varchar2 is

    v_src_table               varchar2(100);
    v_count                   number;
    v_sql                     varchar2(2000);
    v_entity_code             varchar2(4);
    v_task_code               varchar2(18);
    v_task_code_old           varchar2(18);
    v_seq_no                  number:=0;
  begin
   if  p_entity_id is null or p_year_month is null
       or p_biz_type_id is null or p_draw_type is null then
       return null;
   end if;

   --业务单位是否存在

   select count(1) into v_count from bpluser.bbs_entity where entity_id = p_entity_id;
   if v_count < 1 then
      return null;
   end if;

  select entity_code into v_entity_code from bpluser.bbs_entity where entity_id = p_entity_id;

   --业务期间在准备中，已准备，处理中的业务期间

   select count(1) into v_count from dm_conf_bussperiod
          where entity_id = p_entity_id
                and year_month = p_year_month
                and VALID_IS = '1'
                and period_state in ('0','1','2');
   if v_count < 1 then
      return null;
   end if;

   --模型是否存在
   select count(1) into v_count  from dm_conf_table where biz_type_id = p_biz_type_id;

   if v_count < 1 then
      return null;
   end if;

   select 'ODS_'||biz_code into v_src_table from dm_conf_table where biz_type_id = p_biz_type_id;

   --p_draw_type是否正确
   select count(1) into v_count from DM_CONF_DRAWTYPE where DRAW_TYPE = p_draw_type;

   if v_count < 1 then
      return null;
   end if;

   v_task_code := 'DM'|| p_draw_type || lpad(v_entity_code, 4, '0') ||  rpad(p_year_month, 8, '0') ;

   --查询当前模型是否存在entity_code字段
    select count(1)
    into v_count
    from user_tab_columns where table_name= upper(v_src_table) and column_name = upper('entity_code');


    if v_count > 0 then
     --查找是否存在当前业务期间，最大的批次
     v_sql := ' select max(task_code) from '|| v_src_table ||' where entity_code = '''|| v_entity_code ||'''
                     and task_code like '''||v_task_code||'%''';
    else
     --查找是否存在当前业务期间，最大的批次
     v_sql := ' select max(task_code) from '|| v_src_table ||' where task_code like '''||v_task_code||'%''';
    end if;

   execute immediate v_sql into v_task_code_old;

   if v_task_code_old is not null then
      select to_number(replace(v_task_code_old,v_task_code,''))+1 into v_seq_no from dual;
   else
      v_seq_no := v_seq_no+1;
   end if;

   v_task_code := v_task_code||lpad(v_seq_no, 3, '0');

   return v_task_code;

  end func_get_task_code;

  function func_check_year_month(p_str varchar2)
      --------------------
      -- 检查是否年月。 正确的年月是有 年份+月份 或 年份+JS 构成， 例如 202101、2021JS 。
      -- 如果是正确的年月， 返回 1， 否则返回 0 .
      --------------------
      return number is
      s1 varchar2(1);
      s2 varchar2(1);
  begin
      if length(p_str) <> 6 then
          return 0;
      end if;

      if substr(p_str, 1, 1) not in ('1', '2') then
          return 0;
      end if;

      for i in 2 .. 4 loop
              s1 := substr(p_str, i, 1);
              if s1 < '0' or s1 > '9' then
                  return 0;
              end if;
          end loop;

      s1 := substr(p_str, 5, 1);
      s2 := substr(p_str, 6, 1);

      if s1 = '0' then
          if s2 < '1' or s2 > '9' then
              return 0;
          end if;
      elsif s1 = '1' then
          if s2 < '0' or s2 > '2' then
              return 0;
          end if;
      elsif s1 = 'J' then
          if s2 <> 'S' then
              return 0;
          end if;
      else
          return 0;
      end if;

      return 1;

  end func_check_year_month;

END dm_pack_common;
/
