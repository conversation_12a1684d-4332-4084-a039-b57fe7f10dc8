create or replace package exp_pack_check is

  FUNCTION func_analytical_relyon(p_entityid  NUMBER,
                                  p_bookcode  VARCHAR2,
                                  p_yearmonth VARCHAR2,
                                  p_relyon    VARCHAR2,
                                  p_ruleexpr  VARCHAR2) RETURN VARCHAR2;

  FUNCTION func_checkrulesqlisexecute(p_relyon   VARCHAR2,
                                      p_ruleexpr VARCHAR2) RETURN NUMBER;
/*
  PROCEDURE proc_checkrules(p_entityid   NUMBER,
                            p_bookcode   VARCHAR2,
                            p_yearmonth  VARCHAR2,
                            p_procid     NUMBER,
                            p_periodtype VARCHAR2,
                            p_userid     NUMBER);
*/
  --bpl函数，因为权限问题迁移到此
  TYPE Type_List_Table IS TABLE OF VARCHAR2(4000); --无索引列表
  TYPE Type_Array_Table IS TABLE OF VARCHAR2(4000) INDEX BY BINARY_INTEGER; --带索引列表

end exp_pack_check;
/
create or replace package body exp_pack_check is

  FUNCTION func_analytical_relyon(p_entityid  NUMBER,
                                  p_bookcode  VARCHAR2,
                                  p_yearmonth VARCHAR2,
                                  p_relyon    VARCHAR2,
                                  p_ruleexpr  VARCHAR2) RETURN VARCHAR2 AS
    /***********************************************************************
    NAME : exp_PROC_ANALYTICAL_RELYON
    DESCRIPTION : 脚本附加条件解析函数
    DATE :2021-01-12
    AUTHOR :YINXH

    -------
    MODIFY LOG

      UPDATE DATE :
      UPDATE BY   :
      UPDATE DESC :
    ***********************************************************************/

    v_execute_sql   VARCHAR2(4000); --脚本解析结果
    v_centeridflag  CHAR(1); --附件条件勾选标记1：业务单位
    v_bookcodeflag  CHAR(1); --附件条件勾选标记2：账套
    v_yearmonthflag CHAR(1); --附件条件勾选标记3：会计期间
  BEGIN
    BEGIN
      -- 判断脚本是否包含WHERE条件语句，若无则添加 WHERE 1 = 1
      IF INSTR(UPPER(p_ruleexpr), UPPER('where')) > 0 THEN
        v_execute_sql := p_ruleexpr || ' ';
      ELSE
        v_execute_sql := p_ruleexpr || ' where 1 = 1 ';

      END IF;
      -- RELY_ON:脚本附件条件，示例：'101'，分别代表已勾选核算单位，未勾选账套，已勾选会计期间
      IF p_relyon IS NOT NULL AND LENGTH(COALESCE(p_relyon, '0')) = 3 THEN
        -- 解析附加条件
        v_centeridflag  := substr(p_relyon, 1, 1);
        v_bookcodeflag  := substr(p_relyon, 2, 1);
        v_yearmonthflag := substr(p_relyon, 3, 1);
        -- 拼接附加条件1，如果包含{entity_id}，就做替换，否则就做追加
        IF v_centeridflag = '1' THEN
          IF INSTR(UPPER(p_ruleexpr), UPPER('{entity_id}')) > 0 THEN
            v_execute_sql := REPLACE(UPPER(v_execute_sql),
                                     UPPER('{entity_id}'),
                                     CAST(p_entityid AS VARCHAR2));
          ELSE
            v_execute_sql := v_execute_sql || ' and entity_id = ' ||
                             p_entityid;

          END IF;

        END IF;
        -- 拼接附加条件2
        /*
              IF
                v_bookcodeflag = '1' THEN
                IF
                  strpos( UPPER ( p_ruleexpr ), UPPER ( '{book_code}' ) ) > 0 THEN
                    v_execute_sql := REPLACE ( UPPER ( v_execute_sql ), UPPER ( '{book_code}' ), '''' || p_bookcode || '''' );
                  ELSE v_execute_sql := v_execute_sql || ' and book_code = ''' || p_bookcode || '''';

                END IF;

              END IF;
        */
        -- 拼接附加条件3
        IF v_yearmonthflag = '1' THEN
          IF INSTR(UPPER(p_ruleexpr), UPPER('{year_month}')) > 0 THEN
            v_execute_sql := REPLACE(UPPER(v_execute_sql),
                                     UPPER('{year_month}'),
                                     '''' || p_yearmonth || '''');
          ELSE
            v_execute_sql := v_execute_sql || ' and year_month = ''' ||
                             p_yearmonth || '''';

          END IF;

        END IF;
      ELSE
        v_execute_sql := p_ruleexpr;

      END IF;
    EXCEPTION
      WHEN OTHERS THEN
        --抛出异常提示信息
        DBMS_OUTPUT.PUT_LINE('**SQLSTATE:' || SQLCODE || ';' ||
                             '**SQLERRM:' || SQLERRM ||
                             ',【脚本解析失败，请检查脚本Sql：' || v_execute_sql ||
                             '，附件条件：' || p_relyon || '】');

    END;
    --返回脚本结果
    RETURN v_execute_sql;
    -- 此处不做异常捕获，外层做捕获异常处理，并记录异常信息

  END func_analytical_relyon;

  FUNCTION func_checkrulesqlisexecute(p_relyon   VARCHAR2,
                                      p_ruleexpr VARCHAR2) RETURN NUMBER AS
    /***********************************************************************
    NAME : exp_PROC_CHECKRULESQLISEXECUTE
    DESCRIPTION : 检查规则脚本是否可执行
    DATE :2021-01-12
    AUTHOR :YINXH

    -------
    MODIFY LOG

      UPDATE DATE :
      UPDATE BY   :
      UPDATE DESC :
    ***********************************************************************/

    v_result      NUMBER; --脚本执行结果值
    v_execute_sql VARCHAR2(4000); --脚本解析结果
    -- 校验SQL默认数据
    v_centerid_check  NUMBER := 1;
    v_bookcode_check  VARCHAR2(10) := 'BookI17';
    v_yearmonth_check VARCHAR2(10) := '202101';
  BEGIN
    -- 调用函数：解析附加条件后拼接成可执行SQL脚本返回
    v_execute_sql := exp_pack_check.func_analytical_relyon(v_centerid_check,
                                                           v_bookcode_check,
                                                           v_yearmonth_check,
                                                           p_relyon,
                                                           p_ruleexpr);
    BEGIN
      -- 不分场景试执行脚本
      EXECUTE Immediate v_execute_sql;

    EXCEPTION
      WHEN OTHERS THEN
        v_result := 0;
        --抛出异常提示信息

        DBMS_OUTPUT.PUT_LINE('**SQLSTATE:' || SQLCODE || ';' ||
                             '**SQLERRM:' || SQLERRM || ';【脚本执行失败，脚本Sql：' ||
                             v_execute_sql);

    END;
    --返回脚本结果 0-执行不通过；1-执行通过
    RETURN COALESCE(v_result, 1);
    -- 此处不做异常捕获，外层做捕获异常处理，并记录异常信息

  END func_checkrulesqlisexecute;

/*
  PROCEDURE proc_checkrules(p_entityid   NUMBER,
                            p_bookcode   VARCHAR2,
                            p_yearmonth  VARCHAR2,
                            p_procid     NUMBER,
                            p_periodtype VARCHAR2,
                            p_userid     NUMBER) AS
    /***********************************************************************
    NAME : exp_PROC_CHECKRULES
    DESCRIPTION : 规则校验
    DATE :2020-12-19
    AUTHOR :YINXH
    -------
    UPDATE DATE :
    UPDATE BY   :
    UPDATE DESC :
    ***********************************************************************/
   /* v_actlogid                 NUMBER; --业务日志表ID
    v_exec_rule_result         NUMBER; --校验规则执行结果
    v_current_rule_check_state CHAR(1); --当前节点匹配规则的检查状态
    v_errormessage             VARCHAR2(2000); --规则校验不通过信息
    v_busmessage               VARCHAR2(1000) := ''; --业务异常主键
    v_checkexpr                VARCHAR2(4000); --规则校验执行脚本信息记录
    v_ruleexprsql              VARCHAR2(4000); --规则校验执行脚本(拼接附加条件后)
    bussness_no_list type_array_table;  --数组用于临时存放业务主键值
	  i                          NUMBER;
    v_prefix_exprsql           VARCHAR2(4000); --规则校验执行脚本(按from拆分后的前缀部分)
    v_suffix_exprsql           VARCHAR2(4000);
    --rec_item record;
    --rec_proc record;--存放一行流程节点数据记录
    --rec_checkrule record;--存放一行规则数据记录
    v_book_code        VARCHAR2(32);
    v_taskCode         VARCHAR2(32);
    v_expression_array bpl_pack_common.Type_Array_Table;
    type cursor_type is ref cursor;
    v_expression_cursor cursor_type;
    v_expr_cursor_val   VARCHAR2(4000);
  BEGIN

    v_book_code := bpl_pack_common.func_get_accountSet(p_entityid); ---bpluser
    --获取任务ID任务类型(自动A/手动M);P_TASKBIZ 业务类型（各业务版块）,
    v_taskCode := bpl_pack_common.func_get_taskcode('EXP', 'M', 'ACT'); ---bpluser
    -- 【节点循环】：根据 核算单位ID 和 账套编码，获取当前节点所有父节点及其所有子节点

    FOR rec_proc IN (WITH bp2 AS
                        (select bp1.proc_id,
                               bp1.parent_proc_id,
                               connect_by_isleaf isleaf
                          from bpl_act_re_procdef bp1
                         where bp1.valid_is = '1'
                         start with bp1.proc_id = p_procid
                        connect by prior proc_id = parent_proc_id)
                       SELECT *
                         FROM bp2
                        WHERE EXISTS (SELECT 1
                                 FROM bpluser.bpl_conf_checkrule ac
                                WHERE ac.proc_id = bp2.proc_id
                                  AND ac.valid_is = '1'
                                  AND ac.effective_date <= sysdate
                                  AND ac.expriration_date >= sysdate
                                  AND ac.audit_state = '1'
                                  AND ac.entity_id = p_entityid
                               --AND (ac.book_code = p_bookcode or  p_bookcode is null)
                               )) LOOP
      v_actlogid := exp_seq_actionlog.nextval; --按节点获取新的业务日志ID
      -- 将当前校验规则写入业务日志表(精确到节点)
      exp_pack_common.proc_add_actionlog(v_actlogid,
                                         v_taskCode,
                                         p_entityid,
                                         v_book_code,
                                         p_yearmonth,
                                         rec_proc.proc_id,
                                         rec_proc.parent_proc_id,
                                         '0', --默认不通过，后续按节点规则取并集状态更新
                                         p_periodtype,
                                         p_userid --创建人员
                                         );

      -- 【规则循环】：循环获取节点的所有匹配规则，并校验
      FOR rec_checkrule IN (SELECT cr.check_rule_id,
                                   cr.entity_id, --核算单位ID
                                   cr.book_code, --账套编码
                                   cr.proc_id, --流程节点ID
                                   cr.warning_type, --校验规则类型(父节点为对账节点时有值)：
                                   cr.rule_expr, --SQL脚本规则
                                   cr.rely_on, --规则附件条件
                                   cr.rule_direction --规则方向：1-正向规则，0-反向规则
                              FROM bpluser.bpl_conf_checkrule cr
                             WHERE cr.entity_id = p_entityid
                                  --AND (cr.book_code = p_bookcode or  p_bookcode is null)
                               AND cr.valid_is = '1'
                               AND cr.effective_date <= sysdate
                               AND cr.expriration_date >= sysdate
                               AND cr.audit_state = '1'
                               AND cr.proc_id = rec_proc.proc_id) LOOP
        BEGIN
          -- 是否为科目对账校验，否则都归为SQL脚本校验--费用分摊不存在科目检验
          -- 执行SQL脚本规则，并记录结果
          IF rec_checkrule.rule_expr IS NOT NULL THEN
            BEGIN
              -- 调用函数：解析附加条件后拼接成可执行SQL脚本返回
              v_ruleexprsql := exp_pack_check.func_analytical_relyon(p_entityid,
                                                                     v_book_code,
                                                                     p_yearmonth,
                                                                     rec_checkrule.rely_on,
                                                                     rec_checkrule.rule_expr);
              -- 分场景执行脚本
              -- 1、脚本自带统计函数 COUNT
              v_busmessage := '';
              IF instr(UPPER(v_ruleexprsql), UPPER(' count(')) > 0 THEN
                EXECUTE IMMEDIATE v_ruleexprsql
                  INTO v_exec_rule_result; --结果计数值
              ELSE
                -- 脚本不包含统计函数count的脚本需要另外统一进行结果统计
								-- 先将业务主键批量插入数组列表
								EXECUTE IMMEDIATE to_char(v_ruleexprsql)  BULK COLLECT
										INTO bussness_no_list;
								--插入业务数据前，清除临时表数据

                i := 0;
                FOR i IN 1 .. bussness_no_list.count LOOP
                  -- 截取前5个业务主键存入日志子表的业务异常主键字段
                    IF i <= 5 THEN
                      v_busmessage := v_busmessage || ',' || bussness_no_list(i);
                    END IF;
                END LOOP;
                -- 去除第一个逗号分割符
                SELECT TRIM(LEADING ',' FROM v_busmessage)
                  INTO v_busmessage
                  from dual;
                IF v_busmessage IS NOT NULL   THEN
                  v_exec_rule_result := 1; --有值则给结果计数值为 1
                ELSE
                  v_exec_rule_result := 0; --无值则给结果计数值为 0
                END IF;
              END IF;

              -- 记录脚本执行信息
              v_checkexpr := 'Sql脚本|' || v_ruleexprsql || '，执行结果|' ||
                             v_exec_rule_result;
            EXCEPTION
              WHEN OTHERS THEN
                v_current_rule_check_state := '0';
                v_checkexpr                := 'Sql脚本|' || v_ruleexprsql ||
                                              '，执行执行异常';
                --抛出异常提示信息
                DBMS_OUTPUT.PUT_LINE('**SQLSTATE:' || sqlcode || ';' ||
                                     '**SQLERRM:' || SQLERRM);

            END;
          ELSE
            v_exec_rule_result := 0;
            v_checkexpr        := 'Sql脚本为空';
          END IF;

          -- 规则方向为正向，且执行SQL脚本结果值大于指定值，则视为校验通过
          IF rec_checkrule.rule_direction = '1' THEN
            -- 正向规则时，仅大于0校验通过
            IF v_exec_rule_result > 0 THEN
              v_current_rule_check_state := '1';
            ELSE
              v_current_rule_check_state := '0';
            END IF;
          ELSE
            -- 反向规则时，大于0为不通过
            IF v_exec_rule_result > 0 THEN
              v_current_rule_check_state := '0';
            ELSE
              v_current_rule_check_state := '1';
            END IF;
          END IF;

          -- 将当前校验规则写入业务日志详情表(精确到规则)
          exp_pack_common.proc_add_actionlogdetails(v_actlogid,
                                                    rec_checkrule.check_rule_id,
                                                    v_checkexpr,
                                                    v_current_rule_check_state, --规则校验状态
                                                    v_errormessage,
                                                    v_busmessage,
                                                    p_userid --创建人员
                                                    );

        END;
      END LOOP;
      --当前循环节点校验完毕后更新节点校验状态
      UPDATE exp_actionlog A
         SET STATE =
             (SELECT COALESCE(MIN(COALESCE(ld.STATE, '0')), '0')
                FROM exp_actionlogdetails ld
               WHERE ld.act_log_id = A.act_log_id)
       WHERE A.act_log_id = v_actlogid;

    END LOOP;
    --当前节点名下所有子节点校验完毕后更新传入节点校验状态

  EXCEPTION
    WHEN OTHERS THEN
      --抛出异常提示信息
      DBMS_OUTPUT.PUT_LINE('**SQLSTATE:' || sqlcode || ';' || '**SQLERRM:' ||
                           SQLERRM || ',【数据检查发生异常，请检查】');

  END proc_checkrules;
*/
end exp_pack_check;
/
