<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by Taiping MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2025-05-28 17:57:11 -->
<!-- Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.alloc.AtrBussClaimImportMainDao">
  <!-- 本配置文件由Taiping MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**BaseDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->
  <resultMap id="VoResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussClaimImportMainVo">
    <id column="claim_main_id" property="claimMainId" jdbcType="BIGINT" />
    <result column="entity_id" property="entityId" jdbcType="BIGINT" />
    <result column="year_month" property="yearMonth" jdbcType="VARCHAR" />
    <result column="version_no" property="versionNo" jdbcType="VARCHAR" />
    <result column="confirm_is" property="confirmIs" jdbcType="CHAR" />
    <result column="confirm_id" property="confirmId" jdbcType="BIGINT" />
    <result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP" />
    <result column="creator_id" property="creatorId" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="updator_id" property="updatorId" jdbcType="BIGINT" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />

    <result column="entity_code" property="entityCode" jdbcType="VARCHAR" />
    <result column="entity_e_name" property="entityEName" jdbcType="VARCHAR" />
    <result column="entity_l_name" property="entityLName" jdbcType="VARCHAR" />
    <result column="entity_c_name" property="entityCName" jdbcType="VARCHAR" />

    <result column="confirm_name" property="confirmName" jdbcType="VARCHAR" />
    <result column="creator_name" property="creatorName" jdbcType="VARCHAR" />
    <result column="updator_name" property="updatorName" jdbcType="VARCHAR" />

  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Vo_Column_List">
    t.claim_main_id, t.entity_id, t.year_month, t.version_no, t.confirm_is, t.confirm_id, t.confirm_time,
    t.creator_id, t.create_time, t.updator_id, t.update_time
  </sql>

  <select id="fuzzySearchPage" flushCache="false"  parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussBecfMainVo" resultMap="VoResultMap" useCache="true">
    select
    <include refid="Vo_Column_List" />
    ,t2.entity_code
    ,t2.entity_c_name
    ,t2.entity_l_name
    ,t2.entity_e_name
    ,cfr.user_name as confirm_name
    ,cr.user_name as creator_name
    ,up.user_name as updator_name
    from atr_buss_claim_import_main t
    LEFT JOIN bpluser.bbs_conf_entity t2
    ON t2.entity_id = t.entity_id
    left join bpluser.bpl_saa_user cfr
    on t.CONFIRM_ID = cfr.user_id
    left join bpluser.bpl_saa_user cr
    on t.creator_id = cr.user_id
    left join bpluser.bpl_saa_user up
    on t.updator_id = up.user_id
    <where>
      <include refid="Fuzzy_Query_Conditions"></include>
    </where>
    order by t.YEAR_MONTH asc, t.VERSION_NO desc
  </select>

  <select id="findByMainId" flushCache="false"  parameterType="Long" resultMap="VoResultMap" useCache="true">
    select
    <include refid="Vo_Column_List" />
    ,t2.entity_code
    ,t2.entity_c_name
    ,t2.entity_l_name
    ,t2.entity_e_name
    ,cfr.user_name as confirm_name
    ,cr.user_name as creator_name
    ,up.user_name as updator_name
    from atr_buss_claim_import_main t
    LEFT JOIN bpluser.bbs_conf_entity t2
    ON t2.entity_id = t.entity_id
    left join bpluser.bpl_saa_user cfr
    on t.CONFIRM_ID = cfr.user_id
    left join bpluser.bpl_saa_user cr
    on t.creator_id = cr.user_id
    left join bpluser.bpl_saa_user up
    on t.updator_id = up.user_id
    where claim_main_id = #{claimMainId,jdbcType=DECIMAL}
  </select>

  <select id="findByVo" flushCache="false"   resultMap="VoResultMap" useCache="true">
    select
    <include refid="Vo_Column_List" />
    from atr_buss_claim_import_main t
    where t.entity_id = #{entityId,jdbcType=DECIMAL}
    and t.YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
    and t.CONFIRM_IS = '1'
  </select>

  <select id="countConfirm" flushCache="false" useCache="true" resultType="Integer" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussBecfMain">
    select count(1) from atr_buss_claim_import_main
    where entity_id = #{entityId,jdbcType=DECIMAL}
      and YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
      and CONFIRM_IS = '1'
  </select>

  <!-- 按对象查询会计期间记录的WHERE部分 -->
  <sql id="Fuzzy_Query_Conditions">
    <trim prefixOverrides="and">
      <if test="entityId != null ">
        and t.entity_id = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
        and t.YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="confirmIs != null and confirmIs != ''">
        and t.CONFIRM_IS = #{confirmIs,jdbcType=CHAR}
      </if>
      <if test="creatorName != null and creatorName != ''">
        and cr.user_name = #{creatorName,jdbcType=VARCHAR}
      </if>
    </trim>
  </sql>
</mapper>