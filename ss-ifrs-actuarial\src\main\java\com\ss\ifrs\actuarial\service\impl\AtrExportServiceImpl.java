package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.conf.AppConfig;
import com.ss.ifrs.actuarial.feign.BmsTrackExportFeignClient;
import com.ss.ifrs.actuarial.service.AtrConfCodeService;
import com.ss.ifrs.actuarial.service.AtrExportService;
import com.ss.ifrs.actuarial.util.ExcelDropdownHandler;
import com.ss.library.excel.ExcelSheet;
import com.ss.library.mybatis.model.Page;
import com.ss.library.utils.ExcelDownloadUtil;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.SystemConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.pojo.bms.log.vo.BmsLogExportTrackVo;
import com.ss.platform.util.ExcelExportUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class AtrExportServiceImpl implements AtrExportService {

    protected final Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    AppConfig appConfig;

    @Autowired
    AtrConfCodeService atrConfCodeService;

    @Autowired
    BmsTrackExportFeignClient bmsTrackExportFeignClient;

    private static final String ZIP = ".zip";
    private static final String XLSX = ".xlsx";
 
    /*
    * 传入Page对象导出
    * */
    @Override
    public void exportPage(HttpServletRequest request, HttpServletResponse response, Page<?> page, Class voClazz, String beanName, String fileName, String targetRouter, Long userId) throws Exception {
        this.checkFileNameIsExist(fileName);
        String srcFilePath = getTemplatePath();
        String targetFilePath = getOutPutPath();
        String saveFilePath = getOutPutPathSave();
        Date outPutTime = new Date();
        SimpleDateFormat formatterTime = new SimpleDateFormat("HHmmss");
        String formatterTimeCode = formatterTime.format(outPutTime);
        atrConfCodeService.cacheSsTranslateCode(voClazz);
        try {
            ExcelExportUtil.exportExcel(request, srcFilePath, targetFilePath, fileName, page.getContent(), beanName, formatterTimeCode);
            ExcelDownloadUtil.downloadExcel(request, response, targetFilePath, fileName, formatterTimeCode);
            String exportFileName = fileName + XLSX;
            this.saveExportTrackLog(exportFileName, targetRouter, saveFilePath + fileName + formatterTimeCode + XLSX, userId, true);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            throw e;
        }
    }

    /*
     * 传入List对象导出
     * */
    @Override
    public void exportList(HttpServletRequest request, HttpServletResponse response, List<?> list, Class voClazz, String beanName, String fileName, String targetRouter, Long userId) throws Exception {
        this.checkFileNameIsExist(fileName);
        String srcFilePath = getTemplatePath();
        String targetFilePath = getOutPutPath();
        String saveFilePath = getOutPutPathSave();
        Date outPutTime = new Date();
        SimpleDateFormat formatterTime = new SimpleDateFormat("HHmmss");
        String formatterTimeCode = formatterTime.format(outPutTime);
        atrConfCodeService.cacheSsTranslateCode(voClazz);
        try {
            ExcelExportUtil.exportFile(request, srcFilePath, targetFilePath, fileName, list, beanName, formatterTimeCode);
            ExcelDownloadUtil.downloadExcel(request, response, targetFilePath, fileName, formatterTimeCode);
            String exportFileName = fileName + XLSX;
            this.saveExportTrackLog(exportFileName, targetRouter, saveFilePath + fileName + formatterTimeCode + XLSX, userId, true);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            throw e;
        }
    }

    @Override
    public String generateExcelBySheetList(HttpServletRequest request, List<ExcelSheet> sheetList, String fileName) throws Exception {
        this.checkFileNameIsExist(fileName);
        String srcFilePath = getTemplatePath();
        String targetFilePath = getOutPutPath();
        Date outPutTime = new Date();
        SimpleDateFormat formatterTime = new SimpleDateFormat("HHmmss");
        String formatterTimeCode = formatterTime.format(outPutTime);
        try {
            ExcelExportUtil.exportFile(request, srcFilePath, targetFilePath, fileName, sheetList, formatterTimeCode);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            throw e;
        }
        return targetFilePath + fileName + formatterTimeCode + XLSX;
    }

    @Override
    public void exportSheetList(HttpServletRequest request, HttpServletResponse response, List<ExcelSheet> sheetList,  String fileName, String targetRouter, Long userId) throws Exception {
        this.checkFileNameIsExist(fileName);
        String srcFilePath = getTemplatePath();
        String targetFilePath = getOutPutPath();
        String saveFilePath = getOutPutPathSave();
        Date outPutTime = new Date();
        SimpleDateFormat formatterTime = new SimpleDateFormat("HHmmss");
        String formatterTimeCode = formatterTime.format(outPutTime);
        try {
            ExcelExportUtil.exportFile(request, srcFilePath, targetFilePath, fileName, sheetList, formatterTimeCode);
            ExcelDownloadUtil.downloadExcel(request, response, targetFilePath, fileName, formatterTimeCode);
            String exportFileName = fileName + XLSX;
            this.saveExportTrackLog(exportFileName, targetRouter, saveFilePath + fileName + formatterTimeCode + XLSX, userId, true);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            throw e;
        }
    }

    @Override
    public Map<String,String> syncExportSheetList(String language, List<ExcelSheet> sheetList,
                                                  List<Class> voClassList, String fileName,
                                                  String targetRouter, Long userId) throws Exception {
        this.checkFileNameIsExist(fileName);
        Map<String,String> map = new HashMap<>(2);
        String srcFilePath = getTemplatePath();
        String targetFilePath = getOutPutPath();
        Date outPutTime = new Date();
        SimpleDateFormat formatterTime = new SimpleDateFormat("HHmmss");
        String formatterTimeCode = formatterTime.format(outPutTime);
        try {
            ExcelExportUtil.syncExportFile(language, srcFilePath, targetFilePath, fileName, sheetList, formatterTimeCode);
            map.put(fileName + formatterTimeCode+ XLSX, targetFilePath + fileName + formatterTimeCode + XLSX) ;
            return map;
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            throw e;
        }
    }

    @Override
    public Map<String, String> syncExportSheetList(String language, List<ExcelSheet> sheetList,
                                                   List<Class> voClassList, String fileName,
                                                   String targetRouter, String feeType, Integer currentSheetNo, Integer totalSheetNo) throws Exception {
        this.checkFileNameIsExist(fileName);
        Map<String,String> map = new HashMap<>(2);
        String srcFilePath = getTemplatePath();
        String targetFilePath = getOutPutPath();
        Date outPutTime = new Date();
        SimpleDateFormat formatterTime = new SimpleDateFormat("HHmmss");
        String formatterTimeCode = formatterTime.format(outPutTime);
        try {

            ExcelExportUtil.syncExportFile(language, srcFilePath, targetFilePath, fileName, sheetList, feeType);
            // 1. 创建临时文件
            File tempFile = new File(targetFilePath + fileName + feeType + XLSX);
            processSheets(tempFile,currentSheetNo,  totalSheetNo);
            map.put(fileName + feeType+ XLSX, targetFilePath + fileName + feeType + XLSX) ;
            return map;
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            throw e;
        }
    }

    // 处理Sheet页 - 删除不需要的Sheet
    private static void  processSheets(File inputFile, Integer currentSheetNo, Integer totalSheetNo) throws Exception {
        System.out.println("开始处理Sheet页...");
        Workbook workbook = null;
        FileInputStream fis = null;
        FileOutputStream fos = null;

        try {
            // 1. 读取源文件
            fis = new FileInputStream(inputFile);
            workbook = WorkbookFactory.create(fis);

            // 2. 要删除的Sheet索引（0-3和5-9）
            List<Integer> sheetsToDelete = new ArrayList<>();
            for (int i = 0; i < currentSheetNo; i++) sheetsToDelete.add(i);      // Sheet 1-4
            for (int i = currentSheetNo+1; i < totalSheetNo; i++) sheetsToDelete.add(i);     // Sheet 6-10

            // 3. 按索引降序排序（从后往前删除）
            sheetsToDelete.sort(Comparator.reverseOrder());

            // 4. 删除Sheet页
            for (int index : sheetsToDelete) {
                if (index < workbook.getNumberOfSheets()) {
                    String sheetName = workbook.getSheetAt(index).getSheetName();
                    workbook.removeSheetAt(index);
                    System.out.println("已删除Sheet: " + sheetName);
                }
            }

            // 5. 保存修改到原文件
            fos = new FileOutputStream(inputFile);
            workbook.write(fos);

        } finally {
            // 6. 确保关闭所有资源
            if (fis != null) fis.close();
            if (fos != null) fos.close();
            if (workbook != null) workbook.close();
        }
    }


    /**
     * @deprecated  导出多Sheet的Excel文件方法
     * @param sheetList sheet页数据列表包含[页号、页数据], fileName-导出模板文件名, logFileName-导出Excel日志文件名, targetRouter-模板文件路径, userId-当前用户id
     * @return void
     * @date 2024/1/24
     * */
    @Override
    public void exportSheetList(HttpServletRequest request, HttpServletResponse response,
                                List<ExcelSheet> sheetList,
                                String tempSrc,
                                String fileName,
                                String logFileName,
                                String targetRouter,
                                Long userId) throws Exception {
        this.checkFileNameIsExist(fileName);
        String targetFilePath = getOutPutPath();
        String saveFilePath = getOutPutPathSave();
        Date outPutTime = new Date();
        SimpleDateFormat formatterTime = new SimpleDateFormat("HHmmss");
        String formatterTimeCode = formatterTime.format(outPutTime)+1;
        sheetList.stream().forEach(sheet -> {
            sheet.getSheetDataList().forEach(excelSheetData -> {
                if (ObjectUtils.isNotEmpty(excelSheetData.getVoClazz())) {
                    atrConfCodeService.cacheSsTranslateCode(excelSheetData.getVoClazz());
                }
            });
        });
        try {
            ExcelExportUtil.exportFileByTemplate(request, tempSrc, targetFilePath, fileName, sheetList, formatterTimeCode);
            ExcelDownloadUtil.downloadExcel(request, response, targetFilePath, fileName, formatterTimeCode);
            String exportFileName = (ObjectUtils.isNotEmpty(logFileName)? logFileName : fileName) + XLSX;
            this.saveExportTrackLog(exportFileName, targetRouter, saveFilePath + fileName + formatterTimeCode + XLSX, userId, true);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            throw e;
        }
    }

    @Override
    public Map<String, String> exportSheetListReturnName(HttpServletRequest request, HttpServletResponse response,
                                                         List<ExcelSheet> sheetList,
                                                         String tempSrc,
                                                         String fileName,
                                                         String logFileName,
                                                         String targetRouter,
                                                         Long userId) throws Exception {
        Map<String,String> map = new HashMap<>(2);
        this.checkFileNameIsExist(fileName);
        String targetFilePath = getOutPutPath();
        String saveFilePath = getOutPutPathSave();
        Date outPutTime = new Date();
        SimpleDateFormat formatterTime = new SimpleDateFormat("HHmmss");
        String formatterTimeCode = formatterTime.format(outPutTime)+1;
        sheetList.stream().forEach(sheet -> {
            sheet.getSheetDataList().forEach(excelSheetData -> {
                if (ObjectUtils.isNotEmpty(excelSheetData.getVoClazz())) {
                    atrConfCodeService.cacheSsTranslateCode(excelSheetData.getVoClazz());
                }
            });
        });
        try {
            ExcelExportUtil.exportFileByTemplate(request, tempSrc, targetFilePath, fileName, sheetList, formatterTimeCode);
            ExcelDownloadUtil.downloadExcel(request, response, targetFilePath, fileName, formatterTimeCode);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            throw e;
        }
        map.put(fileName + XLSX, targetFilePath + fileName + formatterTimeCode + XLSX) ;
        return map;
    }

    public Long saveExportTrackLog(String exportFileName, String targetRouter, String filePath, Long userId, Boolean successfulIs) {
        BmsLogExportTrackVo bmsLogExportTrackVo = new BmsLogExportTrackVo();
        bmsLogExportTrackVo.setFileName(exportFileName);
        bmsLogExportTrackVo.setTargetRouter(targetRouter);
        bmsLogExportTrackVo.setFilePath(filePath);
        bmsLogExportTrackVo.setSystemCode(SystemConstant.AtrIdentity.APP_CODE);
        bmsLogExportTrackVo.setDirection("1");
        bmsLogExportTrackVo.setCreatorId(userId);
        bmsLogExportTrackVo.setCreateTime(new Date());
        bmsLogExportTrackVo.setTaskStatus(successfulIs ? CommonConstant.ExportLogTaskStatus.SUCCESSFUL : CommonConstant.ExportLogTaskStatus.PROGRESS);
        BaseResponse<Long> response = bmsTrackExportFeignClient.save(bmsLogExportTrackVo);
        return response.getResData();
    }

    public void updateExportTrackLog(Long logId,String taskStatus,Long userId,String errorMsg ) {
        BmsLogExportTrackVo logVo = new BmsLogExportTrackVo();
        logVo.setExportTrackLogId(logId);
        logVo.setTaskStatus(taskStatus);
        logVo.setErrorMsg(errorMsg);
        logVo.setUpdatorId(userId);
        logVo.setUpdateTime(new Date());
        bmsTrackExportFeignClient.update(logVo);
    }

    public String getTemplatePath() {
        String basePath = appConfig.getBasePath();
        String templatePath = appConfig.getExportExcelTemplate();
        String srcFilePath = basePath + templatePath;
        return srcFilePath;
    }
    public String getOutPutPath() {
        Date outPutTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy/MM/dd");
        String formattedDate = formatter.format(outPutTime);
        String basePath = appConfig.getBasePath();
        String outPutPath = appConfig.getExportExcelOutTemplate();
        String targetFilePath = basePath + outPutPath + formattedDate + "/";
        return targetFilePath;
    }

    public String getOutPutPathSave() {
        Date outPutTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy/MM/dd");
        String formattedDate = formatter.format(outPutTime);
        String outPutPath = appConfig.getExportExcelOutTemplate();
        String saveFilePath =outPutPath + formattedDate + "/";
        return saveFilePath;
    }


    /**
     * 压缩文件
     *
     * @param files 文件map，key为文件名称，value为文件路径
     */
    public void dealZip(String zipName, Map<String,String>files, String targetRouter, Long userId) {
        String zipFileName;
        if (zipName.contains(ZIP)) {
            zipFileName = zipName;
        } else {
            zipFileName = getZipName(zipName, null);
        }
        String saveFilePath = getOutPutPath();
        ExcelExportUtil.dealZip(zipFileName, saveFilePath, files);
    }



    @Override
    public void downloadTemplateExcel(HttpServletRequest request, HttpServletResponse response, String fileName, String targetRouter, Long userId) throws Exception {
        String srcFilePath = getTemplatePath();
        ExcelDownloadUtil.downloadExcel(request, response, srcFilePath, fileName,"");
    }

    /**
     * @deprecated  导出多Sheet的Excel文件方法
     * @param sheetList sheet页数据列表包含[页号、页数据], fileName-导出模板文件名, logFileName-导出Excel日志文件名, targetRouter-模板文件路径, userId-当前用户id
     * @return void
     * @date 2024/1/24
     * */
    @Override
    public void exportExcelSheetList(HttpServletRequest request, HttpServletResponse response, List<ExcelSheet> sheetList, String fileName, String logFileName, String targetRouter, Long userId) throws Exception {
        if (ObjectUtils.isEmpty(fileName)) {
            throw new Exception("Excel模板名称为空");
        }
        String srcFilePath = getTemplatePath();
        String targetFilePath = getOutPutPath();
        String saveFilePath = getOutPutPathSave();
        Date outPutTime = new Date();
        SimpleDateFormat formatterTime = new SimpleDateFormat("HHmmss");
        String formatterTimeCode = formatterTime.format(outPutTime);
        try {
            ExcelExportUtil.exportFile(request, srcFilePath, targetFilePath, fileName, sheetList, formatterTimeCode);
            ExcelDownloadUtil.downloadExcel(request, response, targetFilePath, fileName, formatterTimeCode);
            String exportFileName = (ObjectUtils.isNotEmpty(logFileName)? logFileName : fileName) + ".xlsx";
            this.saveExportTrackLog(exportFileName, targetRouter, saveFilePath + fileName + formatterTimeCode + ".xlsx", userId, true);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            throw e;
        }
    }

    /**
     * 获取压缩文件名
     */
    public String getZipName(String zipName, String logFileName) {
        Date outPutTime = new Date();
        SimpleDateFormat formatterTime = new SimpleDateFormat("HHmmss");
        String formatterTimeCode = formatterTime.format(outPutTime);
        String zipFileName = (ObjectUtils.isNotEmpty(logFileName)? logFileName : zipName) + formatterTimeCode + ZIP;
        return zipFileName;
    }

    /**
     * 导出多Sheet的Excel文件方法，支持扩展参数
     * @param request HTTP请求
     * @param response HTTP响应
     * @param sheetList sheet页数据列表
     * @param fileName 导出模板文件名
     * @param extendMap 扩展参数
     * @param targetRouter 模板文件路径
     * @param userId 当前用户id
     * @throws Exception 异常
     */
    @Override
    public void exportExcelSheetList(HttpServletRequest request, HttpServletResponse response, List<ExcelSheet> sheetList, String fileName, Map<String, Object> extendMap, String targetRouter, Long userId) throws Exception {
        if (ObjectUtils.isEmpty(fileName)) {
            throw new Exception("Excel模板名称为空");
        }
        String srcFilePath = getTemplatePath();
        String targetFilePath = getOutPutPath();
        String saveFilePath = getOutPutPathSave();
        Date outPutTime = new Date();
        SimpleDateFormat formatterTime = new SimpleDateFormat("HHmmss");
        String formatterTimeCode = formatterTime.format(outPutTime);
        try {
            // 导出Excel文件
            ExcelExportUtil.exportFile(request, srcFilePath, targetFilePath, fileName, sheetList, formatterTimeCode);
            
            // 如果有下拉选项，添加到Excel文件中
            if (extendMap != null) {
                // 打开Excel文件
                String excelFilePath = targetFilePath + fileName + formatterTimeCode + ".xlsx";
                try (FileInputStream fis = new FileInputStream(excelFilePath);
                     Workbook workbook = WorkbookFactory.create(fis)) {
                    
                    boolean hasDropdowns = false;
                    
                    // 为第一个sheet添加下拉选项
                    if (extendMap.containsKey("sheet0DropdownOptions")) {
                        @SuppressWarnings("unchecked")
                        List<String> options = (List<String>) extendMap.get("sheet0DropdownOptions");
                        if (workbook.getNumberOfSheets() > 0) {
                            Sheet sheet = workbook.getSheetAt(0);
                            ExcelDropdownHandler.addDropdownList(sheet, options, 1, 1000, 4, 4);
                            hasDropdowns = true;
                        }
                    }
                    
                    // 为第二个sheet添加下拉选项
                    if (extendMap.containsKey("sheet1DropdownOptions")) {
                        @SuppressWarnings("unchecked")
                        List<String> options = (List<String>) extendMap.get("sheet1DropdownOptions");
                        if (workbook.getNumberOfSheets() > 1) {
                            Sheet sheet = workbook.getSheetAt(1);
                            ExcelDropdownHandler.addDropdownList(sheet, options, 1, 1000, 4, 4);
                            hasDropdowns = true;
                        }
                    }
                    
                    // 如果添加了下拉选项，保存修改后的Excel文件
                    if (hasDropdowns) {
                        try (FileOutputStream fos = new FileOutputStream(excelFilePath)) {
                            workbook.write(fos);
                        }
                    }
                }
            }
            
            // 下载Excel文件
            ExcelDownloadUtil.downloadExcel(request, response, targetFilePath, fileName, formatterTimeCode);
            
            // 保存导出日志
            String exportFileName = fileName + ".xlsx";
            this.saveExportTrackLog(exportFileName, targetRouter, saveFilePath + fileName + formatterTimeCode + ".xlsx", userId, true);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            throw e;
        }
    }

    private void checkFileNameIsExist(String fileName) throws Exception{
        if (ObjectUtils.isEmpty(fileName)) {
            throw new Exception("Excel Template does not exist");
        }
    }
}
