--增加字段：模板分类
alter table rpt_conf_report_template add template_class varchar2(32);
comment on column rpt_conf_report_template.template_class is 'template_class|模板分类';

alter table rpt_conf_report_templatehis add template_class varchar2(32);
comment on column rpt_conf_report_templatehis.template_class is 'template_class|模板分类';

alter table rpt_buss_report add template_class varchar2(32);
comment on column rpt_buss_report.template_class is 'template_class|模板分类';

alter table rpt_buss_reporthis add template_class varchar2(32);
comment on column rpt_buss_reporthis.template_class is 'template_class|模板分类';


--增加码表：模板分类
INSERT INTO rpt_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES ((rpt_seq_conf_code.nextval) , '0', 'RptTemplateClass', '模板分类', '模板分類', 'Template Class', '', NULL, '1', '1', null, null, null);

INSERT INTO rpt_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES ((rpt_seq_conf_code.nextval), (select code_id from rpt_conf_code where code_code = 'RptTemplateClass'), '1', '披露报表', '披露報表', 'Disclosure Statement', '', '1', '1', '1', null, null, null);
INSERT INTO rpt_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no, valid_is, creator_id, create_time, updator_id, update_time) VALUES ((rpt_seq_conf_code.nextval), (select code_id from rpt_conf_code where code_code = 'RptTemplateClass'), '2', '财务报表', '財務報表', 'Financial Statement', '', '2', '1', '1', null, null, null);

commit;