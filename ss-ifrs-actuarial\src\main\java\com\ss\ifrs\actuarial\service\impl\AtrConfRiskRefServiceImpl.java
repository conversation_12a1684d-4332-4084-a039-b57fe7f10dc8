package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.dao.conf.AtrConfRiskRefDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfRiskRefHisDao;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfRiskRef;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfRiskRefHis;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfRiskRefVo;
import com.ss.ifrs.actuarial.service.AtrConfRiskRefService;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ClassUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/23
 */
@Service
public class AtrConfRiskRefServiceImpl implements AtrConfRiskRefService {
    @Autowired
    AtrConfRiskRefDao atrConfRiskRefDao;

    @Autowired
    AtrConfRiskRefHisDao atrConfRiskRefHisDao;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void save(AtrConfRiskRefVo atrConfRiskRefVo, Long userId) {
        AtrConfRiskRef atrConfRiskRef = ClassUtil.convert(atrConfRiskRefVo, AtrConfRiskRef.class);
        atrConfRiskRef.setValidIs(CommonConstant.ValidStatus.VALID);
        atrConfRiskRef.setAuditState(CommonConstant.AuditStatus.TO_BE_AUDITED);
        atrConfRiskRef.setSerialNo(1);
        atrConfRiskRefDao.save(atrConfRiskRef);
        dealSaveHis(atrConfRiskRef, userId, CommonConstant.OperType.ADD);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void saveList(List<AtrConfRiskRefVo> atrConfQuotaVos, Long userId) {

    }

    @Override
    public AtrConfRiskRefVo findById(Long riskRefId) {
        AtrConfRiskRefVo atrConfRiskRefVo = atrConfRiskRefDao.findByRiskRefId(riskRefId);
        //AtrConfRiskRefVo atrConfRiskRefVo = ClassUtil.convert(atrConfRiskRef, AtrConfRiskRefVo.class);
        return atrConfRiskRefVo;
    }

    @Override
    public AtrConfRiskRefVo findHisById(Long riskRefHisId) {
        AtrConfRiskRefVo atrConfRiskRefVo = atrConfRiskRefHisDao.findByRiskRefHisId(riskRefHisId);
        return atrConfRiskRefVo;
    }

    @Override
    public Page<AtrConfRiskRefVo> searchPage(AtrConfRiskRefVo atrConfQuotaVo, Pageable pageParam) {
        Page<AtrConfRiskRefVo> atrConfRiskRefVoPage = atrConfRiskRefDao.fuzzySearchPage(atrConfQuotaVo, pageParam);

        return atrConfRiskRefVoPage;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void update(AtrConfRiskRefVo atrConfRiskRefVo, Long userId) {
        AtrConfRiskRef atrConfRiskRef = ClassUtil.convert(atrConfRiskRefVo, AtrConfRiskRef.class);
        Integer serialNo = atrConfRiskRef.getSerialNo();
        atrConfRiskRef.setSerialNo(ObjectUtils.isEmpty(serialNo) ? 1 : ++serialNo);
        atrConfRiskRefDao.updateById(atrConfRiskRef);
        dealSaveHis(atrConfRiskRef, userId, CommonConstant.OperType.MODIFY);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void delete(Long riskRefId, Long userId) {
        //写入轨迹表
        AtrConfRiskRef atrConfRiskRef = atrConfRiskRefDao.findById(riskRefId);
        Integer serialNo = atrConfRiskRef.getSerialNo();
        atrConfRiskRef.setSerialNo(ObjectUtils.isEmpty(serialNo) ? 1 : ++serialNo);
        this.dealSaveHis(atrConfRiskRef, userId, CommonConstant.OperType.DELETE);
        atrConfRiskRefDao.deleteById(riskRefId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void updateValid(AtrConfRiskRefVo atrConfRiskRefVo, Long userId) {
        AtrConfRiskRef po = new AtrConfRiskRef();
        Long riskRefId = atrConfRiskRefVo.getRiskRefId();
        if (riskRefId != null) {
            po = ClassUtil.convert(this.findById(riskRefId), AtrConfRiskRef.class);
            po.setValidIs(atrConfRiskRefVo.getValidIs());
        }
        if (po != null) {
            po.setUpdateTime(new Date());
            //待审核时，审核意见应为空
            po.setAuditState("0");
            po.setCheckedMsg(null);
            po.setCheckedId(null);
            po.setCheckedTime(null);
            atrConfRiskRefDao.updateById(po);
            this.dealSaveHis(po, userId, CommonConstant.OperType.MODIFY);

            //保存轨迹信息
            //dealSaveHis(po, userId,"4");
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void auditList(List<AtrConfRiskRefVo> atrConfRiskRefVoList, Long userId) {
        List<AtrConfRiskRef> atrConfRiskRefList = ClassUtil.convert(atrConfRiskRefVoList, AtrConfRiskRef.class);
        atrConfRiskRefList.stream().forEach(atrConfRiskRef->{
            this.dealSaveHis(atrConfRiskRef, userId, CommonConstant.OperType.AUDIT);
            atrConfRiskRefDao.updateById(atrConfRiskRef);
        });
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void audit(AtrConfRiskRefVo atrConfRiskRefVo, Long userId) {
        AtrConfRiskRef po = ClassUtil.convert(atrConfRiskRefVo, AtrConfRiskRef.class);
        atrConfRiskRefDao.updateById(po);
        this.dealSaveHis(po, userId, CommonConstant.OperType.AUDIT);

    }

    @Override
    public Long checkNaturalPk(AtrConfRiskRefVo atrConfQuotaVo) {
        return atrConfRiskRefDao.checkNaturalPk(atrConfQuotaVo);
    }




    private void dealSaveHis(AtrConfRiskRef atrConfRiskRef, Long operId, String operType) {
        AtrConfRiskRefHis atrConfRiskRefHis = new AtrConfRiskRefHis();
        ClassUtil.copyProperties(atrConfRiskRef, atrConfRiskRefHis);
        atrConfRiskRefHis.setOperId(operId);
        atrConfRiskRefHis.setOperTime(new Date());
        atrConfRiskRefHis.setOperType(operType);
        atrConfRiskRefHisDao.save(atrConfRiskRefHis);
    }
}
