<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-01-11 16:41:01 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussLrcCfPeriodDetailDao">
  <!-- 本配置文件由GIS MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**IDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->
  <select id="findPeriodById" flushCache="false" useCache="true" resultType="Integer" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfPeriod">
    select
      DISTINCT cpd.dev_period
    from ATR_BUSS_LRC_CF_PERIOD_DETAIL  cpd
    LEFT JOIN atr_buss_lrc_cf_period cp
      ON cpd.lrc_cf_period_id = cp.lrc_cf_period_id
    WHERE cp.lrc_cf_main_id = #{lrcCfMainId,jdbcType=DECIMAL}
      AND cp.LRC_CF_FEE_TYPE= #{lrcCfFeeType,jdbcType=VARCHAR}
    ORDER BY cpd.dev_period
  </select>

  <select id="findPeriodDetail" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfPeriod">
    select
      cpd.LRC_CF_PERIOD_ID, cpd.DEV_PERIOD, cpd.LRC_CF_AMOUNT
    from ATR_BUSS_LRC_CF_PERIOD_DETAIL  cpd
       LEFT JOIN atr_buss_lrc_cf_period cp
       ON cpd.lrc_cf_period_id = cp.lrc_cf_period_id
    WHERE cp.lrc_cf_main_id = #{lrcCfMainId,jdbcType=DECIMAL}
      AND cp.LRC_CF_FEE_TYPE= #{lrcCfFeeType,jdbcType=VARCHAR}
    ORDER BY cpd.dev_period
  </select>

  <delete id="deleteByLrcCfMainId" flushCache="true" parameterType="java.lang.Long">
    delete from ATR_BUSS_LRC_CF_PERIOD_DETAIL cpd
    where exists (
      select 1 from atr_buss_lrc_cf_period cp WHERE cpd.lrc_cf_period_id=cp.lrc_cf_period_id AND cp.lrc_cf_main_id = #{lrcCfMainId,jdbcType=DECIMAL}
    )
  </delete>
</mapper>