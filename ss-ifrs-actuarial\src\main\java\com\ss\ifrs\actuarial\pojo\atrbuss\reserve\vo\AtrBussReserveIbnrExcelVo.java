package com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo;

/**
 * @ClassName AtrBussReserveIbnrExcelVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/9/5
 **/
public class AtrBussReserveIbnrExcelVo {

    private String dataType;
    private String damageYearMonth;
    private Integer devPeriod;
    private String value;

    private String codeCodeInx;

    private String codeCode;
    private String codeCName;
    private String codeLName;
    private String codeEName;

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getDamageYearMonth() {
        return damageYearMonth;
    }

    public void setDamageYearMonth(String damageYearMonth) {
        this.damageYearMonth = damageYearMonth;
    }

    public Integer getDevPeriod() {
        return devPeriod;
    }

    public void setDevPeriod(Integer devPeriod) {
        this.devPeriod = devPeriod;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getCodeCodeInx() {
        return codeCodeInx;
    }

    public void setCodeCodeInx(String codeCodeInx) {
        this.codeCodeInx = codeCodeInx;
    }

    public String getCodeCode() {
        return codeCode;
    }

    public void setCodeCode(String codeCode) {
        this.codeCode = codeCode;
    }

    public String getCodeCName() {
        return codeCName;
    }

    public void setCodeCName(String codeCName) {
        this.codeCName = codeCName;
    }

    public String getCodeLName() {
        return codeLName;
    }

    public void setCodeLName(String codeLName) {
        this.codeLName = codeLName;
    }

    public String getCodeEName() {
        return codeEName;
    }

    public void setCodeEName(String codeEName) {
        this.codeEName = codeEName;
    }
}
