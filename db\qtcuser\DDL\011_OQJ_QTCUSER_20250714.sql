drop table if exists qtc_buss_evl_acc_result ;

create table qtc_buss_evl_acc_result
(
    result_id            bigint generated always as identity,
    year_month           varchar(6),
    evl_main_id          bigint,
    business_source_code varchar(6),
    portfolio_no         varchar(64),
    icg_no               varchar(64),
    str1                 varchar(32),
    str2                 varchar(32),
    str3                 varchar(32),
    str4                 varchar(32),
    str5                 varchar(32),
    str6                 varchar(32),
    str7                 varchar(32),
    str8                 varchar(32),
    str9                 varchar(32),
    str10                varchar(32),
    str11                varchar(32),
    str12                varchar(32),
    str13                varchar(32),
    str14                varchar(32),
    str15                varchar(32),
    var1                 varchar(6),
    num1                 numeric(32, 8),
    num2                 numeric(32, 8),
    num3                 numeric(32, 8),
    num4                 numeric(32, 8),
    num5                 numeric(32, 8),
    num6                 numeric(32, 8),
    num7                 numeric(32, 8),
    num8                 numeric(32, 8),
    num9                 numeric(32, 8),
    num10                numeric(32, 8),
    num11                numeric(32, 8),
    num12                numeric(32, 8),
    num13                numeric(32, 8),
    num14                numeric(32, 8),
    num15                numeric(32, 8),
    num16                numeric(32, 8),
    num17                numeric(32, 8),
    num18                numeric(32, 8),
    num19                numeric(32, 8),
    num20                numeric(32, 8),
    num21                numeric(32, 8),
    num22                numeric(32, 8),
    num23                numeric(32, 8),
    num24                numeric(32, 8),
    num25                numeric(32, 8),
    num26                numeric(32, 8),
    num27                numeric(32, 8),
    num28                numeric(32, 8),
    num29                numeric(32, 8),
    num30                numeric(32, 8),
    num31                numeric(32, 8),
    num32                numeric(32, 8),
    num33                numeric(32, 8),
    num34                numeric(32, 8),
    num35                numeric(32, 8)
) tablespace  qtc_space ;

comment on table qtc_buss_evl_acc_result is '计量结果：计量推送会计数据';

create index idx_qtc_buss_evl_acc_result_evl_main_id on qtc_buss_evl_acc_result (evl_main_id);