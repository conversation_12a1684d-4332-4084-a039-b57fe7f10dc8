create or replace view rpt_v_conf_report_item as
select v.report_item_id,
              v.serial_no,
              v.upper_report_item_id,
              v.report_item_code,
              replace(SUBSTR(v.report_item_c_name,2,length(v.report_item_c_name)-2),'|','/') AS report_item_c_name,
              replace(SUBSTR(v.report_item_l_name,2,length(v.report_item_l_name)-2),'|','/') AS report_item_l_name,
              replace(SUBSTR(v.report_item_e_name,2,length(v.report_item_e_name)-2),'|','/') AS report_item_e_name,
              v.remark,
              v.valid_is,
              v.creator_id,
              v.create_time,
              v.updator_id,
              v.update_time,
              v.audit_state,
              v.checked_id,
              v.checked_time,
              v.checked_msg
         from (select c.report_item_id,
                      c.serial_no,
                      c.upper_report_item_id,
                      c.report_item_code,
                      SYS_CONNECT_BY_PATH(c.report_item_c_name, '|') || '|' AS report_item_c_name,
                      SYS_CONNECT_BY_PATH(c.report_item_l_name, '|') || '|' AS report_item_l_name,
                      SYS_CONNECT_BY_PATH(c.report_item_e_name, '|') || '|' AS report_item_e_name,
                      c.remark,
                      c.valid_is,
                      c.creator_id,
                      c.create_time,
                      c.updator_id,
                      c.update_time,
                      c.audit_state,
                      c.checked_id,
                      c.checked_time,
                      c.checked_msg
                 from rpt_conf_report_item c
                START WITH c.upper_report_item_id = 0
               CONNECT BY PRIOR
                           c.report_item_id = c.upper_report_item_id) v
        order by v.report_item_code;
