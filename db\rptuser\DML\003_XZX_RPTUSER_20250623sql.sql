-- 更新自动生成字段
update RPT_CONF_REPORT_TEMPLATE
set AUTO_GENERATE = '1'
where AUTO_GENERATE is null;
commit;

-- 增加查看类型码值
delete from Rpt_Conf_Code where code_id in (select code_id from Rpt_v_Conf_Code where code_code_idx like
                                                                                      'RptShowTypeList%');

INSERT INTO rpt_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no,
                           valid_is, creator_id, create_time, updator_id, update_time)
VALUES ((nextval('rpt_seq_conf_code')),
        0,
        'RptShowTypeList', '报表模版支持查看类型', '報表模版支持查看類型', 'Report templates support viewing types',
        '', null, '1',
        1, null, null, null);

INSERT INTO rpt_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no,
                           valid_is, creator_id, create_time, updator_id, update_time)
VALUES ((nextval('rpt_seq_conf_code')),
        (select code_id from RPT_CONF_CODE where CODE_CODE = 'RptShowTypeList' and UPPER_CODE_ID = 0),
        '1', '自定义报表', '自定義報表', 'Custom report',
        '', 1, '1',
        1, null, null, null);

INSERT INTO rpt_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no,
                           valid_is, creator_id, create_time, updator_id, update_time)
VALUES ((nextval('rpt_seq_conf_code')),
        (select code_id from RPT_CONF_CODE where CODE_CODE = 'RptShowTypeList' and UPPER_CODE_ID = 0),
        '2', '会计报表预览', '會計報表預覽', 'ACC Report Preview',
        '', 1, '1',
        1, null, null, null);
commit;




update RPT_CONF_REPORT_TASK_CONDITION set show_type = '1' where show_type is null;
commit;





delete
from rpt_conf_code
where CODE_ID in (select code_id from RPT_v_CONF_CODE where CODE_CODE_IDX like 'RptItemType%');
INSERT INTO rpt_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no,
                           valid_is, creator_id, create_time, updator_id, update_time)
VALUES ((nextval('rpt_seq_conf_code')),
        0,
        'RptItemType', '预览报表的列项类型', '預覽報表的列項類型', 'Preview report column item types',
        '', 1, '1',
        1, null, null, null);
INSERT INTO rpt_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no,
                           valid_is, creator_id, create_time, updator_id, update_time)
VALUES ((nextval('rpt_seq_conf_code')),
        (select code_id from RPT_CONF_CODE where CODE_CODE = 'RptItemType' and UPPER_CODE_ID = 0),
        '1', '列报项', '列報項', 'Reporting item',
        '', 1, '1',
        1, null, null, null);

INSERT INTO rpt_conf_code (code_id, upper_code_id, code_code, code_c_name, code_l_name, code_e_name, remark, display_no,
                           valid_is, creator_id, create_time, updator_id, update_time)
VALUES ((nextval('rpt_seq_conf_code')),
        (select code_id from RPT_CONF_CODE where CODE_CODE = 'RptItemType' and UPPER_CODE_ID = 0),
        '2', '科目', '科目', 'Account',
        '', 1, '1',
        1, null, null, null);

commit;