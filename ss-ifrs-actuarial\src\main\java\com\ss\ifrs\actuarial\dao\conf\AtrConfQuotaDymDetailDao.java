/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2023-02-13 15:48:14
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao.conf;


import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDymDetail;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaDymDetailVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaDymMainVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaDymVo;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-13 15:48:14<br/>
 * Description: 假设值事故年月明细表 （基于事故年月） Dao类<br/>
 * Related Table Name: ATR_CONF_QUOTA_DYM_DETAIL<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrConfQuotaDymDetailDao extends IDao<AtrConfQuotaDymDetail, Long> {
    void deleteByQuotaId(Long quotaId);

    Integer deleteByDimension(AtrConfQuotaDymMainVo atrConfQuotaDymVo);

    List<AtrConfQuotaDymDetailVo> findByDimensionVo(AtrConfQuotaDymMainVo atrConfQuotaDymVo);

    List<String> findPeriodListByDimensionVo(AtrConfQuotaDymMainVo atrConfQuotaDymVo);

    void loadPrePeriodQuotaDymDetail(AtrConfQuotaDymVo atrConfQuotaDymVo);
}