package com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
public class AtrBussIbnrcalcSettledDevResultVo {

    private String riskCode;
    private Long entityId;
    private String claimNo;
    private String currencyCode;
    private Date approvalDate;
    private String policyNo;
    private Date effectiveDate;
    private Date expiryDate;
    private Date accidentDate;
    private String accidentYear;
    private String approvalYear;
    private Integer devNoOri;
    private Integer devNo;
    private BigDecimal settledAmount;
    private Long updateId;
    private Date updateTime;

    private String loaCode;
    private String loaName;
    private String entityCode;
    private String updateIdName;

}
