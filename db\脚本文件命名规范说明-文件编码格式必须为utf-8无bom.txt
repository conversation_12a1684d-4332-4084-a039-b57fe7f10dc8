1、按脚本执行用户分类放置，再继续按DDL、DML、PCK分类放置
     DDL:存放表结构调整的sql
     DML:存放表数据调整的sql
     PCK:存放存储过程、视图、触发器的sql/pck
     示例：
       ddl脚本：**/DB/accuser/ddl/*
       dml脚本：**/DB/accuser/dml/*
       pck存储过程或视图脚本：**/DB/accuser/pck/*

2、命名规范：
       ddl、dml脚本(字母必须大写)：
            001_CJF_ACCUSER_20220623.sql
            
       pck存储过程或视图脚本(字母必须小写)：
	        accuser.acc_pack_contra.pck
	        atruser.atr_v_evaluate_result_his.sql

3、格式规范
     sql脚本文件必须是UTF-8格式无BOM，防止中文字符导入数据库乱码