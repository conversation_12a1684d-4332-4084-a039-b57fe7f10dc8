<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-03-08 10:07:11 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussFOLrcIcuCalcDao">
  <!-- 本配置文件由GIS MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**IDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->
  <select id="countDateByVo" fetchSize="2000" flushCache="false" useCache="true" resultType="Long"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
    select
    count(a.ACTION_NO) as "actionNo"
    from atruser.ATR_BUSS_FO_LRC_ICU_CALC a
    where a.action_no = #{actionNo,jdbcType=VARCHAR}
    <if test="portfolioNo != null and portfolioNo != ''">
      and a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
    </if>
    <if test="icgNo != null and icgNo != ''">
      and a.icg_no = #{icgNo,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="findDateByVo" fetchSize="1000" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
    select
      a.ACTION_NO as "actionNo",
      a.RI_POLICY_NO as "riPolicyNo",
      a.RI_ENDORSE_SEQ_NO as "riEndorseSeqNo",
      a.currency_code as "currency",
      a.YEAR_MONTH as "yearMonth",
      a.PORTFOLIO_NO as "portfolioNo",
      a.ICG_NO as "icgNo",
      a.EVALUATE_APPROACH as "evaluateApproach",
      a.LOA_CODE as "loaCode",
      a.CMUNIT_NO as "cmunitNo",
      a.PRODUCT_CODE as "productCode",
      to_char(a.EVALUATE_DATE,'yyyy/mm/dd') as "evaluateDate",
      to_char(a.CONTRACT_DATE,'yyyy/mm/dd') as "contractDate",
      to_char(a.EFFECTIVE_DATE_IN_DATE,'yyyy/mm/dd') as "effectiveDateInDate",
      to_char(a.APPROVAL_DATE_IN_DATE,'yyyy/mm/dd') as "checkDateInDate",
      to_char(a.EXPIRY_DATE_IN_DATE,'yyyy/mm/dd') as "expiryDateInDate",
      to_char(a.EFFECTIVE_DATE_BOM,'yyyy/mm/dd') as "effectiveDateBom",
      to_char(a.EXPIRY_DATE_EOM,'yyyy/mm/dd') as "expiryDateEom",
      a.payment_frequency_code as "premiumFrequency",
      a.payment_frequency_no as "premiumTerm",
      a.GROSS_PREMIUM as "grossPremium",
      a.COVERAGE_AMOUNT as "coverageAmount",
      a.PASSED_DATES as "passedDates",
      a.PASSED_MONTHS as "passedMonths",
      a.remaining_months_for_recv as "remainingMonthsForRecv",
      a.REMAINING_PREM_TERM_PE as "remainingPremTermPe",
      a.REMAINING_MONTHS_FUTURE as "remainingMonthsFuture",
      a.REMAINING_PREM_TERM_CB as "remainingPremTermCb",
      a.PAYMENT_QUARTER as "paymentQuarter",
      a.CUMULATIVE_PAID_PREMIUM AS "cumulativePaidPremium",
      a.ed_premium_per_coverage_day as "edPremiumPerCoverageDay",
      a.ED_PREMIUM as "edPremium",
      a.PRI_CUR_END_REMAIN_CSM_RATE as "priCurEndRemainCsmRate",
      a.PRI_UNTIL_REPORT_REMAIN_CSM_RATE as "priUntilReportRemainCsmRate",
      a.CUMULATIVE_ED_RATE as "cumulativeEdRate",
      a.CUR_END_REMAIN_CSM_RATE as "curEndRemainCsmRate",
      a.UNTIL_REPORT_REMAIN_CSM_RATE as "untilReportRemainCsmRate",
      a.ELR as "elr",
      (select QUOTA_VALUE from Atr_Buss_Quota_Value t1 where a.action_no=t1.action_no
                                                            AND a.loa_code=t1.dimension_value AND quota_code='BE012'  limit 1) as "BE012",
      (select QUOTA_VALUE from Atr_Buss_Quota_Value t1 where a.action_no=t1.action_no
                                                            AND a.loa_code=t1.dimension_value AND quota_code='BE013'  limit 1) as "BE013",
      (select QUOTA_VALUE from Atr_Buss_Quota_Value t1 where a.action_no=t1.action_no
                                                            AND a.loa_code=t1.dimension_value AND quota_code='QR003'  limit 1) as "QR003",
      (select QUOTA_VALUE from Atr_Buss_Quota_Value t1 where a.action_no=t1.action_no
                                                            AND a.loa_code=t1.dimension_value AND quota_code='QR010' limit 1) as "QR010",
      c.entity_code as "entityCode",
      c.entity_e_name as "entityEName"
      <foreach collection="devNoList" item="item" index="index" open=","  separator=",">
        MAX(CASE cd.DEV_NO WHEN ${item} THEN cd.${feeType} ELSE NULL END) AS "${item}"
      </foreach>
    from (select * from ATR_BUSS_FO_LRC_ICU_CALC a
          where a.action_no = #{actionNo,jdbcType=VARCHAR}
          <if test="portfolioNo != null and portfolioNo != ''">
            and a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
          </if>
          <if test="icgNo != null and icgNo != ''">
            and a.icg_no = #{icgNo,jdbcType=VARCHAR}
          </if>
          order by a.ICG_NO,a.RI_POLICY_NO
          <if test="limit != null and offset != null">
            limit ${limit} offset ${offset}
          </if>
        ) a
     left join atruser.ATR_BUSS_FO_LRC_ICU_CALC_detail cd on a.id = cd.main_id
     left join bpluser.bbs_conf_entity c on a.entity_id = c.entity_id
     left join bpluser.BBS_CONF_CURRENCY cur on a.currency_code = cur.CURRENCY_CODE
    GROUP BY
    a.ACTION_NO,
    a.RI_POLICY_NO,
    a.RI_ENDORSE_SEQ_NO,
    a.currency_code,
    a.YEAR_MONTH,
    a.PORTFOLIO_NO,
    a.ICG_NO,
    a.EVALUATE_APPROACH,
    a.LOA_CODE,
    a.CMUNIT_NO,
    a.PRODUCT_CODE,
    to_char(a.EVALUATE_DATE,'yyyy/mm/dd'),
    to_char(a.CONTRACT_DATE,'yyyy/mm/dd'),
    to_char(a.EFFECTIVE_DATE_IN_DATE,'yyyy/mm/dd'),
    to_char(a.APPROVAL_DATE_IN_DATE,'yyyy/mm/dd'),
    to_char(a.EXPIRY_DATE_IN_DATE,'yyyy/mm/dd'),
    to_char(a.EFFECTIVE_DATE_BOM,'yyyy/mm/dd'),
    to_char(a.EXPIRY_DATE_EOM,'yyyy/mm/dd') ,
    a.payment_frequency_code,
    a.payment_frequency_no,
    a.GROSS_PREMIUM,
    a.COVERAGE_AMOUNT,
    a.PASSED_DATES,
    a.PASSED_MONTHS,
    a.remaining_months_for_recv,
    a.REMAINING_PREM_TERM_PE,
    a.REMAINING_MONTHS_FUTURE,
    a.REMAINING_PREM_TERM_CB,
    a.PAYMENT_QUARTER,
    a.CUMULATIVE_PAID_PREMIUM,
    a.ed_premium_per_coverage_day,
    a.ED_PREMIUM,
    a.PRI_CUR_END_REMAIN_CSM_RATE,
    a.PRI_UNTIL_REPORT_REMAIN_CSM_RATE,
    a.CUMULATIVE_ED_RATE,
    a.CUR_END_REMAIN_CSM_RATE,
    a.UNTIL_REPORT_REMAIN_CSM_RATE,
    a.ELR ,
    c.entity_code ,
    c.entity_e_name ,
    a.id
    order by a.id
  </select>


</mapper>
