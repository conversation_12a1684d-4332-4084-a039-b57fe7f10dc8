package com.ss.ifrs.actuarial.api;

import com.ss.ifrs.actuarial.dao.AtrBussIbnrcalcActionDao;
import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcAction;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcActionAddVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcActionQueryVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcConfirmParamVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcDeleteParamVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcICPQueryResultVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcICPQueryVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrCalcJobVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcSettledDevQueryVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcStep3CalcParamVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcStep4CalcParamVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcStepQueryVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcStepVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfIbnrcalcQuotaDetailVo;
import com.ss.ifrs.actuarial.service.AtrConfIbnrcalcQuotaDevService;
import com.ss.ifrs.actuarial.service.impl.AtrIbnrCalcService;
import com.ss.ifrs.actuarial.service.impl.AtrIbnrCalcSettledDevService;
import com.ss.ifrs.actuarial.service.impl.AtrIbnrCalcStepExportService;
import com.ss.ifrs.actuarial.util.dc.DataConverter;
import com.ss.ifrs.actuarial.util.dc.DictParam;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.platform.core.annotation.LogScheduleTask;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.annotation.TrackUserBehavioral;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-11-29
 */

@RestController
@RequestMapping("/ibnrCalc")
@Api(value = "IBNR 计算")
@Slf4j
public class AtrIbnrCalcApi extends BaseApi {

    @Resource
    private AtrIbnrCalcService atrIbnrCalcService;
    @Resource
    private AtrBussIbnrcalcActionDao atrBussIbnrcalcActionDao;
    @Resource
    private AtrIbnrCalcStepExportService atrIbnrCalcStepExportService;
    @Resource
    private AtrIbnrCalcSettledDevService atrIbnrCalcSettledDevService;
    @Autowired
    private AtrConfIbnrcalcQuotaDevService atrConfIbnrcalcQuotaDevService;

    @ApiOperation(value = "IBNR 计算自动任务")
    @PermissionRequest(required = false)
    @LogScheduleTask(bizCode = "ATR_IBNR_CALC",
            argsValue = {"#vo.entityId", "#vo.yearMonth"})
    @RequestMapping(value = "/doJob", method = RequestMethod.POST)
    public BaseResponse<?> doJob(HttpServletRequest request, @RequestBody AtrBussIbnrCalcJobVo vo) {
        vo.setUserId(this.loginUserId(request));
        try {
            atrIbnrCalcService.autoJob(vo);
            return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            log.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, e.getMessage());
        }
    }

    @RequestMapping(value = "/addAction", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<?> addAction(@RequestBody AtrBussIbnrcalcActionAddVo vo, HttpServletRequest request) {
        String msg = atrIbnrCalcService.addAction(vo, loginUserId(request));
        Map<String, Object> result = new HashMap<>();
        result.put("msg", msg);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @RequestMapping(value = "/searchPage", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<?> searchPage(@RequestBody AtrBussIbnrcalcActionQueryVo vo, int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrBussIbnrcalcAction> data = atrBussIbnrcalcActionDao.searchPage(vo, pageParam);

        DataConverter dc = new DataConverter();
        dc.addDictParam(DictParam.ofEntityCode());
        dc.addDictParam(DictParam.ofEntityName());
        dc.addDictParam(DictParam.ofLoa());
        dc.addDictParam(DictParam.ofUser("confirmId"));
        dc.addDictParam(DictParam.ofUser("createId"));

        Map<String, Object> result = new HashMap<>();
        result.put("data", dc.toData(data));
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @RequestMapping(value = "/searchICPPage", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<?> searchICPPage(@RequestBody AtrBussIbnrcalcICPQueryVo vo, int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrBussIbnrcalcICPQueryResultVo> data = atrBussIbnrcalcActionDao.searchICPPage(vo, pageParam);

        DataConverter dc = new DataConverter()
                .addDictParam(DictParam.ofEntityCode())
                .addDictParam(DictParam.ofEntityName())
                .addDictParam(DictParam.ofLoa());

        Map<String, Object> result = new HashMap<>();
        result.put("data", dc.toData(data));
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @RequestMapping(value = "/confirm", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<?> confirm(@RequestBody AtrBussIbnrcalcConfirmParamVo vo, HttpServletRequest request) {
        atrIbnrCalcService.confirm(vo, loginUserId(request));
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "ok");
    }

    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<?> delete(@RequestBody AtrBussIbnrcalcDeleteParamVo vo) {
        atrIbnrCalcService.delete(vo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "ok");
    }

    @RequestMapping(value = "/getAllStep", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<?> getAllStep(@RequestBody AtrBussIbnrcalcStepQueryVo vo) {
        AtrBussIbnrcalcStepVo data = atrIbnrCalcService.getAllStep(vo);
        Map<String, Object> result = new HashMap<>();
        result.put("data", data);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @RequestMapping(value = "/calcStep3", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<?> calcStep3(@RequestBody AtrBussIbnrcalcStep3CalcParamVo vo) {
        atrIbnrCalcService.calcStep3(vo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "ok");
    }

    @PostMapping("/calcStep4")
    @ResponseBody
    public BaseResponse<?> calcStep4(@RequestBody AtrBussIbnrcalcStep4CalcParamVo vo) {
        atrIbnrCalcService.calcStep4(vo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "ok");
    }

    @RequestMapping(value = "/exportStep", method = RequestMethod.POST)
    public void exportStep(@RequestBody Map<String, String> paramMap, HttpServletResponse response)
            throws IOException {
        atrIbnrCalcStepExportService.export(paramMap.get("actionNo"), response);
    }

    @RequestMapping(value = "/exportSettledDev", method = RequestMethod.POST)
    public void exportSettledDev(@RequestBody AtrBussIbnrcalcSettledDevQueryVo vo, HttpServletResponse response)
            throws IOException {
        atrIbnrCalcSettledDevService.export(vo, response);
    }

    @RequestMapping(value = "/uploadSettledDev", method = RequestMethod.POST)
    public Object uploadSettledDev(@RequestParam(value = "file") MultipartFile multipartFile,
                                   HttpServletRequest request) throws IOException {
        atrIbnrCalcSettledDevService.uploadSettledDevAdjustment(multipartFile, loginUserId(request));
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "ok");
    }

    @ApiOperation(value = "查询ibnr计算发展期配置")
    @TrackUserBehavioral(description = "query conf ibnr_calc")
    @PostMapping("/queryIbnrcalcQuota")
    public BaseResponse<Map<String, Object>> queryAllIbnrcalcDev() {
        List<AtrConfIbnrcalcQuotaDetailVo> atrConfIbnrcalcQuotaDetailVos = atrConfIbnrcalcQuotaDevService.queryAllIbnrcalcDev();
        Map<String, Object> result = new HashMap<>();
        result.put("result", atrConfIbnrcalcQuotaDetailVos);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "保存或更新ibnr计算发展期配置")
    @TrackUserBehavioral(description = "save or update conf ibnr_calc")
    @PostMapping(value = "/saveOrUpdateIbnrcalcQuota")
    public BaseResponse<?> saveConfWeight(@RequestBody List<AtrConfIbnrcalcQuotaDetailVo> vos,
                                          HttpServletRequest request) {
        Long userId = loginUserId(request);
        if (userId == null) {
            userId = 1L;
        }
        atrConfIbnrcalcQuotaDevService.saveOrUpdateIbnrcalc(vos, userId);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "OK");
    }

}
