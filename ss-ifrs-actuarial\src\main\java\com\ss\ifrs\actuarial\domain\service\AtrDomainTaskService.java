package com.ss.ifrs.actuarial.domain.service;

import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.platform.pojo.com.vo.ActOverviewVo;
import com.ss.platform.pojo.com.vo.BussActionStateVo;

public interface AtrDomainTaskService {


    /**
     *  临时接入旧版日志状态
     * @param bussActionStateVo
     */
    void mergeActuarialProcessState(BussActionStateVo bussActionStateVo);

    /**
     * 配置检查
     * @param atrConfBussPeriodVo
     */
    void configCheck(AtrConfBussPeriodVo atrConfBussPeriodVo);


    /**
     *  综合查询获取流程节点信息
     */
    ActOverviewVo getActOverviewVoByObject(ActOverviewVo actOverviewVo);

}
