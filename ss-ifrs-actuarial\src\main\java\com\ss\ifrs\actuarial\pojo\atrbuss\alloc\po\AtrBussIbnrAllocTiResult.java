/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2025-07-03 11:27:52
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.alloc.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2025-07-03 11:27:52<br/>
 * Description: TI分摊结果操作表<br/>
 * Table Name: atr_buss_ibnr_alloc_ti_result<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "TI分摊结果操作表")
public class AtrBussIbnrAllocTiResult implements Serializable {
    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.id
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.action_no
     * Database remarks: 执行编号
     */
    @ApiModelProperty(value = "执行编号", required = true)
    private String actionNo;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.entity_id
     * Database remarks: 业务单位ID
     */
    @ApiModelProperty(value = "业务单位ID", required = true)
    private Long entityId;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.year_month
     * Database remarks: 业务年月|评估期
     */
    @ApiModelProperty(value = "业务年月|评估期", required = true)
    private String yearMonth;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.portfolio_no
     * Database remarks: null
     */
    private String portfolioNo;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.icg_no
     * Database remarks: null
     */
    private String icgNo;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.icg_no_name
     * Database remarks: null
     */
    private String icgNoName;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.evaluate_approach
     * Database remarks: null
     */
    private String evaluateApproach;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.pl_judge_rslt
     * Database remarks: null
     */
    private String plJudgeRslt;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.acc_year_month
     * Database remarks: null
     */
    private String accYearMonth;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.ri_dept
     * Database remarks: null
     */
    private String riDept;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.treaty_no
     * Database remarks: null
     */
    private String treatyNo;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.treaty_name
     * Database remarks: null
     */
    private String treatyName;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.risk_class_code
     * Database remarks: null
     */
    private String riskClassCode;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.ibnr_amount
     * Database remarks: null
     */
    private BigDecimal ibnrAmount;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.fin_detail_code
     * Database remarks: null
     */
    private String finDetailCode;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.fin_product_code
     * Database remarks: null
     */
    private String finProductCode;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.fin_sub_product_code
     * Database remarks: null
     */
    private String finSubProductCode;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.fin_acc_channel
     * Database remarks: null
     */
    private String finAccChannel;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.center_code
     * Database remarks: null
     */
    private String centerCode;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.dept_id
     * Database remarks: null
     */
    private String deptId;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.channel_id
     * Database remarks: null
     */
    private String channelId;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.company_code4
     * Database remarks: null
     */
    private String companyCode4;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.creator_id
     * Database remarks: null
     */
    private Long creatorId;

    /**
     * Database column: atr_buss_ibnr_alloc_ti_result.create_time
     * Database remarks: null
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public String getIcgNoName() {
        return icgNoName;
    }

    public void setIcgNoName(String icgNoName) {
        this.icgNoName = icgNoName;
    }

    public String getEvaluateApproach() {
        return evaluateApproach;
    }

    public void setEvaluateApproach(String evaluateApproach) {
        this.evaluateApproach = evaluateApproach;
    }

    public String getPlJudgeRslt() {
        return plJudgeRslt;
    }

    public void setPlJudgeRslt(String plJudgeRslt) {
        this.plJudgeRslt = plJudgeRslt;
    }

    public String getAccYearMonth() {
        return accYearMonth;
    }

    public void setAccYearMonth(String accYearMonth) {
        this.accYearMonth = accYearMonth;
    }

    public String getRiDept() {
        return riDept;
    }

    public void setRiDept(String riDept) {
        this.riDept = riDept;
    }

    public String getTreatyNo() {
        return treatyNo;
    }

    public void setTreatyNo(String treatyNo) {
        this.treatyNo = treatyNo;
    }

    public String getTreatyName() {
        return treatyName;
    }

    public void setTreatyName(String treatyName) {
        this.treatyName = treatyName;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public BigDecimal getIbnrAmount() {
        return ibnrAmount;
    }

    public void setIbnrAmount(BigDecimal ibnrAmount) {
        this.ibnrAmount = ibnrAmount;
    }

    public String getFinDetailCode() {
        return finDetailCode;
    }

    public void setFinDetailCode(String finDetailCode) {
        this.finDetailCode = finDetailCode;
    }

    public String getFinProductCode() {
        return finProductCode;
    }

    public void setFinProductCode(String finProductCode) {
        this.finProductCode = finProductCode;
    }

    public String getFinSubProductCode() {
        return finSubProductCode;
    }

    public void setFinSubProductCode(String finSubProductCode) {
        this.finSubProductCode = finSubProductCode;
    }

    public String getFinAccChannel() {
        return finAccChannel;
    }

    public void setFinAccChannel(String finAccChannel) {
        this.finAccChannel = finAccChannel;
    }

    public String getCenterCode() {
        return centerCode;
    }

    public void setCenterCode(String centerCode) {
        this.centerCode = centerCode;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getCompanyCode4() {
        return companyCode4;
    }

    public void setCompanyCode4(String companyCode4) {
        this.companyCode4 = companyCode4;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}