/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2022-06-06 10:15:00
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po;

import com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo.AtrBussReserveIbnrDetailVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2022-06-06 10:15:00<br/>
 * Description: IBNR明细数据表<br/>
 * Table Name: atr_buss_reserve_ibnr_detail<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "IBNR明细数据表")
public class AtrBussReserveIbnrDetail implements Serializable {
    /**
     * Database column: atr_buss_reserve_ibnr_detail.ibnr_detail_id
     * Database remarks: Ibnr_Detail_Id|主键
     */
    @ApiModelProperty(value = "Ibnr_Detail_Id|主键", required = true)
    private Long ibnrDetailId;

    /**
     * Database column: atr_buss_reserve_ibnr_detail.reserve_ibnr_id
     * Database remarks: reserve_ibnr_id|IBNR提取主表主键
     */
    @ApiModelProperty(value = "reserve_ibnr_id|IBNR提取主表主键", required = true)
    private Long reserveIbnrId;

    /**
     * Database column: atr_buss_reserve_ibnr_detail.data_type
     * Database remarks: Data_Type|数据类型
     */
    @ApiModelProperty(value = "Data_Type|数据类型", required = true)
    private String dataType;

    /**
     * Database column: atr_buss_reserve_ibnr_detail.damage_year_month
     * Database remarks: Damage_Year_Month|事故出险月份
     */
    @ApiModelProperty(value = "Damage_Year_Month|事故出险月份", required = false)
    private String damageYearMonth;

    /**
     * Database column: atr_buss_reserve_ibnr_detail.dev_period
     * Database remarks: Dev_Period|发展期次
     */
    @ApiModelProperty(value = "Dev_Period|发展期次", required = true)
    private Integer devPeriod;

    /**
     * Database column: atr_buss_reserve_ibnr_detail.currency
     * Database remarks: Currency|币别（原币）
     */
    @ApiModelProperty(value = "Currency|币别（原币）", required = false)
    private String currencyCode;

    /**
     * Database column: atr_buss_reserve_ibnr_detail.value
     * Database remarks: value|金额（原币）
     */
    @ApiModelProperty(value = "value|金额（原币）", required = false)
    private BigDecimal value;

    @ApiModelProperty(value = "loaCode|风险大类", required = true)
    private String loaCode;

    @ApiModelProperty(value = "portfolio_no|合同组合", required = true)
    private String portfolioNo;

    /**
     * Database column: atr_buss_reserve_ibnr_detail.EXCHANGE_RATE
     * Database remarks: EXCHANGE_RATE|兑换率
     */
    @ApiModelProperty(value = "EXCHANGE_RATE|兑换率", required = false)
    private BigDecimal exchangeRate;

    private List<AtrBussReserveIbnrDetailVo> thisYearMonthList;

    private static final long serialVersionUID = 1L;

    public List<AtrBussReserveIbnrDetailVo> getThisYearMonthList() {
        return thisYearMonthList;
    }

    public void setThisYearMonthList(List<AtrBussReserveIbnrDetailVo> thisYearMonthList) {
        this.thisYearMonthList = thisYearMonthList;
    }

    public Long getIbnrDetailId() {
        return ibnrDetailId;
    }

    public void setIbnrDetailId(Long ibnrDetailId) {
        this.ibnrDetailId = ibnrDetailId;
    }

    public Long getReserveIbnrId() {
        return reserveIbnrId;
    }

    public void setReserveIbnrId(Long reserveIbnrId) {
        this.reserveIbnrId = reserveIbnrId;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getDamageYearMonth() {
        return damageYearMonth;
    }

    public void setDamageYearMonth(String damageYearMonth) {
        this.damageYearMonth = damageYearMonth;
    }

    public Integer getDevPeriod() {
        return devPeriod;
    }

    public void setDevPeriod(Integer devPeriod) {
        this.devPeriod = devPeriod;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal Amount) {
        this.value = Amount;
    }

    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }
}