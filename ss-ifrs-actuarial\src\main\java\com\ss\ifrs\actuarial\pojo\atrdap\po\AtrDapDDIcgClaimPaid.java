/**
 *
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-02-02 18:13:50
 * Author liebin.zheng
 *
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 *
 */
package com.ss.ifrs.actuarial.pojo.atrdap.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-02 18:13:50<br/>
 * Description: 当期实付赔款(合同组维度，直保&临分分入)<br/>
 * Table Name: ATR_DAP_DD_CLAIM_PAID<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "当期实付赔款(合同组维度，直保&临分分入)")
public class AtrDapDDIcgClaimPaid implements Serializable {
    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.ID
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.CENTER_ID
     * Database remarks: 业务单位ID
     */
    @ApiModelProperty(value = "业务单位ID", required = true)
    private Long entityId;

    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.LOSS_NO
     * Database remarks: 赔案号
     */
    @ApiModelProperty(value = "赔案号", required = true)
    private String lossNo;

    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.LOSS_SEQ_NO
     * Database remarks: 赔案序号
     */
    @ApiModelProperty(value = "赔案序号", required = true)
    private Short lossSeqNo;

    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.CHECK_DATE
     * Database remarks: 核赔日期
     */
    @ApiModelProperty(value = "核赔日期", required = true)
    private Date checkDate;

    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.DEAL_DATE
     * Database remarks: 实际赔付日期
     */
    @ApiModelProperty(value = "实际赔付日期", required = true)
    private Date dealDate;

    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.YEAR_MONTH
     * Database remarks: 业务年月|实际赔付年月
     */
    @ApiModelProperty(value = "业务年月|实际赔付年月", required = true)
    private String yearMonth;

    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.PORTFOLIO_NO
     * Database remarks: 合同组合号码
     */
    @ApiModelProperty(value = "合同组合号码", required = true)
    private String portfolioNo;

    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.ICG_NO
     * Database remarks: 合同组号码
     */
    @ApiModelProperty(value = "合同组号码", required = true)
    private String icgNo;

    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.EVALUATE_APPROACH
     * Database remarks: 评估方法
     */
    @ApiModelProperty(value = "评估方法", required = true)
    private String evaluateApproach;

    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.LOA_CODE
     * Database remarks: LOA编码
     */
    @ApiModelProperty(value = "LOA编码", required = true)
    private String loaCode;

    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.PRODUCT_CODE
     * Database remarks: 产品代码
     */
    @ApiModelProperty(value = "产品代码", required = true)
    private String productCode;

    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.RISK_CODE
     * Database remarks: 险种代码
     */
    @ApiModelProperty(value = "险种代码", required = true)
    private String riskCode;

    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.CMUNIT_NO
     * Database remarks: 公共项/计量单元编号
     */
    @ApiModelProperty(value = "公共项/计量单元编号", required = true)
    private String cmunitNo;

    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.CLAIM_NO
     * Database remarks: 立案号码
     */
    @ApiModelProperty(value = "立案号码", required = true)
    private String claimNo;

    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.POLICY_NO
     * Database remarks: 保单号码
     */
    @ApiModelProperty(value = "保单号码", required = true)
    private String policyNo;

    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.ENDORSE_SEQ_NO
     * Database remarks: 批改序号
     */
    @ApiModelProperty(value = "批改序号", required = true)
    private String endorseSeqNo;

    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.DAMAGE_DATE
     * Database remarks: 事故日期
     */
    @ApiModelProperty(value = "事故日期", required = true)
    private Date damageDate;

    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.DEPT_ID
     * Database remarks: 业务部门
     */
    @ApiModelProperty(value = "业务部门", required = true)
    private Long deptId;

    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.CURRENCY
     * Database remarks: 币种
     */
    @ApiModelProperty(value = "币种", required = true)
    private String currencyCode;

    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.PAID_AMOUNT
     * Database remarks: 实付赔款
     */
    @ApiModelProperty(value = "实付赔款", required = true)
    private BigDecimal paidAmount;

    /**
     * Database column: ATR_DAP_DD_CLAIM_PAID.DRAW_TIME
     * Database remarks: 提数日期
     */
    @ApiModelProperty(value = "提数日期", required = true)
    private Date drawTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getLossNo() {
        return lossNo;
    }

    public void setLossNo(String lossNo) {
        this.lossNo = lossNo;
    }

    public Short getLossSeqNo() {
        return lossSeqNo;
    }

    public void setLossSeqNo(Short lossSeqNo) {
        this.lossSeqNo = lossSeqNo;
    }

    public Date getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(Date checkDate) {
        this.checkDate = checkDate;
    }

    public Date getDealDate() {
        return dealDate;
    }

    public void setDealDate(Date dealDate) {
        this.dealDate = dealDate;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public String getEvaluateApproach() {
        return evaluateApproach;
    }

    public void setEvaluateApproach(String evaluateApproach) {
        this.evaluateApproach = evaluateApproach;
    }

    public String getLoaCode() {
        return loaCode;
    }

    public void setLoaCode(String loaCode) {
        this.loaCode = loaCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getRiskCode() {
        return riskCode;
    }

    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    public String getCmunitNo() {
        return cmunitNo;
    }

    public void setCmunitNo(String cmunitNo) {
        this.cmunitNo = cmunitNo;
    }

    public String getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(String claimNo) {
        this.claimNo = claimNo;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getEndorseSeqNo() {
        return endorseSeqNo;
    }

    public void setEndorseSeqNo(String endorseSeqNo) {
        this.endorseSeqNo = endorseSeqNo;
    }

    public Date getDamageDate() {
        return damageDate;
    }

    public void setDamageDate(Date damageDate) {
        this.damageDate = damageDate;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(BigDecimal paidAmount) {
        this.paidAmount = paidAmount;
    }

    public Date getDrawTime() {
        return drawTime;
    }

    public void setDrawTime(Date drawTime) {
        this.drawTime = drawTime;
    }
}