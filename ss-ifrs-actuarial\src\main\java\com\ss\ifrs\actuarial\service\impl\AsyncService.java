package com.ss.ifrs.actuarial.service.impl;

import com.ss.platform.util.CommonUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;

@Service
public class AsyncService {

    @Resource
    private AsyncService asyncService;

    /**
     * 开新线程执行， 强制使用新的新的数据库连接
     */
    public <T> T doAsync(Supplier<T> r) {
        List<T> list = new ArrayList<>();
        List<Exception> ex = CommonUtil.runThreads((t, n) -> list.add(asyncService.doAsync0(r)), () -> 1);
        if (!ex.isEmpty()) {
            throw new RuntimeException(ex.get(0));
        }
        return list.get(0);
    }

    /**
     * 开新线程执行， 强制使用新的新的数据库连接
     */
    public void doAsync(Runnable r) {
        List<Exception> ex = CommonUtil.runThreads((t, n) -> asyncService.doAsync0(r), () -> 1);
        if (!ex.isEmpty()) {
            throw new RuntimeException(ex.get(0));
        }
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public <T> T doAsync0(Supplier<T> r) {
        return r.get();
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void doAsync0(Runnable r) {
        r.run();
    }

}
