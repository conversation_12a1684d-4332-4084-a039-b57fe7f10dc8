/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-12-04 15:35:00
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter @Getter
public class AtrBussIbnrcalcICPQueryResultVo {

    @ApiModelProperty(value = "entityId", required = true)
    private Long entityId;

    @ApiModelProperty(value = "currency_code", required = true)
    private String currencyCode;

    @ApiModelProperty(value = "actionNo", required = true)
    private String actionNo;

    @ApiModelProperty(value = "合同组合ID， 唯一指向一条记录", required = true)
    private Long icpId;

    @ApiModelProperty(value = "loa", required = true)
    private String loaCode;

    @ApiModelProperty(value = "合同组合", required = true)
    private String portfolioNo;

    @ApiModelProperty(value = "IBNR 类型", required = true)
    private String ibnrType;

    @ApiModelProperty(value = "确认状态", required = true)
    private String confirmIs;

}