package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.dao.dap.AtrDapTreatyDao;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrTemplateVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaImportVo;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrDapIcgRiskClassVo;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrDapTreatyVo;
import com.ss.ifrs.actuarial.service.AtrDapTreatyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AtrDapTreatyServiceImpl implements AtrDapTreatyService {

    @Autowired
    private AtrDapTreatyDao atrDapTreatyDao;

    @Override
    public List<AtrDapTreatyVo> findDapTreatyList(AtrTemplateVo entityId) {
        return atrDapTreatyDao.findDapTreatyList(entityId);
    }

    @Override
    public List<AtrDapIcgRiskClassVo> findTreatyIcgList(AtrConfQuotaImportVo atrConfQuotaImportVo) {
        return atrDapTreatyDao.findTreatyIcgList(atrConfQuotaImportVo);
    }


    @Override
    public List<AtrDapIcgRiskClassVo> findDDIcgList(Long entityId) {
        return atrDapTreatyDao.findDDIcgList(entityId);
    }

    @Override
    public List<AtrDapIcgRiskClassVo> findFOIcgList(Long entityId) {
        return atrDapTreatyDao.findFOIcgList(entityId);
    }
}
