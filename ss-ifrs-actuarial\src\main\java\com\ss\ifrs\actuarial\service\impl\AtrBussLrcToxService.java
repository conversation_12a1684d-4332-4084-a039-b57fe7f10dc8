package com.ss.ifrs.actuarial.service.impl;

import com.google.common.base.Splitter;
import com.ss.ifrs.actuarial.dao.AtrBussLrcToxDao;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.AtrBussPGKey;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.AtrDapTreaty;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.AtrBussLrcToIcg;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.AtrBussLrcToIcgDev;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.AtrBussToLrcIcgPremium;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.x.AtrBussLrcToUlx;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.x.AtrBussLrcToUlxDev;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.x.AtrDuctLrcToxPaid;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.x.AtrDuctLrcToxPolicy;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.x.AtrDuctToUlxPre;
import com.ss.ifrs.actuarial.util.Dates;
import com.ss.ifrs.actuarial.util.EcfUtil;
import com.ss.ifrs.actuarial.util.IndexFactory;
import com.ss.ifrs.actuarial.util.ThreadUtil;
import com.ss.ifrs.actuarial.util.abp.AsyncBatchProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 预期保费现金流 service （超赔合约分出业务）
 *
 * <AUTHOR>
 */
@Service
@Scope("prototype")
@Slf4j
public class AtrBussLrcToxService extends AbstractAtrBussLrcService {

    protected AtomicLong ulMainIdGen;

    @Resource
    private AtrBussLrcToxDao atrBussLrcToxDao;

    private final Map<String, AtrDapTreaty> treatyInfoMap = new HashMap<>();


    // 分片相关配置
    private static final int PARTITION_SIZE = 50; // 分片数量

    // 全量数据依赖的缓存（需要在分片间共享）
    private final Map<String, BigDecimal> treatySumPremiumCache = new HashMap<>();

    private final Map<AtrBussPGKey, AtrBussLrcToIcg> icgMap = new HashMap<>();

    private final IndexFactory<BigDecimal> cumlMdpPaidIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> curMdpPaidIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> curAdjustPaidIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> cumlAdjustPaidIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> curNetFeePaidIndex = new IndexFactory<>();
    private final IndexFactory<AtrDuctLrcToxPaid> paidInfoIndex = new IndexFactory<>();
    private final IndexFactory<AtrDuctToUlxPre> ulPreIndex = new IndexFactory<>();
    // 统计新旧保费，index 0 为旧保费,index 1 为新保费
    private final Map<List<String>,BigDecimal[]> icgPremiumIndex = new HashMap<>();

    // 定义BigDecimal常量和工具方法
    private static final BigDecimal ZERO = BigDecimal.ZERO;
    private static final MathContext MC = new MathContext(15, RoundingMode.HALF_UP);


    public void entry(String actionNo, Long entityId, String yearMonth) {
        initEnvParams(actionNo, entityId, yearMonth, "TO");
        try (AsyncBatchProcessor abp = createAsyncBatchProcessor()) {
            initAbp(abp);
            collectData();
            calcTreatyUlPolicyByPartition();
            calcIcg();
            saveIcgPremium();
            abp.end();
        } catch (Exception e) {
            logError(e);
            throw new RuntimeException(e);
        }
    }

    private void calcIcg() {
        icgMap.values().forEach(this::calcIcg);
    }

    private void calcIcg(AtrBussLrcToIcg icg) {
        BigDecimal totalEdPremium = icg.getDevEdPremiumMap()
                .values()
                .stream()
                .reduce(BigDecimal::add)
                .orElse(ZERO);
        totalEdPremium = round(totalEdPremium);
        icg.setTotalPremium(totalEdPremium);
        List<AtrBussLrcToIcgDev> icgDevList = new ArrayList<>();
        for (int i = 0; i < icg.getMaxDevNo(); i++) {
            AtrBussLrcToIcgDev icgDev = new AtrBussLrcToIcgDev();
            icgDevList.add(icgDev);
            icgDev.setMainId(icg.getId());
            icgDev.setDevNo(i);
            icgDev.setRecvPremium(icg.getDevRecvPremiumMap().getOrDefault(i,BigDecimal.ZERO));
            icgDev.setEdPremium(icg.getDevEdPremiumMap().getOrDefault(i,BigDecimal.ZERO));
            if (totalEdPremium.compareTo(ZERO) != 0) {
                BigDecimal ratio = icgDev.getEdPremium().divide(totalEdPremium, MC);
                icgDev.setEdRatio(roundR(ratio));
            }
        }
        icg.setBusinessType("TX");
        abp.insert(icg);
        icgDevList.forEach(abp::insert);
    }

    private void saveIcgPremium(){
        icgPremiumIndex.forEach((k,v) -> {
            AtrBussToLrcIcgPremium atrBussToLrcIcgPremium = new AtrBussToLrcIcgPremium();
            atrBussToLrcIcgPremium.setActionNo(actionNo);
            atrBussToLrcIcgPremium.setYearMonth(yearMonth);
            atrBussToLrcIcgPremium.setPortfolioNo(k.get(0));
            atrBussToLrcIcgPremium.setIcgNo(k.get(1));
            atrBussToLrcIcgPremium.setRiskClassCode(k.get(2));
            atrBussToLrcIcgPremium.setOldPremium(v[0]);
            atrBussToLrcIcgPremium.setNewPremium(v[1]);
            atrBussToLrcIcgPremium.setMainTreatyType("X");
            abp.insert(atrBussToLrcIcgPremium);
        });
    }

    private void calcIcgPremium(AtrDapTreaty treatyInfo) {
        String contractYearMonth = Dates.toChar(treatyInfo.getContractDate(), "yyyyMM");
        List<String> uniKeys = Arrays.asList(treatyInfo.getPortfolioNo(), treatyInfo.getIcgNo(), treatyInfo.getRiskClassCodes());

        // 只有当合同日期为当期时，才将premium计入新保费
        if (contractYearMonth.equals(yearMonth)) {
            if (icgPremiumIndex.containsKey(uniKeys)) {
                BigDecimal[] premiums = icgPremiumIndex.get(uniKeys);
                premiums[1] = premiums[1].add(treatyInfo.getEstPremium());
                icgPremiumIndex.put(uniKeys, premiums);
            } else {
                icgPremiumIndex.put(uniKeys, new BigDecimal[]{ZERO, treatyInfo.getEstPremium()});
            }
        }else{
            if ( !icgPremiumIndex.containsKey(uniKeys) ) {
                BigDecimal[] bigDecimals = icgPremiumIndex.get(uniKeys);
                if (bigDecimals != null && bigDecimals.length > 0) {
                    icgPremiumIndex.put(uniKeys, new BigDecimal[]{bigDecimals[0], BigDecimal.ZERO});
                }

            }
        }
    }


    private void collectIcg(AtrDapTreaty treatyInfo, List<AtrBussLrcToUlxDev> ulDevList) {
        String portfolioNo = treatyInfo.getPortfolioNo();
        String icgNo = treatyInfo.getIcgNo();
        AtrBussPGKey key = new AtrBussPGKey(portfolioNo, icgNo);
        AtrBussLrcToIcg icg = icgMap.get(key);
        if (!icgMap.containsKey(key)) {
            icg = new AtrBussLrcToIcg();
            icgMap.put(key, icg);
            icg.setId(icgMainIdGen.incrementAndGet());
            icg.setActionNo(actionNo);
            icg.setEntityId(entityId);
            icg.setYearMonth(yearMonth);
            icg.setPortfolioNo(portfolioNo);
            icg.setIcgNo(icgNo);
            icg.setMaxDevNo(-1);
        }

        for (AtrBussLrcToUlxDev ulxDev : ulDevList) {
            int devNo = ulxDev.getDevNo();
            putDevValueB(icg.getDevRecvPremiumMap(), devNo, ulxDev.getRecvPremium());
            putDevValueB(icg.getDevEdPremiumMap(), devNo, ulxDev.getEdPremium());
            if (icg.getMaxDevNo() < devNo) {
                icg.setMaxDevNo(devNo);
            }
        }
    }

    /**
     * 将BigDecimal值放入map中，如果map中已有该键，则将值相加
     */
    private void putDevValueB(Map<Integer, BigDecimal> map, int key, BigDecimal value) {
        if (value == null) {
            return;
        }
        map.merge(key, value, BigDecimal::add);
    }

    private void collectUlxPre(String treatyNo) {
        ulPreIndex.clear();
        List<AtrDuctToUlxPre> preList = atrBussLrcToxDao.findUlPre(lastActionNo, lastYearMonth, treatyNo);
        for (AtrDuctToUlxPre pre : preList) {
            ulPreIndex.add(createTreatyUlKey(pre), pre);
        }
    }

    private void collectData() {
        collectTreatyInfo();
        // 移除原有的collectUlPolicy和calcUlPolicyEarnedPremium，改为分片处理
        partitionPolicyData();
        collectPaid();
        collectPreIcgPremium();
        initIdGen();
    }

    private void initIdGen() {
        long maxUlId = atrBussLrcToxDao.getUlMaxId();
        long maxIcgId = atrBussLrcToDao.getIcgMaxMainId();
        ulMainIdGen = new AtomicLong(maxUlId);
        icgMainIdGen = new AtomicLong(maxIcgId);
    }

    private void collectPreIcgPremium(){
        List<AtrBussToLrcIcgPremium> atrBussToLrcIcgPremiums = atrBussLrcToxDao.listPreIcgPremium(commonParamMap);
        atrBussToLrcIcgPremiums.forEach(item -> {
            // 将上期的旧保费和新保费总和作为当期的旧保费
            BigDecimal totalPremium = nvl(item.getOldPremium()).add(nvl(item.getNewPremium()));
            icgPremiumIndex.put(Arrays.asList(item.getPortfolioNo(), item.getIcgNo(), item.getRiskClassCode()), 
                               new BigDecimal[]{totalPremium, ZERO});
        });
    }

    private void collectPaid() {
        List<AtrDuctLrcToxPaid> vos = atrBussLrcToxDao.findPaid(commonParamMap);
        for (AtrDuctLrcToxPaid vo : vos) {
            List<?> key = Collections.singletonList(vo.getTreatyNo());
            String yearMonth = vo.getYearMonth();
            if (this.yearMonth.equals(yearMonth)) {
                curMdpPaidIndex.plus(key, vo.getMdp());
                curAdjustPaidIndex.plus(key, vo.getAdjustFee());
                curNetFeePaidIndex.plus(key, vo.getNetFee());
            }
            paidInfoIndex.add(key, vo);
            cumlAdjustPaidIndex.plus(key, vo.getAdjustFee());
            cumlMdpPaidIndex.plus(key, vo.getMdp());
        }
    }

    // 原有的方法已被分片处理替代，保留注释作为参考
    /*
     * 原calcUlPolicyEarnedPremium和collectUlPolicy方法已被分片处理替代
     * 已赚保费计算现在在SQL层面完成，避免内存占用过大
     */

    private void collectTreatyInfo() {
        List<AtrDapTreaty> dapTreatyList = atrBussLrcToxDao.findTreayInfo(commonParamMap);
        for (AtrDapTreaty vo : dapTreatyList) {
            treatyInfoMap.put(vo.getTreatyNo(), vo);
        }
    }


    @Override
    protected void initAbp(AsyncBatchProcessor abp) {
        super.initAbp(abp);
        abp.addType(AtrBussLrcToUlx.class);
        abp.addType(AtrBussLrcToUlxDev.class);
        abp.addType(AtrBussLrcToIcg.class);
        abp.addType(AtrBussLrcToIcgDev.class);
        abp.addType(AtrBussToLrcIcgPremium.class);
    }

    private List<?> createTreatyUlKey(Object vo) {
        return createTreatyUlKey(EcfUtil.readField(vo, "treatyNo").toString(), vo);
    }

    private List<?> createTreatyUlKey(String treatyNo, Object vo) {
        List<Object> list = new ArrayList<>();
        list.add(treatyNo);
        list.add(EcfUtil.readField(vo, "policyNo"));
        list.add(EcfUtil.readField(vo, "endorseSeqNo"));
        list.add(EcfUtil.readField(vo, "kindCode"));
        return list;
    }

    private Set<String> splitRiskClassCodes(String riskClassCodeStr) {
        if (riskClassCodeStr == null || riskClassCodeStr.trim().isEmpty()) {
            return new HashSet<>();
        }
        Splitter splitter = Splitter.onPattern(",").trimResults().omitEmptyStrings();
        return new HashSet<>(splitter.splitToList(riskClassCodeStr));
    }

    /**
     * 分片保单数据
     */
    private void partitionPolicyData() {
        logDebug("开始分片保单数据");

        // 1. 清空分区表
        clearPartitionTable();

        // 2. 插入保单数据到分区表并分片
        insertPolicyDataToPartitionTable();

        // 3. 预计算全量依赖数据
        preCalculateGlobalData();

        logDebug("保单数据分片完成");
    }

    /**
     * 清空分区表数据
     */
    private void clearPartitionTable() {
        try {
            // 清空分区表数据
            atrBussLrcToxDao.truncatePartitionTable();

            logDebug("分区表数据清空成功");
        } catch (Exception e) {
            logError(e);
            throw new RuntimeException("清空分区表数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 插入保单数据到分区表并分片
     */
    private void insertPolicyDataToPartitionTable() {
        Set<String> irsSet = new HashSet<>();
        for (AtrDapTreaty treatyInfo : treatyInfoMap.values()) {
            if (treatyInfo.getIssueDate() == null) {
                logDebug("合约 " + treatyInfo.getTreatyNo() + " 的签发日期为空，跳过");
                continue;
            }
            String issueYear = DateFormatUtils.format(treatyInfo.getIssueDate(), "yyyy");
            Set<String> riskClassCodes = splitRiskClassCodes(treatyInfo.getRiskClassCodes());
            if (!riskClassCodes.isEmpty()) {
                riskClassCodes.forEach(riskClassCode -> irsSet.add(issueYear + "," + riskClassCode));
            }
        }

        if (irsSet.isEmpty()) {
            logDebug("没有有效的合约数据，跳过保单数据插入");
            return;
        }

        // 转换为List，MyBatis对List的支持更好
        List<String> irs = new ArrayList<>(irsSet);

        Map<String, Object> params = new HashMap<>(commonParamMap);
        params.put("parts", PARTITION_SIZE);
        params.put("issueYearRiskCodes", irs);

        // 调试信息
        logDebug("issueYearRiskCodes size: " + irs.size());
        logDebug("issueYearRiskCodes content: " + irs.toString());

        // 插入数据到分区表，同时进行分片和已赚保费计算
        atrBussLrcToxDao.insertPartitionPolicyData(params);

        logDebug("保单数据插入分区表完成，分片数: " + PARTITION_SIZE);
    }

    /**
     * 预计算全量依赖的数据（如各合约的保单总保费）
     */
    private void preCalculateGlobalData() {
        logDebug("开始预计算全量依赖数据");

        for (AtrDapTreaty treatyInfo : treatyInfoMap.values()) {
            if (treatyInfo.getIssueDate() == null) {
                logDebug("合约 " + treatyInfo.getTreatyNo() + " 的签发日期为空，跳过预计算");
                treatySumPremiumCache.put(treatyInfo.getTreatyNo(), BigDecimal.ZERO);
                continue;
            }

            Set<String> riskClassCodes = splitRiskClassCodes(treatyInfo.getRiskClassCodes());
            if (riskClassCodes.isEmpty()) {
                logDebug("合约 " + treatyInfo.getTreatyNo() + " 的风险类别代码为空，跳过预计算");
                treatySumPremiumCache.put(treatyInfo.getTreatyNo(), BigDecimal.ZERO);
                continue;
            }

            String issueYear = DateFormatUtils.format(treatyInfo.getIssueDate(), "yyyy");

            // 计算该合约下所有匹配保单的已赚保费总和
            Map<String, Object> params = new HashMap<>();
            params.put("riskClassCodes", riskClassCodes);
            params.put("issueYear", issueYear);

            BigDecimal sumPremium = atrBussLrcToxDao.calculateTreatySumPremium(params);
            treatySumPremiumCache.put(treatyInfo.getTreatyNo(), sumPremium);
        }

        logDebug("全量依赖数据预计算完成");
    }

    /**
     * 使用分片方式计算合约底层保单
     */
    private void calcTreatyUlPolicyByPartition() {
        logDebug("开始分片计算合约底层保单");

        for (int partNo = 0; partNo < PARTITION_SIZE; partNo++) {
            Map<String, Object> paramMap = new HashMap<>(commonParamMap);
            paramMap.put("pn", partNo);

            // 并行准备该批次计算所需的数据
            List<String> marks = new ArrayList<>();
            marks.add("getPartPolicyData");

            List<AtrDuctLrcToxPolicy> partPolicyList = new ArrayList<>();

            int finalPartNo = partNo;
            ThreadUtil.runThreadsThrow(marks, (mark) -> {
                logDebug(mark + "-part-" + finalPartNo, "start");
                if ("getPartPolicyData".equals(mark)) {
                    partPolicyList.addAll(atrBussLrcToxDao.getPartPolicyData(paramMap));
                }
                logDebug(mark + "-part-" + finalPartNo, "end");
            }, marks::size);

            if (partPolicyList.isEmpty()) {
                continue; // 跳过空分片
            }

            logDebug("calcTreatyUlPolicy-part-" + partNo + ", 保单数量: " + partPolicyList.size());

            // 处理当前分片的保单数据
            calcTreatyUlPolicyForPartition(partPolicyList);

            // 清理内存
            partPolicyList.clear();

            logDebug("calcTreatyUlPolicy-part-" + partNo + "-end");
        }

        logDebug("分片计算合约底层保单完成");
    }

    /**
     * 处理单个分片的保单数据
     */
    private void calcTreatyUlPolicyForPartition(List<AtrDuctLrcToxPolicy> partPolicyList) {
        // 按合约分组处理
        Map<String, List<AtrDuctLrcToxPolicy>> policyByTreaty = new HashMap<>();

        for (AtrDuctLrcToxPolicy policy : partPolicyList) {
            // 找到该保单对应的合约
            for (AtrDapTreaty treatyInfo : treatyInfoMap.values()) {
                Set<String> riskClassCodes = splitRiskClassCodes(treatyInfo.getRiskClassCodes());
                if (riskClassCodes.contains(policy.getRiskClassCode())) {
                    policyByTreaty.computeIfAbsent(treatyInfo.getTreatyNo(), k -> new ArrayList<>()).add(policy);
                }
            }
        }

        // 处理每个合约的保单
        for (Map.Entry<String, List<AtrDuctLrcToxPolicy>> entry : policyByTreaty.entrySet()) {
            String treatyNo = entry.getKey();
            List<AtrDuctLrcToxPolicy> treatyPolicies = entry.getValue();
            AtrDapTreaty treatyInfo = treatyInfoMap.get(treatyNo);

            if (treatyInfo != null) {
                calcTreatyUlPolicyForSingleTreaty(treatyInfo, treatyPolicies);
            }
        }
    }

    /**
     * 处理单个合约的保单数据（使用缓存的全量数据）
     */
    private void calcTreatyUlPolicyForSingleTreaty(AtrDapTreaty treatyInfo, List<AtrDuctLrcToxPolicy> matchedPolicies) {
        String treatyNo = treatyInfo.getTreatyNo();
        Date effectiveDate = treatyInfo.getEffectiveDate();
        Date expiryDate = treatyInfo.getExpiryDate();
        int remainingMonths = Math.max(Dates.monthsBetween(evDate, expiryDate), 0);
        List<String> treatyKey = Collections.singletonList(treatyNo);

        // 步骤1: 确定MDP来源 - 检查是否存在计提记录
        boolean hasPaidRecord = cumlMdpPaidIndex.one(treatyKey, ZERO).compareTo(ZERO) != 0;
        BigDecimal mdp = treatyInfo.getEstPremium(); // 默认使用合约的预估保费

        // 如果存在计提记录，则使用计提的MDP
        if (hasPaidRecord) {
            mdp = cumlMdpPaidIndex.one(treatyKey, ZERO);
        }

        collectUlxPre(treatyNo);

        // 步骤2: 使用缓存的总保费（避免重复计算）
        BigDecimal sumPremium = treatySumPremiumCache.get(treatyNo);
        if (sumPremium == null || sumPremium.compareTo(ZERO) == 0) {
            log.error("合约{}底层保单已赚保费总和为 0 ， 无法拆分", treatyNo);
            return;
        }

        // 计算ICG保费
        calcIcgPremium(treatyInfo);

        // 确定最大开发期
        int maxDevNo = remainingMonths;
        if (!hasPaidRecord) {
            maxDevNo = Math.max(maxDevNo, 1);
        }

        // 计算合约总天数
        BigDecimal totalDays = new BigDecimal(Dates.daysBetween(effectiveDate, expiryDate) + 1);

        // 当期已支付保费（MDP + 调整金额）
        BigDecimal curPaidPremium = curMdpPaidIndex.one(treatyKey, ZERO);
        curPaidPremium = round(curPaidPremium.add(curAdjustPaidIndex.one(treatyKey, ZERO)));

        // 合约总保费（MDP + 累计调整金额）
        BigDecimal allTreatyPremium = mdp.add(cumlAdjustPaidIndex.one(treatyKey, BigDecimal.ZERO));

        // 步骤3-5: 对每个底层保单进行处理（与原逻辑相同）
        for (AtrDuctLrcToxPolicy ulPolicy : matchedPolicies) {
            processUlPolicy(treatyInfo, ulPolicy, sumPremium, allTreatyPremium, curPaidPremium,
                          mdp, maxDevNo, totalDays, hasPaidRecord, treatyNo);
        }
    }

    /**
     * 处理单个底层保单（从原calcTreatyUlPolicy方法中提取）
     */
    private void processUlPolicy(AtrDapTreaty treatyInfo, AtrDuctLrcToxPolicy ulPolicy,
                               BigDecimal sumPremium, BigDecimal allTreatyPremium, BigDecimal curPaidPremium,
                               BigDecimal mdp, int maxDevNo, BigDecimal totalDays, boolean hasPaidRecord,
                               String treatyNo) {

        // 步骤3: 计算底层保单的分摊比例 - 使用已赚保费计算
        BigDecimal earnedPremiumPolicy = ulPolicy.getEarnedPremium();
        BigDecimal rate = earnedPremiumPolicy.divide(sumPremium, MC);

        // 步骤4: 按比例分摊合约保费到底层保单
        BigDecimal premiumU = round(allTreatyPremium.multiply(rate));

        long ulMainId = ulMainIdGen.incrementAndGet();
        List<AtrBussLrcToUlxDev> ulDevList = new ArrayList<>();

        // 非当期已赚保费汇总
        BigDecimal sumEdPremiumEcept0 = ZERO;

        // 获取上期累计已赚保费
        AtrDuctToUlxPre ulxPre = ulPreIndex.one(createTreatyUlKey(treatyNo, ulPolicy), new AtrDuctToUlxPre());
        BigDecimal cumlCurEdMdpPre = nvl(ulxPre.getCumlCurEdMdp());

        // 计算当期可分配的保费 = 总分摊保费 - 上期累计已赚保费
        BigDecimal allocatablePremium = round(premiumU.subtract(cumlCurEdMdpPre));

        // 创建开发期对象并同时计算各开发期的保费和已赚保费
        for (int i = 0; i <= maxDevNo; i++) {
            AtrBussLrcToUlxDev dev = new AtrBussLrcToUlxDev();
            ulDevList.add(dev);
            dev.setYearMonth(yearMonth);
            dev.setMainId(ulMainId);
            dev.setDevNo(i);

            // 应收保费设置
            if (i == 0) {
                // 当期已支付的保费
                dev.setRecvPremium(curPaidPremium.multiply(rate));
            } else if (!hasPaidRecord && i == 1) {
                // 合约计提前，第一期应收MDP
                dev.setRecvPremium(mdp.multiply(rate));
            }

            // 计算非当期的已赚率和已赚保费（1/365分法）
            if (i > 0 && i <= Dates.monthsBetween(evDate, treatyInfo.getExpiryDate())) {
                Date devDate = devDate(i);
                Date devDateBom = Dates.truncMonth(devDate);

                if (i == Dates.monthsBetween(evDate, treatyInfo.getExpiryDate())) {
                    // 最后一个月可能不是整月
                    BigDecimal devDays = new BigDecimal(Dates.daysBetween(devDateBom, treatyInfo.getExpiryDate()) + 1);
                    dev.setEdRate(roundR(devDays.divide(totalDays, MC)));
                } else {
                    // 中间月份按当月天数计算
                    BigDecimal devDays = new BigDecimal(Dates.daysBetween(devDateBom, devDate) + 1);
                    dev.setEdRate(roundR(devDays.divide(totalDays, MC)));
                }

                // 计算非当期的已赚保费 - 使用可分配保费作为基础
                BigDecimal edPremium = round(allocatablePremium.multiply(dev.getEdRate()));
                dev.setEdPremium(edPremium);
                sumEdPremiumEcept0 = round(sumEdPremiumEcept0.add(edPremium));
            }
        }

        // 计算当期已赚保费 = 可分配保费 - 非当期已赚保费
        BigDecimal edPremium0 = round(allocatablePremium.subtract(sumEdPremiumEcept0));
        ulDevList.get(0).setEdPremium(edPremium0);

        // 创建并填充底层保单对象
        AtrBussLrcToUlx ulx = new AtrBussLrcToUlx();
        BeanUtils.copyProperties(ulPolicy, ulx);
        ulx.setId(ulMainId);
        ulx.setYearMonth(yearMonth);
        ulx.setActionNo(actionNo);
        ulx.setTreatyNo(treatyNo);
        ulx.setRiskCode(ulPolicy.getRiskCode());
        ulx.setPortfolioNo(treatyInfo.getPortfolioNo());
        ulx.setIcgNo(treatyInfo.getIcgNo());
        ulx.setIcgName(treatyInfo.getIcgName());
        ulx.setCmunitNo(treatyInfo.getCmunitNo());
        ulx.setContractDate(treatyInfo.getContractDate());
        ulx.setEntityId(ulPolicy.getEntityId());

        // 从paidInfoIndex获取对象，然后获取字段值
        List<?> key = Collections.singletonList(treatyNo);
        AtrDuctLrcToxPaid paidInfo = paidInfoIndex.one(key);
        if (paidInfo != null) {
            ulx.setCompanyCode1(paidInfo.getCompanyCode1());
            ulx.setCompanyCode2(paidInfo.getCompanyCode2());
            ulx.setCompanyCode3(paidInfo.getCompanyCode3());
            ulx.setCompanyCode4(paidInfo.getCompanyCode4());
            ulx.setFinAccChannel(paidInfo.getFinAccChannel());
            ulx.setTreatyName(paidInfo.getTreatyName());
        }

        // 从底层保单获取部门ID、渠道ID和核算机构字段
        ulx.setFinProductCode(ulPolicy.getFinProductCode());
        ulx.setFinDetailCode(ulPolicy.getFinDetailCode());
        ulx.setFinSubProductCode(ulPolicy.getFinSubProductCode());
        ulx.setDeptId(ulPolicy.getDeptId());
        ulx.setChannelId(ulPolicy.getChannelId());
        ulx.setCenterCode(ulPolicy.getCenterCode());

        // 从treatyInfo中获取pl_judge_rslt字段值
        ulx.setPlJudgeRslt(treatyInfo.getPlJudgeRslt());

        // 更新当前累计已赚保费和当期已赚保费
        ulx.setCumlCurEdMdp(cumlCurEdMdpPre.add(edPremium0));
        ulx.setCurEdPremium(edPremium0);

        // 保存数据
        abp.insert(ulx);
        ulDevList.forEach(abp::insert);

        // 收集ICG数据
        collectIcg(treatyInfo, ulDevList);
    }

}
