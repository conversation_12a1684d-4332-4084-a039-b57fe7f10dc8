[2m2025-07-30 11:02:19.303[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m The following profiles are active: dev
[2m2025-07-30 11:02:19.927[0;39m [dev] [33m WARN[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-30 11:02:20.036[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=ff1a2d2c-d19d-3931-a591-81b4348e9e38
[2m2025-07-30 11:02:20.319[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 7602 (http)
[2m2025-07-30 11:02:20.387[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 1065 ms
[2m2025-07-30 11:02:20.451[0;39m [dev] [33m WARN[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-30 11:02:20.453[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-30 11:02:20.461[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.netflix.config.DynamicPropertyFactory [0;39m [2m:[0;39m DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@6573d2f7
[2m2025-07-30 11:02:21.189[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-30 11:02:21.190[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-30 11:02:21.273[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-30 11:02:21.273[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-30 11:02:21.742[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.web.DefaultSecurityFilterChain    [0;39m [2m:[0;39m Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@117b2cc6, org.springframework.security.web.context.SecurityContextPersistenceFilter@1a717d79, org.springframework.security.web.header.HeaderWriterFilter@4899799b, org.springframework.security.web.authentication.logout.LogoutFilter@4ff66917, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@1b3a9ef4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@13aed42b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@22ae905f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6def0632, org.springframework.security.web.session.SessionManagementFilter@15994b0b, org.springframework.security.web.access.ExceptionTranslationFilter@6775c0d1, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@42107318]
[2m2025-07-30 11:02:21.749[0;39m [dev] [33m WARN[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-30 11:02:21.749[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-30 11:02:21.847[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService 'applicationTaskExecutor'
[2m2025-07-30 11:02:22.414[0;39m [dev] [33m WARN[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mockingLoadBalancerClientRibbonWarnLogger[0;39m [2m:[0;39m You already have RibbonLoadBalancerClient on your classpath. It will be used by default. As Spring Cloud Ribbon is in maintenance mode. We recommend switching to BlockingLoadBalancerClient instead. In order to use it, set the value of `spring.cloud.loadbalancer.ribbon.enabled` to `false` or remove spring-cloud-starter-netflix-ribbon from your project.
[2m2025-07-30 11:02:22.507[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-30 11:02:22.540[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Initializing Eureka in region us-east-1
[2m2025-07-30 11:02:22.540[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Client configured to neither register nor query for data.
[2m2025-07-30 11:02:22.551[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Discovery Client initialized at timestamp 1753844542550 with initial instances count: 0
[2m2025-07-30 11:02:22.589[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initializing ...
[2m2025-07-30 11:02:22.590[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Adding new peer nodes [**********************************************/eureka/]
[2m2025-07-30 11:02:23.028[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-30 11:02:23.028[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-30 11:02:23.028[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-30 11:02:23.028[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-30 11:02:23.121[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Replica node URL:  **********************************************/eureka/
[2m2025-07-30 11:02:23.130[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Finished initializing remote region registries. All known remote regions: []
[2m2025-07-30 11:02:23.130[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initialized
[2m2025-07-30 11:02:23.140[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 2 endpoint(s) beneath base path '/actuator'
[2m2025-07-30 11:02:23.210[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application SS-PLATFORM-EUREKA with eureka with status UP
[2m2025-07-30 11:02:23.212[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Setting the eureka configuration..
[2m2025-07-30 11:02:23.212[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka data center value eureka.datacenter is not set, defaulting to default
[2m2025-07-30 11:02:23.213[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka environment value eureka.environment is not set, defaulting to test
[2m2025-07-30 11:02:23.223[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m isAws returned false
[2m2025-07-30 11:02:23.223[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Initialized server context
[2m2025-07-30 11:02:23.224[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Got 1 instances from neighboring DS node
[2m2025-07-30 11:02:23.224[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Renew threshold is: 1
[2m2025-07-30 11:02:23.224[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Changing status to UP
[2m2025-07-30 11:02:23.232[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36me.s.EurekaServerInitializerConfiguration[0;39m [2m:[0;39m Started Eureka Server
[2m2025-07-30 11:02:23.248[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 7602 (http) with context path ''
[2m2025-07-30 11:02:23.249[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 7602
[2m2025-07-30 11:02:23.612[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m Started EurekaServerApplication in 5.535 seconds (JVM running for 7.864)
[2m2025-07-30 11:02:25.700[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[6)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-30 11:02:25.703[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[6)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 3 ms
[2m2025-07-30 11:02:59.273[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=false)
[2m2025-07-30 11:02:59.969[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=true)
[2m2025-07-30 11:03:05.963[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=false)
[2m2025-07-30 11:03:06.482[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-6][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=true)
[2m2025-07-30 11:03:10.977[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=false)
[2m2025-07-30 11:03:11.506[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-9][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=true)
[2m2025-07-30 11:03:13.716[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=false)
[2m2025-07-30 11:03:14.248[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=true)
[2m2025-07-30 11:03:23.228[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 11:04:10.384[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-30 11:04:10.911[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-30 11:04:23.242[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 11:05:23.256[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
