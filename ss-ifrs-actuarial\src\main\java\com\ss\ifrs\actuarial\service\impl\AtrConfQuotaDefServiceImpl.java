package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.dao.conf.AtrConfQuotaDefDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfQuotaDefFactDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfQuotaDefFactHisDao;
import com.ss.ifrs.actuarial.dao.conf.AtrConfQuotaDefHisDao;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDef;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDefFact;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDefFactHis;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDefHis;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaDefFactVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaDefVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaVo;
import com.ss.ifrs.actuarial.service.AtrConfQuotaDataService;
import com.ss.ifrs.actuarial.service.AtrConfQuotaDefFactService;
import com.ss.ifrs.actuarial.service.AtrConfQuotaDefService;
import com.ss.ifrs.actuarial.service.AtrConfQuotaService;
import com.ss.ifrs.actuarial.constant.ActuarialConstant;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.platform.util.ClassUtil;
import com.ss.library.utils.FilterUtil;
import com.ss.library.utils.StringUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName AtrConfQuotaDefServiceImpl
 * <AUTHOR>
 * @Date 2021/11/10
 **/
@Service("atrConfQuotaDefService")
@Transactional
public class AtrConfQuotaDefServiceImpl implements AtrConfQuotaDefService {

    @Autowired
    AtrConfQuotaDefDao atrConfQuotaDefDao;

    @Autowired
    AtrConfQuotaDefHisDao atrConfQuotaDefHisDao;
    
    @Autowired
    AtrConfQuotaDataService atrConfQuotaService;

    @Autowired
    AtrConfQuotaDefFactService atrConfQuotaDefFactService;

    @Autowired
    AtrConfQuotaDefFactDao atrConfQuotaDefFactDao;

    @Autowired
    AtrConfQuotaDefFactHisDao atrConfQuotaDefFactHisDao;

    final Logger LOG = LoggerFactory.getLogger(getClass());

    @Override
    public Page<?> findAtrConfQuotaDefList(AtrConfQuotaDefVo atrConfQuotaDefVo, Pageable pageable){
        //模糊查询把%替换成*, 如查H开头的编号，H*。后加%也可以模糊查询
        atrConfQuotaDefVo.setQuotaCode(FilterUtil.transitionSearch(atrConfQuotaDefVo.getQuotaCode()));
        atrConfQuotaDefVo.setQuotaName(FilterUtil.transitionSearch(atrConfQuotaDefVo.getQuotaName()));

        // 原表达式为“code - name”,故需改为实际的code
        String ruleCode = atrConfQuotaDefVo.getRuleCode();
        String[] ruleCodes = new String[0];
        if(null != ruleCode && !"".equals(atrConfQuotaDefVo.getRuleCode())){
            ruleCodes = atrConfQuotaDefVo.getRuleCode().split("-");
        }
        if(null != ruleCodes && ruleCodes.length > 0){
            atrConfQuotaDefVo.setRuleCode(ruleCodes[0].trim());
        }

        Page<AtrConfQuotaDef> page = atrConfQuotaDefDao.fuzzySearchPage(atrConfQuotaDefVo,pageable);
        Page<AtrConfQuotaDefVo> resList = ClassUtil.convert(page,AtrConfQuotaDefVo.class);
        return resList;
    }

    @Override
    public void addOrUpdateVo(AtrConfQuotaDefVo atrConfQuotaDefVo, Long userId){
        try{
            AtrConfQuotaDef po;
            String codeType = atrConfQuotaDefVo.getCodeType();
            Integer versionNo;
            //数组初始化
            String[] codeTypes = new String[0];
            if(null != codeType){
                codeTypes = atrConfQuotaDefVo.getCodeType().split("-");
            }

            if(atrConfQuotaDefVo.getQuotaDefId() == null){
                po = ClassUtil.convert(atrConfQuotaDefVo, AtrConfQuotaDef.class);
                po.setCreateTime(new Date());
                po.setCreatorId(userId);
                po.setAuditState(CommonConstant.AuditStatus.TO_BE_AUDITED);
                if(null != codeTypes && codeTypes.length > 0){
                    po.setCodeType(codeTypes[0].trim());
                }
                if("".equals(codeType)){
                    po.setCodeType(null);
                }

                po.setSerialNo(1);
                atrConfQuotaDefDao.save(po);

                if("".equals(codeType)){
                    atrConfQuotaDefDao.updateCodeType(po);
                }
                //写入轨迹表
                this.dealSaveHis(po, userId, CommonConstant.OperType.ADD);
                //
                if(atrConfQuotaDefVo.getUserModelList()!=null){
                    AtrConfQuotaDefFactVo atrConfQuotaDefFact = ClassUtil.convert(atrConfQuotaDefVo, AtrConfQuotaDefFactVo.class);
                    AtrConfQuotaDefFact factPo = new AtrConfQuotaDefFact();
                    for(String userModel : atrConfQuotaDefFact.getUserModelList()) {
                        factPo.setBusinessSourceCode(userModel);
                        factPo.setQuotaDefId(po.getQuotaDefId());
                        factPo.setQuotaCode(po.getQuotaCode());
                        factPo.setCreatorId(userId);
                        factPo.setCreateTime(new Date());
                        factPo.setSerialNo(po.getSerialNo());
                        atrConfQuotaDefFactService.save(factPo);
                    }
                }
            }else {
                AtrConfQuotaDef atrConfQuotaDef = atrConfQuotaDefDao.findById(atrConfQuotaDefVo.getQuotaDefId());
                po = ClassUtil.convert(atrConfQuotaDefVo, AtrConfQuotaDef.class);
                po.setUpdateTime(new Date());
                po.setUpdatorId(userId);
                po.setAuditState(CommonConstant.AuditStatus.TO_BE_AUDITED);
                if(null != codeTypes && codeTypes.length > 0){
                    po.setCodeType(codeTypes[0].trim());
                }
                if("".equals(codeType)){
                    po.setCodeType(null);
                }

                versionNo = po.getSerialNo();
                if(versionNo == null){
                    versionNo = 1;
                }else {
                    versionNo++;
                }
                po.setSerialNo(versionNo);

                atrConfQuotaDefDao.updateById(po);
                if(null == po.getCodeType() ||  "".equals(po.getCodeType())){
                    if(null != atrConfQuotaDef && null != atrConfQuotaDef.getCodeType() && !"".equals(atrConfQuotaDef.getCodeType())){
                        po.setCodeType(null);
                    }
                }
                if("".equals(codeType)){
                    atrConfQuotaDefDao.updateCodeType(po);
                }

                //写入轨迹表
                this.dealSaveHis(po, userId, CommonConstant.OperType.MODIFY);
                //
                if(atrConfQuotaDefVo.getUserModelList()!=null){
                    AtrConfQuotaDefFactVo atrConfQuotaDefFact = ClassUtil.convert(atrConfQuotaDefVo, AtrConfQuotaDefFactVo.class);
                    AtrConfQuotaDefFact factPo = new AtrConfQuotaDefFact();
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("quotaDefId", atrConfQuotaDefFact.getQuotaDefId());
                    atrConfQuotaDefFactDao.deleteByMap(map);
                    for(String model : atrConfQuotaDefFact.getUserModelList()) {
                        factPo.setBusinessSourceCode(model);
                        factPo.setQuotaDefId(po.getQuotaDefId());
                        factPo.setQuotaCode(po.getQuotaCode());
                        factPo.setCreatorId(userId);
                        factPo.setCreateTime(new Date());
                        factPo.setSerialNo(po.getSerialNo());
                        atrConfQuotaDefFactService.save(factPo);
                    }
                }
                if(!atrConfQuotaDef.getQuotaClass().equals(atrConfQuotaDefVo.getQuotaClass())) {
                    atrConfQuotaService.syncQuotaClass(atrConfQuotaDefVo);
                }
            }
        } catch (UnexpectedRollbackException e) {
            LOG.error(e.getLocalizedMessage(), e);
            throw e;
        }
    }

    @Override
    public AtrConfQuotaDefVo findByPk(Long quotaDefId){
        AtrConfQuotaDefVo atrConfQuotaDefVo = new AtrConfQuotaDefVo();
        if(null != quotaDefId){
            atrConfQuotaDefVo = atrConfQuotaDefDao.findVoById(quotaDefId);
        }
        return atrConfQuotaDefVo;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public String deleteByPk(Long quotaDefId, Long userId){
        // result：1可删除；0不可删除（该指标已有相关功能使用）。默认可删除
        String result = "1";
        AtrConfQuotaVo atrConfQuotaVo = new AtrConfQuotaVo();
        atrConfQuotaVo.setQuotaDefId(quotaDefId);
        List<AtrConfQuotaVo> atrConfQuotaVos =  atrConfQuotaService.findList(atrConfQuotaVo);

        // 如果该指标定义有被使用，则不可删除
        if(StringUtil.isNotEmpty(atrConfQuotaVos)){
            result = "0";
        }else {
            //写入轨迹表
            AtrConfQuotaDef atrConfQuotaDef = atrConfQuotaDefDao.findById(quotaDefId);
            atrConfQuotaDef.setSerialNo(ObjectUtils.isEmpty(atrConfQuotaDef.getSerialNo()) ? 1 : atrConfQuotaDef.getSerialNo() + 1);
            this.dealSaveHis(atrConfQuotaDef, userId, CommonConstant.OperType.DELETE);
            atrConfQuotaDefDao.deleteById(quotaDefId);
            atrConfQuotaDefFactService.delete(quotaDefId);
        }

        return result;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public void updateAudit(AtrConfQuotaDefVo atrConfQuotaDefVo, Long userId) {
        try{
            atrConfQuotaDefVo.setCheckedTime(new Date());
            atrConfQuotaDefVo.setCheckedId(userId);
            atrConfQuotaDefDao.updateAudit(atrConfQuotaDefVo);
            AtrConfQuotaDef atrConfQuotaDef = ClassUtil.convert(atrConfQuotaDefVo, AtrConfQuotaDef.class);
            this.dealSaveHis(atrConfQuotaDef, userId, CommonConstant.OperType.AUDIT);
        }catch (UnexpectedRollbackException e){
            LOG.error(e.getLocalizedMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public String disableValid(AtrConfQuotaDefVo atrConfQuotaDefVo, Long userId) {
        AtrConfQuotaDef atrConfQuotaDef = atrConfQuotaDefDao.findById(atrConfQuotaDefVo.getQuotaDefId());
        if (!atrConfQuotaDef.getValidIs().equals(atrConfQuotaDefVo.getValidIs())) {
            try {
                atrConfQuotaDefVo.setAuditState(CommonConstant.AuditStatus.TO_BE_AUDITED);
                atrConfQuotaDefVo.setCheckedMsg("");
                atrConfQuotaDefDao.updateValid(ClassUtil.convert(atrConfQuotaDefVo, AtrConfQuotaDef.class));
                //写入轨迹表
                this.dealSaveHis(atrConfQuotaDef, userId, CommonConstant.OperType.MODIFY);
                return atrConfQuotaDefVo.getValidIs();
            } catch (UnexpectedRollbackException e) {
                LOG.error(e.getLocalizedMessage(), e);
                throw e;
            }
        } else {
            atrConfQuotaDefVo.setAuditState(CommonConstant.AuditStatus.TO_BE_AUDITED);
            atrConfQuotaDefDao.updateValid(ClassUtil.convert(atrConfQuotaDefVo, AtrConfQuotaDef.class));
            //写入轨迹表
            this.dealSaveHis(atrConfQuotaDef, userId, CommonConstant.OperType.MODIFY);
            return atrConfQuotaDefVo.getValidIs();
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
    public String batchAudit(ArrayList<AtrConfQuotaDefVo> auditDealList, Long userId) {
        /**
         * 业务逻辑：
         *  1、审核状态变为待审核状态 0-待审核；
         */
        try {
            List<AtrConfQuotaDef> atrConfQuotaDefList = ClassUtil.convert(auditDealList, AtrConfQuotaDef.class);
            atrConfQuotaDefList.stream().forEach(atrConfQuotaDef -> {
                // 更新前获取待更新对象原始数据，插入日志表
                Date operDate = new Date();//更新时间
                atrConfQuotaDef.setUpdatorId(userId);
                atrConfQuotaDef.setUpdateTime(operDate);
                atrConfQuotaDef.setCheckedId(userId);
                atrConfQuotaDef.setCheckedTime(operDate);
                atrConfQuotaDefDao.updateById(atrConfQuotaDef);
                // 先写入轨迹表，内部已捕捉异常处理,操作类型(1-add；2-Modify；3-delete；4-audit
                this.dealSaveHis(atrConfQuotaDef, userId, CommonConstant.OperType.AUDIT);
            });

            return ResCodeConstant.ResCode.SUCCESS;
        } catch (UnexpectedRollbackException e) {
            LOG.error(e.getLocalizedMessage(),e);
            throw e;
        }
    }

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 保存轨迹信息
     * @Return
     */
    private void dealSaveHis(AtrConfQuotaDef po, Long userId, String operType) {
        AtrConfQuotaDefHis atrConfQuotaDefHis = new AtrConfQuotaDefHis();
        ClassUtil.copyProperties(po, atrConfQuotaDefHis);
        atrConfQuotaDefHis.setOperId(userId);
        atrConfQuotaDefHis.setOperTime(new Date());
        atrConfQuotaDefHis.setOperType(operType);
        atrConfQuotaDefHisDao.save(atrConfQuotaDefHis);
    }

    @Override
    public List<AtrConfQuotaDef> findVoByQuota(AtrConfQuotaVo atrConfQuotaVo){
        return atrConfQuotaDefDao.findVoByQuota(atrConfQuotaVo);
    }

    @Override
    public List<AtrConfQuotaDefVo> findDefListByVo(AtrConfQuotaVo atrConfQuotaVo){
        return atrConfQuotaDefDao.findDefListByVo(atrConfQuotaVo);
    }

    @Override
    public List<AtrConfQuotaDefVo> findDefListByPeriod(AtrConfQuotaVo atrConfQuotaVo){
        return atrConfQuotaDefDao.findDefListByPeriod(atrConfQuotaVo);
    }

    @Override
    public String findValidAtrQuotaCode(String quotaCode){
        AtrConfQuotaDef atrConfQuotaDef = atrConfQuotaDefDao.findByAtrQuotaCode(quotaCode);
        String validString = "1";
        //如果找到说明已存在相关code
        if (atrConfQuotaDef != null) {
            validString = "0";
        }
        return validString;
    }

    /*@Override
    public List<AtrConfQuotaDefVo> finDefListByAdd(AtrConfQuotaVo atrConfQuotaVo){
        return atrConfQuotaDefDao.finDefListByAdd(atrConfQuotaVo);
    }*/

    @Override
    public AtrConfQuotaDef findByQuotaCode(String quotaCode){
        return atrConfQuotaDefDao.findByAtrQuotaCode(quotaCode);
    }

    @Override
    public List<AtrConfQuotaDef> findList(AtrConfQuotaDef atrConfQuotaDef){
        return atrConfQuotaDefDao.findList(atrConfQuotaDef);
    }

    @Override
    public AtrConfQuotaDefVo findVoByPk(Long quotaDefId){
        AtrConfQuotaDefVo atrConfQuotaDefVo = new AtrConfQuotaDefVo();
        if(null != quotaDefId){
            atrConfQuotaDefVo = atrConfQuotaDefDao.findVoById(quotaDefId);
            List<AtrConfQuotaDefFact> list = atrConfQuotaDefFactDao.findByDefId(quotaDefId);
            List<String> defList = new ArrayList<>();
            for(AtrConfQuotaDefFact atrConfQuotaDefFact: list){
                defList.add(atrConfQuotaDefFact.getBusinessSourceCode());
            }
            atrConfQuotaDefVo.setUserModelList(defList);
        }
        return atrConfQuotaDefVo;
    }

    @Override
    public AtrConfQuotaDefVo findHisVoByPk(Long quotaDefId) {
        AtrConfQuotaDefVo atrConfQuotaDefVo = new AtrConfQuotaDefVo();
        if(null != quotaDefId){
            atrConfQuotaDefVo = atrConfQuotaDefHisDao.findVoByHisId(quotaDefId);
            List<AtrConfQuotaDefFactHis> list = atrConfQuotaDefFactHisDao.findByDefVo(quotaDefId);
            List<String> defList = new ArrayList<>();
            for(AtrConfQuotaDefFactHis atrConfQuotaDefFact: list){
                defList.add(atrConfQuotaDefFact.getBusinessSourceCode());
            }
            atrConfQuotaDefVo.setUserModelList(defList);
        }
        return atrConfQuotaDefVo;
    }

    @Override
    public Map<String, Object> findQuotaDefByGroup(AtrConfQuotaDefVo confQuotaDefVo) {
        Map<String, Object> quotaDefMap = new LinkedHashMap<>();
        AtrConfQuotaDef atrConfQuotaDef = new AtrConfQuotaDef();
        atrConfQuotaDef.setValidIs(CommonConstant.ValidStatus.VALID);
        atrConfQuotaDef.setAuditState(CommonConstant.AuditStatus.APPROVED);
        atrConfQuotaDef.setBusinessSourceCode(confQuotaDefVo.getBusinessSourceCode());
        atrConfQuotaDef.setDimension(confQuotaDefVo.getDimension());
        atrConfQuotaDef.setQuotaGroup(ActuarialConstant.QuotaGroup.LIC);
        List<AtrConfQuotaDef> quotaDefList = atrConfQuotaDefDao.findByPo(atrConfQuotaDef);
        if(ObjectUtils.isEmpty(quotaDefList)) {
            return quotaDefMap;
        }
        List<AtrConfQuotaDef> quotaDefListSort =  quotaDefList.stream().sorted(Comparator.comparing(AtrConfQuotaDef::getDisplayNo)).collect(Collectors.toList());
        for(AtrConfQuotaDef confQuotaDef : quotaDefListSort) {
            quotaDefMap.put(confQuotaDef.getQuotaCode(), confQuotaDef);
        }
        return quotaDefMap;
    }

    @Override
    public List<AtrConfQuotaDefVo> findQuotaDefList(AtrConfQuotaDefVo confQuotaDefVo) {
        AtrConfQuotaDef atrConfQuotaDef = new AtrConfQuotaDef();
        atrConfQuotaDef.setValidIs(CommonConstant.ValidStatus.VALID);
        atrConfQuotaDef.setAuditState(CommonConstant.AuditStatus.APPROVED);
        atrConfQuotaDef.setBusinessSourceCode(confQuotaDefVo.getBusinessSourceCode());
        atrConfQuotaDef.setDimension(confQuotaDefVo.getDimension());
        atrConfQuotaDef.setQuotaGroup(ActuarialConstant.QuotaGroup.LIC);
        List<AtrConfQuotaDef> quotaDefList = atrConfQuotaDefDao.findByPo(atrConfQuotaDef);
        return ClassUtil.convert(quotaDefList, AtrConfQuotaDefVo.class);
    }

    @Override
    public List<AtrConfQuotaDefVo> findConfQuotaCodeType() {
        AtrConfQuotaDef atrConfQuotaDef = new AtrConfQuotaDef();
        atrConfQuotaDef.setValidIs(CommonConstant.ValidStatus.VALID);
        atrConfQuotaDef.setAuditState(CommonConstant.AuditStatus.APPROVED);
        atrConfQuotaDef.setQuotaValueType("5");
        List<AtrConfQuotaDef> atrConfQuotaDefList = atrConfQuotaDefDao.findList(atrConfQuotaDef);
        return ClassUtil.convert(atrConfQuotaDefList, AtrConfQuotaDefVo.class);
    }

}
