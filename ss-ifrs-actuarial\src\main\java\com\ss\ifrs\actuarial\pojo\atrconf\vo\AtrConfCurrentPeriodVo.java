/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2021-02-23 15:17:10
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import java.io.Serializable;

/**
 * Description: 计量单元过程数据-当期<br/>
 */
public class AtrConfCurrentPeriodVo implements Serializable {

    private String unitNo   ;          // 计量单元
    private long evaluateMainId  ;
    private long curPeriod   ;        // 当期期次
    private Double docIacfFee     ;   // 跟单保险获取现金流
    private Double repBopNonEarned  ; // 报告期初未赚保费
    private Double curEopNonEarned   ;  // 当期期末未赚保费
    private Double curPremium       ;   // 当期收取保费
    private Double surrenderChargeFee ;  // 退保手续费
    private Double curDocIacfFee    ;   // 当期支付的跟单保险获取现金流
    private Double curNonDocIacfFee ;   // 当期支付的非跟单保险获取现金流

    public String getUnitNo() {
        return unitNo;
    }

    public void setUnitNo(String unitNo) {
        this.unitNo = unitNo;
    }

    public long getEvaluateMainId() {
        return evaluateMainId;
    }

    public void setEvaluateMainId(long evaluateMainId) {
        this.evaluateMainId = evaluateMainId;
    }

    public long getCurPeriod() {
        return curPeriod;
    }

    public void setCurPeriod(long curPeriod) {
        this.curPeriod = curPeriod;
    }

    public Double getDocIacfFee() {
        return docIacfFee;
    }

    public void setDocIacfFee(Double docIacfFee) {
        this.docIacfFee = docIacfFee;
    }

    public Double getRepBopNonEarned() {
        return repBopNonEarned;
    }

    public void setRepBopNonEarned(Double repBopNonEarned) {
        this.repBopNonEarned = repBopNonEarned;
    }

    public Double getCurEopNonEarned() {
        return curEopNonEarned;
    }

    public void setCurEopNonEarned(Double curEopNonEarned) {
        this.curEopNonEarned = curEopNonEarned;
    }

    public Double getCurPremium() {
        return curPremium;
    }

    public void setCurPremium(Double curPremium) {
        this.curPremium = curPremium;
    }

    public Double getSurrenderChargeFee() {
        return surrenderChargeFee;
    }

    public void setSurrenderChargeFee(Double surrenderChargeFee) {
        this.surrenderChargeFee = surrenderChargeFee;
    }

    public Double getCurDocIacfFee() {
        return curDocIacfFee;
    }

    public void setCurDocIacfFee(Double curDocIacfFee) {
        this.curDocIacfFee = curDocIacfFee;
    }

    public Double getCurNonDocIacfFee() {
        return curNonDocIacfFee;
    }

    public void setCurNonDocIacfFee(Double curNonDocIacfFee) {
        this.curNonDocIacfFee = curNonDocIacfFee;
    }
}