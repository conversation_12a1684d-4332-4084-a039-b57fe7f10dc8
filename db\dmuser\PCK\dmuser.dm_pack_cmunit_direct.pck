CREATE OR REPLACE PACKAGE dm_pack_cmunit_direct IS

  FUNCTION func_get_current_year_month(p_entity_id  NUMBER,
                                       p_year_month VARCHAR2) RETURN VARCHAR2;

  FUNCTION func_get_valid_year_month(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2) RETURN VARCHAR2;

  PROCEDURE proc_cmunit_identify_all(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2);

  PROCEDURE proc_cmunit_identify(p_entity_id  NUMBER,
                                 p_year_month VARCHAR2);

  PROCEDURE proc_majorrisk_test(p_entity_id  NUMBER,
                                p_year_month VARCHAR2);

  PROCEDURE proc_majorrisk_test_nopass(p_entity_id  NUMBER,
                                       p_year_month VARCHAR2);

  PROCEDURE proc_approach_discern(p_entity_id  NUMBER,
                                  p_year_month VARCHAR2);

  PROCEDURE proc_portfolio_discern(p_entity_id  NUMBER,
                                   p_year_month VARCHAR2);

  PROCEDURE proc_profit_loss_discern(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2);

  /*PROCEDURE proc_icg_fixed(p_entity_id  NUMBER,
                           p_year_month VARCHAR2);*/

  PROCEDURE proc_icg_discern(p_entity_id  NUMBER,
                             p_year_month VARCHAR2);

  PROCEDURE proc_icg_group(p_entity_id  NUMBER,
                           p_year_month VARCHAR2);

  PROCEDURE proc_investment_separate(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2);

  PROCEDURE proc_contract_group_confirm(p_entity_id  NUMBER,
                                        p_year_month VARCHAR2);

  PROCEDURE proc_backup_profit_unit_result(p_entity_id  NUMBER,
                                           p_year_month VARCHAR2);

  PROCEDURE proc_buss_cmunit_log(p_entity_id  NUMBER,
                                 p_year_month VARCHAR2,
                                 p_trace_no VARCHAR2,
                                 p_trace_code VARCHAR2,
                                 p_trace_status VARCHAR2,
                                 p_trace_msg VARCHAR2);
END dm_pack_cmunit_direct;
/
CREATE OR REPLACE PACKAGE BODY dm_pack_cmunit_direct IS

  FUNCTION func_get_current_year_month(p_entity_id  NUMBER,
                                       p_year_month VARCHAR2) RETURN VARCHAR2 IS
  BEGIN
    RETURN dm_pack_buss_period.func_get_current_year_month(p_entity_id, p_year_month, 'BUSS_CMUNIT_DIRECT');

  END func_get_current_year_month;

  FUNCTION func_get_valid_year_month(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2) RETURN VARCHAR2 IS
  BEGIN
    RETURN dm_pack_buss_period.func_get_valid_year_month(p_entity_id, p_year_month, 'BUSS_CMUNIT_DIRECT');

  END func_get_valid_year_month;

  PROCEDURE proc_cmunit_identify_all(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2) IS
      --CUR_YEARMONTH RECORD;
  BEGIN

    IF p_year_month IS NULL OR length(p_year_month) = 0 THEN

      FOR cur_yearmonth IN (SELECT year_month
                              FROM dm_conf_bussperiod
                             WHERE entity_id = p_entity_id
                               AND period_state IN ('0', '1', '2')
                             ORDER BY year_month) LOOP

        -- 直保 - 生成计量单元
        proc_cmunit_identify(p_entity_id, cur_yearmonth.year_month);

      END LOOP;
    ELSE
      -- 直保 - 生成计量单元
      proc_cmunit_identify(p_entity_id, p_year_month);
    END IF;

  END proc_cmunit_identify_all;

  PROCEDURE proc_cmunit_identify(p_entity_id  NUMBER,
                                 p_year_month VARCHAR2) IS
    v_proc_id                 NUMBER;
    v_end_date                DATE;
    v_short_risk_flag_open_is VARCHAR(1);
    v_tic_code                VARCHAR2(50);
    v_error_msg               varchar2(2000);
    v_tic_code_conf           varchar2(100);
  BEGIN

    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]计量单元划分-传参不为空：proc_cmunit_identify(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_valid_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]计量单元划分-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    v_end_date := add_months(to_date(p_year_month, 'yyyymm'), 1);
    --根据PROC_CODE查询PROC_ID
    v_proc_id := dm_pack_common.func_get_procid('DM_UNIT_RECOGNIZER');
    v_tic_code := 'TicCode/';

    --长短险标识生成是否开启 0-不开启
    SELECT MIN(valid_is)
      INTO v_short_risk_flag_open_is
      FROM bpluser.bpl_conf_code
     WHERE code_code = 'ShortRiskFlagOpenIs'
       AND upper_code_id = 0
       AND valid_is = '1';

    IF v_short_risk_flag_open_is <> '1' THEN
      v_short_risk_flag_open_is := '0';
    END IF;
    
    --查询适配tic_code的值
    select code_code into v_tic_code_conf
     from bpluser.bpl_conf_code 
           where upper_code_id = (select code_id from bpluser.bpl_conf_code where upper_code_id = 0 and code_code = 'TicCodeConf' )
                and rownum = 1 ;

    ---- 保单信息----------------------
    INSERT INTO dm_buss_cmunit_direct
      (cm_unit_id,
       cmunit_no, -- 计量单元编码
       entity_id, -- 业务单位
       dept_id,
       business_source_code, -- 业务类型
       policy_type_code, -- 保单类型
       policy_no, -- 保单号
       loa_code, --LOA编码
       product_code, -- 产品代码
       risk_class_code, -- 险类代码
       risk_code, -- 险种代码
       short_risk_flag, -- 长短险标志
       min_loss_rate, -- 最低赔付率
       expected_loss_rate, -- 预估赔付率
       expected_expenses_rate, --預期費用率
       max_loss_rate, -- 最高赔付率
       --ERMINATED_UNLIMITED_IS, -- 是否无条件解约
       terminate_x_days, -- 提取X天解约
       present_is, -- 是否赠送,
       policy_nature_code, --团个单标识
       channel_code,
       coins_is, --是否司內共保標籤
       coins_policy_no, --司內共保方保單號
       draw_type,
       create_time,
       proc_id,
       node_state,
       buss_year_month,
       approval_date,
       effective_date,
       issue_date,
       offshore_is，
       tic_code)
      SELECT dm_seq_buss_cmunit_direct.nextval cm_unit_id,
             cmunit_no, -- 计量单元编码
             entity_id, -- 业务单位
             dept_id,
             business_source_code, -- 业务类型
             policy_type_code, -- 保单类型
             policy_no, -- 保单号
             loa_code, --LOA编码
             product_code, -- 产品代码
             risk_class_code, -- 险类代码
             risk_code, -- 险种代码
             short_risk_flag, -- 长短险标志
             min_loss_rate, -- 最低赔付率
             expected_loss_rate, -- 预估赔付率
             expected_expenses_rate, --預期費用率
             max_loss_rate, -- 最高赔付率
             --ERMINATED_UNLIMITED_IS, -- 是否无条件解约
             terminate_x_days, -- 提取X天解约
             present_is, -- 是否赠送,
             policy_nature_code, --团个单标识
             channel_code,
             coins_is, --是否司內共保標籤
             coins_policy_no, --司內共保方保單號
             draw_type,
             create_time,
             proc_id,
             node_state,
             buss_year_month,
             approval_date,
             effective_date,
             issue_date,
             offshore_is,
             tic_code
        FROM (SELECT row_number() over(PARTITION BY t1.entity_id, t1.policy_no ORDER BY 1) rn,
                     --ON(T1.entity_id,T1.POLICY_NO,T2.RISK_CODE)
                     --NEXTVAL('DM_SEQ_BUSS_CMUNIT') CM_UNIT_ID,
                     /*DM_PACK_CMUNIT_COMMON_FUNC_GET_CMUNITNO(T1.entity_id,
                     TO_CHAR(T2.EFFECTIVE_DATE, 'YYYY'),
                     T2.RISK_CODE,
                     NULL) CMUNIT_NO,*/
                     dm_pack_cmunit_common.func_get_cmunitno(t1.entity_id, to_char(t2.effective_date, 'YYYY'), t2.product_code, NULL) cmunit_no,
                     t1.entity_id,
                     t1.dept_id,
                     t1.business_source_code,
                     t1.policy_type_code,
                     t1.policy_no,
                     (case when substr(t2.product_code,0,2) = v_tic_code_conf then
                                   (SELECT MIN(loa_code)
                        FROM bpluser.bbs_conf_loa        loa,
                             bpluser.bbs_conf_loa_detail load,
                             bpluser.bbs_conf_loa_tic tic
                       WHERE loa.loa_id = load.loa_id
                         and loa.audit_state = '1'
                         and loa.valid_is = '1'
                         AND loa.business_model = 'D'
                         AND loa.business_direction = 'D'
                         and loa.loa_id = tic.loa_id
                         and loa.entity_id = t1.entity_id
                         and tic.tic_id = (select code_id from bpluser.bpl_conf_code where
                                        upper_code_id = (select code_id from bpluser.bpl_conf_code where upper_code_id = 0 and code_code = 'TicCode' )
                                        and code_code = t2.tic_code )
                         AND load.business_id = (SELECT MAX(product_id) FROM bpluser.bbs_conf_product bcp WHERE bcp.product_code = t2.product_code and loa.entity_id = bcp.entity_id))
                     else
                        (SELECT MIN(loa_code)
                        FROM bpluser.bbs_conf_loa        loa,
                             bpluser.bbs_conf_loa_detail load
                       WHERE loa.loa_id = load.loa_id
                         and loa.audit_state = '1'
                         and loa.valid_is = '1'
                         AND loa.business_model = 'D'
                         AND loa.business_direction = 'D'
                         and loa.entity_id = t1.entity_id
                         AND load.business_id = (SELECT MAX(product_id) FROM bpluser.bbs_conf_product bcp WHERE bcp.product_code = t2.product_code and loa.entity_id = bcp.entity_id))
                      end) AS loa_code,
                     t2.product_code,
                     NULL AS risk_class_code,
                     NULL AS risk_code,
                     --T2.risk_class_code,
                     --T2.RISK_CODE,
                     (CASE
                       WHEN v_short_risk_flag_open_is = '1' THEN
                        (SELECT (CASE
                                  WHEN add_months(pm.effective_date, 12) > pm.expiry_date THEN
                                   '1' -- 短险
                                  ELSE
                                   '2' -- 长险
                                END)
                           FROM dm_policy_main pm
                          WHERE pm.entity_id = t2.entity_id
                            AND pm.policy_no = t2.policy_no
                            AND pm.endorse_seq_no = '000'
                            AND rownum = 1)
                       ELSE
                        '0'
                     END) AS short_risk_flag, -- 长短险标识
                     t1.min_loss_rate,
                     t1.expected_loss_rate,
                     t1.expected_expenses_rate, --預期費用率
                     t1.max_loss_rate,
                     --T1.TERMINATED_UNLIMITED_IS,
                     t1.terminate_x_days,
                     t1.present_is,
                     t1.policy_nature_code,
                     t1.channel_code,
                     t1.coins_is, --是否司內共保標籤
                     t1.coins_policy_no, --司內共保方保單號
                     t1.draw_type,
                     localtimestamp create_time,
                     v_proc_id proc_id,
                     '1' node_state,
                     p_year_month AS buss_year_month,
                     (SELECT pm.approval_date
                        FROM dm_policy_main pm
                       WHERE pm.entity_id = t2.entity_id
                         AND pm.policy_no = t2.policy_no
                         AND pm.endorse_seq_no = '000'
                         AND rownum = 1) AS approval_date,
                     (SELECT pm.effective_date
                        FROM dm_policy_main pm
                       WHERE pm.entity_id = t2.entity_id
                         AND pm.policy_no = t2.policy_no
                         AND pm.endorse_seq_no = '000'
                         AND rownum = 1) AS effective_date,
                     (SELECT pm.issue_date
                        FROM dm_policy_main pm
                       WHERE pm.entity_id = t2.entity_id
                         AND pm.policy_no = t2.policy_no
                         AND pm.endorse_seq_no = '000'
                         AND rownum = 1) AS issue_date,
                     t1.offshore_is,
                     (case when substr(t2.product_code,0,2) = v_tic_code_conf then
                       t2.tic_code
                      else
                         null
                     end) as tic_code
                FROM dm_policy_main t1
                JOIN dm_policy_premium t2
                  ON t1.policy_no = t2.policy_no
                 AND t1.endorse_seq_no = t2.endorse_seq_no
                    --AND T1.TASK_STATUS = T2.TASK_STATUS
                 AND t1.entity_id = t2.entity_id

               WHERE t1.entity_id = p_entity_id
                 AND t2.task_status = '4'
                    --AND TO_CHAR(T.approval_date,'YYYYMM') = P_YEAR_MONTH
                    -- 核保通过日期小于等于当前评估月，且为该范围最新的批单
                 AND t1.approval_date < v_end_date
                 and t1.endorse_seq_no = '000'
                 --AND (t1.endorse_seq_no = '000' OR (t1.endorse_seq_no <> '000' AND t1.endorse_effective_date < v_end_date))
                 AND NOT EXISTS (SELECT cm_unit_id
                        FROM dm_buss_cmunit_direct cm1
                       WHERE cm1.policy_no = t1.policy_no
                         AND cm1.entity_id = t1.entity_id
                      --确保不重复生成计量单元(校验规则未成功校验数据重复时)
                      )
               ORDER BY t1.entity_id,
                        t1.policy_no,
                        t2.endorse_seq_no) g
       WHERE rn = 1;
    COMMIT;

    --loa配置重新配置
    --只有处理中的才能重新执行
    IF func_get_current_year_month(p_entity_id, p_year_month) IS NOT NULL THEN
      UPDATE dm_buss_cmunit_direct cm
         SET node_state           = '1',
             proc_id              = v_proc_id,
             major_risk           = NULL,
             evaluate_approach    = NULL, --评估方法
             portfolio_no         = NULL,
             pl_judge_rslt        = NULL, --盈亏
             pl_judge_date        = NULL, --盈亏时间
             year_month           = NULL,
             border_date          = NULL,
             icg_no               = NULL,
             invest_rate          = NULL,
             invest_amount        = NULL,
             reason_of_failure    = NULL,
             reason_of_mr_failure = NULL,
             exception_message    = NULL,
             d1                   = NULL,
             d2                   = NULL,
             d3                   = NULL,
             d4                   = NULL,
             loa_code             =(case when substr(cm.product_code,0,2) = v_tic_code_conf then
                                       (SELECT MIN(loa_code)
                                        FROM bpluser.bbs_conf_loa        loa,
                                             bpluser.bbs_conf_loa_detail load,
                                             bpluser.bbs_conf_loa_tic tic
                                       WHERE loa.loa_id = load.loa_id
                                         and loa.audit_state = '1'
                                         and loa.valid_is = '1'
                                         AND loa.business_model = 'D'
                                         AND loa.business_direction = 'D'
                                         and loa.loa_id = tic.loa_id
                                         and tic.tic_id = (select code_id from bpluser.bpl_conf_code where
                                        upper_code_id = (select code_id from bpluser.bpl_conf_code where upper_code_id = 0 and code_code = 'TicCode' )
                                        and code_code = cm.tic_code )
                                         AND load.business_id = (SELECT MAX(product_id) FROM bpluser.bbs_conf_product bcp WHERE bcp.product_code = cm.product_code and loa.entity_id = bcp.entity_id))
                                     else
                                        (SELECT MIN(loa_code)
                                          FROM bpluser.bbs_conf_loa        loa,
                                               bpluser.bbs_conf_loa_detail load
                                         WHERE loa.loa_id = load.loa_id
                                           and loa.audit_state = '1'
                                           and loa.valid_is = '1'
                                           AND loa.business_model = 'D'
                                           AND loa.business_direction = 'D'
                                           AND load.business_id = (SELECT MAX(product_id) FROM bpluser.bbs_conf_product bcp WHERE bcp.product_code = cm.product_code and loa.entity_id = bcp.entity_id))
                                      end)
       WHERE year_month IS NULL
         AND buss_year_month <= p_year_month;
      --and loa_code is null;
      COMMIT;
    END IF;

    --未有loa配置提示配置不存在
    UPDATE dm_buss_cmunit_direct cm
       SET major_risk           = NULL,
           evaluate_approach    = NULL, --评估方法
           portfolio_no         = NULL,
           pl_judge_rslt        = NULL, --盈亏
           pl_judge_date        = NULL, --盈亏时间
           year_month           = NULL,
           border_date          = NULL,
           icg_no               = NULL,
           invest_rate          = NULL,
           invest_amount        = NULL,
           node_state           = '2', -- 节点状态
           proc_id              = v_proc_id,
           reason_of_failure    = 'DM_loa',
           reason_of_mr_failure = 'DM_loa',
           exception_message    = 'not find loa config' --异常信息
     WHERE year_month IS NULL
       AND buss_year_month <= p_year_month
       AND loa_code IS NULL;
    COMMIT;

    -- 修改已生成计量单元标识；按计量单元更新风险信息
    UPDATE dm_policy_premium t
       SET task_status = '5'
     WHERE EXISTS (SELECT 1
              FROM dm_buss_cmunit_direct g
             WHERE g.entity_id = t.entity_id
               AND g.policy_no = t.policy_no
            --AND G.ENDORSE_SEQ_NO = T.ENDORSE_SEQ_NO
            )
       AND t.entity_id = p_entity_id
       AND t.task_status = '4';
    COMMIT;

    -- 修改已生成计量单元标识
    --根据风险信息更新保单主表，才不会错误更新批改风险相应的批单主保单信息
    UPDATE dm_policy_main t
       SET task_status = '5'
     WHERE EXISTS (SELECT 1
              FROM dm_policy_premium g
             WHERE g.entity_id = t.entity_id
               AND g.policy_no = t.policy_no
                  --AND G.ENDORSE_SEQ_NO = T.ENDORSE_SEQ_NO
               AND g.task_status = '5')
       AND t.entity_id = p_entity_id
       AND t.task_status = '4';
    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN
      --意外处理
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]计量单元划分:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_cmunit_identify;

  --重大风险测试
  PROCEDURE proc_majorrisk_test(p_entity_id  NUMBER,
                                p_year_month VARCHAR2) IS
    v_proc_id   NUMBER;
    v_count     NUMBER;
    v_test_plan_type       VARCHAR2(200);
    v_error_msg            VARCHAR2(2000);
    v_buss_model VARCHAR2(10);
  BEGIN
    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]重测处理-传参不为空：proc_majorrisk_test(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]重测处理-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 根据PROC_CODE查询PROC_ID
    v_proc_id := dm_pack_common.func_get_procid('DM_RISK_TEST');

    IF v_proc_id IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]重测处理-获取流程节点(v_proc_id)为空';
      raise_application_error(-20002,v_error_msg);

    END IF;
    v_buss_model := 'DD';
    --查找重大风险方案的配置
    SELECT COUNT(1)
      INTO v_count
      FROM dm_conf_icg_majorrisk_plan
     WHERE entity_id = p_entity_id
       AND business_model||business_direction = v_buss_model;

    IF v_count > 0 THEN
      SELECT MIN(test_plan_type)
        INTO v_test_plan_type
        FROM dm_conf_icg_majorrisk_plan
       WHERE entity_id = p_entity_id
         AND business_model||business_direction = v_buss_model;
    END IF;

    if v_test_plan_type is null then
      v_test_plan_type := 'A';
    end if;

    IF v_test_plan_type IS NOT NULL THEN
      IF v_test_plan_type = 'A' THEN
        --全部通过
        UPDATE dm_buss_cmunit_direct t
           SET proc_id              = v_proc_id,
               evaluate_approach    = NULL, --评估方法
               portfolio_no         = NULL,
               pl_judge_rslt        = NULL, --盈亏
               pl_judge_date        = NULL, --盈亏时间
               year_month           = NULL,
               border_date          = NULL,
               icg_no               = NULL,
               invest_rate          = NULL,
               invest_amount        = NULL,
               node_state           = '1',
               major_risk           = 'Y',
               reason_of_failure    = NULL,
               reason_of_mr_failure = NULL,
               exception_message    = NULL
         WHERE entity_id = p_entity_id
           AND buss_year_month <= p_year_month
           AND year_month IS NULL;
        COMMIT;
      ELSIF v_test_plan_type = 'C' THEN
        --配置
            MERGE INTO DM_BUSS_CMUNIT_DIRECT A
                USING (SELECT CM.CM_UNIT_ID,
                              V_PROC_ID AS PROC_ID,
                              (CASE
                                  WHEN RD.TEST_RESULT IS NULL THEN
                                   '2'
                                  ELSE
                                   '1'
                              END) AS NODE_STATE,
                              RD.TEST_RESULT AS MAJOR_RISK,
                              (CASE
                                  WHEN RD.TEST_RESULT IS NULL THEN
                                   'DM_001'
                                  ELSE
                                   NULL
                              END) AS REASON_OF_FAILURE,
                              (CASE
                                  WHEN RD.TEST_RESULT IS NULL THEN
                                   'DM_001'
                                  ELSE
                                   NULL
                              END) AS REASON_OF_MR_FAILURE,
                              (CASE
                                  WHEN RD.TEST_RESULT IS NULL THEN
                                   'DM_001'
                                  ELSE
                                   NULL
                              END) AS EXCEPTION_MESSAGE
                         FROM DM_BUSS_CMUNIT_DIRECT CM
                         LEFT JOIN DM_CONF_CONTRACT_RISKDEF RD
                           ON CM.ENTITY_ID = RD.ENTITY_ID
                          AND CM.PRODUCT_CODE = RD.PRODUCT_CODE
                          AND RD.VALID_IS = '1'
                          AND RD.AUDIT_STATE = '1'
                          AND RD.BUSINESS_MODEL||RD.BUSINESS_DIRECTION = v_buss_model
                          AND RD.AUDIT_STATE = '1'
                        WHERE CM.ENTITY_ID = p_entity_id
                          AND CM.BUSS_YEAR_MONTH <= p_year_month
                          AND CM.YEAR_MONTH IS NULL) B
                ON (A.CM_UNIT_ID = B.CM_UNIT_ID)
                WHEN MATCHED THEN
                    UPDATE
                       SET PROC_ID = B.PROC_ID,
                       NODE_STATE = B.NODE_STATE,
                       MAJOR_RISK = B.MAJOR_RISK,
                       EVALUATE_APPROACH = null,
                       PORTFOLIO_NO = null,
                       PL_JUDGE_RSLT = null,
                       PL_JUDGE_DATE = null,
                       BORDER_DATE = null,
                       ICG_NO = null,
                       INVEST_RATE = null,
                       INVEST_AMOUNT = null,
                       REASON_OF_FAILURE = B.REASON_OF_FAILURE,
                       REASON_OF_MR_FAILURE = B.REASON_OF_MR_FAILURE,
                        EXCEPTION_MESSAGE = B.EXCEPTION_MESSAGE;

        COMMIT;

      ELSIF v_test_plan_type = 'R' THEN

        --规则
          MERGE INTO DM_BUSS_CMUNIT_DIRECT A
          USING (SELECT CM_UNIT_ID,
                        V_PROC_ID AS PROC_ID,
                        (CASE
                            WHEN (CMRD.MAJOR_RISK = 'Y' OR CMRD.MAJOR_RISK = 'N') THEN
                             '1'
                            ELSE
                             '2'
                        END) AS NODE_STATE,
                        (CASE
                            WHEN CMRD.MAJOR_RISK LIKE 'E:%' THEN
                             NULL
                            ELSE
                             CMRD.MAJOR_RISK
                        END) AS MAJOR_RISK,
                        (CASE
                            WHEN CMRD.MAJOR_RISK LIKE 'E:%' THEN
                             'DM_001'
                            WHEN CMRD.MAJOR_RISK LIKE 'R:%' THEN
                             'DM_003'
                            ELSE
                             NULL
                        END) AS REASON_OF_FAILURE,
                        (CASE
                            WHEN CMRD.MAJOR_RISK LIKE 'E:%' THEN
                             'DM_001'
                            WHEN CMRD.MAJOR_RISK LIKE 'R:%' THEN
                             'DM_003'
                            ELSE
                             NULL
                        END) AS REASON_OF_MR_FAILURE,
                        CMRD.MAJOR_RISK AS EXCEPTION_MESSAGE
                   FROM (SELECT CM_UNIT_ID,
                                COALESCE(DM_PACK_CMUNIT_COMMON.FUNC_FACTOR_VALUE_CAL(CM_UNIT_ID,ENTITY_ID,product_CODE,NULL,'1',v_buss_model),'R:') AS MAJOR_RISK
                           FROM DM_BUSS_CMUNIT_DIRECT
                          WHERE ENTITY_ID = P_ENTITY_ID
                            AND BUSS_YEAR_MONTH <= P_YEAR_MONTH
                            AND YEAR_MONTH IS NULL) CMRD
                 ) B
          ON (A.CM_UNIT_ID = B.CM_UNIT_ID)
              WHEN MATCHED THEN
                              UPDATE
                                 SET PROC_ID = B.PROC_ID,
                                 NODE_STATE = B.NODE_STATE,
                                 MAJOR_RISK = B.MAJOR_RISK,
                                 EVALUATE_APPROACH = null,
                                 PORTFOLIO_NO = null,
                                 PL_JUDGE_RSLT = null,
                                 PL_JUDGE_DATE = null,
                                 BORDER_DATE = null,
                                 ICG_NO = null,
                                 INVEST_RATE = null,
                                 INVEST_AMOUNT = null,
                                 REASON_OF_FAILURE = B.REASON_OF_FAILURE,
                                 REASON_OF_MR_FAILURE = B.REASON_OF_MR_FAILURE,
                                 EXCEPTION_MESSAGE = B.EXCEPTION_MESSAGE;
        commit;
        -- 1，更新未配置规则重测标识为 不通过
        SELECT count(1)
          INTO v_count
          FROM dm_conf_contract_rule_def
         WHERE valid_is = '1'
           AND audit_state = '1'
           AND contract_rule_type = '1'
           AND entity_id = p_entity_id
           AND business_model||business_direction = v_buss_model;

        IF v_count = 0 THEN
          --未配置规则，处理异常
          UPDATE dm_buss_cmunit_direct
             SET node_state           = '2', -- 节点状态
                 proc_id              = v_proc_id,
                 evaluate_approach    = NULL, --评估方法
                 portfolio_no         = NULL,
                 pl_judge_rslt        = NULL, --盈亏
                 pl_judge_date        = NULL, --盈亏时间
                 year_month           = NULL,
                 border_date          = NULL,
                 icg_no               = NULL,
                 invest_rate          = NULL,
                 invest_amount        = NULL,
                 major_risk           = NULL,
                 reason_of_failure    = 'DM_001',
                 reason_of_mr_failure = 'DM_001',
                 exception_message    = NULL
           WHERE entity_id = p_entity_id
             AND buss_year_month <= p_year_month
             AND year_month IS NULL;

          COMMIT;

        ELSE
          -- 2，根据规则更新重测标识
          --规则
          MERGE INTO DM_BUSS_CMUNIT_DIRECT A
          USING (SELECT CM_UNIT_ID,
                        V_PROC_ID AS PROC_ID,
                        (CASE
                            WHEN (CMRD.MAJOR_RISK = 'Y' OR CMRD.MAJOR_RISK = 'N') THEN
                             '1'
                            ELSE
                             '2'
                        END) AS NODE_STATE,
                        (CASE
                            WHEN CMRD.MAJOR_RISK LIKE 'E:%' THEN
                             NULL
                            ELSE
                             CMRD.MAJOR_RISK
                        END) AS MAJOR_RISK,
                        (CASE
                            WHEN CMRD.MAJOR_RISK LIKE 'E:%' THEN
                             'DM_001'
                            WHEN CMRD.MAJOR_RISK LIKE 'R:%' THEN
                             'DM_003'
                            ELSE
                             NULL
                        END) AS REASON_OF_FAILURE,
                        (CASE
                            WHEN CMRD.MAJOR_RISK LIKE 'E:%' THEN
                             'DM_001'
                            WHEN CMRD.MAJOR_RISK LIKE 'R:%' THEN
                             'DM_003'
                            ELSE
                             NULL
                        END) AS REASON_OF_MR_FAILURE,
                        CMRD.MAJOR_RISK AS EXCEPTION_MESSAGE
                   FROM (SELECT CM_UNIT_ID,
                                COALESCE(DM_PACK_CMUNIT_COMMON.FUNC_FACTOR_VALUE_CAL(CM_UNIT_ID,ENTITY_ID,product_CODE,NULL,'1',v_buss_model),'R:') AS MAJOR_RISK
                           FROM DM_BUSS_CMUNIT_DIRECT
                          WHERE ENTITY_ID = P_ENTITY_ID
                            AND BUSS_YEAR_MONTH <= P_YEAR_MONTH
                            AND YEAR_MONTH IS NULL) CMRD
                 ) B
          ON (A.CM_UNIT_ID = B.CM_UNIT_ID)
              WHEN MATCHED THEN
                              UPDATE
                                 SET PROC_ID = B.PROC_ID,
                                 NODE_STATE = B.NODE_STATE,
                                 MAJOR_RISK = B.MAJOR_RISK,
                                 EVALUATE_APPROACH = null,
                                 PORTFOLIO_NO = null,
                                 PL_JUDGE_RSLT = null,
                                 PL_JUDGE_DATE = null,
                                 BORDER_DATE = null,
                                 ICG_NO = null,
                                 INVEST_RATE = null,
                                 INVEST_AMOUNT = null,
                                 REASON_OF_FAILURE = B.REASON_OF_FAILURE,
                                 REASON_OF_MR_FAILURE = B.REASON_OF_MR_FAILURE,
                                 EXCEPTION_MESSAGE = B.EXCEPTION_MESSAGE;
          COMMIT;

        END IF;

      END IF;

    ELSE
      --规则不存在

      UPDATE dm_buss_cmunit_direct
         SET node_state           = '2', -- 节点状态
             proc_id              = v_proc_id,
             evaluate_approach    = NULL, --评估方法
             portfolio_no         = NULL,
             pl_judge_rslt        = NULL, --盈亏
             pl_judge_date        = NULL, --盈亏时间
             year_month           = NULL,
             border_date          = NULL,
             icg_no               = NULL,
             invest_rate          = NULL,
             invest_amount        = NULL,
             reason_of_failure    = 'DM_001',
             reason_of_mr_failure = 'DM_001'
       WHERE entity_id = p_entity_id
         AND buss_year_month <= p_year_month
         AND year_month IS NULL;

        COMMIT;

    END IF;

   --如果有不通过的，设置为业务处理通过
     MERGE INTO DM_BUSS_CMUNIT_DIRECT A
          USING (SELECT CM_UNIT_ID
                 from DM_BUSS_CMUNIT_DIRECT cm
                  WHERE ENTITY_ID = P_ENTITY_ID
                            AND BUSS_YEAR_MONTH <= P_YEAR_MONTH
                            AND YEAR_MONTH IS NULL
                            and MAJOR_RISK = 'N') B
          ON (A.CM_UNIT_ID = B.CM_UNIT_ID)
              WHEN MATCHED THEN
                              UPDATE
                                 SET MAJOR_RISK = 'O';
    commit;



    proc_majorrisk_test_nopass(p_entity_id, p_year_month);

  EXCEPTION
    WHEN OTHERS THEN
      --意外处理
      -- 执行异常，记录异常原因
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]重测处理:'||SQLERRM;
      UPDATE dm_buss_cmunit_direct t
         SET node_state           = '2', -- 节点状态
             proc_id              = v_proc_id,
             reason_of_failure    = 'DM_003',
             reason_of_mr_failure = 'DM_003',
             exception_message    = v_error_msg --异常信息
       WHERE entity_id = p_entity_id
         AND (major_risk IS NULL OR (node_state = '2' AND proc_id = v_proc_id))
         AND year_month IS NULL
         AND buss_year_month <= p_year_month;

      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_majorrisk_test;

  PROCEDURE proc_majorrisk_test_nopass(p_entity_id  NUMBER,
                                       p_year_month VARCHAR2) IS

    v_icg_column         VARCHAR2(4000);
    v_portfolio_column    VARCHAR2(4000);
    v_icg_proc_id        NUMBER;
    v_portflio_proc_id   NUMBER;
    v_count              NUMBER;
    v_error_msg          VARCHAR2(2000);
  BEGIN
    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]重测处理不通过-传参不为空：proc_majorrisk_test_nopass(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]重测处理不通过-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 是否存在重测业务不通过及未生成合同组数据
    SELECT COUNT(1)
      INTO v_count
      FROM dm_buss_cmunit_direct
     WHERE entity_id = p_entity_id
       AND buss_year_month <= p_year_month
       AND year_month IS NULL
       AND major_risk = 'O' -- 重测业务不通过
       AND icg_no IS NULL -- 未生成合同组
       ;

    IF v_count = 0 THEN
       return;
    END IF;

    -- 重测业务不通过，生成合同组合
    v_portfolio_column := dm_pack_cmunit_common.func_get_contractcode(p_entity_id, 'G', 'DD');

    -- 根据PROC_CODE查询PROC_ID
    v_portflio_proc_id := dm_pack_common.func_get_procid('DM_PORTFOLIO');

    IF v_portfolio_column IS NOT NULL THEN
      v_icg_column := dm_pack_cmunit_common.func_get_contractcode(p_entity_id, 'C', 'DD');
      IF v_icg_column IS NOT NULL THEN
         v_icg_proc_id := dm_pack_common.func_get_procid('DM_CONTRACTGROUP');
      END IF;

    END IF;

    -- 1,评估方法及盈亏判定设置为 D-不区分
    -- 2,生成合同组合编码
    -- 3,更新合同组编码
    -- 4,《确认日期》为确认不通过的业务期间月末
    EXECUTE IMMEDIATE ('update dm_buss_cmunit_direct t '
                       ||' set evaluate_approach = ''D'''
                       ||', pl_judge_rslt = ''D'''
                       ||', portfolio_no = ' || v_portfolio_column
                       ||', icg_no = ' || v_icg_column
                       ||', border_date = ' || (CASE WHEN v_icg_column IS NOT NULL THEN 'last_day(to_date('''||p_year_month||'01'',''YYYYMMDD''))' END)
                       ||', proc_id= ' || (CASE WHEN v_portfolio_column IS NULL THEN v_portflio_proc_id ELSE v_icg_proc_id END)
                       ||', node_state= ''' || (CASE WHEN v_portflio_proc_id IS NULL OR v_icg_proc_id IS NULL THEN '2' ELSE '1' END)||''''
                       ||', reason_of_failure = ''' || (CASE WHEN v_portfolio_column IS NULL OR v_icg_column IS NULL THEN 'DM_003' ELSE NULL END) ||''''
                       ||', reason_of_mr_failure = ''' || (CASE WHEN v_portfolio_column IS NULL OR v_icg_column IS NULL THEN 'DM_003' ELSE NULL END) ||''''
                    ||' where entity_id = ' || p_entity_id
                    ||' and major_risk = ''O'' '
                    --||' and evaluate_approach is not null '
                    --||' and pl_judge_rslt is not null '
                    --||' and  portfolio_no is null '
                    ||' and year_month is null '
                    ||' and buss_year_month <= ''' || p_year_month || '''');


    COMMIT;

    --投成拆分
    -- 根据PROC_CODE查询PROC_ID
    /*  V_PROC_ID := dm_pack_common.func_get_procid('DM_INVESTMENT_COST');

    IF V_PROC_ID IS NOT NULL THEN

      --投成拆分
     UPDATE dm_buss_cmunit_direct CM
       SET (INVEST_RATE,
           INVEST_AMOUNT,
           PROC_ID,
           NODE_STATE,
           REASON_OF_FAILURE)
      =( SELECT
           NVL(TO_NUMBER(BCQ.QUOTA_VALUE, '999.999999'), 0) AS INVEST_RATE,
           --NVL(PREMIUM,0)*
           NVL(TO_NUMBER(BCQ.QUOTA_VALUE,'999.999999'),0) AS INVEST_RATE,
           1 AS PROC_ID,
           '1' AS NODE_STATE,
           NULL AS REASON_OF_FAILURE
      FROM BBS_CONF_QUOTA BCQ
         WHERE BCQ.QUOTA_DEF_ID = (SELECT QUOTA_DEF_ID
                  FROM BBS_CONF_QUOTA_DEF
                 WHERE QUOTA_CODE = 'ReportingPeriodRatio') --取投成拆分字段
           AND BCQ.BUSINESS_MODEL = 'D' --取直保的
           AND CM.entity_id = BCQ.entity_id
           AND BCQ.VALID_IS = '1'
           AND BCQ.audit_state = '1'
           AND ((CM.risk_class_code IS NOT NULL AND CM.risk_class_code = BCQ.risk_class_code)
           OR   (BCQ.risk_class_code = (SELECT
                            R.risk_class_code AS risk_class_code
                            FROM BPLUSER.BBS_CONF_RISK R
                             WHERE R.RISK_CODE = CM.RISK_CODE
                               AND CM.entity_id = R.entity_id
                               AND R.VALID_IS = '1'
                               AND R.audit_state = '1' AND ROWNUM = 1)))
       )
       WHERE CM.EVALUATE_APPROACH IS NOT NULL
       AND CM.PL_JUDGE_RSLT IS NOT NULL
       AND CM.BORDER_DATE IS NOT NULL
       AND CM.ICG_NO IS NOT NULL
       AND CM.INVEST_RATE IS NULL
       AND CM.INVEST_AMOUNT IS NULL
       AND CM.entity_id = p_entity_id
       AND CM.MAJOR_RISK = 'O'
       AND CM.YEAR_MONTH IS NULL
       AND CM.BUSS_YEAR_MONTH <= P_YEAR_MONTH;


      --未配置投成拆分规则
        UPDATE dm_buss_cmunit_direct CM
        SET PROC_ID = V_PROC_ID,NODE_STATE= '2',REASON_OF_FAILURE = 'DM_001'
        WHERE CM.MAJOR_RISK = 'O'
          AND CM.EVALUATE_APPROACH IS NOT NULL
          AND CM.PL_JUDGE_RSLT IS NOT NULL
          AND CM.BORDER_DATE IS NOT NULL
          AND CM.ICG_NO IS NOT NULL
          AND CM.INVEST_RATE IS NULL
          AND CM.INVEST_AMOUNT IS NULL
          AND CM.YEAR_MONTH IS NULL
          AND CM.entity_id = p_entity_id
          AND CM.BUSS_YEAR_MONTH <= P_YEAR_MONTH;

    END IF;  */

  EXCEPTION
    WHEN OTHERS THEN
      -- 执行异常，记录异常原因
      --意外处理
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]重测处理:'||SQLERRM;
      /*
      UPDATE dm_buss_cmunit_direct t
         SET node_state           = '2', -- 节点状态
             proc_id              = v_proc_id,
             reason_of_failure    = 'DM_003',
             reason_of_mr_failure = 'DM_003',
             exception_message    = v_error_msg --异常信息
       WHERE entity_id = p_entity_id
         AND (major_risk IS NULL OR (node_state = '2' AND proc_id = v_proc_id))
         AND year_month IS NULL
         AND buss_year_month <= p_year_month;
         */

      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_majorrisk_test_nopass;

  PROCEDURE proc_approach_discern(p_entity_id  NUMBER,
                                  p_year_month VARCHAR2) IS
    v_proc_id     NUMBER;
    v_error_msg   VARCHAR2(2000);
  BEGIN
    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]评估方法适配-传参不为空：proc_approach_discern(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]评估方法适配-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 根据PROC_CODE查询PROC_ID
    --SELECT dm_pack_common.func_get_procid('DM_EVALDEF_CONFIG') INTO v_proc_id FROM dual;
    v_proc_id := dm_pack_common.func_get_procid('DM_EVALDEF_CONFIG');

    MERGE INTO dm_buss_cmunit_direct t
    USING (SELECT t1.cm_unit_id,
                  ce.evaluate_approach evaluate_approach
             FROM dm_buss_cmunit_direct t1
             LEFT JOIN bpluser.bbs_conf_loa ce
               ON ce.entity_id = t1.entity_id
              AND ce.loa_code = t1.loa_code
              AND ce.audit_state = '1'
              AND ce.valid_is = '1'
              AND ce.business_model = 'D'
              AND ce.business_direction = 'D'
            WHERE t1.major_risk IN ('Y', 'P')
              AND t1.year_month IS NULL
              AND t1.entity_id = p_entity_id
              AND t1.buss_year_month <= p_year_month
           ) c
    ON (t.cm_unit_id = c.cm_unit_id)
    WHEN MATCHED THEN
      UPDATE
         SET t.portfolio_no      = NULL,
             t.pl_judge_rslt     = NULL, --盈亏
             t.pl_judge_date     = NULL, --盈亏时间
             t.year_month        = NULL,
             t.border_date       = NULL,
             t.icg_no            = NULL,
             t.invest_rate       = NULL,
             t.invest_amount     = NULL,
             t.proc_id           = v_proc_id,
             t.evaluate_approach = c.evaluate_approach,
             t.node_state        = (case when c.evaluate_approach is not null then '1' else '2' end),--未配置loa_code提示异常
             t.reason_of_failure = (case when c.evaluate_approach is not null then null else 'DM_001' end)
       WHERE t.major_risk IN ('Y', 'P')
         AND t.year_month IS NULL
         AND t.entity_id = p_entity_id
         AND t.buss_year_month <= p_year_month
      ;

   COMMIT;

  EXCEPTION
    WHEN OTHERS THEN

      -- 执行异常，记录异常原因
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]评方法适配:'||SQLERRM;

      /*
      UPDATE dm_buss_cmunit_direct t
         SET node_state           = '2', -- 节点状态
             proc_id              = v_proc_id,
             reason_of_failure    = 'DM_loa',
             reason_of_mr_failure = 'DM_loa',
             exception_message    = v_error_msg --异常信息
       WHERE entity_id = p_entity_id
         AND (major_risk IS NULL OR (node_state = '2' AND proc_id = v_proc_id))
         AND year_month IS NULL
         AND buss_year_month <= p_year_month;
      */

      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);


  END proc_approach_discern;

  PROCEDURE proc_portfolio_discern(p_entity_id  NUMBER,
                                   p_year_month VARCHAR2) IS

    v_column      VARCHAR2(4000);
    v_proc_id     NUMBER;
    v_count       NUMBER;
    v_error_msg   VARCHAR2(2000);
  BEGIN
    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]合同组合划分-传参不为空：proc_portfolio_discern(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

    --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]合同组合划分-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 当前业务年月存在未进行评估方法的数据
    SELECT count(1)
      INTO v_count
      FROM dm_buss_cmunit_direct
     WHERE entity_id = p_entity_id
       AND buss_year_month <= p_year_month
       --AND major_risk IN ('Y', 'P')
       AND evaluate_approach IS NULL
       AND year_month IS NULL
       AND rownum = 1;

    IF v_count > 0 THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]合同组合划分-当前业务年月存在未进行评估方法的数据';
      raise_application_error(-20002,v_error_msg);

    END IF;

    v_column := dm_pack_cmunit_common.func_get_contractcode(p_entity_id, 'G', 'DD');

    -- 根据PROC_CODE查询PROC_ID(合同组合)
    v_proc_id := dm_pack_common.func_get_procid('DM_PORTFOLIO');

    -- 更新合同组合编码
    --dbms_output.put_line('v_column:' || v_column);
    EXECUTE IMMEDIATE ('update  dm_buss_cmunit_direct t'
                       ||' set portfolio_no = ' || v_column
                       ||', proc_id=' || v_proc_id
                       ||', pl_judge_rslt = null'
                       ||', pl_judge_date = null'
                       ||', year_month = null'
                       ||', border_date = null'
                       ||', icg_no = null'
                       ||', invest_rate = null'
                       ||', invest_amount = null'
                       ||', node_state= '''|| (CASE WHEN v_column IS NULL THEN '2' ELSE '1' END) ||''''
                       ||', reason_of_failure = ''' || (CASE WHEN v_column IS NULL THEN 'DM_003' ELSE NULL END) ||''''
                       ||', reason_of_mr_failure = ''' || (CASE WHEN v_column IS NULL THEN 'DM_003' ELSE NULL END) ||''''
                       ||' where entity_id = ' || p_entity_id
                       ||' and evaluate_approach <> ''D'' '
                       --||' and major_risk in (''Y'', ''P'') '
                       --||' and EVALUATE_APPROACH is not null '
                      -- ||' and pl_judge_rslt is not null '
                       ||' and YEAR_MONTH IS NULL '
                       ||' and buss_year_month <= ''' || p_year_month || ''' ');

    COMMIT;
  EXCEPTION
    WHEN OTHERS THEN
      -- 执行异常，记录异常原因
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]合同组合划分:'||SQLERRM;
      /*
      UPDATE dm_buss_cmunit_direct t
         SET node_state           = '2', -- 节点状态
             proc_id              = v_proc_id,
             reason_of_failure    = 'DM_003',
             reason_of_mr_failure = 'DM_003',
             exception_message    = v_error_msg --异常信息
       WHERE entity_id = p_entity_id
         AND (major_risk IS NULL OR (node_state = '2' AND proc_id = v_proc_id))
         AND year_month IS NULL
         AND buss_year_month <= p_year_month;
      */
      --意外处理
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);


  END proc_portfolio_discern;

  PROCEDURE proc_profit_loss_discern(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2) IS
    v_proc_id        NUMBER;
    v_data_key       VARCHAR2(5000);
    v_count          NUMBER;
    v_test_plan_type VARCHAR2(10);
    v_error_msg      varchar2(2000);
  BEGIN

    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]盈亏判定-传参不为空：proc_profit_loss_discern(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

       --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]盈亏判定-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    --如果查询当月没有数据做，直接返回
    select count(1) into v_count
     from dm_buss_cmunit_direct where entity_id = p_entity_id and buss_year_month <= p_year_month
     and year_month is null ;

     if v_count <= 0 then
        return;
     end if;


    -- 根据PROC_CODE查询PROC_ID
    v_proc_id := dm_pack_common.func_get_procid('DM_DETERMINATION');

    v_data_key := p_entity_id || p_year_month;
    --dbms_output.put_line('data_key: '||v_data_key);

    --清空 流程节点 盈亏判定、合同组、合同确认日期、投成拆分
    UPDATE dm_buss_cmunit_direct
       SET proc_id           = v_proc_id,
           node_state        = '0',
           reason_of_failure = NULL,
           pl_judge_rslt     = NULL,
           pl_judge_date     = NULL,
           year_month        = NULL,
           border_date       = NULL,
           icg_no            = NULL,
           invest_rate       = NULL,
           invest_amount     = NULL
     WHERE entity_id = p_entity_id
       AND portfolio_no IS NOT NULL -- 已生成合同组合编码
       AND year_month IS NULL --未确认的计量单元
       AND evaluate_approach <> 'D'
       --AND major_risk IN ('Y','P')--通过/业务处理通过
          --AND ICG_NO IS NULL  -- 未生成合同组
          --  AND PL_JUDGE_RSLT IS NULL     -- 盈亏判定标识为空
       AND buss_year_month <= p_year_month;
    COMMIT;


 --按盈亏判定方案配置区分: C-配置 R-规则
    SELECT count(1)
      INTO v_count
      FROM dm_conf_icg_profitloss_plan
     WHERE entity_id = p_entity_id
       AND business_model = 'D';

    if v_count > 0 then
     --按盈亏判定方案配置区分: C-配置 R-规则
    SELECT test_plan_type
      INTO v_test_plan_type
      FROM dm_conf_icg_profitloss_plan
     WHERE entity_id = p_entity_id
       AND business_model = 'D';
    end if;

    if v_test_plan_type is null then
      v_test_plan_type := 'C';
    end if;


    IF v_test_plan_type = 'C' THEN

      MERGE INTO dm_buss_cmunit_direct t
      USING (SELECT dcl.judge_result as judge_result,
                    t1.cm_unit_id
               FROM dm_buss_cmunit_direct t1
               LEFT JOIN dm_conf_contract_lossdef dcl
                 ON dcl.product_code = t1.product_code
                AND dcl.entity_id = t1.entity_id
                AND dcl.valid_is = '1'
                AND dcl.audit_state = '1'
                AND dcl.business_model = 'D'
                AND dcl.business_direction = 'D'
              WHERE t1.entity_id = p_entity_id
                AND t1.portfolio_no IS NOT NULL
                AND t1.evaluate_approach <> 'D'
                AND t1.year_month IS NULL
                AND t1.buss_year_month <= p_year_month) c
      ON (t.cm_unit_id = c.cm_unit_id)
      WHEN MATCHED THEN
        UPDATE
           SET t.pl_judge_rslt     = c.judge_result,
               t.pl_judge_date    =
               (CASE
                 WHEN c.judge_result = 'L' THEN
                  last_day(to_date(p_year_month, 'yyyymm'))
                 ELSE
                  NULL
               END), -- 判定结果为亏损，设置判定时间
               t.node_state        = (case when c.judge_result is not null then '1' else '2' end),
               t.reason_of_failure = (case when c.judge_result is not null then null else 'DM_001' end),
               t.exception_message = (case when c.judge_result is not null then null else 'not find config' end)
         WHERE t.entity_id = p_entity_id
           AND t.portfolio_no IS NOT NULL
           AND t.evaluate_approach <> 'D'
           AND t.year_month IS NULL
           AND t.buss_year_month <= p_year_month;
      COMMIT;

    ELSE

      --  初始化盈亏判断计量单元
      DELETE FROM dm_duct_direct_profit_unit t
       WHERE EXISTS (SELECT 1
                FROM dm_buss_cmunit_direct c
               WHERE
                 --c.cm_unit_id = t.cmunit_id 应该整月都清掉重新做
                 --AND
                 c.entity_id = p_entity_id
                 AND c.buss_year_month <= p_year_month
                 AND c.portfolio_no IS NOT NULL -- 已生成合同组合编码
                 AND c.year_month IS NULL --未确认的评估期
                 AND c.evaluate_approach <> 'D');
      COMMIT;

      DELETE FROM dm_duct_direct_profit_param t WHERE t.data_key = v_data_key;
      COMMIT;

      INSERT INTO dm_duct_direct_profit_unit
        (cmunit_id,
         entity_id,
         year_month,
         data_key,
         set_no,
         portfolio_no,
         loa_code,
         policy_no,
         endorse_seq_no,
         risk_class_code,
         risk_code,
         evaluate,
         issue_date,
         effective_date,
         expiry_date,
         premium,
         fee_amount,
         node_state)
        SELECT cm_unit_id,
               entity_id,
               p_year_month year_month,
               v_data_key data_key,
               portfolio_no AS set_no,
               portfolio_no,
               loa_code,
               policy_no,
               endorse_seq_no,
               risk_class_code,
               risk_code,
               evaluate,
               issue_date,
               effective_date,
               expiry_date,
               premium,
               fee_amount,
               node_state
          FROM (SELECT c.entity_id,
                       c.cm_unit_id,
                       c.portfolio_no,
                       MAX(c.loa_code) loa_code,
                       MAX(m.policy_no) policy_no,
                       MAX(m.endorse_seq_no) endorse_seq_no,
                       MAX(p.risk_class_code) risk_class_code,
                       MAX(p.risk_code) risk_code,
                       MAX(evaluate_approach) evaluate,
                       MIN(m.issue_date) issue_date,
                       MIN(m.effective_date) effective_date,
                       MAX(m.expiry_date) expiry_date,
                       coalesce(SUM(p.premium), 0.0) premium,
                       coalesce(SUM(p.fee_amount), 0.0) + coalesce(SUM(p.commission_amount), 0.0) fee_amount,
                       '1' node_state
                  FROM dm_buss_cmunit_direct c
                  LEFT JOIN dm_policy_main m
                    ON m.policy_no = c.policy_no
                   AND m.entity_id = c.entity_id
                   AND (m.endorse_effective_date IS NULL OR to_char(m.endorse_effective_date, 'YYYYMM') <= p_year_month) --只取评估时点前生效的批改
                  LEFT JOIN dm_policy_premium p
                    ON m.policy_no = p.policy_no
                   AND m.endorse_seq_no = p.endorse_seq_no
                   AND m.entity_id = p.entity_id
                   AND p.product_code = c.product_code
                 WHERE c.entity_id = p_entity_id
                   AND c.portfolio_no IS NOT NULL -- 已生成合同组合编码
                   AND c.year_month IS NULL --未确认的评估期
                   --AND c.major_risk IN ('Y','P')
                   AND c.evaluate_approach <> 'D'
                      --AND ICG_NO IS NULL  -- 未生成合同组
                      --  AND PL_JUDGE_RSLT IS NULL     -- 盈亏判定标识为空
                   AND c.buss_year_month <= p_year_month
                 GROUP BY c.entity_id,
                          c.cm_unit_id,
                          c.icg_no,
                          c.portfolio_no) t;
      COMMIT;

      SELECT COUNT(1)
        INTO v_count
        FROM dm_duct_direct_profit_unit
       WHERE data_key = v_data_key
         AND rownum = 1;

      -- 满足条件的数据为空, 结束判定
      IF v_count = 0 THEN

         return;

      END IF;

      -- 校验数据:数据完整性 及 配置表数据是否缺失
      -- NODE_STATE - 1 校验通过；  NODE_STATE -2 校验不通过；
      dm_pack_cmunit_common.proc_direct_profit_check(p_entity_id, p_year_month, v_data_key);

      SELECT COUNT(1)
        INTO v_count
        FROM dm_duct_direct_profit_unit
       WHERE data_key = v_data_key
         AND node_state = '2'
         AND rownum = 1;

      -- 数据不通过校验，更新数据状态，结束处理
      IF v_count > 0 THEN
        -- 更新判定结果及流程状态
        MERGE INTO dm_buss_cmunit_direct t
        USING (SELECT dpu.cmunit_id,
                     nvl(dpu.node_state, '2') AS node_state,
                     nvl(dpu.fail_code, 'DM_001') AS fail_code
                FROM dm_duct_direct_profit_unit dpu
               WHERE dpu.data_key = v_data_key
                 AND dpu.node_state = '2'
      ) c
        ON (t.cm_unit_id = c.cmunit_id)
        WHEN MATCHED THEN
          UPDATE
             SET t.node_state        = c.node_state,
                 t.reason_of_failure = fail_code
           WHERE t.entity_id = p_entity_id
             AND t.portfolio_no IS NOT NULL
             AND t.evaluate_approach <> 'D'
             AND t.year_month IS NULL
             AND t.buss_year_month <= p_year_month
             AND EXISTS (SELECT 1 FROM dm_duct_direct_profit_unit dpu
                        WHERE dpu.cmunit_id = t.cm_unit_id
                          AND dpu.data_key = v_data_key
                          AND dpu.node_state = '2');
        COMMIT;

        --抛出已自定义异常信息【会中断事务】
        v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]盈亏判定-盈亏数据不通过校验：';
        raise_application_error(-20002,v_error_msg);

      END IF;

      -- 校验通过,处理数据

      -- 假设参数,折现因子
      dm_pack_cmunit_common.proc_direct_profit_param(p_entity_id, p_year_month, v_data_key, 'QUOTA');

      -- 计量单元 - 现金流
      dm_pack_cmunit_common.proc_direct_profit_param(p_entity_id, p_year_month, v_data_key, 'UNIT');

      -- 集合现金流
      dm_pack_cmunit_common.proc_direct_profit_param(p_entity_id, p_year_month, v_data_key, 'SET');

      -- PAA
      dm_pack_cmunit_common.proc_direct_judge_paa(p_entity_id, p_year_month, v_data_key);

      -- BBA
      dm_pack_cmunit_common.proc_direct_judge_bba(p_entity_id, p_year_month, v_data_key);

      -- 更新判定结果及流程状态
      MERGE INTO dm_buss_cmunit_direct t
      USING (SELECT dpu.cmunit_id,
                   nvl(dpu.node_state, '2') AS node_state,
                   judge_rslt,
                   judge_date
              FROM dm_duct_direct_profit_unit dpu
             WHERE dpu.data_key = v_data_key) c
      ON (t.cm_unit_id = c.cmunit_id)
      WHEN MATCHED THEN
        UPDATE
           SET t.pl_judge_rslt     = c.judge_rslt,
               t.pl_judge_date     = c.judge_date,
               t.node_state        = c.node_state,
               t.reason_of_failure = (case when c.node_state = '2' then 'DM_001' else null end)
         WHERE t.entity_id = p_entity_id
           AND t.portfolio_no IS NOT NULL
           AND t.evaluate_approach <> 'D'
           AND t.year_month IS NULL
           AND t.buss_year_month <= p_year_month;
      COMMIT;

    END IF;
    proc_buss_cmunit_log(p_entity_id, p_year_month, v_data_key || '-' || v_test_plan_type, 'proc_profit_loss_discern', '1', NULL);

  EXCEPTION
    WHEN OTHERS THEN
      --dbms_output.put_line(SQLERRM);
      proc_buss_cmunit_log(p_entity_id, p_year_month, v_data_key || '-' || v_test_plan_type, 'proc_profit_loss_discern', '2', SQLERRM);
      --意外处理
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]盈亏判定:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_profit_loss_discern;

  PROCEDURE proc_icg_fixed(p_entity_id  NUMBER,
                           p_year_month VARCHAR2) IS
    --v_proc_id    NUMBER; --当前节点
    v_end_date   TIMESTAMP; -- 当前业务年月的下个月第一天
    v_error_msg  varchar2(2000);
  BEGIN

    /*-- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      RETURN;
    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      RETURN;
    END IF;*/

    --v_proc_id := dm_pack_common.func_get_procid('DM_CONTRACTGROUP');

    v_end_date := add_months(to_date(p_year_month, 'yyyymm'), 1);

    --更新D1
      MERGE INTO dm_buss_cmunit_direct cma
    USING (SELECT c.entity_id,
                                       c.cm_unit_id,
                                       c.policy_no,
                                       MIN(pol.endorse_seq_no) AS endorse_seq_no,
                                       MIN(pol.approval_date) AS d1 --获取D1:核保通过日期
                                 FROM dmuser.dm_buss_cmunit_direct c
                                 LEFT JOIN dmuser.dm_policy_main pol
                                   ON c.entity_id = pol.entity_id
                                  AND c.policy_no = pol.policy_no
                                  AND ((pol.endorse_effective_date IS NOT NULL AND pol.endorse_effective_date < v_end_date) --v_end_date下个月第一天
                                      OR (pol.endorse_effective_date IS NULL AND pol.approval_date < v_end_date)) --v_end_date下个月第一天
                                 WHERE c.entity_id = p_entity_id
                                    AND c.pl_judge_rslt IS NOT NULL -- 盈亏判定
                                    AND c.evaluate_approach <> 'D'
                                    --AND c.pl_judge_rslt <> 'D'
                                    AND c.year_month IS NULL -- 未生成合同组
                                    AND c.buss_year_month <= p_year_month
                                GROUP BY c.entity_id,
                                         c.cm_unit_id,
                                         c.policy_no) cmb
    ON (cma.cm_unit_id = cmb.cm_unit_id)
    WHEN MATCHED THEN
      UPDATE
         SET endorse_seq_no = cmb.endorse_seq_no , d1 = cmb.d1;
    COMMIT;

    --更新D3
     MERGE INTO dm_buss_cmunit_direct cma
    USING (SELECT c.entity_id,
                                       c.cm_unit_id,
                                       c.policy_no,
                                      MIN(prem.effective_date) AS d3 --D3:保险责任起期
                                 FROM dmuser.dm_buss_cmunit_direct c
                                  LEFT JOIN dmuser.dm_policy_premium prem
                                     ON c.entity_id = prem.entity_id
                                    AND c.policy_no = prem.policy_no
                                    AND prem.endorse_seq_no = c.endorse_seq_no
                                 WHERE c.entity_id = p_entity_id
                                    AND c.pl_judge_rslt IS NOT NULL -- 盈亏判定
                                    AND c.evaluate_approach <> 'D'
                                    --AND c.pl_judge_rslt <> 'D'
                                    AND c.year_month IS NULL -- 未生成合同组
                                    AND c.buss_year_month <= p_year_month
                                GROUP BY c.entity_id,
                                         c.cm_unit_id,
                                         c.policy_no) cmb
    ON (cma.cm_unit_id = cmb.cm_unit_id)
    WHEN MATCHED THEN
      UPDATE
         SET d3 = cmb.d3;
       COMMIT;
    --更新D2
    MERGE INTO dm_buss_cmunit_direct cma
    USING (SELECT c.entity_id,
                                       c.cm_unit_id,
                                       c.policy_no,
                                     (CASE WHEN MIN(pln.est_payment_date) IS NULL THEN
                                         null
                                        ELSE
                                         MIN(pln.est_payment_date)
                                        END) d2  --D2:首次付款到期日=MIN（最早预计缴费日期，最早的实际支付日期）
                                 FROM dmuser.dm_buss_cmunit_direct c
                                    LEFT JOIN dmuser.dm_policy_payment_plan pln
                                     ON c.entity_id = pln.entity_id
                                    AND c.policy_no = pln.policy_no
                                    AND c.policy_no = pln.policy_no
                                    AND pln.endorse_seq_no = c.endorse_seq_no
                                    AND pln.est_payment_seq_no = 1
                                 WHERE c.entity_id = p_entity_id
                                    AND c.pl_judge_rslt IS NOT NULL -- 盈亏判定
                                    AND c.evaluate_approach <> 'D'
                                    --AND c.pl_judge_rslt <> 'D'
                                    AND c.year_month IS NULL -- 未生成合同组
                                    AND c.buss_year_month <= p_year_month
                                GROUP BY c.entity_id,
                                         c.cm_unit_id,
                                         c.policy_no) cmb
    ON (cma.cm_unit_id = cmb.cm_unit_id)
    WHEN MATCHED THEN
      UPDATE
         SET d2 = cmb.d2;
        COMMIT;
    --业务时间的更新
    MERGE INTO dm_buss_cmunit_direct cma
    USING (SELECT t.cm_unit_id,
                  --t.policy_no,
                  --t.endorse_seq_no,
                  t.d1, --获取D1:核保通过日期
                  t.d3, --D3:保险责任起期
                  t.d2, --D2:首次付款到期日=MIN（最早预计缴费日期，最早的实际支付日期）
                  greatest((CASE
                               WHEN d2 IS NULL THEN
                                d3
                               WHEN d3 IS NULL THEN  --d3 is not null
                                d2
                               WHEN d2 > d3 THEN
                                d3
                               ELSE
                                d2
                             END), d1) AS d4 --greatest(least(D2,D3),D1) D4
             FROM (SELECT MIN(c.cm_unit_id) AS cm_unit_id,
                          c.policy_no AS policy_no,
                          MIN(c.d1) AS d1, --获取D1:核保通过日期
                          MIN(c.d3) AS d3, --D3:保险责任起期
                          --MIN(pln.est_payment_date) AS est_payment_date,
                          --MIN(pay.actual_payment_date) AS actual_payment_date,
                          (CASE WHEN MIN(c.d2) IS NULL THEN
                             MIN(pay.actual_payment_date)
                            WHEN MIN(pay.actual_payment_date) IS NULL THEN
                             MIN(c.d2)
                            WHEN MIN(c.d2)  > MIN(pay.actual_payment_date) THEN
                             MIN(pay.actual_payment_date)
                            ELSE
                             MIN(c.d2)
                            END) d2  --D2:首次付款到期日=MIN（最早预计缴费日期，最早的实际支付日期）
                     FROM  dmuser.dm_buss_cmunit_direct c
                     LEFT JOIN dmuser.dm_acc_payment pay
                       ON c.entity_id = pay.entity_id
                      AND c.policy_no = pay.policy_no
                      AND c.policy_no = pay.policy_no
                      AND pay.endorse_seq_no = c.endorse_seq_no
                      AND pay.est_payment_seq_no = 1
                      --AND pay.actual_payment_date < v_end_date --v_end_date下个月第一天 --不需要看是否存在评估期内，有数据就可以 2023/03/08
                      AND pay.policy_no IS NOT NULL
                      WHERE c.entity_id = p_entity_id
                        AND c.pl_judge_rslt IS NOT NULL -- 盈亏判定
                        AND c.evaluate_approach <> 'D'
                        --AND c.pl_judge_rslt <> 'D'
                        AND c.year_month IS NULL -- 未生成合同组
                        AND c.buss_year_month <= p_year_month
                        GROUP BY c.entity_id,
                                 c.cm_unit_id,
                                 c.policy_no) t) cmb
    ON (cma.cm_unit_id = cmb.cm_unit_id)
    WHEN MATCHED THEN
      UPDATE
         SET d1 = cmb.d1,
             d2 = cmb.d2,
             d3 = cmb.d3,
             d4 = cmb.d4,
             border_date =(CASE WHEN cmb.d4 >= v_end_date THEN NULL  ELSE cmb.d4 END);--v_end_date下个月第一天
    COMMIT;
    /*--业务时间的更新
    MERGE INTO dm_buss_cmunit_direct cma
    USING (SELECT t.cm_unit_id,
                  --t.policy_no,
                  --t.endorse_seq_no,
                  t.d1, --获取D1:核保通过日期
                  t.d3, --D3:保险责任起期
                  t.d2, --D2:首次付款到期日=MIN（最早预计缴费日期，最早的实际支付日期）
                  greatest((CASE
                               WHEN d2 IS NULL THEN
                                d3
                               WHEN d3 IS NULL THEN  --d3 is not null
                                d2
                               WHEN d2 > d3 THEN
                                d3
                               ELSE
                                d2
                             END), d1) AS d4 --greatest(least(D2,D3),D1) D4
             FROM (SELECT MIN(c.cm_unit_id) AS cm_unit_id,
                          c.policy_no AS policy_no,
                          c.endorse_seq_no AS endorse_seq_no,
                          MIN(pm.approval_date) AS d1, --获取D1:核保通过日期
                          MIN(prem.effective_date) AS d3, --D3:保险责任起期
                          --MIN(pln.est_payment_date) AS est_payment_date,
                          --MIN(pay.actual_payment_date) AS actual_payment_date,
                          (CASE WHEN MIN(pln.est_payment_date) IS NULL THEN
                             MIN(pay.actual_payment_date)
                            WHEN MIN(pay.actual_payment_date) IS NULL THEN
                             MIN(pln.est_payment_date)
                            WHEN MIN(pln.est_payment_date) > MIN(pay.actual_payment_date) THEN
                             MIN(pay.actual_payment_date)
                            ELSE
                             MIN(pln.est_payment_date)
                            END) d2  --D2:首次付款到期日=MIN（最早预计缴费日期，最早的实际支付日期）
                     FROM (SELECT c.entity_id,
                                       c.cm_unit_id,
                                       c.policy_no,
                                       MIN(pol.endorse_seq_no) AS endorse_seq_no
                                 FROM dmuser.dm_buss_cmunit_direct c
                                 LEFT JOIN dmuser.dm_policy_main pol
                                   ON c.entity_id = pol.entity_id
                                  AND c.policy_no = pol.policy_no
                                  AND ((pol.endorse_effective_date IS NOT NULL AND pol.endorse_effective_date < v_end_date) --v_end_date下个月第一天
                                      OR (pol.endorse_effective_date IS NULL AND pol.approval_date < v_end_date)) --v_end_date下个月第一天
                                 WHERE c.entity_id = p_entity_id
                                    AND c.pl_judge_rslt IS NOT NULL -- 盈亏判定
                                    AND c.evaluate_approach <> 'D'
                                    --AND c.pl_judge_rslt <> 'D'
                                    AND c.year_month IS NULL -- 未生成合同组
                                    AND c.buss_year_month <= p_year_month
                                GROUP BY c.entity_id,
                                         c.cm_unit_id,
                                         c.policy_no) c
                     LEFT JOIN dmuser.dm_policy_main pm
                       ON pm.entity_id = c.entity_id
                      AND pm.policy_no = c.policy_no
                      AND pm.endorse_seq_no = c.endorse_seq_no
                     LEFT JOIN dmuser.dm_policy_premium prem
                       ON c.entity_id = prem.entity_id
                      AND c.policy_no = prem.policy_no
                      AND prem.endorse_seq_no = c.endorse_seq_no
                     LEFT JOIN dmuser.dm_policy_payment_plan pln
                       ON c.entity_id = pln.entity_id
                      AND c.policy_no = pln.policy_no
                      AND c.policy_no = pln.policy_no
                      AND pln.endorse_seq_no = c.endorse_seq_no
                      AND pln.est_payment_seq_no = 1
                      --AND pln.est_payment_date < v_end_date --v_end_date下个月第一天 --不需要看是否存在评估期内，有数据就可以 2023/03/08
                      AND pln.policy_no IS NOT NULL
                     LEFT JOIN dmuser.dm_acc_payment pay
                       ON c.entity_id = pay.entity_id
                      AND c.policy_no = pay.policy_no
                      AND c.policy_no = pay.policy_no
                      AND pay.endorse_seq_no = c.endorse_seq_no
                      AND pay.est_payment_seq_no = 1
                      --AND pay.actual_payment_date < v_end_date --v_end_date下个月第一天 --不需要看是否存在评估期内，有数据就可以 2023/03/08
                      AND pay.policy_no IS NOT NULL
                    GROUP BY c.policy_no,
                             c.endorse_seq_no) t) cmb
    ON (cma.cm_unit_id = cmb.cm_unit_id)
    WHEN MATCHED THEN
      UPDATE
         SET cma.d1 = cmb.d1,
             cma.d2 = cmb.d2,
             cma.d3 = cmb.d3,
             cma.d4 = cmb.d4,
             cma.border_date =(CASE WHEN cmb.d4 >= v_end_date THEN NULL  ELSE cmb.d4 END)--v_end_date下个月第一天
             --proc_id = (CASE WHEN cmb.d4 >= v_end_date THEN NULL ELSE v_proc_id END),
             --NODE_STATE = (CASE WHEN cmb.d4 >= v_end_date THEN NULL ELSE '1' END),
             --invest_rate   = NULL,
             --invest_amount = NULL
       WHERE cma.entity_id = p_entity_id
         AND cma.pl_judge_rslt IS NOT NULL -- 盈亏判定
         AND evaluate_approach <> 'D'
         --AND cma.pl_judge_rslt <> 'D'
         AND cma.year_month IS NULL -- 未生成合同组
         AND cma.buss_year_month <= p_year_month;
    COMMIT;*/

  EXCEPTION
    WHEN OTHERS THEN

       --意外处理
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]合同分组-获取合同确认日期业务数据:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_icg_fixed;

  PROCEDURE proc_icg_discern(p_entity_id  NUMBER,
                             p_year_month VARCHAR2) IS

    v_column     VARCHAR2(4000);
    v_proc_id    NUMBER;
    v_error_msg  varchar2(2000);
  BEGIN
    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]合同分组-传参不为空：proc_icg_discern(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]合同分组-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    --  合同确认
    proc_icg_fixed(p_entity_id, p_year_month);

    -- ROUTINE BODY GOES HERE...
    v_column := dm_pack_cmunit_common.func_get_contractcode(p_entity_id, 'C', 'DD');

    -- 根据PROC_CODE查询PROC_ID
    v_proc_id := dm_pack_common.func_get_procid('DM_CONTRACTGROUP');

    -- 更新合同组编码
    EXECUTE IMMEDIATE ('update  dm_buss_cmunit_direct t'
                       ||' set icg_no = (case when border_date is not null then '|| v_column||' else null end )'
                       ||', proc_id=' || v_proc_id
                       ||', invest_rate = null'
                       ||', invest_amount = null'
                       ||', node_state= '''|| (CASE WHEN v_column IS NULL THEN '2' ELSE '1' END) ||''''
                       ||', reason_of_failure = ''' || (CASE WHEN v_column IS NULL THEN 'DM_003' ELSE NULL END) ||''''
                       ||' where entity_id = ' || p_entity_id
                       ||' and evaluate_approach <> ''D'' '
                       ||' and pl_judge_rslt <> ''D'' '
                       ||' and year_month IS NULL '
                       ||' and buss_year_month <= ''' || p_year_month || ''' ');


    COMMIT;
  EXCEPTION
    WHEN OTHERS THEN

       --意外处理
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]合同分组:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_icg_discern;

  PROCEDURE proc_icg_group(p_entity_id  NUMBER,
                           p_year_month VARCHAR2) IS
  BEGIN

    --  生成合同组编码
    proc_icg_discern(p_entity_id, p_year_month);

  END proc_icg_group;

  PROCEDURE proc_investment_separate(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2) IS
    v_proc_id     NUMBER;
    v_count       NUMBER;
    v_year_month  VARCHAR2(200);
    v_error_msg   varchar2(2000);
  BEGIN

    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]投成拆分-传参不为空：proc_investment_separate(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 限定只处理 处于处理中的业务年月
    SELECT year_month
      INTO v_year_month
      FROM dm_conf_bussperiod
     WHERE entity_id = p_entity_id
       AND year_month = p_year_month
       AND period_state = '2' -- 处理中
       AND valid_is = '1'
       AND rownum = 1;

    -- 条件不满足,结束判定
    IF v_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]投成拆分-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 当前业务年月所有计量单元都生成合同分组，才能进行投成拆分
    SELECT 1
      INTO v_count
      FROM dm_buss_cmunit_direct
     WHERE entity_id = p_entity_id
       AND buss_year_month <= p_year_month
       AND icg_no IS NOT NULL
       AND invest_rate IS NULL
       AND invest_amount IS NULL
       AND rownum = 1;
    -- 存在合同分组为空的数据
    IF v_count != 1 THEN

       --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]投成拆分-存在合同分组为空的数据：';
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 根据PROC_CODE查询PROC_ID
    v_proc_id := dm_pack_common.func_get_procid('DM_INVESTMENT_COST');

    UPDATE dm_buss_cmunit_direct cm
       SET (invest_rate, invest_amount, proc_id, node_state, reason_of_failure) =
           (SELECT coalesce(to_number(icd.investment_rate, '999.999999'), 0) AS invest_rate,
                   --COALESCE(PREMIUM,0)*
                   coalesce(to_number(icd.investment_rate, '999.999999'), 0) AS invest_amount,
                   v_proc_id AS proc_id,
                   '1' AS node_state,
                   NULL AS reason_of_failure
              FROM dm_conf_contract_icddef icd
             WHERE 1 = 1
               AND icd.business_model = 'DD' --取直保的
               AND cm.entity_id = icd.entity_id
               AND icd.product_code = cm.product_code
               AND icd.valid_is = '1'
               AND icd.audit_state = '1')
     WHERE cm.entity_id = p_entity_id
       AND cm.major_risk IS NOT NULL
       AND cm.evaluate_approach IS NOT NULL
       AND cm.pl_judge_rslt IS NOT NULL
       AND cm.border_date IS NOT NULL
       AND cm.icg_no IS NOT NULL
       AND cm.year_month IS NULL
       AND cm.buss_year_month <= p_year_month;
    COMMIT;


    --未配置投成拆分规则
    UPDATE dm_buss_cmunit_direct cm
       SET proc_id           = v_proc_id,
           node_state        = '2',
           reason_of_failure = 'DM_001'
     WHERE cm.major_risk IS NOT NULL
       AND cm.evaluate_approach IS NOT NULL
       AND cm.pl_judge_rslt IS NOT NULL
       AND cm.border_date IS NOT NULL
       AND cm.icg_no IS NOT NULL
       AND cm.invest_rate IS NULL
       AND cm.invest_amount IS NULL
       AND cm.entity_id = p_entity_id
       AND cm.year_month IS NULL
       AND cm.buss_year_month <= p_year_month;
    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN

      --意外处理
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]投成拆分:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);


  END proc_investment_separate;


  PROCEDURE proc_contract_group_confirm(p_entity_id  NUMBER,
                                        p_year_month VARCHAR2) IS

  v_error_msg varchar2(2000);

  BEGIN
    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]确认-传参不为空：proc_contract_group_confirm(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

    --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]确认-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    UPDATE dm_buss_cmunit_direct
       SET year_month = p_year_month
     WHERE entity_id = p_entity_id
       AND buss_year_month <= p_year_month
       --AND border_date IS NOT NULL
       --AND invest_rate IS NOT NULL
       --AND invest_amount IS NOT NULL
       AND icg_no IS NOT NULL
       AND year_month IS NULL;
    COMMIT;

    proc_backup_profit_unit_result(p_entity_id, p_year_month);

  EXCEPTION
    WHEN OTHERS THEN

     --意外处理
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]确认:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);


  END proc_contract_group_confirm;


  PROCEDURE proc_backup_profit_unit_result(p_entity_id  NUMBER,
                                           p_year_month VARCHAR2) IS
      v_execute VARCHAR2(100);
      v_error_msg varchar2(2000);
  BEGIN

      --按盈亏判定方案配置区分: C-配置 R-规则
    SELECT test_plan_type
      INTO v_execute
      FROM dm_conf_icg_profitloss_plan
     WHERE entity_id = p_entity_id
       AND business_model = 'D';

    IF v_execute = 'R' THEN

      -- backup profit unit result and ref. param value
      dm_pack_cmunit_common.proc_backup_profit_unit_result_tab(p_entity_id,p_year_month,'dm_duct_direct_profit_unit');
      dm_pack_cmunit_common.proc_backup_profit_unit_result_tab(p_entity_id,p_year_month,'dm_duct_direct_profit_param');
      dm_pack_cmunit_common.proc_backup_profit_unit_result_tab(p_entity_id,p_year_month,'dm_duct_direct_profit_amount');

    END IF;

  EXCEPTION
    WHEN OTHERS THEN
         --意外处理
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]盈亏轨迹备份:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);
      
  END proc_backup_profit_unit_result;

  PROCEDURE proc_buss_cmunit_log(p_entity_id  NUMBER,
                                 p_year_month VARCHAR2,
                                 p_trace_no VARCHAR2,
                                 p_trace_code VARCHAR2,
                                 p_trace_status VARCHAR2,
                                 p_trace_msg VARCHAR2) IS

  BEGIN
    INSERT INTO DM_LOG_BUSS_CMUNIT
        (entity_id,
         year_month,
         buss_model,
         trace_no,
         trace_code,
         trace_status,
         trace_msg,
         create_time)
      VALUES
        (p_entity_id,
         p_year_month,
         'DD',
         p_trace_no,
         p_trace_code,
         p_trace_status,
         p_trace_msg,
         SYSDATE);
     COMMIT;

  EXCEPTION
    --意外处理
      WHEN OTHERS THEN
        null;

  END proc_buss_cmunit_log;


END dm_pack_cmunit_direct;
/
