package com.ss.ifrs.actuarial.api;

import com.ss.ifrs.actuarial.pojo.atrconf.vo.*;
import com.ss.ifrs.actuarial.service.AtrConfQuotaDataService;
import com.ss.ifrs.actuarial.service.AtrConfQuotaService;
import com.ss.platform.core.annotation.TrackUserBehavioralEnable;
import com.ss.platform.core.annotation.TrackUserBehavioral;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.ifrs.actuarial.constant.ActuarialConstant;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.library.constant.RestfulCodeConstant;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ExceptionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021/11/10 10:42
 * @Param param
 * @return return
 **/
@RestController
@RequestMapping("/quota_class")
@TrackUserBehavioralEnable
@Api(value = "假设值配置管理")
public class AtrConfQuotaClassApi extends BaseApi {

    @Autowired
    AtrConfQuotaService atrConfQuotaService;

    @Autowired
    AtrConfQuotaDataService confQuotaDataService;

    /**预期现金流类别*/
    @ApiOperation(value = "查找预期现金流假设值表头数据")
    @RequestMapping(value = "/becf/find_quota_header", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findBecfQuotaHeader (@RequestBody AtrConfQuotaDefVo confQuotaDefVo){
        try {
            confQuotaDefVo.setQuotaClass(ActuarialConstant.QuotaClass.BECF);
            Map<String,Object> map = confQuotaDataService.findQuotaDefHeader(confQuotaDefVo);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
        } catch (Exception ex) {
            String message = ExceptionUtil.getMessage(ex);
            logger.error(ex.getLocalizedMessage(), ex);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, message, RestfulCodeConstant.RestfulCode.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "查询预期现金流假设值列表")
    @TrackUserBehavioral(description = "Enquiry Assumption Config")
    @RequestMapping(value = "/becf/enquiry" , method = {RequestMethod.POST})
    public BaseResponse<Object> enquiryBecfQuota(HttpServletRequest request, @RequestBody AtrConfQuotaVo atrConfQuotaVo, int _pageNo, int _pageSize){
        Pageable pageable = new Pageable(_pageNo,_pageSize);
        atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.BECF);
        Page<?> page = confQuotaDataService.findAtrConfQuotaList(atrConfQuotaVo,pageable);
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("atrConfQuotaVoList",page);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
    }

    @ApiOperation(value = "保存或新增假设值配置信息")
    @RequestMapping(value = "/becf/add_or_update", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> saveBecfQuota(HttpServletRequest request, @RequestBody @Validated AtrConfQuotaMainVo atrConfQuotaMainVo, BindingResult br) {
        try {
            Long userId = this.loginUserId(request);
            atrConfQuotaMainVo.setQuotaClass(ActuarialConstant.QuotaClass.BECF);
            confQuotaDataService.addOrUpdate(atrConfQuotaMainVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, "SUCCESS");
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation("根据轨迹ID查找轨迹对象")
    @PermissionRequest(required = false)
    @RequestMapping(value = "/becf/his/find_by_pk/{quotaId}", method = RequestMethod.GET)
    public BaseResponse<AtrConfQuotaVo> findBecfQuotaHisById(@PathVariable("quotaId") Long quotaId){
        AtrConfQuotaVo atrConfQuotaVo = confQuotaDataService.findHisByPk(quotaId);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, atrConfQuotaVo);
    }

    @ApiOperation(value = "激活或禁用")
    @TrackUserBehavioral(description = "valid Status Assumption")
    @RequestMapping(value = "/becf/valid_status", method = RequestMethod.POST)
    public BaseResponse<Object> validStatusBecfQuota (HttpServletRequest request, @RequestBody AtrConfQuotaVo atrConfQuotaVo){
        try {
            Long userId = this.loginUserId(request);
            atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.BECF);
            String resMsg = confQuotaDataService.disableValid(atrConfQuotaVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, RestfulCodeConstant.RestfulCode.SUCCESS);
        } catch (Exception ex) {
            String message = ExceptionUtil.getMessage(ex);
            logger.error(ex.getLocalizedMessage(), ex);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, message, RestfulCodeConstant.RestfulCode.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "删除假设值对象信息")
    @TrackUserBehavioral(description = "Delete Assumption")
    @RequestMapping(value = "/becf/delete", method = RequestMethod.POST)
    public BaseResponse<Object> deleteBecfQuota(HttpServletRequest request,@RequestBody AtrConfQuotaVo atrConfQuotaVo) {
        Long userId = this.loginUserId(request);
        try {
            atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.BECF);
            confQuotaDataService.deleteByVo(atrConfQuotaVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, RestfulCodeConstant.RestfulCode.SUCCESS);
        } catch (Exception ex) {
            String message = ExceptionUtil.getMessage(ex);
            logger.error(ex.getLocalizedMessage(), ex);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, message, RestfulCodeConstant.RestfulCode.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "审核")
    @RequestMapping(value = "/becf/audit", method = RequestMethod.POST)
    public BaseResponse<String> auditBecfQuota (HttpServletRequest request, @RequestBody @Validated AtrConfQuotaVo
            atrConfQuotaVo, BindingResult br){
        Long userId = this.loginUserId(request);
        atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.BECF);
        confQuotaDataService.updateAudit(atrConfQuotaVo, userId);
        return new BaseResponse<String>(ResCodeConstant.ResCode.SUCCESS, "SUCCESS");
    }


    @ApiOperation(value = "批量审核")
    @TrackUserBehavioral(description = "Batch Audit Assumption")
    @RequestMapping(value = "/becf/batch_audit", method = RequestMethod.POST)
    public BaseResponse<String> batchAuditBecfQuota (HttpServletRequest
                                                         request, @RequestBody ArrayList< AtrConfQuotaVo > auditDealList, String auditState, String checkedMsg){
        // 获取当前操作人Id
        Long userId = this.loginUserId(request);
        for (AtrConfQuotaVo atrConfQuotaVo : auditDealList) {
            atrConfQuotaVo.setAuditState(auditState);
            atrConfQuotaVo.setCheckedMsg(checkedMsg);
            atrConfQuotaVo.setCheckedId(this.loginUserId(request));
            atrConfQuotaVo.setCheckedTime(new Date());
            atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.BECF);
        }
        try {
            confQuotaDataService.batchAudit(auditDealList, userId);
            return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, ResCodeConstant.ResCode.SUCCESS);
        } catch (UnexpectedRollbackException e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<String>(ResCodeConstant.ResCode.OTHER_ERROR, null);
        }
    }

    @ApiOperation(value = "查询假设值审计信息")
    @RequestMapping(value = "/becf/find_audit_information_by_vo" , method = {RequestMethod.POST})
    @PermissionRequest(required = false)
    public BaseResponse<Object> findBecfVo(HttpServletRequest request, @RequestBody AtrConfQuotaVo atrConfQuotaVo){
        AtrConfQuotaVo vo = confQuotaDataService.findAtrConfQuotaVo(atrConfQuotaVo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, vo);
    }

    @ApiOperation(value = "查找上一期的假设值")
    @RequestMapping(value = "/becf/find_pre_period_info" , method = {RequestMethod.POST})
    @PermissionRequest(required = false)
    public BaseResponse<Object> findBecfQuotaDefMap(@RequestBody AtrConfQuotaVo atrConfQuotaVo){
        Map<String,Object> map = new HashMap<String, Object>();
        atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.BECF);
        map = confQuotaDataService.findPrePeriod(atrConfQuotaVo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, map);
    }

    @ApiOperation(value = "加载上一期的假设值")
    @RequestMapping(value = "/becf/load_pre_period_quota" , method = {RequestMethod.POST})
    @PermissionRequest(required = false)
    public BaseResponse<Object> loadPrePeriodBecfQuota(HttpServletRequest request, @RequestBody AtrConfQuotaVo atrConfQuotaVo){
        // 获取当前操作人Id
        Long userId = this.loginUserId(request);
        atrConfQuotaVo.setCreatorId(userId);
        atrConfQuotaVo.setCreateTime(new Date());
        atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.BECF);
        Map<String,Object> map = new HashMap<String, Object>();
        map = confQuotaDataService.loadPrePeriodQuota(atrConfQuotaVo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, map);
    }


    /*******************/
    @ApiOperation(value = "假设值配置假设数据")
    @RequestMapping(value = "/becf/find_conf_quota_by_vo", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findConfBecfQuota(@RequestBody AtrConfQuotaVo atrConfQuotaVo) {
        atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.BECF);
        List<AtrConfQuotaGroupDefVo> map =  confQuotaDataService.findConfQuotaDef(atrConfQuotaVo);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
    }

    @ApiOperation(value = "查找假设值信息(发展期类型)")
    @RequestMapping(value = "/becf/find_dtl_by_vo", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findConfBecfQuotaByVo(HttpServletRequest request, @RequestBody AtrConfQuotaVo atrConfQuotaVo) {
        atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.BECF);
        Map<String, Object> resultMap = confQuotaDataService.findPeriodMap(atrConfQuotaVo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, resultMap);
    }


    @ApiOperation(value = "查找是否存在相同数据")
    @RequestMapping(value = "/becf/find_validate_code", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findBecfValidateCode (HttpServletRequest request, @RequestBody AtrConfQuotaVo atrConfQuotaVo){
        try {
            String validateFlag = confQuotaDataService.findValidateCode(atrConfQuotaVo);
            Map<String,Object> map = new HashMap<String, Object>();
            map.put("validateFlag",validateFlag);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
        } catch (Exception ex) {
            String message = ExceptionUtil.getMessage(ex);
            logger.error(ex.getLocalizedMessage(), ex);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, message, RestfulCodeConstant.RestfulCode.INTERNAL_SERVER_ERROR);
        }
    }


    @ApiOperation(value = "查找PAA假设值的发展期数据")
    @RequestMapping(value = "/becf/find_quota_detail_by_vo", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findBecfQuotaDetailByVo(HttpServletRequest request, @RequestBody AtrConfQuotaVo atrConfQuotaVo) {
        atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.BECF);
        Map<String, Object> resultMap = confQuotaDataService.findQuotaDetailByVo(atrConfQuotaVo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, resultMap);
    }



    @ApiOperation(value = "假设值配置假设轨迹数据")
    @RequestMapping(value = "/becf/his/find_conf_quota_by_vo", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findConfBecfQuotaHis(@RequestBody AtrConfQuotaVo atrConfQuotaVo) {
        atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.BECF);
        List<AtrConfQuotaGroupDefVo> map =  confQuotaDataService.findConfQuotaHisDef(atrConfQuotaVo);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
    }

    @ApiOperation(value = "查找假设信息轨迹（发展期）")
    @RequestMapping(value = "/becf/his/find_dtl_by_vo", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findConfBecfQuotaHisByVo(HttpServletRequest request, @RequestBody AtrConfQuotaVo atrConfQuotaVo) {
        atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.BECF);
        Map<String, Object> resultMap = confQuotaDataService.findPeriodMapHis(atrConfQuotaVo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, resultMap);
    }


    @ApiOperation(value = "假设值导入")
    @RequestMapping(value = "/becf/import", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> becfQuotaImport(HttpServletRequest request, @RequestParam(value = "file", required = false) MultipartFile file,
                                           AtrConfQuotaImportVo importVo) throws Exception {
        Long userId = this.loginUserId(request);
        importVo.setQuotaClass(ActuarialConstant.QuotaClass.BECF);
        List<AtrConfQuotaImportVo> atrConfQuotaImportVoList = confQuotaDataService.excelImport(file, importVo, userId);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, atrConfQuotaImportVoList);
    }

    @ApiOperation(value = "生成假设配置上传模板")
    @TrackUserBehavioral(description = "generate template")
    @RequestMapping(value = "/becf/generate_template", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> generateBecfTemplate(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrConfQuotaImportVo importVo) {
        importVo.setQuotaClass(ActuarialConstant.QuotaClass.BECF);
        try {
            confQuotaDataService.generateTemplate(request, response, importVo);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, RestfulCodeConstant.RestfulCode.SUCCESS);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, RestfulCodeConstant.RestfulCode.INTERNAL_SERVER_ERROR);
        }

    }

    @ApiOperation(value = "上传假设配置数据")
    @RequestMapping(value = "/becf/data_upload/upload", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> dataUploadBecf(@RequestParam(value = "file", required = false) MultipartFile file,@RequestParam(value = "detailFile", required = false) MultipartFile detailFile,
                                           HttpServletRequest request, AtrConfQuotaImportVo importVo) throws Exception {
        String result = "0";
        String exceptionMsg = null;
        Long userId = this.loginUserId(request);
        importVo.setQuotaClass(ActuarialConstant.QuotaClass.BECF);
        try {
            Map<String, Object> dealMap = confQuotaDataService.executeUpload(request, file, importVo, userId);
            result = "1";
        } catch (Exception ex) {
            exceptionMsg = ExceptionUtil.getMessage(ex);
            logger.error(ex.getLocalizedMessage(), ex);
        }
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, exceptionMsg, result);
    }

    @ApiOperation(value = "假设值批量导入")
    @RequestMapping(value = "/becf/batch_import", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> batchImport(HttpServletRequest request, @RequestParam(value = "file", required = false) MultipartFile file,
                                            AtrConfQuotaImportVo atrConfQuotaVo)  {
        Long userId = this.loginUserId(request);
        atrConfQuotaVo.setCreatorId(userId);
        atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.BECF);
        
        Map<String, Object> resultMap = new HashMap<>();
        
        try {
            // 验证文件是否为空
            if (file == null || file.isEmpty()) {
                logger.error("上传文件不能为空");
                resultMap.put("success", false);
                resultMap.put("message", "上传文件不能为空");
                return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, resultMap);
            }

            // 验证文件格式
            String fileName = file.getOriginalFilename();
            if (fileName == null || !fileName.toLowerCase().endsWith(".xlsx")) {
                logger.error("文件格式不正确，只支持.xlsx格式");
                resultMap.put("success", false);
                resultMap.put("message", "文件格式不正确，只支持.xlsx格式");
                return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, resultMap);
            }

            // 记录导入开始时间
            long startTime = System.currentTimeMillis();
            logger.info("开始导入假设值数据，文件名: {}", fileName);

            // 使用按列位置的方法处理Excel导入，解决列名变更问题
            List<AtrConfQuotaVo> importedData = atrConfQuotaService.excelImportToQuotaByPosition(file, atrConfQuotaVo);

            // 记录导入结束时间
            long endTime = System.currentTimeMillis();
            logger.info("假设值数据导入完成，耗时: {} 毫秒，导入记录数: {}",
                    (endTime - startTime),
                    importedData != null ? importedData.size() : 0);



            return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "导入成功", "1");
        } catch (Exception e) {
            String errorMsg = ExceptionUtil.getMessage(e);
            logger.error("假设值导入失败: {}", errorMsg, e);

            resultMap.put("success", false);
            resultMap.put("message", "导入失败: " + errorMsg);

            return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, resultMap);
        }
    }

    @ApiOperation(value = "导出假设值")
    @RequestMapping(value = "/becf/export_excel" , method = {RequestMethod.POST})
    @PermissionRequest(required = false)
    public void downloadQuotaExcel(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrConfQuotaImportVo qtcConfQuotaVo) throws Exception {
        Long userId = this.loginUserId(request);
        qtcConfQuotaVo.setCreatorId(userId);
        qtcConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.BECF);
        confQuotaDataService.downloadQuotaExcel(request,response, qtcConfQuotaVo);
    }


    /**
     * 精算假设类别
     * */
    @ApiOperation(value = "查找精算假设表头数据")
    @RequestMapping(value = "/act/find_quota_header", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findActuarialQuotaHeader (@RequestBody AtrConfQuotaDefVo confQuotaDefVo){
        try {
            confQuotaDefVo.setQuotaClass(ActuarialConstant.QuotaClass.ACTUARIAL);
            Map<String,Object> map = confQuotaDataService.findQuotaDefHeader(confQuotaDefVo);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
        } catch (Exception ex) {
            String message = ExceptionUtil.getMessage(ex);
            logger.error(ex.getLocalizedMessage(), ex);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, message, RestfulCodeConstant.RestfulCode.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "查询精算假设列表")
    @TrackUserBehavioral(description = "Enquiry Assumption Config")
    @RequestMapping(value = "/act/enquiry" , method = {RequestMethod.POST})
    public BaseResponse<Object> enquiryActuarialQuota(HttpServletRequest request, @RequestBody AtrConfQuotaVo atrConfQuotaVo, int _pageNo, int _pageSize){
        Pageable pageable = new Pageable(_pageNo,_pageSize);
        atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.ACTUARIAL);
        Page<?> page = confQuotaDataService.findAtrConfQuotaList(atrConfQuotaVo,pageable);
        Map<String,Object> map = new HashMap<String, Object>();
        map.put("atrConfQuotaVoList",page);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
    }

    @ApiOperation(value = "保存或新增假设值配置信息")
    @RequestMapping(value = "/act/add_or_update", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> saveActuarialQuota(HttpServletRequest request, @RequestBody @Validated AtrConfQuotaMainVo atrConfQuotaMainVo, BindingResult br) {
        try {
            Long userId = this.loginUserId(request);
            atrConfQuotaMainVo.setQuotaClass(ActuarialConstant.QuotaClass.ACTUARIAL);
            confQuotaDataService.addOrUpdate(atrConfQuotaMainVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, "SUCCESS");
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation("根据轨迹ID查找轨迹对象")
    @PermissionRequest(required = false)
    @RequestMapping(value = "/act/his/find_by_pk/{quotaId}", method = RequestMethod.GET)
    public BaseResponse<AtrConfQuotaVo> findActuarialQuotaHisById(@PathVariable("quotaId") Long quotaId){
        AtrConfQuotaVo atrConfQuotaVo = confQuotaDataService.findHisByPk(quotaId);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, atrConfQuotaVo);
    }

    @ApiOperation(value = "激活或禁用")
    @TrackUserBehavioral(description = "valid Status Assumption")
    @RequestMapping(value = "/act/valid_status", method = RequestMethod.POST)
    public BaseResponse<Object> validStatusActuarialQuota (HttpServletRequest request, @RequestBody AtrConfQuotaVo atrConfQuotaVo){
        try {
            Long userId = this.loginUserId(request);
            atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.ACTUARIAL);
            String resMsg = confQuotaDataService.disableValid(atrConfQuotaVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, RestfulCodeConstant.RestfulCode.SUCCESS);
        } catch (Exception ex) {
            String message = ExceptionUtil.getMessage(ex);
            logger.error(ex.getLocalizedMessage(), ex);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, message, RestfulCodeConstant.RestfulCode.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "删除假设值对象信息")
    @TrackUserBehavioral(description = "Delete Assumption")
    @RequestMapping(value = "/act/delete", method = RequestMethod.POST)
    public BaseResponse<Object> deleteActuarialQuota(HttpServletRequest request,@RequestBody AtrConfQuotaVo atrConfQuotaVo) {
        Long userId = this.loginUserId(request);
        try {
            atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.ACTUARIAL);
            confQuotaDataService.deleteByVo(atrConfQuotaVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, RestfulCodeConstant.RestfulCode.SUCCESS);
        } catch (Exception ex) {
            String message = ExceptionUtil.getMessage(ex);
            logger.error(ex.getLocalizedMessage(), ex);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, message, RestfulCodeConstant.RestfulCode.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "审核")
    @RequestMapping(value = "/act/audit", method = RequestMethod.POST)
    public BaseResponse<String> auditActuarialQuota (HttpServletRequest request, @RequestBody @Validated AtrConfQuotaVo
            atrConfQuotaVo, BindingResult br){
        Long userId = this.loginUserId(request);
        atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.ACTUARIAL);
        confQuotaDataService.updateAudit(atrConfQuotaVo, userId);
        return new BaseResponse<String>(ResCodeConstant.ResCode.SUCCESS, "SUCCESS");
    }


    @ApiOperation(value = "批量审核")
    @TrackUserBehavioral(description = "Batch Audit Assumption")
    @RequestMapping(value = "/act/batch_audit", method = RequestMethod.POST)
    public BaseResponse<String> batchAuditActuarialQuota (HttpServletRequest
                                                                  request, @RequestBody ArrayList< AtrConfQuotaVo > auditDealList, String auditState, String checkedMsg){
        // 获取当前操作人Id
        Long userId = this.loginUserId(request);
        for (AtrConfQuotaVo atrConfQuotaVo : auditDealList) {
            atrConfQuotaVo.setAuditState(auditState);
            atrConfQuotaVo.setCheckedMsg(checkedMsg);
            atrConfQuotaVo.setCheckedId(this.loginUserId(request));
            atrConfQuotaVo.setCheckedTime(new Date());
            atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.ACTUARIAL);
        }
        try {
            confQuotaDataService.batchAudit(auditDealList, userId);
            return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, ResCodeConstant.ResCode.SUCCESS);
        } catch (UnexpectedRollbackException e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<String>(ResCodeConstant.ResCode.OTHER_ERROR, null);
        }
    }

    @ApiOperation(value = "查询假设值审计信息")
    @RequestMapping(value = "/act/find_audit_information_by_vo" , method = {RequestMethod.POST})
    @PermissionRequest(required = false)
    public BaseResponse<Object> findActuarialVo(HttpServletRequest request, @RequestBody AtrConfQuotaVo atrConfQuotaVo){
        AtrConfQuotaVo vo = confQuotaDataService.findAtrConfQuotaVo(atrConfQuotaVo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, vo);
    }

    @ApiOperation(value = "查找上一期的假设值")
    @RequestMapping(value = "/act/find_pre_period_info" , method = {RequestMethod.POST})
    @PermissionRequest(required = false)
    public BaseResponse<Object> findActuarialQuotaDefMap(@RequestBody AtrConfQuotaVo atrConfQuotaVo){
        Map<String,Object> map = new HashMap<String, Object>();
        atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.ACTUARIAL);
        map = confQuotaDataService.findPrePeriod(atrConfQuotaVo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, map);
    }

    @ApiOperation(value = "加载上一期的假设值")
    @RequestMapping(value = "/act/load_pre_period_quota" , method = {RequestMethod.POST})
    @PermissionRequest(required = false)
    public BaseResponse<Object> loadPrePeriodActuarialQuota(HttpServletRequest request, @RequestBody AtrConfQuotaVo atrConfQuotaVo){
        // 获取当前操作人Id
        Long userId = this.loginUserId(request);
        atrConfQuotaVo.setCreatorId(userId);
        atrConfQuotaVo.setCreateTime(new Date());
        atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.ACTUARIAL);
        Map<String,Object> map = new HashMap<String, Object>();
        map = confQuotaDataService.loadPrePeriodQuota(atrConfQuotaVo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, map);
    }

    @ApiOperation(value = "假设值配置假设数据")
    @RequestMapping(value = "/act/find_conf_quota_by_vo", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findConfActuarialQuota(@RequestBody AtrConfQuotaVo atrConfQuotaVo) {
        atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.ACTUARIAL);
        List<AtrConfQuotaGroupDefVo> map =  confQuotaDataService.findConfQuotaDef(atrConfQuotaVo);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
    }

    @ApiOperation(value = "查找假设值信息(发展期类型)")
    @RequestMapping(value = "/act/find_dtl_by_vo", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findConfActuarialQuotaByVo(HttpServletRequest request, @RequestBody AtrConfQuotaVo atrConfQuotaVo) {
        atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.ACTUARIAL);
        Map<String, Object> resultMap = confQuotaDataService.findPeriodMap(atrConfQuotaVo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, resultMap);
    }


    @ApiOperation(value = "查找是否存在相同数据")
    @RequestMapping(value = "/act/find_validate_code", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findActuarialValidateCode (HttpServletRequest request, @RequestBody AtrConfQuotaVo atrConfQuotaVo){
        try {
            String validateFlag = confQuotaDataService.findValidateCode(atrConfQuotaVo);
            Map<String,Object> map = new HashMap<String, Object>();
            map.put("validateFlag",validateFlag);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
        } catch (Exception ex) {
            String message = ExceptionUtil.getMessage(ex);
            logger.error(ex.getLocalizedMessage(), ex);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, message, RestfulCodeConstant.RestfulCode.INTERNAL_SERVER_ERROR);
        }
    }


    @ApiOperation(value = "查找PAA假设值的发展期数据")
    @RequestMapping(value = "/act/find_quota_detail_by_vo", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findActuarialQuotaDetailByVo(HttpServletRequest request, @RequestBody AtrConfQuotaVo atrConfQuotaVo) {
        atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.ACTUARIAL);
        Map<String, Object> resultMap = confQuotaDataService.findQuotaDetailByVo(atrConfQuotaVo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, resultMap);
    }



    @ApiOperation(value = "假设值配置假设轨迹数据")
    @RequestMapping(value = "/act/his/find_conf_quota_by_vo", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findConfActuarialQuotaHis(@RequestBody AtrConfQuotaVo atrConfQuotaVo) {
        atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.ACTUARIAL);
        List<AtrConfQuotaGroupDefVo> map =  confQuotaDataService.findConfQuotaHisDef(atrConfQuotaVo);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
    }

    @ApiOperation(value = "查找假设信息轨迹（发展期）")
    @RequestMapping(value = "/act/his/find_dtl_by_vo", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findConfActuarialQuotaHisByVo(HttpServletRequest request, @RequestBody AtrConfQuotaVo atrConfQuotaVo) {
        atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.ACTUARIAL);
        Map<String, Object> resultMap = confQuotaDataService.findPeriodMapHis(atrConfQuotaVo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, resultMap);
    }


    @ApiOperation(value = "假设值导入")
    @RequestMapping(value = "/act/import", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> ActuarialQuotaImport(HttpServletRequest request, @RequestParam(value = "file", required = false) MultipartFile file,
                                                     AtrConfQuotaImportVo importVo) throws Exception {
        Long userId = this.loginUserId(request);
        importVo.setQuotaClass(ActuarialConstant.QuotaClass.ACTUARIAL);
        List<AtrConfQuotaImportVo> atrConfQuotaImportVoList = confQuotaDataService.excelImport(file, importVo, userId);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, atrConfQuotaImportVoList);
    }

    @ApiOperation(value = "生成假设配置上传模板")
    @TrackUserBehavioral(description = "generate template")
    @RequestMapping(value = "/act/generate_template", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> generateActuarialTemplate(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrConfQuotaImportVo importVo) {
        importVo.setQuotaClass(ActuarialConstant.QuotaClass.ACTUARIAL);
        try {
            confQuotaDataService.generateTemplate(request, response, importVo);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, RestfulCodeConstant.RestfulCode.SUCCESS);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, RestfulCodeConstant.RestfulCode.INTERNAL_SERVER_ERROR);
        }

    }

    @ApiOperation(value = "上传假设配置数据")
    @RequestMapping(value = "/act/data_upload/upload", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> dataUploadActuarial(@RequestParam(value = "file", required = false) MultipartFile file,@RequestParam(value = "detailFile", required = false) MultipartFile detailFile,
                                                    HttpServletRequest request, AtrConfQuotaImportVo importVo) throws Exception {
        String result = "0";
        String exceptionMsg = null;
        Long userId = this.loginUserId(request);
        importVo.setQuotaClass(ActuarialConstant.QuotaClass.ACTUARIAL);
        try {
            Map<String, Object> dealMap = confQuotaDataService.executeUpload(request, file, importVo, userId);
            result = "1";
        } catch (Exception ex) {
            exceptionMsg = ExceptionUtil.getMessage(ex);
            logger.error(ex.getLocalizedMessage(), ex);
        }
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, exceptionMsg, result);
    }

    @ApiOperation(value = "查找假设值个数")
    @RequestMapping(value = "/becf/countQuota" , method = {RequestMethod.POST})
    @PermissionRequest(required = false)
    public BaseResponse<Object> countBecfQuota(@RequestBody AtrConfQuotaVo atrConfQuotaVo){
        atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.BECF);
        Long count = atrConfQuotaService.countQuota(atrConfQuotaVo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, count);
    }


    @ApiOperation(value = "导出假设值批量导入模板")
    @RequestMapping(value = "/act/downloadTemplate" , method = {RequestMethod.POST})
    @PermissionRequest(required = false)
    public void downloadActQuota(HttpServletRequest request, HttpServletResponse response,@RequestBody AtrConfQuotaImportVo qtcConfQuotaVo) throws Exception {
        Long userId = this.loginUserId(request);
        qtcConfQuotaVo.setCreatorId(userId);
        qtcConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.ACTUARIAL);
        atrConfQuotaService.downloadQtpQuota(request,response, qtcConfQuotaVo);
    }


    @ApiOperation(value = "查找假设值个数")
    @RequestMapping(value = "/act/countQuota" , method = {RequestMethod.POST})
    @PermissionRequest(required = false)
    public BaseResponse<Object> countActQuota(@RequestBody AtrConfQuotaVo atrConfQuotaVo){
        atrConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.ACTUARIAL);
        Long count = atrConfQuotaService.countQuota(atrConfQuotaVo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, count);
    }


    @ApiOperation(value = "导出假设值批量导入模板")
    @RequestMapping(value = "/icg_risk/downloadTemplate" , method = {RequestMethod.POST})
    @PermissionRequest(required = false)
    public void downloadIcgTemplate(HttpServletRequest request, HttpServletResponse response,@RequestBody AtrConfQuotaImportVo qtcConfQuotaVo) throws Exception {
        Long userId = this.loginUserId(request);
        qtcConfQuotaVo.setCreatorId(userId);
        qtcConfQuotaVo.setQuotaClass(ActuarialConstant.QuotaClass.ACTUARIAL);
        confQuotaDataService.downloadIcgTemplate(request,response, qtcConfQuotaVo);
    }

    @ApiOperation(value = "假设值导入")
    @RequestMapping(value = "/icg_risk/import",method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> epiImport(HttpServletRequest request, @RequestParam(value = "file", required = false) MultipartFile file, AtrConfQuotaImportVo importVo){

        importVo.setCreatorId(this.loginUserId(request));
        importVo.setQuotaClass(ActuarialConstant.QuotaClass.BECF);
        Map<String, Object> resultMap = new HashMap<>();
        try {
            // 验证文件是否为空
            if (file == null || file.isEmpty()) {
                logger.error("上传文件不能为空");
                resultMap.put("success", false);
                resultMap.put("message", "上传文件不能为空");
                return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, resultMap);
            }

            // 验证文件格式
            String fileName = file.getOriginalFilename();
            if (fileName == null || !fileName.toLowerCase().endsWith(".xlsx")) {
                logger.error("文件格式不正确，只支持.xlsx格式");
                resultMap.put("success", false);
                resultMap.put("message", "文件格式不正确，只支持.xlsx格式");
                return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, resultMap);
            }

            // 记录导入开始时间
            long startTime = System.currentTimeMillis();
            logger.info("开始导入假设值数据，文件名: {}", fileName);

            // 使用按列位置的方法处理Excel导入，解决列名变更问题
            List<AtrConfQuotaVo> importedData = confQuotaDataService.quotaIcgImport(file, importVo);

            // 记录导入结束时间
            long endTime = System.currentTimeMillis();
            logger.info("假设值数据导入完成，耗时: {} 毫秒，导入记录数: {}",
                    (endTime - startTime),
                    importedData != null ? importedData.size() : 0);

            return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "导入成功", "1");
        } catch (Exception e) {
            String errorMsg = ExceptionUtil.getMessage(e);
            logger.error("假设值导入失败: {}", errorMsg, e);

            resultMap.put("success", false);
            resultMap.put("message", "导入失败: " + errorMsg);

            return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, resultMap);
        }
    }
}
