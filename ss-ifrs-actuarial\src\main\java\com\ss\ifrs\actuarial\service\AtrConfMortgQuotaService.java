package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfMortgQuotaVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface AtrConfMortgQuotaService {

    void addOrUpdate(AtrConfMortgQuotaVo atrConfMortgQuotaVo, Long userId);

    void saveList(List<AtrConfMortgQuotaVo> atrConfQuotaVos, Long userId);

    AtrConfMortgQuotaVo findById(Long riskRefId);

    AtrConfMortgQuotaVo findHisById(Long riskRefHisId);

    Page<AtrConfMortgQuotaVo> searchPage(AtrConfMortgQuotaVo atrConfMortgQuotaVo, Pageable pageParam);

    void delete(Long confQuotaId, Long userId);

    void updateValid(AtrConfMortgQuotaVo dmConfigCheckRuleVo, Long userId);

    void auditList(List<AtrConfMortgQuotaVo> atrConfQuotaVos, Long userId);

    void audit(AtrConfMortgQuotaVo atrConfMortgQuotaVo, Long userId);

    Long checkNaturalPk(AtrConfMortgQuotaVo atrConfMortgQuotaVo);

    AtrConfMortgQuotaVo validData(MultipartFile file, AtrConfMortgQuotaVo atrConfMortgQuotaVo) throws Exception ;

    void excelImport(MultipartFile file, AtrConfMortgQuotaVo atrConfMortgQuotaVo, Long userId) throws Exception ;
}
