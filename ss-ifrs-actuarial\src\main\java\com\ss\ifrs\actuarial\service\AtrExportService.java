package com.ss.ifrs.actuarial.service;

import com.ss.library.excel.ExcelSheet;
import com.ss.library.mybatis.model.Page;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface AtrExportService {

    public void exportPage(HttpServletRequest request, HttpServletResponse response, Page<?> page, Class voClazz, String beanName, String fileName, String targetRouter, Long userId) throws Exception;

    public void exportList(HttpServletRequest request, HttpServletResponse response, List<?> list, Class voClazz, String beanName, String fileName, String targetRouter, Long userId) throws Exception;

    public String generateExcelBySheetList(HttpServletRequest request, List<ExcelSheet> sheetList, String fileName) throws Exception;

    public void exportSheetList(HttpServletRequest request, HttpServletResponse response, List<ExcelSheet> sheetList, String fileName, String targetRouter, Long userId) throws Exception;

    public Map<String,String> syncExportSheetList(String language, List<ExcelSheet> sheetList, List<Class> voClassList, String fileName, String targetRouter, Long userId) throws Exception;


    public Map<String,String> syncExportSheetList(String language, List<ExcelSheet> sheetList, List<Class> voClassList, String fileName, String targetRouter, String feeType, Integer currentSheetNo, Integer totalSheetNo) throws Exception;

    public void exportSheetList(HttpServletRequest request, HttpServletResponse response, List<ExcelSheet> sheetList, String fileName, String tempSrc, String logFileName, String targetRouter, Long userId) throws Exception;

    public Map<String,String> exportSheetListReturnName(HttpServletRequest request, HttpServletResponse response, List<ExcelSheet> sheetList, String fileName, String tempSrc, String logFileName, String targetRouter, Long userId) throws Exception;


    Long saveExportTrackLog(String exportFileName, String targetRouter, String filePath, Long userId, Boolean successfulIs);

    void updateExportTrackLog(Long logId, String taskStatus, Long userId, String errorMsg);

    public void dealZip(String zipName, Map<String,String>files, String targetRouter, Long userId);

    void downloadTemplateExcel(HttpServletRequest request, HttpServletResponse response, String fileName, String targetRouter, Long userId) throws Exception;

    String getOutPutPathSave();

    String getOutPutPath();

    String getTemplatePath();

    String getZipName(String zipName, String logFileName);

    void exportExcelSheetList(HttpServletRequest request, HttpServletResponse response, List<ExcelSheet> sheetList, String fileName, String logFileName, String targetRouter, Long userId) throws Exception;

    /**
     * 导出多Sheet的Excel文件方法，支持扩展参数
     * @param request HTTP请求
     * @param response HTTP响应
     * @param sheetList sheet页数据列表
     * @param fileName 导出模板文件名
     * @param extendMap 扩展参数
     * @param targetRouter 模板文件路径
     * @param userId 当前用户id
     * @throws Exception 异常
     */
    void exportExcelSheetList(HttpServletRequest request, HttpServletResponse response, List<ExcelSheet> sheetList, String fileName, Map<String, Object> extendMap, String targetRouter, Long userId) throws Exception;

}
