package com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class AtrBussIbnrcalcTVDiscountVo {
    private Integer devNo;
    private BigDecimal slExpectedReportedRatio;
    private BigDecimal lrInterestRatio;
    private BigDecimal clInterestRatio;
    private BigDecimal bfInterestRatio;
    private BigDecimal timePeriod;
    private String futureNode;
    private BigDecimal lrDisCountedUnpaidAmount;
    private BigDecimal bfDisCountedUnpaidAmount;
    private BigDecimal clDisCountedUnpaidAmount;
}
