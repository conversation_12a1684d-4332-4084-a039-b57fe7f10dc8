/**
 * 
 * This file was generated by Taiping MyBatis Generator(v1.2.12).
 * Date: 2024-08-22 11:14:51
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.ir.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by Taiping MyBatis Generator(v1.2.12).
 * Create Date: 2024-08-22 11:14:51<br/>
 * Description: 当期初始利率发展期<br/>
 * Table Name: ATR_BUSS_IR_WEIGHT_DEV<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "当期初始利率发展期")
public class AtrBussIrWeightDev implements Serializable {
    /**
     * Database column: ATR_BUSS_IR_WEIGHT_DEV.ID
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: ATR_BUSS_IR_WEIGHT_DEV.MAIN_ID
     * Database remarks: 主表ID
     */
    @ApiModelProperty(value = "主表ID", required = true)
    private Long mainId;

    /**
     * Database column: ATR_BUSS_IR_WEIGHT_DEV.DEV_NO
     * Database remarks: 发展期序号
     */
    @ApiModelProperty(value = "发展期序号", required = true)
    private Long devNo;

    /**
     * Database column: ATR_BUSS_IR_WEIGHT_DEV.RATE_BASE
     * Database remarks: 基础利率
     */
    @ApiModelProperty(value = "基础利率", required = true)
    private BigDecimal rateBase;

    /**
     * Database column: ATR_BUSS_IR_WEIGHT_DEV.RATE_B
     * Database remarks: 期初利率
     */
    @ApiModelProperty(value = "期初利率", required = true)
    private BigDecimal rateB;

    /**
     * Database column: ATR_BUSS_IR_WEIGHT_DEV.RATE_M
     * Database remarks: 期中利率
     */
    @ApiModelProperty(value = "期中利率", required = true)
    private BigDecimal rateM;

    /**
     * Database column: ATR_BUSS_IR_WEIGHT_DEV.RATE_E
     * Database remarks: 期末利率
     */
    @ApiModelProperty(value = "期末利率", required = true)
    private BigDecimal rateE;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMainId() {
        return mainId;
    }

    public void setMainId(Long mainId) {
        this.mainId = mainId;
    }

    public Long getDevNo() {
        return devNo;
    }

    public void setDevNo(Long devNo) {
        this.devNo = devNo;
    }

    public BigDecimal getRateBase() {
        return rateBase;
    }

    public void setRateBase(BigDecimal rateBase) {
        this.rateBase = rateBase;
    }

    public BigDecimal getRateB() {
        return rateB;
    }

    public void setRateB(BigDecimal rateB) {
        this.rateB = rateB;
    }

    public BigDecimal getRateM() {
        return rateM;
    }

    public void setRateM(BigDecimal rateM) {
        this.rateM = rateM;
    }

    public BigDecimal getRateE() {
        return rateE;
    }

    public void setRateE(BigDecimal rateE) {
        this.rateE = rateE;
    }
}