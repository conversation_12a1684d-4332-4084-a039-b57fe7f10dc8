package com.ss.ifrs.actuarial.pojo.other.vo;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;

/**
 * <AUTHOR>
 */
public class AtrExcelCellVo {
    private XSSFCell hssfCell;
    private XSSFCellStyle hssfCellStyle;
    /**
     * 行
     */
    private Integer row;
    /**
     * 列
     */
    private Integer column;

    private Object value;

    private String columnCellType;

    /**
     * 对齐方式
     */
    private HorizontalAlignment alignment;

    public XSSFCellStyle getHssfCellStyle() {
        return hssfCellStyle;
    }

    public void setHssfCellStyle(XSSFCellStyle hssfCellStyle) {
        this.hssfCellStyle = hssfCellStyle;
    }

    /**
     * 合并单元格，行开始
     */
    private Integer mergerRowStart;

    /**
     * 合并单元格，行结束
     */
    private Integer mergerRowEnd;

    /**
     * 合并单元格，列开始
     */
    private Integer mergerColumnStart;

    /**
     * 合并单元格，列结束
     */
    private Integer mergerColumnEnd;

    /**
     * 增加批注
     */
    private String comment;

    /**
     * 字段长度
     */
    private String colLength;

    private String language;


    public AtrExcelCellVo(XSSFCellStyle hssfCellStyle, Integer row, Integer column, Object value) {
        this.hssfCellStyle = hssfCellStyle;
        this.row = row;
        this.column = column;
        this.value = value;

    }

    public AtrExcelCellVo(XSSFCellStyle hssfCellStyle, Integer row, Integer column, Object value, String columnCellType,
                          String comment, String colLength, String language, HorizontalAlignment alignment) {
        this.hssfCellStyle = hssfCellStyle;
        this.row = row;
        this.column = column;
        this.value = value;
        this.columnCellType = columnCellType;
        this.comment = comment;
        this.colLength = colLength;
        this.language = language;
        this.alignment = alignment;
    }

    public AtrExcelCellVo(XSSFCellStyle hssfCellStyle, Integer row, Integer column, Object value, Integer mergerRowStart, Integer mergerRowEnd, Integer mergerColumnStart, Integer mergerColumnEnd) {
        this.hssfCellStyle = hssfCellStyle;
        this.row = row;
        this.column = column;
        this.value = value;
        this.mergerRowStart = mergerRowStart;
        this.mergerRowEnd = mergerRowEnd;
        this.mergerColumnStart = mergerColumnStart;
        this.mergerColumnEnd = mergerColumnEnd;
    }

    public AtrExcelCellVo(XSSFCellStyle hssfCellStyle, Integer row, Integer column, Object value, String columnCellType,String comment,String colLength,String language) {
        this.hssfCellStyle = hssfCellStyle;
        this.row = row;
        this.column = column;
        this.value = value;
        this.columnCellType = columnCellType;
        this.comment = comment;
        this.colLength = colLength;
        this.language = language;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    public AtrExcelCellVo() {
    }

    public Integer getRow() {
        return row;
    }

    public void setRow(Integer row) {
        this.row = row;
    }

    public Integer getColumn() {
        return column;
    }

    public void setColumn(Integer column) {
        this.column = column;
    }

    public Integer getMergerRowStart() {
        return mergerRowStart;
    }

    public void setMergerRowStart(Integer mergerRowStart) {
        this.mergerRowStart = mergerRowStart;
    }

    public Integer getMergerRowEnd() {
        return mergerRowEnd;
    }

    public void setMergerRowEnd(Integer mergerRowEnd) {
        this.mergerRowEnd = mergerRowEnd;
    }

    public Integer getMergerColumnStart() {
        return mergerColumnStart;
    }

    public void setMergerColumnStart(Integer mergerColumnStart) {
        this.mergerColumnStart = mergerColumnStart;
    }

    public Integer getMergerColumnEnd() {
        return mergerColumnEnd;
    }

    public void setMergerColumnEnd(Integer mergerColumnEnd) {
        this.mergerColumnEnd = mergerColumnEnd;
    }

    public boolean isMergerCell() {
        if (ObjectUtils.isEmpty(mergerRowStart) || ObjectUtils.isEmpty(mergerRowEnd)
                || ObjectUtils.isEmpty(mergerColumnStart) || ObjectUtils.isEmpty(mergerColumnEnd)) {

            return false;
        } else {
            return true;
        }

    }

    public String getColumnCellType() {
        return columnCellType;
    }

    public void setColumnCellType(String columnCellType) {
        this.columnCellType = columnCellType;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getColLength() {
        return colLength;
    }

    public void setColLength(String colLength) {
        this.colLength = colLength;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public HorizontalAlignment getAlignment() {
        return alignment;
    }

    public void setAlignment(HorizontalAlignment alignment) {
        this.alignment = alignment;
    }
}
