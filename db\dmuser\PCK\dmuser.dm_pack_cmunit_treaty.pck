create or replace package dm_pack_cmunit_treaty is

PROCEDURE proc_cmunit_identify_all(p_entity_id NUMBER, p_year_month VARCHAR2);

PROCEDURE proc_majorrisk_test(p_entity_id NUMBER, p_year_month VARCHAR2);

PROCEDURE proc_majorrisk_test_nopass(p_entity_id NUMBER, p_year_month VARCHAR2);

PROCEDURE proc_approach_discern(p_entity_id NUMBER, p_year_month VARCHAR2);

PROCEDURE proc_portfolio_discern(p_entity_id NUMBER, p_year_month VARCHAR2);

PROCEDURE proc_profit_loss_discern(p_entity_id NUMBER, p_year_month VARCHAR2);

PROCEDURE proc_icg_group(p_entity_id NUMBER, p_year_month VARCHAR2);

PROCEDURE proc_investment_separate(p_entity_id NUMBER, p_year_month VARCHAR2);

PROCEDURE proc_contract_group_confirm(p_entity_id NUMBER, p_year_month VARCHAR2);

end dm_pack_cmunit_treaty;
/
create or replace package body dm_pack_cmunit_treaty is

PROCEDURE proc_cmunit_identify_all(p_entity_id NUMBER, p_year_month VARCHAR2)
 is

    cur_yearmonth  VARCHAR2(200);
BEGIN


    if p_year_month is null or length(p_year_month) = 0 then

        for cur_yearmonth in (select year_month
                              from dm_conf_bussperiod
                              where entity_id = p_entity_id
                                and period_state in ('0', '1', '2')
                              order by year_month)
            loop
               -- 合约分入
                dm_pack_cmunit_treaty_inward.proc_cmunit_identify(p_entity_id, cur_yearmonth.year_month);
                -- 合约分出
                dm_pack_cmunit_treaty_outward.proc_cmunit_identify(p_entity_id,  cur_yearmonth.year_month);

           end loop;
    else
      -- 合约分入
      dm_pack_cmunit_treaty_inward.proc_cmunit_identify(p_entity_id, p_year_month);
      -- 合约分出
      dm_pack_cmunit_treaty_outward.proc_cmunit_identify(p_entity_id, p_year_month );

    end if;

END proc_cmunit_identify_all;


PROCEDURE proc_majorrisk_test(p_entity_id NUMBER, p_year_month VARCHAR2)
 is

    v_proc_id INTEGER;
    cur_yearmonth   VARCHAR2(200);
    v_test_plan_type VARCHAR2(200);
    v_year_month VARCHAR2(200);
BEGIN

    -- 条件不满足,结束判定
    if p_entity_id is null OR p_year_month is null then
       return;
    end if;

    --- 限定只处理 处于处理中的业务年月
    SELECT year_month
      INTO v_year_month
      FROM dm_conf_bussperiod
     WHERE entity_id = p_entity_id
       AND year_month = p_year_month
       AND period_state = '2' -- 处理中
       AND valid_is = '1'
       AND rownum = 1;

      -- 条件不满足,结束判定
      if v_year_month is null then
          return;
      end if;

     proc_majorrisk_test(p_entity_id, p_year_month);

     proc_majorrisk_test(p_entity_id, p_year_month);

 EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE(SQLERRM);
END proc_majorrisk_test;

PROCEDURE proc_majorrisk_test_nopass(p_entity_id NUMBER, p_year_month VARCHAR2)
 is

    v_proc_id INTEGER;
    cur_yearmonth   VARCHAR2(200);
    v_test_plan_type VARCHAR2(200);
    v_year_month VARCHAR2(200);
BEGIN

    -- 条件不满足,结束判定
    if p_entity_id is null and p_year_month is null then
       return;
    end if;

    -- 限定只处理 处于处理中的业务年月
    select year_month into v_year_month
       from dm_conf_bussperiod
           where entity_id = p_entity_id
            and year_month = p_year_month
            and period_state = '2'  -- 处理中
            and valid_is = '1'
            and rownum=1;

      -- 条件不满足,结束判定
      if v_year_month is null then
          return;
      end if;

      proc_majorrisk_test_nopass(p_entity_id, p_year_month);

      proc_majorrisk_test_nopass(p_entity_id, p_year_month);

 EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE(SQLERRM);
END proc_majorrisk_test_nopass;

PROCEDURE proc_approach_discern(p_entity_id NUMBER, p_year_month VARCHAR2)
 is

    v_proc_id INTEGER;
    cur_yearmonth   VARCHAR2(200);
    v_test_plan_type VARCHAR2(200);
    v_year_month VARCHAR2(200);
BEGIN

  -- 条件不满足,结束判定
    if p_entity_id is null and p_year_month is null then
       return;
    end if;

      -- 限定只处理 处于处理中的业务年月
    select year_month into v_year_month
       from dm_conf_bussperiod
           where entity_id = p_entity_id
             and year_month = p_year_month
            and period_state = '2'  -- 处理中
            and valid_is = '1'
            and rownum=1;

      -- 条件不满足,结束判定
      if v_year_month is null then
          return;
      end if;

       proc_approach_discern(p_entity_id, p_year_month);

       proc_approach_discern(p_entity_id, p_year_month);

 EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE(SQLERRM);
END proc_approach_discern;

PROCEDURE proc_portfolio_discern(p_entity_id NUMBER, p_year_month VARCHAR2)
 is

    v_proc_id INTEGER;
    cur_yearmonth   VARCHAR2(200);
    v_test_plan_type VARCHAR2(200);
    v_year_month VARCHAR2(200);
BEGIN

   -- 条件不满足,结束判定
    if p_entity_id is null and p_year_month is null then
       return;
    end if;

    -- 限定只处理 处于处理中的业务年月
    select year_month into v_year_month
       from dm_conf_bussperiod
           where entity_id = p_entity_id
            and year_month = p_year_month
            and period_state = '2'  -- 处理中
            and valid_is = '1'
            and rownum=1;

      -- 条件不满足,结束判定
      if v_year_month is null then
          return;
      end if;

       proc_portfolio_discern(p_entity_id, p_year_month);

       proc_portfolio_discern(p_entity_id, p_year_month);

 EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE(SQLERRM);
END proc_portfolio_discern;


PROCEDURE proc_profit_loss_discern(p_entity_id NUMBER, p_year_month VARCHAR2)
 is

    v_proc_id INTEGER;
    cur_yearmonth   VARCHAR2(200);
    v_test_plan_type VARCHAR2(200);
    v_year_month VARCHAR2(200);
BEGIN

   -- 条件不满足,结束判定
    if p_entity_id is null and p_year_month is null then
       return;
    end if;

    -- 限定只处理 处于处理中的业务年月
    select year_month into v_year_month
       from dm_conf_bussperiod
           where entity_id = p_entity_id
            and year_month = p_year_month
            and period_state = '2'  -- 处理中
            and valid_is = '1'
            and rownum=1;

      -- 条件不满足,结束判定
      if v_year_month is null then
          return;
      end if;

      proc_profit_loss_discern(p_entity_id, p_year_month);

      proc_profit_loss_discern(p_entity_id, p_year_month);

 EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE(SQLERRM);
END proc_profit_loss_discern;

PROCEDURE proc_icg_group(p_entity_id NUMBER, p_year_month VARCHAR2)
 is

    v_proc_id INTEGER;
    cur_yearmonth   VARCHAR2(200);
    v_test_plan_type VARCHAR2(200);
    v_year_month VARCHAR2(200);
BEGIN

    -- 条件不满足,结束判定
    if p_entity_id is null and p_year_month is null then
       return;
    end if;

    -- 限定只处理 处于处理中的业务年月
    select year_month into v_year_month
       from dm_conf_bussperiod
           where entity_id = p_entity_id
            and year_month = p_year_month
            and period_state = '2'  -- 处理中
            and valid_is = '1'
            and rownum=1;

      -- 条件不满足,结束判定
      if v_year_month is null then
          return;
      end if;

       proc_icg_group(p_entity_id, p_year_month);

       proc_icg_group(p_entity_id, p_year_month);

 EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE(SQLERRM);
END proc_icg_group;


PROCEDURE proc_investment_separate(p_entity_id NUMBER, p_year_month VARCHAR2)
 is

    v_proc_id INTEGER;
    cur_yearmonth   VARCHAR2(200);
    v_test_plan_type VARCHAR2(200);
    v_year_month VARCHAR2(200);
BEGIN

   -- 条件不满足,结束判定
    if p_entity_id is null and p_year_month is null then
       return;
    end if;

    -- 限定只处理 处于处理中的业务年月
    select year_month into v_year_month
       from dm_conf_bussperiod
           where entity_id = p_entity_id
            and year_month = p_year_month
            and period_state = '2'  -- 处理中
            and valid_is = '1'
            and rownum=1;

      -- 条件不满足,结束判定
      if v_year_month is null then
          return;
      end if;

       proc_investment_separate(p_entity_id, p_year_month);

       proc_investment_separate(p_entity_id, p_year_month);

 EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE(SQLERRM);
END proc_investment_separate;

PROCEDURE proc_contract_group_confirm(p_entity_id NUMBER, p_year_month VARCHAR2)
 is

    v_proc_id INTEGER;
    cur_yearmonth   VARCHAR2(200);
    v_test_plan_type VARCHAR2(200);
    v_year_month VARCHAR2(200);
BEGIN

  -- 条件不满足,结束判定
    if p_entity_id is null and p_year_month is null then
       return;
    end if;

    -- 限定只处理 处于处理中的业务年月
    select year_month into v_year_month
       from dm_conf_bussperiod
           where entity_id = p_entity_id
            and year_month = p_year_month
            and period_state = '2' -- 处理中
            and valid_is = '1'
            and rownum=1;

      -- 条件不满足,结束判定
      if v_year_month is null then
          return;
      end if;

       proc_contract_group_confirm(p_entity_id, p_year_month);

       proc_contract_group_confirm(p_entity_id, p_year_month);

 EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE(SQLERRM);
END proc_contract_group_confirm;

end dm_pack_cmunit_treaty;
/
