package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.fo;

import com.ss.ifrs.actuarial.util.abp.IgnoreCol;
import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Data
@Tab("atr_buss_fo_lrc_g")
public class AtrBussLrcFoIcg {

    /** ID */
    private Long id;

    /** 执行编号 */
    private String actionNo;

    /** 业务单位ID */
    private Long entityId;

    /** 评估期年月 */
    private String yearMonth;

    /** 险类代码 */
    private String riskClassCode;

    /** 合同组合编号 */
    private String portfolioNo;

    /** 合同组号码 */
    private String icgNo;


    // 总保费
    private BigDecimal totalPremium;

    // 总手续费
    private BigDecimal totalNetFee;


    @IgnoreCol
    private int maxClmQuotaDevNo;

    @IgnoreCol
    private int remainingMonths;

    @IgnoreCol
    private Map<Integer, BigDecimal> devEdPremiumMap = new HashMap<>();

    @IgnoreCol
    private Map<Integer, BigDecimal> devRecvPremiumMap = new HashMap<>();

    @IgnoreCol
    private Map<Integer, BigDecimal> devNetFeeMap = new HashMap<>();
    
    /** 发展期已赚净额结算手续费 */
    @IgnoreCol
    private Map<Integer, BigDecimal> devEdNetFeeMap = new HashMap<>();
}
