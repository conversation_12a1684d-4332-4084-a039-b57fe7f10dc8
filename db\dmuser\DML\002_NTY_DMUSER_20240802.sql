--关闭标的占比有效性校验规则
update dm_conf_checkrule set valid_is = 0 where rule_code  = 'REINS_OUTWARD_DETAIL_RI_CEDING_RATE_VALID_IS';


--分保安排明细信息数据校验---分赔案的数据，也是需要校验保单号+批改序号在风险费用信息表中是否存在
UPDATE dm_conf_checkrule
SET rule_sql = 'select id from odsuser.ods_reins_outward_detail rfs
where not exists ( select 1 from dm_policy_premium p  where p.policy_no = rfs.policy_no and p.endorse_seq_no = rfs.endorse_seq_no ) '
WHERE rule_code = 'REINS_OUTWARD_DETAIL_DATA_CHECK';


--分保安排明细信息有效性校验---BCA没有D03的费用类型，根据claim_no进行判定调整。
UPDATE dm_conf_checkrule SET rule_sql = 'select id
  from odsuser.ods_reins_outward_detail t
 where claim_no = ''-''
   and not exists (select id
          from dm_reins_outward tro
         where t.entity_code = tro.entity_code
           and t.ri_policy_no = tro.ri_policy_no
           and t.ri_endorse_seq_no = tro.ri_endorse_seq_no)
   and t.task_code = ''#TASK_CODE#''
union all
select id
  from odsuser.ods_reins_outward_detail t
 where claim_no <> ''-''
   and not exists (select id
          from dm_reins_outward tro
         where t.entity_code = tro.entity_code
           and t.ri_policy_no = tro.ri_policy_no)
   and t.task_code = ''#TASK_CODE#''' WHERE rule_code = 'REINS_OUTWARD_DETAIL_DATA_VALID_IS';