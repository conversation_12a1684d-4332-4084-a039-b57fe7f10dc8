package com.ss.ifrs.actuarial.api;

import com.alibaba.fastjson.JSONObject;
import com.ss.ifrs.actuarial.pojo.atrcode.po.AtrConfCodeAdapter;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfCode;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfCodeVo;
import com.ss.ifrs.actuarial.service.AtrConfCodeService;
import com.ss.platform.core.model.Tree;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.library.constant.RestfulCodeConstant;
import com.ss.platform.core.constant.SystemConstant;
import com.ss.platform.util.*;
import com.ss.library.utils.DataUtil;
import com.ss.library.utils.ExceptionUtil;
import com.ss.library.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName: AtrConfCodeApi
 * @Description: 基础数据Api
 * @date 2018年3月27日 上午10:56:35
 */
@RestController
@RequestMapping("/gg_code")
@Api(value = "")
public class AtrConfCodeApi extends BaseApi {

    @Autowired
    AtrConfCodeService atrConfCodeService;

    /**
     * findBusinessListPageByCode(双击域根据配置Code查询业务表)
     *
     * @param params
     * @param _pageNo
     * @param _pageSize
     * @return Map<String, Object>
     * @Title: findBusinessListPageByCode
     * @Description: 双击域根据配置Code查询业务表
     * <AUTHOR>
     */
    @RequestMapping(value = "/find_business_list_page_by_code", method = RequestMethod.POST)
    @ResponseBody
    @PermissionRequest(required = false)
    public BaseResponse<Map<String, Object>> findBusinessListPageByCode(@RequestBody Map<String, Object> params,
                                                                        int _pageNo, int _pageSize) throws Exception {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Map<String, String> sqlParam = new HashMap<>(3);
        AtrConfCodeAdapter sysCodeConfig = atrConfCodeService.getSearchConfig(params.get("code").toString());
        String poName = sysCodeConfig.getPoName();
        sqlParam.put("tableName", poName);
        sqlParam.put("condition", DataUtil.appendCondition(sysCodeConfig.getSearchParams(),
                params.get("value").equals("") ? "" : params.get("value").toString(), params, poName, SystemConstant.AtrIdentity.POCLASSPATH));
        Page<?> page = atrConfCodeService.dynamicSql(sqlParam, pageParam);
        // 构造返回类型数据
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        resultMap.put("businessList", ClassUtil.convertOtherPage(page, ClassUtil.getClass(poName.replace("_", ""), SystemConstant.AtrIdentity.POCLASSPATH)));
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, resultMap);
    }

    /**
     * findOtherListPage(返回各表的列表(分页))
     *
     * @param params    key: poName </br>
     *                  value: Po对象类名(第一个字母小写 如:saaUserRole)</br>
     *                  key: 查询条件的属性名（po对象内含有 如:roleCode）</br>
     *                  value: 属性值</br>
     * @param _pageNo
     * @param _pageSize
     * @return Map<String, Object>
     * @throws Exception BaseResponse<Map<String,Object>> 返回类型
     * @Title: findOtherListPage
     * @Description: 返回各表的列表(分页)
     * <AUTHOR>
     */
    @RequestMapping(value = "/find_other_list_page", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<Map<String, Object>> findOtherListPage(@RequestBody Map<String, Object> params, int _pageNo,
                                                               int _pageSize) throws Exception {

        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Map<String, String> sqlParam = new HashMap<>(3);
        String poName = params.get("poName").toString();

        // 拼接SQL
        sqlParam.put("tableName", poName);
        sqlParam.put("condition", DataUtil.appendConition(params, poName, SystemConstant.AtrIdentity.POCLASSPATH));

        Page<?> page = atrConfCodeService.dynamicSql(sqlParam, pageParam);
        // 构造返回类型数据
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        resultMap.put("businessList", ClassUtil.convertOtherPage(page, ClassUtil.getClass(poName, SystemConstant.AtrIdentity.POCLASSPATH)));
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, resultMap);

    }

    /**
     * findOtherList(返回各表的列表(分页))
     *
     * @param request
     * @param params  key: poName </br>
     *                value: Po对象类名(第一个字母小写 如:saaUserRole)</br>
     *                key: 查询条件的属性名（po对象内含有 如:roleCode）</br>
     *                value: 属性值</br>
     * @return Map<String, Object>
     * @throws Exception BaseResponse<Map<String,Object>> 返回类型
     * @Title: findOtherList
     * @Description: 返回各表的列表(分页)
     * <AUTHOR>
     */
    @RequestMapping(value = "/find_other_list", method = RequestMethod.POST)
    @ResponseBody
    @PermissionRequest(required = false)
    public BaseResponse<Map<String, Object>> findOtherList(HttpServletRequest request,
                                                           @RequestBody Map<String, Object> params) throws Exception {
        // 构造返回类型数据
        Map<String, Object> resultMap = new HashMap<String, Object>(2);

        String poName = params.get("poName").toString();
        // 拼接SQL
        Map<String, String> sqlParam = new HashMap<>(3);
        sqlParam.put("tableName", poName);
        sqlParam.put("condition", DataUtil.appendConition(params, poName, SystemConstant.AtrIdentity.POCLASSPATH));

        List<?> businessList = atrConfCodeService.dynamicSql(sqlParam);

        resultMap.put("businessList", ClassUtil.convertOtherList(businessList, ClassUtil.getClass(poName.replace("_", ""), SystemConstant.AtrIdentity.POCLASSPATH)));
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, resultMap);
    }

    /**
     * 查询多个配置
     *
     * @return Map<String, Object>
     * @throws Exception BaseResponse<Map<String,Object>> 返回类型
     * @Title: findOtherList
     * @Description:
     * <AUTHOR>
     */
    @RequestMapping(value = "/find_other_list_mult", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<Map<String, Object>> findOtherListMult(HttpServletRequest request, @RequestBody JSONObject params) throws Exception {
        Map<String, Object> resultMap = new HashMap<>(16);
        if (params != null) {
            Iterator iter = params.entrySet().iterator();
            while (iter.hasNext()) {
                Map.Entry entry = (Map.Entry) iter.next();
                String key = entry.getKey().toString();
                Map<String, Object> value = (Map<String, Object>) entry.getValue();
                BaseResponse<Map<String, Object>> res = this.findOtherList(request, value);
                if (res != null && res.getResData() != null && res.getResData().get("businessList") != null) {
                    resultMap.put(key, res.getResData().get("businessList"));
                }
            }
        }
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, resultMap);
    }

    @RequestMapping(value = "/find_list", method = RequestMethod.POST)
    @ResponseBody
    @PermissionRequest(required = false)
    public BaseResponse<Map<String, Object>> findsysCodeListV2(HttpServletRequest request,
                                                               @RequestBody AtrConfCodeVo sysCodeVo) {
        Map<String, Object> result = atrConfCodeService.findListV2(sysCodeVo);
        Map<String, Object> map = new HashMap<String, Object>(2);
        map.put("codeCodeVoList", result);
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, map);
    }

    /**
     * sysCodePage(分页查询sysCode列表)
     *
     * @param request
     * @param sysCodeVo
     * @param _pageNo
     * @param _pageSize
     * @return BaseResponse<Map < String, Object>> 返回类型
     * @Title: sysCodePage
     * @Description: 分页查询sysCode列表
     * <AUTHOR>
     */
    @ApiOperation(value = "分页查询所有功能信息")
    @RequestMapping(value = "/code_item/index/enquiry", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<Map<String, Object>> sysCodePage(HttpServletRequest request,
                                                         @RequestBody AtrConfCodeVo sysCodeVo, int _pageNo, int _pageSize) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrConfCodeVo> result = atrConfCodeService.findSysCodePage(sysCodeVo, pageParam);
        Map<String, Object> map = new HashMap<String, Object>(2);
        map.put("codeCodeVoList", result);
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, map);
    }

    /**
     * findListValid(分页查找有效的sysCode的列表)
     *
     * @param request
     * @param sysCodeVo
     * @param _pageNo
     * @param _pageSize
     * @return BaseResponse<Map < String, Object>> 返回类型
     * @Title: findListValid
     * @Description: 分页查找有效的sysCode的列表
     * <AUTHOR>
     */
    @RequestMapping(value = "/find_list_valid_dbclick", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<Map<String, Object>> findListForDbClick(HttpServletRequest request,
                                                                @RequestBody AtrConfCodeVo sysCodeVo, int _pageNo, int _pageSize) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrConfCodeVo> result = atrConfCodeService.findListValid(sysCodeVo, pageParam);
        Map<String, Object> map = new HashMap<String, Object>(2);
        map.put("codeCodeVoList", result);
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, map);
    }

    @RequestMapping(value = "/find_list_valid_auto_complete", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<Map<String, Object>> findListForAutoComplete(HttpServletRequest request,
                                                                     @RequestBody AtrConfCodeVo sysCodeVo, int _pageNo, int _pageSize) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrConfCodeVo> result = atrConfCodeService.findListForAutoComplete(sysCodeVo, pageParam);
        Map<String, Object> map = new HashMap<String, Object>(2);
        map.put("businessList", result);
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, map);
    }

    /**
     * findByPrimary(根据主键查找sysCode数据)
     *
     * @param request
     * @param codeId
     * @return BaseResponse<sysCodeVo> 返回类型
     * @Title: findByPrimary
     * @Description: 根据主键查找sysCode数据
     * <AUTHOR>
     */
    @RequestMapping(value = "/code_item/index/find_by_pk/{codeId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<AtrConfCodeVo> findByPrimary(HttpServletRequest request, @PathVariable Long codeId) {
        AtrConfCodeVo result = atrConfCodeService.findAtrCodeByPk(codeId);
        return new BaseResponse<AtrConfCodeVo>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    /**
     * deleteByPrimary(禁用或激活sysCode数据)
     *
     * @param request
     * @param confCodeVo
     * @return BaseResponse<Object> 返回类型
     * @Title: deleteByPrimary
     * @Description: 禁用或激活sysCode数据
     * <AUTHOR>
     */
    @RequestMapping(value = "/valid_status", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<Object> validStatusSysConfCode(HttpServletRequest request,
                                                       @RequestBody AtrConfCodeVo confCodeVo) {

        Long userId = this.loginUserId(request);
        confCodeVo.setUpdatorId(userId);
        try {
            String resMsg = atrConfCodeService.disableAtrConfCode(confCodeVo);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, RestfulCodeConstant.RestfulCode.SUCCESS);
        } catch (Exception ex) {
            String message = ExceptionUtil.getMessage(ex);
            logger.error(ex.getLocalizedMessage(), ex);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, message,
                    RestfulCodeConstant.RestfulCode.INTERNAL_SERVER_ERROR);
        }

    }

    @ApiOperation(value = "校验UpperCode是否存在")
    @RequestMapping(value = "/validate_code", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Map<String, String>> validateCode(@RequestBody String codeCode) {
        String existFlag = "1";
        if (StringUtil.isNotEmpty(codeCode)) {
            AtrConfCodeVo upperCode = atrConfCodeService.findByCodeCode(codeCode);
            if (upperCode != null && upperCode.getCodeCode() != null) {
                existFlag = "0";
            }
        }
        Map<String, String> result = new HashMap<>();
        result.put("existFlag", existFlag);
        return new BaseResponse<Map<String, String>>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @RequestMapping(value = "/enquiry", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<Map<String, Object>> enquirySysConfCodePage(@RequestBody AtrConfCodeVo confCodeVo,
                                                                    int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<Tree<AtrConfCodeVo>> upperCodeVoList = atrConfCodeService.searchAtrConfCodePage(confCodeVo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("atrCodeVoTree", upperCodeVoList);
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<Object> addUpperCode(HttpServletRequest request,
                                             @RequestBody @Validated AtrConfCodeVo upperCodeVo, BindingResult br) {

        if (br.hasErrors()) {
            // 该状态码为“其它格式转换错误”
            StringBuffer errorMessages = new StringBuffer();
            for (int i = 0; i < br.getErrorCount(); i++) {
                errorMessages.append(br.getAllErrors().get(i).getDefaultMessage() + ";");
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.FORMAT_TRANSFORM_ERROR,new CheckParamsUtil().getMessages(errorMessages.toString()));
        }
        Long userId = this.loginUserId(request);
        upperCodeVo.setCreatorId(userId);
        upperCodeVo.setCreateTime(new Date());
        try {
            atrConfCodeService.saveAtrUpperCode(upperCodeVo);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (UnexpectedRollbackException e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.OTHER_ERROR, null);
        }
    }

    @RequestMapping(value = "/find_by_pk/{codeId}", method = RequestMethod.GET)
    @ResponseBody
    @PermissionRequest(required = false)
    public BaseResponse<AtrConfCodeVo> findByUpperCodeId(@PathVariable Long codeId) {
        AtrConfCodeVo upperCodeVo = atrConfCodeService.findByUpperCodeId(codeId);
        return new BaseResponse<AtrConfCodeVo>(ResCodeConstant.ResCode.SUCCESS, upperCodeVo);
    }

    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<Object> editUpperCode(HttpServletRequest request,
                                              @RequestBody AtrConfCodeVo confUpperCodeVo) {

        Long userId = this.loginUserId(request);
        confUpperCodeVo.setUpdatorId(userId);
        confUpperCodeVo.setUpdateTime(new Date());
        try {
            atrConfCodeService.updateAtrUpperCode(confUpperCodeVo);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (UnexpectedRollbackException e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.OTHER_ERROR, null);
        }
    }

    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<Object> delete(HttpServletRequest request, @RequestBody AtrConfCodeVo confCodeVo) {
        try {
            atrConfCodeService.delete(confCodeVo);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (UnexpectedRollbackException e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.OTHER_ERROR, null);
        }
    }

    /**
     * saveOrUpdateCode(更新sysCode数据)
     *
     * @param request
     * @param sysCodeAddReqVo
     * @return BaseResponse<Object> 返回类型
     * @Title: saveOrUpdateCode
     * @Description: 更新sysCode数据
     * <AUTHOR>
     */
    @RequestMapping(value = "/code_item/index/add", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<Object> saveAtrCode(HttpServletRequest request,
                                            @RequestBody @Validated AtrConfCodeVo sysCodeAddReqVo, BindingResult br) {

        if (br.hasErrors()) {
            // 该状态码为“其它格式转换错误”
            StringBuffer errorMessages = new StringBuffer();
            for (int i = 0; i < br.getErrorCount(); i++) {
                errorMessages.append(br.getAllErrors().get(i).getDefaultMessage() + ";");
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.FORMAT_TRANSFORM_ERROR,new CheckParamsUtil().getMessages(errorMessages.toString()));
        }
        Long userId = this.loginUserId(request);
        sysCodeAddReqVo.setCreatorId(userId);
        try {
            atrConfCodeService.saveAtrCode(sysCodeAddReqVo);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (UnexpectedRollbackException e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.OTHER_ERROR, null);
        }

    }

    @RequestMapping(value = "/code_item/index/edit", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<Object> editAtrCode(HttpServletRequest request,
                                            @RequestBody @Validated
                                                    AtrConfCodeVo sysCodeUpdateReqVo, BindingResult br) {
        if (br.hasErrors()) {
            // 该状态码为“其它格式转换错误”
            StringBuffer errorMessages = new StringBuffer();
            for (int i = 0; i < br.getErrorCount(); i++) {
                errorMessages.append(br.getAllErrors().get(i).getDefaultMessage() + ";");
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.FORMAT_TRANSFORM_ERROR,new CheckParamsUtil().getMessages(errorMessages.toString()));
        }
        Long userId = this.loginUserId(request);
        sysCodeUpdateReqVo.setUpdatorId(userId);
        try {
            atrConfCodeService.updateAtrCode(sysCodeUpdateReqVo);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (UnexpectedRollbackException e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.OTHER_ERROR, null);
        }

    }

    @ApiOperation(value = "校验codeCode是否存在")
    @RequestMapping(value = "/code_item/index/validate_code", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Map<String, String>> validateCodeCode(@RequestBody AtrConfCodeVo bplCodeVo) {
        String existFlag = "1";
        List<AtrConfCode> bplCodeVoLists = atrConfCodeService.findList(bplCodeVo);
        if (bplCodeVoLists.isEmpty()) {
            existFlag = "0";
        }
        Map<String, String> result = new HashMap<>();
        result.put("existFlag", existFlag);
        return new BaseResponse<Map<String, String>>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    /**
     * getTree(根据表名查找数据)
     *
     * @param request
     * @param params
     * @return BaseResponse<Map < String, Object>> 返回类型
     * @Title: getTree
     * @Description: 根据表名查找数据
     * <AUTHOR>
     */
    @RequestMapping(value = "/get_tree", method = RequestMethod.POST)
    @ResponseBody
    public BaseResponse<Map<String, Object>> getTree(HttpServletRequest request,
                                                     @RequestBody Map<String, String> params) {
        String json = atrConfCodeService.getTree(params);
        Map<String, Object> result = new HashMap<String, Object>(2);
        result.put("result", json);
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, result);
    }


    @ApiOperation(value = "根据codeCodeIdx查询有效的数据")
    @RequestMapping(value = "/find_code_by_codeIdx", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findByCodeIdx(@RequestBody AtrConfCodeVo atrCodeVo) {
        List<AtrConfCodeVo> list = atrConfCodeService.findCodeByCodeIdx(atrCodeVo);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, list);

    }
}
