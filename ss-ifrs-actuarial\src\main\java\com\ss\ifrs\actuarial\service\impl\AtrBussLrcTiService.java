package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.AtrBussRPGKey;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.ti.*;
import com.ss.ifrs.actuarial.util.Dates;
import com.ss.ifrs.actuarial.util.EcfUtil;
import com.ss.ifrs.actuarial.util.IndexFactory;
import com.ss.ifrs.actuarial.util.ThreadUtil;
import com.ss.ifrs.actuarial.util.abp.AsyncBatchProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 预期保费现金流 service （合约分入业务）
 *
 * <AUTHOR>
 */
@Service
@Scope("prototype")
@Slf4j
public class AtrBussLrcTiService extends AbstractAtrBussLrcService {

    private static final String Q_LAPSE_RATIO = EcfUtil.Q_LAPSE_RATIO;
    private static final String Q_CLAIM_SETTLED_PATTERN = EcfUtil.Q_LRC_CLAIM_SETTLED_PATTERN;
    private static final String Q_CLAIM_RATIO = EcfUtil.Q_LRC_CLAIM_RATIO;
    private static final String Q_MT_RATIO = EcfUtil.Q_LRC_MT_RATIO;
    private static final String Q_ULAE_RATIO = EcfUtil.Q_LRC_ULAE_RATIO;
    private static final String Q_RA_RATIO = EcfUtil.Q_LRC_RA_RATIO;

    private final Map<AtrBussRPGKey, AtrBussLrcTiIcg> icgMap = new ConcurrentHashMap<>();

    private final IndexFactory<BigDecimal> curPaidPremiumIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> curPaidNetFeeIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> curPaidIacfIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> prePaidPremiumIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> prePaidNetFeeIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> prePaidIacfIndex = new IndexFactory<>();

    // 总的对内、对外跟单获取费用
    private final IndexFactory<BigDecimal> allExpAllocInIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> allExpAllocOutIndex = new IndexFactory<>();

    // 对内非跟单获取费用
    private final IndexFactory<BigDecimal> curExpAllocInIndex = new IndexFactory<>();
    // 对外非跟单获取费用
    private final IndexFactory<BigDecimal> curExpAllocOutIndex = new IndexFactory<>();

    private final IndexFactory<BigDecimal> curPaidBadDebtIndex = new IndexFactory<>();
    private final IndexFactory<BigDecimal> prePaidBadDebtIndex = new IndexFactory<>();
    private final IndexFactory<AtrDapLrcTiPaid> allPaidPremiumIndex = new IndexFactory<>();
    private final IndexFactory<AtrDapLrcTiPayPlan> payPlanIndex = new IndexFactory<>();
    private final IndexFactory<AtrDapLrcTiEdPlan> edPlanIndex = new IndexFactory<>();
    private final IndexFactory<AtrDuctLrcTiIcuPre> preIcuIndex = new IndexFactory<>();
    // 统计新旧保费，index 0 为旧保费,index 1 为新保费
    private final Map<List<String>, BigDecimal[]> icgPremiumIndex = new HashMap<>();

    // 添加一个新的存储结构，用于存储当前评估期的实际发展期已赚保费（从0开始）
    private final IndexFactory<AtrDapLrcTiEdPlan> currentEdPlanDevIndex = new IndexFactory<>();

    private final Map<List<String>, String> riskKindCodeIndex = new HashMap<>();

    // 缓存默认的基础实体信息，避免在循环中重复查询数据库
    private AtrBussBaseEntity defaultBaseEntity;


    public void entry(String actionNo, Long entityId, String yearMonth) {
        initEnvParams(actionNo, entityId, yearMonth, "TI");
        try (AsyncBatchProcessor abp = createAsyncBatchProcessor()) {
            initAbp(abp);
            logDebug("collectData");
            collectData();
            logDebug("calcIcu");
            calcIcu();
            logDebug("calcIcg");
            calcIcg();
            logDebug("saveIcgPremium");
            saveIcgPremium();
            logDebug("abp-end");
            abp.end();
            logDebug("end");
        } catch (Exception e) {
            logError(e);
            throw new RuntimeException(e);
        }
    }

    private void calcIcu() {
        List<AtrBussLrcTiIcu> vos = atrBussLrcTiDao.findInitIcu(yearMonth);
        vos.forEach(this::calcIcu);
    }

    private void calcIcu(AtrBussLrcTiIcu icu) {
        long mainId = icuMainIdGen.incrementAndGet();
        icu.setId(mainId);
        String treatyNo = icu.getTreatyNo();
        String riskClassCode = icu.getRiskClassCode();
        // 已赚保费
        List<?> unitKey = createUnitKey(treatyNo, riskClassCode);
        List<AtrDapLrcTiEdPlan> edPlanVos = edPlanIndex.list(unitKey);
        boolean writtern = !edPlanVos.isEmpty();

        // 预估账单
        List<AtrDapLrcTiPayPlan> planVos = payPlanIndex.list(unitKey);

        if (!planVos.isEmpty()) {
            String riskCode = planVos.get(0).getRiskCode();
            icu.setRiskCode(riskCode);
            String accRiskCode = riskKindCodeIndex.get(Collections.singletonList(riskCode));
            // 设置财务产品段
            icu.setFinProductCode(accRiskCode);
        }

        // 不存在已写入的情况，使用默认的财务部门
        if (!writtern) {
            // 使用预先缓存的默认基础实体信息，避免重复查询数据库
            icu.setDeptId(defaultBaseEntity.getFdsMappingCode());
            icu.setCenterCode(defaultBaseEntity.getFcsMappingCode());
        }
        List<?> unitKeyCalc = createUnitKey(icu);
        // 转换费率相关的数值为BigDecimal，并使用适当的精度
        BigDecimal nefFeeRate = nvl(icu.getPrepaidFeeRate());
        if (nefFeeRate.compareTo(BigDecimal.ZERO) == 0) {
            nefFeeRate = nvl(icu.getFixedFeeRate());
        }
        nefFeeRate = roundR(nefFeeRate); // 利率保留15位小数

        // 计算经纪费率：从实付数据中计算 net_fee/premium
        BigDecimal brokerageRate = BigDecimal.ZERO;
        List<AtrDapLrcTiPaid> allPaidPremVos = allPaidPremiumIndex.list(unitKeyCalc);
        if (!allPaidPremVos.isEmpty()) {
            // 汇总所有实付数据的net_fee和premium
            BigDecimal totalNetFee = sumBigDecimal(allPaidPremVos, vo -> nvl(vo.getNetFee()));
            BigDecimal totalPremium = sumBigDecimal(allPaidPremVos, vo -> nvl(vo.getPremium()));

            // 计算经纪费率 = net_fee / premium，保留15位小数
            if (totalPremium.compareTo(BigDecimal.ZERO) != 0) {
                brokerageRate = totalNetFee.divide(totalPremium, 15, java.math.RoundingMode.HALF_UP);
            }
        }


        BigDecimal preBadDebt = prePaidBadDebtIndex.one(unitKeyCalc, BigDecimal.ZERO);

        BigDecimal iaehcIn = curExpAllocInIndex.one(unitKeyCalc, BigDecimal.ZERO);
        BigDecimal iaehcOut = curExpAllocOutIndex.one(unitKeyCalc, BigDecimal.ZERO);



        // 当前评估期实际发展期已赚保费数据 - 从0期开始的发展期
        List<AtrDapLrcTiEdPlan> currentEdPlanDevVos = currentEdPlanDevIndex.list(unitKey);

        // 进行数据校验
        validateIcuData(icu, treatyNo, riskClassCode, writtern, planVos, allPaidPremVos, edPlanVos, currentEdPlanDevVos);

        // ed pattern (发展期与评估期无关）- 使用历史已赚数据计算
        BigDecimal sumEdPrem = sumBigDecimal(edPlanVos, vo -> nvl(vo.getPremium()));
        sumEdPrem = round(sumEdPrem); // 保留8位小数

        List<BigDecimal> edPattern = new ArrayList<>();

        // 使用历史已赚保费数据来计算发展期的最大值
        int edPatternMaxDevNo = edPlanVos.isEmpty() ? 0 :
                edPlanVos.stream()
                        .map(AtrDapLrcTiEdPlan::getDevNo)
                        .max(Integer::compare)
                        .orElse(0);

        for (int i = 0; i <= edPatternMaxDevNo; i++) {
            int devNo = i;
            edPattern.add(BigDecimal.ZERO);
            if (sumEdPrem.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal devEdPrem = edPlanVos.stream()
                        .filter(t -> t.getDevNo() == devNo)
                        .map(t -> nvl(t.getPremium()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                // 计算比例并保留15位小数（利率）
                BigDecimal ratio = devEdPrem.divide(sumEdPrem, 15, java.math.RoundingMode.HALF_UP);
                edPattern.set(i, roundR(ratio));
            }
        }

        // 以下是edPremWList的声明和初始化 - 使用当前评估期实际发展期已赚数据
        List<BigDecimal> edPremWList = new ArrayList<>();
        for (int i = 0; i <= edPatternMaxDevNo; i++) {
            int finalI = i;
            BigDecimal val = currentEdPlanDevVos.stream()
                    .filter(t -> t.getDevNo() == finalI)
                    .map(t -> nvl(t.getPremium()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            edPremWList.add(round(val)); // 保留8位小数
        }

        // 计算最大发展期序号
        // 1、 最少 1 期
        // 2、 如果有实际帐，预估账单最大期次 + 已赚保费的期次 + 1
        // 3、 如果没有实际帐，预估账单最大期次 + 13
        int maxDevNo = 1;
        //预估账单最大发展期
        int maxDev = planVos.stream().map(AtrDapLrcTiPayPlan::getDevNo).max(Integer::compare).orElse(0);

        // 检查是否有实际账（实收保费数据）
        boolean hasActualAccount = !allPaidPremVos.isEmpty();

        // 计算已赚保费的最大期次
        int edPlanMaxDevNo = edPlanVos.isEmpty() ? 0 : edPlanVos.stream()
                .map(AtrDapLrcTiEdPlan::getDevNo)
                .max(Integer::compare)
                .orElse(0);

        // 根据规则计算最终的最大发展期序号
        if (hasActualAccount) {
            // 如果有实际帐，预估账单最大期次 + 已赚保费的期次 + 1
            maxDevNo = Math.max(maxDevNo, maxDev + edPlanMaxDevNo + 1);
        } else {
            // 如果没有实际帐，预估账单最大期次 + 13
            maxDevNo = Math.max(maxDevNo, maxDev + 13);
        }
        BigDecimal recvPremiumSum = BigDecimal.ZERO;
        List<AtrBussLrcTiIcuDev> devs = new ArrayList<>();
        for (int i = 0; i < maxDevNo; i++) {
            AtrBussLrcTiIcuDev dev = new AtrBussLrcTiIcuDev();
            devs.add(dev);
            dev.setMainId(mainId);
            dev.setDevNo(i);
            dev.setYearMonth(yearMonth);
            // 保费现金流
            if (i == 0) {
                dev.setRecvPremium(curPaidPremiumIndex.one(unitKey));
            } else {
                for (AtrDapLrcTiPayPlan planVo : planVos) {
                    if (planVo.getDevNo() == i) {
                        dev.setRecvPremium(planVo.getPremium());
                    }
                }
            }
            recvPremiumSum = recvPremiumSum.add(nvl(dev.getRecvPremium()));
            // 已赚保费现金流
            // 预估帐算的已赚
            BigDecimal edPremiumE = BigDecimal.ZERO;
            if (writtern) {
                // 有写入的场景
                // 0 期固定为写入的已赚
                // 其他期次为 写入的 + 预估帐 * 已赚模式
                // 写入的已赚
                BigDecimal edPremiumW = getBigDecimal(edPremWList, i);
                if (i == 0) {
                    dev.setEdPremium(edPremiumW);
                } else {
                    for (int k = 1; k <= i; k++) {
                        AtrBussLrcTiIcuDev devRecv = devs.get(k);
                        BigDecimal recvPremium = devRecv.getRecvPremium();
                        if (recvPremium != null && !recvPremium.equals(BigDecimal.ZERO)) {
                            int offset = i - k;
                            if (edPattern.size() > offset) {
                                BigDecimal patternValue = edPattern.get(offset);
                                BigDecimal increment = recvPremium.multiply(patternValue);
                                edPremiumE = edPremiumE.add(increment);
                                edPremiumE = round(edPremiumE); // 保留8位小数
                            }
                        }
                    }
                    BigDecimal total = edPremiumW.add(edPremiumE);
                    dev.setEdPremium(round(total));
                }
            } else {
                // 没有写入的场景
                // 未来期次的应收按二十四分之一法延展
                for (int k = 1; k <= i; k++) {
                    AtrBussLrcTiIcuDev devRecv = devs.get(k);
                    BigDecimal recvPremium = devRecv.getRecvPremium();
                    if (recvPremium != null && recvPremium.compareTo(BigDecimal.ZERO) != 0) {
                        int offset = i - k;
                        if (offset == 0 || offset == 12) {
                            BigDecimal increment = recvPremium.divide(BigDecimal.valueOf(24), 15, java.math.RoundingMode.HALF_UP);
                            edPremiumE = edPremiumE.add(increment);
                        } else if (offset < 12) {
                            BigDecimal increment = recvPremium.divide(BigDecimal.valueOf(12), 15, java.math.RoundingMode.HALF_UP);
                            edPremiumE = edPremiumE.add(increment);
                        }
                    }
                }
                dev.setEdPremium(round(edPremiumE));
            }

            // 计算ICG保费，传递BigDecimal参数
            calcIcgPremium(icu.getPortfolioNo(), icu.getIcgNo(), icu.getRiskClassCode(),
                    icu.getContractDate(), recvPremiumSum);

            // 已赚净额
            BigDecimal edNetFeeW = curPaidNetFeeIndex.one(unitKey, BigDecimal.ZERO).multiply(getBigDecimal(edPattern, i));
            BigDecimal edNetFeeE = edPremiumE.multiply(nefFeeRate);
            dev.setEdNetFee(round(edNetFeeW.add(edNetFeeE)));

            // 已赚iacf
            BigDecimal edIacfW = curPaidIacfIndex.one(unitKey, BigDecimal.ZERO).multiply(getBigDecimal(edPattern, i));
            BigDecimal edIacfE = edPremiumE.multiply(brokerageRate);
            dev.setEdIacf(round(edIacfW.add(edIacfE)));

            // 减值现金流
            if (i == 0) {
                dev.setBadDebt(curPaidBadDebtIndex.one(unitKey));
            }

            // 净额
            if (i == 0) {
                dev.setNetFee(curPaidNetFeeIndex.one(unitKey));
            } else {
                dev.setNetFee(round(nvl(dev.getRecvPremium()).multiply(nefFeeRate)));
            }

            // iacf
            if (i == 0) {
                dev.setIacf(curPaidIacfIndex.one(unitKey));
            } else {
                dev.setIacf(round(nvl(dev.getRecvPremium()).multiply(brokerageRate)));
            }

            // iaehcIn/iaehcOut
            if (i == 0) {
                dev.setIaehcIn(iaehcIn);
                dev.setIaehcOut(iaehcOut);
            }
        }

        // 计算投资成分
        BigDecimal floatingHandlingFeeCap = nvl(icu.getFloatingHandlingFeeCap());
        BigDecimal prepaidFeeRate = nvl(icu.getPrepaidFeeRate());
        BigDecimal investmentRate = floatingHandlingFeeCap.subtract(prepaidFeeRate);
        BigDecimal edPremium = devs.get(0).getEdPremium();
        icu.setInvAmount(investmentRate.multiply(edPremium));

        // 已赚非跟单
        if (!iaehcIn.equals(BigDecimal.ZERO)) {
            if (writtern) {
                for (int i = 0; i < devs.size(); i++) {
                    AtrBussLrcTiIcuDev dev = devs.get(i);
                    if (edPattern.size() > i) {
                        BigDecimal patternValue = edPattern.get(i);
                        dev.setEdIaehcIn(round(iaehcIn.multiply(patternValue)));
                        dev.setEdIaehcOut(round(iaehcOut.multiply(patternValue)));
                    }
                }
            } else {
                // 计算总保费
                BigDecimal totalRecvPrem = sumBigDecimal(devs, dev ->
                        dev.getRecvPremium() != null ? dev.getRecvPremium() : BigDecimal.ZERO);
                totalRecvPrem = round(totalRecvPrem);

                if (!totalRecvPrem.equals(BigDecimal.ZERO)) {
                    for (AtrBussLrcTiIcuDev dev : devs) {
                        if (dev.getRecvPremium() != null) {
                            BigDecimal recvPremium = dev.getRecvPremium();
                            BigDecimal ratio = recvPremium.divide(totalRecvPrem, 15, java.math.RoundingMode.HALF_UP);

                            dev.setEdIaehcIn(round(iaehcIn.multiply(ratio)));
                            dev.setEdIaehcOut(round(iaehcOut.multiply(ratio)));
                        }
                    }
                }
            }
        }

        AtrDuctLrcTiIcuPre preIcu = preIcuIndex.one(unitKey, new AtrDuctLrcTiIcuPre());

        BigDecimal prePaidPremium = prePaidPremiumIndex.one(unitKey, BigDecimal.ZERO);
        BigDecimal curPaidPremium = curPaidPremiumIndex.one(unitKey, BigDecimal.ZERO);
        BigDecimal epiPremium = sumBigDecimal(planVos, vo -> nvl(vo.getPremium()));

        // 计算总保费
        BigDecimal totalPremium = prePaidPremium.add(curPaidPremium).add(epiPremium);
        totalPremium = round(totalPremium); // 保留8位小数

        icu.setActionNo(actionNo);
        icu.setEntityId(entityId);
        icu.setYearMonth(yearMonth);
        icu.setPremium(totalPremium);
        icu.setNetFeeRate(nefFeeRate);

        // 计算总坏账
        BigDecimal totalBadDebt = preBadDebt;
        if (devs.get(0).getBadDebt() != null) {
            totalBadDebt = totalBadDebt.add(devs.get(0).getBadDebt());
        }
        icu.setTotalBadDebt(totalBadDebt);

        // 计算净额和手续费
        BigDecimal premium = icu.getPremium();
        icu.setNetFee(round(premium.multiply(nefFeeRate)));
        icu.setIacf(round(premium.multiply(brokerageRate)));

        icu.setIaehcIn(iaehcIn);
        icu.setIaehcOut(iaehcOut);

        // 设置累计值
        icu.setPreCumlPaidPremium(prePaidPremium);
        icu.setPreCumlPaidNetFee(prePaidNetFeeIndex.one(unitKey, BigDecimal.ZERO));
        icu.setPreCumlPaidIacf(prePaidIacfIndex.one(unitKey, BigDecimal.ZERO));
        icu.setPreCumlEdPremium(nvl(preIcu.getPreCumlEdPremium()).add(nvl(preIcu.getCurEdPremium())));
        icu.setPreCumlEdNetFee(nvl(preIcu.getPreCumlEdNetFee()).add(nvl(preIcu.getCurEdNetFee())));
        icu.setPreCumlEdIacf(nvl(preIcu.getPreCumlEdIacf()).add(nvl(preIcu.getCurEdIacf())));
        icu.setPreCumlEdIaehcIn(nvl(preIcu.getPreCumlEdIaehcIn()).add(nvl(preIcu.getCurEdIaehcIn())));
        icu.setPreCumlEdIaehcOut(nvl(preIcu.getPreCumlEdIaehcOut()).add(nvl(preIcu.getCurEdIaehcOut())));
        icu.setCurEdPremium(nvl(devs.get(0).getEdPremium()));
        icu.setCurEdNetFee(nvl(devs.get(0).getEdNetFee()));
        icu.setCurEdIacf(nvl(devs.get(0).getEdIacf()));
        icu.setCurEdIaehcIn(nvl(devs.get(0).getIaehcIn()));
        icu.setCurEdIaehcOut(nvl(devs.get(0).getIaehcOut()));
        // 处理尾差
        handleAmountTailDifference(devs, premium, icu.getNetFee(), icu.getIacf(), iaehcIn, iaehcOut,
                nvl(preIcu.getPreCumlEdPremium()).add(nvl(preIcu.getCurEdPremium())),
                nvl(preIcu.getPreCumlEdNetFee()).add(nvl(preIcu.getCurEdNetFee())),
                nvl(preIcu.getPreCumlEdIacf()).add(nvl(preIcu.getCurEdIacf())));

        // 重新计算总已赚保费（处理尾差后）
        BigDecimal edPremiumSum = sumBigDecimal(devs, dev -> nvl(dev.getEdPremium()));

        // 执行一致性校验
        validateIcuConsistency(
                treatyNo,
                riskClassCode,
                premium,
                prePaidPremium,
                curPaidPremium,
                epiPremium,
                edPremiumSum,
                preIcu,
                devs,
                prePaidNetFeeIndex.one(unitKey, BigDecimal.ZERO),
                prePaidIacfIndex.one(unitKey, BigDecimal.ZERO),
                iaehcIn,
                iaehcOut
        );

        abp.insert(icu);
        devs.forEach(abp::insert);

        collectIcg(icu, devs);
    }

    /**
     * 处理各种金额的尾差
     *
     * @param devVos           发展期列表
     * @param premium          保费
     * @param netFee           净额结算手续费
     * @param iacf             跟单获取费用
     * @param iaehcIn          非跟单获取费用-对内
     * @param iaehcOut         非跟单获取费用-对外
     * @param preCumlEdPremium 上期累计已赚保费
     * @param preCumlEdNetFee  上期累计已赚净额结算手续费
     * @param preCumlEdIacf    上期累计已赚跟单获取费用
     */
    private void handleAmountTailDifference(List<AtrBussLrcTiIcuDev> devVos,
                                            BigDecimal premium, BigDecimal netFee, BigDecimal iacf,
                                            BigDecimal iaehcIn, BigDecimal iaehcOut,
                                            BigDecimal preCumlEdPremium, BigDecimal preCumlEdNetFee,
                                            BigDecimal preCumlEdIacf) {
        // 已赚保费尾差处理
        AtrBussLrcTiIcuDev targetDevVo = null;
        BigDecimal maxEdPremium = BigDecimal.ZERO;

        for (AtrBussLrcTiIcuDev dev : devVos) {
            if (dev.getEdPremium() != null && dev.getEdPremium().compareTo(BigDecimal.ZERO) != 0) {
                if (targetDevVo == null || dev.getEdPremium().compareTo(maxEdPremium) > 0) {
                    targetDevVo = dev;
                    maxEdPremium = dev.getEdPremium();
                }
            }
        }
        BigDecimal edPremiumSum = devVos.stream()
                .map(dev -> nvl(dev.getEdPremium()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (targetDevVo != null && premium != null) {
            BigDecimal gap = premium.subtract(preCumlEdPremium).subtract(edPremiumSum);
            targetDevVo.setEdPremium(round(targetDevVo.getEdPremium().add(gap)));
        }

        // 净额结算手续费尾差处理
        AtrBussLrcTiIcuDev targetNetFeeDevVo = null;
        BigDecimal maxEdNetFee = BigDecimal.ZERO;
        for (AtrBussLrcTiIcuDev dev : devVos) {
            if (dev.getEdNetFee() != null && dev.getEdNetFee().compareTo(BigDecimal.ZERO) != 0) {
                if (targetNetFeeDevVo == null || dev.getEdNetFee().compareTo(maxEdNetFee) > 0) {
                    targetNetFeeDevVo = dev;
                    maxEdNetFee = dev.getEdNetFee();
                }
            }
        }
        BigDecimal edNetFeeSum = devVos.stream()
                .map(dev -> nvl(dev.getEdNetFee()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (targetNetFeeDevVo != null && netFee != null) {
            BigDecimal netFeeGap = netFee.subtract(preCumlEdNetFee).subtract(edNetFeeSum);
            targetNetFeeDevVo.setEdNetFee(round(targetNetFeeDevVo.getEdNetFee().add(netFeeGap)));
        }

        // 跟单获取费用尾差处理
        AtrBussLrcTiIcuDev targetIacfDevVo = null;
        BigDecimal maxEdIacf = BigDecimal.ZERO;
        for (AtrBussLrcTiIcuDev dev : devVos) {
            if (dev.getEdIacf() != null && dev.getEdIacf().compareTo(BigDecimal.ZERO) != 0) {
                if (targetIacfDevVo == null || dev.getEdIacf().compareTo(maxEdIacf) > 0) {
                    targetIacfDevVo = dev;
                    maxEdIacf = dev.getEdIacf();
                }
            }
        }
        BigDecimal edIacfSum = devVos.stream()
                .map(dev -> nvl(dev.getEdIacf()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (targetIacfDevVo != null && iacf != null) {
            BigDecimal iacfGap = iacf.subtract(preCumlEdIacf).subtract(edIacfSum);
            targetIacfDevVo.setEdIacf(round(targetIacfDevVo.getEdIacf().add(iacfGap)));
        }

        // 非跟单获取费用-对内尾差处理
        AtrBussLrcTiIcuDev targetIaehcInDevVo = null;
        BigDecimal maxEdIaehcIn = BigDecimal.ZERO;
        for (AtrBussLrcTiIcuDev dev : devVos) {
            if (dev.getEdIaehcIn() != null && dev.getEdIaehcIn().compareTo(BigDecimal.ZERO) != 0) {
                if (targetIaehcInDevVo == null || dev.getEdIaehcIn().compareTo(maxEdIaehcIn) > 0) {
                    targetIaehcInDevVo = dev;
                    maxEdIaehcIn = dev.getEdIaehcIn();
                }
            }
        }
        BigDecimal edIaehcInSum = devVos.stream()
                .map(dev -> nvl(dev.getEdIaehcIn()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (targetIaehcInDevVo != null && iaehcIn != null) {
            BigDecimal iaehcInGap = iaehcIn.subtract(edIaehcInSum);
            targetIaehcInDevVo.setEdIaehcIn(round(targetIaehcInDevVo.getEdIaehcIn().add(iaehcInGap)));
        }

        // 非跟单获取费用-对外尾差处理
        AtrBussLrcTiIcuDev targetIaehcOutDevVo = null;
        BigDecimal maxEdIaehcOut = BigDecimal.ZERO;
        for (AtrBussLrcTiIcuDev dev : devVos) {
            if (dev.getEdIaehcOut() != null && dev.getEdIaehcOut().compareTo(BigDecimal.ZERO) != 0) {
                if (targetIaehcOutDevVo == null || dev.getEdIaehcOut().compareTo(maxEdIaehcOut) > 0) {
                    targetIaehcOutDevVo = dev;
                    maxEdIaehcOut = dev.getEdIaehcOut();
                }
            }
        }
        BigDecimal edIaehcOutSum = devVos.stream()
                .map(dev -> nvl(dev.getEdIaehcOut()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (targetIaehcOutDevVo != null && iaehcOut != null) {
            BigDecimal iaehcOutGap = iaehcOut.subtract(edIaehcOutSum);
            targetIaehcOutDevVo.setEdIaehcOut(round(targetIaehcOutDevVo.getEdIaehcOut().add(iaehcOutGap)));
        }
    }

    /**
     * 校验ICU数据
     *
     * @param icu                 ICU对象
     * @param treatyNo            合约号
     * @param riskClassCode       险类代码
     * @param writtern            是否已导入过已赚保费
     * @param planVos             预估账单数据
     * @param allPaidPremVos      实收保费数据
     * @param edPlanVos           已赚保费数据
     * @param currentEdPlanDevVos 当期已赚保费数据
     */
    private void validateIcuData(AtrBussLrcTiIcu icu, String treatyNo, String riskClassCode,
                                 boolean writtern, List<AtrDapLrcTiPayPlan> planVos,
                                 List<AtrDapLrcTiPaid> allPaidPremVos,
                                 List<AtrDapLrcTiEdPlan> edPlanVos,
                                 List<AtrDapLrcTiEdPlan> currentEdPlanDevVos) {
        // 判断合约是否在有效期内
        Date expiryDate = icu.getExpiryDate();
        boolean isContractActive = expiryDate != null && !expiryDate.before(Dates.toDate(yearMonth + "01"));

        // 校验规则1：合约在有效期内，且之前已导入过已赚保费，则当期必须导入已赚保费
        if (isContractActive && writtern && currentEdPlanDevVos.isEmpty()) {
            throw new RuntimeException(String.format("合约[%s]险类[%s]在有效期内，已导入过已赚保费，当期未导入已赚保费数据",
                    treatyNo, riskClassCode));
        }

        // 校验规则2：合约在有效期内，当期必须导入预估账单
        if (isContractActive && planVos.isEmpty()) {
            throw new RuntimeException(String.format("合约[%s]险类[%s]在有效期内，当期未导入预估账单数据",
                    treatyNo, riskClassCode));
        }

        // 校验规则3：累计实收需等于累计已赚
        BigDecimal totalPaidPremium = sumBigDecimal(allPaidPremVos, vo -> nvl(vo.getPremium()));
        totalPaidPremium = round(totalPaidPremium); // 保留8位小数

        BigDecimal totalEdPremium = sumBigDecimal(edPlanVos, vo -> nvl(vo.getPremium()));
        totalEdPremium = round(totalEdPremium); // 保留8位小数

        if (!totalPaidPremium.equals(totalEdPremium)) {
            throw new RuntimeException(String.format("合约[%s]险类[%s] 总实收:%s;\n 不等于导入的累计已赚保费:%s",
                    treatyNo, riskClassCode, totalPaidPremium, totalEdPremium));
        }
    }

    private void calcIcg() {
        icgMap.values().forEach(this::calcIcg);
    }

    private void calcIcg(AtrBussLrcTiIcg icg) {
        long mainId = icgMainIdGen.incrementAndGet();

        int remainingMonths = icg.getRemainingMonths();
        String riskClassCode = icg.getRiskClassCode();
        String icgNo = icg.getIcgNo();
        int maxClmQuotaDevNo = icg.getMaxClmQuotaDevNo();
        Map<Integer, BigDecimal> devEdPremiumMap = icg.getDevEdPremiumMap();
        Map<Integer, BigDecimal> devIacfMap = icg.getDevIacfMap();
        Map<Integer, BigDecimal> devNetFeeMap = icg.getDevNetFeeMap();
        Map<Integer, BigDecimal> devBadDebtMap = icg.getDevBadDebtMap();
        Map<Integer, BigDecimal> devIaehcInMap = icg.getDevIaehcInMap();
        Map<Integer, BigDecimal> devIaehcOutMap = icg.getDevIaehcOutMap();
        // 添加对已赚字段Map的引用
        Map<Integer, BigDecimal> devEdNetFeeMap = icg.getDevEdNetFeeMap();
        Map<Integer, BigDecimal> devEdIacfMap = icg.getDevEdIacfMap();
        Map<Integer, BigDecimal> devEdIaehcInMap = icg.getDevEdIaehcInMap();
        Map<Integer, BigDecimal> devEdIaehcOutMap = icg.getDevEdIaehcOutMap();

        // 预期赔付率
        icg.setClaimRate(BigDecimal.valueOf(getQuota(Q_CLAIM_RATIO, riskClassCode, icgNo)));
        // 维持费用率
        icg.setMtRate(BigDecimal.valueOf(getQuota(Q_MT_RATIO, riskClassCode, icgNo)));
        // 退保率
        icg.setLapseRate(BigDecimal.valueOf(getQuota(Q_LAPSE_RATIO, riskClassCode, icgNo)));
        // 间接理赔费用率
        icg.setUlaeRate(BigDecimal.valueOf(getQuota(Q_ULAE_RATIO, riskClassCode, icgNo)));
        // 非金融风险调整率
        icg.setRaRate(BigDecimal.valueOf(getQuota(Q_RA_RATIO, riskClassCode, icgNo)));

        // 基于赔付计算的最大发展期
        int maxClmDevNo = 0;
        if (maxClmQuotaDevNo > 0) {
            maxClmDevNo = remainingMonths + maxClmQuotaDevNo - 1;
        }

        // 最大发展期
        int maxDevNo = Math.max(remainingMonths, maxClmDevNo);

        // 初始化发展期
        List<AtrBussLrcTiIcgDev> devVos = new ArrayList<>();
        for (int i = 0; i <= maxDevNo; i++) {
            AtrBussLrcTiIcgDev devVo = new AtrBussLrcTiIcgDev();
            devVos.add(devVo);
            devVo.setMainId(mainId);
            devVo.setDevNo(i);
            devVo.setYearMonth(yearMonth);
            devVo.setEdPremium(devEdPremiumMap.get(i));
            if (devEdPremiumMap.get(i) == null) {
                devVo.setEdRate(BigDecimal.ZERO);
            } else {
                BigDecimal edPremium = devEdPremiumMap.get(i);
                BigDecimal totalEdPremium = sumBigDecimal(devEdPremiumMap.values(), Function.identity());
                if (totalEdPremium.compareTo(BigDecimal.ZERO) == 0) {
                    devVo.setEdRate(BigDecimal.ZERO);
                } else {
                    devVo.setEdRate(roundR(edPremium.divide(totalEdPremium, 15, java.math.RoundingMode.HALF_UP)));
                }
            }


        }

        // 退保费、维持费用
        for (int i = 1; i <= remainingMonths; i++) {
            AtrBussLrcTiIcgDev devVo = devVos.get(i);
            BigDecimal edPremium = nvl(devVo.getEdPremium());
            BigDecimal lapseRate = nvl(icg.getLapseRate());
            BigDecimal lapse = round(edPremium.multiply(lapseRate));
            devVo.setLapse(lapse);

            BigDecimal mtRate = nvl(icg.getMtRate());
            BigDecimal mtFee = round(edPremium.subtract(lapse).multiply(mtRate));
            devVo.setMtFee(mtFee);
        }

        // 预期赔付、 间接理赔费用、 RA
        BigDecimal claimRatio = nvl(icg.getClaimRate());
        for (int i = 1; i <= maxClmDevNo; i++) {
            BigDecimal claim = BigDecimal.ZERO;
            for (int d = 1; d <= i && d <= remainingMonths; d++) {
                int q = i - d + 1;
                if (q > maxClmQuotaDevNo) {
                    continue;
                }
                AtrBussLrcTiIcgDev devVo = devVos.get(d);
                BigDecimal edPremium = nvl(devVo.getEdPremium());
                BigDecimal lapse = nvl(devVo.getLapse());
                double patternValue = q > maxClmQuotaDevNo ? 0 : getQuota(Q_CLAIM_SETTLED_PATTERN, riskClassCode, icgNo, q);
                BigDecimal pattern = BigDecimal.valueOf(patternValue);

                // 计算 (edPremium - lapse) * claimRatio * pattern
                BigDecimal premiumAfterLapse = edPremium.subtract(lapse);
                BigDecimal premiumAfterLapseWithRatio = premiumAfterLapse.multiply(claimRatio);
                BigDecimal increment = premiumAfterLapseWithRatio.multiply(pattern);

                claim = claim.add(increment);
                claim = round(claim);
            }

            AtrBussLrcTiIcgDev devVo = devVos.get(i);
            BigDecimal ulaeRate = nvl(icg.getUlaeRate());
            BigDecimal ulae = round(claim.multiply(ulaeRate));
            BigDecimal mtFee = nvl(devVo.getMtFee());
            devVo.setClaim(claim);
            devVo.setUlae(ulae);

            BigDecimal raRate = nvl(icg.getRaRate());
            BigDecimal total = claim.add(mtFee).add(ulae);
            BigDecimal ra = round(total.multiply(raRate));
            devVo.setRa(ra);
        }

        // 同步qtc性能优化，将icu发展期的数据合并到icg发展期
        for (AtrBussLrcTiIcgDev devVo : devVos) {
            Integer devNo = devVo.getDevNo();
            devVo.setRecvPremium(icg.getDevRecvPremiumMap().get(devNo));
            devVo.setNetFee(devNetFeeMap.get(devNo));
            devVo.setIacf(icg.getDevIacfMap().get(devNo));
            devVo.setIaehcIn(devIaehcInMap.get(devNo));
            devVo.setIaehcOut(devIaehcOutMap.get(devNo));
            devVo.setBadDebt(devBadDebtMap.get(devNo));

            // 计算已赚字段，直接使用从ICU发展期汇总的数据
            devVo.setEdNetFee(devEdNetFeeMap.getOrDefault(devNo, BigDecimal.ZERO));
            devVo.setEdIacf(devEdIacfMap.getOrDefault(devNo, BigDecimal.ZERO));
            devVo.setEdIaehcIn(devEdIaehcInMap.getOrDefault(devNo, BigDecimal.ZERO));
            devVo.setEdIaehcOut(devEdIaehcOutMap.getOrDefault(devNo, BigDecimal.ZERO));
        }

        icg.setId(mainId);
        abp.insert(icg);
        devVos.forEach(abp::insert);
    }

    private void collectIcg(AtrBussLrcTiIcu icu, List<AtrBussLrcTiIcuDev> icuDevs) {
        String riskClassCode = icu.getRiskClassCode();
        String portfolioNo = icu.getPortfolioNo();
        String icgNo = icu.getIcgNo();
        AtrBussRPGKey key = new AtrBussRPGKey();
        key.setRiskClassCode(riskClassCode);
        key.setPortfolioNo(portfolioNo);
        key.setIcgNo(icgNo);
        AtrBussLrcTiIcg icg = icgMap.get(key);
        int remainingMonths = icuDevs.size() - 1;
        // 总保费/总手续费
        BigDecimal totalPremium = BigDecimal.ZERO;
        BigDecimal totalNetFee = BigDecimal.ZERO;
        for (AtrBussLrcTiIcuDev icuDev : icuDevs) {
            totalPremium = totalPremium.add(nvl(icuDev.getRecvPremium()));
            totalNetFee = totalNetFee.add(nvl(icuDev.getNetFee()));
        }


        if (icg == null) {
            icg = new AtrBussLrcTiIcg();
            BeanUtils.copyProperties(icu, icg);
            icg.setRemainingMonths(remainingMonths);
            icg.setMaxClmQuotaDevNo(getMaxClmPatternDevNo(riskClassCode, icgNo));

            icg.setBadDebt(icu.getTotalBadDebt());
            icg.setTotalPremium(round(totalPremium));
            icg.setTotalNetFee(round(totalNetFee));
            icg.setIacf(round(icuDevs.get(0).getIacf()));
            icg.setIaehcIn(round(icuDevs.get(0).getIaehcIn()));
            icg.setIaehcOut(round(icuDevs.get(0).getIaehcOut()));
            icg.setInvestAmount(round(icu.getInvAmount()));
            // 确保设置新增的字段
            icg.setIcgName(icu.getIcgName());
            icg.setPlJudgeRslt(icu.getPlJudgeRslt());
            icgMap.put(key, icg);
        } else {
            if (remainingMonths > icg.getRemainingMonths()) {
                icg.setRemainingMonths(remainingMonths);
            }
            icg.setIacf(round(nvl(icuDevs.get(0).getIacf()).add(nvl(icg.getIacf()))));
            icg.setIaehcIn(round(nvl(icuDevs.get(0).getIaehcIn()).add(nvl(icg.getIaehcIn()))));
            icg.setIaehcOut(round(nvl(icuDevs.get(0).getIaehcOut()).add(nvl(icg.getIaehcOut()))));
            icg.setTotalPremium(round(totalPremium.add(nvl(icg.getTotalPremium()))));
            icg.setTotalNetFee(round(totalNetFee.add(nvl(icg.getTotalNetFee()))));
            icg.setBadDebt(round(nvl(icu.getTotalBadDebt()).add(nvl(icg.getBadDebt()))));
            icg.setInvestAmount(round(nvl(icu.getInvAmount()).add(nvl(icg.getInvestAmount()))));
        }

        for (AtrBussLrcTiIcuDev icuDev : icuDevs) {
            Integer devNo = icuDev.getDevNo();
            putDevValue(icg.getDevEdPremiumMap(), devNo, icuDev.getEdPremium());
            putDevValue(icg.getDevRecvPremiumMap(), devNo, icuDev.getRecvPremium());
            putDevValue(icg.getDevNetFeeMap(), devNo, icuDev.getNetFee());
            putDevValue(icg.getDevIacfMap(), devNo, icuDev.getIacf());
            putDevValue(icg.getDevIaehcInMap(), devNo, icuDev.getIaehcIn());
            putDevValue(icg.getDevBadDebtMap(), devNo, icuDev.getBadDebt());
            putDevValue(icg.getDevIaehcOutMap(), devNo, icuDev.getIaehcOut());

            // 添加对已赚字段的收集
            putDevValue(icg.getDevEdNetFeeMap(), devNo, icuDev.getEdNetFee());
            putDevValue(icg.getDevEdIacfMap(), devNo, icuDev.getEdIacf());
            putDevValue(icg.getDevEdIaehcInMap(), devNo, icuDev.getEdIaehcIn());
            putDevValue(icg.getDevEdIaehcOutMap(), devNo, icuDev.getEdIaehcOut());
        }
    }

    @Override
    protected void initAbp(AsyncBatchProcessor abp) {
        super.initAbp(abp);
        abp.addType(AtrBussLrcTiIcu.class);
        abp.addType(AtrBussLrcTiIcuDev.class);
        abp.addType(AtrBussLrcTiIcg.class);
        abp.addType(AtrBussLrcTiIcgDev.class);
        abp.addType(AtrBussTiLrcIcgPremium.class);
    }

    private void collectData() {
        logDebug("initMainIdGen");
        initMainIdGen();
        partitionBaseData();
        logDebug("collectQuotaDef");
        collectQuotaDef();
        logDebug("collectQuota");
        collectQuota();
        logDebug("collectRiskKindCode");
        collectRiskKindCodeMapToAcc();
        logDebug("collectDefaultBaseEntity");
        collectDefaultBaseEntity();
        logDebug("collectionPreIcgPremium");
        collectPreIcgPremium();

        // 并行准备该批次计算所需的数据
        List<String> marks = new ArrayList<>();
        marks.add("collectDapPaid");
        marks.add("collectExpAlloc");
        // 获取已赚
        marks.add("collectEdPlan");
        // 获取预估账单
        marks.add("collectPayPlan");
        marks.add("collectPreIcu");
        ThreadUtil.runThreadsThrow(marks, (mark) -> {
            logDebug(mark, "start");
            if ("collectDapPaid".equals(mark)) {
                collectDapPaid();
            } else if ("collectExpAlloc".equals(mark)) {
                collectExpAlloc();
            } else if ("collectEdPlan".equals(mark)) {
                collectEdPlan();
            } else if ("collectPayPlan".equals(mark)) {
                collectPayPlan();
            } else if ("collectPreIcu".equals(mark)) {
                collectPreIcu();
            }
            logDebug(mark, "end");
        }, marks::size);
    }

    private void partitionBaseData() {
        atrBussLrcTiDao.truncateBaseData();
        atrBussLrcTiDao.partitionBaseData(commonParamMap);
    }

    private void initMainIdGen() {
        icuMainIdGen = new AtomicLong(atrBussLrcTiDao.getIcuMaxMainId());
        icgMainIdGen = new AtomicLong(atrBussLrcTiDao.getIcgMaxMainId());
    }

    private void collectDapPaid() {
        List<AtrDapLrcTiPaid> vos = atrBussLrcTiDao.findDapPaid(commonParamMap);
        for (AtrDapLrcTiPaid vo : vos) {
            String yearMonth = vo.getYearMonth();
            List<?> key = createUnitKey(vo);
            if (yearMonth.equals(this.yearMonth)) {
                curPaidPremiumIndex.plus(key, vo.getPremium());
                curPaidNetFeeIndex.plus(key, vo.getNetFee());
                curPaidIacfIndex.plus(key, vo.getIacf());
                curPaidBadDebtIndex.plus(key, vo.getBadDebt());
            } else if (yearMonth.compareTo(this.yearMonth) < 0) {
                prePaidPremiumIndex.plus(key, vo.getPremium());
                prePaidNetFeeIndex.plus(key, vo.getNetFee());
                prePaidIacfIndex.plus(key, vo.getIacf());
                prePaidBadDebtIndex.plus(key, vo.getBadDebt());
            }
            if (BigDecimal.ZERO.compareTo(vo.getPremium()) != 0) {
                allPaidPremiumIndex.add(key, vo);
            }
        }
    }

    private void collectPayPlan() {
        payPlanIndex.clear();
        List<AtrDapLrcTiPayPlan> vos = atrBussLrcTiDao.findPayPlan(commonParamMap);
        for (AtrDapLrcTiPayPlan vo : vos) {
            payPlanIndex.add(createUnitKey(vo.getTreatyNo(), vo.getRiskClassCode()), vo);
        }
    }

    private void collectExpAlloc() {
        List<AtrDapExpAlloc> list = atrBussLrcTiDao.findExpAlloc(commonParamMap);

        List<AtrDapExpAlloc> expAllocIn = list.stream().filter(t -> "0".equals(t.getSchemeStatus())).collect(Collectors.toList());
        for (AtrDapExpAlloc a : expAllocIn) {
            List<?> unitKey = createUnitKey(a);
            if ( a.getYearMonth().equals(yearMonth)){
                curExpAllocInIndex.plus(unitKey, a.getAmount());
            }else{
                allExpAllocInIndex.plus(unitKey, a.getAmount());
            }
        }
        List<AtrDapExpAlloc> expAllocOut = list.stream().filter(t -> "1".equals(t.getSchemeStatus())).collect(Collectors.toList());
        for (AtrDapExpAlloc a : expAllocOut) {
            List<?> unitKey = createUnitKey(a);
            if ( a.getYearMonth().equals(yearMonth)){
                curExpAllocOutIndex.plus(unitKey, a.getAmount());
            }else{
                allExpAllocOutIndex.plus(unitKey, a.getAmount());
            }
        }

    }

    private void collectPreIcgPremium() {
        List<AtrBussTiLrcIcgPremium> atrBussDdLrcIcgPremiums = atrBussLrcTiDao.listPreIcgPremium(commonParamMap);

        atrBussDdLrcIcgPremiums.forEach(item -> {
            List<String> key = Arrays.asList(item.getPortfolioNo(), item.getIcgNo(), item.getRiskClassCode());
            BigDecimal totalPremium = nvl(item.getNewPremium()).add(nvl(item.getOldPremium()));
            icgPremiumIndex.put(key, new BigDecimal[]{totalPremium, BigDecimal.ZERO});
        });
    }

    private void saveIcgPremium() {
        icgPremiumIndex.forEach((key, value) -> {
            AtrBussTiLrcIcgPremium atrBussTiLrcIcgPremium = new AtrBussTiLrcIcgPremium();
            atrBussTiLrcIcgPremium.setActionNo(actionNo);
            atrBussTiLrcIcgPremium.setYearMonth(yearMonth);
            atrBussTiLrcIcgPremium.setPortfolioNo(key.get(0));
            atrBussTiLrcIcgPremium.setIcgNo(key.get(1));
            atrBussTiLrcIcgPremium.setRiskClassCode(key.get(2));
            atrBussTiLrcIcgPremium.setOldPremium(value[0]);
            atrBussTiLrcIcgPremium.setNewPremium(value[1]);
            abp.insert(atrBussTiLrcIcgPremium);
        });
    }

    // 新旧保费汇总，合同确认日期为当月则为新保费，旧保费采用上一个月的
    private void calcIcgPremium(String portfolioNo, String icgNo, String riskClassCode, Date contractDate, BigDecimal premium) {
        List<String> key = Arrays.asList(portfolioNo, icgNo, riskClassCode);
        if (Dates.toChar(contractDate, "yyyyMM").equals(yearMonth)) {
            if (icgPremiumIndex.containsKey(key)) {
                BigDecimal[] values = icgPremiumIndex.get(key);
                values[1] = values[1].add(premium);
                icgPremiumIndex.put(key, values);
            } else {
                icgPremiumIndex.put(key, new BigDecimal[]{BigDecimal.ZERO, premium});
            }
        }
    }

    // 获取已赚保费
    private void collectEdPlan() {
        edPlanIndex.clear();
        currentEdPlanDevIndex.clear(); // 清空当前评估期实际发展期已赚保费索引

        // 查询当前评估期所有合约和险类的已赚保费发展期数据
        List<AtrDapLrcTiEdPlan> allHistoryEdPlans = atrBussLrcTiDao.findAllEdPlanByDevPeriod(commonParamMap);

        // 获取当前评估期的已赚保费数据
        List<AtrDapLrcTiEdPlan> currentEdPlans = atrBussLrcTiDao.findEdPlan(commonParamMap);

        // 按合约号和险类代码分组历史数据，并找出每组的最大发展期和首次年月
        Map<List<?>, Integer> maxDevNoMap = new HashMap<>();
        Map<List<?>, String> firstYearMonthMap = new HashMap<>();

        // 处理历史数据，添加到索引
        for (AtrDapLrcTiEdPlan plan : allHistoryEdPlans) {
            String treatyNo = plan.getTreatyNo();
            String riskClassCode = plan.getRiskClassCode();
            List<?> key = createUnitKey(treatyNo, riskClassCode);

            // 记录最大发展期
            maxDevNoMap.compute(key, (k, v) -> v == null ? plan.getDevNo() : Math.max(v, plan.getDevNo()));

            // 记录首次年月(对应发展期为0的)
            if (plan.getDevNo() == 0 && !firstYearMonthMap.containsKey(key)) {
                firstYearMonthMap.put(key, plan.getFirstYearMonth());
            }

            // 添加到索引
            edPlanIndex.add(key, plan);
        }

        // 按合约号和险类代码对当前评估期数据进行分组
        Map<List<?>, List<AtrDapLrcTiEdPlan>> currentPlansByKey = new HashMap<>();
        for (AtrDapLrcTiEdPlan plan : currentEdPlans) {
            List<?> key = createUnitKey(plan.getTreatyNo(), plan.getRiskClassCode());
            if (!currentPlansByKey.containsKey(key)) {
                currentPlansByKey.put(key, new ArrayList<>());
            }
            currentPlansByKey.get(key).add(plan);

            currentEdPlanDevIndex.add(key, plan);
        }

        // 处理当前评估期数据
        for (Map.Entry<List<?>, List<AtrDapLrcTiEdPlan>> entry : currentPlansByKey.entrySet()) {
            List<?> key = entry.getKey();
            List<AtrDapLrcTiEdPlan> plans = entry.getValue();

            // 获取历史记录的最大发展期和首次年月
            int maxHistoryDevNo = maxDevNoMap.getOrDefault(key, -1);
            String firstYearMonth = firstYearMonthMap.getOrDefault(key, yearMonth);

            // 对当前评估期的数据按发展期排序
            plans.sort(Comparator.comparing(AtrDapLrcTiEdPlan::getDevNo));

            // 将当前评估期的所有发展期数据添加到索引
            for (int i = 0; i < plans.size(); i++) {
                AtrDapLrcTiEdPlan plan = plans.get(i);
                AtrDapLrcTiEdPlan newPlan = new AtrDapLrcTiEdPlan();
                BeanUtils.copyProperties(plan, newPlan);

                // 设置发展期和首次年月，发展期从历史发展期的下一期开始
                newPlan.setDevNo(maxHistoryDevNo + 1 + i);
                newPlan.setFirstYearMonth(firstYearMonth);

                // 添加到索引
                edPlanIndex.add(key, newPlan);
            }
        }
    }

    private void collectPreIcu() {
        preIcuIndex.clear();
        List<AtrDuctLrcTiIcuPre> vos = atrBussLrcTiDao.findPreIcu(commonParamMap);
        for (AtrDuctLrcTiIcuPre vo : vos) {
            preIcuIndex.add(createUnitKey(vo), vo);
        }
    }

    /**
     * 预先收集默认的基础实体信息，避免在循环计算中重复查询数据库
     */
    private void collectDefaultBaseEntity() {
        defaultBaseEntity = atrBussLrcTiDao.getDefaultBaseEntityCode();
        if (defaultBaseEntity == null) {
            throw new RuntimeException("无法获取默认的基础实体信息");
        }
    }

    private List<?> createUnitKey(Object vo) {
        List<Object> key = new ArrayList<>();
        key.add(EcfUtil.readField(vo, "treatyNo"));
        key.add(EcfUtil.readField(vo, "riskClassCode"));
        key.add(EcfUtil.readField(vo, "deptId"));
        key.add(EcfUtil.readField(vo, "channelId"));
        key.add(EcfUtil.readField(vo, "centerCode"));
        key.add(EcfUtil.readField(vo, "finProductCode"));
        key.add(EcfUtil.readField(vo, "finDetailCode"));
        key.add(EcfUtil.readField(vo, "finSubProductCode"));
        return key;
    }

    private List<?> createUnitKey(String treatyNo, String riskClassCode) {
        return Arrays.asList(treatyNo, riskClassCode);
    }

    // 获取BigDecimal版本的get方法，用于从List中安全获取元素
    protected BigDecimal getBigDecimal(List<BigDecimal> list, int index) {
        if (list == null || index < 0 || index >= list.size()) {
            return BigDecimal.ZERO;
        }
        BigDecimal value = list.get(index);
        return value == null ? BigDecimal.ZERO : value;
    }

    // 计算BigDecimal列表的总和
    protected <T> BigDecimal sumBigDecimal(List<T> list, Function<T, BigDecimal> f) {
        if (list == null || list.isEmpty()) {
            return BigDecimal.ZERO;
        }
        return list.stream()
                .map(f)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }


    private void collectRiskKindCodeMapToAcc() {
        List<AtrBussRiskKindCodeToAcc> atrBussRiskKindCodeToAccs = atrBussLrcTiDao.listRiskKindCodeToAcc();
        atrBussRiskKindCodeToAccs.forEach(atrBussRiskKindCodeToAcc -> {
            if (atrBussRiskKindCodeToAcc.getKindCode() == null) {
                riskKindCodeIndex.put(Collections.singletonList(atrBussRiskKindCodeToAcc.getRiskCode()), atrBussRiskKindCodeToAcc.getAccKindCode());
            } else {
                riskKindCodeIndex.put(Arrays.asList(atrBussRiskKindCodeToAcc.getRiskCode(), atrBussRiskKindCodeToAcc.getKindCode()), atrBussRiskKindCodeToAcc.getAccKindCode());
            }
        });
    }

    // 计算BigDecimal集合的总和
    protected <T> BigDecimal sumBigDecimal(Collection<T> collection, Function<T, BigDecimal> f) {
        if (collection == null || collection.isEmpty()) {
            return BigDecimal.ZERO;
        }
        return collection.stream()
                .map(f)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }


    /**
     * 合约分入一致性校验
     *
     * @param treatyNo           合约号
     * @param riskClassCode      险类代码
     * @param premium            保费
     * @param preCumlPaidPremium 上期累计实付保费
     * @param curPaidPremium     当期实付保费
     * @param epiPremium         预估账单保费
     * @param edPremiumSum       已赚保费汇总
     * @param preIcu             上期计量单元
     * @param devs               发展期列表
     * @param preCumlPaidNetFee  上期累计实付净额结算
     * @param preCumlPaidIacf    上期累计实付跟单获取费用
     * @param iaehcIn            非跟单获取费用(对内)
     * @param iaehcOut           非跟单获取费用(对外)
     */
    private void validateIcuConsistency(String treatyNo, String riskClassCode,
                                        BigDecimal premium, BigDecimal preCumlPaidPremium,
                                        BigDecimal curPaidPremium, BigDecimal epiPremium,
                                        BigDecimal edPremiumSum, AtrDuctLrcTiIcuPre preIcu,
                                        List<AtrBussLrcTiIcuDev> devs,
                                        BigDecimal preCumlPaidNetFee,
                                        BigDecimal preCumlPaidIacf,
                                        BigDecimal iaehcIn, BigDecimal iaehcOut) {

        // 1. 合约分入保费一致性校验
        // 累计实付保费 + 未来保费现金流 = 累计已赚保费(当期已赚+上期累计已赚)
        BigDecimal totalPaidPremium = preCumlPaidPremium.add(curPaidPremium);
        BigDecimal totalPremium = totalPaidPremium.add(epiPremium);

        if (totalPremium.compareTo(premium) != 0) {
            throw new RuntimeException(String.format("合约号为%s,险类代码为%s的保费为:%s,累计实收和未来保费现金流汇总为:%s 不一致",
                    treatyNo, riskClassCode, premium.toString(), totalPremium));
        }
        // 上期已赚
        BigDecimal preCumlEdPremium = nvl(preIcu.getPreCumlEdPremium()).add(nvl(preIcu.getCurEdPremium()));
        if ((preCumlEdPremium.add(edPremiumSum)).compareTo(premium) != 0) {
            throw new RuntimeException(String.format("合约号为%s,险类代码为%s的保费为:%s,累计已赚保费为:%s 不一致",
                    treatyNo, riskClassCode, premium.toString(), preCumlEdPremium.add(edPremiumSum)));
        }

        // 2. 合约分入净额结算一致性校验
        // 累计实付净额结算 + 未来净额结算现金流 = 累计已赚净额结算
        // 未来净额结算
        BigDecimal netFeeSum = sumBigDecimal(devs, dev -> nvl(dev.getNetFee()));
        // 当期已赚净额
        BigDecimal edNetFeeSum = sumBigDecimal(devs, dev -> nvl(dev.getEdNetFee()));
        // 上期累计已赚净额
        BigDecimal preCumlEdNetFee = nvl(preIcu.getPreCumlEdNetFee()).add(nvl(preIcu.getCurEdNetFee()));

        if (preCumlPaidNetFee.add(netFeeSum).compareTo(preCumlEdNetFee.add(edNetFeeSum)) != 0) {
            throw new RuntimeException(String.format("合约号为%s,险类代码为%s的净额结算不一致，累计实付净额结算+未来净额结算为:%s,累计已赚净额结算为:%s",
                    treatyNo, riskClassCode, preCumlPaidNetFee.add(netFeeSum), preCumlEdNetFee.add(edNetFeeSum)));
        }

        // 3. 合约分入跟单获取费用一致性校验
        // 累计实付跟单获取费用 + 未来跟单获取费用现金流 = 累计已赚跟单获取费用现金流
        BigDecimal iacfSum = sumBigDecimal(devs, dev -> nvl(dev.getIacf()));
        BigDecimal edIacfSum = sumBigDecimal(devs, dev -> nvl(dev.getEdIacf()));
        BigDecimal preCumlEdIacf = nvl(preIcu.getPreCumlEdIacf()).add(nvl(preIcu.getCurEdIacf()));

        if (preCumlPaidIacf.add(iacfSum).compareTo(preCumlEdIacf.add(edIacfSum)) != 0) {
            throw new RuntimeException(String.format("合约号为%s,险类代码为%s的跟单获取费用不一致，累计实付跟单获取费用+未来跟单获取费用为:%s,累计已赚跟单获取费用为:%s",
                    treatyNo, riskClassCode, preCumlPaidIacf.add(iacfSum), preCumlEdIacf.add(edIacfSum)));
        }

        // 4. 合约分入非跟单获取费用一致性校验
        BigDecimal edIaehcInSum = sumBigDecimal(devs, dev -> nvl(dev.getEdIaehcIn()));

        if (iaehcIn.compareTo(edIaehcInSum) != 0) {
            throw new RuntimeException(String.format("合约号为%s,险类代码为%s的非跟单获取费用(对内)不一致，实付非跟单获取费用为:%s,累计已赚非跟单获取费用为:%s",
                    treatyNo, riskClassCode, iaehcIn, edIaehcInSum));
        }

        BigDecimal edIaehcOutSum = sumBigDecimal(devs, dev -> nvl(dev.getEdIaehcOut()));

        if (iaehcOut.compareTo(edIaehcOutSum) != 0) {
            throw new RuntimeException(String.format("合约号为%s,险类代码为%s的非跟单获取费用(对外)不一致，累计实付非跟单获取费用为:%s,累计已赚非跟单获取费用为:%s",
                    treatyNo, riskClassCode, iaehcOut, edIaehcOutSum));
        }
    }

}
