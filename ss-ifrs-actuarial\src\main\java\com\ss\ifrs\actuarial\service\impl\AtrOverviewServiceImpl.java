package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.ifrs.actuarial.service.AtrConfBussPeriodService;
import com.ss.ifrs.actuarial.service.AtrOverviewService;
import com.ss.library.utils.ClassUtil;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.SystemConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.pojo.bbs.vo.BbsConfAccountPeriodVo;
import com.ss.platform.pojo.com.vo.ActOverviewVo;
import com.ss.platform.pojo.com.vo.TrackWorkFlowActionVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service(value = "atrActionLogService")
public class AtrOverviewServiceImpl implements AtrOverviewService {
    final Logger LOG = LoggerFactory.getLogger(getClass());

    @Autowired
    private AtrConfBussPeriodService atrConfBussPeriodService;

    /**
     * @Description: 查询期间下最近的执行日志信息
     * @Param : [atrConfBussPeriodVo]
     * @Return: Map
     * @Author: wuyh.
     * @CreateDate: 2020/12/28 14:26
     */
    @Override
    public Map<String, Object> findOverview(AtrConfBussPeriodVo atrConfBussPeriodVo) {
        Map<String, Object> map = new HashMap<String, Object>();

        AtrConfBussPeriodVo confBussPeriodVo = atrConfBussPeriodService.findCurrentPeriod(atrConfBussPeriodVo);
        BbsConfAccountPeriodVo bbsConfAccountPeriodVo = ClassUtil.convert(confBussPeriodVo, BbsConfAccountPeriodVo.class);
        bbsConfAccountPeriodVo.setSystemCode(SystemConstant.AtrIdentity.APP_CODE);
        bbsConfAccountPeriodVo.setProcCode(CommonConstant.RootProcCode.ATR);
        BaseResponse<ActOverviewVo> response =  new BaseResponse<>();
        BaseResponse<TrackWorkFlowActionVo> response1 =  new BaseResponse<>();
        try{
        } catch (Exception ex) {
            LOG.error(ex.getLocalizedMessage(), ex);
        }

        ActOverviewVo bplActOwVoActOverviewVo = response.getResData();
        map.put("atrOwProcVoTree", bplActOwVoActOverviewVo);
        map.put("actionLogVo", response1.getResData());
        return map;
    }


}
