package com.ss.ifrs.actuarial.api.puhua;


import com.ss.ifrs.actuarial.feign.BmsConfCodeFeignClient;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussClaimImportMainVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrAllocActionVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrAllocResultVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussIbnrImportMainVo;
import com.ss.ifrs.actuarial.service.AtrBussIbnrClaimImportService;
import com.ss.ifrs.actuarial.service.AtrBussIbnrImportService;
import com.ss.ifrs.actuarial.service.AtrConfCodeService;
import com.ss.ifrs.actuarial.service.puhua.AtrBussIbnrSecAllocService;
import com.ss.ifrs.actuarial.service.AtrExportService;
import com.ss.library.constant.RestfulCodeConstant;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ClassUtil;
import com.ss.library.utils.ExceptionUtil;
import com.ss.platform.core.annotation.LogScheduleTask;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.annotation.TrackUserBehavioral;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping( "/ibnr")
public class AtrBussIbnrSecAllocApi extends BaseApi {

    @Autowired
    AtrBussIbnrImportService atrBussIbnrImportService;

    @Autowired
    AtrBussIbnrSecAllocService atrBussIbnrSecAllocService;

    @Autowired
    AtrBussIbnrClaimImportService atrBussIbnrClaimImportService;

    @Autowired
    AtrExportService atrExportService;

    @Autowired
    BmsConfCodeFeignClient bmsConfCodeFeignClient;

    @Autowired
    AtrConfCodeService atrConfCodeService;


    @ApiOperation(value = "IBNR导入主表查询")
    @TrackUserBehavioral(description = "enquiry CashFlow")
    @RequestMapping(value = "/import/enquiry", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Map<String, Object>> enquiryBecfPage(@RequestBody AtrBussIbnrImportMainVo atrBussBecfMainVo, int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Map<String, Object> result = new HashMap<>();
        if ("CL".equals(atrBussBecfMainVo.getDataType())) {
            Page<AtrBussClaimImportMainVo> atrBussClaimImportMainVoPage = atrBussIbnrClaimImportService.searchXoClaimPage(ClassUtil.convert(atrBussBecfMainVo, AtrBussClaimImportMainVo.class), pageParam);
            result.put("bussBecfMainVoList", atrBussClaimImportMainVoPage);
        } else {
            Page<AtrBussIbnrImportMainVo> bussIbnrImportMainVoPage = atrBussIbnrImportService.searchLicPage(atrBussBecfMainVo, pageParam);
            result.put("bussBecfMainVoList", bussIbnrImportMainVoPage);
        }
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "下载上传模板")
    @TrackUserBehavioral(description = "Ibnr Template")
    @RequestMapping(value = "/import/exportTemplate", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public void downTemplate(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrBussIbnrImportMainVo atrBussBecfMainVo) {
        try {
            Long userId = this.loginUserId(request);
            atrExportService.downloadTemplateExcel(request, response, atrBussBecfMainVo.getTemplateFileName(), null, userId);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
        }
    }

    @ApiOperation(value = "IBNR导入")
    @RequestMapping(value = "/import/import", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> ibnrImport(HttpServletRequest request, @RequestParam(value = "file", required = false) MultipartFile file,
                                           AtrBussIbnrImportMainVo atrBussIbnrImportMainVo)  {
        Long userId = this.loginUserId(request);
        String result = "0";
        String exceptionMsg = null;
        try{
            atrBussIbnrImportService.ibnrExcelImport(file, atrBussIbnrImportMainVo, userId);
        } catch (Exception e) {
            exceptionMsg = ExceptionUtil.getMessage(e);
            logger.error(e.getLocalizedMessage(), e);
        }
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, exceptionMsg, result);
    }

    @ApiOperation(value = "IBNR导入数据是否已确认")
    @RequestMapping(value = "/import/check_confirm", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> checkPK(@RequestBody AtrBussIbnrImportMainVo atrBussBecfMainVo) {
        String result = "0";
        try {
            List<AtrBussIbnrImportMainVo> vos = atrBussIbnrImportService.findIbnrList(atrBussBecfMainVo);
            if(vos.size()>0){
                result = "1";
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, result);
        }
    }

    @ApiOperation(value = "IBNR导入数据确认")
    @RequestMapping(value = "/import/confirm", method = RequestMethod.POST)
    public BaseResponse<Object> confirm(@RequestBody AtrBussIbnrImportMainVo atrBussIbnrImportMainVo, HttpServletRequest request) {
        Long userId = this.loginUserId(request);
        try {
            Boolean confirmFlag = atrBussIbnrImportService.licDataConfirm(atrBussIbnrImportMainVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, confirmFlag);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "根据id查询赔付现金流信息")
    @RequestMapping(value = "/import/find_by_id/{ibnrMainId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<AtrBussIbnrImportMainVo> findById(@PathVariable("ibnrMainId") Long ibnrMainId) {
        AtrBussIbnrImportMainVo atrBussBecfMainVo = atrBussIbnrImportService.findById(ibnrMainId);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, atrBussBecfMainVo);
    }

    @ApiOperation(value = "根据id删除IBNR导入数据")
    @RequestMapping(value = "/import/delete_by_pk/{ibnrMainId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<Object> deleteById(@PathVariable("ibnrMainId") Long ibnrMainId) {
        AtrBussIbnrImportMainVo atrBussBecfMainVo = atrBussIbnrImportService.deleteIbnrByBecfId(ibnrMainId);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, atrBussBecfMainVo);
    }


    @ApiOperation(value = "超赔信息导入")
    @RequestMapping(value = "/import/import_claim", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> claimImport(HttpServletRequest request, @RequestParam(value = "file", required = false) MultipartFile file,
                                            AtrBussIbnrImportMainVo atrBussIbnrImportMainVo)  {
        Long userId = this.loginUserId(request);
        String result = "0";
        String exceptionMsg = null;
        try{
            atrBussIbnrImportService.claimExcelImport(file, atrBussIbnrImportMainVo, userId);
        } catch (Exception e) {
            exceptionMsg = ExceptionUtil.getMessage(e);
            logger.error(e.getLocalizedMessage(), e);
        }
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, exceptionMsg, result);
    }

    @ApiOperation(value = "超赔导入数据是否已确认")
    @RequestMapping(value = "/cl_import/check_confirm", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> checkCL(@RequestBody AtrBussClaimImportMainVo atrBussClaimImportMainVo) {
        String result = "0";
        try {
            List<AtrBussClaimImportMainVo> vos = atrBussIbnrClaimImportService.findCLList(atrBussClaimImportMainVo);
            if(vos.size() > 0){
                result = "1";
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, result);
        }
    }

    @ApiOperation(value = "超赔信息导入数据确认")
    @RequestMapping(value = "/cl_import/confirm", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> confirmCl(@RequestBody AtrBussClaimImportMainVo atrBussClaimImportMainVo, HttpServletRequest request) {
        Long userId = this.loginUserId(request);
        try {
            atrBussIbnrClaimImportService.ibnrDataConfirm(atrBussClaimImportMainVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "根据id查询赔付现金流信息")
    @RequestMapping(value = "/cl_import/find_by_id/{claimMainId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<AtrBussClaimImportMainVo> findCLById(@PathVariable("claimMainId") Long claimMainId) {
        AtrBussClaimImportMainVo atrBussClaimImportMainVo = atrBussIbnrClaimImportService.findById(claimMainId);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, atrBussClaimImportMainVo);
    }

    @ApiOperation(value = "根据id删除IBNR导入数据")
    @RequestMapping(value = "/cl_import/delete_by_pk/{claimMainId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<Object> deleteCLById(@PathVariable("claimMainId") Long claimMainId) {
        AtrBussClaimImportMainVo atrBussClaimImportMainVo = atrBussIbnrClaimImportService.deleteCLByBecfId(claimMainId);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, atrBussClaimImportMainVo);
    }



    @ApiOperation(value = "二次分摊查询")
    @RequestMapping(value = "/alloc/enquiry", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> iBnrResResultInput(@RequestBody AtrBussIbnrAllocActionVo atrBussIbnrAllocActionVo, int _pageNo, int _pageSize) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Map<String, Object> map = new HashMap<>();
        if (ObjectUtils.isNotEmpty(atrBussIbnrAllocActionVo)) {
            Page<AtrBussIbnrAllocActionVo> secAllocActionList = atrBussIbnrSecAllocService. findForDataTables(atrBussIbnrAllocActionVo, pageParam);
            map.put("ibnrAllocPage", secAllocActionList);
            return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, map);
        }
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, map);
    }


    @ApiOperation(value = "二次分摊前检查是否有IBNR")
    @RequestMapping(value = "/alloc/calc_check", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> iBnrSecondaryAllocCheck(HttpServletRequest request,
                                                            @RequestBody AtrBussIbnrImportMainVo atrBussBecfMainVo) {
        Boolean flag;
        if (ObjectUtils.isNotEmpty(atrBussBecfMainVo) && ObjectUtils.isNotEmpty(atrBussBecfMainVo.getYearMonth())) {
            flag = atrBussIbnrImportService.hasIbnrData(atrBussBecfMainVo);
            return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, flag);
        }
        return new BaseResponse<>(ResCodeConstant.ResCode.OTHER_ERROR, false);
    }

    @ApiOperation(value = "二次分摊前检查是否有IBNR")
    @RequestMapping(value = "/alloc/calc_run_check", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> iBnrSecondaryAllocRunCheck(HttpServletRequest request,
                                                        @RequestBody AtrBussIbnrAllocActionVo allocActionVo) {
        Boolean flag;
        if (ObjectUtils.isNotEmpty(allocActionVo) && ObjectUtils.isNotEmpty(allocActionVo.getYearMonth())) {
            flag = atrBussIbnrSecAllocService.hasIbnrCalc(allocActionVo);
            return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, flag);
        }
        return new BaseResponse<>(ResCodeConstant.ResCode.OTHER_ERROR, false);
    }


    @ApiOperation(value = "二次分摊计算")
    @RequestMapping(value = "/alloc/model_compute", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    @LogScheduleTask(bizCode = "BUSS_IBNR_CALC", argsValue = { "#ibnrAllocActionVo.entityId",
            "#ibnrAllocActionVo.yearMonth",
            "#ibnrAllocActionVo.taskMode",
            "#ibnrAllocActionVo.taskCode",
            "#ibnrAllocActionVo.retryOrder"})
    public BaseResponse<Object> iBnrSecondaryAllocationCalc(HttpServletRequest request,
            @RequestBody AtrBussIbnrAllocActionVo ibnrAllocActionVo) {
        if (ObjectUtils.isNotEmpty(ibnrAllocActionVo) && ObjectUtils.isNotEmpty(ibnrAllocActionVo.getYearMonth())) {
            Long userId = this.loginUserId(request);
            if (ObjectUtils.isNotEmpty(ibnrAllocActionVo.getBusinessSourceCode())) {
                logger.info("结果0：" + ibnrAllocActionVo.getBusinessSourceCode());
                atrBussIbnrSecAllocService.calculateAll(ibnrAllocActionVo, userId);
            } else {
                List<String> businessModelList = atrConfCodeService.findCodeByCodeIdx("IBNRBusinessModel");
                for(String businessMode: businessModelList){
                    ibnrAllocActionVo.setBusinessSourceCode(businessMode);
                    atrBussIbnrSecAllocService.calculateAll(ibnrAllocActionVo, userId);
                }
             }
            return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS);
        }
        return new BaseResponse<>(ResCodeConstant.ResCode.OTHER_ERROR);
    }

    @ApiOperation(value = "根据id删除信息")
    @RequestMapping(value = "/alloc/delete_by_id/{id}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<String> deleteAllocAction(HttpServletRequest request, @PathVariable("id") Long id) {
        Long userId = this.loginUserId(request);
        atrBussIbnrSecAllocService.delete(id, userId);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS);
    }


    @ApiOperation(value = "查询分摊结果数据")
    @RequestMapping(value = "/alloc/findAllocList", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findAllocList(HttpServletRequest request,  @RequestBody AtrBussIbnrAllocActionVo allocActionVo, int _pageSize, int _pageNo) {

        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrBussIbnrAllocResultVo> ibnrAllocResultVoPage = atrBussIbnrSecAllocService.findAllocList(allocActionVo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("ibnrAllocResultVoPage", ibnrAllocResultVoPage);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "查询超赔分摊结果数据")
    @RequestMapping(value = "/alloc/findXAllocList", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findXAllocList(HttpServletRequest request,  @RequestBody AtrBussIbnrAllocActionVo allocActionVo, int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrBussIbnrAllocResultVo> ibnrAllocResultVoPage = atrBussIbnrSecAllocService.findXAllocList(allocActionVo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("ibnrXAllocResultVoPage", ibnrAllocResultVoPage);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, result);
    }


    @ApiOperation(value = "二次分摊确认")
    @RequestMapping(value = "/alloc/confirm", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    @LogScheduleTask(bizCode = "BUSS_IBNR_CFM", argsValue = { "#ibnrAllocActionVo.entityId",
            "#ibnrAllocActionVo.yearMonth",
            "#ibnrAllocActionVo.taskMode",
            "#ibnrAllocActionVo.taskCode",
            "#ibnrAllocActionVo.retryOrder"})
    public BaseResponse<Object> confirmAlloc(HttpServletRequest request, @RequestBody AtrBussIbnrAllocActionVo ibnrAllocActionVo) {
        Long userId = this.loginUserId(request);
        Boolean confirmFlag = true;
        try {
            if (ObjectUtils.isNotEmpty(ibnrAllocActionVo) && ObjectUtils.isNotEmpty(ibnrAllocActionVo.getBusinessSourceCode())) {
                 confirmFlag = atrBussIbnrSecAllocService.confirm(ibnrAllocActionVo, userId);
            } else {
                List<String> businessModelList = atrConfCodeService.findCodeByCodeIdx("IBNRBusinessModel");
                for(String businessMode: businessModelList){
                    ibnrAllocActionVo.setBusinessSourceCode(businessMode);
                    atrBussIbnrSecAllocService.confirm(ibnrAllocActionVo, userId);
                }
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, confirmFlag);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "导出分摊数据")
    @RequestMapping(value = "/alloc/export_data", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> exportAllocDataByModel(HttpServletRequest request, HttpServletResponse response,
             @RequestBody AtrBussIbnrAllocActionVo ibnrAllocActionVo) {
        try {
            ibnrAllocActionVo.setLanguage(request.getHeader("ss-Language"));
            Long userId = this.loginUserId(request);
            atrBussIbnrSecAllocService.exportAllocData(request, response, ibnrAllocActionVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, RestfulCodeConstant.RestfulCode.SUCCESS);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, RestfulCodeConstant.RestfulCode.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "导出分摊数据")
    @RequestMapping(value = "/alloc/download", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public void downloadAllocData(HttpServletRequest request, HttpServletResponse response,
                                                       @RequestBody AtrBussIbnrAllocActionVo ibnrAllocActionVo) {
        try {
            ibnrAllocActionVo.setLanguage(request.getHeader("ss-Language"));
            Long userId = this.loginUserId(request);
            atrBussIbnrSecAllocService.downloadAllocData(request, response, ibnrAllocActionVo, userId);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
        }
    }
}
