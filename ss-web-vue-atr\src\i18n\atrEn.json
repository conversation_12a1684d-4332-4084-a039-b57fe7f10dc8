{"atrCurrency": "Currency Type", "atrYearMonth": "Applicable Period", "atrNormMonth": "Calendar Month", "atrNormTime": "Length (in Years)", "atrLowValue": "Low Value", "atrLowProfitRate": "Low Value Yield (%) ", "atrHighValue": "High Value", "atrHighProfitRate": "High Value Yield (%)", "atrInterpolatedSpotRate": "Interpolated Spot Rate (%)", "atrLiquidityPremium": "Liquidity Premium", "atrAnnualSpotRateWithPremium": "Annual Spot Rate With Premium (%)", "atrMonthlySpotRateWithPremium": "Monthly Spot Rate With Premium (%)", "atrMonthlyForwardRateWithPremium": "Monthly Forward Rate With Premium (%)", "atrForwardDiscountFactor": "Forward Discount Factor", "atrSpotDiscountFactor": "Spot Discount Factor", "atrMonImmProfit": "Annualized Spot Return Rate of The Month (%)", "atrMonImmProfitFlow": "Forward interest rate (including liquidity premium)", "atrMonFarProfitFlow": "Current month forward interest rate curve(including liquidity premium)", "atrCurveSegmentation": "Interest Rate Curve Segmentation", "atrFirstInterpolation": "First Interpolation", "atrSecondInterpolation": "Second Interpolation", "atrUltimateMethod": "Ultimate Method", "atrConfInterestDelMsg": "This Version Is Confirmed to Be Adopted, and Cannot Be Deleted!", "atrExcelIsNull": "Please select import file", "atrImportYearIs": "Acquired Configured Imported Year Is Incorrect!", "atrConfigYear": "Limit The Number of Imported File (Year)", "atrFormatIsIntZeroError": "Format Error; It Should Be Greater Than 0", "atrUploadIsNull": "Uploaded File Cannot Be Null", "atrInterestRateResultInfo": "Risk-free Interest Rate Curve Computing Result Information", "atrCurrForwardRate": "Current Forward Interest Rate Curve", "atrCurrInitialRate": "Current Initial Interest Rate Curve", "atrWeightedMeanRate": "Weighted Average Initial Interest Rate Curve", "atrPayTimeSwitch": "Received/ Paid Time Spot", "atrInitialWeightedMean": "Initial Interest Rate Weight", "atrNewPolicyWeight": "New Policy Weight", "atrPolicyWeight": "Existing In-force Policy Weight", "atrQuotaYearMonth": "Adapted Quantification Month", "atrAccidentYearMonth": "Accident Date", "atrRiskClass": "Risk Class Code", "atrDevelopMonth": "Development Period", "atrDevelopYear": "Development Year", "atrQuotaType": "Assumption Type", "atrCoefficientValue": "Assumption Value", "atrCoefficientDescribe": "Assumption Describe", "atrCoefficientValueDraw": "Assumption Value Draw", "atrLossRate": "Loss Rate", "atrDefaultValue": "Coefficient", "atrQuotaDetail": "Development Period Assumptions", "atrBtnDevExport": "Development Period Export", "atrBtnDevImport": "Development Period Import", "atrQuotaExtra": "Assumption Configuration", "atrQuotaTypeIs": "Acquired Assumption Type invalid", "atrAllNumber": "Positive Integers Only!", "atrNumber": "Numbers Only!", "atrRate": "<PERSON><PERSON> Exceed 100!", "atBusinessrOrDev": "Business Year/Development Year", "rptBussPeriod": "Business Year/Month", "atrLicYearMonth": "Quantification Month", "atrExtractYearMonth": "Data Extraction Period (Year/Month)", "atrDataSource": "Data Source", "atrCalProductClass": "Product Risk Class", "atrTraceabilityInterval": "Retroactive Dimension", "atrTracingIntervalNumber": "Number of Retrospective Intervals", "atrDrawTime": "Data Extraction Time", "atrDrawStartDate": "Data Extraction Start Date", "atrDrawEndDate": "Data Extraction End Date", "atrDrawEndYear": "Extraction end month and year", "atrReserveOsExtract": "O/S Withdraw", "atrExtractDate": "Extraction Date", "atrReserveOsDetail": "Outstanding Reserve Detail", "atrBtnDraw": "Extraction", "atrExecutorName": "Executor", "atrExecutorTime": "Execution Time", "atrLicState": "Status", "atrLicResult": "LIC Amount", "atrConfirmResult": "Confirmation Status", "atrLicConfirm": "LIC/CSM Confirmation", "atrConfirmName": "Confirmed by", "atrConfirmTime": "Confirm The Time", "atrDrawBegin": "LIC/CSM Computing Start", "atrBtnCala": "Calculation", "atrItemYear": "yearly", "atrBtnInflationRate": "Proportional Overview", "atrCoefficient": "Coefficient%", "atrConfirmState": "Confirm status", "atrInflationMation": "Inflation coefficient information", "atrDebtIncurred": "Incurred Liability Debt", "atrTriangleFlowOfAccidentMonth": "Date of accident and Claim Payment Run-Off Triangle", "atrTriangleFlowOfCumulativeMonth": "Date of accident and Cumulated Claim Payment Run-Off Triangle", "atrLossCoef": "Average Loss Development Coefficient", "atrAccumulatedLoss": "Final Cumulated Loss Amount (Actual + Expected)", "atrMonthlyLoss": "Loss Amount (Actual+ Expected)", "atrCashFlow": "Cash Flow at The End of Period (Assumption)", "atrQuota": "Assumptions", "atrStepFirst": "Step One", "atrStepSecond": "Step Two", "atrStepThird": "Step Three", "atrStepFourth": "Step Four", "atrStepFifth": "Step Five", "atrStepSixth": "Step Six", "atrStepSeventh": "Step Seven", "atrDateOfAccident": "Date of accident (Year/Month)", "atrBtnLICNext": "Computing (LIC)", "atrBtnCSMNext": "Computing (CSM)", "gConfirm": "Confirm", "gConfirmSuccess": "Confirmed Successfully", "gConfirmErrer": "Confirmation failed", "gLicConfirmed": "Approved Version Exists", "gLicAccidentAllInd": "Cumulated Claims:", "gLicPremium": "Premium", "gLicLossRate": "Loss Rate during 2020:", "gLicIBnr": "IBNR", "gLicPlus": "+", "atrCalcType": "Calculation Type", "atrLicConfirmed": "LIC Confirmation", "atrCSMConfirmed": "CSM Confirmation", "atrFormatIsRate": "Format Error, The Proportion Should Be within 0-100", "atrDeleteConfirmedMsg": "The approved version cannot be deleted!", "atrPaidClaim": "<PERSON><PERSON>", "atrCaseReserve": "Case Reserve", "atrIBNRAlgorithm": "IBNR Algorithm", "atrMeasureMethod": "Quantification Type", "sysAtrQualificationInfo": "Unexpired Res. Cal. Mth. Configuration Information", "atrBtnLICCalc": "LIC Calculation", "atrBtnCSMCalc": "CSM Calculation", "atrBtnDetailData": "Detailed Data", "atrLrcDrawBegin": "LRC Calculation Start", "atrLrcDrawCondition": "Extraction Condition", "atrLrcAmount": "LRC Amount", "atrLrcConfirmed": "LRC Confirmed", "atrLrcRealityAmount": "Actual Unearned Amount", "atrRadioProductLine": "By Product Line", "atrRadioPortfolioLine": "By Contract Group Line", "atrCalcQueryCondition": "Enquiry Condition", "atrTabsMain": "Main Information", "atrTabsResult": "Result", "atrTabsHistory": "Historical Data", "atrAssumption": "Assumption", "atrMain": "Main", "atrMeasurementModel": "Quantification Model", "atrItem": "<PERSON><PERSON>", "atrAction": "Operation", "atrQuantificationByProductLine": "Quantifying by Product Line", "atrQuantificationByPortfolio": "Quantifying by Portfolio", "atrMoreQuery": "More Enquiry Condition", "atrAcquisitionSource": "Acquisition Source", "atrPremiumAmount": "Premium Amount", "atrClaimsAcq": "<PERSON><PERSON><PERSON>", "atrRa": "RA", "atrPcFcf": "PV Annuity (Fulfillment)", "atrPvRa": "PV Annuity (RA)", "atr∑PvRa": "Summed PV Annuity (RA)", "atrExpenseAcq": "Acquisition Expense", "atrDiscountRate": "Discount Rate", "atrHistoryData": "History Data", "atrMonth1": "Jan", "atrMonth2": "Feb", "atrMonth3": "Mar", "atrMonth4": "Apr", "atrMonth5": "May", "atrMonth6": "Jun", "atrMonth7": "Jul", "atrMonth8": "Aug", "atrMonth9": "Sept", "atrMonth10": "Oct", "atrMonth11": "Nov", "atrMonth12": "Dec", "atrDevelopmentAssumption": "Company Loss Development Factor Assumption", "atrAcquisitionInfo": "Assumption Information", "atrWeight": "Proportion", "atrSelectBuss": "Please Select Business Type", "atrSelectYear": "Please Select Year", "atrSelectPortfolio": "Please Select Portfolio", "atrBtnQuery": "Enquiry", "atrCalcUwYear": "Quantification Year", "atrLicCalcDetail": "LIC Detailed Data", "atrLrcCalcDetail": "LRC Detailed Data", "atrRiskDistributionMode": "Unexpired Res. Cal. Mth.", "atrMeasurementResult": "Quantification Result", "atrMeasurement": "Quantification", "atrEvalPaaDetail": "Quantification Detailed Data", "atrEvalInCalculation": "Quantifying Is Under Progress, Please Check The Result Later", "atrEvalConfirm": "Version Confirmation Failed", "atrMeasurementAll": "Quantify All Data", "atrEvalRepNoEarned": "Unearned Premium at Initial Reporting Period", "atrEvalCurNoEarned": "Unearned Premium at The End of Current Period", "atrEvalCexpIacfCf": "Claim or Expense_Amortized Insurance Acquisition Cash Flow", "atrEvalEopIcPv": "Investment Component PV at The End of Period", "atrCumulatedConfirmed": "Cumulated Confirmed PV of Investment Component", "atrEvalCurAffirmPrePv": "Cumulated Confirmed Premium PV at The End of Period During The Reporting Period", "atrEvalContractRevenue": "Insurance Contract Revenue", "atrEvalLrcNonLc": "Remaining Liability Debt_Non-onerous Component", "atrEvalLrcLc": "Remaining Liability Debt_Onerous Component", "atrEvalIfieUnexpdInt": "IFIE_Remaining_Remaining Interest Accretion", "atrEvalLossContractPl": "Onerous Contract Profit or Loss", "atrEvalDocIacfFee": "Policy Insurance Acquisition Cash Flow", "atrEvalCurPremium": "Received Premium in The Current Period", "atrEvalCurSurrenderChargeFee": "Surrender Fee", "atrEvalCurDocIacfFee": "Paid Policy Insurance Acquisition Cash Flow in The Current Period", "atrEvalCurNDocIacfFee": "Paid Non-policy Insurance Acquisition Cash Flow in The Current Period", "atrEvalCurPolicyDay": "Insured days", "atrEvalCurIACFRate": "IACF Payment Time Spot", "atrEvalCurInvestment": "Investment Component Proportion", "atrEvalCurDiscountRate": "Discount Rate", "atrCurrencyCu": "Base Currency", "atrEvalDevelopItem": "Assumpted Item \\ Development Period", "atrEvalIcgItem": "Output Item \\ Contract Group", "atrEvalBeforeData": "Pre-quantification Data", "atrEvalCFDevelopPeriodData": "Expected Cash Flow Of Contract Group (Development Period)", "atrEvalDuctData": "Quantification Process Data (Current Period)", "atrDuctData": "Quantification Process Data", "atrLicCurReportedOsPerformingCashFlow": "Current Accident_ Incurred Reported Outstanding Claim Debt_ Fulfillment Cash Flow", "atrLicCurNoReportedOsPerformingCashFlow": "Current Accident_ Incurred Non-reported Outstanding Claim Debt_ Fulfillment Cash Flow", "atrLicCurReportedRiskJudge": "Current Accident_ Incurred Reported Outstanding Claim Debt_ Non-financial Risk Adjustment", "atrLicCurNoReportedRiskJudge": "Current Accident_ Incurred Non-reported Outstanding Claim Debt_ Non-financial Risk Adjustment", "atrLicPreReportedOsPerformingCashFlow": "Previous Accident_ Incurred Reported Outstanding Claim Debt_ Fulfillment Cash Flow", "atrLicPreNoReportedOsPerformingCashFlow": "Previous Accident_ Incurred Non-reported Outstanding Claim Debt_ Fulfillment Cash Flow", "atrLicPreReportedRiskJudge": "Previous Accident_ Incurred Reported Outstanding Claim Debt_ Non-financial Risk Adjustment", "atrLicPreNoReportedRiskJudge": "Previous Accident_ Incurred Non-reported Outstanding Claim Debt_ Non-financial Risk Adjustment", "atrLicReportedOsPerformingCashFlow": "Incurred Reported Outstanding Claim Debt_ Fulfillment Cash Flow", "atrLicNoReportedOsPerformingCashFlow": "Incurred Non-reported Outstanding Claim Debt_ Fulfilment Cash Flow", "atrLicReportedRiskJudge": "Incurred Reported Outstanding Claim Debt_ Non-financial Risk Adjustment", "atrLicNoReportedClaimRiskJudge": "Incurred Non-reported Outstanding Indirect Claim Expense Cash Flow_ Non-financial Risk Adjustment", "atrLicPayAndFeeReportedLibPmCF": "Claim and Expense_ Incurred Reported Outstanding Claim Debt Difference_ Fulfillment Cash Flow", "atrLicPayAndFeeNoReportedLibPmCF": "Claim and Expense_ Incurred Non-reported Outstanding Claim Debt Difference_ Fulfillment Cash Flow", "atrLicPayAndFeeReportedLibRJ": "Claim and Expense_ Incurred Reported Outstanding C…im Debt Difference_ Non-financial Risk Adjustment", "atrLicPayAndFeeNoReportedLibRJ": "Claim and Expense_ Incurred Non-reported Outstandi…im Debt Difference_ Non-financial Risk Adjustment", "atrLicCurClaim": "Settled in The Current Period", "atrLicCurAcidClaimFeePay": "Current Accident_ Indirect Claim Expense_ Current Payment", "atrLicPreAcidClaimFeePay": "Previous Accident_ Indirect Claim Expense_ Current Payment", "atrLicIFIEReportedPmCF": "IFIE_ Incurred Reported_ Fulfillment Cash Flow", "atrLicIFIEReportedRJ": "IFIE_ Incurred Reported_ Non-financial Risk Adjustment", "atrLicIFIENoReportPmCF": "IFIE_ Incurred Non-reported_ Fulfillment Cash Flow", "atrLicIFIENoReportRJ": "IFIE_ Incurred Non-reported_ Non-financial Risk Adjustment", "atrLicPreAcidOsReportedPmCF": "Previous Accident Period_ Outstanding Interest Accretion_ Incurred Reported_ Fulfillment Cash Flow", "atrLicPreAcidOsReportedRJ": "Previous Accident Period_ Outstanding Interest Acc… Incurred Reported_ Non-financial Risk Adjustment", "atrLicPreAcidOsNoReportedPmCF": "Previous Accident Period_ Outstanding Interest Acc…ion_ Incurred Non-reported_ Fulfillment Cash Flow", "atrLicPreAcidOsNoReportedRJ": "Previous Accident Period_ Outstanding Interest Acc…urred Non-reported_ Non-financial Risk Adjustment", "atrLicIFIEOccPmCF": "IFIE_ Incurred_ Fulfillment Cash Flow", "atrLicIFIEOccRJ": "IFIE_ Incurred_ Non-financial Risk Adjustment", "atrLicPrePayAdjPmCF": "Previous Claim Adjustment_ Fulfillment Cash Flow", "atrLicPrePayAdjRJ": "Previous Claim Adjustment_ Non-financial Risk Adjustment", "atrLicCFPayFee": "Cash Flow_ Paid Claim and Expense", "atrLicPreAcidOsPmCF": "Previous Accident Period_ Outstanding Interest Accretion_ Fulfillment Cash Flow", "atrLicPreAcidOsRJ": "Previous Accident Period_ Outstanding Interest Accretion_ Non-financial Risk Adjustment", "atrLicCurAcidReportedOsCF": "Current Accident_ Incurred Reported Outstanding Claim Cash Flow", "atrLicPreAcidReportedOsCF": "Previous Accident_ Incurred Reported Outstanding Claim Cash Flow", "atrLicCurAcidReportedClaimFeeCF": "Current Accident_ Incurred Reported Outstanding Indirect Claim Expense Cash Flow", "atrLicPreAcidReportedClaimFeeCF": "Previous Accident_ Incurred Reported Outstanding Indirect Claim Expense Cash Flow", "atrLicCurAcidNoReportedOsCF": "Current Accident_ Incurred Non-reported Outstanding Claim Cash Flow", "atrLicPreAcidNoReportedOsCF": "Previous Accident_ Incurred Reported Outstanding Claim Cash Flow", "atrLicCurAcidNoReportedClaimFeeCF": "Current Accident_ Incurred Non-reported Outstanding Indirect Claim Expense Cash Flow", "atrLicPreAcidNoReportedClaimFeeCF": "Previous Accident_ Incurred Non-reported Outstanding Indirect Claim Expense Cash Flow", "atrLicOsPayDiscountFactor": "Outstanding Claim Discounting Factor", "atrPaaLic": "Incurred <PERSON><PERSON><PERSON>", "atrPaaLrc": "Remaining Coverage Debt", "atrValidVersionNo": "Version Number Cannot Be Null, Please Enter Again!", "atrValidVersionNoExists": "Version Number Exists, Please Enter Again!", "atrValidCenterCode": "Business Unit Cannot Be Null, Please Enter Again!", "atrValidRiskClass": "Product Risk Class Cannot Be Null, Please Enter Again!", "atrValidYearMonth": "Assessment Date Cannot Be <PERSON>, Please Enter Again!", "atrValidEvaluateApproach": "Assessment Method Cannot Be Null, Please Enter Again!", "atrValidPortfolioNo": "<PERSON><PERSON><PERSON>not Be <PERSON>ull, Please Enter Again!", "atrValidIcgNo": "Contract Group Cannot Be Null, Please Enter Again!", "atrValidUnitNo": "Quantification Unit Cannot Be Null, Please Enter Again!", "atrValidDevPeriod": "Development Period Cannot Be Null, Please Enter Again!", "atrValidDataVal": "Quantification Result <PERSON>not Be Null, Please Enter Again!", "atrDataValFormat": "Quantification Result Format Error, Please Enter Number!", "atrDevPeriodFormat": "Development Period Format Error, Please Enter Number!", "atrValidCenterMsg": "The Corresponding Business Unit to The Code Does Not Exist, Please Enter Again!", "atrValidRiskClassMsg": "The Corresponding Product Risk Class to The Code Does Not Exist, Please Enter Again!", "atrValidFactorMsg": "The Corresponding Quantification Item to The Code Does Not Exist, Please Enter Again!", "atrValidUniqueNotUnitMsg": "Same Business Unit, Product Risk Class, Assessment…nit and Development Period), Please Enter Again! ", "atrValidUniqueUnitMsg": "Same Business Unit, Product Risk Class, Assessment…nit and Development Period), Please Enter Again! ", "atrValidNotDevPeriodMsg": "This Quantification Item Should Not Have Development Period, Please Enter Again!", "atrValidNumber14": "Should Be 14 Digits (Like YYYYMMDDHHMMSS), Please Enter Again!", "atrExcelVersionNo": "Version No.", "atrExcelCenterCode": "Business Unit", "atrExcelRiskClass": "Product Risk Class", "atrEvaluationYearMonth": "Assessment Period", "atrExcelEvaluateApproach": "Assessment Method", "atrExcelPortfolioNo": "Portfolio", "atrExcelIcgNo": "Contract Group", "atrExcelUnitNo": "Quantification Unit", "atrExcelDevPeriod": "Development Period", "atrExcelFactorCode": "Quantification Item", "atrExcelDataVal": "Quantification Result", "atrValidUnitNoAndDevPeriod": "Quantification Unit and Development Period Should …or Not Null at The Same Time, Please Enter Again!", "atrExcelFormatErrorMsg": "Uploaded File Format Error, Only xls or xlsx Files Accepted!", "atrPaidAmount": "Settled <PERSON><PERSON><PERSON> Amount", "atrOsAmount": "Outstanding Claim Amount", "atrReserveBeforeRI": "Pre-reinsurance", "atrReserveBeforeRIPaid": "Pre-reinsurance (Claim)", "atrReserveBeforeRIExpense": "Pre-reinsurance (Expense)", "atrReserveOutward": "Outward Reinsurance", "atrReserveAfterRI": "Retention", "atrReserveOsTitle": "Outstanding Claim Reserve Detailed List", "atrEvaluateDetailExcel": "Quantification Result Detailed Data", "atrEvaluateExportTitle": "Export information of Quantification results", "atrModelFactorSource": "Factor Source", "atrModelDimension": "Factor Dimension", "atrModelName": "Model", "atrModelNodeType": "Node Type", "atrModelNodeRule": "Node Rule", "atrModelDefInfo": "Model Definition Information", "atrModelNodeInfo": "Model Node Information", "atrModelNodeRouteInfo": "Model Node URL Information", "atrModelRuleFactorInfo": "Quantification Factor Information", "atrModelRuleInfo": "Quantification Rule Information", "atrModelSourceRef": "Source Node", "atrModelTargetRef": "Target Node", "atrModelRelationMsg": "This Model Has Been Applied to Rules, and Cannot Be Deleted!", "atrReserveUprExtract": "Remaining Liability Reserve Withdraw", "atrReserveDacExtract": "Deferred Commission Reserve Withdraw", "atrReserveUpr": "Remaining Liability Reserve ", "atrReserveUprDetail": "Remaining Liability Reserve Detail", "atrReserveDac": "Deferred Commission Reserve", "atrReserveDacDetail": "Deferred Commission Reserve Detail", "atrReserveConfirmDate": "Selected Version Computing Date Is Not End of Month", "atrReservePremium": "Premium", "atrReserveUnearnedPremium": "Remaining Liability Premium", "atrReserveCommission": "Commission", "atrReserveUnearnedCommission": "Deferred Commission", "atrDevelopFlag": "Development Period Identifier", "modelDimension": "Quantification Granularity", "atrFactorType": "Factor Type", "atrSystemCode": "Applicable Platform", "atrModelFactorEntryRefInfo": "Quantification Factor Posting Mapping Configured Information", "atrModelPlanInfo": "Model Configured Information", "atrCalEach": "(Every Period)", "atrCalPrevious": "(Last Period)", "atrCalCurrent": "(Current Period)", "atrCalNext": "(Next Period)", "atrCalDevelop": "(Future Development Period)", "atrSelectFactor": "Select Quantification Factor", "atrAccrualDimension": "Accrual Granularity", "atrSelectEntryFactor": "Select Posting Quantification Factor", "atrModelRelationRuleMsg": "This Model Has Been Applied to Quantification Rule, and Cannot Be Deleted!", "atrFactor": "Quantification Factor", "atrIbnrType": "IBNR Type", "atrStatisZones": "Extraction Interval", "atrIbnrCalculate": "IBNR Calculation", "atrTotalCompensationFlowTriangle": "Cumulated Claim Run-Off Triangle", "atrReparationsCDFFlowTriangle": "Claim CDF Run-Off Triangle", "atrBFMethodEstimateLossRate": "BF Method Estimate Loss Rate", "atrBFCalculatesIBNR": "BF Method Calculated IBNR", "atrAccidentTime": "Time of Accident", "atrCalculateComplete": "Calculation Completed!", "atrCalculateTips": "Calculation Completed, Result Can Be Viewed in Step Four!", "atrPaymentDelayTime": "<PERSON><PERSON><PERSON>ed <PERSON>", "atrPaymentDelay": "Settlement Delayed Time(Total)", "atrFC": "Final Settled", "atrTOS": "Total Unpaid Claims", "atrIBNR": "IBNR ", "atrPCTT": "Indemnity Forecast Run-off Triangle", "atrRisk": "Product Risk", "atrEvalPVCFOfContractGroupData": "PV Of Expected Cash Flow Of Contract Group", "atrBussUnitNoList": "Quantification Unit Data", "atrIcgDataList": "Quantification Detailed Information (Contract Group)", "atrUnitDataList": "Quantification Detailed Information (Quantification Unit)", "ATR_MEAS_ERROR_001": "Assessment Date Is Not Earlier than Current Business Period, Please Check!", "ATR_MEAS_ERROR_002": "Accounting Period Base Currency Is Not Configured, Please Check!", "ATR_MEAS_ERROR_003": "Business Model Is Not Configured with Quantification Model, Please Check!", "ATR_MEAS_ERROR_004": "Business Model Is Not Configured with Business Line, Please Check!", "ATR_MEAS_ERROR_005": "Business Model Has Business Line Which Is Not Configured with Assumption Data, Please Check!", "ATR_MEAS_ERROR_006": "Assuming the data is empty or does not comply with the entry rules, please check!", "ATR_MEAS_ERROR_007": "The business model has no confirmed BECF data during this evaluation period, and measurement cannot start. Please check!", "atrCenterAndModelIsNull": "Business Unit,Model Enquiry, and assessment period Enquiry Criteria Cannot Be Null!", "atrBecfType": "Cash Flow Type", "atrValuationDate": "Assessment Date", "atrClaimCashFlowImport": "Claim Cash Flow Import", "atrClaimRunNo": "No", "atrClaimCfDate": "Date", "atrExpectedClaimCurrent": "Expected <PERSON><PERSON><PERSON>", "atrExpectedClaimPast": "Expected <PERSON><PERSON><PERSON>", "atrExpectedUlaeCurrent": "Expected <PERSON><PERSON><PERSON>", "atrExpectedUlaePast": "Expected <PERSON><PERSON><PERSON>", "atrPremiumCashFlowImport": "Premium cash flow import", "atrLrcMaintenanceExpense": "Maintenance Expense", "atrLrcExpectedClaim": "Expected <PERSON><PERSON><PERSON>", "atrLrcCoverageUnit": "Insurance Amount/Limit", "atrLrcExpectedBrokerage": "Broker <PERSON>e", "atrLrcExpectedAdjustmentOfCommission": "Expected adjustment of RI provisional commison", "atrLrcExpectedClaimAdjustmentOfCommission": "Expected claim with Expected adjustment of RI provisional commison", "atrLrcCSMAmortizedRate": "CSM Amortized Rate", "atrLrcAccumulatedEarned Rate": "Accumulated Earned Rate", "atrLrcCurEndRemainUnRate": "Remaining unamortized proportion at the end of the current period", "atrLrcRptPerRemainUnRate": "Remaining unamortized proportion as of the beginning of the reporting period", "atrLrcLpCurEndRemainUnRate": "(last period) remaining unamortized proportion at the end of the current period", "atrLrclpRptPerRemainUnRate": "(Last period) Remaining unamortized proportion as of the beginning of the reporting period", "atrLrcPremReceipt": "Prem Receipt", "atrLrcUEPProjection": "UEP Projection", "atrTitleAcqExp": "Acquisition fee", "atrLrcBrokerageCf": "Expected Brokerage", "atrLrcAdjCommCf": "Expected adjustment of RI provisional commission", "atrTitleCfPut": "Lrc Cash Flow", "atrTitlePolicyNoEndorsement": "Business No.(Policy No.-Endorsement/Bill No.)", "atrConfQuotaDimension": "Dimension", "atrConfQuotaDimensionValue": "Dimension Value", "atrConfDimensionTip": "The granularity of the assumed value marked in purple is smaller than that of the contract combination. It is used to measure that the assumed value is not found in the corresponding dimension, and the assumed value can be obtained in the contract combination", "atrInterestRateType": "Interest Rate Type", "atrInsuredName": "Insurer", "atrUwYear": "Underwriting Year", "atrDelinquencyRate": "Delinquency Ratio", "atrDefaultRate": "Bad Loans Ratio", "atrAlDateLoanOs": "Lend Loans", "atrRunOffPattern": "Run-Off Pattern", "atrLiquiditySettings": "Basic Parameter Settings", "atrUltimateYear": "Ultimate Year", "atrFinalLiquidityPeriod": "Final Liquidity Period (LLP)", "atrUltimateRate": "Ultimate Rate", "atrUltimateLiquidityPremium": "Ultimate Liquidity Premium", "atrValidNotPeriodMsg": " not contain development period data, please re-enter!", "atrValidHasMortMsg": "Line already has the same configuration, please re-enter!", "atrValidSameMsg": "Upload Excel with the same policy number, please re-enter！", "atrOsImportTitle": "O/S Information Import", "atrUsedOsInfoIs": "Whether to import O/S information", "atrDOA": "Accident Month", "atrOS": "O/S", "atrCurClaimPaid": "<PERSON><PERSON> Amount In The Current Period", "atrPaidMode": "Paid <PERSON>", "atrSelectValue": "Select Value", "atrExpectedClaim": "Expected <PERSON><PERSON><PERSON>", "atrindirectClaimFeeRate": "Indirect Claim Expense Loss Rate", "atrosIndirectClaimFeeRate": "Indirect Claim Expense Loss Rate O/S", "atrmanulAdjAddiClaim": "Manual Adjustment - <PERSON>tached Claims", "atribnrCur": "IBNR Current", "atribnrPre": "IBNR Past", "atrosCur": "O/S Current", "atrosPre": "O/S Past", "atrulaeCur": "ULAE Current", "atrulaePre": "ULAE Past", "atrAmortizedRate": "Amortized Rate", "atrExpClaimCur": "Expected <PERSON><PERSON><PERSON>", "atrExpClaimPre": "Expected <PERSON><PERSON><PERSON>", "atrDataKey": "Data Key", "atrLoaCode": "LOA Code", "atrCheckDateInDate": "Check Date In Date", "atrAuditStartDate": "Audit Start Date", "atrAuditEndDate": "Audit End Date", "atrAuditOrAccidentDate": "Audit Or Accident Date", "atrAccidentStartDate": "Start Date Of Accident", "atrAccidentEndtDate": "End Date Of Accident", "atrEffectiveDateBom": "EffectiveDate(Bom)", "atrExpiryDateEom": "ExpiryDate(Eom)", "atrGrossPremium": "Gross Premium", "atrNetPremium": "Net Premium", "atrNonAcqExpense": "Non Documentary Acquisition Fees", "atrCoverageAmount": "Coverage Amount", "atrFacultativeIs": "Facultative Is", "atrPassedDates": "Passed Dates", "atrPassedMonths": "Passed Months", "atrRemainingMonths": "Remaining Months", "atrRemainingPremTermPe": "Remaining PremTerm Pe", "atrRemainingMonthsFuture": "Remaining Months Future", "atrRemainingPremTermCb": "Remaining Prem Term Cb", "atrPaymentQuarter": "Payment Quarter", "atrGepEdPremiumCoverageMonth": "Gep Ed Premium Coverage Month", "atrGepEdPremium": "Gep Ed Premium", "atrRemainingQuartersCb": "Remaining Quarters Cb", "atrExpectedPremiumReceivable": "Expected Premium Receivable", "atrEdPremiumGEP": "Earned Premium(GEP)", "atrEdPremiumUEP": "Unearned Premium", "atrAdjustmentFee": "Adjustment Fee", "atrExpectedBrokerage": "Broker <PERSON>e", "atrAccidentYearMonthDevelop": "Accident Date/Development Periods", "atrCfTypeClassTitle": "Quantification cash flow version information", "atrCfTypeClass": "Cash Flow Category", "atrLicCashFlowTitle": "Details Of Expected Claim Cash Flow", "atrLrcCashFlowTitle": "Details Of Expected Cash Flow Of Premium Compensation", "atrLrcCfIcgFee": "Contract Group Dimension", "atrLrcCfPolicyFee": "Policy Dimension", "atrExistConfirmSuccess": "Confirmed Selection Exists, Please Select Again!", "accDataPendingInfo": "Business Data Information", "atrExtractionTip": "Extraction Is Under Progress, Please Check The Result Later", "atrLicCashFlowDetailTitle": "Expected Claim Cash Flow Contract Portfolio Information", "atrActionNo": "Action No.", "atrQuotaLoadPrePeriod": "Load Previous Assumptions", "atrQuotaLoadMsg": "There are no loadable assumptions during the previous evaluation period", "atrExtractInterval": "Extract Interval", "atrExtractIntervalNum": "Extract interval number", "atrAdaptationEvaluationPeriod": "Adaptation evaluation period", "atrWeightedParameterConfig": "Weighted Parameter Config", "atrUnderwritingDate": "Underwriting Date", "atrSettledExpense": "Settled Expense", "atrOSClaim": "<PERSON>/<PERSON>", "atrOSExpense": "O/S Expense", "atrOSYearMmonth": "O/S Statistical year and year", "atrAmount": "Amount", "atrAssumpParaDetails": "Assumption parameter calculation details", "atrWeightedParameterInformation": "Weighted parameter information", "atrINNRCalculaConfig": "IBNR Calculation Development Period Allocation", "atrINNRTimePeriod": "Time period", "atrINNRInterest": "Interest", "atrReserveEarnedDetail": "Details Of Earned Prem&Released Comm Reserve", "atrReleasedCommission": "Released Commission", "atrTransverseView": "Landscape view", "atrDirectionView": "Vertical view", "atrConfQuotaClass": "Assumption Classification", "atrConfQuotaEffect": "Assumption Impact", "atrLicCashFlow": "Expected Claim Cash Flows", "atrLrcCashFlow": "Expected Premium Cash Flows", "atrEvaluateReCalc": "<PERSON><PERSON>", "atrEvaluateCfNoConfirm": "Not confirmed yet", "atrEvaluateCfSubmit": "Please confirm that the expected cash flow for cancellation has been confirmed and recalculated", "atrEvaluateToCfPage": "Please confirm entering the expected cash flow page", "gLoadSuccessful": "Successfully Loaded", "gBtnToExcelQuantification": "Export Results", "atrWarnNoSearchData": "No quantification result data", "atrUploadSelectFile": "Select File", "atrDrawType": "Extraction Method", "atrControlExcelImportMsg": "The number of imported files is limited to between 1 and 60000!", "atrUlt": "Cumulative Payment % to Ultimate", "gAssumeBatchImportMsg": "Batch import will overwrite the existing assumed value configuration in the system. Please note!", "gDmEndorseSeqNo": "Endorse Seq No.", "gEvaluateDate": "Current Assessment Date", "gDmBorderDate": "Contract Recognition Date", "atrExpectedLossRate": "Expected Loss Rate", "atrClaimExpenseRate": "Indirect Claims Expense Rate", "atrRARate": "Non financial risk adjustment rate", "atrClassificationMark": "Classification Mark", "atrEvaluationMonth": "Evaluation Month", "atrInsuranceTypeCode": "Types Insurance Code", "atrOrganizationCode": "Organization Code", "atrAccidentMonth": "Accident Month", "atrIBNREvalAmount": "IBNR Evaluation Amount", "atrEarnedWeight": "Earned Weight", "atrDecidedWeight": "Decided Weight", "atrBackLookMonth": "Looking Back Month", "atrInstitutionalHierarchy": "Institutional Hierarchy", "atrCompensationMonth": "Compensation Month", "atrRegulatoryCode": "Regulatory Category Code", "atrInsuranceCode": "Insurance Code", "atrInTypeCode": "Insurance Type Code", "atrLegalRMB": "Legal amount in RMB", "atrAllocatioType": "Allocation business type", "atrContractGroupNumber": "Contract group number", "atrCONTRACTNO": "CONTRACT NO", "atrDecidedPremium": "Decided premium", "atrPendingPremium": "Pending premium", "atrParticipate": "Whether to participate in the allocation", "atrPendingWeight": "Pending weight", "atrSharingBase": "Sharing base", "atrAllocationRatio": "Allocation ratio", "atrIBNRResults": "IBNR results", "atrIBNRSyncTable": "Sync Table", "atrMaintenanceCostRate": "Maintain expense ratio", "atrAdjFeeRate": "Adjust the handling fee rate", "atrProfitFeeRate": "Pure profit procedure fee rate", "atrBadDebt": "Premium Bad Debt Adjustment", "atrInterestRateTemplate": "Risk-free Interest Rate Curve Template", "atrBecfFileName": "Expected Premium Cash Flows", "atrDDBecfFileName": "Direct", "atrTIBecfFileName": "Treaty_In", "atrFOBecfFileName": "Fac_Out", "atrTOBecfFileName": "Treaty_Out", "atrStatementPeriod": "Account period", "atrIacf": "IACF", "atrAccumPaidIacf": "Accumulated actual payment of IACF", "atrAccumPaidPremium": "Earned Premium", "atrIbnrKindCode": "Risk Kind", "atrPresentValueBeginCurRate": "Present Value at Beginning of t (Current Rate)", "atrPresentValueBeginLockRate": "Present Value at Beginning of t (Lock-in Rate)", "atrPresentValueEndCurRate": "Present Value at End of t (Current Rate)", "atrPresentValueEndLockRate": "Present Value at End of t (Lock-in Rate)", "atrPresentValueChangeCurRate": "Change in Present Value at End (Current Rate)", "atrPresentValueChangeLockRate": "Change in Present Value at End (Lock-in Rate)", "atrBtnEPIImport": "Import EPI", "atrDataType": "Data Type", "atrBtnEarnedImport": "Import Earned Premium", "atrDataImport": "Data Import", "atrInsuranceClass": "Insurance Class", "atrContractNo": "Contract No", "atrTreatyName": "Contract Name", "atrImportDate": "Import Date", "atrContractEffectiveDate": "Contract Effective Date", "atrContractExpiryDate": "Contract Expiry Date", "preEdNetFee": "PNetFee", "feeRate": "Netting fee rate", "atrPreCumlEdIacf": "Acquisition fee earned in previous period for followers", "atrIaehcIn": "Non-documentary Acquisition Expense - Internal", "atrPreCumlEdIaehcIn": "Previous Period Accumulated Earned Non-documentary Acquisition Expense - Internal", "atrIaehcOut": "Non-documentary Acquisition Expense - External", "atrPreCumlEdIaehcOut": "Previous Period Accumulated Earned Non-documentary Acquisition Expense - External", "atrPreCumlPaidPremium": "Previous Period Accumulated Actual Premium Received", "atrPreCumlPaidNetFee": "Previous Period Accumulated Actual Net Settlement Fee Received", "atrLapseRate": "Expected Lapse Rate", "atrMtRate": "Expected Maintenance Expense Rate", "atrClaimRate": "Expected Claim Rate", "atrUlaeRate": "Unexpired Indirect Claim Expense Rate", "atrSectionoCode": "Reinsurer Section", "atrReinsurerCode": "Reinsurer Code", "atrRiEffectiveDate": "Contract Effective Date", "atrRiExpiryDate": "Contract Expiry Date", "atrPrepaidFeeRate": "Prepaid Fee Rate", "atrFloatingHandlingFeeCap": "Floating Handling Fee Cap", "atrInvAmount": "Investment Component", "atrDevNo": "Development Period", "atrPaidEdPremium": "<PERSON><PERSON> Earned Premium", "atrEdPremium": "Earned Premium (incl. EPI)", "atrPaidNetFee": "<PERSON><PERSON>", "atrEdNetFee": "Earned <PERSON> (incl. EPI)"}