package com.ss.ifrs.actuarial.service;

 import java.util.List;

import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfRiskRefVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;

public interface AtrConfRiskRefService {

    void save(AtrConfRiskRefVo atrConfQuotaVo, Long userId);

    void saveList(List<AtrConfRiskRefVo> atrConfQuotaVos, Long userId);

    AtrConfRiskRefVo findById(Long riskRefId);

    AtrConfRiskRefVo findHisById(Long riskRefHisId);

    Page<AtrConfRiskRefVo> searchPage(AtrConfRiskRefVo atrConfQuotaVo, Pageable pageParam);

    void update(AtrConfRiskRefVo atrConfQuotaVo, Long userId);

    void delete(Long confQuotaId, Long userId);

    void updateValid(AtrConfRiskRefVo dmConfigCheckRuleVo, Long userId);

    void auditList(List<AtrConfRiskRefVo> atrConfQuotaVos, Long userId);

    void audit(AtrConfRiskRefVo atrConfQuotaVo, Long userId);

    Long checkNaturalPk(AtrConfRiskRefVo atrConfQuotaVo);

}
