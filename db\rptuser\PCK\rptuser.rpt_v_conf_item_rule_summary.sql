CREATE OR REPLACE VIEW RPT_V_CONF_ITEM_RULE_SUMMARY AS
SELECT DISTINCT r2.entity_id,
                r2.book_code,
                r1.upper_report_item_id AS report_item_id,
                ( SELECT r3.report_item_code
                  FROM rpt_conf_report_item r3
                  WHERE r3.report_item_id = r1.upper_report_item_id) AS report_item_code,
                LISTAGG(('[' || r1.report_item_code) || ']', '+') WITHIN GROUP (ORDER BY r1.report_item_code) AS summary_expr
FROM rpt_conf_report_item r1,
     rpt_conf_report_item_rule r2
WHERE r1.report_item_id = r2.report_item_id AND (r1.upper_report_item_id IN ( SELECT r4.report_item_id
                                                                              FROM rpt_conf_report_item_rule r4))
GROUP BY r2.entity_id, r2.book_code, r1.upper_report_item_id
ORDER BY (( SELECT r3.report_item_code
            FROM rpt_conf_report_item r3
            WHERE r3.report_item_id = r1.upper_report_item_id));