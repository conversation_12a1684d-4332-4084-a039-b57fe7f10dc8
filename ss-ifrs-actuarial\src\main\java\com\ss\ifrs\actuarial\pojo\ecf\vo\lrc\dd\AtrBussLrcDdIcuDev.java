package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.dd;

import com.ss.ifrs.actuarial.util.abp.Tab; // 假设这个 import 是需要的
import lombok.Data;

import java.math.BigDecimal;

/**
 * LRC 单元发展期 (直保&临分分入) - 对应表 atr_buss_dd_lrc_u_dev
 * 注意：此类根据新表结构更新了字段
 */
@Data
@Tab("atr_buss_dd_lrc_u_dev")
public class AtrBussLrcDdIcuDev {

    /** 主表ID (main_id) */
    private Long mainId;

    /** 发展期序号 (dev_no) */
    private Integer devNo; // smallint 通常映射为 Integer

    /** 业务年月 (year_month) - 备注：评估期的年月 */
    private String yearMonth;

    /** 应收保费 (recv_premium) */
    private BigDecimal recvPremium; // decimal -> BigDecimal

    /** 净额结算手续费 (net_fee) */
    private BigDecimal netFee; // decimal -> BigDecimal

    /** 跟单获取费用 (iacf) */
    private BigDecimal iacf; // decimal -> BigDecimal

    /** 非跟单获取费用-对内 (iaehc_in) */
    private BigDecimal iaehcIn; // 新增字段, decimal -> BigDecimal

    /** 非跟单获取费用-对外 (iaehc_out) */
    private BigDecimal iaehcOut; // 新增字段, decimal -> BigDecimal

    /** 减值 (bad_debt) */
    private BigDecimal badDebt; // decimal -> BigDecimal

    /** 已赚比例 (ed_rate) */
    private BigDecimal edRate; // decimal(16, 15) -> BigDecimal

    /** 已赚保费 (ed_premium) */
    private BigDecimal edPremium; // decimal -> BigDecimal

    /** 已赚净额结算 (ed_net_fee) */
    private BigDecimal edNetFee; // decimal -> BigDecimal

    /** 已赚跟单获取费用 (ed_iacf) */
    private BigDecimal edIacf; // decimal -> BigDecimal

    /** 已赚非跟单获取费用-对内 (ed_iaehc_in) */
    private BigDecimal edIaehcIn; // 新增字段, decimal -> BigDecimal

    /** 已赚非跟单获取费用-对外 (ed_iaehc_out) */
    private BigDecimal edIaehcOut; // 新增字段, decimal -> BigDecimal

}