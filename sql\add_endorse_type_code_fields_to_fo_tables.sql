-- 为FO业务临时表添加endorse_type_code特殊处理相关字段

-- 添加字段到 atr_temp_fo_lrc_u 临时表
ALTER TABLE atruser.atr_temp_fo_lrc_u ADD COLUMN IF NOT EXISTS endorse_type_code VARCHAR(100) NULL;
ALTER TABLE atruser.atr_temp_fo_lrc_u ADD COLUMN IF NOT EXISTS special_process_type INTEGER NULL;

-- 添加字段注释
COMMENT ON COLUMN atruser.atr_temp_fo_lrc_u.endorse_type_code IS '批改类型代码（逗号分隔）';
COMMENT ON COLUMN atruser.atr_temp_fo_lrc_u.special_process_type IS '特殊处理类型（0:正常处理, 1:只计算第0期, 2:不计算发展期）';

-- 验证字段是否添加成功
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'atruser' 
  AND table_name = 'atr_temp_fo_lrc_u'
  AND column_name IN ('endorse_type_code', 'special_process_type')
ORDER BY column_name;
