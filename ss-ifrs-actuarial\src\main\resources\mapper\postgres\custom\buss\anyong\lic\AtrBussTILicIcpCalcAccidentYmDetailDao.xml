<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-03-02 15:53:51 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussTILicIcpCalcAccidentYmDetailDao">
  <!-- 本配置文件由SS MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**IDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->

  <resultMap id="CustResultMap" type="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
    <result column="ACCIDENT_YEAR_MONTH" property="accidentYearMonth" jdbcType="VARCHAR" />
    <result column="main_id" property="mainId" jdbcType="DECIMAL" />
    <result column="DEV_NO" property="devNo" jdbcType="DECIMAL" />
    <result column="PAID_MODE" property="paidMode" jdbcType="DECIMAL" />
    <result column="IBNR" property="ibnr" jdbcType="DECIMAL" />
    <result column="OS" property="os" jdbcType="DECIMAL" />
    <result column="ULAE" property="ulae" jdbcType="DECIMAL" />
  </resultMap>

  <select id="findYearMonth" flushCache="false" useCache="true" resultMap="CustResultMap" parameterType="java.lang.Long">
        select distinct ACCIDENT_YEAR_MONTH
        from ATR_BUSS_TI_LIC_ICG_CALC_ACCIDENT_YM_DETAIL
        where main_id = #{mainId,jdbcType=NUMERIC}
        and ACCIDENT_YEAR_MONTH is not NULL
        order by ACCIDENT_YEAR_MONTH
    </select>

  <select id="findPaidMode" flushCache="false" useCache="true" resultMap="CustResultMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
    select
    <include refid="Base_Column_List" />
    from ATR_BUSS_TI_LIC_ICG_CALC_ACCIDENT_YM_DETAIL
    where main_id = #{mainId,jdbcType=NUMERIC}
    and ACCIDENT_YEAR_MONTH = #{accidentYearMonth,jdbcType=VARCHAR}
    order by ACCIDENT_YEAR_MONTH
  </select>
</mapper>