/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2022-01-05 15:08:15
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao.conf;
 
import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcAction;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfModelDef;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.Map;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2022-01-05 15:08:15<br/>
 * Description: 模型定义 Dao类<br/>
 * Related Table Name: atr_conf_model_def<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrConfModelDefDao extends IDao<AtrConfModelDef, Long> {

    Integer countBecfImportConfirm(AtrBussLrcAction bussAction);

    Integer countLrcBecfConfirm(AtrBussLrcAction bussAction);

    Integer countLicBecfConfirm(AtrBussLrcAction bussAction);

    /**
     * @Description: 计量流程日志信息生成
     * @Param : [params]
     * @Return: void
     * @CreateDate: 2023/2/3 20:46
     */
    void saveAtrActionLog(Map<String,Object> params);

}