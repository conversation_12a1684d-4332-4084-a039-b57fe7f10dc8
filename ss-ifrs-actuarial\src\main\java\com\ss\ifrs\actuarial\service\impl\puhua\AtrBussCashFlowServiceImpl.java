package com.ss.ifrs.actuarial.service.impl.puhua;

import com.ss.ifrs.actuarial.conf.AppConfig;
import com.ss.ifrs.actuarial.constant.ActuarialConstant;
import com.ss.ifrs.actuarial.dao.buss.puhua.lic.*;
import com.ss.ifrs.actuarial.dao.buss.puhua.lrc.*;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrAllocActionVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrAllocResultVo;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo;
import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrBussBecfViewVo;
import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrConfBecfOutPutVo;
import com.ss.ifrs.actuarial.service.*;
import com.ss.ifrs.actuarial.service.puhua.AtrBussPuHuaActionService;
import com.ss.library.excel.ExcelSheet;
import com.ss.library.excel.ExcelSheetData;
import com.ss.library.mybatis.handler.CommonResultHandler;
import com.ss.library.utils.ClassUtil;
import com.ss.library.utils.ExcelDownloadUtil;
import com.ss.library.utils.StringUtil;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.model.SsException;
import com.ss.platform.util.ExcelExportUtil;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName: AtrBussCalcServiceImpl
 * @Description: 计量计算服务接口实现类
 * @Author: yinxh.
 * @CreateDate: 2021/3/8 17:58
 * @Version: 1.0
 */
@Service(value = "atrBussPuHuaActionService")
public class AtrBussCashFlowServiceImpl implements AtrBussPuHuaActionService {
	private static final String XLSX = ".xlsx";
	// 日志管理
	final Logger LOG  = LoggerFactory.getLogger(getClass());
	@Autowired
	private AtrBussDDLrcGDao atrBussDDLrcGDao;
	@Autowired
	private AtrBussDDLrcGDevDao atrBussDDLrcGDevDao;
	@Autowired
	private AtrBussDDLrcUDao atrBussDDLrcUDao;
	@Autowired
	private AtrBussDDLrcUDevDao atrBussDDLrcUDevDao;

	@Autowired
	private AtrBussFOLrcGDao atrBussFOLrcGDao;
	@Autowired
	private AtrBussFOLrcGDevDao atrBussFOLrcGDevDao;
	@Autowired
	private AtrBussFOLrcUDao atrBussFOLrcUDao;
	@Autowired
	private AtrBussFOLrcUDevDao atrBussFOLrcUDevDao;

	@Autowired
	private AtrBussTILrcGDao atrBussTILrcGDao;
	@Autowired
	private AtrBussTILrcGDevDao atrBussTILrcGDevDao;
	@Autowired
	private AtrBussTILrcUDao atrBussTILrcUDao;
	@Autowired
	private AtrBussTILrcUDevDao atrBussTILrcUDevDao;

	@Autowired
	private AtrBussTOLrcGDao atrBussTOLrcGDao;
	@Autowired
	private AtrBussTOLrcGDevDao atrBussTOLrcGDevDao;
	@Autowired
	private AtrBussTOLrcUDao atrBussTOLrcUDao;
	@Autowired
	private AtrBussTOLrcUDevDao atrBussTOLrcUDevDao;

	@Autowired
	private AppConfig appConfig;
	@Autowired
	AtrExportService atrExportService;
	@Autowired
	AtrBussBecfQuotaService atrBussBecfQuotaService;

	@Autowired
	private AtrBussLicGDao atrBussLicGDao;
	@Autowired
	AtrBussLicGAccDao atrBussLicGAccDao;
	@Autowired
	AtrBussLicGAccAmountDao atrBussLicGAccAmountDao;
	@Autowired
	AtrBussLicGAccDevAmountDao atrBussLicGAccDevAmountDao;

	public Map<String, String> processLicDownloadFiles(HttpServletRequest request,
								   HttpServletResponse response,
								   AtrBussBecfViewVo atrBussBecfViewVo,
								   List<AtrConfBecfOutPutVo> lrcConfBecfOutPutVos,
								   Long userId) throws Exception {
		if (ObjectUtils.isEmpty(atrBussBecfViewVo)) {
			return null;
		}
		if (ObjectUtils.isEmpty(atrBussBecfViewVo.getLicTemplateFileName())) {
			throw new RuntimeException("Excel Template is null");
		}
		//获取语言
		String language = request.getHeader("ss-Language");
		if(StringUtil.isEmpty(language)){
			language = "en";
		}

		atrBussBecfViewVo.setLicBecfCodes(lrcConfBecfOutPutVos.stream().map(AtrConfBecfOutPutVo:: getOutCode).collect(Collectors.toList()));
		// 查询现金流数据
		List<AtrDapDrawVo> cashFlowList = atrBussLicGDao.listBecfViewList(atrBussBecfViewVo);
		// 收集所有 devNo 并排序，用于生成表头列
		List<Integer> devNoList = cashFlowList.stream()
				.map(AtrDapDrawVo::getDevNo)
				.distinct()
				.sorted()
				.collect(Collectors.toList());

		// 查询折现数据
		List<AtrDapDrawVo> discountList = atrBussLicGDao.listBecfViewDiscount(atrBussBecfViewVo);

		atrBussBecfViewVo.setLicTemplateFileName(atrBussBecfViewVo.getLicTemplateFileName()+"_"+language);
		// 生成模板
		String templateExcelFilePath = generateNewTemplateExcel(devNoList, atrBussBecfViewVo);


		// 对现金流和折现数据进行预处理
		List<Map<String, Object>> cashFlowData = formatCashFlowData(cashFlowList, devNoList);
		List<Map<String, Object>> discountData = formatDiscountData(discountList);

		// 填充数据到Excel
		return fillNewLicDataToExcel(request, response, atrBussBecfViewVo, templateExcelFilePath, cashFlowData, discountData);
	}

	/**
	 * @Description: 生成符合要求的Excel模板，包含两个Sheet
	 * @Author: wyh.
	 * @CreateDate: 2024/2/27
	 */
	private String generateNewTemplateExcel(List<Integer> devNoList, AtrBussBecfViewVo atrBussLicCashFlowVo) {
		// 获取原始模板路径
		String basePath = appConfig.getBasePath();
		String excelPath = appConfig.getExportExcelTemplate();
		String templatePath = appConfig.getExportExcelOutTemplate();
		String originalTemplateFile = basePath + excelPath + atrBussLicCashFlowVo.getLicTemplateFileName() + ".xlsx";

		// 生成临时模板文件路径
		Date outPutTime = new Date();
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
		String timestamp = formatter.format(outPutTime);
		String tempTemplateFile = basePath + templatePath + atrBussLicCashFlowVo.getLicTemplateFileName() + "_" + timestamp + ".xlsx";

		// 按devNoList动态修改模板
		try (FileInputStream fis = new FileInputStream(originalTemplateFile);
			 Workbook workbook = WorkbookFactory.create(fis)) {

			// 获取第一个Sheet
			Sheet sheet = workbook.getSheetAt(0);

			// 获取标题行
			Row titleRow = sheet.getRow(0);
			Row headerRow = sheet.getRow(1);
			if (headerRow == null) {
				headerRow = sheet.createRow(1);
			}


			// 基础列数量（合同组、保单号、险别代码、事故发生月、现金流类型）
			int baseColumnCount = 8;
			if ("TI".equals(atrBussLicCashFlowVo.getBusinessSourceCode())) {
				baseColumnCount = 7;
			}
			// 从现有列获取样式作为参考
			CellStyle titleCellStyle = null;
			CellStyle headerCellStyle = null;

			// 获取已有单元格的样式，确保至少有一个已有列
			if (titleRow.getCell(0) != null) {
				titleCellStyle = titleRow.getCell(0).getCellStyle();
			}
			if (headerRow.getCell(0) != null) {
				headerCellStyle = headerRow.getCell(0).getCellStyle();
			}

			// 设置devNo列标题
			for (int i = 0; i < devNoList.size(); i++) {
				Integer devNo = devNoList.get(i);
				// 列索引从0开始，前5列是固定列
				int columnIndex = baseColumnCount + i;

				// 创建或获取标题行单元格
				Cell titleRowCell = titleRow.getCell(columnIndex);
				if (titleRowCell == null) {
					titleRowCell = titleRow.createCell(columnIndex);
				}
				titleRowCell.setCellValue(devNo);

				// 应用标题行样式
				if (titleCellStyle != null) {
					titleRowCell.setCellStyle(titleCellStyle);
				}

				// 创建或获取头部行单元格
				Cell cell = headerRow.getCell(columnIndex);
				if (cell == null) {
					cell = headerRow.createCell(columnIndex);
				}
				cell.setCellValue("{df." + (i+1) + "}");

				// 应用头部行样式
				if (headerCellStyle != null) {
					cell.setCellStyle(headerCellStyle);
				}

				// 设置列宽
				sheet.setColumnWidth(columnIndex, 12 * 256); // 12个字符宽度
			}

			// 保存修改后的模板
			try (FileOutputStream fos = new FileOutputStream(tempTemplateFile)) {
				workbook.write(fos);
			}

			LOG.info("成功生成动态模板: {}", tempTemplateFile);
			return tempTemplateFile;

		} catch (Exception e) {
			LOG.error("动态修改模板失败", e);
			// 失败时返回原始模板
			return originalTemplateFile;
		}
	}

	/**
	 * @Description: 将现金流数据格式化为图1所示的格式
	 * @Author: wyh.
	 * @CreateDate: 2024/2/27
	 */
	private List<Map<String, Object>> formatCashFlowData(List<AtrDapDrawVo> cashFlowList, List<Integer> devNoList) {
		// 创建结果列表
		List<Map<String, Object>> resultList = new ArrayList<>();


		// 分组的key
		Map<String, Map<String, Object>> groupingMap = new HashMap<>();

		// 按合同组、保单号、险别代码、事故年月、费用类型分组
		for (AtrDapDrawVo vo : cashFlowList) {
			String groupKey = vo.getIcgNo() + "|" + vo.getPolicyNo() + "|" +
					vo.getKindCode() + "|" + vo.getAccidentYearMonth() + "|" + vo.getFeeType();

			// 如果该组合不存在，创建一个新行
			if (!groupingMap.containsKey(groupKey)) {
				Map<String, Object> rowData = new LinkedHashMap<>();
				// 使用与Excel模板中的占位符完全匹配的字段名
				rowData.put("yearMonth", vo.getYearMonth());
				rowData.put("icgNo", vo.getIcgNo());
				rowData.put("icgNoName", vo.getIcgNo());
				if ("TI".equals(vo.getBusinessSourceCode())) {
					rowData.put("treatyNo", vo.getTreatyNo());
					rowData.put("riskClassCode", vo.getRiskClassCode());
				} else {
					rowData.put("businessSourceCode", vo.getBusinessSourceCode());
					rowData.put("riskClassCode", vo.getRiskClassCode());
					rowData.put("kindCode", vo.getKindCode());
				}
				rowData.put("accYearMonth", vo.getAccidentYearMonth());
				rowData.put("feeType", vo.getFeeType());

				// 初始化所有发展期列为null
				for (int i = 0; i < devNoList.size(); i++) {
					// 发展期列的序号从1开始
					rowData.put(String.valueOf(i+1), null);
				}

				groupingMap.put(groupKey, rowData);
				resultList.add(rowData);
			}

			// 设置对应devNo的数据
			Map<String, Object> rowData = groupingMap.get(groupKey);
			if (vo.getDevNo() != null) {
				// 找到当前devNo在devNoList中的索引位置
				int index = devNoList.indexOf(vo.getDevNo());
				if (index >= 0 && index < devNoList.size()) {
					// 索引从0开始，但列名从1开始
					rowData.put(String.valueOf(index+1), vo.getAmount());
				}
			}
		}

		return resultList;
	}


	/**
	 * @Description: 将折现数据格式化为图2所示的格式
	 * @Author: wyh.
	 * @CreateDate: 2024/2/27
	 */
	private List<Map<String, Object>> formatDiscountData(List<AtrDapDrawVo> discountList) {
		// 创建结果列表，直接处理数据不使用过于复杂的嵌套Map
		List<Map<String, Object>> resultList = new ArrayList<>();

		// 分组的key
		Map<String, Map<String, Object>> groupingMap = new HashMap<>();

		// 按合同组、事故发生月、费用类型分组
		for (AtrDapDrawVo vo : discountList) {
			String groupKey = vo.getIcgNo() + "|" + vo.getAccidentYearMonth() + "|" + vo.getFeeType();

			// 如果该组合不存在，创建一个新行
			if (!groupingMap.containsKey(groupKey)) {
				Map<String, Object> rowData = new LinkedHashMap<>();
				// 使用与Excel模板中的占位符完全匹配的字段名
				rowData.put("icgNo", vo.getIcgNo());
				rowData.put("accYearMonth", vo.getAccidentYearMonth());
				rowData.put("feeType", vo.getFeeType());

				// 初始化所有金额列为null
				rowData.put("L1", null);
				rowData.put("L2", null);
				rowData.put("L3", null);
				rowData.put("L4", null);
				rowData.put("C1", null);
				rowData.put("C2", null);

				groupingMap.put(groupKey, rowData);
				resultList.add(rowData);
			}

			// 设置对应金额类型的数据
			if (vo.getAmountType() != null) {
				Map<String, Object> rowData = groupingMap.get(groupKey);
				rowData.put(vo.getAmountType(), vo.getAmount());
			}
		}
		return resultList;
	}

	/**
	 * @Description: 填充格式化后的数据到Excel上
	 * @Author: wyh.
	 * @CreateDate: 2024/2/27
	 */
	private Map<String,String> fillNewLicDataToExcel(HttpServletRequest request, HttpServletResponse response,
									   AtrBussBecfViewVo atrBussBecfViewVo, String templateFile,
									   List<Map<String, Object>> cashFlowData, List<Map<String, Object>> discountData) throws Exception {

		// 确保数据不为空
		if (cashFlowData.isEmpty() || discountData.isEmpty()) {
			LOG.warn("导出数据为空，cashFlowData size: {}, discountData size: {}",
					cashFlowData.size(), discountData.size());
		}

		// 创建Sheet数据列表
		List<ExcelSheet> sheetList = new ArrayList<>();

		// 第一个sheet - 现金流数据
		List<ExcelSheetData> cashFlowSheetDataList = new ArrayList<>();
		ExcelSheetData cashFlowSheetData = new ExcelSheetData("df", cashFlowData);
		cashFlowSheetDataList.add(cashFlowSheetData);
		ExcelSheet cashFlowSheet = new ExcelSheet(0, cashFlowSheetDataList);
		sheetList.add(cashFlowSheet);

		// 第二个sheet - 折现数据
		List<ExcelSheetData> discountSheetDataList = new ArrayList<>();
		ExcelSheetData discountSheetData = new ExcelSheetData("df", discountData);
		discountSheetDataList.add(discountSheetData);
		ExcelSheet discountSheet = new ExcelSheet(1, discountSheetDataList);
		sheetList.add(discountSheet);
		return atrExportService.exportSheetListReturnName(request,response, sheetList,
				templateFile,
				atrBussBecfViewVo.getLicTemplateFileName(),
				atrBussBecfViewVo.getLogFileName(),
				atrBussBecfViewVo.getTargetRouter(),
				atrBussBecfViewVo.getCreatorId());
	}



	public List<Integer> findIcgDevNo(AtrBussBecfViewVo atrBussBecfViewVo) {
		List<Integer> icuDevNoList = new ArrayList<>();
		switch (atrBussBecfViewVo.getBusinessSourceCode()) {
			case "DD":
				icuDevNoList = atrBussDDLrcGDevDao.findByDevVo(atrBussBecfViewVo);
				break;
			case "FO":
				icuDevNoList = atrBussFOLrcGDevDao.findByDevVo(atrBussBecfViewVo);
				break;
			case "TI":
				icuDevNoList = atrBussTILrcGDevDao.findByDevVo(atrBussBecfViewVo);
				break;
			case "TO":
				icuDevNoList = atrBussTOLrcGDevDao.findByDevVo(atrBussBecfViewVo);
				break;
		};
		return icuDevNoList;
	}


	public List<Integer> findPeriodHeaderData(AtrBussBecfViewVo atrBussBecfViewVo) {
		List<Integer> icuDevNoList = new ArrayList<>();
		switch (atrBussBecfViewVo.getBusinessSourceCode()) {
			case "DD":
				icuDevNoList = atrBussDDLrcUDevDao.findByDevVo(atrBussBecfViewVo);
				break;
			case "FO":
				icuDevNoList = atrBussFOLrcUDevDao.findByDevVo(atrBussBecfViewVo);
				break;
			case "TI":
				icuDevNoList = atrBussTILrcUDevDao.findByDevVo(atrBussBecfViewVo);
				break;
			case "TO":
				icuDevNoList = atrBussTOLrcUDevDao.findByDevVo(atrBussBecfViewVo);
				break;
		};
		return icuDevNoList;
	}


	@Override
	@Async
	public void becfDownload(AtrBussBecfViewVo atrBussBecfViewVo, HttpServletRequest request, HttpServletResponse response) throws Exception {
		if (ObjectUtils.isNotEmpty(atrBussBecfViewVo)) {
			String zipName = atrExportService.getZipName(atrBussBecfViewVo.getLrcTemplateFileName(), atrBussBecfViewVo.getLogFileName());
			String logPath = atrExportService.getOutPutPathSave() + zipName;;
			Long logId = atrExportService.saveExportTrackLog(zipName, atrBussBecfViewVo.getTargetRouter(), logPath, atrBussBecfViewVo.getCreatorId(), false);
			boolean taskStatus = true;
			String errorMsg = null;
			atrBussBecfViewVo.setZipName(zipName);
			atrBussBecfViewVo.setBusinessSourceCode(atrBussBecfViewVo.getBusinessSourceCode());
			try{
				this.downloadData1(atrBussBecfViewVo.getActionNo(), atrBussBecfViewVo, request, response);
			} catch (Exception e) {
				taskStatus = false;
				errorMsg = ExceptionUtils.getStackTrace(e);
				if (errorMsg.length() >= 3900) {
					errorMsg = errorMsg.substring(1, 3900);
				}
				LOG.info(errorMsg);
			} finally {
				atrExportService.updateExportTrackLog(logId,
						taskStatus ? CommonConstant.ExportLogTaskStatus.SUCCESSFUL : CommonConstant.ExportLogTaskStatus.FAIL
						, atrBussBecfViewVo.getCreatorId(), errorMsg);
			}

		}
	}

	/**
	 * @param actionNo
	 * @param atrBussBecfViewVo
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	private void downloadData1(String actionNo, AtrBussBecfViewVo atrBussBecfViewVo, HttpServletRequest request, HttpServletResponse response) throws Exception {
		/**
		 * 1 查询lrc与lic的现金流配置
		 * 2 循环lrc现金流配置
		 *   2.1 一个lrc费用生成一个excel文件
		 *   2.2 放入压缩包
		 * 3 循环lic现金流配置
		 *   2.1 一个lic费用生成一个excel文件
		 *   2.2 放入压缩包
		 * 4 记录日志
		 */

		AtrBussBecfViewVo atrBussQuotaValueVo = new AtrBussBecfViewVo();
		atrBussQuotaValueVo.setBusinessSourceCode(atrBussBecfViewVo.getBusinessSourceCode());
		atrBussQuotaValueVo.setBecfType("Lrc");
		atrBussQuotaValueVo.setLrcBecfIds(atrBussBecfViewVo.getLrcBecfIds());
		List<AtrConfBecfOutPutVo> lrcConfBecfOutPutVos = atrBussBecfQuotaService.findBecfListByIds(atrBussQuotaValueVo);

		// 创建一个统一的文件集合，用于存储所有需要打包的文件
		Map<String, String> allFiles = new HashMap<>();

		// 处理Lrc模板导出
		Map<String, String> toFiles = processDownload(actionNo, atrBussBecfViewVo, lrcConfBecfOutPutVos, request, response);
		if (ObjectUtils.isNotEmpty(toFiles)) {
			allFiles.putAll(toFiles);
		}

		// 将所有文件打包成一个ZIP
		if (ObjectUtils.isNotEmpty(allFiles)) {
			//String zipName = atrExportService.getZipName(atrBussBecfViewVo.getLrcTemplateFileName(), atrBussBecfViewVo.getLogFileName());
			//atrBussBecfViewVo.setZipName(zipName);
			atrExportService.dealZip(atrBussBecfViewVo.getZipName(), allFiles, atrBussBecfViewVo.getTargetRouter(), atrBussBecfViewVo.getCreatorId());
		}
	}

	// 抽取的处理下载逻辑，返回生成的文件集合而不是直接打包
	private Map<String, String> processDownload(String actionNo,
												AtrBussBecfViewVo atrBussBecfViewVo,
												List<AtrConfBecfOutPutVo> lrcConfBecfOutPutVos,
												HttpServletRequest request,
												HttpServletResponse response) throws Exception {
		AtrBussBecfViewVo bussBecfViewVo = ClassUtil.convert(atrBussBecfViewVo, AtrBussBecfViewVo.class);
		Map<String, String> files = new HashMap<>();
		List<Integer> icuDevNoList = this.findPeriodHeaderData(bussBecfViewVo);
		List<Integer> icgDevNoList = this.findIcgDevNo(bussBecfViewVo);

		if (ObjectUtils.isNotEmpty(lrcConfBecfOutPutVos)) {
			for(int j = 0; j< lrcConfBecfOutPutVos.size(); j++){
				Map<String, String> excelName =
						this.exportHandlerDDAllocData(atrBussBecfViewVo.getLrcTemplateFileName(),
								atrBussBecfViewVo,
								lrcConfBecfOutPutVos.get(j),
								icuDevNoList,
								icgDevNoList);
				files.putAll(excelName);
			}
		}
		LOG.info("LRC导出数据查询结束时间：" + LocalTime.now());
		return files;
	}

	private Map<String, String> exportHandlerDDAllocData(String fileName,
														 AtrBussBecfViewVo atrBussBecfViewVo,
														 AtrConfBecfOutPutVo atrConfBecfOutPutVo,
														 List<Integer> icuDevNoList,
														 List<Integer> icgDevNoList
														 ) throws IOException {
		Map<String, String> mapList = new HashMap<>(2);
		if(ObjectUtils.isEmpty(atrBussBecfViewVo) && ObjectUtils.isEmpty(atrBussBecfViewVo.getBusinessSourceCode())){
			return mapList;
		}
		atrBussBecfViewVo.setFeeType(atrConfBecfOutPutVo.getOutCode());
		Boolean isIcg = "G".equals(atrConfBecfOutPutVo.getDimension());
		Boolean isDev = "1".equals(atrConfBecfOutPutVo.getType());
		atrBussBecfViewVo.setDevNoList(Collections.emptyList());
		if (isIcg && isDev) {
			atrBussBecfViewVo.setDevNoList(icgDevNoList.stream().filter(devNo -> devNo<= ActuarialConstant.Export.CF_MAX_COLUMN_DEV).collect(Collectors.toList()));
		}
		if (!isIcg && isDev) {
			atrBussBecfViewVo.setDevNoList(icuDevNoList.stream().filter(devNo -> devNo<= ActuarialConstant.Export.CF_MAX_COLUMN_DEV).collect(Collectors.toList()));
		}

		String fileSavePath = atrExportService.getOutPutPath();
		String tempFilePath = atrExportService.getTemplatePath() + "lrc/" + fileName + atrConfBecfOutPutVo.getDisplayNo() + ".xlsx";
		tempFilePath = ExcelExportUtil.dealFilePath(tempFilePath, "en");

		CommonResultHandler<Map<String, Object>> resultHandler = null;
		Long maxRow = null;

		switch (atrBussBecfViewVo.getBusinessSourceCode()) {
			case "DD":
				if (isIcg) {
					maxRow = atrBussDDLrcGDao.countLrcGDetail(atrBussBecfViewVo);
				} else {
					maxRow = atrBussDDLrcUDao.countLrcUDetail(atrBussBecfViewVo);
				}
				break;
			case "FO":
				if (isIcg) {
					maxRow = atrBussFOLrcGDao.countLrcGDetail(atrBussBecfViewVo);
				} else {
					maxRow = atrBussFOLrcUDao.countLrcUDetail(atrBussBecfViewVo);
				}
				break;
			case "TI":
				if (isIcg) {
					maxRow = atrBussTILrcGDao.countLrcGDetail(atrBussBecfViewVo);
				} else {
					maxRow = atrBussTILrcUDao.countLrcUDetail(atrBussBecfViewVo);
				}
				break;
			case "TO":
				if (isIcg) {
					maxRow = atrBussTOLrcGDao.countLrcGDetail(atrBussBecfViewVo);
				} else {
					maxRow = atrBussTOLrcUDao.countLrcUDetail(atrBussBecfViewVo);
				}
				break;
			default:
				break;
		}
		resultHandler =
				new CommonResultHandler<Map<String, Object>>(fileSavePath
						, fileName + "_"   + atrConfBecfOutPutVo.getOutCName(), tempFilePath, "df", false, maxRow) {
					@Override
					public Object processing(Map<String, Object> map) {
						return map;
					}
				};

		switch (atrBussBecfViewVo.getBusinessSourceCode()) {
			case "DD":
				if (isIcg) {
					atrBussDDLrcGDao.findLrcHandelIcgDetail(resultHandler, atrBussBecfViewVo);
				} else {
					atrBussDDLrcUDao.findLrcHandelDetailPage(resultHandler, atrBussBecfViewVo);
				}
				break;
			case "FO":
				if (isIcg) {
					atrBussFOLrcGDao.findLrcHandelIcgDetail(resultHandler, atrBussBecfViewVo);
				} else {
					atrBussFOLrcUDao.findLrcHandelDetailPage(resultHandler, atrBussBecfViewVo);
				}
				break;
			case "TI":
				if (isIcg) {
					atrBussTILrcGDao.findLrcHandelIcgDetail(resultHandler, atrBussBecfViewVo);
				} else {
					atrBussTILrcUDao.findLrcHandelDetailPage(resultHandler, atrBussBecfViewVo);
				}
				break;
			case "TO":
				if (isIcg) {
					atrBussTOLrcGDao.findLrcHandelIcgDetail(resultHandler, atrBussBecfViewVo);
				} else {
					atrBussTOLrcUDao.findLrcHandelDetailPage(resultHandler, atrBussBecfViewVo);
				}
				break;
			default:
				break;
		}
		if (ObjectUtils.isNotEmpty(maxRow) && maxRow == 0){
			resultHandler.finish();
		}
		resultHandler.getWriter().finish();
		mapList.put(fileName + "_" + atrConfBecfOutPutVo.getOutCName() + XLSX,
				atrExportService.getOutPutPath() + fileName  + "_"  + atrConfBecfOutPutVo.getOutCName() + XLSX) ;
		return mapList;
	}


}
