package com.ss.ifrs.actuarial.dao;

import com.ss.ifrs.actuarial.pojo.ecf.vo.AtrSD7;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.AtrBussLrcAction;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.AtrLogLrc;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface AtrBussLrcDao {

    void insertLog(AtrLogLrc vo);

    Integer getConfParts(@Param("yearMonth") String yearMonth, @Param("businessSourceCode") String businessSourceCode);

    void createAction(AtrBussLrcAction vo);

    void updateActionStatus(AtrBussLrcAction vo);

    String getActionNo(@Param("yearMonth") String yearMonth, @Param("businessSourceCode") String businessSourceCode);

    List<AtrSD7> findTreatyNetFeeT(Map<String, Object> commonParamMap);

    long getMaxLogId();
}
