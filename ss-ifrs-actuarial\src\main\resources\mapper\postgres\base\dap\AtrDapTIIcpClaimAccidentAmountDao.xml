<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-02-02 18:31:30 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.dap.AtrDapTIIcpClaimAccidentAmountDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrdap.po.AtrDapTIIcpClaimAccidentAmount">
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="entity_id" property="entityId" jdbcType="DECIMAL" />
    <result column="CURRENCY_CODE" property="currencyCode" jdbcType="VARCHAR" />
    <result column="YEAR_MONTH" property="yearMonth" jdbcType="VARCHAR" />
    <result column="DAMAGE_YEAR_MONTH" property="damageYearMonth" jdbcType="VARCHAR" />
    <result column="PORTFOLIO_NO" property="portfolioNo" jdbcType="VARCHAR" />
    <result column="EVALUATE_APPROACH" property="evaluateApproach" jdbcType="VARCHAR" />
    <result column="LOA_CODE" property="loaCode" jdbcType="VARCHAR" />
    <result column="IBNR" property="ibnr" jdbcType="DECIMAL" />
    <result column="OS" property="os" jdbcType="DECIMAL" />
    <result column="DRAW_TIME" property="drawTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    ID, entity_id, CURRENCY_CODE, YEAR_MONTH, DAMAGE_YEAR_MONTH, PORTFOLIO_NO, EVALUATE_APPROACH,
    LOA_CODE, IBNR, OS, DRAW_TIME
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="id != null ">
          and ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="currencyCode != null and currencyCode != ''">
          and CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
          and YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="damageYearMonth != null and damageYearMonth != ''">
          and DAMAGE_YEAR_MONTH = #{damageYearMonth,jdbcType=VARCHAR}
      </if>
      <if test="portfolioNo != null and portfolioNo != ''">
          and PORTFOLIO_NO = #{portfolioNo,jdbcType=VARCHAR}
      </if>
      <if test="evaluateApproach != null and evaluateApproach != ''">
          and EVALUATE_APPROACH = #{evaluateApproach,jdbcType=VARCHAR}
      </if>
      <if test="loaCode != null and loaCode != ''">
          and LOA_CODE = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="ibnr != null ">
          and IBNR = #{ibnr,jdbcType=DECIMAL}
      </if>
      <if test="os != null ">
          and OS = #{os,jdbcType=DECIMAL}
      </if>
      <if test="drawTime != null ">
          and DRAW_TIME = #{drawTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.id != null ">
          and ID = #{condition.id,jdbcType=DECIMAL}
      </if>
      <if test="condition.entityId != null ">
          and entity_id = #{condition.entityId,jdbcType=DECIMAL}
      </if>
      <if test="condition.currencyCode != null and condition.currencyCode != ''">
          and CURRENCY_CODE = #{condition.currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.yearMonth != null and condition.yearMonth != ''">
          and YEAR_MONTH = #{condition.yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="condition.damageYearMonth != null and condition.damageYearMonth != ''">
          and DAMAGE_YEAR_MONTH = #{condition.damageYearMonth,jdbcType=VARCHAR}
      </if>
      <if test="condition.portfolioNo != null and condition.portfolioNo != ''">
          and PORTFOLIO_NO = #{condition.portfolioNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.evaluateApproach != null and condition.evaluateApproach != ''">
          and EVALUATE_APPROACH = #{condition.evaluateApproach,jdbcType=VARCHAR}
      </if>
      <if test="condition.loaCode != null and condition.loaCode != ''">
          and LOA_CODE = #{condition.loaCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.ibnr != null ">
          and IBNR = #{condition.ibnr,jdbcType=DECIMAL}
      </if>
      <if test="condition.os != null ">
          and OS = #{condition.os,jdbcType=DECIMAL}
      </if>
      <if test="condition.drawTime != null ">
          and DRAW_TIME = #{condition.drawTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="id != null ">
          and ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="currencyCode != null and currencyCode != ''">
          and CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="yearMonth != null and yearMonth != ''">
          and YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
      </if>
      <if test="damageYearMonth != null and damageYearMonth != ''">
          and DAMAGE_YEAR_MONTH = #{damageYearMonth,jdbcType=VARCHAR}
      </if>
      <if test="portfolioNo != null and portfolioNo != ''">
          and PORTFOLIO_NO = #{portfolioNo,jdbcType=VARCHAR}
      </if>
      <if test="evaluateApproach != null and evaluateApproach != ''">
          and EVALUATE_APPROACH = #{evaluateApproach,jdbcType=VARCHAR}
      </if>
      <if test="loaCode != null and loaCode != ''">
          and LOA_CODE = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="ibnr != null ">
          and IBNR = #{ibnr,jdbcType=DECIMAL}
      </if>
      <if test="os != null ">
          and OS = #{os,jdbcType=DECIMAL}
      </if>
      <if test="drawTime != null ">
          and DRAW_TIME = #{drawTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from ATR_DAP_TI_ICP_CLAIM_ACCIDENT_AMOUNT
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from ATR_DAP_TI_ICP_CLAIM_ACCIDENT_AMOUNT
    where ID in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ATR_DAP_TI_ICP_CLAIM_ACCIDENT_AMOUNT
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrdap.po.AtrDapTIIcpClaimAccidentAmount">
    select 
    <include refid="Base_Column_List" />
    from ATR_DAP_TI_ICP_CLAIM_ACCIDENT_AMOUNT
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from ATR_DAP_TI_ICP_CLAIM_ACCIDENT_AMOUNT
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="ID" keyProperty="id" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrdap.po.AtrDapTIIcpClaimAccidentAmount">
    insert into ATR_DAP_TI_ICP_CLAIM_ACCIDENT_AMOUNT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="entityId != null">
        entity_id,
      </if>
      <if test="currencyCode != null">
        CURRENCY_CODE,
      </if>
      <if test="yearMonth != null">
        YEAR_MONTH,
      </if>
      <if test="damageYearMonth != null">
        DAMAGE_YEAR_MONTH,
      </if>
      <if test="portfolioNo != null">
        PORTFOLIO_NO,
      </if>
      <if test="evaluateApproach != null">
        EVALUATE_APPROACH,
      </if>
      <if test="loaCode != null">
        LOA_CODE,
      </if>
      <if test="ibnr != null">
        IBNR,
      </if>
      <if test="os != null">
        OS,
      </if>
      <if test="drawTime != null">
        DRAW_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=DECIMAL},
      </if>
      <if test="currencyCode != null">
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="yearMonth != null">
        #{yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="damageYearMonth != null">
        #{damageYearMonth,jdbcType=VARCHAR},
      </if>
      <if test="portfolioNo != null">
        #{portfolioNo,jdbcType=VARCHAR},
      </if>
      <if test="evaluateApproach != null">
        #{evaluateApproach,jdbcType=VARCHAR},
      </if>
      <if test="loaCode != null">
        #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="ibnr != null">
        #{ibnr,jdbcType=DECIMAL},
      </if>
      <if test="os != null">
        #{os,jdbcType=DECIMAL},
      </if>
      <if test="drawTime != null">
        #{drawTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all 
    <foreach collection="list" item="item" index="index">
      into ATR_DAP_TI_ICP_CLAIM_ACCIDENT_AMOUNT values
       (#{item.id,jdbcType=DECIMAL}, 
        #{item.entityId,jdbcType=DECIMAL}, #{item.currencyCode,jdbcType=VARCHAR}, #{item.yearMonth,jdbcType=VARCHAR},
        #{item.damageYearMonth,jdbcType=VARCHAR}, #{item.portfolioNo,jdbcType=VARCHAR}, 
        #{item.evaluateApproach,jdbcType=VARCHAR}, #{item.loaCode,jdbcType=VARCHAR}, #{item.ibnr,jdbcType=DECIMAL}, 
        #{item.os,jdbcType=DECIMAL}, #{item.drawTime,jdbcType=TIMESTAMP})
    </foreach>
    select 1 
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrdap.po.AtrDapTIIcpClaimAccidentAmount">
    update ATR_DAP_TI_ICP_CLAIM_ACCIDENT_AMOUNT
    <set>
      <if test="entityId != null">
        entity_id = #{entityId,jdbcType=DECIMAL},
      </if>
      <if test="currencyCode != null">
        CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="yearMonth != null">
        YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="damageYearMonth != null">
        DAMAGE_YEAR_MONTH = #{damageYearMonth,jdbcType=VARCHAR},
      </if>
      <if test="portfolioNo != null">
        PORTFOLIO_NO = #{portfolioNo,jdbcType=VARCHAR},
      </if>
      <if test="evaluateApproach != null">
        EVALUATE_APPROACH = #{evaluateApproach,jdbcType=VARCHAR},
      </if>
      <if test="loaCode != null">
        LOA_CODE = #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="ibnr != null">
        IBNR = #{ibnr,jdbcType=DECIMAL},
      </if>
      <if test="os != null">
        OS = #{os,jdbcType=DECIMAL},
      </if>
      <if test="drawTime != null">
        DRAW_TIME = #{drawTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrdap.po.AtrDapTIIcpClaimAccidentAmount">
    update ATR_DAP_TI_ICP_CLAIM_ACCIDENT_AMOUNT
    <set>
      <if test="record.entityId != null">
        entity_id = #{record.entityId,jdbcType=DECIMAL},
      </if>
      <if test="record.currencyCode != null">
        CURRENCY_CODE = #{record.currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.yearMonth != null">
        YEAR_MONTH = #{record.yearMonth,jdbcType=VARCHAR},
      </if>
      <if test="record.damageYearMonth != null">
        DAMAGE_YEAR_MONTH = #{record.damageYearMonth,jdbcType=VARCHAR},
      </if>
      <if test="record.portfolioNo != null">
        PORTFOLIO_NO = #{record.portfolioNo,jdbcType=VARCHAR},
      </if>
      <if test="record.evaluateApproach != null">
        EVALUATE_APPROACH = #{record.evaluateApproach,jdbcType=VARCHAR},
      </if>
      <if test="record.loaCode != null">
        LOA_CODE = #{record.loaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.ibnr != null">
        IBNR = #{record.ibnr,jdbcType=DECIMAL},
      </if>
      <if test="record.os != null">
        OS = #{record.os,jdbcType=DECIMAL},
      </if>
      <if test="record.drawTime != null">
        DRAW_TIME = #{record.drawTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from ATR_DAP_TI_ICP_CLAIM_ACCIDENT_AMOUNT
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from ATR_DAP_TI_ICP_CLAIM_ACCIDENT_AMOUNT
    where ID in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from ATR_DAP_TI_ICP_CLAIM_ACCIDENT_AMOUNT
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrdap.po.AtrDapTIIcpClaimAccidentAmount">
    select count(1) from ATR_DAP_TI_ICP_CLAIM_ACCIDENT_AMOUNT
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>