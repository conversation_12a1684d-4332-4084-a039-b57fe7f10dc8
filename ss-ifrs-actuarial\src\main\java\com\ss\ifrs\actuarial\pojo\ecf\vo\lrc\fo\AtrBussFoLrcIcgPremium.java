package com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.fo; // 注意：根据您的要求放在 dd 包下

import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 合同组保费 (临分分出) - 对应表 atr_buss_fo_lrc_icg_premium
 */
@Tab("atr_buss_fo_lrc_icg_premium")
@Data
public class AtrBussFoLrcIcgPremium { // 类名根据表名调整

    /**
     * 执行编号 (action_no)
     */
    private String actionNo;

    /**
     * 评估期年月 (year_month)
     */
    private String yearMonth;

    /**
     * 险类代码 (risk_class_code)
     */
    private String riskClassCode;

    /**
     * 合同组合编号 (portfolio_no)
     */
    private String portfolioNo;

    /**
     * 合同组编号 (icg_no)
     */
    private String icgNo;

    /**
     * 新保费 (new_premium)
     */
    private BigDecimal newPremium;

    /**
     * 旧保费 (old_premium)
     */
    private BigDecimal oldPremium;

}