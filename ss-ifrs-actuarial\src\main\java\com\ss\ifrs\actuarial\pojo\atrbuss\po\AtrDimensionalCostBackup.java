/**
 * 
 * This file was generated by Taiping MyBatis Generator(v1.2.12).
 * Date: 2024-08-15 09:41:23
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by Taiping MyBatis Generator(v1.2.12).
 * Create Date: 2024-08-15 09:41:23<br/>
 * Description: IBNR-二次分摊维度费用备份表<br/>
 * Table Name: ATR_DIMENSIONAL_COST_BACKUP<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "IBNR-二次分摊维度费用备份表")
public class AtrDimensionalCostBackup implements Serializable {
    /**
     * Database column: ATR_DIMENSIONAL_COST_BACKUP.ID
     * Database remarks: ID|主键
     */
    @ApiModelProperty(value = "ID|主键", required = true)
    private Long id;

    /**
     * Database column: ATR_DIMENSIONAL_COST_BACKUP.ENTITY_ID
     * Database remarks: 业务单位
     */
    @ApiModelProperty(value = "业务单位", required = false)
    private Long entityId;

    /**
     * Database column: ATR_DIMENSIONAL_COST_BACKUP.YEAR_MONTH
     * Database remarks: 业务年月
     */
    @ApiModelProperty(value = "业务年月", required = false)
    private String yearMonth;

    /**
     * Database column: ATR_DIMENSIONAL_COST_BACKUP.ACTION_NO
     * Database remarks: null
     */
    private String actionNo;

    /**
     * Database column: ATR_DIMENSIONAL_COST_BACKUP.POLICY_NO
     * Database remarks: 保单号
     */
    @ApiModelProperty(value = "保单号", required = false)
    private String policyNo;

    /**
     * Database column: ATR_DIMENSIONAL_COST_BACKUP.TREATY_NO
     * Database remarks: 合约号
     */
    @ApiModelProperty(value = "合约号", required = false)
    private String treatyNo;

    /**
     * Database column: ATR_DIMENSIONAL_COST_BACKUP.RISK_CATEGORY_CODE
     * Database remarks: 监管大类代码
     */
    @ApiModelProperty(value = "监管大类代码", required = false)
    private String riskCategoryCode;

    /**
     * Database column: ATR_DIMENSIONAL_COST_BACKUP.COM_CODE
     * Database remarks: 机构代码
     */
    @ApiModelProperty(value = "机构代码", required = false)
    private String comCode;

    /**
     * Database column: ATR_DIMENSIONAL_COST_BACKUP.CLASS_CODE
     * Database remarks: 险类代码
     */
    @ApiModelProperty(value = "险类代码", required = false)
    private String classCode;

    /**
     * Database column: ATR_DIMENSIONAL_COST_BACKUP.RISK_CODE
     * Database remarks: 险种代码
     */
    @ApiModelProperty(value = "险种代码", required = false)
    private String riskCode;

    /**
     * Database column: ATR_DIMENSIONAL_COST_BACKUP.BUSINESS_TYPE
     * Database remarks: 业务类型1直接2分入
     */
    @ApiModelProperty(value = "业务类型1直接2分入", required = false)
    private String businessType;

    /**
     * Database column: ATR_DIMENSIONAL_COST_BACKUP.EP_PREMIUM
     * Database remarks: 已赚保费
     */
    @ApiModelProperty(value = "已赚保费", required = false)
    private BigDecimal epPremium;

    /**
     * Database column: ATR_DIMENSIONAL_COST_BACKUP.PAID_PREMIUM
     * Database remarks: 已决保费
     */
    @ApiModelProperty(value = "已决保费", required = false)
    private BigDecimal paidPremium;

    /**
     * Database column: ATR_DIMENSIONAL_COST_BACKUP.CASE_PREMIUM
     * Database remarks: 未决保费
     */
    @ApiModelProperty(value = "未决保费", required = false)
    private BigDecimal casePremium;

    /**
     * Database column: ATR_DIMENSIONAL_COST_BACKUP.CREATE_TIME
     * Database remarks: 创建时间
     */
    @ApiModelProperty(value = "创建时间", required = false)
    private Date createTime;

    private String payFlag;

    private String businessSourceCode;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getTreatyNo() {
        return treatyNo;
    }

    public void setTreatyNo(String treatyNo) {
        this.treatyNo = treatyNo;
    }

    public String getRiskCategoryCode() {
        return riskCategoryCode;
    }

    public void setRiskCategoryCode(String riskCategoryCode) {
        this.riskCategoryCode = riskCategoryCode;
    }

    public String getComCode() {
        return comCode;
    }

    public void setComCode(String comCode) {
        this.comCode = comCode;
    }

    public String getClassCode() {
        return classCode;
    }

    public void setClassCode(String classCode) {
        this.classCode = classCode;
    }

    public String getRiskCode() {
        return riskCode;
    }

    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public BigDecimal getEpPremium() {
        return epPremium;
    }

    public void setEpPremium(BigDecimal epPremium) {
        this.epPremium = epPremium;
    }

    public BigDecimal getPaidPremium() {
        return paidPremium;
    }

    public void setPaidPremium(BigDecimal paidPremium) {
        this.paidPremium = paidPremium;
    }

    public BigDecimal getCasePremium() {
        return casePremium;
    }

    public void setCasePremium(BigDecimal casePremium) {
        this.casePremium = casePremium;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getPayFlag() {
        return payFlag;
    }

    public void setPayFlag(String payFlag) {
        this.payFlag = payFlag;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }
}