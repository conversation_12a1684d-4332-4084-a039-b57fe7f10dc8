CREATE OR REPLACE PACKAGE qtc_pack_data_sync IS
  
    
  PROCEDURE proc_update_bussperiod_detail(p_buss_period_id number,
                                          p_biz_code       varchar2,
                                          p_ready_state    varchar2,
                                          p_error_msg      varchar2);


 

  PROCEDURE proc_fetch_all(p_entityid  NUMBER,
                           p_yearmonth VARCHAR2,
                           p_userid    NUMBER);


 
END qtc_pack_data_sync;
/
CREATE OR REPLACE PACKAGE BODY qtc_pack_data_sync IS

  type record_buss is record(
    entity_id             number(11),
    year_month            varchar2(6),
    buss_period_id        number(11),
    user_id        number(11));
    
    
  procedure proc_update_bussperiod_detail(p_buss_period_id number,
                                          p_biz_code       varchar2,
                                          p_ready_state    varchar2,
                                          p_error_msg      varchar2) is
  
  BEGIN
    update qtc_conf_bussperiod_detail t
       set t.exec_result = decode(p_ready_state,
                                  '1',
                                  'success',
                                  substrb(p_error_msg, 1, 500)),
           t.ready_state = p_ready_state,
           t.update_time = localtimestamp
     where t.buss_period_id = p_buss_period_id
       and exists (select *
              from qtc_conf_table c
             where c.biz_code = p_biz_code
               and c.biz_type_id = t.biz_type_id);
    commit;
  end proc_update_bussperiod_detail;
  
  
  function func_check_if_biz_continue(p_buss  record_buss,
                                      p_biz_code varchar2) return number is
    v_count number(5);
  begin
    select count(*)
      into v_count
      from qtc_conf_bussperiod_detail t
     where t.buss_period_id = p_buss.buss_period_id
       and t.ready_state = '0'
       and exists (select *
              from qtc_conf_table c
             where c.biz_code = p_biz_code
               and c.biz_type_id = t.biz_type_id);
    if v_count = 1 then
      return 1;
    end if;
  
    if v_count > 1 then
      RAISE_APPLICATION_ERROR(-20081,
                              '模型' || p_biz_code || '业务期间' ||
                              p_buss.year_month ||
                              '有多条详情，无法处理，请核实业务期间控制详情表');
    end if;
  
    return 0;
  end func_check_if_biz_continue;
  
  
  PROCEDURE PROC_FETCH_ECF_DD_ICU(p_buss record_buss) IS
    ------------------------------------------
    -- 保费 （直保&临分分入）
    ------------------------------------------
    v_biz_code varchar2(30) := 'DAP_ECF_DD_ICU';
  BEGIN
  
    if func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
      return;
    end if;
    
    DELETE FROM QTC_DAP_ECF_DD_ICU A
     WHERE 1 = 1
       AND A.entity_id = p_buss.entity_id
       AND YEAR_MONTH = p_buss.year_month;
    COMMIT;
    
    INSERT INTO QTC_DAP_ECF_DD_ICU
      (ICU_ID,
       ENTITY_ID,
       DEPT_ID,
       EVALUATE_APPROACH,
       LOA_CODE,
       PORTFOLIO_NO,
       ICG_NO,
       POLICY_NO,
       ENDORSE_SEQ_NO,
       YEAR_MONTH,
       POLICY_TYPE,
       ENDORSE_TYPE,
       UNIT_NO,
       CONTRACT_DATE,
       EFFECTIVE_DATE,
       EXPIRY_DATE,
       VALID_DATE,
       ENDORSE_END_DATE,
       CURRENCY_CODE,
       PREMIUM,
       IACF_FEE,
       INVEST_AMOUNT,
       INVEST_RATE,
       CUR_END_REMAIN_UN_RATE,
       LP_CUR_END_REMAIN_UN_RATE,
       ACCUMULATED_EARNED_RATE,
       RPT_PER_REMAIN_UN_RATE,
       TASK_CODE,
       DRAW_TIME)
      SELECT QTC_SEQ_DAP_ECF_DD_ICU.NEXTVAL,
             T1.ENTITY_ID,
             NULL                            AS DEPT_ID,
             T2.EVALUATE_APPROACH,
             T2.LOA_CODE,
             T2.PORTFOLIO_NO,
             T2.ICG_NO,
             T2.POLICY_NO,
             T2.ENDORSE_SEQ_NO,
             T2.YEAR_MONTH,
             NULL                           AS POLICY_TYPE,
             NULL                 AS ENDORSE_TYPE,
             --T3.Policy_Type_Code                           AS POLICY_TYPE,
             --T3.Endorse_Type_Code                 AS ENDORSE_TYPE,
             T2.CMUNIT_NO                    AS UNIT_NO,
             T2.CONTRACT_DATE,
             T2.EFFECTIVE_DATE_IN_DATE,
             T2.EXPIRY_DATE_IN_DATE,
             ''                            AS VALID_DATE,
             ''                            AS ENDORSE_END_DATE,
             T2.CURRENCY_CODE,
             T2.GROSS_PREMIUM,
             NULL                            AS IACF_FEE,
             NULL                            AS INVEST_AMOUNT,
             NULL                            AS INVEST_RATE,
             T2.CUR_END_REMAIN_CSM_RATE      AS CUR_END_REMAIN_UN_RATE, --当期期末未摊销比例
             T2.PRI_CUR_END_REMAIN_CSM_RATE  AS LP_CUR_END_REMAIN_UN_RATE, -- 上期期末未摊销比例
             T2.CUMULATIVE_ED_RATE           AS ACCUMULATED_EARNED_RATE, --累计已赚比例
             T2.UNTIL_REPORT_REMAIN_CSM_RATE AS RPT_PER_REMAIN_UN_RATE, --报告期初未摊销比例
             T2.TASK_CODE                    AS TASK_CODE,
             SYSDATE                         DRAW_TIME
        FROM ATRUSER.ATR_BUSS_LRC_ACTION      T1,
             ATRUSER.ATR_BUSS_DD_LRC_ICU_CALC T2 
             --,dmuser.dm_policy_main T3
       WHERE T1.ACTION_NO = T2.ACTION_NO
         AND T1.CONFIRM_IS = '1'
         --AND t1.ENTITY_ID = t3.ENTITY_ID
         --AND t2.policy_no = t3.policy_no
         --AND t2.endorse_seq_no = t3.endorse_seq_no
         AND T1.ENTITY_ID = p_buss.entity_id
         AND T1.YEAR_MONTH = p_buss.year_month;
    COMMIT;
    
    --INSERT INTO QTC_DAP_ECF_DD_ICU
    --SELECT * FROM atruser.atr_buss_lic_action;
    COMMIT;
    proc_update_bussperiod_detail(p_buss.buss_period_id,
                                  v_biz_code,
                                  '1',
                                  null);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      proc_update_bussperiod_detail(p_buss.buss_period_id,
                                    v_biz_code,
                                    '0',
                                    substr( SQLERRM, 1, 400));
  END PROC_FETCH_ECF_DD_ICU;
  
  
  PROCEDURE PROC_FETCH_ECF_FO_ICU(p_buss record_buss) IS
    ------------------------------------------
    -- 保费 （临分分出）
    ------------------------------------------
    v_biz_code varchar2(30) := 'DAP_ECF_FO_ICU';
  BEGIN
  
    if func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
      return;
    end if;
    DELETE FROM QTC_DAP_ECF_FO_ICU A
     WHERE 1 = 1
       AND A.entity_id = p_buss.entity_id
       AND YEAR_MONTH = p_buss.year_month;
    COMMIT;
    
    INSERT INTO QTC_DAP_ECF_FO_ICU
      (ICU_ID,
       ENTITY_ID,
       DEPT_ID,
       EVALUATE_APPROACH,
       LOA_CODE,
       PORTFOLIO_NO,
       ICG_NO,
       RI_POLICY_NO,
       RI_ENDORSE_SEQ_NO,
       RI_STATEMENT_NO,
       YEAR_MONTH,
       POLICY_NO,
       ENDORSE_SEQ_NO,
       UNIT_NO,
       CONTRACT_DATE,
       EFFECTIVE_DATE,
       EXPIRY_DATE,
       VALID_DATE,
       ENDORSE_END_DATE,
       CURRENCY_CODE,
       PREMIUM,
       IACF_FEE,
       INVEST_AMOUNT,
       INVEST_RATE,
       CUR_END_REMAIN_UN_RATE,
       LP_CUR_END_REMAIN_UN_RATE,
       ACCUMULATED_EARNED_RATE,
       RPT_PER_REMAIN_UN_RATE,
       TASK_CODE,
       DRAW_TIME)
      SELECT QTC_SEQ_DAP_ECF_FO_ICU.NEXTVAL,
             T1.ENTITY_ID,
             NULL                            AS DEPT_ID,
             T2.EVALUATE_APPROACH,
             T2.LOA_CODE,
             T2.PORTFOLIO_NO,
             T2.ICG_NO,
             T2.RI_POLICY_NO,
             T2.RI_ENDORSE_SEQ_NO,
             T2.RI_STATEMENT_NO,
             T2.YEAR_MONTH,
             NULL                            AS POLICY_NO,
             NULL                            AS ENDORSE_SEQ_NO,
             T2.CMUNIT_NO                    AS UNIT_NO,
             T2.CONTRACT_DATE,
             T2.EFFECTIVE_DATE_IN_DATE,
             T2.EXPIRY_DATE_IN_DATE,
             NULL                            AS VALID_DATE,
             NULL                            AS ENDORSE_END_DATE,
             T2.CURRENCY_CODE,
             T2.GROSS_PREMIUM,
             NULL                            AS IACF_FEE,
             NULL                            AS INVEST_AMOUNT,
             NULL                            AS INVEST_RATE,
             T2.CUR_END_REMAIN_CSM_RATE      AS CUR_END_REMAIN_UN_RATE, --当期期末未摊销比例
             T2.PRI_CUR_END_REMAIN_CSM_RATE  AS LP_CUR_END_REMAIN_UN_RATE, -- 上期期末未摊销比例
             T2.CUMULATIVE_ED_RATE           AS ACCUMULATED_EARNED_RATE, --累计已赚比例
             T2.UNTIL_REPORT_REMAIN_CSM_RATE AS RPT_PER_REMAIN_UN_RATE, --报告期初未摊销比例
             T2.TASK_CODE                    AS TASK_CODE,
             SYSDATE                         DRAW_TIME
        FROM ATRUSER.ATR_BUSS_LRC_ACTION      T1,
             ATRUSER.ATR_BUSS_FO_LRC_ICU_CALC T2
       WHERE T1.ACTION_NO = T2.ACTION_NO
         AND T1.CONFIRM_IS = '1'
         AND T1.ENTITY_ID = p_buss.entity_id
         AND T1.YEAR_MONTH = p_buss.year_month;
     
    COMMIT;
    proc_update_bussperiod_detail(p_buss.buss_period_id,
                                  v_biz_code,
                                  '1',
                                  null);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      proc_update_bussperiod_detail(p_buss.buss_period_id,
                                    v_biz_code,
                                    '0',
                                    substr( SQLERRM, 1, 400));
  END PROC_FETCH_ECF_FO_ICU;
  
 
  PROCEDURE PROC_FETCH_ECF_TI_ICU(p_buss record_buss) IS
    ------------------------------------------
    -- 保费 （直保&临分分入）
    ------------------------------------------
    v_biz_code varchar2(30) := 'DAP_ECF_TI_ICU';
  BEGIN
  
    if func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
      return;
    end if;
    DELETE FROM QTC_DAP_ECF_TI_ICU A
     WHERE 1 = 1
       AND A.entity_id = p_buss.entity_id
       AND YEAR_MONTH = p_buss.year_month;
    COMMIT;
    
    INSERT INTO QTC_DAP_ECF_TI_ICU
      (ICU_ID,
       ENTITY_ID,
       DEPT_ID,
       EVALUATE_APPROACH,
       LOA_CODE,
       PORTFOLIO_NO,
       ICG_NO,
       TREATY_NO,
       YEAR_MONTH,
       UNIT_NO,
       CONTRACT_DATE,
       EFFECTIVE_DATE,
       EXPIRY_DATE,
       CURRENCY_CODE,
       PREMIUM,
       IACF_FEE,
       INVEST_AMOUNT,
       INVEST_RATE,
       CUR_END_REMAIN_UN_RATE,
       LP_CUR_END_REMAIN_UN_RATE,
       ACCUMULATED_EARNED_RATE,
       RPT_PER_REMAIN_UN_RATE,
       TASK_CODE,
       DRAW_TIME)
      SELECT QTC_SEQ_DAP_ECF_TI_ICU.NEXTVAL,
             T1.ENTITY_ID,
             NULL                            AS DEPT_ID,
             T2.EVALUATE_APPROACH,
             T2.LOA_CODE,
             T2.PORTFOLIO_NO,
             T2.ICG_NO,
             T2.TREATY_NO,
             T2.YEAR_MONTH,
             T2.CMUNIT_NO                    AS UNIT_NO,
             T2.CONTRACT_DATE,
             T2.EFFECTIVE_DATE_IN_DATE,
             T2.EXPIRY_DATE_IN_DATE,
             T2.CURRENCY_CODE,
             T2.GROSS_PREMIUM,
             NULL                            AS IACF_FEE,
             NULL                            AS INVEST_AMOUNT,
             NULL                            AS INVEST_RATE,
             T2.CUR_END_REMAIN_CSM_RATE      AS CUR_END_REMAIN_UN_RATE, --当期期末未摊销比例
             T2.PRI_CUR_END_REMAIN_CSM_RATE  AS LP_CUR_END_REMAIN_UN_RATE, -- 上期期末未摊销比例
             T2.CUMULATIVE_ED_RATE           AS ACCUMULATED_EARNED_RATE, --累计已赚比例
             T2.UNTIL_REPORT_REMAIN_CSM_RATE AS RPT_PER_REMAIN_UN_RATE, --报告期初未摊销比例
             T2.TASK_CODE                    AS TASK_CODE,
             SYSDATE                         DRAW_TIME
        FROM ATRUSER.ATR_BUSS_LRC_ACTION      T1,
             ATRUSER.ATR_BUSS_TI_LRC_ICU_CALC T2
       WHERE T1.ACTION_NO = T2.ACTION_NO
         AND T1.CONFIRM_IS = '1'
         AND T1.ENTITY_ID = p_buss.entity_id
         AND T1.YEAR_MONTH = p_buss.year_month;
    COMMIT;
    proc_update_bussperiod_detail(p_buss.buss_period_id,
                                  v_biz_code,
                                  '1',
                                  null);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      proc_update_bussperiod_detail(p_buss.buss_period_id,
                                    v_biz_code,
                                    '0',
                                    substr( SQLERRM, 1, 400));
  END PROC_FETCH_ECF_TI_ICU;
  
  
  PROCEDURE PROC_FETCH_ECF_TO_ICU(p_buss record_buss) IS
  ------------------------------------------
  -- 保费 （直保&临分分入）
  ------------------------------------------
    v_biz_code varchar2(30) := 'DAP_ECF_TO_ICU';
  BEGIN
  
    if func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
      return;
    end if;
    
    DELETE FROM QTC_DAP_ECF_TO_ICU A
     WHERE 1 = 1
       AND A.entity_id = p_buss.entity_id
       AND YEAR_MONTH = p_buss.year_month;
    COMMIT;
    
    INSERT INTO QTC_DAP_ECF_TO_ICU
      (ICU_ID,
       ENTITY_ID,
       DEPT_ID,
       EVALUATE_APPROACH,
       LOA_CODE,
       PORTFOLIO_NO,
       ICG_NO,
       TREATY_NO,
       YEAR_MONTH,
       UNIT_NO,
       CONTRACT_DATE,
       EFFECTIVE_DATE,
       EXPIRY_DATE,
       CURRENCY_CODE,
       PREMIUM,
       IACF_FEE,
       INVEST_AMOUNT,
       INVEST_RATE,
       CUR_END_REMAIN_UN_RATE,
       LP_CUR_END_REMAIN_UN_RATE,
       ACCUMULATED_EARNED_RATE,
       RPT_PER_REMAIN_UN_RATE,
       TASK_CODE,
       DRAW_TIME)
      SELECT QTC_SEQ_DAP_ECF_TO_ICU.NEXTVAL,
             T1.ENTITY_ID,
             NULL                            AS DEPT_ID,
             T2.EVALUATE_APPROACH,
             T2.LOA_CODE,
             T2.PORTFOLIO_NO,
             T2.ICG_NO,
             T2.TREATY_NO,
             T2.YEAR_MONTH,
             T2.CMUNIT_NO                    AS UNIT_NO,
             T2.CONTRACT_DATE,
             T2.EFFECTIVE_DATE_IN_DATE,
             T2.EXPIRY_DATE_IN_DATE,
             T2.CURRENCY_CODE,
             T2.GROSS_PREMIUM,
             NULL                            AS IACF_FEE,
             NULL                            AS INVEST_AMOUNT,
             NULL                            AS INVEST_RATE,
             T2.CUR_END_REMAIN_CSM_RATE      AS CUR_END_REMAIN_UN_RATE, --当期期末未摊销比例
             T2.PRI_CUR_END_REMAIN_CSM_RATE  AS LP_CUR_END_REMAIN_UN_RATE, -- 上期期末未摊销比例
             T2.CUMULATIVE_ED_RATE           AS ACCUMULATED_EARNED_RATE, --累计已赚比例
             T2.UNTIL_REPORT_REMAIN_CSM_RATE AS RPT_PER_REMAIN_UN_RATE, --报告期初未摊销比例
             T2.TASK_CODE                    AS TASK_CODE,
             SYSDATE                         DRAW_TIME
        FROM ATRUSER.ATR_BUSS_LRC_ACTION      T1,
             ATRUSER.ATR_BUSS_TO_LRC_ICU_CALC T2
       WHERE T1.ACTION_NO = T2.ACTION_NO
         AND T1.CONFIRM_IS = '1'
         AND T1.ENTITY_ID = p_buss.entity_id
         AND T1.YEAR_MONTH = p_buss.year_month;
    COMMIT;
    proc_update_bussperiod_detail(p_buss.buss_period_id,
                                  v_biz_code,
                                  '1',
                                  null);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      proc_update_bussperiod_detail(p_buss.buss_period_id,
                                    v_biz_code,
                                    '0',
                                    substr( SQLERRM, 1, 400));
  END PROC_FETCH_ECF_TO_ICU;
  
  
  
  PROCEDURE PROC_FETCH_ECF_DD_ICU_CF(p_buss record_buss) IS
  ------------------------------------------
  -- 保费 （直保&临分分入）
  ------------------------------------------
    v_biz_code varchar2(30) := 'DAP_ECF_DD_ICU_CF';
  BEGIN
  
    if func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
      return;
    end if;
    
    DELETE FROM QTC_DAP_ECF_DD_ICU_CF A
     WHERE 1 = 1
       AND A.entity_id = p_buss.entity_id
       AND YEAR_MONTH = p_buss.year_month;
    COMMIT;
    
    INSERT INTO QTC_DAP_ECF_DD_ICU_CF
     (ICU_DETAIL_ID,
      ENTITY_ID,
      DEPT_ID,
      EVALUATE_APPROACH,
      YEAR_MONTH,
      LOA_CODE,
      PORTFOLIO_NO,
      ICG_NO,
      POLICY_NO,
      ENDORSE_SEQ_NO,
      DEV_PERIOD,
      UE_PREMIUM,
      ED_PREMIUM,
      RECV_PREMIUM,
      IACF_FEE,
      IACF_FEE_NON_POLICY,
      MAINTENANCE_FEE,
      ADJ_COMMISSION,
      BROKERAGE_FEE,
      ULTIMATE_LOSS,
      TASK_CODE,
      DRAW_TIME)
     SELECT QTC_SEQ_DAP_ECF_DD_ICU_CF.NEXTVAL AS ICU_DETAIL_ID,
            T1.ENTITY_ID,
            NULL                              AS DEPT_ID,
            T2.EVALUATE_APPROACH,
            T2.YEAR_MONTH,
            T2.LOA_CODE,
            T2.PORTFOLIO_NO,
            T2.ICG_NO,
            T2.POLICY_NO,
            T2.ENDORSE_SEQ_NO,
            T3.DEV_NO                         AS DEV_PERIOD,
            T3.UE_PREMIUM,
            T3.ED_PREMIUM,
            T3.RECV_PREMIUM,
            T3.IACF_FEE,
            T3.IACF_FEE_NON_POLICY,
            T3.MAINTENANCE_FEE,
            T3.ADJ_COMMISSION,
            T3.BROKERAGE_FEE,
            T3.ULTIMATE_LOSS,
            T1.TASK_CODE,
            SYSDATE                           AS DRAW_TIME
       FROM ATRUSER.ATR_BUSS_LRC_ACTION             T1,
            ATRUSER.ATR_BUSS_DD_LRC_ICU_CALC        T2,
            ATRUSER.ATR_BUSS_DD_LRC_ICU_CALC_DETAIL T3
      WHERE T1.ACTION_NO = T2.ACTION_NO
        AND T2.ID = T3.MAIN_ID
        AND T1.CONFIRM_IS = '1'
           AND T1.ENTITY_ID = p_buss.entity_id
           AND T1.YEAR_MONTH = p_buss.year_month;
    COMMIT;
    proc_update_bussperiod_detail(p_buss.buss_period_id,
                                  v_biz_code,
                                  '1',
                                  null);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      proc_update_bussperiod_detail(p_buss.buss_period_id,
                                    v_biz_code,
                                    '0',
                                    substr( SQLERRM, 1, 400));
  END PROC_FETCH_ECF_DD_ICU_CF;  
  
  
  
  PROCEDURE PROC_FETCH_ECF_FO_ICU_CF(p_buss record_buss) IS
  ------------------------------------------
  -- 保费 （直保&临分分入）
  ------------------------------------------
    v_biz_code varchar2(30) := 'DAP_ECF_FO_ICU_CF';
  BEGIN
  
    if func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
      return;
    end if;
    
    DELETE FROM QTC_DAP_ECF_FO_ICU_CF A
     WHERE 1 = 1
       AND A.entity_id = p_buss.entity_id
       AND YEAR_MONTH = p_buss.year_month;
    COMMIT;
    
    INSERT INTO QTC_DAP_ECF_FO_ICU_CF
      (ICU_DETAIL_ID,
       ENTITY_ID,
       DEPT_ID,
       EVALUATE_APPROACH,
       YEAR_MONTH,
       LOA_CODE,
       PORTFOLIO_NO,
       ICG_NO,
       RI_POLICY_NO,
       DEV_PERIOD,
       UE_PREMIUM,
       ED_PREMIUM,
       RECV_PREMIUM,
       IACF_FEE,
       IACF_FEE_NON_POLICY,
       MAINTENANCE_FEE,
       ADJ_COMMISSION,
       BROKERAGE_FEE,
       ULTIMATE_LOSS,
       TASK_CODE,
       DRAW_TIME)
      SELECT QTC_SEQ_DAP_ECF_FO_ICU_CF.NEXTVAL AS ICU_DETAIL_ID,
             T1.ENTITY_ID,
             NULL                              AS DEPT_ID,
             T2.EVALUATE_APPROACH,
             T1.YEAR_MONTH,
             T2.LOA_CODE,
             T2.PORTFOLIO_NO,
             T2.ICG_NO,
             T2.RI_POLICY_NO,
             T3.DEV_NO                         AS DEV_PERIOD,
             T3.UE_PREMIUM,
             T3.ED_PREMIUM,
             T3.RECV_PREMIUM,
             T3.IACF_FEE,
             T3.IACF_FEE_NON_POLICY,
             T3.MAINTENANCE_FEE,
             T3.ADJ_COMMISSION,
             T3.BROKERAGE_FEE,
             T3.ULTIMATE_LOSS,
             T1.TASK_CODE,
             SYSDATE                           AS DRAW_TIME
        FROM ATRUSER.ATR_BUSS_LRC_ACTION             T1,
             ATRUSER.ATR_BUSS_FO_LRC_ICU_CALC        T2,
             ATRUSER.ATR_BUSS_FO_LRC_ICU_CALC_DETAIL T3
       WHERE T1.ACTION_NO = T2.ACTION_NO
         AND T2.ID = T3.MAIN_ID
         AND T1.CONFIRM_IS = '1'
         AND T1.ENTITY_ID = p_buss.entity_id
         AND T1.YEAR_MONTH = p_buss.year_month;
    COMMIT;
    proc_update_bussperiod_detail(p_buss.buss_period_id,
                                  v_biz_code,
                                  '1',
                                  null);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      proc_update_bussperiod_detail(p_buss.buss_period_id,
                                    v_biz_code,
                                    '0',
                                    substr( SQLERRM, 1, 400));
  END PROC_FETCH_ECF_FO_ICU_CF;  
  
  
  PROCEDURE PROC_FETCH_ECF_TI_ICU_CF(p_buss record_buss) IS
  ------------------------------------------
  -- 保费 （直保&临分分入）
  ------------------------------------------
    v_biz_code varchar2(30) := 'DAP_ECF_TI_ICU_CF';
  BEGIN
  
    if func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
      return;
    end if;
    
    DELETE FROM QTC_DAP_ECF_TI_ICU_CF A
     WHERE 1 = 1
       AND A.entity_id = p_buss.entity_id
       AND YEAR_MONTH = p_buss.year_month;
    COMMIT;
    
    INSERT INTO QTC_DAP_ECF_TI_ICU_CF
      (ICU_DETAIL_ID,
       ENTITY_ID,
       DEPT_ID,
       EVALUATE_APPROACH,
       YEAR_MONTH,
       LOA_CODE,
       PORTFOLIO_NO,
       ICG_NO,
       TREATY_NO,
       DEV_PERIOD,
       UE_PREMIUM,
       ED_PREMIUM,
       RECV_PREMIUM,
       IACF_FEE,
       IACF_FEE_NON_POLICY,
       MAINTENANCE_FEE,
       ADJ_COMMISSION,
       BROKERAGE_FEE,
       ULTIMATE_LOSS,
       TASK_CODE,
       DRAW_TIME)
      SELECT QTC_SEQ_DAP_ECF_TI_ICU_CF.NEXTVAL AS ICU_DETAIL_ID,
             T1.ENTITY_ID,
             NULL                              AS DEPT_ID,
             T2.EVALUATE_APPROACH,
             T1.YEAR_MONTH,
             T2.LOA_CODE,
             T2.PORTFOLIO_NO,
             T2.ICG_NO,
             T2.TREATY_NO,
             T3.DEV_NO                         AS DEV_PERIOD,
             T3.UE_PREMIUM,
             T3.ED_PREMIUM,
             T3.RECV_PREMIUM,
             T3.IACF_FEE,
             T3.IACF_FEE_NON_POLICY,
             T3.MAINTENANCE_FEE,
             T3.ADJ_COMMISSION,
             T3.BROKERAGE_FEE,
             T3.ULTIMATE_LOSS,
             T1.TASK_CODE,
             SYSDATE                           AS DRAW_TIME
        FROM ATRUSER.ATR_BUSS_LRC_ACTION             T1,
             ATRUSER.ATR_BUSS_TI_LRC_ICU_CALC        T2,
             ATRUSER.ATR_BUSS_TI_LRC_ICU_CALC_DETAIL T3
       WHERE T1.ACTION_NO = T2.ACTION_NO
         AND T2.ID = T3.MAIN_ID
         AND T1.CONFIRM_IS = '1'
         AND T1.ENTITY_ID = p_buss.entity_id
         AND T1.YEAR_MONTH = p_buss.year_month;
    COMMIT;
    proc_update_bussperiod_detail(p_buss.buss_period_id,
                                  v_biz_code,
                                  '1',
                                  null);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      proc_update_bussperiod_detail(p_buss.buss_period_id,
                                    v_biz_code,
                                    '0',
                                    substr( SQLERRM, 1, 400));
  END PROC_FETCH_ECF_TI_ICU_CF;    
  
  
  
  PROCEDURE PROC_FETCH_ECF_TO_ICU_CF(p_buss record_buss) IS
  ------------------------------------------
  -- 保费 （直保&临分分入）
  ------------------------------------------
    v_biz_code varchar2(30) := 'DAP_ECF_TO_ICU_CF';
  BEGIN
  
    if func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
      return;
    end if;
    
    DELETE FROM QTC_DAP_ECF_TO_ICU_CF A
      WHERE 1 = 1
        AND A.entity_id = p_buss.entity_id
        AND YEAR_MONTH = p_buss.year_month;
    COMMIT;
    
    INSERT INTO QTC_DAP_ECF_TO_ICU_CF
      (ICU_DETAIL_ID,
       ENTITY_ID,
       DEPT_ID,
       EVALUATE_APPROACH,
       YEAR_MONTH,
       LOA_CODE,
       PORTFOLIO_NO,
       ICG_NO,
       TREATY_NO,
       DEV_PERIOD,
       UE_PREMIUM,
       ED_PREMIUM,
       RECV_PREMIUM,
       IACF_FEE,
       IACF_FEE_NON_POLICY,
       MAINTENANCE_FEE,
       ADJ_COMMISSION,
       BROKERAGE_FEE,
       ULTIMATE_LOSS,
       TASK_CODE,
       DRAW_TIME)
      SELECT QTC_SEQ_DAP_ECF_TO_ICU_CF.NEXTVAL AS ICU_DETAIL_ID,
             T1.ENTITY_ID,
             NULL                              AS DEPT_ID,
             T2.EVALUATE_APPROACH,
             T1.YEAR_MONTH,
             T2.LOA_CODE,
             T2.PORTFOLIO_NO,
             T2.ICG_NO,
             T2.TREATY_NO,
             T3.DEV_NO                         AS DEV_PERIOD,
             T3.UE_PREMIUM,
             T3.ED_PREMIUM,
             T3.RECV_PREMIUM,
             NULL AS IACF_FEE,
             NULL AS IACF_FEE_NON_POLICY,
             T3.MAINTENANCE_FEE,
             T3.ADJ_COMMISSION,
             T3.BROKERAGE_FEE,
             T3.ULTIMATE_LOSS,
             T1.TASK_CODE,
             SYSDATE                           AS DRAW_TIME
        FROM ATRUSER.ATR_BUSS_LRC_ACTION             T1,
             ATRUSER.ATR_BUSS_TO_LRC_ICU_CALC        T2,
             ATRUSER.ATR_BUSS_TO_LRC_ICU_CALC_DETAIL T3
       WHERE T1.ACTION_NO = T2.ACTION_NO
         AND T2.ID = T3.MAIN_ID
         AND T1.CONFIRM_IS = '1'
         AND T1.ENTITY_ID = p_buss.entity_id
         AND T1.YEAR_MONTH = p_buss.year_month;
    COMMIT;
    proc_update_bussperiod_detail(p_buss.buss_period_id,
                                  v_biz_code,
                                  '1',
                                  null);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      proc_update_bussperiod_detail(p_buss.buss_period_id,
                                    v_biz_code,
                                    '0',
                                    substr( SQLERRM, 1, 400));
  END PROC_FETCH_ECF_TO_ICU_CF;   
  
  
  
 PROCEDURE PROC_FETCH_ECF_CLAIM_DD_ICG_CF(p_buss record_buss) IS
  ------------------------------------------
  -- 保费 （直保&临分分入）
  ------------------------------------------
    v_biz_code varchar2(30) := 'DAP_ECF_CLAIM_DD_ICG_CF';
  BEGIN
  
    if func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
      return;
    end if;
    
    DELETE FROM QTC_DAP_ECF_CLAIM_DD_ICG_CF A
     WHERE 1 = 1
       AND A.entity_id = p_buss.entity_id
       AND YEAR_MONTH = p_buss.year_month;
    COMMIT;
    
    INSERT INTO QTC_DAP_ECF_CLAIM_DD_ICG_CF
      (ICU_DETAIL_ID,
       ENTITY_ID,
       DEPT_ID,
       EVALUATE_APPROACH,
       YEAR_MONTH,
       LOA_CODE,
       PORTFOLIO_NO,
       ICG_NO,
       DEV_PERIOD,
       IBNR_CUR,
       IBNR_PRE,
       OS_CUR,
       OS_PRE,
       ULAE_CUR,
       ULAE_PRE,
       TASK_CODE,
       DRAW_TIME)
      SELECT QTC_SEQ_DAP_ECF_CLAIM_DD_ICG_CF.NEXTVAL AS ICU_DETAIL_ID,
             T1.ENTITY_ID,
             NULL                                    AS DEPT_ID,
             T2.EVALUATE_APPROACH,
             T1.YEAR_MONTH,
             T2.LOA_CODE,
             T2.PORTFOLIO_NO,
             T2.ICG_NO,
             T3.DEV_NO                               AS DEV_PERIOD,
             IBNR_CUR,
             IBNR_PRE,
             OS_CUR,
             OS_PRE,
             ULAE_CUR,
             ULAE_PRE,
             T1.TASK_CODE,
             SYSDATE                                 AS DRAW_TIME
        FROM ATRUSER.ATR_BUSS_LIC_ACTION             T1,
             ATRUSER.ATR_BUSS_TO_LIC_ICG_CALC        T2,
             ATRUSER.ATR_BUSS_TO_LIC_ICG_CALC_DETAIL T3
       WHERE T1.ACTION_NO = T2.ACTION_NO
         AND T2.ID = T3.MAIN_ID
         AND T1.CONFIRM_IS = '1'
         AND T1.ENTITY_ID = p_buss.entity_id
         AND T1.YEAR_MONTH = p_buss.year_month;
    COMMIT;
    proc_update_bussperiod_detail(p_buss.buss_period_id,
                                  v_biz_code,
                                  '1',
                                  null);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      proc_update_bussperiod_detail(p_buss.buss_period_id,
                                    v_biz_code,
                                    '0',
                                    substr( SQLERRM, 1, 400));
  END PROC_FETCH_ECF_CLAIM_DD_ICG_CF;  
  
  
 PROCEDURE PROC_FETCH_ECF_CLAIM_FO_ICG_CF(p_buss record_buss) IS
  ------------------------------------------
  -- 保费 （直保&临分分入）
  ------------------------------------------
    v_biz_code varchar2(30) := 'DAP_ECF_CLAIM_FO_ICG_CF';
  BEGIN
  
    if func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
      return;
    end if;
    
    DELETE FROM QTC_DAP_ECF_CLAIM_FO_ICG_CF A
     WHERE 1 = 1
       AND A.entity_id = p_buss.entity_id
       AND YEAR_MONTH = p_buss.year_month;
    COMMIT;
    
    INSERT INTO QTC_DAP_ECF_CLAIM_FO_ICG_CF
      (ICU_DETAIL_ID,
       ENTITY_ID,
       DEPT_ID,
       EVALUATE_APPROACH,
       YEAR_MONTH,
       LOA_CODE,
       PORTFOLIO_NO,
       ICG_NO,
       DEV_PERIOD,
       IBNR_CUR,
       IBNR_PRE,
       OS_CUR,
       OS_PRE,
       ULAE_CUR,
       ULAE_PRE,
       TASK_CODE,
       DRAW_TIME)
      SELECT QTC_SEQ_DAP_ECF_CLAIM_FO_ICG_CF.NEXTVAL AS ICU_DETAIL_ID,
             T1.ENTITY_ID,
             NULL                                    AS DEPT_ID,
             T2.EVALUATE_APPROACH,
             T1.YEAR_MONTH,
             T2.LOA_CODE,
             T2.PORTFOLIO_NO,
             T2.ICG_NO,
             T3.DEV_NO                               AS DEV_PERIOD,
             IBNR_CUR,
             IBNR_PRE,
             OS_CUR,
             OS_PRE,
             ULAE_CUR,
             ULAE_PRE,
             T1.TASK_CODE,
             SYSDATE                                 AS DRAW_TIME
        FROM ATRUSER.ATR_BUSS_LIC_ACTION             T1,
             ATRUSER.ATR_BUSS_FO_LIC_ICG_CALC        T2,
             ATRUSER.ATR_BUSS_FO_LIC_ICG_CALC_DETAIL T3
       WHERE T1.ACTION_NO = T2.ACTION_NO
         AND T2.ID = T3.MAIN_ID
         AND T1.CONFIRM_IS = '1'
         AND T1.ENTITY_ID = p_buss.entity_id
         AND T1.YEAR_MONTH = p_buss.year_month;
    COMMIT;
    proc_update_bussperiod_detail(p_buss.buss_period_id,
                                  v_biz_code,
                                  '1',
                                  null);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      proc_update_bussperiod_detail(p_buss.buss_period_id,
                                    v_biz_code,
                                    '0',
                                    substr( SQLERRM, 1, 400));
  END PROC_FETCH_ECF_CLAIM_FO_ICG_CF;  
  
  
 PROCEDURE PROC_FETCH_ECF_CLAIM_TI_ICG_CF(p_buss record_buss) IS
  ------------------------------------------
  -- 保费 （直保&临分分入）
  ------------------------------------------
    v_biz_code varchar2(30) := 'DAP_ECF_CLAIM_TI_ICG_CF';
  BEGIN
  
    if func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
      return;
    end if;
    
    DELETE FROM QTC_DAP_ECF_CLAIM_TI_ICG_CF A
     WHERE 1 = 1
       AND A.entity_id = p_buss.entity_id
       AND YEAR_MONTH = p_buss.year_month;
    COMMIT;
    
    INSERT INTO QTC_DAP_ECF_CLAIM_TI_ICG_CF
      (ICU_DETAIL_ID,
       ENTITY_ID,
       DEPT_ID,
       EVALUATE_APPROACH,
       YEAR_MONTH,
       LOA_CODE,
       PORTFOLIO_NO,
       ICG_NO,
       DEV_PERIOD,
       IBNR_CUR,
       IBNR_PRE,
       OS_CUR,
       OS_PRE,
       ULAE_CUR,
       ULAE_PRE,
       TASK_CODE,
       DRAW_TIME)
      SELECT QTC_SEQ_DAP_ECF_CLAIM_TI_ICG_CF.NEXTVAL AS ICU_DETAIL_ID,
             T1.ENTITY_ID,
             NULL                                    AS DEPT_ID,
             T2.EVALUATE_APPROACH,
             T1.YEAR_MONTH,
             T2.LOA_CODE,
             T2.PORTFOLIO_NO,
             T2.ICG_NO,
             T3.DEV_NO                               AS DEV_PERIOD,
             IBNR_CUR,
             IBNR_PRE,
             OS_CUR,
             OS_PRE,
             ULAE_CUR,
             ULAE_PRE,
             T1.TASK_CODE,
             SYSDATE                                 AS DRAW_TIME
        FROM ATRUSER.ATR_BUSS_LIC_ACTION             T1,
             ATRUSER.ATR_BUSS_TI_LIC_ICG_CALC        T2,
             ATRUSER.ATR_BUSS_TI_LIC_ICG_CALC_DETAIL T3
       WHERE T1.ACTION_NO = T2.ACTION_NO
         AND T2.ID = T3.MAIN_ID
         AND T1.CONFIRM_IS = '1'
         AND T1.ENTITY_ID = p_buss.entity_id
         AND T1.YEAR_MONTH = p_buss.year_month;
    COMMIT;
    proc_update_bussperiod_detail(p_buss.buss_period_id,
                                  v_biz_code,
                                  '1',
                                  null);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      proc_update_bussperiod_detail(p_buss.buss_period_id,
                                    v_biz_code,
                                    '0',
                                    substr( SQLERRM, 1, 400));
  END PROC_FETCH_ECF_CLAIM_TI_ICG_CF;  
  
  
 PROCEDURE PROC_FETCH_ECF_CLAIM_TO_ICG_CF(p_buss record_buss) IS
  ------------------------------------------
  -- 保费 （直保&临分分入）
  ------------------------------------------
    v_biz_code varchar2(30) := 'DAP_ECF_CLAIM_TO_ICG_CF';
  BEGIN
  
    if func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
      return;
    end if;
    
    DELETE FROM QTC_DAP_ECF_CLAIM_TO_ICG_CF A
     WHERE 1 = 1
       AND A.entity_id = p_buss.entity_id
       AND YEAR_MONTH = p_buss.year_month;
    COMMIT;
    
    INSERT INTO QTC_DAP_ECF_CLAIM_TO_ICG_CF
      (ICU_DETAIL_ID,
       ENTITY_ID,
       DEPT_ID,
       EVALUATE_APPROACH,
       YEAR_MONTH,
       LOA_CODE,
       PORTFOLIO_NO,
       ICG_NO,
       DEV_PERIOD,
       IBNR_CUR,
       IBNR_PRE,
       OS_CUR,
       OS_PRE,
       ULAE_CUR,
       ULAE_PRE,
       TASK_CODE,
       DRAW_TIME)
      SELECT QTC_SEQ_DAP_ECF_CLAIM_TO_ICG_CF.NEXTVAL AS ICU_DETAIL_ID,
             T1.ENTITY_ID,
             NULL                                    AS DEPT_ID,
             T2.EVALUATE_APPROACH,
             T1.YEAR_MONTH,
             T2.LOA_CODE,
             T2.PORTFOLIO_NO,
             T2.ICG_NO,
             T3.DEV_NO                               AS DEV_PERIOD,
             IBNR_CUR,
             IBNR_PRE,
             OS_CUR,
             OS_PRE,
             ULAE_CUR,
             ULAE_PRE,
             T1.TASK_CODE,
             SYSDATE                                 AS DRAW_TIME
        FROM ATRUSER.ATR_BUSS_LIC_ACTION             T1,
             ATRUSER.ATR_BUSS_TO_LIC_ICG_CALC        T2,
             ATRUSER.ATR_BUSS_TO_LIC_ICG_CALC_DETAIL T3
       WHERE T1.ACTION_NO = T2.ACTION_NO
         AND T2.ID = T3.MAIN_ID
         AND T1.CONFIRM_IS = '1'
         AND T1.ENTITY_ID = p_buss.entity_id
         AND T1.YEAR_MONTH = p_buss.year_month;
    COMMIT;
    proc_update_bussperiod_detail(p_buss.buss_period_id,
                                  v_biz_code,
                                  '1',
                                  null);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      proc_update_bussperiod_detail(p_buss.buss_period_id,
                                    v_biz_code,
                                    '0',
                                    substr( SQLERRM, 1, 400));
  END PROC_FETCH_ECF_CLAIM_TO_ICG_CF;  
  
  
 PROCEDURE PROC_FETCH_DD_PREMIUM_PAID(p_buss record_buss) IS
  ------------------------------------------
  -- 保费 （直保&临分分入）
  ------------------------------------------
    v_biz_code varchar2(30) := 'DAP_DD_PREMIUM_PAID';
    v_task_code varchar2(32);
  BEGIN
    v_task_code := bpluser.bpl_pack_common.func_get_taskcode('QTC','M','SYNC');
    
    if func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
      return;
    end if;
    
    DELETE FROM QTC_DAP_DD_PREMIUM_PAID A
     WHERE 1 = 1
       AND A.entity_id = p_buss.entity_id
       AND YEAR_MONTH = p_buss.year_month;
    COMMIT;
    
    INSERT INTO QTC_DAP_DD_PREMIUM_PAID
      (PAID_ID,
       ENTITY_ID,
       DEPT_ID,
       POLICY_NO,
       ENDORSE_SEQ_NO,
       EST_PAYMENT_SEQ_NO,
       YEAR_MONTH,
       PAID_DATE,
       CURRENCY_CODE,
       PAID_PREMIUM,
       COMMISSION,
       TASK_CODE,
       DRAW_TIME)
      SELECT QTC_SEQ_DAP_DD_PREMIUM_PAID.NEXTVAL AS PAID_ID,
             T1.ENTITY_ID,
             T1.DEPT_ID,
             T1.POLICY_NO,
             T1.ENDORSE_SEQ_NO,
             T1.EST_PAYMENT_SEQ_NO,
             T1.YEAR_MONTH,
             T1.PAID_DATE,
             T1.CURRENCY_CODE,
             T1.PAID_PREMIUM,
             T1.COMMISSION,
             v_task_code                         AS TASK_CODE,
             SYSDATE                             AS DRAW_TIME
        FROM ATRUSER.ATR_DAP_DD_PREMIUM_PAID T1
       WHERE T1.ENTITY_ID = p_buss.entity_id
         AND T1.YEAR_MONTH = p_buss.year_month;
    COMMIT;
    proc_update_bussperiod_detail(p_buss.buss_period_id,
                                  v_biz_code,
                                  '1',
                                  null);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      proc_update_bussperiod_detail(p_buss.buss_period_id,
                                    v_biz_code,
                                    '0',
                                    substr( SQLERRM, 1, 400));
  END PROC_FETCH_DD_PREMIUM_PAID;   
  
  
 PROCEDURE PROC_FETCH_FO_PREMIUM_PAID(p_buss record_buss) IS
  ------------------------------------------
  -- 保费 （直保&临分分入）
  ------------------------------------------
    v_biz_code varchar2(30) := 'DAP_FO_PREMIUM_PAID';
    v_task_code varchar2(32);
  BEGIN
    v_task_code := bpluser.bpl_pack_common.func_get_taskcode('QTC','M','SYNC');
    if func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
      return;
    end if;
    
    DELETE FROM QTC_DAP_FO_PREMIUM_PAID A
     WHERE 1 = 1
       AND A.entity_id = p_buss.entity_id
       AND YEAR_MONTH = p_buss.year_month;
    COMMIT;
    
    INSERT INTO QTC_DAP_FO_PREMIUM_PAID
      (PAID_ID,
       ENTITY_ID,
       DEPT_ID,
       POLICY_NO,
       ENDORSE_SEQ_NO,
       RI_POLICY_NO,
       RI_ENDORSE_SEQ_NO,
       RI_STATEMENT_NO,
       YEAR_MONTH,
       PAID_DATE,
       CURRENCY_CODE,
       PAID_PREMIUM,
       COMMISSION,
       TASK_CODE,
       DRAW_TIME)
      SELECT QTC_SEQ_DAP_FO_PREMIUM_PAID.NEXTVAL AS PAID_ID,
             T1.ENTITY_ID,
             T1.DEPT_ID,
             T1.POLICY_NO,
             T1.ENDORSE_SEQ_NO,
             T1.RI_POLICY_NO,
             T1.RI_ENDORSE_SEQ_NO,
             T1.RI_STATEMENT_NO,
             T1.YEAR_MONTH,
             T1.PAID_DATE,
             T1.CURRENCY_CODE,
             T1.PAID_PREMIUM,
             T1.COMMISSION,
             v_task_code                         AS TASK_CODE,
             SYSDATE                             AS DRAW_TIME
        FROM ATRUSER.ATR_DAP_FO_PREMIUM_PAID T1
       WHERE T1.ENTITY_ID = p_buss.entity_id
         AND T1.YEAR_MONTH = p_buss.year_month;
    COMMIT;
    proc_update_bussperiod_detail(p_buss.buss_period_id,
                                  v_biz_code,
                                  '1',
                                  null);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      proc_update_bussperiod_detail(p_buss.buss_period_id,
                                    v_biz_code,
                                    '0',
                                    substr( SQLERRM, 1, 400));
  END PROC_FETCH_FO_PREMIUM_PAID;    
  
  
 PROCEDURE PROC_FETCH_TI_PREMIUM_PAID(p_buss record_buss) IS
  ------------------------------------------
  -- 保费 （直保&临分分入）
  ------------------------------------------
    v_biz_code varchar2(30) := 'DAP_TI_PREMIUM_PAID';
    v_task_code varchar2(32);
  BEGIN
    v_task_code := bpluser.bpl_pack_common.func_get_taskcode('QTC','M','SYNC');
  
    if func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
      return;
    end if;
    
    DELETE FROM QTC_DAP_TI_PREMIUM_PAID A
     WHERE 1 = 1
       AND A.entity_id = p_buss.entity_id
       AND YEAR_MONTH = p_buss.year_month;
    COMMIT;
    INSERT INTO QTC_DAP_TI_PREMIUM_PAID
      (PAID_ID,
       ENTITY_ID,
       DEPT_ID,
       TREATY_NO,
       RI_STATEMENT_NO,
       RI_MASTER_STATEMENT_NO,
       YEAR_MONTH,
       PAID_DATE,
       CURRENCY_CODE,
       PAID_PREMIUM,
       COMMISSION,
       TASK_CODE,
       DRAW_TIME)
      SELECT QTC_SEQ_DAP_TI_PREMIUM_PAID.NEXTVAL AS PAID_ID,
             T1.ENTITY_ID,
             T1.DEPT_ID,
             T1.TREATY_NO,
             T1.RI_STATEMENT_NO,
             T1.RI_MASTER_STATEMENT_NO,
             T1.YEAR_MONTH,
             T1.PAID_DATE,
             T1.CURRENCY_CODE,
             T1.PAID_PREMIUM,
             T1.COMMISSION,
             v_task_code                           AS TASK_CODE,
             SYSDATE                             AS DRAW_TIME
        FROM ATRUSER.ATR_DAP_TI_PREMIUM_PAID T1
       WHERE T1.ENTITY_ID = p_buss.entity_id
         AND T1.YEAR_MONTH = p_buss.year_month;
    COMMIT;
    proc_update_bussperiod_detail(p_buss.buss_period_id,
                                  v_biz_code,
                                  '1',
                                  null);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      proc_update_bussperiod_detail(p_buss.buss_period_id,
                                    v_biz_code,
                                    '0',
                                    substr( SQLERRM, 1, 400));
  END PROC_FETCH_TI_PREMIUM_PAID;   
  
  
 PROCEDURE PROC_FETCH_TO_PREMIUM_PAID(p_buss record_buss) IS
  ------------------------------------------
  -- 保费 （直保&临分分入）
  ------------------------------------------
    v_biz_code varchar2(30) := 'DAP_TO_PREMIUM_PAID';
    v_task_code varchar2(32);
  BEGIN
    v_task_code := bpluser.bpl_pack_common.func_get_taskcode('QTC','M','SYNC');
  
    if func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
      return;
    end if;
    
    DELETE FROM QTC_DAP_TO_PREMIUM_PAID A
     WHERE 1 = 1
       AND A.entity_id = p_buss.entity_id
       AND YEAR_MONTH = p_buss.year_month;
    COMMIT;
    
    INSERT INTO QTC_DAP_TO_PREMIUM_PAID
      (PAID_ID,
       ENTITY_ID,
       DEPT_ID,
       TREATY_NO,
       RI_STATEMENT_NO,
       RI_MASTER_STATEMENT_NO,
       YEAR_MONTH,
       PAID_DATE,
       CURRENCY_CODE,
       PAID_PREMIUM,
       COMMISSION,
       TASK_CODE,
       DRAW_TIME)
      SELECT QTC_SEQ_DAP_TO_PREMIUM_PAID.NEXTVAL AS PAID_ID,
             T1.ENTITY_ID,
             T1.DEPT_ID,
             T1.TREATY_NO,
             T1.RI_STATEMENT_NO,
             T1.RI_MASTER_STATEMENT_NO,
             T1.YEAR_MONTH,
             T1.PAID_DATE,
             T1.CURRENCY_CODE,
             T1.PAID_PREMIUM,
             T1.COMMISSION,
             v_task_code                        AS TASK_CODE,
             SYSDATE                             AS DRAW_TIME
        FROM ATRUSER.ATR_DAP_TO_PREMIUM_PAID T1
       WHERE T1.ENTITY_ID = p_buss.entity_id
         AND T1.YEAR_MONTH = p_buss.year_month;
    COMMIT;
    proc_update_bussperiod_detail(p_buss.buss_period_id,
                                  v_biz_code,
                                  '1',
                                  null);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      proc_update_bussperiod_detail(p_buss.buss_period_id,
                                    v_biz_code,
                                    '0',
                                    substr( SQLERRM, 1, 400));
  END PROC_FETCH_TO_PREMIUM_PAID;   
  
  
  
 PROCEDURE PROC_FETCH_DD_CLAIM_PAID(p_buss record_buss) IS
  ------------------------------------------
  -- 保费 （直保&临分分入）
  ------------------------------------------
    v_biz_code varchar2(30) := 'DAP_DD_CLAIM_PAID';
    v_task_code varchar2(32);
  BEGIN
    v_task_code := bpluser.bpl_pack_common.func_get_taskcode('QTC','M','SYNC');
  
    if func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
      return;
    end if;
    
    DELETE FROM QTC_DAP_DD_CLAIM_PAID A
     WHERE 1 = 1
       AND A.entity_id = p_buss.entity_id
       AND YEAR_MONTH = p_buss.year_month;
    COMMIT;
    
    INSERT INTO QTC_DAP_DD_CLAIM_PAID
      (PAID_ID,
       ENTITY_ID,
       DEPT_ID,
       POLICY_NO,
       ENDORSE_SEQ_NO,
       CLAIM_LOSS_NO,
       CLAIM_LOSS_SEQ_NO,
       YEAR_MONTH,
       ACTUAL_PAYMENT_DATE,
       ACCIDENT_DATE_TIME,
       CURRENCY_CODE,
       PAID_AMOUNT,
       TASK_CODE,
       DRAW_TIME)
      SELECT QTC_SEQ_DAP_DD_CLAIM_PAID.NEXTVAL AS PAID_ID,
             T1.ENTITY_ID,
             T1.DEPT_ID,
             T1.POLICY_NO,
             T1.ENDORSE_SEQ_NO,
             T1.CLAIM_LOSS_NO,
             T1.CLAIM_LOSS_SEQ_NO,
             T1.YEAR_MONTH,
             T1.ACTUAL_PAYMENT_DATE,
             T1.ACCIDENT_DATE_TIME,
             T1.CURRENCY_CODE,
             T1.PAID_AMOUNT,
             v_task_code                    AS TASK_CODE,
             SYSDATE                             AS DRAW_TIME
        FROM ATRUSER.ATR_DAP_DD_CLAIM_PAID T1
       WHERE T1.ENTITY_ID = p_buss.entity_id
         AND T1.YEAR_MONTH = p_buss.year_month;
   
    COMMIT;
    proc_update_bussperiod_detail(p_buss.buss_period_id,
                                  v_biz_code,
                                  '1',
                                  null);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      proc_update_bussperiod_detail(p_buss.buss_period_id,
                                    v_biz_code,
                                    '0',
                                    substr( SQLERRM, 1, 400));
  END PROC_FETCH_DD_CLAIM_PAID;   
  
 PROCEDURE PROC_FETCH_FO_CLAIM_PAID(p_buss record_buss) IS
  ------------------------------------------
  -- 保费 （直保&临分分入）
  ------------------------------------------
    v_biz_code varchar2(30) := 'DAP_FO_CLAIM_PAID';
    v_task_code varchar2(32);
  BEGIN
    v_task_code := bpluser.bpl_pack_common.func_get_taskcode('QTC','M','SYNC');
  
    if func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
      return;
    end if;
    
    DELETE FROM QTC_DAP_FO_CLAIM_PAID A
     WHERE 1 = 1
       AND A.entity_id = p_buss.entity_id
       AND YEAR_MONTH = p_buss.year_month;
    COMMIT;
    
    INSERT INTO QTC_DAP_FO_CLAIM_PAID
      (PAID_ID,
       ENTITY_ID,
       DEPT_ID,
       RI_POLICY_NO,
       RI_ENDORSE_SEQ_NO,
       CLAIM_LOSS_NO,
       CLAIM_LOSS_SEQ_NO,
       YEAR_MONTH,
       ACTUAL_PAYMENT_DATE,
       ACCIDENT_DATE_TIME,
       CURRENCY_CODE,
       PAID_AMOUNT,
       TASK_CODE,
       DRAW_TIME)
      SELECT QTC_SEQ_DAP_FO_CLAIM_PAID.NEXTVAL AS PAID_ID,
             T1.ENTITY_ID,
             T1.DEPT_ID,
             T1.RI_POLICY_NO,
             T1.RI_ENDORSE_SEQ_NO,
             T1.CLAIM_LOSS_NO,
             T1.CLAIM_LOSS_SEQ_NO,
             T1.YEAR_MONTH,
             T1.ACTUAL_PAYMENT_DATE,
             T1.ACCIDENT_DATE_TIME,
             T1.CURRENCY_CODE,
             T1.PAID_AMOUNT,
             v_task_code                     AS TASK_CODE,
             SYSDATE                             AS DRAW_TIME
        FROM ATRUSER.ATR_DAP_FO_CLAIM_PAID T1
       WHERE T1.ENTITY_ID = p_buss.entity_id
         AND T1.YEAR_MONTH = p_buss.year_month;
    COMMIT;
    proc_update_bussperiod_detail(p_buss.buss_period_id,
                                  v_biz_code,
                                  '1',
                                  null);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      proc_update_bussperiod_detail(p_buss.buss_period_id,
                                    v_biz_code,
                                    '0',
                                    substr( SQLERRM, 1, 400));
  END PROC_FETCH_FO_CLAIM_PAID;   
  
  
 PROCEDURE PROC_FETCH_TI_CLAIM_PAID(p_buss record_buss) IS
  ------------------------------------------
  -- 保费 （直保&临分分入）
  ------------------------------------------
    v_biz_code varchar2(30) := 'DAP_TI_CLAIM_PAID';
    v_task_code varchar2(32);
  BEGIN
    v_task_code := bpluser.bpl_pack_common.func_get_taskcode('QTC','M','SYNC');
  
    if func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
      return;
    end if;
    
    DELETE FROM QTC_DAP_TI_CLAIM_PAID A
     WHERE 1 = 1
       AND A.entity_id = p_buss.entity_id
       AND YEAR_MONTH = p_buss.year_month;
    COMMIT;
    INSERT INTO QTC_DAP_TI_CLAIM_PAID
      (PAID_ID,
       ENTITY_ID,
       DEPT_ID,
       TREATY_NO,
       RI_STATEMENT_NO,
       RI_MASTER_STATEMENT_NO,
       CLAIM_LOSS_NO,
       CLAIM_LOSS_SEQ_NO,
       YEAR_MONTH,
       ACTUAL_PAYMENT_DATE,
       ACCIDENT_DATE_TIME,
       CURRENCY_CODE,
       PAID_AMOUNT,
       TASK_CODE,
       DRAW_TIME)
      SELECT QTC_SEQ_DAP_TI_CLAIM_PAID.NEXTVAL AS PAID_ID,
             T1.ENTITY_ID,
             T1.DEPT_ID,
             TREATY_NO,
             RI_STATEMENT_NO,
             RI_MASTER_STATEMENT_NO,
             T1.CLAIM_LOSS_NO,
             T1.CLAIM_LOSS_SEQ_NO,
             T1.YEAR_MONTH,
             T1.ACTUAL_PAYMENT_DATE,
             T1.ACCIDENT_DATE_TIME,
             T1.CURRENCY_CODE,
             T1.PAID_AMOUNT,
             v_task_code                      AS TASK_CODE,
             SYSDATE                             AS DRAW_TIME
        FROM ATRUSER.ATR_DAP_TI_CLAIM_PAID T1
       WHERE T1.ENTITY_ID = p_buss.entity_id
         AND T1.YEAR_MONTH = p_buss.year_month;
    COMMIT;
    proc_update_bussperiod_detail(p_buss.buss_period_id,
                                  v_biz_code,
                                  '1',
                                  null);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      proc_update_bussperiod_detail(p_buss.buss_period_id,
                                    v_biz_code,
                                    '0',
                                    substr( SQLERRM, 1, 400));
  END PROC_FETCH_TI_CLAIM_PAID;   
  
  
 PROCEDURE PROC_FETCH_TO_CLAIM_PAID(p_buss record_buss) IS
  ------------------------------------------
  -- 保费 （直保&临分分入）
  ------------------------------------------
    v_biz_code varchar2(30) := 'DAP_TO_CLAIM_PAID';
    v_task_code varchar2(32);
  BEGIN
    v_task_code := bpluser.bpl_pack_common.func_get_taskcode('QTC','M','SYNC');
  
    if func_check_if_biz_continue(p_buss, v_biz_code) = 0 then
      return;
    end if;
    
    DELETE FROM QTC_DAP_TO_CLAIM_PAID A
     WHERE 1 = 1
       AND A.entity_id = p_buss.entity_id
       AND YEAR_MONTH = p_buss.year_month;
    COMMIT;
    
    INSERT INTO QTC_DAP_TO_CLAIM_PAID
      (PAID_ID,
       ENTITY_ID,
       DEPT_ID,
       TREATY_NO,
       RI_STATEMENT_NO,
       RI_MASTER_STATEMENT_NO,
       CLAIM_LOSS_NO,
       CLAIM_LOSS_SEQ_NO,
       YEAR_MONTH,
       ACTUAL_PAYMENT_DATE,
       ACCIDENT_DATE_TIME,
       CURRENCY_CODE,
       PAID_AMOUNT,
       TASK_CODE,
       DRAW_TIME)
      SELECT QTC_SEQ_DAP_DD_CLAIM_PAID.NEXTVAL AS PAID_ID,
             T1.ENTITY_ID,
             T1.DEPT_ID,
             TREATY_NO,
             RI_STATEMENT_NO,
             RI_MASTER_STATEMENT_NO,
             T1.CLAIM_LOSS_NO,
             T1.CLAIM_LOSS_SEQ_NO,
             T1.YEAR_MONTH,
             T1.ACTUAL_PAYMENT_DATE,
             T1.ACCIDENT_DATE_TIME,
             T1.CURRENCY_CODE,
             T1.PAID_AMOUNT,
             v_task_code                    AS TASK_CODE,
             SYSDATE                             AS DRAW_TIME
        FROM ATRUSER.ATR_DAP_TO_CLAIM_PAID T1
       WHERE T1.ENTITY_ID = p_buss.entity_id
         AND T1.YEAR_MONTH = p_buss.year_month;
     
    COMMIT;
    proc_update_bussperiod_detail(p_buss.buss_period_id,
                                  v_biz_code,
                                  '1',
                                  null);
  EXCEPTION
    WHEN OTHERS THEN
      ROLLBACK;
      proc_update_bussperiod_detail(p_buss.buss_period_id,
                                    v_biz_code,
                                    '0',
                                    substr( SQLERRM, 1, 400));
  END PROC_FETCH_TO_CLAIM_PAID;   
  
  
  
  
  
  procedure proc_fetch_all_actuarial_data(v_buss record_buss) is
    
    v_log_msg   VARCHAR2(4000); --日志信息
    v_error_msg VARCHAR2(200); --异常信息
  begin
    PROC_FETCH_ECF_DD_ICU(v_buss);
    PROC_FETCH_ECF_FO_ICU(v_buss);
    PROC_FETCH_ECF_TI_ICU(v_buss);
    PROC_FETCH_ECF_TO_ICU(v_buss);
    
    PROC_FETCH_ECF_DD_ICU_CF(v_buss);
    PROC_FETCH_ECF_FO_ICU_CF(v_buss);
    PROC_FETCH_ECF_TI_ICU_CF(v_buss);
    PROC_FETCH_ECF_TO_ICU_CF(v_buss);
    
    PROC_FETCH_ECF_CLAIM_DD_ICG_CF(v_buss);
    PROC_FETCH_ECF_CLAIM_FO_ICG_CF(v_buss);
    PROC_FETCH_ECF_CLAIM_TI_ICG_CF(v_buss);
    PROC_FETCH_ECF_CLAIM_TO_ICG_CF(v_buss);
    
    PROC_FETCH_DD_PREMIUM_PAID(v_buss);
    PROC_FETCH_FO_PREMIUM_PAID(v_buss);
    PROC_FETCH_TI_PREMIUM_PAID(v_buss);
    PROC_FETCH_TO_PREMIUM_PAID(v_buss);
    
    PROC_FETCH_DD_CLAIM_PAID(v_buss);
    PROC_FETCH_FO_CLAIM_PAID(v_buss);
    PROC_FETCH_TI_CLAIM_PAID(v_buss);
    PROC_FETCH_TO_CLAIM_PAID(v_buss);
  EXCEPTION
      WHEN OTHERS THEN
        --抛出自定义异常信息 【会中断事务】
        v_log_msg := substr('计量同步发生异常，请检查！：'  || '；**SQLERRM: ' || SQLERRM || '**Error line: ' || dbms_utility.format_error_backtrace() || '', 1, 4000);
         dbms_output.put_line(v_log_msg);
        
        --往外层抛出异常信息
        raise_application_error(-20003, v_log_msg);
    
  end proc_fetch_all_actuarial_data;
  
 
  PROCEDURE PROC_FETCH_ALL(p_entityid NUMBER,
                           p_yearmonth VARCHAR2,
                           p_userid NUMBER) is
    --------------------------------
    -- 提取所有接口表数据
    --------------------------------
    v_year_month    varchar2(6);
    v_data_status          VARCHAR2(10):= 3;
    v_buss_period_id       NUMBER(11);
    v_period_detail_id     NUMBER(11);
    v_period_detail_status VARCHAR2(10);
    v_base_currency varchar2(3);
    v_buss          record_buss;
    
    v_log_msg   VARCHAR2(4000); --日志信息
    v_error_msg VARCHAR2(200); --异常信息
  begin
    SELECT T.EXECUTION_STATE 
      INTO V_DATA_STATUS 
      FROM ATRUSER.ATR_CONF_BUSSPERIOD T
     WHERE T.ENTITY_ID = P_ENTITYID
       AND T.YEAR_MONTH = P_YEARMONTH
       AND t.valid_is = '1'; 
       
    --查询当前业务期间详情id, 验证当前数据是否为准备中
    SELECT p.EXECUTION_STATE,p.buss_period_id 
      INTO v_period_detail_status,v_buss_period_id
      FROM qtc_conf_bussperiod p
     WHERE p.entity_id = p_entityid
       AND p.year_month = p_yearmonth
       AND p.valid_is = '1';
       
    v_buss.entity_id     := p_entityid;
    v_buss.year_month    := p_yearmonth;
    v_buss.user_id       := p_userid;
    v_buss.buss_period_id     := v_buss_period_id;
       
	   
    IF v_data_status != '3' THEN
      v_error_msg := '精算平台期间状态不是已完成为:' || '，计量数据不执行同步：[' || p_entityid || ']-[' || p_yearmonth || ']';
      --往外层抛出异常信息
      raise_application_error(-20002, v_error_msg);
    END IF;
    IF v_data_status >= '3'
       AND v_period_detail_status = '0' THEN
       proc_fetch_all_actuarial_data(v_buss);
      dbms_output.put_line(v_buss_period_id);
      dbms_output.put_line(v_period_detail_status);

    END IF;
    --同步业务期间执行状态
    qtcuser.qtc_pack_buss_period.proc_period_execution(p_entityid, '1'); 
    EXCEPTION
      WHEN OTHERS THEN
        --抛出自定义异常信息 【会中断事务】
        v_log_msg := substr('计量同步发生异常，请检查！：' || v_error_msg || '；**SQLERRM: ' || SQLERRM || '**Error line: ' || dbms_utility.format_error_backtrace() || '', 1, 4000);
         dbms_output.put_line(v_log_msg);
        
        --往外层抛出异常信息
        raise_application_error(-20003, v_log_msg);
  end PROC_FETCH_ALL;
  

END qtc_pack_data_sync;
/
