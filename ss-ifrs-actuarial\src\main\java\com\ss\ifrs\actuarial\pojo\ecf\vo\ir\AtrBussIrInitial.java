package com.ss.ifrs.actuarial.pojo.ecf.vo.ir;

import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Data;

import java.util.Date;

@Data
@Tab("atr_buss_ir_initial")
public class AtrBussIrInitial {

    private Long id;

    private Long uploadRateId;

    private Long entityId;

    private String yearMonth;

    private String currencyCode;

    private String confirmIs;

    private Date confirmTime;

    private Date createTime;

}
