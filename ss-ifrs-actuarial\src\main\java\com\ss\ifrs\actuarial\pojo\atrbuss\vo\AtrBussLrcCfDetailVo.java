/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2022-12-07 10:49:05
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2022-12-07 10:49:05<br/>
 * Description: LRC现金流明细<br/>
 * Table Name: ATR_BUSS_LRC_CF_DETAIL<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "LRC现金流明细")
public class AtrBussLrcCfDetailVo implements Serializable {
    /**
     * Database column: ATR_BUSS_LRC_CF_DETAIL.LRC_CF_DETAIL_ID
     * Database remarks: lrc_cf_detail_id|主键
     */
    @ApiModelProperty(value = "lrc_cf_detail_id|主键", required = true)
    private Long lrcCfDetailId;

    /**
     * Database column: ATR_BUSS_LRC_CF_DETAIL.LRC_CF_MAIN_ID
     * Database remarks: lrc_cf_main_id|主键
     */
    @ApiModelProperty(value = "lrc_cf_main_id|主键", required = true)
    private Long lrcCfMainId;

    /**
     * Database column: ATR_BUSS_LRC_CF_DETAIL.PORTFOLIO_NO
     * Database remarks: portfolio_no|合同组合
     */
    @ApiModelProperty(value = "portfolio_no|合同组合", required = false)
    private String portfolioNo;

    /**
     * Database column: ATR_BUSS_LRC_CF_DETAIL.ICG_NO
     * Database remarks: icg_no|合同组
     */
    @ApiModelProperty(value = "icg_no|合同组", required = false)
    private String icgNo;

    /**
     * Database column: ATR_BUSS_LRC_CF_DETAIL.RUN_NO
     * Database remarks: run_No|编号
     */
    @ApiModelProperty(value = "run_No|编号", required = false)
    private Long runNo;

    /**
     * Database column: ATR_BUSS_LRC_CF_DETAIL.CF_DATE
     * Database remarks: cf_date|现金流日期
     */
    @ApiModelProperty(value = "cf_date|现金流日期", required = false)
    private String cfDate;

    /**
     * Database column: ATR_BUSS_LRC_CF_DETAIL.MAINTENANCE_EXPENSE
     * Database remarks: maintenance_expense|维持费用
     */
    @ApiModelProperty(value = "maintenance_expense|维持费用", required = false)
    private BigDecimal maintenanceExpense;

    /**
     * Database column: ATR_BUSS_LRC_CF_DETAIL.EXPECTED_CLAIM
     * Database remarks: expected_claim|预期理赔费用
     */
    @ApiModelProperty(value = "expected_claim|预期理赔费用", required = false)
    private BigDecimal expectedClaim;

    /**
     * Database column: ATR_BUSS_LRC_CF_DETAIL.COVERAGE_UNIT
     * Database remarks: coverage_unit|保额/限额
     */
    @ApiModelProperty(value = "coverage_unit|保额/限额", required = false)
    private BigDecimal coverageUnit;

    /**
     * Database column: ATR_BUSS_LRC_CF_DETAIL.EXPECTED_BROKERAGE
     * Database remarks: expected_brokerage|经纪人费用
     */
    @ApiModelProperty(value = "expected_brokerage|经纪人费用", required = false)
    private BigDecimal expectedBrokerage;

    /**
     * Database column: ATR_BUSS_LRC_CF_DETAIL.EXPECTED_ADJ_COMMISON
     * Database remarks: expected_adj_commison|预期手续费调整
     */
    @ApiModelProperty(value = "expected_adj_commison|预期手续费调整", required = false)
    private BigDecimal expectedAdjCommission;

    /**
     * Database column: ATR_BUSS_LRC_CF_DETAIL.EXPECTED_CLAIM_ADJ_COMMISON
     * Database remarks: expected_claim_adj_commison|预期理赔费用(含手续费调整)
     */
    @ApiModelProperty(value = "expected_claim_adj_commison|预期理赔费用(含手续费调整)", required = false)
    private BigDecimal expectedClaimAdjCommission;

    /**
     * Database column: ATR_BUSS_LRC_CF_DETAIL.CSM_AMORTIZED_RATE
     * Database remarks: csm_Amortized_rate|CSM 摊销比例
     */
    @ApiModelProperty(value = "csm_Amortized_rate|CSM 摊销比例", required = false)
    private BigDecimal csmAmortizedRate;


    private static final long serialVersionUID = 1L;

    public Long getLrcCfDetailId() {
        return lrcCfDetailId;
    }

    public void setLrcCfDetailId(Long lrcCfDetailId) {
        this.lrcCfDetailId = lrcCfDetailId;
    }

    public Long getLrcCfMainId() {
        return lrcCfMainId;
    }

    public void setLrcCfMainId(Long lrcCfMainId) {
        this.lrcCfMainId = lrcCfMainId;
    }

    public String getPortfolioNo() {
        return portfolioNo;
    }

    public void setPortfolioNo(String portfolioNo) {
        this.portfolioNo = portfolioNo;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public Long getRunNo() {
        return runNo;
    }

    public void setRunNo(Long runNo) {
        this.runNo = runNo;
    }

    public String getCfDate() {
        return cfDate;
    }

    public void setCfDate(String cfDate) {
        this.cfDate = cfDate;
    }

    public BigDecimal getMaintenanceExpense() {
        return maintenanceExpense;
    }

    public void setMaintenanceExpense(BigDecimal maintenanceExpense) {
        this.maintenanceExpense = maintenanceExpense;
    }

    public BigDecimal getExpectedClaim() {
        return expectedClaim;
    }

    public void setExpectedClaim(BigDecimal expectedClaim) {
        this.expectedClaim = expectedClaim;
    }

    public BigDecimal getCoverageUnit() {
        return coverageUnit;
    }

    public void setCoverageUnit(BigDecimal coverageUnit) {
        this.coverageUnit = coverageUnit;
    }

    public BigDecimal getExpectedBrokerage() {
        return expectedBrokerage;
    }

    public void setExpectedBrokerage(BigDecimal expectedBrokerage) {
        this.expectedBrokerage = expectedBrokerage;
    }

    public BigDecimal getExpectedAdjCommission() {
        return expectedAdjCommission;
    }

    public void setExpectedAdjCommission(BigDecimal expectedAdjCommission) {
        this.expectedAdjCommission = expectedAdjCommission;
    }

    public BigDecimal getExpectedClaimAdjCommission() {
        return expectedClaimAdjCommission;
    }

    public void setExpectedClaimAdjCommission(BigDecimal expectedClaimAdjCommission) {
        this.expectedClaimAdjCommission = expectedClaimAdjCommission;
    }

    public BigDecimal getCsmAmortizedRate() {
        return csmAmortizedRate;
    }

    public void setCsmAmortizedRate(BigDecimal csmAmortizedRate) {
        this.csmAmortizedRate = csmAmortizedRate;
    }

}