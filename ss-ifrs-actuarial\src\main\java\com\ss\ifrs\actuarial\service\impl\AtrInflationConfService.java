package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.dao.conf.AtrConfInflationFactorDao;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrInflationRatioOverviewVo;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfInflationFactor;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfInflationFactorConditionVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrInflationRatioRangeVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrInflationRatioVo;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Service
public class AtrInflationConfService {

    @Resource
    private AtrConfInflationFactorDao atrConfInflationFactorDao;

    public void save(AtrConfInflationFactor vo) {
        String bussYear = vo.getBussYear();
        BigDecimal infFactor = vo.getInfFactor();
        Assert.hasText(bussYear, "year must not be empty");
        Assert.notNull(infFactor, "factor must not be empty");

        AtrConfInflationFactor po = new AtrConfInflationFactor();
        po.setBussYear(bussYear);
        po.setInfFactor(infFactor);
        po.setConfirmIs("0");
        po.setConfirmId(null);
        po.setUpdateTime(new Date());

        if (vo.getId() == null) {
            AtrConfInflationFactor poQ = new AtrConfInflationFactor();
            poQ.setBussYear(bussYear);
            if (!atrConfInflationFactorDao.findList(poQ).isEmpty()) {
                throw new RuntimeException("The inflation coefficient of the year " + bussYear
                        + " has been configured, " +
                        "and cannot be configured repeatedly");
            }
            po.setCreateTime(new Date());
            atrConfInflationFactorDao.save(po);
        } else {
            po.setId(vo.getId());
            atrConfInflationFactorDao.updateById(po);
        }
    }

    public void confirm(AtrConfInflationFactor vo, Long userId) {
        Long id = vo.getId();
        Assert.notNull(id, "id must not be empty");

        AtrConfInflationFactor po = atrConfInflationFactorDao.findById(id);
        if (po == null) {
            throw new RuntimeException("Configuration [id=" + id +"] does not exist");
        }
        if ("1".equalsIgnoreCase(po.getConfirmIs())) {
            throw new RuntimeException("Configuration [id=" + id +"] has been confirmed, " +
                    "and cannot be confirmed repeatedly");
        }
        po.setConfirmIs("1");
        po.setConfirmId(userId);
        po.setConfirmTime(new Date());
        atrConfInflationFactorDao.updateById(po);

        atrConfInflationFactorDao.calcRatio();
    }

    public void delete(AtrConfInflationFactorConditionVo vo) {
        Long id = vo.getId();
        Assert.notNull(id, "id must not be empty");

        AtrConfInflationFactor po = atrConfInflationFactorDao.findById(id);
        if (po == null) {
            throw new RuntimeException("Configuration [id=" + id + "] does not exist");
        }
        if ("1".equalsIgnoreCase(po.getConfirmIs())) {
            throw new RuntimeException("Configuration [id=" + id +"] has been confirmed and cannot be deleted");
        }

        atrConfInflationFactorDao.deleteById(id);

        atrConfInflationFactorDao.calcRatio();
    }

    public AtrInflationRatioOverviewVo overviewConfRatio() {
        AtrInflationRatioRangeVo rangeVo = atrConfInflationFactorDao.queryRatioRange();
        List<AtrInflationRatioVo> ratioVos = atrConfInflationFactorDao.queryAllRatio();
        return overviewConfRatio(rangeVo, ratioVos);
    }

    public AtrInflationRatioOverviewVo overviewConfRatio(AtrInflationRatioRangeVo rangeVo,
                                                         List<AtrInflationRatioVo> ratioVos) {

        if (rangeVo == null || rangeVo.getMinBussYear() == null || rangeVo.getMinDevYear() == null) {
            AtrInflationRatioOverviewVo vo = new AtrInflationRatioOverviewVo();
            vo.setYears(new int[0]);
            vo.setRatios(new BigDecimal[0][0]);
            return vo;
        }

        Integer minBussYear = rangeVo.getMinBussYear();
        Integer maxBussYear = rangeVo.getMaxBussYear();
        Integer minDevYear = rangeVo.getMinDevYear();
        Integer maxDevYear = rangeVo.getMaxDevYear();

        int[] years = new int[maxDevYear - minDevYear + 1];
        BigDecimal[][] ratios = new BigDecimal[maxBussYear - minBussYear + 1][years.length];

        for (int i = minDevYear; i <= maxDevYear; i++) {
            years[i - minDevYear] = i;
        }

        for (AtrInflationRatioVo ratioVo : ratioVos) {
            int bussYear = ratioVo.getBussYear();
            int devYear = ratioVo.getDevYear();
            ratios[bussYear - minBussYear][devYear - minDevYear] = ratioVo.getRatio();
        }

        AtrInflationRatioOverviewVo vo = new AtrInflationRatioOverviewVo();
        vo.setYears(years);
        vo.setRatios(ratios);
        return vo;
    }
}
