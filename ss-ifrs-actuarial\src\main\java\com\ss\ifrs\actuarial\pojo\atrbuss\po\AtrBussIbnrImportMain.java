/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2023-02-06 17:15:22
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-06 17:15:22<br/>
 * Description: IBNR导入主表<br/>
 * Table Name: ATR_BUSS_IBNR_IMPORT_MAIN<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "IBNR导入主表")
public class AtrBussIbnrImportMain implements Serializable {
    /**
     * Database column: ATR_BUSS_IBNR_IMPORT_MAIN.IBNR_MAIN_ID
     * Database remarks: ibnr_main_id|主键
     */
    @ApiModelProperty(value = "ibnr_main_id|主键", required = true)
    private Long ibnrMainId;

    /**
     * Database column: ATR_BUSS_IBNR_IMPORT_MAIN.CENTER_ID
     * Database remarks: Center_Id|核算单位ID
     */
    @ApiModelProperty(value = "Center_Id|核算单位ID", required = false)
    private Long entityId;

    /**
     * Database column: ATR_BUSS_IBNR_IMPORT_MAIN.YEAR_MONTH
     * Database remarks: year_month|评估期
     */
    @ApiModelProperty(value = "year_month|评估期", required = false)
    private String yearMonth;

    /**
     * Database column: ATR_BUSS_IBNR_IMPORT_MAIN.CURRENCY
     * Database remarks: currency|币种
     */
    @ApiModelProperty(value = "currency|币种", required = false)
    private String ibnrRange;

    private BigDecimal provisionRatio;


    /**
     * Database column: ATR_BUSS_IBNR_IMPORT_MAIN.USE_IMPORT_OS_IS
     * Database remarks: use_import_os_is|是否使用导入OS数据(0-否/1-是)
     */
    @ApiModelProperty(value = "use_import_os_is|是否使用导入OS数据(0-否/1-是)", required = false)
    private String useImportOsIs;

    /**
     * Database column: ATR_BUSS_IBNR_IMPORT_MAIN.VERSION_NO
     * Database remarks: version_no|版本号
     */
    @ApiModelProperty(value = "version_no|版本号", required = false)
    private String versionNo;

    /**
     * Database column: ATR_BUSS_IBNR_IMPORT_MAIN.CONFIRM_IS
     * Database remarks: confirm_is|是否确认
     */
    @ApiModelProperty(value = "confirm_is|是否确认", required = false)
    private String confirmIs;

    /**
     * Database column: ATR_BUSS_IBNR_IMPORT_MAIN.CONFIRM_ID
     * Database remarks: confirm_id|确认人
     */
    @ApiModelProperty(value = "confirm_id|确认人", required = false)
    private Long confirmId;

    /**
     * Database column: ATR_BUSS_IBNR_IMPORT_MAIN.CONFIRM_TIME
     * Database remarks: confirm_time|确认时间
     */
    @ApiModelProperty(value = "confirm_time|确认时间", required = false)
    private Date confirmTime;

    /**
     * Database column: ATR_BUSS_IBNR_IMPORT_MAIN.REMARK
     * Database remarks: remark|备注
     */
    @ApiModelProperty(value = "remark|备注", required = false)
    private String remark;

    /**
     * Database column: ATR_BUSS_IBNR_IMPORT_MAIN.CREATOR_ID
     * Database remarks: creator_Id|创建人
     */
    @ApiModelProperty(value = "creator_Id|创建人", required = false)
    private Long creatorId;

    /**
     * Database column: ATR_BUSS_IBNR_IMPORT_MAIN.CREATE_TIME
     * Database remarks: create_Time|创建时间
     */
    @ApiModelProperty(value = "create_Time|创建时间", required = false)
    private Date createTime;

    /**
     * Database column: ATR_BUSS_IBNR_IMPORT_MAIN.UPDATOR_ID
     * Database remarks: updator_Id|最后修改人
     */
    @ApiModelProperty(value = "updator_Id|最后修改人", required = false)
    private Long updatorId;

    /**
     * Database column: ATR_BUSS_IBNR_IMPORT_MAIN.UPDATE_TIME
     * Database remarks: update_Time|最后修改时间
     */
    @ApiModelProperty(value = "update_Time|最后修改时间", required = false)
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public Long getIbnrMainId() {
        return ibnrMainId;
    }

    public void setIbnrMainId(Long ibnrMainId) {
        this.ibnrMainId = ibnrMainId;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getIbnrRange() {
        return ibnrRange;
    }

    public void setIbnrRange(String ibnrRange) {
        this.ibnrRange = ibnrRange;
    }

    public String getUseImportOsIs() {
        return useImportOsIs;
    }

    public void setUseImportOsIs(String useImportOsIs) {
        this.useImportOsIs = useImportOsIs;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

    public String getConfirmIs() {
        return confirmIs;
    }

    public void setConfirmIs(String confirmIs) {
        this.confirmIs = confirmIs;
    }

    public Long getConfirmId() {
        return confirmId;
    }

    public void setConfirmId(Long confirmId) {
        this.confirmId = confirmId;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public BigDecimal getProvisionRatio() {
        return provisionRatio;
    }

    public void setProvisionRatio(BigDecimal provisionRatio) {
        this.provisionRatio = provisionRatio;
    }
}