<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-03-02 18:07:59 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussTILicIcgCalcDao">
  <!-- 本配置文件由SS MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**IDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->
    <resultMap id="AccidentResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussTILicIcgCalcVo">
        <id column="ID" property="id" jdbcType="DECIMAL" />
        <result column="ACTION_NO" property="actionNo" jdbcType="VARCHAR" />
        <result column="TASK_CODE" property="taskCode" jdbcType="VARCHAR" />
        <result column="CALC_TYPE" property="calcType" jdbcType="VARCHAR" />
        <result column="entity_id" property="entityId" jdbcType="DECIMAL" />
        <result column="CURRENCY_CODE" property="currencyCode" jdbcType="VARCHAR" />
        <result column="YEAR_MONTH" property="yearMonth" jdbcType="VARCHAR" />
        <result column="PORTFOLIO_NO" property="portfolioNo" jdbcType="VARCHAR" />
        <result column="EVALUATE_APPROACH" property="evaluateApproach" jdbcType="VARCHAR" />
        <result column="LOA_CODE" property="loaCode" jdbcType="VARCHAR" />
        <result column="ULAE_RATE" property="ulaeRate" jdbcType="DECIMAL" />
        <result column="MIN_ACCIDENT_YEAR_MONTH" property="minAccidentYearMonth" jdbcType="VARCHAR" />
    </resultMap>
    <!-- 通用查询结果列-->
    <sql id="Accident_Ym_Column_List">
        a.ID, a.ACTION_NO, a.TASK_CODE, a.CALC_TYPE, a.entity_id, a.currency_code, a.YEAR_MONTH, a.PORTFOLIO_NO, a.EVALUATE_APPROACH,
     a.LOA_CODE, a.ULAE_RATE, a.MIN_ACCIDENT_YEAR_MONTH,
    b.OS, b.IBNR, b.CLAIM_PAID as claimPaid, b.claim_recv as claimRecv, b.ACCIDENT_YEAR_MONTH as accidentYearMonth
    </sql>

    <select id="findId" flushCache="false" useCache="true" resultMap="AccidentResultMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
        select
            ID
        from ATR_BUSS_TI_LIC_ICG_CALC
        where entity_id = #{entityId,jdbcType=DECIMAL}
          and CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
          and ACTION_NO = #{actionNo,jdbcType=VARCHAR}
          and PORTFOLIO_NO = #{portfolioNo,jdbcType=VARCHAR}
          and ICG_NO = #{icgNo,jdbcType=VARCHAR}
          and YEAR_MONTH = #{yearMonth,jdbcType=VARCHAR}
          limit 1
    </select>

    <select id="findAccidentClaimPaid" flushCache="false" useCache="true" resultMap="AccidentResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
        select
        <include refid="Accident_Ym_Column_List" />
        from ATR_BUSS_TI_LIC_ICG_CALC a
        left join ATR_BUSS_TI_LIC_ICG_CALC_ACCIDENT_YM b
        on a.id = b.main_id
        where a.entity_id = #{entityId,jdbcType=DECIMAL}
        and a.currency_code = #{currencyCode,jdbcType=VARCHAR}
        and a.ACTION_NO = #{actionNo,jdbcType=VARCHAR}
        <if test="portfolioNo != null and portfolioNo != ''">
            and a.PORTFOLIO_NO = #{portfolioNo,jdbcType=VARCHAR}
        </if>
        <if test="icgNo != null and icgNo != ''">
            and a.ICG_NO = #{icgNo,jdbcType=VARCHAR}
        </if>
        order by a.YEAR_MONTH, a.PORTFOLIO_NO,a.ICG_NO asc,b.accident_year_month  desc
    </select>


    <resultMap id="CustResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussTILicIcgCalcVo">
        <id column="ID" property="id" jdbcType="DECIMAL" />
        <result column="TASK_CODE" property="taskCode" jdbcType="VARCHAR" />
        <result column="CALC_TYPE" property="calcType" jdbcType="VARCHAR" />
        <result column="entity_id" property="entityId" jdbcType="DECIMAL" />
        <result column="CURRENCY_CODE" property="currencyCode" jdbcType="VARCHAR" />
        <result column="YEAR_MONTH" property="yearMonth" jdbcType="VARCHAR" />
        <result column="PORTFOLIO_NO" property="portfolioNo" jdbcType="VARCHAR" />
        <result column="ICG_NO" property="icgNo" jdbcType="VARCHAR" />
        <result column="EVALUATE_APPROACH" property="evaluateApproach" jdbcType="VARCHAR" />
        <result column="LOA_CODE" property="loaCode" jdbcType="VARCHAR" />
        <result column="CLAIM_PAID_CUR" property="claimPaidCur" jdbcType="DECIMAL" />
        <result column="CLAIM_PAID_PRE" property="claimPaidPre" jdbcType="DECIMAL" />
    </resultMap>
    <!-- 通用查询结果列-->
    <sql id="Cust_Column_List">
        ID, TASK_CODE, CALC_TYPE, entity_id, CURRENCY_CODE, YEAR_MONTH, PORTFOLIO_NO, ICG_NO,
    EVALUATE_APPROACH, LOA_CODE,
    CLAIM_PAID_CUR, CLAIM_PAID_PRE
    </sql>

    <select id="findClaimPaid" flushCache="false" useCache="true" resultMap="CustResultMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
        select
            icg_no,
            sum( CLAIM_PAID_CUR ) AS CLAIM_PAID_CUR,
            sum( CLAIM_PAID_PRE ) AS CLAIM_PAID_PRE
        from ATR_BUSS_TI_LIC_ICG_CALC
        where CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
          and ACTION_NO = #{actionNo,jdbcType=VARCHAR}
          and PORTFOLIO_NO = #{portfolioNo,jdbcType=VARCHAR}
          and ICG_NO = #{icgNo,jdbcType=VARCHAR}
        GROUP BY icg_no
        ORDER BY icg_no
    </select>
</mapper>