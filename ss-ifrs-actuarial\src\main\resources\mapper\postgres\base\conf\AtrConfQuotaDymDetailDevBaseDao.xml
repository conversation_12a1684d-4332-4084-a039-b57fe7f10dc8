<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-02-15 19:31:48 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.conf.AtrConfQuotaDymDetailDevDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDymDetailDev">
    <id column="DEV_ID" property="devId" jdbcType="DECIMAL" />
    <result column="QUOTA_DETAIL_ID" property="quotaDetailId" jdbcType="DECIMAL" />
    <result column="DEV_PERIOD" property="devPeriod" jdbcType="DECIMAL" />
    <result column="QUOTA_VALUE" property="quotaValue" jdbcType="VARCHAR" />
    <result column="CREATOR_ID" property="creatorId" jdbcType="DECIMAL" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATOR_ID" property="updatorId" jdbcType="DECIMAL" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="SERIAL_NO" property="serialNo" jdbcType="DECIMAL" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    DEV_ID, QUOTA_DETAIL_ID, DEV_PERIOD, QUOTA_VALUE, CREATOR_ID, CREATE_TIME, UPDATOR_ID, 
    UPDATE_TIME, SERIAL_NO
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="devId != null ">
          and DEV_ID = #{devId,jdbcType=DECIMAL}
      </if>
      <if test="quotaDetailId != null ">
          and QUOTA_DETAIL_ID = #{quotaDetailId,jdbcType=DECIMAL}
      </if>
      <if test="devPeriod != null ">
          and DEV_PERIOD = #{devPeriod,jdbcType=DECIMAL}
      </if>
      <if test="quotaValue != null and quotaValue != ''">
          and QUOTA_VALUE = #{quotaValue,jdbcType=VARCHAR}
      </if>
      <if test="creatorId != null ">
          and CREATOR_ID = #{creatorId,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null ">
          and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and UPDATOR_ID = #{updatorId,jdbcType=DECIMAL}
      </if>
      <if test="updateTime != null ">
          and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="serialNo != null ">
          and SERIAL_NO = #{serialNo,jdbcType=DECIMAL}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.devId != null ">
          and DEV_ID = #{condition.devId,jdbcType=DECIMAL}
      </if>
      <if test="condition.quotaDetailId != null ">
          and QUOTA_DETAIL_ID = #{condition.quotaDetailId,jdbcType=DECIMAL}
      </if>
      <if test="condition.devPeriod != null ">
          and DEV_PERIOD = #{condition.devPeriod,jdbcType=DECIMAL}
      </if>
      <if test="condition.quotaValue != null and condition.quotaValue != ''">
          and QUOTA_VALUE = #{condition.quotaValue,jdbcType=VARCHAR}
      </if>
      <if test="condition.creatorId != null ">
          and CREATOR_ID = #{condition.creatorId,jdbcType=DECIMAL}
      </if>
      <if test="condition.createTime != null ">
          and CREATE_TIME = #{condition.createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.updatorId != null ">
          and UPDATOR_ID = #{condition.updatorId,jdbcType=DECIMAL}
      </if>
      <if test="condition.updateTime != null ">
          and UPDATE_TIME = #{condition.updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.serialNo != null ">
          and SERIAL_NO = #{condition.serialNo,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="devId != null ">
          and DEV_ID = #{devId,jdbcType=DECIMAL}
      </if>
      <if test="quotaDetailId != null ">
          and QUOTA_DETAIL_ID = #{quotaDetailId,jdbcType=DECIMAL}
      </if>
      <if test="devPeriod != null ">
          and DEV_PERIOD = #{devPeriod,jdbcType=DECIMAL}
      </if>
      <if test="quotaValue != null and quotaValue != ''">
          and QUOTA_VALUE = #{quotaValue,jdbcType=VARCHAR}
      </if>
      <if test="creatorId != null ">
          and CREATOR_ID = #{creatorId,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null ">
          and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and UPDATOR_ID = #{updatorId,jdbcType=DECIMAL}
      </if>
      <if test="updateTime != null ">
          and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="serialNo != null ">
          and SERIAL_NO = #{serialNo,jdbcType=DECIMAL}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from ATR_CONF_QUOTA_DYM_DETAIL_DEV
    where DEV_ID = #{devId,jdbcType=DECIMAL}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from ATR_CONF_QUOTA_DYM_DETAIL_DEV
    where DEV_ID in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ATR_CONF_QUOTA_DYM_DETAIL_DEV
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDymDetailDev">
    select 
    <include refid="Base_Column_List" />
    from ATR_CONF_QUOTA_DYM_DETAIL_DEV
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from ATR_CONF_QUOTA_DYM_DETAIL_DEV
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="DEV_ID" keyProperty="devId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDymDetailDev">
    <selectKey resultType="long" keyProperty="devId" order="BEFORE">
      select nextval('ATR_SEQ_CONF_QUOTA_DYM_DETAIL_DEV') as sequenceNo 
    </selectKey>
    insert into ATR_CONF_QUOTA_DYM_DETAIL_DEV
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="devId != null">
        DEV_ID,
      </if>
      <if test="quotaDetailId != null">
        QUOTA_DETAIL_ID,
      </if>
      <if test="devPeriod != null">
        DEV_PERIOD,
      </if>
      <if test="quotaValue != null">
        QUOTA_VALUE,
      </if>
      <if test="creatorId != null">
        CREATOR_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updatorId != null">
        UPDATOR_ID,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="serialNo != null">
        SERIAL_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="devId != null">
        #{devId,jdbcType=DECIMAL},
      </if>
      <if test="quotaDetailId != null">
        #{quotaDetailId,jdbcType=DECIMAL},
      </if>
      <if test="devPeriod != null">
        #{devPeriod,jdbcType=DECIMAL},
      </if>
      <if test="quotaValue != null">
        #{quotaValue,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        #{updatorId,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all 
    <foreach collection="list" item="item" index="index">
      into ATR_CONF_QUOTA_DYM_DETAIL_DEV values 
       (#{item.devId,jdbcType=DECIMAL}, 
        #{item.quotaDetailId,jdbcType=DECIMAL}, #{item.devPeriod,jdbcType=DECIMAL}, #{item.quotaValue,jdbcType=VARCHAR}, 
        #{item.creatorId,jdbcType=DECIMAL}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updatorId,jdbcType=DECIMAL}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.serialNo,jdbcType=DECIMAL})
    </foreach>
    select 1 
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDymDetailDev">
    update ATR_CONF_QUOTA_DYM_DETAIL_DEV
    <set>
      <if test="quotaDetailId != null">
        QUOTA_DETAIL_ID = #{quotaDetailId,jdbcType=DECIMAL},
      </if>
      <if test="devPeriod != null">
        DEV_PERIOD = #{devPeriod,jdbcType=DECIMAL},
      </if>
      <if test="quotaValue != null">
        QUOTA_VALUE = #{quotaValue,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        CREATOR_ID = #{creatorId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        UPDATOR_ID = #{updatorId,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="serialNo != null">
        SERIAL_NO = #{serialNo,jdbcType=DECIMAL},
      </if>
    </set>
    where DEV_ID = #{devId,jdbcType=DECIMAL}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDymDetailDev">
    update ATR_CONF_QUOTA_DYM_DETAIL_DEV
    <set>
      <if test="record.quotaDetailId != null">
        QUOTA_DETAIL_ID = #{record.quotaDetailId,jdbcType=DECIMAL},
      </if>
      <if test="record.devPeriod != null">
        DEV_PERIOD = #{record.devPeriod,jdbcType=DECIMAL},
      </if>
      <if test="record.quotaValue != null">
        QUOTA_VALUE = #{record.quotaValue,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorId != null">
        CREATOR_ID = #{record.creatorId,jdbcType=DECIMAL},
      </if>
      <if test="record.createTime != null">
        CREATE_TIME = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatorId != null">
        UPDATOR_ID = #{record.updatorId,jdbcType=DECIMAL},
      </if>
      <if test="record.updateTime != null">
        UPDATE_TIME = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.serialNo != null">
        SERIAL_NO = #{record.serialNo,jdbcType=DECIMAL},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from ATR_CONF_QUOTA_DYM_DETAIL_DEV
    where DEV_ID = #{devId,jdbcType=DECIMAL}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from ATR_CONF_QUOTA_DYM_DETAIL_DEV
    where DEV_ID in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from ATR_CONF_QUOTA_DYM_DETAIL_DEV
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDymDetailDev">
    select count(1) from ATR_CONF_QUOTA_DYM_DETAIL_DEV
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>