<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2022-06-23 17:24:59 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.reserve.AtrBussReserveUprDetailDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveUprDetail">
    <id column="upr_detail_id" property="uprDetailId" jdbcType="NUMERIC" />
    <result column="reserve_upr_id" property="reserveUprId" jdbcType="NUMERIC" />
    <result column="risk_class_code" property="riskClassCode" jdbcType="VARCHAR" />
    <result column="risk_code" property="riskCode" jdbcType="VARCHAR" />
    <result column="icg_no" property="icgNo" jdbcType="VARCHAR" />
    <result column="cmunit_no" property="cmunitNo" jdbcType="VARCHAR" />
    <result column="policy_no" property="policyNo" jdbcType="VARCHAR" />
    <result column="endorse_seq_no" property="endorseSeqNo" jdbcType="VARCHAR" />
    <result column="endorse_no" property="endorseNo" jdbcType="VARCHAR" />
    <result column="business_source_code" property="businessSourceCode" jdbcType="VARCHAR" />
    <result column="reins_type" property="reinsType" jdbcType="VARCHAR" />
    <result column="start_date" property="startDate" jdbcType="TIMESTAMP" />
    <result column="end_date" property="endDate" jdbcType="TIMESTAMP" />
    <result column="CURRENCY_CODE" property="currencyCode" jdbcType="VARCHAR" />
    <result column="premium" property="premium" jdbcType="NUMERIC" />
    <result column="draw_premium" property="drawPremium" jdbcType="NUMERIC" />
    <result column="draw_premium_adjust" property="drawPremiumAdjust" jdbcType="NUMERIC" />
    <result column="icg_no" property="icgNo" jdbcType="VARCHAR" />
    <result column="check_date" property="checkDate" jdbcType="TIMESTAMP" />
    <result column="atr_method" property="atrMethod" jdbcType="VARCHAR" />
    <result column="loa_code" property="loaCode" jdbcType="VARCHAR"/>
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    upr_detail_id, reserve_upr_id, risk_class_code, risk_code, cmunit_no, policy_no, endorse_seq_no,
    endorse_no, business_source_code, reins_type, start_date, end_date, CURRENCY_CODE, premium, draw_premium,
    draw_premium_adjust, icg_no, check_date, atr_method, loa_code
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="uprDetailId != null ">
          and upr_detail_id = #{uprDetailId,jdbcType=NUMERIC}
      </if>
      <if test="reserveUprId != null ">
          and reserve_upr_id = #{reserveUprId,jdbcType=NUMERIC}
      </if>
      <if test="riskClassCode != null and riskClassCode != ''">
          and risk_class_code = #{riskClassCode,jdbcType=VARCHAR}
      </if>
      <if test="riskCode != null and riskCode != ''">
          and risk_code = #{riskCode,jdbcType=VARCHAR}
      </if>
      <if test="icgNo != null and icgNo != ''">
        and icg_no = #{icgNo,jdbcType=VARCHAR}
      </if>
      <if test="cmunitNo != null and cmunitNo != ''">
          and cmunit_no = #{cmunitNo,jdbcType=VARCHAR}
      </if>
      <if test="policyNo != null and policyNo != ''">
          and policy_no = #{policyNo,jdbcType=VARCHAR}
      </if>
      <if test="endorseSeqNo != null and endorseSeqNo != ''">
          and endorse_seq_no = #{endorseSeqNo,jdbcType=VARCHAR}
      </if>
      <if test="endorseNo != null and endorseNo != ''">
          and endorse_no = #{endorseNo,jdbcType=VARCHAR}
      </if>
      <if test="businessSourceCode != null and businessSourceCode != ''">
          and business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="reinsType != null and reinsType != ''">
          and reins_type = #{reinsType,jdbcType=VARCHAR}
      </if>
      <if test="startDate != null ">
          and start_date = #{startDate,jdbcType=TIMESTAMP}
      </if>
      <if test="endDate != null ">
          and end_date = #{endDate,jdbcType=TIMESTAMP}
      </if>
      <if test="currencyCode != null and currencyCode != ''">
          and CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="premium != null ">
          and premium = #{premium,jdbcType=NUMERIC}
      </if>
      <if test="drawPremium != null ">
          and draw_premium = #{drawPremium,jdbcType=NUMERIC}
      </if>
      <if test="drawPremiumAdjust != null ">
          and draw_premium_adjust = #{drawPremiumAdjust,jdbcType=NUMERIC}
      </if>
      <if test="icgNo != null and icgNo != ''">
          and icg_no = #{icgNo,jdbcType=VARCHAR}
      </if>
      <if test="checkDate != null ">
          and check_date = #{checkDate,jdbcType=TIMESTAMP}
      </if>
      <if test="atrMethod != null and atrMethod != ''">
          and atr_method = #{atrMethod,jdbcType=VARCHAR}
      </if>
      <if test="loaCode != null and loaCode != ''">
        and loa_code = #{loaCode,jdbcType=VARCHAR}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.uprDetailId != null ">
          and upr_detail_id = #{condition.uprDetailId,jdbcType=NUMERIC}
      </if>
      <if test="condition.reserveUprId != null ">
          and reserve_upr_id = #{condition.reserveUprId,jdbcType=NUMERIC}
      </if>
      <if test="condition.riskClassCode != null and condition.riskClassCode != ''">
          and risk_class_code = #{condition.riskClassCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.riskCode != null and condition.riskCode != ''">
          and risk_code = #{condition.riskCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.cmunitNo != null and condition.cmunitNo != ''">
          and cmunit_no = #{condition.cmunitNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.policyNo != null and condition.policyNo != ''">
          and policy_no = #{condition.policyNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.endorseSeqNo != null and condition.endorseSeqNo != ''">
          and endorse_seq_no = #{condition.endorseSeqNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.endorseNo != null and condition.endorseNo != ''">
          and endorse_no = #{condition.endorseNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.businessSourceCode != null and condition.businessSourceCode != ''">
          and business_source_code = #{condition.businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.reinsType != null and condition.reinsType != ''">
          and reins_type = #{condition.reinsType,jdbcType=VARCHAR}
      </if>
      <if test="condition.startDate != null ">
          and start_date = #{condition.startDate,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.endDate != null ">
          and end_date = #{condition.endDate,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.currencyCode != null and condition.currencyCode != ''">
          and CURRENCY_CODE = #{condition.currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.premium != null ">
          and premium = #{condition.premium,jdbcType=NUMERIC}
      </if>
      <if test="condition.drawPremium != null ">
          and draw_premium = #{condition.drawPremium,jdbcType=NUMERIC}
      </if>
      <if test="condition.drawPremiumAdjust != null ">
          and draw_premium_adjust = #{condition.drawPremiumAdjust,jdbcType=NUMERIC}
      </if>
      <if test="condition.icgNo != null and condition.icgNo != ''">
          and icg_no = #{condition.icgNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.checkDate != null ">
          and check_date = #{condition.checkDate,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.atrMethod != null and condition.atrMethod != ''">
          and atr_method = #{condition.atrMethod,jdbcType=VARCHAR}
      </if>
      <if test="condition.loaCode != null and condition.loaCode != ''">
        and loa_code = #{condition.loaCode,jdbcType=VARCHAR}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="uprDetailId != null ">
          and upr_detail_id = #{uprDetailId,jdbcType=NUMERIC}
      </if>
      <if test="reserveUprId != null ">
          and reserve_upr_id = #{reserveUprId,jdbcType=NUMERIC}
      </if>
      <if test="riskClassCode != null and riskClassCode != ''">
          and risk_class_code = #{riskClassCode,jdbcType=VARCHAR}
      </if>
      <if test="riskCode != null and riskCode != ''">
          and risk_code = #{riskCode,jdbcType=VARCHAR}
      </if>
      <if test="cmunitNo != null and cmunitNo != ''">
          and cmunit_no = #{cmunitNo,jdbcType=VARCHAR}
      </if>
      <if test="policyNo != null and policyNo != ''">
          and policy_no = #{policyNo,jdbcType=VARCHAR}
      </if>
      <if test="endorseSeqNo != null and endorseSeqNo != ''">
          and endorse_seq_no = #{endorseSeqNo,jdbcType=VARCHAR}
      </if>
      <if test="endorseNo != null and endorseNo != ''">
          and endorse_no = #{endorseNo,jdbcType=VARCHAR}
      </if>
      <if test="businessSourceCode != null and businessSourceCode != ''">
          and business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="reinsType != null and reinsType != ''">
          and reins_type = #{reinsType,jdbcType=VARCHAR}
      </if>
      <if test="startDate != null ">
          and start_date = #{startDate,jdbcType=TIMESTAMP}
      </if>
      <if test="endDate != null ">
          and end_date = #{endDate,jdbcType=TIMESTAMP}
      </if>
      <if test="currencyCode != null and currencyCode != ''">
          and CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="premium != null ">
          and premium = #{premium,jdbcType=NUMERIC}
      </if>
      <if test="drawPremium != null ">
          and draw_premium = #{drawPremium,jdbcType=NUMERIC}
      </if>
      <if test="drawPremiumAdjust != null ">
          and draw_premium_adjust = #{drawPremiumAdjust,jdbcType=NUMERIC}
      </if>
      <if test="icgNo != null and icgNo != ''">
          and icg_no = #{icgNo,jdbcType=VARCHAR}
      </if>
      <if test="checkDate != null ">
          and check_date = #{checkDate,jdbcType=TIMESTAMP}
      </if>
      <if test="atrMethod != null and atrMethod != ''">
          and atr_method = #{atrMethod,jdbcType=VARCHAR}
      </if>
      <if test="loaCode != null and loaCode != ''">
        and loa_code = #{loaCode,jdbcType=VARCHAR}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from atr_buss_reserve_upr_detail
    where upr_detail_id = #{uprDetailId,jdbcType=NUMERIC}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from atr_buss_reserve_upr_detail
    where upr_detail_id in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from atr_buss_reserve_upr_detail
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveUprDetail">
    select 
    <include refid="Base_Column_List" />
    from atr_buss_reserve_upr_detail
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from atr_buss_reserve_upr_detail
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="upr_detail_id" keyProperty="uprDetailId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveUprDetail">
    insert into atr_buss_reserve_upr_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uprDetailId != null">
        upr_detail_id,
      </if>
      <if test="reserveUprId != null">
        reserve_upr_id,
      </if>
      <if test="riskClassCode != null">
        risk_class_code,
      </if>
      <if test="riskCode != null">
        risk_code,
      </if>
      <if test="cmunitNo != null">
        cmunit_no,
      </if>
      <if test="policyNo != null">
        policy_no,
      </if>
      <if test="endorseSeqNo != null">
        endorse_seq_no,
      </if>
      <if test="endorseNo != null">
        endorse_no,
      </if>
      <if test="businessSourceCode != null">
        business_source_code,
      </if>
      <if test="reinsType != null">
        reins_type,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="currencyCode != null">
        CURRENCY_CODE,
      </if>
      <if test="premium != null">
        premium,
      </if>
      <if test="drawPremium != null">
        draw_premium,
      </if>
      <if test="drawPremiumAdjust != null">
        draw_premium_adjust,
      </if>
      <if test="icgNo != null">
        icg_no,
      </if>
      <if test="checkDate != null">
        check_date,
      </if>
      <if test="atrMethod != null">
        atr_method,
      </if>
      <if test="loaCode != null">
        loa_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uprDetailId != null">
        #{uprDetailId,jdbcType=NUMERIC},
      </if>
      <if test="reserveUprId != null">
        #{reserveUprId,jdbcType=NUMERIC},
      </if>
      <if test="riskClassCode != null">
        #{riskClassCode,jdbcType=VARCHAR},
      </if>
      <if test="riskCode != null">
        #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="cmunitNo != null">
        #{cmunitNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="endorseSeqNo != null">
        #{endorseSeqNo,jdbcType=VARCHAR},
      </if>
      <if test="endorseNo != null">
        #{endorseNo,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceCode != null">
        #{businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="reinsType != null">
        #{reinsType,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="currencyCode != null">
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="premium != null">
        #{premium,jdbcType=NUMERIC},
      </if>
      <if test="drawPremium != null">
        #{drawPremium,jdbcType=NUMERIC},
      </if>
      <if test="drawPremiumAdjust != null">
        #{drawPremiumAdjust,jdbcType=NUMERIC},
      </if>
      <if test="icgNo != null">
        #{icgNo,jdbcType=VARCHAR},
      </if>
      <if test="checkDate != null">
        #{checkDate,jdbcType=TIMESTAMP},
      </if>
      <if test="atrMethod != null">
        #{atrMethod,jdbcType=VARCHAR},
      </if>
      <if test="loaCode != null">
        #{loaCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert into atr_buss_reserve_upr_detail
     (upr_detail_id, reserve_upr_id, risk_class_code,
      risk_code, cmunit_no, policy_no, 
      endorse_seq_no, endorse_no, business_source_code,
      reins_type, start_date, end_date, 
      currency_code, premium, draw_premium,
      draw_premium_adjust, icg_no, check_date, 
      atr_method, loa_code)
     values 
    <foreach collection="list" item="item" index="index" separator=",">
       (#{item.uprDetailId,jdbcType=NUMERIC}, #{item.reserveUprId,jdbcType=NUMERIC}, #{item.riskClassCode,jdbcType=VARCHAR},
        #{item.riskCode,jdbcType=VARCHAR}, #{item.cmunitNo,jdbcType=VARCHAR}, #{item.policyNo,jdbcType=VARCHAR}, 
        #{item.endorseSeqNo,jdbcType=VARCHAR}, #{item.endorseNo,jdbcType=VARCHAR}, #{item.businessSourceCode,jdbcType=VARCHAR},
        #{item.reinsType,jdbcType=VARCHAR}, #{item.startDate,jdbcType=TIMESTAMP}, #{item.endDate,jdbcType=TIMESTAMP}, 
        #{item.currencyCode,jdbcType=VARCHAR}, #{item.premium,jdbcType=NUMERIC}, #{item.drawPremium,jdbcType=NUMERIC},
        #{item.drawPremiumAdjust,jdbcType=NUMERIC}, #{item.icgNo,jdbcType=VARCHAR}, #{item.checkDate,jdbcType=TIMESTAMP}, 
        #{item.atrMethod,jdbcType=VARCHAR},#{item.loa_code,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveUprDetail">
    update atr_buss_reserve_upr_detail
    <set>
      <if test="reserveUprId != null">
        reserve_upr_id = #{reserveUprId,jdbcType=NUMERIC},
      </if>
      <if test="riskClassCode != null">
        risk_class_code = #{riskClassCode,jdbcType=VARCHAR},
      </if>
      <if test="riskCode != null">
        risk_code = #{riskCode,jdbcType=VARCHAR},
      </if>
      <if test="cmunitNo != null">
        cmunit_no = #{cmunitNo,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        policy_no = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="endorseSeqNo != null">
        endorse_seq_no = #{endorseSeqNo,jdbcType=VARCHAR},
      </if>
      <if test="endorseNo != null">
        endorse_no = #{endorseNo,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceCode != null">
        business_source_code = #{businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="reinsType != null">
        reins_type = #{reinsType,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="currencyCode != null">
        CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="premium != null">
        premium = #{premium,jdbcType=NUMERIC},
      </if>
      <if test="drawPremium != null">
        draw_premium = #{drawPremium,jdbcType=NUMERIC},
      </if>
      <if test="drawPremiumAdjust != null">
        draw_premium_adjust = #{drawPremiumAdjust,jdbcType=NUMERIC},
      </if>
      <if test="icgNo != null">
        icg_no = #{icgNo,jdbcType=VARCHAR},
      </if>
      <if test="checkDate != null">
        check_date = #{checkDate,jdbcType=TIMESTAMP},
      </if>
      <if test="atrMethod != null">
        atr_method = #{atrMethod,jdbcType=VARCHAR},
      </if>
      <if test="loaCode != null">
        loa_code = #{loaCode,jdbcType=VARCHAR},
      </if>
    </set>
    where upr_detail_id = #{uprDetailId,jdbcType=NUMERIC}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveUprDetail">
    update atr_buss_reserve_upr_detail
    <set>
      <if test="record.reserveUprId != null">
        reserve_upr_id = #{record.reserveUprId,jdbcType=NUMERIC},
      </if>
      <if test="record.riskClassCode != null">
        risk_class_code = #{record.riskClassCode,jdbcType=VARCHAR},
      </if>
      <if test="record.riskCode != null">
        risk_code = #{record.riskCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cmunitNo != null">
        cmunit_no = #{record.cmunitNo,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        policy_no = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.endorseSeqNo != null">
        endorse_seq_no = #{record.endorseSeqNo,jdbcType=VARCHAR},
      </if>
      <if test="record.endorseNo != null">
        endorse_no = #{record.endorseNo,jdbcType=VARCHAR},
      </if>
      <if test="record.businessSourceCode != null">
        business_source_code = #{record.businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.reinsType != null">
        reins_type = #{record.reinsType,jdbcType=VARCHAR},
      </if>
      <if test="record.startDate != null">
        start_date = #{record.startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endDate != null">
        end_date = #{record.endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.currencyCode != null">
        CURRENCY_CODE = #{record.currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.premium != null">
        premium = #{record.premium,jdbcType=NUMERIC},
      </if>
      <if test="record.drawPremium != null">
        draw_premium = #{record.drawPremium,jdbcType=NUMERIC},
      </if>
      <if test="record.drawPremiumAdjust != null">
        draw_premium_adjust = #{record.drawPremiumAdjust,jdbcType=NUMERIC},
      </if>
      <if test="record.icgNo != null">
        icg_no = #{record.icgNo,jdbcType=VARCHAR},
      </if>
      <if test="record.checkDate != null">
        check_date = #{record.checkDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.atrMethod != null">
        atr_method = #{record.atrMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.loaCode != null">
        loa_code = #{record.loaCode,jdbcType=VARCHAR},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from atr_buss_reserve_upr_detail
    where upr_detail_id = #{uprDetailId,jdbcType=NUMERIC}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from atr_buss_reserve_upr_detail
    where upr_detail_id in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=NUMERIC}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from atr_buss_reserve_upr_detail
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.reserve.po.AtrBussReserveUprDetail">
    select count(1) from atr_buss_reserve_upr_detail
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>