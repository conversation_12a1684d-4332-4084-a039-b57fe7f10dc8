package com.ss.ifrs.actuarial.dao;

import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.AtrDapTreaty;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.AtrBussToLrcIcgPremium;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.x.AtrDuctLrcToxPaid;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.x.AtrDuctLrcToxPolicy;
import com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to.x.AtrDuctToUlxPre;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface AtrBussLrcToxDao {


    List<AtrDapTreaty> findTreayInfo(Map<?, ?> paramMap);

    List<AtrDuctLrcToxPolicy> findDuctUlPolicy(@Param("riskClassCode") String riskClassCode,
                                               @Param("issueYear") String issueYear);

    List<AtrDuctLrcToxPaid> findPaid(Map<?, ?> paramMap);

    List<AtrBussToLrcIcgPremium> listPreIcgPremium(Map<?, ?> paramMap);

    Long getUlMaxId();

    List<AtrDuctToUlxPre> findUlPre(@Param("lastActionNo") String lastActionNo,
                                    @Param("lastYearMonth") String lastYearMonth,
                                    @Param("treatyNo")  String treatyNo);

    // 分片处理相关方法
    void truncatePartitionTable();

    void insertPartitionPolicyData(Map<?, ?> paramMap);

    BigDecimal calculateTreatySumPremium(Map<?, ?> paramMap);

    List<AtrDuctLrcToxPolicy> getPartPolicyData(Map<?, ?> paramMap);
}
