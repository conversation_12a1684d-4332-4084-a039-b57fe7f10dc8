<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by Taiping MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2025-03-24 19:27:36 -->
<!-- Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.alloc.AtrBussIbnrAllocTiResultDao">
  <!-- 本配置文件由Taiping MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**BaseDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->
  <insert id="saveIbnrAllocTiResult" flushCache="true" parameterType="java.util.List">
    INSERT INTO atr_buss_ibnr_alloc_ti_result (
      ID,
      action_no,
      entity_id,
      year_month,
      portfolio_no,
      icg_no,
      icg_no_name,
      acc_year_month,
      ri_dept,
      treaty_no,
      treaty_name,
      risk_class_code,
      center_code,
      pl_judge_rslt, fin_detail_code, fin_product_code, fin_sub_product_code,
      fin_acc_channel, dept_id, channel_id, company_code4,
      ibnr_amount,
      create_time
    ) SELECT
        nextval('atr_seq_buss_ibnr_alloc_dd_result' ) AS ID,
        biaa.action_no,
        biaa.entity_id,
        biid.year_month,
        bcti.portfolio_no,
        bcti.icg_no,
        '' as icg_no_name,
        bcti.year_month as acc_year_month,
        biid.ri_dept,
        biid.treaty_no,
        biid.treaty_name,
        biid.risk_class_code,
        bem.fcs_mapping_code as center_code,
        bcti.pl_judge_rslt,
        '0' as fin_detail_code,
        '' as fin_product_code,
        '01' fin_sub_product_code,
        '' as fin_acc_channel,
        bem.fcs_mapping_code as dept_id,
        '0100' as channel_id,
        bem.fds_mapping_code as company_code4,
        biid.ibnr_amount,
        LOCALTIMESTAMP as create_time
    FROM atr_buss_ibnr_alloc_action biaa
    LEFT JOIN atr_buss_ibnr_import_detail biid ON biaa.business_source_code = biid.business_model
    left join dmuser.dm_buss_cmunit_treaty_inward bcti on biid.treaty_no=bcti.treaty_no
    left join dmuser.dm_base_entity_mapping bem on bem.entity_code= '01000010'
    WHERE biaa.ID = #{allocAction.id,jdbcType=DECIMAL}
      AND biid.ibnr_main_id = #{ibnrImport.ibnrMainId,jdbcType=DECIMAL}
      and bcti.year_month is not null
  </insert>

    <resultMap id="ResultVoResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.alloc.vo.AtrBussIbnrAllocResultVo">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="action_no" property="actionNo" jdbcType="VARCHAR" />
        <result column="entity_id" property="entityId" jdbcType="BIGINT" />
        <result column="year_month" property="yearMonth" jdbcType="VARCHAR" />
        <result column="portfolio_no" property="portfolioNo" jdbcType="VARCHAR" />
        <result column="icg_no" property="icgNo" jdbcType="VARCHAR" />
        <result column="acc_year_month" property="accYearMonth" jdbcType="VARCHAR" />
        <result column="treaty_no" property="treatyNo" jdbcType="VARCHAR" />
        <result column="risk_class_code" property="riskClassCode" jdbcType="VARCHAR" />
        <result column="ri_dept" property="riDept" jdbcType="VARCHAR" />
        <result column="policy_no" property="policyNo" jdbcType="VARCHAR" />
        <result column="kind_code" property="kindCode" jdbcType="VARCHAR" />
        <result column="ep_amount" property="epAmount" jdbcType="NUMERIC" />
        <result column="ep_ratio" property="epRatio" jdbcType="NUMERIC" />
        <result column="ibnr_amount" property="ibnrAmount" jdbcType="NUMERIC" />
        <result column="creator_id" property="creatorId" jdbcType="BIGINT" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <select id="findAllocList" resultMap="ResultVoResultMap">
        select biaa.action_no, biaa.year_month,biadr.acc_year_month,biadr.portfolio_no,
               biadr.icg_no,biadr.icg_no_name, biadr.treaty_no,biadr.treaty_name,
               biadr.risk_class_code, biadr.ri_dept, biadr.ibnr_amount
        FROM atr_buss_ibnr_alloc_action biaa,atr_buss_ibnr_alloc_ti_result biadr
        where biaa.action_No = biadr.action_No
        <choose>
            <when test="actionNo != null">
                and biaa.action_no = #{actionNo,jdbcType=VARCHAR}
            </when>
            <otherwise>
                <if test="entityId != null ">
                    and biaa.entity_id = #{entityId,jdbcType=BIGINT}
                </if>
                <if test="yearMonth != null and yearMonth != ''">
                    and biaa.year_month = #{yearMonth,jdbcType=VARCHAR}
                </if>
                <if test="yearMonthStart != null and yearMonthStart != ''">
                    and biaa.year_month >= #{yearMonthStart}
                </if>
                <if test="yearMonthEnd != null and yearMonthEnd != ''">
                    and biaa.year_month  <![CDATA[ <= ]]> #{yearMonthEnd}
                </if>
                <if test="businessSourceCode != null and businessSourceCode != ''">
                    and biaa.business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
                </if>
                <if test="status != null and status != ''">
                    and biaa.STATUS = #{status,jdbcType=VARCHAR}
                </if>
                <if test="confirmIs != null and confirmIs != ''">
                    and biaa.CONFIRM_IS = #{confirmIs,jdbcType=VARCHAR}
                </if>
            </otherwise>
        </choose>
    </select>

    <delete id="deleteDapIbnr">
        delete from atr_dap_ti_ibnr
        where entity_id = #{entityId,jdbcType=BIGINT}
          and year_month = #{yearMonth,jdbcType=VARCHAR}
    </delete>

    <insert id="confirm">
        INSERT INTO atr_dap_ti_ibnr (
            year_month,
            entity_id,
            acc_year_month,
            portfolio_no,
            icg_no,
            risk_class_code,
            treaty_no,
            pl_judge_rslt,center_code,
            fin_detail_code, fin_product_code, fin_sub_product_code,
            fin_acc_channel, dept_id, channel_id, company_code4,
            amount,
            draw_time
        )
        SELECT
            biaa.year_month,
            biaa.entity_id,
            biadr.acc_year_month,
            biadr.portfolio_no,
            biadr.icg_no,
            biadr.risk_class_code,
            biadr.treaty_no,
        biadr.pl_judge_rslt,biadr.center_code,
        biadr.fin_detail_code, biadr.fin_product_code, biadr.fin_sub_product_code,
        biadr.fin_acc_channel, biadr.dept_id, biadr.channel_id, biadr.company_code4,
            biadr.ibnr_amount,
            LOCALTIMESTAMP as create_time
        FROM atr_buss_ibnr_alloc_action biaa
                 LEFT JOIN atr_buss_ibnr_alloc_ti_result  biadr ON biadr.action_no =biaa.action_no
        WHERE biaa.confirm_is='1'
          and biadr.action_no is not null
        <choose>
            <when test="actionNo != null">
                and biaa.action_no = #{actionNo,jdbcType=VARCHAR}
            </when>
            <otherwise>
                <if test="entityId != null ">
                    and biaa.entity_id = #{entityId,jdbcType=BIGINT}
                </if>
                <if test="yearMonth != null and yearMonth != ''">
                    and biaa.year_month = #{yearMonth,jdbcType=VARCHAR}
                </if>
                <if test="businessSourceCode != null and businessSourceCode != ''">
                    and biaa.business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
                </if>
            </otherwise>
        </choose>
    </insert>
</mapper>