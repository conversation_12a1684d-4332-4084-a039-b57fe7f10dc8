package com.ss.ifrs.actuarial.service.impl;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.ss.ifrs.actuarial.dao.*;
import com.ss.ifrs.actuarial.pojo.atrbuss.po.*;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussBecfDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussBecfMainVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLrcCfDetailVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLrcCfMainVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.ifrs.actuarial.service.AtrBussDataImportService;
import com.ss.ifrs.actuarial.service.AtrConfModelDefService;
import com.ss.ifrs.actuarial.service.AtrImportService;
import com.ss.ifrs.actuarial.constant.ActuarialConstant;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.ClassUtil;
import com.ss.library.utils.DateUtil;
import com.ss.platform.util.ExcelExportUtil;
import com.ss.library.utils.ExcelImportUtil;
import oracle.sql.TIMESTAMP;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
public class AtrBussDataImportServiceImpl implements AtrBussDataImportService {

    @Autowired
    private AtrBussBecfMainDao atrBussBecfMainDao;

    @Autowired
    private AtrBussBecfDetailDao atrBussBecfDetailDao;

    @Autowired
    private AtrBussLrcCfMainDao atrBussLrcCfMainDao;

    @Autowired
    private AtrBussLrcCfDetailDao atrBussLrcCfDetailDao;

    @Autowired
    private AtrBussLrcCfPeriodDao atrBussLrcCfPeriodDao;

    @Autowired
    private AtrBussLrcCfPeriodDetailDao atrBussLrcCfPeriodDetailDao;

    @Autowired
    private AtrImportService atrImportService;

    @Autowired
    AtrConfModelDefService atrConfModelDefService;

    final static Map<Integer, String> cfFeeTypeMap = new ConcurrentHashMap() {
        {
            put(0, "AcqExp");
            put(1, "UEPProjection");
            put(2, "PremReceipt");
            put(3, "ExpectedBrokerage");
            put(4, "AdjComm");
            put(5, "MaintenanceExpense");
            put(6, "ExpectedClaim");
        }
    };

    @Override
    public Page<AtrBussBecfMainVo> searchLicPage(AtrBussBecfMainVo atrBussBecfMainVo, Pageable pageParam) {
        Page<AtrBussBecfMainVo> atrBussBecfMainVoPage = atrBussBecfMainDao.fuzzySearchPage(atrBussBecfMainVo, pageParam);
        return atrBussBecfMainVoPage;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, readOnly = false)
    public void licExcelImport(MultipartFile file, AtrBussBecfMainVo atrBussBecfMainVo, Long userId) throws Exception {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMM");

        SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy-MM-dd");
        Date valuationDate =  formatter1.parse(atrBussBecfMainVo.getValuationDateStr());
        atrBussBecfMainVo.setCreatorId(userId);
        atrBussBecfMainVo.setCreateTime(new Date());
        atrBussBecfMainVo.setValuationDate(valuationDate);
        atrBussBecfMainVo.setYearMonth(formatter.format(valuationDate));
        atrBussBecfMainVo.setConfirmIs("0");
        atrBussBecfMainVo.setVersionNo(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        AtrBussBecfMain po = ClassUtil.convert(atrBussBecfMainVo, AtrBussBecfMain.class);
        atrBussBecfMainDao.save(po);
        List<AtrBussBecfDetail> list = new ArrayList<>();
        try {
            list= ExcelExportUtil.read(file.getInputStream(), AtrBussBecfDetail.class);
            atrImportService.importFile(file,atrBussBecfMainVo.getTargetRouter(),userId);
        } catch (ExcelAnalysisException e) {
            throw e;
        }
        list.forEach(bussBecfDetail -> {
            bussBecfDetail.setBecfMainId(po.getBecfMainId());
            atrBussBecfDetailDao.save(bussBecfDetail);
        });
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, readOnly = false)
    public void licDataConfirm(AtrBussBecfMainVo atrBussBecfMainVo, Long userId) {
        AtrBussBecfMain po = atrBussBecfMainDao.findById(atrBussBecfMainVo.getBecfMainId());
        if (ObjectUtils.isNotEmpty(po)) {
            Integer count = atrBussBecfMainDao.countConfirm(po);
            if (count > 0 ) {
                return;
            }
            po.setConfirmIs("1");
            po.setConfirmId(userId);
            po.setConfirmTime(new Date());
            po.setUpdatorId(userId);
            po.setUpdateTime(new Date());
            atrBussBecfMainDao.updateById(po);

            AtrConfBussPeriodVo atrConfBussPeriodVo = new AtrConfBussPeriodVo();
            atrConfBussPeriodVo.setEntityId(po.getEntityId());
            atrConfBussPeriodVo.setYearMonth(po.getYearMonth());
            atrConfBussPeriodVo.setProcCode(ActuarialConstant.ProcCode.EXPECTED_CF_LIC);
            atrConfModelDefService.confirmBeCFProc(atrConfBussPeriodVo, userId);
        }
    }

    @Override
    public List<AtrBussBecfMainVo> findLicList(AtrBussBecfMainVo atrBussBecfMainVo) {
        List<AtrBussBecfMain> atrBussBecfMainList = atrBussBecfMainDao.findList(ClassUtil.convert(atrBussBecfMainVo, AtrBussBecfMain.class));
        return ClassUtil.convert(atrBussBecfMainList, AtrBussBecfMainVo.class);
    }

    @Override
    public AtrBussBecfMainVo findLicById(Long becfMainId) {
        AtrBussBecfMainVo atrBussBecfMainVo = atrBussBecfMainDao.findByMainId(becfMainId);
        AtrBussBecfDetail atrBussBecfDetail = new AtrBussBecfDetail();
        atrBussBecfDetail.setBecfMainId(becfMainId);
        List<AtrBussBecfDetail> atrBussBecfDetailList = atrBussBecfDetailDao.findList(atrBussBecfDetail);
        atrBussBecfMainVo.setAtrBussBecfDetailVoList(ClassUtil.convert(atrBussBecfDetailList, AtrBussBecfDetailVo.class));
        return atrBussBecfMainVo;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, readOnly = false)
    public AtrBussBecfMainVo deleteLicByBecfId(Long becfMainId) {
        AtrBussBecfMainVo atrBussBecfMainVo = atrBussBecfMainDao.findByMainId(becfMainId);
        if (ObjectUtils.isEmpty(atrBussBecfMainVo) || "1".equals(atrBussBecfMainVo.getConfirmIs())) {
            return atrBussBecfMainVo;
        }
        HashMap map = new HashMap();
        map.put("becfMainId", becfMainId);
        atrBussBecfDetailDao.deleteByMap(map);
        atrBussBecfMainDao.deleteById(becfMainId);
        return atrBussBecfMainVo;
    }

    @Override
    public Page<AtrBussLrcCfMainVo> searchLrcPage(AtrBussLrcCfMainVo atrBussLrcCfMainVo, Pageable pageParam) {
        Page<AtrBussLrcCfMainVo> atrBussBecfMainVoPage = atrBussLrcCfMainDao.fuzzySearchPage(atrBussLrcCfMainVo, pageParam);
        return atrBussBecfMainVoPage;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void lrcExcelImport(MultipartFile file, AtrBussLrcCfMainVo atrBussLrcCfMainVo, Long userId) throws Exception {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMM");
        SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy-MM-dd");
        Date valuationDate =  formatter1.parse(atrBussLrcCfMainVo.getValuationDateStr());
        atrBussLrcCfMainVo.setCreatorId(userId);
        atrBussLrcCfMainVo.setCreateTime(new Date());
        atrBussLrcCfMainVo.setValuationDate(valuationDate);
        atrBussLrcCfMainVo.setYearMonth(formatter.format(valuationDate));
        atrBussLrcCfMainVo.setConfirmIs("0");
        atrBussLrcCfMainVo.setVersionNo(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        AtrBussLrcCfMain po = ClassUtil.convert(atrBussLrcCfMainVo, AtrBussLrcCfMain.class);
        //LRC数据导入主表存储
        atrBussLrcCfMainDao.save(po);
        try {
            //Excel数据提取到Map对象中，key是sheet页码，value是分页数据
            Map<Integer, List<Map<String, String>>> sheetDataMap = ExcelImportUtil.addExcel(file);
            //Map<String, List<Map<String, String>>> sheetDataMap = ImportExcelUtil.readExcel(file);
            //保存数据
            this.saveLrcCfDetail(po, sheetDataMap);
            atrImportService.importFile(file,atrBussLrcCfMainVo.getTargetRouter(),userId);
        } catch (ExcelAnalysisException e) {
            throw e;
        }
    }

    /**
     * 解析Excel数据并存储
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void saveLrcCfDetail(AtrBussLrcCfMain po, Map<Integer, List<Map<String, String>>> sheetDataMap) {
        List<AtrBussLrcCfDetail> cfDetailList = new ArrayList<>();
        List<AtrBussLrcCfPeriod> acqCfPeriodList;
        //获取费用acq、已赚报表UEP、应收保费Prem_receipt的发展期数据
        List<AtrBussLrcCfPeriodDetail> acqAllCfPeriodDetailList;

        for (Map.Entry<Integer, List<Map<String, String>>> entry : sheetDataMap.entrySet()) {
            switch (entry.getKey()) {
                case 7:
                    cfDetailList = (List) this.transDataListBySheet(AtrBussLrcCfDetail.class, entry.getValue());
                    break;
                default:
                    acqCfPeriodList = new ArrayList<>();
                    acqAllCfPeriodDetailList = new ArrayList<>();
                    acqCfPeriodList= (List) this.transDataListBySheet(AtrBussLrcCfPeriod.class, entry.getValue());
                    acqAllCfPeriodDetailList = this.transPeriodDataListBySheet(entry.getValue());
                    acqCfPeriodList.forEach(CfPeriod ->{
                        CfPeriod.setLrcCfFeeType(cfFeeTypeMap.get(entry.getKey()));
                    });
                    acqAllCfPeriodDetailList.forEach(cfPeriodDetail -> {
                        cfPeriodDetail.setLrcCfFeeType(cfFeeTypeMap.get(entry.getKey()));
                    });
                    this.saveLrcCfPeriodData(po, acqCfPeriodList, acqAllCfPeriodDetailList);
                    break;
            }
        }
       cfDetailList.forEach(bussLrcCfDetail -> {
            bussLrcCfDetail.setLrcCfMainId(po.getLrcCfMainId());
            atrBussLrcCfDetailDao.save(bussLrcCfDetail);
        });

    }


    /**
     * 解析Excel数据并存储
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void saveLrcCfPeriodData(AtrBussLrcCfMain po, List<AtrBussLrcCfPeriod> bussLrcCfPeriodList, List<AtrBussLrcCfPeriodDetail> bussLrcCfPeriodDetailList) {
        List<AtrBussLrcCfPeriodDetail> finalPremAllCfPeriodDetailList = bussLrcCfPeriodDetailList;
        bussLrcCfPeriodList.forEach(bussLrcCfPeriod -> {
            bussLrcCfPeriod.setLrcCfMainId(po.getLrcCfMainId());
            atrBussLrcCfPeriodDao.save(bussLrcCfPeriod);
            List<AtrBussLrcCfPeriodDetail> cfPeriodDetailList1 = finalPremAllCfPeriodDetailList.stream().filter(periodDetail2 -> bussLrcCfPeriod.getPolicyNoEndorsement().equals(periodDetail2.getPolicyNoEndorsement())).sorted(Comparator.comparing(AtrBussLrcCfPeriodDetail::getDevPeriod)).collect(Collectors.toList());
            cfPeriodDetailList1.forEach(bussLrcCfPeriodDetail -> {
                bussLrcCfPeriodDetail.setLrcCfPeriodId(bussLrcCfPeriod.getLrcCfPeriodId());
                atrBussLrcCfPeriodDetailDao.save(bussLrcCfPeriodDetail);
            });
        });
    }


        /**
         * 解析Excel的单个sheet数据
         * @return List
         */
    public List<Object> transDataListBySheet(Class voClazz, List<Map<String, String>> sheet) {
        List<Object> list = new ArrayList<>();
        List<Map<String, String>> objectList = new ArrayList<>();
        Map mapAnnots = this.getAnnotations(voClazz, ExcelProperty.class);
        sheet.stream().forEach(rowMap -> {
            Map<String, String> map = new HashMap<>();
            rowMap.forEach((k, v) -> {
                if (ObjectUtils.isNotEmpty(v)) {
                    map.put((String) mapAnnots.get(k), v);
                }
            });
            Object obj = map2bean(map, voClazz);
            list.add(obj);
        });
        return list;
    }

    /**
     * 解析Excel的发展期数据
     * @return List<AtrBussLrcCfPeriodDetail>
     */
    public List<AtrBussLrcCfPeriodDetail> transPeriodDataListBySheet(List<Map<String, String>> sheet) {
        List<AtrBussLrcCfPeriodDetail> list = new ArrayList<>();
        sheet.stream().forEach(rowMap -> {
             rowMap.forEach((k, v) -> {
                if (StringUtils.isNumeric(k)) {
                    if (ObjectUtils.isNotEmpty(v)){
                        AtrBussLrcCfPeriodDetail detail = new AtrBussLrcCfPeriodDetail();
                        detail.setPolicyNoEndorsement(rowMap.get(ActuarialConstant.Import.LRC_BUSINESS_NO));
                        detail.setDevPeriod(Integer.valueOf(k));
                        detail.setLrcCfAmount(getAccountValueToNember(v));
                        list.add(detail);
                    }
                }
            });
        });
        return list;
    }

    public static <T> T map2bean(Map<String, String> map, Class<T> clz)  {
        try {
            //创建一个需要转换为的类型的对象
            T obj = clz.newInstance();
            //从Map中获取和属性名称一样的值，把值设置给对象(setter方法)
            if (null == map) {
                return obj;
            }
            //得到属性的描述器
            BeanInfo b = Introspector.getBeanInfo(clz, Object.class);
            PropertyDescriptor[] pds = b.getPropertyDescriptors();
            for (PropertyDescriptor pd : pds) {
                //得到属性的setter方法
                Method setter = pd.getWriteMethod();
                //得到key名字和属性名字相同的value设置给属性
                Object o = map.get(pd.getName());
                try {
                    setter.invoke(obj, getValue(setter, map.get(pd.getName())));
                } catch (Exception e) {
                    e.printStackTrace();
                    continue;
                }
            }
            return obj;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public static Object getValue(Method setter, String value) throws ParseException {
        if (ObjectUtils.isEmpty(value)) {
            return value;
        }
        BigDecimal percent = new BigDecimal(100);
        Class<?> mClazz = setter.getParameterTypes()[0];
        if (mClazz == BigDecimal.class) {
            if (value.startsWith("(") && value.endsWith(")")) {
                value = value.replace("(" , "-");
                value = value.replace(")" , "");
            }
            if (value.endsWith("%")) {
                return new BigDecimal(value.replaceAll(",","").replaceAll("%","")).divide(percent);
            } else {
                return new BigDecimal(value.replaceAll(",",""));
            }
            //modify by songry 处理oralce的TIMESTAMP字段类型转换问题
        } else if (TIMESTAMP.class.equals(mClazz)) {
            return DateUtil.strToDate(value);
        } else {
            return value;
        }
    }

    public static BigDecimal getAccountValueToNember(String value) {
        if("- 0".equals(value)) {
            return new BigDecimal(0);
        }
        if (value.startsWith("(") && value.endsWith(")")) {
            value = value.replace("(" , "-");
            value = value.replace(")" , "");
        }
        if (value.endsWith("%")) {
            BigDecimal percent = new BigDecimal(100);
            return new BigDecimal(value.replaceAll(",","").replaceAll("%","")).divide(percent);
        } else {
            return new BigDecimal(value.replaceAll(",",""));
        }


    }

    public static Map getAnnotations(Class clazz, Class<? extends Annotation> annotationType) {
        Map classAnnotations = new HashMap();

        Field[] fields = clazz.getDeclaredFields();
        //循环对象的属性
        for (Field field : fields) {
            String fieldName = field.getName();
            if (field.isAnnotationPresent(annotationType)) {
                Object ss = field.getAnnotation(annotationType);
                ExcelProperty excelProperty = (ExcelProperty) ss;
                classAnnotations.put(excelProperty.value()[0], fieldName);
            }
        }
        return classAnnotations;
    }



    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void lrcDataConfirm(AtrBussLrcCfMainVo atrBussLrcCfMainVo, Long userId) {
        AtrBussLrcCfMain po = atrBussLrcCfMainDao.findById(atrBussLrcCfMainVo.getLrcCfMainId());
        if (ObjectUtils.isNotEmpty(po)) {
            Integer count = atrBussLrcCfMainDao.countConfirm(po);
            if (count > 0 ) {
                return;
            }
            po.setConfirmIs("1");
            po.setConfirmId(userId);
            po.setConfirmTime(new Date());
            po.setUpdatorId(userId);
            po.setUpdateTime(new Date());
            atrBussLrcCfMainDao.updateById(po);

            AtrConfBussPeriodVo atrConfBussPeriodVo = new AtrConfBussPeriodVo();
            atrConfBussPeriodVo.setEntityId(po.getEntityId());
            atrConfBussPeriodVo.setYearMonth(po.getYearMonth());
            atrConfBussPeriodVo.setProcCode(ActuarialConstant.ProcCode.EXPECTED_CF_LRC);
            atrConfModelDefService.confirmBeCFProc(atrConfBussPeriodVo, userId);
        }
    }

    @Override
    public List<AtrBussLrcCfMainVo> findLrcList(AtrBussLrcCfMainVo atrBussLrcCfMainVo) {
        List<AtrBussLrcCfMain> atrBussLrcCfMains = atrBussLrcCfMainDao.findList(ClassUtil.convert(atrBussLrcCfMainVo, AtrBussLrcCfMain.class));
        return ClassUtil.convert(atrBussLrcCfMains, AtrBussLrcCfMainVo.class);
    }

    @Override
    public AtrBussLrcCfMainVo findLrcById(Long lrcCfMainId) {
        AtrBussLrcCfMainVo atrBussLrcCfMainVo = atrBussLrcCfMainDao.findByMainId(lrcCfMainId);
        AtrBussLrcCfDetail atrBussBecfDetail = new AtrBussLrcCfDetail();
        atrBussBecfDetail.setLrcCfMainId(lrcCfMainId);
        List<AtrBussLrcCfDetail> atrBussLrcCfDetailVoList = atrBussLrcCfDetailDao.findList(atrBussBecfDetail);
        atrBussLrcCfMainVo.setAtrBussLrcCfDetailVoList(ClassUtil.convert(atrBussLrcCfDetailVoList, AtrBussLrcCfDetailVo.class));
        return atrBussLrcCfMainVo;
    }

    /*
    * 查询发展期List
    * */
    @Override
    public LinkedHashMap<String, Object> findPeriodHeaderDataById(Long lrcCfMainId) {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        AtrBussLrcCfMainVo atrBussLrcCfMainVo = atrBussLrcCfMainDao.findByMainId(lrcCfMainId);
        if (ObjectUtils.isNotEmpty(atrBussLrcCfMainVo)) {
            AtrBussLrcCfPeriod cfPeriod = new AtrBussLrcCfPeriod();
            cfPeriod.setLrcCfMainId(lrcCfMainId);
            cfPeriod.setLrcCfFeeType(cfFeeTypeMap.get(0));
            List<Integer> acqCfDetailList = atrBussLrcCfPeriodDetailDao.findPeriodById(cfPeriod);
            resultMap.put("acqCf", acqCfDetailList);
            cfPeriod.setLrcCfFeeType(cfFeeTypeMap.get(1));
            List<Integer> uepCfDetailList = atrBussLrcCfPeriodDetailDao.findPeriodById(cfPeriod);
            resultMap.put("uepCf", uepCfDetailList);
            cfPeriod.setLrcCfFeeType(cfFeeTypeMap.get(2));
            List<Integer> premCfDetailList = atrBussLrcCfPeriodDetailDao.findPeriodById(cfPeriod);
            resultMap.put("premCf", premCfDetailList);
            cfPeriod.setLrcCfFeeType(cfFeeTypeMap.get(3));
            List<Integer> brokerageCfDetailList = atrBussLrcCfPeriodDetailDao.findPeriodById(cfPeriod);
            resultMap.put("brokerageCf", brokerageCfDetailList);
            cfPeriod.setLrcCfFeeType(cfFeeTypeMap.get(4));
            List<Integer> adjustmentCfDetailList = atrBussLrcCfPeriodDetailDao.findPeriodById(cfPeriod);
            resultMap.put("adjCommCf", adjustmentCfDetailList);
            cfPeriod.setLrcCfFeeType(cfFeeTypeMap.get(5));
            List<Integer> maintenanceExpenseCfDetailList = atrBussLrcCfPeriodDetailDao.findPeriodById(cfPeriod);
            resultMap.put("maintenanceExpenseCf", maintenanceExpenseCfDetailList);
            cfPeriod.setLrcCfFeeType(cfFeeTypeMap.get(6));
            List<Integer> expectedClaimCfDetailList = atrBussLrcCfPeriodDetailDao.findPeriodById(cfPeriod);
            resultMap.put("expectedClaimCf", expectedClaimCfDetailList);
        }
        return (LinkedHashMap<String, Object>) resultMap;
    }

    @Override
    public LinkedHashMap<String, Object> findPeriodDataById(Long lrcCfMainId) {
        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        AtrBussLrcCfMainVo atrBussLrcCfMainVo = atrBussLrcCfMainDao.findByMainId(lrcCfMainId);
        if (ObjectUtils.isNotEmpty(atrBussLrcCfMainVo)) {
            AtrBussLrcCfPeriod cfPeriod = new AtrBussLrcCfPeriod();
            cfPeriod.setLrcCfMainId(lrcCfMainId);
            cfPeriod.setLrcCfFeeType(cfFeeTypeMap.get(0));
            List<Map<String, Object>> acqCfDetailList = atrBussLrcCfPeriodDao.findCfPeriodList(cfPeriod);
            List<AtrBussLrcCfPeriodDetail> acqCfPeriodDetailList = atrBussLrcCfPeriodDetailDao.findPeriodDetail(cfPeriod);
            acqCfDetailList.forEach(cfPeriodMap -> {
                System.out.println(cfPeriodMap.get("lrcCfPeriodId"));
                List<AtrBussLrcCfPeriodDetail> cfPeriodDetailList = acqCfPeriodDetailList.stream().filter(periodDetail -> cfPeriodMap.get("lrcCfPeriodId").toString().equals(String.valueOf(periodDetail.getLrcCfPeriodId()))).collect(Collectors.toList());
                cfPeriodDetailList.forEach(cfPeriodDetail -> {
                    cfPeriodMap.put(cfPeriodDetail.getDevPeriod().toString(), cfPeriodDetail.getLrcCfAmount());
                });
            });
            resultMap.put("acqCf", acqCfDetailList);


            cfPeriod.setLrcCfFeeType(cfFeeTypeMap.get(1));
            List<Map<String, Object>> uepCfDetailList = atrBussLrcCfPeriodDao.findCfPeriodList(cfPeriod);
            List<AtrBussLrcCfPeriodDetail> uepCfPeriodDetailList = atrBussLrcCfPeriodDetailDao.findPeriodDetail(cfPeriod);
            uepCfDetailList.forEach(uepCfPeriodMap -> {
                List<AtrBussLrcCfPeriodDetail> cfPeriodDetailList = uepCfPeriodDetailList.stream().filter(periodDetail -> uepCfPeriodMap.get("lrcCfPeriodId").toString().equals(periodDetail.getLrcCfPeriodId().toString())).collect(Collectors.toList());
                cfPeriodDetailList.forEach(cfPeriodDetail -> {
                    uepCfPeriodMap.put(cfPeriodDetail.getDevPeriod().toString(), cfPeriodDetail.getLrcCfAmount());
                });
            });
            resultMap.put("uepCf", uepCfDetailList);


            cfPeriod.setLrcCfFeeType(cfFeeTypeMap.get(2));
            List<Map<String, Object>> premCfDetailList = atrBussLrcCfPeriodDao.findCfPeriodList(cfPeriod);
            List<AtrBussLrcCfPeriodDetail> premCfPeriodDetailList = atrBussLrcCfPeriodDetailDao.findPeriodDetail(cfPeriod);
            premCfDetailList.forEach(premCfPeriodMap -> {
                List<AtrBussLrcCfPeriodDetail> cfPeriodDetailList = premCfPeriodDetailList.stream().filter(periodDetail -> premCfPeriodMap.get("lrcCfPeriodId").toString().equals(periodDetail.getLrcCfPeriodId().toString())).collect(Collectors.toList());
                cfPeriodDetailList.forEach(cfPeriodDetail -> {
                    premCfPeriodMap.put(cfPeriodDetail.getDevPeriod().toString(), cfPeriodDetail.getLrcCfAmount());
                });
            });
            resultMap.put("premCf", premCfDetailList);

            cfPeriod.setLrcCfFeeType(cfFeeTypeMap.get(3));
            List<Map<String, Object>> brokerageCfDetailList = atrBussLrcCfPeriodDao.findCfPeriodList(cfPeriod);
            List<AtrBussLrcCfPeriodDetail> brokerageCfPeriodDetailList = atrBussLrcCfPeriodDetailDao.findPeriodDetail(cfPeriod);
            brokerageCfDetailList.forEach(premCfPeriodMap -> {
                List<AtrBussLrcCfPeriodDetail> cfPeriodDetailList = brokerageCfPeriodDetailList.stream().filter(periodDetail -> premCfPeriodMap.get("lrcCfPeriodId").toString().equals(periodDetail.getLrcCfPeriodId().toString())).collect(Collectors.toList());
                cfPeriodDetailList.forEach(cfPeriodDetail -> {
                    premCfPeriodMap.put(cfPeriodDetail.getDevPeriod().toString(), cfPeriodDetail.getLrcCfAmount());
                });
            });
            resultMap.put("brokerageCf", brokerageCfDetailList);

            cfPeriod.setLrcCfFeeType(cfFeeTypeMap.get(4));
            List<Map<String, Object>> adjCommCfDetailList = atrBussLrcCfPeriodDao.findCfPeriodList(cfPeriod);
            List<AtrBussLrcCfPeriodDetail> adjCommCfPeriodDetailList = atrBussLrcCfPeriodDetailDao.findPeriodDetail(cfPeriod);
            adjCommCfDetailList.forEach(premCfPeriodMap -> {
                List<AtrBussLrcCfPeriodDetail> cfPeriodDetailList = adjCommCfPeriodDetailList.stream().filter(periodDetail -> premCfPeriodMap.get("lrcCfPeriodId").toString().equals(periodDetail.getLrcCfPeriodId().toString())).collect(Collectors.toList());
                cfPeriodDetailList.forEach(cfPeriodDetail -> {
                    premCfPeriodMap.put(cfPeriodDetail.getDevPeriod().toString(), cfPeriodDetail.getLrcCfAmount());
                });
            });
            resultMap.put("adjCommCf", adjCommCfDetailList);

            cfPeriod.setLrcCfFeeType(cfFeeTypeMap.get(5));
            List<Map<String, Object>> maCfDetailList = atrBussLrcCfPeriodDao.findCfPeriodList(cfPeriod);
            List<AtrBussLrcCfPeriodDetail> maCfPeriodDetailList = atrBussLrcCfPeriodDetailDao.findPeriodDetail(cfPeriod);
            maCfDetailList.forEach(maCfPeriodMap -> {
                List<AtrBussLrcCfPeriodDetail> cfPeriodDetailList = maCfPeriodDetailList.stream().filter(periodDetail -> maCfPeriodMap.get("lrcCfPeriodId").toString().equals(periodDetail.getLrcCfPeriodId().toString())).collect(Collectors.toList());
                cfPeriodDetailList.forEach(cfPeriodDetail -> {
                    maCfPeriodMap.put(cfPeriodDetail.getDevPeriod().toString(), cfPeriodDetail.getLrcCfAmount());
                });
            });
            resultMap.put("maCf", maCfDetailList);

            cfPeriod.setLrcCfFeeType(cfFeeTypeMap.get(6));
            List<Map<String, Object>> expClaimCfDetailList = atrBussLrcCfPeriodDao.findCfPeriodList(cfPeriod);
            List<AtrBussLrcCfPeriodDetail> expClaimCfPeriodDetailList = atrBussLrcCfPeriodDetailDao.findPeriodDetail(cfPeriod);
            expClaimCfDetailList.forEach(expClaimCfPeriodMap -> {
                List<AtrBussLrcCfPeriodDetail> cfPeriodDetailList = expClaimCfPeriodDetailList.stream().filter(periodDetail -> expClaimCfPeriodMap.get("lrcCfPeriodId").toString().equals(periodDetail.getLrcCfPeriodId().toString())).collect(Collectors.toList());
                cfPeriodDetailList.forEach(cfPeriodDetail -> {
                    expClaimCfPeriodMap.put(cfPeriodDetail.getDevPeriod().toString(), cfPeriodDetail.getLrcCfAmount());
                });
            });
            resultMap.put("expClaimCf", expClaimCfDetailList);
        }
        return resultMap;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public AtrBussLrcCfMainVo deleteLrcByLrcCfMainId(Long lrcCfMainId) {
        AtrBussLrcCfMainVo atrBussLrcCfMainVo = atrBussLrcCfMainDao.findByMainId(lrcCfMainId);
        if (ObjectUtils.isEmpty(atrBussLrcCfMainVo) || "1".equals(atrBussLrcCfMainVo.getConfirmIs())) {
            return atrBussLrcCfMainVo;
        }
        HashMap map = new HashMap();
        map.put("lrcCfMainId", lrcCfMainId);
        atrBussLrcCfPeriodDetailDao.deleteByLrcCfMainId(lrcCfMainId);
        atrBussLrcCfPeriodDao.deleteByMap(map);
        atrBussLrcCfDetailDao.deleteByMap(map);
        atrBussLrcCfMainDao.deleteById(lrcCfMainId);

        return atrBussLrcCfMainVo;
    }
}
