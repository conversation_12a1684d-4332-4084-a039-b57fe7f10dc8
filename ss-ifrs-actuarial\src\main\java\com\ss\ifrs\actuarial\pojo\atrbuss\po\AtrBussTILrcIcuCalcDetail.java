/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-08-11 10:56:32
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-08-11 10:56:32<br/>
 * Description: LRC 计算结果明细(单维度，合约分入)<br/>
 * Table Name: ATR_BUSS_TI_LRC_ICU_CALC_DETAIL<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "LRC 计算结果明细(单维度，合约分入)")
public class AtrBussTILrcIcuCalcDetail implements Serializable {
    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC_DETAIL.ID
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC_DETAIL.MAIN_ID
     * Database remarks: 结果表的ID
     */
    @ApiModelProperty(value = "结果表的ID", required = true)
    private Long mainId;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC_DETAIL.DEV_NO
     * Database remarks: 发展期
     */
    @ApiModelProperty(value = "发展期", required = false)
    private Short devNo;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC_DETAIL.RIF_RUN_OFF_PATTERN
     * Database remarks: 公共项/RIF Run-Off Pattern
     */
    @ApiModelProperty(value = "公共项/RIF Run-Off Pattern", required = false)
    private BigDecimal rifRunOffPattern;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC_DETAIL.AFTER_PREMIUM_IMPAIRMENT_RATE
     * Database remarks: 公共项/保费减值后比例
     */
    @ApiModelProperty(value = "公共项/保费减值后比例", required = false)
    private BigDecimal afterPremiumImpairmentRate;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC_DETAIL.RECV_PREMIUM
     * Database remarks: 预期应收保费/应收保费
     */
    @ApiModelProperty(value = "预期应收保费/应收保费", required = false)
    private BigDecimal recvPremium;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC_DETAIL.ADJ_COMMISSION
     * Database remarks: 调整手续费/调整手续费
     */
    @ApiModelProperty(value = "调整手续费/调整手续费", required = false)
    private BigDecimal adjCommission;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC_DETAIL.BROKERAGE_FEE
     * Database remarks: 经纪人费用/经纪人费用
     */
    @ApiModelProperty(value = "经纪人费用/经纪人费用", required = false)
    private BigDecimal brokerageFee;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC_DETAIL.IACF_FEE
     * Database remarks: 获取费用/获取费用
     */
    @ApiModelProperty(value = "获取费用/获取费用", required = false)
    private BigDecimal iacfFee;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC_DETAIL.ED_PREMIUM_BACKUP
     * Database remarks: GEP_Persist 已赚保费/已赚保费 BACKUP
     */
    @ApiModelProperty(value = "GEP_Persist 已赚保费/已赚保费 BACKUP", required = false)
    private BigDecimal edPremiumBackup;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC_DETAIL.ED_PREMIUM
     * Database remarks: GEP_Persist 已赚保费/已赚保费
     */
    @ApiModelProperty(value = "GEP_Persist 已赚保费/已赚保费", required = false)
    private BigDecimal edPremium;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC_DETAIL.MAINTENANCE_FEE
     * Database remarks: 维持费用/维持费用
     */
    @ApiModelProperty(value = "维持费用/维持费用", required = false)
    private BigDecimal maintenanceFee;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC_DETAIL.IACF_FEE_NON_POLICY
     * Database remarks: 非跟单获取费用
     */
    @ApiModelProperty(value = "非跟单获取费用", required = false)
    private BigDecimal iacfFeeNonPolicy;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC_DETAIL.UE_PREMIUM
     * Database remarks: 未赚保费/未赚保费
     */
    @ApiModelProperty(value = "未赚保费/未赚保费", required = false)
    private BigDecimal uePremium;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC_DETAIL.GEP_UWQ
     * Database remarks: Claim_byUWQ_AQ_Vert/GEP
     */
    @ApiModelProperty(value = "Claim_byUWQ_AQ_Vert/GEP", required = false)
    private BigDecimal gepUwq;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC_DETAIL.ULTIMATE_LOSS
     * Database remarks: Claim_byUWQ_AQ_Vert/Ultimate Loss
     */
    @ApiModelProperty(value = "Claim_byUWQ_AQ_Vert/Ultimate Loss", required = false)
    private BigDecimal ultimateLoss;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC_DETAIL.RIF_RUN_OFF_PATTERN_RATE
     * Database remarks: Claim_byAQ_Vert_Mortgage/RIF% Run-Off Pattern
     */
    @ApiModelProperty(value = "Claim_byAQ_Vert_Mortgage/RIF% Run-Off Pattern", required = false)
    private BigDecimal rifRunOffPatternRate;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC_DETAIL.ULTIMATE_LOSS_MORTGAGE
     * Database remarks: Claim_byAQ_Vert_Mortgage/Ultimate Loss
     */
    @ApiModelProperty(value = "Claim_byAQ_Vert_Mortgage/Ultimate Loss", required = false)
    private BigDecimal ultimateLossMortgage;

    /**
     * Database column: ATR_BUSS_TI_LRC_ICU_CALC_DETAIL.COVERAGE_AMOUNT
     * Database remarks: Coverage_Unit 保额&限额/保额/限额
     */
    @ApiModelProperty(value = "Coverage_Unit 保额&限额/保额/限额", required = false)
    private BigDecimal coverageAmount;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMainId() {
        return mainId;
    }

    public void setMainId(Long mainId) {
        this.mainId = mainId;
    }

    public Short getDevNo() {
        return devNo;
    }

    public void setDevNo(Short devNo) {
        this.devNo = devNo;
    }

    public BigDecimal getRifRunOffPattern() {
        return rifRunOffPattern;
    }

    public void setRifRunOffPattern(BigDecimal rifRunOffPattern) {
        this.rifRunOffPattern = rifRunOffPattern;
    }

    public BigDecimal getAfterPremiumImpairmentRate() {
        return afterPremiumImpairmentRate;
    }

    public void setAfterPremiumImpairmentRate(BigDecimal afterPremiumImpairmentRate) {
        this.afterPremiumImpairmentRate = afterPremiumImpairmentRate;
    }

    public BigDecimal getRecvPremium() {
        return recvPremium;
    }

    public void setRecvPremium(BigDecimal recvPremium) {
        this.recvPremium = recvPremium;
    }

    public BigDecimal getAdjCommission() {
        return adjCommission;
    }

    public void setAdjCommission(BigDecimal adjCommission) {
        this.adjCommission = adjCommission;
    }

    public BigDecimal getBrokerageFee() {
        return brokerageFee;
    }

    public void setBrokerageFee(BigDecimal brokerageFee) {
        this.brokerageFee = brokerageFee;
    }

    public BigDecimal getIacfFee() {
        return iacfFee;
    }

    public void setIacfFee(BigDecimal iacfFee) {
        this.iacfFee = iacfFee;
    }

    public BigDecimal getEdPremiumBackup() {
        return edPremiumBackup;
    }

    public void setEdPremiumBackup(BigDecimal edPremiumBackup) {
        this.edPremiumBackup = edPremiumBackup;
    }

    public BigDecimal getEdPremium() {
        return edPremium;
    }

    public void setEdPremium(BigDecimal edPremium) {
        this.edPremium = edPremium;
    }

    public BigDecimal getMaintenanceFee() {
        return maintenanceFee;
    }

    public void setMaintenanceFee(BigDecimal maintenanceFee) {
        this.maintenanceFee = maintenanceFee;
    }

    public BigDecimal getIacfFeeNonPolicy() {
        return iacfFeeNonPolicy;
    }

    public void setIacfFeeNonPolicy(BigDecimal iacfFeeNonPolicy) {
        this.iacfFeeNonPolicy = iacfFeeNonPolicy;
    }

    public BigDecimal getUePremium() {
        return uePremium;
    }

    public void setUePremium(BigDecimal uePremium) {
        this.uePremium = uePremium;
    }

    public BigDecimal getGepUwq() {
        return gepUwq;
    }

    public void setGepUwq(BigDecimal gepUwq) {
        this.gepUwq = gepUwq;
    }

    public BigDecimal getUltimateLoss() {
        return ultimateLoss;
    }

    public void setUltimateLoss(BigDecimal ultimateLoss) {
        this.ultimateLoss = ultimateLoss;
    }

    public BigDecimal getRifRunOffPatternRate() {
        return rifRunOffPatternRate;
    }

    public void setRifRunOffPatternRate(BigDecimal rifRunOffPatternRate) {
        this.rifRunOffPatternRate = rifRunOffPatternRate;
    }

    public BigDecimal getUltimateLossMortgage() {
        return ultimateLossMortgage;
    }

    public void setUltimateLossMortgage(BigDecimal ultimateLossMortgage) {
        this.ultimateLossMortgage = ultimateLossMortgage;
    }

    public BigDecimal getCoverageAmount() {
        return coverageAmount;
    }

    public void setCoverageAmount(BigDecimal coverageAmount) {
        this.coverageAmount = coverageAmount;
    }
}