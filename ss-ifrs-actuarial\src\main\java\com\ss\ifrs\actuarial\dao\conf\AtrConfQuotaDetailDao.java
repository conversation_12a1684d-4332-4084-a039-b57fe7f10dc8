/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-11-10 10:06:14
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd.
 * 
 */
package com.ss.ifrs.actuarial.dao.conf;

import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuota;
import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDetail;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaDetailVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaVo;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2021-11-10 10:06:14<br/>
 * Description: null Dao类<br/>
 * Related Table Name: bbs_conf_quota_detail<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 * <AUTHOR>
 */
@Mapper
public interface AtrConfQuotaDetailDao extends IDao<AtrConfQuotaDetail, Long> {
    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/17
     * @Description 根据业务单位ID和风险代码查找指标明细配置对象信息
     * @Return
     */
    AtrConfQuotaDetail findByCenterAndRisk(AtrConfQuotaDetailVo detailVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/17
     * @Description 修改指标明细配置信息
     * @Return
     */
    void updateByPo(AtrConfQuotaDetail detail);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/17
     * @Description 根据指标Id查找指标明细配置信息列表
     * @Return
     */
    List<AtrConfQuotaDetailVo> findListByQuotaId(Long quotaId);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/17
     * @Description 根据指标Id删除指标明细配置信息
     * @Return
     */
    void deleteByQuotaId(Long quotaId);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/24
     * @Description 根据指标Id删除指标明细配置信息
     * @Return
     */
    AtrConfQuotaDetailVo findQuotaIdAndPeriod(AtrConfQuotaVo bbsConfQuotaVo);

    /**
     * @Description 根据指标Id查找指标明细配置信息列表
     * @Return List
     */
    List<AtrConfQuotaDetail> findListByQuotaVo(AtrConfQuotaVo bbsConfQuotaVo);

    /**
     * @Date 2022/12/01
     * @Description 根据假设值维度信息删除
     */
    void deleteByDimensionVo(AtrConfQuota bbsConfQuota);

    /**
     * @Date 2022/12/01
     * @Description 根据假设值维度信息删除
     */
    void deleteByLoaDimension(AtrConfQuota bbsConfQuota);

    /**
     * @Date 2022/12/01
     * @Description 根据假设值维度信息删除
     */
    void deleteByOtherModel(AtrConfQuota bbsConfQuota);

    /**
     * @Date 2023/3/23
     * @Description 查找发展期数据
     */
    List<AtrConfQuotaDetail> findConfQuotaPeriodValue(AtrConfQuotaVo bbsConfQuotaVo);

    void loadPrePeriodQuotaDetail(AtrConfQuotaVo bbsConfQuota);

    int saveQuotaDetailList(@Param("mainId") Long ibnrMainId, @Param("list") List<AtrConfQuotaDetail> qtcConfQuotaDetailList);
}