package com.ss.ifrs.actuarial.aop;

import com.ss.ifrs.actuarial.constant.ActuarialConstant;
import com.ss.ifrs.actuarial.domain.service.AtrDomainTaskService;
import com.ss.ifrs.actuarial.feign.BmsTrackBussActionFeignClient;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.library.utils.ClassUtil;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.constant.SystemConstant;
import com.ss.platform.pojo.com.vo.ActOverviewVo;
import com.ss.platform.pojo.com.vo.BussActionStateVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 该数据日志的切面。
 * 利用AOP对标注了{@link TrackActuarialProcess}的方法进行拦截，
 * 在方法执行前、执行后（包括异常情况）记录日志。
 */
@Aspect
@Component
@Slf4j
@SuppressWarnings("all")
public class AtrTrackProcessAspect extends BaseApi {

    private AtrDomainTaskService atrDomainTaskService;

    @Autowired
    BmsTrackBussActionFeignClient bmsTrackBussActionFeignClient;

    public AtrTrackProcessAspect(AtrDomainTaskService atrDomainTaskService) {
        this.atrDomainTaskService = atrDomainTaskService;
    }

    @Pointcut("@annotation(com.ss.ifrs.actuarial.annotation.TrackActuarialProcess)")
    public void trackProcess() {
    }
    /**
     * 在切点方法执行前后织入逻辑，更新旧表日志至新表 后续接入拆分存储过程需改造。
     *
     * @param joinPoint    切入点，表示被拦截的方法。
     * @param TrackExpenseProcess 被拦截方法上的注解, 作用方法上第一个参数传入对象 需转换日志对象。
     * @throws Throwable 如果目标方法执行过程中抛出异常，则抛出。
     */
    @After("trackProcess()")
    public void trackAction(JoinPoint joinPoint) throws Throwable {
        AtrConfBussPeriodVo bussActionStateVo = ClassUtil.convert(joinPoint.getArgs()[0], AtrConfBussPeriodVo.class);
        try {
            this.updateTrackActionStatus(bussActionStateVo);
        } catch (Exception e) {
            log.error("trackAction exception:{}", e.getMessage());
            throw e;
        }
    }

    public void updateTrackActionStatus(AtrConfBussPeriodVo atrConfBussPeriodVo) {
        BussActionStateVo actionVo = ClassUtil.convert(atrConfBussPeriodVo, BussActionStateVo.class);
        actionVo.setSystemCode(SystemConstant.AtrIdentity.APP_CODE);
        actionVo.setState(CommonConstant.BussActionState.STATE_ONE);

        try {
            if (ObjectUtils.isNotEmpty(atrConfBussPeriodVo.getProcId())) {
                atrDomainTaskService.mergeActuarialProcessState(actionVo);
            }

            if (ObjectUtils.isNotEmpty(atrConfBussPeriodVo.getProcCode()) ) {
                ActOverviewVo param = new ActOverviewVo();
                switch (atrConfBussPeriodVo.getProcCode()) {
                    case ActuarialConstant.ProcCode.EXPECTED_CF_LRC:
                        param.setProcCode(ActuarialConstant.ProcCode.EXPECTED_CF_LRC);
                        ActOverviewVo actOverviewVo = atrDomainTaskService.getActOverviewVoByObject(param);
                        actionVo.setProcId(actOverviewVo.getProcId());
                        bmsTrackBussActionFeignClient.refreshBussActionState(actionVo);

                        param.setProcCode(ActuarialConstant.ProcCode.ATR_DISCOUNTING_LRC);
                        actOverviewVo = atrDomainTaskService.getActOverviewVoByObject(param);
                        actionVo.setProcId(actOverviewVo.getProcId());
                        bmsTrackBussActionFeignClient.refreshBussActionState(actionVo);
                        break;
                    case ActuarialConstant.ProcCode.EXPECTED_CF_LIC:
                        param = new ActOverviewVo();
                        param.setProcCode(ActuarialConstant.ProcCode.EXPECTED_CF_LIC);
                        actOverviewVo = atrDomainTaskService.getActOverviewVoByObject(param);
                        actionVo.setProcId(actOverviewVo.getProcId());
                        bmsTrackBussActionFeignClient.refreshBussActionState(actionVo);

                        param.setProcCode(ActuarialConstant.ProcCode.ATR_DISCOUNTING_LIC);
                        actOverviewVo = atrDomainTaskService.getActOverviewVoByObject(param);
                        actionVo.setProcId(actOverviewVo.getProcId());
                        bmsTrackBussActionFeignClient.refreshBussActionState(actionVo);
                        break;
                }
            }
        } catch (Exception e) {
            log.error("updateActionLog error :{}", e.getLocalizedMessage());
            throw e;
        }
    }

}
