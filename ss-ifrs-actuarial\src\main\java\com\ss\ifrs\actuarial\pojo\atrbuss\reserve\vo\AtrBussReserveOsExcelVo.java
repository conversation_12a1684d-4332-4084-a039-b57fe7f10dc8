package com.ss.ifrs.actuarial.pojo.atrbuss.reserve.vo;

//import com.alibaba.excel.annotation.ExcelProperty;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName AtrBussReserveOsExcelVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/2/22
 **/
public class AtrBussReserveOsExcelVo {

    //@ExcelProperty("姓名")
    private String name;
    //@ExcelProperty("年龄")
    private Integer age;
    //@ExcelProperty("职业")
    private String occupation;
    //@ExcelProperty("账户")
    private String account;
    //@ExcelProperty("地址")
    private String address;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getOccupation() {
        return occupation;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    /*private String entityCode;
    private String yearMonth;
    private String riskClassCode;
    private String riskCode;
    private String policyNo;
    private String endorseNo;
    private String claimNo;
    private String businessType;
    private Date startDate;
    private Date endDate;
    private BigDecimal beforeOsAmount;
    private BigDecimal outwardOsAmount;
    private BigDecimal afterOsAmount;
    private BigDecimal beforePaidAmount;
    private BigDecimal outwardPaidAmount;
    private BigDecimal afterPaidAmount;

    public AtrBussReserveOsExcelVo(String centerCode, String yearMonth, String riskClass, String riskCode, String policyNo, String endorseNo, String claimNo, String businessType, Date startDate, Date endDate, BigDecimal beforeOsAmount, BigDecimal outwardOsAmount, BigDecimal afterOsAmount, BigDecimal beforePaidAmount, BigDecimal outwardPaidAmount, BigDecimal afterPaidAmount) {
        this.centerCode = centerCode;
        this.yearMonth = yearMonth;
        this.riskClass = riskClass;
        this.riskCode = riskCode;
        this.policyNo = policyNo;
        this.endorseNo = endorseNo;
        this.claimNo = claimNo;
        this.businessType = businessType;
        this.startDate = startDate;
        this.endDate = endDate;
        this.beforeOsAmount = beforeOsAmount;
        this.outwardOsAmount = outwardOsAmount;
        this.afterOsAmount = afterOsAmount;
        this.beforePaidAmount = beforePaidAmount;
        this.outwardPaidAmount = outwardPaidAmount;
        this.afterPaidAmount = afterPaidAmount;
    }

    @Override
    public String toString() {
        return "AtrBussReserveOsExcelVo{" +
                "centerCode='" + centerCode + '\'' +
                ", yearMonth='" + yearMonth + '\'' +
                ", riskClass='" + riskClass + '\'' +
                ", riskCode='" + riskCode + '\'' +
                ", policyNo='" + policyNo + '\'' +
                ", endorseNo='" + endorseNo + '\'' +
                ", claimNo='" + claimNo + '\'' +
                ", businessType='" + businessType + '\'' +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", beforeOsAmount=" + beforeOsAmount +
                ", outwardOsAmount=" + outwardOsAmount +
                ", afterOsAmount=" + afterOsAmount +
                ", beforePaidAmount=" + beforePaidAmount +
                ", outwardPaidAmount=" + outwardPaidAmount +
                ", afterPaidAmount=" + afterPaidAmount +
                '}';
    }

    public String getEntityCode() {
        return entityCode;
    }

    public void setEntityCode(String entityCode) {
        this.entityCode = entityCode;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public String getRiskCode() {
        return riskCode;
    }

    public void setRiskCode(String riskCode) {
        this.riskCode = riskCode;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getEndorseNo() {
        return endorseNo;
    }

    public void setEndorseNo(String endorseNo) {
        this.endorseNo = endorseNo;
    }

    public String getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(String claimNo) {
        this.claimNo = claimNo;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public BigDecimal getBeforeOsAmount() {
        return beforeOsAmount;
    }

    public void setBeforeOsAmount(BigDecimal beforeOsAmount) {
        this.beforeOsAmount = beforeOsAmount;
    }

    public BigDecimal getOutwardOsAmount() {
        return outwardOsAmount;
    }

    public void setOutwardOsAmount(BigDecimal outwardOsAmount) {
        this.outwardOsAmount = outwardOsAmount;
    }

    public BigDecimal getAfterOsAmount() {
        return afterOsAmount;
    }

    public void setAfterOsAmount(BigDecimal afterOsAmount) {
        this.afterOsAmount = afterOsAmount;
    }

    public BigDecimal getBeforePaidAmount() {
        return beforePaidAmount;
    }

    public void setBeforePaidAmount(BigDecimal beforePaidAmount) {
        this.beforePaidAmount = beforePaidAmount;
    }

    public BigDecimal getOutwardPaidAmount() {
        return outwardPaidAmount;
    }

    public void setOutwardPaidAmount(BigDecimal outwardPaidAmount) {
        this.outwardPaidAmount = outwardPaidAmount;
    }

    public BigDecimal getAfterPaidAmount() {
        return afterPaidAmount;
    }

    public void setAfterPaidAmount(BigDecimal afterPaidAmount) {
        this.afterPaidAmount = afterPaidAmount;
    }*/
}
