TRUNCATE TABLE qtcuser.qtc_conf_factor_output_ref;
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (1, 3, 'qtc_buss_evaluate_result', 'num2', 'M3_OA_02', 'Lrc', '1', 1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (2, 4, 'qtc_buss_evaluate_result', 'num2', 'M4_OA_02', 'Lrc', '1', -1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (3, 3, 'qtc_buss_evaluate_result', 'num3', 'M3_OA_03', 'Lrc', '1', -1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (4, 4, 'qtc_buss_evaluate_result', 'num3', 'M4_OA_03', 'Lrc', '1', -1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (5, 3, 'qtc_buss_evaluate_result', 'num7', 'M3_OB_02', 'Lrc', '1', -1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (6, 4, 'qtc_buss_evaluate_result', 'num7', 'M4_OB_02', 'Lrc', '1', 1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (7, 3, 'qtc_buss_evaluate_result', 'num8', 'M3_OB_03', 'Lrc', '1', -1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (8, 4, 'qtc_buss_evaluate_result', 'num8', 'M4_OB_03', 'Lrc', '1', 1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (9, 3, 'qtc_buss_evaluate_result', 'num12', 'M3_OC_02', 'Lrc', '1', -1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (10, 3, 'qtc_buss_evaluate_result', 'num13', 'M3_OC_03', 'Lrc', '1', 1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (11, 3, 'qtc_buss_evaluate_result', 'num17', 'M3_OD_02', 'Lrc', '1', -1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (12, 3, 'qtc_buss_evaluate_result', 'num18', 'M3_OD_03', 'Lrc', '1', 1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (13, 3, 'qtc_buss_evaluate_result', 'num22', 'M3_OE_02', 'Lrc', '1', -1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (14, 3, 'qtc_buss_evaluate_result', 'num26', 'M3_OF_02', 'Lrc', '1', -1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (15, 3, 'qtc_buss_evaluate_result', 'num28', 'M3_OG_01', 'Lrc', '1', 1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (16, 3, 'qtc_buss_evaluate_result', 'num2', 'M3_OI_02', 'Lic', '1', -1, 'C');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (17, 4, 'qtc_buss_evaluate_result', 'num2', 'M4_OI_02', 'Lic', '1', 1, 'C');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (18, 3, 'qtc_buss_evaluate_result', 'num3', 'M3_OI_03', 'Lic', '1', -1, 'P');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (19, 4, 'qtc_buss_evaluate_result', 'num3', 'M4_OI_03', 'Lic', '1', 1, 'P');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (20, 3, 'qtc_buss_evaluate_result', 'num4', 'M3_OI_04', 'Lic', '1', -1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (21, 4, 'qtc_buss_evaluate_result', 'num4', 'M4_OI_04', 'Lic', '1', 1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (22, 3, 'qtc_buss_evaluate_result', 'num5', 'M3_OI_05', 'Lic', '1', -1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (23, 4, 'qtc_buss_evaluate_result', 'num5', 'M4_OI_05', 'Lic', '1', 1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (24, 3, 'qtc_buss_evaluate_result', 'num8', 'M3_OJ_02', 'Lic', '1', -1, 'C');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (25, 4, 'qtc_buss_evaluate_result', 'num8', 'M4_OJ_02', 'Lic', '1', 1, 'C');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (26, 3, 'qtc_buss_evaluate_result', 'num9', 'M3_OJ_03', 'Lic', '1', -1, 'P');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (27, 4, 'qtc_buss_evaluate_result', 'num9', 'M4_OJ_03', 'Lic', '1', 1, 'P');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (28, 3, 'qtc_buss_evaluate_result', 'num10', 'M3_OJ_04', 'Lic', '1', -1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (29, 4, 'qtc_buss_evaluate_result', 'num10', 'M4_OJ_04', 'Lic', '1', 1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (30, 3, 'qtc_buss_evaluate_result', 'num11', 'M3_OJ_05', 'Lic', '1', -1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (31, 4, 'qtc_buss_evaluate_result', 'num11', 'M4_OJ_05', 'Lic', '1', 1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (32, 3, 'qtc_buss_evaluate_result', 'num14', 'M3_OK_02', 'Lic', '1', -1, 'C');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (33, 3, 'qtc_buss_evaluate_result', 'num15', 'M3_OK_03', 'Lic', '1', -1, 'P');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (34, 3, 'qtc_buss_evaluate_result', 'num16', 'M3_OK_04', 'Lic', '1', -1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (35, 3, 'qtc_buss_evaluate_result', 'num17', 'M3_OK_05', 'Lic', '1', -1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (36, 3, 'qtc_buss_evaluate_result', 'num20', 'M3_OL_02', 'Lic', '1', -1, 'C');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (37, 4, 'qtc_buss_evaluate_result', 'num14', 'M4_OL_02', 'Lic', '1', 1, 'C');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (38, 3, 'qtc_buss_evaluate_result', 'num21', 'M3_OL_03', 'Lic', '1', -1, 'P');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (39, 4, 'qtc_buss_evaluate_result', 'num15', 'M4_OL_03', 'Lic', '1', 1, 'P');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (40, 3, 'qtc_buss_evaluate_result', 'num22', 'M3_OL_04', 'Lic', '1', -1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (41, 4, 'qtc_buss_evaluate_result', 'num16', 'M4_OL_04', 'Lic', '1', 1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (42, 3, 'qtc_buss_evaluate_result', 'num23', 'M3_OL_05', 'Lic', '1', -1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (43, 4, 'qtc_buss_evaluate_result', 'num17', 'M4_OL_05', 'Lic', '1', 1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (44, 4, 'qtc_buss_evaluate_result', 'num20', 'M4_OM_02', 'Lic', '1', -1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (45, 4, 'qtc_buss_evaluate_result', 'num12', 'M4_OC_02', 'Lrc', '1', -1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (46, 4, 'qtc_buss_evaluate_result', 'num14', 'M4_OD_01', 'Lrc', '1', -1, 'N');
INSERT INTO qtcuser.qtc_conf_factor_output_ref (table_column_id, model_def_id, tab_name, tab_column, value_ref, factor_type, account_flag, cal_sign, current_previous_is) VALUES (47, 3, 'qtc_buss_evaluate_result', 'num25', 'M3_OE_05', 'Lrc', '1', -1, 'N');

