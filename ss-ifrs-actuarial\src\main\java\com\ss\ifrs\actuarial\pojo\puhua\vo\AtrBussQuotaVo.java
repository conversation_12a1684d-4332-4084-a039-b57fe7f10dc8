/**
 * 
 * This file was generated by  MyBatis Generator(v1.2.12).
 * Date: 2021-11-10 17:08:27
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027,  l Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.puhua.vo;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * This code was generated by  MyBatis Generator(v1.2.12).
 * Create Date: 2021-11-10 17:08:27<br/>
 * Description: null<br/>
 * Table Name: bbs_conf_quota_def<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 * <AUTHOR>
 */
//基础配置表
public class AtrBussQuotaVo implements Serializable {

    private Long quotaDefId;
    private String quotaCode;
    private String quotaCName;
    private String quotaEName;
    private String quotaLName;
    //指标类型
    private String quotaType;
    //指标数据类型
    private String quotaValueType;

    //指标规则
    private String quotaRule;
    private String codeType;


    private String expressEMsg;

    private String expressCMsg;

    private String expressTMsg;

    private String quotaNullIs;

    private String validIs;

    private String quotaGroup;

    private String value;

    private Long quotaId;

    private Long versionNo;

    private Long ruleId;
    private String ruleExpression;
    private String msgCName;
    private String msgLName;
    private String msgEName;

    private List<String> valueList;

    private String percentHundredIs;

    private String riskClassCode;

    LinkedHashMap<String, AtrBussQuotaVo> childQuota;

    public String getRiskClassCode() {
        return riskClassCode;
    }

    public void setRiskClassCode(String riskClassCode) {
        this.riskClassCode = riskClassCode;
    }

    public LinkedHashMap<String, AtrBussQuotaVo> getChildQuota() {
        return childQuota;
    }

    public void setChildQuota(LinkedHashMap<String, AtrBussQuotaVo> childQuota) {
        this.childQuota = childQuota;
    }

    public String getPercentHundredIs() {
        return percentHundredIs;
    }

    public void setPercentHundredIs(String percentHundredIs) {
        this.percentHundredIs = percentHundredIs;
    }

    public List<String> getValueList() {
        return valueList;
    }

    public void setValueList(List<String> valueList) {
        this.valueList = valueList;
    }

    private static final long serialVersionUID = 1L;

    public Long getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Long versionNo) {
        this.versionNo = versionNo;
    }

    public Long getQuotaId() {
        return quotaId;
    }

    public void setQuotaId(Long quotaId) {
        this.quotaId = quotaId;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Long getQuotaDefId() {
        return quotaDefId;
    }

    public void setQuotaDefId(Long quotaDefId) {
        this.quotaDefId = quotaDefId;
    }

    public String getQuotaCode() {
        return quotaCode;
    }

    public void setQuotaCode(String quotaCode) {
        this.quotaCode = quotaCode;
    }

    public String getQuotaType() {
        return quotaType;
    }

    public void setQuotaType(String quotaType) {
        this.quotaType = quotaType;
    }

    public String getQuotaRule() {
        return quotaRule;
    }

    public void setQuotaRule(String quotaRule) {
        this.quotaRule = quotaRule;
    }

    public String getQuotaNullIs() {
        return quotaNullIs;
    }

    public void setQuotaNullIs(String quotaNullIs) {
        this.quotaNullIs = quotaNullIs;
    }

    public String getValidIs() {
        return validIs;
    }

    public void setValidIs(String validIs) {
        this.validIs = validIs;
    }

    public String getQuotaCName() {
        return quotaCName;
    }

    public void setQuotaCName(String quotaCName) {
        this.quotaCName = quotaCName;
    }

    public String getQuotaEName() {
        return quotaEName;
    }

    public void setQuotaEName(String quotaEName) {
        this.quotaEName = quotaEName;
    }

    public String getQuotaLName() {
        return quotaLName;
    }

    public void setQuotaLName(String quotaLName) {
        this.quotaLName = quotaLName;
    }

    public String getCodeType() {
        return codeType;
    }

    public void setCodeType(String codeType) {
        this.codeType = codeType;
    }

    public String getQuotaValueType() {
        return quotaValueType;
    }

    public void setQuotaValueType(String quotaValueType) {
        this.quotaValueType = quotaValueType;
    }

    public String getExpressEMsg() {
        return expressEMsg;
    }

    public void setExpressEMsg(String expressEMsg) {
        this.expressEMsg = expressEMsg;
    }

    public String getExpressCMsg() {
        return expressCMsg;
    }

    public void setExpressCMsg(String expressCMsg) {
        this.expressCMsg = expressCMsg;
    }

    public String getExpressTMsg() {
        return expressTMsg;
    }

    public void setExpressTMsg(String expressTMsg) {
        this.expressTMsg = expressTMsg;
    }

    public String getQuotaGroup() {
        return quotaGroup;
    }

    public void setQuotaGroup(String quotaGroup) {
        this.quotaGroup = quotaGroup;
    }


    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public String getRuleExpression() {
        return ruleExpression;
    }

    public void setRuleExpression(String ruleExpression) {
        this.ruleExpression = ruleExpression;
    }

    public String getMsgCName() {
        return msgCName;
    }

    public void setMsgCName(String msgCName) {
        this.msgCName = msgCName;
    }

    public String getMsgLName() {
        return msgLName;
    }

    public void setMsgLName(String msgLName) {
        this.msgLName = msgLName;
    }

    public String getMsgEName() {
        return msgEName;
    }

    public void setMsgEName(String msgEName) {
        this.msgEName = msgEName;
    }
}