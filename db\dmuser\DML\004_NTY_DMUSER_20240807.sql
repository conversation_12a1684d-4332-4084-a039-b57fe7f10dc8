--关闭收付信息业务数据有效性校验规则
update dm_conf_checkrule set valid_is = '0' where rule_code = 'ACC_PAYMENT_BUSINESS_DATA_VALID_IS';

--再保前业务应收应付数据有效性校验规则
delete from dm_conf_checkrule where rule_code = 'ACC_PAYMENT_BEFORE_REINS_G_DATA_VALID_IS';

INSERT INTO dm_conf_checkrule (config_rule_id, rule_code, rule_e_name, rule_l_name, rule_c_name, rule_config, rule_version, valid_date, invalid_date, col_id, opt_type, match_value, rule_sql, valid_is, create_time, creator_id, update_time, updator_id, rule_direction, rule_type_code, original_is, biz_type_id, remark, check_type, display_no, serial_no, checked_msg, audit_state, checked_time, checked_id, apply_model, rule_type)
VALUES
(nextval('dm_seq_conf_checkrule'), 'ACC_PAYMENT_BEFORE_REINS_G_DATA_VALID_IS',
 'Validity Of Pre Reinsurance Business Credit Data', '再保前業務掛賬數據有效性', '再保前业务挂账数据有效性',
 NULL, '1.00', '2024-08-07 14:30:00', '9999-12-31 00:00:00', NULL, '0', NULL,
 'select id from odsuser.ods_acc_payment where id in (select x.id from odsuser.ods_acc_payment x
left join dm_policy_premium y on y.entity_code = x.entity_code and y.policy_no = x.policy_no
where y.id is null
and x.policy_no is not null
and substr(x.extend_column1,1,2) in (''BA'',''BB'',''BO'',''BP'',''CF'',''DI'',''IC'',''OP'',''JR'',''IR'')
and substr(x.entry_type_code,1,2) in (''DN'',''CN''))', '1', '2024-08-07 14:30:00', NULL, '2024-08-07 14:30:00', 1, '0', '1', '1',
 (SELECT biz_type_id FROM dm_conf_table WHERE biz_code = UPPER ( 'ACC_PAYMENT' )),
 '再保前业务当保单号+批改序号都不为空时，需要校验保单号+批改序号在风险费用信息表有通过的数据',
 (select cast(max(check_type) as INTEGER)-1 from dm_conf_checkrule where biz_type_id = (SELECT biz_type_id FROM dm_conf_table WHERE biz_code = UPPER ( 'ACC_PAYMENT' ))),
 NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2');

--临分分出业务应收应付数据有效性校验规则
delete from dm_conf_checkrule where rule_code = 'ACC_PAYMENT_FAC_OUT_G_DATA_VALID_IS';

INSERT INTO dm_conf_checkrule (config_rule_id, rule_code, rule_e_name, rule_l_name, rule_c_name, rule_config, rule_version, valid_date, invalid_date, col_id, opt_type, match_value, rule_sql, valid_is, create_time, creator_id, update_time, updator_id, rule_direction, rule_type_code, original_is, biz_type_id, remark, check_type, display_no, serial_no, checked_msg, audit_state, checked_time, checked_id, apply_model, rule_type)
VALUES
(nextval('dm_seq_conf_checkrule'), 'ACC_PAYMENT_FAC_OUT_G_DATA_VALID_IS',
'Validity Verification Of Accounts Receivable And Payable Data For Temporary Separation Business', '臨分分出業務應收應付數據有效性', '临分分出业务应收应付数据有效性',
NULL, '1.00', '2024-08-07 14:30:00', '9999-12-31 00:00:00', NULL, '0', NULL,
'select id from odsuser.ods_acc_payment where id in (select x.id from odsuser.ods_acc_payment x
left join dm_reins_outward_detail y on y.entity_code = x.entity_code and y.ri_policy_no = x.ri_policy_no
where y.id is null
and x.ri_policy_no is not null
and substr(x.extend_column1,1,2) in (''AF'',''FO'')
and substr(x.entry_type_code,1,2) in (''DN'',''CN''))', '1', '2024-08-07 14:30:00', NULL, '2024-08-07 14:30:00', 1, '0', '1', '1',
(SELECT biz_type_id FROM dm_conf_table WHERE biz_code = UPPER ( 'ACC_PAYMENT' )),
'临分分出分保单号码不为空时，需要校验分保单号码在分保安排明细信息表有通过的数据',
(select cast(max(check_type) as INTEGER)-1 from dm_conf_checkrule where biz_type_id = (SELECT biz_type_id FROM dm_conf_table WHERE biz_code = UPPER ( 'ACC_PAYMENT' ))),
NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2');

--超赔合约分出业务应收应付数据有效性校验规则
delete from dm_conf_checkrule where rule_code = 'ACC_PAYMENT_OVER_COMPENSATED_OUT_G_DATA_VALID_IS';

INSERT INTO dm_conf_checkrule (config_rule_id, rule_code, rule_e_name, rule_l_name, rule_c_name, rule_config, rule_version, valid_date, invalid_date, col_id, opt_type, match_value, rule_sql, valid_is, create_time, creator_id, update_time, updator_id, rule_direction, rule_type_code, original_is, biz_type_id, remark, check_type, display_no, serial_no, checked_msg, audit_state, checked_time, checked_id, apply_model, rule_type)
VALUES
(nextval('dm_seq_conf_checkrule'), 'ACC_PAYMENT_OVER_COMPENSATED_OUT_G_DATA_VALID_IS',
'Validity Verification Of Accounts Receivable And Payable Data For Over Compensation Contract Business', '超賠合約分出業務應收應付數據有效性', '超赔合约分出业务应收应付数据有效性',
NULL, '1.00', '2024-08-07 14:30:00', '9999-12-31 00:00:00', NULL, '0', NULL,
'select id from odsuser.ods_acc_payment where id in (select x.id from odsuser.ods_acc_payment x
left join dm_reins_bill_detail y on y.entity_code = x.entity_code and y.ri_statement_no = x.ri_statement_no
where ((y.id is null and x.ri_statement_no is not null) or x.ri_statement_no is null)
and substr(x.extend_column1,1,2) in (''AP'',''RP'',''X1'',''X2'',''X3'',''X4'',''X5'',''XX'')
and substr(x.entry_type_code,1,2) in (''DN'',''CN''))', '1', '2024-08-07 14:30:00', NULL, '2024-08-07 14:30:00', 1, '0', '1', '1',
(SELECT biz_type_id FROM dm_conf_table WHERE biz_code = UPPER ( 'ACC_PAYMENT' )),
'超赔合约账单号不允许为空，且需要校验账单号在账单明细信息表有通过的数据',
(select cast(max(check_type) as INTEGER)-1 from dm_conf_checkrule where biz_type_id = (SELECT biz_type_id FROM dm_conf_table WHERE biz_code = UPPER ( 'ACC_PAYMENT' ))),
NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2');


--再保前业务实收实付数据有效性校验规则
delete from dm_conf_checkrule where rule_code = 'ACC_PAYMENT_BEFORE_REINS_S_DATA_VALID_IS';

INSERT INTO dm_conf_checkrule (config_rule_id, rule_code, rule_e_name, rule_l_name, rule_c_name, rule_config, rule_version, valid_date, invalid_date, col_id, opt_type, match_value, rule_sql, valid_is, create_time, creator_id, update_time, updator_id, rule_direction, rule_type_code, original_is, biz_type_id, remark, check_type, display_no, serial_no, checked_msg, audit_state, checked_time, checked_id, apply_model, rule_type)
VALUES
(nextval('dm_seq_conf_checkrule'), 'ACC_PAYMENT_BEFORE_REINS_S_DATA_VALID_IS',
'Validity Of Actual Payment Data Before Reinsurance', '再保前業務實收實付數據有效性', '再保前业务实收实付数据有效性',
NULL, '1.00', '2024-08-07 14:30:00', '9999-12-31 00:00:00', NULL, '0', NULL,
'select id from odsuser.ods_acc_payment where id in (select x.id from odsuser.ods_acc_payment x
left join dm_policy_premium y on y.entity_code = x.entity_code and split_part(y.policy_no,''-'',1) = x.policy_no
where y.id is null
and x.policy_no is not null
and substr(x.extend_column1,1,2) in (''BA'',''BB'',''BO'',''BP'',''CF'',''DI'',''IC'',''OP'',''JR'',''IR'')
and substr(x.entry_type_code,1,2) not in (''DN'',''CN''))', '1', '2024-08-07 14:30:00', NULL, '2024-08-07 14:30:00', 1, '0', '1', '1',
(SELECT biz_type_id FROM dm_conf_table WHERE biz_code = UPPER ( 'ACC_PAYMENT' )),
'再保前校验大保单是否存在，收付这边的 Policy_no 匹配风险费用表存在',
(select cast(max(check_type) as INTEGER)-1 from dm_conf_checkrule where biz_type_id = (SELECT biz_type_id FROM dm_conf_table WHERE biz_code = UPPER ( 'ACC_PAYMENT' ))),
NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2');


--临分分出业务实收实付数据有效性校验规则
delete from dm_conf_checkrule where rule_code = 'ACC_PAYMENT_FAC_OUT_S_DATA_VALID_IS';

INSERT INTO dm_conf_checkrule (config_rule_id, rule_code, rule_e_name, rule_l_name, rule_c_name, rule_config, rule_version, valid_date, invalid_date, col_id, opt_type, match_value, rule_sql, valid_is, create_time, creator_id, update_time, updator_id, rule_direction, rule_type_code, original_is, biz_type_id, remark, check_type, display_no, serial_no, checked_msg, audit_state, checked_time, checked_id, apply_model, rule_type)
VALUES
    (nextval('dm_seq_conf_checkrule'), 'ACC_PAYMENT_FAC_OUT_S_DATA_VALID_IS',
     'Validity Of Actual Receipt And Payment Data For Temporary Distribution Business', '臨分分出業務實收實付數據有效性', '临分分出业务实收实付数据有效性',
     NULL, '1.00', '2024-08-07 14:30:00', '9999-12-31 00:00:00', NULL, '0', NULL,
     'select id from odsuser.ods_acc_payment where id in (select x.id from odsuser.ods_acc_payment x
     left join dm_policy_premium y on y.entity_code = x.entity_code and split_part(y.policy_no,''-'',1) = x.policy_no
     where y.id is null
     and x.policy_no is not null
     and substr(x.extend_column1,1,2) in (''AF'',''FO'')
     and substr(x.entry_type_code,1,2) not in (''DN'',''CN''))', '1', '2024-08-07 14:30:00', NULL, '2024-08-07 14:30:00', 1, '0', '1', '1',
     (SELECT biz_type_id FROM dm_conf_table WHERE biz_code = UPPER ( 'ACC_PAYMENT' )),
     '临分分出校验大保单是否存在，收付这边的 Policy_no 匹配风险费用表存在',
     (select cast(max(check_type) as INTEGER)-1 from dm_conf_checkrule where biz_type_id = (SELECT biz_type_id FROM dm_conf_table WHERE biz_code = UPPER ( 'ACC_PAYMENT' ))),
     NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2');


--合约分出数据合约号有效性校验规则
delete from dm_conf_checkrule where rule_code = 'ACC_PAYMENT_TREATY_OUT_TREATY_NO_VALID_IS';

INSERT INTO dm_conf_checkrule (config_rule_id, rule_code, rule_e_name, rule_l_name, rule_c_name, rule_config, rule_version, valid_date, invalid_date, col_id, opt_type, match_value, rule_sql, valid_is, create_time, creator_id, update_time, updator_id, rule_direction, rule_type_code, original_is, biz_type_id, remark, check_type, display_no, serial_no, checked_msg, audit_state, checked_time, checked_id, apply_model, rule_type)
VALUES
    (nextval('dm_seq_conf_checkrule'), 'ACC_PAYMENT_TREATY_OUT_TREATY_NO_VALID_IS',
     'Validity Of Contract Number For Contract Allocation', '合約分出合約號有效性', '合约分出合约号有效性',
     NULL, '1.00', '2024-08-07 14:30:00', '9999-12-31 00:00:00', NULL, '0', NULL,
     'select x.id from odsuser.ods_acc_payment x
     where
     x.treaty_no is null
     and left(x.extend_column1,2) in (''CP'',''QS'',''S1'',''S2'',''XP'',''SP'',''DC'',''AP'',''RP'',''X1'',''X2'',''X3'',''X4'',''X5'',''XX'')', '1', '2024-08-07 14:30:00', NULL, '2024-08-07 14:30:00', 1, '0', '1', '1',
     (SELECT biz_type_id FROM dm_conf_table WHERE biz_code = UPPER ( 'ACC_PAYMENT' )),
     '合约分出的数据合约号不可以为空',
     (select cast(max(check_type) as INTEGER)-1 from dm_conf_checkrule where biz_type_id = (SELECT biz_type_id FROM dm_conf_table WHERE biz_code = UPPER ( 'ACC_PAYMENT' ))),
     NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2');