/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-03-02 18:26:41
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-03-02 18:26:41<br/>
 * Description: LIC 计算结果明细(合同组维度，合约分入)<br/>
 * Table Name: ATR_BUSS_TI_LIC_ICG_CALC_DETAIL<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "LIC 计算结果明细(合同组维度)")
public class AtrBussLicIcgAmountVo implements Serializable {
    /**
     * Database column: ATR_BUSS_TI_LIC_ICG_CALC_DETAIL.ID
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: ATR_BUSS_TI_LIC_ICG_CALC_DETAIL.MAIN_ID
     * Database remarks: 结果表的ID
     */
    @ApiModelProperty(value = "结果表的ID", required = true)
    private Long mainId;

    private String icgNo;

    private String cfType;


    private String accYearMonth;

    @ApiModelProperty(value = "t-1估计_当期利率@t", required = false)
    private BigDecimal amountT1;

    @ApiModelProperty(value = "t-2估计_当期利率@t", required = false)
    private BigDecimal amountT2;

    @ApiModelProperty(value = "t-3估计_当期利率@t", required = false)
    private BigDecimal amountT3;

    @ApiModelProperty(value = "t-4估计_当期利率@t", required = false)
    private BigDecimal amountT4;

    @ApiModelProperty(value = "t-5估计_当期利率@t", required = false)
    private BigDecimal amountT5;

    @ApiModelProperty(value = "t-6估计_当期利率@t", required = false)
    private BigDecimal amountT6;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMainId() {
        return mainId;
    }

    public void setMainId(Long mainId) {
        this.mainId = mainId;
    }

    public String getIcgNo() {
        return icgNo;
    }

    public void setIcgNo(String icgNo) {
        this.icgNo = icgNo;
    }

    public String getCfType() {
        return cfType;
    }

    public void setCfType(String cfType) {
        this.cfType = cfType;
    }

    public String getAccYearMonth() {
        return accYearMonth;
    }

    public void setAccYearMonth(String accYearMonth) {
        this.accYearMonth = accYearMonth;
    }

    public BigDecimal getAmountT1() {
        return amountT1;
    }

    public void setAmountT1(BigDecimal amountT1) {
        this.amountT1 = amountT1;
    }

    public BigDecimal getAmountT2() {
        return amountT2;
    }

    public void setAmountT2(BigDecimal amountT2) {
        this.amountT2 = amountT2;
    }

    public BigDecimal getAmountT3() {
        return amountT3;
    }

    public void setAmountT3(BigDecimal amountT3) {
        this.amountT3 = amountT3;
    }

    public BigDecimal getAmountT4() {
        return amountT4;
    }

    public void setAmountT4(BigDecimal amountT4) {
        this.amountT4 = amountT4;
    }

    public BigDecimal getAmountT5() {
        return amountT5;
    }

    public void setAmountT5(BigDecimal amountT5) {
        this.amountT5 = amountT5;
    }

    public BigDecimal getAmountT6() {
        return amountT6;
    }

    public void setAmountT6(BigDecimal amountT6) {
        this.amountT6 = amountT6;
    }
}