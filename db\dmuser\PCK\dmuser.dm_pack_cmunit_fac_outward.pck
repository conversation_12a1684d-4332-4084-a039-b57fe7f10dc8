CREATE OR REPLACE PACKAGE dm_pack_cmunit_fac_outward IS

  PROCEDURE proc_cmunit_identify_all(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2);

  PROCEDURE proc_cmunit_identify(p_entity_id  NUMBER,
                                 p_year_month VARCHAR2);

  PROCEDURE proc_majorrisk_test(p_entity_id  NUMBER,
                                p_year_month VARCHAR2);

  PROCEDURE proc_majorrisk_test_nopass(p_entity_id  NUMBER,
                                       p_year_month VARCHAR2);

  PROCEDURE proc_approach_discern(p_entity_id  NUMBER,
                                  p_year_month VARCHAR2);

  PROCEDURE proc_portfolio_discern(p_entity_id  NUMBER,
                                   p_year_month VARCHAR2);

  PROCEDURE proc_profit_loss_discern(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2);

  /*PROCEDURE proc_icg_fixed(p_entity_id  NUMBER,
                           p_year_month VARCHAR2);*/

  PROCEDURE proc_icg_discern(p_entity_id  NUMBER,
                             p_year_month VARCHAR2);

  PROCEDURE proc_icg_group(p_entity_id  NUMBER,
                           p_year_month VARCHAR2);

  PROCEDURE proc_investment_separate(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2);

  PROCEDURE proc_contract_group_confirm(p_entity_id  NUMBER,
                                        p_year_month VARCHAR2);

END dm_pack_cmunit_fac_outward;
/
CREATE OR REPLACE PACKAGE BODY dm_pack_cmunit_fac_outward IS

  FUNCTION func_get_current_year_month(p_entity_id  NUMBER,
                                       p_year_month VARCHAR2) RETURN VARCHAR2 IS
  BEGIN
    RETURN dm_pack_buss_period.func_get_current_year_month(p_entity_id, p_year_month, 'BUSS_CMUNIT_FAC_OUT');

  END func_get_current_year_month;

  FUNCTION func_get_valid_year_month(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2) RETURN VARCHAR2 IS
  BEGIN
    RETURN dm_pack_buss_period.func_get_valid_year_month(p_entity_id, p_year_month, 'BUSS_CMUNIT_FAC_OUT');

  END func_get_valid_year_month;

  PROCEDURE proc_cmunit_identify_all(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2) IS

    cur_yearmonth VARCHAR2(200);
  BEGIN

    IF p_year_month IS NULL OR length(p_year_month) = 0 THEN

      FOR cur_yearmonth IN (SELECT year_month
                              FROM dm_conf_bussperiod
                             WHERE entity_id = p_entity_id
                               AND period_state IN ('0', '1', '2')
                             ORDER BY year_month) LOOP
        -- 临分分出 - 生成计量单元
        proc_cmunit_identify(p_entity_id, cur_yearmonth.year_month);
      END LOOP;
    ELSE
      -- 临分分出 - 生成计量单元
      proc_cmunit_identify(p_entity_id, p_year_month);
    END IF;

  END proc_cmunit_identify_all;


  PROCEDURE proc_cmunit_identify(p_entity_id  NUMBER,
                                 p_year_month VARCHAR2) IS

    v_proc_id                 NUMBER;
    v_end_date                DATE;
    v_short_risk_flag_open_is VARCHAR(1) := '0';
    v_count                   NUMBER;
    v_tic_code                VARCHAR2(50);
    v_error_msg               varchar2(2000);
    v_tic_code_conf           varchar2(100);
  BEGIN

    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][临分分出-合同分组]计量单元划分-传参不为空：proc_cmunit_identify(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;
    IF func_get_valid_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][临分分出-合同分组]计量单元划分-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

     SELECT COUNT(*)
      INTO v_count
      FROM bpluser.bpl_conf_code
     WHERE code_code = 'ShortRiskFlagOpenIs'
       AND upper_code_id = 0
       AND valid_is = '1'
       AND rownum = 1;

    IF v_count > 0 THEN
      --长短险标识生成是否开启 0-不开启
      SELECT valid_is
        INTO v_short_risk_flag_open_is
        FROM bpluser.bpl_conf_code
       WHERE code_code = 'ShortRiskFlagOpenIs'
         AND upper_code_id = 0
         AND valid_is = '1'
         AND rownum = 1;

    END IF;


    IF v_short_risk_flag_open_is IS NULL THEN
      v_short_risk_flag_open_is := '0';
    END IF;

    v_end_date := add_months(to_date(p_year_month, 'yyyymm'), 1);
    --根据PROC_CODE查询PROC_ID
    v_proc_id := dm_pack_common.func_get_procid('DM_UNIT_RECOGNIZER');

    v_tic_code := 'TicCode/';
    
    --查询适配tic_code的值
    select code_code into v_tic_code_conf
     from bpluser.bpl_conf_code 
           where upper_code_id = (select code_id from bpluser.bpl_conf_code where upper_code_id = 0 and code_code = 'TicCodeConf' )
                and rownum = 1 ;

    --计量单元信息
    --直保业务转临分分出\临分分入业务转临分分出-----------------------------
    INSERT INTO dm_buss_cmunit_fac_outwards
      (cm_unit_outwards_id,
       cmunit_no,
       entity_id,
       ri_direction_code,
       fac_no,
       policy_no,
       short_risk_flag, --长短险标志
       policy_type_code, --保单类型
       product_code,
       treaty_type_code, --合约类型
       ri_class_code, --再保大类
       ri_risk_code, --再保小类
       risk_code, --险种代码
       risk_class_code,
       --ri_ceding_ratio,--分出比例
       related_is, --是否关联交易
       related_party, --关联交易方
       draw_type,
       proc_id,
       node_state,
       buss_year_month, --业务年月
       create_time,
       business_source_code,
       company_in_is,
       reinsurer_code,
       reinsurer_type_code,
       approval_date,
       effective_date,
       issue_date,
       loa_code,
       offshore_is,
       tic_code)
      SELECT dm_seq_buss_cmunit_fac_outward.nextval cm_unit_outwards_id,
             cmunit_no,
             entity_id,
             ri_direction_code,
             fac_no,
             policy_no,
             short_risk_flag, --长短险标志
             policy_type_code, --保单类型
             product_code,
             treaty_type_code, --合约类型
             ri_class_code, --再保大类
             ri_risk_code, --再保小类
             risk_code, --险种代码
             risk_class_code,
             --ri_ceding_ratio,--分出比例
             related_is, --是否关联交易
             related_party, --关联交易方
             draw_type,
             proc_id,
             node_state,
             buss_year_month, --业务年月
             create_time,
             business_source_code,
             company_in_is,
             reinsurer_code,
             reinsurer_type_code,
             approval_date,
             effective_date,
             issue_date,
             loa_code,
             offshore_is,
             (case when substr(product_code,0,2) = v_tic_code_conf then
                   tic_code
             else
               null
             end )as tic_code
        FROM (SELECT row_number() over(PARTITION BY rfs.entity_id,rfs.ri_policy_no ORDER BY 1) rn,
                     --DISTINCTON(RFS.entity_id,SUBSTRING(RFS.ri_policy_no,0,( CASE WHEN POSITION ( '_' IN RFS.ri_policy_no ) > 0 THEN POSITION ( '_' IN RFS.ri_policy_no ) ELSE CHAR_LENGTH ( RFS.ri_policy_no ) + 1  END ) ) ,RFS.RISK_CODE)
                     --NEXTVAL('dm_seq_buss_cmunit_fac_outwards') CM_UNIT_OUTWARDS_ID,
                     dm_pack_cmunit_common.func_get_ri_cmunitno(rfs.entity_id, 'F', 'O') cmunit_no,
                     rfs.entity_id,
                     rfs.ri_direction_code,
                     --SUBSTRING(RFS.ri_policy_no,0,( CASE WHEN POSITION ( '_' IN RFS.ri_policy_no ) > 0 THEN POSITION ( '_' IN RFS.ri_policy_no ) ELSE CHAR_LENGTH ( RFS.ri_policy_no ) + 1  END ) )  FAC_NO,
                     rfs.ri_policy_no AS fac_no,
                     rfsd.policy_no,
                     (CASE
                       WHEN v_short_risk_flag_open_is = '1' THEN
                        (CASE
                          WHEN rfs.business_source_code = 'DB'
                               OR rfs.business_source_code = 'FB' THEN
                           (SELECT (CASE
                                   --WHEN PM.EFFECTIVE_DATE + INTERVAL '1 year' > PM.EXPIRY_DATE  THEN
                                     WHEN add_months(pm.effective_date, 12) > pm.expiry_date THEN
                                      '1' -- 短险
                                     ELSE
                                      '2' -- 长险
                                   END)
                              FROM dm_policy_main pm
                             WHERE pm.entity_id = rfs.entity_id
                               AND pm.policy_no = rfsd.policy_no
                               AND pm.endorse_seq_no = '000'
                               AND rownum = 1)
                       ELSE
                        '0'
                        END)
                     ELSE
                        '0'
                     END) AS short_risk_flag, --长短险标志
                     CASE
                       WHEN rfsd.endorse_seq_no = '000' THEN
                        'N'
                       ELSE
                        'E'
                     END policy_type_code, --policy_type_code
                     (SELECT (CASE
                               WHEN rfs.business_source_code = 'DB'
                                    OR rfs.business_source_code = 'FB' THEN
                                (SELECT product_code
                                   FROM dm_policy_premium pm
                                  WHERE pm.entity_id = rfs.entity_id
                                    AND pm.policy_no = rfsd.policy_no
                                    AND pm.endorse_seq_no = '000'
                                    AND rownum = 1)
                             END)
                        FROM dual) product_code, --产品代码
                     t1.treaty_type_code, --合约类型
                     NULL ri_class_code, -- 再保大类
                     rfsd.ri_risk_code ri_risk_code, --再保小类
                     NULL AS risk_code, --险种代码
                     NULL AS risk_class_code,
                     --RFS.RISK_CODE,--险种代码
                     --(SELECT BR.risk_class_code FROM DM_BASE_RISK BR WHERE BR.RISK_CODE =  RFS.RISK_CODE AND ROWNUM = 1) risk_class_code,--產品風險大類代碼
                     --RFS.ri_ceding_ratio,--分出比例
                     (CASE
                       WHEN (SELECT MAX(company_in_is)
                               FROM dm_reins_reinsurer res
                              WHERE rfs.entity_id = res.entity_id
                                AND rfsd.reinsurer_code = res.reinsurer_code) = '1' THEN
                        '1'
                       ELSE
                        '0'
                     END) related_is, -- 是否关联交易
                     (CASE
                       WHEN (SELECT MAX(company_in_is)
                               FROM dm_reins_reinsurer res
                              WHERE rfs.entity_id = res.entity_id
                                AND rfsd.reinsurer_code = res.reinsurer_code) = '1' THEN
                        (SELECT related_party
                           FROM dm_reins_treaty rts
                          WHERE rfs.entity_id = rts.entity_id
                            AND rfsd.treaty_no = rts.treaty_no
                            AND rownum = 1)
                       ELSE
                        ''
                     END) related_party, --关联交易方
                     rfs.draw_type,
                     v_proc_id proc_id,
                     '1' node_state,
                     p_year_month AS buss_year_month, --业务年月
                     localtimestamp create_time,
                     rfs.business_source_code,
                     (CASE
                       WHEN (SELECT rr.company_in_is
                               FROM dm_reins_reinsurer rr
                              WHERE rfs.entity_id = rr.entity_id
                                AND rfsd.treaty_no = rr.treaty_no
                                AND rr.reinsurer_code = rfsd.reinsurer_code
                                AND rownum = 1) = '1' THEN
                        'Y'
                       ELSE
                        'N'
                     END) company_in_is, --Y司内N司外
                     rfsd.reinsurer_code,
                     (SELECT rr.reinsurer_type_code
                        FROM dm_reins_reinsurer rr
                       WHERE rfs.entity_id = rr.entity_id
                         AND rfsd.treaty_no = rr.treaty_no
                         AND rr.section_no_code = rfsd.section_no_code
                         AND rownum = 1) AS reinsurer_type_code,
                     rfs.approval_date,
                     rfs.effective_date,
                     rfs.issue_date,
                     (case when substr((SELECT (CASE WHEN rfs.business_source_code = 'DB' OR rfs.business_source_code = 'FB' THEN
                                                                                      (SELECT product_code FROM dm_policy_premium pm
                                                                                        WHERE pm.entity_id = rfs.entity_id
                                                                                          AND pm.policy_no = rfsd.policy_no
                                                                                          AND pm.endorse_seq_no = '000'
                                                                                          AND rownum = 1)
                                                                                   END)
                                                                              FROM dual),0,2) = v_tic_code_conf  then
                       (SELECT MIN(loa.loa_code)
                        FROM bpluser.bbs_conf_loa        loa,
                             bpluser.bbs_conf_loa_detail loat,
                             bpluser.bbs_conf_loa_tic tic
                       WHERE loa.loa_id = loat.loa_id
                         and loa.loa_id = tic.loa_id
                         and tic.tic_id = (select code_id from bpluser.bpl_conf_code where
                                        upper_code_id = (select code_id from bpluser.bpl_conf_code where upper_code_id = 0 and code_code = 'TicCode' )
                                        and code_code = rfsd.tic_code )
                         AND loat.business_id = (SELECT MAX(product_id)
                                                   FROM bpluser.bbs_conf_product bcp
                                                  WHERE bcp.entity_id = loa.entity_id
                                                    AND bcp.product_code = (SELECT (CASE WHEN rfs.business_source_code = 'DB' OR rfs.business_source_code = 'FB' THEN
                                                                                      (SELECT product_code FROM dm_policy_premium pm
                                                                                        WHERE pm.entity_id = rfs.entity_id
                                                                                          AND pm.policy_no = rfsd.policy_no
                                                                                          AND pm.endorse_seq_no = '000'
                                                                                          AND rownum = 1)
                                                                                   END)
                                                                              FROM dual))
                         AND loa.entity_id = rfs.entity_id
                         AND loa.business_model = 'D'
                         AND loa.business_direction = 'D'
                         and loa.AUDIT_STATE = '1'
                         and loa.valid_is = '1')
                       else
                       (SELECT MIN(loa.loa_code)
                        FROM bpluser.bbs_conf_loa        loa,
                             bpluser.bbs_conf_loa_detail loat
                       WHERE loa.loa_id = loat.loa_id
                         AND loat.business_id = (SELECT MAX(product_id)
                                                   FROM bpluser.bbs_conf_product bcp
                                                  WHERE bcp.entity_id = loa.entity_id
                                                    AND bcp.product_code = (SELECT (CASE WHEN rfs.business_source_code = 'DB' OR rfs.business_source_code = 'FB' THEN
                                                                                      (SELECT product_code FROM dm_policy_premium pm
                                                                                        WHERE pm.entity_id = rfs.entity_id
                                                                                          AND pm.policy_no = rfsd.policy_no
                                                                                          AND pm.endorse_seq_no = '000'
                                                                                          AND rownum = 1)
                                                                                   END)
                                                                              FROM dual))
                         AND loa.entity_id = rfs.entity_id
                         AND loa.business_model = 'D'
                         AND loa.business_direction = 'D'
                         and loa.AUDIT_STATE = '1'
                         and loa.valid_is = '1')
                       end) AS loa_code,
                       (select offshore_is from dm_policy_main pm where pm.policy_no = rfsd.policy_no and rfs.entity_id = pm.entity_id and rownum = 1) as offshore_is,
                       rfsd.tic_code
                FROM dm_reins_outward rfs
                join dm_reins_outward_detail rfsd
                    on rfs.entity_id = rfsd.entity_id
                    and rfs.ri_policy_no = rfsd.ri_policy_no
                    and rfs.ri_endorse_seq_no = rfs.ri_endorse_seq_no
                JOIN dm_reins_treaty t1
                  ON t1.treaty_no = rfsd.treaty_no
                 AND t1.entity_id = rfs.entity_id
                 AND t1.treaty_type_code IN ('91', '92')
                 and t1.ri_direction_code = rfs.ri_direction_code
               WHERE rfs.entity_id = p_entity_id
                 AND rfs.task_status = '4'
                 AND rfs.ri_direction_code = 'O'
                    --AND RFS.business_source_code = V_business_source_code
                 AND rfsd.treaty_no IS NOT NULL
                 AND rfs.ri_policy_no IS NOT NULL
                 AND rfsd.policy_no IS NOT NULL
                 AND rfsd.endorse_seq_no IS NOT NULL
                 AND rfsd.risk_code IS NOT NULL
                    --AND T1.issue_date BETWEEN TO_DATE(P_YEAR_MONTH,'yyyymm') AND V_END_DATE;
                    --对于未处理成功的历史数据都归入当前进行处理
                 --AND rfs.approval_date < v_end_date 采用签发日期作为生成标识 lgx:20230621
                 AND rfs.issue_date < v_end_date
                 AND NOT EXISTS (SELECT cm_unit_outwards_id
                        FROM dm_buss_cmunit_fac_outwards cm1
                       WHERE cm1.entity_id = rfs.entity_id
                         AND cm1.fac_no = rfs.ri_policy_no
                      --AND CM1.RISK_CODE = RFS.RISK_CODE
                      --确保不重复生成计量单元(校验规则未成功校验数据重复时)
                      )
                 and exists (select 1 from bpluser.BBS_CONF_FEE_TYPE_MAPPING bcfe
                     where bcfe.expenses_type_code = rfsd.expenses_type_code
                     and bcfe.FEE_CLASS = 'Premium'
                     and bcfe.business_source_code = 'FO'
                     )
               ORDER BY rfs.entity_id,rfs.ri_policy_no,rfs.ri_endorse_seq_no) g
       WHERE rn = 1;
    COMMIT;

    ---------------------------------
    --loa配置重新配置
    --只有处理中的才能重新执行

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NOT NULL THEN
      UPDATE dm_buss_cmunit_fac_outwards cm
         SET node_state           = '1',
             proc_id              = v_proc_id,
             major_risk           = NULL,
             evaluate_approach    = NULL, --评估方法
             portfolio_no         = NULL,
             pl_judge_rslt        = NULL, --盈亏
             pl_judge_date        = NULL, --盈亏时间
             year_month           = NULL,
             border_date          = NULL,
             icg_no               = NULL,
             invest_rate          = NULL,
             invest_amount        = NULL,
             reason_of_failure    = NULL,
             reason_of_mr_failure = NULL,
             exception_message    = NULL,
             loa_code             =(case when substr((SELECT (CASE WHEN cm.business_source_code = 'DB' OR cm.business_source_code = 'FB' THEN
                                                                    (SELECT product_code FROM dm_policy_premium pm
                                                                      WHERE pm.entity_id = cm.entity_id
                                                                        AND pm.policy_no = cm.policy_no
                                                                        AND pm.endorse_seq_no = '000'
                                                                        AND rownum = 1)
                                                                 END)
                                                            FROM dual),0,2) = v_tic_code_conf
                                        then (SELECT MIN(loa.loa_code)
                                          FROM bpluser.bbs_conf_loa        loa,
                                               bpluser.bbs_conf_loa_detail loat,
                                               bpluser.bbs_conf_loa_tic tic
                                         WHERE loa.loa_id = loat.loa_id
                                           and loa.loa_id = tic.loa_id
                                           and tic.tic_id = (select code_id from bpluser.bpl_v_conf_code where code_code_idx = v_tic_code||cm.tic_code)
                                           AND loat.business_id = (SELECT MAX(product_id)
                                                                     FROM bpluser.bbs_conf_product bcp
                                                                    WHERE bcp.entity_id = loa.entity_id
                                                                      AND bcp.product_code = (SELECT (CASE
                                                                                                       WHEN cm.business_source_code = 'DB' OR cm.business_source_code = 'FB' THEN
                                                                                                        (SELECT product_code FROM dm_policy_premium pm
                                                                                                          WHERE pm.entity_id = cm.entity_id
                                                                                                            AND pm.policy_no = cm.policy_no
                                                                                                            AND pm.endorse_seq_no = '000'
                                                                                                            AND rownum = 1)
                                                                                                     END)
                                                                                                FROM dual))
                                           AND loa.entity_id = cm.entity_id
                                           AND loa.business_model = 'D'
                                           AND loa.business_direction = 'D'
                                           and loa.AUDIT_STATE = '1'
                                           and loa.valid_is = '1')
                                        else
                                            (SELECT MIN(loa.loa_code)
                                          FROM bpluser.bbs_conf_loa        loa,
                                               bpluser.bbs_conf_loa_detail loat
                                         WHERE loa.loa_id = loat.loa_id
                                           AND loat.business_id = (SELECT MAX(product_id)
                                                                     FROM bpluser.bbs_conf_product bcp
                                                                    WHERE bcp.entity_id = loa.entity_id
                                                                      AND bcp.product_code = (SELECT (CASE
                                                                                                       WHEN cm.business_source_code = 'DB'
                                                                                                            OR cm.business_source_code = 'FB' THEN
                                                                                                        (SELECT product_code
                                                                                                           FROM dm_policy_premium pm
                                                                                                          WHERE pm.entity_id = cm.entity_id
                                                                                                            AND pm.policy_no = cm.policy_no
                                                                                                            AND pm.endorse_seq_no = '000'
                                                                                                            AND rownum = 1)
                                                                                                     END)
                                                                                                FROM dual))
                                           AND loa.entity_id = cm.entity_id
                                           AND loa.business_model = 'D'
                                           AND loa.business_direction = 'D'
                                           and loa.AUDIT_STATE = '1'
                                           and loa.valid_is = '1')
                                        end)

       WHERE year_month IS NULL
         AND buss_year_month <= p_year_month;
      --and loa_code is null;
      COMMIT;

    END IF;

    --未有loa配置提示异常
    UPDATE dm_buss_cmunit_fac_outwards cm
       SET major_risk           = NULL,
           evaluate_approach    = NULL, --评估方法
           portfolio_no         = NULL,
           pl_judge_rslt        = NULL, --盈亏
           pl_judge_date        = NULL, --盈亏时间
           year_month           = NULL,
           border_date          = NULL,
           icg_no               = NULL,
           invest_rate          = NULL,
           invest_amount        = NULL,
           node_state           = '2', -- 节点状态
           proc_id              = v_proc_id,
           reason_of_failure    = 'DM_loa',
           reason_of_mr_failure = 'DM_loa',
           exception_message    = 'not find loa config' --异常信息
     WHERE year_month IS NULL
       AND buss_year_month <= p_year_month
       AND loa_code IS NULL;
    COMMIT;

    ---------------------------------

    -- 修改已生成计量单元标识
    UPDATE dm_reins_outward rfs
       SET task_status = '5'
     WHERE EXISTS (SELECT 1
              FROM dm_buss_cmunit_fac_outwards cm1
             WHERE cm1.entity_id = rfs.entity_id
                  --AND COALESCE(G.FAC_NO,'') = COALESCE(T.ri_policy_no,'')
                  --AND CM1.FAC_NO = SUBSTRING(RFS.ri_policy_no,0,( CASE WHEN POSITION ( '_' IN RFS.ri_policy_no ) > 0 THEN POSITION ( '_' IN RFS.ri_policy_no ) ELSE CHAR_LENGTH ( RFS.ri_policy_no ) + 1  END ) )
               AND cm1.fac_no = rfs.ri_policy_no
               AND rfs.ri_policy_no IS NOT NULL
            --AND CM1.RISK_CODE = RFS.RISK_CODE
            )
       AND rfs.entity_id = p_entity_id
       AND rfs.task_status = '4'
       and exists (select * from dm_reins_outward_detail rfsd
           where rfsd.entity_id = rfs.entity_id and rfsd.ri_policy_no = rfs.ri_policy_no
             and rfsd.ri_endorse_seq_no = rfs.ri_endorse_seq_no
             AND rfsd.treaty_no IS NOT NULL)
       AND rfs.ri_policy_no IS NOT NULL;
    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN

       --意外处理
      v_error_msg := '[EXCEPTION][临分分出-合同分组]计量单元划分:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_cmunit_identify;

  PROCEDURE proc_majorrisk_test(p_entity_id  NUMBER,
                                p_year_month VARCHAR2) IS
    v_proc_id     NUMBER;
    v_error_msg               varchar2(2000);
    v_count     NUMBER;
  BEGIN
    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][临分分出-合同分组]重测处理-传参不为空：proc_majorrisk_test(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][直保/临分分入-合同分组]重测处理-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);


    END IF;

    -- 当前业务年月存在未进行重大风险测试的数据
    -- 根据proc_code查询proc_id
    v_proc_id := dm_pack_common.func_get_procid('DM_RISK_TEST');
    IF v_proc_id IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][临分分出-合同分组]重测处理-获取流程节点(v_proc_id)为空';
      raise_application_error(-20002,v_error_msg);

    END IF;
    
    --取再保前的重测结果
    
    --适配再保前计量单元的重测结果
    MERGE INTO dm_buss_cmunit_fac_outwards A
            USING (SELECT CM.CM_UNIT_OUTWARDS_ID,
                          V_PROC_ID AS PROC_ID,
                          (CASE
                              WHEN cmd.MAJOR_RISK IS NULL THEN
                               '2'
                              ELSE
                               '1'
                          END) AS NODE_STATE,
                          cmd.MAJOR_RISK AS MAJOR_RISK,
                          (CASE
                              WHEN cmd.MAJOR_RISK IS NULL THEN
                               'DM_001'
                              ELSE
                               NULL
                          END) AS REASON_OF_FAILURE,
                          (CASE
                              WHEN cmd.MAJOR_RISK IS NULL THEN
                               'DM_001'
                              ELSE
                               NULL
                          END) AS REASON_OF_MR_FAILURE,
                          (CASE
                              WHEN cmd.MAJOR_RISK IS NULL THEN
                               'DM_001'
                              ELSE
                               NULL
                          END) AS EXCEPTION_MESSAGE
                     FROM dm_buss_cmunit_fac_outwards CM
                     LEFT JOIN dm_buss_cmunit_direct cmd
                       ON CM.ENTITY_ID = cmd.ENTITY_ID
                      AND CM.policy_no = cmd.policy_no
                    WHERE CM.ENTITY_ID = p_entity_id
                      AND CM.BUSS_YEAR_MONTH <= p_year_month
                      AND CM.YEAR_MONTH IS NULL) B
            ON (A.CM_UNIT_OUTWARDS_ID = B.CM_UNIT_OUTWARDS_ID)
            WHEN MATCHED THEN
                UPDATE
                   SET PROC_ID = B.PROC_ID, 
                   NODE_STATE = B.NODE_STATE, 
                   MAJOR_RISK = B.MAJOR_RISK, 
                   EVALUATE_APPROACH = null, 
                   PORTFOLIO_NO = null, 
                   PL_JUDGE_RSLT = null, 
                   PL_JUDGE_DATE = null, 
                   BORDER_DATE = null, 
                   ICG_NO = null, 
                   INVEST_RATE = null, 
                   INVEST_AMOUNT = null,
                   REASON_OF_FAILURE = B.REASON_OF_FAILURE, 
                   REASON_OF_MR_FAILURE = B.REASON_OF_MR_FAILURE,
                    EXCEPTION_MESSAGE = B.EXCEPTION_MESSAGE;

    COMMIT;

    proc_majorrisk_test_nopass(p_entity_id, p_year_month);
  EXCEPTION
    WHEN OTHERS THEN

      --意外处理
      -- 执行异常，记录异常原因
      v_error_msg := '[EXCEPTION][临分分出-合同分组]重测处理:'||SQLERRM;
        --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_majorrisk_test;

  PROCEDURE proc_majorrisk_test_nopass(p_entity_id  NUMBER,
                                       p_year_month VARCHAR2) IS
    v_portfolio_column   VARCHAR(4000);
    v_icg_column         VARCHAR(4000);
    v_portfolio_proc_id  INTEGER;
    v_icg_proc_id        NUMBER;
    v_num                NUMBER;
    v_error_msg          varchar2(2000);
  BEGIN

    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][临分分出-合同分组]重测处理不通过-传参不为空：proc_majorrisk_test_nopass(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][临分分出-合同分组]重测处理不通过-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 是否存在重测业务不通过及未生成合同组数据
    SELECT COUNT(1)
      INTO v_num
      FROM dm_buss_cmunit_fac_outwards
     WHERE entity_id = p_entity_id
       AND buss_year_month <= p_year_month
       AND major_risk = 'O' -- 重测业务不通过
       AND icg_no IS NULL -- 未生成合同组
       AND rownum = 1;

    IF v_num IS NULL THEN

       return;

    END IF;

    v_portfolio_column := dm_pack_cmunit_common.func_get_contractcode(p_entity_id, 'G', 'FO');
    -- 根据PROC_CODE查询PROC_ID
    v_portfolio_proc_id := dm_pack_common.func_get_procid('DM_CONTRACTGROUP');

    IF v_portfolio_column IS NOT NULL THEN
      -- 重测业务不通过，生成合同组
      v_icg_column := dm_pack_cmunit_common.func_get_contractcode(p_entity_id, 'C', 'FO');
      -- 根据PROC_CODE查询PROC_ID
      v_icg_proc_id := dm_pack_common.func_get_procid('DM_CONTRACTGROUP');
    END IF;

    -- 1,评估方法及盈亏判定设置为 D-不区分
    -- 2,生成合同组合编码
    -- 3,更新合同组编码
    -- 4,《确认日期》为确认不通过的业务期间月末

    EXECUTE IMMEDIATE ('update  dm_buss_cmunit_fac_outwards t'
                      ||' set evaluate_approach = ''D'''
                      ||', pl_judge_rslt = ''D'''
                      ||', portfolio_no = ' || v_portfolio_column
                      ||', icg_no = ' || v_icg_column
                      ||', border_date = ' || (CASE WHEN v_icg_column IS NOT NULL THEN 'last_day(to_date('''||p_year_month||'01'',''YYYYMMDD''))' END)
                      ||', proc_id = ' || (CASE WHEN v_portfolio_column IS NULL THEN v_portfolio_proc_id ELSE v_icg_proc_id END)
                      ||', node_state = '''|| (CASE WHEN v_portfolio_column IS NULL OR v_icg_column IS NULL THEN '2' ELSE '1' END)||''' '
                      ||', reason_of_failure = ''' || (CASE WHEN v_portfolio_column IS NULL OR v_icg_column IS NULL THEN 'DM_003' ELSE NULL END) ||''''
                      ||', reason_of_mr_failure = ''' || (CASE WHEN v_portfolio_column IS NULL OR v_icg_column IS NULL THEN 'DM_003' ELSE NULL END) ||''''
                      ||' where entity_id = ' || p_entity_id
                      ||' and major_risk = ''O'' '
                      --||' and evaluate_approach is not null '
                      -- ' and pl_judge_rslt is not null ' ||
                      --||' and  portfolio_no is null '
                      ||' and year_month is null  '
                      ||' and buss_year_month <= ''' || p_year_month || ''' ');
    COMMIT;

    -- 根据PROC_CODE查询PROC_ID
    /*   V_PROC_ID := dm_pack_common.func_get_procid('DM_INVESTMENT_COST');

    UPDATE DM_BUSS_CMUNIT_FAC_OUTWARDS T
    SET (INVEST_RATE,
        INVEST_AMOUNT,
        PROC_ID,
        NODE_STATE,
        REASON_OF_FAILURE) =
        (SELECT COALESCE(TO_NUMBER(BCQ.QUOTA_VALUE,'999.999999'),0) INVEST_RATE,
                COALESCE((SELECT SUM(RFS.AMOUNT) FROM DM_REINS_OUTWARD RFS WHERE RFS.entity_id = CMOUT.entity_id AND RFS.POLICY_NO = CMOUT.POLICY_NO AND RFS.RISK_CODE = CMOUT.RISK_CODE AND RFS.issue_date <= V_END_DATE),0)*COALESCE(TO_NUMBER(BCQ.QUOTA_VALUE,'999.999999'),0) INVEST_AMOUNT,
                V_PROC_ID PROC_ID,
                '1' NODE_STATE,
                NULL REASON_OF_FAILURE
         FROM
                BPLUSER.BBS_CONF_QUOTA BCQ,
                DM_BUSS_CMUNIT_FAC_OUTWARDS CMOUT
         WHERE
          BCQ.QUOTA_DEF_ID = ( SELECT QUOTA_DEF_ID FROM BPLUSER.BBS_CONF_QUOTA_DEF WHERE QUOTA_CODE = 'QR001' )--取投成拆分字段
          AND BCQ.BUSINESS_MODEL = 'D'--取再保临分的
          AND CMOUT.entity_id = BCQ.entity_id
          AND (
            (CMOUT.risk_class_code IS NOT NULL AND CMOUT.risk_class_code = BCQ.risk_class_code)
            OR
            (SELECT R.risk_class_code AS risk_class_code FROM BPLUSER.BBS_CONF_RISK R WHERE R.RISK_CODE = CMOUT.RISK_CODE
              AND CMOUT.entity_id = R.entity_id
              AND R.VALID_IS = '1'
              AND R.AUDIT_STATE = '1'
              AND ROWNUM = 1) = BCQ.risk_class_code
          )
          AND BCQ.VALID_IS = '1'
          AND BCQ.AUDIT_STATE = '1'
          AND CMOUT.MAJOR_RISK = 'O'
          AND CMOUT.EVALUATE_APPROACH IS NOT NULL
          AND CMOUT.PL_JUDGE_RSLT IS NOT NULL
          AND CMOUT.BORDER_DATE IS NOT NULL
          AND CMOUT.ICG_NO IS NOT NULL
          --AND CMOUT.INVEST_RATE IS NULL
          --AND CMOUT.INVEST_AMOUNT IS NULL
          AND CMOUT.entity_id = p_entity_id
          AND CMOUT.YEAR_MONTH IS NULL
          AND CMOUT.BUSS_YEAR_MONTH <= P_YEAR_MONTH
            ) ;

    --临分分出未配置投成拆分规则
    UPDATE DM_BUSS_CMUNIT_FAC_OUTWARDS CMOUT
    SET PROC_ID = V_PROC_ID,NODE_STATE= '2',REASON_OF_FAILURE = 'DM_001'
    WHERE CMOUT.MAJOR_RISK = 'O'
      AND CMOUT.EVALUATE_APPROACH IS NOT NULL
      AND CMOUT.PL_JUDGE_RSLT IS NOT NULL
      AND CMOUT.BORDER_DATE IS NOT NULL
      AND CMOUT.ICG_NO IS NOT NULL
      AND CMOUT.INVEST_RATE IS NULL
      AND CMOUT.INVEST_AMOUNT IS NULL
      AND CMOUT.entity_id = p_entity_id
      AND CMOUT.YEAR_MONTH IS NULL
      AND CMOUT.BUSS_YEAR_MONTH <= P_YEAR_MONTH;*/

  EXCEPTION
    WHEN OTHERS THEN

      -- 执行异常，记录异常原因
      --意外处理
      v_error_msg := '[EXCEPTION][临分分出-合同分组]重测处理:'||SQLERRM;

      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_majorrisk_test_nopass;

  PROCEDURE proc_approach_discern(p_entity_id  NUMBER,
                                  p_year_month VARCHAR2) IS
    v_proc_id     NUMBER;
    v_error_msg   varchar2(2000);

  BEGIN
    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][临分分出-合同分组]评估方法适配-传参不为空：proc_approach_discern(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][临分分出-合同分组]评估方法适配-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 根据proc_code查询proc_id
    v_proc_id := dm_pack_common.func_get_procid('DM_EVALDEF_CONFIG');

    UPDATE dm_buss_cmunit_fac_outwards t
       SET (evaluate_approach,
            proc_id,
            portfolio_no,
            pl_judge_rslt,--盈亏
            pl_judge_date,--盈亏时间
            year_month,
            border_date,
            icg_no,
            invest_rate,
            invest_amount,
            node_state,
            reason_of_failure) ---不通过原因设置为数据配置异常
            = (SELECT (CASE
                     WHEN (ce.audit_state = '1' AND ce.valid_is = '1') THEN
                      ce.evaluate_approach
                     ELSE
                      NULL
                   END) AS evaluate_approach,
                   v_proc_id AS proc_id,
                   NULL AS portfolio_no,
                   NULL AS pl_judge_rslt,
                   NULL AS pl_judge_date,
                   NULL AS year_month,
                   NULL AS border_date,
                   NULL AS icg_no,
                   NULL AS invest_rate,
                   NULL AS invest_amount,
                   (CASE
                     WHEN (ce.audit_state = '1' AND ce.valid_is = '1') THEN
                      '1'
                     ELSE
                      '2'
                   END) AS node_state,
                   (CASE
                     WHEN (ce.audit_state = '1' AND ce.valid_is = '1') THEN
                      NULL
                     ELSE
                      'DM_001'
                   END) AS reason_of_failure
              FROM bpluser.bbs_conf_loa ce
             WHERE ce.entity_id = t.entity_id
                --AND t.loa_code = (SELECT MIN(loa.loa_code) FROM bbs_conf_loa loa WHERE loa.loa_id = ce.loa_id)
               AND ce.loa_code = t.loa_code
               AND ce.business_model = 'D'
               AND ce.business_direction = 'D'
               and ce.AUDIT_STATE = '1'
               and ce.valid_is = '1'
               AND rownum = 1)
     WHERE t.entity_id = p_entity_id
       AND t.buss_year_month <= p_year_month
       AND t.major_risk IN ('Y', 'P')
       --AND t.evaluate_approach <> 'D'
       AND  t.year_month IS NULL
       AND t.loa_code IS NOT NULL;


           --(REASON_OF_FAILURE无规则)没有更新评估方法的字段表示找不到规则
    UPDATE dm_buss_cmunit_fac_outwards t
       SET evaluate_approach = NULL,
           proc_id           = v_proc_id,
           portfolio_no      = NULL,
           pl_judge_rslt     = NULL, --盈亏
           pl_judge_date     = NULL, --盈亏时间
           year_month        = NULL,
           border_date       = NULL,
           icg_no            = NULL,
           invest_rate       = NULL,
           invest_amount     = NULL,
           node_state        = '2',
           reason_of_failure = 'DM_001'
     WHERE t.entity_id = p_entity_id
       AND t.buss_year_month <= p_year_month
       AND t.major_risk IN ('Y', 'P')
       AND t.evaluate_approach is null
       AND t.year_month IS NULL;

    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN

      -- 执行异常，记录异常原因
      v_error_msg := '[EXCEPTION][临分分出-合同分组]评方法适配:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_approach_discern;

  PROCEDURE proc_portfolio_discern(p_entity_id  NUMBER,
                                   p_year_month VARCHAR2) IS

    v_column      VARCHAR2(4000);
    v_proc_id     INTEGER;
    v_count       NUMBER;
    v_error_msg   varchar2(2000);

  BEGIN
    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][临分分出-合同分组]合同组合划分-传参不为空：proc_portfolio_discern(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][临分分出-合同分组]合同组合划分-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 当前业务年月存在未进行评估方法的数据
    SELECT count(1)
      INTO v_count
      FROM dm_buss_cmunit_fac_outwards
     WHERE entity_id = p_entity_id
       AND buss_year_month <= p_year_month
       --AND major_risk IN ('Y', 'P')
       AND evaluate_approach IS NULL
       AND year_month IS NULL
       AND rownum = 1;

    IF v_count > 0 THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION]临分分出-合同分组]合同组合划分-当前业务年月存在未进行评估方法的数据';
      raise_application_error(-20002,v_error_msg);

    END IF;


    -- 根据proc_code查询proc_id(合同分组)
    v_proc_id := dm_pack_common.func_get_procid('DM_PORTFOLIO');

    -- 临分分出
    v_column := dm_pack_cmunit_common.func_get_contractcode(p_entity_id, 'G', 'FO');

    -- 更新合同组合编码
    --dbms_output.put_line(v_column);
    EXECUTE IMMEDIATE ('update  dm_buss_cmunit_fac_outwards t'
                      ||' set portfolio_no = ' || v_column
                      ||', proc_id = ' || v_proc_id
                      ||', pl_judge_rslt = null'
                      ||', pl_judge_date = null'
                      --||', year_month = null'
                      ||', border_date = null'
                      ||', icg_no = null'
                      ||', invest_rate = null'
                      ||', invest_amount = null'
                      ||', node_state = '''|| (CASE WHEN v_column IS NULL THEN '2' ELSE '1' END)||''' '
                      ||', reason_of_failure = ''' || (CASE WHEN v_column IS NULL THEN 'DM_003' ELSE NULL END) ||''''
                      ||' where entity_id = ' || p_entity_id
                      ||' and evaluate_approach <> ''D'''
                      --||' AND major_risk IN (''Y'', ''P'') '
                      ||' and year_month IS NULL '
                      ||' and buss_year_month <= ''' || p_year_month || ''' ');
    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN

      -- 执行异常，记录异常原因
      v_error_msg := '[EXCEPTION][临分分出-合同分组]合同组合划分:'||SQLERRM;
      --意外处理
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_portfolio_discern;

  PROCEDURE proc_profit_loss_discern(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2) IS

    v_proc_id     INTEGER;
    v_current_day DATE; -- 当前业务年月第一天
    v_error_msg   varchar2(2000);

  BEGIN
    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][临分分出-合同分组]盈亏判定-传参不为空：proc_profit_loss_discern(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

       --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][临分分出-合同分组]盈亏判定-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 根据proc_code查询proc_id
    v_proc_id := dm_pack_common.func_get_procid('DM_DETERMINATION');


    -- 盈亏取业务年月最一天作为盈亏判定时间
    v_current_day := last_day(to_date(p_year_month, 'yyyymm'));

    -- 临分分出：盈亏判定设定为不区分
    UPDATE dm_buss_cmunit_fac_outwards
       SET proc_id           = v_proc_id,
           node_state        = '1',
           reason_of_failure = NULL,
           pl_judge_date     = v_current_day,
           pl_judge_rslt     = 'D',
           year_month        = NULL,
           border_date       = NULL,
           icg_no            = NULL,
           invest_rate       = NULL,
           invest_amount     = NULL
     WHERE entity_id = p_entity_id
       AND buss_year_month <= p_year_month
       AND evaluate_approach <> 'D'
       --AND major_risk IN ('Y', 'P')
       AND portfolio_no IS NOT NULL
       AND year_month IS NULL; --未确认的评估期
    --and pl_judge_rslt is null;
    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN

      --意外处理
      v_error_msg := '[EXCEPTION][临分分出-合同分组]盈亏判定:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_profit_loss_discern;


  PROCEDURE proc_icg_fixed(p_entity_id  NUMBER,
                           p_year_month VARCHAR2) IS
    --v_proc_id    INTEGER; --当前节点
    v_end_date   TIMESTAMP; -- 当前业务年月的下个月第一天
    v_error_msg  varchar2(2000);

  BEGIN
    /*-- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      RETURN;
    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      RETURN;
    END IF;*/

    --v_proc_id := dm_pack_common.func_get_procid('DM_CONTRACTGROUP');
    v_end_date := add_months(to_date(p_year_month, 'yyyymm'), 1);
    
    --再保前：更新原批改序号，更新D1
    MERGE INTO dm_buss_cmunit_fac_outwards cma
    USING (SELECT c.CM_UNIT_OUTWARDS_ID,
                                      c.policy_no,
                                      MIN(pol.endorse_seq_no) AS endorse_seq_no,
                                      MIN(pol.approval_date) AS d1 --获取D1:核保通过日期      
                                 FROM dmuser.dm_buss_cmunit_fac_outwards c
                                 LEFT JOIN dmuser.dm_policy_main pol
                                   ON c.entity_id = pol.entity_id
                                  AND c.policy_no = pol.policy_no
                                  AND ((pol.endorse_effective_date IS NOT NULL AND pol.endorse_effective_date < v_end_date) --v_end_date下个月第一天
                                      OR (pol.endorse_effective_date IS NULL AND pol.approval_date < v_end_date)) --v_end_date下个月第一天 
                    WHERE c.entity_id = p_entity_id
                      AND c.pl_judge_rslt IS NOT NULL -- 盈亏判定
                      AND c.evaluate_approach <> 'D'
                      --AND c.pl_judge_rslt <> 'D'
                      AND c.year_month IS NULL -- 未生成合同组
                      AND c.buss_year_month <= p_year_month
                    GROUP BY c.CM_UNIT_OUTWARDS_ID,
                             c.policy_no) cmb
    ON (cma.CM_UNIT_OUTWARDS_ID = cmb.CM_UNIT_OUTWARDS_ID)
    WHEN MATCHED THEN
      UPDATE set endorse_seq_no = cmb.endorse_seq_no,
                 d1 = cmb.d1;
    COMMIT;
  
   --再保前：更新D3
   MERGE INTO dm_buss_cmunit_fac_outwards cma
    USING (SELECT c.CM_UNIT_OUTWARDS_ID AS CM_UNIT_OUTWARDS_ID,
                          c.policy_no AS policy_no,
                          c.endorse_seq_no AS endorse_seq_no,
                          MIN(prem.effective_date) AS d3 --D3:保险责任起期
                     FROM dmuser.dm_buss_cmunit_fac_outwards c
                     LEFT JOIN dmuser.dm_policy_premium prem
                       ON c.entity_id = prem.entity_id
                      AND c.policy_no = prem.policy_no
                      AND prem.endorse_seq_no = c.endorse_seq_no
                    WHERE c.entity_id = p_entity_id
                      AND c.pl_judge_rslt IS NOT NULL -- 盈亏判定
                      AND c.evaluate_approach <> 'D'
                      --AND c.pl_judge_rslt <> 'D'
                      AND c.year_month IS NULL -- 未生成合同组
                      AND c.buss_year_month <= p_year_month
                        GROUP BY c.CM_UNIT_OUTWARDS_ID,
                             c.policy_no,
                             c.endorse_seq_no) cmb
    ON (cma.CM_UNIT_OUTWARDS_ID = cmb.CM_UNIT_OUTWARDS_ID)
    WHEN MATCHED THEN
      UPDATE set d3 = cmb.d3;
    COMMIT;
   --再保前：更新D2
    MERGE INTO dm_buss_cmunit_fac_outwards cma
    USING (SELECT c.CM_UNIT_OUTWARDS_ID AS CM_UNIT_OUTWARDS_ID,
                          c.policy_no AS policy_no,
                          c.endorse_seq_no AS endorse_seq_no,
                          --MIN(pln.est_payment_date) AS est_payment_date,
                          --MIN(pay.actual_payment_date) AS actual_payment_date,
                          (CASE WHEN MIN(pln.est_payment_date) IS NULL THEN
                                null
                            else 
                              MIN(pln.est_payment_date)
                            end ) d2  --D2:首次付款到期日=MIN（最早预计缴费日期，最早的实际支付日期）
                     FROM dmuser.dm_buss_cmunit_fac_outwards c
                     LEFT JOIN dmuser.dm_policy_payment_plan pln
                       ON c.entity_id = pln.entity_id
                      AND c.policy_no = pln.policy_no
                      AND pln.endorse_seq_no = c.endorse_seq_no
                      AND pln.est_payment_seq_no = 1
                      --AND pln.est_payment_date < v_end_date --v_end_date下个月第一天 --不需要看是否存在评估期内，有数据就可以 2023/03/08
                      AND pln.policy_no IS NOT NULL
                    WHERE c.entity_id = p_entity_id
                      AND c.pl_judge_rslt IS NOT NULL -- 盈亏判定
                      AND c.evaluate_approach <> 'D'
                      --AND c.pl_judge_rslt <> 'D'
                      AND c.year_month IS NULL -- 未生成合同组
                      AND c.buss_year_month <= p_year_month
                    GROUP BY c.CM_UNIT_OUTWARDS_ID,
                             c.policy_no,
                             c.endorse_seq_no) cmb
    ON (cma.CM_UNIT_OUTWARDS_ID = cmb.CM_UNIT_OUTWARDS_ID)
    WHEN MATCHED THEN
      UPDATE set d2 = cmb.d2;
    COMMIT;

    --获取再保前数据，更新d5(这里的d1,d2,d3,d4是再保前的日期)
     MERGE INTO dm_buss_cmunit_fac_outwards cma
    USING (SELECT t.CM_UNIT_OUTWARDS_ID,
                  --t.policy_no,
                  --t.endorse_seq_no,
                  t.d1, --获取D1:核保通过日期
                  t.d3, --D3:保险责任起期
                  t.d2, --D2:首次付款到期日=MIN（最早预计缴费日期，最早的实际支付日期）
                  greatest((CASE
                               WHEN d2 IS NULL THEN
                                d3
                               WHEN d3 IS NULL THEN  --d3 is not null
                                d2
                               WHEN d2 > d3 THEN
                                d3
                               ELSE
                                d2
                             END), d1) AS d4 --greatest(least(D2,D3),D1) D4
             FROM (SELECT c.CM_UNIT_OUTWARDS_ID AS CM_UNIT_OUTWARDS_ID,
                          c.policy_no AS policy_no,
                          c.endorse_seq_no AS endorse_seq_no,
                          MIN(c.d1) AS d1, --获取D1:核保通过日期
                          MIN(c.d3) AS d3, --D3:保险责任起期
                          (CASE WHEN MIN(c.d2) IS NULL THEN
                             MIN(pay.actual_payment_date)
                            WHEN MIN(pay.actual_payment_date) IS NULL THEN
                             MIN(c.d2)
                            WHEN MIN(c.d2) > MIN(pay.actual_payment_date) THEN
                             MIN(pay.actual_payment_date)
                            ELSE
                             MIN(c.d2)
                            END) d2  --D2:首次付款到期日=MIN（最早预计缴费日期，最早的实际支付日期）
                     FROM dmuser.dm_buss_cmunit_fac_outwards c
                     LEFT JOIN dmuser.dm_acc_payment pay
                       ON c.entity_id = pay.entity_id
                      AND c.policy_no = pay.policy_no
                      AND pay.endorse_seq_no = c.endorse_seq_no
                      AND pay.est_payment_seq_no = 1
                      --AND pay.actual_payment_date < v_end_date --v_end_date下个月第一天 --不需要看是否存在评估期内，有数据就可以 2023/03/08
                      AND pay.policy_no IS NOT NULL
                    WHERE c.entity_id = p_entity_id
                      AND c.pl_judge_rslt IS NOT NULL -- 盈亏判定
                      AND c.evaluate_approach <> 'D'
                      --AND c.pl_judge_rslt <> 'D'
                      AND c.year_month IS NULL -- 未生成合同组
                      AND c.buss_year_month <= p_year_month
                    GROUP BY c.CM_UNIT_OUTWARDS_ID,
                             c.policy_no,
                             c.endorse_seq_no) t) cmb
    ON (cma.CM_UNIT_OUTWARDS_ID = cmb.CM_UNIT_OUTWARDS_ID)
    WHEN MATCHED THEN
      UPDATE set
             d1 = null,
             d2 = null,
             d3 = null,
             d5 =(CASE WHEN cmb.d4 >= v_end_date THEN NULL  ELSE cmb.d4 END) ;--v_end_date下个月第一天
      
    COMMIT;

    --业务时间的更新
    MERGE INTO dm_buss_cmunit_fac_outwards cma
    USING (SELECT t.cm_unit_outwards_id,
                  t.d1,
                  t.d3,
                  t.d5,
                  (CASE WHEN t.treaty_type_code = '91' and t.d5 is not null THEN --比例临分 MAX(D1,D3,D5)
                         greatest(t.d1, t.d3, t.d5)
                        WHEN t.treaty_type_code = '92' THEN --非比例临分 MAX(D1,D3)
                         greatest(t.d1, t.d3)
                        ELSE
                         NULL
                    END) AS d4
             FROM (SELECT cmo.cm_unit_outwards_id AS cm_unit_outwards_id,
                          MIN(ro.issue_date) AS d1,
                          MIN(ro.effective_date) AS d3,
                          MIN(cmo.d5) AS d5,--使用上面获取再保前的d5
                          MIN(cmo.treaty_type_code) AS treaty_type_code
                     FROM dmuser.dm_buss_cmunit_fac_outwards cmo
                     LEFT JOIN dmuser.dm_reins_outward ro --获取D1:REINS_FAC_SECTION.SIGNATURE_DATE，D3:REINS_FAC_SECTION.issue_date
                       ON cmo.entity_id = ro.entity_id
                      AND cmo.fac_no = ro.ri_policy_no
                      AND ro.issue_date < v_end_date
                      AND ro.business_source_code IN ('DB', 'FB')
                    WHERE cmo.entity_id = p_entity_id
                      AND cmo.evaluate_approach <> 'D'
                      AND cmo.pl_judge_rslt IS NOT NULL -- 盈亏判定
                      --AND cmo.pl_judge_rslt <> 'D'-- 盈亏判定
                      AND cmo.year_month IS NULL -- 未生成合同组
                      AND cmo.buss_year_month <= p_year_month
                    GROUP BY cmo.cm_unit_outwards_id --,cmo.policy_no
                  ) t
           ) cmb
    ON (cma.cm_unit_outwards_id = cmb.cm_unit_outwards_id)
    WHEN MATCHED THEN
      UPDATE
         SET cma.d1 = cmb.d1,
             cma.d3 = cmb.d3,
             cma.d5 = cmb.d5,
             cma.d4 = cmb.d4,
             cma.border_date = (CASE WHEN cmb.d4 >= v_end_date THEN NULL ELSE cmb.d4 END);--v_end_date下个月第一天
             --proc_id = v_proc_id,
             --node_state = '1'
             --invest_rate   = NULL,
             --invest_amount = NULL
    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN

      --意外处理
      v_error_msg := '[EXCEPTION][临分分出-合同分组]合同分组-获取合同确认日期业务数据:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_icg_fixed;

  PROCEDURE proc_icg_discern(p_entity_id  NUMBER,
                             p_year_month VARCHAR2) IS
    v_column     VARCHAR2(4000);
    v_proc_id    INTEGER;
    v_error_msg  varchar2(2000);

  BEGIN
    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][临分分出-合同分组]合同分组-传参不为空：proc_icg_discern(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][临分分出-合同分组]合同分组-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 合同确认日期
    proc_icg_fixed(p_entity_id, p_year_month);

    -- 根据proc_code查询proc_id
    v_proc_id := dm_pack_common.func_get_procid('DM_CONTRACTGROUP');

    -- 临分分出
    v_column := dm_pack_cmunit_common.func_get_contractcode(p_entity_id, 'C', 'FO');

    -- 更新合同组合编码
    EXECUTE IMMEDIATE ('update  dm_buss_cmunit_fac_outwards t'
                      ||' set icg_no = (case when border_date is not null then '|| v_column||' else null end )'
                      ||', proc_id = ' || v_proc_id
                      ||', invest_rate = null'
                      ||', invest_amount = null'
                      ||', node_state = '''|| (CASE WHEN v_column IS NULL THEN '2' ELSE '1' END)||''' '
                      ||', reason_of_failure = ''' || (CASE WHEN v_column IS NULL THEN 'DM_003' ELSE NULL END) ||''''
                      ||' where entity_id = ' || p_entity_id
                      ||' AND evaluate_approach <> ''D'' '
                      ||' AND pl_judge_rslt is not null '
                      ||' and year_month IS NULL '
                      ||' and buss_year_month <= ''' || p_year_month || ''' ');
    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN

      --意外处理
      v_error_msg := '[EXCEPTION][临分分出-合同分组]合同分组:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_icg_discern;


  PROCEDURE proc_icg_group(p_entity_id  NUMBER,
                           p_year_month VARCHAR2) IS

  BEGIN

    --  生成合同组编码
    proc_icg_discern(p_entity_id, p_year_month);

  END proc_icg_group;

  PROCEDURE proc_investment_separate(p_entity_id  NUMBER,
                                     p_year_month VARCHAR2) IS

    v_proc_id     INTEGER;
    v_data_key    VARCHAR2(200);
    v_row         INTEGER;
    v_current_day DATE; -- 当前业务年月第一天
    v_year_month  VARCHAR2(200);
    v_error_msg   varchar2(2000);

  BEGIN

    -- 条件不满足,结束判定
    IF p_entity_id IS NULL
       AND p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][临分分出-合同分组]投成拆分-传参不为空：proc_investment_separate(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 限定只处理 处于处理中的业务年月
    SELECT year_month
      INTO v_year_month
      FROM dm_conf_bussperiod
     WHERE entity_id = p_entity_id
       AND year_month = p_year_month
       AND period_state = '2' -- 处理中
       AND valid_is = '1'
       AND rownum = 1;

    -- 条件不满足,结束判定
    IF v_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][临分分出-合同分组]投成拆分-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    -- 当前业务年月所有计量单元都生成合同分组，才能进行投成拆分
    SELECT 1
      INTO v_row
      FROM dm_buss_cmunit_fac_outwards
     WHERE entity_id = p_entity_id
       AND buss_year_month <= p_year_month
       AND icg_no IS NOT NULL
       AND invest_rate IS NULL
       AND invest_amount IS NULL
       AND rownum = 1;
    -- 存在合同分组为空的数据
    IF v_row != 1 THEN
      RETURN;
    END IF;

    -- 根据PROC_CODE查询PROC_ID
    v_proc_id := dm_pack_common.func_get_procid('DM_INVESTMENT_COST');
    IF v_proc_id IS NULL THEN
      RETURN;
    END IF;

    --不需要做
    RETURN;

    -- 当前业务年月所有计量单元都生成合同分组，才能进行投成拆分
    SELECT 1
      INTO v_row
      FROM dm_buss_cmunit_fac_outwards
     WHERE entity_id = p_entity_id
       AND buss_year_month <= p_year_month
       AND icg_no IS NOT NULL
       AND invest_rate IS NULL
       AND invest_amount IS NULL
       AND rownum = 1;
    -- 存在合同分组为空的数据
    IF v_row != 1 THEN
      RETURN;
    END IF;

    --临分分出：投成拆分
    /* UPDATE DM_BUSS_CMUNIT_FAC_OUTWARDS CMOUT
      SET INVEST_RATE       = COALESCE(TO_NUMBER(BCQ.QUOTA_VALUE,
                                                 '999.999999'),
                                       0),
          INVEST_AMOUNT     = COALESCE(PREMIUM, 0) *
                              COALESCE(TO_NUMBER(BCQ.QUOTA_VALUE,
                                                 '999.999999'),
                                       0),
          PROC_ID           = V_PROC_ID,
          NODE_STATE        = '1',
          REASON_OF_FAILURE = NULL FROM BBS_CONF_QUOTA BCQ
    WHERE BCQ.QUOTA_DEF_ID =
          (SELECT QUOTA_DEF_ID
             FROM BBS_CONF_QUOTA_DEF
            WHERE QUOTA_CODE = 'ReportingPeriodRatio') --取投成拆分字段
      AND BCQ.BUSINESS_MODEL = 'D' --取再保临分的
      AND CMOUT.entity_id = BCQ.entity_id
      AND ((CMOUT.risk_class_code IS NOT NULL AND
          CMOUT.risk_class_code = BCQ.risk_class_code) OR
          (SELECT R.risk_class_code AS risk_class_code
              FROM BPLUSER.BBS_CONF_RISK R
             WHERE R.RISK_CODE = CMOUT.RISK_CODE
               AND CMOUT.entity_id = R.entity_id
               AND R.VALID_IS = '1'
               AND R.AUDIT_STATE = '1' LIMIT 1) = BCQ.risk_class_code)
      AND BCQ.VALID_IS = '1'
      AND BCQ.AUDIT_STATE = '1'
      AND CMOUT.MAJOR_RISK IS NOT NULL
      AND CMOUT.EVALUATE_APPROACH IS NOT NULL
      AND CMOUT.PL_JUDGE_RSLT IS NOT NULL
      AND CMOUT.BORDER_DATE IS NOT NULL
      AND CMOUT.ICG_NO IS NOT NULL
         --and cmout.invest_rate is null
         --and cmout.invest_amount is null
      AND CMOUT.entity_id = p_entity_id
      AND CMOUT.YEAR_MONTH IS NULL
      AND CMOUT.BUSS_YEAR_MONTH <= P_YEAR_MONTH;*/

    --WLI未有投成拆分的功能
    IF v_proc_id = NULL
       OR v_proc_id = '' THEN
      RETURN;
    END IF;

  EXCEPTION
    WHEN OTHERS THEN

      --意外处理
      v_error_msg := '[EXCEPTION][临分分出-合同分组]投成拆分:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_investment_separate;

  PROCEDURE proc_contract_group_confirm(p_entity_id  NUMBER,
                                        p_year_month VARCHAR2) IS

    v_error_msg          varchar2(2000);

  BEGIN
    -- 条件不满足,结束判定
    IF p_entity_id IS NULL OR p_year_month IS NULL THEN

      --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][临分分出-合同分组]确认-传参不为空：proc_contract_group_confirm(p_entity_id:'||p_entity_id||',p_year_month:'||p_year_month||')';
      raise_application_error(-20002,v_error_msg);

    END IF;

    IF func_get_current_year_month(p_entity_id, p_year_month) IS NULL THEN

    --抛出已自定义异常信息【会中断事务】
      v_error_msg := '[EXCEPTION][临分分出-合同分组]确认-无效月份：'||p_year_month;
      raise_application_error(-20002,v_error_msg);

    END IF;

    UPDATE dm_buss_cmunit_fac_outwards
       SET year_month = p_year_month
     WHERE entity_id = p_entity_id
       AND buss_year_month <= p_year_month
       AND year_month IS NULL
       AND icg_no IS NOT NULL;
    COMMIT;

  EXCEPTION
    WHEN OTHERS THEN

     --意外处理
      v_error_msg := '[EXCEPTION][临分分出-合同分组]确认:'||SQLERRM;
      --捕获异常，往外抛
      raise_application_error(-20004,v_error_msg);

  END proc_contract_group_confirm;

END dm_pack_cmunit_fac_outward;
/
