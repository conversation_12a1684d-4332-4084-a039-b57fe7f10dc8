package com.ss.ifrs.actuarial.dao;

import com.ss.platform.pojo.bms.track.vo.TrackMaintainsReqVo;
import com.ss.platform.pojo.bms.track.vo.TrackMaintainsVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface AtrBussTrackMaintainsDao {
    Page<TrackMaintainsReqVo> fuzzySearchPage(TrackMaintainsVo trackMaintainsVo, Pageable pageParam);
}
