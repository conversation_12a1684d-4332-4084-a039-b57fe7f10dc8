{"remainingRequest": "O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\node_modules\\babel-loader\\lib\\index.js!O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\src\\pages\\atr\\expectedCashFlow\\puhua\\lrcCashFlowApp\\components\\lrcViewDetailIndex.vue?vue&type=script&lang=js", "dependencies": [{"path": "O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\src\\pages\\atr\\expectedCashFlow\\puhua\\lrcCashFlowApp\\components\\lrcViewDetailIndex.vue", "mtime": 1753845795780}, {"path": "O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\babel.config.js", "mtime": 1741157760359}, {"path": "O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1742174260879}, {"path": "O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1742174263321}, {"path": "O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1742174260879}, {"path": "O:\\workspace\\coder\\ifrs17-hgic\\ss-web-vue-atr\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1742174262792}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["feild<PERSON>ard", "name", "props", "type", "licAction", "title", "licShowDataForLossCurrent", "id", "riskClass", "yearMonth", "dataType", "drawTime", "statisZones", "centerId", "currency", "portfolioNo", "mainId", "data", "form", "entityCode", "ibnrType", "businessSourceCode", "riskCName", "loaCode", "loaName", "currencyName", "taskCode", "riskClassCode", "recvDetailVoList", "gepDetailVoList", "covDetailVoList", "uepDetailVoList", "adjDetailVoList", "mainDetailVoList", "iacfDetailVoList", "nonMaimDetailVoList", "csmDetailVoList", "loding", "is<PERSON><PERSON><PERSON>ly", "isDisabled", "tableField", "DD", "edPremiumDetailTableFields", "edNetFeeDetailTableFields", "edIacfDetailTableFields", "edIaehcInDetailTableFields", "edIaehcOutDetailTableFields", "premiumDetailTableFields", "netFeeDetailTableFields", "badDebtDetailTableFields", "iacfDetailTableFields", "iaehcInDetailTableFields", "iaehcOutDetailTableFields", "lapseDetail<PERSON><PERSON><PERSON><PERSON>s", "mtFeeDetailTableFields", "claimDetailTableFields", "ulaeDetailTableFields", "raDetailTableFields", "TI", "TO", "premiumCfDetailTableFields", "netFeeCfDetailTableFields", "invAmountDetailTableFields", "TX", "FO", "recvPremiumDetailTableFields", "isReadonlySave", "rules", "dialogFormVisible", "pickerOptions", "disabledDate", "time", "getTime", "Date", "now", "activeName", "quotaTable", "basic", "headerstyle", "cellstyle", "custom", "fields", "firstRowFields", "second<PERSON><PERSON><PERSON><PERSON>s", "devPeriodTable", "fieldInit", "prop", "labelKey", "sortable", "showOverflowTooltip", "width", "developmentColumns", "quotaDtl", "quota", "bussQuotaVoList", "bussQuotaDevelopVoList", "quotaObj", "loading", "periodShow", "quotaDefName", "palettes", "rowPalettes", "lrcLicTab", "premTypeArray", "tabMap", "showRiskClassCode", "watch", "n", "o", "$emit", "licActionBusinessSourceCode", "newVal", "formRiskClassCode", "oldVal", "findBussQuotaDataById", "methods", "onSubmit", "_this", "url", "$refs", "validate", "valid", "<PERSON><PERSON>", "gvUtil", "confirm", "msg", "getInzTranslate", "then", "getUrl", "apiName", "contextName", "http", "post", "res", "isDialog", "successSubmit", "resCode", "catch", "message", "resetForm", "formName", "lrcCfMainId", "resetFields", "onClose", "onEditorChange", "val", "modelContent", "text", "initPage", "requestData", "user", "sessionStorage", "getItem", "JSON", "parse", "userCenterId", "selectCenterId", "setentityCode", "initRules", "trigger", "required", "businessType", "urlParams", "get", "resData", "icgNo", "handleCenterData", "handleCurrencyData", "initLrcFeeType", "timer", "setTimeout", "requestData1", "param", "becfType", "shareData", "isEmpty", "remark", "actionNo", "item", "devNoFiled", "getPeriodFiled", "devNo", "key", "fee", "getfieldList", "concat", "push", "$set", "table", "api", "vo", "context", "isShowMore", "search", "feeType", "list", "searchList", "periods", "becfVo", "devNoField", "childrenFields", "periodList", "start", "end", "length", "startDevNo", "endDevNo", "toString", "quotaEName", "quotaCName", "quotaTName", "fieldType", "fieldSplic", "requestData3", "icgNO", "lrcCashFlowVoList", "selectRowCurrency", "row", "currencyCode", "centerVo", "getInzName", "entityEName", "entityCName", "entityTName", "currencyVo", "currencyEName", "currencyCName", "currencyTName", "onListBtn", "flag", "onViewQuotaDevelopPeriod", "actionVo", "dimensionValue", "obj", "ob<PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "initQuotaTableAndData", "rowspan", "headerAlign", "headColor", "insuranceType", "i", "children", "<PERSON><PERSON><PERSON><PERSON>", "uniqueBaseCodes", "Set", "<PERSON><PERSON><PERSON><PERSON>", "j", "childItem", "baseCode", "quotaCode", "add", "baseCodes", "Array", "from", "sampleItem", "k", "field<PERSON>bj", "quotaLName", "className", "quotaValueType", "quotaType", "currentRiskClassCode", "fullFieldName", "value", "groupField", "colspan", "initTableHeader", "extractRiskClassCodes", "riskClasses", "codePattern", "groupKey", "group", "<PERSON><PERSON><PERSON>", "matches", "match", "indexOf", "style", "document", "createElement", "innerHTML", "head", "append<PERSON><PERSON><PERSON>", "quotaDef", "findBussQuotaPeriod", "console", "error", "onFold", "initQuotaDetailTableAndData", "finally", "label", "rowData", "quotaPeriod", "quotaValue", "handleClick", "tab", "event", "_this2", "$nextTick", "getParamsMixin", "params", "cacheFilters", "assign", "_pageSize", "mixinObject", "searchSet", "pageSize", "_pageNo", "pageNo", "onHandleCurrentChange", "isInit", "onHandleSizeChange", "tabName", "voName", "currentPage", "serachParms", "uprDetailsVoList", "content", "total", "totalElements", "handleSetList", "tableObj", "mounted", "_this3"], "sources": ["src/pages/atr/expectedCashFlow/puhua/lrcCashFlowApp/components/lrcViewDetailIndex.vue"], "sourcesContent": ["<template>\r\n    <el-dialog :title=\"'atrLrcCashFlowTitle' | translate\" custom-class=\"gv-dialog-form\" :visible.sync=\"dialogFormVisible\"\r\n               width=\"96%\" :close-on-click-modal=\"false\" top=\"5vh\" append-to-body>\r\n        <gv-form :model=\"form\" ref=\"form\" :rules=\"rules\">\r\n            <el-collapse v-model=\"mixinObject.activeNames\">\r\n                <el-collapse-item :title=\"'gTitleBasics' | translate('Basics Data')\" name=\"1\" class=\"table-line\">\r\n                    <div class=\"gv-row\">\r\n                        <gv-form-item key-name=\"gCenterCode\" prop=\"entityCode\" >\r\n                            <gv-auto-complete context-name=\"common\" url=\"/basic_center/find_Branch\" code-name=\"entityCode,entityCName\"\r\n                                              :is-readonly=\"isReadonlySave\"\r\n                                              label-name=\"entityCode,entityCName\"\r\n                                              v-model=\"form.entityCode\" ></gv-auto-complete>\r\n                        </gv-form-item>\r\n                        <gv-form-item key-name=\"dmBusinessType\" prop=\"businessSourceCode\">\r\n                            <gv-select :disabled=\"isReadonlySave\" size=\"mini\" options-set=\"0\" code-type=\"BusinessModel/Base\" v-model=\"form.businessSourceCode\"></gv-select>\r\n                        </gv-form-item>\r\n                        <gv-form-item key-name=\"atrEvaluationYearMonth\" prop=\"yearMonth\">\r\n                            <el-input maxlength=\"6\" placeholder=\"yyyymm\" v-model=\"form.yearMonth\" :disabled=\"isReadonlySave\">\r\n                            </el-input>\r\n                        </gv-form-item>\r\n                    </div>\r\n                    <div class=\"gv-row\">\r\n                        <gv-form-item key-name=\"gDmPortfolioNo\" prop=\"portfolioNo\" v-if=\"type !=='add'\">\r\n                            <el-input v-model=\"form.portfolioNo\" :disabled=\"isReadonlySave\"></el-input>\r\n                        </gv-form-item>\r\n                        <gv-form-item key-name=\"gDmContractGroupNo\" prop=\"icgNo\" v-if=\"type !=='add'\">\r\n                            <el-input v-model=\"form.icgNo\" :disabled=\"isReadonlySave\"></el-input>\r\n                        </gv-form-item>\r\n                      <gv-form-item key-name=\"atrInsuranceClass\"  v-if=\"showRiskClassCode\" prop=\"riskClassCode\">\r\n                        <el-input v-model=\"form.riskClassCode\" :disabled=\"isReadonlySave\"></el-input>\r\n                      </gv-form-item>\r\n                    </div>\r\n                </el-collapse-item>\r\n\r\n                <!--aioi-->\r\n                <el-collapse-item :title=\"'atrAcquisitionInfo' | translate\" name=\"2\" v-if=\"form.businessSourceCode !== 'TO' && form.businessSourceCode !== 'FO'\">\r\n                    <div class=\"gv-atr-collapse-content\">\r\n                        <div id=\"tabs-atr\">\r\n                            <table class=\"custom-table\">\r\n                                <thead>\r\n                                  <!-- 第一行表头 -->\r\n                                  <tr>\r\n                                    <template v-for=\"(field, index) in quotaTable.firstRowFields\">\r\n                                      <th v-if=\"field.rowspan\" \r\n                                          :key=\"'first-'+index\"\r\n                                          :rowspan=\"field.rowspan\" \r\n                                          :class=\"field.className\"\r\n                                          :style=\"{ backgroundColor: field.headColor, textAlign: 'center' }\">\r\n                                        {{ field.labelKey | translate }}\r\n                                      </th>\r\n                                      <th v-else \r\n                                          :key=\"('first-'+index)\"\r\n                                          :colspan=\"field.colspan\" \r\n                                          :class=\"field.className\"\r\n                                          :style=\"{ backgroundColor: field.headColor, textAlign: 'center' }\">\r\n                                        {{ field.labelKey | translate }}\r\n                                      </th>\r\n                                    </template>\r\n                                  </tr>\r\n                                  <!-- 第二行表头 -->\r\n                                  <tr>\r\n                                    <template v-for=\"(field, index) in quotaTable.secondRowFields\">\r\n                                      <th v-if=\"field.prop !== 'insuranceType'\"\r\n                                          :key=\"'second-'+index\" \r\n                                          :class=\"field.className\"\r\n                                          :style=\"{ backgroundColor: field.headColor, textAlign: 'center' }\">\r\n                                        {{ field.labelKey | translate }}\r\n                                      </th>\r\n                                    </template>\r\n                                  </tr>\r\n                                </thead>\r\n                                <tbody>\r\n                                  <tr v-for=\"(row, rowIndex) in bussQuotaVoList\" :key=\"rowIndex\">\r\n                                    <!-- 险种列 -->\r\n                                    <td style=\"text-align: center;\">{{ row.insuranceType }}</td>\r\n                                    <!-- 数据单元格 -->\r\n                                    <template v-for=\"(field, fieldIndex) in quotaTable.secondRowFields\">\r\n                                      <td v-if=\"field.prop !== 'insuranceType'\"\r\n                                          :key=\"'cell-'+fieldIndex\" \r\n                                          :class=\"field.className\" \r\n                                          style=\"text-align: right;\">\r\n                                        <!-- 如果是发展期指标(quotaType=1)，只显示图标不显示值 -->\r\n                                        <template v-if=\"field.quotaType === '1'\">\r\n                                          <i class=\"el-icon-my-line-graph\"\r\n                                             style=\"margin-left: 5px; cursor: pointer;\"\r\n                                             @click=\"onListBtn(row, 'develop', field.prop)\"></i>\r\n                                        </template>\r\n                                        <!-- 否则根据字段类型格式化数据 -->\r\n                                        <template v-else>\r\n                                          <span v-if=\"field.fieldType === '2'\">\r\n                                            {{ row[field.prop] | amount(true, 2) }}\r\n                                          </span>\r\n                                          <span v-else-if=\"field.fieldType === '4'\">\r\n                                            {{ row[field.prop] | amountZero(false, 2) }}%\r\n                                          </span>\r\n                                          <span v-else>\r\n                                            {{ row[field.prop] }}\r\n                                          </span>\r\n                                        </template>\r\n                                      </td>\r\n                                    </template>\r\n                                  </tr>\r\n                                </tbody>\r\n                            </table>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"gv-atr-collapse-content\" v-if=\"periodShow\">\r\n                        <div>\r\n                            <span class=\"rectangularr\"></span>\r\n                            <span class=\"gv-panel-titleBar\">{{quotaDefName}}</span>\r\n                            <div  class=\"pull-right\">\r\n                                <el-button class=\"mr15\" style=\"margin-right: 8px\" type=\"text\" @click=\"onFold\"><i class=\"el-dialog__close el-icon el-icon-close\"></i></el-button>\r\n                            </div>\r\n                        </div>\r\n                        <div>\r\n                            <el-table :data=\"bussQuotaDevelopVoList\" border :header-cell-style=\"{ backgroundColor: '#f5f7fa', color: '#606266' }\">\r\n                                <el-table-column prop=\"quotaPeriod\" :label=\"'atrDevelopMonth' | translate\" width=\"180\"></el-table-column>\r\n                                <el-table-column\r\n                                    v-for=\"(col, index) in developmentColumns\"\r\n                                    :key=\"index\"\r\n                                    :prop=\"col.prop\"\r\n                                    :label=\"col.label\"\r\n                                    align=\"right\">\r\n                                    <template v-slot=\"scope\">\r\n                                        {{ scope.row[col.prop] | amountZero(false, 2) }} %\r\n                                    </template>\r\n                                </el-table-column>\r\n                            </el-table>\r\n                        </div>\r\n                    </div>\r\n                </el-collapse-item>\r\n                <el-collapse-item :title=\"'atrAioiExpectedPremiumCF' | translate\" name=\"3\">\r\n                    <el-tabs v-model=\"lrcLicTab\"  v-if=\"loding\" @tab-click=\"handleClick\">\r\n                        <el-tab-pane v-for=\"obj in this.premTypeArray\" :key=\"obj.remark\" :label=\"obj.outCName\"\r\n                                     :name=\"obj.remark\" >\r\n                            <template  >\r\n                                <gv-data-table-list v-if=\"obj.remark===lrcLicTab && tabMap[obj.remark]\" :ref=\"obj.remark+'Table'\" @upListData=\"handleSetList\" :table=\"tabMap[obj.remark].table\" :list=\"tabMap[obj.remark].list\" :id=\"obj.remark\"   :listTableTop=\"true\" :paging=\"true\"\r\n                                                    :currentPage=\"mixinObject.searchSet.currentPage\" :MaxHeight='\"400px\"' :total=\"mixinObject.searchSet.total\" >\r\n                                </gv-data-table-list>\r\n                            </template>\r\n                       </el-tab-pane>\r\n                    </el-tabs>\r\n                </el-collapse-item>\r\n            </el-collapse>\r\n            <el-row class=\"toolbar-btn txt-center\">\r\n                <el-button class=\"gv-btn gv-btn-primary\" :disabled=\"isReadonlySave\" v-if=\"type!='view'\" type=\"primary\" @click=\"onSubmit()\">{{\r\n                        'atrBtnDraw' | translate }}\r\n                </el-button>\r\n                <el-button class=\"gv-btn gv-btn-white\" @click=\"onClose()\">{{ 'gBtnClose' | translate}}</el-button>\r\n            </el-row>\r\n        </gv-form>\r\n    </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport feildCard from './card.js'\r\nexport default {\r\n    name: 'lrcAppEditIndex',\r\n    props: {\r\n        type:'',\r\n        licAction:{},\r\n        title:'',\r\n        licShowDataForLossCurrent:'',\r\n        id: '',\r\n        riskClass:'',\r\n        yearMonth:'',\r\n        dataType: '',\r\n        drawTime: '',\r\n        statisZones: '',\r\n        centerId: '',\r\n        currency: '',\r\n        portfolioNo: '',\r\n        mainId: '',\r\n    },\r\n    data: function () {\r\n        return {\r\n            form: {\r\n                centerId: '',\r\n                entityCode: '',\r\n                riskClass: null,\r\n                currency: '',\r\n                ibnrType: '',\r\n                businessSourceCode: '',\r\n                statisZones: '',\r\n                drawTime: '',\r\n                dataType: '',\r\n                riskCName: '',\r\n                loaCode: '',\r\n                loaName: '',\r\n                portfolioNo: null,\r\n                yearMonth: '',\r\n                currencyName: '',\r\n                taskCode: '',\r\n                riskClassCode: '',\r\n                recvDetailVoList:[],\r\n                gepDetailVoList:[],\r\n                covDetailVoList:[],\r\n                uepDetailVoList:[],\r\n                adjDetailVoList:[],\r\n                mainDetailVoList:[],\r\n                iacfDetailVoList:[],\r\n                nonMaimDetailVoList:[],\r\n                csmDetailVoList:[],\r\n            },\r\n            loding: true,\r\n            isReadonly: false,\r\n            isDisabled: false,// 禁用下拉选项\r\n\r\n          tableField: {\r\n            DD: {\r\n              edPremiumDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt', 'cmunitNo',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate', 'comfirmDate','approvalDate',\r\n                'currencyCode', 'grossPremium','preAccumEdPremium' ],\r\n              edNetFeeDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode' ,'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt', 'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                  'currencyCode','feeRate','netFee', 'preCumlEdNetFee'],\r\n              edIacfDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','iacf','preCumlEdIacf'\r\n                 ],\r\n              edIaehcInDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt',  'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','iaehcIn','preCumlEdIaehcIn'],\r\n              edIaehcOutDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','iaehcOut','preCumlEdIaehcOut'],\r\n              premiumDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt',  'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','premium','preCumlPaidPremium'],\r\n              netFeeDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt',  'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode', 'netFee','preCumlPaidNetFee'],\r\n              badDebtDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt',  'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','badDebt'],\r\n              iacfDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','iacf','preCumlEdIacf'],\r\n              iaehcInDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','iaehcIn'],\r\n              iaehcOutDetailTableFields:[\r\n                'actionNO','entityId','policyNo', 'endorseSeqNo',\r\n                'riskCode','kindCode','atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo','icgName','plJudgeRslt',  'cmunitNo','evaluateApproach',\r\n                'businessSourceCode','evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate', 'issueDate','comfirmDate', 'approvalDate',\r\n                'currencyCode','iaehcOut'],\r\n              lapseDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode',\r\n                 'lapseRate'],\r\n              mtFeeDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode',\r\n                'mtRate'],\r\n              claimDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode',\r\n                'claimRate'],\r\n              ulaeDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode',\r\n                'ulaeRate'],\r\n              raDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode',\r\n                'raRatio'],\r\n\r\n            },\r\n            TI: {\r\n              edPremiumDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate',\r\n                'grossPremium','preCumlPaidPremium' ],\r\n              edNetFeeDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','feeRate','netFee', 'preCumlEdNetFee'],\r\n              edIacfDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','iacf','preCumlEdIacf'\r\n              ],\r\n              edIaehcInDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','iaehcIn','preCumlEdIaehcIn'],\r\n              edIaehcOutDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','iaehcOut','preCumlEdIaehcOut'],\r\n              premiumDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','premium','preCumlPaidPremium'],\r\n              netFeeDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate', 'netFee','preCumlPaidNetFee'],\r\n              iacfDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','iacf','preCumlEdIacf'],\r\n              iaehcInDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','iaehcIn'],\r\n              iaehcOutDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName', 'riskClassCode',\r\n                'atrCenter1','atrCenter2','atrCenter3','atrCenter4',\r\n                'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt',\r\n                'evaluateDate', 'contractDate','currencyCode',\r\n                'effectiveDate','expiryDate','iaehcOut'],\r\n              lapseDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode','currencyCode',\r\n                'lapseRate'],\r\n              mtFeeDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode','currencyCode',\r\n                'mtRate'],\r\n              claimDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode','currencyCode',\r\n                'claimRate'],\r\n              ulaeDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode','currencyCode',\r\n                'ulaeRate'],\r\n              raDetailTableFields:[\r\n                'actionNO','entityId', 'yearMonth','portfolioNo','icgNo', 'icgName','plJudgeRslt','riskClassCode','currencyCode',\r\n                'raRatio'],\r\n            },\r\n            TO: {\r\n              edPremiumDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','policyNo', 'endorseSeqNo','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                'grossPremium','preEdPremium' ],\r\n              premiumCfDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','policyNo', 'endorseSeqNo','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo',\r\n                'evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                'grossPremium', 'preAccumEdPremium' ],\r\n              netFeeCfDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','policyNo', 'endorseSeqNo','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                 'netFee','preAccumNetFee'],\r\n              edNetFeeDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','policyNo', 'endorseSeqNo','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',\r\n                'effectiveDate','expiryDate', 'feeRate','netFee', 'preEdNetFee'],\r\n              invAmountDetailTableFields:[\r\n                  \r\n              ]\r\n            },\r\n            TX: {\r\n              edPremiumDetailTableFields:[\r\n                'actionNO','entityId','treatyNo','policyNo', 'endorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                'grossPremium','preEdPremium' ],\r\n              premiumCfDetailTableFields:[\r\n                'actionNO','entityId','treatyNo','policyNo', 'endorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo',\r\n                'evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                'grossPremium' ]\r\n            },\r\n            FO: {\r\n              edPremiumDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                'grossPremium','preEdPremium' ],\r\n              recvPremiumDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo',\r\n                'evaluateDate', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                'grossPremium', 'preAccumEdPremium' ],\r\n              netFeeDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',\r\n                'effectiveDate','expiryDate',\r\n                'netFee','preAccumNetFee'],\r\n              edNetFeeDetailTableFields:[\r\n                'actionNO','entityId','treatyNo', 'treatyName','riskClassCode','riPolicyNo','riEndorseSeqNo','kindCode','yearMonth',\r\n                'portfolioNo','icgNo', 'icgName','plJudgeRslt', 'cmunitNo', 'contractDate',\r\n                'effectiveDate','expiryDate','netFee', 'preEdNetFee']\r\n            },\r\n          },\r\n            isReadonlySave: false,\r\n            rules: {},\r\n            dialogFormVisible: true,\r\n            pickerOptions: {\r\n                disabledDate: function (time) {\r\n                    return time.getTime() < Date.now() - 8.64e7;\r\n                }\r\n            },\r\n            activeName: 'first',\r\n\r\n            /****************aioi假设值*****************/\r\n            //版本指标假设业务值\r\n            quotaTable: {\r\n                basic: {\r\n                    headerstyle:true,\r\n                    cellstyle:{},\r\n                    custom: true, // 添加自定义渲染支持\r\n                },\r\n                fields: [],\r\n                firstRowFields: [], // 第一行表头字段\r\n                secondRowFields: [] // 第二行表头字段\r\n            },\r\n            //版本指标发展期业务值\r\n            devPeriodTable: {\r\n                basic: {\r\n                    fieldInit: false,\r\n                    cellstyle: true,\r\n                    headerstyle: true\r\n                },\r\n                fields: [{\r\n                    prop: \"quotaPeriod\", //属性\r\n                    labelKey: 'atrDevelopMonth',\r\n                    sortable:false,\r\n                    showOverflowTooltip: true,\r\n                    width:\"150px\",\r\n                }]\r\n            },\r\n            developmentColumns: [], // 存储发展期表格列\r\n            quotaDtl:{\r\n                quota:''\r\n            },\r\n            bussQuotaVoList:[],\r\n            bussQuotaDevelopVoList:[],\r\n            quotaObj:'',\r\n            loading: false,\r\n            periodShow: false,\r\n            quotaDefName:'',\r\n            palettes:['#DAD4F0','#ECE9F7'],\r\n            rowPalettes:['purple-to-gray','light-gray'],\r\n            lrcLicTab: '',\r\n            premTypeArray:[],\r\n            tabMap: {\r\n            },\r\n            showRiskClassCode: false,\r\n        }\r\n    },\r\n    watch: {\r\n        dialogFormVisible: function (n, o) {\r\n            !n && this.$emit('close');\r\n        },\r\n        'licAction.businessSourceCode': function(newVal) {\r\n            this.showRiskClassCode = newVal === 'DD' || newVal === 'TI';\r\n        },\r\n        'form.riskClassCode': function(newVal, oldVal) {\r\n            // 当险类代码变化且有值时，重新获取假设数据\r\n            if (newVal && newVal !== oldVal) {\r\n                this.findBussQuotaDataById();\r\n            }\r\n        }\r\n    },\r\n\r\n    methods: {\r\n        // 确认按钮（表单提交）\r\n        onSubmit: function() {\r\n            var _this = this,\r\n                url;\r\n            this.$refs.form.validate(function(valid) {\r\n                if (valid) {\r\n                    Vue.gvUtil.confirm({\r\n                        msg: Vue.gvUtil.getInzTranslate('gSaveSubmit')\r\n                    }).then(function() {\r\n                        // 新增\r\n                        url = Vue.gvUtil.getUrl({\r\n                            apiName: 'lrcCashFlowAdd',\r\n                            contextName: 'actuarial'\r\n                        });\r\n                        Vue.gvUtil.http.post(url, _this.form).then(function (res) {\r\n                            if (_this.isDialog) {\r\n                                // _this.dialogSuccessSubmit();\r\n                            } else {\r\n                                _this.successSubmit(res)\r\n                            }\r\n                            if (res.resCode === '0017') {\r\n                                msg: Vue.gvUtil.getInzTranslate('gSaveError');\r\n                            }\r\n\r\n                        });\r\n                    }).catch(function(){});\r\n                } else {\r\n                    Vue.gvUtil.message(Vue.gvUtil.getInzTranslate('gValidateContent'));\r\n                    return false;\r\n                }\r\n            });\r\n        },\r\n        // 清除表单\r\n        resetForm: function (formName) {\r\n            var lrcCfMainId = this.form.lrcCfMainId;\r\n            var entityCode = this.form.entityCode;\r\n            this.$refs[formName].resetFields();\r\n            this.form.lrcCfMainId = '';\r\n            if (this.type === 'edit') {\r\n                this.form.lrcCfMainId = lrcCfMainId;\r\n                this.form.entityCode = entityCode;\r\n            }\r\n        },\r\n        // 关闭\r\n        onClose: function () {\r\n            this.dialogFormVisible = false;\r\n            this.$emit('close');\r\n        },\r\n        onEditorChange: function (val) {\r\n            this.form.modelContent = val.text;\r\n        },\r\n\r\n        // 初始化页面，低层直接调用\r\n        initPage: function () {\r\n            if (this.type !== 'add') {\r\n                this.requestData();\r\n            }\r\n            if (this.type === 'view') {\r\n                this.isReadonly = true;\r\n                this.isDisabled = true;\r\n                this.isReadonlySave = true;\r\n            }\r\n            if (this.type === 'add') {\r\n                var user = sessionStorage.getItem('user');\r\n                if (user) {\r\n                    user = JSON.parse(user);\r\n                    this.form.centerId = user.userCenterId;\r\n\r\n                }\r\n                this.selectCenterId();\r\n            }\r\n        },\r\n        //配置初始化核算单位，并查询数据\r\n        selectCenterId: function () {\r\n            this.form.entityCode = Vue.gvUtil.setentityCode()\r\n        },\r\n        // 初始化校验，低层直接调用\r\n        initRules: function () {\r\n            this.rules = {\r\n                entityCode: [{\r\n                    trigger: 'blur',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }, {\r\n                    trigger: 'change',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }],\r\n                businessType: [{\r\n                    trigger: 'blur',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }, {\r\n                    trigger: 'change',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }],\r\n                yearMonth: [{\r\n                    trigger: 'blur',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }, {\r\n                    trigger: 'change',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }],\r\n                currency: [{\r\n                    trigger: 'blur',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }, {\r\n                    trigger: 'change',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }],\r\n                drawTime: [{\r\n                    trigger: 'blur',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }, {\r\n                    trigger: 'change',\r\n                    required: true,\r\n                    message: Vue.gvUtil.getInzTranslate('gValidateRequired')\r\n                }],\r\n            };\r\n        },\r\n        requestData: function () {\r\n            var _this = this,\r\n                url = Vue.gvUtil.getUrl({\r\n                    apiName: 'bussLrcCashFlowFindByPk',\r\n                    urlParams: {\r\n                        id: _this.licAction.id\r\n                    },\r\n                    contextName: 'actuarial'\r\n                });\r\n            Vue.gvUtil.http.get(url).then(function (res) {\r\n                if (res.resCode === '0000') {\r\n                    // $.extend(true, _this.form, res.resData);\r\n                    //表单基础数据查询和格式化\r\n                    res.resData.portfolioNo = _this.licAction.portfolioNo;\r\n                    res.resData.icgNo = _this.licAction.icgNo;\r\n                    res.resData.riskClassCode = _this.licAction.riskClassCode;\r\n                    _this.form=res.resData\r\n                    _this.handleCenterData(res.resData);\r\n                    _this.handleCurrencyData(res.resData);\r\n                    \r\n                    // 只有当业务类型不是TO和FO时才获取假设数据\r\n                    if (_this.licAction.businessSourceCode !== 'TO' && _this.licAction.businessSourceCode !== 'FO') {\r\n                        _this.findBussQuotaDataById();\r\n                    }\r\n                    \r\n                    _this.initLrcFeeType(_this.licAction.businessSourceCode);\r\n                    _this.timer = setTimeout(function(){\r\n                        _this.requestData1();\r\n                    },1000)// 进入该分支说明当前并没有在计时，那么就开始一个计时\r\n                    //表单LRC的明细数据\r\n                }\r\n            });\r\n        },\r\n\r\n        //业务单位国际化\r\n        initLrcFeeType: function(businessSourceCode) {\r\n            var param = {\r\n              businessSourceCode : businessSourceCode,\r\n              becfType:'Lrc'\r\n            };\r\n            var _this = this,\r\n                url = Vue.gvUtil.getUrl({\r\n                    apiName: 'findLrcFeeTypeByCodeIdx',\r\n                    contextName: 'actuarial'\r\n                });\r\n            Vue.gvUtil.http.post(url, param).then(function(res) {\r\n                if (res.resCode === '0000') {\r\n                    var shareData = res.resData;\r\n                    if (!Vue.gvUtil.isEmpty(shareData)) {\r\n                        _this.premTypeArray = shareData;\r\n                        _this.lrcLicTab = shareData[0].remark\r\n                    }\r\n                }\r\n            });\r\n        },\r\n\r\n        requestData1: function () {\r\n            var _this = this,\r\n                url = Vue.gvUtil.getUrl({\r\n                    apiName: 'lrcFindPeriodHeader',\r\n                    contextName: 'actuarial'\r\n                });\r\n            var urlParams={\r\n                actionNo: _this.form.actionNo,\r\n                businessSourceCode : _this.licAction.businessSourceCode,\r\n                icgNo: _this.form.icgNo,\r\n            };\r\n            _this.loding = false\r\n            Vue.gvUtil.http.post(url,urlParams).then(function (res) {\r\n                if (res.resCode === '0000') {\r\n                    for(let item in _this.premTypeArray){\r\n                        var devNoFiled =_this.getPeriodFiled(res.resData.devNo, _this.premTypeArray[item]);\r\n                        let key = _this.premTypeArray[item].remark;\r\n                        let fee = _this.premTypeArray[item].remark;\r\n                        let fields = feildCard.getfieldList(_this[`tableField`][`${_this.licAction.businessSourceCode}`][`${fee}DetailTableFields`]);\r\n                        if (_this.premTypeArray[item].type=='1') {\r\n                            fields.push(devNoFiled)\r\n                        }\r\n                        _this.$set(_this.tabMap,key,{\r\n                            table: {\r\n                                basic: {\r\n                                    api: \"findLrcDataDetail\", //分页列表请求api\r\n                                    vo: \"lrcCashFlowVoList\", //分页列表返回的vo\r\n                                    context: \"actuarial\", //分页列表请求上下文\r\n                                    isShowMore: true\r\n                                },\r\n                                search: { //查询域元数据\r\n                                    actionNo: _this.form.actionNo,\r\n                                    icgNo: _this.form.icgNo,\r\n                                    businessSourceCode:_this.licAction.businessSourceCode,\r\n                                    feeType: key,\r\n                                },\r\n                                fields: fields,\r\n                            },\r\n\r\n                            list:[]\r\n                        })\r\n                    }\r\n                    _this.searchList();\r\n                }\r\n                _this.loding = true\r\n            });\r\n        },\r\n\r\n\r\n        getPeriodFiled: function (periods, becfVo) {\r\n            var devNoField = {\r\n                width: \"auto\",\r\n                sortable: false,\r\n                labelKey: 'atrDevelopMonth',\r\n                childrenFields:[]\r\n            };\r\n            var periodList=[];\r\n            var start =0;\r\n            var end = periods.length-1;\r\n            if (!Vue.gvUtil.isEmpty(becfVo.startDevNo)) {\r\n                start = becfVo.startDevNo;\r\n            }\r\n            if (!Vue.gvUtil.isEmpty(becfVo.endDevNo)) {\r\n                end = becfVo.endDevNo;\r\n            }\r\n            for(start; start<= end; start++) {\r\n                periodList[periods[start].toString()] = {\r\n                    prop: periods[start].toString(),\r\n                    quotaEName: periods[start].toString(),\r\n                    quotaCName: periods[start].toString(),\r\n                    quotaTName: periods[start].toString(),\r\n                    fieldType: '2'\r\n                }\r\n            }\r\n            devNoField.childrenFields = Vue.gvUtil.fieldSplic(periodList)\r\n            return devNoField;\r\n        },\r\n\r\n\r\n        requestData3: function (feeType) {\r\n            var _this = this,\r\n                url = Vue.gvUtil.getUrl({\r\n                    apiName: 'lrcFindDate',\r\n                    contextName: 'actuarial'\r\n                });\r\n            var urlParams={\r\n                actionNo: _this.form.actionNo,\r\n                icgNO: _this.form.icgNO,\r\n                businessSourceCode:_this.licAction.businessSourceCode,\r\n                feeType:_this.licAction.businessSourceCode,\r\n            };\r\n            Vue.gvUtil.http.post(url,urlParams).then(function (res) {\r\n                if (res.resCode === '0000') {\r\n                   _this.tabMap.get(feeType).list = res.resData.lrcCashFlowVoList\r\n                }\r\n            });\r\n        },\r\n\r\n\r\n        selectRowCurrency: function (row) {\r\n            if(row) {\r\n                this.form.currency = row.currencyCode;\r\n            } else {\r\n                this.form.currency = '';\r\n            }\r\n        },\r\n        // 业务单位国际化处理\r\n        handleCenterData: function (centerVo) {\r\n            var _this = this;\r\n            _this.form.entityCode = centerVo.entityCode + ' -- ' + Vue.gvUtil.getInzName(centerVo.entityEName, centerVo.entityCName, centerVo.entityTName);\r\n        },\r\n        // 业务单位国际化处理\r\n        handleCurrencyData: function (currencyVo) {\r\n            var _this = this;\r\n            if (Vue.gvUtil.isEmpty(Vue.gvUtil.getInzName(currencyVo.currencyEName, currencyVo.currencyCName, currencyVo.currencyTName))) {\r\n                return _this.form.currencyName = currencyVo.currency;\r\n            }\r\n            _this.form.currencyName = currencyVo.currency + ' -- ' + Vue.gvUtil.getInzName(currencyVo.currencyEName, currencyVo.currencyCName, currencyVo.currencyTName);\r\n        },\r\n        // 保存成功后回调的方法\r\n        successSubmit: function (data) {\r\n            Vue.gvUtil.message(Vue.gvUtil.getInzTranslate('atrExtractionTip'),1500,'success');\r\n            this.$emit('searchList');\r\n            this.$emit('findLicVersionData');\r\n            this.onClose();\r\n        },\r\n\r\n        /*aioi*/\r\n        //版本操作列事件\r\n        onListBtn: function (row, flag, prop) {\r\n            if (flag === 'develop') {\r\n                this.onViewQuotaDevelopPeriod(row, null, prop);\r\n            }\r\n        },\r\n        //查询版本的计量假设数据\r\n        findBussQuotaDataById:function () {\r\n            var actionVo = {\r\n                actionNo : this.licAction.actionNo,\r\n                dimensionValue : this.licAction.icgNo,\r\n                riskClassCode: this.form.riskClassCode\r\n            };\r\n            var _this = this,\r\n                url = Vue.gvUtil.getUrl({\r\n                    apiName: 'findAtrBussQuotaData',\r\n                    contextName: 'actuarial'\r\n                });\r\n            Vue.gvUtil.http.post(url, actionVo).then(function (res) {\r\n                if (res.resCode === '0000') {\r\n                    var obj =  res.resData.data;\r\n                    var objKeys = Object.keys(obj)\r\n                    if (Vue.gvUtil.isEmpty(obj) || objKeys.length==0) {\r\n                        actionVo.dimensionValue = _this.licAction.portfolioNo;\r\n                        Vue.gvUtil.http.post(url, actionVo).then(function (res) {\r\n                            if (res.resCode === '0000') {\r\n                                obj =  res.resData.data;\r\n                                _this.initQuotaTableAndData(obj);\r\n                            }\r\n                        });\r\n                    } else {\r\n                        _this.initQuotaTableAndData(obj);\r\n                    }\r\n                }\r\n            });\r\n        },\r\n        initQuotaTableAndData: function (obj) {\r\n            var data = {};\r\n            var _this = this;\r\n            var objKeys = Object.keys(obj);\r\n            \r\n            // 准备表格结构 - 第一行和第二行的表头结构\r\n            var firstRowFields = [];\r\n            var secondRowFields = [];\r\n            \r\n            // 第一列显示险种代码\r\n            firstRowFields.push({\r\n                prop: 'insuranceType',\r\n                labelKey: 'atrInsuranceClass',\r\n                width: \"120px\",\r\n                rowspan: 2, // 第一列需要跨越两行\r\n                headerAlign: 'center',\r\n                headColor: _this.palettes[0]\r\n            });\r\n            \r\n            // 创建只包含当前险类的 bussQuotaVoList\r\n            var row = {\r\n                insuranceType: this.form.riskClassCode || '' // 显示当前险类代码\r\n            };\r\n            \r\n            // 处理API返回的一般假设和事故年月等分组\r\n            for (var i = 0; i < objKeys.length; i++) {\r\n                var quota = obj[objKeys[i]];\r\n                var children = [];  // 存储该分组下的所有指标\r\n                \r\n                // 如果没有子项，跳过\r\n                if (!quota.childQuota) continue;\r\n                \r\n                // 获取该组下所有的不同指标代码（非险种），如\"#lic_ulae_ratio\"和\"#lic_ra_ratio\"\r\n                var uniqueBaseCodes = new Set();\r\n                var childQuota = quota.childQuota;\r\n                var childKeys = Object.keys(childQuota);\r\n                \r\n                for (var j = 0; j < childKeys.length; j++) {\r\n                    var childItem = childQuota[childKeys[j]];\r\n                    // 从完整代码中提取基础指标代码，例如从\"#lic_ulae_ratio01\"提取\"#lic_ulae_ratio\"\r\n                    var baseCode = childItem.quotaCode;\r\n                    uniqueBaseCodes.add(baseCode);\r\n                }\r\n                \r\n                // 将Set转换为数组\r\n                var baseCodes = Array.from(uniqueBaseCodes);\r\n                \r\n                // 为每个指标创建一个表头列\r\n                for (var j = 0; j < baseCodes.length; j++) {\r\n                    var baseCode = baseCodes[j];\r\n                    \r\n                    // 找到第一个匹配此指标的项目，用于获取名称\r\n                    var sampleItem = null;\r\n                    for (var k = 0; k < childKeys.length; k++) {\r\n                        if (childQuota[childKeys[k]].quotaCode === baseCode) {\r\n                            sampleItem = childQuota[childKeys[k]];\r\n                            break;\r\n                        }\r\n                    }\r\n                    \r\n                    if (!sampleItem) continue;\r\n                    \r\n                    // 添加第二行表头字段（指标名称）\r\n                    var fieldObj = {\r\n                        prop: baseCode,\r\n                        labelKey: Vue.gvUtil.getInzName(\r\n                            sampleItem.quotaEName,\r\n                            sampleItem.quotaCName,\r\n                            sampleItem.quotaLName\r\n                        ),\r\n                        headColor: _this.palettes[i % 2],\r\n                        className: _this.rowPalettes[i % 2],\r\n                        headerAlign: 'center',\r\n                        fieldType: sampleItem.quotaValueType,\r\n                        quotaType: sampleItem.quotaType, // 添加quotaType属性用于判断是否是发展期指标\r\n                        width: \"120px\"\r\n                    };\r\n                    \r\n                    // 查找当前险类的值\r\n                    var currentRiskClassCode = this.form.riskClassCode;\r\n                    var fullFieldName = baseCode + currentRiskClassCode;\r\n                    \r\n                    // 检查该险类的这个指标是否存在\r\n                    if (childQuota[fullFieldName]) {\r\n                        // 如果存在，添加到行数据中\r\n                        row[baseCode] = childQuota[fullFieldName].value;\r\n                    }\r\n                    \r\n                    secondRowFields.push(fieldObj);\r\n                    children.push(fieldObj);\r\n                }\r\n                \r\n                // 创建第一行表头的分组项\r\n                var groupField = {\r\n                    prop: objKeys[i],\r\n                    labelKey: Vue.gvUtil.getInzName(\r\n                        quota.quotaEName,\r\n                        quota.quotaCName,\r\n                        quota.quotaLName\r\n                    ),\r\n                    headerAlign: 'center',\r\n                    headColor: _this.palettes[i % 2],\r\n                    colspan: children.length, // 子项数量\r\n                    children: children\r\n                };\r\n                \r\n                if (children.length > 0) {\r\n                    firstRowFields.push(groupField);\r\n                }\r\n            }\r\n            \r\n            // 设置表头结构\r\n            _this.quotaTable.fields = [];\r\n            _this.quotaTable.firstRowFields = firstRowFields;\r\n            _this.quotaTable.secondRowFields = secondRowFields;\r\n            \r\n            // 添加行数据\r\n            _this.bussQuotaVoList = [row];\r\n            \r\n            // 保存原始数据以便后续使用\r\n            _this.quotaObj = obj;\r\n            _this.initTableHeader();\r\n        },\r\n        \r\n        // 从数据中提取所有险种代码\r\n        extractRiskClassCodes: function(obj) {\r\n            var riskClasses = [];\r\n            var codePattern = /(\\d+)$/; // 匹配字段名末尾的数字\r\n            \r\n            for (var groupKey in obj) {\r\n                var group = obj[groupKey];\r\n                if (!group.childQuota) continue;\r\n                \r\n                for (var fieldKey in group.childQuota) {\r\n                    var matches = fieldKey.match(codePattern);\r\n                    if (matches && matches[1]) {\r\n                        var riskClass = matches[1];\r\n                        if (riskClasses.indexOf(riskClass) === -1) {\r\n                            riskClasses.push(riskClass);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            \r\n            return riskClasses;\r\n        },\r\n\r\n        //初始化合同组输出表头\r\n        initTableHeader: function (){\r\n            var _this = this;\r\n            \r\n            // 添加自定义样式\r\n            var style = document.createElement('style');\r\n            style.innerHTML = `\r\n                .custom-table {\r\n                    width: 100%;\r\n                    border-collapse: collapse;\r\n                    margin-bottom: 20px;\r\n                }\r\n                .custom-table th, .custom-table td {\r\n                    border: 1px solid #dfe6ec;\r\n                    padding: 8px;\r\n                }\r\n                .custom-table thead th {\r\n                    background-color: #f5f7fa;\r\n                    font-weight: bold;\r\n                    color: #606266;\r\n                }\r\n                .custom-table tbody tr:hover {\r\n                    background-color: #f5f7fa;\r\n                }\r\n                .purple-to-gray {\r\n                    background-color: #ECE9F7;\r\n                }\r\n                .light-gray {\r\n                    background-color: #F5F7FA;\r\n                }\r\n            `;\r\n            document.head.appendChild(style);\r\n            \r\n            // 自定义表格渲染\r\n            _this.quotaTable.basic.custom = true;\r\n            \r\n            _this.loading = true;\r\n        },\r\n        //查看发展期指标数据\r\n        onViewQuotaDevelopPeriod: function (row, value, prop) {\r\n            var quotaDef;\r\n            var objKeys = Object.keys(this.quotaObj);\r\n            \r\n            // 构建完整的字段名称，例如 \"#lic_ulae_ratio01\"\r\n            var fullFieldName = prop + this.form.riskClassCode;\r\n            \r\n            // 在表格数据中查找匹配的指标\r\n            for (var i = 0; i < objKeys.length; i++) {\r\n                var childQuota = this.quotaObj[objKeys[i]].childQuota;\r\n                if (!childQuota) continue;\r\n                \r\n                // 直接通过完整字段名查找\r\n                if (childQuota[fullFieldName]) {\r\n                    quotaDef = childQuota[fullFieldName];\r\n                    break;\r\n                } else if (childQuota[prop]) {\r\n                    // 回退到只使用基础指标代码\r\n                    quotaDef = childQuota[prop];\r\n                    break;\r\n                }\r\n            }\r\n            \r\n            if (quotaDef) {\r\n                this.quotaDtl.quota = quotaDef;\r\n                this.findBussQuotaPeriod(prop);\r\n            } else {\r\n                console.error('未找到匹配的指标定义', fullFieldName);\r\n                Vue.gvUtil.message(Vue.gvUtil.getInzTranslate('gError'));\r\n            }\r\n        },\r\n        //关闭指标发展期框\r\n        onFold: function () {\r\n            this.periodShow = false;\r\n        },\r\n\r\n        //查询计量假设配置发展期数据\r\n        findBussQuotaPeriod: function (quotaCode) {\r\n            this.quotaDefName = Vue.gvUtil.getInzName(this.quotaDtl.quota.quotaEName, this.quotaDtl.quota.quotaCName, this.quotaDtl.quota.quotaLName);\r\n            this.periodShow = true;   // 显示页面\r\n            \r\n            // 显示加载指示器或处理逻辑\r\n            this.loading = true;\r\n            \r\n            var param = {\r\n                actionNo : this.licAction.actionNo,\r\n                dimensionValue : this.licAction.icgNo,\r\n                quotaCode: quotaCode,\r\n                riskClassCode: this.form.riskClassCode\r\n            };\r\n            \r\n            var _this = this;\r\n            var url = Vue.gvUtil.getUrl({\r\n                apiName: 'findAtrBussQuotaDataDetail',\r\n                contextName: 'actuarial'\r\n            });\r\n            \r\n            Vue.gvUtil.http.post(url, param).then(function (res) {\r\n                if (res.resCode === '0000' && res.resData) {\r\n                    _this.bussQuotaDevelopVoList = [];\r\n                    var obj = res.resData;\r\n                    var objKeys = Object.keys(obj);\r\n                    \r\n                    if (Vue.gvUtil.isEmpty(obj) || objKeys.length == 0) {\r\n                        param.dimensionValue = _this.licAction.portfolioNo;\r\n                        Vue.gvUtil.http.post(url, param).then(function (res) {\r\n                            if (res.resCode === '0000') {\r\n                                obj = res.resData;\r\n                                _this.initQuotaDetailTableAndData(obj);\r\n                            }\r\n                        }).finally(function() {\r\n                            _this.loading = false;\r\n                        });\r\n                    } else {\r\n                        _this.initQuotaDetailTableAndData(obj);\r\n                        _this.loading = false;\r\n                    }\r\n                } else {\r\n                    _this.loading = false;\r\n                }\r\n            }).catch(function() {\r\n                _this.loading = false;\r\n            }).finally(function() {\r\n                _this.periodShow = true;\r\n            });\r\n        },\r\n\r\n        // 更新发展期数据处理方法\r\n        initQuotaDetailTableAndData: function (obj) {\r\n            var _this = this;\r\n            \r\n            // 清空当前数据\r\n            _this.bussQuotaDevelopVoList = [];\r\n            _this.developmentColumns = [];\r\n            \r\n            if (!obj) return;\r\n            \r\n            var objKeys = Object.keys(obj);\r\n            if (objKeys.length === 0) return;\r\n            \r\n            // 创建列配置\r\n            for (var i = 0; i < objKeys.length; i++) {\r\n                _this.developmentColumns.push({\r\n                    prop: objKeys[i],\r\n                    label: objKeys[i]\r\n                });\r\n            }\r\n            \r\n            // 创建行数据\r\n            var rowData = {\r\n                quotaPeriod: _this.quotaDefName || '发展期'\r\n            };\r\n            \r\n            // 填充行数据\r\n            for (var i = 0; i < objKeys.length; i++) {\r\n                var key = objKeys[i];\r\n                if (obj[key] && !Vue.gvUtil.isEmpty(obj[key].quotaValue)) {\r\n                    rowData[key] = obj[key].quotaValue; // 使用原始值，由模板负责格式化\r\n                } else {\r\n                    rowData[key] = '';\r\n                }\r\n            }\r\n            \r\n            // 添加行数据\r\n            _this.bussQuotaDevelopVoList.push(rowData);\r\n        },\r\n\r\n        handleClick: function (tab, event) {\r\n            this.$nextTick(() => {\r\n                this.searchList(tab.name);\r\n            })\r\n        },\r\n\r\n        getParamsMixin: function getParamsMixin(params) {\r\n            this.cacheFilters = Object.assign({\r\n                _pageSize: this.mixinObject.searchSet.pageSize,\r\n                _pageNo: this.mixinObject.searchSet.pageNo\r\n            }, params);\r\n            return this.cacheFilters;\r\n        },\r\n\r\n        /**\r\n         * 页码变动\r\n         * @param val 码数\r\n         */\r\n        onHandleCurrentChange: function onHandleCurrentChange(val) {\r\n            if (typeof val === 'undefined') {\r\n                return;\r\n            }\r\n            this.mixinObject.searchSet.pageNo = val - 1;\r\n            this.mixinObject.isInit = true;\r\n            this.searchList('uprDetailsVoList');\r\n        },\r\n\r\n        /**\r\n         * 查询行数变动\r\n         * @param 行数\r\n         */\r\n        onHandleSizeChange: function onHandleSizeChange(val) {\r\n            this.mixinObject.searchSet.pageSize = val;\r\n            this.mixinObject.isInit = true;\r\n            this.searchList('uprDetailsVoList');\r\n        },\r\n\r\n        /**\r\n         * 获取查询数据\r\n         */\r\n        searchList: function searchList(tabName) {\r\n            tabName = Vue.gvUtil.isEmpty(this.lrcLicTab)? tabName : this.lrcLicTab;\r\n            var urlParams={\r\n                actionNo: this.form.actionNo,\r\n                icgNo: this.form.icgNo,\r\n                businessSourceCode:this.licAction.businessSourceCode,\r\n                riskClassCode: this.form.riskClassCode,\r\n                feeType: tabName,\r\n            };\r\n            var voName= 'lrcCashFlowVoList';\r\n            if (!this.mixinObject.isInit) {\r\n                this.mixinObject.searchSet.pageNo = 0;\r\n                this.mixinObject.searchSet.currentPage = 1;\r\n            } else {\r\n                this.mixinObject.isInit = false;\r\n            }\r\n            var params = this.getParamsMixin(),\r\n                url = Vue.gvUtil.getUrl({\r\n                    apiName: 'findLrcDataDetail',\r\n                    contextName: 'actuarial',\r\n                    serachParms: { _pageSize: params._pageSize, _pageNo: params._pageNo }\r\n                }),\r\n                _this = this,\r\n                list = [];\r\n            Vue.gvUtil.http.post(url, urlParams).then(function (res) {\r\n                if (res.resCode === '0000') {\r\n                    _this.uprDetailsVoList = res.resData[voName].content;\r\n                    _this.$set(_this.tabMap[tabName],'list',res.resData[voName].content)\r\n                    _this.mixinObject.searchSet.total = res.resData[voName]['total'] ? res.resData[voName].total : res.resData[voName].totalElements;\r\n                    _this.lrcLicTab = tabName;\r\n                } else {\r\n                    _this.mixinObject.searchSet.total = 0;\r\n                    _this.lrcLicTab = tabName;\r\n                }\r\n            });\r\n        },\r\n\r\n        handleSetList(list){\r\n            let tableObj = this.tabMap[this.lrcLicTab]\r\n            this.$set(tableObj,'list',list)\r\n            this.$set(this.tabMap,this.lrcLicTab,tableObj)\r\n\r\n        },\r\n    },\r\n    mounted() {\r\n        this.$nextTick(() => {\r\n            this.showRiskClassCode = this.licAction && (this.licAction.businessSourceCode === 'DD' || this.licAction.businessSourceCode === 'TI');\r\n        });\r\n    },\r\n}\r\n\r\n</script>"], "mappings": ";;;;;;;;;;;;;;AA2JA,OAAAA,SAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACAC,IAAA;IACAC,SAAA;IACAC,KAAA;IACAC,yBAAA;IACAC,EAAA;IACAC,SAAA;IACAC,SAAA;IACAC,QAAA;IACAC,QAAA;IACAC,WAAA;IACAC,QAAA;IACAC,QAAA;IACAC,WAAA;IACAC,MAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;QACAL,QAAA;QACAM,UAAA;QACAX,SAAA;QACAM,QAAA;QACAM,QAAA;QACAC,kBAAA;QACAT,WAAA;QACAD,QAAA;QACAD,QAAA;QACAY,SAAA;QACAC,OAAA;QACAC,OAAA;QACAT,WAAA;QACAN,SAAA;QACAgB,YAAA;QACAC,QAAA;QACAC,aAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,eAAA;QACAC,eAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,eAAA;MACA;MACAC,MAAA;MACAC,UAAA;MACAC,UAAA;MAAA;;MAEAC,UAAA;QACAC,EAAA;UACAC,0BAAA,GACA,oDACA,gFACA,2EACA,sDACA,2EACA;UACAC,yBAAA,GACA,oDACA,gFACA,+FACA,sDACA,2EACA;UACAC,uBAAA,GACA,oDACA,gFACA,+FACA,sDACA,2EACA,wCACA;UACAC,0BAAA,GACA,oDACA,gFACA,+FACA,sDACA,2EACA;UACAC,2BAAA,GACA,oDACA,gFACA,+FACA,sDACA,2EACA;UACAC,wBAAA,GACA,oDACA,gFACA,+FACA,sDACA,2EACA;UACAC,uBAAA,GACA,oDACA,gFACA,+FACA,sDACA,2EACA;UACAC,wBAAA,GACA,oDACA,gFACA,+FACA,sDACA,2EACA;UACAC,qBAAA,GACA,oDACA,gFACA,+FACA,sDACA,2EACA;UACAC,wBAAA,GACA,oDACA,gFACA,+FACA,sDACA,2EACA;UACAC,yBAAA,GACA,oDACA,gFACA,+FACA,sDACA,2EACA;UACAC,sBAAA,GACA,wGACA;UACAC,sBAAA,GACA,wGACA;UACAC,sBAAA,GACA,wGACA;UACAC,qBAAA,GACA,wGACA;UACAC,mBAAA,GACA,wGACA;QAEA;QACAC,EAAA;UACAhB,0BAAA,GACA,mEACA,wDACA,+DACA,gDACA,+BACA;UACAC,yBAAA,GACA,mEACA,wDACA,+DACA,gDACA;UACAC,uBAAA,GACA,mEACA,wDACA,+DACA,gDACA,uDACA;UACAC,0BAAA,GACA,mEACA,wDACA,+DACA,gDACA;UACAC,2BAAA,GACA,mEACA,wDACA,+DACA,gDACA;UACAC,wBAAA,GACA,mEACA,wDACA,+DACA,gDACA;UACAC,uBAAA,GACA,mEACA,wDACA,+DACA,gDACA;UACAE,qBAAA,GACA,mEACA,wDACA,+DACA,gDACA;UACAC,wBAAA,GACA,mEACA,wDACA,+DACA,gDACA;UACAC,yBAAA,GACA,mEACA,wDACA,+DACA,gDACA;UACAC,sBAAA,GACA,wHACA;UACAC,sBAAA,GACA,wHACA;UACAC,sBAAA,GACA,wHACA;UACAC,qBAAA,GACA,wHACA;UACAC,mBAAA,GACA,wHACA;QACA;QACAE,EAAA;UACAjB,0BAAA,GACA,wJACA,8EACA,+BACA;UACAkB,0BAAA,GACA,wJACA,8DACA,gCACA,+BACA;UACAC,yBAAA,GACA,wJACA,8EACA,+BACA;UACAlB,yBAAA,GACA,wJACA,8EACA;UACAmB,0BAAA;QAGA;QACAC,EAAA;UACArB,0BAAA,GACA,yFACA,8EACA,+BACA;UACAkB,0BAAA,GACA,yFACA,8DACA,gCACA,+BACA;QACA;QACAI,EAAA;UACAtB,0BAAA,GACA,4HACA,8EACA,+BACA;UACAuB,4BAAA,GACA,+DACA,8DACA,gCACA,+BACA;UACAjB,uBAAA,GACA,4HACA,8EACA,+BACA;UACAL,yBAAA,GACA,4HACA,8EACA;QACA;MACA;MACAuB,cAAA;MACAC,KAAA;MACAC,iBAAA;MACAC,aAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA;UACA,OAAAA,IAAA,CAAAC,OAAA,KAAAC,IAAA,CAAAC,GAAA;QACA;MACA;MACAC,UAAA;MAEA;MACA;MACAC,UAAA;QACAC,KAAA;UACAC,WAAA;UACAC,SAAA;UACAC,MAAA;QACA;QACAC,MAAA;QACAC,cAAA;QAAA;QACAC,eAAA;MACA;MACA;MACAC,cAAA;QACAP,KAAA;UACAQ,SAAA;UACAN,SAAA;UACAD,WAAA;QACA;QACAG,MAAA;UACAK,IAAA;UAAA;UACAC,QAAA;UACAC,QAAA;UACAC,mBAAA;UACAC,KAAA;QACA;MACA;MACAC,kBAAA;MAAA;MACAC,QAAA;QACAC,KAAA;MACA;MACAC,eAAA;MACAC,sBAAA;MACAC,QAAA;MACAC,OAAA;MACAC,UAAA;MACAC,YAAA;MACAC,QAAA;MACAC,WAAA;MACAC,SAAA;MACAC,aAAA;MACAC,MAAA,GACA;MACAC,iBAAA;IACA;EACA;EACAC,KAAA;IACAtC,iBAAA,WAAAA,kBAAAuC,CAAA,EAAAC,CAAA;MACA,CAAAD,CAAA,SAAAE,KAAA;IACA;IACA,yCAAAC,4BAAAC,MAAA;MACA,KAAAN,iBAAA,GAAAM,MAAA,aAAAA,MAAA;IACA;IACA,+BAAAC,kBAAAD,MAAA,EAAAE,MAAA;MACA;MACA,IAAAF,MAAA,IAAAA,MAAA,KAAAE,MAAA;QACA,KAAAC,qBAAA;MACA;IACA;EACA;EAEAC,OAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MACA,IAAAC,KAAA;QACAC,GAAA;MACA,KAAAC,KAAA,CAAArG,IAAA,CAAAsG,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAC,GAAA,CAAAC,MAAA,CAAAC,OAAA;YACAC,GAAA,EAAAH,GAAA,CAAAC,MAAA,CAAAG,eAAA;UACA,GAAAC,IAAA;YACA;YACAT,GAAA,GAAAI,GAAA,CAAAC,MAAA,CAAAK,MAAA;cACAC,OAAA;cACAC,WAAA;YACA;YACAR,GAAA,CAAAC,MAAA,CAAAQ,IAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAD,KAAA,CAAAnG,IAAA,EAAA6G,IAAA,WAAAM,GAAA;cACA,IAAAhB,KAAA,CAAAiB,QAAA;gBACA;cAAA,CACA;gBACAjB,KAAA,CAAAkB,aAAA,CAAAF,GAAA;cACA;cACA,IAAAA,GAAA,CAAAG,OAAA;gBACAX,GAAA,EAAAH,GAAA,CAAAC,MAAA,CAAAG,eAAA;cACA;YAEA;UACA,GAAAW,KAAA;QACA;UACAf,GAAA,CAAAC,MAAA,CAAAe,OAAA,CAAAhB,GAAA,CAAAC,MAAA,CAAAG,eAAA;UACA;QACA;MACA;IACA;IACA;IACAa,SAAA,WAAAA,UAAAC,QAAA;MACA,IAAAC,WAAA,QAAA3H,IAAA,CAAA2H,WAAA;MACA,IAAA1H,UAAA,QAAAD,IAAA,CAAAC,UAAA;MACA,KAAAoG,KAAA,CAAAqB,QAAA,EAAAE,WAAA;MACA,KAAA5H,IAAA,CAAA2H,WAAA;MACA,SAAA1I,IAAA;QACA,KAAAe,IAAA,CAAA2H,WAAA,GAAAA,WAAA;QACA,KAAA3H,IAAA,CAAAC,UAAA,GAAAA,UAAA;MACA;IACA;IACA;IACA4H,OAAA,WAAAA,QAAA;MACA,KAAA3E,iBAAA;MACA,KAAAyC,KAAA;IACA;IACAmC,cAAA,WAAAA,eAAAC,GAAA;MACA,KAAA/H,IAAA,CAAAgI,YAAA,GAAAD,GAAA,CAAAE,IAAA;IACA;IAEA;IACAC,QAAA,WAAAA,SAAA;MACA,SAAAjJ,IAAA;QACA,KAAAkJ,WAAA;MACA;MACA,SAAAlJ,IAAA;QACA,KAAAmC,UAAA;QACA,KAAAC,UAAA;QACA,KAAA2B,cAAA;MACA;MACA,SAAA/D,IAAA;QACA,IAAAmJ,IAAA,GAAAC,cAAA,CAAAC,OAAA;QACA,IAAAF,IAAA;UACAA,IAAA,GAAAG,IAAA,CAAAC,KAAA,CAAAJ,IAAA;UACA,KAAApI,IAAA,CAAAL,QAAA,GAAAyI,IAAA,CAAAK,YAAA;QAEA;QACA,KAAAC,cAAA;MACA;IACA;IACA;IACAA,cAAA,WAAAA,eAAA;MACA,KAAA1I,IAAA,CAAAC,UAAA,GAAAuG,GAAA,CAAAC,MAAA,CAAAkC,aAAA;IACA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,KAAA3F,KAAA;QACAhD,UAAA;UACA4I,OAAA;UACAC,QAAA;UACAtB,OAAA,EAAAhB,GAAA,CAAAC,MAAA,CAAAG,eAAA;QACA;UACAiC,OAAA;UACAC,QAAA;UACAtB,OAAA,EAAAhB,GAAA,CAAAC,MAAA,CAAAG,eAAA;QACA;QACAmC,YAAA;UACAF,OAAA;UACAC,QAAA;UACAtB,OAAA,EAAAhB,GAAA,CAAAC,MAAA,CAAAG,eAAA;QACA;UACAiC,OAAA;UACAC,QAAA;UACAtB,OAAA,EAAAhB,GAAA,CAAAC,MAAA,CAAAG,eAAA;QACA;QACArH,SAAA;UACAsJ,OAAA;UACAC,QAAA;UACAtB,OAAA,EAAAhB,GAAA,CAAAC,MAAA,CAAAG,eAAA;QACA;UACAiC,OAAA;UACAC,QAAA;UACAtB,OAAA,EAAAhB,GAAA,CAAAC,MAAA,CAAAG,eAAA;QACA;QACAhH,QAAA;UACAiJ,OAAA;UACAC,QAAA;UACAtB,OAAA,EAAAhB,GAAA,CAAAC,MAAA,CAAAG,eAAA;QACA;UACAiC,OAAA;UACAC,QAAA;UACAtB,OAAA,EAAAhB,GAAA,CAAAC,MAAA,CAAAG,eAAA;QACA;QACAnH,QAAA;UACAoJ,OAAA;UACAC,QAAA;UACAtB,OAAA,EAAAhB,GAAA,CAAAC,MAAA,CAAAG,eAAA;QACA;UACAiC,OAAA;UACAC,QAAA;UACAtB,OAAA,EAAAhB,GAAA,CAAAC,MAAA,CAAAG,eAAA;QACA;MACA;IACA;IACAuB,WAAA,WAAAA,YAAA;MACA,IAAAhC,KAAA;QACAC,GAAA,GAAAI,GAAA,CAAAC,MAAA,CAAAK,MAAA;UACAC,OAAA;UACAiC,SAAA;YACA3J,EAAA,EAAA8G,KAAA,CAAAjH,SAAA,CAAAG;UACA;UACA2H,WAAA;QACA;MACAR,GAAA,CAAAC,MAAA,CAAAQ,IAAA,CAAAgC,GAAA,CAAA7C,GAAA,EAAAS,IAAA,WAAAM,GAAA;QACA,IAAAA,GAAA,CAAAG,OAAA;UACA;UACA;UACAH,GAAA,CAAA+B,OAAA,CAAArJ,WAAA,GAAAsG,KAAA,CAAAjH,SAAA,CAAAW,WAAA;UACAsH,GAAA,CAAA+B,OAAA,CAAAC,KAAA,GAAAhD,KAAA,CAAAjH,SAAA,CAAAiK,KAAA;UACAhC,GAAA,CAAA+B,OAAA,CAAAzI,aAAA,GAAA0F,KAAA,CAAAjH,SAAA,CAAAuB,aAAA;UACA0F,KAAA,CAAAnG,IAAA,GAAAmH,GAAA,CAAA+B,OAAA;UACA/C,KAAA,CAAAiD,gBAAA,CAAAjC,GAAA,CAAA+B,OAAA;UACA/C,KAAA,CAAAkD,kBAAA,CAAAlC,GAAA,CAAA+B,OAAA;;UAEA;UACA,IAAA/C,KAAA,CAAAjH,SAAA,CAAAiB,kBAAA,aAAAgG,KAAA,CAAAjH,SAAA,CAAAiB,kBAAA;YACAgG,KAAA,CAAAH,qBAAA;UACA;UAEAG,KAAA,CAAAmD,cAAA,CAAAnD,KAAA,CAAAjH,SAAA,CAAAiB,kBAAA;UACAgG,KAAA,CAAAoD,KAAA,GAAAC,UAAA;YACArD,KAAA,CAAAsD,YAAA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAH,cAAA,WAAAA,eAAAnJ,kBAAA;MACA,IAAAuJ,KAAA;QACAvJ,kBAAA,EAAAA,kBAAA;QACAwJ,QAAA;MACA;MACA,IAAAxD,KAAA;QACAC,GAAA,GAAAI,GAAA,CAAAC,MAAA,CAAAK,MAAA;UACAC,OAAA;UACAC,WAAA;QACA;MACAR,GAAA,CAAAC,MAAA,CAAAQ,IAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAsD,KAAA,EAAA7C,IAAA,WAAAM,GAAA;QACA,IAAAA,GAAA,CAAAG,OAAA;UACA,IAAAsC,SAAA,GAAAzC,GAAA,CAAA+B,OAAA;UACA,KAAA1C,GAAA,CAAAC,MAAA,CAAAoD,OAAA,CAAAD,SAAA;YACAzD,KAAA,CAAAd,aAAA,GAAAuE,SAAA;YACAzD,KAAA,CAAAf,SAAA,GAAAwE,SAAA,IAAAE,MAAA;UACA;QACA;MACA;IACA;IAEAL,YAAA,WAAAA,aAAA;MACA,IAAAtD,KAAA;QACAC,GAAA,GAAAI,GAAA,CAAAC,MAAA,CAAAK,MAAA;UACAC,OAAA;UACAC,WAAA;QACA;MACA,IAAAgC,SAAA;QACAe,QAAA,EAAA5D,KAAA,CAAAnG,IAAA,CAAA+J,QAAA;QACA5J,kBAAA,EAAAgG,KAAA,CAAAjH,SAAA,CAAAiB,kBAAA;QACAgJ,KAAA,EAAAhD,KAAA,CAAAnG,IAAA,CAAAmJ;MACA;MACAhD,KAAA,CAAAhF,MAAA;MACAqF,GAAA,CAAAC,MAAA,CAAAQ,IAAA,CAAAC,IAAA,CAAAd,GAAA,EAAA4C,SAAA,EAAAnC,IAAA,WAAAM,GAAA;QACA,IAAAA,GAAA,CAAAG,OAAA;UACA,SAAA0C,IAAA,IAAA7D,KAAA,CAAAd,aAAA;YACA,IAAA4E,UAAA,GAAA9D,KAAA,CAAA+D,cAAA,CAAA/C,GAAA,CAAA+B,OAAA,CAAAiB,KAAA,EAAAhE,KAAA,CAAAd,aAAA,CAAA2E,IAAA;YACA,IAAAI,GAAA,GAAAjE,KAAA,CAAAd,aAAA,CAAA2E,IAAA,EAAAF,MAAA;YACA,IAAAO,GAAA,GAAAlE,KAAA,CAAAd,aAAA,CAAA2E,IAAA,EAAAF,MAAA;YACA,IAAA/F,MAAA,GAAAjF,SAAA,CAAAwL,YAAA,CAAAnE,KAAA,kBAAAoE,MAAA,CAAApE,KAAA,CAAAjH,SAAA,CAAAiB,kBAAA,MAAAoK,MAAA,CAAAF,GAAA;YACA,IAAAlE,KAAA,CAAAd,aAAA,CAAA2E,IAAA,EAAA/K,IAAA;cACA8E,MAAA,CAAAyG,IAAA,CAAAP,UAAA;YACA;YACA9D,KAAA,CAAAsE,IAAA,CAAAtE,KAAA,CAAAb,MAAA,EAAA8E,GAAA;cACAM,KAAA;gBACA/G,KAAA;kBACAgH,GAAA;kBAAA;kBACAC,EAAA;kBAAA;kBACAC,OAAA;kBAAA;kBACAC,UAAA;gBACA;gBACAC,MAAA;kBAAA;kBACAhB,QAAA,EAAA5D,KAAA,CAAAnG,IAAA,CAAA+J,QAAA;kBACAZ,KAAA,EAAAhD,KAAA,CAAAnG,IAAA,CAAAmJ,KAAA;kBACAhJ,kBAAA,EAAAgG,KAAA,CAAAjH,SAAA,CAAAiB,kBAAA;kBACA6K,OAAA,EAAAZ;gBACA;gBACArG,MAAA,EAAAA;cACA;cAEAkH,IAAA;YACA;UACA;UACA9E,KAAA,CAAA+E,UAAA;QACA;QACA/E,KAAA,CAAAhF,MAAA;MACA;IACA;IAGA+I,cAAA,WAAAA,eAAAiB,OAAA,EAAAC,MAAA;MACA,IAAAC,UAAA;QACA7G,KAAA;QACAF,QAAA;QACAD,QAAA;QACAiH,cAAA;MACA;MACA,IAAAC,UAAA;MACA,IAAAC,KAAA;MACA,IAAAC,GAAA,GAAAN,OAAA,CAAAO,MAAA;MACA,KAAAlF,GAAA,CAAAC,MAAA,CAAAoD,OAAA,CAAAuB,MAAA,CAAAO,UAAA;QACAH,KAAA,GAAAJ,MAAA,CAAAO,UAAA;MACA;MACA,KAAAnF,GAAA,CAAAC,MAAA,CAAAoD,OAAA,CAAAuB,MAAA,CAAAQ,QAAA;QACAH,GAAA,GAAAL,MAAA,CAAAQ,QAAA;MACA;MACA,KAAAJ,KAAA,EAAAA,KAAA,IAAAC,GAAA,EAAAD,KAAA;QACAD,UAAA,CAAAJ,OAAA,CAAAK,KAAA,EAAAK,QAAA;UACAzH,IAAA,EAAA+G,OAAA,CAAAK,KAAA,EAAAK,QAAA;UACAC,UAAA,EAAAX,OAAA,CAAAK,KAAA,EAAAK,QAAA;UACAE,UAAA,EAAAZ,OAAA,CAAAK,KAAA,EAAAK,QAAA;UACAG,UAAA,EAAAb,OAAA,CAAAK,KAAA,EAAAK,QAAA;UACAI,SAAA;QACA;MACA;MACAZ,UAAA,CAAAC,cAAA,GAAA9E,GAAA,CAAAC,MAAA,CAAAyF,UAAA,CAAAX,UAAA;MACA,OAAAF,UAAA;IACA;IAGAc,YAAA,WAAAA,aAAAnB,OAAA;MACA,IAAA7E,KAAA;QACAC,GAAA,GAAAI,GAAA,CAAAC,MAAA,CAAAK,MAAA;UACAC,OAAA;UACAC,WAAA;QACA;MACA,IAAAgC,SAAA;QACAe,QAAA,EAAA5D,KAAA,CAAAnG,IAAA,CAAA+J,QAAA;QACAqC,KAAA,EAAAjG,KAAA,CAAAnG,IAAA,CAAAoM,KAAA;QACAjM,kBAAA,EAAAgG,KAAA,CAAAjH,SAAA,CAAAiB,kBAAA;QACA6K,OAAA,EAAA7E,KAAA,CAAAjH,SAAA,CAAAiB;MACA;MACAqG,GAAA,CAAAC,MAAA,CAAAQ,IAAA,CAAAC,IAAA,CAAAd,GAAA,EAAA4C,SAAA,EAAAnC,IAAA,WAAAM,GAAA;QACA,IAAAA,GAAA,CAAAG,OAAA;UACAnB,KAAA,CAAAb,MAAA,CAAA2D,GAAA,CAAA+B,OAAA,EAAAC,IAAA,GAAA9D,GAAA,CAAA+B,OAAA,CAAAmD,iBAAA;QACA;MACA;IACA;IAGAC,iBAAA,WAAAA,kBAAAC,GAAA;MACA,IAAAA,GAAA;QACA,KAAAvM,IAAA,CAAAJ,QAAA,GAAA2M,GAAA,CAAAC,YAAA;MACA;QACA,KAAAxM,IAAA,CAAAJ,QAAA;MACA;IACA;IACA;IACAwJ,gBAAA,WAAAA,iBAAAqD,QAAA;MACA,IAAAtG,KAAA;MACAA,KAAA,CAAAnG,IAAA,CAAAC,UAAA,GAAAwM,QAAA,CAAAxM,UAAA,YAAAuG,GAAA,CAAAC,MAAA,CAAAiG,UAAA,CAAAD,QAAA,CAAAE,WAAA,EAAAF,QAAA,CAAAG,WAAA,EAAAH,QAAA,CAAAI,WAAA;IACA;IACA;IACAxD,kBAAA,WAAAA,mBAAAyD,UAAA;MACA,IAAA3G,KAAA;MACA,IAAAK,GAAA,CAAAC,MAAA,CAAAoD,OAAA,CAAArD,GAAA,CAAAC,MAAA,CAAAiG,UAAA,CAAAI,UAAA,CAAAC,aAAA,EAAAD,UAAA,CAAAE,aAAA,EAAAF,UAAA,CAAAG,aAAA;QACA,OAAA9G,KAAA,CAAAnG,IAAA,CAAAO,YAAA,GAAAuM,UAAA,CAAAlN,QAAA;MACA;MACAuG,KAAA,CAAAnG,IAAA,CAAAO,YAAA,GAAAuM,UAAA,CAAAlN,QAAA,YAAA4G,GAAA,CAAAC,MAAA,CAAAiG,UAAA,CAAAI,UAAA,CAAAC,aAAA,EAAAD,UAAA,CAAAE,aAAA,EAAAF,UAAA,CAAAG,aAAA;IACA;IACA;IACA5F,aAAA,WAAAA,cAAAtH,IAAA;MACAyG,GAAA,CAAAC,MAAA,CAAAe,OAAA,CAAAhB,GAAA,CAAAC,MAAA,CAAAG,eAAA;MACA,KAAAjB,KAAA;MACA,KAAAA,KAAA;MACA,KAAAkC,OAAA;IACA;IAEA;IACA;IACAqF,SAAA,WAAAA,UAAAX,GAAA,EAAAY,IAAA,EAAA/I,IAAA;MACA,IAAA+I,IAAA;QACA,KAAAC,wBAAA,CAAAb,GAAA,QAAAnI,IAAA;MACA;IACA;IACA;IACA4B,qBAAA,WAAAA,sBAAA;MACA,IAAAqH,QAAA;QACAtD,QAAA,OAAA7K,SAAA,CAAA6K,QAAA;QACAuD,cAAA,OAAApO,SAAA,CAAAiK,KAAA;QACA1I,aAAA,OAAAT,IAAA,CAAAS;MACA;MACA,IAAA0F,KAAA;QACAC,GAAA,GAAAI,GAAA,CAAAC,MAAA,CAAAK,MAAA;UACAC,OAAA;UACAC,WAAA;QACA;MACAR,GAAA,CAAAC,MAAA,CAAAQ,IAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAiH,QAAA,EAAAxG,IAAA,WAAAM,GAAA;QACA,IAAAA,GAAA,CAAAG,OAAA;UACA,IAAAiG,GAAA,GAAApG,GAAA,CAAA+B,OAAA,CAAAnJ,IAAA;UACA,IAAAyN,OAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAH,GAAA;UACA,IAAA/G,GAAA,CAAAC,MAAA,CAAAoD,OAAA,CAAA0D,GAAA,KAAAC,OAAA,CAAA9B,MAAA;YACA2B,QAAA,CAAAC,cAAA,GAAAnH,KAAA,CAAAjH,SAAA,CAAAW,WAAA;YACA2G,GAAA,CAAAC,MAAA,CAAAQ,IAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAiH,QAAA,EAAAxG,IAAA,WAAAM,GAAA;cACA,IAAAA,GAAA,CAAAG,OAAA;gBACAiG,GAAA,GAAApG,GAAA,CAAA+B,OAAA,CAAAnJ,IAAA;gBACAoG,KAAA,CAAAwH,qBAAA,CAAAJ,GAAA;cACA;YACA;UACA;YACApH,KAAA,CAAAwH,qBAAA,CAAAJ,GAAA;UACA;QACA;MACA;IACA;IACAI,qBAAA,WAAAA,sBAAAJ,GAAA;MACA,IAAAxN,IAAA;MACA,IAAAoG,KAAA;MACA,IAAAqH,OAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAH,GAAA;;MAEA;MACA,IAAAvJ,cAAA;MACA,IAAAC,eAAA;;MAEA;MACAD,cAAA,CAAAwG,IAAA;QACApG,IAAA;QACAC,QAAA;QACAG,KAAA;QACAoJ,OAAA;QAAA;QACAC,WAAA;QACAC,SAAA,EAAA3H,KAAA,CAAAjB,QAAA;MACA;;MAEA;MACA,IAAAqH,GAAA;QACAwB,aAAA,OAAA/N,IAAA,CAAAS,aAAA;MACA;;MAEA;MACA,SAAAuN,CAAA,MAAAA,CAAA,GAAAR,OAAA,CAAA9B,MAAA,EAAAsC,CAAA;QACA,IAAArJ,KAAA,GAAA4I,GAAA,CAAAC,OAAA,CAAAQ,CAAA;QACA,IAAAC,QAAA;;QAEA;QACA,KAAAtJ,KAAA,CAAAuJ,UAAA;;QAEA;QACA,IAAAC,eAAA,OAAAC,GAAA;QACA,IAAAF,UAAA,GAAAvJ,KAAA,CAAAuJ,UAAA;QACA,IAAAG,SAAA,GAAAZ,MAAA,CAAAC,IAAA,CAAAQ,UAAA;QAEA,SAAAI,CAAA,MAAAA,CAAA,GAAAD,SAAA,CAAA3C,MAAA,EAAA4C,CAAA;UACA,IAAAC,SAAA,GAAAL,UAAA,CAAAG,SAAA,CAAAC,CAAA;UACA;UACA,IAAAE,QAAA,GAAAD,SAAA,CAAAE,SAAA;UACAN,eAAA,CAAAO,GAAA,CAAAF,QAAA;QACA;;QAEA;QACA,IAAAG,SAAA,GAAAC,KAAA,CAAAC,IAAA,CAAAV,eAAA;;QAEA;QACA,SAAAG,CAAA,MAAAA,CAAA,GAAAK,SAAA,CAAAjD,MAAA,EAAA4C,CAAA;UACA,IAAAE,QAAA,GAAAG,SAAA,CAAAL,CAAA;;UAEA;UACA,IAAAQ,UAAA;UACA,SAAAC,CAAA,MAAAA,CAAA,GAAAV,SAAA,CAAA3C,MAAA,EAAAqD,CAAA;YACA,IAAAb,UAAA,CAAAG,SAAA,CAAAU,CAAA,GAAAN,SAAA,KAAAD,QAAA;cACAM,UAAA,GAAAZ,UAAA,CAAAG,SAAA,CAAAU,CAAA;cACA;YACA;UACA;UAEA,KAAAD,UAAA;;UAEA;UACA,IAAAE,QAAA;YACA5K,IAAA,EAAAoK,QAAA;YACAnK,QAAA,EAAAmC,GAAA,CAAAC,MAAA,CAAAiG,UAAA,CACAoC,UAAA,CAAAhD,UAAA,EACAgD,UAAA,CAAA/C,UAAA,EACA+C,UAAA,CAAAG,UACA;YACAnB,SAAA,EAAA3H,KAAA,CAAAjB,QAAA,CAAA8I,CAAA;YACAkB,SAAA,EAAA/I,KAAA,CAAAhB,WAAA,CAAA6I,CAAA;YACAH,WAAA;YACA5B,SAAA,EAAA6C,UAAA,CAAAK,cAAA;YACAC,SAAA,EAAAN,UAAA,CAAAM,SAAA;YAAA;YACA5K,KAAA;UACA;;UAEA;UACA,IAAA6K,oBAAA,QAAArP,IAAA,CAAAS,aAAA;UACA,IAAA6O,aAAA,GAAAd,QAAA,GAAAa,oBAAA;;UAEA;UACA,IAAAnB,UAAA,CAAAoB,aAAA;YACA;YACA/C,GAAA,CAAAiC,QAAA,IAAAN,UAAA,CAAAoB,aAAA,EAAAC,KAAA;UACA;UAEAtL,eAAA,CAAAuG,IAAA,CAAAwE,QAAA;UACAf,QAAA,CAAAzD,IAAA,CAAAwE,QAAA;QACA;;QAEA;QACA,IAAAQ,UAAA;UACApL,IAAA,EAAAoJ,OAAA,CAAAQ,CAAA;UACA3J,QAAA,EAAAmC,GAAA,CAAAC,MAAA,CAAAiG,UAAA,CACA/H,KAAA,CAAAmH,UAAA,EACAnH,KAAA,CAAAoH,UAAA,EACApH,KAAA,CAAAsK,UACA;UACApB,WAAA;UACAC,SAAA,EAAA3H,KAAA,CAAAjB,QAAA,CAAA8I,CAAA;UACAyB,OAAA,EAAAxB,QAAA,CAAAvC,MAAA;UAAA;UACAuC,QAAA,EAAAA;QACA;QAEA,IAAAA,QAAA,CAAAvC,MAAA;UACA1H,cAAA,CAAAwG,IAAA,CAAAgF,UAAA;QACA;MACA;;MAEA;MACArJ,KAAA,CAAAzC,UAAA,CAAAK,MAAA;MACAoC,KAAA,CAAAzC,UAAA,CAAAM,cAAA,GAAAA,cAAA;MACAmC,KAAA,CAAAzC,UAAA,CAAAO,eAAA,GAAAA,eAAA;;MAEA;MACAkC,KAAA,CAAAvB,eAAA,IAAA2H,GAAA;;MAEA;MACApG,KAAA,CAAArB,QAAA,GAAAyI,GAAA;MACApH,KAAA,CAAAuJ,eAAA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAApC,GAAA;MACA,IAAAqC,WAAA;MACA,IAAAC,WAAA;;MAEA,SAAAC,QAAA,IAAAvC,GAAA;QACA,IAAAwC,KAAA,GAAAxC,GAAA,CAAAuC,QAAA;QACA,KAAAC,KAAA,CAAA7B,UAAA;QAEA,SAAA8B,QAAA,IAAAD,KAAA,CAAA7B,UAAA;UACA,IAAA+B,OAAA,GAAAD,QAAA,CAAAE,KAAA,CAAAL,WAAA;UACA,IAAAI,OAAA,IAAAA,OAAA;YACA,IAAA3Q,SAAA,GAAA2Q,OAAA;YACA,IAAAL,WAAA,CAAAO,OAAA,CAAA7Q,SAAA;cACAsQ,WAAA,CAAApF,IAAA,CAAAlL,SAAA;YACA;UACA;QACA;MACA;MAEA,OAAAsQ,WAAA;IACA;IAEA;IACAF,eAAA,WAAAA,gBAAA;MACA,IAAAvJ,KAAA;;MAEA;MACA,IAAAiK,KAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,KAAA,CAAAG,SAAA,o1BAwBA;MACAF,QAAA,CAAAG,IAAA,CAAAC,WAAA,CAAAL,KAAA;;MAEA;MACAjK,KAAA,CAAAzC,UAAA,CAAAC,KAAA,CAAAG,MAAA;MAEAqC,KAAA,CAAApB,OAAA;IACA;IACA;IACAqI,wBAAA,WAAAA,yBAAAb,GAAA,EAAAgD,KAAA,EAAAnL,IAAA;MACA,IAAAsM,QAAA;MACA,IAAAlD,OAAA,GAAAC,MAAA,CAAAC,IAAA,MAAA5I,QAAA;;MAEA;MACA,IAAAwK,aAAA,GAAAlL,IAAA,QAAApE,IAAA,CAAAS,aAAA;;MAEA;MACA,SAAAuN,CAAA,MAAAA,CAAA,GAAAR,OAAA,CAAA9B,MAAA,EAAAsC,CAAA;QACA,IAAAE,UAAA,QAAApJ,QAAA,CAAA0I,OAAA,CAAAQ,CAAA,GAAAE,UAAA;QACA,KAAAA,UAAA;;QAEA;QACA,IAAAA,UAAA,CAAAoB,aAAA;UACAoB,QAAA,GAAAxC,UAAA,CAAAoB,aAAA;UACA;QACA,WAAApB,UAAA,CAAA9J,IAAA;UACA;UACAsM,QAAA,GAAAxC,UAAA,CAAA9J,IAAA;UACA;QACA;MACA;MAEA,IAAAsM,QAAA;QACA,KAAAhM,QAAA,CAAAC,KAAA,GAAA+L,QAAA;QACA,KAAAC,mBAAA,CAAAvM,IAAA;MACA;QACAwM,OAAA,CAAAC,KAAA,eAAAvB,aAAA;QACA9I,GAAA,CAAAC,MAAA,CAAAe,OAAA,CAAAhB,GAAA,CAAAC,MAAA,CAAAG,eAAA;MACA;IACA;IACA;IACAkK,MAAA,WAAAA,OAAA;MACA,KAAA9L,UAAA;IACA;IAEA;IACA2L,mBAAA,WAAAA,oBAAAlC,SAAA;MACA,KAAAxJ,YAAA,GAAAuB,GAAA,CAAAC,MAAA,CAAAiG,UAAA,MAAAhI,QAAA,CAAAC,KAAA,CAAAmH,UAAA,OAAApH,QAAA,CAAAC,KAAA,CAAAoH,UAAA,OAAArH,QAAA,CAAAC,KAAA,CAAAsK,UAAA;MACA,KAAAjK,UAAA;;MAEA;MACA,KAAAD,OAAA;MAEA,IAAA2E,KAAA;QACAK,QAAA,OAAA7K,SAAA,CAAA6K,QAAA;QACAuD,cAAA,OAAApO,SAAA,CAAAiK,KAAA;QACAsF,SAAA,EAAAA,SAAA;QACAhO,aAAA,OAAAT,IAAA,CAAAS;MACA;MAEA,IAAA0F,KAAA;MACA,IAAAC,GAAA,GAAAI,GAAA,CAAAC,MAAA,CAAAK,MAAA;QACAC,OAAA;QACAC,WAAA;MACA;MAEAR,GAAA,CAAAC,MAAA,CAAAQ,IAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAsD,KAAA,EAAA7C,IAAA,WAAAM,GAAA;QACA,IAAAA,GAAA,CAAAG,OAAA,eAAAH,GAAA,CAAA+B,OAAA;UACA/C,KAAA,CAAAtB,sBAAA;UACA,IAAA0I,GAAA,GAAApG,GAAA,CAAA+B,OAAA;UACA,IAAAsE,OAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAH,GAAA;UAEA,IAAA/G,GAAA,CAAAC,MAAA,CAAAoD,OAAA,CAAA0D,GAAA,KAAAC,OAAA,CAAA9B,MAAA;YACAhC,KAAA,CAAA4D,cAAA,GAAAnH,KAAA,CAAAjH,SAAA,CAAAW,WAAA;YACA2G,GAAA,CAAAC,MAAA,CAAAQ,IAAA,CAAAC,IAAA,CAAAd,GAAA,EAAAsD,KAAA,EAAA7C,IAAA,WAAAM,GAAA;cACA,IAAAA,GAAA,CAAAG,OAAA;gBACAiG,GAAA,GAAApG,GAAA,CAAA+B,OAAA;gBACA/C,KAAA,CAAA4K,2BAAA,CAAAxD,GAAA;cACA;YACA,GAAAyD,OAAA;cACA7K,KAAA,CAAApB,OAAA;YACA;UACA;YACAoB,KAAA,CAAA4K,2BAAA,CAAAxD,GAAA;YACApH,KAAA,CAAApB,OAAA;UACA;QACA;UACAoB,KAAA,CAAApB,OAAA;QACA;MACA,GAAAwC,KAAA;QACApB,KAAA,CAAApB,OAAA;MACA,GAAAiM,OAAA;QACA7K,KAAA,CAAAnB,UAAA;MACA;IACA;IAEA;IACA+L,2BAAA,WAAAA,4BAAAxD,GAAA;MACA,IAAApH,KAAA;;MAEA;MACAA,KAAA,CAAAtB,sBAAA;MACAsB,KAAA,CAAA1B,kBAAA;MAEA,KAAA8I,GAAA;MAEA,IAAAC,OAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAH,GAAA;MACA,IAAAC,OAAA,CAAA9B,MAAA;;MAEA;MACA,SAAAsC,CAAA,MAAAA,CAAA,GAAAR,OAAA,CAAA9B,MAAA,EAAAsC,CAAA;QACA7H,KAAA,CAAA1B,kBAAA,CAAA+F,IAAA;UACApG,IAAA,EAAAoJ,OAAA,CAAAQ,CAAA;UACAiD,KAAA,EAAAzD,OAAA,CAAAQ,CAAA;QACA;MACA;;MAEA;MACA,IAAAkD,OAAA;QACAC,WAAA,EAAAhL,KAAA,CAAAlB,YAAA;MACA;;MAEA;MACA,SAAA+I,CAAA,MAAAA,CAAA,GAAAR,OAAA,CAAA9B,MAAA,EAAAsC,CAAA;QACA,IAAA5D,GAAA,GAAAoD,OAAA,CAAAQ,CAAA;QACA,IAAAT,GAAA,CAAAnD,GAAA,MAAA5D,GAAA,CAAAC,MAAA,CAAAoD,OAAA,CAAA0D,GAAA,CAAAnD,GAAA,EAAAgH,UAAA;UACAF,OAAA,CAAA9G,GAAA,IAAAmD,GAAA,CAAAnD,GAAA,EAAAgH,UAAA;QACA;UACAF,OAAA,CAAA9G,GAAA;QACA;MACA;;MAEA;MACAjE,KAAA,CAAAtB,sBAAA,CAAA2F,IAAA,CAAA0G,OAAA;IACA;IAEAG,WAAA,WAAAA,YAAAC,GAAA,EAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,SAAA;QACAD,MAAA,CAAAtG,UAAA,CAAAoG,GAAA,CAAAvS,IAAA;MACA;IACA;IAEA2S,cAAA,WAAAA,eAAAC,MAAA;MACA,KAAAC,YAAA,GAAAnE,MAAA,CAAAoE,MAAA;QACAC,SAAA,OAAAC,WAAA,CAAAC,SAAA,CAAAC,QAAA;QACAC,OAAA,OAAAH,WAAA,CAAAC,SAAA,CAAAG;MACA,GAAAR,MAAA;MACA,YAAAC,YAAA;IACA;IAEA;AACA;AACA;AACA;IACAQ,qBAAA,WAAAA,sBAAArK,GAAA;MACA,WAAAA,GAAA;QACA;MACA;MACA,KAAAgK,WAAA,CAAAC,SAAA,CAAAG,MAAA,GAAApK,GAAA;MACA,KAAAgK,WAAA,CAAAM,MAAA;MACA,KAAAnH,UAAA;IACA;IAEA;AACA;AACA;AACA;IACAoH,kBAAA,WAAAA,mBAAAvK,GAAA;MACA,KAAAgK,WAAA,CAAAC,SAAA,CAAAC,QAAA,GAAAlK,GAAA;MACA,KAAAgK,WAAA,CAAAM,MAAA;MACA,KAAAnH,UAAA;IACA;IAEA;AACA;AACA;IACAA,UAAA,WAAAA,WAAAqH,OAAA;MACAA,OAAA,GAAA/L,GAAA,CAAAC,MAAA,CAAAoD,OAAA,MAAAzE,SAAA,IAAAmN,OAAA,QAAAnN,SAAA;MACA,IAAA4D,SAAA;QACAe,QAAA,OAAA/J,IAAA,CAAA+J,QAAA;QACAZ,KAAA,OAAAnJ,IAAA,CAAAmJ,KAAA;QACAhJ,kBAAA,OAAAjB,SAAA,CAAAiB,kBAAA;QACAM,aAAA,OAAAT,IAAA,CAAAS,aAAA;QACAuK,OAAA,EAAAuH;MACA;MACA,IAAAC,MAAA;MACA,UAAAT,WAAA,CAAAM,MAAA;QACA,KAAAN,WAAA,CAAAC,SAAA,CAAAG,MAAA;QACA,KAAAJ,WAAA,CAAAC,SAAA,CAAAS,WAAA;MACA;QACA,KAAAV,WAAA,CAAAM,MAAA;MACA;MACA,IAAAV,MAAA,QAAAD,cAAA;QACAtL,GAAA,GAAAI,GAAA,CAAAC,MAAA,CAAAK,MAAA;UACAC,OAAA;UACAC,WAAA;UACA0L,WAAA;YAAAZ,SAAA,EAAAH,MAAA,CAAAG,SAAA;YAAAI,OAAA,EAAAP,MAAA,CAAAO;UAAA;QACA;QACA/L,KAAA;QACA8E,IAAA;MACAzE,GAAA,CAAAC,MAAA,CAAAQ,IAAA,CAAAC,IAAA,CAAAd,GAAA,EAAA4C,SAAA,EAAAnC,IAAA,WAAAM,GAAA;QACA,IAAAA,GAAA,CAAAG,OAAA;UACAnB,KAAA,CAAAwM,gBAAA,GAAAxL,GAAA,CAAA+B,OAAA,CAAAsJ,MAAA,EAAAI,OAAA;UACAzM,KAAA,CAAAsE,IAAA,CAAAtE,KAAA,CAAAb,MAAA,CAAAiN,OAAA,WAAApL,GAAA,CAAA+B,OAAA,CAAAsJ,MAAA,EAAAI,OAAA;UACAzM,KAAA,CAAA4L,WAAA,CAAAC,SAAA,CAAAa,KAAA,GAAA1L,GAAA,CAAA+B,OAAA,CAAAsJ,MAAA,aAAArL,GAAA,CAAA+B,OAAA,CAAAsJ,MAAA,EAAAK,KAAA,GAAA1L,GAAA,CAAA+B,OAAA,CAAAsJ,MAAA,EAAAM,aAAA;UACA3M,KAAA,CAAAf,SAAA,GAAAmN,OAAA;QACA;UACApM,KAAA,CAAA4L,WAAA,CAAAC,SAAA,CAAAa,KAAA;UACA1M,KAAA,CAAAf,SAAA,GAAAmN,OAAA;QACA;MACA;IACA;IAEAQ,aAAA,WAAAA,cAAA9H,IAAA;MACA,IAAA+H,QAAA,QAAA1N,MAAA,MAAAF,SAAA;MACA,KAAAqF,IAAA,CAAAuI,QAAA,UAAA/H,IAAA;MACA,KAAAR,IAAA,MAAAnF,MAAA,OAAAF,SAAA,EAAA4N,QAAA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA,KAAAzB,SAAA;MACAyB,MAAA,CAAA3N,iBAAA,GAAA2N,MAAA,CAAAhU,SAAA,KAAAgU,MAAA,CAAAhU,SAAA,CAAAiB,kBAAA,aAAA+S,MAAA,CAAAhU,SAAA,CAAAiB,kBAAA;IACA;EACA;AACA"}]}