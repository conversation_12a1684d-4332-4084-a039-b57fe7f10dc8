package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.constant.ActuarialConstant;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.*;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface AtrConfQuotaDataService {

    /**
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 查询假设对象列表,按业务维度汇总假设值
     * @Return Page
     */
    Page<Map<String,Object>> findAtrConfQuotaList(AtrConfQuotaVo atrConfQuotaVo, Pageable pageable);


    List<AtrConfQuotaVo> findList(AtrConfQuotaVo atrConfQuotaVo);


    AtrConfQuotaVo findHisByPk(Long quotaHisId);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 根据主键ID查找对象指标对象
     * @Return
     */
    void deleteByPk(Long bussQuotaId, Long userId);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 审核指标对象
     * @Return
     */
    void updateAudit(AtrConfQuotaVo atrConfQuotaVo, Long userId);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 激活或禁用
     * @Return
     */
    String disableValid(AtrConfQuotaVo atrConfQuotaVo, Long userId);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/10
     * @Description 批量审核指标对象
     * @Return
     */
    String batchAudit(ArrayList<AtrConfQuotaVo> auditDealList, Long userId);


    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/13
     * @Description 根据centerId,riskCOde查询指标配置vo对象
     * @Return
     */
    AtrConfQuotaVo findAtrConfQuotaVo(AtrConfQuotaVo atrConfQuotaVo);


    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/15 查找指标定义Vo对象
     * @Description
     * @Return
     */
    Map<String, Object> findPeriodMap(AtrConfQuotaVo atrConfQuotaVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/17 保存或新增指标配置信息
     * @Description
     * @Return
     */
    void addOrUpdate(AtrConfQuotaMainVo atrConfQuotaMainVo, Long userId);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/17 保存或新增指标配置信息
     * @Description
     * @Return
     */
    void saveAtrConfQuotaList(AtrConfQuotaMainVo atrConfQuotaMainVo, Long userId);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/22 查找指标配置明细假设数据列表
     * @Description
     * @Return
     */
    List<AtrConfQuotaGroupDefVo> findConfQuotaDef(AtrConfQuotaVo atrConfQuotaVo);


    Map<String,Object> findPrePeriod(AtrConfQuotaVo atrConfQuotaVo);


    Map<String,Object> loadPrePeriodQuota(AtrConfQuotaVo atrConfQuotaVo);
    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/23 根据vo删除对象信息
     * @Description
     * @Return
     */
    void deleteByVo(AtrConfQuotaVo atrConfQuotaVo, Long userId);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/11/25 查找是否存在相同数据
     * @Description
     * @Return
     */
    String findValidateCode(AtrConfQuotaVo atrConfQuotaVo);

    void syncQuotaClass(AtrConfQuotaDefVo confQuotaDefVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/12/18 查找指标配置结果列的表头
     * @Description
     * @Return
     */
    Map<String, Object> findQuotaDefHeader(AtrConfQuotaDefVo confQuotaDefVo);

    /**
     * @Method
     * <AUTHOR>
     * @Date 2021/12/18 查找发展期map
     * @Description
     * @Return
     */
    Map<String, Object> findQuotaDetailByVo(AtrConfQuotaVo atrConfQuotaVo);

    List<AtrConfQuotaGroupDefVo> findConfQuotaHisDef(AtrConfQuotaVo atrConfQuotaVo);

    Map<String, Object> findPeriodMapHis(AtrConfQuotaVo atrConfQuotaVo);

    List<AtrConfQuotaImportVo> excelImport(MultipartFile file, AtrConfQuotaImportVo atrConfQuotaVo, Long userId) throws Exception ;

    void generateTemplate(HttpServletRequest request, HttpServletResponse response, AtrConfQuotaImportVo importVo) throws IOException;

    Map<String, Object> executeUpload(HttpServletRequest request, MultipartFile file, AtrConfQuotaImportVo importVo, Long userId) throws Exception;


    List<AtrConfQuotaVo> quotaIcgImport(MultipartFile file, AtrConfQuotaImportVo importVo) throws Exception;

    void downloadIcgTemplate(HttpServletRequest request, HttpServletResponse response, AtrConfQuotaImportVo confQuotaVo) throws Exception;

    void downloadQuotaExcel(HttpServletRequest request, HttpServletResponse response, AtrConfQuotaImportVo qtcConfQuotaVo) throws Exception;
}
