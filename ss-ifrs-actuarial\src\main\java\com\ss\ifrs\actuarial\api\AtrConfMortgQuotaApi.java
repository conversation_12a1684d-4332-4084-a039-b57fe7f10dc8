package com.ss.ifrs.actuarial.api;

import com.ss.ifrs.actuarial.pojo.atrconf.vo.*;
import com.ss.ifrs.actuarial.service.*;
import com.ss.platform.core.annotation.TrackUserBehavioral;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.library.constant.RestfulCodeConstant;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.platform.util.CheckParamsUtil;
import com.ss.library.utils.ExceptionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021/3/23
 */
@RestController
@RequestMapping("/mortg")
@Api(value = "mortgage假设配置")
public class AtrConfMortgQuotaApi extends BaseApi{

    @Autowired
    AtrConfMortgQuotaService atrConfMortgQuotaService;

    @Autowired
    AtrExportService atrExportService;

    @Autowired
    AtrConfQuotaDefService atrConfQuotaDefService;

    @Autowired
    AtrConfQuotaDymService atrConfQuotaDymService;

    @ApiOperation(value = "增加指标配置信息")
    @RequestMapping(value = "/quota/add", method = RequestMethod.POST)
    public BaseResponse<Object> add(@RequestBody @Validated
                                                 AtrConfMortgQuotaVo atrConfMortgQuotaVo, HttpServletRequest request, BindingResult br
    ) {
        if (br.hasErrors()) {
            // 该状态码为“其它格式转换错误”
            StringBuffer errorMessages = new StringBuffer();
            for (int i = 0; i < br.getErrorCount(); i++) {
                errorMessages.append(br.getAllErrors().get(i).getDefaultMessage() + ";");
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.FORMAT_TRANSFORM_ERROR,new CheckParamsUtil().getMessages(errorMessages.toString()));
        }
        Long userId = this.loginUserId(request);
        atrConfMortgQuotaVo.setCreatorId(userId);
        atrConfMortgQuotaVo.setCreateTime(new Date());
        try {
            atrConfMortgQuotaService.addOrUpdate(atrConfMortgQuotaVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "根据id查询指标配置信息")
    @RequestMapping(value = "/quota/find_by_id/{mortgQuotaId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<AtrConfMortgQuotaVo> findById(@PathVariable("mortgQuotaId") Long mortgQuotaId) {
        AtrConfMortgQuotaVo atrConfMortgQuotaVo = atrConfMortgQuotaService.findById(mortgQuotaId);
        return new BaseResponse<AtrConfMortgQuotaVo>(ResCodeConstant.ResCode.SUCCESS, atrConfMortgQuotaVo);
    }

    @ApiOperation(value = "根据id查询指标配置信息")
    @RequestMapping(value = "/quota/his/find_by_id/{riskRefHisId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<AtrConfMortgQuotaVo> findHisById(@PathVariable("riskRefHisId") Long riskRefHisId) {
        AtrConfMortgQuotaVo atrConfMortgQuotaVo = atrConfMortgQuotaService.findHisById(riskRefHisId);
        return new BaseResponse<AtrConfMortgQuotaVo>(ResCodeConstant.ResCode.SUCCESS, atrConfMortgQuotaVo);
    }

    @ApiOperation(value = "查询指标配置列表")
    @RequestMapping(value = "/quota/enquiry", method = RequestMethod.POST)
    public BaseResponse<Map<String, Object>> enquiry(@RequestBody AtrConfMortgQuotaVo atrConfMortgQuotaVo, int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrConfMortgQuotaVo> atrConfRiskRefVoPage = atrConfMortgQuotaService.searchPage(atrConfMortgQuotaVo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("atrConfMortgQuotaVoList", atrConfRiskRefVoPage);
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, result);
    }

    @ApiOperation(value = "修改指标配置信息")
    @RequestMapping(value = "/quota/edit", method = RequestMethod.POST)
    public BaseResponse<Object> edit(@RequestBody @Validated AtrConfMortgQuotaVo atrConfMortgQuotaVo, HttpServletRequest request, BindingResult br) {
        if (br.hasErrors()) {
            // 该状态码为“其它格式转换错误”
            StringBuffer errorMessages = new StringBuffer();
            for (int i = 0; i < br.getErrorCount(); i++) {
                errorMessages.append(br.getAllErrors().get(i).getDefaultMessage() + ";");
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.FORMAT_TRANSFORM_ERROR,new CheckParamsUtil().getMessages(errorMessages.toString()));
        }
        atrConfMortgQuotaVo.setUpdatorId(this.loginUserId(request));
        atrConfMortgQuotaVo.setUpdateTime(new Date());
        atrConfMortgQuotaVo.setAuditState("0");
        atrConfMortgQuotaVo.setCheckedMsg(null);
        atrConfMortgQuotaVo.setCheckedId(null);
        atrConfMortgQuotaVo.setCheckedTime(null);
        try {
            Long userId = this.loginUserId(request);
            atrConfMortgQuotaService.addOrUpdate(atrConfMortgQuotaVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }


    @ApiOperation(value = "批量删除指标配置信息")
    @RequestMapping(value = "/quota/delete_by_id/{mortgQuotaId}", method = RequestMethod.GET)
    @PermissionRequest(required = false)
    public BaseResponse<Object> delete(HttpServletRequest request, @PathVariable("mortgQuotaId") Long mortgQuotaId) {
        try {
            Long userId = this.loginUserId(request);
            atrConfMortgQuotaService.delete(mortgQuotaId, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "有效状态更新")
    @RequestMapping(value = "/quota/valid_status", method = RequestMethod.POST)
    public BaseResponse<Object> validStatus(HttpServletRequest request, @RequestBody AtrConfMortgQuotaVo atrConfMortgQuotaVo) {
        atrConfMortgQuotaVo.setUpdatorId(this.loginUserId(request));
        Long userId = this.loginUserId(request);
        try {
            atrConfMortgQuotaService.updateValid(atrConfMortgQuotaVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, "SUCCESS");
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "批量审核指标配置")
    @RequestMapping(value = "/quota/batch_audit", method = RequestMethod.POST)
    public BaseResponse<Object> batchAudit(@RequestBody List<AtrConfMortgQuotaVo> atrConfRiskRefVos, HttpServletRequest request, String auditState, String checkedMsg) {
        Long userId = this.loginUserId(request);
        for (AtrConfMortgQuotaVo atrConfMortgQuotaVo : atrConfRiskRefVos) {
            atrConfMortgQuotaVo.setAuditState(auditState);
            atrConfMortgQuotaVo.setCheckedMsg(checkedMsg);
            atrConfMortgQuotaVo.setCheckedId(this.loginUserId(request));
            atrConfMortgQuotaVo.setCheckedTime(new Date());
        }
        try {
            atrConfMortgQuotaService.auditList(atrConfRiskRefVos, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "审核指标配置")
    @RequestMapping(value = "/quota/audit", method = RequestMethod.POST)
    public BaseResponse<Object> audit(@RequestBody List<AtrConfMortgQuotaVo> atrConfMortgQuotaVos, HttpServletRequest request, String auditState, String checkedMsg) {
        Long userId = this.loginUserId(request);
        for (AtrConfMortgQuotaVo confMortgQuotaVo : atrConfMortgQuotaVos) {
            confMortgQuotaVo.setAuditState(auditState);
            confMortgQuotaVo.setCheckedMsg(checkedMsg);
            confMortgQuotaVo.setCheckedId(this.loginUserId(request));
            confMortgQuotaVo.setCheckedTime(new Date());
        }
        try {
            atrConfMortgQuotaService.auditList(atrConfMortgQuotaVos, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
        }
    }

    @ApiOperation(value = "校验有无配置")
    @RequestMapping(value = "/quota/check_pk", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> checkPK(@RequestBody AtrConfMortgQuotaVo atrConfMortgQuotaVo) {
        String result = "0";
        try {
            Long sum = atrConfMortgQuotaService.checkNaturalPk(atrConfMortgQuotaVo);
            if(sum > 0){
                result = "1";
            }
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, result);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, result);
        }
    }

    @ApiOperation(value = "导出风险分布模式配置")
    @TrackUserBehavioral(description = "exportExcelModel")
    @RequestMapping(value = "/quota/export_excel", method = RequestMethod.POST)
    public BaseResponse<Map<String, Object>> exportExcelModel(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrConfMortgQuotaVo atrConfMortgQuotaVo) throws Exception {
        Pageable pageParam = new Pageable(0, maxExcelPageSize);
        Page<AtrConfMortgQuotaVo> atrConfRiskRefVoPage = atrConfMortgQuotaService.searchPage(atrConfMortgQuotaVo, pageParam);
        try {
            Long userId = this.loginUserId(request);
            atrExportService.exportPage(request, response,  atrConfRiskRefVoPage, AtrConfMortgQuotaVo.class, "df", atrConfMortgQuotaVo.getTemplateFileName(), atrConfMortgQuotaVo.getTargetRouter(), userId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, null);
    }

    @ApiOperation(value = "mortgage假设数据有效性校验")
    @RequestMapping(value = "/quota/valid_data", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> validDataLossDef(HttpServletRequest request, @RequestParam(value = "file", required = false) MultipartFile file,
                                                 AtrConfMortgQuotaVo atrConfMortgQuotaVo) {

        Map<String, Object> map = new HashMap<String, Object>();
        AtrConfMortgQuotaVo mortgQuotaVo = null;
        try{
             mortgQuotaVo = atrConfMortgQuotaService.validData(file, atrConfMortgQuotaVo);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
        }
        map.put("validVo", mortgQuotaVo);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);

    }

    @ApiOperation(value = "mortgage假设值导入")
    @RequestMapping(value = "/quota/import", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> becfImport(HttpServletRequest request, @RequestParam(value = "file", required = false) MultipartFile file,
                                        AtrConfMortgQuotaVo atrConfMortgQuotaVo)  {
        Long userId = this.loginUserId(request);
        String result = "0";
        String exceptionMsg = null;
        try{
            atrConfMortgQuotaService.excelImport(file, atrConfMortgQuotaVo, userId);
            result = "1";
        } catch (Exception e) {
            exceptionMsg = ExceptionUtil.getMessage(e);
            logger.error(e.getLocalizedMessage(), e);
        }
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, exceptionMsg, result);
    }


    //
    @ApiOperation(value = "查找事故年月分组假设值定义")
    @RequestMapping(value = "/dym/find_quota_def", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findQuotaHeader (HttpServletRequest request, @RequestBody AtrConfQuotaDefVo confQuotaDefVo){
        try {
            Map<String,Object> map = atrConfQuotaDefService.findQuotaDefByGroup(confQuotaDefVo);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
        } catch (Exception ex) {
            String message = ExceptionUtil.getMessage(ex);
            logger.error(ex.getLocalizedMessage(), ex);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, message, RestfulCodeConstant.RestfulCode.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "查找事故年月分组假设值定义")
    @RequestMapping(value = "/dym/find_quota_list", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findQuotaDefList (HttpServletRequest request, @RequestBody AtrConfQuotaDefVo confQuotaDefVo){
        try {
            List<AtrConfQuotaDefVo> confQuotaDefVoList = atrConfQuotaDefService.findQuotaDefList(confQuotaDefVo);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, confQuotaDefVoList);
        } catch (Exception ex) {
            String message = ExceptionUtil.getMessage(ex);
            logger.error(ex.getLocalizedMessage(), ex);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, message, RestfulCodeConstant.RestfulCode.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "查询指标配置列表")
    @RequestMapping(value = "/dym/enquiry", method = RequestMethod.POST)
    public BaseResponse<Map<String, Object>> dymEnquiry(@RequestBody AtrConfQuotaDymVo atrConfQuotaDymVo, int _pageSize, int _pageNo) {
        Pageable pageParam = new Pageable(_pageNo, _pageSize);
        Page<AtrConfQuotaDymVo> atrConfQuotaDymVoPage = atrConfQuotaDymService.searchPage(atrConfQuotaDymVo, pageParam);
        Map<String, Object> result = new HashMap<>();
        result.put("atrConfQuotaDymVoList", atrConfQuotaDymVoPage);
        return new BaseResponse<Map<String, Object>>(ResCodeConstant.ResCode.SUCCESS, result);
    }




    @ApiOperation(value = "增加事故年月假设信息")
    @RequestMapping(value = "/dym/add", method = RequestMethod.POST)
    public BaseResponse<Object> add(@RequestBody AtrConfQuotaDymMainVo confQuotaDymVo, HttpServletRequest request) {
        Long userId = this.loginUserId(request);
        atrConfQuotaDymService.addOrUpdate(confQuotaDymVo, userId);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, "SUCCESS");
    }

    @ApiOperation(value = "删除事故年月假设配置")
    @RequestMapping(value = "/dym/delete", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> deleteQuotaDym(@RequestBody AtrConfQuotaDymMainVo confQuotaDymMainVo, HttpServletRequest request) {
        Long userId = this.loginUserId(request);
        atrConfQuotaDymService.delete(confQuotaDymMainVo, userId);
        return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, RestfulCodeConstant.RestfulCode.SUCCESS);
    }

    @ApiOperation(value = "审核事故年月假设配置")
    @RequestMapping(value = "/dym/audit", method = RequestMethod.POST)
    public BaseResponse<Object> auditQuotaDym(@RequestBody AtrConfQuotaDymMainVo confQuotaDymMainVo, HttpServletRequest request) {
        Long userId = this.loginUserId(request);
        atrConfQuotaDymService.audit(confQuotaDymMainVo, userId);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, "SUCCESS");
    }

    @ApiOperation(value = "激活或禁用审核事故年月假设配置")
    @TrackUserBehavioral(description = "valid Status Quota")
    @RequestMapping(value = "/dym/valid_status", method = RequestMethod.POST)
    public BaseResponse<Object> validStatusQuota (HttpServletRequest request, @RequestBody AtrConfQuotaDymMainVo atrConfQuotaVo){
        try {
            Long userId = this.loginUserId(request);
            String resMsg = atrConfQuotaDymService.disableValid(atrConfQuotaVo, userId);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, RestfulCodeConstant.RestfulCode.SUCCESS);
        } catch (Exception ex) {
            String message = ExceptionUtil.getMessage(ex);
            logger.error(ex.getLocalizedMessage(), ex);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, message, RestfulCodeConstant.RestfulCode.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "批量审核")
    @TrackUserBehavioral(description = "Batch Audit Quota")
    @RequestMapping(value = "/dym/batch_audit", method = RequestMethod.POST)
    public BaseResponse<String> batchAuditQuota (HttpServletRequest request,
                                                 @RequestBody ArrayList<AtrConfQuotaDymMainVo > confQuotaDymMainVos, String auditState, String checkedMsg){
        // 获取当前操作人Id
        Long userId = this.loginUserId(request);
        for (AtrConfQuotaDymMainVo confQuotaDymMainVo : confQuotaDymMainVos) {
            confQuotaDymMainVo.setAuditState(auditState);
            confQuotaDymMainVo.setCheckedMsg(checkedMsg);
            confQuotaDymMainVo.setCheckedId(this.loginUserId(request));
            confQuotaDymMainVo.setCheckedTime(new Date());
        }
        try {
            atrConfQuotaDymService.batchAudit(confQuotaDymMainVos, userId);
            return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, ResCodeConstant.ResCode.SUCCESS);
        } catch (UnexpectedRollbackException e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<String>(ResCodeConstant.ResCode.OTHER_ERROR, null);
        }
    }


    @ApiOperation("根据维度值查找事故年月假设信息")
    @RequestMapping(value = "/dym/find_by_pk", method = RequestMethod.GET)
    public BaseResponse<Object> findById(@RequestBody @Validated AtrConfQuotaDymVo confQuotaDymVo){
        List<AtrConfQuotaDymVo> confQuotaDymVoList = atrConfQuotaDymService.findConfQuotaByVo(confQuotaDymVo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, confQuotaDymVoList);
    }

    @ApiOperation(value = "查找是否存在相同数据")
    @RequestMapping(value = "/dym/validate_dimension", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findValidateCode (HttpServletRequest request, @RequestBody AtrConfQuotaDymVo confQuotaDymVo){
        try {
            String validateFlag = atrConfQuotaDymService.findValidateDimension(confQuotaDymVo);
            Map<String,Object> map = new HashMap<String, Object>();
            map.put("validateFlag",validateFlag);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
        } catch (Exception ex) {
            String message = ExceptionUtil.getMessage(ex);
            logger.error(ex.getLocalizedMessage(), ex);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, message, RestfulCodeConstant.RestfulCode.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "查找是否存在相同数据")
    @RequestMapping(value = "/dym/find_quota_by_dimension", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> findQuotaByDimensionVo(HttpServletRequest request, @RequestBody AtrConfQuotaDymMainVo confQuotaDymVo){
        try {
            Map<String, Object> map = atrConfQuotaDymService.findQuotaByDimensionVo(confQuotaDymVo);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
        } catch (Exception ex) {
            String message = ExceptionUtil.getMessage(ex);
            logger.error(ex.getLocalizedMessage(), ex);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, message, RestfulCodeConstant.RestfulCode.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "查询假设值审计信息")
    @RequestMapping(value = "/dym/find_audit_information_by_vo" , method = {RequestMethod.POST})
    @PermissionRequest(required = false)
    public BaseResponse<Object> findVo(@RequestBody AtrConfQuotaDymVo confQuotaDymVo){
        AtrConfQuotaDymVo vo = atrConfQuotaDymService.findAtrConfQuotaVo(confQuotaDymVo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, vo);
    }


    @ApiOperation(value = "查找假设值个数")
    @RequestMapping(value = "/dym/countQuota" , method = {RequestMethod.POST})
    @PermissionRequest(required = false)
    public BaseResponse<Object> countQuota(@RequestBody AtrConfQuotaDymVo confQuotaDymVo){
        Long count = atrConfQuotaDymService.countQuota(confQuotaDymVo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, count);
    }

    @ApiOperation(value = "加载上一期的假设值")
    @RequestMapping(value = "/dym/load_pre_period_quota" , method = {RequestMethod.POST})
    @PermissionRequest(required = false)
    public BaseResponse<Object> loadPrePeriodQuota(HttpServletRequest request, @RequestBody AtrConfQuotaDymVo confQuotaDymVo){
        // 获取当前操作人Id
        Long userId = this.loginUserId(request);
        confQuotaDymVo.setCreatorId(userId);
        confQuotaDymVo.setCreateTime(new Date());
        Map<String,Object> map = new HashMap<String, Object>();
        map = atrConfQuotaDymService.loadPrePeriodQuota(confQuotaDymVo);
        return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, map);
    }



/*    @ApiOperation(value = "生成假设配置上传模板")
    @TrackUserBehavioral(description = "generate template")
    @RequestMapping(value = "/generate_template", method = RequestMethod.POST)
    @PermissionRequest(required = false)
    public BaseResponse<Object> generateTemplate(HttpServletRequest request, HttpServletResponse response, @RequestBody AtrConfQuotaImportVo importVo) {
        try {
            atrConfQuotaDymService.generateTemplate(request, response, importVo);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, RestfulCodeConstant.RestfulCode.SUCCESS);
        } catch (Exception e) {
            logger.error(e.getLocalizedMessage(), e);
            return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, RestfulCodeConstant.RestfulCode.INTERNAL_SERVER_ERROR);
        }

    }*/

}
