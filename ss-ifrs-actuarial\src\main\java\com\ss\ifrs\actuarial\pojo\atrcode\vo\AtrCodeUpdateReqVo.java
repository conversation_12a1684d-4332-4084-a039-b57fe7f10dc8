package com.ss.ifrs.actuarial.pojo.atrcode.vo;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

public class AtrCodeUpdateReqVo {
	private Long codeId;
	
	private String codeType;
	
	private String codeCode;

	@ApiModelProperty(value = "codeCName|中文名称", required = false)
	@NotBlank(message = "The Chinese Name can't be null|中文名称不能为空|中文名稱不能為空")
	@Size(max = 200, message = "The Chinese Name's length is too long|中文名称过长|中文名稱過長")
	private String codeCName;

	@ApiModelProperty(value = "codeTName|繁体名称", required = false)
	@NotBlank(message = "The Traditional Chinese Name can't be null|繁体名称不能为空|繁體名稱不能為空")
	@Size(max = 200, message = "The Traditional Chinese Name's length is too long|繁体名称过长|繁體名稱過長")
	private String codeLName;

	@ApiModelProperty(value = "codeEName|英文", required = false)
	@NotBlank(message = "The English Name can't be null|英文名称不能为空|英文名稱不能為空")
	@Size(max = 200, message = "The English Name's length is too long|英文名称过长|英文名稱過長")
	private String codeEName;

	@ApiModelProperty(value = "displayNo|序号", required = false)
	@NotNull(message = "The displayNo can't be null|序号不能为空|序號不能為空")
	private Integer displayNo;

	private Date validDate;

	private Date invalidDate;

	private String validIs;

	private String remark;

	private Long updatorId;

	public Long getCodeId() {
		return codeId;
	}

	public void setCodeId(Long codeId) {
		this.codeId = codeId;
	}

	public String getCodeType() {
		return codeType;
	}

	public void setCodeType(String codeType) {
		this.codeType = codeType;
	}

	public String getCodeCode() {
		return codeCode;
	}

	public void setCodeCode(String codeCode) {
		this.codeCode = codeCode;
	}

	public String getCodeCName() {
		return codeCName;
	}

	public void setCodeCName(String codeCName) {
		this.codeCName = codeCName;
	}

	public String getCodeLName() {
		return codeLName;
	}

	public void setCodeLName(String codeLName) {
		this.codeLName = codeLName;
	}

	public String getCodeEName() {
		return codeEName;
	}

	public void setCodeEName(String codeEName) {
		this.codeEName = codeEName;
	}

	public Integer getDisplayNo() {
		return displayNo;
	}

	public void setDisplayNo(Integer displayNo) {
		this.displayNo = displayNo;
	}

	public Date getValidDate() {
		return validDate;
	}

	public void setValidDate(Date validDate) {
		this.validDate = validDate;
	}

	public Date getInvalidDate() {
		return invalidDate;
	}

	public void setInvalidDate(Date invalidDate) {
		this.invalidDate = invalidDate;
	}

	public String getValidIs() {
		return validIs;
	}

	public void setValidIs(String validIs) {
		this.validIs = validIs;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Long getUpdatorId() {
		return updatorId;
	}

	public void setUpdatorId(Long updatorId) {
		this.updatorId = updatorId;
	}
}
