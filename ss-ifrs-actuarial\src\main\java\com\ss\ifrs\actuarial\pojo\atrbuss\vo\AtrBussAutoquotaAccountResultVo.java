package com.ss.ifrs.actuarial.pojo.atrbuss.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
public class AtrBussAutoquotaAccountResultVo {

    @ApiModelProperty(value = "核算单位ID")
    private String entityId;

    @ApiModelProperty(value = "核算单位代码")
    private String entityCode;

    @ApiModelProperty(value = "核算单位名称")
    private String entityName;

    @ApiModelProperty(value = "业务类型|DD-直接业务&临分分入业务，TI-合约分入，FO-临分分出，TO-合约分出")
    private String businessSourceCode;

    @ApiModelProperty(value = "业务线")
    private String loaCode;

    @ApiModelProperty(value = "会计期间")
    private String yearMonth;

    @ApiModelProperty(value = "科目")
    private String accountCode;

    @ApiModelProperty(value = "发生额")
    private BigDecimal amount;





}
