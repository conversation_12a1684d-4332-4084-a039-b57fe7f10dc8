<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-08-28 15:11:17 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussAutoquotaActionDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussAutoquotaAction">
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="action_no" property="actionNo" jdbcType="VARCHAR" />
    <result column="entity_id" property="entityId" jdbcType="BIGINT" />
    <result column="business_source_code" property="businessSourceCode" jdbcType="VARCHAR" />
    <result column="loa_code" property="loaCode" jdbcType="VARCHAR" />
    <result column="deadline" property="deadline" jdbcType="DATE" />
    <result column="draw_interval" property="drawInterval" jdbcType="VARCHAR" />
    <result column="draw_interval_quantity" property="drawIntervalQuantity" jdbcType="INTEGER" />
    <result column="appli_ev_yearmonths" property="appliEvYearmonths" jdbcType="VARCHAR" />
    <result column="appli_quota_codes" property="appliQuotaCodes" jdbcType="VARCHAR" />
    <result column="oper_id" property="operId" jdbcType="BIGINT" />
    <result column="oper_time" property="operTime" jdbcType="TIMESTAMP" />
    <result column="draw_state" property="drawState" jdbcType="VARCHAR" />
    <result column="draw_start_time" property="drawStartTime" jdbcType="TIMESTAMP" />
    <result column="draw_end_time" property="drawEndTime" jdbcType="TIMESTAMP" />
    <result column="approval_state" property="approvalState" jdbcType="VARCHAR" />
    <result column="approval_id" property="approvalId" jdbcType="BIGINT" />
    <result column="approval_time" property="approvalTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    id, action_no, entity_id, business_source_code, loa_code, deadline, draw_interval, 
    draw_interval_quantity, appli_ev_yearmonths, appli_quota_codes, oper_id, oper_time, 
    draw_state, draw_start_time, draw_end_time, approval_state, approval_id, approval_time
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="id != null ">
          and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="actionNo != null and actionNo != ''">
          and action_no = #{actionNo,jdbcType=VARCHAR}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=BIGINT}
      </if>
      <if test="businessSourceCode != null and businessSourceCode != ''">
          and business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="loaCode != null and loaCode != ''">
          and loa_code = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="deadline != null ">
          and deadline = #{deadline,jdbcType=DATE}
      </if>
      <if test="drawInterval != null and drawInterval != ''">
          and draw_interval = #{drawInterval,jdbcType=VARCHAR}
      </if>
      <if test="drawIntervalQuantity != null ">
          and draw_interval_quantity = #{drawIntervalQuantity,jdbcType=INTEGER}
      </if>
      <if test="appliEvYearmonths != null and appliEvYearmonths != ''">
          and appli_ev_yearmonths = #{appliEvYearmonths,jdbcType=VARCHAR}
      </if>
      <if test="appliQuotaCodes != null and appliQuotaCodes != ''">
          and appli_quota_codes = #{appliQuotaCodes,jdbcType=VARCHAR}
      </if>
      <if test="operId != null ">
          and oper_id = #{operId,jdbcType=BIGINT}
      </if>
      <if test="operTime != null ">
          and oper_time = #{operTime,jdbcType=TIMESTAMP}
      </if>
      <if test="drawState != null and drawState != ''">
          and draw_state = #{drawState,jdbcType=VARCHAR}
      </if>
      <if test="drawStartTime != null ">
          and draw_start_time = #{drawStartTime,jdbcType=TIMESTAMP}
      </if>
      <if test="drawEndTime != null ">
          and draw_end_time = #{drawEndTime,jdbcType=TIMESTAMP}
      </if>
      <if test="approvalState != null and approvalState != ''">
          and approval_state = #{approvalState,jdbcType=VARCHAR}
      </if>
      <if test="approvalId != null ">
          and approval_id = #{approvalId,jdbcType=BIGINT}
      </if>
      <if test="approvalTime != null ">
          and approval_time = #{approvalTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.id != null ">
          and id = #{condition.id,jdbcType=BIGINT}
      </if>
      <if test="condition.actionNo != null and condition.actionNo != ''">
          and action_no = #{condition.actionNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.entityId != null ">
          and entity_id = #{condition.entityId,jdbcType=BIGINT}
      </if>
      <if test="condition.businessSourceCode != null and condition.businessSourceCode != ''">
          and business_source_code = #{condition.businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.loaCode != null and condition.loaCode != ''">
          and loa_code = #{condition.loaCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.deadline != null ">
          and deadline = #{condition.deadline,jdbcType=DATE}
      </if>
      <if test="condition.drawInterval != null and condition.drawInterval != ''">
          and draw_interval = #{condition.drawInterval,jdbcType=VARCHAR}
      </if>
      <if test="condition.drawIntervalQuantity != null ">
          and draw_interval_quantity = #{condition.drawIntervalQuantity,jdbcType=INTEGER}
      </if>
      <if test="condition.appliEvYearmonths != null and condition.appliEvYearmonths != ''">
          and appli_ev_yearmonths = #{condition.appliEvYearmonths,jdbcType=VARCHAR}
      </if>
      <if test="condition.appliQuotaCodes != null and condition.appliQuotaCodes != ''">
          and appli_quota_codes = #{condition.appliQuotaCodes,jdbcType=VARCHAR}
      </if>
      <if test="condition.operId != null ">
          and oper_id = #{condition.operId,jdbcType=BIGINT}
      </if>
      <if test="condition.operTime != null ">
          and oper_time = #{condition.operTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.drawState != null and condition.drawState != ''">
          and draw_state = #{condition.drawState,jdbcType=VARCHAR}
      </if>
      <if test="condition.drawStartTime != null ">
          and draw_start_time = #{condition.drawStartTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.drawEndTime != null ">
          and draw_end_time = #{condition.drawEndTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.approvalState != null and condition.approvalState != ''">
          and approval_state = #{condition.approvalState,jdbcType=VARCHAR}
      </if>
      <if test="condition.approvalId != null ">
          and approval_id = #{condition.approvalId,jdbcType=BIGINT}
      </if>
      <if test="condition.approvalTime != null ">
          and approval_time = #{condition.approvalTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="id != null ">
          and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="actionNo != null and actionNo != ''">
          and action_no = #{actionNo,jdbcType=VARCHAR}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=BIGINT}
      </if>
      <if test="businessSourceCode != null and businessSourceCode != ''">
          and business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="loaCode != null and loaCode != ''">
          and loa_code = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="deadline != null ">
          and deadline = #{deadline,jdbcType=DATE}
      </if>
      <if test="drawInterval != null and drawInterval != ''">
          and draw_interval = #{drawInterval,jdbcType=VARCHAR}
      </if>
      <if test="drawIntervalQuantity != null ">
          and draw_interval_quantity = #{drawIntervalQuantity,jdbcType=INTEGER}
      </if>
      <if test="appliEvYearmonths != null and appliEvYearmonths != ''">
          and appli_ev_yearmonths = #{appliEvYearmonths,jdbcType=VARCHAR}
      </if>
      <if test="appliQuotaCodes != null and appliQuotaCodes != ''">
          and appli_quota_codes = #{appliQuotaCodes,jdbcType=VARCHAR}
      </if>
      <if test="operId != null ">
          and oper_id = #{operId,jdbcType=BIGINT}
      </if>
      <if test="operTime != null ">
          and oper_time = #{operTime,jdbcType=TIMESTAMP}
      </if>
      <if test="drawState != null and drawState != ''">
          and draw_state = #{drawState,jdbcType=VARCHAR}
      </if>
      <if test="drawStartTime != null ">
          and draw_start_time = #{drawStartTime,jdbcType=TIMESTAMP}
      </if>
      <if test="drawEndTime != null ">
          and draw_end_time = #{drawEndTime,jdbcType=TIMESTAMP}
      </if>
      <if test="approvalState != null and approvalState != ''">
          and approval_state = #{approvalState,jdbcType=VARCHAR}
      </if>
      <if test="approvalId != null ">
          and approval_id = #{approvalId,jdbcType=BIGINT}
      </if>
      <if test="approvalTime != null ">
          and approval_time = #{approvalTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_autoquota_action"
    where id = #{id,jdbcType=BIGINT}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_autoquota_action"
    where id in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_autoquota_action"
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussAutoquotaAction">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_autoquota_action"
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_autoquota_action"
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="id" keyProperty="id" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussAutoquotaAction">
    insert into "atr_buss_autoquota_action"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="actionNo != null">
        action_no,
      </if>
      <if test="entityId != null">
        entity_id,
      </if>
      <if test="businessSourceCode != null">
        business_source_code,
      </if>
      <if test="loaCode != null">
        loa_code,
      </if>
      <if test="deadline != null">
        deadline,
      </if>
      <if test="drawInterval != null">
        draw_interval,
      </if>
      <if test="drawIntervalQuantity != null">
        draw_interval_quantity,
      </if>
      <if test="appliEvYearmonths != null">
        appli_ev_yearmonths,
      </if>
      <if test="appliQuotaCodes != null">
        appli_quota_codes,
      </if>
      <if test="operId != null">
        oper_id,
      </if>
      <if test="operTime != null">
        oper_time,
      </if>
      <if test="drawState != null">
        draw_state,
      </if>
      <if test="drawStartTime != null">
        draw_start_time,
      </if>
      <if test="drawEndTime != null">
        draw_end_time,
      </if>
      <if test="approvalState != null">
        approval_state,
      </if>
      <if test="approvalId != null">
        approval_id,
      </if>
      <if test="approvalTime != null">
        approval_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="actionNo != null">
        #{actionNo,jdbcType=VARCHAR},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=BIGINT},
      </if>
      <if test="businessSourceCode != null">
        #{businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="loaCode != null">
        #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="deadline != null">
        #{deadline,jdbcType=DATE},
      </if>
      <if test="drawInterval != null">
        #{drawInterval,jdbcType=VARCHAR},
      </if>
      <if test="drawIntervalQuantity != null">
        #{drawIntervalQuantity,jdbcType=INTEGER},
      </if>
      <if test="appliEvYearmonths != null">
        #{appliEvYearmonths,jdbcType=VARCHAR},
      </if>
      <if test="appliQuotaCodes != null">
        #{appliQuotaCodes,jdbcType=VARCHAR},
      </if>
      <if test="operId != null">
        #{operId,jdbcType=BIGINT},
      </if>
      <if test="operTime != null">
        #{operTime,jdbcType=TIMESTAMP},
      </if>
      <if test="drawState != null">
        #{drawState,jdbcType=VARCHAR},
      </if>
      <if test="drawStartTime != null">
        #{drawStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="drawEndTime != null">
        #{drawEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="approvalState != null">
        #{approvalState,jdbcType=VARCHAR},
      </if>
      <if test="approvalId != null">
        #{approvalId,jdbcType=BIGINT},
      </if>
      <if test="approvalTime != null">
        #{approvalTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert into "atr_buss_autoquota_action"
     (id, action_no, entity_id, 
      business_source_code, loa_code, 
      deadline, draw_interval, draw_interval_quantity, 
      appli_ev_yearmonths, appli_quota_codes, 
      oper_id, oper_time, draw_state, 
      draw_start_time, draw_end_time, 
      approval_state, approval_id, approval_time
      )
     values 
    <foreach collection="list" item="item" index="index" separator=",">
       (#{item.id,jdbcType=BIGINT}, #{item.actionNo,jdbcType=VARCHAR}, #{item.entityId,jdbcType=BIGINT}, 
        #{item.businessSourceCode,jdbcType=VARCHAR}, #{item.loaCode,jdbcType=VARCHAR}, 
        #{item.deadline,jdbcType=DATE}, #{item.drawInterval,jdbcType=VARCHAR}, #{item.drawIntervalQuantity,jdbcType=INTEGER}, 
        #{item.appliEvYearmonths,jdbcType=VARCHAR}, #{item.appliQuotaCodes,jdbcType=VARCHAR}, 
        #{item.operId,jdbcType=BIGINT}, #{item.operTime,jdbcType=TIMESTAMP}, #{item.drawState,jdbcType=VARCHAR}, 
        #{item.drawStartTime,jdbcType=TIMESTAMP}, #{item.drawEndTime,jdbcType=TIMESTAMP}, 
        #{item.approvalState,jdbcType=VARCHAR}, #{item.approvalId,jdbcType=BIGINT}, #{item.approvalTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussAutoquotaAction">
    update "atr_buss_autoquota_action"
    <set>
      <if test="actionNo != null">
        action_no = #{actionNo,jdbcType=VARCHAR},
      </if>
      <if test="entityId != null">
        entity_id = #{entityId,jdbcType=BIGINT},
      </if>
      <if test="businessSourceCode != null">
        business_source_code = #{businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="loaCode != null">
        loa_code = #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="deadline != null">
        deadline = #{deadline,jdbcType=DATE},
      </if>
      <if test="drawInterval != null">
        draw_interval = #{drawInterval,jdbcType=VARCHAR},
      </if>
      <if test="drawIntervalQuantity != null">
        draw_interval_quantity = #{drawIntervalQuantity,jdbcType=INTEGER},
      </if>
      <if test="appliEvYearmonths != null">
        appli_ev_yearmonths = #{appliEvYearmonths,jdbcType=VARCHAR},
      </if>
      <if test="appliQuotaCodes != null">
        appli_quota_codes = #{appliQuotaCodes,jdbcType=VARCHAR},
      </if>
      <if test="operId != null">
        oper_id = #{operId,jdbcType=BIGINT},
      </if>
      <if test="operTime != null">
        oper_time = #{operTime,jdbcType=TIMESTAMP},
      </if>
      <if test="drawState != null">
        draw_state = #{drawState,jdbcType=VARCHAR},
      </if>
      <if test="drawStartTime != null">
        draw_start_time = #{drawStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="drawEndTime != null">
        draw_end_time = #{drawEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="approvalState != null">
        approval_state = #{approvalState,jdbcType=VARCHAR},
      </if>
      <if test="approvalId != null">
        approval_id = #{approvalId,jdbcType=BIGINT},
      </if>
      <if test="approvalTime != null">
        approval_time = #{approvalTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussAutoquotaAction">
    update "atr_buss_autoquota_action"
    <set>
      <if test="record.actionNo != null">
        action_no = #{record.actionNo,jdbcType=VARCHAR},
      </if>
      <if test="record.entityId != null">
        entity_id = #{record.entityId,jdbcType=BIGINT},
      </if>
      <if test="record.businessSourceCode != null">
        business_source_code = #{record.businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.loaCode != null">
        loa_code = #{record.loaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.deadline != null">
        deadline = #{record.deadline,jdbcType=DATE},
      </if>
      <if test="record.drawInterval != null">
        draw_interval = #{record.drawInterval,jdbcType=VARCHAR},
      </if>
      <if test="record.drawIntervalQuantity != null">
        draw_interval_quantity = #{record.drawIntervalQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.appliEvYearmonths != null">
        appli_ev_yearmonths = #{record.appliEvYearmonths,jdbcType=VARCHAR},
      </if>
      <if test="record.appliQuotaCodes != null">
        appli_quota_codes = #{record.appliQuotaCodes,jdbcType=VARCHAR},
      </if>
      <if test="record.operId != null">
        oper_id = #{record.operId,jdbcType=BIGINT},
      </if>
      <if test="record.operTime != null">
        oper_time = #{record.operTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.drawState != null">
        draw_state = #{record.drawState,jdbcType=VARCHAR},
      </if>
      <if test="record.drawStartTime != null">
        draw_start_time = #{record.drawStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.drawEndTime != null">
        draw_end_time = #{record.drawEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.approvalState != null">
        approval_state = #{record.approvalState,jdbcType=VARCHAR},
      </if>
      <if test="record.approvalId != null">
        approval_id = #{record.approvalId,jdbcType=BIGINT},
      </if>
      <if test="record.approvalTime != null">
        approval_time = #{record.approvalTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from "atr_buss_autoquota_action"
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from "atr_buss_autoquota_action"
    where id in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from "atr_buss_autoquota_action"
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussAutoquotaAction">
    select count(1) from "atr_buss_autoquota_action"
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>