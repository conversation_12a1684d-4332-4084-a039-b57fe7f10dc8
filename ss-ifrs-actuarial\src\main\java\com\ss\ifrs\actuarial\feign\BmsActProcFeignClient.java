package com.ss.ifrs.actuarial.feign;

import com.ss.platform.core.conf.FeignAuthConfig;
import com.ss.platform.pojo.base.po.BaseResponse;
import com.ss.platform.pojo.com.vo.ActOverviewVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;
import java.util.Map;

/**
 * 调用BPL流程管理的接口
 * 
 * <AUTHOR>
 */
@FeignClient(value = "SS-PLATFORM-COMMON", configuration = { FeignAuthConfig.class })
public interface BmsActProcFeignClient {

	/**
	 * "根据流程编码查找流程信息"
	 * 
	 * @param
	 * @return 流程定义信息
	 */
	@RequestMapping(method = RequestMethod.GET, value = "/act/find_valid_overview_by_node/{systemCode}/{procCode}")
	BaseResponse<List<ActOverviewVo>> findAccOwActProcdef(@PathVariable("systemCode") String systemCode,
														  @PathVariable("procCode") String procCode);

	/**
	 * "根据流程id查找流程信息"
	 * 
	 * @param procId
	 * @return 流程定义信息
	 */
	@RequestMapping(method = RequestMethod.GET, value = "/act/find_by_id/{procId}")
	BaseResponse<ActOverviewVo> findById(@PathVariable("procId") Long procId);


	/**
	 * "查询流程信息"
	 */
	@RequestMapping(method = RequestMethod.POST, value = "/actionLog/find_overview")
	BaseResponse<Map<String, Object>> findOverview(@RequestBody ActOverviewVo actOverviewVo);


	/**
	 * "综合查询流程节点信息"
	 *
	 * @return 流程定义信息
	 */
	@RequestMapping(method = RequestMethod.POST, value = "/act/find_by_object")
	BaseResponse<Object> findActOverviewByObject(@RequestBody ActOverviewVo actOverviewVo);
}
