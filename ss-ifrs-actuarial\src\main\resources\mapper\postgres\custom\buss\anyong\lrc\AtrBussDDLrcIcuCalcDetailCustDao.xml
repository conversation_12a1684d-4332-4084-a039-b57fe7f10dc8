<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-02-10 17:54:03 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussDDLrcIcuCalcDetailDao">
  <!-- 本配置文件由GIS MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**IDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->
  <resultMap id="CustResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussDDLrcIcuCalcDetailVo">
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="MAIN_ID" property="mainId" jdbcType="DECIMAL" />
    <result column="DEV_NO" property="devNo" jdbcType="DECIMAL" />
    <result column="AFTER_PREMIUM_IMPAIRMENT_RATE" property="afterPremiumImpairmentRate" jdbcType="DECIMAL" />
    <result column="RECV_PREMIUM" property="recvPremium" jdbcType="DECIMAL" />
    <result column="ADJ_COMMISSION" property="adjCommission" jdbcType="DECIMAL" />
    <result column="BROKERAGE_FEE" property="brokerageFee" jdbcType="DECIMAL" />
    <result column="IACF_FEE" property="iacfFee" jdbcType="DECIMAL" />
    <result column="ED_PREMIUM" property="edPremium" jdbcType="DECIMAL" />
    <result column="MAINTENANCE_FEE" property="maintenanceFee" jdbcType="DECIMAL" />
    <result column="UE_PREMIUM" property="uePremium" jdbcType="DECIMAL" />
    <result column="GEP_UWQ" property="gepUwq" jdbcType="DECIMAL" />
    <result column="ULTIMATE_LOSS" property="ultimateLoss" jdbcType="DECIMAL" />
    <result column="COVERAGE_AMOUNT" property="coverageAmount" jdbcType="DECIMAL" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Cust_Column_List">
    ID, MAIN_ID, DEV_NO, AFTER_PREMIUM_IMPAIRMENT_RATE, RECV_PREMIUM, ADJ_COMMISSION,
    BROKERAGE_FEE, IACF_FEE, ED_PREMIUM, MAINTENANCE_FEE, UE_PREMIUM, GEP_UWQ, ULTIMATE_LOSS,
    COVERAGE_AMOUNT
  </sql>

  <select id="findByMainId" flushCache="false" useCache="true" resultMap="CustResultMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
    select
    <include refid="Cust_Column_List" />
    from ATR_BUSS_DD_LRC_ICU_CALC_DETAIL
    where MAIN_ID = #{mainId,jdbcType=DECIMAL}
    <if test="feeType != null and feeType != ''">
      <choose>
        <when test='feeType=="recv"'>
          and RECV_PREMIUM is not null
        </when>
        <when test='feeType=="gep"'>
          and ED_PREMIUM is not null
        </when>
        <when test='feeType=="cov"'>
          and COVERAGE_AMOUNT is not null
        </when>
        <when test='feeType=="adj"'>
          and ADJ_COMMISSION is not null
        </when>
        <when test='feeType=="main"'>
          and MAINTENANCE_FEE is not null
        </when>
        <when test='feeType=="bro"'>
          and BROKERAGE_FEE is not null
        </when>
        <when test='feeType=="iacf"'>
          and IACF_FEE is not null
        </when>

        <when test='feeType=="UNIT"'>
          and RECV_PREMIUM is not null
        </when>

        <otherwise>
          and RECV_PREMIUM is not null
        </otherwise>
      </choose>
    </if>
    ORDER BY DEV_NO
  </select>

  <select id="findDateByMainId" flushCache="false" useCache="true" resultMap="CustResultMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
    select
    <include refid="Cust_Column_List" />
    from ATR_BUSS_DD_LRC_ICU_CALC_DETAIL b
    where exists(
     select 1 from ATR_BUSS_DD_LRC_ICU_CALC a
    where b.MAIN_ID = a.id and a.action_no = #{actionNo,jdbcType=VARCHAR}
    )
    ORDER BY DEV_NO desc
  </select>

  <select id="findByVo" flushCache="false" useCache="true" resultType="Integer"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
    SELECT distinct DEV_NO from ATR_BUSS_DD_LRC_ICU_CALC_DETAIL where MAIN_ID in
    (select id from ATR_BUSS_DD_LRC_ICU_CALC where action_no = #{actionNo,jdbcType=VARCHAR}
    <if test="portfolioNo != null and portfolioNo != ''">
      and PORTFOLIO_NO = #{portfolioNo,jdbcType=VARCHAR}
    </if>
    <if test="icgNo != null and icgNo != ''">
      and icg_no = #{icgNo,jdbcType=VARCHAR}
    </if>
    )
    <if test="feeType != null and feeType != ''">
      <choose>
        <when test='feeType=="recv"'>
          and RECV_PREMIUM is not null
        </when>
        <when test='feeType=="gep"'>
          and ED_PREMIUM is not null
        </when>
        <when test='feeType=="cov"'>
          and COVERAGE_AMOUNT is not null
        </when>
        <when test='feeType=="uep"'>
          and UE_PREMIUM is not null
        </when>
        <when test='feeType=="adj"'>
          and ADJ_COMMISSION is not null
        </when>
        <when test='feeType=="main"'>
          and MAINTENANCE_FEE is not null
        </when>
        <when test='feeType=="bro"'>
          and BROKERAGE_FEE is not null
        </when>
        <when test='feeType=="iacf"'>
          and IACF_FEE is not null
        </when>

        <when test='feeType=="UNIT"'>
          and RECV_PREMIUM is not null
        </when>

        <otherwise>
          and RECV_PREMIUM is not null
        </otherwise>
      </choose>
    </if>
    ORDER BY DEV_NO
  </select>

  <select id="findByBetweenId" flushCache="false" useCache="true" resultType="Integer"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
    SELECT DEV_NO from ATR_BUSS_DD_LRC_ICU_CALC_DETAIL
    where MAIN_ID between  #{startId,jdbcType=DECIMAL} and #{endId,jdbcType=DECIMAL}
    group by DEV_NO
  </select>
</mapper>
