<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-03-08 16:40:46 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussQuotaValueDao">
  <!-- 本配置文件由GIS MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**IDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->





  <sql id="Base1_Select_By_Entity_Where">
    <where>
      <if test="id != null ">
        and ID = #{id,jdbcType=DECIMAL}
      </if>
      <if test="taskCode != null and taskCode != ''">
        and TASK_CODE = #{taskCode,jdbcType=VARCHAR}
      </if>
      <if test="entityId != null ">
        and entity_id = #{entityId,jdbcType=DECIMAL}
      </if>
      <if test="quotaCode != null and quotaCode != ''">
        and QUOTA_CODE = #{quotaCode,jdbcType=VARCHAR}
      </if>
      <if test="dimension != null and dimension != ''">
        and DIMENSION = #{dimension,jdbcType=VARCHAR}
      </if>
      <if test="dimensionValue != null and dimensionValue != ''">
        and DIMENSION_VALUE = #{dimensionValue,jdbcType=VARCHAR}
      </if>
      <if test="devNo != null ">
        and DEV_NO = #{devNo,jdbcType=DECIMAL}
      </if>
      <if test="quotaValue != null ">
        and QUOTA_VALUE = #{quotaValue,jdbcType=DECIMAL}
      </if>
    </where>
  </sql>
</mapper>