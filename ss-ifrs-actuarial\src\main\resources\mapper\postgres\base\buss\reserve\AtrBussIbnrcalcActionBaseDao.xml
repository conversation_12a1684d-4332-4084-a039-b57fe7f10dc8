<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-12-07 09:24:43 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussIbnrcalcActionDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**BaseDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcAction">
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="action_no" property="actionNo" jdbcType="VARCHAR" />
    <result column="entity_id" property="entityId" jdbcType="BIGINT" />
    <result column="currency_code" property="currencyCode" jdbcType="VARCHAR" />
    <result column="extraction_interval" property="extractionInterval" jdbcType="VARCHAR" />
    <result column="extraction_zones" property="extractionZones" jdbcType="SMALLINT" />
    <result column="extraction_deadline" property="extractionDeadline" jdbcType="VARCHAR" />
    <result column="ibnr_type" property="ibnrType" jdbcType="VARCHAR" />
    <result column="loa_code" property="loaCode" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="VARCHAR" />
    <result column="error_msg" property="errorMsg" jdbcType="VARCHAR" />
    <result column="confirm_is" property="confirmIs" jdbcType="VARCHAR" />
    <result column="confirm_id" property="confirmId" jdbcType="BIGINT" />
    <result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP" />
    <result column="confirm_method" property="confirmMethod" jdbcType="VARCHAR" />
    <result column="create_id" property="createId" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="complete_time" property="completeTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    id, action_no, entity_id, currency_code, extraction_interval, extraction_zones, extraction_deadline, 
    ibnr_type, loa_code, status, error_msg, confirm_is, confirm_id, confirm_time, confirm_method, 
    create_id, create_time, complete_time
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="id != null ">
          and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="actionNo != null and actionNo != ''">
          and action_no = #{actionNo,jdbcType=VARCHAR}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=BIGINT}
      </if>
      <if test="currencyCode != null and currencyCode != ''">
          and currency_code = #{currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="extractionInterval != null and extractionInterval != ''">
          and extraction_interval = #{extractionInterval,jdbcType=VARCHAR}
      </if>
      <if test="extractionZones != null ">
          and extraction_zones = #{extractionZones,jdbcType=SMALLINT}
      </if>
      <if test="extractionDeadline != null and extractionDeadline != ''">
          and extraction_deadline = #{extractionDeadline,jdbcType=VARCHAR}
      </if>
      <if test="ibnrType != null and ibnrType != ''">
          and ibnr_type = #{ibnrType,jdbcType=VARCHAR}
      </if>
      <if test="loaCode != null and loaCode != ''">
          and loa_code = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="status != null and status != ''">
          and status = #{status,jdbcType=VARCHAR}
      </if>
      <if test="errorMsg != null and errorMsg != ''">
          and error_msg = #{errorMsg,jdbcType=VARCHAR}
      </if>
      <if test="confirmIs != null and confirmIs != ''">
          and confirm_is = #{confirmIs,jdbcType=VARCHAR}
      </if>
      <if test="confirmId != null ">
          and confirm_id = #{confirmId,jdbcType=BIGINT}
      </if>
      <if test="confirmTime != null ">
          and confirm_time = #{confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="confirmMethod != null and confirmMethod != ''">
          and confirm_method = #{confirmMethod,jdbcType=VARCHAR}
      </if>
      <if test="createId != null ">
          and create_id = #{createId,jdbcType=BIGINT}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="completeTime != null ">
          and complete_time = #{completeTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.id != null ">
          and id = #{condition.id,jdbcType=BIGINT}
      </if>
      <if test="condition.actionNo != null and condition.actionNo != ''">
          and action_no = #{condition.actionNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.entityId != null ">
          and entity_id = #{condition.entityId,jdbcType=BIGINT}
      </if>
      <if test="condition.currencyCode != null and condition.currencyCode != ''">
          and currency_code = #{condition.currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.extractionInterval != null and condition.extractionInterval != ''">
          and extraction_interval = #{condition.extractionInterval,jdbcType=VARCHAR}
      </if>
      <if test="condition.extractionZones != null ">
          and extraction_zones = #{condition.extractionZones,jdbcType=SMALLINT}
      </if>
      <if test="condition.extractionDeadline != null and condition.extractionDeadline != ''">
          and extraction_deadline = #{condition.extractionDeadline,jdbcType=VARCHAR}
      </if>
      <if test="condition.ibnrType != null and condition.ibnrType != ''">
          and ibnr_type = #{condition.ibnrType,jdbcType=VARCHAR}
      </if>
      <if test="condition.loaCode != null and condition.loaCode != ''">
          and loa_code = #{condition.loaCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.status != null and condition.status != ''">
          and status = #{condition.status,jdbcType=VARCHAR}
      </if>
      <if test="condition.errorMsg != null and condition.errorMsg != ''">
          and error_msg = #{condition.errorMsg,jdbcType=VARCHAR}
      </if>
      <if test="condition.confirmIs != null and condition.confirmIs != ''">
          and confirm_is = #{condition.confirmIs,jdbcType=VARCHAR}
      </if>
      <if test="condition.confirmId != null ">
          and confirm_id = #{condition.confirmId,jdbcType=BIGINT}
      </if>
      <if test="condition.confirmTime != null ">
          and confirm_time = #{condition.confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.confirmMethod != null and condition.confirmMethod != ''">
          and confirm_method = #{condition.confirmMethod,jdbcType=VARCHAR}
      </if>
      <if test="condition.createId != null ">
          and create_id = #{condition.createId,jdbcType=BIGINT}
      </if>
      <if test="condition.createTime != null ">
          and create_time = #{condition.createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.completeTime != null ">
          and complete_time = #{condition.completeTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="id != null ">
          and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="actionNo != null and actionNo != ''">
          and action_no = #{actionNo,jdbcType=VARCHAR}
      </if>
      <if test="entityId != null ">
          and entity_id = #{entityId,jdbcType=BIGINT}
      </if>
      <if test="currencyCode != null and currencyCode != ''">
          and currency_code = #{currencyCode,jdbcType=VARCHAR}
      </if>
      <if test="extractionInterval != null and extractionInterval != ''">
          and extraction_interval = #{extractionInterval,jdbcType=VARCHAR}
      </if>
      <if test="extractionZones != null ">
          and extraction_zones = #{extractionZones,jdbcType=SMALLINT}
      </if>
      <if test="extractionDeadline != null and extractionDeadline != ''">
          and extraction_deadline = #{extractionDeadline,jdbcType=VARCHAR}
      </if>
      <if test="ibnrType != null and ibnrType != ''">
          and ibnr_type = #{ibnrType,jdbcType=VARCHAR}
      </if>
      <if test="loaCode != null and loaCode != ''">
          and loa_code = #{loaCode,jdbcType=VARCHAR}
      </if>
      <if test="status != null and status != ''">
          and status = #{status,jdbcType=VARCHAR}
      </if>
      <if test="errorMsg != null and errorMsg != ''">
          and error_msg = #{errorMsg,jdbcType=VARCHAR}
      </if>
      <if test="confirmIs != null and confirmIs != ''">
          and confirm_is = #{confirmIs,jdbcType=VARCHAR}
      </if>
      <if test="confirmId != null ">
          and confirm_id = #{confirmId,jdbcType=BIGINT}
      </if>
      <if test="confirmTime != null ">
          and confirm_time = #{confirmTime,jdbcType=TIMESTAMP}
      </if>
      <if test="confirmMethod != null and confirmMethod != ''">
          and confirm_method = #{confirmMethod,jdbcType=VARCHAR}
      </if>
      <if test="createId != null ">
          and create_id = #{createId,jdbcType=BIGINT}
      </if>
      <if test="createTime != null ">
          and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="completeTime != null ">
          and complete_time = #{completeTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_ibnrcalc_action"
    where id = #{id,jdbcType=BIGINT}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_ibnrcalc_action"
    where id in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_ibnrcalc_action"
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcAction">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_ibnrcalc_action"
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from "atr_buss_ibnrcalc_action"
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="id" keyProperty="id" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcAction"
          keyColumn="id" keyProperty="id" useGeneratedKeys="true">
    insert into "atr_buss_ibnrcalc_action"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="actionNo != null">
        action_no,
      </if>
      <if test="entityId != null">
        entity_id,
      </if>
      <if test="currencyCode != null">
        currency_code,
      </if>
      <if test="extractionInterval != null">
        extraction_interval,
      </if>
      <if test="extractionZones != null">
        extraction_zones,
      </if>
      <if test="extractionDeadline != null">
        extraction_deadline,
      </if>
      <if test="ibnrType != null">
        ibnr_type,
      </if>
      <if test="loaCode != null">
        loa_code,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="errorMsg != null">
        error_msg,
      </if>
      <if test="confirmIs != null">
        confirm_is,
      </if>
      <if test="confirmId != null">
        confirm_id,
      </if>
      <if test="confirmTime != null">
        confirm_time,
      </if>
      <if test="confirmMethod != null">
        confirm_method,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="actionNo != null">
        #{actionNo,jdbcType=VARCHAR},
      </if>
      <if test="entityId != null">
        #{entityId,jdbcType=BIGINT},
      </if>
      <if test="currencyCode != null">
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="extractionInterval != null">
        #{extractionInterval,jdbcType=VARCHAR},
      </if>
      <if test="extractionZones != null">
        #{extractionZones,jdbcType=SMALLINT},
      </if>
      <if test="extractionDeadline != null">
        #{extractionDeadline,jdbcType=VARCHAR},
      </if>
      <if test="ibnrType != null">
        #{ibnrType,jdbcType=VARCHAR},
      </if>
      <if test="loaCode != null">
        #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="confirmIs != null">
        #{confirmIs,jdbcType=VARCHAR},
      </if>
      <if test="confirmId != null">
        #{confirmId,jdbcType=BIGINT},
      </if>
      <if test="confirmTime != null">
        #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmMethod != null">
        #{confirmMethod,jdbcType=VARCHAR},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert into "atr_buss_ibnrcalc_action"
     (id, action_no, entity_id, 
      currency_code, extraction_interval, 
      extraction_zones, extraction_deadline, 
      ibnr_type, loa_code, status, 
      error_msg, confirm_is, confirm_id, 
      confirm_time, confirm_method, 
      create_id, create_time, complete_time
      )
     values 
    <foreach collection="list" item="item" index="index" separator=",">
       (#{item.id,jdbcType=BIGINT}, #{item.actionNo,jdbcType=VARCHAR}, #{item.entityId,jdbcType=BIGINT}, 
        #{item.currencyCode,jdbcType=VARCHAR}, #{item.extractionInterval,jdbcType=VARCHAR}, 
        #{item.extractionZones,jdbcType=SMALLINT}, #{item.extractionDeadline,jdbcType=VARCHAR}, 
        #{item.ibnrType,jdbcType=VARCHAR}, #{item.loaCode,jdbcType=VARCHAR}, #{item.status,jdbcType=VARCHAR}, 
        #{item.errorMsg,jdbcType=VARCHAR}, #{item.confirmIs,jdbcType=VARCHAR}, #{item.confirmId,jdbcType=BIGINT}, 
        #{item.confirmTime,jdbcType=TIMESTAMP}, #{item.confirmMethod,jdbcType=VARCHAR}, 
        #{item.createId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.completeTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcAction">
    update "atr_buss_ibnrcalc_action"
    <set>
      <if test="actionNo != null">
        action_no = #{actionNo,jdbcType=VARCHAR},
      </if>
      <if test="entityId != null">
        entity_id = #{entityId,jdbcType=BIGINT},
      </if>
      <if test="currencyCode != null">
        currency_code = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="extractionInterval != null">
        extraction_interval = #{extractionInterval,jdbcType=VARCHAR},
      </if>
      <if test="extractionZones != null">
        extraction_zones = #{extractionZones,jdbcType=SMALLINT},
      </if>
      <if test="extractionDeadline != null">
        extraction_deadline = #{extractionDeadline,jdbcType=VARCHAR},
      </if>
      <if test="ibnrType != null">
        ibnr_type = #{ibnrType,jdbcType=VARCHAR},
      </if>
      <if test="loaCode != null">
        loa_code = #{loaCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="errorMsg != null">
        error_msg = #{errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="confirmIs != null">
        confirm_is = #{confirmIs,jdbcType=VARCHAR},
      </if>
      <if test="confirmId != null">
        confirm_id = #{confirmId,jdbcType=BIGINT},
      </if>
      <if test="confirmTime != null">
        confirm_time = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmMethod != null">
        confirm_method = #{confirmMethod,jdbcType=VARCHAR},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcAction">
    update "atr_buss_ibnrcalc_action"
    <set>
      <if test="record.actionNo != null">
        action_no = #{record.actionNo,jdbcType=VARCHAR},
      </if>
      <if test="record.entityId != null">
        entity_id = #{record.entityId,jdbcType=BIGINT},
      </if>
      <if test="record.currencyCode != null">
        currency_code = #{record.currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.extractionInterval != null">
        extraction_interval = #{record.extractionInterval,jdbcType=VARCHAR},
      </if>
      <if test="record.extractionZones != null">
        extraction_zones = #{record.extractionZones,jdbcType=SMALLINT},
      </if>
      <if test="record.extractionDeadline != null">
        extraction_deadline = #{record.extractionDeadline,jdbcType=VARCHAR},
      </if>
      <if test="record.ibnrType != null">
        ibnr_type = #{record.ibnrType,jdbcType=VARCHAR},
      </if>
      <if test="record.loaCode != null">
        loa_code = #{record.loaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.errorMsg != null">
        error_msg = #{record.errorMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.confirmIs != null">
        confirm_is = #{record.confirmIs,jdbcType=VARCHAR},
      </if>
      <if test="record.confirmId != null">
        confirm_id = #{record.confirmId,jdbcType=BIGINT},
      </if>
      <if test="record.confirmTime != null">
        confirm_time = #{record.confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.confirmMethod != null">
        confirm_method = #{record.confirmMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.createId != null">
        create_id = #{record.createId,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.completeTime != null">
        complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from "atr_buss_ibnrcalc_action"
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from "atr_buss_ibnrcalc_action"
    where id in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from "atr_buss_ibnrcalc_action"
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussIbnrcalcAction">
    select count(1) from "atr_buss_ibnrcalc_action"
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>