package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.conf.AppConfig;
import com.ss.ifrs.actuarial.dao.AtrBussIbnrcalcActionDao;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcSettledDevAdjustmentVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcSettledDevQueryVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.ibnrcalc.AtrBussIbnrcalcSettledDevResultVo;
import com.ss.ifrs.actuarial.util.dc.DataConverter;
import com.ss.ifrs.actuarial.util.dc.DictParam;
import com.ss.library.utils.HttpResponseUtil;
import com.ss.library.utils.LazyValidator;
import com.ss.library.utils.StringUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

@Service
public class AtrIbnrCalcSettledDevService {

    @Resource
    private AtrBussIbnrcalcActionDao atrBussIbnrcalcActionDao;

    @Resource
    private AppConfig appConfig;

    public void export(AtrBussIbnrcalcSettledDevQueryVo vo, HttpServletResponse response) throws IOException {
        LazyValidator.validate(v -> {
            if (vo == null) {
                v.mark("The condition cannot be empty");
            } else {
                if (!((vo.getApprovalDateStart() != null && vo.getApprovalDateEnd() != null) ||
                        (vo.getAccidentDateStart() != null && vo.getAccidentDateEnd() != null))) {
                    v.mark("At least one set of audit dates and insurance dates cannot be empty");
                }

            }
        });

        List<AtrBussIbnrcalcSettledDevResultVo> resultVos = atrBussIbnrcalcActionDao.querySettledDevData(vo);
        new DataConverter()
                .addDictParam(DictParam.ofEntityCode())
                .addDictParam(DictParam.ofLoa())
                .addDictParam(DictParam.ofUser("updateId"))
                .translate(resultVos);

        byte[] bytes = FileUtils.readFileToByteArray(getTemplateFile());
        ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try (Workbook wb = new XSSFWorkbook(bis)) {
            Sheet sheet = wb.getSheetAt(0);
            copyRow(sheet, 1, resultVos.size());

            Sheet sheetStyle = wb.getSheet("tpl##style");
            CellStyle changeCellStyle = sheetStyle.getRow(0).getCell(0).getCellStyle();

            for (int i = 1; i <= resultVos.size(); i++) {
                AtrBussIbnrcalcSettledDevResultVo resultVo = resultVos.get(i - 1);
                setCellValue(sheet, i, 0, resultVo.getEntityCode());
                setCellValue(sheet, i, 1, resultVo.getRiskCode());
                setCellValue(sheet, i, 2, resultVo.getPolicyNo());
                setCellValue(sheet, i, 3, resultVo.getClaimNo());
                setCellValue(sheet, i, 4, resultVo.getEffectiveDate());
                setCellValue(sheet, i, 5, resultVo.getExpiryDate());
                setCellValue(sheet, i, 6, resultVo.getAccidentDate());
                setCellValue(sheet, i, 7, resultVo.getAccidentYear());
                setCellValue(sheet, i, 8, resultVo.getApprovalDate());
                setCellValue(sheet, i, 9, resultVo.getApprovalYear());
                setCellValue(sheet, i, 10, resultVo.getDevNoOri());
                setCellValue(sheet, i, 11, resultVo.getDevNo());
                setCellValue(sheet, i, 12, resultVo.getCurrencyCode());
                setCellValue(sheet, i, 13, resultVo.getSettledAmount());
                setCellValue(sheet, i, 14, resultVo.getUpdateTime());
                setCellValue(sheet, i, 15, resultVo.getUpdateIdName());

                if (resultVo.getDevNo() != null && !Objects.equals(resultVo.getDevNoOri(), resultVo.getDevNo())) {
                    sheet.getRow(i).getCell(11).setCellStyle(changeCellStyle);
                }
            }

            deleteTplSheet(wb);
            wb.write(bos);
        }

        String fileName = "settled_dev_"
                + DateFormatUtils.format(new Date(), "_yyyyMMdd_HHmmss_")
                + RandomStringUtils.randomAlphanumeric(3).toLowerCase()
                + ".xlsx";
        HttpResponseUtil.xlsx(response, fileName, bos.toByteArray());
    }

    @Transactional
    public void uploadSettledDevAdjustment(MultipartFile uploadFile, Long userId) throws IOException {
        List<AtrBussIbnrcalcSettledDevAdjustmentVo> vos = new ArrayList<>();
        try (InputStream inputStream = uploadFile.getInputStream();
             Workbook wb = new XSSFWorkbook(inputStream)) {

            Sheet sheet = wb.getSheetAt(0);

            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row != null) {
                    Integer devNo = getCellValue(row, 11, Integer.class);
                    if (devNo != null) {
                        AtrBussIbnrcalcSettledDevAdjustmentVo vo = new AtrBussIbnrcalcSettledDevAdjustmentVo();
                        vo.setRowIndex(i + 1);
                        vo.setEntityCode(getCellValue(row, 0, String.class));
                        vo.setClaimNo(getCellValue(row, 3, String.class));
                        vo.setCurrencyCode(getCellValue(row, 12, String.class));
                        vo.setApprovalDate(getCellValue(row, 8, Date.class));
                        vo.setDevNo(devNo);
                        vo.setUpdateId(userId);
                        vos.add(vo);
                    }
                }
            }
        }

        if (!vos.isEmpty()) {
            LazyValidator.validate(v -> {
                for (AtrBussIbnrcalcSettledDevAdjustmentVo vo : vos) {
                    v.notEmpty(vo.getEntityCode(), "ROW[" + vo.getRowIndex() + "] COB");
                    v.notEmpty(vo.getClaimNo(), "ROW[" + vo.getRowIndex() + "] CLAIMNO");
                    v.notEmpty(vo.getCurrencyCode(), "ROW[" + vo.getRowIndex() + "] CURRENCY");
                    v.notEmpty(vo.getApprovalDate(), "ROW[" + vo.getRowIndex() + "] APPROVEDDATE");
                }
            });

            List<List<AtrBussIbnrcalcSettledDevAdjustmentVo>> subLists = splitList(vos);
            for (List<AtrBussIbnrcalcSettledDevAdjustmentVo> subList : subLists) {
                atrBussIbnrcalcActionDao.saveSettledDevAdjustment(subList);
            }
        }

    }

    private <T> List<List<T>> splitList(List<T> all) {
        List<List<T>> subLists = new ArrayList<>();
        int count = 0;
        List<T> subList = new ArrayList<>();
        subLists.add(subList);
        for (T t : all) {
            count++;
            if (count > 100) {
                subList = new ArrayList<>();
                subLists.add(subList);
            }
            subList.add(t);
        }
        return subLists;
    }

    @SuppressWarnings("unchecked")
    private <T> T getCellValue(Row row, int cellIndex, Class<T> clazz) {
        Cell cell = row.getCell(cellIndex);
        if (cell == null) {
            return null;
        }
        if (cell.getCellType() == CellType.BLANK) {
            return null;
        }
        if (Date.class.isAssignableFrom(clazz)) {
            return (T) new DateTime(cell.getDateCellValue()).withTimeAtStartOfDay().toDate();
        }
        Object value;
        if (cell.getCellType() == CellType.NUMERIC) {
            value = cell.getNumericCellValue();
        } else {
            value = cell.getStringCellValue();
        }
        if (String.class.equals(clazz)) {
            return (T) value.toString();
        } else if (Integer.class.equals(clazz)) {
            return (T) Integer.valueOf(new BigDecimal(value.toString()).intValue());
        } else {
            throw new RuntimeException("Unsupport type " + clazz);
        }
    }

    private void setCellValue(Sheet sheet, int rowIndex, int cellIndex, Object value) {
        Cell cell = sheet.getRow(rowIndex).getCell(cellIndex, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
        if (value == null) {
            cell.setCellValue((String) null);
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Date) {
            cell.setCellValue((Date) value);
        } else if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else {
            throw new IllegalArgumentException("Unsupport type " + value.getClass().getName());
        }
    }

    private void copyRow(Sheet sheet, int rowIndex, int len) {
        Row startRow = sheet.getRow(rowIndex);
        for (int i = 1; i < len; i++) {
            Row row = sheet.createRow(rowIndex + i);
            for (int k = 0; k < startRow.getLastCellNum(); k++) {
                Cell cellSource = startRow.getCell(k);
                if (cellSource != null) {
                    Cell cell = row.getCell(k, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                    cell.setCellStyle(cellSource.getCellStyle());
                }
            }
        }
    }

    private File getTemplateFile() {
        String path = StringUtil.joinPaths(appConfig.getBasePath(),
                appConfig.getExportExcelTemplate(),
                "Ibnrcalc_settled_dev_tpl.xlsx");
        File file = new File(path);
        if (!file.exists()) {
            throw new RuntimeException("There is no configuration template file");
        }
        return file;
    }

    private void deleteTplSheet(Workbook wb) {
        Iterator<Sheet> itr = wb.sheetIterator();
        List<Sheet> sheets = new ArrayList<>();
        while (itr.hasNext()) {
            Sheet sheet = itr.next();
            if (sheet.getSheetName().startsWith("tpl##")) {
                sheets.add(sheet);
            }
        }

        for (Sheet sheet : sheets) {
            wb.removeSheetAt(wb.getSheetIndex(sheet));
        }
    }
}
