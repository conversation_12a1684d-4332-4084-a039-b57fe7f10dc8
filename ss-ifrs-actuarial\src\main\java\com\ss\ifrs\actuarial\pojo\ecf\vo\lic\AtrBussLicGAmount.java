package com.ss.ifrs.actuarial.pojo.ecf.vo.lic;

import com.ss.ifrs.actuarial.util.abp.Tab;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Tab("atr_buss_lic_g_amount")
public class AtrBussLicGAmount {
    /**
     * 主表ID
     */
    private Long mainId;
    
    /**
     * 评估期年月
     */
    private String yearMonth;
    
    /**
     * 现金流类型 OS/IBNR/ULAE/RA/RD
     */
    private String cfType;
    
    /**
     * 金额类型 CUR_CF/LAST_CF/MOVEMENT
     */
    private String amountType;
    
    /**
     * 金额
     */
    private BigDecimal amount;
} 