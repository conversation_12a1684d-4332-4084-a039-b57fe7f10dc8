[2m2025-07-15 09:26:30.365[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m The following profiles are active: dev
[2m2025-07-15 09:26:32.080[0;39m [dev] [33m WARN[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-15 09:26:32.343[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=ff1a2d2c-d19d-3931-a591-81b4348e9e38
[2m2025-07-15 09:26:32.854[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 7602 (http)
[2m2025-07-15 09:26:32.946[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 2544 ms
[2m2025-07-15 09:26:33.032[0;39m [dev] [33m WARN[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-15 09:26:33.034[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-15 09:26:33.046[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.netflix.config.DynamicPropertyFactory [0;39m [2m:[0;39m DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@7640a5b1
[2m2025-07-15 09:26:34.082[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-15 09:26:34.083[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-15 09:26:34.200[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-15 09:26:34.200[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-15 09:26:34.811[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.web.DefaultSecurityFilterChain    [0;39m [2m:[0;39m Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5d829ef0, org.springframework.security.web.context.SecurityContextPersistenceFilter@6734ff92, org.springframework.security.web.header.HeaderWriterFilter@4e50ae56, org.springframework.security.web.authentication.logout.LogoutFilter@10e56da9, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@2c2e3460, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4ce267cc, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7fc7152e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4416e18d, org.springframework.security.web.session.SessionManagementFilter@7e2c6702, org.springframework.security.web.access.ExceptionTranslationFilter@615e83ac, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6b09ce57]
[2m2025-07-15 09:26:34.821[0;39m [dev] [33m WARN[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-15 09:26:34.821[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-15 09:26:34.944[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService 'applicationTaskExecutor'
[2m2025-07-15 09:26:35.791[0;39m [dev] [33m WARN[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mockingLoadBalancerClientRibbonWarnLogger[0;39m [2m:[0;39m You already have RibbonLoadBalancerClient on your classpath. It will be used by default. As Spring Cloud Ribbon is in maintenance mode. We recommend switching to BlockingLoadBalancerClient instead. In order to use it, set the value of `spring.cloud.loadbalancer.ribbon.enabled` to `false` or remove spring-cloud-starter-netflix-ribbon from your project.
[2m2025-07-15 09:26:35.911[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-15 09:26:35.948[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Initializing Eureka in region us-east-1
[2m2025-07-15 09:26:35.948[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Client configured to neither register nor query for data.
[2m2025-07-15 09:26:35.958[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Discovery Client initialized at timestamp 1752542795957 with initial instances count: 0
[2m2025-07-15 09:26:36.002[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initializing ...
[2m2025-07-15 09:26:36.005[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Adding new peer nodes [**********************************************/eureka/]
[2m2025-07-15 09:26:36.525[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-15 09:26:36.525[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-15 09:26:36.525[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-15 09:26:36.525[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-15 09:26:36.610[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Replica node URL:  **********************************************/eureka/
[2m2025-07-15 09:26:36.619[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Finished initializing remote region registries. All known remote regions: []
[2m2025-07-15 09:26:36.620[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initialized
[2m2025-07-15 09:26:36.631[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 2 endpoint(s) beneath base path '/actuator'
[2m2025-07-15 09:26:36.715[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application SS-PLATFORM-EUREKA with eureka with status UP
[2m2025-07-15 09:26:36.718[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[      Thread-30][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Setting the eureka configuration..
[2m2025-07-15 09:26:36.718[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[      Thread-30][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka data center value eureka.datacenter is not set, defaulting to default
[2m2025-07-15 09:26:36.718[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[      Thread-30][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka environment value eureka.environment is not set, defaulting to test
[2m2025-07-15 09:26:36.736[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[      Thread-30][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m isAws returned false
[2m2025-07-15 09:26:36.736[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[      Thread-30][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Initialized server context
[2m2025-07-15 09:26:36.737[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[      Thread-30][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Got 1 instances from neighboring DS node
[2m2025-07-15 09:26:36.737[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[      Thread-30][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Renew threshold is: 1
[2m2025-07-15 09:26:36.737[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[      Thread-30][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Changing status to UP
[2m2025-07-15 09:26:36.744[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[      Thread-30][0;39m [36me.s.EurekaServerInitializerConfiguration[0;39m [2m:[0;39m Started Eureka Server
[2m2025-07-15 09:26:36.758[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 7602 (http) with context path ''
[2m2025-07-15 09:26:36.759[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 7602
[2m2025-07-15 09:26:37.212[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m Started EurekaServerApplication in 10.171 seconds (JVM running for 15.624)
[2m2025-07-15 09:26:37.345[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[1)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-15 09:26:37.351[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[1)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 6 ms
[2m2025-07-15 09:26:54.104[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=false)
[2m2025-07-15 09:26:54.740[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=true)
[2m2025-07-15 09:27:04.272[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=false)
[2m2025-07-15 09:27:04.806[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[nio-7602-exec-6][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=true)
[2m2025-07-15 09:27:05.901[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=false)
[2m2025-07-15 09:27:06.354[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[io-7602-exec-10][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=false)
[2m2025-07-15 09:27:06.420[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=true)
[2m2025-07-15 09:27:06.420[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=true)
[2m2025-07-15 09:27:09.278[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-15 09:27:09.798[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-15 09:27:36.741[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-15 09:28:36.752[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-15 09:29:36.753[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-15 09:30:36.755[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-15 09:31:36.769[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-15 09:32:36.781[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-15 09:33:36.796[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-15 09:34:36.805[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-15 09:35:36.817[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-15 09:36:36.820[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-15 09:37:36.826[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-15 09:38:36.831[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-15 09:39:36.830[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-15 09:40:36.843[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-15 09:41:36.627[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-15 09:41:36.857[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-15 09:42:36.863[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-15 09:43:36.867[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-15 09:44:36.874[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-15 09:45:36.885[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-15 09:46:36.888[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-15 09:47:36.900[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-15 09:48:36.912[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-15 09:49:36.924[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-15 09:50:36.936[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-15 09:51:36.948[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-15 09:52:36.951[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-15 09:53:36.955[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-15 09:54:36.960[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-15 09:55:36.973[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-15 09:56:36.639[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-15 09:56:36.980[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-15 09:57:36.983[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-15 09:58:36.998[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-15 09:59:37.010[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-15 10:00:37.021[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-15 10:01:37.023[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-15 10:02:37.027[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-15 10:03:37.033[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-15 10:04:37.035[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-15 10:05:37.047[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-15 10:06:37.056[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-15 10:07:37.058[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-15 10:08:37.071[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-15 10:09:37.072[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-15 10:10:37.078[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-15 10:11:36.650[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-15 10:11:37.081[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-15 10:12:37.095[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-15 10:13:37.108[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-15 10:14:37.121[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-15 10:15:37.134[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-15 10:16:37.138[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-15 10:17:37.159[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 22ms
[2m2025-07-15 10:18:37.165[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-15 10:19:37.174[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-15 10:20:37.180[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-15 10:21:37.187[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-15 10:22:37.196[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-15 10:23:37.202[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-15 10:24:37.207[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-15 10:25:37.219[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-15 10:26:36.662[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-15 10:26:37.233[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-15 10:27:37.239[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-15 10:28:37.253[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-15 10:29:37.259[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-15 10:30:37.269[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-15 10:31:37.282[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-15 10:32:37.287[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-15 10:33:37.300[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-15 10:34:37.305[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-15 10:35:37.309[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-15 10:36:37.324[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-15 10:37:37.326[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-15 10:38:37.328[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-15 10:39:37.339[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-15 10:40:37.343[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-15 10:41:36.670[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-15 10:41:37.352[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-15 10:42:37.358[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-15 10:43:25.025[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status DOWN (replication=false)
[2m2025-07-15 10:43:25.039[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[nio-7602-exec-9][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status DOWN (replication=false)
[2m2025-07-15 10:43:25.042[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status DOWN (replication=false)
[2m2025-07-15 10:43:25.071[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application SS-PLATFORM-EUREKA with eureka with status DOWN
[2m2025-07-15 10:43:25.144[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status DOWN (replication=false)
[2m2025-07-15 10:43:25.230[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shutting down ...
[2m2025-07-15 10:43:25.241[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status DOWN (replication=false)
[2m2025-07-15 10:43:25.303[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.batchSize.Will start computing stats again.
[2m2025-07-15 10:43:25.309[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.executionTime.Will start computing stats again.
[2m2025-07-15 10:43:25.310[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.executionTime.Will start computing stats again.
[2m2025-07-15 10:43:25.310[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.servo.monitor.StatsMonitor  [0;39m [2m:[0;39m Attempting to get the value for an expired monitor: eurekaServer.replication.batchSize.Will start computing stats again.
[2m2025-07-15 10:43:25.414[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Shut down
[2m2025-07-15 10:43:25.457[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Shutting down ExecutorService 'applicationTaskExecutor'
[2m2025-07-15 10:43:25.481[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Shutting down DiscoveryClient ...
[2m2025-07-15 10:43:25.483[0;39m [dev] [32m INFO[0;39m [35m33804[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Completed shut down of DiscoveryClient
