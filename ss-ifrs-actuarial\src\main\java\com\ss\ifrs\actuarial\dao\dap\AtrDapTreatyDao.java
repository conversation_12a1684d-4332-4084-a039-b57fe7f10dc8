/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-02-02 18:13:50
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao.dap;


import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrTemplateVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfQuotaImportVo;
import com.ss.ifrs.actuarial.pojo.atrdap.po.AtrDapDDIcgClaimPaid;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrDapIcgRiskClassVo;
import com.ss.ifrs.actuarial.pojo.other.vo.AtrDapTreatyVo;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-02 18:13:50<br/>
 * Description: 当期实付赔款(合同组维度，直保&临分分入) Dao类<br/>
 * Related Table Name: ATR_DAP_DD_CLAIM_PAID<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrDapTreatyDao extends IDao<AtrDapDDIcgClaimPaid, Long> {

    List<AtrDapTreatyVo> findDapTreatyList(AtrTemplateVo entityId);

    List<AtrDapIcgRiskClassVo> findTreatyIcgList(AtrConfQuotaImportVo entityId);

    List<AtrDapIcgRiskClassVo> findDDIcgList(Long entityId);

    List<AtrDapIcgRiskClassVo> findFOIcgList(Long entityId);
}