package com.ss.ifrs.actuarial.service;

import com.ss.ifrs.actuarial.pojo.atrconf.vo.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
/**
 * <AUTHOR>
 */
public interface AtrConfQuotaService {

    Long countQuota(AtrConfQuotaVo atrConfQuotaVo);

    /**
     * 下载假设值批量导入模板
     * @param request HTTP请求
     * @param response HTTP响应
     * @param confQuotaVo 导入配置VO
     * @throws Exception 异常
     */
    void downloadQtpQuota(HttpServletRequest request, HttpServletResponse response, AtrConfQuotaImportVo confQuotaVo) throws Exception;

    /**
     * 使用按列位置读取Excel数据导入假设值，解决列名可能变更的问题
     * 
     * @param file Excel文件
     * @param atrConfQuotaVo 导入参数
     * @return 导入的假设值列表
     * @throws Exception 导入异常
     */
    List<AtrConfQuotaVo> excelImportToQuotaByPosition(MultipartFile file, AtrConfQuotaImportVo atrConfQuotaVo) throws Exception;
}
