<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-01-11 16:41:00 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.AtrBussLrcCfPeriodDao">
  <!-- 本文件由GIS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfPeriod">
    <id column="LRC_CF_PERIOD_ID" property="lrcCfPeriodId" jdbcType="DECIMAL" />
    <result column="LRC_CF_MAIN_ID" property="lrcCfMainId" jdbcType="DECIMAL" />
    <result column="POLICY_NO_ENDORSEMENT" property="policyNoEndorsement" jdbcType="VARCHAR" />
    <result column="POLICY_NO" property="policyNo" jdbcType="VARCHAR" />
    <result column="PORTFOLIO_NO" property="portfolioNo" jdbcType="VARCHAR" />
    <result column="ICG_NO" property="icgNo" jdbcType="VARCHAR" />
    <result column="ACCUMULATED_EARNED_RATE" property="accumulatedEarnedRate" jdbcType="DECIMAL" />
    <result column="CUR_END_REMAIN_UN_RATE" property="curEndRemainUnRate" jdbcType="DECIMAL" />
    <result column="RPT_PER_REMAIN_UN_RATE" property="rptPerRemainUnRate" jdbcType="DECIMAL" />
    <result column="LP_CUR_END_REMAIN_UN_RATE" property="lpCurEndRemainUnRate" jdbcType="DECIMAL" />
    <result column="LP_RPT_PER_REMAIN_UN_RATE" property="lpRptPerRemainUnRate" jdbcType="DECIMAL" />
    <result column="LRC_CF_FEE_TYPE" property="lrcCfFeeType" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    LRC_CF_PERIOD_ID, LRC_CF_MAIN_ID, POLICY_NO_ENDORSEMENT, POLICY_NO, PORTFOLIO_NO, 
    ICG_NO, ACCUMULATED_EARNED_RATE, CUR_END_REMAIN_UN_RATE, RPT_PER_REMAIN_UN_RATE, 
    LP_CUR_END_REMAIN_UN_RATE, LP_RPT_PER_REMAIN_UN_RATE, LRC_CF_FEE_TYPE
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="lrcCfPeriodId != null ">
          and LRC_CF_PERIOD_ID = #{lrcCfPeriodId,jdbcType=DECIMAL}
      </if>
      <if test="lrcCfMainId != null ">
          and LRC_CF_MAIN_ID = #{lrcCfMainId,jdbcType=DECIMAL}
      </if>
      <if test="policyNoEndorsement != null and policyNoEndorsement != ''">
          and POLICY_NO_ENDORSEMENT = #{policyNoEndorsement,jdbcType=VARCHAR}
      </if>
      <if test="policyNo != null and policyNo != ''">
          and POLICY_NO = #{policyNo,jdbcType=VARCHAR}
      </if>
      <if test="portfolioNo != null and portfolioNo != ''">
          and PORTFOLIO_NO = #{portfolioNo,jdbcType=VARCHAR}
      </if>
      <if test="icgNo != null and icgNo != ''">
          and ICG_NO = #{icgNo,jdbcType=VARCHAR}
      </if>
      <if test="accumulatedEarnedRate != null ">
          and ACCUMULATED_EARNED_RATE = #{accumulatedEarnedRate,jdbcType=DECIMAL}
      </if>
      <if test="curEndRemainUnRate != null ">
          and CUR_END_REMAIN_UN_RATE = #{curEndRemainUnRate,jdbcType=DECIMAL}
      </if>
      <if test="rptPerRemainUnRate != null ">
          and RPT_PER_REMAIN_UN_RATE = #{rptPerRemainUnRate,jdbcType=DECIMAL}
      </if>
      <if test="lpCurEndRemainUnRate != null ">
          and LP_CUR_END_REMAIN_UN_RATE = #{lpCurEndRemainUnRate,jdbcType=DECIMAL}
      </if>
      <if test="lpRptPerRemainUnRate != null ">
          and LP_RPT_PER_REMAIN_UN_RATE = #{lpRptPerRemainUnRate,jdbcType=DECIMAL}
      </if>
      <if test="lrcCfFeeType != null and lrcCfFeeType != ''">
          and LRC_CF_FEE_TYPE = #{lrcCfFeeType,jdbcType=VARCHAR}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.lrcCfPeriodId != null ">
          and LRC_CF_PERIOD_ID = #{condition.lrcCfPeriodId,jdbcType=DECIMAL}
      </if>
      <if test="condition.lrcCfMainId != null ">
          and LRC_CF_MAIN_ID = #{condition.lrcCfMainId,jdbcType=DECIMAL}
      </if>
      <if test="condition.policyNoEndorsement != null and condition.policyNoEndorsement != ''">
          and POLICY_NO_ENDORSEMENT = #{condition.policyNoEndorsement,jdbcType=VARCHAR}
      </if>
      <if test="condition.policyNo != null and condition.policyNo != ''">
          and POLICY_NO = #{condition.policyNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.portfolioNo != null and condition.portfolioNo != ''">
          and PORTFOLIO_NO = #{condition.portfolioNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.icgNo != null and condition.icgNo != ''">
          and ICG_NO = #{condition.icgNo,jdbcType=VARCHAR}
      </if>
      <if test="condition.accumulatedEarnedRate != null ">
          and ACCUMULATED_EARNED_RATE = #{condition.accumulatedEarnedRate,jdbcType=DECIMAL}
      </if>
      <if test="condition.curEndRemainUnRate != null ">
          and CUR_END_REMAIN_UN_RATE = #{condition.curEndRemainUnRate,jdbcType=DECIMAL}
      </if>
      <if test="condition.rptPerRemainUnRate != null ">
          and RPT_PER_REMAIN_UN_RATE = #{condition.rptPerRemainUnRate,jdbcType=DECIMAL}
      </if>
      <if test="condition.lpCurEndRemainUnRate != null ">
          and LP_CUR_END_REMAIN_UN_RATE = #{condition.lpCurEndRemainUnRate,jdbcType=DECIMAL}
      </if>
      <if test="condition.lpRptPerRemainUnRate != null ">
          and LP_RPT_PER_REMAIN_UN_RATE = #{condition.lpRptPerRemainUnRate,jdbcType=DECIMAL}
      </if>
      <if test="condition.lrcCfFeeType != null and condition.lrcCfFeeType != ''">
          and LRC_CF_FEE_TYPE = #{condition.lrcCfFeeType,jdbcType=VARCHAR}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="lrcCfPeriodId != null ">
          and LRC_CF_PERIOD_ID = #{lrcCfPeriodId,jdbcType=DECIMAL}
      </if>
      <if test="lrcCfMainId != null ">
          and LRC_CF_MAIN_ID = #{lrcCfMainId,jdbcType=DECIMAL}
      </if>
      <if test="policyNoEndorsement != null and policyNoEndorsement != ''">
          and POLICY_NO_ENDORSEMENT = #{policyNoEndorsement,jdbcType=VARCHAR}
      </if>
      <if test="policyNo != null and policyNo != ''">
          and POLICY_NO = #{policyNo,jdbcType=VARCHAR}
      </if>
      <if test="portfolioNo != null and portfolioNo != ''">
          and PORTFOLIO_NO = #{portfolioNo,jdbcType=VARCHAR}
      </if>
      <if test="icgNo != null and icgNo != ''">
          and ICG_NO = #{icgNo,jdbcType=VARCHAR}
      </if>
      <if test="accumulatedEarnedRate != null ">
          and ACCUMULATED_EARNED_RATE = #{accumulatedEarnedRate,jdbcType=DECIMAL}
      </if>
      <if test="curEndRemainUnRate != null ">
          and CUR_END_REMAIN_UN_RATE = #{curEndRemainUnRate,jdbcType=DECIMAL}
      </if>
      <if test="rptPerRemainUnRate != null ">
          and RPT_PER_REMAIN_UN_RATE = #{rptPerRemainUnRate,jdbcType=DECIMAL}
      </if>
      <if test="lpCurEndRemainUnRate != null ">
          and LP_CUR_END_REMAIN_UN_RATE = #{lpCurEndRemainUnRate,jdbcType=DECIMAL}
      </if>
      <if test="lpRptPerRemainUnRate != null ">
          and LP_RPT_PER_REMAIN_UN_RATE = #{lpRptPerRemainUnRate,jdbcType=DECIMAL}
      </if>
      <if test="lrcCfFeeType != null and lrcCfFeeType != ''">
          and LRC_CF_FEE_TYPE = #{lrcCfFeeType,jdbcType=VARCHAR}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_LRC_CF_PERIOD
    where LRC_CF_PERIOD_ID = #{lrcCfPeriodId,jdbcType=DECIMAL}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_LRC_CF_PERIOD
    where LRC_CF_PERIOD_ID in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_LRC_CF_PERIOD
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfPeriod">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_LRC_CF_PERIOD
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from ATR_BUSS_LRC_CF_PERIOD
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="LRC_CF_PERIOD_ID" keyProperty="lrcCfPeriodId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfPeriod">
    <selectKey resultType="long" keyProperty="lrcCfPeriodId" order="BEFORE">
      select nextval('ATR_SEQ_BUSS_LRC_CF_PERIOD') as sequenceNo 
    </selectKey>
    insert into ATR_BUSS_LRC_CF_PERIOD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="lrcCfPeriodId != null">
        LRC_CF_PERIOD_ID,
      </if>
      <if test="lrcCfMainId != null">
        LRC_CF_MAIN_ID,
      </if>
      <if test="policyNoEndorsement != null">
        POLICY_NO_ENDORSEMENT,
      </if>
      <if test="policyNo != null">
        POLICY_NO,
      </if>
      <if test="portfolioNo != null">
        PORTFOLIO_NO,
      </if>
      <if test="icgNo != null">
        ICG_NO,
      </if>
      <if test="accumulatedEarnedRate != null">
        ACCUMULATED_EARNED_RATE,
      </if>
      <if test="curEndRemainUnRate != null">
        CUR_END_REMAIN_UN_RATE,
      </if>
      <if test="rptPerRemainUnRate != null">
        RPT_PER_REMAIN_UN_RATE,
      </if>
      <if test="lpCurEndRemainUnRate != null">
        LP_CUR_END_REMAIN_UN_RATE,
      </if>
      <if test="lpRptPerRemainUnRate != null">
        LP_RPT_PER_REMAIN_UN_RATE,
      </if>
      <if test="lrcCfFeeType != null">
        LRC_CF_FEE_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="lrcCfPeriodId != null">
        #{lrcCfPeriodId,jdbcType=DECIMAL},
      </if>
      <if test="lrcCfMainId != null">
        #{lrcCfMainId,jdbcType=DECIMAL},
      </if>
      <if test="policyNoEndorsement != null">
        #{policyNoEndorsement,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="portfolioNo != null">
        #{portfolioNo,jdbcType=VARCHAR},
      </if>
      <if test="icgNo != null">
        #{icgNo,jdbcType=VARCHAR},
      </if>
      <if test="accumulatedEarnedRate != null">
        #{accumulatedEarnedRate,jdbcType=DECIMAL},
      </if>
      <if test="curEndRemainUnRate != null">
        #{curEndRemainUnRate,jdbcType=DECIMAL},
      </if>
      <if test="rptPerRemainUnRate != null">
        #{rptPerRemainUnRate,jdbcType=DECIMAL},
      </if>
      <if test="lpCurEndRemainUnRate != null">
        #{lpCurEndRemainUnRate,jdbcType=DECIMAL},
      </if>
      <if test="lpRptPerRemainUnRate != null">
        #{lpRptPerRemainUnRate,jdbcType=DECIMAL},
      </if>
      <if test="lrcCfFeeType != null">
        #{lrcCfFeeType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all 
    <foreach collection="list" item="item" index="index">
      into ATR_BUSS_LRC_CF_PERIOD values 
       (#{item.lrcCfPeriodId,jdbcType=DECIMAL}, 
        #{item.lrcCfMainId,jdbcType=DECIMAL}, #{item.policyNoEndorsement,jdbcType=VARCHAR}, 
        #{item.policyNo,jdbcType=VARCHAR}, #{item.portfolioNo,jdbcType=VARCHAR}, #{item.icgNo,jdbcType=VARCHAR}, 
        #{item.accumulatedEarnedRate,jdbcType=DECIMAL}, #{item.curEndRemainUnRate,jdbcType=DECIMAL}, 
        #{item.rptPerRemainUnRate,jdbcType=DECIMAL}, #{item.lpCurEndRemainUnRate,jdbcType=DECIMAL}, 
        #{item.lpRptPerRemainUnRate,jdbcType=DECIMAL}, #{item.lrcCfFeeType,jdbcType=VARCHAR}
        )
    </foreach>
    select 1 
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfPeriod">
    update ATR_BUSS_LRC_CF_PERIOD
    <set>
      <if test="lrcCfMainId != null">
        LRC_CF_MAIN_ID = #{lrcCfMainId,jdbcType=DECIMAL},
      </if>
      <if test="policyNoEndorsement != null">
        POLICY_NO_ENDORSEMENT = #{policyNoEndorsement,jdbcType=VARCHAR},
      </if>
      <if test="policyNo != null">
        POLICY_NO = #{policyNo,jdbcType=VARCHAR},
      </if>
      <if test="portfolioNo != null">
        PORTFOLIO_NO = #{portfolioNo,jdbcType=VARCHAR},
      </if>
      <if test="icgNo != null">
        ICG_NO = #{icgNo,jdbcType=VARCHAR},
      </if>
      <if test="accumulatedEarnedRate != null">
        ACCUMULATED_EARNED_RATE = #{accumulatedEarnedRate,jdbcType=DECIMAL},
      </if>
      <if test="curEndRemainUnRate != null">
        CUR_END_REMAIN_UN_RATE = #{curEndRemainUnRate,jdbcType=DECIMAL},
      </if>
      <if test="rptPerRemainUnRate != null">
        RPT_PER_REMAIN_UN_RATE = #{rptPerRemainUnRate,jdbcType=DECIMAL},
      </if>
      <if test="lpCurEndRemainUnRate != null">
        LP_CUR_END_REMAIN_UN_RATE = #{lpCurEndRemainUnRate,jdbcType=DECIMAL},
      </if>
      <if test="lpRptPerRemainUnRate != null">
        LP_RPT_PER_REMAIN_UN_RATE = #{lpRptPerRemainUnRate,jdbcType=DECIMAL},
      </if>
      <if test="lrcCfFeeType != null">
        LRC_CF_FEE_TYPE = #{lrcCfFeeType,jdbcType=VARCHAR},
      </if>
    </set>
    where LRC_CF_PERIOD_ID = #{lrcCfPeriodId,jdbcType=DECIMAL}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfPeriod">
    update ATR_BUSS_LRC_CF_PERIOD
    <set>
      <if test="record.lrcCfMainId != null">
        LRC_CF_MAIN_ID = #{record.lrcCfMainId,jdbcType=DECIMAL},
      </if>
      <if test="record.policyNoEndorsement != null">
        POLICY_NO_ENDORSEMENT = #{record.policyNoEndorsement,jdbcType=VARCHAR},
      </if>
      <if test="record.policyNo != null">
        POLICY_NO = #{record.policyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.portfolioNo != null">
        PORTFOLIO_NO = #{record.portfolioNo,jdbcType=VARCHAR},
      </if>
      <if test="record.icgNo != null">
        ICG_NO = #{record.icgNo,jdbcType=VARCHAR},
      </if>
      <if test="record.accumulatedEarnedRate != null">
        ACCUMULATED_EARNED_RATE = #{record.accumulatedEarnedRate,jdbcType=DECIMAL},
      </if>
      <if test="record.curEndRemainUnRate != null">
        CUR_END_REMAIN_UN_RATE = #{record.curEndRemainUnRate,jdbcType=DECIMAL},
      </if>
      <if test="record.rptPerRemainUnRate != null">
        RPT_PER_REMAIN_UN_RATE = #{record.rptPerRemainUnRate,jdbcType=DECIMAL},
      </if>
      <if test="record.lpCurEndRemainUnRate != null">
        LP_CUR_END_REMAIN_UN_RATE = #{record.lpCurEndRemainUnRate,jdbcType=DECIMAL},
      </if>
      <if test="record.lpRptPerRemainUnRate != null">
        LP_RPT_PER_REMAIN_UN_RATE = #{record.lpRptPerRemainUnRate,jdbcType=DECIMAL},
      </if>
      <if test="record.lrcCfFeeType != null">
        LRC_CF_FEE_TYPE = #{record.lrcCfFeeType,jdbcType=VARCHAR},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from ATR_BUSS_LRC_CF_PERIOD
    where LRC_CF_PERIOD_ID = #{lrcCfPeriodId,jdbcType=DECIMAL}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from ATR_BUSS_LRC_CF_PERIOD
    where LRC_CF_PERIOD_ID in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from ATR_BUSS_LRC_CF_PERIOD
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcCfPeriod">
    select count(1) from ATR_BUSS_LRC_CF_PERIOD
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>