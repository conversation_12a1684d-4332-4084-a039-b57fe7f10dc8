<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="efb6cdce-7bef-459b-9014-907921cc6acd" name="Changes" comment="perf: 优化比例合约分出计算逻辑">
      <change beforePath="$PROJECT_DIR$/ss-ifrs-actuarial/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ss-ifrs-actuarial/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-ifrs-actuarial/src/main/java/com/ss/ifrs/actuarial/service/impl/AtrBussLrcTotService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ss-ifrs-actuarial/src/main/java/com/ss/ifrs/actuarial/service/impl/AtrBussLrcTotService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-ifrs-actuarial/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ss-ifrs-actuarial/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-ifrs-actuarial/src/main/resources/mapper/postgres/custom/buss/puhua/lrc/AtrBussDDLrcGCustDao.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ss-ifrs-actuarial/src/main/resources/mapper/postgres/custom/buss/puhua/lrc/AtrBussDDLrcGCustDao.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-ifrs-actuarial/src/main/resources/mapper/postgres/custom/buss/puhua/lrc/AtrBussDDLrcUCustDao.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ss-ifrs-actuarial/src/main/resources/mapper/postgres/custom/buss/puhua/lrc/AtrBussDDLrcUCustDao.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-ifrs-actuarial/src/main/resources/mapper/postgres/custom/ecf/AtrBussLrcToCustDao.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ss-ifrs-actuarial/src/main/resources/mapper/postgres/custom/ecf/AtrBussLrcToCustDao.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-ifrs-quantification/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ss-ifrs-quantification/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-library-mq/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ss-library-mq/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-library-mybatis/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ss-library-mybatis/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-library-security/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ss-library-security/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-library-utils/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ss-library-utils/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-platform-admin/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ss-platform-admin/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-platform-admin/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ss-platform-admin/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-platform-base/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ss-platform-base/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-platform-common/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ss-platform-common/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-platform-common/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ss-platform-common/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-platform-gateway/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ss-platform-gateway/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-platform-gateway/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ss-platform-gateway/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-platform-schedule/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ss-platform-schedule/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-platform-schedule/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ss-platform-schedule/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-web-vue-atr/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/ss-web-vue-atr/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-web-vue-atr/src/pages/atr/expectedCashFlow/puhua/lrcCashFlowApp/components/card.js" beforeDir="false" afterPath="$PROJECT_DIR$/ss-web-vue-atr/src/pages/atr/expectedCashFlow/puhua/lrcCashFlowApp/components/card.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-web-vue-atr/yarn.lock" beforeDir="false" afterPath="$PROJECT_DIR$/ss-web-vue-atr/yarn.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-web-vue-dm/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/ss-web-vue-dm/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-web-vue-dm/yarn.lock" beforeDir="false" afterPath="$PROJECT_DIR$/ss-web-vue-dm/yarn.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-web-vue-exp/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/ss-web-vue-exp/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-web-vue-exp/yarn.lock" beforeDir="false" afterPath="$PROJECT_DIR$/ss-web-vue-exp/yarn.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-web-vue/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/ss-web-vue/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-web-vue/vue.config.js" beforeDir="false" afterPath="$PROJECT_DIR$/ss-web-vue/vue.config.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ss-web-vue/yarn.lock" beforeDir="false" afterPath="$PROJECT_DIR$/ss-web-vue/yarn.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_临分分出1_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_临分分出1_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_临分分出2_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_临分分出2_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_临分分出3_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_临分分出3_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_临分分出4_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_临分分出4_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_合约分出1_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_合约分出1_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_合约分出2_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_合约分出2_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_合约分出3_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_合约分出3_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_合约分出4_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_合约分出4_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保10_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保10_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保11_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保11_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保12_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保12_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保13_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保13_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保14_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保14_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保15_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保15_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保16_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保16_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保1_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保1_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保2_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保2_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保3_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保3_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保4_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保4_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保5_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保5_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保6_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保6_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保7_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保7_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保8_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保8_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保9_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/lrc/底层_LRC_直保9_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/底层_LRC_临分分出_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/底层_LRC_临分分出_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/底层_LRC_临分分出_zh.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/底层_LRC_临分分出_zh.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/底层_LRC_合约分出_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/底层_LRC_合约分出_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/底层_LRC_合约分出_zh.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/底层_LRC_合约分出_zh.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/底层_LRC_直保_en.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/底层_LRC_直保_en.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/底层_LRC_直保_zh.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/底层_LRC_直保_zh.xlsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/底层_LRC_超赔分出_zh.xlsx" beforeDir="false" afterPath="$PROJECT_DIR$/templatedoc/atr/export/excelTemplate/底层_LRC_超赔分出_zh.xlsx" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/ss-ifrs-actuarial" />
    <option name="ROOT_SYNC" value="DONT_SYNC" />
    <option name="UPDATE_TYPE" value="REBASE" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$USER_HOME$/.jabba/jdk/1.8/jre/lib/rt.jar!/java/util/Map.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../../environments/maven/repository/com/alibaba/easyexcel-core/3.1.0/easyexcel-core-3.1.0-sources.jar!/com/alibaba/excel/event/AnalysisEventListener.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../../environments/maven/repository/com/alibaba/easyexcel-core/3.1.0/easyexcel-core-3.1.0-sources.jar!/com/alibaba/excel/read/processor/DefaultAnalysisEventProcessor.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../../environments/maven/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.34/tomcat-embed-core-9.0.34-sources.jar!/org/apache/catalina/core/ApplicationContext.java" root0="SKIP_INSPECTION" />
  </component>
  <component name="HttpClientSelectedEnvironments">
    <file url="jar://$APPLICATION_HOME_DIR$/plugins/restClient/lib/restClient.jar!/com/intellij/ws/rest/client/requests/collection/post-requests.http" environment="test" />
  </component>
  <component name="JRebelWorkspace">
    <option name="jrebelEnabledAutocompile" value="true" />
    <option name="hasSeenReactiveStreamsDisablingDialog" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2vR8gSz6aQ0BkeoShBNvPnonLa5" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "HTTP Request.ActuarialServiceApplication | #3.executor": "Run",
    "JUnit.TestMethod.test.executor": "Debug",
    "JUnit.TestMethod.test2.executor": "Run",
    "JUnit.TestOO.test.executor": "Run",
    "JUnit.TestP.test.executor": "Run",
    "JUnit.TestP.test11.executor": "Run",
    "JUnit.TestPP.test.executor": "Run",
    "JUnit.TestQ.test.executor": "Run",
    "JUnit.TestQ.test2.executor": "Run",
    "JUnit.TestQQ.testQQ1.executor": "Run",
    "JUnit.TestQQ.testQQ2.executor": "Debug",
    "Maven.ss-ifrs-actuarial [clean].executor": "Run",
    "Maven.ss-ifrs-actuarial [package].executor": "Run",
    "Maven.ss-ifrs-actuarial [verify].executor": "Run",
    "Maven.ss-ifrs-datamgr [package].executor": "Run",
    "Maven.ss-ifrs-quantification [clean].executor": "Run",
    "Maven.ss-ifrs-quantification [package].executor": "Run",
    "Maven.ss-library-mybatis [install].executor": "Run",
    "Maven.ss-library-utils [install].executor": "Run",
    "Maven.ss-library-utils [package].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.ActuarialServiceApplication.executor": "JRebel Debug",
    "Spring Boot.AdminServiceApplication.executor": "Run",
    "Spring Boot.CommonServiceApplication.executor": "Run",
    "Spring Boot.DataMgrServiceApplication.executor": "Run",
    "Spring Boot.EurekaServerApplication.executor": "Run",
    "Spring Boot.GatewayApplication.executor": "Run",
    "Spring Boot.QuantificationServiceApplication.executor": "Run",
    "Spring Boot.ScheduleServiceApplication.executor": "Run",
    "git-widget-placeholder": "develop",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "O:/workspace/documents/temp/ss-ifrs-actuarial",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "npm.build (1).executor": "Run",
    "npm.build.executor": "Run",
    "npm.serve (1).executor": "Run",
    "npm.serve.executor": "Run",
    "project.structure.last.edited": "Modules",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.2",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "com.zeroturnaround.javarebel.idea.plugin.settings.JRebelSettingsComponent",
    "ts.external.directory.path": "O:\\applications\\Jetbrain\\IntelliJ IDEA 2024.3.2\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "postgresql"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="O:\workspace\coder\ifrs17-hgic\ss-ifrs-expense" />
      <recent name="O:\workspace\coder\ifrs17-hgic\ss-ifrs-datamgr" />
      <recent name="O:\workspace\coder\ifrs17-hgic\ss-web-vue-exp" />
      <recent name="O:\workspace\coder\ifrs17-hgic\ss-web-vue-dm" />
      <recent name="O:\workspace\coder\ifrs17-hgic\templatedoc\atr\export\excelTemplate" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="O:\workspace\coder\ifrs17-hgic\db\bpluser\DML" />
      <recent name="O:\workspace\coder\ifrs17-hgic\ss-web-vue-atr\src\pages\atr\expectedCashFlow\dataImport\components" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.ss.ifrs.actuarial.pojo.ecf.vo.lrc.to" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="npm.build">
    <configuration default="true" type="JUnit" factoryName="JUnit">
      <shortenClasspath name="MANIFEST" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ActuarialServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="ss-ifrs-actuarial" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ss.ifrs.actuarial.ActuarialServiceApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ss.ifrs.actuarial.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AdminServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ss-platform-admin" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ss.platform.admin.AdminServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CommonServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ss-platform-common" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ss.platform.common.CommonServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DataMgrServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ss-ifrs-datamgr" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ss.ifrs.datamgr.DataMgrServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="EurekaServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="ss-platform-eureka" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ss.platform.eureka.EurekaServerApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ss.platform.eureka.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ss-platform-gateway" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ss.platform.gateway.GatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="QuantificationServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ss-ifrs-quantification" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ss.ifrs.quantification.QuantificationServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ScheduleServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ss-platform-schedule" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ss.platform.schedule.ScheduleServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="build" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/ss-web-vue-atr/package.json" />
      <command value="run" />
      <scripts>
        <script value="build" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="serve (1)" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/ss-web-vue-atr/package.json" />
      <command value="run" />
      <scripts>
        <script value="serve" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="serve" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/ss-web-vue/package.json" />
      <command value="run" />
      <scripts>
        <script value="serve" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="npm.build" />
      <item itemvalue="npm.serve" />
      <item itemvalue="npm.serve (1)" />
      <item itemvalue="Spring Boot.DataMgrServiceApplication" />
      <item itemvalue="Spring Boot.QuantificationServiceApplication" />
      <item itemvalue="Spring Boot.AdminServiceApplication" />
      <item itemvalue="Spring Boot.CommonServiceApplication" />
      <item itemvalue="Spring Boot.GatewayApplication" />
      <item itemvalue="Spring Boot.ScheduleServiceApplication" />
      <item itemvalue="Spring Boot.EurekaServerApplication" />
      <item itemvalue="Spring Boot.ActuarialServiceApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="npm.build" />
        <item itemvalue="npm.serve" />
        <item itemvalue="npm.serve (1)" />
        <item itemvalue="Spring Boot.ActuarialServiceApplication" />
        <item itemvalue="Spring Boot.EurekaServerApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-125ca727e0f0-intellij.indexing.shared.core-IU-243.23654.117" />
        <option value="bundled-js-predefined-d6986cc7102b-822845ee3bb5-JavaScript-IU-243.23654.117" />
      </set>
    </attachedChunks>
  </component>
  <component name="ShelveChangesManager">
    <option name="remove_strategy" value="true" />
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="efb6cdce-7bef-459b-9014-907921cc6acd" name="Changes" comment="" />
      <created>1744096638811</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1744096638811</updated>
      <workItem from="1744096640011" duration="47372000" />
      <workItem from="1744267883806" duration="263000" />
      <workItem from="1744268164279" duration="84000" />
      <workItem from="1744270281384" duration="12022000" />
      <workItem from="1744790497912" duration="43622000" />
      <workItem from="1745201695929" duration="405565000" />
      <workItem from="1747185212725" duration="326111000" />
      <workItem from="1748913109876" duration="243356000" />
      <workItem from="1749810013488" duration="143962000" />
      <workItem from="1750554440828" duration="3890000" />
      <workItem from="1750639848151" duration="213397000" />
      <workItem from="1751077578440" duration="2286000" />
      <workItem from="1751104954630" duration="177386000" />
      <workItem from="1751608855324" duration="265646000" />
      <workItem from="1752714406072" duration="104051000" />
      <workItem from="1753256169724" duration="139109000" />
    </task>
    <task id="LOCAL-00101" summary="fix: 字段调整修复">
      <option name="closed" value="true" />
      <created>1751418769883</created>
      <option name="number" value="00101" />
      <option name="presentableId" value="LOCAL-00101" />
      <option name="project" value="LOCAL" />
      <updated>1751418769883</updated>
    </task>
    <task id="LOCAL-00102" summary="fix: 修复推送计量字段映射异常的问题">
      <option name="closed" value="true" />
      <created>1751422962557</created>
      <option name="number" value="00102" />
      <option name="presentableId" value="LOCAL-00102" />
      <option name="project" value="LOCAL" />
      <updated>1751422962557</updated>
    </task>
    <task id="LOCAL-00103" summary="fix: 修复lic 推送计量缺失辅助核算项的问题">
      <option name="closed" value="true" />
      <created>1751430363567</created>
      <option name="number" value="00103" />
      <option name="presentableId" value="LOCAL-00103" />
      <option name="project" value="LOCAL" />
      <updated>1751430363567</updated>
    </task>
    <task id="LOCAL-00104" summary="fix: 修复直保倒签单场景计算异常的问题">
      <option name="closed" value="true" />
      <created>1751452151985</created>
      <option name="number" value="00104" />
      <option name="presentableId" value="LOCAL-00104" />
      <option name="project" value="LOCAL" />
      <updated>1751452151985</updated>
    </task>
    <task id="LOCAL-00105" summary="feat: 字段映射取别名">
      <option name="closed" value="true" />
      <created>1751454395036</created>
      <option name="number" value="00105" />
      <option name="presentableId" value="LOCAL-00105" />
      <option name="project" value="LOCAL" />
      <updated>1751454395036</updated>
    </task>
    <task id="LOCAL-00106" summary="fix: 调整精度的处理">
      <option name="closed" value="true" />
      <created>1751461324825</created>
      <option name="number" value="00106" />
      <option name="presentableId" value="LOCAL-00106" />
      <option name="project" value="LOCAL" />
      <updated>1751461324825</updated>
    </task>
    <task id="LOCAL-00107" summary="fix: 去掉lic中的risk_code推送">
      <option name="closed" value="true" />
      <created>1751461371014</created>
      <option name="number" value="00107" />
      <option name="presentableId" value="LOCAL-00107" />
      <option name="project" value="LOCAL" />
      <updated>1751461371014</updated>
    </task>
    <task id="LOCAL-00108" summary="feat: 合约分出新增到分项维度计算维度">
      <option name="closed" value="true" />
      <created>1751529766840</created>
      <option name="number" value="00108" />
      <option name="presentableId" value="LOCAL-00108" />
      <option name="project" value="LOCAL" />
      <updated>1751529766840</updated>
    </task>
    <task id="LOCAL-00109" summary="fix: 修复主表插入异常导致id异常的问题">
      <option name="closed" value="true" />
      <created>1751531133958</created>
      <option name="number" value="00109" />
      <option name="presentableId" value="LOCAL-00109" />
      <option name="project" value="LOCAL" />
      <updated>1751531133958</updated>
    </task>
    <task id="LOCAL-00110" summary="feat: 合约分出计算改为全量">
      <option name="closed" value="true" />
      <created>1751534817476</created>
      <option name="number" value="00110" />
      <option name="presentableId" value="LOCAL-00110" />
      <option name="project" value="LOCAL" />
      <updated>1751534817477</updated>
    </task>
    <task id="LOCAL-00111" summary="fix: 修复主表插入异常导致id异常的问题">
      <option name="closed" value="true" />
      <created>1751539324879</created>
      <option name="number" value="00111" />
      <option name="presentableId" value="LOCAL-00111" />
      <option name="project" value="LOCAL" />
      <updated>1751539324879</updated>
    </task>
    <task id="LOCAL-00112" summary="fix: 修复合约分出计算结果异常以及直保非跟单获取费用异常的问题">
      <option name="closed" value="true" />
      <created>1751626651708</created>
      <option name="number" value="00112" />
      <option name="presentableId" value="LOCAL-00112" />
      <option name="project" value="LOCAL" />
      <updated>1751626651710</updated>
    </task>
    <task id="LOCAL-00113" summary="refactor: 合约全量推送取数逻辑调整">
      <option name="closed" value="true" />
      <created>1751853221127</created>
      <option name="number" value="00113" />
      <option name="presentableId" value="LOCAL-00113" />
      <option name="project" value="LOCAL" />
      <updated>1751853221127</updated>
    </task>
    <task id="LOCAL-00114" summary="fix(reinsurance): 修复LRC/LIC模块中的多项数据计算和同步问题&#10;主要修复内容包括：&#10;**LRC直保**: 纠正了在获取实收保费时业务年月不匹配的问题。&#10;**LRC临分分出**: 修复了获取底层保单费用时的逻辑错误。&#10;**LIC**: 修复了LIC结果计算错误。&#10;**数据推送**: 修正了推送给上层计量系统的数据结构或数值异常。">
      <option name="closed" value="true" />
      <created>1752023987098</created>
      <option name="number" value="00114" />
      <option name="presentableId" value="LOCAL-00114" />
      <option name="project" value="LOCAL" />
      <updated>1752023987098</updated>
    </task>
    <task id="LOCAL-00115" summary="fix: 修复加权利率计算存在的问题">
      <option name="closed" value="true" />
      <created>1752033403253</created>
      <option name="number" value="00115" />
      <option name="presentableId" value="LOCAL-00115" />
      <option name="project" value="LOCAL" />
      <updated>1752033403253</updated>
    </task>
    <task id="LOCAL-00116" summary="perf: 优化LIC流程">
      <option name="closed" value="true" />
      <created>1752055423858</created>
      <option name="number" value="00116" />
      <option name="presentableId" value="LOCAL-00116" />
      <option name="project" value="LOCAL" />
      <updated>1752055423858</updated>
    </task>
    <task id="LOCAL-00117" summary="feat: LRC合约分出添加到再保人维度的信息以及修复了已知问题">
      <option name="closed" value="true" />
      <created>1752225718039</created>
      <option name="number" value="00117" />
      <option name="presentableId" value="LOCAL-00117" />
      <option name="project" value="LOCAL" />
      <updated>1752225718039</updated>
    </task>
    <task id="LOCAL-00118" summary="refactor: 调整LIC的RA计算到单层级，LRC直接业务跟单获取费用到ext表取值">
      <option name="closed" value="true" />
      <created>1752459204836</created>
      <option name="number" value="00118" />
      <option name="presentableId" value="LOCAL-00118" />
      <option name="project" value="LOCAL" />
      <updated>1752459204837</updated>
    </task>
    <task id="LOCAL-00119" summary="feat: 去掉超赔分出投资成分的推送">
      <option name="closed" value="true" />
      <created>1752551991309</created>
      <option name="number" value="00119" />
      <option name="presentableId" value="LOCAL-00119" />
      <option name="project" value="LOCAL" />
      <updated>1752551991309</updated>
    </task>
    <task id="LOCAL-00120" summary="fix: 修复折现计算存在的问题">
      <option name="closed" value="true" />
      <created>1752628681333</created>
      <option name="number" value="00120" />
      <option name="presentableId" value="LOCAL-00120" />
      <option name="project" value="LOCAL" />
      <updated>1752628681334</updated>
    </task>
    <task id="LOCAL-00121" summary="perf: 优化合约分入的计算逻辑">
      <option name="closed" value="true" />
      <created>1752637580144</created>
      <option name="number" value="00121" />
      <option name="presentableId" value="LOCAL-00121" />
      <option name="project" value="LOCAL" />
      <updated>1752637580144</updated>
    </task>
    <task id="LOCAL-00122" summary="perf: 优化推送到计量的性能">
      <option name="closed" value="true" />
      <created>1752748108759</created>
      <option name="number" value="00122" />
      <option name="presentableId" value="LOCAL-00122" />
      <option name="project" value="LOCAL" />
      <updated>1752748108760</updated>
    </task>
    <task id="LOCAL-00123" summary="fix: 修复LRC 临分分出结果异常的问题">
      <option name="closed" value="true" />
      <created>1752748619094</created>
      <option name="number" value="00123" />
      <option name="presentableId" value="LOCAL-00123" />
      <option name="project" value="LOCAL" />
      <updated>1752748619094</updated>
    </task>
    <task id="LOCAL-00124" summary="fix: 修复RD假设值异常的问题">
      <option name="closed" value="true" />
      <created>1752749785221</created>
      <option name="number" value="00124" />
      <option name="presentableId" value="LOCAL-00124" />
      <option name="project" value="LOCAL" />
      <updated>1752749785221</updated>
    </task>
    <task id="LOCAL-00125" summary="fix: 优化合约分入数据获取">
      <option name="closed" value="true" />
      <created>1752808915433</created>
      <option name="number" value="00125" />
      <option name="presentableId" value="LOCAL-00125" />
      <option name="project" value="LOCAL" />
      <updated>1752808915433</updated>
    </task>
    <task id="LOCAL-00126" summary="perf: 优化页面查询">
      <option name="closed" value="true" />
      <created>1752831434293</created>
      <option name="number" value="00126" />
      <option name="presentableId" value="LOCAL-00126" />
      <option name="project" value="LOCAL" />
      <updated>1752831434293</updated>
    </task>
    <task id="LOCAL-00127" summary="feat: 合约分入添加险种">
      <option name="closed" value="true" />
      <created>1752835944967</created>
      <option name="number" value="00127" />
      <option name="presentableId" value="LOCAL-00127" />
      <option name="project" value="LOCAL" />
      <updated>1752835944968</updated>
    </task>
    <task id="LOCAL-00128" summary="fix: 修复内存占用异常的问题">
      <option name="closed" value="true" />
      <created>1752930598072</created>
      <option name="number" value="00128" />
      <option name="presentableId" value="LOCAL-00128" />
      <option name="project" value="LOCAL" />
      <updated>1752930598072</updated>
    </task>
    <task id="LOCAL-00129" summary="perf: 优化超赔分出的处理逻辑，防止数据量过大导致OOM的问题">
      <option name="closed" value="true" />
      <created>1753060943205</created>
      <option name="number" value="00129" />
      <option name="presentableId" value="LOCAL-00129" />
      <option name="project" value="LOCAL" />
      <updated>1753060943205</updated>
    </task>
    <task id="LOCAL-00130" summary="feat: 还原非跟单的获取费用下载逻辑">
      <option name="closed" value="true" />
      <created>1753063028250</created>
      <option name="number" value="00130" />
      <option name="presentableId" value="LOCAL-00130" />
      <option name="project" value="LOCAL" />
      <updated>1753063028250</updated>
    </task>
    <task id="LOCAL-00131" summary="fix: 修复非跟单获取费用计算异常以及临分分出计算异常的问题">
      <option name="closed" value="true" />
      <created>1753149524514</created>
      <option name="number" value="00131" />
      <option name="presentableId" value="LOCAL-00131" />
      <option name="project" value="LOCAL" />
      <updated>1753149524514</updated>
    </task>
    <task id="LOCAL-00132" summary="perf: 优化显示小数位数">
      <option name="closed" value="true" />
      <created>1753152614968</created>
      <option name="number" value="00132" />
      <option name="presentableId" value="LOCAL-00132" />
      <option name="project" value="LOCAL" />
      <updated>1753152614969</updated>
    </task>
    <task id="LOCAL-00133" summary="perf: 优化推送到上层的逻辑和分出LRC的校验失败提示信息">
      <option name="closed" value="true" />
      <created>1753169817630</created>
      <option name="number" value="00133" />
      <option name="presentableId" value="LOCAL-00133" />
      <option name="project" value="LOCAL" />
      <updated>1753169817630</updated>
    </task>
    <task id="LOCAL-00134" summary="fix: 修复推送计量缺少非跟单获取费用以及合约分入未来保费现金流统计错误的问题">
      <option name="closed" value="true" />
      <created>1753261133980</created>
      <option name="number" value="00134" />
      <option name="presentableId" value="LOCAL-00134" />
      <option name="project" value="LOCAL" />
      <updated>1753261133981</updated>
    </task>
    <task id="LOCAL-00135" summary="fix: 修复LRC直保合同组维度导出报错的问题的">
      <option name="closed" value="true" />
      <created>1753262938217</created>
      <option name="number" value="00135" />
      <option name="presentableId" value="LOCAL-00135" />
      <option name="project" value="LOCAL" />
      <updated>1753262938217</updated>
    </task>
    <task id="LOCAL-00136" summary="perf: 优化LRC直接业务的流程">
      <option name="closed" value="true" />
      <created>1753342597346</created>
      <option name="number" value="00136" />
      <option name="presentableId" value="LOCAL-00136" />
      <option name="project" value="LOCAL" />
      <updated>1753342597346</updated>
    </task>
    <task id="LOCAL-00137" summary="fix: 修复lic计算内存异常的问题">
      <option name="closed" value="true" />
      <created>1753351897463</created>
      <option name="number" value="00137" />
      <option name="presentableId" value="LOCAL-00137" />
      <option name="project" value="LOCAL" />
      <updated>1753351897464</updated>
    </task>
    <task id="LOCAL-00138" summary="fix: 修复lic计算内存异常的问题">
      <option name="closed" value="true" />
      <created>1753353972757</created>
      <option name="number" value="00138" />
      <option name="presentableId" value="LOCAL-00138" />
      <option name="project" value="LOCAL" />
      <updated>1753353972757</updated>
    </task>
    <task id="LOCAL-00139" summary="fix: 修复导出结果异常的问题">
      <option name="closed" value="true" />
      <created>1753412607756</created>
      <option name="number" value="00139" />
      <option name="presentableId" value="LOCAL-00139" />
      <option name="project" value="LOCAL" />
      <updated>1753412607756</updated>
    </task>
    <task id="LOCAL-00140" summary="fix: 修复导出结果异常的问题">
      <option name="closed" value="true" />
      <created>1753430128128</created>
      <option name="number" value="00140" />
      <option name="presentableId" value="LOCAL-00140" />
      <option name="project" value="LOCAL" />
      <updated>1753430128128</updated>
    </task>
    <task id="LOCAL-00141" summary="fix: 修复导出的问题">
      <option name="closed" value="true" />
      <created>1753436180196</created>
      <option name="number" value="00141" />
      <option name="presentableId" value="LOCAL-00141" />
      <option name="project" value="LOCAL" />
      <updated>1753436180196</updated>
    </task>
    <task id="LOCAL-00142" summary="fix: 修复导出异常的问题">
      <option name="closed" value="true" />
      <created>1753439106249</created>
      <option name="number" value="00142" />
      <option name="presentableId" value="LOCAL-00142" />
      <option name="project" value="LOCAL" />
      <updated>1753439106250</updated>
    </task>
    <task id="LOCAL-00143" summary="fix: 修复导出异常的问题">
      <option name="closed" value="true" />
      <created>1753441163501</created>
      <option name="number" value="00143" />
      <option name="presentableId" value="LOCAL-00143" />
      <option name="project" value="LOCAL" />
      <updated>1753441163501</updated>
    </task>
    <task id="LOCAL-00144" summary="fix: 修复模板异常的问题">
      <option name="closed" value="true" />
      <created>1753442102553</created>
      <option name="number" value="00144" />
      <option name="presentableId" value="LOCAL-00144" />
      <option name="project" value="LOCAL" />
      <updated>1753442102553</updated>
    </task>
    <task id="LOCAL-00145" summary="fix: 修复模板异常的问题">
      <option name="closed" value="true" />
      <created>1753442404982</created>
      <option name="number" value="00145" />
      <option name="presentableId" value="LOCAL-00145" />
      <option name="project" value="LOCAL" />
      <updated>1753442404982</updated>
    </task>
    <task id="LOCAL-00146" summary="refactor: 对于退保和注销的批单的进行方案调整">
      <option name="closed" value="true" />
      <created>1753692043369</created>
      <option name="number" value="00146" />
      <option name="presentableId" value="LOCAL-00146" />
      <option name="project" value="LOCAL" />
      <updated>1753692043369</updated>
    </task>
    <task id="LOCAL-00147" summary="refactor: 临分分出和合约分出 对于退保和注销的批单的进行方案调整">
      <option name="closed" value="true" />
      <created>1753755829294</created>
      <option name="number" value="00147" />
      <option name="presentableId" value="LOCAL-00147" />
      <option name="project" value="LOCAL" />
      <updated>1753755829294</updated>
    </task>
    <task id="LOCAL-00148" summary="fix: 修复下载报错的问题">
      <option name="closed" value="true" />
      <created>1753760393186</created>
      <option name="number" value="00148" />
      <option name="presentableId" value="LOCAL-00148" />
      <option name="project" value="LOCAL" />
      <updated>1753760393186</updated>
    </task>
    <task id="LOCAL-00149" summary="perf: 优化比例合约分出计算逻辑">
      <option name="closed" value="true" />
      <created>1753787121171</created>
      <option name="number" value="00149" />
      <option name="presentableId" value="LOCAL-00149" />
      <option name="project" value="LOCAL" />
      <updated>1753787121171</updated>
    </task>
    <option name="localTasksCounter" value="150" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="4f561793-c174-4f2d-9e10-06c372d89011" value="TOOL_WINDOW" />
        <entry key="d3ba3d4e-542a-4be0-814d-40bf2728d934" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="4f561793-c174-4f2d-9e10-06c372d89011">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$/ss-platform-base" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State>
              <option name="COLUMN_ID_WIDTH">
                <map>
                  <entry key="Table.Default.Author.ColumnIdWidth" value="93" />
                  <entry key="Table.Default.Date.ColumnIdWidth" value="93" />
                </map>
              </option>
            </State>
          </value>
        </entry>
        <entry key="d3ba3d4e-542a-4be0-814d-40bf2728d934">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="perf: 优化合约分入的计算逻辑" />
    <MESSAGE value="perf: 优化推送到计量的性能" />
    <MESSAGE value="fix: 修复LRC 临分分出结果异常的问题" />
    <MESSAGE value="fix: 修复RD假设值异常的问题" />
    <MESSAGE value="fix: 优化合约分入数据获取" />
    <MESSAGE value="perf: 优化页面查询" />
    <MESSAGE value="feat: 合约分入添加险种" />
    <MESSAGE value="fix: 修复内存占用异常的问题" />
    <MESSAGE value="perf: 优化超赔分出的处理逻辑，防止数据量过大导致OOM的问题" />
    <MESSAGE value="feat: 还原非跟单的获取费用下载逻辑" />
    <MESSAGE value="fix: 修复非跟单获取费用计算异常以及临分分出计算异常的问题" />
    <MESSAGE value="perf: 优化显示小数位数" />
    <MESSAGE value="perf: 优化推送到上层的逻辑和分出LRC的校验失败提示信息" />
    <MESSAGE value="fix: 修复推送计量缺少非跟单获取费用以及合约分入未来保费现金流统计错误的问题" />
    <MESSAGE value="fix: 修复LRC直保合同组维度导出报错的问题的" />
    <MESSAGE value="perf: 优化LRC直接业务的流程" />
    <MESSAGE value="fix: 修复lic计算内存异常的问题" />
    <MESSAGE value="fix: 修复导出结果异常的问题" />
    <MESSAGE value="fix: 修复导出的问题" />
    <MESSAGE value="fix: 修复导出异常的问题" />
    <MESSAGE value="fix: 修复模板异常的问题" />
    <MESSAGE value="refactor: 对于退保和注销的批单的进行方案调整" />
    <MESSAGE value="refactor: 临分分出和合约分出 对于退保和注销的批单的进行方案调整" />
    <MESSAGE value="fix: 修复下载报错的问题" />
    <MESSAGE value="perf: 优化比例合约分出计算逻辑" />
    <option name="LAST_COMMIT_MESSAGE" value="perf: 优化比例合约分出计算逻辑" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/ss-ifrs-actuarial/src/main/java/com/ss/ifrs/actuarial/service/impl/AtrConfQuotaDataServiceImpl.java</url>
          <line>1268</line>
          <option name="timeStamp" value="454" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/ss-ifrs-actuarial/src/main/java/com/ss/ifrs/actuarial/util/AtrConfQuotaImportUtil.java</url>
          <line>81</line>
          <option name="timeStamp" value="455" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/ss-ifrs-actuarial/src/main/java/com/ss/ifrs/actuarial/util/AtrConfQuotaImportUtil.java</url>
          <line>95</line>
          <option name="timeStamp" value="468" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/ss-ifrs-actuarial/src/main/java/com/ss/ifrs/actuarial/api/AtrConfQuotaClassApi.java</url>
          <line>54</line>
          <option name="timeStamp" value="776" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/ss-ifrs-actuarial/src/main/java/com/ss/ifrs/actuarial/service/impl/AtrBussLrcDdService.java</url>
          <line>974</line>
          <option name="timeStamp" value="805" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/ss-ifrs-actuarial/src/main/java/com/ss/ifrs/actuarial/service/impl/AtrBussLrcDdService.java</url>
          <line>981</line>
          <option name="timeStamp" value="806" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/ss-ifrs-actuarial/src/main/java/com/ss/ifrs/actuarial/service/impl/AtrBussLrcDdService.java</url>
          <line>994</line>
          <option name="timeStamp" value="807" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/ss-ifrs-actuarial/src/main/java/com/ss/ifrs/actuarial/service/impl/AtrBussLrcDdService.java</url>
          <line>1007</line>
          <option name="timeStamp" value="808" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/ss-ifrs-actuarial/src/main/java/com/ss/ifrs/actuarial/service/impl/AtrBussLrcDdService.java</url>
          <line>1017</line>
          <option name="timeStamp" value="809" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/ss-ifrs-actuarial/src/main/java/com/ss/ifrs/actuarial/service/impl/AtrBussLrcDdService.java</url>
          <line>1025</line>
          <option name="timeStamp" value="810" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>