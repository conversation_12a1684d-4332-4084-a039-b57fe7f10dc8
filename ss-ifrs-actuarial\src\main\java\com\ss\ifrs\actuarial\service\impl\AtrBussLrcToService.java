package com.ss.ifrs.actuarial.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 预期保费现金流 service （合约分出业务）
 * <AUTHOR>
 */
@Service
@Scope("prototype")
@Slf4j
public class AtrBussLrcToService extends AbstractAtrBussLrcService {

    @Resource
    private AtrBussLrcTotService atrBussLrcTotService;

    @Resource
    private AtrBussLrcToxService atrBussLrcToxService;

    public void entry(String actionNo, Long entityId, String yearMonth) {
        atrBussLrcTotService.entry(actionNo, entityId, yearMonth);
        atrBussLrcToxService.entry(actionNo, entityId, yearMonth);
    }

}
