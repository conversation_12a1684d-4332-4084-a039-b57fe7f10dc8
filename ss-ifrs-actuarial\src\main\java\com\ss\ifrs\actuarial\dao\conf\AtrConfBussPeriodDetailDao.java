package com.ss.ifrs.actuarial.dao.conf;

import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfBussPeriodDetail;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodDetailVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.ifrs.actuarial.pojo.domain.vo.buss.BussPeriodReqVo;
import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12). Create Date:
 * 2021-07-26 11:39:39<br/>
 * Description: 计量平台业务年月详情表 Dao类<br/>
 * Related Table Name: atr_conf_bussperiod_detail<br/>
 * <br/>
 * Remark: 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入tpcloud-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrConfBussPeriodDetailDao extends IDao<AtrConfBussPeriodDetail, Long> {

	List<AtrConfBussPeriodDetailVo> findPeriodDtlByEntityId(AtrConfBussPeriodVo atrConfBussPeriodVo);

	AtrConfBussPeriodDetailVo findByVo(AtrConfBussPeriodDetailVo atrConfBussPeriodDetailVo);
	
	int checkReadyState(AtrConfBussPeriodDetailVo confBussPeriodDetailVo);

	void updateByDetail(AtrConfBussPeriodDetailVo confBussPeriodDetailVo);

	void clearPeriodDetail(BussPeriodReqVo bussPeriodReqVo);
}