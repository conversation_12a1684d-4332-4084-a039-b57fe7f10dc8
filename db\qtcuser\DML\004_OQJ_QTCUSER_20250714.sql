
delete from qtc_conf_calc_sql where sql_code = 'MA_TR_Z99' ;

INSERT INTO qtcuser.qtc_conf_calc_sql ( sql_code, seq_no, expr, param, sql_select, sql_where) VALUES ( 'MA_TR_Z99', 5199, null, null, e'insert into qtc_buss_evl_acc_result(evl_main_id, year_month, var1, portfolio_no, icg_no, str1, str2, str3, str4, str5, str6, str7, str8, str11, str12, str13,str14,
    num1, num2, num3,num4, num5,num6,num7,num8,num9,num10, num11, num12, num13,num14, num15,num16,num17,num18,num19,num20,num21, num22, num23,num24, num25,num26,num27,num28,num29)
select t2.evl_main_id, t2.year_month, t2.var1, t2.portfolio_no, t2.icg_no, str1, str2, str3, str4, str5, str6, str7, str8, str11, str12, str13,str14,
    sum(num1), sum(num2), sum(num3), sum(num4), sum(num5), sum(num6),sum(num7),sum(num8),sum(num9),sum(num10), sum(num11), sum(num12), sum(num13),sum(num14), sum(num15),sum(num16),sum(num17),sum(num18),sum(num19),sum(num20),sum(num21), sum(num22), sum(num23),sum(num24), sum(num25),sum(num26),sum(num27),sum(num28),sum(num29)
from qtc_buss_calc_alloc_cfg t1 ,  qtc_buss_evl_alloc_result t2
where t1.evl_main_id = #evl_main_id#  and t2.year_month = t1.year_month  and t2.evl_main_id = t1.evl_main_id
group by t2.evl_main_id, t2.year_month, t2.var1, t2.portfolio_no, t2.icg_no, str1, str2, str3, str4, str5, str6, str7, str8, str11, str12, str13,str14', null);


delete from qtc_conf_node_sql_ref where sql_code = 'MA_TR_Z99' ;

INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 3, 'MA_TR_Z99', 'calc');
INSERT INTO qtcuser.qtc_conf_node_sql_ref (env_no, model_def_id, sql_code, node_code) VALUES ('Deloitte-HGIC', 4, 'MA_TR_Z99', 'calc');

commit;