/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2023-02-03 18:32:02
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrconf.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2023-02-03 18:32:02<br/>
 * Description: Mortgage借出贷款假设发展期配置<br/>
 * Table Name: ATR_CONF_MORTG_QUOTA_PERIODHIS<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "Mortgage借出贷款假设发展期配置")
public class AtrConfMortgQuotaPeriodHisVo implements Serializable {
    /**
     * Database column: ATR_CONF_MORTG_QUOTA_PERIODHIS.MORTG_QUOTA_PERIOD_HIS_ID
     * Database remarks: mortg Quota Period His Id|主键
     */
    @ApiModelProperty(value = "mortg Quota Period His Id|主键", required = true)
    private Long mortgQuotaPeriodHisId;

    /**
     * Database column: ATR_CONF_MORTG_QUOTA_PERIODHIS.MORTG_QUOTA_PERIOD_ID
     * Database remarks: mortg Quota Period Id|主键
     */
    @ApiModelProperty(value = "mortg Quota Period Id|主键", required = true)
    private Long mortgQuotaPeriodId;

    /**
     * Database column: ATR_CONF_MORTG_QUOTA_PERIODHIS.MORTG_QUOTA_ID
     * Database remarks: mortg Quota Id|Mortgage假设配置主表id
     */
    @ApiModelProperty(value = "mortg Quota Id|Mortgage假设配置主表id", required = true)
    private Long mortgQuotaId;

    /**
     * Database column: ATR_CONF_MORTG_QUOTA_PERIODHIS.QUOTA_PERIOD
     * Database remarks: quota Period|发展期期次
     */
    @ApiModelProperty(value = "quota Period|发展期期次", required = false)
    private Long quotaPeriod;

    /**
     * Database column: ATR_CONF_MORTG_QUOTA_PERIODHIS.QUOTA_VALUE
     * Database remarks: quota Value|假设值
     */
    @ApiModelProperty(value = "quota Value|假设值", required = false)
    private BigDecimal quotaValue;

    /**
     * Database column: ATR_CONF_MORTG_QUOTA_PERIODHIS.SERIAL_NO
     * Database remarks: null
     */
    private Long serialNo;

    private static final long serialVersionUID = 1L;

    public Long getMortgQuotaPeriodHisId() {
        return mortgQuotaPeriodHisId;
    }

    public void setMortgQuotaPeriodHisId(Long mortgQuotaPeriodHisId) {
        this.mortgQuotaPeriodHisId = mortgQuotaPeriodHisId;
    }

    public Long getMortgQuotaPeriodId() {
        return mortgQuotaPeriodId;
    }

    public void setMortgQuotaPeriodId(Long mortgQuotaPeriodId) {
        this.mortgQuotaPeriodId = mortgQuotaPeriodId;
    }

    public Long getMortgQuotaId() {
        return mortgQuotaId;
    }

    public void setMortgQuotaId(Long mortgQuotaId) {
        this.mortgQuotaId = mortgQuotaId;
    }

    public Long getQuotaPeriod() {
        return quotaPeriod;
    }

    public void setQuotaPeriod(Long quotaPeriod) {
        this.quotaPeriod = quotaPeriod;
    }

    public BigDecimal getQuotaValue() {
        return quotaValue;
    }

    public void setQuotaValue(BigDecimal quotaValue) {
        this.quotaValue = quotaValue;
    }

    public Long getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(Long serialNo) {
        this.serialNo = serialNo;
    }
}