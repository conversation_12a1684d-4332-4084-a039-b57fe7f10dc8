package com.ss.ifrs.actuarial.service.impl;

import com.ss.ifrs.actuarial.dao.AtrLogActionErrorDao;
import com.ss.ifrs.actuarial.pojo.other.po.AtrLogActionError;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class AtrLogActionErrorService {

    @Resource
    private AtrLogActionErrorDao atrLogActionErrorDao;

    /**
     * @param mark 当相同的 actionNo 时， 可用这个字段区分
     * @param e 如果传入异常， 将自动将根异常堆栈信息记录到错误日志中
     */
    public void addLog(String mark, String actionNo, Exception e) {
        AtrLogActionError po = new AtrLogActionError();
        po.setMark(mark);
        po.setActionNo(actionNo);
        if (e != null) {
            Throwable rootCause = ExceptionUtils.getRootCause(e);
            po.setErrorMsg(ExceptionUtils.getStackTrace(rootCause));
        }
        atrLogActionErrorDao.save(po);
    }

}
