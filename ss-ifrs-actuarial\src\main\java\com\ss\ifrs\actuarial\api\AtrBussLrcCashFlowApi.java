package com.ss.ifrs.actuarial.api;

import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLrcActionVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConvertJsonVo;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo;
import com.ss.ifrs.actuarial.service.AtrBussLrcCashFlowService;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.platform.core.annotation.LogScheduleTask;
import com.ss.platform.core.annotation.PermissionRequest;
import com.ss.platform.core.api.BaseApi;
import com.ss.platform.core.constant.ResCodeConstant;
import com.ss.platform.pojo.base.po.BaseResponse;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @ClassName: AtrLicCsmCalcApi
 * @Description: LIC和CSM计算对外Api接口
 * @Author: yinxh.
 * @CreateDate: 2021/3/8 17:46
 * @Version: 1.0
 */
@RestController
@RequestMapping( "/lrc_action")
public class AtrBussLrcCashFlowApi extends BaseApi{

	// 日志管理
	final Logger LOG  = LoggerFactory.getLogger(getClass());

	@Autowired
	private AtrBussLrcCashFlowService atrBussLrcCashFlowService;

	@ApiOperation(value = "查找对象")
	@RequestMapping(value = "/enquiry", method = { RequestMethod.POST })
	public BaseResponse<Object> search(HttpServletRequest request, @RequestBody AtrBussLrcActionVo atrBussLrcActionVo,
									   int _pageNo, int _pageSize) {
		Pageable pageParam = new Pageable(_pageNo, _pageSize );
		Page<AtrBussLrcActionVo> result = atrBussLrcCashFlowService.findForDataTables(atrBussLrcActionVo, pageParam);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("atrBussLicCashFlowVoList", result);
		return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
	}

	@ApiOperation(value = "提取")
	@LogScheduleTask(bizCode = "BUSS_LRC_BECF_CALC", argsValue = { "#atrBussLrcActionVo.entityId",
			"#atrBussLrcActionVo.yearMonth",
			"#atrBussLrcActionVo.taskMode",
			"#atrBussLrcActionVo.taskCode",
			"#atrBussLrcActionVo.retryOrder"})
	@RequestMapping(value = "/add", method = RequestMethod.POST)
	public BaseResponse<Object> add(HttpServletRequest request, @RequestBody @Validated
									AtrBussLrcActionVo atrBussLrcActionVo) {
		atrBussLrcActionVo.setCreatorId(this.loginUserId(request));
		atrBussLrcActionVo.setCreateTime(new Date());
		try {
			Long userId = this.loginUserId(request);
			atrBussLrcCashFlowService.save(atrBussLrcActionVo, userId);
			return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
		} catch (Exception e) {
			LOG.error(e.getLocalizedMessage(), e);
			return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, e.getMessage());
		}
	}

	@ApiOperation(value = "根据id查询Lrc计算信息")
	@RequestMapping(value = "/find_by_id/{id}", method = RequestMethod.GET)
	@PermissionRequest(required = false)
	public BaseResponse<AtrBussLrcActionVo> findById(@PathVariable("id") Long id) {
		AtrBussLrcActionVo atrBussLrcActionVo = atrBussLrcCashFlowService.findById(id);
		return new BaseResponse<AtrBussLrcActionVo>(ResCodeConstant.ResCode.SUCCESS, atrBussLrcActionVo);
	}

	@ApiOperation(value = "删除Lrc计算信息")
	@RequestMapping(value = "/delete_by_id/{id}", method = RequestMethod.GET)
	@PermissionRequest(required = false)
	public BaseResponse<Object> delete(HttpServletRequest request, @PathVariable("id") Long id) {
		try {
			Long userId = this.loginUserId(request);
			atrBussLrcCashFlowService.delete(id, userId);
			return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
		} catch (Exception e) {
			logger.error(e.getLocalizedMessage(), e);
			return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, null);
		}
	}

	@ApiOperation(value = "确认")
	@RequestMapping(value = "/confirm", method = RequestMethod.POST)
	@LogScheduleTask(bizCode = "BUSS_LRC_BECF_CFM", argsValue = { "#atrBussLrcActionVo.entityId",
			"#atrBussLrcActionVo.yearMonth",
			"#atrBussLrcActionVo.taskMode",
			"#atrBussLrcActionVo.taskCode",
			"#atrBussLrcActionVo.retryOrder"})
	@PermissionRequest(required = false)
	public BaseResponse<Object> confirm(HttpServletRequest request, @RequestBody @Validated
											AtrBussLrcActionVo atrBussLrcActionVo) {
		atrBussLrcActionVo.setUpdatorId(this.loginUserId(request));
		atrBussLrcActionVo.setUpdateTime(new Date());
		try {
			Long userId = this.loginUserId(request);
			Boolean confirmFlag = atrBussLrcCashFlowService.confirm(atrBussLrcActionVo, userId);
			return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, confirmFlag);
		} catch (Exception e) {
			LOG.error(e.getLocalizedMessage(), e);
			return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, e.getMessage());
		}
	}

	@ApiOperation(value = "查询评估结果结果合同组明细数据接口")
	@RequestMapping(value = "/find_lrc_detail", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<List<Map<String, Object>>> findPeriodData(@RequestBody AtrDapDrawVo atrDapDrawVo,
	 		int _pageNo, int _pageSize){
		Pageable pageable = new Pageable(_pageNo, _pageSize );
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			Page<List<Map<String, Object>>> aa = atrBussLrcCashFlowService.findPeriodData(atrDapDrawVo, pageable);
			map.put("lrcCashFlowVoList", aa);
		} catch (Exception ex) {
			logger.error(ex.getLocalizedMessage(), ex);
		}
		return new BaseResponse(ResCodeConstant.ResCode.SUCCESS, map);
	}

	@ApiOperation(value = "查询评估结果结果合同组明细数据接口")
	@RequestMapping(value = "/find_periodHeader", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<Map<String, Object>> findPeriodHeaderDataById(@RequestBody AtrDapDrawVo atrDapDrawVo) throws IllegalAccessException {
		LinkedHashMap<String,Object> resultMap = new LinkedHashMap<>();
		try {
			List<Integer> devList = atrBussLrcCashFlowService.findPeriodHeaderData(atrDapDrawVo);
			resultMap.put("devNo", devList);
		} catch (Exception ex) {
			logger.error(ex.getLocalizedMessage(), ex);
		}
		return new BaseResponse(ResCodeConstant.ResCode.SUCCESS, resultMap);
	}


	@ApiOperation("查询同一批次下不同合同组合数据")
	@RequestMapping(value = "/find_portfolio_data", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<Object> findPortfolioData(@RequestBody AtrDapDrawVo atrDapDrawVo, int _pageSize, int _pageNo) {
		Pageable pageParam = new Pageable(_pageNo, _pageSize);
		Page<AtrDapDrawVo> page = atrBussLrcCashFlowService.findPortfolioData(atrDapDrawVo, pageParam);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("resultList",page);
		return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, map);
	}

	@ApiOperation(value = "下载")
	@RequestMapping(value = "/download", method = RequestMethod.POST)
	public void download(@RequestBody AtrBussLrcActionVo atrBussLrcActionVo, HttpServletRequest request, HttpServletResponse response) {
		Long userId = this.loginUserId(request);
		atrBussLrcActionVo.setCreatorId(userId);
		String language = request.getHeader("ss-Language");
		atrBussLrcActionVo.setLanguage(language);
		try {
			atrBussLrcCashFlowService.download(atrBussLrcActionVo, request, response);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@ApiOperation(value = "下载")
	@RequestMapping(value = "/download1", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public void download1(@RequestBody AtrBussLrcActionVo atrBussLrcActionVo, HttpServletRequest request, HttpServletResponse response) {
		Long userId = this.loginUserId(request);
		atrBussLrcActionVo.setCreatorId(userId);
		String language = request.getHeader("ss-Language");
		atrBussLrcActionVo.setLanguage(language);
		try {
			atrBussLrcCashFlowService.download1(atrBussLrcActionVo, request, response);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@ApiOperation(value = "保费预期现金流计算")
	@PermissionRequest(required = false)
	@LogScheduleTask(bizCode = "BUSS_LRC_BECF_CALC", argsValue = { "#atrBussLrcActionVo.entityId",
			"#atrBussLrcActionVo.yearMonth",
			"#atrBussLrcActionVo.taskMode",
			"#atrBussLrcActionVo.taskCode",
			"#atrBussLrcActionVo.retryOrder"})
	@RequestMapping(value = "/calculate_all", method = RequestMethod.POST)
	public BaseResponse<Object> calculateAll(HttpServletRequest request, @RequestBody @Validated
														 AtrBussLrcActionVo atrBussLrcActionVo) {
		atrBussLrcActionVo.setCreatorId(this.loginUserId(request));
		atrBussLrcActionVo.setCreateTime(new Date());
		try {
			Long userId = this.loginUserId(request);
			atrBussLrcCashFlowService.calculateAll(atrBussLrcActionVo, userId);
			return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, null);
		} catch (Exception e) {
			LOG.error(e.getLocalizedMessage(), e);
			return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, e.getMessage());
		}
	}


	@ApiOperation(value = "保费预期现金流计算后确认")
	@LogScheduleTask(bizCode = "BUSS_LRC_BECF_CFM", argsValue = { "#atrBussLrcActionVo.entityId",
			"#atrBussLrcActionVo.yearMonth",
			"#atrBussLrcActionVo.taskMode",
			"#atrBussLrcActionVo.taskCode",
			"#atrBussLrcActionVo.retryOrder"})
	@RequestMapping(value = "/confirmAll", method = RequestMethod.POST)
	@PermissionRequest(required = false)
	public BaseResponse<Object> confirmPeriod(HttpServletRequest request, @RequestBody @Validated AtrBussLrcActionVo atrBussLrcActionVo) {
		atrBussLrcActionVo.setUpdatorId(this.loginUserId(request));
		atrBussLrcActionVo.setUpdateTime(new Date());
		try {
			Long userId = this.loginUserId(request);
			String result = atrBussLrcCashFlowService.confirmLrcCfVersion(atrBussLrcActionVo, userId);
			if (ObjectUtils.isEmpty(result)) {
				return new BaseResponse<Object>(ResCodeConstant.ResCode.SUCCESS, null);
			} else {
				return new BaseResponse<Object>(ResCodeConstant.ResCode.NULL_RESULT, result);
			}
		} catch (Exception e) {
			LOG.error(e.getLocalizedMessage(), e);
			return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, e.getMessage());
		}
	}

	@ApiOperation(value = "计算过渡期已赚保费")
	@PermissionRequest(required = false)
	@LogScheduleTask(bizCode = "BUSS_TRANSITION_ED_PREMIUM", argsValue = { "#convertJsonVo.entityId",
			"#convertJsonVo.yearMonth",
			"#convertJsonVo.taskMode",
			"#convertJsonVo.taskCode",
			"#convertJsonVo.retryOrder"})
	@RequestMapping(value = "/calculateTransitionEarnedPremium", method = RequestMethod.POST)
	public BaseResponse<Object> calculateTransitionEarnedPremium(HttpServletRequest request,@RequestBody AtrConvertJsonVo convertJsonVo) {
		try {
			Long userId = this.loginUserId(request);
			// 调用已赚保费计算方法
			atrBussLrcCashFlowService.calculateTransitionEarnedPremium(convertJsonVo.getEntityId(), userId);

			return new BaseResponse<>(ResCodeConstant.ResCode.SUCCESS, null);
		} catch (Exception e) {
			LOG.error(e.getLocalizedMessage(), e);
			return new BaseResponse<>(ResCodeConstant.ResCode.DB_ERROR, e.getMessage());
		}
	}
}
