/**
 * 
 * This file was generated by SS MyBatis Generator(v1.2.12).
 * Date: 2023-12-12 11:40:09
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * This code was generated by SS MyBatis Generator(v1.2.12).
 * Create Date: 2023-12-12 11:40:09<br/>
 * Description: ibnr计算-action下的基于出险时间节点的计算结果<br/>
 * Table Name: atr_buss_ibnrcalc_acc_result<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "ibnr计算-action下的基于出险时间节点的计算结果")
public class AtrBussIbnrcalcAccResult implements Serializable {
    /**
     * Database column: atr_buss_ibnrcalc_acc_result.id
     * Database remarks: ID
     */
    @ApiModelProperty(value = "ID", required = true)
    private Long id;

    /**
     * Database column: atr_buss_ibnrcalc_acc_result.action_no
     * Database remarks: action编号
     */
    @ApiModelProperty(value = "action编号", required = true)
    private String actionNo;

    /**
     * Database column: atr_buss_ibnrcalc_acc_result.icp_id
     * Database remarks: 合同组合层ID
     */
    @ApiModelProperty(value = "合同组合层ID", required = true)
    private Long icpId;

    /**
     * Database column: atr_buss_ibnrcalc_acc_result.accident_node
     * Database remarks: 出险时间节点（年或月）
     */
    @ApiModelProperty(value = "出险时间节点（年或月）", required = true)
    private String accidentNode;

    /**
     * Database column: atr_buss_ibnrcalc_acc_result.ed_premium_ori
     * Database remarks: 已赚保费（自动计算的）
     */
    @ApiModelProperty(value = "已赚保费（自动计算的）", required = false)
    private BigDecimal edPremiumOri;

    /**
     * Database column: atr_buss_ibnrcalc_acc_result.ed_premium
     * Database remarks: 已赚保费（用户调整的）
     */
    @ApiModelProperty(value = "已赚保费（用户调整的）", required = false)
    private BigDecimal edPremium;

    /**
     * Database column: atr_buss_ibnrcalc_acc_result.setteld_amount
     * Database remarks: 已决赔款 (根据通货膨胀系数调整后)
     */
    @ApiModelProperty(value = "已决赔款 (根据通货膨胀系数调整后)", required = false)
    private BigDecimal setteldAmount;

    /**
     * Database column: atr_buss_ibnrcalc_acc_result.os_amount
     * Database remarks: OS
     */
    @ApiModelProperty(value = "OS", required = false)
    private BigDecimal osAmount;

    /**
     * Database column: atr_buss_ibnrcalc_acc_result.reported_amount
     * Database remarks: 已报告赔款之和|已决赔款+OS
     */
    @ApiModelProperty(value = "已报告赔款之和|已决赔款+OS", required = false)
    private BigDecimal reportedAmount;

    /**
     * Database column: atr_buss_ibnrcalc_acc_result.reported_loss_ratio
     * Database remarks: Reported Loss Ratio
     */
    @ApiModelProperty(value = "Reported Loss Ratio", required = false)
    private BigDecimal reportedLossRatio;

    /**
     * Database column: atr_buss_ibnrcalc_acc_result.expected_reported_ratio
     * Database remarks: Expected Reported
     */
    @ApiModelProperty(value = "Expected Reported", required = false)
    private BigDecimal expectedReportedRatio;

    /**
     * Database column: atr_buss_ibnrcalc_acc_result.lr_expected_loss_ratio
     * Database remarks: LR 算法的 Expected Loss Ratio
     */
    @ApiModelProperty(value = "LR 算法的 Expected Loss Ratio", required = false)
    private BigDecimal lrExpectedLossRatio;

    /**
     * Database column: atr_buss_ibnrcalc_acc_result.lr_ultimate_loss
     * Database remarks: LR 算法的 Ultimate Loss
     */
    @ApiModelProperty(value = "LR 算法的 Ultimate Loss", required = false)
    private BigDecimal lrUltimateLoss;

    /**
     * Database column: atr_buss_ibnrcalc_acc_result.lr_ibnr
     * Database remarks: LR 算法的 IBNR
     */
    @ApiModelProperty(value = "LR 算法的 IBNR", required = false)
    private BigDecimal lrIbnr;

    /**
     * Database column: atr_buss_ibnrcalc_acc_result.cl_proj_reported_ultimate
     * Database remarks: CL 算法的 Proj. Reported Ultimate
     */
    @ApiModelProperty(value = "CL 算法的 Proj. Reported Ultimate", required = false)
    private BigDecimal clProjReportedUltimate;

    /**
     * Database column: atr_buss_ibnrcalc_acc_result.cl_ibnr
     * Database remarks: CL 算法的 IBNR
     */
    @ApiModelProperty(value = "CL 算法的 IBNR", required = false)
    private BigDecimal clIbnr;

    /**
     * Database column: atr_buss_ibnrcalc_acc_result.bf_expected_loss_ratio
     * Database remarks: BF 算法的 Expected Loss Ratio
     */
    @ApiModelProperty(value = "BF 算法的 Expected Loss Ratio", required = false)
    private BigDecimal bfExpectedLossRatio;

    /**
     * Database column: atr_buss_ibnrcalc_acc_result.bf_proj_reported_ultimate
     * Database remarks: BF 算法的 Proj. Reported Ultimate
     */
    @ApiModelProperty(value = "BF 算法的 Proj. Reported Ultimate", required = false)
    private BigDecimal bfProjReportedUltimate;

    /**
     * Database column: atr_buss_ibnrcalc_acc_result.bf_ibnr
     * Database remarks: BF 算法的 IBNR
     */
    @ApiModelProperty(value = "BF 算法的 IBNR", required = false)
    private BigDecimal bfIbnr;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public Long getIcpId() {
        return icpId;
    }

    public void setIcpId(Long icpId) {
        this.icpId = icpId;
    }

    public String getAccidentNode() {
        return accidentNode;
    }

    public void setAccidentNode(String accidentNode) {
        this.accidentNode = accidentNode;
    }

    public BigDecimal getEdPremiumOri() {
        return edPremiumOri;
    }

    public void setEdPremiumOri(BigDecimal edPremiumOri) {
        this.edPremiumOri = edPremiumOri;
    }

    public BigDecimal getEdPremium() {
        return edPremium;
    }

    public void setEdPremium(BigDecimal edPremium) {
        this.edPremium = edPremium;
    }

    public BigDecimal getSetteldAmount() {
        return setteldAmount;
    }

    public void setSetteldAmount(BigDecimal setteldAmount) {
        this.setteldAmount = setteldAmount;
    }

    public BigDecimal getOsAmount() {
        return osAmount;
    }

    public void setOsAmount(BigDecimal osAmount) {
        this.osAmount = osAmount;
    }

    public BigDecimal getReportedAmount() {
        return reportedAmount;
    }

    public void setReportedAmount(BigDecimal reportedAmount) {
        this.reportedAmount = reportedAmount;
    }

    public BigDecimal getReportedLossRatio() {
        return reportedLossRatio;
    }

    public void setReportedLossRatio(BigDecimal reportedLossRatio) {
        this.reportedLossRatio = reportedLossRatio;
    }

    public BigDecimal getExpectedReportedRatio() {
        return expectedReportedRatio;
    }

    public void setExpectedReportedRatio(BigDecimal expectedReportedRatio) {
        this.expectedReportedRatio = expectedReportedRatio;
    }

    public BigDecimal getLrExpectedLossRatio() {
        return lrExpectedLossRatio;
    }

    public void setLrExpectedLossRatio(BigDecimal lrExpectedLossRatio) {
        this.lrExpectedLossRatio = lrExpectedLossRatio;
    }

    public BigDecimal getLrUltimateLoss() {
        return lrUltimateLoss;
    }

    public void setLrUltimateLoss(BigDecimal lrUltimateLoss) {
        this.lrUltimateLoss = lrUltimateLoss;
    }

    public BigDecimal getLrIbnr() {
        return lrIbnr;
    }

    public void setLrIbnr(BigDecimal lrIbnr) {
        this.lrIbnr = lrIbnr;
    }

    public BigDecimal getClProjReportedUltimate() {
        return clProjReportedUltimate;
    }

    public void setClProjReportedUltimate(BigDecimal clProjReportedUltimate) {
        this.clProjReportedUltimate = clProjReportedUltimate;
    }

    public BigDecimal getClIbnr() {
        return clIbnr;
    }

    public void setClIbnr(BigDecimal clIbnr) {
        this.clIbnr = clIbnr;
    }

    public BigDecimal getBfExpectedLossRatio() {
        return bfExpectedLossRatio;
    }

    public void setBfExpectedLossRatio(BigDecimal bfExpectedLossRatio) {
        this.bfExpectedLossRatio = bfExpectedLossRatio;
    }

    public BigDecimal getBfProjReportedUltimate() {
        return bfProjReportedUltimate;
    }

    public void setBfProjReportedUltimate(BigDecimal bfProjReportedUltimate) {
        this.bfProjReportedUltimate = bfProjReportedUltimate;
    }

    public BigDecimal getBfIbnr() {
        return bfIbnr;
    }

    public void setBfIbnr(BigDecimal bfIbnr) {
        this.bfIbnr = bfIbnr;
    }
}