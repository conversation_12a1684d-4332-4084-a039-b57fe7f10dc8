/**
 * 
 * This file was generated by Taiping MyBatis Generator(v1.2.12).
 * Date: 2024-08-14 14:48:14
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, CHINA TAIPING INSURANCE GROUP LTD. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.pojo.atrbuss.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * This code was generated by Taiping MyBatis Generator(v1.2.12).
 * Create Date: 2024-08-14 14:48:14<br/>
 * Description: IBNR-二次分摊执行记录<br/>
 * Table Name: ATR_SEC_ALLOC_ACTION<br/>
 * <br/>
 * Remark: 如需使用乐观锁，需在PO类中添加int类型的version字段，并重新生成本配置文件！
 * <br/>
 */
@ApiModel(value = "IBNR-二次分摊执行记录")
public class AtrSecAllocAction implements Serializable {
    /**
     * Database column: ATR_SEC_ALLOC_ACTION.ID
     * Database remarks: ID|主键
     */
    @ApiModelProperty(value = "ID|主键", required = true)
    private Long id;

    /**
     * Database column: ATR_SEC_ALLOC_ACTION.ACTION_NO
     * Database remarks: 执行编号
     */
    @ApiModelProperty(value = "执行编号", required = false)
    private String actionNo;

    /**
     * Database column: ATR_SEC_ALLOC_ACTION.ENTITY_ID
     * Database remarks: 业务单位
     */
    @ApiModelProperty(value = "业务单位", required = false)
    private Long entityId;

    /**
     * Database column: ATR_SEC_ALLOC_ACTION.YEAR_MONTH
     * Database remarks: 业务期间
     */
    @ApiModelProperty(value = "业务期间", required = false)
    private String yearMonth;

    /**
     * Database column: ATR_SEC_ALLOC_ACTION.BUSINESS_SOURCE_CODE
     * Database remarks: 业务类型
     */
    @ApiModelProperty(value = "业务类型", required = false)
    private String businessSourceCode;

    /**
     * Database column: ATR_SEC_ALLOC_ACTION.STATUS
     * Database remarks: 执行状态 0: 待执行  1:执行中 2:执行成功 3:执行异常
     */
    @ApiModelProperty(value = "执行状态 0: 待执行  1:执行中 2:执行成功 3:执行异常", required = false)
    private String status;

    /**
     * Database column: ATR_SEC_ALLOC_ACTION.CONFIRM_IS
     * Database remarks: 是否确认 1:确认 0: 待确认
     */
    @ApiModelProperty(value = "是否确认 1:确认 0: 待确认", required = false)
    private String confirmIs;

    /**
     * Database column: ATR_SEC_ALLOC_ACTION.CONFIRM_USER
     * Database remarks: 确认人
     */
    @ApiModelProperty(value = "确认人", required = false)
    private Long confirmUser;

    /**
     * Database column: ATR_SEC_ALLOC_ACTION.CONFIRM_TIME
     * Database remarks: 确认时间
     */
    @ApiModelProperty(value = "确认时间", required = false)
    private Date confirmTime;

    /**
     * Database column: ATR_SEC_ALLOC_ACTION.CREATE_ID
     * Database remarks: 创建人
     */
    @ApiModelProperty(value = "创建人", required = false)
    private Long createId;

    /**
     * Database column: ATR_SEC_ALLOC_ACTION.CREATE_TIME
     * Database remarks: 创建时间
     */
    @ApiModelProperty(value = "创建时间", required = false)
    private Date createTime;

    private String currencyCode;

    private String errorMessage;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActionNo() {
        return actionNo;
    }

    public void setActionNo(String actionNo) {
        this.actionNo = actionNo;
    }

    public Long getEntityId() {
        return entityId;
    }

    public void setEntityId(Long entityId) {
        this.entityId = entityId;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getBusinessSourceCode() {
        return businessSourceCode;
    }

    public void setBusinessSourceCode(String businessSourceCode) {
        this.businessSourceCode = businessSourceCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getConfirmIs() {
        return confirmIs;
    }

    public void setConfirmIs(String confirmIs) {
        this.confirmIs = confirmIs;
    }

    public Long getConfirmUser() {
        return confirmUser;
    }

    public void setConfirmUser(Long confirmUser) {
        this.confirmUser = confirmUser;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public Long getCreateId() {
        return createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }


    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}