package com.ss.ifrs.actuarial.feign;

import com.ss.platform.core.conf.FeignAuthConfig;
import com.ss.platform.pojo.base.po.BaseResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 精算同步计量接口数据处理
 */
@FeignClient(name = "SS-IFRS-QUANTIFICATION", configuration = {FeignAuthConfig.class})
public interface QtcDapAtrFeignClient {

    @RequestMapping(value = "/dap_atr/clearDapData/{table}/{yearMonth}")
    BaseResponse<?> clearDapData(@PathVariable String table, @PathVariable String yearMonth);

}
