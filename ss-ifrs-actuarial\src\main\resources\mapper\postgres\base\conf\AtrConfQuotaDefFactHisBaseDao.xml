<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by SS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2022-09-16 14:50:26 -->
<!-- Copyright (c) 2017-2027, CHINA SS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.conf.AtrConfQuotaDefFactHisDao">
  <!-- 本文件由SS MyBatis Generator(v1.2.12)工具自动生成，请勿手工修改！ -->
  <!-- 请勿修改基础配置(**IDao.xml)中的内容，如需添加自定义内容，请在自定义配置(**CustDao.xml)中添加！ -->
  <!-- 如需使用乐观锁，PO类中需在添加int类型的version属性，并重新生成本配置文件！ -->
  <!-- 默认开启Mybatis缓存配置,使用FIFO（先进先出:按对象进入缓存的顺序来移除它们）算法来收回 -->
  <cache eviction="FIFO" flushInterval="30000" size="32" readOnly="true"/>
  <!-- 通用查询结果对象-->
  <resultMap id="BaseResultMap" type="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDefFactHis">
    <id column="QUOTA_DEF_FACT_HIS_ID" property="quotaDefFactHisId" jdbcType="DECIMAL" />
    <result column="QUOTA_DEF_FACT_ID" property="quotaDefFactId" jdbcType="DECIMAL" />
    <result column="QUOTA_DEF_ID" property="quotaDefId" jdbcType="DECIMAL" />
    <result column="QUOTA_CODE" property="quotaCode" jdbcType="VARCHAR" />
    <result column="business_source_code" property="businessSourceCode" jdbcType="DECIMAL" />
    <result column="CREATOR_ID" property="creatorId" jdbcType="DECIMAL" />
    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    <result column="UPDATOR_ID" property="updatorId" jdbcType="DECIMAL" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="serial_no" property="serialNo" jdbcType="NUMERIC" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Base_Column_List">
    QUOTA_DEF_FACT_HIS_ID, QUOTA_DEF_FACT_ID, QUOTA_DEF_ID, QUOTA_CODE, business_source_code, 
    CREATOR_ID, CREATE_TIME, UPDATOR_ID, UPDATE_TIME,serial_no
  </sql>
  <sql id="Blob_Column_List" />
  <!-- 按对象查询记录的WHERE部分 -->
  <sql id="Base_Select_By_Entity_Where">
    <where>
      <if test="quotaDefFactHisId != null ">
          and QUOTA_DEF_FACT_HIS_ID = #{quotaDefFactHisId,jdbcType=DECIMAL}
      </if>
      <if test="quotaDefFactId != null ">
          and QUOTA_DEF_FACT_ID = #{quotaDefFactId,jdbcType=DECIMAL}
      </if>
      <if test="quotaDefId != null ">
          and QUOTA_DEF_ID = #{quotaDefId,jdbcType=DECIMAL}
      </if>
      <if test="quotaCode != null and quotaCode != ''">
          and QUOTA_CODE = #{quotaCode,jdbcType=VARCHAR}
      </if>
      <if test="businessSourceCode != null ">
          and business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="creatorId != null ">
          and CREATOR_ID = #{creatorId,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null ">
          and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and UPDATOR_ID = #{updatorId,jdbcType=DECIMAL}
      </if>
      <if test="updateTime != null ">
          and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
  </sql>
  <!-- 使用PO对象作为动态条件更新记录的WHERE部分 -->
  <sql id="Base_Update_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="condition.quotaDefFactHisId != null ">
          and QUOTA_DEF_FACT_HIS_ID = #{condition.quotaDefFactHisId,jdbcType=DECIMAL}
      </if>
      <if test="condition.quotaDefFactId != null ">
          and QUOTA_DEF_FACT_ID = #{condition.quotaDefFactId,jdbcType=DECIMAL}
      </if>
      <if test="condition.quotaDefId != null ">
          and QUOTA_DEF_ID = #{condition.quotaDefId,jdbcType=DECIMAL}
      </if>
      <if test="condition.quotaCode != null and condition.quotaCode != ''">
          and QUOTA_CODE = #{condition.quotaCode,jdbcType=VARCHAR}
      </if>
      <if test="condition.businessSourceCode != null ">
          and business_source_code = #{condition.businessSourceCode,jdbcType=DECIMAL}
      </if>
      <if test="condition.creatorId != null ">
          and CREATOR_ID = #{condition.creatorId,jdbcType=DECIMAL}
      </if>
      <if test="condition.createTime != null ">
          and CREATE_TIME = #{condition.createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="condition.updatorId != null ">
          and UPDATOR_ID = #{condition.updatorId,jdbcType=DECIMAL}
      </if>
      <if test="condition.updateTime != null ">
          and UPDATE_TIME = #{condition.updateTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 使用PO对象作为动态条件删除多条记录的WHERE部分 -->
  <sql id="Base_Delete_By_Entity_Where">
    <trim prefixOverrides="and">
      <if test="quotaDefFactHisId != null ">
          and QUOTA_DEF_FACT_HIS_ID = #{quotaDefFactHisId,jdbcType=DECIMAL}
      </if>
      <if test="quotaDefFactId != null ">
          and QUOTA_DEF_FACT_ID = #{quotaDefFactId,jdbcType=DECIMAL}
      </if>
      <if test="quotaDefId != null ">
          and QUOTA_DEF_ID = #{quotaDefId,jdbcType=DECIMAL}
      </if>
      <if test="quotaCode != null and quotaCode != ''">
          and QUOTA_CODE = #{quotaCode,jdbcType=VARCHAR}
      </if>
      <if test="businessSourceCode != null ">
          and business_source_code = #{businessSourceCode,jdbcType=VARCHAR}
      </if>
      <if test="creatorId != null ">
          and CREATOR_ID = #{creatorId,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null ">
          and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updatorId != null ">
          and UPDATOR_ID = #{updatorId,jdbcType=DECIMAL}
      </if>
      <if test="updateTime != null ">
          and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </trim>
  </sql>
  <!-- 按主键查询一条记录 -->
  <select id="findById" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select 
    <include refid="Base_Column_List" />
    from ATR_CONF_QUOTA_DEF_FACTHIS
    where QUOTA_DEF_FACT_HIS_ID = #{quotaDefFactHisId,jdbcType=DECIMAL}
  </select>
  <!-- 按主键数组查询列表 -->
  <select id="findByIds" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Collection">
    select 
    <include refid="Base_Column_List" />
    from ATR_CONF_QUOTA_DEF_FACTHIS
    where QUOTA_DEF_FACT_HIS_ID in 
    <foreach collection="collection" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </select>
  <!-- 简单列表查询语句-->
  <select id="findAll" flushCache="false" useCache="true" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ATR_CONF_QUOTA_DEF_FACTHIS
  </select>
  <!-- 通用列表查询语句-->
  <select id="findList" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDefFactHis">
    select 
    <include refid="Base_Column_List" />
    from ATR_CONF_QUOTA_DEF_FACTHIS
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 通用列表查询语句(Map版本)-->
  <select id="findListByMap" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="java.util.Map">
    select 
    <include refid="Base_Column_List" />
    from ATR_CONF_QUOTA_DEF_FACTHIS
    <include refid="Base_Select_By_Entity_Where" />
  </select>
  <!-- 插入一条记录(为空的字段不操作) -->
  <!-- 如需返回数据库自动生成的主键ID，可在insert标签中添加以下属性： -->
  <!-- keyColumn="QUOTA_DEF_FACT_HIS_ID" keyProperty="quotaDefFactHisId" useGeneratedKeys="true" -->
  <insert id="save" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDefFactHis">
    <selectKey resultType="long" keyProperty="quotaDefFactHisId" order="BEFORE">
      select nextval('atr_seq_conf_quota_def_facthis') as sequenceNo 
    </selectKey>
    insert into ATR_CONF_QUOTA_DEF_FACTHIS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="quotaDefFactHisId != null">
        QUOTA_DEF_FACT_HIS_ID,
      </if>
      <if test="quotaDefFactId != null">
        QUOTA_DEF_FACT_ID,
      </if>
      <if test="quotaDefId != null">
        QUOTA_DEF_ID,
      </if>
      <if test="quotaCode != null">
        QUOTA_CODE,
      </if>
      <if test="businessSourceCode != null">
        business_source_code,
      </if>
      <if test="creatorId != null">
        CREATOR_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updatorId != null">
        UPDATOR_ID,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="serialNo != null">
        serial_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="quotaDefFactHisId != null">
        #{quotaDefFactHisId,jdbcType=DECIMAL},
      </if>
      <if test="quotaDefFactId != null">
        #{quotaDefFactId,jdbcType=DECIMAL},
      </if>
      <if test="quotaDefId != null">
        #{quotaDefId,jdbcType=DECIMAL},
      </if>
      <if test="quotaCode != null">
        #{quotaCode,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceCode != null">
        #{businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        #{updatorId,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=NUMERIC},
      </if>
    </trim>
  </insert>
  <!-- 通用批量插入语句。注：批量插入时，需要在代码中初始化所有数据库非空字段！-->
  <insert id="saveList" flushCache="true" parameterType="java.util.List">
    insert all 
    <foreach collection="list" item="item" index="index">
      into ATR_CONF_QUOTA_DEF_FACTHIS values 
       (#{item.quotaDefFactHisId,jdbcType=DECIMAL}, 
        #{item.quotaDefFactId,jdbcType=DECIMAL}, #{item.quotaDefId,jdbcType=DECIMAL}, #{item.quotaCode,jdbcType=VARCHAR}, 
        #{item.businessSourceCode,jdbcType=DECIMAL}, #{item.creatorId,jdbcType=DECIMAL}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updatorId,jdbcType=DECIMAL}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.serialNo,jdbcType=TIMESTAMP})
    </foreach>
    select 1 
  </insert>
  <!-- 根据逻辑主键，更新一条记录(为空的字段不操作) -->
  <update id="updateById" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDefFactHis">
    update ATR_CONF_QUOTA_DEF_FACTHIS
    <set>
      <if test="quotaDefFactId != null">
        QUOTA_DEF_FACT_ID = #{quotaDefFactId,jdbcType=DECIMAL},
      </if>
      <if test="quotaDefId != null">
        QUOTA_DEF_ID = #{quotaDefId,jdbcType=DECIMAL},
      </if>
      <if test="quotaCode != null">
        QUOTA_CODE = #{quotaCode,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceCode != null">
        business_source_code = #{businessSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        CREATOR_ID = #{creatorId,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorId != null">
        UPDATOR_ID = #{updatorId,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="serialNo != null">
        serial_no = #{serialNo,jdbcType=NUMERIC},
      </if>
    </set>
    where QUOTA_DEF_FACT_HIS_ID = #{quotaDefFactHisId,jdbcType=DECIMAL}
  </update>
  <!-- 根据动态条件，更新一条或多条记录(为空的字段不操作) -->
  <!-- 注：使用动态更新时，需传入两个参数，第一个为需更新的值，第二个的动态更新的where条件 -->
  <update id="update" flushCache="true" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDefFactHis">
    update ATR_CONF_QUOTA_DEF_FACTHIS
    <set>
      <if test="record.quotaDefFactId != null">
        QUOTA_DEF_FACT_ID = #{record.quotaDefFactId,jdbcType=DECIMAL},
      </if>
      <if test="record.quotaDefId != null">
        QUOTA_DEF_ID = #{record.quotaDefId,jdbcType=DECIMAL},
      </if>
      <if test="record.quotaCode != null">
        QUOTA_CODE = #{record.quotaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.businessSourceCode != null">
        business_source_code = #{record.businessSourceCode,jdbcType=DECIMAL},
      </if>
      <if test="record.creatorId != null">
        CREATOR_ID = #{record.creatorId,jdbcType=DECIMAL},
      </if>
      <if test="record.createTime != null">
        CREATE_TIME = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatorId != null">
        UPDATOR_ID = #{record.updatorId,jdbcType=DECIMAL},
      </if>
      <if test="record.updateTime != null">
        UPDATE_TIME = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where
    <include refid="Base_Update_By_Entity_Where" />
  </update>
  <!-- 按主键删除一条记录 -->
  <delete id="deleteById" flushCache="true" parameterType="java.lang.Long">
    delete from ATR_CONF_QUOTA_DEF_FACTHIS
    where QUOTA_DEF_FACT_HIS_ID = #{quotaDefFactHisId,jdbcType=DECIMAL}
  </delete>
  <!-- 按主键批量删除多条记录 -->
  <delete id="deleteByIds" flushCache="true" parameterType="java.util.List">
    delete from ATR_CONF_QUOTA_DEF_FACTHIS
    where QUOTA_DEF_FACT_HIS_ID in 
    <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=DECIMAL}
    </foreach>
  </delete>
  <!-- 按简单动态条件删除多条记录 ，Map中的Key必须与PO字段名一致-->
  <delete id="deleteByMap" flushCache="true" parameterType="java.util.Map">
    delete from ATR_CONF_QUOTA_DEF_FACTHIS
    where 
    <include refid="Base_Delete_By_Entity_Where" />
  </delete>
  <!-- 根据条件，进行简单的统计 -->
  <select id="count" flushCache="false" useCache="true" resultMap="BaseResultMap" parameterType="com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfQuotaDefFactHis">
    select count(1) from ATR_CONF_QUOTA_DEF_FACTHIS
    <include refid="Base_Select_By_Entity_Where" />
  </select>
</mapper>