drop table if exists qtc_dap_ecf_icg ;

create table qtc_dap_ecf_icg
(
    icu_id               bigint       not null
        constraint pk_qtc_dap_ecf_icg
            primary key,
    entity_id            numeric(11),
    year_month           varchar(6)   not null,
    currency_code        varchar(3)   not null,
    evaluate_approach    varchar(16),
    buss_model           varchar(6)   not null,
    business_source_code varchar(6),
    loa_code             varchar(32),
    portfolio_no         varchar(64),
    icg_no               varchar(60)  not null,
    premium              numeric(32, 8),
    net_charge           numeric(32, 8),
    iacf_fee             numeric(32, 8),
    iacf_fee_non         numeric(32, 8),
    dec_fee              numeric(32, 8),
    invest_amount        numeric(32, 8),
    ed_premium_rate      numeric(32, 8),
    ed_iacf_rate         numeric(32, 8),
    ed_iacf_non_rate     numeric(32, 8),
    ed_net_charge_rate   numeric(32, 8),
    iacf_fee_non_in      numeric(32, 8),
    ed_iacf_non_rate_in  numeric(32, 8),
    dept_id              bigint,
    fin_acc_channel      varchar(6),
    pl_judge_rslt        varchar(3),
    center_code          varchar(64),
    version_no           varchar(64)  not null,
    draw_time            timestamp(6) not null
)
    tablespace qtc_space;

comment on table qtc_dap_ecf_icg is '合同组信息';

comment on column qtc_dap_ecf_icg.entity_id is '机构ID';

comment on column qtc_dap_ecf_icg.year_month is '业务年月';

comment on column qtc_dap_ecf_icg.currency_code is '币别';

comment on column qtc_dap_ecf_icg.evaluate_approach is '评估方法';

comment on column qtc_dap_ecf_icg.buss_model is 'DD-直保；FI-临分分入;FO-临分分出；TI-合约分入；TO-合约分出';

comment on column qtc_dap_ecf_icg.business_source_code is '业务来源';

comment on column qtc_dap_ecf_icg.loa_code is '业务线';

comment on column qtc_dap_ecf_icg.portfolio_no is '合同组合';

comment on column qtc_dap_ecf_icg.icg_no is '合同组';

comment on column qtc_dap_ecf_icg.premium is '总保费';

comment on column qtc_dap_ecf_icg.net_charge is '净额结算手续费';

comment on column qtc_dap_ecf_icg.iacf_fee is '跟单IACF';

comment on column qtc_dap_ecf_icg.iacf_fee_non is '非跟单IACF-对外';

comment on column qtc_dap_ecf_icg.dec_fee is '总减值';

comment on column qtc_dap_ecf_icg.invest_amount is '当期确认的投资成分';

comment on column qtc_dap_ecf_icg.ed_premium_rate is '当期赚取比例_保费 ';

comment on column qtc_dap_ecf_icg.ed_iacf_rate is '当期赚取比例_跟单IACF';

comment on column qtc_dap_ecf_icg.ed_iacf_non_rate is '当期赚取比例_非跟单IACF-对外';

comment on column qtc_dap_ecf_icg.ed_net_charge_rate is '当期赚取比例_净额结算';

comment on column qtc_dap_ecf_icg.iacf_fee_non_in is '非跟单IACF-对内';

comment on column qtc_dap_ecf_icg.ed_iacf_non_rate_in is '非跟单IACF-对内';

comment on column qtc_dap_ecf_icg.dept_id is '部门';

comment on column qtc_dap_ecf_icg.fin_acc_channel is '财务核算渠道';

comment on column qtc_dap_ecf_icg.pl_judge_rslt is '盈亏标识';

comment on column qtc_dap_ecf_icg.center_code is '核算机构';

comment on column qtc_dap_ecf_icg.version_no is '版本号';

comment on column qtc_dap_ecf_icg.draw_time is '提数时间';

alter table qtc_dap_ecf_icg
    owner to qtcuser;

create index idx_qtc_dap_ecf_icg_ym
    on qtc_dap_ecf_icg (year_month);

grant delete, insert, select, update on qtc_dap_ecf_icg to atruser;


drop table if exists qtc_dap_ecf_icg_cf ;

create table qtc_dap_ecf_icg_cf
(
    icu_cf_id          bigint       not null,
    year_month         varchar(6)   not null,
    entity_id          numeric(11),
    buss_model         varchar(6)   not null,
    loa_code           varchar(32),
    evaluate_approach  varchar(16),
    portfolio_no       varchar(64),
    currency_code      varchar(3)   not null,
    icg_no             varchar(60)  not null,
    dev_period         numeric,
    premium            numeric(32, 8),
    ed_rate            numeric(32, 8),
    ed_premium         numeric(32, 8),
    ed_iacf_fee        numeric(32, 8),
    ed_iacf_fee_non    numeric(32, 8),
    ed_net_charge      numeric(32, 8),
    ed_iacf_fee_non_in numeric(32, 8),
    recv_premium       numeric(32, 8),
    iacf_fee           numeric(32, 8),
    iacf_fee_non       numeric(32, 8),
    net_charge         numeric(32, 8),
    dec_fee            numeric(32, 8),
    srd_fee            numeric(32, 8),
    maintenance        numeric(32, 8),
    loss_fee           numeric(32, 8),
    ra_fee             numeric(32, 8),
    iacf_fee_non_in    numeric(32, 8),
    version_no         varchar(64)  not null,
    draw_time          timestamp(6) not null,
    center_code        varchar(64)
)
    tablespace qtc_space;

comment on table qtc_dap_ecf_icg_cf is '预期现金流';

comment on column qtc_dap_ecf_icg_cf.dev_period is '发展期';

comment on column qtc_dap_ecf_icg_cf.premium is '未来保费现金流';

comment on column qtc_dap_ecf_icg_cf.ed_rate is '已赚比例';

comment on column qtc_dap_ecf_icg_cf.ed_premium is '已赚保费';

comment on column qtc_dap_ecf_icg_cf.ed_iacf_fee is '已赚跟单IACF';

comment on column qtc_dap_ecf_icg_cf.ed_iacf_fee_non is '已赚非跟单IACF-监管';

comment on column qtc_dap_ecf_icg_cf.ed_net_charge is '已赚净额结算手续费';

comment on column qtc_dap_ecf_icg_cf.ed_iacf_fee_non_in is '已赚非跟单IACF-对内';

comment on column qtc_dap_ecf_icg_cf.iacf_fee is '跟单IACF';

comment on column qtc_dap_ecf_icg_cf.iacf_fee_non is '非跟单IACF-监管';

comment on column qtc_dap_ecf_icg_cf.net_charge is '净额结算手续费';

comment on column qtc_dap_ecf_icg_cf.dec_fee is '减值现金流';

comment on column qtc_dap_ecf_icg_cf.srd_fee is '预期退保现金流';

comment on column qtc_dap_ecf_icg_cf.maintenance is '预期维持费用';

comment on column qtc_dap_ecf_icg_cf.loss_fee is '预期赔付现金流';

comment on column qtc_dap_ecf_icg_cf.ra_fee is '非金融风险调整现金流';

comment on column qtc_dap_ecf_icg_cf.iacf_fee_non_in is '非跟单IACF-对内';

comment on column qtc_dap_ecf_icg_cf.version_no is '版本号';

comment on column qtc_dap_ecf_icg_cf.draw_time is '提数时间';

comment on column qtc_dap_ecf_icg_cf.center_code is '核算机构';

alter table qtc_dap_ecf_icg_cf
    owner to qtcuser;

create index idx_qtc_dap_ecf_icg_cf_ym
    on qtc_dap_ecf_icg_cf (year_month);

grant delete, insert, select, update on qtc_dap_ecf_icg_cf to atruser;



drop table if exists qtc_dap_ecf_lic_pv ;

create table qtc_dap_ecf_lic_pv
(
    lic_pv_id    bigint       not null,
    entity_id    numeric(11),
    year_month   varchar(6)   not null,
    buss_model   varchar(6)   not null,
    loa_code     varchar(32),
    portfolio_no varchar(64)  not null,
    icg_no       varchar(60)  not null,
    cf_type      varchar(6)   not null,
    acc_time     varchar(6),
    p_pfr        numeric(32, 8),
    p_pwr        numeric(32, 8),
    p_cfr        numeric(32, 8),
    p_cwr        numeric(32, 8),
    c_cfr        numeric(32, 8),
    c_cwr        numeric(32, 8),
    version_no   varchar(64)  not null,
    draw_time    timestamp(6) not null,
    center_code  varchar(64)
)
    tablespace qtc_space;

comment on table qtc_dap_ecf_lic_pv is '未决赔款-现金流现值';

comment on column qtc_dap_ecf_lic_pv.entity_id is '机构ID';

comment on column qtc_dap_ecf_lic_pv.year_month is '业务年月';

comment on column qtc_dap_ecf_lic_pv.buss_model is '业务模型：DD-直保；FO-临分分出；TI-合约分入；TO-合约分出';

comment on column qtc_dap_ecf_lic_pv.loa_code is 'LOA';

comment on column qtc_dap_ecf_lic_pv.portfolio_no is '合同组合';

comment on column qtc_dap_ecf_lic_pv.icg_no is '合同组';

comment on column qtc_dap_ecf_lic_pv.cf_type is '现金流类型：OS-已发生已报告；IBNR-已发生未报告；UALE-间接理赔费用；RA-非金融风险调整；NTC-再保人风险调整';

comment on column qtc_dap_ecf_lic_pv.acc_time is '事故发生时点：C-当期；P-往期';

comment on column qtc_dap_ecf_lic_pv.p_pfr is '上期_期初现值-即期利率';

comment on column qtc_dap_ecf_lic_pv.p_pwr is '上期_期初现值-锁期利率';

comment on column qtc_dap_ecf_lic_pv.p_cfr is '上期_期末现值-锁期利率';

comment on column qtc_dap_ecf_lic_pv.p_cwr is '上期_期末现值-即期利率';

comment on column qtc_dap_ecf_lic_pv.c_cfr is '当期_期末现值-锁期利率';

comment on column qtc_dap_ecf_lic_pv.c_cwr is '当期_期末现值-即期利率';

comment on column qtc_dap_ecf_lic_pv.version_no is '提数版本号';

comment on column qtc_dap_ecf_lic_pv.draw_time is '提数时间';

comment on column qtc_dap_ecf_lic_pv.center_code is '核算机构';

alter table qtc_dap_ecf_lic_pv
    owner to qtcuser;

create index idx_qtc_dap_ecf_lic_pv_ym
    on qtc_dap_ecf_lic_pv (year_month);

grant delete, insert, select, update on qtc_dap_ecf_lic_pv to atruser;


drop table if exists qtc_dap_ecf_wa_ir ;

create table qtc_dap_ecf_wa_ir
(
    wa_ir_id      numeric(11)  not null,
    entity_id     numeric(11),
    currency_code varchar(3),
    buss_model    varchar(6)   not null,
    loa_code      varchar(64),
    portfolio_no  varchar(64),
    year_month    varchar(6)   not null,
    icg_no        varchar(60)  not null,
    dev_period    numeric,
    ir_base       numeric(32, 8),
    ir_rate1      numeric(32, 8),
    ir_rate2      numeric(32, 8),
    ir_rate3      numeric(32, 8),
    draw_time     timestamp(6) not null,
    center_code   varchar(64)
)
    tablespace qtc_space;

comment on table qtc_dap_ecf_wa_ir is '加权利率数据';

comment on column qtc_dap_ecf_wa_ir.entity_id is '机构ID';

comment on column qtc_dap_ecf_wa_ir.currency_code is '币别';

comment on column qtc_dap_ecf_wa_ir.buss_model is '业务模型';

comment on column qtc_dap_ecf_wa_ir.loa_code is 'LOA';

comment on column qtc_dap_ecf_wa_ir.portfolio_no is '合同组合';

comment on column qtc_dap_ecf_wa_ir.year_month is '业务年月';

comment on column qtc_dap_ecf_wa_ir.icg_no is '合同组';

comment on column qtc_dap_ecf_wa_ir.ir_base is '基础值';

comment on column qtc_dap_ecf_wa_ir.ir_rate1 is '期初';

comment on column qtc_dap_ecf_wa_ir.ir_rate2 is '期中';

comment on column qtc_dap_ecf_wa_ir.ir_rate3 is '期末';

comment on column qtc_dap_ecf_wa_ir.center_code is '核算机构';

alter table qtc_dap_ecf_wa_ir
    owner to qtcuser;

grant delete, insert, select, update on qtc_dap_ecf_wa_ir to atruser;


drop table if exists qtc_dap_recovered ;

create table qtc_dap_recovered
(
    recovered_id    numeric(11) not null
        constraint pk_qtc_dap_recovered
            primary key,
    entity_id       numeric(11),
    currency_code   varchar(3),
    year_month      varchar(6),
    buss_model      varchar(6),
    loa_code        varchar(64),
    portfolio_no    varchar(64),
    icg_no          varchar(64),
    ri_buss_model   varchar(6),
    ri_loa_code     varchar(64),
    ri_portfolio_no varchar(64),
    ri_icg_no       varchar(64),
    upr             numeric(32, 8),
    ri_upr          numeric(32, 8),
    draw_time       timestamp(6)
)
    tablespace qtc_space;

comment on table qtc_dap_recovered is '摊回保费接口表';

comment on column qtc_dap_recovered.recovered_id is 'ID';

comment on column qtc_dap_recovered.entity_id is '业务单位ID';

comment on column qtc_dap_recovered.currency_code is '币别';

comment on column qtc_dap_recovered.year_month is '业务年月';

comment on column qtc_dap_recovered.buss_model is '再保前业务模型 DD-直保；FI-临分分入;TI-合约分入';

comment on column qtc_dap_recovered.loa_code is 'LOA';

comment on column qtc_dap_recovered.portfolio_no is '再保前合同组合';

comment on column qtc_dap_recovered.icg_no is '再保前合同组';

comment on column qtc_dap_recovered.ri_buss_model is '分出业务模型  FO-临分分出；TO-合约分出';

comment on column qtc_dap_recovered.ri_loa_code is '分出LOA';

comment on column qtc_dap_recovered.ri_portfolio_no is '分出合同组合';

comment on column qtc_dap_recovered.ri_icg_no is '分出合同组';

comment on column qtc_dap_recovered.upr is '再保前期末未赚保费';

comment on column qtc_dap_recovered.ri_upr is '分出期末未赚保费';

alter table qtc_dap_recovered
    owner to qtcuser;

grant delete, insert, select, update on qtc_dap_recovered to atruser;




drop table if exists qtc_dap_lrc_fo_apt ;

create table qtc_dap_lrc_fo_apt
(
    apt_id           bigint generated always as identity,
    entity_id        bigint      not null,
    year_month       varchar(6)  not null,
    currency_code    varchar(3)  not null,
    loa_code         varchar(64),
    portfolio_no     varchar(64),
    icg_no           varchar(64) not null,
    treaty_no        varchar(64) not null,
    policy_no        varchar(64) not null,
    kind_code        varchar(64) not null,
    cr_premium       numeric(32, 8),
    ed_premium       numeric(32, 8),
    ed_iacf          numeric(32, 8),
    ed_iacf_non      numeric(32, 8),
    ed_net_charge    numeric(32, 8),
    ed_dec_fee       numeric(32, 8),
    re_premium       numeric(32, 8),
    re_iacf          numeric(32, 8),
    re_iacf_non      numeric(32, 8),
    re_net_charge    numeric(32, 8),
    re_dec_fee       numeric(32, 8),
    ed_iacf_non_in   numeric(32, 8),
    re_iacf_non_in   numeric(32, 8),
    version_no       varchar(64),
    draw_time        timestamp(6),
    dept_code        varchar(30),
    product_code     varchar(60),
    fin_detail_code  varchar(60),
    sup_product_code varchar(60),
    dept_id          bigint,
    fin_acc_channel  varchar(6),
    pl_judge_rslt    varchar(3),
    buss_source_code varchar(6),
    center_code      varchar(64),
    risk_code        varchar(32),
    invest_amount    numeric(32, 8)
)
    tablespace qtc_space;

comment on table qtc_dap_lrc_fo_apt is '计量分摊接口表-临分分出';

comment on column qtc_dap_lrc_fo_apt.apt_id is 'ID';

comment on column qtc_dap_lrc_fo_apt.entity_id is '业务单位ID';

comment on column qtc_dap_lrc_fo_apt.year_month is '业务年月';

comment on column qtc_dap_lrc_fo_apt.currency_code is '币别';

comment on column qtc_dap_lrc_fo_apt.portfolio_no is '合同组合';

comment on column qtc_dap_lrc_fo_apt.icg_no is '合同组';

comment on column qtc_dap_lrc_fo_apt.treaty_no is '临时合约号';

comment on column qtc_dap_lrc_fo_apt.policy_no is '标的保单号';

comment on column qtc_dap_lrc_fo_apt.kind_code is '险别';

comment on column qtc_dap_lrc_fo_apt.cr_premium is '当月新增保费';

comment on column qtc_dap_lrc_fo_apt.ed_premium is '当月已赚_保费';

comment on column qtc_dap_lrc_fo_apt.ed_iacf is '当月已赚_跟单iacf';

comment on column qtc_dap_lrc_fo_apt.ed_iacf_non is '当月已赚_非跟单iacf';

comment on column qtc_dap_lrc_fo_apt.ed_net_charge is '当月已赚_净额结算';

comment on column qtc_dap_lrc_fo_apt.ed_dec_fee is '当月已赚_减值';

comment on column qtc_dap_lrc_fo_apt.re_premium is '当月实收_保费';

comment on column qtc_dap_lrc_fo_apt.re_iacf is '当月支付_跟单iacf';

comment on column qtc_dap_lrc_fo_apt.re_iacf_non is '当月支付_非跟单iacf';

comment on column qtc_dap_lrc_fo_apt.re_net_charge is '当月支付_净额结算';

comment on column qtc_dap_lrc_fo_apt.re_dec_fee is '当月支付_减值';

comment on column qtc_dap_lrc_fo_apt.version_no is '版本号';

comment on column qtc_dap_lrc_fo_apt.draw_time is '提数时间';

comment on column qtc_dap_lrc_fo_apt.dept_code is '部门编码';

comment on column qtc_dap_lrc_fo_apt.product_code is '财务产品段';

comment on column qtc_dap_lrc_fo_apt.fin_detail_code is '财务明细段';

comment on column qtc_dap_lrc_fo_apt.sup_product_code is '财务补充产品段';

comment on column qtc_dap_lrc_fo_apt.dept_id is '部门';

comment on column qtc_dap_lrc_fo_apt.fin_acc_channel is '财务核算渠道';

comment on column qtc_dap_lrc_fo_apt.pl_judge_rslt is '盈亏标识';

comment on column qtc_dap_lrc_fo_apt.buss_source_code is '业务来源';

comment on column qtc_dap_lrc_fo_apt.center_code is '核算机构';

comment on column qtc_dap_lrc_fo_apt.invest_amount is '当期确认的投资成分';

alter table qtc_dap_lrc_fo_apt
    owner to qtcuser;

grant delete, insert, select, update on qtc_dap_lrc_fo_apt to atruser;


drop table if exists qtc_dap_lrc_to_apt ;

create table qtc_dap_lrc_to_apt
(
    apt_id           bigint generated always as identity,
    entity_id        bigint      not null,
    year_month       varchar(6)  not null,
    currency_code    varchar(3)  not null,
    loa_code         varchar(64),
    portfolio_no     varchar(64),
    icg_no           varchar(64) not null,
    treaty_no        varchar(64) not null,
    policy_no        varchar(64) not null,
    kind_code        varchar(64) not null,
    cr_premium       numeric(32, 8),
    ed_premium       numeric(32, 8),
    ed_iacf          numeric(32, 8),
    ed_iacf_non      numeric(32, 8),
    ed_net_charge    numeric(32, 8),
    ed_dec_fee       numeric(32, 8),
    re_premium       numeric(32, 8),
    re_iacf          numeric(32, 8),
    re_iacf_non      numeric(32, 8),
    re_net_charge    numeric(32, 8),
    re_dec_fee       numeric(32, 8),
    ed_iacf_non_in   numeric(32, 8),
    re_iacf_non_in   numeric(32, 8),
    version_no       varchar(64),
    draw_time        timestamp(6),
    dept_code        varchar(30),
    product_code     varchar(60),
    fin_detail_code  varchar(60),
    sup_product_code varchar(60),
    dept_id          bigint,
    fin_acc_channel  varchar(6),
    pl_judge_rslt    varchar(3),
    buss_source_code varchar(6),
    treaty_type      varchar(3),
    center_code      varchar(64),
    risk_code        varchar(32),
    invest_amount    numeric(32, 8)
)
    tablespace qtc_space;

comment on table qtc_dap_lrc_to_apt is '计量分摊接口表-合约分出';

comment on column qtc_dap_lrc_to_apt.apt_id is 'ID';

comment on column qtc_dap_lrc_to_apt.entity_id is '业务单位ID';

comment on column qtc_dap_lrc_to_apt.year_month is '业务年月';

comment on column qtc_dap_lrc_to_apt.currency_code is '币别';

comment on column qtc_dap_lrc_to_apt.portfolio_no is '合同组合';

comment on column qtc_dap_lrc_to_apt.icg_no is '合同组';

comment on column qtc_dap_lrc_to_apt.treaty_no is '合约号';

comment on column qtc_dap_lrc_to_apt.policy_no is '标的保单号';

comment on column qtc_dap_lrc_to_apt.kind_code is '险别';

comment on column qtc_dap_lrc_to_apt.cr_premium is '当月新增保费';

comment on column qtc_dap_lrc_to_apt.ed_premium is '当月已赚_保费';

comment on column qtc_dap_lrc_to_apt.ed_iacf is '当月已赚_跟单iacf';

comment on column qtc_dap_lrc_to_apt.ed_iacf_non is '当月已赚_非跟单iacf';

comment on column qtc_dap_lrc_to_apt.ed_net_charge is '当月已赚_净额结算';

comment on column qtc_dap_lrc_to_apt.ed_dec_fee is '当月已赚_减值';

comment on column qtc_dap_lrc_to_apt.re_premium is '当月实收_保费';

comment on column qtc_dap_lrc_to_apt.re_iacf is '当月支付_跟单iacf';

comment on column qtc_dap_lrc_to_apt.re_iacf_non is '当月支付_非跟单iacf';

comment on column qtc_dap_lrc_to_apt.re_net_charge is '当月支付_净额结算';

comment on column qtc_dap_lrc_to_apt.re_dec_fee is '当月支付_减值';

comment on column qtc_dap_lrc_to_apt.version_no is '版本号';

comment on column qtc_dap_lrc_to_apt.draw_time is '提数时间';

comment on column qtc_dap_lrc_to_apt.dept_code is '部门编码';

comment on column qtc_dap_lrc_to_apt.product_code is '财务产品段';

comment on column qtc_dap_lrc_to_apt.fin_detail_code is '财务明细段';

comment on column qtc_dap_lrc_to_apt.sup_product_code is '财务补充产品段';

comment on column qtc_dap_lrc_to_apt.dept_id is '部门';

comment on column qtc_dap_lrc_to_apt.fin_acc_channel is '财务核算渠道';

comment on column qtc_dap_lrc_to_apt.pl_judge_rslt is '盈亏标识';

comment on column qtc_dap_lrc_to_apt.buss_source_code is '业务来源';

comment on column qtc_dap_lrc_to_apt.treaty_type is '合约业务类型';

comment on column qtc_dap_lrc_to_apt.center_code is '核算机构';

comment on column qtc_dap_lrc_to_apt.invest_amount is '当期确认的投资成分';

alter table qtc_dap_lrc_to_apt
    owner to qtcuser;

grant delete, insert, select, update on qtc_dap_lrc_to_apt to atruser;


drop table if exists qtc_dap_lrc_ti_apt ;

create table qtc_dap_lrc_ti_apt
(
    apt_id           bigint generated always as identity,
    entity_id        bigint      not null,
    year_month       varchar(6)  not null,
    currency_code    varchar(3)  not null,
    loa_code         varchar(64),
    portfolio_no     varchar(64),
    icg_no           varchar(64) not null,
    treaty_no        varchar(64) not null,
    ri_dept          varchar(64) not null,
    risk_class       varchar(64) not null,
    cr_premium       numeric(32, 8),
    ed_premium       numeric(32, 8),
    ed_iacf          numeric(32, 8),
    ed_iacf_non      numeric(32, 8),
    ed_net_charge    numeric(32, 8),
    ed_dec_fee       numeric(32, 8),
    re_premium       numeric(32, 8),
    re_iacf          numeric(32, 8),
    re_iacf_non      numeric(32, 8),
    re_net_charge    numeric(32, 8),
    re_dec_fee       numeric(32, 8),
    ed_iacf_non_in   numeric(32, 8),
    re_iacf_non_in   numeric(32, 8),
    version_no       varchar(64),
    draw_time        timestamp(6),
    product_code     varchar(60),
    fin_detail_code  varchar(60),
    sup_product_code varchar(60),
    dept_id          bigint,
    fin_acc_channel  varchar(6),
    pl_judge_rslt    varchar(3),
    buss_source_code varchar(6),
    treaty_type      varchar(3),
    dept_code        varchar(30),
    center_code      varchar(64),
    invest_amount    numeric(32, 8)
)
    tablespace qtc_space;

comment on table qtc_dap_lrc_ti_apt is '计量分摊接口表-合约分入';

comment on column qtc_dap_lrc_ti_apt.apt_id is 'ID';

comment on column qtc_dap_lrc_ti_apt.entity_id is '业务单位ID';

comment on column qtc_dap_lrc_ti_apt.year_month is '业务年月';

comment on column qtc_dap_lrc_ti_apt.currency_code is '币别';

comment on column qtc_dap_lrc_ti_apt.portfolio_no is '合同组合';

comment on column qtc_dap_lrc_ti_apt.icg_no is '合同组';

comment on column qtc_dap_lrc_ti_apt.treaty_no is '合约号';

comment on column qtc_dap_lrc_ti_apt.ri_dept is '再保业务部';

comment on column qtc_dap_lrc_ti_apt.risk_class is '险类';

comment on column qtc_dap_lrc_ti_apt.cr_premium is '当月新增保费';

comment on column qtc_dap_lrc_ti_apt.ed_premium is '当月已赚_保费';

comment on column qtc_dap_lrc_ti_apt.ed_iacf is '当月已赚_跟单iacf';

comment on column qtc_dap_lrc_ti_apt.ed_iacf_non is '当月已赚_非跟单iacf';

comment on column qtc_dap_lrc_ti_apt.ed_net_charge is '当月已赚_净额结算';

comment on column qtc_dap_lrc_ti_apt.ed_dec_fee is '当月已赚_减值';

comment on column qtc_dap_lrc_ti_apt.re_premium is '当月实收_保费';

comment on column qtc_dap_lrc_ti_apt.re_iacf is '当月支付_跟单iacf';

comment on column qtc_dap_lrc_ti_apt.re_iacf_non is '当月支付_非跟单iacf';

comment on column qtc_dap_lrc_ti_apt.re_net_charge is '当月支付_净额结算';

comment on column qtc_dap_lrc_ti_apt.re_dec_fee is '当月支付_减值';

comment on column qtc_dap_lrc_ti_apt.version_no is '版本号';

comment on column qtc_dap_lrc_ti_apt.draw_time is '提数时间';

comment on column qtc_dap_lrc_ti_apt.product_code is '财务产品段';

comment on column qtc_dap_lrc_ti_apt.fin_detail_code is '财务明细段';

comment on column qtc_dap_lrc_ti_apt.sup_product_code is '财务补充产品段';

comment on column qtc_dap_lrc_ti_apt.dept_id is '部门';

comment on column qtc_dap_lrc_ti_apt.fin_acc_channel is '财务核算渠道';

comment on column qtc_dap_lrc_ti_apt.pl_judge_rslt is '盈亏标识';

comment on column qtc_dap_lrc_ti_apt.buss_source_code is '业务来源';

comment on column qtc_dap_lrc_ti_apt.treaty_type is '合约业务类型';

comment on column qtc_dap_lrc_ti_apt.dept_code is '部门编码';

comment on column qtc_dap_lrc_ti_apt.center_code is '核算机构';

comment on column qtc_dap_lrc_ti_apt.invest_amount is '当期确认的投资成分';

alter table qtc_dap_lrc_ti_apt
    owner to qtcuser;

grant delete, insert, select, update on qtc_dap_lrc_ti_apt to atruser;



drop table if exists qtc_dap_lic_fo_apt ;

create table qtc_dap_lic_fo_apt
(
    apt_id           bigint generated always as identity,
    entity_id        bigint      not null,
    year_month       varchar(6)  not null,
    currency_code    varchar(3)  not null,
    loa_code         varchar(64),
    portfolio_no     varchar(64),
    icg_no           varchar(64) not null,
    treaty_no        varchar(64) not null,
    policy_no        varchar(64) not null,
    kind_code        varchar(64) not null,
    acc_time         varchar(6),
    os               numeric(32, 8),
    ibnr             numeric(32, 8),
    ra               numeric(32, 8),
    ntc              numeric(32, 8),
    version_no       varchar(64),
    draw_time        timestamp(6),
    dept_code        varchar(30),
    product_code     varchar(60),
    fin_detail_code  varchar(60),
    sup_product_code varchar(60),
    dept_id          bigint,
    fin_acc_channel  varchar(6),
    pl_judge_rslt    varchar(3),
    buss_source_code varchar(6),
    center_code      varchar(64)
)
    tablespace qtc_space;

comment on table qtc_dap_lic_fo_apt is '计量分摊接口表-赔款-临分分出';

comment on column qtc_dap_lic_fo_apt.entity_id is '机构ID';

comment on column qtc_dap_lic_fo_apt.year_month is '业务年月';

comment on column qtc_dap_lic_fo_apt.loa_code is 'LOA';

comment on column qtc_dap_lic_fo_apt.portfolio_no is '合同组合';

comment on column qtc_dap_lic_fo_apt.icg_no is '合同组';

comment on column qtc_dap_lic_fo_apt.treaty_no is '临时合约号';

comment on column qtc_dap_lic_fo_apt.policy_no is '标的保单号';

comment on column qtc_dap_lic_fo_apt.kind_code is '险别';

comment on column qtc_dap_lic_fo_apt.acc_time is '事故发生时点：C-当期；P-往期';

comment on column qtc_dap_lic_fo_apt.os is '已发生已报告';

comment on column qtc_dap_lic_fo_apt.ibnr is '已发生未报告';

comment on column qtc_dap_lic_fo_apt.ra is '非金融风险调整';

comment on column qtc_dap_lic_fo_apt.ntc is '再保人不履约风险调整';

comment on column qtc_dap_lic_fo_apt.version_no is '提数版本号';

comment on column qtc_dap_lic_fo_apt.draw_time is '提数时间';

comment on column qtc_dap_lic_fo_apt.dept_code is '部门编码';

comment on column qtc_dap_lic_fo_apt.product_code is '财务产品段';

comment on column qtc_dap_lic_fo_apt.fin_detail_code is '财务明细段';

comment on column qtc_dap_lic_fo_apt.sup_product_code is '财务补充产品段';

comment on column qtc_dap_lic_fo_apt.dept_id is '部门';

comment on column qtc_dap_lic_fo_apt.fin_acc_channel is '财务核算渠道';

comment on column qtc_dap_lic_fo_apt.pl_judge_rslt is '盈亏标识';

comment on column qtc_dap_lic_fo_apt.buss_source_code is '业务来源';

comment on column qtc_dap_lic_fo_apt.center_code is '核算机构';

alter table qtc_dap_lic_fo_apt
    owner to qtcuser;

create index idx_qtc_dap_lic_fo_apt_ym
    on qtc_dap_lic_fo_apt (year_month);

grant delete, insert, select, update on qtc_dap_lic_fo_apt to atruser;


drop table if exists qtc_dap_lic_ti_apt ;

create table qtc_dap_lic_ti_apt
(
    apt_id           bigint generated always as identity,
    entity_id        bigint      not null,
    year_month       varchar(6)  not null,
    currency_code    varchar(3)  not null,
    loa_code         varchar(64),
    portfolio_no     varchar(64),
    icg_no           varchar(64) not null,
    treaty_no        varchar(64) not null,
    ri_dept          varchar(64) not null,
    risk_class       varchar(64) not null,
    acc_time         varchar(6),
    os               numeric(32, 8),
    ibnr             numeric(32, 8),
    uale             numeric(32, 8),
    ra               numeric(32, 8),
    version_no       varchar(64),
    draw_time        timestamp(6),
    dept_code        varchar(30),
    product_code     varchar(60),
    fin_detail_code  varchar(60),
    sup_product_code varchar(60),
    dept_id          bigint,
    fin_acc_channel  varchar(6),
    pl_judge_rslt    varchar(3),
    buss_source_code varchar(6),
    treaty_type      varchar(3),
    center_code      varchar(64)
)
    tablespace qtc_space;

comment on table qtc_dap_lic_ti_apt is '计量分摊接口表-赔款-合约分入';

comment on column qtc_dap_lic_ti_apt.entity_id is '机构ID';

comment on column qtc_dap_lic_ti_apt.year_month is '业务年月';

comment on column qtc_dap_lic_ti_apt.loa_code is 'LOA';

comment on column qtc_dap_lic_ti_apt.portfolio_no is '合同组合';

comment on column qtc_dap_lic_ti_apt.icg_no is '合同组';

comment on column qtc_dap_lic_ti_apt.treaty_no is '合约号';

comment on column qtc_dap_lic_ti_apt.ri_dept is '再保业务部';

comment on column qtc_dap_lic_ti_apt.risk_class is '险类';

comment on column qtc_dap_lic_ti_apt.acc_time is '事故发生时点：C-当期；P-往期';

comment on column qtc_dap_lic_ti_apt.os is '已发生已报告';

comment on column qtc_dap_lic_ti_apt.ibnr is '已发生未报告';

comment on column qtc_dap_lic_ti_apt.uale is '间接理赔费用';

comment on column qtc_dap_lic_ti_apt.ra is '非金融风险调整';

comment on column qtc_dap_lic_ti_apt.version_no is '提数版本号';

comment on column qtc_dap_lic_ti_apt.draw_time is '提数时间';

comment on column qtc_dap_lic_ti_apt.dept_code is '部门编码';

comment on column qtc_dap_lic_ti_apt.product_code is '财务产品段';

comment on column qtc_dap_lic_ti_apt.fin_detail_code is '财务明细段';

comment on column qtc_dap_lic_ti_apt.sup_product_code is '财务补充产品段';

comment on column qtc_dap_lic_ti_apt.dept_id is '部门';

comment on column qtc_dap_lic_ti_apt.fin_acc_channel is '财务核算渠道';

comment on column qtc_dap_lic_ti_apt.pl_judge_rslt is '盈亏标识';

comment on column qtc_dap_lic_ti_apt.buss_source_code is '业务来源';

comment on column qtc_dap_lic_ti_apt.treaty_type is '合约业务类型';

comment on column qtc_dap_lic_ti_apt.center_code is '核算机构';

alter table qtc_dap_lic_ti_apt
    owner to qtcuser;

create index idx_qtc_dap_lic_ti_apt_ym
    on qtc_dap_lic_ti_apt (year_month);

grant delete, insert, select, update on qtc_dap_lic_ti_apt to atruser;


drop table if exists qtc_dap_lic_to_apt ;

create table qtc_dap_lic_to_apt
(
    apt_id           bigint generated always as identity,
    entity_id        bigint      not null,
    year_month       varchar(6)  not null,
    currency_code    varchar(3)  not null,
    loa_code         varchar(64),
    portfolio_no     varchar(64),
    icg_no           varchar(64) not null,
    treaty_no        varchar(64) not null,
    policy_no        varchar(64) not null,
    kind_code        varchar(64) not null,
    acc_time         varchar(6),
    os               numeric(32, 8),
    ibnr             numeric(32, 8),
    ra               numeric(32, 8),
    ntc              numeric(32, 8),
    version_no       varchar(64),
    draw_time        timestamp(6),
    dept_code        varchar(30),
    product_code     varchar(60),
    fin_detail_code  varchar(60),
    sup_product_code varchar(60),
    dept_id          bigint,
    fin_acc_channel  varchar(6),
    pl_judge_rslt    varchar(3),
    buss_source_code varchar(6),
    treaty_type      varchar(3),
    center_code      varchar(64)
)
    tablespace qtc_space;

comment on table qtc_dap_lic_to_apt is '计量分摊接口表-赔款-临分分出';

comment on column qtc_dap_lic_to_apt.entity_id is '机构ID';

comment on column qtc_dap_lic_to_apt.year_month is '业务年月';

comment on column qtc_dap_lic_to_apt.loa_code is 'LOA';

comment on column qtc_dap_lic_to_apt.portfolio_no is '合同组合';

comment on column qtc_dap_lic_to_apt.icg_no is '合同组';

comment on column qtc_dap_lic_to_apt.treaty_no is '临时合约号';

comment on column qtc_dap_lic_to_apt.policy_no is '标的保单号';

comment on column qtc_dap_lic_to_apt.kind_code is '险别';

comment on column qtc_dap_lic_to_apt.acc_time is '事故发生时点：C-当期；P-往期';

comment on column qtc_dap_lic_to_apt.os is '已发生已报告';

comment on column qtc_dap_lic_to_apt.ibnr is '已发生未报告';

comment on column qtc_dap_lic_to_apt.ra is '非金融风险调整';

comment on column qtc_dap_lic_to_apt.ntc is '再保人不履约风险调整';

comment on column qtc_dap_lic_to_apt.version_no is '提数版本号';

comment on column qtc_dap_lic_to_apt.draw_time is '提数时间';

comment on column qtc_dap_lic_to_apt.dept_code is '部门编码';

comment on column qtc_dap_lic_to_apt.product_code is '财务产品段';

comment on column qtc_dap_lic_to_apt.fin_detail_code is '财务明细段';

comment on column qtc_dap_lic_to_apt.sup_product_code is '财务补充产品段';

comment on column qtc_dap_lic_to_apt.dept_id is '部门';

comment on column qtc_dap_lic_to_apt.fin_acc_channel is '财务核算渠道';

comment on column qtc_dap_lic_to_apt.pl_judge_rslt is '盈亏标识';

comment on column qtc_dap_lic_to_apt.buss_source_code is '业务来源';

comment on column qtc_dap_lic_to_apt.treaty_type is '合约业务类型';

comment on column qtc_dap_lic_to_apt.center_code is '核算机构';

alter table qtc_dap_lic_to_apt
    owner to qtcuser;

create index idx_qtc_dap_lic_to_apt_ym
    on qtc_dap_lic_to_apt (year_month);

grant delete, insert, select, update on qtc_dap_lic_to_apt to atruser;


drop table if exists qtc_dap_lrc_dd_apt ;

create table qtc_dap_lrc_dd_apt
(
    apt_id           bigint generated always as identity,
    entity_id        bigint      not null,
    year_month       varchar(6)  not null,
    currency_code    varchar(3)  not null,
    loa_code         varchar(64),
    portfolio_no     varchar(64),
    icg_no           varchar(64) not null,
    policy_no        varchar(64) not null,
    kind_code        varchar(64) not null,
    fin_acc_channel  varchar(60),
    dept_id          bigint,
    buss_source_code varchar(6),
    pl_judge_rslt    varchar(3),
    dept_code        varchar(30),
    product_code     varchar(60),
    fin_detail_code  varchar(60),
    sup_product_code varchar(60),
    center_code      varchar(64),
    cr_premium       numeric(32, 8),
    ed_premium       numeric(32, 8),
    ed_iacf          numeric(32, 8),
    ed_iacf_non      numeric(32, 8),
    ed_net_charge    numeric(32, 8),
    ed_dec_fee       numeric(32, 8),
    re_premium       numeric(32, 8),
    re_iacf          numeric(32, 8),
    re_iacf_non      numeric(32, 8),
    re_net_charge    numeric(32, 8),
    re_dec_fee       numeric(32, 8),
    ed_iacf_non_in   numeric(32, 8),
    re_iacf_non_in   numeric(32, 8),
    invest_amount    numeric(32, 8),
    version_no       varchar(64),
    draw_time        timestamp(6)
)
     partition by LIST (year_month);

alter table  qtc_dap_lrc_dd_apt set  tablespace qtc_space ;

comment on table qtc_dap_lrc_dd_apt is '计量分摊接口表-直保业务';

comment on column qtc_dap_lrc_dd_apt.apt_id is 'ID';

comment on column qtc_dap_lrc_dd_apt.entity_id is '业务单位ID';

comment on column qtc_dap_lrc_dd_apt.year_month is '业务年月';

comment on column qtc_dap_lrc_dd_apt.currency_code is '币别';

comment on column qtc_dap_lrc_dd_apt.portfolio_no is '合同组合';

comment on column qtc_dap_lrc_dd_apt.icg_no is '合同组';

comment on column qtc_dap_lrc_dd_apt.policy_no is '保单号';

comment on column qtc_dap_lrc_dd_apt.kind_code is '险别';

comment on column qtc_dap_lrc_dd_apt.fin_acc_channel is '财务核算渠道';

comment on column qtc_dap_lrc_dd_apt.dept_id is '部门';

comment on column qtc_dap_lrc_dd_apt.buss_source_code is '业务来源';

comment on column qtc_dap_lrc_dd_apt.pl_judge_rslt is '盈亏标识';

comment on column qtc_dap_lrc_dd_apt.dept_code is '部门编码';

comment on column qtc_dap_lrc_dd_apt.product_code is '财务产品段';

comment on column qtc_dap_lrc_dd_apt.fin_detail_code is '财务明细段';

comment on column qtc_dap_lrc_dd_apt.sup_product_code is '财务补充产品段';

comment on column qtc_dap_lrc_dd_apt.center_code is '核算机构';

comment on column qtc_dap_lrc_dd_apt.cr_premium is '当月新增保费';

comment on column qtc_dap_lrc_dd_apt.ed_premium is '当月已赚_保费';

comment on column qtc_dap_lrc_dd_apt.ed_iacf is '当月已赚_跟单iacf';

comment on column qtc_dap_lrc_dd_apt.ed_iacf_non is '当月已赚_非跟单iacf';

comment on column qtc_dap_lrc_dd_apt.ed_net_charge is '当月已赚_净额结算';

comment on column qtc_dap_lrc_dd_apt.ed_dec_fee is '当月已赚_减值';

comment on column qtc_dap_lrc_dd_apt.re_premium is '当月实收_保费';

comment on column qtc_dap_lrc_dd_apt.re_iacf is '当月支付_跟单iacf';

comment on column qtc_dap_lrc_dd_apt.re_iacf_non is '当月支付_非跟单iacf';

comment on column qtc_dap_lrc_dd_apt.re_net_charge is '当月支付_净额结算';

comment on column qtc_dap_lrc_dd_apt.re_dec_fee is '当月支付_减值';

comment on column qtc_dap_lrc_dd_apt.invest_amount is '当期确认的投资成分';

comment on column qtc_dap_lrc_dd_apt.version_no is '版本号';

comment on column qtc_dap_lrc_dd_apt.draw_time is '提数时间';

alter table qtc_dap_lrc_dd_apt
    owner to qtcuser;

grant delete, insert, select, update on qtc_dap_lrc_dd_apt to atruser;


drop table if exists qtc_dap_lic_dd_apt ;

create table qtc_dap_lic_dd_apt
(
    apt_id           bigint generated always as identity,
    entity_id        bigint      not null,
    year_month       varchar(6)  not null,
    currency_code    varchar(3)  not null,
    loa_code         varchar(64),
    portfolio_no     varchar(64),
    icg_no           varchar(64) not null,
    policy_no        varchar(64) not null,
    kind_code        varchar(64) not null,
    dept_code        varchar(30),
    product_code     varchar(60),
    fin_detail_code  varchar(60),
    sup_product_code varchar(60),
    dept_id          bigint,
    fin_acc_channel  varchar(6),
    pl_judge_rslt    varchar(3),
    buss_source_code varchar(6),
    center_code      varchar(64),
    acc_time         varchar(6),
    os               numeric(32, 8),
    ibnr             numeric(32, 8),
    uale             numeric(32, 8),
    ra               numeric(32, 8),
    version_no       varchar(64),
    draw_time        timestamp(6)
)
     partition by LIST (year_month);

alter table  qtc_dap_lic_dd_apt set  tablespace qtc_space ;

comment on table qtc_dap_lic_dd_apt is '计量分摊接口表-赔款-直保业务';

comment on column qtc_dap_lic_dd_apt.entity_id is '机构ID';

comment on column qtc_dap_lic_dd_apt.year_month is '业务年月';

comment on column qtc_dap_lic_dd_apt.loa_code is 'LOA';

comment on column qtc_dap_lic_dd_apt.portfolio_no is '合同组合';

comment on column qtc_dap_lic_dd_apt.icg_no is '合同组';

comment on column qtc_dap_lic_dd_apt.policy_no is '保单号';

comment on column qtc_dap_lic_dd_apt.kind_code is '险别';

comment on column qtc_dap_lic_dd_apt.dept_code is '部门编码';

comment on column qtc_dap_lic_dd_apt.product_code is '财务产品段';

comment on column qtc_dap_lic_dd_apt.fin_detail_code is '财务明细段';

comment on column qtc_dap_lic_dd_apt.sup_product_code is '财务补充产品段';

comment on column qtc_dap_lic_dd_apt.dept_id is '部门';

comment on column qtc_dap_lic_dd_apt.fin_acc_channel is '财务核算渠道';

comment on column qtc_dap_lic_dd_apt.pl_judge_rslt is '盈亏标识';

comment on column qtc_dap_lic_dd_apt.buss_source_code is '业务来源';

comment on column qtc_dap_lic_dd_apt.center_code is '核算机构';

comment on column qtc_dap_lic_dd_apt.acc_time is '事故发生时点：C-当期；P-往期';

comment on column qtc_dap_lic_dd_apt.os is '已发生已报告';

comment on column qtc_dap_lic_dd_apt.ibnr is '已发生未报告';

comment on column qtc_dap_lic_dd_apt.uale is '间接理赔费用';

comment on column qtc_dap_lic_dd_apt.ra is '非金融风险调整';

comment on column qtc_dap_lic_dd_apt.version_no is '提数版本号';

comment on column qtc_dap_lic_dd_apt.draw_time is '提数时间';

alter table qtc_dap_lic_dd_apt
    owner to qtcuser;

grant delete, insert, select, update on qtc_dap_lic_dd_apt to atruser;




--- 创建 202401 到 202512 月份分区
do
$$
    DECLARE
        -- 声明变量
        v_tbm   varchar ;
        rec_tb  record ;
        v_sql   text ;
        v_count integer ;
        v_ym    varchar ;
        v_num   integer ;
        rec_ym  record ;
        p_ym    varchar ;

    BEGIN

        v_ym := '202401';
        v_num := 24;

        for rec_ym in 0..v_num - 1
            loop

                p_ym := to_char(to_date(v_ym, 'yyyymm') + rec_ym * interval '1 month', 'yyyymm');

                for rec_tb in (select 'qtc_dap_lrc_dd_apt' tm
                               union all
                               select 'qtc_dap_lic_dd_apt' tm )
                    loop

                        v_tbm := rec_tb.tm || '_' || p_ym;

                        execute ('drop table if exists ' || v_tbm);

                        execute ('create table ' || v_tbm || ' partition of ' || rec_tb.tm || ' for values in (''' || p_ym || ''')');

                        execute ('grant delete, insert, select, update on ' || v_tbm || ' to atruser ');

                    end loop;

            end loop;

    END
$$;

