/**
 * 
 * This file was generated by GIS MyBatis Generator(v1.2.12).
 * Date: 2021-03-23 14:27:06
 * Author liebin.zheng
 * 
 * Copyright (c) 2017-2027, Sinosoft Technology Information System Co.Ltd. All Rights Reserved.
 * 
 */
package com.ss.ifrs.actuarial.dao.conf;


import com.ss.library.mybatis.interfaces.IDao;
import org.apache.ibatis.annotations.Mapper;

import com.ss.ifrs.actuarial.pojo.atrconf.po.AtrConfRiskRef;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfRiskRefVo;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;

/**
 * This code was generated by GIS MyBatis Generator(v1.2.12).
 * Create Date: 2021-03-23 14:27:06<br/>
 * Description: LRC计量方式配置表 Dao类<br/>
 * Related Table Name: ATR_CONF_RISK_REF<br/>
 * <br/>
 * Remark: 
 * 关于乐观锁说明如下：<br/>
 * 1、使用最新版本代码生成器，重新生成mapper文件，引入ssplatform-base-core包！<br/>
 * 2、updateById操作时，如PO类的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * 3、update或updateByExample操作时，如record参数的属性中包含int类型的version字段，将自动在where条件中添加version字段判断。<br/>
 * <br/>
 */
@Mapper
public interface AtrConfRiskRefDao extends IDao<AtrConfRiskRef, Long> {

    Page<AtrConfRiskRefVo> fuzzySearchPage(AtrConfRiskRefVo atrConfRiskRefVo, Pageable pageParam);

    AtrConfRiskRefVo findByRiskRefId(Long riskRefId);

    Long checkNaturalPk(AtrConfRiskRefVo atrConfRiskRefVo);

}