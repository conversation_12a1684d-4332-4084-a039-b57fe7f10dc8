CREATE OR <PERSON><PERSON><PERSON>CE VIEW ods_v_acc_payment AS
 SELECT FIN.ID,
        FIN.DRAW_TIME,
        FIN.DRAW_TYPE,
        FIN.TASK_STATUS,
        FIN.TASK_CODE,
        FIN.DRAW_USER,
        FIN.CHECK_MSG,
        FIN.entity_code,
        FIN.POLICY_NO,
        FIN.ENDORSE_NO,
        FIN.ENDORSE_SEQ_NO,
        FIN.claim_loss_no,
        FIN.ri_policy_no,
        FIN.ri_statement_no,
        FIN.est_payment_seq_no,
        FIN.TREATY_NO,
        FIN.RISK_CODE,
        FIN.DEPT_CODE,
        FIN.CHANNEL_CODE,
        FIN.event_code,
        FIN.actual_payment_date,
        FIN.est_payment_date,
        FIN.entry_type_code,
        FIN.ri_arrangement_code,
        FIN.business_source_code,
        FIN.payment_type_code,
        FIN.policy_type_code,
        FIN.ri_direction_code,
        FIN.treaty_type_code,
        FIN.expenses_type_code,
        FIN.currency_code,
        FIN.AMOUNT,
        FIN.ri_broker_code,
        FIN.REINSURER_CODE,
        FIN.CASHFLOW_ARTICLE,
        FIN.account_code,
        FIN.account_entry_code,
        FIN.bu_voucher_no,
        FIN.bu_voucher_date,
        FIN.EXTEND_COLUMN1,
        FIN.EXTEND_COLUMN2,
        FIN.EXTEND_COLUMN3,
        CAS.BOOK_CODE
   FROM ODS_ACC_PAYMENT FIN, BPLUSER.bbs_entity B2
   LEFT JOIN BPLUSER.BBS_CONF_ACCOUNT_SET CAS
     ON CAS.entity_id = B2.entity_id
  WHERE B2.entity_code = FIN.entity_code;
